GIT_REPO_NAME ?= $(shell basename `git rev-parse --show-toplevel`)

DOCKER_REPOSITORY ?= 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/$(GIT_REPO_NAME)-deploy
DOCKER_TAG ?= $(shell cat Dockerfile entrypoint.sh | md5sum | cut -c1-10)
DOCKER_IMAGE ?= $(DOCKER_REPOSITORY):$(DOCKER_TAG)

.PHONY: docker-build
docker-build:
	docker build . -t $(DOCKER_IMAGE)

.PHONY: docker-push
docker-push: docker-build
	docker push $(DOCKER_IMAGE)
