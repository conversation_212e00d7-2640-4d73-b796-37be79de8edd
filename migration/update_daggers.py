# -*- coding: utf-8 -*-

import os
import sys
from io import StringIO

import ruamel.yaml
import pencilbox as pb

from utils import find_files, copytree


yaml = ruamel.yaml.YAML()
yaml.width = 4096
yaml.preserve_quotes = True

executor_config_sql = """
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
    - name: airflow-dags
      subPath: tmp
      mountPath: /usr/local/airflow/tmp
"""

executor_config_other = """
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
"""

dags_folder = os.path.dirname(os.getcwd())
if len(sys.argv) > 1:
    with open(sys.argv[1], "r") as f:
        daggers = f.readlines()
    daggers = list(map(lambda x: os.path.join(dags_folder, x.strip()), daggers))
else:
    daggers = find_files(os.path.join(dags_folder, "daggers"), "config.yml")

sheet_id = "1RtFD-NY7zu0DLsm91gx5FZzjF15boqvnzIhIeD-wQOA"
sheet_name = "per_dag_memory_usage"
df = pb.from_sheets(sheet_id, sheet_name, service_account="service_account")
dagger_paths = df["path"].tolist()

for dagger in daggers:
    with open(dagger, "r") as f:
        config = yaml.load(f)

    if config.get("path") is None:
        config[
            "path"
        ] = f"{config['namespace']}/{config['project_name']}/{config['dag_type']}/{config['dag_name']}"

    if config.get("nobuild", False):
        print(config["path"], "is nobuild, please update it manually")
        continue

    if config["template_name"] in ["replicant", "nessie"]:
        continue

    print("updating", config["path"])

    if config["template_name"] == "sql":
        with StringIO(executor_config_sql) as f:
            k8s_executor_config = yaml.load(f)
    else:
        with StringIO(executor_config_other) as f:
            k8s_executor_config = yaml.load(f)

    if config["path"] in dagger_paths:
        dag = df[df["path"] == config["path"]]
        node_type = dag.iloc[0]["node_type"]
        memory_limit = dag.iloc[0]["memory_limit"]

        k8s_executor_config["executor"]["config"]["node_selectors"][
            "nodetype"
        ] = node_type
        k8s_executor_config["executor"]["config"]["memory"][
            "limit"
        ] = f"{memory_limit}G"

    config.update(k8s_executor_config)

    with open(dagger, "w") as f:
        yaml.dump(config, f)
