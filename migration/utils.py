# -*- coding: utf-8 -*-

import os
import shutil


def find_files(root, filename):
    for d, _, files in os.walk(root):
        for f in files:
            if filename in f:
                yield os.path.join(d, f)


def copytree(src, dst, symlinks=False, ignore=None):
    for item in os.listdir(src):
        s = os.path.join(src, item)
        d = os.path.join(dst, item)
        if os.path.isdir(s):
            shutil.rmtree(dst)
            shutil.copytree(s, d, symlinks, ignore)
        else:
            shutil.copy2(s, d)
