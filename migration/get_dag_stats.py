# -*- coding: utf-8 -*-

import os
import glob
import math
import pytz
import time
import yaml
import traceback
import datetime as dt

import pandas
import requests

import pencilbox as pb

from utils import find_files, copytree


def get_prometheus_stats(url, query, start, end):
    response = requests.get(
        url + "/api/v1/query_range",
        params={
            "query": query,
            "dedup": True,
            "partial_response": True,
            "start": start,
            "end": end,
            "step": 345,
        },
    )
    return response.json()["data"]["result"]


url = "http://thanos-query-service-monitoring.prod-sgp-k8s.grofer.io:9090"
end = dt.datetime.now(pytz.timezone("UTC"))
start = end - dt.timedelta(days=1)

end = time.mktime(end.timetuple())
start = time.mktime(start.timetuple())

con = pb.get_connection("airflow")
dags = pandas.read_sql(
    "SELECT dag_id from dag WHERE is_paused = 'False' AND is_active = 'True'", con=con
)
dags = dags["dag_id"].tolist()

dags_folder = os.path.dirname(os.getcwd())
daggers = find_files(os.path.join(dags_folder, "daggers"), "config.yml")


def save_data(data):
    columns = [
        "namespace",
        "project_name",
        "dag_type",
        "dag_name",
        "interval_bucket",
        "owner",
        "nobuild",
        "has_macros",
        "escalation_priority",
        "dag_run_duration",
        "memory_usage",
        "has_memory_usage",
    ]
    df = pandas.DataFrame(data)
    df = df[columns]
    df = df.sort_values(by="memory_usage", ascending=False)
    df.to_csv("per_dag_memory_usage.csv", quoting=1, encoding="utf-8", index=False)


# TODO: Reuse prometheus exporter code instead of making requests to thanos

data = []
i = 1
try:
    for dagger in daggers:
        with open(dagger, "r") as f:
            config = yaml.load(f, Loader=yaml.FullLoader)
        dag_id = f"{config['namespace']}_{config['project_name']}_{config['dag_type']}_{config['dag_name']}_v{config['version']}"

        if "canary_dag" in dag_id:
            dag_id = "canary_dag"

        if dag_id in dags:
            try:
                print(i, "running for", dag_id)
                pod_name = dag_id.split("_v")[0].replace("_", "")
                memory_usage_query = (
                    'sum(container_memory_usage_bytes{namespace="airflow", pod_name=~"%s.*"}) by (pod_name)'
                    % pod_name
                )

                print("getting memory usage")
                result = get_prometheus_stats(url, memory_usage_query, start, end)
                memory_usage = max(int(v[1]) for v in result[0]["values"]) / math.pow(
                    1024, 3
                )
                has_memory_usage = True
            except Exception as e:
                print("Failed getting memory usage for", dag_id, str(e))
                memory_usage = 0
                has_memory_usage = False

            time.sleep(1)

            try:
                dag_schedule_delay_query = (
                    'airflow_dag_schedule_delay{namespace="airflow", dag_id="%s"}'
                    % dag_id
                )

                print("getting interval bucket")
                result = get_prometheus_stats(url, dag_schedule_delay_query, start, end)
                interval_bucket = result[0]["metric"]["interval_bucket"]
            except Exception as e:
                print("Failed getting schedule delay for", dag_id, str(e))
                interval_bucket = "na"

            time.sleep(1)

            try:
                dag_run_duration_query = (
                    'airflow_dag_run_duration{namespace="airflow", dag_id="%s"}'
                    % dag_id
                )

                print("getting dag run duration")
                result = get_prometheus_stats(url, dag_run_duration_query, start, end)
                dag_run_duration = max(float(v[1]) for v in result[0]["values"])
            except Exception as e:
                print("Failed getting run duration for", dag_id, str(e))
                dag_run_duration = 0

            time.sleep(1)

            has_macros = False
            if config["template_name"] == "sql":
                if config["source"]["incremental"]["parameters"] is not None and any(
                    "macros" in str(v)
                    for v in config["source"]["incremental"]["parameters"].values()
                ):
                    has_macros = True
            elif config["template_name"] == "notebook":
                if config["notebook"]["parameters"] is not None and any(
                    "macros" in str(v)
                    for v in config["notebook"]["parameters"].values()
                ):
                    has_macros = True
            elif config["template_name"] == "multi_notebook":
                for notebook in config["notebooks"]:
                    if notebook["parameters"] is not None and any(
                        "macros" in str(v) for v in notebook["parameters"].values()
                    ):
                        has_macros = True
                        break

            if isinstance(config["owner"], list):
                owner_name = config["owner"][0]["name"]
            else:
                owner_name = config["owner"]["name"]

            data.append(
                {
                    "namespace": config["namespace"],
                    "project_name": config["project_name"],
                    "dag_type": config["dag_type"],
                    "dag_name": config["dag_name"],
                    "interval_bucket": interval_bucket,
                    "owner": owner_name,
                    "nobuild": config.get("nobuild", False),
                    "has_macros": has_macros,
                    "escalation_priority": config["escalation_priority"],
                    "dag_run_duration": dag_run_duration,
                    "memory_usage": memory_usage,
                    "has_memory_usage": has_memory_usage,
                }
            )
            i += 1
except KeyboardInterrupt:
    print("process interrupted, saving data")
except Exception as e:
    traceback.print_exc(e)
finally:
    save_data(data)
