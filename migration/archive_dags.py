import os
import shutil
import ruamel.yaml
from utils import find_files

cwd = os.getcwd()

# We are scanning more than the current version
# This covers use case of dags which were reverted
# And the script never archive them
EXTRA_BUFFER = 5

daggers = find_files("daggers", "config.yml")

for dagger in list(daggers):
    with open(dagger, "r") as f:
        config = ruamel.yaml.round_trip_load(f, preserve_quotes=True)
    namespace = config["namespace"]
    project_name = config["project_name"]
    dag_type = config["dag_type"]
    dag_name = config["dag_name"]
    version = config["version"]
    paused = config["paused"]
    nobuild = config.get("nobuild", False)

    if paused == False and nobuild == True:
        print(f"Manual intervention needed for {config.get('path', '')}")
        continue

    try:
        old_versions = range(1, version+1) if paused else range(1, version)
        for old_version in range(1, version+EXTRA_BUFFER):
            # If the dag is not paused skip the current version
            if paused == False and version == old_version:
                continue

            dag_id = f"dag_v{old_version}.py"
            dag_path = os.path.join(cwd, "dags", namespace, project_name, dag_type, dag_name, dag_id)
            archive_path = os.path.join(cwd, "archived_dags", namespace, project_name, dag_type, dag_name)
            archive_dag_path = os.path.join(archive_path, dag_id)

            if not os.path.exists(archive_path):
                os.makedirs(archive_path)
            if not os.path.exists(dag_path):
                continue

            print(f"Archiving {dag_name} at version {version}")
            print("==========================================")
            print(f"Moving {dag_id}")
            shutil.move(dag_path, archive_dag_path)
            print("==========================================")
    except Exception as e:
        print(f"ERRORED {e}")
