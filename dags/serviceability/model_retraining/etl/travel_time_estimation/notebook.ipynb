{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost==0.26.1\n", "!pip install haversine\n", "!pip install -<PERSON> seaborn\n", "!pip install h3\n", "!pip install joblib\n", "!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "from multiprocessing import Pool\n", "from haversine import haversine\n", "\n", "import pencilbox as pb\n", "import seaborn as sns\n", "import pandas as pd\n", "import numpy as np\n", "from h3 import h3\n", "import warnings\n", "import requests\n", "import logging\n", "import pickle\n", "import joblib\n", "import random\n", "import shutil\n", "import glob\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(format=\"%(asctime)s %(levelname)s:%(message)s\")\n", "logger = logging.getLogger(\"picking_model_retraining\")\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=60)\n", "end_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "START_DATE = start_date.strftime(\"%Y-%m-%d\")\n", "VALIDATION_DATE = (\n", "    datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=15)\n", ").strftime(\"%Y-%m-%d\")\n", "TEST_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=8)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "END_DATE = end_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_splits = []\n", "itter_date = start_date\n", "\n", "while itter_date.strftime(\"%Y-%m-%d\") < END_DATE:\n", "    date_splits.append(itter_date.strftime(\"%Y-%m-%d\"))\n", "    itter_date += <PERSON><PERSON><PERSON>(days=10)\n", "date_splits.append(END_DATE)\n", "trips_data_folder = \"trips_reched_doorstep\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"Fetching Data from {str(START_DATE)} to {str(END_DATE)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# trips_data_folder = \"trips_reched_doorstep\"\n", "if not os.path.exists(trips_data_folder):\n", "    os.mkdir(trips_data_folder)\n", "\n", "\n", "for i in range(len(date_splits) - 1):\n", "    logger.info(f\"Fetching Data from {str(date_splits[i])} to {str(date_splits[i+1])}\")\n", "    trip_details_query = f\"\"\"SELECT\n", "            ldsp.trip_id,\n", "            ldsp.current_delivery_start + interval '5.5 hour' AS slot_start,\n", "            ldsp.current_delivery_end + interval '5.5 hour' AS slot_end,\n", "            ldsp.order_id,\n", "            CASE\n", "                WHEN json_extract_path_text(ldsp.tags,'next_day_delivery',TRUE)='true' THEN 'next_day_delivery'\n", "                WHEN json_extract_path_text(ldsp.tags,'super_express',TRUE)='true'\n", "                     AND oo.install_ts::date=current_delivery_start::date THEN 'super_express'\n", "                ELSE 'normal'\n", "                END AS order_type,\n", "            ldsp.delivery_fe_id,\n", "            ldsp.dark_store_id as darkstore_id,\n", "            ldsp.trip_start_timestamp + interval '5.5 hour' as trip_start_timestamp,\n", "            ldsp.delivery_timestamp + interval '5.5 hour' as delivery_timestamp,\n", "            json_extract_path_text(lt.payload,'delivery_address','latitude',true) as customer_latitude,\n", "            json_extract_path_text(lt.payload,'delivery_address','longitude',true) as customer_longitude,\n", "            json_extract_path_text(lt.form_data,'doorstep_location','latitude',true) as doorstep_latitude,\n", "            json_extract_path_text(lt.form_data,'doorstep_location','longitude',true) as doorstep_longitude,\n", "            min(tml.update_ts + interval '5.5 hour') as reached_doorstep_time\n", "            FROM lake_logistics.view_logistics_dark_store_projection ldsp\n", "            inner join lake_logistics.logistics_task  lt on lt.shipment_label_value = ldsp.order_id and lt.type='ORDER_DELIVERY_TASK' \n", "            and lt.shipment_label_type = 'ORDER_ID'\n", "            inner join lake_logistics.task_management_taskstatelog tml on lt.id = tml.task_id and to_state = 'AT_DOORSTEP'\n", "            INNER JOIN lake_oms_bifrost.oms_order oo ON oo.id=ldsp.order_id\n", "            WHERE (oo.install_ts BETWEEN '{date_splits[i]}':: timestamp - interval '5.5 hours' \n", "            AND '{date_splits[i+1]}':: timestamp + interval '1 day' - interval '5.5 hours')\n", "            GROUP by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "            ORDER BY 1,2,3\"\"\"\n", "    trip_details = pd.read_sql_query(sql=trip_details_query, con=con_redshift)\n", "    trip_details.to_csv(\n", "        f\"{trips_data_folder}/{date_splits[i]}_{date_splits[i+1]}.csv\", index=False\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details = pd.concat(\n", "    [pd.read_csv(i) for i in glob.glob(f\"{trips_data_folder}/*.csv\")]\n", ")\n", "trip_details = trip_details.query(\"order_type == 'super_express'\")\n", "trip_details = trip_details.sort_values(\"slot_start\")\n", "trip_details = trip_details.tail(7000000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_location_query = \"\"\"\n", "SELECT lna.install_ts::date,\n", "       ln.external_id AS darkstore_id,\n", "       ln.external_name AS superstore_name,\n", "       lna.latitude AS latitude,\n", "       lna.longitude AS longitude,\n", "       lna.formatted_address AS address,\n", "       lna.city as city\n", "FROM lake_logistics.logistics_node_address lna\n", "INNER JOIN lake_logistics.logistics_node ln ON ln.node_address_id = lna.id\n", "ORDER BY 1\n", "\"\"\"\n", "ds_location = pd.read_sql(sql=ds_location_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"Starting Data Cleaning process\")\n", "\n", "\n", "ds_location = ds_location.rename(\n", "    {\"latitude\": \"ds_latitude\", \"longitude\": \"ds_longitude\"}, axis=1\n", ")\n", "trip_details = trip_details.astype({\"darkstore_id\": \"int64\", \"order_id\": \"int64\"})\n", "ds_location = ds_location.astype({\"darkstore_id\": \"int64\"})\n", "\n", "trip_details_merged = pd.merge(\n", "    trip_details,\n", "    ds_location[[\"darkstore_id\", \"ds_latitude\", \"ds_longitude\"]],\n", "    how=\"inner\",\n", "    left_on=\"darkstore_id\",\n", "    right_on=\"darkstore_id\",\n", ")\n", "\n", "trip_details_merged[\"customer_latitude\"] = pd.to_numeric(\n", "    trip_details_merged[\"customer_latitude\"], downcast=\"float\"\n", ")\n", "\n", "trip_details_merged[\"customer_longitude\"] = pd.to_numeric(\n", "    trip_details_merged[\"customer_longitude\"], downcast=\"float\"\n", ")\n", "\n", "trip_details_merged[\"doorstep_latitude\"] = pd.to_numeric(\n", "    trip_details_merged[\"doorstep_latitude\"], downcast=\"float\"\n", ")\n", "trip_details_merged[\"doorstep_longitude\"] = pd.to_numeric(\n", "    trip_details_merged[\"doorstep_longitude\"], downcast=\"float\"\n", ")\n", "\n", "trip_details_super_express = trip_details_merged[\n", "    trip_details_merged[\"order_type\"] == \"super_express\"\n", "]\n", "trip_details_super_express = (\n", "    trip_details_super_express.sort_values([\"trip_id\", \"reached_doorstep_time\"])\n", "    .drop_duplicates([\"trip_id\"], keep=\"first\")\n", "    .dropna()\n", ")\n", "trip_details_super_express = trip_details_super_express[\n", "    [\n", "        \"trip_id\",\n", "        \"order_id\",\n", "        \"darkstore_id\",\n", "        \"order_type\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"trip_start_timestamp\",\n", "        \"reached_doorstep_time\",\n", "        \"ds_latitude\",\n", "        \"ds_longitude\",\n", "        \"customer_latitude\",\n", "        \"customer_longitude\",\n", "        \"doorstep_latitude\",\n", "        \"doorstep_longitude\",\n", "    ]\n", "]\n", "trip_details_super_express.reached_doorstep_time = pd.to_datetime(\n", "    trip_details_super_express.reached_doorstep_time\n", ")\n", "trip_details_super_express.trip_start_timestamp = pd.to_datetime(\n", "    trip_details_super_express.trip_start_timestamp\n", ")\n", "\n", "trip_details_super_express[\"reached_doorstep_timestamp\"] = (\n", "    trip_details_super_express.reached_doorstep_time\n", "    - trip_details_super_express.trip_start_timestamp\n", ").dt.seconds / 60\n", "trip_details_super_express = trip_details_super_express.query(\n", "    \"reached_doorstep_timestamp <= 60 & reached_doorstep_timestamp >= 2.2\"\n", ")\n", "\n", "logger.info(f\"Finished Data Cleaninig Process\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\"\n", "\n", "\n", "def osrm_url_formatter(coords, url, profile=\"route\"):\n", "    result_fetched = 0\n", "    wait_time = 5\n", "    wait_increase = 2\n", "\n", "    statement = (\n", "        url\n", "        + \"/{}/v1/driving/\".format(profile)\n", "        + coords\n", "        + \"?overview=false&annotations=true\"\n", "    )\n", "\n", "    while result_fetched != 1:\n", "        try:\n", "            r = requests.get(statement)\n", "            result_fetched = 1\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to fetch data for {statement} with error {e} retrying in {wait_time} secs\"\n", "            )\n", "            wait_time = wait_time * wait_increase\n", "    return r.json()\n", "\n", "\n", "def multithread_osrsm_request(coord):\n", "    start_time = time.time()\n", "    pool = Pool(4)\n", "    distance_lst = list(pool.imap(get_inter_order_distance, coord))\n", "    pool.close()\n", "    pool.join()\n", "    print(\"--- %s seconds ---\" % (time.time() - start_time))\n", "\n", "    return distance_lst\n", "\n", "\n", "def get_inter_order_distance(locations):\n", "    pair1 = locations[1]\n", "    pair2 = locations[0]\n", "\n", "    coords = (\n", "        \"\"\n", "        + str(pair1[1])\n", "        + \",\"\n", "        + str(pair1[0])\n", "        + \";\"\n", "        + str(pair2[1])\n", "        + \",\"\n", "        + str(pair2[0])\n", "    )\n", "    response = osrm_url_formatter(coords, OSRM_URL, \"route\")\n", "\n", "    try:\n", "        distance = response[\"routes\"][0][\"distance\"] / 1000  # distance in kms\n", "    #         duration = response[\"routes\"][0][\"duration\"] / 60\n", "    except:\n", "        return 0\n", "\n", "    return distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details_super_express[\"customer_hex\"] = trip_details_super_express.apply(\n", "    lambda x: h3.geo_to_h3(x[\"customer_latitude\"], x[\"customer_longitude\"], 10), axis=1\n", ")\n", "trip_details_super_express[\"darkstore_hex\"] = trip_details_super_express.apply(\n", "    lambda x: h3.geo_to_h3(x[\"ds_latitude\"], x[\"ds_longitude\"], 10), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_hex_geo_mapping = np.array(\n", "    trip_details_super_express.apply(\n", "        lambda x: h3.h3_to_geo(x[\"customer_hex\"]), axis=1\n", "    ).tolist()\n", ")\n", "darkstore_hex_geo_mapping = np.array(\n", "    trip_details_super_express.apply(\n", "        lambda x: h3.h3_to_geo(x[\"darkstore_hex\"]), axis=1\n", "    ).tolist()\n", ")\n", "\n", "trip_details_super_express[\"customer_hex_latitude\"] = customer_hex_geo_mapping[:, 0]\n", "trip_details_super_express[\"customer_hex_longitude\"] = customer_hex_geo_mapping[:, 1]\n", "trip_details_super_express[\"darkstore_hex_latitude\"] = darkstore_hex_geo_mapping[:, 0]\n", "trip_details_super_express[\"darkstore_hex_longitude\"] = darkstore_hex_geo_mapping[:, 1]\n", "\n", "b = [0, 4, 8, 12, 16, 20, 24]\n", "l = [0, 1, 2, 3, 4, 5]\n", "trip_details_super_express[\"part_of_day\"] = pd.cut(\n", "    trip_details_super_express[\"trip_start_timestamp\"].dt.hour,\n", "    bins=b,\n", "    labels=l,\n", "    include_lowest=True,\n", ")\n", "trip_details_super_express[\n", "    \"day_of_week\"\n", "] = trip_details_super_express.trip_start_timestamp.dt.dayofweek"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hex_pair_subset = trip_details_super_express[\n", "    [\n", "        \"darkstore_hex\",\n", "        \"customer_hex\",\n", "        \"darkstore_hex_latitude\",\n", "        \"darkstore_hex_longitude\",\n", "        \"customer_hex_latitude\",\n", "        \"customer_hex_longitude\",\n", "    ]\n", "].drop_duplicates(subset=[\"darkstore_hex\", \"customer_hex\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coord = list()\n", "for row in hex_pair_subset.itertuples(index=False):\n", "    coord.append(\n", "        [\n", "            (row.darkstore_hex_latitude, row.darkstore_hex_longitude),\n", "            (row.customer_hex_latitude, row.customer_hex_longitude),\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"Finding Hex distance using osrm\")\n", "hex_pair_subset[\"darkstore_customer_distance_hex\"] = multithread_osrsm_request(coord)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details_super_express = trip_details_super_express.merge(\n", "    hex_pair_subset[\n", "        [\"darkstore_hex\", \"customer_hex\", \"darkstore_customer_distance_hex\"]\n", "    ],\n", "    on=[\"darkstore_hex\", \"customer_hex\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details_super_express[\"speed\"] = trip_details_super_express[\n", "    \"darkstore_customer_distance_hex\"\n", "] / (trip_details_super_express[\"reached_doorstep_timestamp\"] / 60)\n", "relevant_trips = trip_details_super_express.query(\n", "    f\"reached_doorstep_timestamp <= 60 & reached_doorstep_timestamp >= 2.2 & speed >= {np.percentile(trip_details_super_express.speed,5)} & speed <= {np.percentile(trip_details_super_express.speed,98)} \"\n", ")\n", "trips = relevant_trips[\n", "    [\n", "        \"slot_start\",\n", "        \"darkstore_id\",\n", "        \"customer_hex\",\n", "        \"part_of_day\",\n", "        \"day_of_week\",\n", "        \"darkstore_customer_distance_hex\",\n", "        \"reached_doorstep_timestamp\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data = trips.query(f\"(slot_start <'{VALIDATION_DATE}')\")\n", "\n", "\n", "validation_data = trips.query(\n", "    f\"((slot_start >= '{VALIDATION_DATE}') & (slot_start <= '{TEST_DATE}') )\"\n", ")\n", "\n", "test_data = trips.query(f\"(slot_start > '{TEST_DATE}')\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.shape, validation_data.shape, test_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if not os.path.exists(\"reached_doorstep\"):\n", "#     os.mkdir(\"reached_doorstep\")\n", "\n", "# training_data.to_csv(\"reached_doorstep/training_data.csv\", index=False)\n", "# validation_data.to_csv(\"reached_doorstep/validation_data.csv\", index=False)\n", "# test_data.to_csv(\"reached_doorstep/test_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["categorical_feature = [\"darkstore_id\", \"customer_hex\", \"part_of_day\", \"day_of_week\"]\n", "continous_feature = [\"darkstore_customer_distance_hex\"]\n", "features = continous_feature + categorical_feature\n", "prediction_feature = \"reached_doorstep_timestamp\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_params = {\n", "    \"n_estimators\": 1555,\n", "    \"learning_rate\": 0.0710775870446307,\n", "    \"l2_leaf_reg\": 1,\n", "    \"depth\": 3,\n", "    \"loss_function\": \"MAE\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_catboost(\n", "    tr_data,\n", "    val_data,\n", "    categorical_features,\n", "    continous_features,\n", "    params,\n", "    dependent_variable=\"picking_time\",\n", "):\n", "\n", "    features = continous_features + categorical_features\n", "\n", "    model = CatBoostRegressor(\n", "        **params,\n", "        cat_features=categorical_features,\n", "        custom_metric=[\"MAE\", \"R2\", \"RMSE\"],\n", "        verbose=200,\n", "        train_dir=\"/tmp/logs\",\n", "    )\n", "\n", "    model.fit(\n", "        tr_data[features],\n", "        tr_data[dependent_variable],\n", "        eval_set=(\n", "            val_data[features],\n", "            val_data[dependent_variable],\n", "        ),\n", "        use_best_model=True,\n", "    )\n", "\n", "    return model\n", "\n", "\n", "def fillna(x):\n", "    prediction_value = 0\n", "    if str(x[\"predicted_time\"]) == \"nan\":\n", "        prediction_value = training_data.reached_doorstep_timestamp.mean()\n", "    else:\n", "        prediction_value = x[\"predicted_time\"]\n", "    return prediction_value\n", "\n", "\n", "def get_base_line_prediction(test_data):\n", "    training_data_group = training_data.groupby(\n", "        [\"darkstore_id\"], as_index=False\n", "    ).mean()[[\"darkstore_id\", \"reached_doorstep_timestamp\"]]\n", "\n", "    test_data_baseline = test_data[\n", "        [\"darkstore_id\", \"reached_doorstep_timestamp\"]\n", "    ].merge(\n", "        training_data_group,\n", "        on=[\n", "            \"darkstore_id\",\n", "        ],\n", "        how=\"left\",\n", "    )\n", "    display(test_data_baseline.isna().sum())\n", "    test_data_baseline.columns = [\n", "        \"darkstore_id\",\n", "        \"reached_doorstep_timestamp\",\n", "        \"predicted_time\",\n", "    ]\n", "\n", "    test_data_baseline[\"predicted_time\"] = test_data_baseline.apply(\n", "        lambda x: fillna(x), axis=1\n", "    )\n", "    return test_data_baseline[\"predicted_time\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"training on {training_data.shape[0]} data points\")\n", "model = train_catboost(\n", "    training_data,\n", "    validation_data,\n", "    categorical_feature,\n", "    continous_feature,\n", "    model_params,\n", "    dependent_variable=prediction_feature,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"predictions\"] = model.predict(test_data[features].dropna())\n", "\n", "model_test_mae = mean_absolute_error(\n", "    test_data.reached_doorstep_timestamp, test_data.predictions\n", ")\n", "\n", "print(\n", "    \"Model:\",\n", "    model.get_best_score()[\"learn\"][\"MAE\"],\n", "    model.get_best_score()[\"validation\"][\"MAE\"],\n", "    model_test_mae,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_version = 1.0\n", "model_subid = datetime.now().strftime(\"%Y%m%d\")\n", "model_name = \"se_travel_time_estimation\"\n", "model_id = \"{}_v{}_{}\".format(model_name, model_version, model_subid)\n", "model_type = \"catboost_regressor\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs = {\n", "    \"training_data_start_date\": training_data.slot_start.min(),\n", "    \"training_data_end_date\": training_data.slot_start.max(),\n", "    \"validation_data_start_date\": validation_data.slot_start.min(),\n", "    \"validation_data_end_date\": validation_data.slot_start.max(),\n", "    \"test_data_start_date\": test_data.slot_start.min(),\n", "    \"test_data_end_date\": test_data.slot_start.max(),\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_subid\": model_subid,\n", "    \"model_type\": model_type,\n", "    \"speed_lower_bound\": round(\n", "        np.percentile(\n", "            training_data.darkstore_customer_distance_hex\n", "            / (training_data.reached_doorstep_timestamp / 60),\n", "            5,\n", "        ),\n", "        2,\n", "    ),\n", "    \"speed_upper_bound\": round(\n", "        np.percentile(\n", "            training_data.darkstore_customer_distance_hex\n", "            / (training_data.reached_doorstep_timestamp / 60),\n", "            98,\n", "        ),\n", "        2,\n", "    ),\n", "    \"model_train_mae\": round(model.get_best_score()[\"learn\"][\"MAE\"], 2),\n", "    \"model_validation_mae\": round(model.get_best_score()[\"validation\"][\"MAE\"], 2),\n", "    \"model_test_mae\": round(model_test_mae, 2),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BUCKET_NAME = \"prod-dse-projects\"\n", "MODEL_CLOUDPATH = (\n", "    \"/eta_prediction/travel_time_estimation/models/{}/{}/model.pkl\".format(\n", "        \"v1.0\", model_subid\n", "    )\n", ")\n", "MODEL_METADATA_CLOUDPATH = (\n", "    \"/eta_prediction/travel_time_estimation/models/{}/{}/metadata.json\".format(\n", "        \"v1.0\", model_subid\n", "    )\n", ")\n", "MODEL_POINTER_CLOUDPATH = \"/eta_prediction/travel_time_estimation/models/current.json\"\n", "\n", "MODEL_LOCALPATH = \"model.pkl\"\n", "MODEL_POINTER_LOCALPATH = \"current.json\"\n", "MODEL_METADATA_LOCALPATH = \"metadata.json\"\n", "\n", "\n", "AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"model cloud path:\", MODEL_CLOUDPATH)\n", "print(\"model metadata path:\", MODEL_METADATA_CLOUDPATH)\n", "print(\"model pointer:\", MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_pointer = {\n", "    \"created_at\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"created_by\": AUTH<PERSON>,\n", "    \"bucket_name\": BUCKET_NAME,\n", "    \"model_path\": MODEL_CLOUDPATH,\n", "    \"metadata_path\": MODEL_METADATA_CLOUDPATH,\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_type\": model_type,\n", "}\n", "\n", "model_metadata = model_pointer.copy()\n", "model_metadata[\"darkstore_ids\"] = training_data.darkstore_id.unique().tolist()\n", "model_metadata[\"model_params\"] = model_params\n", "model_metadata[\"model_test_mae\"] = model_logs[\"model_test_mae\"]\n", "\n", "\n", "model_logs[\"s3_bucket\"] = BUCKET_NAME\n", "model_logs[\"s3_path\"] = MODEL_CLOUDPATH\n", "model_logs_df = pd.DataFrame([OrderedDict(model_logs)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dump model locally\n", "joblib.dump(model, MODEL_LOCALPATH, compress=1)\n", "\n", "# model metadata\n", "with open(MODEL_METADATA_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_metadata, fp)\n", "\n", "# model pointer\n", "with open(MODEL_POINTER_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_pointer, fp)\n", "\n", "# push model to s3\n", "pb.to_s3(MODEL_LOCALPATH, BUCKET_NAME, MODEL_CLOUDPATH)\n", "\n", "# push metadata to s3 (darkstore ids trained on)\n", "pb.to_s3(MODEL_METADATA_LOCALPATH, BUCKET_NAME, MODEL_METADATA_CLOUDPATH)\n", "\n", "# push model pointer to s3 (current model filepath)\n", "pb.to_s3(MODEL_POINTER_LOCALPATH, BUCKET_NAME, MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(logs):\n", "\n", "    frame = logs.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"training_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"training data start date\",\n", "        },\n", "        {\n", "            \"name\": \"training_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"training data end date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Validation data start date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"validation data end date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"test data start date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"test data end date\",\n", "        },\n", "        {\n", "            \"name\": \"model_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"id of the generated model\",\n", "        },\n", "        {\n", "            \"name\": \"model_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"name of the model used for predicting the eta component\",\n", "        },\n", "        {\n", "            \"name\": \"model_version\",\n", "            \"type\": \"float\",\n", "            \"description\": \"version of the model\",\n", "        },\n", "        {\n", "            \"name\": \"model_subid\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"model subid is the date on which the model was generated\",\n", "        },\n", "        {\n", "            \"name\": \"model_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"type of the ml model\",\n", "        },\n", "        {\n", "            \"name\": \"speed_lower_bound\",\n", "            \"type\": \"float\",\n", "            \"description\": \"lower bound of speed below which eta fallbacks\",\n", "        },\n", "        {\n", "            \"name\": \"speed_upper_bound\",\n", "            \"type\": \"float\",\n", "            \"description\": \"upper bound of speed above which eta fallbacks\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"s3_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"bucket name where artifacts are stores\",\n", "        },\n", "        {\n", "            \"name\": \"s3_path\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"path inside the bucket where the model is stored\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"serviceability_travel_time_model_retraining_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"model_id\"],\n", "        \"sortkey\": [\"model_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Travel time estimation retraining pipeline logs\",\n", "    }\n", "\n", "    pb.to_redshift(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(model_logs_df) != 0:\n", "    push_to_redshift(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}