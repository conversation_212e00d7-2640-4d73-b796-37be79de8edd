{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip3 install -U scikit-learn scipy matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import date, datetime, timedelta\n", "from collections import OrderedDict\n", "import math\n", "import random\n", "import time\n", "import pytz\n", "import json\n", "import requests\n", "import os\n", "import pickle\n", "from sklearn.metrics import mean_absolute_error\n", "import pencilbox as pb\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE = (\n", "    datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=41)\n", ").strftime(\"%Y-%m-%d\")\n", "\n", "\n", "VALIDATION_DATE = (\n", "    datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=5)\n", ").strftime(\"%Y-%m-%d\")\n", "\n", "\n", "TEST_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=3)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "\n", "END_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "\n", "(START_DATE, VALIDATION_DATE, TEST_DATE, END_DATE)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE, END_DATE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_stores = pd.read_sql_query(\n", "    sql=\"\"\"select \n", "                                                * \n", "                                            from \n", "                                                lake_logistics.logistics_node\n", "                                            where\n", "                                                active = True\n", "                                                \"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_stores = active_stores[active_stores.type == \"DARK_STORE\"][[\"external_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_query = f\"\"\"\n", "SELECT order_checkout_ts_ist checked_out_time,\n", "       frontend_merchant_id AS dark_store_id,\n", "       order_id,\n", "       order_weight,\n", "       distinct_items_ordered AS sku,\n", "       total_items_quantity_ordered AS ipo,\n", "       order_picking_started_ts_ist AS picking_start,\n", "       order_picking_completed_ts_ist AS picking_end,\n", "       extract(seconds\n", "               FROM order_picking_completed_ts_ist-order_picking_started_ts_ist) AS picking_time_seconds\n", "FROM dwh.fact_supply_chain_order_details\n", "WHERE order_type = 'super_express'\n", "  AND order_current_status = 'DELIVERED'\n", "  AND is_order_cancelled = FALSE\n", "  AND is_order_fully_filled = TRUE\n", "  AND order_checkout_ts_ist >= '{START_DATE}'\n", "  AND order_checkout_ts_ist < '{END_DATE}'\n", " \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.read_sql_query(sql=picking_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# logger.info(f\"total rows fetched {raw_data.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = raw_data.astype({\"order_id\": \"int64\", \"dark_store_id\": \"int64\"})\n", "raw_data.rename({\"picking_time_seconds\": \"picking_time\"}, axis=1, inplace=True)\n", "\n", "\n", "raw_data = raw_data.dropna().reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_training_data = raw_data.query(f\"(checked_out_time <'{VALIDATION_DATE}') \")\n", "\n", "\n", "validation_data = raw_data.query(\n", "    f\"((checked_out_time >= '{VALIDATION_DATE}') & (checked_out_time <= '{TEST_DATE}') )\"\n", ")\n", "\n", "test_data = raw_data.query(f\"(checked_out_time > '{TEST_DATE}')\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["duplicates = raw_training_data.duplicated([\"order_id\"])\n", "print(\"Number of duplicate rows = \" + str(duplicates.sum()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sum(duplicates) > 0:\n", "    raw_training_data = raw_training_data.drop_duplicates([\"order_id\"]).reset_index(\n", "        drop=True\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["errorneous_data_points = raw_training_data.query(\"sku > ipo or picking_time <0\")\n", "raw_training_data = raw_training_data.query(\n", "    f\"order_id not in {tuple(errorneous_data_points.order_id.unique())}\"\n", ")\n", "\n", "print(\n", "    f\"% data points having sku > ipo is {round(len(errorneous_data_points)/len(raw_training_data)*100.0,2)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if sku and ipo less than the threshold and picking time > cutoff then the points are outliers\n", "PICKING_TIME_CUTOFF = 1032\n", "IPO_THRESHOLD = 8\n", "SKU_THRESHOLD = 6\n", "\n", "\n", "# Upper bounds for ipo, sku and picking time\n", "WEIGHT_THRESHOLD = 66000\n", "IPO_UPPER_BOUND = 70\n", "SKU_UPPER_BOUD = 52\n", "PICKING_TIME_UPPER_BOUND = 1600\n", "\n", "\n", "outliers = raw_training_data.query(\n", "    f\"(sku <={SKU_THRESHOLD} & ipo <={IPO_THRESHOLD}\\\n", "    & picking_time >={PICKING_TIME_CUTOFF}) \\\n", "    or order_weight>={WEIGHT_THRESHOLD} \\\n", "    or picking_time > {PICKING_TIME_UPPER_BOUND}\"\n", ")\n", "\n", "outlier_oids = outliers.order_id.unique()\n", "cleaned_training_data = raw_training_data.query(\n", "    f\"order_id not in {tuple(outlier_oids)}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data = cleaned_training_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data[[\"sku\", \"ipo\", \"order_weight\", \"picking_time\"]].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_data[[\"sku\", \"ipo\", \"order_weight\", \"picking_time\"]].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[[\"sku\", \"ipo\", \"order_weight\", \"picking_time\"]].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression\n", "\n", "model = LinearRegression()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def lm(training_data, test_data):\n", "    test_data = test_data[\n", "        test_data.dark_store_id == training_data.dark_store_id.unique()[0]\n", "    ]\n", "    model = LinearRegression()\n", "    model.fit(training_data[[\"sku\"]], training_data[[\"picking_time\"]])\n", "    r_sq = model.score(training_data[[\"sku\"]], training_data[[\"picking_time\"]])\n", "    if test_data.empty == False:\n", "        y_pred = model.predict(test_data[[\"sku\"]])\n", "        lr_test_mae = mean_absolute_error(test_data[[\"picking_time\"]], y_pred)\n", "    else:\n", "        lr_test_mae = 0\n", "\n", "    return r_sq, model.intercept_, model.coef_, lr_test_mae"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = training_data.groupby(\"dark_store_id\").apply(lambda x: lm(x, test_data))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_df = model.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_df = model_df.assign(\n", "    r_sq=model_df.apply(lambda x: x[0][0], axis=1),\n", "    intercept=model_df.apply(lambda x: x[0][1][0], axis=1),\n", "    coef=model_df.apply(lambda x: x[0][2][0][0], axis=1),\n", "    lr_test_mae=model_df.apply(lambda x: x[0][3], axis=1),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_df = model_df[~((model_df.r_sq < 0.4) | (model_df.lr_test_mae > 120))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_df.loc[model_df.intercept >= 35, \"intercept\"] = 35\n", "model_df.loc[model_df.intercept <= 10, \"intercept\"] = 10"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Model output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_df = model_df[model_df.coef.between(10, 50)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Picking constant bulk upload"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_all_store_ids():\n", "    url = \"http://serviceability-serviceability.prod-sgp-k8s.grofer.io/api/v1/nodes/\"\n", "    payload_dict = {\"node_type\": \"MERCHANT\"}\n", "    payload = json.dumps(payload_dict)\n", "    headers = {\n", "        \"content-type\": \"application/json\",\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, data=payload, headers=headers)\n", "    store_ids = [x[\"node_id\"] for x in response.json()[\"nodes\"]]\n", "    return store_ids\n", "\n", "\n", "def update_store_config(records):\n", "    url = \"http://serviceability-serviceability.prod-sgp-k8s.grofer.io/api/v1/store_config/bulk_update/\"\n", "    payload_dict = {\"store_config\": records}\n", "    payload = json.dumps(payload_dict)\n", "\n", "    headers = {\n", "        \"content-type\": \"application/json\",\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, data=payload, headers=headers)\n", "    if response.status_code >= 200 and response.status_code < 300:\n", "        return \"Success\"\n", "        print(\"success\")\n", "    else:\n", "        print(response.text, response.status_code)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_df = model_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# config_df = pd.read_csv(\"28-jul.csv\")\n", "# config_df.head(2)\n", "config_list = config_df.to_dict(\"records\")\n", "\n", "\n", "new_config_list = []\n", "store_ids_list = []\n", "constant_picking_duration_sum = 0\n", "picking_duration_per_sku_sum = 0\n", "count = 0\n", "for config in config_list:\n", "    # if config['dark_store_id'] == 30212:\n", "    #     continue\n", "    store_ids_list.append(config[\"dark_store_id\"])\n", "    new_config_list.append(\n", "        {\n", "            \"merchant_id\": config[\"dark_store_id\"],\n", "            \"constant_picking_duration\": config[\"intercept\"] / 60,\n", "            \"picking_duration_per_sku\": config[\"coef\"] / 60,\n", "        }\n", "    )\n", "    constant_picking_duration_sum += config[\"intercept\"] / 60\n", "    picking_duration_per_sku_sum += config[\"coef\"] / 60\n", "    count += 1\n", "constant_picking_duration_mean = constant_picking_duration_sum / count\n", "picking_duration_per_sku_mean = picking_duration_per_sku_sum / count\n", "\n", "all_store_ids = get_all_store_ids()\n", "remaining_store_ids = []\n", "for store_id in all_store_ids:\n", "    if int(store_id) not in store_ids_list:\n", "        new_config_list.append(\n", "            {\n", "                \"merchant_id\": int(store_id),\n", "                \"constant_picking_duration\": constant_picking_duration_mean,\n", "                \"picking_duration_per_sku\": picking_duration_per_sku_mean,\n", "            }\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}