{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Installations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["!pip install -q catboost==0.26.1\n", "!pip install -U scikit-learn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import mean_absolute_error\n", "from datetime import date, datetime, timedelta\n", "from catboost import CatBoostRegressor\n", "from collections import OrderedDict\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "import logging\n", "import random\n", "import joblib\n", "import math\n", "import time\n", "import pytz\n", "import json\n", "import os\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(format=\"%(asctime)s %(levelname)s:%(message)s\")\n", "logger = logging.getLogger(\"picking_model_retraining\")\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=31)\n", "\n", "\n", "validation_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=5)\n", "\n", "\n", "test_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=3)\n", "\n", "\n", "end_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")) - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "\n", "START_DATE, VALIDATION_DATE, TEST_DATE, END_DATE = (\n", "    str(start_date.date()),\n", "    str(validation_date.date()),\n", "    str(test_date.date()),\n", "    str(end_date.date()),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_query_result_by_checkpoint(\n", "    query,\n", "    start_date,\n", "    end_date,\n", "    step=10,\n", "    connection=\"[Warehouse] Redshift\",\n", "    variable_to_format={},\n", "):\n", "    if variable_to_format is None:\n", "        variable_to_format = {}\n", "\n", "    def get_date_checkpoints(start_date_, end_date_, step_):\n", "        iter_date = start_date_\n", "        date_list_ = []\n", "        while iter_date <= end_date_:\n", "            date_list_.append(iter_date)\n", "            iter_date += <PERSON><PERSON>ta(days=step_)\n", "        date_list_.append(end_date_)\n", "        return date_list_\n", "\n", "    date_list = get_date_checkpoints(start_date, end_date, step)\n", "    query_result = []\n", "\n", "    for i in range(len(date_list) - 1):\n", "        logger.info(f\" At iteration {i+1} of {len(date_list)-1}\")\n", "        sd = date_list[i].strftime(\"%Y-%m-%d\")\n", "        ed = date_list[i + 1].strftime(\"%Y-%m-%d\")\n", "\n", "        variable_to_format[\"start_date\"] = sd\n", "        variable_to_format[\"end_date\"] = ed\n", "\n", "        query_result_ = pd.read_sql(\n", "            query.format(**variable_to_format), con=pb.get_connection(connection)\n", "        )\n", "        query_result.append(query_result_)\n", "\n", "    return pd.concat(query_result).drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_query = \"\"\"\n", "SELECT order_checkout_ts_ist,\n", "       frontend_merchant_id AS dark_store_id,\n", "       order_id,\n", "       order_weight,\n", "       distinct_items_ordered AS sku,\n", "       total_items_quantity_ordered AS ipo,\n", "       order_picking_started_ts_ist AS picking_start,\n", "       order_picking_completed_ts_ist AS picking_end,\n", "       extract(seconds\n", "               FROM order_picking_completed_ts_ist-order_picking_started_ts_ist) AS picking_time_seconds\n", "FROM dwh.fact_supply_chain_order_details\n", "WHERE order_type = 'super_express'\n", "  AND order_current_status = 'DELIVERED'\n", "  AND is_order_cancelled = FALSE\n", "  AND is_order_fully_filled = TRUE\n", "  AND is_rescheduled = FALSE\n", "  AND order_checkout_ts_ist >= '{start_date}'\n", "  AND order_checkout_ts_ist < '{end_date}'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = get_query_result_by_checkpoint(picking_query, start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"total rows fetched {raw_data.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = raw_data.astype(\n", "    {\n", "        \"order_id\": \"int64\",\n", "        \"dark_store_id\": \"int64\",\n", "        \"order_checkout_ts_ist\": \"datetime64[ns]\",\n", "    }\n", ")\n", "raw_data.rename({\"picking_time_seconds\": \"picking_time\"}, axis=1, inplace=True)\n", "raw_data = raw_data.dropna().reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_training_data = raw_data.query(f\"order_checkout_ts_ist < '{VALIDATION_DATE}'\")\n", "\n", "\n", "validation_data = raw_data.query(\n", "    f\"order_checkout_ts_ist >= '{VALIDATION_DATE}' and order_checkout_ts_ist <= '{TEST_DATE}'\"\n", ")\n", "\n", "test_data = raw_data.query(f\"order_checkout_ts_ist > '{TEST_DATE}'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["duplicates = raw_training_data.duplicated([\"order_id\"])\n", "print(\"Number of duplicate rows = \" + str(duplicates.sum()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sum(duplicates) > 0:\n", "    raw_training_data = raw_training_data.drop_duplicates([\"order_id\"]).reset_index(\n", "        drop=True\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["errorneous_data_points = raw_training_data.query(\"sku > ipo or picking_time <0\")\n", "raw_training_data = raw_training_data.query(\n", "    f\"order_id not in {tuple(errorneous_data_points.order_id.unique())}\"\n", ")\n", "\n", "print(\n", "    f\"% data points having sku > ipo is {round(len(errorneous_data_points)/len(raw_training_data)*100.0,2)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if sku and ipo less than the threshold and picking time > cutoff then the points are outliers\n", "PICKING_TIME_CUTOFF = 1032\n", "IPO_THRESHOLD = 8\n", "SKU_THRESHOLD = 6\n", "\n", "\n", "# Upper bounds for ipo, sku and picking time\n", "WEIGHT_THRESHOLD = 66 * 1000\n", "IPO_UPPER_BOUND = 70\n", "SKU_UPPER_BOUD = 52\n", "PICKING_TIME_UPPER_BOUND = 1600\n", "\n", "cleaned_training_data = raw_training_data.query(\n", "    f\"order_weight<{WEIGHT_THRESHOLD} \\\n", "    and picking_time < {PICKING_TIME_UPPER_BOUND}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cleaned_training_data.shape, raw_training_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mode training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# training_data = cleaned_training_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["categorical_features = [\"dark_store_id\"]\n", "continous_features = [\"sku\"]\n", "features = categorical_features + continous_features\n", "dependent_variable = \"picking_time\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_params = {\n", "    \"n_estimators\": 1361,\n", "    \"loss_function\": \"Quantile:alpha=0.25\",\n", "    \"learning_rate\": 0.4720809949314063,\n", "    \"l2_leaf_reg\": 1,\n", "    \"depth\": 3,\n", "}\n", "\n", "clf = CatBoostRegressor(\n", "    **model_params,\n", "    cat_features=categorical_features,\n", "    custom_metric=[\"MAE\", \"R2\", \"RMSE\"],\n", "    verbose=200,\n", "    train_dir=\"/tmp/logs\",\n", "    early_stopping_rounds=500,\n", "    thread_count=2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_splits = np.array_split(\n", "    cleaned_training_data, max(cleaned_training_data.shape[0] // 100000, 1)\n", ")\n", "\n", "for i, training_chunk in enumerate(training_splits):\n", "    print(f\"Training on chunk {i+1} of {len(training_splits)}\")\n", "    if i == 0:\n", "        clf.fit(\n", "            training_chunk[features],\n", "            training_chunk[dependent_variable],\n", "            eval_set=(validation_data[features], validation_data[dependent_variable]),\n", "        )\n", "    else:\n", "        clf.fit(\n", "            training_chunk[features],\n", "            training_chunk[dependent_variable],\n", "            eval_set=(validation_data[features], validation_data[dependent_variable]),\n", "            init_model=\"model.cbm\",\n", "        )\n", "    clf.save_model(\"model.cbm\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metrics on train, validation and test data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_baseline_prediction(x):\n", "    return (0.2936844801503067 + 0.3558305 * x) * 60"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cleaned_training_data[\"baseline_prediction\"] = cleaned_training_data.sku.apply(\n", "    get_baseline_prediction\n", ")\n", "\n", "validation_data[\"baseline_prediction\"] = validation_data.sku.apply(\n", "    get_baseline_prediction\n", ")\n", "\n", "test_data[\"baseline_prediction\"] = test_data.sku.apply(get_baseline_prediction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["clf.predict(test_data[features][:10]).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data_splits = np.array_split(test_data, max(1, test_data.shape[0] // 100000))\n", "clf_model_predictions = []\n", "for i, test_split in enumerate(test_data_splits):\n", "    logger.info(f\"Predicting Split {i+1}/{len(test_data_splits)}\")\n", "    clf_model_predictions += clf.predict(test_split[features]).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"cb_regressor_prediction\"] = clf_model_predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["baseline_train_mae = mean_absolute_error(\n", "    cleaned_training_data[\"picking_time\"], cleaned_training_data[\"baseline_prediction\"]\n", ")\n", "baseline_validation_mae = mean_absolute_error(\n", "    validation_data[\"picking_time\"], validation_data[\"baseline_prediction\"]\n", ")\n", "baseline_test_mae = mean_absolute_error(\n", "    test_data[\"picking_time\"], test_data[\"baseline_prediction\"]\n", ")\n", "\n", "\n", "model_test_mae = mean_absolute_error(\n", "    test_data[\"picking_time\"], test_data[\"cb_regressor_prediction\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Baseline model MAE: \", baseline_test_mae)\n", "\n", "\n", "print(\n", "    \"CB Regressor MAE with : \",\n", "    model_test_mae,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"cb_regressor_prediction\"].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Log performance and execution params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_version = 1.0\n", "model_subid = datetime.now().strftime(\"%Y%m%d\")\n", "\n", "model_name = \"se_picking_time_estimation\"\n", "model_id = \"{}_v{}_{}\".format(model_name, model_version, model_subid)\n", "model_type = \"catboost_regressor\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs = {\n", "    \"training_data_start_date\": cleaned_training_data.order_checkout_ts_ist.min(),\n", "    \"training_data_end_date\": cleaned_training_data.order_checkout_ts_ist.max(),\n", "    \"validation_data_start_date\": validation_data.order_checkout_ts_ist.min(),\n", "    \"validation_data_end_date\": validation_data.order_checkout_ts_ist.max(),\n", "    \"test_data_start_date\": test_data.order_checkout_ts_ist.min(),\n", "    \"test_data_end_date\": test_data.order_checkout_ts_ist.max(),\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_subid\": model_subid,\n", "    \"model_type\": model_type,\n", "    \"model_train_mae\": round(clf.get_best_score()[\"learn\"][\"MAE\"], 2),\n", "    \"model_validation_mae\": round(clf.get_best_score()[\"validation\"][\"MAE\"], 2),\n", "    \"model_test_mae\": round(model_test_mae, 2),\n", "    \"baseline_train_mae\": round(baseline_train_mae, 2),\n", "    \"baseline_validation_mae\": round(baseline_validation_mae, 2),\n", "    \"baseline_test_mae\": round(baseline_test_mae, 2),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Push the model to s3 and logs to redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BUCKET_NAME = \"prod-dse-projects\"\n", "MODEL_CLOUDPATH = (\n", "    \"/eta_prediction/picking_time_estimation/models/{}/{}/model.pkl\".format(\n", "        \"v1.0\", model_subid\n", "    )\n", ")\n", "MODEL_METADATA_CLOUDPATH = (\n", "    \"/eta_prediction/picking_time_estimation/models/{}/{}/metadata.json\".format(\n", "        \"v1.0\", model_subid\n", "    )\n", ")\n", "MODEL_POINTER_CLOUDPATH = \"/eta_prediction/picking_time_estimation/models/current.json\"\n", "\n", "MODEL_LOCALPATH = \"/tmp/model.pkl\"\n", "MODEL_POINTER_LOCALPATH = \"/tmp/current.json\"\n", "MODEL_METADATA_LOCALPATH = \"/tmp/metadata.json\"\n", "\n", "\n", "AUTHOR = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"model cloud path:\", MODEL_CLOUDPATH)\n", "print(\"model metadata path:\", MODEL_METADATA_CLOUDPATH)\n", "print(\"model pointer:\", MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_pointer = {\n", "    \"created_at\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"created_by\": AUTH<PERSON>,\n", "    \"bucket_name\": BUCKET_NAME,\n", "    \"model_path\": MODEL_CLOUDPATH,\n", "    \"metadata_path\": MODEL_METADATA_CLOUDPATH,\n", "    \"model_id\": model_id,\n", "    \"model_name\": model_name,\n", "    \"model_version\": model_version,\n", "    \"model_type\": model_type,\n", "}\n", "\n", "model_metadata = model_pointer.copy()\n", "model_metadata[\"darkstore_ids\"] = cleaned_training_data.dark_store_id.unique().tolist()\n", "model_metadata[\"model_params\"] = model_params\n", "model_metadata[\"model_test_mae\"] = model_logs[\"model_test_mae\"]\n", "model_metadata[\"baseline_test_mae\"] = model_logs[\"baseline_test_mae\"]\n", "\n", "\n", "model_logs[\"s3_bucket\"] = BUCKET_NAME\n", "model_logs[\"s3_path\"] = MODEL_CLOUDPATH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dump model locally\n", "joblib.dump(clf, MODEL_LOCALPATH)\n", "\n", "# model metadata\n", "with open(MODEL_METADATA_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_metadata, fp)\n", "\n", "# model pointer\n", "with open(MODEL_POINTER_LOCALPATH, \"w\") as fp:\n", "    json.dump(model_pointer, fp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# push model to s3\n", "pb.to_s3(MODEL_LOCALPATH, BUCKET_NAME, MODEL_CLOUDPATH)\n", "\n", "# push metadata to s3 (darkstore ids trained on)\n", "pb.to_s3(MODEL_METADATA_LOCALPATH, BUCKET_NAME, MODEL_METADATA_CLOUDPATH)\n", "\n", "# push model pointer to s3 (current model filepath)\n", "pb.to_s3(MODEL_POINTER_LOCALPATH, BUCKET_NAME, MODEL_POINTER_CLOUDPATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs_df = pd.DataFrame([OrderedDict(model_logs)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_logs_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(logs):\n", "\n", "    frame = logs.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"training_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"training data start date\",\n", "        },\n", "        {\n", "            \"name\": \"training_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"training data end date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Validation data start date\",\n", "        },\n", "        {\n", "            \"name\": \"validation_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"validation data end date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_start_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"test data start date\",\n", "        },\n", "        {\n", "            \"name\": \"test_data_end_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"test data end date\",\n", "        },\n", "        {\n", "            \"name\": \"model_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"id of the generated model\",\n", "        },\n", "        {\n", "            \"name\": \"model_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"name of the model used for predicting the eta component\",\n", "        },\n", "        {\n", "            \"name\": \"model_version\",\n", "            \"type\": \"float\",\n", "            \"description\": \"version of the model\",\n", "        },\n", "        {\n", "            \"name\": \"model_subid\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"model subid is the date on which the model was generated\",\n", "        },\n", "        {\n", "            \"name\": \"model_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"type of the ml model\",\n", "        },\n", "        {\n", "            \"name\": \"model_train_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"model_validation_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"model_test_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_train_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of baseline model on training data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_validation_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of baseline model on validation data\",\n", "        },\n", "        {\n", "            \"name\": \"baseline_test_mae\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean absolute error of baseline model on test data\",\n", "        },\n", "        {\n", "            \"name\": \"s3_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"bucket name where artifacts are stores\",\n", "        },\n", "        {\n", "            \"name\": \"s3_path\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"path inside the bucket where the model is stored\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"serviceability_picking_time_model_retraining_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"model_id\"],\n", "        \"sortkey\": [\"model_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Picking time estimation retraining pipeline logs\",\n", "    }\n", "\n", "    pb.to_redshift(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(model_logs_df) != 0:\n", "    push_to_redshift(model_logs_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}