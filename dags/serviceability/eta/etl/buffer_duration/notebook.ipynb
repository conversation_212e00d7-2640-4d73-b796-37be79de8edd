{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["### `Components`\n", "     1. Super Express Store eta components statistic\n", "         i) Outlier removal using inter-quartile range\n", "         ii) Currently using 50th percentile as the statistic for each eta component\n", "         iii) Publish data to gsheet\n", "\n", "\n", "     2. Super Express Store gsp speed statistic\n", "         i) Remove data points where in the gsp coordinates are quite away from customer coordinates since it impacts the delivery timestamp\n", "         ii) Remove data points where the gsp speed is too errorneous using lower and upper bound on speed\n", "         iii) Publish data to gsheet\n", "         \n", "         \n", "     3. Push the statistics to redshift         "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "import random\n", "import os\n", "import pytz\n", "\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "import time\n", "import requests\n", "\n", "import pencilbox as pb\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=3)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "END_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\"))).strftime(\"%Y-%m-%d\")\n", "\n", "(START_DATE, END_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\"\n", "\n", "DOORSTEP_DISTANCE_OUTLIER_CUTOFF = 0.8  # in kms\n", "SPEED_LOWER_BOUND = 2  # km/hr\n", "SPEED_UPPER_BOUND = 60  # km/hr\n", "\n", "SHEET_ID = \"1Okb_aT8yXITnNERyqK_pe-22TveCd6DzmTAHDz_Am-g\"\n", "ETA_SHEET_NAME = \"v0_superexpress_config\"\n", "SPEED_SHEET_NAME = \"v0_speed_config\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_query = f\"\"\"\n", "select order_delivered_ts_ist::date as sch_date,\n", "fs.frontend_merchant_id as dark_store_id,\n", "fs.order_id,\n", "fs.order_type,\n", "max(datediff(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist)) as checkedout_to_picker_assigned,\n", "max(datediff(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist)) as picker_assigned_to_picking_start,\n", "max(datediff(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist)) as picking_time,\n", "max(datediff(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist)) as picked_to_billing_start,\n", "max(datediff(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist)) as billing_time,\n", "max(datediff(seconds,order_billing_completed_ts_ist,order_enroute_ts_ist)) as billed_to_enroute,\n", "max(datediff(seconds,order_partner_assigned_ts_ist,order_enroute_ts_ist)) as partner_assigned_to_enroute,\n", "max(datediff(seconds,order_enroute_ts_ist,order_delivered_ts_ist)) as delivery_time\n", "from dwh.fact_supply_chain_order_details fs\n", "inner join dwh.fact_sales_order_details f on fs.order_id = f.order_id\n", "and fs.is_rescheduled = False\n", "and f.is_internal_order = False\n", "and fs.order_delivered_ts_ist::date between '{START_DATE}'::timestamp and '{END_DATE}'::timestamp\n", "group by 1,2,3,4\n", "\"\"\".replace(\n", "    \"%\", \"%%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.read_sql_query(sql=store_level_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data[\"sch_date\"] = pd.to_datetime(raw_data[\"sch_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_data = raw_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Number of darkstores are {store_level_data.dark_store_id.nunique()}\")\n", "print(f\"Type of services are {store_level_data.order_type.unique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_bussiness_type = (\n", "    store_level_data.groupby([\"dark_store_id\", \"order_type\"])\n", "    .agg({\"order_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename({\"order_id\": \"num_orders\"}, axis=1)\n", ")\n", "store_bussiness_type.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_orders = (\n", "    store_level_data.query(\"order_type in ('super_express',)\")\n", "    .dropna()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"# stores doing super express are {super_express_orders.dark_store_id.nunique()}\"\n", ")\n", "\n", "print(f\"# super express orders are {super_express_orders.order_id.nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Super Express Orders (30 mins)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_branches = [\n", "    \"checkedout_to_picker_assigned\",\n", "    \"picker_assigned_to_picking_start\",\n", "    \"picking_time\",\n", "    \"picked_to_billing_start\",\n", "    \"billing_time\",\n", "    \"billed_to_enroute\",\n", "    \"partner_assigned_to_enroute\",\n", "    \"delivery_time\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_orders[eta_branches].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Removing outliers in Super Express eta components"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def remove_outliers(se_orders, eta_components):\n", "\n", "    cleaned_df = pd.DataFrame(\n", "        {\n", "            \"sch_date\": se_orders.sch_date.values,\n", "            \"dark_store_id\": se_orders.dark_store_id.values,\n", "            \"order_id\": se_orders.order_id.values,\n", "        }\n", "    )\n", "\n", "    for col in eta_components:\n", "        df = se_orders.copy()\n", "        cols_lst = [\"order_id\", col]\n", "        threshold = 1.5\n", "\n", "        q25 = np.percentile(df[col].values, 25)\n", "        q75 = np.percentile(df[col].values, 75)\n", "        iqr = q75 - q25\n", "        lower_bound = max(q25 - threshold * iqr, 0)\n", "        upper_bound = q75 + threshold * iqr\n", "\n", "        if col == \"picked_to_billing_start\":\n", "            lower_bound = -1\n", "            upper_bound = np.percentile(df[col].values, 99.99)\n", "        tmp = df.query(f\"{col} > {lower_bound} and {col} < {upper_bound}\")\n", "        print(\n", "            \"Min-Max outlier bounds for {} are ({} , {})\".format(\n", "                col, lower_bound, upper_bound\n", "            )\n", "        )\n", "        print(\"*\" * 50)\n", "        print(\"\\n\")\n", "\n", "        cleaned_df = cleaned_df.merge(\n", "            tmp[cols_lst], how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", "        )\n", "\n", "    return cleaned_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_components_df = remove_outliers(super_express_orders, eta_branches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def get_confidence_interval(df, col):\n", "\n", "#     for k, v in alphas.items():\n", "#         lower_percentile, upper_percentile = v / 2, 100 - v / 2\n", "#         x1 = np.percentile(df[col].values, lower_percentile)\n", "#         x2 = np.percentile(df[col].values, upper_percentile)\n", "#         ci = \"[{x1} ,{x2}]\"\n", "#         print(f\"For {col} the {k}% CI is [{x1} ,{x2}]\")\n", "#         print(\"*\" * 50)\n", "\n", "#     std = np.std(df[col].values)\n", "#     median = np.std(df[col].values)\n", "#     mean = np.mean(df[col].values)\n", "#     print(f\"The std deviation for {col} is {std}\")\n", "#     print(f\"The median for {col} is {median}\")\n", "#     print(f\"The mean for {col} is {mean}\")\n", "#     print(\"*\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_components_df = eta_components_df.astype({\"dark_store_id\": \"int64\"})\n", "statistic = lambda x: x.median(skipna=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics = (\n", "    eta_components_df.groupby(\"dark_store_id\")\n", "    .agg(\n", "        {\n", "            \"checkedout_to_picker_assigned\": statistic,\n", "            \"picker_assigned_to_picking_start\": statistic,\n", "            \"picking_time\": statistic,\n", "            \"picked_to_billing_start\": statistic,\n", "            \"billing_time\": statistic,\n", "            \"billed_to_enroute\": statistic,\n", "            \"partner_assigned_to_enroute\": statistic,\n", "            \"delivery_time\": statistic,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics[eta_branches].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.rename({\"dark_store_id\": \"darkstore_id\"}, inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics[\n", "    [\"picker_assigned_to_picking_start\", \"picked_to_billing_start\"]\n", "] = eta_statistics[\n", "    [\"picker_assigned_to_picking_start\", \"picked_to_billing_start\"]\n", "].fillna(\n", "    0\n", ")\n", "eta_statistics.head()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Publish revised eta components"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(eta_statistics, SHEET_ID, ETA_SHEET_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}