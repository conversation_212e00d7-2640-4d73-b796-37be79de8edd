{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["### `Components`\n", "     1. Super Express Store eta components statistic\n", "         i) Outlier removal using inter-quartile range\n", "         ii) Currently using 50th percentile as the statistic for each eta component\n", "         iii) Publish data to gsheet\n", "\n", "\n", "     2. Super Express Store gsp speed statistic\n", "         i) Remove data points where in the gsp coordinates are quite away from customer coordinates since it impacts the delivery timestamp\n", "         ii) Remove data points where the gsp speed is too errorneous using lower and upper bound on speed\n", "         iii) Publish data to gsheet\n", "         \n", "         \n", "     3. Push the statistics to redshift         "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "import random\n", "import os\n", "import pytz\n", "\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "import time\n", "import requests\n", "\n", "import pencilbox as pb\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=3)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "END_DATE = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) - timedelta(days=1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "(START_DATE, END_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\"\n", "\n", "DOORSTEP_DISTANCE_OUTLIER_CUTOFF = 0.8  # in kms\n", "SPEED_LOWER_BOUND = 2  # km/hr\n", "SPEED_UPPER_BOUND = 60  # km/hr\n", "\n", "SHEET_ID = \"1Okb_aT8yXITnNERyqK_pe-22TveCd6DzmTAHDz_Am-g\"\n", "ETA_SHEET_NAME = \"v0_superexpress_config\"\n", "SPEED_SHEET_NAME = \"v0_speed_config\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_query = f\"\"\"\n", "select order_delivered_ts_ist::date as sch_date,\n", "fs.frontend_merchant_id as dark_store_id,\n", "fs.order_id,\n", "fs.order_type,\n", "max(datediff(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist)) as checkedout_to_picker_assigned,\n", "max(datediff(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist)) as picker_assigned_to_picking_start,\n", "max(datediff(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist)) as picking_time,\n", "max(datediff(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist)) as picked_to_billing_start,\n", "max(datediff(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist)) as billing_time,\n", "max(datediff(seconds,order_billing_completed_ts_ist,order_enroute_ts_ist)) as billed_to_enroute,\n", "max(datediff(seconds,order_partner_assigned_ts_ist,order_enroute_ts_ist)) as partner_assigned_to_enroute,\n", "max(datediff(seconds,order_enroute_ts_ist,order_delivered_ts_ist)) as delivery_time\n", "from dwh.fact_supply_chain_order_details fs\n", "inner join dwh.fact_sales_order_details f on fs.order_id = f.order_id\n", "and fs.is_rescheduled = False\n", "and f.is_internal_order = False\n", "and fs.order_delivered_ts_ist::date between '{START_DATE}'::timestamp and '{END_DATE}'::timestamp\n", "group by 1,2,3,4\n", "\"\"\".replace(\n", "    \"%\", \"%%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.read_sql_query(sql=store_level_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data[\"sch_date\"] = pd.to_datetime(raw_data[\"sch_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_data = raw_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_level_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Number of darkstores are {store_level_data.dark_store_id.nunique()}\")\n", "print(f\"Type of services are {store_level_data.order_type.unique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_bussiness_type = (\n", "    store_level_data.groupby([\"dark_store_id\", \"order_type\"])\n", "    .agg({\"order_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename({\"order_id\": \"num_orders\"}, axis=1)\n", ")\n", "store_bussiness_type.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_orders = (\n", "    store_level_data.query(\"order_type in ('super_express',)\")\n", "    .dropna()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"# stores doing super express are {super_express_orders.dark_store_id.nunique()}\"\n", ")\n", "\n", "print(f\"# super express orders are {super_express_orders.order_id.nunique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_orders.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Super Express Orders (30 mins)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_branches = [\n", "    \"checkedout_to_picker_assigned\",\n", "    \"picker_assigned_to_picking_start\",\n", "    \"picking_time\",\n", "    \"picked_to_billing_start\",\n", "    \"billing_time\",\n", "    \"billed_to_enroute\",\n", "    \"partner_assigned_to_enroute\",\n", "    \"delivery_time\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_orders[eta_branches].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Removing outliers in Super Express eta components"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def remove_outliers(se_orders, eta_components):\n", "\n", "    cleaned_df = pd.DataFrame(\n", "        {\n", "            \"sch_date\": se_orders.sch_date.values,\n", "            \"dark_store_id\": se_orders.dark_store_id.values,\n", "            \"order_id\": se_orders.order_id.values,\n", "        }\n", "    )\n", "\n", "    for col in eta_components:\n", "        df = se_orders.copy()\n", "        cols_lst = [\"order_id\", col]\n", "        threshold = 1.5\n", "\n", "        q25 = np.percentile(df[col].values, 25)\n", "        q75 = np.percentile(df[col].values, 75)\n", "        iqr = q75 - q25\n", "        lower_bound = max(q25 - threshold * iqr, 5)\n", "        upper_bound = q75 + threshold * iqr\n", "\n", "        if col == \"picked_to_billing_start\":\n", "            lower_bound = -1\n", "            upper_bound = np.percentile(df[col].values, 99.99)\n", "        tmp = df.query(f\"{col} > {lower_bound} and {col} < {upper_bound}\")\n", "        print(\n", "            \"Min-Max outlier bounds for {} are ({} , {})\".format(\n", "                col, lower_bound, upper_bound\n", "            )\n", "        )\n", "        print(\"*\" * 50)\n", "        print(\"\\n\")\n", "\n", "        cleaned_df = cleaned_df.merge(\n", "            tmp[cols_lst], how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", "        )\n", "\n", "    return cleaned_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_components_df = remove_outliers(super_express_orders, eta_branches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_components_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def get_confidence_interval(df, col):\n", "\n", "#     for k, v in alphas.items():\n", "#         lower_percentile, upper_percentile = v / 2, 100 - v / 2\n", "#         x1 = np.percentile(df[col].values, lower_percentile)\n", "#         x2 = np.percentile(df[col].values, upper_percentile)\n", "#         ci = \"[{x1} ,{x2}]\"\n", "#         print(f\"For {col} the {k}% CI is [{x1} ,{x2}]\")\n", "#         print(\"*\" * 50)\n", "\n", "#     std = np.std(df[col].values)\n", "#     median = np.std(df[col].values)\n", "#     mean = np.mean(df[col].values)\n", "#     print(f\"The std deviation for {col} is {std}\")\n", "#     print(f\"The median for {col} is {median}\")\n", "#     print(f\"The mean for {col} is {mean}\")\n", "#     print(\"*\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_components_df = eta_components_df.astype({\"dark_store_id\": \"int64\"})\n", "statistic = lambda x: x.median(skipna=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics = (\n", "    eta_components_df.groupby(\"dark_store_id\")\n", "    .agg(\n", "        {\n", "            \"checkedout_to_picker_assigned\": statistic,\n", "            \"picker_assigned_to_picking_start\": statistic,\n", "            \"picking_time\": statistic,\n", "            \"picked_to_billing_start\": statistic,\n", "            \"billing_time\": statistic,\n", "            \"billed_to_enroute\": statistic,\n", "            \"partner_assigned_to_enroute\": statistic,\n", "            \"delivery_time\": statistic,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics[eta_branches].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.rename({\"dark_store_id\": \"darkstore_id\"}, inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Publish revised eta components"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(eta_statistics, SHEET_ID, ETA_SHEET_NAME)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Speed Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_trip_query = f\"\"\"\n", "        SELECT\n", "        ldsp.trip_id,\n", "        ldsp.current_delivery_start + interval '5.5 hour' AS slot_start,\n", "        ldsp.current_delivery_end + interval '5.5 hour' AS slot_end,\n", "        ldsp.order_id,\n", "        CASE\n", "            WHEN json_extract_path_text(ldsp.tags,'next_day_delivery',TRUE)='true' THEN 'next_day_delivery'\n", "            WHEN json_extract_path_text(ldsp.tags,'super_express',TRUE)='true'\n", "                 AND oo.install_ts::date=current_delivery_start::date THEN 'super_express'\n", "            ELSE 'normal'\n", "            END AS order_type,\n", "        ldsp.delivery_fe_id,\n", "        ldsp.dark_store_id as darkstore_id,\n", "        ldsp.trip_start_timestamp + interval '5.5 hour' as trip_start_timestamp,\n", "        ldsp.delivery_timestamp + interval '5.5 hour' as delivery_timestamp,\n", "        json_extract_path_text(lt.payload,'delivery_address','latitude',true) as customer_latitude,\n", "        json_extract_path_text(lt.payload,'delivery_address','longitude',true) as customer_longitude,\n", "        json_extract_path_text(lt.form_data,'doorstep_location','latitude',true) as doorstep_latitude,\n", "        json_extract_path_text(lt.form_data,'doorstep_location','longitude',true) as doorstep_longitude,\n", "        min(tml.update_ts + interval '5.5 hour') as reached_doorstep_time\n", "        FROM lake_logistics.view_logistics_dark_store_projection ldsp\n", "        inner join lake_logistics.logistics_task  lt on lt.shipment_label_value = ldsp.order_id and lt.type='ORDER_DELIVERY_TASK' \n", "        and lt.shipment_label_type = 'ORDER_ID'\n", "        inner join lake_logistics.task_management_taskstatelog tml on lt.id = tml.task_id and to_state = 'AT_DOORSTEP'\n", "        INNER JOIN lake_oms_bifrost.oms_order oo ON oo.id=ldsp.order_id\n", "        WHERE (oo.install_ts BETWEEN '{START_DATE}':: timestamp - interval '5.5 hours' \n", "        AND '{END_DATE}':: timestamp + interval '1 day' - interval '5.5 hours') \n", "        GROUP by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "        ORDER BY 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_location_query = \"\"\"\n", "SELECT lna.install_ts::date,\n", "       ln.external_id AS darkstore_id,\n", "       ln.external_name AS superstore_name,\n", "       lna.latitude AS latitude,\n", "       lna.longitude AS longitude,\n", "       lna.formatted_address AS address\n", "FROM lake_logistics.logistics_node_address lna\n", "INNER JOIN lake_logistics.logistics_node ln ON ln.node_address_id = lna.id\n", "ORDER BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details = pd.read_sql_query(sql=gsp_trip_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_location = pd.read_sql(sql=ds_location_query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_location = ds_location.rename(\n", "    {\"latitude\": \"ds_latitude\", \"longitude\": \"ds_longitude\"}, axis=1\n", ")\n", "ds_location.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details = trip_details.astype({\"darkstore_id\": \"int64\", \"order_id\": \"int64\"})\n", "ds_location = ds_location.astype({\"darkstore_id\": \"int64\"})\n", "\n", "trip_details = pd.merge(\n", "    trip_details,\n", "    ds_location[[\"darkstore_id\", \"ds_latitude\", \"ds_longitude\"]],\n", "    how=\"inner\",\n", "    left_on=\"darkstore_id\",\n", "    right_on=\"darkstore_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details[\"customer_latitude\"] = pd.to_numeric(\n", "    trip_details[\"customer_latitude\"], downcast=\"float\"\n", ")\n", "trip_details[\"customer_longitude\"] = pd.to_numeric(\n", "    trip_details[\"customer_longitude\"], downcast=\"float\"\n", ")\n", "\n", "trip_details[\"doorstep_latitude\"] = pd.to_numeric(\n", "    trip_details[\"doorstep_latitude\"], downcast=\"float\"\n", ")\n", "trip_details[\"doorstep_longitude\"] = pd.to_numeric(\n", "    trip_details[\"doorstep_longitude\"], downcast=\"float\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trip_details.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"Total number of trips b/w {START_DATE} and {END_DATE} are {trip_details.trip_id.nunique()}\"\n", ")\n", "\n", "print(\n", "    f\"Total number of orders b/w {START_DATE} and {END_DATE} are {trip_details.order_id.nunique()}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["na_rows = trip_details[\n", "    (trip_details[\"trip_id\"].isna())\n", "    & (trip_details[\"trip_start_timestamp\"].isna())\n", "    & (trip_details[\"delivery_timestamp\"].isna())\n", "]\n", "\n", "trip_starttime_na = trip_details[\n", "    (trip_details[\"trip_start_timestamp\"].isna())\n", "    & (~trip_details[\"order_id\"].isin(na_rows[\"order_id\"]))\n", "]\n", "\n", "deliverytime_na = trip_details[\n", "    (trip_details[\"delivery_timestamp\"].isna())\n", "    & (~trip_details[\"order_id\"].isin(na_rows[\"order_id\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(na_rows))\n", "print(len(trip_starttime_na))\n", "print(len(deliverytime_na))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1.  Trip start time for all order_ids is the same therefore delivery end_time for first order needs to be taken as the start_time for the second order. \n", "2. As can be seen from the above example, some order_ids in a trip do not have the delivery_timestamp. With this missing data we do not know when was this order delivered and it's sequence. We cannot also directly remoive this particular order_id with missing data because removing only this would result in wrong eta calculation for subsequent order_ids\n", "3. Therefore, removing the entire trip for which any time_stamp is missing (also % missing data is too less, so we can go ahead and remove the trip_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["na_timestamp_trip_ids = list(deliverytime_na[\"trip_id\"].values) + list(\n", "    trip_starttime_na[\"trip_id\"].values\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trips = trip_details.query(\n", "    f\"(trip_id not in {na_timestamp_trip_ids}) and (trip_id==trip_id)\"\n", ").reset_index(drop=True)\n", "\n", "trips = trips[~trips.customer_latitude.isna()].reset_index(drop=True)\n", "trips = trips.sort_values(by=[\"trip_id\", \"delivery_timestamp\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["duplicates = trips.duplicated([\"trip_id\", \"order_id\"])\n", "print(\"Number of duplicate rows = \" + str(duplicates.sum()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trips = trips.drop_duplicates([\"trip_id\", \"order_id\"], keep=\"last\").reset_index(\n", "    drop=True\n", ")\n", "print(\n", "    f\"% order_ids lost is {(trip_details.order_id.nunique() - trips.order_id.nunique())/trip_details.order_id.nunique()*100.0}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trips[\"origin_latitude\"] = trips.groupby([\"trip_id\"])[\"customer_latitude\"].shift(1)\n", "trips[\"origin_latitude\"] = trips.apply(\n", "    lambda x: x[\"ds_latitude\"]\n", "    if pd.isna(x[\"origin_latitude\"])\n", "    else x[\"origin_latitude\"],\n", "    axis=1,\n", ")\n", "\n", "trips[\"origin_longitude\"] = trips.groupby([\"trip_id\"])[\"customer_longitude\"].shift(1)\n", "trips[\"origin_longitude\"] = trips.apply(\n", "    lambda x: x[\"ds_longitude\"]\n", "    if pd.isna(x[\"origin_longitude\"])\n", "    else x[\"origin_longitude\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Keeping the first order in the trip only (dark_store --> customer)\n", "trips = trips.drop_duplicates([\"trip_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips = trips.query(\"order_type=='super_express'\").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"# super epxress orders are {super_express_trips.order_id.nunique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coord = list()\n", "doorstep_coords = list()\n", "for row in super_express_trips.itertuples(index=False):\n", "    coord.append(\n", "        [\n", "            (row.customer_latitude, row.customer_longitude),\n", "            (row.origin_latitude, row.origin_longitude),\n", "        ]\n", "    )\n", "\n", "    doorstep_coords.append(\n", "        [\n", "            (row.customer_latitude, row.customer_longitude),\n", "            (row.doorstep_latitude, row.doorstep_longitude),\n", "        ]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get inter order distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def osrm_url_formatter(coords, url, profile=\"route\"):\n", "    result_fetched = 0\n", "    wait_time = 5\n", "    wait_increase = 2\n", "\n", "    statement = (\n", "        url\n", "        + \"/{}/v1/driving/\".format(profile)\n", "        + coords\n", "        + \"?overview=false&annotations=true\"\n", "    )\n", "\n", "    while result_fetched != 1:\n", "        try:\n", "            r = requests.get(statement)\n", "            result_fetched = 1\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to fetch data for {statement} with error {e} retrying in {wait_time} secs\"\n", "            )\n", "            time.sleep(wait_time)\n", "            wait_time = wait_time * wait_increase\n", "    return r.json()\n", "\n", "\n", "def get_inter_order_distance(locations):\n", "    pair1 = locations[1]\n", "    pair2 = locations[0]\n", "\n", "    coords = (\n", "        \"\"\n", "        + str(pair1[1])\n", "        + \",\"\n", "        + str(pair1[0])\n", "        + \";\"\n", "        + str(pair2[1])\n", "        + \",\"\n", "        + str(pair2[0])\n", "    )\n", "    response = osrm_url_formatter(coords, OSRM_URL, \"route\")\n", "\n", "    try:\n", "        distance = response[\"routes\"][0][\"distance\"] / 1000  # distance in kms\n", "    #         duration = response[\"routes\"][0][\"duration\"] / 60\n", "    except:\n", "        return 0\n", "\n", "    return distance\n", "\n", "\n", "def multithread_osrm_request(coord):\n", "    pool = None\n", "    pool = ThreadPoolExecutor(max_workers=8)\n", "    start_time = time.time()\n", "\n", "    distance_lst = list()\n", "\n", "    for coords_distance in pool.map(get_inter_order_distance, coord):\n", "        distance_lst.append(coords_distance)\n", "\n", "    return distance_lst\n", "\n", "\n", "def batch_requests(coordinates, batch_size=10000):\n", "    num_batches = math.ceil(len(coordinates) / batch_size)\n", "    distance = list()\n", "\n", "    index = 0\n", "    for i in range(num_batches):\n", "        start = index\n", "        end = batch_size + index\n", "        coordinate_subset = coordinates[start:end]\n", "        index = end\n", "        subset_distance = multithread_osrm_request(coordinate_subset)\n", "        distance.extend(subset_distance)\n", "        time.sleep(5)\n", "        print(f\"----Processed batch {i}----\")\n", "\n", "    return distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inter_order_dist = batch_requests(coord)\n", "\n", "if len(inter_order_dist) == len(coord):\n", "    super_express_trips[\"inter_order_distance\"] = inter_order_dist"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_dist = batch_requests(doorstep_coords)\n", "\n", "if len(doorstep_dist) == len(doorstep_coords):\n", "    super_express_trips[\"doorstep_distance\"] = doorstep_dist"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips[\"distance_travelled\"] = super_express_trips.groupby(\"trip_id\")[\n", "    \"inter_order_distance\"\n", "].cumsum()\n", "super_express_trips[\n", "    [\n", "        \"customer_latitude\",\n", "        \"customer_longitude\",\n", "        \"ds_latitude\",\n", "        \"ds_longitude\",\n", "        \"distance_travelled\",\n", "    ]\n", "].head(6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data wrangling and outlier removal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips[\"oid_start_time\"] = super_express_trips.groupby([\"trip_id\"])[\n", "    \"delivery_timestamp\"\n", "].shift(1)\n", "super_express_trips[\"oid_start_time\"] = super_express_trips.apply(\n", "    lambda x: x[\"trip_start_timestamp\"]\n", "    if pd.isna(x[\"oid_start_time\"])\n", "    else x[\"oid_start_time\"],\n", "    axis=1,\n", ")\n", "\n", "\n", "super_express_trips[\"doorstep_time\"] = super_express_trips.apply(\n", "    lambda x: (x[\"delivery_timestamp\"] - x[\"reached_doorstep_time\"]).seconds / 60,\n", "    axis=1,\n", ")\n", "\n", "\n", "super_express_trips[\"inter_order_time\"] = super_express_trips.apply(\n", "    lambda x: (x[\"reached_doorstep_time\"] - x[\"oid_start_time\"])\n", "    / np.timedelta64(1, \"m\"),\n", "    axis=1,\n", ")\n", "\n", "super_express_trips[\"eta\"] = super_express_trips.apply(\n", "    lambda x: (x[\"reached_doorstep_time\"] - x[\"trip_start_timestamp\"])\n", "    / np.timedelta64(1, \"m\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips[[\"eta\", \"distance_travelled\"]].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# speed in km/hr\n", "super_express_trips[\"inter_order_speed\"] = super_express_trips.apply(\n", "    lambda x: (x[\"inter_order_distance\"] * 60) / x[\"inter_order_time\"]\n", "    if x[\"inter_order_time\"] != 0\n", "    else 15.0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips[\n", "    [\"eta\", \"distance_travelled\", \"inter_order_speed\", \"doorstep_distance\"]\n", "].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_outliers = super_express_trips.query(\n", "    f\"doorstep_distance >={DOORSTEP_DISTANCE_OUTLIER_CUTOFF}\"\n", ").reset_index(drop=True)\n", "\n", "outliers = super_express_trips.query(\n", "    f\"inter_order_speed >= {SPEED_UPPER_BOUND} or inter_order_speed <= {SPEED_LOWER_BOUND} or eta > 60\"\n", ")\n", "outlier_oids = list(outliers.order_id.values) + list(doorstep_outliers.order_id.values)\n", "outlier_oids = set(outlier_oids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["super_express_trips.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(outlier_oids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["speed_data = super_express_trips.query(\n", "    f\"order_id not in {tuple(outlier_oids)}\"\n", ").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["speed_data[[\"eta\", \"distance_travelled\", \"inter_order_speed\"]].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["speed_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def q75(x):\n", "    return x.quantile(0.75)\n", "\n", "\n", "def q90(x):\n", "    return x.quantile(0.9)\n", "\n", "\n", "tmp_statistic = (\n", "    speed_data.groupby(\"darkstore_id\")\n", "    .agg({\"inter_order_speed\": [\"min\", \"mean\", \"median\", q75, q90, \"max\"]})\n", "    .reset_index()\n", ")\n", "\n", "speed_statistic = pd.DataFrame({\"darkstore_id\": tmp_statistic.darkstore_id.values})\n", "speed_statistic = speed_statistic.merge(\n", "    tmp_statistic, how=\"inner\", left_on=\"darkstore_id\", right_on=\"darkstore_id\"\n", ")\n", "\n", "\n", "speed_statistic.rename(\n", "    {\n", "        (\"inter_order_speed\", \"min\"): \"min_speed\",\n", "        (\"inter_order_speed\", \"mean\"): \"mean_speed\",\n", "        (\"inter_order_speed\", \"median\"): \"median_speed\",\n", "        (\"inter_order_speed\", \"q75\"): \"q75_speed\",\n", "        (\"inter_order_speed\", \"q90\"): \"q90_speed\",\n", "        (\"inter_order_speed\", \"max\"): \"max_speed\",\n", "    },\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "\n", "speed_statistic[\"min_speed\"] = speed_statistic.min_speed.apply(lambda x: round(x, 2))\n", "speed_statistic[\"mean_speed\"] = speed_statistic.mean_speed.apply(lambda x: round(x, 2))\n", "speed_statistic[\"median_speed\"] = speed_statistic.median_speed.apply(\n", "    lambda x: round(x, 2)\n", ")\n", "speed_statistic[\"q75_speed\"] = speed_statistic.q75_speed.apply(lambda x: round(x, 2))\n", "speed_statistic[\"q90_speed\"] = speed_statistic.q90_speed.apply(lambda x: round(x, 2))\n", "speed_statistic[\"max_speed\"] = speed_statistic.max_speed.apply(lambda x: round(x, 2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["speed_statistic.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["speed_statistic.darkstore_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    speed_statistic, SHEET_ID, SPEED_SHEET_NAME, service_account=\"service_account\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics = eta_statistics.merge(\n", "    speed_statistic, how=\"right\", left_on=\"darkstore_id\", right_on=\"darkstore_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eta_statistics.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Push to redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(se_stats_df):\n", "\n", "    frame = se_stats_df.copy()\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    cols = [\n", "        \"darkstore_id\",\n", "        \"checkedout_to_picker_assigned\",\n", "        \"picker_assigned_to_picking_start\",\n", "        \"picking_time\",\n", "        \"picked_to_billing_start\",\n", "        \"billing_time\",\n", "        \"billed_to_enroute\",\n", "        \"delivery_time\",\n", "        \"min_speed\",\n", "        \"mean_speed\",\n", "        \"median_speed\",\n", "        \"q75_speed\",\n", "        \"q90_speed\",\n", "        \"max_speed\",\n", "        \"updated_at\",\n", "        \"partner_assigned_to_enroute\",\n", "    ]\n", "    frame = frame[cols]\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"darkstore_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"dark store id\",\n", "        },\n", "        {\n", "            \"name\": \"checkedout_to_picker_assigned\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time delta b/w order checkout to picker assigned\",\n", "        },\n", "        {\n", "            \"name\": \"picker_assigned_to_picking_start\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time delta b/w picklist assigned to picking started\",\n", "        },\n", "        {\n", "            \"name\": \"picking_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"picking time of the order\",\n", "        },\n", "        {\n", "            \"name\": \"picked_to_billing_start\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time delta b/w picking end and billing start\",\n", "        },\n", "        {\n", "            \"name\": \"billing_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"billing time of the order\",\n", "        },\n", "        {\n", "            \"name\": \"billed_to_enroute\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time delta b/w enroute event and billing end timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"delivery_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"last mile time of the order\",\n", "        },\n", "        {\n", "            \"name\": \"min_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"min of gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"mean_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mean of gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"median_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"median gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"q75_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"75th percentile of gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"q90_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"90th percentile of gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"max_speed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"max of gsp speed in the darkstore\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"table updation timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"partner_assigned_to_enroute\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time delta b/w start of enroute event and partner assigned event\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"serviceability_store_activity_durations\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"sortkey\": [\"darkstore_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"append\",\n", "        \"table_description\": \"Super Express store activity duration components\",\n", "    }\n", "\n", "    pb.to_redshift(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["push_to_redshift(eta_statistics)\n", "\n", "print(\"*\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}