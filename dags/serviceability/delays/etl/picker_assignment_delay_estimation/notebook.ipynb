{"cells": [{"cell_type": "markdown", "id": "14eaa490-cb19-4db8-bf72-cf507a3d4f6e", "metadata": {}, "source": ["# Importing packages"]}, {"cell_type": "code", "execution_count": null, "id": "c5b35356-cc39-4986-813e-155efb5f5fd2", "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "!pip install sklearn"]}, {"cell_type": "code", "execution_count": null, "id": "32002b44-ac74-414e-8680-3cbe2e9328be", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandasql as ps\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import sklearn\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import r2_score\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression"]}, {"cell_type": "markdown", "id": "74875528-5f70-4165-be8f-e1bd7f6fab0a", "metadata": {}, "source": ["# Training data"]}, {"cell_type": "code", "execution_count": null, "id": "6556cbbc-5dc1-4aef-895a-e165f0b32eac", "metadata": {}, "outputs": [], "source": ["date_start = (datetime.now() + timedelta(hours=5, minutes=30, days=-20)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "date_end = (datetime.now() + timedelta(hours=5, minutes=30, days=-1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "date_range = \"'\" + date_start + \"' and '\" + date_end + \"'\"\n", "print(\"Delays-Picker assigment: params pushed to redshift:\", date_range)"]}, {"cell_type": "code", "execution_count": null, "id": "c944983d-b083-4255-bd47-0de7e7a19b03", "metadata": {}, "outputs": [], "source": ["backlog = \"\"\"select at_date_ist as date,log_ts_ist,merchant_id,max_busy_pickers as busy_pickers,max_available_pickers as available_pickers,\n", "max_pickers_order_backlog as pickers_order_backlog,\\\n", "CASE\n", "   WHEN busy_pickers+available_pickers = 0 THEN 0\n", "   ELSE (((pickers_order_backlog+busy_pickers)*1.0) / (busy_pickers+available_pickers))\n", "END AS disruption_index_picking\n", "from metrics.serviceability_store_manpower_state m\n", "inner join lake_logistics.logistics_node ln on m.merchant_id = ln.external_id\n", "    and at_date_ist between {date_}\n", "    and extract(hour from (log_ts_ist)) > 5\n", "inner join lake_logistics.logistics_node_address lna on ln.node_address_id = lna.id\n", "where pickers_order_backlog > available_pickers and busy_pickers > 0\n", "\"\"\".format(\n", "    date_=date_range\n", ")\n", "df_backlog = pd.read_sql(backlog, pb.get_connection(\"[Warehouse] Redshift\"))\n", "df_backlog[\"log_ts_ist\"] = df_backlog[\"log_ts_ist\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d %H:%M\") + \":00\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "54b3bc1c-4012-49e5-9647-032585e34398", "metadata": {}, "outputs": [], "source": ["orders = \"\"\"with delayed_orders as (SELECT a.order_id,\n", "          a.frontend_merchant_id,\n", "          merchant_name,\n", "          m.city_name,\n", "          a.order_checkout_ts_ist,\n", "          extract(hour from order_checkout_ts_ist) as checkout_hour,\n", "          a.order_checkout_ts_ist::date AS order_date,\n", "          eta_computed_mins AS eta_computed_mins,\n", "          datediff(seconds,order_checkout_ts_ist,order_reached_doorstep_ts_ist)::float/60 AS actual_doorstep_time,\n", "          greatest(round(actual_doorstep_time - eta_computed_mins),0) AS eta_delay,\n", "          round(est_picker_eta_mins,2) as est_picker_eta_mins\n", "   FROM dwh.fact_supply_chain_order_details a\n", "   INNER JOIN dwh.dim_merchant m ON a.frontend_merchant_id = m.merchant_id\n", "        and order_checkout_ts_ist::date between {date_}\n", "        and eta_shown_mins is not null\n", "         and is_current = true\n", "         AND is_rescheduled = FALSE\n", "         AND is_order_delivered = TRUE\n", "   INNER JOIN dwh.fact_sales_order_details f on a.order_id = f.order_id\n", "    and f.is_internal_order = False\n", ")\n", "\n", "select * from\n", "(select distinct d.*,\n", "rank() over(partition by oe.order_id order by install_ts) as rn_first,\n", "(case when rn_first = 1 then (install_ts + interval '5.5 hours') else null end) as first_assignment_,\n", "datediff('seconds',order_checkout_ts_ist,first_assignment_)::float/60 as act_picker_eta_mins,\n", "greatest(round(act_picker_eta_mins-est_picker_eta_mins),0) as picker_assign_delay\n", "from delayed_orders d\n", "inner join lake_oms_bifrost.oms_order_event oe on d.order_id = oe.order_id\n", "    and (oe.install_ts+ interval '5.5 hours')::date between {date_}\n", "    and event_type_key in ('retail_picker_assignment')\n", ")\n", "where picker_assign_delay >= 1\"\"\".format(\n", "    date_=date_range\n", ")\n", "\n", "df_orders = pd.read_sql(orders, pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "id": "e9a8ec2b-7d28-49f6-93f9-1f405f80ac65", "metadata": {}, "outputs": [], "source": ["df_orders[\"exp_assignment_time\"] = df_orders.apply(\n", "    lambda row: pd.to_datetime(row[\"order_checkout_ts_ist\"])\n", "    + <PERSON><PERSON><PERSON>(minutes=row[\"est_picker_eta_mins\"]),\n", "    axis=1,\n", ")\n", "df_orders[\"trunc_exp_assignment_time\"] = df_orders[\"exp_assignment_time\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d %H:%M\") + \":00\"\n", ")\n", "\n", "df_merge = df_backlog.merge(\n", "    df_orders,\n", "    how=\"inner\",\n", "    left_on=[\"merchant_id\", \"log_ts_ist\"],\n", "    right_on=[\"frontend_merchant_id\", \"trunc_exp_assignment_time\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "92a3f913-9295-446b-b6f0-43b142b7e117", "metadata": {}, "outputs": [], "source": ["actual_backlog = \"\"\"\n", "with allo_base as (select (b.install_ts + interval '5.5 hours') as install_ts_ist,\n", "(b.update_ts + interval '5.5 hours') as update_ts_ist,\n", "(r.install_ts + interval '5.5 hours') as r_install_ts_ist,\n", "(r.update_ts + interval '5.5 hours') as r_update_ts_ist,\n", "label_value::int as order_id,\n", "r.state,\n", "fs.frontend_merchant_id,\n", "order_create_ts_ist,\n", "rank() over(partition by label_value order by r.update_ts) as rn,\n", "distinct_items_ordered\n", "from lake_logistics.logistics_allocation_batch b\n", "inner join lake_logistics.logistics_allocation_request r on b.id = r.allocation_batch_id\n", "and (r.install_ts+ interval '5.5 hours')::date between {date_}\n", "and r.type in ('AUTO_PICKER_ALLOCATION_REQUEST')\n", "AND r.STATE in ('COMPLETED')\n", "AND label_type = 'ORDER_ID'\n", "inner join dwh.fact_supply_chain_order_details fs on label_value::int = fs.order_id\n", "inner join dwh.fact_sales_order_details fo on fs.order_id = fo.order_id    \n", "    and is_internal_order = False\n", "\n", ")\n", "\n", "select * from allo_base where rn = 1\n", "\"\"\".format(\n", "    date_=date_range\n", ")\n", "\n", "df_act_backlog = pd.read_sql(actual_backlog, pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "markdown", "id": "272c1aed-5838-416d-9e04-8d402129ef36", "metadata": {}, "source": ["# Removing duplicates"]}, {"cell_type": "code", "execution_count": null, "id": "06c953e8-a1c1-48cd-b830-111847531cd0", "metadata": {}, "outputs": [], "source": ["dup = df_merge.duplicated([\"order_id\"])\n", "if dup.sum() > 0:\n", "    dup_df = df_merge[df_merge.duplicated()]\n", "    df_merge = df_merge.drop_duplicates(subset=[\"order_id\"], keep=\"first\").reset_index(\n", "        drop=True\n", "    )\n", "# (df_merge.duplicated([\"order_id\"])).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d349781b-cf7b-4c2b-abf9-906e7bdf1fd3", "metadata": {}, "outputs": [], "source": ["dup = df_act_backlog.duplicated([\"order_id\"])\n", "if dup.sum() > 0:\n", "    dup_df = df_act_backlog[df_act_backlog.duplicated()]\n", "    df_act_backlog = df_act_backlog.drop_duplicates(\n", "        subset=[\"order_id\"], keep=\"first\"\n", "    ).reset_index(drop=True)\n", "# (df_act_backlog.duplicated([\"order_id\"])).sum()"]}, {"cell_type": "markdown", "id": "d6f88938-357f-41bb-afc2-af095036c4f4", "metadata": {}, "source": ["## Merge actual backlog with assignment time"]}, {"cell_type": "code", "execution_count": null, "id": "479557b6-9176-49ad-8467-6ab958899372", "metadata": {}, "outputs": [], "source": ["merge_backlog = \"\"\"select m.*,\n", "count(distinct a.order_id) as act_backlog_orders,\n", "count(distinct case when a.order_create_ts_ist <= order_checkout_ts_ist then a.order_id else null end) as checkout_backlog,\n", "sum(distinct_items_ordered) as total_backlog_spo,\n", "sum(distinct case when a.order_create_ts_ist <= order_checkout_ts_ist then distinct_items_ordered else null end) as effective_backlog_spo\n", "from df_merge m\n", "left join df_act_backlog a on m.merchant_id = a.frontend_merchant_id and m.exp_assignment_time between a.r_install_ts_ist and a.update_ts_ist\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24 \"\"\"\n", "\n", "main_df = ps.sqldf(merge_backlog)"]}, {"cell_type": "code", "execution_count": null, "id": "633c5b5d-a312-4a45-ac00-54ce54b504c2", "metadata": {}, "outputs": [], "source": ["main_df[\"total_backlog_spo\"] = main_df[\"total_backlog_spo\"].fillna(0)\n", "main_df[\"effective_backlog_spo\"] = main_df[\"effective_backlog_spo\"].fillna(0)\n", "main_df[\"revised_di\"] = main_df.apply(\n", "    lambda row: 0\n", "    if (row[\"busy_pickers\"] + row[\"available_pickers\"] == 0)\n", "    else (\n", "        (row[\"checkout_backlog\"] + row[\"busy_pickers\"])\n", "        / (row[\"busy_pickers\"] + row[\"available_pickers\"])\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "markdown", "id": "e7167617-e61e-4592-932a-6812eb17d89d", "metadata": {}, "source": ["## Outlier removal"]}, {"cell_type": "code", "execution_count": null, "id": "bd4bcfec-4b13-413a-81bf-b0de19394526", "metadata": {}, "outputs": [], "source": ["main_df[\"ratio\"] = main_df[\"picker_assign_delay\"] / main_df[\"revised_di\"]\n", "main_df = main_df[(main_df[\"ratio\"] < 4) & (main_df[\"revised_di\"] < 15)].reset_index(\n", "    drop=True\n", ")\n", "main_df = main_df[~main_df[\"checkout_hour\"].isin([6, 23])].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d91c77fe-ddc9-453f-8807-e0034e77305d", "metadata": {}, "outputs": [], "source": ["# main_df[main_df[\"picker_assign_delay\"] < 50].plot.scatter(x = 'revised_di', y = 'picker_assign_delay', s = 1)"]}, {"cell_type": "code", "execution_count": null, "id": "03f3c5e9-3e54-4ad0-a381-879f3188c1ff", "metadata": {}, "outputs": [], "source": ["main_df = main_df[main_df[\"picker_assign_delay\"] <= 50].reset_index(drop=True)"]}, {"cell_type": "markdown", "id": "ac81c116-8d6a-46e6-ac9b-d307c6192fe3", "metadata": {}, "source": ["# Get coefficients"]}, {"cell_type": "code", "execution_count": null, "id": "df4e6578-b7f0-4433-86d2-16d91e2e5740", "metadata": {}, "outputs": [], "source": ["x = main_df[[\"revised_di\", \"effective_backlog_spo\"]]\n", "# x=df_merge[['disruption_index_picking']]\n", "y = main_df.picker_assign_delay"]}, {"cell_type": "code", "execution_count": null, "id": "dca0dbc3-7776-41de-86d2-9ac0f9ad20e8", "metadata": {}, "outputs": [], "source": ["x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=0)\n", "# shapes of splitted data\n", "# print(\"X_train:\",x_train.shape)\n", "# print(\"X_test:\",x_test.shape)\n", "# print(\"Y_train:\",y_train.shape)\n", "# print(\"Y_test:\",y_test.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "3a05f770-0aaf-4579-a82a-db2d870166b4", "metadata": {}, "outputs": [], "source": ["linreg = LinearRegression()\n", "linreg.fit(x_train, y_train)\n", "r_2 = linreg.score(x, y)"]}, {"cell_type": "code", "execution_count": null, "id": "bcdae585-7447-4011-b77e-2aaadd943dac", "metadata": {}, "outputs": [], "source": ["# print(f\"coefficient of determination: {r_2}\")\n", "# print(f\"intercept: {linreg.intercept_}\")\n", "\n", "\n", "# print(f\"slope: {linreg.coef_}\")"]}, {"cell_type": "code", "execution_count": null, "id": "32f216cf-98e2-4e77-a622-b8fbc96f1b1b", "metadata": {}, "outputs": [], "source": ["r_2 = linreg.score(x, y)\n", "intercept = linreg.intercept_\n", "slope_di = (linreg.coef_)[0]\n", "slope_sku = (linreg.coef_)[1]\n", "# print(r_2,intercept,slope_di,slope_sku)"]}, {"cell_type": "code", "execution_count": null, "id": "c345b155-d836-441b-936d-23b5feefa54a", "metadata": {}, "outputs": [], "source": ["df_params = pd.DataFrame(\n", "    columns=[\"r_2\", \"intercept\", \"coeff_di\", \"coeff_sku\"],\n", "    data=[[r_2, intercept, slope_di, slope_sku]],\n", ")\n", "df_params[\"install_ts_ist\"] = (\n", "    datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "df_params = df_params[[\"install_ts_ist\", \"r_2\", \"intercept\", \"coeff_di\", \"coeff_sku\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "9c0cc588-54f8-49a3-9870-757759599d55", "metadata": {}, "outputs": [], "source": ["def push_to_redshift(df):\n", "    # upload df to redshift\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"delays_picker_assignment_model_retraining_logs\",\n", "        \"table_description\": \"Picker assignment model retraining logs\",\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"install_ts_ist\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"time of retraining the model\",\n", "            },\n", "            {\n", "                \"name\": \"r_2\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"coefficient of determination of the model\",\n", "            },\n", "            {\n", "                \"name\": \"intercept\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"intercept of the linear equation\",\n", "            },\n", "            {\n", "                \"name\": \"coeff_di\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"coefficient of disruption index\",\n", "            },\n", "            {\n", "                \"name\": \"coeff_sku\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"coefficient of sku\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"install_ts_ist\"],\n", "        \"sortkey\": [\"install_ts_ist\"],\n", "        \"load_type\": \"append\",\n", "    }\n", "\n", "    if df.shape[0] > 0:\n", "        pb.to_redshift(df, **kwargs)\n", "        print(\"Delays-Picker assigment: params pushed to redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "50b5f95e-a9cd-448b-8e0e-ced25207d81a", "metadata": {}, "outputs": [], "source": ["push_to_redshift(df_params)"]}, {"cell_type": "code", "execution_count": null, "id": "70b77b9f-bc67-4413-87bc-7d9a740f0b3b", "metadata": {}, "outputs": [], "source": ["y_pred = linreg.predict(x_test)\n", "y_pred"]}, {"cell_type": "code", "execution_count": null, "id": "ff77723a-02b8-4088-8ae0-eff0b5f5f191", "metadata": {}, "outputs": [], "source": ["# x_test[\"check_formula\"] = x_test.apply(lambda row: -2.6001404693759684 + (2.4641444*row[\"revised_di\"]) + (0.03513866*row[\"effective_backlog_spo\"]) + (0.09274284*row[\"busy_pickers\"]),axis = 1)"]}, {"cell_type": "code", "execution_count": null, "id": "6eec817b-c056-468e-a716-65ca30552cfe", "metadata": {}, "outputs": [], "source": ["Accuracy = r2_score(y_test, y_pred) * 100\n", "print(\" Accuracy is %.2f\" % Accuracy)"]}, {"cell_type": "code", "execution_count": null, "id": "a4fb16d3-f39c-4c57-9eb9-addea3b9ee12", "metadata": {}, "outputs": [], "source": ["sns.regplot(x=y_test, y=y_pred, ci=None, color=\"red\")"]}, {"cell_type": "code", "execution_count": null, "id": "5823b191-1800-4ffd-b28b-75f4ade4a790", "metadata": {}, "outputs": [], "source": ["pred_df_di = pd.DataFrame(\n", "    {\n", "        \"Actual Value\": y_test,\n", "        \"Predicted Value\": y_pred,\n", "        \"di_error\": round(y_test - y_pred),\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "040571e9-88de-42b0-8be7-44c38c728355", "metadata": {}, "outputs": [], "source": ["pred_df_di.sort_values([\"Actual Value\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d21d8d7a-4793-4b80-8abf-24bad0c90395", "metadata": {}, "outputs": [], "source": ["pred_df_di.sort_values([\"Actual Value\"], ascending=False).head(60)"]}, {"cell_type": "code", "execution_count": null, "id": "1ac76850-16b8-4b57-96c1-a09c2656ed14", "metadata": {}, "outputs": [], "source": ["# -2 to 2\n", "pred_df_di[\n", "    (pred_df_di[\"di_error\"].between(-2, 2))\n", "    & (pred_df_di[\"Actual Value\"].between(10, 15))\n", "][\"Actual Value\"].count() / pred_df_di[pred_df_di[\"Actual Value\"].between(10, 15)][\n", "    \"Actual Value\"\n", "].count()"]}, {"cell_type": "code", "execution_count": null, "id": "ba60b4f4-bff0-4e14-a70e-9beabbe130c0", "metadata": {}, "outputs": [], "source": ["pred_df_di[pred_df_di[\"Actual Value\"].between(10, 15)][\"Actual Value\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "07689528-76c0-46c3-818e-00a2496ebc9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}