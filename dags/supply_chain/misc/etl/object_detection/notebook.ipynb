{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tensorflow==1.13.2 Pillow==7.0.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import pytz\n", "import zipfile\n", "import warnings\n", "import six.moves.urllib as urllib\n", "\n", "from io import StringIO\n", "from collections import defaultdict\n", "from datetime import datetime, timedelta\n", "from distutils.version import StrictVersion\n", "from botocore.exceptions import ClientError\n", "from PIL import UnidentifiedImageError\n", "import numpy as np\n", "import pandas as pd\n", "from PIL import Image\n", "import tensorflow as tf\n", "\n", "import pencilbox as pb\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename = \"/tmp/model.zip\"\n", "cloud_filename = \"pencilbox/object-detection/v1/model/model.zip\"\n", "dir_name = \"object_detection\"\n", "image_dir = \"images\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "pb.from_s3(bucket_name, cloud_filename, local_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with zipfile.ZipFile(local_filename, \"r\") as zip_ref:\n", "    if not os.path.exists(dir_name):\n", "        os.makedirs(dir_name)\n", "        zip_ref.extractall(dir_name)\n", "    else:\n", "        zip_ref.extractall(dir_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["start_date = (datetime.now() - timedelta(hours=3)).isoformat()\n", "end_date = datetime.now().isoformat()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(cwd)\n", "\n", "import object_detection.label_map_util\n", "from object_detection.utils import ops as utils_ops"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH base AS\n", "  (SELECT -- distinct\n", "   delivery_slot_start_time AS delivery_date,\n", "                     c.name AS city, --s.name as station,\n", "CASE\n", "    WHEN json_extract_path_text(od.properties,'deliveryProofType',TRUE) = '' THEN 'NO PROOF'\n", "    ELSE json_extract_path_text(od.properties,'deliveryProofType',TRUE)\n", "                                                           END AS deliveryProofType,\n", "regexp_substr(json_extract_path_text(od.properties,'dropCoordinates',TRUE), '[0-9]+\\\\.[0-9]*', 1, 1) AS delivery_lat,\n", "                                                                                                                                                                                   regexp_substr(json_extract_path_text(od.properties,'dropCoordinates',TRUE), '[0-9]+\\\\.[0-9]*', 1, 2) AS delivery_lon, -- a.latitude as cust_lat,\n", "-- a.longitude as cust_long\n", "--json_extract_path_text(od.properties,'recipient',true) as recipient,\n", "CASE\n", "    WHEN json_extract_path_text(od.properties,'deliveryProofType',TRUE) = 'PHOTO' THEN json_extract_path_text(od.properties,'deliveryProofLocation',TRUE)\n", "    ELSE json_extract_path_text(od.properties,'recipient',TRUE) END AS Delivery_location,\n", "    json_extract_path_text(od.properties,'subOrderBillingDetails',TRUE) AS order_details,\n", "    order_id AS lm_order_id\n", "   FROM lake_last_mile.order_detail od -- where order_id = 34552658\n", "INNER JOIN lake_last_mile.order o ON o.id = od.order_id\n", "   AND order_status = 'DELIVERED'\n", "   INNER JOIN lake_last_mile.station s ON s.id = o.station_id\n", "   INNER JOIN lake_last_mile.city c ON c.id = s.city_id\n", "   LEFT JOIN lake_last_mile.address a ON a.id = o.delivery_address_id\n", "   WHERE delivery_slot_start_time  BETWEEN '{start_date}' AND '{end_date}'\n", "     AND s.name NOT IN ('TEST')\n", "     AND c.name NOT IN ('Not in service area')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8)\n", "SELECT b1.delivery_date,\n", "       b1.lm_order_id,\n", "       b1.delivery_lat,\n", "       b1.delivery_lon,\n", "       b1.city,\n", "       b1.deliveryProofType,\n", "       b1.Delivery_location,\n", "       o1.external_order_id as oms_order_id\n", "       from\n", "(SELECT delivery_date,\n", "       lm_order_id,\n", "       delivery_lat,\n", "       delivery_lon,\n", "       city,\n", "       deliveryProofType,\n", "       Delivery_location,\n", "       order_details\n", "FROM base\n", "WHERE deliveryProofType = 'PHOTO') as b1\n", "inner join lake_last_mile.order o1 on o1.id = b1.lm_order_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_attributes = pd.read_sql(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_attributes = image_attributes.drop([\"deliveryprooftype\", \"city\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pull_images(bucket_name, frame):\n", "    ids_ = []\n", "    for oid in frame[\"oms_order_id\"].values:\n", "        sub_path = \"{}{}{}\".format(\"delivery_proof\", os.sep, oid)\n", "        cloud_filepath = \"{}{}{}\".format(sub_path, os.sep, oid + \".jpg\")\n", "        local_filepath = \"{}{}{}\".format(image_dir, os.sep, oid + \".jpg\")\n", "        try:\n", "            pb.from_s3(bucket_name, cloud_filepath, local_filepath)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON>:\n", "            ids_.append(oid)\n", "    return ids_  # returning order_ids whose images were not found"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not os.path.exists(image_dir):\n", "    os.makedirs(image_dir)\n", "\n", "images_bucket_name = \"grofers-prod-lastmile-obelix\"\n", "oids = pull_images(images_bucket_name, image_attributes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODEL_NAME = \"{}{}{}\".format(\"object_detection\", os.sep, \"grofers_image_model\")\n", "# Path to frozen detection graph. This is the actual model that is used for the object detection.\n", "PATH_TO_FROZEN_GRAPH = \"{}{}{}\".format(MODEL_NAME, os.sep, \"frozen_inference_graph.pb\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detection_graph = tf.Graph()\n", "with detection_graph.as_default():\n", "    od_graph_def = tf.GraphDef()\n", "    with tf.gfile.GFile(PATH_TO_FROZEN_GRAPH, \"rb\") as fid:\n", "        serialized_graph = fid.read()\n", "        od_graph_def.ParseFromString(serialized_graph)\n", "        tf.import_graph_def(od_graph_def, name=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_index = {1: {\"id\": 1, \"name\": \"<PERSON><PERSON>\"}, 2: {\"id\": 2, \"name\": \"<PERSON>ata\"}}\n", "label_map = {1: \"Grofers_bag\", 2: \"Aata\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_image_into_numpy_array(image):\n", "    (im_width, im_height) = image.size\n", "    return np.array(image.getdata()).reshape((im_height, im_width, 3)).astype(np.uint8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_inference_for_single_image(image, graph):\n", "    with graph.as_default():\n", "        with tf.Session() as sess:\n", "            # Get handles to input and output tensors\n", "            ops = tf.get_default_graph().get_operations()\n", "            all_tensor_names = {output.name for op in ops for output in op.outputs}\n", "            tensor_dict = {}\n", "            for key in [\n", "                \"num_detections\",\n", "                \"detection_boxes\",\n", "                \"detection_scores\",\n", "                \"detection_classes\",\n", "                \"detection_masks\",\n", "            ]:\n", "                tensor_name = key + \":0\"\n", "                if tensor_name in all_tensor_names:\n", "                    tensor_dict[key] = tf.get_default_graph().get_tensor_by_name(\n", "                        tensor_name\n", "                    )\n", "\n", "            image_tensor = tf.get_default_graph().get_tensor_by_name(\"image_tensor:0\")\n", "\n", "            # Run inference\n", "            output_dict = sess.run(\n", "                tensor_dict, feed_dict={image_tensor: np.expand_dims(image, 0)}\n", "            )\n", "\n", "            # all outputs are float32 numpy arrays, so convert types as appropriate\n", "            output_dict[\"num_detections\"] = int(output_dict[\"num_detections\"][0])\n", "            output_dict[\"detection_classes\"] = output_dict[\"detection_classes\"][\n", "                0\n", "            ].astype(np.uint8)\n", "            output_dict[\"detection_scores\"] = output_dict[\"detection_scores\"][0]\n", "\n", "    return output_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict(image_dir, label_map):\n", "    output_list = []\n", "    for filename in os.listdir(image_dir):\n", "        if filename.endswith(\".jpg\") or filename.endswith(\".png\"):\n", "            try:\n", "                image = Image.open(os.path.join(image_dir, filename))\n", "            except UnidentifiedImageError:\n", "                print(filename)\n", "                continue\n", "            image_np = load_image_into_numpy_array(image)\n", "            image_np_expanded = np.expand_dims(image_np, axis=0)\n", "            # print(image_np_expanded)\n", "            output_dict = run_inference_for_single_image(image_np, detection_graph)\n", "            idx = list(output_dict[\"detection_scores\"] > 0.72).index(False)\n", "            list_items_detect = list(\n", "                map(\n", "                    lambda i: label_map[i],\n", "                    set(list(output_dict[\"detection_classes\"])[0:idx]),\n", "                )\n", "            )\n", "            img = [filename.split(\".\")[0], json.dumps(list_items_detect)]\n", "            output_list.append(img)\n", "    results = pd.DataFrame(output_list)\n", "    results.columns = [\"order_id\", \"objects_detected\"]\n", "    results[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(pred_frame, images_frame):\n", "    df = images_frame.merge(\n", "        pred_frame, left_on=\"oms_order_id\", right_on=\"order_id\", how=\"inner\"\n", "    ).drop([\"order_id\"], axis=1)\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"delivery_date\", \"type\": \"timestamp\"},\n", "        {\"name\": \"lm_order_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"delivery_lat\", \"type\": \"varchar\"},\n", "        {\"name\": \"delivery_lon\", \"type\": \"varchar\"},\n", "        {\"name\": \"delivery_location\", \"type\": \"varchar\"},\n", "        {\"name\": \"oms_order_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"objects_detected\", \"type\": \"varchar\"},\n", "        {\"name\": \"updated_at\", \"type\": \"timestamp\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"features\",\n", "        \"table_name\": \"object_detection\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"oms_order_id\"],\n", "        \"sortkey\": [\"oms_order_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "    }\n", "    pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(image_attributes) != 0:\n", "    results = predict(image_dir, label_map)\n", "    push_to_redshift(results, image_attributes)"]}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}