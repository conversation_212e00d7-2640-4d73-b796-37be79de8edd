<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    border-radius: 5px 5px 0 0;
    width: 80%;
    max-width: 900px;
}
.tg .td1 {
    font-family: Roboto, sans-serif;
    font-size: 14px;
    padding: 2px 2px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #333;
    background-color: #FFEC33;
}
    
.tg td {
    font-family: Roboto, sans-serif;
    font-size: 14px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #333;
    background-color: #fff;
}
    
.tg th {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: normal;
    padding: 10px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #fff;
    background-color: #009879;
}
.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}
.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}  

.tg .p1 { text-align: center; }

</style>
</head>






    
    
    
    
<table class="tg">
<tbody>
    {% for row in rca_details %}
<tr>
<td class="td1" colspan="5">
<p class="p1"><strong>LOSS SNAPSHOT</strong></p>
</td>
</tr>
<tr>
<td class="td1">
<p class="p1"><strong>SLA</strong></p>
</td>
<td class="td1">
<p class="p1"><strong>Units</strong></p>
</td>
<td class="td1" colspan="2">
<p><strong>YESTERDAY</strong></p>
</td>
<td class="td1">
<p class="p1"><strong>MTD</strong></p>
</td>
</tr>
<tr>
<td>
<p><strong>PNAs</strong></p>
</td>
<td>
<p><strong>Amount</strong></p>
</td>
<td colspan="2">{{ row['PNA Loss T-1'] }}</td>
<td>{{ row['PNA Loss'] }}</td>
</tr>
<tr>
<td>
<p><strong>Stock Variance - Overall</strong></p>
</td>
<td>
<p><strong>Amount</strong></p>
</td>
<td colspan="2">{{ row['Stock Variance Loss T-1'] }}</td>
<td>{{ row['Stock Variance Loss'] }}</td>
</tr>
<tr>
<td>
<p><strong>Damages</strong></p>
</td>
<td>
<p><strong>Amount</strong></p>
</td>
<td colspan="2">{{ row['Damages Loss T-1'] }}</td>
<td>{{ row['Damages Loss'] }}</td>
</tr>
<tr>
<td>
<p><strong>Checkout to Billed within 3 mins (IPO&lt;=8)</strong></p>
</td>
<td>
<p><strong>Orders</strong></p>
</td>
<td colspan="2">{{ row['TAT Loss T-1'] }}</td>
<td>{{ row['TAT Loss'] }}</td>
</tr>
    {% endfor %}
</tbody>
</table>
<p>&nbsp;</p>


<p><b>
Please note the above is a tentative snapshot till date and will reduce when amends are made at the store. The final debit amount would be calculated on the 25th of every month, post leverage reduction.    
</b></p>




<table class="tg">
<tbody>
    {% for row in dn_details %}
<tr>
<td class="td1" colspan="5">
<p class="p1"><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [IMP] DN Process Adherence Snapshot</strong></p>
</td>
<td class="td1">&nbsp;</td>
</tr>
<tr>
<td class="td1" colspan="2">
<p class="p1"><strong>SLA</strong></p>
</td>
<td class="td1" colspan="2">
<p class="p1"><strong>T-1</strong></p>
</td>
<td class="td1">
<p class="p1"><strong>T-2</strong></p>
</td>
<td class="td1">
<p class="p1"><strong>MTD</strong></p>
</td>
</tr>
<tr>
<td  rowspan="3">
<p class="p1"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></p>
<p class="p1"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;GRN + DN % &nbsp;</strong></p>
</td>
<td>
<p class="p1"><strong>Within TAT</strong></p>
</td>
<td colspan="2">{{row['grn_per_within_tat_t_minus_1']}} %</td>
<td>{{row['grn_per_within_tat_t_minus_2']}} %</td>
<td>{{row['grn_per_within_tat']}} %</td>
</tr>
    <tr>
<td>
<p class="p1"><strong>Post TAT</strong></p>
</td>
<td colspan="2">{{row['grn_post_tat_t_minus_1']}} %</td>
<td>{{row['grn_post_tat_t_minus_2']}} %</td>
<td >{{row['grn_post_tat']}} %</td>
    </tr>
<tr>
<td>
<p class="p1"><strong>Overall</strong></p>
</td>
<td colspan="2">{{row['grn_per_overall_t_minus_1']}} %</td>
<td>{{row['grn_per_overall_t_minus_2']}} %</td>
<td >{{row['grn_per_overall']}} %</td>
</tr>
<tr>
<td rowspan="3"><br />
<p class="p1"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DN LOSS (Amount)</strong></p>
</td>
<td>
<p class="p1"><strong>Within TAT</strong></p>
</td>
<td colspan="2">{{row['dn_within_tat_t_minus_1']}}</td>
<td>{{row['dn_within_tat_t_minus_2']}}</td>
<td >{{row['dn_within_tat']}}</td>
</tr>
    <tr>
<td>
<p class="p1"><strong>Post TAT</strong></p>
</td>
<td colspan="2">{{row['dn_post_tat_t_minus_1']}}</td>
<td>{{row['dn_post_tat_t_minus_2']}}</td>
<td >{{row['dn_post_tat']}}</td>
    </tr>
<tr>
<td>
<p class="p1"><strong>Overall</strong></p>
</td>
<td colspan="2">{{row['ttl_dn_amt_t_minus_1']}}</td>
<td>{{row['ttl_dn_amt_t_minus_2']}}</td>
<td >{{row['ttl_dn_amt']}}</td>
</tr>
    <tr>
<td>
<p class="p1"><strong> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; DN Others (Amount)</strong></p>
</td>
<td>
<p class="p1"><strong>Overall</strong></p>
</td>
<td colspan="2">{{row['dn_other_amount_t_minus_1']}}</td>
<td>{{row['dn_other_amount_t_minus_2']}}</td>
<td >{{row['dn_other_amount']}}</td>
</tr>
<tr>
<td>
<p class="p1"><strong> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; B2B Return (Amount)</strong></p>
</td>
<td>
<p class="p1"><strong>Overall</strong></p>
</td>
<td colspan="2">{{row['ttl_b2b_ret_amount_t_minus_1']}}</td>
<td>{{row['ttl_b2b_ret_amount_t_minus_2']}}</td>
<td >{{row['ttl_b2b_ret_amount']}}</td>
</tr>
    {% endfor %}
</tbody>
</table>


<p><b>
Above data is for the DN process adherence at your store. We request you to take immediate remedial measures so as to not breach the TAT. 
</b></p>





<li>TAT for morning, evening and night indent is 9, 16 and 12 hours respectively.</li>
<li>Ensure driver scans the QR code twice - firstly for vehicle arrival and secondly for vehicle unloading start.</li>
<li>In case you have any concerns/doubts, please immediately raise to your respective operations team/partner POC.</li>
<li>Please return any DN related STO items in the next run/next-to-next run.</li>





<p><b>Appendix:</b></p>

<p>Please refer to the below definitions for clarification. Feel free to reach out for any other doubts</p>
<li><b>PNAs:</b> If a picker does not pick an item for an order which is available in inventory, it becomes PNA (Product Not Available).</li>
<li><b>Stock variance:</b> Overall negative inventory due to manual stock updates.</li>
<li><b>Quality complaints - Item Missing:</b> Incidents (# of items) where a customer raises a complaint for ‘Item Missing at doorstep’ after delivery.</li>
<li><b>Quality complaints - Item Expired:</b> Incidents (# of items) where a customer raises complaints for ‘Item Expired at doorstep’ after delivery.</li>
<li><b>Damages:</b> Items that got damaged in store (Exclusive of Perishables and F&V Items).</li>
<li><b>In-store time within 3 mins (IPO<=8):</b> If an order has less than or equal to 8 items, an on-time order is supposed to have an in-store time of <= 3 mins.</li>
<li><b>In-store time within 5 mins (IPO>8):</b> If an order has greater than 8 items, an on-time order is supposed to have an in-store time of <= 5 mins.</li>
<li><b>Re-inventorization of Return (within 2 hrs):</b> Returned items are supposed to be put away within 2 hours of processing them. The percentage of items whose putaway is completed within 2 hours from the total return putaways comes under this header.</li>
<li><b>Open ESTO:</b> Unaccounted ESTO (STO dispatch - GRN- DN total)</li>

<p><b>Please be informed that penalties will be applied on these SLAs in future payout cycles. Kindly focus on improving wherever performance is not satisfactory.</b></p>
    
<p><b>PS:</b> <i>Please find the raw dump for SLA data in the below excel file.</i></p>
    


<p>Regards,<br />
    Blinkit Merchant Support</p>
<p></p>



