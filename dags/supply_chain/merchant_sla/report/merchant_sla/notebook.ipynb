{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import calendar\n", "import json\n", "import traceback as tb\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Connection and Defining Start Date and End Date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "# if current_date.day==26:\n", "start_date = current_date\n", "end_date = current_date\n", "if current_date.day in (27, 28, 29, 30, 31):\n", "    start_date = date(datetime.now().year, datetime.now().month, 26)\n", "    end_date = current_date - <PERSON><PERSON><PERSON>(1)\n", "    if end_date.year != start_date.year:\n", "        start_date = date(datetime.now().year - 1, datetime.now().month, 26)\n", "    elif start_date.month == 12 and end_date.month == 1:\n", "        start_date = date(datetime.now().year - 1, datetime.now().month, 26)\n", "else:\n", "    if (datetime.now().month - 1) == 0:\n", "        start_month = 12\n", "    else:\n", "        start_month = datetime.now().month - 1\n", "\n", "    start_date = date(datetime.now().year, start_month, 26)\n", "    # datetime.today().replace(day=1).date()\n", "    end_date = current_date - <PERSON><PERSON><PERSON>(1)\n", "    if end_date.year != start_date.year:\n", "        start_date = date(datetime.now().year - 1, start_month, 26)\n", "    elif start_date.month == 12 and end_date.month == 1:\n", "        start_date = date(datetime.now().year - 1, start_month, 26)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# start_date = date(2022,8,26)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end_date = date(2022,9,25)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Merchant and Partner Owned Stores List"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# merchants_q = \"\"\"select distinct outlet_id, outlet_name\n", "# from lake_logistics.logistics_node ln\n", "# inner join lake_logistics.logistics_node_address lna on lna.id = ln.node_address_id\n", "# inner join consumer.merchant_forecast_carts mfc on mfc.merchant_id = ln.external_id\n", "# inner join lake_po.physical_facility_outlet_mapping fom on fom.facility_id = mfc.facility_id\n", "# inner join lake_retail.console_outlet co on co.facility_id = fom.facility_id\n", "# where co.active=1 and business_type_id=7\n", "# \"\"\"\n", "\n", "merchants_q = \"\"\"\n", "select distinct pos_outlet_id as outlet_id, pos_outlet_name as outlet_name\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True and merchant_business_type_id=7\n", "\n", "\"\"\"\n", "\n", "merchants_data = pd.read_sql(merchants_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data = pb.from_sheets(\n", "    \"19U2dE8shA5Ibh3Oar7sW_9A4sMTVJTGV-J9bpppvlPs\", \"Sheet26\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_outlet_list = list(merchant_rollout_data.outlet_id.astype(str).unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## GMV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_q = \"\"\"\n", "\n", "\n", "\n", "with lm_base as \n", "(select distinct date((sch_slot_start)) as sch_date_ist,city,\n", "-----outlet_id, outlet_name,\n", "---backend_merchant_id,\n", "---frontend_merchant_id,\n", "---frontend_merchant_name,\n", "order_id,\n", "current_status\n", "from \n", " (select distinct \n", "        m.city_name as city,\n", "        ----dwbm.backend_merchant_id,\n", "        m.external_id as frontend_merchant_id,\n", "        m.name as frontend_merchant_name,\n", "        o.id as order_id,\n", "    \n", "        date(timestamp 'epoch' + o.slot_start * interval '1 second'+interval '5.5 hrs ') as sch_slot_start,\n", "        o.current_status\n", "        ---dwbm.pos_outlet_id as outlet_id,\n", "        --dwbm.pos_outlet_name as outlet_name\n", "      \n", "        from lake_oms_bifrost.oms_order \n", "            as o\n", "        inner join lake_oms_bifrost.view_oms_merchant \n", "            as m \n", "            on o.merchant_id = m.id \n", "        ----inner join dwh.dim_merchant_outlet_facility_mapping dwbm on m.external_id = dwbm.frontend_merchant_id\n", "        \n", "    \n", "        \n", "        where \n", "            o.type in ('RetailForwardOrder','InternalForwardOrder')\n", "             ---valid_to_utc >= current_date\n", "            ---and dwbm.is_current in (True,False)\n", "            ---and dwbm.is_mapping_enabled = True\n", "            ---and dwbm.is_frontend_merchant_active in (True,False)\n", "            ---and merchant_business_type_id in (7)\n", "            and o.slot_start >= extract(epoch from ('{start_date}'::DATE - interval '5.5 hour')) \n", "            and o.slot_start <= extract(epoch from ('{end_date}'::DATE + interval '1 day'- interval '5.5 hour'))\n", "            AND o.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "            AND m.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "            ---and pos_outlet_id in {outlet_id}\n", "            ) as a \n", "            ),\n", "            \n", "        \n", "\n", "total_gmv as\n", "(select outlet_id,outlet_name,order_id, \n", "\n", "sum(case when invoice_type_id=1 then procurement_price*procurement_quantity end) as forward_sales,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as fnv_forward_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as non_fnv_forward_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_quantity end) as fnv_forward_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_quantity end) as non_fnv_forward_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       pos.outlet_id,\n", "       co.name as outlet_name,\n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "    inner join lake_retail.console_outlet co on co.id = pos.outlet_id\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        and pos.invoice_type_id in (1) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6,7,8) as a\n", "    group by 1,2,3),\n", " \n", "\n", "\n", "\n", "\n", "return_gmv as\n", "(\n", "select order_id, sum(case when fnv_flag='FnV' then return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when fnv_flag='Non-FnV' then return_gmv else 0 end) as non_fnv_return_gmv,\n", "sum(case when fnv_flag='FnV' then return_qty else 0 end) as fnv_return_qty,\n", "sum(case when fnv_flag='Non-FnV' then return_qty else 0 end) as non_fnv_return_qty\n", "from\n", "(select a.order_id::int, a.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-accepted_qty)*price) as return_gmv,\n", "sum((procured_quantity-accepted_qty)) as return_qty\n", "from lake_oms_bifrost.oms_order_item oi \n", "inner join \n", "(select distinct lsl.label_value as order_id, \n", "json_extract_path_text(lsi.product_metadata,'product_id',true) as product_id,\n", "(lsic.accepted_item_quantity) as accepted_qty,\n", "((lsi.value::int)/100) as price\n", "from \n", "lake_logistics.logistics_shipment_label lsl \n", "inner join lake_logistics.logistics_shipment_item lsi on lsl.shipment_id=lsi.shipment_id \n", "inner join lake_logistics.logistics_shipment_item_complaint lsic on lsi.id = lsic.shipment_item_id\n", "where label_type='ORDER_ID' and reason not in ('Item Missing') \n", "and date(lsl.install_ts)>'{start_date}'::date-interval '10 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '10 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '10 day') a\n", "on oi.product_id=a.product_id and oi.order_id = a.order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = a.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where product_title not ilike '%%Smart Bachat Club Membership%%' and (date(oi.install_ts)>'{start_date}'::date-interval '10 day')\n", "group by 1,2,3\n", "\n", "union \n", "\n", "\n", "select sub.order_id::int,o.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-delivered_quantity)*price_per_unit) as return_gmv,\n", "sum((procured_quantity-delivered_quantity)) as return_qty\n", "from lake_last_mile.order_item o\n", "inner join lake_oms_bifrost.oms_suborder sub on sub.id = o.sub_order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = o.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where missing_reason is not null\n", "    and missing_reason not in ('MISSIGN') and sub.current_status not in ('REPROCURED','CANCELLED') and\n", "    (date(sub.install_ts)> '{start_date}'::DATE - INTERVAL '10 day') and\n", "    (date(updated_at)> '{start_date}'::DATE - INTERVAL '10 day')\n", "    group by 1,2,3\n", ") group by 1\n", "),\n", "\n", "\n", "refund_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=2 then procurement_price*procurement_quantity end) as refund_amount,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as fnv_reverse_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as non_fnv_reverse_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_quantity end) as fnv_reverse_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_quantity end) as non_fnv_reverse_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "\n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        and pos.invoice_type_id in (2) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6) as a\n", "    group by 1),\n", "\n", "\n", "\n", "\n", "\n", "rpu_gmv as\n", "( \n", "select order_id, sum(case when fnv_flag='FnV' then rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when fnv_flag='Non-FnV' then rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "sum(case when fnv_flag='FnV' then quantity else 0 end) as fnv_rpu_qty,\n", "sum(case when fnv_flag='Non-FnV' then quantity else 0 end) as non_fnv_rpu_qty\n", "from\n", "\n", "(select oo.original_order_id as order_id, oi.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "oi.selling_price*oi.quantity as rpu_gmv,\n", "oi.quantity\n", "from lake_oms_bifrost.oms_order o \n", "inner join lake_oms_bifrost.oms_internal_order_mapping oo on oo.internal_order_id = o.id\n", "inner join lm_base lm on lm.order_id = oo.original_order_id\n", "inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = o.id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = oi.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where o.type = 'InternalReverseOrder' and o.current_status = 'DELIVERED'\n", "and date(o.install_ts)>='{start_date}'::DATE - INTERVAL '10 day' \n", "and date(oo.install_ts)>='{start_date}'::DATE - INTERVAL '10 day' \n", "group by 1,2,3,4,5\n", ") as a\n", "group by 1\n", ")\n", "\n", "\n", "\n", "\n", "\n", "select sch_date_ist as delivery_date,\n", "b.outlet_id,\n", "b.outlet_name,\n", "---city,\n", "---backend_merchant_id, \n", "---frontend_merchant_id,\n", "--a.order_id,\n", "--frontend_merchant_name,\n", "-- count(distinct a.order_id) as total_sch_orders,\n", "count(distinct case when current_status='DELIVERED' then a.order_id end) as delivered_order_count,\n", "-- count(distinct case when current_status='CANCELLED' then a.order_id end) as cancelled_order_count,\n", "sum(case when current_status='DELIVERED' then forward_sales else 0 end) as forward_sales,\n", "-- sum(case when current_status='DELIVERED' then fnv_forward_sales else 0 end) as fnv_forward_sales,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_forward_sales else 0 end) as non_fnv_forward_sales,\n", "-- sum(case when current_status='DELIVERED' then fnv_forward_item_count else 0 end) as fnv_forward_item_count,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_forward_item_count else 0 end) as non_fnv_forward_item_count,\n", "sum(case when current_status='DELIVERED' then fnv_return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_return_gmv else 0 end) as non_fnv_return_gmv,\n", "-- sum(case when current_status='DELIVERED' then fnv_return_qty else 0 end) as fnv_return_qty,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_return_qty else 0 end) as non_fnv_return_qty,\n", "sum(case when current_status='DELIVERED' then fnv_rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "-- sum(case when current_status='DELIVERED' then fnv_rpu_qty else 0 end) as fnv_rpu_qty,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_rpu_qty else 0 end) as non_fnv_rpu_qty,\n", "\n", "\n", "sum(case when current_status='DELIVERED' and (fnv_return_gmv is null or fnv_return_gmv=0) and \n", "(fnv_rpu_gmv is null or fnv_rpu_gmv=0)\n", "then ref.fnv_reverse_sales\n", "when current_status='DELIVERED' and ref.fnv_reverse_sales-(coalesce(fnv_return_gmv,0)+coalesce(fnv_rpu_gmv,0))>0 then ref.fnv_reverse_sales-(coalesce(fnv_return_gmv,0)+coalesce(fnv_rpu_gmv,0))\n", "else 0 end) as fnv_refund_gmv,\n", "\n", "sum(case when current_status='DELIVERED' and \n", "(non_fnv_return_gmv is null or non_fnv_return_gmv=0) and \n", "(non_fnv_rpu_gmv is null or non_fnv_rpu_gmv=0)\n", "then ref.non_fnv_reverse_sales \n", "when current_status='DELIVERED' and ref.non_fnv_reverse_sales-(coalesce(non_fnv_return_gmv,0)+coalesce(non_fnv_rpu_gmv,0))>0 then ref.non_fnv_reverse_sales-(coalesce(non_fnv_return_gmv,0)+coalesce(non_fnv_rpu_gmv,0))\n", "else 0 end) as non_fnv_refund_gmv\n", "\n", "\n", "from lm_base a \n", "left join total_gmv b on a.order_id=b.order_id\n", "left join return_gmv c on a.order_id=c.order_id\n", "left join rpu_gmv d on a.order_id=d.order_id\n", "left join refund_gmv ref on a.order_id = ref.order_id\n", "where current_status='DELIVERED' and outlet_id in {outlet_id}\n", "---and a.order_id=102757009\n", "group by 1,2,3\n", "        \n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlet_id=tuple(merchants_outlet_list)\n", ")\n", "\n", "\n", "gmv_orders_data = pd.read_sql_query(gmv_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data.delivery_date.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_temp = gmv_orders_data[\n", "    (gmv_orders_data[\"delivery_date\"] >= start_date)\n", "    & (gmv_orders_data[\"delivery_date\"] <= end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_temp = gmv_orders_data_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_temp[\"return_sales\"] = (\n", "    gmv_orders_data_temp[\"fnv_return_gmv\"] + gmv_orders_data_temp[\"non_fnv_return_gmv\"]\n", ")\n", "\n", "gmv_orders_data_temp[\"rpu_sales\"] = (\n", "    gmv_orders_data_temp[\"fnv_rpu_gmv\"] + gmv_orders_data_temp[\"non_fnv_rpu_gmv\"]\n", ")\n", "\n", "gmv_orders_data_temp[\"refund_amount\"] = (\n", "    gmv_orders_data_temp[\"fnv_refund_gmv\"] + gmv_orders_data_temp[\"non_fnv_refund_gmv\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_temp[\"procured_gmv\"] = (\n", "    gmv_orders_data_temp[\"forward_sales\"]\n", "    - gmv_orders_data_temp[\"return_sales\"]\n", "    - gmv_orders_data_temp[\"rpu_sales\"]\n", "    - gmv_orders_data_temp[\"refund_amount\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_new = gmv_orders_data_temp.groupby([\"outlet_id\"], as_index=False).agg(\n", "    {\"delivered_order_count\": \"sum\", \"procured_gmv\": \"sum\"}\n", ")\n", "gmv_orders_data_new.rename(\n", "    columns={\"delivered_order_count\": \"total_order_count\", \"procured_gmv\": \"gmv\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fill Rate DS Impact (Tail Assortment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_q = \"\"\"\n", "With  LandingPrice AS\n", "  (SELECT w.item_id,\n", "          avg(wlp) landing_price\n", "   FROM dwh.logs_weighted_landing_price w\n", "   where source='pricing_logs' and date(end_ts_ist)='2099-01-01' group by 1),\n", "   \n", "FillRateRawData as (\n", "SELECT       slot_end::date AS ScheduledDate,\n", "             slot_end as Slot_End,\n", "             fa.facility_id,\n", "             fa.facilityname,\n", "             fa.outlet_name,\n", "             fa.outlet_id,\n", "             fa.order_id,\n", "             fa.item_id,\n", "             f.picker_id,\n", "             fa.ordered_quantity,\n", "             fa.procured_quantity,\n", "             avg(b.selling_price) as average_selling_price,\n", "             fa.reason_key,\n", "             case when pp.outlet_type = 1 then 'FnV' else 'Grocery/Cold' end as item_type,\n", "             max(pp.name) as item_name,\n", "             max(variant_mrp) as item_mrp,\n", "             max(unfulfilled_order) AS Unfulfilled,\n", "             (case \n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (upper_limit>0) then 'Metering'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.description = 'Created') then 'Nayala'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.description = 'Reprocure') then 'Reschedule_Impact'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,inward_timestamp,picking_time) between 0 and 120) then 'GRN pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,return_timestamp,picking_time) between 0 and 120) then 'return pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (badstock_qty > ordered_quantity and datediff(min,badstock_timestamp,picking_time) between -120 and 120) then 'badstock marked'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (actual_quantity < 5) then 'Tail_Assortment'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.outlet_type=1) then 'FNV' \n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' then 'Checkout'\n", "                  when reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' then 'Partial Inventory'\n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' then 'Reprocure'\n", "                  else 'Others' end) as reason\n", "      FROM metrics.fillrateattribution_darkstores fa left join lake_rpc.product_product pp on pp.item_id=fa.item_id\n", "      left join lake_oms_bifrost.oms_order_item b on b.order_id = fa.order_id and b.product_id = fa.product_id\n", "      left join dwh.fact_supply_chain_order_details f on f.order_id = fa.order_id and f.outlet_id = fa.outlet_id and \n", "      (date(order_checkout_ts_ist) between %(start_date)s and %(end_date)s)\n", "      WHERE (slot_end::date between %(start_date)s and %(end_date)s)\n", "      and reason_key != '' \n", "      and fa.outlet_id in %(merchants_outlet_list)s\n", "      and fa.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9,\n", "               10,\n", "               11,\n", "               13,\n", "               14,\n", "               18\n", ")\n", "\n", "Select FR.*,lp.landing_price, (ordered_quantity - procured_quantity) as pna_qty from FillRateRawData FR left join LandingPrice lp on FR.item_id = lp.item_id\n", "where FR.outlet_id in %(merchants_outlet_list)s and reason='Tail_Assortment'\n", "and item_type <>'FnV'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19\n", "\"\"\"\n", "\n", "\n", "fill_rate_ds_impact_data = pd.read_sql(\n", "    fill_rate_ds_impact_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[fill_rate_ds_impact_data[\"landing_price\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[\"total_pna_loss\"] = (\n", "    fill_rate_ds_impact_data[\"average_selling_price\"]\n", "    * fill_rate_ds_impact_data[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new = fill_rate_ds_impact_data.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"total_pna_loss\": \"sum\", \"order_id\": \"nunique\", \"pna_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new.rename(\n", "    columns={\n", "        \"order_id\": \"fill_rate_impact_order_count\",\n", "        \"pna_qty\": \"fill_rate_impact_item_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkout To <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_q = \"\"\"\n", "\n", "with base as\n", "(select distinct date(order_checkout_ts_ist) as date_ts,\n", "a.order_id,\n", "        a.cart_id,\n", "        m.location as city,\n", "        a.picker_id,\n", "        distinct_items_ordered as total_skus,\n", "       m.id as outlet_id,\n", "       m.name as outlet_name,\n", "       a.total_items_quantity_delivered as ipo,\n", "       datediff(seconds,a.order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "       datediff(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) as picker_assigned_picking_start,\n", "       datediff(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) as picking_start_to_picking_complete,\n", "       datediff(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       datediff(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       datediff(seconds,a.order_checkout_ts_ist,a.order_billing_completed_ts_ist) as checkout_to_bill_time\n", "    from dwh.fact_supply_chain_order_details a\n", "    inner join dwh.fact_sales_order_details b on date(b.order_create_ts_ist) = date(a.order_checkout_ts_ist)\n", "    and a.outlet_id = b.outlet_id and a.order_id = b.order_id\n", "    inner join lake_retail.console_outlet m on m.id = a.outlet_id\n", "    where is_order_delivered = true and m.business_type_id = 7 and is_rescheduled = false\n", "    and (date(a.order_checkout_ts_ist) between %(start_date)s and %(end_date)s)\n", "    and (date(b.order_create_ts_ist) between %(start_date)s and %(end_date)s)\n", "    and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "    and order_picking_completed_ts_ist is not null\n", "    and m.id in %(merchants_outlet_list)s\n", "    ),\n", "    r as \n", "    ( select distinct date_ts,\n", "    order_id,\n", "             cart_id,\n", "             outlet_id,\n", "             outlet_name,\n", "             city,\n", "             total_skus,\n", "             picker_id,\n", "             1.00*(checkout_to_picker_assigned)/60 as checkout_to_picker_assigned,\n", "             1.00*(picker_assigned_picking_start)/60 as picker_assigned_picking_start,\n", "             1.00*(picking_start_to_picking_complete)/60 as picking_start_to_picking_complete,\n", "             1.00*(picking_complete_to_billing_start)/60 as picking_complete_to_billing_start,\n", "             1.00*(billing_start_to_billing_complete)/60 as billing_start_to_billing_complete,\n", "             1.00*(checkout_to_bill_time)/60 as checkout_to_bill_time,\n", "             ipo\n", "        from \n", "        base \n", "    )\n", "    select * from r\n", "\"\"\"\n", "\n", "checkout_to_billed_data = pd.read_sql(\n", "    checkout_to_billed_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# checkout_to_billed_data['QUERY PLAN'].values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data[\n", "    \"checkout_to_picker_assigned<=30(secs)\"\n", "] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"checkout_to_picker_assigned\"] <= 0.5) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data[\"not_delayed_<=3(mins)\"] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"ipo\"] <= 8 and x[\"checkout_to_bill_time\"] <= 3) else 0,\n", "    axis=1,\n", ")\n", "\n", "checkout_to_billed_data[\"total_orders_<=8_ipo\"] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"ipo\"] <= 8) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# checkout_to_billed_data1[\"not_delayed_ipo_>_8\"] = checkout_to_billed_data1.apply(\n", "#     lambda x: 1\n", "#     if (x[\"ipo\"] > 8 and x[\"checkout_to_bill_time\"] <=5)\n", "#     else 0,\n", "#     axis=1,\n", "# )\n", "checkout_to_billed_data[\"not_delayed_<=5(mins)\"] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"ipo\"] > 8 and x[\"checkout_to_bill_time\"] <= 5) else 0,\n", "    axis=1,\n", ")\n", "\n", "checkout_to_billed_data[\"total_orders_>8_ipo\"] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"ipo\"] > 8) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new = checkout_to_billed_data.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"not_delayed_<=3(mins)\": \"sum\",\n", "        \"total_orders_<=8_ipo\": \"sum\",\n", "        \"not_delayed_<=5(mins)\": \"sum\",\n", "        \"total_orders_>8_ipo\": \"sum\",\n", "        \"checkout_to_picker_assigned<=30(secs)\": \"sum\",\n", "        \"order_id\": \"nunique\",\n", "        \"checkout_to_picker_assigned\": \"mean\",\n", "        \"checkout_to_bill_time\": \"mean\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[checkout_to_billed_data_new[\"outlet_id\"] == 1056]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"%TAT <=8 ipo\"] = (\n", "    checkout_to_billed_data_new[\"not_delayed_<=3(mins)\"]\n", "    * 100.0\n", "    / checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"TAT Loss\"] = np.where(\n", "    checkout_to_billed_data_new[\"%TAT <=8 ipo\"] < 100,\n", "    (100 - checkout_to_billed_data_new[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")\n", "\n", "checkout_to_billed_data_new[\"TAT Penalty\"] = np.where(\n", "    checkout_to_billed_data_new[\"%TAT <=8 ipo\"] < 70,\n", "    (70 - checkout_to_billed_data_new[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[checkout_to_billed_data_new[\"outlet_id\"] == 2739]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Not Good and Expired"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_q = \"\"\"\n", "with base as\n", "(select a.order_id as order__id,\n", "       a.selling_price,\n", "       a.product_id,\n", "       m.id as outlet_id,\n", "       m.name as outlet_name,\n", "       m.location as city,\n", "       a.fe_id,\n", "       a.employee_id,\n", "       a.fe_name,\n", "       a.customer_id,\n", "       a.customer_type,\n", "       a.cart_id,\n", "       a.l1_category_name,\n", "       a.product_name,\n", "       a.general_category,\n", "       a.storage_type,\n", "       a.handling_type,\n", "       a.complaint_type,\n", "       complaint_source,\n", "       date(a.delivery_date) as date,\n", "       (case when complaint_category in ('CART_ITEMS_MISSING','ITEMS_MISSING','Items missing - cart','Item missing - LM','ITEM_MISSING_LM','Item(s) is missing',\n", "                  'ITEMS_MISSING') then 'Items_missing' \n", "             when complaint_category in ('EXPIRED_ITEMS','Item(s) is expired','Expired item(s) received','Expired items')  then 'expired_item'\n", "                  end) as complaint_category\n", "    from metrics.complaints a\n", "    inner join lake_retail.console_outlet m on m.id = a.outlet_id\n", "    where m.business_type_id = 7\n", "    and (date(a.delivery_date) between %(start_date)s and %(end_date)s)\n", "    and a.complaint_category in ('CART_ITEMS_MISSING','ITEMS_MISSING','Items missing - cart','Item missing - LM','ITEM_MISSING_LM','Item(s) is missing',\n", "                  'ITEMS_MISSING','EXPIRED_ITEMS','Item(s) is expired','Expired item(s) received','Expired items')\n", "    and m.id in %(merchants_outlet_list)s\n", "----and a.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10115796,10115976)\n", "    )\n", "    select order__id,\n", "          selling_price,\n", "          product_id,\n", "          outlet_id,\n", "          outlet_name,\n", "          city\n", "          fe_id,\n", "          employee_id,\n", "          fe_name,\n", "          customer_id,\n", "          customer_type,\n", "          cart_id,\n", "          l1_category_name,\n", "          product_name,\n", "          general_category,\n", "          storage_type,\n", "          handling_type,\n", "          complaint_type,\n", "          complaint_source,\n", "          date,\n", "          count(distinct case when complaint_category = 'Items_missing' then order__id end) as complaint_category_item_missing,\n", "          count(distinct case when complaint_category = 'expired_item' then order__id end) as complaint_category_expired\n", "        from base \n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19\n", "        \n", "\"\"\"\n", "\n", "qng_expiry_data = pd.read_sql(\n", "    qng_expiry_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data[qng_expiry_data[\"selling_price\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_temp1 = qng_expiry_data.groupby([\"outlet_id\"], as_index=False).agg(\n", "    {\n", "        \"selling_price\": \"sum\",\n", "        \"product_id\": \"nunique\",\n", "        # \"complaint_category_item_missing\": \"sum\",\n", "        # \"complaint_category_expired\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_temp1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_temp = qng_expiry_data[\n", "    [\n", "        \"order__id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date\",\n", "        \"complaint_category_item_missing\",\n", "        \"complaint_category_expired\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_temp = qng_expiry_data_temp.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new = qng_expiry_data_temp.groupby([\"outlet_id\"], as_index=False).agg(\n", "    {\n", "        # \"selling_price\": \"sum\",\n", "        # \"product_id\": \"nunique\",\n", "        \"complaint_category_item_missing\": \"sum\",\n", "        \"complaint_category_expired\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new = qng_expiry_data_new.merge(qng_expiry_data_temp1, on=\"outlet_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new[qng_expiry_data_new[\"complaint_category_expired\"] > 0].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data[qng_expiry_data[\"outlet_id\"] == 2942].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new[qng_expiry_data_new[\"outlet_id\"] == 2942]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_new.rename(\n", "    columns={\"product_id\": \"qng_item_count\", \"selling_price\": \"quality_gmv_loss\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Damage "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_q = \"\"\"\n", "with t1 as (\n", " SELECT \n", "        fc.id as facility_id,\n", "        fc.name as facility_name,\n", "    i.outlet_id as outlet_id,\n", "    ro.name AS Outlet_name,\n", "     r.item_id as item,\n", "     i.variant_id as variant_id,\n", "     i.upc_id as upc,\n", "     r.name AS Product,\n", "     case when b.item_id is not null and r.perishable=1 then 'perishable' end as perishables,\n", "    (CASE WHEN r.outlet_type = 1 THEN 'F&V_type' WHEN r.outlet_type = 2 THEN 'Grocery_type' ELSE 'Not Assigned' END) AS Item_Type,\n", "     SUM(i.\"delta\") AS quantity,\n", "     r.variant_mrp AS Mrp,\n", "     r.variant_uom_text AS grammage,\n", "     convert_timezone('Asia/Kolkata',i.pos_timestamp) AS POS_Timestamp,\n", "     i2.name AS Update_type,\n", "     ibur.name,\n", "     i.created_by_name as employee_id,\n", "     r.is_pl,\n", "     i.weighted_lp as \"weighted_landing_price\",\n", "     coalesce(tax.cess,0) as cess,\n", "     coalesce(tax.cgst,0) as cgst,\n", "     coalesce(tax.sgst,0) as sgst,\n", "     i.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) as cost_price\n", "     \n", " FROM\n", "    lake_ims.ims_inventory_log i\n", "         INNER JOIN\n", "     lake_rpc.product_product r ON r.variant_id = i.variant_id \n", "     \n", "     Left join \n", "     -----(select distinct item_id from metrics.item_wise_categories where L0 in ('Breakfast & Dairy', 'Eggs, Meat, Fish & Frozen')) b\n", "        (select distinct item_id from lake_rpc.item_category_details where L0 in ('chicken, meat & fish', 'dairy, bread & eggs','Dairy, Bread & Eggs','Chicken, Meat & Fish')) b\n", "        on r.item_id=b.item_id\n", "\n", "     \n", "         INNER JOIN\n", "     lake_retail.console_outlet ro ON ro.id = i.outlet_id and business_type_id =7\n", "     left join lake_crates.facility fc on ro.facility_id=fc.id\n", "         INNER JOIN\n", "     lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         LEFT JOIN\n", "     lake_rpc.product_tax tax ON r.item_id = tax.item_id and ro.tax_location_id = tax.location\n", "       LEFT JOIN\n", "       lake_ims.ims_bad_inventory_update_log ibil on i.inventory_update_id=ibil.inventory_update_id\n", "       LEFT join \n", "       lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", " WHERE\n", "     i.inventory_update_type_id in (11) and i.outlet_id in %(merchants_outlet_list)s\n", "      AND (date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "      and Update_type in ('bad_stock_damage_product') and ibil.reason_id not in (6,7,8,9)\n", "    GROUP BY 1,2,3,4,5, 6,7, 8 , 9,10,12,13 ,14,15,16,17,18,19,20,21,22,23\n", "  ) \n", "  \n", "  \n", "Select distinct\n", " t1.*,\n", " Mrp*quantity as Total_mrp,\n", " weighted_landing_price*quantity as Total_landing_Price,\n", " cost_price*quantity as Total_cost_price,\n", " (weighted_landing_price*quantity) - (cost_price*quantity) as Tax\n", "from t1 where outlet_id in %(merchants_outlet_list)s and item_type not in ('perishable','F&V_type')\n", "and perishables is null and item not in (10110215,10000436,10109019,10002963,10109881,10109878,10109880,10109879,10109877,10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976) \n", "  \"\"\"\n", "\n", "\n", "damage_data = pd.read_sql(\n", "    damage_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new = (\n", "    damage_data.groupby([\"outlet_id\"])\n", "    .agg({\"quantity\": \"sum\", \"total_landing_price\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new = damage_data_new.rename(columns={\"quantity\": \"damage_item_count\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Variance in Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_q = \"\"\"\n", "\n", "\n", "SELECT\n", "        date(pos_timestamp +interval '5.5 hours') as pos_timestamp,\n", "        (pos_timestamp +interval '5.5 hours') as pos_time,\n", "        -- date_trunc('{{day_week_month}}',(iil.pos_timestamp + interval '5.5 hrs')::date) AS {{day_week_month}},\n", "        iil.outlet_id as outlet_id,\n", "        o.name as outlet_name,\n", "        o.location as city,\n", "        p.item_id as \"Item ID\",\n", "        p.name as item_name,\n", "        inventory_operation as \"Operation\",\n", "        iil.\"delta\" as \"Change Quantity\",\n", "        iil.weighted_lp as landing_price,\n", "        iiut.name as \"Inventory Update Type\",\n", "        iil.created_by_name as \"Employee ID\", \n", "        CASE WHEN l0_id in (1487) then 'fnv'\n", "                   WHEN l0_id in (888,15,4,14,12) and l1_id in (279,1425,1361,1362,1363,1388,922,123,923,1200,953,1184) \n", "                    and l2_id in (1956,1425,63,1367,1369,1730,127,1094,949,138,1091,1185,123,1778,950,1389,31,1097,198,1093) then 'perishable'\n", "                   ELSE 'packaged' end as category,\n", "        (iil.\"delta\"*iil.weighted_lp) as Total_landing_Price\n", "        \n", "        \n", "        FROM \n", "        consumer.view_ims_non_infra_inventory_log iil\n", "        INNER JOIN\n", "        lake_rpc.product_product p ON iil.variant_id=p.variant_id and p.active=1\n", "         LEFT JOIN \n", "                    lake_rpc.item_category_details cd\n", "                on p.item_id = cd.item_id\n", "        \n", "        INNER JOIN\n", "        lake_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "        INNER JOIN\n", "        lake_retail.console_outlet o ON o.id=iil.outlet_id and business_type_id =7\n", "        INNER JOIN lake_crates.facility fc on o.facility_id=fc.id\n", "        LEFT JOIN lake_rpc.product_tax tax ON p.item_id = tax.item_id and o.tax_location_id = tax.location\n", "        \n", "        WHERE  (date(convert_timezone('Asia/Kolkata',iil.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "            and p.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10109877,10115796,10115976)\n", "            and iil.outlet_id in %(merchants_outlet_list)s\n", "            and l0_id <> 1487\n", "            and\n", "            CASE \n", "                WHEN \n", "                    -- On below dates ESTO transfer loss was atrributed in \n", "                    -- inventory update_id = 42\n", "                       iil.pos_timestamp BETWEEN ('2022-07-30 00:00:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-07-30 23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-05 23:00:00'::TIMESTAMP -  interval '5.5 hrs') AND ('2022-08-06  23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-15 23:00:00'::TIMESTAMP -  interval '5.5 hrs') AND ('2022-08-18 23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-23 23:45:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-24 01:15:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-24 22:00:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-25 03:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-26 23:30:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-27 04:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-28 23:30:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-29 02:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR (iil.created_by_name = 'GR0001' AND o.business_type_id = 7)\n", "                    \n", "                    \n", "                THEN \n", "                    iil.inventory_update_type_id IN (\n", "                        14, 26, 44, 45, 46, 47, 48, 74, 118, --Positive\n", "                        15, 27, 39, 40, 41, 43, 73, 117, 129, 130, 131, 132 --negative\n", "                    )\n", "                ELSE\n", "                    iil.inventory_update_type_id IN (\n", "                        14, 26, 44, 45, 46, 47, 48, 74, 118, --Positive\n", "                        15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132 --negative\n", "                    )\n", "            END\n", "         \n", "        -- Group by 1,2,3,4,5,6,7,8,9\n", "   \n", "\n", "        \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\n", "\n", "\"\"\"\n", "\n", "inventory_variance_data = pd.read_sql(\n", "    inventory_variance_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"-\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative[\"total_price_change\"] = (\n", "    inventory_variance_data_negative[\"landing_price\"]\n", "    * inventory_variance_data_negative[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_new = inventory_variance_data_negative.groupby(\n", "    [\"outlet_id\", \"item id\"], as_index=False\n", ").agg({\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp = fill_rate_ds_impact_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group = fill_rate_ds_impact_data_temp.groupby(\n", "    [\"outlet_id\", \"item_id\"], as_index=False\n", ").agg({\"order_id\": \"nunique\", \"pna_qty\": \"sum\", \"total_pna_loss\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final = inventory_variance_data_negative_new.merge(\n", "    fill_rate_ds_impact_data_temp_group,\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    left_on=[\"outlet_id\", \"item id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\n", "    \"pna_qty\"\n", "] = inventory_variance_data_negative_final[\"pna_qty\"].fillna(0)\n", "\n", "\n", "inventory_variance_data_negative_final[\n", "    \"total_pna_loss\"\n", "] = inventory_variance_data_negative_final[\"total_pna_loss\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"qty_to_consider\"] = (\n", "    inventory_variance_data_negative_final[\"change quantity\"]\n", "    - inventory_variance_data_negative_final[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"qty_to_consider\"] = np.where(\n", "    inventory_variance_data_negative_final[\"qty_to_consider\"] < 0,\n", "    0,\n", "    inventory_variance_data_negative_final[\"qty_to_consider\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"negative_change\"] = (\n", "    inventory_variance_data_negative_final[\"total_price_change\"]\n", "    - inventory_variance_data_negative_final[\"total_pna_loss\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"negative_change\"] = np.where(\n", "    inventory_variance_data_negative_final[\"negative_change\"] < 0,\n", "    0,\n", "    inventory_variance_data_negative_final[\"negative_change\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\n", "    inventory_variance_data_negative_final[\"pna_qty\"] > 0\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_group = (\n", "    inventory_variance_data_negative_final.groupby([\"outlet_id\"])[\"negative_change\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"+\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive[\"total_price_change\"] = (\n", "    inventory_variance_data_positive[\"landing_price\"]\n", "    * inventory_variance_data_positive[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_variance_raw = pd.concat(\n", "    [inventory_variance_data_negative_final, inventory_variance_data_positive]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_group = inventory_variance_data_positive.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final = inventory_variance_data_negative_group.merge(\n", "    inventory_variance_data_positive_group, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final = inventory_variance_data_final.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"] = (\n", "    inventory_variance_data_final[\"total_price_change\"]\n", "    - inventory_variance_data_final[\"negative_change\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"] = np.where(\n", "    inventory_variance_data_final[\"stock_variance_loss\"] > 0,\n", "    0,\n", "    inventory_variance_data_final[\"stock_variance_loss\"] * -1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final = inventory_variance_data_final[\n", "    [\"outlet_id\", \"stock_variance_loss\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[inventory_variance_data_final[\"outlet_id\"] == 1393]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_final.to_csv('stock_variance_loss.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_new_new = inventory_variance_data_final.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_raw = inventory_variance_data_positive[\n", "    [\n", "        \"pos_timestamp\",\n", "        \"pos_time\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"item id\",\n", "        \"item_name\",\n", "        \"operation\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"inventory update type\",\n", "        \"employee id\",\n", "        \"category\",\n", "        \"total_price_change\",\n", "    ]\n", "]\n", "\n", "inventory_variance_data_negative_raw1 = inventory_variance_data_negative[\n", "    [\n", "        \"pos_timestamp\",\n", "        \"pos_time\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"item id\",\n", "        \"item_name\",\n", "        \"operation\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"inventory update type\",\n", "        \"employee id\",\n", "        \"category\",\n", "        \"total_price_change\",\n", "    ]\n", "]\n", "\n", "inventory_variance_data_negative_raw = inventory_variance_data_negative_final.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw = inventory_variance_data_negative_raw[\n", "    [\n", "        \"outlet_id\",\n", "        \"item id\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"pna_qty\",\n", "        \"qty_to_consider\",\n", "        \"total_pna_loss\",\n", "        \"negative_change\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw = inventory_variance_data_negative_raw.rename(\n", "    columns={\n", "        \"change quantity\": \"Negative Qty\",\n", "        \"qty_to_consider\": \"Negative_qty - pna_qty\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete GRN Time of STO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_q = \"\"\"\n", "with t1 as (\n", " SELECT \n", "\n", "    i.outlet_id as outlet_id,\n", "    ro.name AS Outlet_name,\n", "     r.item_id as item_id,\n", "     -- i.variant_id as variant_id,\n", "     -- i.upc_id as upc,\n", "     r.name AS Product,\n", "     -- case when b.item_id is not null and r.perishable=1 then 'perishable' end as perishables,\n", "    -- (CASE WHEN r.outlet_type = 1 THEN 'F&V_type' WHEN r.outlet_type = 2 THEN 'Grocery_type' ELSE 'Not Assigned' END) AS Item_Type,\n", "     r.variant_mrp AS Mrp,\n", "     -- r.variant_uom_text AS grammage,\n", "     i.pos_timestamp+interval '5.5 hours' AS POS_Timestamp,\n", "     i2.name AS Update_type,\n", "     ibur.name as reason_name,\n", "     i.created_by_name as created,\n", "     r.is_pl,\n", "     i.weighted_lp as \"weighted_landing_price\",\n", "     SUM(i.\"delta\") AS quantity\n", "     \n", " FROM\n", "    lake_ims.ims_inventory_log i\n", "         INNER JOIN\n", "     lake_rpc.product_product r ON r.variant_id = i.variant_id \n", "     \n", "     -- Left join (select distinct item_id from lake_rpc.item_category_details where L0 in ('chicken, meat & fish', 'dairy, bread & eggs')) b\n", "     --    on r.item_id=b.item_id\n", "\n", "     \n", "         INNER JOIN\n", "     lake_retail.console_outlet ro ON ro.id = i.outlet_id and business_type_id =7\n", "     left join lake_crates.facility fc on ro.facility_id=fc.id\n", "         INNER JOIN\n", "     lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         -- LEFT JOIN\n", "     -- lake_rpc.product_tax tax ON r.item_id = tax.item_id and ro.tax_location_id = tax.location\n", "       inner JOIN\n", "       lake_ims.ims_bad_inventory_update_log ibil on i.inventory_update_id=ibil.inventory_update_id\n", "       inner join \n", "       lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", " WHERE\n", "     i.inventory_update_type_id in (11) and i.outlet_id in %(merchants_outlet_list)s\n", "      AND (date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "      and Update_type in ('bad_stock_damage_product')\n", "      and r.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", "      and ibil.reason_id in (6,7,8,9)\n", "    GROUP BY 1,2,3,4,5, 6,7, 8 , 9,10,11\n", "  ),\n", "\n", "t2 as\n", "(\n", "select distinct receiving_outlet_id as outlet_id, item_id, grn_started_at\n", "from metrics.esto_details \n", "where (date(grn_started_at) between %(start_date)s and %(end_date)s)\n", "and outlet_id in %(merchants_outlet_list)s\n", "and item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", ")\n", "\n", "\n", "\n", "\n", "select date_ts, outlet_id, outlet_name, avg(bad_stock_update_time) as avg_bad_stock_update_time\n", "from\n", "(Select \n", " t1.outlet_id,t1.outlet_name,date(POS_Timestamp) as date_ts,\n", " t1.item_id, datediff(seconds,grn_started_at, POS_Timestamp) as bad_stock_update_time\n", " -- perishables,item_type,\n", " --count(distinct t1.item) as damage_item_count,\n", " -- sum(quantity) as damage_item_count,\n", " ---Mrp*quantity as Total_mrp,\n", "-- sum(weighted_landing_price*quantity) as Total_landing_Price\n", "--- cost_price*quantity as Total_cost_price,\n", " ---(weighted_landing_price*quantity) - (cost_price*quantity) as Tax\n", "from t1 \n", "inner join t2 on t1.outlet_id = t2.outlet_id and date(t1.POS_Timestamp) = date(t2.grn_started_at)\n", "---and t1.invoice_id = t2.invoice_id\n", "and t1.item_id = t2.item_id\n", "where t1.outlet_id in %(merchants_outlet_list)s \n", "-- and item_type not in ('perishable','F&V_type')\n", "-- and perishables is null\n", "---not in ('perishable')\n", "group by 1,2,3,4,5)\n", "group by 1,2,3\n", "\n", "\"\"\"\n", "\n", "\n", "grn_to_sto_disc_data = pd.read_sql(\n", "    grn_to_sto_disc_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new = grn_to_sto_disc_data.groupby(\n", "    [\"outlet_id\", \"date_ts\"], as_index=False\n", ").agg({\"avg_bad_stock_update_time\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new[\"delay_flag\"] = grn_to_esto_data_new[\n", "    \"avg_bad_stock_update_time\"\n", "].apply(lambda x: 1 if x > 14400 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new_new = grn_to_esto_data_new.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"delay_flag\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new_new.rename(\n", "    columns={\"receiver_outlet_id\": \"outlet_id\", \"delay_flag\": \"delayed_indent\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_new_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reinventorization of Returns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_q = \"\"\"\n", "\n", "\n", "SELECT date(put_list.created_at+interval '5.5 hours') as date_ts,\n", "put_list.outlet_id,\n", "put_list.id as putlist_id, \n", "\n", "      put_list.created_at+interval '5.5 hours' as Putlist_Created_at,\n", "      put_list.updated_at+interval '5.5 hours' as Putlist_updated_at,\n", "      datediff(seconds, Putlist_Created_at, Putlist_updated_at) as putaway_time,\n", "      sum(quantity) as qty\n", "       -- put_log.pos_timestamp as put_list_ended_at\n", "\n", "            \n", "FROM lake_warehouse_location.warehouse_put_list put_list\n", "INNER JOIN lake_retail.console_outlet outlet ON outlet.id=put_list.outlet_id\n", "\n", "INNER JOIN lake_warehouse_location.warehouse_put_list_item put_list_item ON put_list_item.putlist_id=put_list.id\n", "\n", "where put_list.current_state_id=4 and put_list.outlet_id in %(merchants_outlet_list)s and creation_type=2 and\n", "item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", "and date(put_list.created_at) between %(start_date)s and %(end_date)s\n", "group by 1,2,3,4,5\n", "---) as a\n", "---where \n", "---(date(Putlist_Created_at) = date(Putlist_updated_at)\n", "---group by 1,2\n", "\n", "\n", "\"\"\"\n", "\n", "reinventorization_returns_data = pd.read_sql(\n", "    reinventorization_returns_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data[\"is_delayed\"] = reinventorization_returns_data[\n", "    \"putaway_time\"\n", "].apply(lambda x: 1 if x > 7200 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_new = reinventorization_returns_data.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"is_delayed\": \"sum\", \"putlist_id\": \"nunique\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Open ESTO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_q = \"\"\"\n", "with sto_billed_grn_raw AS(\n", "select (sto.created_at + interval '5.5 hour') as sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (case when id.outlet_type = 1 then 'fnv' when id.perishable = 1 then 'perishable' else 'packaged' end) AS item_type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   \n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id = pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   JOIN lake_retail.console_outlet c on c.id = pi.outlet_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "                FROM lake_ims.ims_inventory_log\n", "                WHERE (date(pos_timestamp+interval '5.5 hours') between '{start_date}'::date- interval '10 days' and '{end_date}')\n", "                and inventory_update_type_id in (1,28,76,90,93)\n", "                group by 1,2\n", "                ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,14,16)\n", "   and sto.merchant_outlet_id in {merchants_outlet_list}\n", "     AND (date(pi.pos_timestamp+interval '5.5 hours') between '{start_date}'::date- interval '10 days' and '{end_date}')\n", "     and item_type in ('perishable','packaged')\n", "     \n", "   group by 1,2,3,4,5,6,7,8,9,13\n", "   )\n", "   ,\n", "sto_billed_grn AS (\n", "SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.item_type,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "\n", "FROM sto_billed_grn_raw sbg\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,17,18\n", ")\n", ",\n", "discrepancy AS(\n", "SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at)) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE (date(created_at+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "            and outlet_id in {merchants_outlet_list}\n", "            GROUP BY 1\n", "            ) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at\n", "         ) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id = dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN (SELECT item_id, upc FROM lake_rpc.product_product group by 1,2) pp ON dnpd.upc_id = pp.upc\n", ") TEMP\n", "GROUP BY 1,2\n", ")\n", ",\n", "return_b2b AS(\n", "SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "FROM(\n", "SELECT \n", "    pi.invoice_id ret_invoice_id,\n", "    pi.original_invoice_id,\n", "    pp.item_id,\n", "    pipd.variant_id,\n", "    max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "    sum(pipd.quantity) AS b2b_return_qty\n", "      \n", "FROM lake_pos.view_pos_invoice pi\n", "INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "WHERE pi.invoice_type_id IN (8,15)\n", "    AND pi2.invoice_type_id IN (5,14,16)\n", "    AND (date(pi.pos_timestamp+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "GROUP BY 1,2,3,4\n", ")tab1\n", "LEFT JOIN (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE WHEN update_type_id IN (23,35) THEN ret_qty END) AS b2b_customer_return,\n", "             sum(CASE WHEN update_type_id IN (24,36) THEN ret_qty END) AS b2b_customer_return_expired,\n", "             sum(CASE WHEN update_type_id IN (25,37) THEN ret_qty END) AS b2b_customer_return_damaged,\n", "             sum(CASE WHEN update_type_id IN (66,68) THEN ret_qty END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE WHEN update_type_id=119 THEN ret_qty END) AS lost_in_transit_positive,\n", "             sum(CASE WHEN update_type_id=120 THEN ret_qty END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "             \n", "             FROM (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE (date(iil.pos_timestamp+interval '5.5 hours') between '{start_date}'::date- interval '10 days' and '{end_date}')\n", "         ) TEMP\n", "      GROUP BY 1,2,3\n", ")tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id\n", "GROUP BY 1,2\n", ")\n", ",\n", "consignment_reach as(\n", "with trip_details as(\n", "                SELECT \n", "                    invoice_id,\n", "                    fc.id as trip_id,\n", "                    min(rec.started_at + interval '5.5 hours') AS dispatch_time,\n", "                    min(rec.completed_at + interval '5.5 hours') AS delivered_time,\n", "                    min(rec.unloading_started_at + interval '5.5 hours') AS unloading_time\n", "                \n", "                FROM lake_vendor_console.fleet_consignment_invoice_mapping icn \n", "                JOIN lake_vendor_console.fleet_consignment rec ON rec.id = icn.consignment_id AND rec.active = True\n", "                JOIN lake_vendor_console.fleet_trip_details fc ON fc.id = rec.trip_id AND fc.active =  True\n", "                LEFT JOIN lake_vendor_console.fleet_consignment_state_transition_log l on l.consignment_id = icn.consignment_id and l.state = 'DELIVERED'\n", "                LEFT JOIN lake_vendor_console.fleet_trip_state_transition_log m on m.trip_id = rec.trip_id and m.state = 'IN_TRANSIT'\n", "                where icn.active = True \n", "                and invoice_id in (select invoice_id from sto_billed_grn group by 1)\n", "                group by 1,2\n", "                )\n", "                ,\n", "                completed_details as (\n", "                select \n", "                    trip_id,\n", "                    min(created_at + interval '5.5 hours') AS return_to_backend\n", "                \n", "                from lake_vendor_console.fleet_trip_state_transition_log \n", "                where state='COMPLETED'\n", "                group by 1\n", "                )\n", "                \n", "                select * from trip_details td\n", "                inner join completed_details cd on td.trip_id = cd.trip_id\n", ")\n", ",\n", "final AS (\n", "SELECT \n", "    sbg.sto_id,\n", "    sbg.outlet_id send_outlet_id,\n", "    ozm1.outlet_name AS send_outlet,\n", "    sbg.merchant_outlet_id rec_outlet_id,\n", "    ozm2.outlet_name AS rec_outlet,\n", "    sbg.invoice_id,\n", "    sbg.sto_created_at,\n", "    sbg.invoice_creation,\n", "    (pi.pos_timestamp + interval '5.5 hours') as bill_ts,\n", "    sbg.grn_timestamp,\n", "    disc_created_at,\n", "    sbg.billing_unit_price,\n", "    hot_landing_price,\n", "    sbg.transfer_state,\n", "    sbg.item_id,\n", "    sbg.Item_name,\n", "    sbg.item_type,\n", "    sbg.billed_qty,\n", "    sbg.grn_quantity,\n", "    sbg.billing_price,\n", "    sbg.diff_qty diff_qty_billed_grn,\n", "    sbg.diff_amt diff_amt_billed_grn,\n", "    COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "    wrong_item_qty,\n", "    variant_mismatch_qty,\n", "    UPC_not_scannable_qty,\n", "    short_qty,\n", "    quality_issue_qty,\n", "    expiry_nte_qty,\n", "    freebies_missing_qty,\n", "    excess_qty,\n", "    damage_qty,\n", "    COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", "    COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", "    COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", "    COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", "    COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", "    COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", "    COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative,\n", "    min(delivered_time) arrive_ds_ts,\n", "    min(unloading_time) unload_ds_ts,\n", "    max(return_to_backend) return_wh_ts,\n", "    max(return_timestamp) return_ts\n", "   \n", "FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", "   left join lake_pos.pos_invoice pi on pi.invoice_id = sbg.invoice_id \n", "            and (date(pi.pos_timestamp+interval '5.5 hours') between '{start_date}'::date- interval '10 days' and '{end_date}')\n", "    left join consignment_reach cr on cr.invoice_id = sbg.invoice_id\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39\n", ")\n", ",\n", "di as(\n", "select d.sto_id, \n", "          d.id,\n", "          max(dib.created_at) + interval '5.5 hrs' sto_dispatched_at\n", "\n", "from lake_warehouse_location.dispatch_indent d\n", "left join (select dispatch_indent_id, max(created_at) as created_at\n", "              from lake_warehouse_location.dispatch_indent_box\n", "              where state = 2\n", "              group by 1) dib on dib.dispatch_indent_id = d.id\n", "where d.sto_id in (select sto_id from final)\n", "and (date(dib.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "group by 1,2\n", ")\n", ",\n", "base as(\n", "SELECT \n", "    f.sto_id,\n", "    send_outlet_id,\n", "    send_outlet,\n", "    cl.name as sender_city_name,\n", "    rec_outlet_id,\n", "    rec_outlet,\n", "    cl1.name as receiver_city_name,\n", "    invoice_id,\n", "    arrive_ds_ts,\n", "    unload_ds_ts,\n", "    sto_created_at,\n", "    invoice_creation,\n", "    bill_ts,\n", "    grn_timestamp,\n", "    disc_created_at,\n", "    return_wh_ts,\n", "    return_ts,\n", "    item_id,\n", "    item_name,\n", "    item_type,\n", "    billing_unit_price,\n", "    hot_landing_price,\n", "    case\n", "        WHEN transfer_state = 1 THEN 'Raised'\n", "        WHEN transfer_state = 2 THEN 'GRN Complete'\n", "        WHEN transfer_state = 3 THEN 'GRN Partial'\n", "        WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "        WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "        ELSE 'NA'\n", "    end AS Invoice_State,\n", "    case when di.sto_dispatched_at is NOT null THEN 'Dispatched' ELSE 'NA' END AS Invoice_Item_State,\n", "    billing_price,\n", "    billed_qty,\n", "    grn_quantity,\n", "    discrepancy_qty::int as total_discrepancy_qty,\n", "    wrong_item_qty,\n", "    variant_mismatch_qty,\n", "    UPC_not_scannable_qty,\n", "    short_qty,\n", "    quality_issue_qty,\n", "    expiry_nte_qty,\n", "    freebies_missing_qty,\n", "    excess_qty,\n", "    damage_qty,\n", "    tot_returned,\n", "    b2breturn_good,\n", "    b2breturn_expired,\n", "    b2breturn_damaged,\n", "    b2breturn_near_expiry,\n", "    lost_in_transit_negative,\n", "    lost_in_transit_positive,\n", "    diff_qty_billed_grn,\n", "    (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "    diff_amt_billed_grn,\n", "    (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "    hot_landing_price*tot_returned AS b2b_return_amount,\n", "    case when extract('hours' from grn_timestamp) between 4 and 12 then 'Morning' \n", "            when extract('hours' from grn_timestamp) between 13 and 19 then 'Evening'\n", "            else 'Night' end as slot\n", "\n", "FROM final f\n", "LEFT JOIN lake_retail.console_outlet co ON co.id = f.send_outlet_id\n", "left join lake_retail.console_location cl on cl.id = co.tax_location_id\n", "LEFT JOIN lake_crates.facility fc ON co.facility_id = fc.id\n", "LEFT JOIN lake_retail.console_outlet co1 ON co1.id = f.rec_outlet_id\n", "left join lake_retail.console_location cl1 on cl1.id = co1.tax_location_id\n", "left join di on di.id = f.invoice_id and di.sto_id = f.sto_id\n", "WHERE billed_qty > 0\n", "and send_outlet not like '%%Infra%%'\n", "and rec_outlet not like '%%Infra%%'\n", "),\n", "\n", "\n", "final_summary as\n", "(select\n", "    invoice_creation::date as date_,\n", "    rec_outlet_id,\n", "    rec_outlet as rec_outlet_name,\n", "    case when receiver_city_name = 'Gurgaon' then 'HR-NCR' when receiver_city_name = 'Ghaziabad' then 'UP-NCR' \n", "        when receiver_city_name = '<PERSON>ida' then 'UP-NCR' when receiver_city_name = 'Budhpur' then 'Delhi' \n", "        else receiver_city_name end as rec_outlet_city,\n", "    slot,\n", "    item_type,\n", "    \n", "    min(arrive_ds_ts) min_arrival_ts,\n", "    max(unload_ds_ts) max_unload_ts,\n", "    min(unload_ds_ts) min_unload_ts,\n", "    max(disc_created_at) max_dn_ts,\n", "    \n", "    (extract('seconds' from max(unload_ds_ts) - min(arrive_ds_ts))/3600) as arrival_to_unload,\n", "    \n", "    (extract('seconds' from max(disc_created_at) - min(unload_ds_ts))/3600) as unload_to_dn,\n", "    \n", "    sum(billed_qty) as sto_qty,\n", "    sum(billed_qty*hot_landing_price) as sto_amt,\n", "    \n", "    sum(grn_quantity) as grn_qty,\n", "    sum(grn_quantity*hot_landing_price) as grn_amt,\n", "    \n", "    sum(short_qty) as dn_short_qty,\n", "    sum(short_qty*hot_landing_price) as dn_short_amt,\n", "    \n", "    sum(excess_qty + damage_qty + wrong_item_qty + variant_mismatch_qty + UPC_not_scannable_qty + quality_issue_qty + expiry_nte_qty + freebies_missing_qty) as other_dn_qty,\n", "    sum((excess_qty + damage_qty + wrong_item_qty + variant_mismatch_qty + UPC_not_scannable_qty + quality_issue_qty + expiry_nte_qty + freebies_missing_qty) * hot_landing_price) as other_dn_amt,\n", "    \n", "    sum(total_discrepancy_qty) as ttl_dn_qty,\n", "    sum(total_discrepancy_qty * hot_landing_price) as ttl_dn_amt,\n", "    \n", "    max(return_wh_ts) min_return_wh_ts,\n", "    min(disc_created_at) min_dn_ts,\n", "    max(return_ts) max_b2b_ts,\n", "    \n", "    (extract('seconds' from max(return_wh_ts) - min(unload_ds_ts))/3600) as fc_return_tat,\n", "    \n", "    (extract('seconds' from max(return_ts) - min(disc_created_at))/3600) as b2b_tat,\n", "    \n", "    sum(b2breturn_good) as b2b_gs_qty,\n", "    sum(b2breturn_good * hot_landing_price) as b2b_gs_amt,\n", "    \n", "    sum(b2breturn_expired + b2breturn_damaged + b2breturn_near_expiry) as b2b_dmg_qty,\n", "    sum((b2breturn_expired + b2breturn_damaged + b2breturn_near_expiry) * hot_landing_price) as b2b_dmg_amt,\n", "    \n", "    sum(lost_in_transit_positive) as lit_pstv_qty,\n", "    sum(lost_in_transit_positive  * hot_landing_price) as lit_pstv_amt,\n", "    \n", "    sum(lost_in_transit_negative) as lit_ngtv_qty,\n", "    sum(lost_in_transit_negative * hot_landing_price) as lit_ngtv_amt,\n", "    \n", "    (b2b_gs_qty + b2b_dmg_qty + lit_pstv_qty + lit_ngtv_qty) as b2b_qty,\n", "    (b2b_gs_amt + b2b_dmg_amt + lit_pstv_amt + lit_ngtv_amt) as b2b_amount,\n", "    \n", "    sum(diff_qty) diff_qty_,\n", "    sum(diff_amt) diff_amount,\n", "    \n", "    'All' as flag,\n", "    getdate() + interval '5.5 hours' as upd_ts,\n", "    \n", "    sum(case when Invoice_Item_State = 'Dispatched' then billed_qty end) as sto_dispatched_qty,\n", "    sum(case when Invoice_Item_State = 'Dispatched' then billed_qty*hot_landing_price end) as sto_dispatched_amt\n", "\n", "from base\n", "where item_id not in (10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", "group by 1,2,3,4,5,6\n", ")\n", "\n", "\n", "select\n", "date_,\n", "date(coalesce(min_arrival_ts,min_unload_ts)) as vehicle_arrival_date,\n", "    rec_outlet_id as outlet_id,\n", "    rec_outlet_name,\n", "    rec_outlet_city,\n", "    slot,\n", "    item_type,\n", "    \n", "    min_arrival_ts,\n", "max_unload_ts,\n", "min_unload_ts,\n", "max_dn_ts,\n", "    \n", "    \n", "coalesce(sto_qty,0) as sto_billed_qty,\n", "coalesce(sto_amt,0) as sto_amt,\n", "    \n", "coalesce(grn_qty,0) as grn_qty,\n", "coalesce(grn_amt,0) as grn_amt,\n", "    \n", "coalesce(dn_short_qty,0) as dn_short_qty,\n", "coalesce(dn_short_amt,0) as dn_short_amt,\n", "    \n", "coalesce(other_dn_qty,0) as other_dn_qty,\n", "coalesce(other_dn_amt,0) as other_dn_amt,\n", "    \n", "coalesce(ttl_dn_qty,0) as ttl_dn_qty,\n", "coalesce(ttl_dn_amt,0) as ttl_dn_amt,\n", "    \n", "    \n", "coalesce(fc_return_tat,0) as fc_return_tat,\n", "    \n", "coalesce(b2b_tat,0) as b2b_tat,\n", "    \n", "    \n", "(coalesce(sto_dispatched_qty,0)-coalesce(grn_qty,0)-coalesce(dn_short_qty,0)-coalesce(other_dn_qty,0)) open_esto_qty,\n", "(coalesce(sto_dispatched_amt,0)-coalesce(grn_amt,0)-coalesce(dn_short_amt,0)-coalesce(other_dn_amt,0)) open_esto_loss,\n", "    \n", "coalesce(sto_dispatched_qty,0) as sto_dispatched_qty,\n", "coalesce(sto_dispatched_amt,0) as sto_dispatched_amt\n", "\n", "\n", "from final_summary\n", "where item_type = 'packaged'\n", "and (date(coalesce(min_arrival_ts,min_unload_ts)) between '{start_date}' and '{end_date}')\n", "\n", "\"\"\".format(\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    merchants_outlet_list=tuple(merchants_outlet_list),\n", ")\n", "\n", "\n", "open_esto_data = pd.read_sql(open_esto_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data[\"open_esto_loss\"] = (\n", "    open_esto_data[\"sto_dispatched_amt\"]\n", "    - open_esto_data[\"grn_amt\"]\n", "    - open_esto_data[\"dn_short_amt\"]\n", "    - open_esto_data[\"other_dn_amt\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data[\"open_esto_loss\"] = np.where(\n", "    open_esto_data[\"open_esto_loss\"] < 0, 0, open_esto_data[\"open_esto_loss\"]\n", ")\n", "\n", "open_esto_data[\"open_esto_qty\"] = np.where(\n", "    open_esto_data[\"open_esto_qty\"] < 0, 0, open_esto_data[\"open_esto_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data = open_esto_data.rename(columns={\"vehicle_arrival_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_new = (\n", "    open_esto_data.groupby(\"outlet_id\")\n", "    .agg({\"open_esto_loss\": \"sum\", \"open_esto_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "# open_esto_data_new = open_esto_data.groupby('outlet_id').agg({'sto_dispatched_amt':'sum','grn_amt':'sum','dn_short_amt':'sum','other_dn_amt':'sum'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open_esto_data_new['open_esto_loss'] = open_esto_data_new['sto_dispatched_amt']-open_esto_data_new['grn_amt']-open_esto_data_new['dn_short_amt']-open_esto_data_new['other_dn_amt']\n", "# open_esto_data_new['open_esto_loss'] = np.where(open_esto_data_new['open_esto_loss']<0,0,open_esto_data_new['open_esto_loss'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_new[open_esto_data_new[\"outlet_id\"] == 995]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DN Process"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_q = \"\"\"\n", "select \n", "invoice_date_ist as date_,\n", "a.ds_unloaded_at_ist as unloading_ts, \n", "a.ds_vehicle_arrival_at_ist as consignment_received_at,\n", "grn_at_ist as grn_timestamp,\n", "dn_at_ist as disc_created_at,\n", "receiver_outlet_id as rec_outlet_id, a.receiver_outlet_name as rec_outlet_name,a.sto_id, a.invoice_id,a.item_id, \n", "item_type,\n", "billed_qty as sto_qty, billed_amt as sto_amt,dispatch_qty as sto_dispatch_qty, dispatch_amt as sto_dispatch_amt,\n", "grn_qty as inwarded_qty,grn_amt as inwarded_amt, (coalesce(dn_short_qty,0) + coalesce(dn_other_qty,0)) as ttl_dn_qty, (coalesce(dn_short_amt,0) + coalesce(dn_other_amt,0)) as ttl_dn_amt,\n", "b2b_amt as ttl_b2b_ret_amount, b2b_qty as ttl_b2b_ret_qty, coalesce(dn_other_amt,0) as dn_other_amount\n", "from consumer.transfer_loss_v3 a\n", "---left join metrics.esto_details b on a.sto_id = b.sto_id and a.item_id = b.item_id and a.invoice_id = b.invoice_id\n", "---and b.sto_created_at> ('{start_date}'::date)-interval '10 days'\n", "----left join metrics.multirep_data c on b.bulk_sto_id = c.bulk_sto_id and c.sto_created_at> ('{start_date}'::date)-interval '10 days'\n", "where (invoice_date_ist between '{start_date}' and '{end_date}')\n", "and receiver_outlet_id in {merchants_outlet_list}\n", "and item_type='packaged' and ds_vehicle_arrival_at_ist is not null\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    merchants_outlet_list=tuple(merchants_outlet_list),\n", ")\n", "\n", "dn_data = pd.read_sql(dn_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# unloading_ts, consignment_received_at,grn_timestamp,disc_created_at,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"unloading_ts\"] = dn_data[\"unloading_ts\"].fillna(\"0\")\n", "dn_data[\"grn_timestamp\"] = dn_data[\"grn_timestamp\"].fillna(\"0\")\n", "dn_data[\"disc_created_at\"] = dn_data[\"disc_created_at\"].fillna(\"0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data.to_csv('dn_data_check.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"inward_time\"] = dn_data.apply(\n", "    lambda x: x[\"consignment_received_at\"]\n", "    if x[\"unloading_ts\"] == \"0\"\n", "    else x[\"unloading_ts\"],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[\"inward_time\"] = np.where(\n", "#     dn_data[\"unloading_ts\"] == \"0\",\n", "#         dn_data[\"consignment_received_at\"],\n", "#     dn_data[\"unloading_ts\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[\"inward_time\"] = np.where(\n", "#     dn_data[\"unloading_ts\"] == \"0\",\n", "#     np.where(\n", "#         dn_data[\"consignment_received_at\"] == \"0\",\n", "#         dn_data[\"grn_timestamp\"],\n", "#         dn_data[\"consignment_received_at\"],\n", "#     ),\n", "#     dn_data[\"unloading_ts\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[dn_data[\"inward_time\"] == \"0\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['inward_time']=pd.to_datetime(dn_data['inward_time'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[dn_data['inward_time']==1661594486000000000]['consignment_received_at'].iloc[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(1661594486000000000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['inward_time'] = dn_data['inward_time'].apply(lambda x: pd.to_datetime(x) if type(x)=='int' else x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[dn_data[\"inward_time\"] == 1661594486000000000].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def timestamp_fix(x):\n", "#     if x['inward_time']!='0' and x['disc_created_at']!='0':\n", "#         if x['inward_time']>x['disc_created_at']:\n", "#             return x['disc_created_at']\n", "#         else:\n", "#             return x['inward_time']\n", "#     elif x['inward_time']=='0':\n", "#         return x['disc_created_at']\n", "#     else:\n", "#         return dn_data['inward_time']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"grn_timestamp\"] = dn_data[\"grn_timestamp\"].apply(\n", "    lambda x: x\n", "    # datetime.strptime(str(x), \"%Y-%m-%d %H:%M:%S\")\n", "    if x != \"0\"\n", "    else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", ")\n", "dn_data[\"disc_created_at\"] = dn_data[\"disc_created_at\"].apply(\n", "    lambda x: x\n", "    # datetime.strptime(str(x), \"%Y-%m-%d %H:%M:%S\")\n", "    if x != \"0\"\n", "    else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", ")\n", "\n", "dn_data[\"inward_time\"] = dn_data[\"inward_time\"].apply(\n", "    lambda x: x\n", "    # datetime.strptime(str(x), \"%Y-%m-%d %H:%M:%S\")\n", "    if x != \"0\"\n", "    else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[dn_data['grn_timestamp']==datetime(4000, 1, 1, 0, 0)].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def mult_if(x):\n", "\n", "#     if x['disc_created_at']!=datetime(4000, 1, 1, 0, 0):\n", "#         if x['inward_time']>x['disc_created_at']:\n", "#             return x['disc_created_at']\n", "#         else:\n", "#             return x['inward_time']\n", "#     else:\n", "#         return x['inward_time']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['inward_time'] = dn_data.apply(lambda x: mult_if(x),axis=1)\n", "# np.where((dn_data['disc_created_at']!=datetime(4000, 1, 1, 0, 0)),np.where(dn_data['inward_time']>dn_data['disc_created_at'],dn_data['disc_created_at'],dn_data['inward_time']),dn_data['inward_time'])\n", "# dn_data.apply(lambda x: timestamp_fix(x),axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[dn_data[\"inward_time\"] == datetime(4000, 1, 1, 0, 0)].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['disc_created_at'].iloc[0]\n", "# head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp = (\n", "    dn_data.groupby([\"date_\", \"invoice_id\", \"rec_outlet_id\"])[\"inward_time\"]\n", "    .min()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp = dn_data_temp.rename(columns={\"inward_time\": \"inward_time_min\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data = dn_data.merge(\n", "    dn_data_temp, on=[\"date_\", \"invoice_id\", \"rec_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[(dn_data['rec_outlet_id']==3812) & (dn_data['date_']==date(2022,10,7)) & (dn_data['invoice_id']=='BLC2653T22144036')]['inward_time_min'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[\"inward_time_min\"] = (\n", "#     dn_data[dn_data[\"inward_time\"] != \"0\"]\n", "#     .groupby([\"date_\", \"indent\", \"rec_outlet_id\"])[\"inward_time\"]\n", "#     .transform(\"min\")\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"inward_time_min\"] = dn_data[\"inward_time_min\"].fillna(\"0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['inward_time_min'].to_csv('data_check.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"inward_time_min\"] = dn_data[\"inward_time_min\"].apply(\n", "    lambda x: x\n", "    # datetime.strptime(x, \"%Y-%m-%d %H:%M:%S\")\n", "    if x != \"0\"\n", "    else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", ")\n", "# dn_data[\"grn_timestamp\"] = dn_data[\"grn_timestamp\"].apply(\n", "#     lambda x: datetime.strptime(x, \"%Y-%m-%d %H:%M:%S\")\n", "#     if x != \"0\"\n", "#     else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", "# )\n", "# dn_data[\"disc_created_at\"] = dn_data[\"disc_created_at\"].apply(\n", "#     lambda x: datetime.strptime(x, \"%Y-%m-%d %H:%M:%S\")\n", "#     if x != \"0\"\n", "#     else datetime.strptime(\"4000-01-01 00:00:00\", \"%Y-%m-%d %H:%M:%S\")\n", "# )\n", "# np.where(dn_data['inward_time_min']!='0',pd.to_datetime(dn_data['inward_time_min']),datetime(4000,1,1,0,0,0))\n", "# dn_data['grn_timestamp'] = np.where(dn_data['grn_timestamp']!='0',pd.to_datetime(dn_data['grn_timestamp']),datetime(4000,1,1,0,0,0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data['disc_created_at'] = dn_data['disc_created_at'].apply(lambda x: datetime.strptime(x,\"%Y-%m-%d %H:%M:%S\") if x!='0' else datetime.strptime('4000-01-01 00:00:00',\"%Y-%m-%d %H:%M:%S\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"inward_time_min\"].iloc[0] + <PERSON><PERSON><PERSON>(hours=12)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def mult_if(x):\n", "\n", "#     if x['disc_created_at']!=datetime(4000, 1, 1, 0, 0):\n", "#         if x['inward_time']>x['disc_created_at']:\n", "#             return x['disc_created_at']\n", "#         else:\n", "#             return x['inward_time']\n", "#     else:\n", "#         return x['inward_time']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"within_tat\"] = dn_data.apply(\n", "    lambda x: 1\n", "    if (\n", "        (x[\"inward_time_min\"] <= x[\"grn_timestamp\"])\n", "        & (x[\"inward_time_min\"] + <PERSON><PERSON><PERSON>(hours=12) >= x[\"grn_timestamp\"])\n", "    )\n", "    else 0,\n", "    axis=1,\n", ")\n", "dn_data[\"disc_within_tat\"] = dn_data.apply(\n", "    lambda x: 1\n", "    if (\n", "        (\n", "            (x[\"inward_time_min\"] <= x[\"disc_created_at\"])\n", "            & (x[\"inward_time_min\"] + <PERSON><PERSON><PERSON>(hours=12) >= x[\"disc_created_at\"])\n", "        )\n", "        | (x[\"inward_time_min\"] > x[\"disc_created_at\"])\n", "    )\n", "    else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"date_\"].iloc[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[\n", "#     (dn_data[\"rec_outlet_id\"] == 2078)\n", "#     & (dn_data[\"indent\"] == \"Indent 1\")\n", "#     & (dn_data[\"date_\"] == date(2022, 8, 26))\n", "# ][\"grn_timestamp\"].min()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[(dn_data['date_']==date(2022,8,26)) & (dn_data['rec_outlet_id']==2363)].to_csv('check_data.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[dn_data[\"disc_within_tat\"] == 1].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"grn_within_tat\"] = np.where(\n", "    dn_data[\"within_tat\"] == 1, dn_data[\"inwarded_amt\"], 0\n", ") + np.where(dn_data[\"disc_within_tat\"] == 1, dn_data[\"ttl_dn_amt\"], 0)\n", "dn_data[\"dn_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"ttl_dn_amt\"], 0\n", ")\n", "\n", "dn_data[\"grn_overall\"] = dn_data[\"ttl_dn_amt\"] + dn_data[\"inwarded_amt\"]\n", "\n", "dn_data[\"amt_to_ds\"] = np.where(\n", "    dn_data[\"sto_dispatch_amt\"] == 0, dn_data[\"sto_amt\"], dn_data[\"sto_dispatch_amt\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group = (\n", "    dn_data.groupby([\"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data[dn_data['rec_outlet_id']==3812].to_csv('3812_dn_data.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group[\"grn_per_within_tat\"] = (\n", "    dn_data_group[\"grn_within_tat\"] * 100.0 / dn_data_group[\"amt_to_ds\"]\n", ")\n", "dn_data_group[\"grn_per_overall\"] = (\n", "    dn_data_group[\"grn_overall\"] * 100.0 / dn_data_group[\"amt_to_ds\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group = dn_data_group.rename(columns={\"rec_outlet_id\": \"outlet_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_data_group.to_csv('dn_aug_sept.csv',index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparing Final MTD Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = gmv_orders_data_new.merge(\n", "    fill_rate_ds_impact_data_new, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data_temp = final_data.merge(\n", "    checkout_to_billed_data_new, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data1 = final_data_temp.merge(\n", "    inventory_variance_data_new_new, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data2 = final_data1.merge(qng_expiry_data_new, on=\"outlet_id\", how=\"left\")\n", "final_data3 = final_data2.merge(grn_to_esto_data_new_new, on=\"outlet_id\", how=\"left\")\n", "final_data4 = final_data3.merge(damage_data_new, on=\"outlet_id\", how=\"left\")\n", "\n", "# final_data5 = final_data4.merge(pna_billing_data_new, on=\"outlet_id\", how=\"left\")\n", "\n", "final_data5 = final_data4.merge(merchants_data, on=\"outlet_id\", how=\"left\")\n", "final_data6 = final_data5.merge(open_esto_data_new, on=\"outlet_id\", how=\"left\")\n", "final_data7 = final_data6.merge(dn_data_group, on=\"outlet_id\", how=\"left\")\n", "final_data8 = final_data7.merge(\n", "    reinventorization_returns_data_new, on=\"outlet_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8 = final_data8.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data8[\"total_price_change_new\"] = np.where(\n", "#     final_data8[\"total_price_change\"] >= 0, 0, final_data8[\"total_price_change\"] * -1\n", "# )\n", "# final_data8[\"Inventory_variance_item_count\"] = np.where(\n", "#     final_data8[\"item id\"] >= 0, 0, final_data8[\"item id\"] * -1\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8[\"Fill Rate PNA Leverage\"] = final_data8[\"gmv\"] * (0.25 / 100)\n", "final_data8[\"Inventory Variance Leverage\"] = final_data8[\"gmv\"] * (0.1 / 100)\n", "final_data8[\"Quality Leverage\"] = final_data8[\"gmv\"] * (0.5 / 100)\n", "final_data8[\"Damage Leverage\"] = final_data8[\"gmv\"] * (0.15 / 100)\n", "\n", "\n", "final_data8[\"%Fill Rate DS Impact\"] = (\n", "    final_data8[\"total_pna_loss\"] * 100\n", ") / final_data8[\"gmv\"]\n", "final_data8[\"%Inventory Variance\"] = (\n", "    final_data8[\"stock_variance_loss\"] * 100\n", ") / final_data8[\"gmv\"]\n", "final_data8[\"%Item Missing\"] = (\n", "    final_data8[\"complaint_category_item_missing\"] * 100\n", ") / final_data8[\"total_order_count\"]\n", "final_data8[\"%Item Expired\"] = (\n", "    final_data8[\"complaint_category_expired\"] * 100\n", ") / final_data8[\"total_order_count\"]\n", "final_data8[\"%Damage\"] = (final_data8[\"total_landing_price\"] * 100) / final_data8[\"gmv\"]\n", "# final_data8[\"%Billing PNA\"] = (final_data8[\"billing_pnas_gmv\"] * 100) / final_data8[\n", "# \"gmv\"\n", "# ]\n", "final_data8[\"%Checkout to <PERSON> Within 3 mins\"] = (\n", "    final_data8[\"not_delayed_<=3(mins)\"] * 100\n", ") / final_data8[\"total_orders_<=8_ipo\"]\n", "\n", "\n", "final_data8[\"%Checkout to <PERSON> Within 5 mins\"] = (\n", "    final_data8[\"not_delayed_<=5(mins)\"] * 100\n", ") / final_data8[\"total_orders_>8_ipo\"]\n", "\n", "final_data8[\"%Checkout to Assign Within 30 secs\"] = (\n", "    final_data8[\"checkout_to_picker_assigned<=30(secs)\"] * 100\n", ") / final_data8[\"order_id\"]\n", "\n", "final_data8[\"Fill Rate PNA Penalty\"] = final_data8.apply(\n", "    lambda x: x[\"total_pna_loss\"] - x[\"Fill Rate PNA Leverage\"]\n", "    if (x[\"total_pna_loss\"] > x[\"Fill Rate PNA Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8[\"Inventory Variance Penalty\"] = final_data8.apply(\n", "    lambda x: x[\"stock_variance_loss\"] - x[\"Inventory Variance Leverage\"]\n", "    if (x[\"stock_variance_loss\"] > x[\"Inventory Variance Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8[\"Quality Penalty\"] = final_data8.apply(\n", "    lambda x: x[\"quality_gmv_loss\"] - x[\"Quality Leverage\"]\n", "    if (x[\"quality_gmv_loss\"] > x[\"Quality Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8[\"Damage Penalty\"] = final_data8.apply(\n", "    lambda x: x[\"total_landing_price\"] - x[\"Damage Leverage\"]\n", "    if (x[\"total_landing_price\"] > x[\"Damage Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8[\"%Reinventorization Returns\"] = (\n", "    final_data8[\"is_delayed\"] * 100 / final_data8[\"putlist_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8[final_data8[\"outlet_id\"] == 2942]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8 = final_data8.rename(\n", "    columns={\n", "        \"outlet_name\": \"Merchant Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"total_order_count\": \"Total Orders\",\n", "        \"gmv\": \"GMV\",\n", "        \"total_pna_loss\": \"Fill Rate DS impact Loss\",\n", "        \"stock_variance_loss\": \"Inventory Variance Loss\",\n", "        \"quality_gmv_loss\": \"Quality Loss\",\n", "        \"delayed_indent\": \"Delayed Indents\",\n", "        \"damage_item_count\": \"Damage Item Count\",\n", "        \"fill_rate_impact_item_qty\": \"Picker PNA Item Qty\",\n", "        \"total_landing_price\": \"Damage Loss\",\n", "        \"billing_pnas_gmv\": \"Billing PNAs Loss\",\n", "        \"is_delayed\": \"Reinventorization Delayed Count\",\n", "        \"sto_count\": \"Discrepancy Raised TAT Breach STO Count\",\n", "        \"checkout_to_picker_assigned\": \"Avg Checkout to Assign Time\",\n", "        \"checkout_to_bill_time\": \"Avg Instore Time\",\n", "        \"open_esto_loss\": \"Open ESTO Loss\",\n", "        \"open_esto_qty\": \"Open ESTO Qty\",\n", "        \"grn_per_within_tat\": \"GRN % Within TAT\",\n", "        \"grn_per_overall\": \"GRN % Overall\",\n", "        \"dn_within_tat\": \"DN Within TAT\",\n", "        \"ttl_dn_amt\": \"DN Overall\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8 = final_data8[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Total Orders\",\n", "        \"GMV\",\n", "        \"Fill Rate DS impact Loss\",\n", "        \"Picker PNA <PERSON>em <PERSON>\",\n", "        \"Quality Loss\",\n", "        \"Delayed Indents\",\n", "        \"Damage Item Count\",\n", "        \"Damage Loss\",\n", "        \"Merchant Name\",\n", "        \"Reinventorization Delayed Count\",\n", "        \"Inventory Variance Loss\",\n", "        \"Fill Rate PNA Leverage\",\n", "        \"Inventory Variance Leverage\",\n", "        \"Quality Leverage\",\n", "        \"Damage Leverage\",\n", "        \"%Fill Rate DS Impact\",\n", "        \"%Inventory Variance\",\n", "        \"%Item Missing\",\n", "        \"%Item Expired\",\n", "        \"%Damage\",\n", "        \"%Checkout to <PERSON> Within 3 mins\",\n", "        \"%Checkout to <PERSON> Within 5 mins\",\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"Fill Rate PNA Penalty\",\n", "        \"Inventory Variance Penalty\",\n", "        \"Quality Penalty\",\n", "        \"Damage Penalty\",\n", "        \"%Reinventorization Returns\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "        \"GRN % Within TAT\",\n", "        \"GRN % Overall\",\n", "        \"DN Within TAT\",\n", "        \"DN Overall\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8 = final_data8.round(\n", "    {\n", "        \"Outlet ID\": 0,\n", "        \"Total Orders\": 0,\n", "        \"GMV\": 2,\n", "        \"Fill Rate DS impact Loss\": 2,\n", "        \"Picker PNA Item Qty\": 0,\n", "        \"Quality Loss\": 2,\n", "        \"Delayed Indents\": 0,\n", "        \"Damage Item Count\": 0,\n", "        \"Damage Loss\": 2,\n", "        \"Reinventorization Delayed Count\": 0,\n", "        \"Inventory Variance Loss\": 2,\n", "        \"Fill Rate PNA Leverage\": 2,\n", "        \"Inventory Variance Leverage\": 2,\n", "        \"Quality Leverage\": 2,\n", "        \"Damage Leverage\": 2,\n", "        \"%Fill Rate DS Impact\": 2,\n", "        \"%Inventory Variance\": 2,\n", "        \"%Item Missing\": 2,\n", "        \"%Item Expired\": 2,\n", "        \"%Damage\": 2,\n", "        \"%Checkout to <PERSON> Within 3 mins\": 2,\n", "        \"%Checkout to <PERSON> Within 5 mins\": 2,\n", "        \"Avg Checkout to Assign Time\": 2,\n", "        \"Avg Instore Time\": 2,\n", "        \"Fill Rate PNA Penalty\": 2,\n", "        \"Inventory Variance Penalty\": 2,\n", "        \"Quality Penalty\": 2,\n", "        \"Damage Penalty\": 2,\n", "        \"%Reinventorization Returns\": 2,\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\": 2,\n", "        \"Open ESTO Loss\": 2,\n", "        \"TAT Loss\": 2,\n", "        \"TAT Penalty\": 2,\n", "        \"GRN % Within TAT\": 2,\n", "        \"GRN % Overall\": 2,\n", "        \"DN Within TAT\": 2,\n", "        \"DN Overall\": 2,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## T-1 View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1 = gmv_orders_data_temp[\n", "    gmv_orders_data_temp[\"delivery_date\"] == end_date\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1_group = gmv_orders_data_t_minus_1.groupby(\n", "    \"outlet_id\", as_index=False\n", ").agg({\"delivered_order_count\": \"sum\", \"procured_gmv\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1_group.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1_group.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_t_minus_1_group.rename(\n", "    columns={\"procured_gmv\": \"gmv\", \"delivered_order_count\": \"total_order_count\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1 = fill_rate_ds_impact_data[\n", "    fill_rate_ds_impact_data[\"scheduleddate\"] == end_date\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1_group = fill_rate_ds_impact_data_t_minus_1.groupby(\n", "    \"outlet_id\", as_index=False\n", ").agg({\"total_pna_loss\": \"sum\", \"order_id\": \"nunique\", \"pna_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1_group.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1_group.rename(\n", "    columns={\n", "        \"order_id\": \"fill_rate_impact_order_count\",\n", "        \"pna_qty\": \"fill_rate_impact_item_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_t_minus_1_group.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.date_ts.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_t_minus_1 = checkout_to_billed_data[\n", "    checkout_to_billed_data[\"date_ts\"] == end_date\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_t_minus_1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_t_minus_1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_t_minus_1[\n", "    \"checkout_to_picker_assigned<=30(secs)\"\n", "] = checkout_to_billed_data_t_minus_1.apply(\n", "    lambda x: 1 if (x[\"checkout_to_picker_assigned\"] <= 0.5) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_t_minus_1[\n", "    \"not_delayed_<=3(mins)\"\n", "] = checkout_to_billed_data_t_minus_1.apply(\n", "    lambda x: 1 if (x[\"ipo\"] <= 8 and x[\"checkout_to_bill_time\"] <= 3) else 0,\n", "    axis=1,\n", ")\n", "\n", "checkout_to_billed_data_t_minus_1[\n", "    \"total_orders_<=8_ipo\"\n", "] = checkout_to_billed_data_t_minus_1.apply(\n", "    lambda x: 1 if (x[\"ipo\"] <= 8) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# checkout_to_billed_data1[\"not_delayed_ipo_>_8\"] = checkout_to_billed_data1.apply(\n", "#     lambda x: 1\n", "#     if (x[\"ipo\"] > 8 and x[\"checkout_to_bill_time\"] <=5)\n", "#     else 0,\n", "#     axis=1,\n", "# )\n", "checkout_to_billed_data_t_minus_1[\n", "    \"not_delayed_<=5(mins)\"\n", "] = checkout_to_billed_data_t_minus_1.apply(\n", "    lambda x: 1 if (x[\"ipo\"] > 8 and x[\"checkout_to_bill_time\"] <= 5) else 0,\n", "    axis=1,\n", ")\n", "\n", "checkout_to_billed_data_t_minus_1[\n", "    \"total_orders_>8_ipo\"\n", "] = checkout_to_billed_data_t_minus_1.apply(\n", "    lambda x: 1 if (x[\"ipo\"] > 8) else 0,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new_t_minus_1_group = checkout_to_billed_data_t_minus_1.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"not_delayed_<=3(mins)\": \"sum\",\n", "        \"total_orders_<=8_ipo\": \"sum\",\n", "        \"not_delayed_<=5(mins)\": \"sum\",\n", "        \"total_orders_>8_ipo\": \"sum\",\n", "        \"checkout_to_picker_assigned<=30(secs)\": \"sum\",\n", "        \"order_id\": \"nunique\",\n", "        \"checkout_to_picker_assigned\": \"mean\",\n", "        \"checkout_to_bill_time\": \"mean\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new_t_minus_1_group[\"%TAT <=8 ipo\"] = (\n", "    checkout_to_billed_data_new_t_minus_1_group[\"not_delayed_<=3(mins)\"]\n", "    * 100.0\n", "    / checkout_to_billed_data_new_t_minus_1_group[\"total_orders_<=8_ipo\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new_t_minus_1_group[\"TAT Loss\"] = np.where(\n", "    checkout_to_billed_data_new_t_minus_1_group[\"%TAT <=8 ipo\"] < 100,\n", "    (100 - checkout_to_billed_data_new_t_minus_1_group[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_new_t_minus_1_group[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")\n", "\n", "checkout_to_billed_data_new_t_minus_1_group[\"TAT Penalty\"] = np.where(\n", "    checkout_to_billed_data_new_t_minus_1_group[\"%TAT <=8 ipo\"] < 70,\n", "    (70 - checkout_to_billed_data_new_t_minus_1_group[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_new_t_minus_1_group[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1 = qng_expiry_data[qng_expiry_data[\"date\"] == end_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_temp1 = qng_expiry_data_t_minus_1.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"selling_price\": \"sum\",\n", "        \"product_id\": \"nunique\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_temp1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_temp = qng_expiry_data_t_minus_1[\n", "    [\n", "        \"order__id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date\",\n", "        \"complaint_category_item_missing\",\n", "        \"complaint_category_expired\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_temp = qng_expiry_data_t_minus_1_temp.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_new = qng_expiry_data_t_minus_1_temp.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        # \"selling_price\": \"sum\",\n", "        # \"product_id\": \"nunique\",\n", "        \"complaint_category_item_missing\": \"sum\",\n", "        \"complaint_category_expired\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_group = qng_expiry_data_t_minus_1_new.merge(\n", "    qng_expiry_data_t_minus_1_temp1, on=\"outlet_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_group.rename(\n", "    columns={\"product_id\": \"qng_item_count\", \"selling_price\": \"quality_gmv_loss\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_t_minus_1_group.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data[\"date_ts\"] = damage_data[\"pos_timestamp\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_t_minus_1 = damage_data[damage_data[\"date_ts\"] == end_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_t_minus_1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_t_minus_1_group = damage_data_t_minus_1.groupby(\n", "    \"outlet_id\", as_index=False\n", ").agg({\"total_landing_price\": \"sum\", \"quantity\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_t_minus_1_group = damage_data_t_minus_1_group.rename(\n", "    columns={\"quantity\": \"damage_item_count\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_t_minus_1_group.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1 = inventory_variance_data[\n", "    inventory_variance_data[\"pos_timestamp\"] <= end_date - timedelta(days=1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative = inventory_variance_data_t_minus_1[\n", "    inventory_variance_data_t_minus_1[\"operation\"] == \"-\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative[\"total_price_change\"] = (\n", "    inventory_variance_data_t_minus_1_negative[\"landing_price\"]\n", "    * inventory_variance_data_t_minus_1_negative[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_new = (\n", "    inventory_variance_data_t_minus_1_negative.groupby(\n", "        [\"outlet_id\", \"item id\"], as_index=False\n", "    ).agg(\n", "        {\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_t_minus_1 = fill_rate_ds_impact_data[\n", "    fill_rate_ds_impact_data[\"scheduleddate\"] <= end_date - timedelta(days=1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fill_rate_ds_impact_data_temp_t_minus_1 = fill_rate_ds_impact_data_t_minus_1.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_t_minus_1[\"pna_qty\"] = (\n", "    fill_rate_ds_impact_data_temp_t_minus_1[\"ordered_quantity\"]\n", "    - fill_rate_ds_impact_data_temp_t_minus_1[\"procured_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_t_minus_1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group_t_minus_1 = (\n", "    fill_rate_ds_impact_data_temp_t_minus_1.groupby(\n", "        [\"outlet_id\", \"item_id\"], as_index=False\n", "    ).agg({\"order_id\": \"nunique\", \"pna_qty\": \"sum\", \"total_pna_loss\": \"sum\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group_t_minus_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final = (\n", "    inventory_variance_data_t_minus_1_negative_new.merge(\n", "        fill_rate_ds_impact_data_temp_group_t_minus_1,\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "        left_on=[\"outlet_id\", \"item id\"],\n", "        how=\"left\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\n", "    \"pna_qty\"\n", "] = inventory_variance_data_t_minus_1_negative_final[\"pna_qty\"].fillna(0)\n", "inventory_variance_data_t_minus_1_negative_final[\n", "    \"total_pna_loss\"\n", "] = inventory_variance_data_t_minus_1_negative_final[\"total_pna_loss\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\"qty_to_consider\"] = (\n", "    inventory_variance_data_t_minus_1_negative_final[\"change quantity\"]\n", "    - inventory_variance_data_t_minus_1_negative_final[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\"qty_to_consider\"] = np.where(\n", "    inventory_variance_data_t_minus_1_negative_final[\"qty_to_consider\"] < 0,\n", "    0,\n", "    inventory_variance_data_t_minus_1_negative_final[\"qty_to_consider\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\"negative_change\"] = (\n", "    inventory_variance_data_t_minus_1_negative_final[\"total_price_change\"]\n", "    - inventory_variance_data_t_minus_1_negative_final[\"total_pna_loss\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\"negative_change\"] = np.where(\n", "    inventory_variance_data_t_minus_1_negative_final[\"negative_change\"] < 0,\n", "    0,\n", "    inventory_variance_data_t_minus_1_negative_final[\"negative_change\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final[\n", "    inventory_variance_data_t_minus_1_negative_final[\"pna_qty\"] > 0\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_t_minus_1_negative_final[\n", "#     inventory_variance_data_t_minus_1_negative_final[\"outlet_id\"] == 1457\n", "# ].to_csv(\"1457_fillrate.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_group = (\n", "    inventory_variance_data_t_minus_1_negative_final.groupby([\"outlet_id\"])[\n", "        \"negative_change\"\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_negative_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive = inventory_variance_data_t_minus_1[\n", "    inventory_variance_data_t_minus_1[\"operation\"] == \"+\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive[\"total_price_change\"] = (\n", "    inventory_variance_data_t_minus_1_positive[\"landing_price\"]\n", "    * inventory_variance_data_t_minus_1_positive[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_variance_raw_t_minus_1 = pd.concat(\n", "    [\n", "        inventory_variance_data_t_minus_1_negative_final,\n", "        inventory_variance_data_t_minus_1_positive,\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive_group = (\n", "    inventory_variance_data_t_minus_1_positive.groupby(\n", "        [\"outlet_id\"], as_index=False\n", "    ).agg(\n", "        {\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_positive_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final = (\n", "    inventory_variance_data_t_minus_1_negative_group.merge(\n", "        inventory_variance_data_t_minus_1_positive_group, on=[\"outlet_id\"], how=\"left\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final[\"stock_variance_loss\"] = (\n", "    inventory_variance_data_t_minus_1_final[\"total_price_change\"]\n", "    - inventory_variance_data_t_minus_1_final[\"negative_change\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final[\"stock_variance_loss\"] = np.where(\n", "    inventory_variance_data_t_minus_1_final[\"stock_variance_loss\"] > 0,\n", "    0,\n", "    inventory_variance_data_t_minus_1_final[\"stock_variance_loss\"] * -1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final = inventory_variance_data_t_minus_1_final[\n", "    [\"outlet_id\", \"stock_variance_loss\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_final[\n", "    inventory_variance_data_t_minus_1_final[\"outlet_id\"] == 1393\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new = inventory_variance_data_final.merge(\n", "    inventory_variance_data_t_minus_1_final, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss\"] = (\n", "    inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss_x\"]\n", "    - inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss_y\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss\"] = np.where(\n", "    inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss\"] < 0,\n", "    0,\n", "    inventory_variance_data_t_minus_1_group_new[\"stock_variance_loss\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_t_minus_1_group_new.to_csv('t_minus_1_group.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_t_minus_1_group_new = (\n", "#     inventory_variance_data_t_minus_1_final.copy()\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_final[inventory_variance_data_final['outlet_id']==1457]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new[\n", "    inventory_variance_data_t_minus_1_group_new[\"outlet_id\"] == 1393\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data[(inventory_variance_data['outlet_id']==1457) & (inventory_variance_data['pos_timestamp']==date(2022,9,6)) & (inventory_variance_data['operation']=='-')]['total_landing_price'].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data[(inventory_variance_data['outlet_id']==1457) & (inventory_variance_data['pos_timestamp']==date(2022,9,6)) & (inventory_variance_data['operation']=='+')]['total_landing_price'].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data[(inventory_variance_data['outlet_id']==1457)].to_csv('1457_stock_Variance.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_t_minus_1_group_new = (\n", "    inventory_variance_data_t_minus_1_group_new[[\"outlet_id\", \"stock_variance_loss\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1 = grn_to_sto_disc_data[\n", "    grn_to_sto_disc_data[\"date_ts\"] == end_date\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group = grn_to_esto_data_t_minus_1.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"avg_bad_stock_update_time\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group[\"delay_flag\"] = grn_to_esto_data_t_minus_1_group[\n", "    \"avg_bad_stock_update_time\"\n", "].apply(lambda x: 1 if x > 14400 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_to_esto_data_t_minus_1_group = grn_to_esto_data_new.groupby(\n", "#     [\"outlet_id\"], as_index=False\n", "# ).agg({\"delay_flag\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group.rename(\n", "    columns={\"receiver_outlet_id\": \"outlet_id\", \"delay_flag\": \"delayed_indent\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_esto_data_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_t_minus_1 = reinventorization_returns_data[\n", "    reinventorization_returns_data[\"date_ts\"] == end_date\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_t_minus_1[\n", "    \"is_delayed\"\n", "] = reinventorization_returns_data_t_minus_1[\"putaway_time\"].apply(\n", "    lambda x: 1 if x > 7200 else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_t_minus_1_group = (\n", "    reinventorization_returns_data_t_minus_1.groupby([\"outlet_id\"], as_index=False).agg(\n", "        {\"is_delayed\": \"sum\", \"putlist_id\": \"nunique\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_t_minus_1 = open_esto_data[open_esto_data[\"date_ts\"] == end_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_t_minus_1_group = open_esto_data_t_minus_1.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"open_esto_loss\": \"sum\", \"open_esto_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_t_minus_1_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_temp = (\n", "    dn_data[dn_data[\"date_\"].isin([end_date])]\n", "    .groupby([\"date_\", \"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "dn_data_t_minus_1_temp1 = (\n", "    dn_data[dn_data[\"date_\"].isin([end_date - timedelta(days=1)])]\n", "    .groupby([\"date_\", \"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_group1 = (\n", "    dn_data_t_minus_1_temp.groupby([\"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "dn_data_t_minus_1_group2 = (\n", "    dn_data_t_minus_1_temp1.groupby([\"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_group1 = dn_data_t_minus_1_group1.rename(\n", "    columns={\n", "        \"grn_within_tat\": \"grn_within_tat_t_minus_1\",\n", "        \"amt_to_ds\": \"amt_to_ds_t_minus_1\",\n", "        \"dn_within_tat\": \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount\": \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"grn_overall\": \"grn_overall_t_minus_1\",\n", "        \"ttl_dn_amt\": \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount\": \"dn_other_amount_t_minus_1\",\n", "    }\n", ")\n", "\n", "\n", "dn_data_t_minus_1_group2 = dn_data_t_minus_1_group2.rename(\n", "    columns={\n", "        \"grn_within_tat\": \"grn_within_tat_t_minus_2\",\n", "        \"amt_to_ds\": \"amt_to_ds_t_minus_2\",\n", "        \"dn_within_tat\": \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount\": \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"grn_overall\": \"grn_overall_t_minus_2\",\n", "        \"ttl_dn_amt\": \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount\": \"dn_other_amount_t_minus_2\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_group = dn_data_t_minus_1_group1.merge(\n", "    dn_data_t_minus_1_group2, on=[\"rec_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_group[\"grn_per_within_tat_t_minus_1\"] = (\n", "    dn_data_t_minus_1_group[\"grn_within_tat_t_minus_1\"]\n", "    * 100.0\n", "    / dn_data_t_minus_1_group[\"amt_to_ds_t_minus_1\"]\n", ")\n", "dn_data_t_minus_1_group[\"grn_per_overall_t_minus_1\"] = (\n", "    dn_data_t_minus_1_group[\"grn_overall_t_minus_1\"]\n", "    * 100.0\n", "    / dn_data_t_minus_1_group[\"amt_to_ds_t_minus_1\"]\n", ")\n", "\n", "\n", "dn_data_t_minus_1_group[\"grn_per_within_tat_t_minus_2\"] = (\n", "    dn_data_t_minus_1_group[\"grn_within_tat_t_minus_2\"]\n", "    * 100.0\n", "    / dn_data_t_minus_1_group[\"amt_to_ds_t_minus_2\"]\n", ")\n", "dn_data_t_minus_1_group[\"grn_per_overall_t_minus_2\"] = (\n", "    dn_data_t_minus_1_group[\"grn_overall_t_minus_2\"]\n", "    * 100.0\n", "    / dn_data_t_minus_1_group[\"amt_to_ds_t_minus_2\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_t_minus_1_group = dn_data_t_minus_1_group.rename(\n", "    columns={\"rec_outlet_id\": \"outlet_id\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_t_minus_1 = gmv_orders_data_t_minus_1_group.merge(\n", "    fill_rate_ds_impact_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data_temp_t_minus_1 = final_data_t_minus_1.merge(\n", "    checkout_to_billed_data_new_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data1_t_minus_1 = final_data_temp_t_minus_1.merge(\n", "    inventory_variance_data_t_minus_1_group_new, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data2_t_minus_1 = final_data1_t_minus_1.merge(\n", "    qng_expiry_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data3_t_minus_1 = final_data2_t_minus_1.merge(\n", "    grn_to_esto_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "final_data4_t_minus_1 = final_data3_t_minus_1.merge(\n", "    damage_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "\n", "\n", "final_data5_t_minus_1 = final_data4_t_minus_1.merge(\n", "    merchants_data, on=\"outlet_id\", how=\"left\"\n", ")\n", "\n", "final_data6_t_minus_1 = final_data5_t_minus_1.merge(\n", "    open_esto_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")\n", "\n", "# final_data7_t_minus_1 = final_data6_t_minus_1.merge(\n", "#     dn_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", "# )\n", "\n", "final_data8_t_minus_1 = final_data6_t_minus_1.merge(\n", "    reinventorization_returns_data_t_minus_1_group, on=\"outlet_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1 = final_data8_t_minus_1.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data8_t_minus_1[\n", "#     final_data8_t_minus_1[\"outlet_name\"] == \"Super Store Kolkata Kamalgazi ES12 PR (LT)\"\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data8_t_minus_1[\"total_price_change_new\"] = np.where(\n", "#     final_data8_t_minus_1[\"total_price_change\"] >= 0,\n", "#     0,\n", "#     final_data8_t_minus_1[\"total_price_change\"] * -1,\n", "# )\n", "# final_data8_t_minus_1[\"Inventory_variance_item_count\"] = np.where(\n", "#     final_data8_t_minus_1[\"item id\"] >= 0, 0, final_data8_t_minus_1[\"item id\"] * -1\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1[\"Fill Rate PNA Leverage\"] = final_data8_t_minus_1[\"gmv\"] * (\n", "    0.25 / 100\n", ")\n", "final_data8_t_minus_1[\"Inventory Variance Leverage\"] = final_data8_t_minus_1[\"gmv\"] * (\n", "    0.1 / 100\n", ")\n", "final_data8_t_minus_1[\"Quality Leverage\"] = final_data8_t_minus_1[\"gmv\"] * (0.5 / 100)\n", "final_data8_t_minus_1[\"Damage Leverage\"] = final_data8_t_minus_1[\"gmv\"] * (0.15 / 100)\n", "\n", "\n", "final_data8_t_minus_1[\"%Fill Rate DS Impact\"] = (\n", "    final_data8_t_minus_1[\"total_pna_loss\"] * 100\n", ") / final_data8_t_minus_1[\"gmv\"]\n", "final_data8_t_minus_1[\"%Inventory Variance\"] = (\n", "    final_data8_t_minus_1[\"stock_variance_loss\"] * 100\n", ") / final_data8_t_minus_1[\"gmv\"]\n", "final_data8_t_minus_1[\"%Item Missing\"] = (\n", "    final_data8_t_minus_1[\"complaint_category_item_missing\"] * 100\n", ") / final_data8_t_minus_1[\"total_order_count\"]\n", "final_data8_t_minus_1[\"%Item Expired\"] = (\n", "    final_data8_t_minus_1[\"complaint_category_expired\"] * 100\n", ") / final_data8_t_minus_1[\"total_order_count\"]\n", "final_data8_t_minus_1[\"%Damage\"] = (\n", "    final_data8_t_minus_1[\"total_landing_price\"] * 100\n", ") / final_data8_t_minus_1[\"gmv\"]\n", "# final_data8_t_minus_1[\"%Billing PNA\"] = (final_data8_t_minus_1[\"billing_pnas_gmv\"] * 100) / final_data8_t_minus_1[\n", "# \"gmv\"\n", "# ]\n", "final_data8_t_minus_1[\"%Checkout to <PERSON> Within 3 mins\"] = (\n", "    final_data8_t_minus_1[\"not_delayed_<=3(mins)\"] * 100\n", ") / final_data8_t_minus_1[\"total_orders_<=8_ipo\"]\n", "\n", "\n", "final_data8_t_minus_1[\"%Checkout to <PERSON> Within 5 mins\"] = (\n", "    final_data8_t_minus_1[\"not_delayed_<=5(mins)\"] * 100\n", ") / final_data8_t_minus_1[\"total_orders_>8_ipo\"]\n", "\n", "final_data8_t_minus_1[\"%Checkout to Assign Within 30 secs\"] = (\n", "    final_data8_t_minus_1[\"checkout_to_picker_assigned<=30(secs)\"] * 100\n", ") / final_data8_t_minus_1[\"order_id\"]\n", "\n", "final_data8_t_minus_1[\"Fill Rate PNA Penalty\"] = final_data8_t_minus_1.apply(\n", "    lambda x: x[\"total_pna_loss\"] - x[\"Fill Rate PNA Leverage\"]\n", "    if (x[\"total_pna_loss\"] > x[\"Fill Rate PNA Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_t_minus_1[\"Inventory Variance Penalty\"] = final_data8_t_minus_1.apply(\n", "    lambda x: x[\"stock_variance_loss\"] - x[\"Inventory Variance Leverage\"]\n", "    if (x[\"stock_variance_loss\"] > x[\"Inventory Variance Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_t_minus_1[\"Quality Penalty\"] = final_data8_t_minus_1.apply(\n", "    lambda x: x[\"quality_gmv_loss\"] - x[\"Quality Leverage\"]\n", "    if (x[\"quality_gmv_loss\"] > x[\"Quality Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_t_minus_1[\"Damage Penalty\"] = final_data8_t_minus_1.apply(\n", "    lambda x: x[\"total_landing_price\"] - x[\"Damage Leverage\"]\n", "    if (x[\"total_landing_price\"] > x[\"Damage Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_t_minus_1[\"%Reinventorization Returns\"] = (\n", "    final_data8_t_minus_1[\"is_delayed\"] * 100 / final_data8_t_minus_1[\"putlist_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1 = final_data8_t_minus_1.rename(\n", "    columns={\n", "        \"outlet_name\": \"Merchant Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"total_order_count\": \"Total Orders\",\n", "        \"gmv\": \"GMV\",\n", "        \"total_pna_loss\": \"Fill Rate DS impact Loss\",\n", "        \"stock_variance_loss\": \"Inventory Variance Loss\",\n", "        \"quality_gmv_loss\": \"Quality Loss\",\n", "        \"delayed_indent\": \"Delayed Indents\",\n", "        \"damage_item_count\": \"Damage Item Count\",\n", "        \"fill_rate_impact_item_qty\": \"Picker PNA Item Qty\",\n", "        \"total_landing_price\": \"Damage Loss\",\n", "        \"billing_pnas_gmv\": \"Billing PNAs Loss\",\n", "        \"is_delayed\": \"Reinventorization Delayed Count\",\n", "        \"sto_count\": \"Discrepancy Raised TAT Breach STO Count\",\n", "        \"checkout_to_picker_assigned\": \"Avg Checkout to Assign Time\",\n", "        \"checkout_to_bill_time\": \"Avg Instore Time\",\n", "        \"open_esto_loss\": \"Open ESTO Loss\",\n", "        \"open_esto_qty\": \"Open ESTO Qty\",\n", "        # \"grn_per_within_tat\":\"GRN % Within TAT\",\n", "        # \"grn_per_overall\":\"GRN % Overall\",\n", "        # \"dn_within_tat\":\"DN Within TAT\",\n", "        # \"ttl_dn_amt\":\"DN Overall\"\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1 = final_data8_t_minus_1[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Total Orders\",\n", "        \"GMV\",\n", "        \"Fill Rate DS impact Loss\",\n", "        \"Picker PNA <PERSON>em <PERSON>\",\n", "        \"Quality Loss\",\n", "        \"Delayed Indents\",\n", "        \"Damage Item Count\",\n", "        \"Damage Loss\",\n", "        \"Merchant Name\",\n", "        \"Reinventorization Delayed Count\",\n", "        \"Inventory Variance Loss\",\n", "        \"Fill Rate PNA Leverage\",\n", "        \"Inventory Variance Leverage\",\n", "        \"Quality Leverage\",\n", "        \"Damage Leverage\",\n", "        \"%Fill Rate DS Impact\",\n", "        \"%Inventory Variance\",\n", "        \"%Item Missing\",\n", "        \"%Item Expired\",\n", "        \"%Damage\",\n", "        \"%Checkout to <PERSON> Within 3 mins\",\n", "        \"%Checkout to <PERSON> Within 5 mins\",\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"Fill Rate PNA Penalty\",\n", "        \"Inventory Variance Penalty\",\n", "        \"Quality Penalty\",\n", "        \"Damage Penalty\",\n", "        \"%Reinventorization Returns\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "        # \"GRN % Within TAT\",\n", "        # \"GRN % Overall\",\n", "        # \"DN Within TAT\",\n", "        # \"DN Overall\"\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1 = final_data8_t_minus_1.round(\n", "    {\n", "        \"Outlet ID\": 0,\n", "        \"Total Orders\": 0,\n", "        \"GMV\": 2,\n", "        \"Fill Rate DS impact Loss\": 2,\n", "        \"Picker PNA Item Qty\": 0,\n", "        \"Quality Loss\": 2,\n", "        \"Delayed Indents\": 0,\n", "        \"Damage Item Count\": 0,\n", "        \"Damage Loss\": 2,\n", "        \"Reinventorization Delayed Count\": 0,\n", "        \"Inventory Variance Loss\": 2,\n", "        \"Fill Rate PNA Leverage\": 2,\n", "        \"Inventory Variance Leverage\": 2,\n", "        \"Quality Leverage\": 2,\n", "        \"Damage Leverage\": 2,\n", "        \"%Fill Rate DS Impact\": 2,\n", "        \"%Inventory Variance\": 2,\n", "        \"%Item Missing\": 2,\n", "        \"%Item Expired\": 2,\n", "        \"%Damage\": 2,\n", "        \"%Checkout to <PERSON> Within 3 mins\": 2,\n", "        \"%Checkout to <PERSON> Within 5 mins\": 2,\n", "        \"Avg Checkout to Assign Time\": 2,\n", "        \"Avg Instore Time\": 2,\n", "        \"Fill Rate PNA Penalty\": 2,\n", "        \"Inventory Variance Penalty\": 2,\n", "        \"Quality Penalty\": 2,\n", "        \"Damage Penalty\": 2,\n", "        \"%Reinventorization Returns\": 2,\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\": 2,\n", "        \"Open ESTO Loss\": 2,\n", "        \"TAT Loss\": 2,\n", "        \"TAT Penalty\": 2,\n", "        # \"GRN % Within TAT\":2,\n", "        # \"GRN % Overall\":2,\n", "        # \"DN Within TAT\":2,\n", "        # \"DN Overall\":2\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining SLA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_sla = pd.DataFrame(\n", "    index=[\n", "        \"Impact of picker PNAs\",\n", "        \"Inventory loss due to Stock updates\",\n", "        \"Item Damages\",\n", "        \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\",\n", "        \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"Quality complaints - Item Missing\",\n", "        \"Quality complaints - Item Expired\",\n", "        # \"GRN to STO Discrepancy (within 4 hrs)\",\n", "        \"Reinventorization of Return (within 2 hrs)\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_sla = final_data8_sla.reset_index()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Preparing View for T-1 and MTD Metric and Loss"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item_temp = final_data8_t_minus_1[\n", "    [\"Merchant Name\", \"Picker PNA Item Qty\", \"Damage Item Count\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item_temp_temp = final_data8[[\"Merchant Name\"]].merge(\n", "    final_data8_t_minus_1_item_temp, on=\"Merchant Name\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item_temp_temp = final_data8_t_minus_1_item_temp_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item_temp_temp.rename(\n", "    columns={\n", "        \"Picker PNA Item Qty\": \"Impact of picker PNAs\",\n", "        \"Damage Item Count\": \"Item Damages\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item_temp_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item = pd.pivot_table(\n", "    final_data8_t_minus_1_item_temp_temp,\n", "    values=[\n", "        \"Impact of picker PNAs\",\n", "        \"Item Damages\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item = final_data8_t_minus_1_item.round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_item.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_item_temp = final_data8[\n", "    [\"Merchant Name\", \"Picker PNA Item Qty\", \"Damage Item Count\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_item_temp.rename(\n", "    columns={\n", "        \"Picker PNA Item Qty\": \"Impact of picker PNAs\",\n", "        \"Damage Item Count\": \"Item Damages\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_item = pd.pivot_table(\n", "    final_data8_item_temp,\n", "    values=[\n", "        \"Impact of picker PNAs\",\n", "        \"Item Damages\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_item = final_data8_mtd_item.round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_item.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp = final_data8_t_minus_1[\n", "    [\n", "        \"Merchant Name\",\n", "        \"GMV\",\n", "        \"Fill Rate DS impact Loss\",\n", "        \"Fill Rate PNA Penalty\",\n", "        \"Inventory Variance Loss\",\n", "        \"Inventory Variance Penalty\",\n", "        \"Quality Loss\",\n", "        \"Delayed Indents\",\n", "        \"Damage Loss\",\n", "        \"Damage Penalty\",\n", "        \"Reinventorization Delayed Count\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp[\"Reinventorization of Returns & Complete GRN TAT\"] = (\n", "    final_data8_t_minus_1_loss_temp[\"Reinventorization Delayed Count\"]\n", "    + final_data8_t_minus_1_loss_temp[\"Delayed Indents\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp_temp = final_data8[[\"Merchant Name\"]].merge(\n", "    final_data8_t_minus_1_loss_temp, on=\"Merchant Name\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp_temp = final_data8_t_minus_1_loss_temp_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp_temp.rename(\n", "    columns={\n", "        \"Fill Rate DS impact Loss\": \"PNA Loss\",\n", "        \"Fill Rate PNA Penalty\": \"PNA Penalty\",\n", "        \"Inventory Variance Loss\": \"Stock Variance Loss\",\n", "        \"Inventory Variance Penalty\": \"Stock Variance Penalty\",\n", "        \"Quality Loss\": \"Quality complaints\",\n", "        \"Delayed Indents\": \"GRN to STO Badstock\",\n", "        \"Damage Loss\": \"Damages Loss\",\n", "        \"Damage Penalty\": \"Damages Penalty\",\n", "        \"Billing PNAs Loss\": \"PNAs due to Billing Errors\",\n", "        \"Reinventorization Delayed Count\": \"Delays in Return Putaway\",\n", "        \"STO Discerpancy Amount\": \"STO Discrepancy Note\",\n", "        \"Open ESTO Loss\": \"Open ESTO Loss\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss = pd.pivot_table(\n", "    final_data8_t_minus_1_loss_temp_temp,\n", "    values=[\n", "        \"GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA Penalty\",\n", "        \"Stock Variance Loss\",\n", "        \"Stock Variance Penalty\",\n", "        \"Quality complaints\",\n", "        \"GRN to STO Badstock\",\n", "        \"Damages Loss\",\n", "        \"Damages Penalty\",\n", "        \"Delays in Return Putaway\",\n", "        \"Reinventorization of Returns & Complete GRN TAT\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss = final_data8_t_minus_1_loss.round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_temp = final_data8[\n", "    [\n", "        \"Merchant Name\",\n", "        \"GMV\",\n", "        \"Fill Rate DS impact Loss\",\n", "        \"Fill Rate PNA Penalty\",\n", "        \"Inventory Variance Loss\",\n", "        \"Inventory Variance Penalty\",\n", "        \"Quality Loss\",\n", "        \"Delayed Indents\",\n", "        \"Damage Loss\",\n", "        \"Damage Penalty\",\n", "        \"Reinventorization Delayed Count\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_temp[\"Reinventorization of Returns & Complete GRN TAT\"] = (\n", "    final_data8_temp[\"Reinventorization Delayed Count\"]\n", "    + final_data8_temp[\"Delayed Indents\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_temp.rename(\n", "    columns={\n", "        \"Fill Rate DS impact Loss\": \"PNA Loss\",\n", "        \"Fill Rate PNA Penalty\": \"PNA Penalty\",\n", "        \"Inventory Variance Loss\": \"Stock Variance Loss\",\n", "        \"Inventory Variance Penalty\": \"Stock Variance Penalty\",\n", "        \"Quality Loss\": \"Quality complaints\",\n", "        \"Delayed Indents\": \"GRN to STO Badstock\",\n", "        \"Damage Loss\": \"Damages Loss\",\n", "        \"Damage Penalty\": \"Damages Penalty\",\n", "        \"Billing PNAs Loss\": \"PNAs due to Billing Errors\",\n", "        \"Reinventorization Delayed Count\": \"Delays in Return Putaway\",\n", "        \"STO Discerpancy Amount\": \"STO Discrepancy Note\",\n", "        \"Open ESTO Loss\": \"Open ESTO Loss\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_loss = pd.pivot_table(\n", "    final_data8_temp,\n", "    values=[\n", "        \"GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA Penalty\",\n", "        \"Stock Variance Loss\",\n", "        \"Stock Variance Penalty\",\n", "        \"Quality complaints\",\n", "        \"GRN to STO Badstock\",\n", "        \"Damages Loss\",\n", "        \"Damages Penalty\",\n", "        \"Delays in Return Putaway\",\n", "        \"Reinventorization of Returns & Complete GRN TAT\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_loss = final_data8_mtd_loss.round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp = final_data8_t_minus_1[\n", "    [\n", "        \"Merchant Name\",\n", "        \"GMV\",\n", "        \"%Fill Rate DS Impact\",\n", "        \"%Inventory Variance\",\n", "        \"%Item Missing\",\n", "        \"%Item Expired\",\n", "        \"%Damage\",\n", "        \"%Checkout to <PERSON> Within 3 mins\",\n", "        \"%Checkout to <PERSON> Within 5 mins\",\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"%Reinventorization Returns\",\n", "        \"Delayed Indents\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp_temp = final_data8[[\"Merchant Name\"]].merge(\n", "    final_data8_t_minus_1_target_temp, on=\"Merchant Name\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp_temp = final_data8_t_minus_1_target_temp_temp.fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp_temp.rename(\n", "    columns={\n", "        \"%Fill Rate DS Impact\": \"Impact of picker PNAs\",\n", "        \"%Inventory Variance\": \"Inventory loss due to Stock updates\",\n", "        \"%Item Missing\": \"Quality complaints - Item Missing\",\n", "        \"%Item Expired\": \"Quality complaints - Item Expired\",\n", "        \"%Damage\": \"Item Damages\",\n", "        \"%Checkout to <PERSON> Within 3 mins\": \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\",\n", "        \"%Checkout to <PERSON> Within 5 mins\": \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\",\n", "        \"%Checkout to <PERSON>sign Within 30 secs\": \"Checkout to <PERSON>sign Within 30 secs\",\n", "        \"%Reinventorization Returns\": \"Reinventorization of Return (within 2 hrs)\",\n", "        \"Delayed Indents\": \"GRN to STO Discrepancy (within 4 hrs)\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp_temp = final_data8_t_minus_1_target_temp_temp.round(\n", "    {\n", "        \"GMV\": 0,\n", "        \"Impact of picker PNAs\": 2,\n", "        \"Inventory loss due to Stock updates\": 2,\n", "        \"Quality complaints - Item Missing\": 2,\n", "        \"Quality complaints - Item Expired\": 2,\n", "        \"Item Damages\": 2,\n", "        \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\": 2,\n", "        \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\": 2,\n", "        \"Checkout to <PERSON><PERSON> Within 30 secs\": 2,\n", "        \"Avg Checkout to Assign Time\": 2,\n", "        \"Avg Instore Time\": 2,\n", "        \"Reinventorization of Return (within 2 hrs)\": 2,\n", "        \"GRN to STO Discrepancy (within 4 hrs)\": 0,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target_temp_temp.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target = pd.pivot_table(\n", "    final_data8_t_minus_1_target_temp_temp,\n", "    values=[\n", "        \"GMV\",\n", "        \"Impact of picker PNAs\",\n", "        \"Inventory loss due to Stock updates\",\n", "        \"Quality complaints - Item Missing\",\n", "        \"Quality complaints - Item Expired\",\n", "        \"Item Damages\",\n", "        \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\",\n", "        \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\",\n", "        \"Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"Reinventorization of Return (within 2 hrs)\",\n", "        \"GRN to STO Discrepancy (within 4 hrs)\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_target.iloc[0:2, 1:] = (\n", "    final_data8_t_minus_1_target.iloc[0:2, 1:].astype(str) + \" mins\"\n", ")\n", "final_data8_t_minus_1_target.iloc[2:5, 1:] = (\n", "    final_data8_t_minus_1_target.iloc[2:5, 1:].astype(str) + \" %\"\n", ")\n", "final_data8_t_minus_1_target.iloc[6:7, 1:] = (\n", "    final_data8_t_minus_1_target.iloc[6:7, 1:].astype(int).astype(str) + \" day\"\n", ")\n", "final_data8_t_minus_1_target.iloc[7:13, 1:] = (\n", "    final_data8_t_minus_1_target.iloc[7:13, 1:].astype(str) + \" %\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target_temp = final_data8[\n", "    [\n", "        \"Merchant Name\",\n", "        \"GMV\",\n", "        \"%Fill Rate DS Impact\",\n", "        \"%Inventory Variance\",\n", "        \"%Item Missing\",\n", "        \"%Item Expired\",\n", "        \"%Damage\",\n", "        \"%Checkout to <PERSON> Within 3 mins\",\n", "        \"%Checkout to <PERSON> Within 5 mins\",\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"%Reinventorization Returns\",\n", "        \"Delayed Indents\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target_temp = final_data8_mtd_target_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target_temp.rename(\n", "    columns={\n", "        \"%Fill Rate DS Impact\": \"Impact of picker PNAs\",\n", "        \"%Inventory Variance\": \"Inventory loss due to Stock updates\",\n", "        \"%Item Missing\": \"Quality complaints - Item Missing\",\n", "        \"%Item Expired\": \"Quality complaints - Item Expired\",\n", "        \"%Damage\": \"Item Damages\",\n", "        \"%Checkout to <PERSON> Within 3 mins\": \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\",\n", "        \"%Checkout to <PERSON> Within 5 mins\": \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\",\n", "        \"%Checkout to <PERSON>sign Within 30 secs\": \"Checkout to <PERSON>sign Within 30 secs\",\n", "        \"%Reinventorization Returns\": \"Reinventorization of Return (within 2 hrs)\",\n", "        \"Delayed Indents\": \"GRN to STO Discrepancy (within 4 hrs)\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target_temp = final_data8_mtd_target_temp.round(\n", "    {\n", "        \"GMV\": 0,\n", "        \"Impact of picker PNAs\": 2,\n", "        \"Inventory loss due to Stock updates\": 2,\n", "        \"Quality complaints - Item Missing\": 2,\n", "        \"Quality complaints - Item Expired\": 2,\n", "        \"Item Damages\": 2,\n", "        \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\": 2,\n", "        \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\": 2,\n", "        \"Checkout to <PERSON><PERSON> Within 30 secs\": 2,\n", "        \"Avg Checkout to Assign Time\": 2,\n", "        \"Avg Instore Time\": 2,\n", "        \"Reinventorization of Return (within 2 hrs)\": 2,\n", "        \"GRN to STO Discrepancy (within 4 hrs)\": 0,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target = pd.pivot_table(\n", "    final_data8_mtd_target_temp,\n", "    values=[\n", "        \"GMV\",\n", "        \"Impact of picker PNAs\",\n", "        \"Inventory loss due to Stock updates\",\n", "        \"Quality complaints - Item Missing\",\n", "        \"Quality complaints - Item Expired\",\n", "        \"Item Damages\",\n", "        \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\",\n", "        \"Checkout to <PERSON><PERSON> within 5 mins (IPO>8)\",\n", "        \"Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"Avg Checkout to Assign Time\",\n", "        \"Avg Instore Time\",\n", "        \"Reinventorization of Return (within 2 hrs)\",\n", "        \"GRN to STO Discrepancy (within 4 hrs)\",\n", "    ],\n", "    columns=[\"Merchant Name\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd_target.iloc[0:2, 1:] = (\n", "    final_data8_mtd_target.iloc[0:2, 1:].astype(str) + \" mins\"\n", ")\n", "final_data8_mtd_target.iloc[2:5, 1:] = (\n", "    final_data8_mtd_target.iloc[2:5, 1:].astype(str) + \" %\"\n", ")\n", "final_data8_mtd_target.iloc[6:7, 1:] = (\n", "    final_data8_mtd_target.iloc[6:7, 1:].astype(int).astype(str) + \" Delay days\"\n", ")\n", "final_data8_mtd_target.iloc[7:13, 1:] = (\n", "    final_data8_mtd_target.iloc[7:13, 1:].astype(str) + \" %\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_names = list(final_data8[\"Merchant Name\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_sla[\"index\"].values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target = {\n", "    \"Impact of picker PNAs\": \"<0.25%\",\n", "    \"Inventory loss due to Stock updates\": \"<0.1%\",\n", "    \"Quality complaints - Item Missing\": \"<0.5%\",\n", "    \"Quality complaints - Item Expired\": \"0%\",\n", "    \"Item Damages\": \"<0.15%\",\n", "    \"Checkout to <PERSON><PERSON> within 3 mins (IPO<=8)\": \">=70%\",\n", "    \"Checkout to Bill<PERSON> within 5 mins (IPO>8)\": \">=70%\",\n", "    \"Checkout to Assign Within 30 secs\": \">=90%\",\n", "    \"Avg Checkout to Assign Time\": \"<=0.5 mins\",\n", "    \"Avg Instore Time\": \"<3 mins\",\n", "    # 'GRN to STO Discrepancy (within 4 hrs)':'0 Days',\n", "    \"Reinventorization of Return (within 2 hrs)\": \"100%\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def target_check(x):\n", "    #     float(x.split()[0])\n", "    if x[\"SLA\"] == \"Impact of picker PNAs\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) < 0.25:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Inventory loss due to Stock updates\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) < 0.1:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Quality complaints - Item Missing\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) < 0.5:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Quality complaints - Item Expired\":\n", "        if int(float(x[\"Month-to-Date\"].split()[0])) <= 0:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Checkout to Bill<PERSON> within 5 mins (IPO>8)\":\n", "        if int(float(x[\"Month-to-Date\"].split()[0])) == 99:\n", "            return \"\\u2015\"\n", "        else:\n", "            return \"\\u2015\"\n", "    if x[\"SLA\"] == \"Checkout to Bill<PERSON> within 3 mins (IPO<=8)\":\n", "        if int(float(x[\"Month-to-Date\"].split()[0])) >= 70:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Checkout to Assign Within 30 secs\":\n", "        if int(float(x[\"Month-to-Date\"].split()[0])) >= 90:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Avg Checkout to Assign Time\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) <= 0.5:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Avg Instore Time\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) < 3:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Item Damages\":\n", "        if float(x[\"Month-to-Date\"].split()[0]) < 0.15:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"Reinventorization of Return (within 2 hrs)\":\n", "        if int(float(x[\"Month-to-Date\"].split()[0])) == 100:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "    if x[\"SLA\"] == \"GRN to STO Discrepancy (within 4 hrs)\":\n", "        if int(x[\"Month-to-Date\"].split()[0]) < 1:\n", "            return \"\\u2705\"\n", "        else:\n", "            return \"\\u274c\"\n", "\n", "\n", "#         print(float(x['Month-to-Date'].split()[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_t_minus_1_loss_temp_temp = final_data8_t_minus_1_loss_temp_temp.rename(\n", "    columns={\n", "        \"PNA Loss\": \"PNA Loss T-1\",\n", "        \"PNA Penalty\": \"PNA Penalty T-1\",\n", "        \"Stock Variance Loss\": \"Stock Variance Loss T-1\",\n", "        \"Stock Variance Penalty\": \"Stock Variance Penalty T-1\",\n", "        \"Quality complaints\": \"Quality complaints T-1\",\n", "        \"GRN to STO Badstock\": \"GRN to STO Badstock T-1\",\n", "        \"Damages Loss\": \"Damages Loss T-1\",\n", "        \"Damages Penalty\": \"Damages Penalty T-1\",\n", "        \"Delays in Return Putaway\": \"Delays in Return Putaway T-1\",\n", "        \"Open ESTO Loss\": \"Open ESTO Loss T-1\",\n", "        \"Open ESTO Qty\": \"Open ESTO Qty T-1\",\n", "        \"TAT Loss\": \"TAT Loss T-1\",\n", "        \"TAT Penalty\": \"TAT Penalty T-1\",\n", "        \"Reinventorization of Returns & Complete GRN TAT\": \"Reinventorization of Returns & Complete GRN TAT T-1\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_summary_loss = final_data8_temp.merge(\n", "    final_data8_t_minus_1_loss_temp_temp[\n", "        [\n", "            \"Merchant Name\",\n", "            \"PNA Loss T-1\",\n", "            \"PNA Penalty T-1\",\n", "            \"Stock Variance Loss T-1\",\n", "            \"Stock Variance Penalty T-1\",\n", "            \"Quality complaints T-1\",\n", "            \"GRN to STO Badstock T-1\",\n", "            \"Damages Loss T-1\",\n", "            \"Damages Penalty T-1\",\n", "            \"Delays in Return Putaway T-1\",\n", "            \"Open ESTO Loss T-1\",\n", "            \"Open ESTO Qty T-1\",\n", "            \"TAT Loss T-1\",\n", "            \"TAT Penalty T-1\",\n", "            \"Reinventorization of Returns & Complete GRN TAT T-1\",\n", "        ]\n", "    ],\n", "    on=[\"Merchant Name\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_summary_loss[\n", "    [\n", "        \"GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA Penalty\",\n", "        \"Stock Variance Loss\",\n", "        \"Stock Variance Penalty\",\n", "        \"Quality complaints\",\n", "        \"GRN to STO Badstock\",\n", "        \"Damages Loss\",\n", "        \"Damages Penalty\",\n", "        \"Delays in Return Putaway\",\n", "        \"Open ESTO Loss\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "        \"Reinventorization of Returns & Complete GRN TAT\",\n", "        \"PNA Loss T-1\",\n", "        \"PNA Penalty T-1\",\n", "        \"Stock Variance Loss T-1\",\n", "        \"Stock Variance Penalty T-1\",\n", "        \"Quality complaints T-1\",\n", "        \"GRN to STO Badstock T-1\",\n", "        \"Damages Loss T-1\",\n", "        \"Damages Penalty T-1\",\n", "        \"Delays in Return Putaway T-1\",\n", "        \"Open ESTO Loss T-1\",\n", "        \"Open ESTO Qty T-1\",\n", "        \"TAT Loss T-1\",\n", "        \"TAT Penalty T-1\",\n", "        \"Reinventorization of Returns & Complete GRN TAT T-1\",\n", "    ]\n", "] = final_summary_loss[\n", "    [\n", "        \"GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA Penalty\",\n", "        \"Stock Variance Loss\",\n", "        \"Stock Variance Penalty\",\n", "        \"Quality complaints\",\n", "        \"GRN to STO Badstock\",\n", "        \"Damages Loss\",\n", "        \"Damages Penalty\",\n", "        \"Delays in Return Putaway\",\n", "        \"Open ESTO Loss\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "        \"Reinventorization of Returns & Complete GRN TAT\",\n", "        \"PNA Loss T-1\",\n", "        \"PNA Penalty T-1\",\n", "        \"Stock Variance Loss T-1\",\n", "        \"Stock Variance Penalty T-1\",\n", "        \"Quality complaints T-1\",\n", "        \"GRN to STO Badstock T-1\",\n", "        \"Damages Loss T-1\",\n", "        \"Damages Penalty T-1\",\n", "        \"Delays in Return Putaway T-1\",\n", "        \"Open ESTO Loss T-1\",\n", "        \"Open ESTO Qty T-1\",\n", "        \"TAT Loss T-1\",\n", "        \"TAT Penalty T-1\",\n", "        \"Reinventorization of Returns & Complete GRN TAT T-1\",\n", "    ]\n", "].round(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_summary = {}\n", "for i in merchant_names:\n", "    #     print(i)\n", "    final_data8_sla_temp = pd.DataFrame()\n", "    final_data8_sla_temp_penalty = pd.DataFrame()\n", "    final_data8_sla_temp = final_data8_sla[[\"index\"]].merge(\n", "        final_data8_t_minus_1_target[[\"index\", i]], on=\"index\"\n", "    )\n", "    final_data8_sla_temp.rename(columns={i: \"Yesterday\"}, inplace=True)\n", "    #     final_data8_sla_temp = final_data8_sla_temp.rename(columns={i:'T-1 Loss'})\n", "    #     final_data8_sla_temp_penalty_new = final_data8_sla_temp.merge(final_data8_t_minus_1_target[['index',i]],on='index')\n", "    #     final_data8_sla_temp_penalty_new = final_data8_sla_temp_penalty_new.rename(columns={i:'T-1 Metric'})\n", "    #     final_data8_sla_temp_t = final_data8_sla_temp_penalty_new.merge(final_data8_mtd_loss[['index',i]],on='index')\n", "    #     final_data8_sla_temp_t = final_data8_sla_temp_t.rename(columns={i:'MTD Loss'})\n", "    final_data8_sla_temp_target = final_data8_sla_temp.merge(\n", "        final_data8_mtd_target[[\"index\", i]], on=\"index\"\n", "    )\n", "    final_data8_sla_temp_target = final_data8_sla_temp_target.rename(\n", "        columns={i: \"Month-to-Date\"}\n", "    )\n", "\n", "    final_data8_sla_temp_target.rename(columns={\"index\": \"SLA\"}, inplace=True)\n", "    final_data8_sla_temp_target[\"Target\"] = final_data8_sla_temp_target[\"SLA\"].map(\n", "        target\n", "    )\n", "    final_data8_sla_temp_target[\"Remarks\"] = final_data8_sla_temp_target.apply(\n", "        lambda x: target_check(x), axis=1\n", "    )\n", "    final_data8_sla_temp_target[\"Yesterday\"] = np.where(\n", "        final_data8_sla_temp_target[\"Yesterday\"] == \"1 day\",\n", "        \"Some Delays\",\n", "        np.where(\n", "            final_data8_sla_temp_target[\"Yesterday\"] == \"0 day\",\n", "            \"No Delays\",\n", "            final_data8_sla_temp_target[\"Yesterday\"],\n", "        ),\n", "    )\n", "    final_data8_sla_temp_target[\"SLA\"] = np.where(\n", "        final_data8_sla_temp_target[\"SLA\"] == \"Impact of picker PNAs\",\n", "        \"PNAs\",\n", "        final_data8_sla_temp_target[\"SLA\"],\n", "    )\n", "    final_data8_sla_temp_target[\"SLA\"] = np.where(\n", "        final_data8_sla_temp_target[\"SLA\"] == \"Inventory loss due to Stock updates\",\n", "        \"Stock variance - Overall\",\n", "        final_data8_sla_temp_target[\"SLA\"],\n", "    )\n", "    final_data8_sla_temp_target[\"SLA\"] = np.where(\n", "        final_data8_sla_temp_target[\"SLA\"] == \"Item Damages\",\n", "        \"Damages\",\n", "        final_data8_sla_temp_target[\"SLA\"],\n", "    )\n", "    # .rename(columns={'Impact of picker PNAs':'PNAs','Inventory loss due to Stock updates':'Stock variance - Overall','Item Damages':'Damages'})\n", "    final_summary[i] = final_data8_sla_temp_target\n", "#     .style.set_properties(subset='Remarks',**{'text-align': 'center'})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MTD Datewise view"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_mtd = gmv_orders_data_temp.groupby(\n", "    [\"delivery_date\", \"outlet_id\"], as_index=False\n", ").agg({\"delivered_order_count\": \"sum\", \"procured_gmv\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_mtd.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_orders_data_mtd.rename(\n", "    columns={\n", "        \"procured_gmv\": \"gmv\",\n", "        \"delivery_date\": \"date_ts\",\n", "        \"delivered_order_count\": \"total_order_count\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_mtd = fill_rate_ds_impact_data.groupby(\n", "    [\"scheduleddate\", \"outlet_id\"], as_index=False\n", ").agg({\"total_pna_loss\": \"sum\", \"order_id\": \"nunique\", \"pna_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_mtd.rename(\n", "    columns={\n", "        \"scheduleddate\": \"date_ts\",\n", "        \"order_id\": \"fill_rate_impact_order_count\",\n", "        \"pna_qty\": \"fill_rate_impact_item_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_mtd = checkout_to_billed_data.groupby(\n", "    [\"date_ts\", \"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"not_delayed_<=3(mins)\": \"sum\",\n", "        \"total_orders_<=8_ipo\": \"sum\",\n", "        \"not_delayed_<=5(mins)\": \"sum\",\n", "        \"total_orders_>8_ipo\": \"sum\",\n", "        \"checkout_to_picker_assigned<=30(secs)\": \"sum\",\n", "        \"order_id\": \"nunique\",\n", "        \"checkout_to_picker_assigned\": \"mean\",\n", "        \"checkout_to_bill_time\": \"mean\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_mtd[\"%TAT <=8 ipo\"] = (\n", "    checkout_to_billed_data_mtd[\"not_delayed_<=3(mins)\"]\n", "    * 100.0\n", "    / checkout_to_billed_data_mtd[\"total_orders_<=8_ipo\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_mtd[\"TAT Loss\"] = np.where(\n", "    checkout_to_billed_data_mtd[\"%TAT <=8 ipo\"] < 100,\n", "    (100 - checkout_to_billed_data_mtd[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_mtd[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")\n", "\n", "checkout_to_billed_data_mtd[\"TAT Penalty\"] = np.where(\n", "    checkout_to_billed_data_mtd[\"%TAT <=8 ipo\"] < 70,\n", "    (70 - checkout_to_billed_data_mtd[\"%TAT <=8 ipo\"])\n", "    * checkout_to_billed_data_mtd[\"total_orders_<=8_ipo\"]\n", "    / 100,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_mtd = checkout_to_billed_data_mtd.rename(\n", "    columns={\n", "        \"order_id\": \"total_orders\",\n", "        \"checkout_to_picker_assigned\": \"checkout_to_picker_assigned(mins)\",\n", "        \"checkout_to_bill_time\": \"instore_time(mins)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_mtd_temp = qng_expiry_data.groupby(\n", "    [\"date\", \"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"selling_price\": \"sum\",\n", "        \"product_id\": \"nunique\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_mtd_temp1 = qng_expiry_data_temp.groupby(\n", "    [\"date\", \"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        # \"selling_price\": \"sum\",\n", "        \"complaint_category_item_missing\": \"sum\",\n", "        \"complaint_category_expired\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_mtd = qng_expiry_data_mtd_temp1.merge(\n", "    qng_expiry_data_mtd_temp, on=[\"outlet_id\", \"date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_mtd.rename(\n", "    columns={\"selling_price\": \"quality_gmv_loss\", \"date\": \"date_ts\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_mtd = damage_data.groupby([\"date_ts\", \"outlet_id\"], as_index=False).agg(\n", "    {\"total_landing_price\": \"sum\", \"quantity\": \"sum\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_mtd = damage_data_mtd.rename(columns={\"quantity\": \"damage_item_count\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"-\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative[\"total_price_change\"] = (\n", "    inventory_variance_data_mtd_negative[\"landing_price\"]\n", "    * inventory_variance_data_mtd_negative[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_new = inventory_variance_data_mtd_negative.groupby(\n", "    [\"pos_timestamp\", \"outlet_id\", \"item id\"], as_index=False\n", ").agg({\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_mtd = fill_rate_ds_impact_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_mtd[\"pna_qty\"] = (\n", "    fill_rate_ds_impact_data_temp_mtd[\"ordered_quantity\"]\n", "    - fill_rate_ds_impact_data_temp_mtd[\"procured_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group_mtd = fill_rate_ds_impact_data_temp_mtd.groupby(\n", "    [\"scheduleddate\", \"outlet_id\", \"item_id\"], as_index=False\n", ").agg({\"order_id\": \"nunique\", \"pna_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final = (\n", "    inventory_variance_data_mtd_negative_new.merge(\n", "        fill_rate_ds_impact_data_temp_group_mtd,\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "        left_on=[\"outlet_id\", \"item id\"],\n", "        how=\"left\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final[\n", "    \"pna_qty\"\n", "] = inventory_variance_data_mtd_negative_final[\"pna_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final[\"qty_to_consider\"] = (\n", "    inventory_variance_data_mtd_negative_final[\"change quantity\"]\n", "    - inventory_variance_data_mtd_negative_final[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final[\"qty_to_consider\"] = np.where(\n", "    inventory_variance_data_mtd_negative_final[\"qty_to_consider\"] < 0,\n", "    0,\n", "    inventory_variance_data_mtd_negative_final[\"qty_to_consider\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final[\"negative_change\"] = (\n", "    inventory_variance_data_mtd_negative_final[\"qty_to_consider\"]\n", "    * inventory_variance_data_mtd_negative_final[\"landing_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_final[\n", "    inventory_variance_data_mtd_negative_final[\"pna_qty\"] > 0\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_negative_group = (\n", "    inventory_variance_data_mtd_negative_final.groupby([\"pos_timestamp\", \"outlet_id\"])[\n", "        \"negative_change\"\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_positive = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"+\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_positive[\"total_price_change\"] = (\n", "    inventory_variance_data_mtd_positive[\"landing_price\"]\n", "    * inventory_variance_data_mtd_positive[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_variance_raw_mtd = pd.concat(\n", "    [inventory_variance_data_mtd_negative_final, inventory_variance_data_mtd_positive]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_positive_group = (\n", "    inventory_variance_data_mtd_positive.groupby(\n", "        [\"pos_timestamp\", \"outlet_id\"], as_index=False\n", "    ).agg(\n", "        {\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_final = inventory_variance_data_mtd_negative_group.merge(\n", "    inventory_variance_data_mtd_positive_group,\n", "    on=[\"outlet_id\", \"pos_timestamp\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_final[\"stock_variance_loss\"] = (\n", "    inventory_variance_data_mtd_final[\"total_price_change\"]\n", "    - inventory_variance_data_mtd_final[\"negative_change\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_final[\"stock_variance_loss\"] = np.where(\n", "    inventory_variance_data_mtd_final[\"stock_variance_loss\"] > 0,\n", "    0,\n", "    inventory_variance_data_mtd_final[\"stock_variance_loss\"] * -1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_final = inventory_variance_data_mtd_final[\n", "    [\"pos_timestamp\", \"outlet_id\", \"stock_variance_loss\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_new = inventory_variance_data_mtd_final.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_new.rename(\n", "    columns={\"pos_timestamp\": \"date_ts\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_mtd_new.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_to_esto_data_t_minus_1 = grn_to_esto_data[grn_to_esto_data['sto_created_date']==end_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd = grn_to_sto_disc_data.groupby(\n", "    [\"outlet_id\", \"date_ts\"], as_index=False\n", ").agg({\"avg_bad_stock_update_time\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd = grn_to_sto_disc_data_mtd.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd[\"delay_flag\"] = grn_to_sto_disc_data_mtd[\n", "    \"avg_bad_stock_update_time\"\n", "].apply(lambda x: 1 if x > 14400 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd_new = grn_to_sto_disc_data_mtd.groupby(\n", "    [\"date_ts\", \"outlet_id\"], as_index=False\n", ").agg({\"delay_flag\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd_new.rename(\n", "    columns={\n", "        \"delay_flag\": \"delayed_indent\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_to_sto_disc_data_mtd_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reinventorization_returns_data_mtd = reinventorization_returns_data.groupby(\n", "    [\"date_ts\", \"outlet_id\"], as_index=False\n", ").agg({\"is_delayed\": \"sum\", \"putlist_id\": \"nunique\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_esto_data_mtd = open_esto_data.groupby(\n", "    [\"date_ts\", \"outlet_id\"], as_index=False\n", ").agg({\"open_esto_loss\": \"sum\", \"open_esto_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_mtd = (\n", "    dn_data.groupby([\"date_\", \"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amount\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_mtd[\"grn_per_within_tat\"] = (\n", "    dn_data_mtd[\"grn_within_tat\"] * 100.0 / dn_data_mtd[\"amt_to_ds\"]\n", ")\n", "dn_data_mtd[\"grn_per_overall\"] = (\n", "    dn_data_mtd[\"grn_overall\"] * 100.0 / dn_data_mtd[\"amt_to_ds\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_mtd = dn_data_mtd.rename(\n", "    columns={\"date_\": \"date_ts\", \"rec_outlet_id\": \"outlet_id\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qng_expiry_data_mtd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mtd = gmv_orders_data_mtd.merge(\n", "    fill_rate_ds_impact_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "final_data_mtd_temp = final_data_mtd.merge(\n", "    checkout_to_billed_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "# final_data1_mtd = final_data_mtd_temp.merge(\n", "#     inventory_variance_data_mtd_new, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", "# )\n", "final_data2_mtd = final_data_mtd_temp.merge(\n", "    qng_expiry_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "# final_data3_mtd = final_data2_mtd.merge(\n", "#     grn_to_sto_disc_data_mtd_new, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", "# )\n", "final_data4_mtd = final_data2_mtd.merge(\n", "    damage_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "final_data5_mtd = final_data4_mtd.merge(merchants_data, on=\"outlet_id\", how=\"left\")\n", "\n", "final_data6_mtd = final_data5_mtd.merge(\n", "    open_esto_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "final_data7_mtd = final_data6_mtd.merge(\n", "    dn_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "\n", "final_data8_mtd = final_data7_mtd.merge(\n", "    reinventorization_returns_data_mtd, on=[\"date_ts\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd = final_data8_mtd.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd[\"Fill Rate PNA Leverage\"] = final_data8_mtd[\"gmv\"] * (0.25 / 100)\n", "# final_data8_mtd[\"Inventory Variance Leverage\"] = final_data8_mtd[\"gmv\"] * (0.1 / 100)\n", "final_data8_mtd[\"Quality Leverage\"] = final_data8_mtd[\"gmv\"] * (0.5 / 100)\n", "final_data8_mtd[\"Damage Leverage\"] = final_data8_mtd[\"gmv\"] * (0.15 / 100)\n", "\n", "\n", "final_data8_mtd[\"%Fill Rate DS Impact\"] = (\n", "    final_data8_mtd[\"total_pna_loss\"] * 100\n", ") / final_data8_mtd[\"gmv\"]\n", "# final_data8_mtd[\"%Inventory Variance\"] = (\n", "#     final_data8_mtd[\"stock_variance_loss\"] * 100\n", "# ) / final_data8_mtd[\"gmv\"]\n", "final_data8_mtd[\"%Item Missing\"] = (\n", "    final_data8_mtd[\"complaint_category_item_missing\"] * 100\n", ") / final_data8_mtd[\"total_order_count\"]\n", "final_data8_mtd[\"%Item Expired\"] = (\n", "    final_data8_mtd[\"complaint_category_expired\"] * 100\n", ") / final_data8_mtd[\"total_order_count\"]\n", "final_data8_mtd[\"%Damage\"] = (\n", "    final_data8_mtd[\"total_landing_price\"] * 100\n", ") / final_data8_mtd[\"gmv\"]\n", "# final_data8_mtd[\"%Billing PNA\"] = (final_data8_mtd[\"billing_pnas_gmv\"] * 100) / final_data8_mtd[\n", "# \"gmv\"\n", "# ]\n", "final_data8_mtd[\"%Checkout to <PERSON> Within 3 mins\"] = (\n", "    final_data8_mtd[\"not_delayed_<=3(mins)\"] * 100\n", ") / final_data8_mtd[\"total_orders_<=8_ipo\"]\n", "\n", "\n", "final_data8_mtd[\"%Checkout to <PERSON> Within 5 mins\"] = (\n", "    final_data8_mtd[\"not_delayed_<=5(mins)\"] * 100\n", ") / final_data8_mtd[\"total_orders_>8_ipo\"]\n", "\n", "final_data8_mtd[\"%Checkout to Assign Within 30 secs\"] = (\n", "    final_data8_mtd[\"checkout_to_picker_assigned<=30(secs)\"] * 100\n", ") / final_data8_mtd[\"total_orders\"]\n", "\n", "final_data8_mtd[\"Fill Rate PNA Penalty\"] = final_data8_mtd.apply(\n", "    lambda x: x[\"total_pna_loss\"] - x[\"Fill Rate PNA Leverage\"]\n", "    if (x[\"total_pna_loss\"] > x[\"Fill Rate PNA Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "# final_data8_mtd[\"Inventory Variance Penalty\"] = final_data8_mtd.apply(\n", "#     lambda x: x[\"stock_variance_loss\"] - x[\"Inventory Variance Leverage\"]\n", "#     if (x[\"stock_variance_loss\"] > x[\"Inventory Variance Leverage\"])\n", "#     else 0,\n", "#     axis=1,\n", "# )\n", "final_data8_mtd[\"Quality Penalty\"] = final_data8_mtd.apply(\n", "    lambda x: x[\"quality_gmv_loss\"] - x[\"Quality Leverage\"]\n", "    if (x[\"quality_gmv_loss\"] > x[\"Quality Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_mtd[\"Damage Penalty\"] = final_data8_mtd.apply(\n", "    lambda x: x[\"total_landing_price\"] - x[\"Damage Leverage\"]\n", "    if (x[\"total_landing_price\"] > x[\"Damage Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "final_data8_mtd[\"%Reinventorization Returns\"] = (\n", "    final_data8_mtd[\"is_delayed\"] * 100 / final_data8_mtd[\"putlist_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd = final_data8_mtd.rename(\n", "    columns={\n", "        \"date_ts\": \"Date\",\n", "        \"outlet_name\": \"Merchant Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"total_order_count\": \"Total Orders\",\n", "        \"gmv\": \"GMV\",\n", "        \"total_pna_loss\": \"Fill Rate DS impact Loss\",\n", "        \"stock_variance_loss\": \"Inventory Variance Loss\",\n", "        \"quality_gmv_loss\": \"Quality Loss\",\n", "        \"delayed_indent\": \"Delayed Indents\",\n", "        \"damage_item_count\": \"Damage Item Count\",\n", "        \"fill_rate_impact_item_qty\": \"Picker PNA Item Qty\",\n", "        \"total_landing_price\": \"Damage Loss\",\n", "        \"billing_pnas_gmv\": \"Billing PNAs Loss\",\n", "        \"is_delayed\": \"Reinventorization Delayed Count\",\n", "        \"sto_count\": \"Discrepancy Raised TAT Breach STO Count\",\n", "        \"open_esto_loss\": \"Open ESTO Loss\",\n", "        \"open_esto_qty\": \"Open ESTO Qty\",\n", "        \"grn_per_within_tat\": \"GRN % Within TAT\",\n", "        \"grn_per_overall\": \"GRN % Overall\",\n", "        \"dn_within_tat\": \"DN Within TAT\",\n", "        \"ttl_dn_amt\": \"DN Overall\",\n", "        \"ttl_b2b_ret_amount\": \"B2B Return Amount\",\n", "        \"dn_other_amount\": \"DN Other Amount\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd = final_data8_mtd[\n", "    [\n", "        \"Date\",\n", "        \"Outlet ID\",\n", "        \"Total Orders\",\n", "        \"GMV\",\n", "        \"Fill Rate DS impact Loss\",\n", "        \"Picker PNA <PERSON>em <PERSON>\",\n", "        \"Quality Loss\",\n", "        # 'Delayed Indents',\n", "        \"Damage Item Count\",\n", "        \"Damage Loss\",\n", "        \"Merchant Name\",\n", "        \"Reinventorization Delayed Count\",\n", "        # \"Inventory Variance Loss\",\n", "        \"Fill Rate PNA Leverage\",\n", "        # \"Inventory Variance Leverage\",\n", "        \"Quality Leverage\",\n", "        \"Damage Leverage\",\n", "        \"%Fill Rate DS Impact\",\n", "        # \"%Inventory Variance\",\n", "        \"%Item Missing\",\n", "        \"%Item Expired\",\n", "        \"%Damage\",\n", "        \"%Checkout to <PERSON> Within 3 mins\",\n", "        \"%Checkout to <PERSON> Within 5 mins\",\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\",\n", "        \"checkout_to_picker_assigned(mins)\",\n", "        \"instore_time(mins)\",\n", "        \"Fill Rate PNA Penalty\",\n", "        # \"Inventory Variance Penalty\",\n", "        \"Quality Penalty\",\n", "        \"Damage Penalty\",\n", "        \"%Reinventorization Returns\",\n", "        \"Open ESTO Loss\",\n", "        \"Open ESTO Qty\",\n", "        \"TAT Loss\",\n", "        \"TAT Penalty\",\n", "        \"GRN % Within TAT\",\n", "        \"GRN % Overall\",\n", "        \"DN Within TAT\",\n", "        \"DN Overall\",\n", "        \"B2B Return Amount\",\n", "        \"DN Other Amount\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd = final_data8_mtd.round(\n", "    {\n", "        \"Outlet ID\": 0,\n", "        \"Total Orders\": 0,\n", "        \"GMV\": 2,\n", "        \"Fill Rate DS impact Loss\": 2,\n", "        \"Picker PNA Item Count\": 0,\n", "        \"Quality Loss\": 2,\n", "        \"Delayed Indents\": 0,\n", "        \"Damage Item Count\": 0,\n", "        \"Damage Loss\": 2,\n", "        \"Reinventorization Delayed Count\": 0,\n", "        \"Inventory Variance Loss\": 2,\n", "        \"Fill Rate PNA Leverage\": 2,\n", "        \"Inventory Variance Leverage\": 2,\n", "        \"Quality Leverage\": 2,\n", "        \"Damage Leverage\": 2,\n", "        \"%Fill Rate DS Impact\": 2,\n", "        \"%Inventory Variance\": 2,\n", "        \"%Item Missing\": 2,\n", "        \"%Item Expired\": 2,\n", "        \"%Damage\": 2,\n", "        \"%Checkout to <PERSON> Within 3 mins\": 2,\n", "        \"%Checkout to <PERSON> Within 5 mins\": 2,\n", "        \"%Checkout to <PERSON><PERSON> Within 30 secs\": 2,\n", "        \"checkout_to_picker_assigned(mins)\": 2,\n", "        \"instore_time(mins)\": 2,\n", "        \"Fill Rate PNA Penalty\": 2,\n", "        \"Inventory Variance Penalty\": 2,\n", "        \"Quality Penalty\": 2,\n", "        \"Damage Penalty\": 2,\n", "        \"%Reinventorization Returns\": 2,\n", "        \"Open ESTO Loss\": 2,\n", "        \"TAT Loss\": 2,\n", "        \"TAT Penalty\": 2,\n", "        \"GRN % Within TAT\": 2,\n", "        \"GRN % Overall\": 2,\n", "        \"DN Within TAT\": 0,\n", "        \"DN Overall\": 0,\n", "        \"B2B Return Amount\": 0,\n", "        \"DN Other Amount\": 0,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data8_mtd.rename(\n", "    columns={\n", "        \"Delayed Indents\": \"Delays in STO Putaway (GRN)\",\n", "        \"Fill Rate DS impact Loss\": \"Impact of picker PNAs Loss\",\n", "        \"%Fill Rate DS Impact\": \"%Impact of picker PNAs\",\n", "        \"Inventory Variance Loss\": \"Inventory loss due to Stock updates\",\n", "        \"%Inventory Variance\": \"%Inventory loss due to Stock updates\",\n", "        \"Quality Loss\": \"Quality complaints Loss\",\n", "        \"%Quality\": \"%Quality complaints\",\n", "        \"Damage Loss\": \"Item Damages Loss\",\n", "        \"%Damage\": \"%Item Damages\",\n", "        \"Billing PNAs Loss\": \"PNAs due to Billing Errors\",\n", "        \"%Billing PNA\": \"%PNAs due to Billing Errors\",\n", "        \"%Checkout to <PERSON> Within time\": \"Checkout to <PERSON><PERSON> within 5 mins (IPO<=10)\",\n", "        \"checkout_to_picker_assigned(mins)\": \"Avg Checkout to Assign Time (mins)\",\n", "        \"instore_time(mins)\": \"Avg Instore Time (mins)\",\n", "        \"Reinventorization Delayed Count\": \"Delays in Return Putaway\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Raw data for Damages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_raw_q = \"\"\"\n", "with t1 as (\n", " SELECT \n", "        fc.id as facility_id,\n", "        fc.name as facility_name,\n", "    i.outlet_id as outlet_id,\n", "    ro.name AS Outlet_name,\n", "     r.item_id as item,\n", "     i.variant_id as variant_id,\n", "     i.upc_id as upc,\n", "     r.name AS Product,\n", "     case when b.item_id is not null and r.perishable=1 then 'perishable' end as perishables,\n", "    (CASE WHEN r.outlet_type = 1 THEN 'F&V_type' WHEN r.outlet_type = 2 THEN 'Grocery_type' ELSE 'Not Assigned' END) AS Item_Type,\n", "     SUM(i.\"delta\") AS quantity,\n", "     r.variant_mrp AS Mrp,\n", "     r.variant_uom_text AS grammage,\n", "     convert_timezone('Asia/Kolkata',i.pos_timestamp) AS POS_Timestamp,\n", "     i2.name AS Update_type,\n", "     ibur.name,\n", "     i.created_by_name as employee_id,\n", "     r.is_pl,\n", "     i.weighted_lp as \"weighted_landing_price\",\n", "     coalesce(tax.cess,0) as cess,\n", "     coalesce(tax.cgst,0) as cgst,\n", "     coalesce(tax.sgst,0) as sgst,\n", "     i.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) as cost_price\n", "     \n", " FROM\n", "    lake_ims.ims_inventory_log i\n", "         INNER JOIN\n", "     lake_rpc.product_product r ON r.variant_id = i.variant_id \n", "     \n", "     Left join \n", "     -----(select distinct item_id from metrics.item_wise_categories where L0 in ('Breakfast & Dairy', 'Eggs, Meat, Fish & Frozen')) b\n", "        (select distinct item_id from lake_rpc.item_category_details where L0 in ('chicken, meat & fish', 'dairy, bread & eggs','Dairy, Bread & Eggs','Chicken, Meat & Fish')) b\n", "        on r.item_id=b.item_id\n", "\n", "     \n", "         INNER JOIN\n", "     lake_retail.console_outlet ro ON ro.id = i.outlet_id and business_type_id =7\n", "     left join lake_crates.facility fc on ro.facility_id=fc.id\n", "         INNER JOIN\n", "     lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         LEFT JOIN\n", "     lake_rpc.product_tax tax ON r.item_id = tax.item_id and ro.tax_location_id = tax.location\n", "       LEFT JOIN\n", "       lake_ims.ims_bad_inventory_update_log ibil on i.inventory_update_id=ibil.inventory_update_id\n", "       LEFT join \n", "       lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", " WHERE\n", "     i.inventory_update_type_id in (11) and i.outlet_id in %(merchants_outlet_list)s\n", "      AND (date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "      and Update_type in ('bad_stock_damage_product') and ibil.reason_id not in (6,7,8,9)\n", "    GROUP BY 1,2,3,4,5, 6,7, 8 , 9,10,12,13 ,14,15,16,17,18,19,20,21,22,23\n", "  ) \n", "  \n", "  \n", "Select distinct\n", " t1.*,\n", " Mrp*quantity as Total_mrp,\n", " weighted_landing_price*quantity as Total_landing_Price,\n", " cost_price*quantity as Total_cost_price,\n", " (weighted_landing_price*quantity) - (cost_price*quantity) as Tax\n", "from t1 where outlet_id in %(merchants_outlet_list)s and item_type not in ('perishable','F&V_type')\n", "and perishables is null and item not in (10110215,10000436,10109019,10002963,10109881,10109878,10109880,10109879,10109877,10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976) \n", "  \"\"\"\n", "\n", "\n", "damage_data_raw = pd.read_sql(\n", "    damage_raw_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_raw.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_to_esto_data.rename(columns={\"putaway_time\": \"putaway_time (secs)\"}, inplace=True)\n", "reinventorization_returns_data.rename(\n", "    columns={\"putaway_time\": \"putaway_time (secs)\"}, inplace=True\n", ")\n", "# checkout_to_billed_data.rename(\n", "#     columns={\"checkout_to_bill_time\": \"checkout_to_bill_time (mins)\"}, inplace=True\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Mailing List and Merchant Name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_dict = dict(zip(merchants_data.outlet_id, merchants_data.outlet_name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data[\"city\"] = merchants_data[\"outlet_name\"].apply(lambda x: x.split()[2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data[\"external_name\"] = merchants_data[\"outlet_name\"].apply(\n", "    lambda x: x[x.rfind(\"ES\") :].split()[0]\n", ")\n", "# merchants_data['outlet_name'].apply(lambda x: \" \".join(x.split()[2:-1]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_data[\"mail_name\"] = (\n", "    merchants_data[\"city\"] + \" \" + merchants_data[\"external_name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mail_subject = dict(zip(merchants_data.outlet_id, merchants_data.mail_name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data[\"outlet_id\"] = merchant_rollout_data[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailing_list = merchant_rollout_data.set_index(\"outlet_id\").T.to_dict(\"list\")\n", "# dict(zip(merchant_rollout_data.outlet_id.astype(int), merchant_rollout_data.mail))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# merchant_rollout_data.set_index(\"outlet_id\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mail = list(merchant_rollout_data.outlet_id.astype(int).unique())\n", "# [1024, 1431, 1485]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data = dn_data_group.merge(\n", "    dn_data_t_minus_1_group, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data = dn_process_data[\n", "    [\n", "        \"outlet_id\",\n", "        \"dn_within_tat\",\n", "        \"ttl_b2b_ret_amount\",\n", "        \"ttl_dn_amt\",\n", "        \"dn_other_amount\",\n", "        \"grn_per_within_tat\",\n", "        \"grn_per_overall\",\n", "        \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount_t_minus_1\",\n", "        \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount_t_minus_2\",\n", "        \"grn_per_within_tat_t_minus_1\",\n", "        \"grn_per_overall_t_minus_1\",\n", "        \"grn_per_within_tat_t_minus_2\",\n", "        \"grn_per_overall_t_minus_2\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_raw = dn_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_raw[\"grn_pending\"] = dn_data_raw[\"amt_to_ds\"] - dn_data_raw[\"grn_overall\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting up the Mailer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import os\n", "import jinja2\n", "from jinja2 import Template"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl\n", "!pip install xlsxwriter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_to_attach = [\n", "    \"mtd_daily_data.xlsx\",\n", "    \"sla_raw_data.xlsx\",\n", "    # \"gmv_raw_data.csv\",\n", "    # \"fill_rate_raw_data.csv\",\n", "    # \"item_missing_expired_raw_data.csv\",\n", "    # \"damage_raw_data.csv\",\n", "    # \"inventory_variance_raw_data.csv\",\n", "    # \"grn_to_sto_disc_raw_data.csv\",\n", "    # \"reinventirozation_returns_raw_data.csv\",\n", "    \"checkout_to_bill_delays_raw_data.csv.gz\",\n", "    # \"APPENDIX.docx\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_summary_loss = final_summary_loss.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data[\n", "    [\n", "        \"dn_within_tat\",\n", "        \"ttl_b2b_ret_amount\",\n", "        \"ttl_dn_amt\",\n", "        \"dn_other_amount\",\n", "        \"grn_per_within_tat\",\n", "        \"grn_per_overall\",\n", "        \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount_t_minus_1\",\n", "        \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount_t_minus_2\",\n", "        \"grn_per_within_tat_t_minus_1\",\n", "        \"grn_per_overall_t_minus_1\",\n", "        \"grn_per_within_tat_t_minus_2\",\n", "        \"grn_per_overall_t_minus_2\",\n", "    ]\n", "] = dn_process_data[\n", "    [\n", "        \"dn_within_tat\",\n", "        \"ttl_b2b_ret_amount\",\n", "        \"ttl_dn_amt\",\n", "        \"dn_other_amount\",\n", "        \"grn_per_within_tat\",\n", "        \"grn_per_overall\",\n", "        \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount_t_minus_1\",\n", "        \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount_t_minus_2\",\n", "        \"grn_per_within_tat_t_minus_1\",\n", "        \"grn_per_overall_t_minus_1\",\n", "        \"grn_per_within_tat_t_minus_2\",\n", "        \"grn_per_overall_t_minus_2\",\n", "    ]\n", "].round(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data = dn_process_data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data[\n", "    [\n", "        \"dn_within_tat\",\n", "        \"ttl_b2b_ret_amount\",\n", "        \"ttl_dn_amt\",\n", "        \"dn_other_amount\",\n", "        \"grn_per_within_tat\",\n", "        \"grn_per_overall\",\n", "        \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount_t_minus_1\",\n", "        \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount_t_minus_2\",\n", "        \"grn_per_within_tat_t_minus_1\",\n", "        \"grn_per_overall_t_minus_1\",\n", "        \"grn_per_within_tat_t_minus_2\",\n", "        \"grn_per_overall_t_minus_2\",\n", "    ]\n", "] = dn_process_data[\n", "    [\n", "        \"dn_within_tat\",\n", "        \"ttl_b2b_ret_amount\",\n", "        \"ttl_dn_amt\",\n", "        \"dn_other_amount\",\n", "        \"grn_per_within_tat\",\n", "        \"grn_per_overall\",\n", "        \"dn_within_tat_t_minus_1\",\n", "        \"ttl_b2b_ret_amount_t_minus_1\",\n", "        \"ttl_dn_amt_t_minus_1\",\n", "        \"dn_other_amount_t_minus_1\",\n", "        \"dn_within_tat_t_minus_2\",\n", "        \"ttl_b2b_ret_amount_t_minus_2\",\n", "        \"ttl_dn_amt_t_minus_2\",\n", "        \"dn_other_amount_t_minus_2\",\n", "        \"grn_per_within_tat_t_minus_1\",\n", "        \"grn_per_overall_t_minus_1\",\n", "        \"grn_per_within_tat_t_minus_2\",\n", "        \"grn_per_overall_t_minus_2\",\n", "    ]\n", "].astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data[\"grn_post_tat\"] = (\n", "    dn_process_data[\"grn_per_overall\"] - dn_process_data[\"grn_per_within_tat\"]\n", ")\n", "dn_process_data[\"grn_post_tat_t_minus_1\"] = (\n", "    dn_process_data[\"grn_per_overall_t_minus_1\"]\n", "    - dn_process_data[\"grn_per_within_tat_t_minus_1\"]\n", ")\n", "dn_process_data[\"grn_post_tat_t_minus_2\"] = (\n", "    dn_process_data[\"grn_per_overall_t_minus_2\"]\n", "    - dn_process_data[\"grn_per_within_tat_t_minus_2\"]\n", ")\n", "\n", "dn_process_data[\"dn_post_tat\"] = (\n", "    dn_process_data[\"ttl_dn_amt\"] - dn_process_data[\"dn_within_tat\"]\n", ")\n", "dn_process_data[\"dn_post_tat_t_minus_1\"] = (\n", "    dn_process_data[\"ttl_dn_amt_t_minus_1\"] - dn_process_data[\"dn_within_tat_t_minus_1\"]\n", ")\n", "dn_process_data[\"dn_post_tat_t_minus_2\"] = (\n", "    dn_process_data[\"ttl_dn_amt_t_minus_2\"] - dn_process_data[\"dn_within_tat_t_minus_2\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_process_data[dn_process_data[\"dn_other_amount\"] > 0].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_process_data[dn_process_data['outlet_id']==2739].to_dict(\"records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_triggered_to = []\n", "mail_not_triggered_to = []\n", "count = 0\n", "for i in merchant_mail:\n", "    #     print(i)\n", "    try:\n", "        for f in files_to_attach:\n", "            # if f == \"APPENDIX.docx\":\n", "            # pass\n", "            #         print('running')\n", "            # elif\n", "            if os.path.exists(f):\n", "                os.remove(f)\n", "\n", "        writer = pd.ExcelWriter(\"sla_raw_data.xlsx\")\n", "\n", "        gmv_orders_data[gmv_orders_data[\"outlet_id\"] == i].to_excel(\n", "            writer, sheet_name=\"gmv\"\n", "        )\n", "        fill_rate_ds_impact_data[fill_rate_ds_impact_data[\"outlet_id\"] == i].to_excel(\n", "            writer, sheet_name=\"pna\"\n", "        )\n", "        qng_expiry_data[qng_expiry_data[\"outlet_id\"] == i].to_excel(\n", "            writer, sheet_name=\"item_missing_expired\"\n", "        )\n", "\n", "        damage_data_raw[damage_data_raw[\"outlet_id\"] == i].to_excel(\n", "            writer, sheet_name=\"damage\"\n", "        )\n", "\n", "        # inventory_variance_data[inventory_variance_data[\"outlet_id\"] == i].to_excel(\n", "        #     writer,sheet_name=\"inventory_variance\"\n", "        # )\n", "        # grn_to_sto_disc_data[grn_to_sto_disc_data[\"outlet_id\"] == i].to_excel(\n", "        # writer,sheet_name=\"grn_to_sto_disc\"\n", "        # )\n", "        reinventorization_returns_data[\n", "            reinventorization_returns_data[\"outlet_id\"] == i\n", "        ].to_excel(writer, sheet_name=\"reinventirozation_returns\")\n", "\n", "        # open_esto_data[open_esto_data[\"outlet_id\"] == i].to_excel(\n", "        #     writer, sheet_name=\"open_esto\"\n", "        # )\n", "\n", "        inventory_variance_data_positive_raw[\n", "            inventory_variance_data_positive_raw[\"outlet_id\"] == i\n", "        ].to_excel(writer, sheet_name=\"Stock Variance Positive dump\")\n", "        inventory_variance_data_negative_raw1[\n", "            inventory_variance_data_negative_raw1[\"outlet_id\"] == i\n", "        ].to_excel(writer, sheet_name=\"Stock Variance Negative dump\")\n", "        inventory_variance_data_negative_raw[\n", "            inventory_variance_data_negative_raw[\"outlet_id\"] == i\n", "        ].to_excel(writer, sheet_name=\"Stock Variance -ve Dump PNA\")\n", "\n", "        dn_data_raw[\n", "            (dn_data_raw[\"rec_outlet_id\"] == i) & (dn_data_raw[\"grn_pending\"] > 0)\n", "        ].to_excel(writer, sheet_name=\"GRN Pending\")\n", "\n", "        dn_data_raw[\n", "            (dn_data_raw[\"rec_outlet_id\"] == i) & (dn_data_raw[\"ttl_dn_amt\"] > 0)\n", "        ].to_excel(writer, sheet_name=\"Overall DN\")\n", "\n", "        dn_data_raw[\n", "            (dn_data_raw[\"rec_outlet_id\"] == i)\n", "            & ((dn_data_raw[\"within_tat\"] == 0) | (dn_data_raw[\"disc_within_tat\"] == 0))\n", "        ].to_excel(writer, sheet_name=\"TAT Adherence\")\n", "        dn_data_raw[(dn_data_raw[\"rec_outlet_id\"] == i)].to_excel(\n", "            writer, sheet_name=\"DN Raw data\"\n", "        )\n", "\n", "        writer.save()\n", "\n", "        #         gmv_orders_data[gmv_orders_data[\"outlet_id\"] == i].to_csv(\n", "        #             \"gmv_raw_data.csv\", index=False\n", "        #         )\n", "        #         fill_rate_ds_impact_data[fill_rate_ds_impact_data[\"outlet_id\"] == i].to_csv(\n", "        #             \"fill_rate_raw_data.csv\", index=False\n", "        #         )\n", "        #         qng_expiry_data[qng_expiry_data[\"outlet_id\"] == i].to_csv(\n", "        #             \"item_missing_expired_raw_data.csv\", index=False\n", "        #         )\n", "\n", "        #         damage_data_raw[damage_data_raw[\"outlet_id\"] == i].to_csv(\n", "        #             \"damage_raw_data.csv\", index=False\n", "        #         )\n", "\n", "        #         inventory_variance_data[inventory_variance_data[\"outlet_id\"] == i].to_csv(\n", "        #             \"inventory_variance_raw_data.csv\", index=False\n", "        #         )\n", "        #         grn_to_sto_disc_data[grn_to_sto_disc_data[\"outlet_id\"] == i].to_csv(\n", "        #             \"grn_to_sto_disc_raw_data.csv\", index=False\n", "        #         )\n", "        # pna_billing_data[pna_billing_data[\"outlet\"] == i].to_csv(\n", "        #     \"billing_pna_raw_data.csv\", index=False\n", "        # )\n", "        checkout_to_billed_data[checkout_to_billed_data[\"outlet_id\"] == i].to_csv(\n", "            \"checkout_to_bill_delays_raw_data.csv.gz\", index=False, compression=\"gzip\"\n", "        )\n", "        # reinventorization_returns_data[\n", "        #     reinventorization_returns_data[\"outlet_id\"] == i\n", "        # ].to_csv(\"reinventirozation_returns_raw_data.csv\", index=False)\n", "        # inbound_disc_data[inbound_disc_data[\"merchant_outlet_id\"] == i].to_csv(\n", "        #     \"sto_discrepancy_note_raw_data.csv.gz\", index=False, compression=\"gzip\"\n", "        # )\n", "\n", "        #     final_data8_mtd[final_data8_mtd['Outlet ID']==i].to_csv('merchant_mtd_date_data.csv',index=False)\n", "        writer_orig = pd.ExcelWriter(\"mtd_daily_data.xlsx\", engine=\"xlsxwriter\")\n", "        final_data8_mtd[final_data8_mtd[\"Outlet ID\"] == i].to_excel(\n", "            writer_orig, index=False, sheet_name=\"report\"\n", "        )\n", "        worksheet = writer_orig.sheets[\"report\"]\n", "        workbook = writer_orig.book\n", "        # red_format = workbook.add_format({\"bg_color\": \"#FF7F7F\"})\n", "        # green_format = workbook.add_format({\"bg_color\": \"#90EE90\"})\n", "        # worksheet.conditional_format(\n", "        #     \"F1:V31\",\n", "        #     {\n", "        #         \"type\": \"cell\",\n", "        #         \"criteria\": \"greater than\",\n", "        #         \"value\": 0,\n", "        #         \"format\": red_format,\n", "        #     },\n", "        # )\n", "        # worksheet.conditional_format(\n", "        #     \"F1:V31\",\n", "        #     {\n", "        #         \"type\": \"cell\",\n", "        #         \"criteria\": \"equal to\",\n", "        #         \"value\": 0,\n", "        #         \"format\": green_format,\n", "        #     },\n", "        # )\n", "        writer_orig.save()\n", "\n", "        files_to_attach = [\n", "            \"mtd_daily_data.xlsx\",\n", "            \"sla_raw_data.xlsx\",\n", "            # \"gmv_raw_data.csv\",\n", "            # \"fill_rate_raw_data.csv\",\n", "            # \"item_missing_expired_raw_data.csv\",\n", "            # \"damage_raw_data.csv\",\n", "            # \"inventory_variance_raw_data.csv\",\n", "            # \"grn_to_sto_disc_raw_data.csv\",\n", "            # \"reinventirozation_returns_raw_data.csv\",\n", "            \"checkout_to_bill_delays_raw_data.csv.gz\",\n", "            # \"APPENDIX.docx\",\n", "        ]\n", "\n", "        loader = jinja2.FileSystemLoader(\n", "            searchpath=\"/usr/local/airflow/dags/repo/dags/supply_chain/merchant_sla/report/merchant_sla\"\n", "            # \"/shared/Shared/<PERSON><PERSON><PERSON>/Merchant SLA Mailer V2\"\n", "            # \"/usr/local/airflow/dags/repo/dags/supply_chain/merchant_sla/report/merchant_sla\"\n", "            #             \"/home/<USER>/Development/\"\n", "            # \"/home/<USER>/Development/grofers/airflow-dags/daggers/supply_chain/merchant_sla/report/merchant_sla\"\n", "            # searchpath = \"\"\n", "            #                                      searchpath=\"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/misc/report/offline_dark_stores\"\n", "            #             searchpath=\"/home/<USER>/test/offline_dark_stores\"\n", "        )\n", "        tmpl_environ1 = jinja2.Environment(loader=loader)\n", "        tmpl_environ2 = jinja2.Environment(loader=loader)\n", "\n", "        template1 = tmpl_environ1.get_template(\"email_template_loss.html\")\n", "        template2 = tmpl_environ1.get_template(\"email_template_sla.html\")\n", "        # template3 = tmpl_environ2.get_template(\"email_template_sla1.html\")\n", "\n", "        # message_text2 = template3.render(\n", "        #     rca_details=final_summary1[merchant_dict[i]].to_dict(\"records\"),\n", "        #     Outlet_Name=merchant_dict[i],\n", "        #     Outlet_ID=i,\n", "        # )\n", "\n", "        message_text1 = template1.render(\n", "            rca_details=final_summary_loss[\n", "                final_summary_loss[\"Merchant Name\"] == merchant_dict[i]\n", "            ].to_dict(\"records\"),\n", "            dn_details=dn_process_data[dn_process_data[\"outlet_id\"] == i].to_dict(\n", "                \"records\"\n", "            ),\n", "            Outlet_Name=merchant_dict[i],\n", "            Outlet_ID=i,\n", "        )\n", "\n", "        message_text = template2.render(\n", "            rca_details=final_summary[merchant_dict[i]].to_dict(\"records\"),\n", "            Outlet_Name=merchant_dict[i],\n", "            Outlet_ID=i,\n", "        )\n", "\n", "        if (start_date.month + 1) > 12:\n", "            end_month = 1\n", "        else:\n", "            end_month = start_date.month + 1\n", "\n", "        subject = (\n", "            \"SLA Performance Report | \"\n", "            + str(merchant_mail_subject[i])\n", "            + \" | \"\n", "            + str(calendar.month_abbr[start_date.month])\n", "            + \"-\"\n", "            + str(calendar.month_abbr[end_month])\n", "        )\n", "\n", "        pb.send_email(\n", "            from_email=\"<EMAIL>\",\n", "            to_email=mailing_list[i],\n", "            # [\"<EMAIL>\"],\n", "            # mailing_list[i],\n", "            # [\"<EMAIL>\",\"<EMAIL>\"],\n", "            # mailing_list[i],\n", "            # [\"<EMAIL>\",\"<EMAIL>\"],\n", "            # ,\"<EMAIL>\",\"<EMAIL>\"],\n", "            # mailing_list[i],\n", "            subject=subject,\n", "            html_content=message_text + message_text1,\n", "            files=files_to_attach,\n", "            bcc=[\"<EMAIL>\"],\n", "            #     cc=[\"<EMAIL>\",\"<EMAIL>\"],\n", "            mime_subtype=\"mixed\",\n", "            mime_charset=\"utf-8\",\n", "        )\n", "        time.sleep(2)\n", "        mail_triggered_to.append(merchant_dict[i])\n", "    except Exception as e:\n", "        mail_not_triggered_to.append(\n", "            (i, \"\".join(tb.format_exception(None, e, e.__traceback__)))\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sending Mail Triggered Merchants To Slack"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-merchant-sla-alert\",\n", "    text=f\"\"\"Email Sent To {len(mail_triggered_to)} Merchants: {mail_triggered_to} \n", "Email Not Sent To {len(mail_not_triggered_to)} Merchants: {mail_not_triggered_to}\n", "\"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}