<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    border-radius: 5px 5px 0 0;
    width: 100%;
    max-width: 900px;
}
.tg td {
    font-family: Roboto, sans-serif;
    font-size: 14px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #333;
    background-color: #fff;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: normal;
    padding: 10px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #000;
    background-color: #FFEC33;
}
.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}
.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}  


</style>
</head>
<p>Hi,</p>
<br />
<p>Here is a summarized report of yesterday’s performance for your store outlet {{Outlet_Name}}. Alongside, we have highlighted the benchmarks for an ideal store.</p>

<p> <b>Outlet Name: </b>{{Outlet_Name}}</p>                     
<p><b>Outlet ID: </b>{{Outlet_ID}}</p>

<p><b>Please find below the Performance as per the SLAs:</b></p><br/>
<table class="tg">
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">SLA</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Target</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Yesterday</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Month-to-Date</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Remarks (Month-to-Date)</span></th>
        </tr>
        {% for row in rca_details %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['SLA'] }}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Target'] }}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Yesterday'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Month-to-Date'] }}</b>
            </td> 
            <td style="text-align:center;" class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Remarks'] }}</b>
            </td> 
    </tr>
        {% endfor %}

    </tbody>
</table>
<br/>


