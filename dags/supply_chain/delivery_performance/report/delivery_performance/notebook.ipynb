{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "import pencilbox as pb\n", "from IPython.display import HTML\n", "import datetime"]}, {"cell_type": "raw", "metadata": {}, "source": ["DP wise-Daily performance-LM v5:\n", "https://redash-queries.grofers.com/queries/188807/source#193819"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "sql = \"\"\"\n", "With \n", "LM_Sch as\n", "(\n", "    select distinct sch_slot_start_ist::date as sch_date,oms_station,order_id, category\n", "    from\n", "    (\n", "        select distinct\n", "            o.id as order_id,\n", "            case when o.type like 'InternalReverse%%' then 'internal' else 'billed' end as category,\n", "            os.id as suborder_id, \n", "            os.install_ts, \n", "            (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start))as int) * interval '1 second' + interval '5.5 hour') as sch_slot_start_ist,\n", "            (timestamp 'epoch' + cast(o.slot_end as int) * interval '1 second' + interval '5.5 hour') as sch_slot_end_ist,\n", "            (ose.install_ts + interval '5.5 hour') as event_ts_ist,\n", "            json_extract_path_text(ose.extra,'to',true) as event,\n", "            o.type as order_type,\n", "            case when o.station in ('NSD') then o.station\n", "                when o.station is null then 'NULL'\n", "                else upper(substring(o.station,8,length(o.station)-7))\n", "            end as oms_station\n", "        from lake_oms_bifrost.oms_order o\n", "        left join lake_oms_bifrost.oms_suborder os on os.order_id = o.id\n", "        left join lake_oms_bifrost.oms_suborder_event ose on os.id = ose.suborder_id\n", "        left join lake_oms_bifrost.oms_merchant m on o.merchant_id = m.id\n", "            and  m.virtual_merchant_type = 'superstore_merchant'\n", "        where \n", "        ((os.type not like 'Digital%%' \n", "            and os.type not like 'Drop%%' \n", "            and lower(m.city_name) not in ('not in service area')\n", "            and lower(m.city_name) not like '%%b2b%%'\n", "            and m.id not in (28759,28760,26680)\n", "            and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'\n", "            and json_extract_path_text(ose.extra,'to',true) = 'BILLED')\n", "            or (o.type like 'InternalReverse%%'\n", "                    and lower(m.city_name) not like '%%b2b%%'\n", "                    and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'))\n", "            and (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start)) as int) * interval '1 second' + interval '5.5 hour')::date = current_date - 1\n", "            \n", "     )\n", "    -- union\n", "    -- (\n", "    --     select distinct sch_date::date as sch_date,oms_station,o.id as order_id,'internal' as category\n", "    --     from metrics.reschedules_and_cancellations_overall s\n", "    --     inner join lake_oms_bifrost.oms_order o on s.oms_order_id = o.id\n", "    --     where s.sch_date::date = current_date - 1\n", "    --     and ((s.event = 'RESCHEDULED' and s.event_type <> 'SAME DAY'))\n", "    --     and o.type like 'InternalReverse%%'\n", "    -- )\n", "),\n", "assigned as \n", "(\n", "    select distinct\n", "    sch_date,\n", "    max_time as assigned_time,\n", "    city,\n", "    station,\n", "    fe_id,\n", "    employee_id,\n", "    name,\n", "    fe_category,\n", "    external_order_id,\n", "    order_id,\n", "    cart_id,\n", "    weight\n", "    from \n", "    (\n", "        select a.*,\n", "        Dense_Rank() over(partition by external_order_id,sch_date Order By max_time desc) as rnk\n", "        from\n", "        (\n", "            Select \n", "                sch.sch_date,\n", "                c.name as city ,\n", "                s.name as station ,\n", "                fe.id as fe_id,\n", "                fe.employee_id ,\n", "                fe.name,\n", "                (Case when \n", "                fe.employee_id like 'GSP%%' or fe.employee_id like 'VSP%%' or fe.employee_id like 'GEP%%' then 'GSP' \n", "                    when dp.id is not null then 'DP'\n", "                    else 'Grofers' \n", "                end)as fe_category,\n", "                o.id as order_id,\n", "                o.external_order_id,\n", "                o.cart_id,\n", "                max(oa.actual_event_time + interval '330 minute') as max_time,\n", "                sum(case when oi.quantity > 0 and oi.total_weight/oi.quantity > 50 then cast(oi.total_weight as decimal(18,2))/1000 \n", "                        else oi.total_weight end) as weight\n", "            from LM_sch sch\n", "            Inner join lake_last_mile.order o on sch.order_id = o.external_order_id\n", "            inner join lake_oms_bifrost.oms_order oo on oo.id = o.external_order_id\n", "            Inner join lake_last_mile.order_action oa on oa.order_id = o.id \n", "            inner join lake_last_mile.order_item oi on oi.order_id = o.id\n", "            Inner join lake_last_mile.field_executive fe on oa.field_executive_id = fe.id\n", "            left join lake_last_mile.distribution_point dp on fe.id = dp.primary_fe_id \n", "            Inner join lake_last_mile.station s on o.station_id = s.id \n", "            Inner join lake_last_mile.city c on s.city_id = c.id \n", "            Where oa.action_type = 'ASSIGNED'\n", "            and (oa.actual_event_time + interval '330 minute')::date between current_date - 2 and current_date -1\n", "            group by 1,2,3,4,5,6,7,8,9,10\n", "        )a\n", "    )a\n", "    where rnk = 1\n", "),\n", "resch_canc_after_billed as \n", "(\n", "    Select distinct\n", "        sch_date::date as sch_date,\n", "        oms_station as station,\n", "        oms_order_id as order_id,\n", "        r.cart_id,\n", "        event_ts_ist as event_time,\n", "        event,\n", "        reason,\n", "        sch_slot_start_ist as old_slot_start,\n", "        sch_slot_end_ist as old_slot_end,\n", "        case when event = 'CANCELLED' then null else new_slot_start_ist end as new_slot_start,\n", "        case when event = 'CANCELLED' then null else new_slot_end_ist end as new_slot_end,\n", "        oms_event_id,\n", "        oms_stage,\n", "        event_type\n", "    from metrics.reschedules_and_cancellations_overall r \n", "    inner join lake_oms_bifrost.oms_order o on r.oms_order_id = o.id\n", "    where ((r.event = 'RESCHEDULED' and r.event_type <> 'SAME DAY') or r.event = 'CANCELLED')\n", "    and (r.oms_stage in ('BILLED BUT PENDING ENROUTE','ENROUTE') or o.type like 'InternalReverse%%') -- resch/canc after billing\n", "    and sch_date::date = current_date-1\n", "),\n", "delivered_details as \n", "( \n", "    Select distinct\n", "        (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as sch_date,\n", "        o.external_order_id as order_id,\n", "        o.route_id,\n", "        (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour') as slot_start,\n", "        (timestamp 'epoch' + cast(oo.slot_end as int) * interval '1 second' + interval '5.5 hour') as slot_end,\n", "        (oa.actual_event_time + interval '330 minute') as delivery_ts,\n", "        fe.id as fe_id,\n", "        fe.employee_id\n", "    from lake_last_mile.order o \n", "    inner join lake_oms_bifrost.oms_order oo on oo.id = o.external_order_id\n", "    Inner join lake_last_mile.order_action oa on o.id = oa.order_id \n", "    Inner join lake_last_mile.order_item oi on o.id = oi.order_id \n", "    Inner join lake_last_mile.field_executive fe on oa.field_executive_id = fe.id\n", "    left join lake_last_mile.distribution_point dp on fe.id = dp.primary_fe_id \n", "    Where oa.action_type = 'DELIVERED'\n", "    AND  (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour')::date = current_date-1\n", "    and oo.type not like 'Drop%%' \n", "    and oo.type not like 'Digital%%'\n", "),\n", "delivery_failed as \n", "(\n", "    select distinct\n", "        sch_date,\n", "        max_time as df_time,\n", "        city,\n", "        station,\n", "        employee_id,\n", "        external_order_id\n", "    from \n", "    (\n", "        select df.*,\n", "        Dense_Rank() over(partition by external_order_id,sch_date Order By max_time desc) as rnk\n", "        from\n", "        (\n", "            Select \n", "                sch.sch_date,\n", "                c.name as city ,\n", "                s.name as station ,\n", "                oa.field_executive_id,\n", "                fe.employee_id,\n", "                o.external_order_id,\n", "                max(oa.actual_event_time + interval '330 minute') as max_time\n", "            from LM_sch sch\n", "            Inner join lake_last_mile.order o on sch.order_id = o.external_order_id\n", "            Inner join lake_last_mile.order_action oa on oa.order_id = o.id \n", "            inner join lake_last_mile.order_item oi on oi.order_id = o.id\n", "            Inner join lake_last_mile.field_executive fe on oa.field_executive_id = fe.id\n", "            left join lake_last_mile.distribution_point dp on fe.id = dp.primary_fe_id \n", "            Inner join lake_last_mile.station s on o.station_id = s.id \n", "            Inner join lake_last_mile.city c on s.city_id = c.id \n", "            Where oa.action_type = 'DELIVERY_FAILED'\n", "            and (oa.actual_event_time + interval '330 minute')::date = current_date -1\n", "            group by 1,2,3,4,5,6\n", "        )df\n", "    )df\n", "    where rnk = 1\n", "),\n", "agg as \n", "( \n", "    Select \n", "        a.sch_date,\n", "        a.city,\n", "        a.station,\n", "        dp.id as dp_id,\n", "        dp.name as dp_name,\n", "        fe.employee_id as emp_id,\n", "        a.employee_id ,\n", "        a.name as fe ,\n", "        a.fe_category as category,\n", "        a.external_order_id as assigned_orders,\n", "        dl.route_id as routes,\n", "        dl.order_id as Delivered,\n", "        (case when dl.delivery_ts between dl.slot_start and dl.slot_end then dl.order_id end) as on_time,\n", "        (case when dl.delivery_ts > dl.slot_end then dl.order_id end) as delayed,\n", "        (case when dl.delivery_ts < dl.slot_start then dl.order_id end) as before_time,\n", "        r.event,\n", "        r.event_type,\n", "        r.order_id ,\n", "        r.reason,\n", "        o.order_status as current_status,\n", "        (Case when (a.weight < 20) Then a.order_id end) as weight_1,\n", "        (Case when (a.weight >= 20 and a.weight < 25) Then a.order_id end) as weight_2,\n", "        (Case when (a.weight >= 25 and a.weight < 30) Then a.order_id end) as weight_3,\n", "        (Case when (a.weight >= 30 and a.weight < 35) Then a.order_id end) as weight_4,\n", "        (Case when (a.weight >=35) Then a.order_id end) as weight_5,\n", "        df.external_order_id as delivery_failed\n", "    from assigned a\n", "    left join resch_canc_after_billed r on a.external_order_id = r.order_id and a.sch_date = r.sch_date\n", "    left join delivered_details dl on dl.order_id = a.external_order_id and a.employee_id = dl.employee_id and dl.sch_date = a.sch_date\n", "    left join delivery_failed df on df.external_order_id = a.external_order_id and df.employee_id = a.employee_id and df.sch_date = a.sch_date\n", "    inner join lake_last_mile.order o on a.order_id = o.id\n", "    left join lake_last_mile.distribution_point dp on dp.id = o.distribution_point_id\n", "    left join lake_last_mile.field_executive fe on fe.id = dp.primary_fe_id\n", "    where a.city not ilike '%%b2b%%'\n", "    and a.city not ilike '%%test%%'\n", "    and a.city not ilike '%%not in service area%%'\n", "    and a.station not ilike '%%b2b%%'\n", "    and a.station not ilike '%%test%%'\n", "    and a.station not ilike '%%dum%%'\n", "    order by 1,2,3,4,5,6\n", ")\n", "\n", "select *\n", "from agg\n", "--where Delivered is not null\n", "\"\"\"\n", "con = pb.get_connection(\"redshift_etl\")\n", "lm_base = pd.read_sql(sql=sql, con=con)\n", "end = time.time()\n", "result = time.localtime(end - start)\n", "print(\"\\n time_min:\", result.tm_min)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_base = lm_base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    \"RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_WEATHER_CONDITIONS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_AC\",\n", "    \"RESCHEDULE_DELIVERY_OTHERS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_LM\",\n", "    \"RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES\",\n", "    \"RESCHEDULE_CUSTOMER_ADDRESS_ISSUE\",\n", "]\n", "y = [\n", "    \"RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS\",\n", "    \"RESCHEDULE_CRM DRIVEN_OTHERS\",\n", "    \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\",\n", "    \"RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM\",\n", "    \"RESCHEDULE_CRM DRIVEN_PRE_ORDER\",\n", "]\n", "dp_base.loc[dp_base[\"event\"] == \"RESCHEDULED\", \"overall_reschedules\"] = dp_base[\n", "    \"order_id\"\n", "]\n", "dp_base.loc[dp_base[\"event\"] == \"CANCELLED\", \"overall_cancellations\"] = dp_base[\n", "    \"order_id\"\n", "]\n", "dp_base.loc[\n", "    (dp_base[\"event\"] == \"RESCHEDULED\") & (dp_base[\"reason\"].isin(x)), \"lm_reschedules\"\n", "] = dp_base[\"order_id\"]\n", "dp_base.loc[\n", "    (dp_base[\"event\"] == \"RESCHEDULED\") & (dp_base[\"reason\"].isin(y)), \"crm_reschedules\"\n", "] = dp_base[\"order_id\"]\n", "dp_base.loc[\n", "    (dp_base[\"event\"] == \"RESCHEDULED\")\n", "    & (dp_base[\"reason\"] == \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\"),\n", "    \"crm_tech_res\",\n", "] = dp_base[\"order_id\"]"]}, {"cell_type": "raw", "metadata": {}, "source": ["logistics DP base"]}, {"cell_type": "raw", "metadata": {}, "source": ["Scheduled orders for current_date-1 "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "sql = \"\"\"With \n", "LM_Sch as\n", "(\n", "    select distinct city,station,sch_slot_start_ist::date as sch_date,oms_station,order_id::text, category\n", "    from\n", "    (\n", "        select distinct\n", "         m.city_name as city,\n", "            o.station,\n", "            o.id as order_id,\n", "            case when o.type like 'InternalReverse%%' then 'internal' else 'billed' end as category,\n", "            os.id as suborder_id, \n", "            os.install_ts, \n", "            (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start))as int) * interval '1 second' + interval '5.5 hour') as sch_slot_start_ist,\n", "            (timestamp 'epoch' + cast(o.slot_end as int) * interval '1 second' + interval '5.5 hour') as sch_slot_end_ist,\n", "            (ose.install_ts + interval '5.5 hour') as event_ts_ist,\n", "            json_extract_path_text(ose.extra,'to',true) as event,\n", "            o.type as order_type,\n", "            case when o.station in ('NSD') then o.station\n", "                when o.station is null then 'NULL'\n", "                else upper(substring(o.station,8,length(o.station)-7))\n", "            end as oms_station\n", "        from lake_oms_bifrost.oms_order o\n", "        left join lake_oms_bifrost.oms_suborder os on os.order_id = o.id\n", "        left join lake_oms_bifrost.oms_suborder_event ose on os.id = ose.suborder_id\n", "        left join lake_oms_bifrost.oms_merchant m on o.merchant_id = m.id\n", "            and  m.virtual_merchant_type = 'superstore_merchant'\n", "        where \n", "        \n", "         ((os.type not like 'Digital%%' \n", "            and os.type not like 'Drop%%' \n", "            and lower(m.city_name) not in ('not in service area')\n", "            and lower(m.city_name) not like '%%b2b%%'\n", "            and m.id not in (28759,28760,26680)\n", "            and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'\n", "            and json_extract_path_text(ose.extra,'to',true) = 'BILLED')\n", "            or (o.type like 'InternalReverse%%'\n", "                    and lower(m.city_name) not like '%%b2b%%'\n", "                    and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'))\n", "            and (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start)) as int) * interval '1 second' + interval '5.5 hour')::date = current_date - 1\n", "            \n", "     )\n", ")\n", "select * from LM_Sch\n", "\"\"\"\n", "con = pb.get_connection(\"redshift_etl\")\n", "df = pd.read_sql(sql=sql, con=con)\n", "print(\"LM_Sch: %s orders retrieved\" % (df.order_id.count()))\n", "end = time.time()\n", "result = time.localtime(end - start)\n", "print(\"\\n time_min:\", result.tm_min)"]}, {"cell_type": "raw", "metadata": {}, "source": ["sql = \"\"\"\n", "select distinct sch_date::date as sch_date,oms_order_id as order_id,'internal' as category\n", "        from metrics.reschedules_and_cancellations_overall s\n", "        inner join lake_oms_bifrost.oms_order o on s.oms_order_id = o.id\n", "        where s.sch_date::date = current_date -1\n", "        and ((s.event = 'RESCHEDULED' and s.event_type in ('LATER DATE','PRE-SCHEDULED')) or s.event = 'CREATED')\n", "        and o.type like 'InternalReverse%%'\n", "\"\"\"\n", "con = pb.get_connection(\"redshift\")\n", "lm_sch_res = pd.read_sql(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LM_Sch = pd.concat([df, lm_sch_res])\n", "LM_Sch = df\n", "LM_Sch.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reschedules from LM and OMS\n", "sql = \"\"\"\n", " Select distinct\n", "        sch_date::date as sch_date,\n", "        oms_station as station,\n", "        oms_order_id::text as order_id,\n", "        r.cart_id,\n", "        event_ts_ist as event_time,\n", "        event,\n", "        reason,\n", "        sch_slot_start_ist as old_slot_start,\n", "        sch_slot_end_ist as old_slot_end,\n", "        case when event = 'CANCELLED' then null else new_slot_start_ist end as new_slot_start,\n", "        case when event = 'CANCELLED' then null else new_slot_end_ist end as new_slot_end,\n", "        oms_event_id,\n", "        oms_stage,\n", "        event_type\n", "    from metrics.reschedules_and_cancellations_overall r \n", "    inner join lake_oms_bifrost.oms_order o on r.oms_order_id = o.id\n", "    where ((r.event = 'RESCHEDULED' and r.event_type <> 'SAME DAY') or r.event = 'CANCELLED')\n", "    and (r.oms_stage in ('BILLED BUT PENDING ENROUTE','ENROUTE','BEFORE PICKING','PICKED BUT NOT BILLED') or o.type like 'InternalReverse%%') -- resch/canc after billing\n", "    and sch_date::date = current_date-1\n", "    and lm_order_id=0\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "resch_canc_after_billed = pd.read_sql(sql=sql, con=con)\n", "print(\n", "    \"resch_canc_after_billed: %s orders retrieved\"\n", "    % (resch_canc_after_billed.order_id.count())\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# assigned, delivered, deliver failed\n", "\n", "\n", "sql = f\"\"\"\n", "with delivery_failed as\n", "(select \n", "    (delivery_start + interval '330 minute')::date as sch_date,\n", "    lt.shipment_label_value as order_id,\n", "    lt.user_profile_id as fe_id,\n", "    employee_id,\n", "    display_id,\n", "   max( tm.install_ts) as df_time\n", "from  lake_logistics.logistics_task lt\n", "inner join  lake_logistics.logistics_shipment_label lsl on lt.shipment_label_value=lsl.label_value\n", "inner join lake_logistics.logistics_shipment ls on lsl.shipment_id=ls.id  and label_type='ORDER_ID' and delivery_start::date=lt.install_ts::date\n", "    inner join lake_logistics.task_management_taskstatelog tm on tm.task_id=lt.id and tm.task_type='ORDER_DELIVERY_TASK' and to_state='DELIVERY_FAILED'\n", "    inner join lake_logistics.logistics_user_profile p on p.id=lt.user_profile_id\n", "    inner join lake_logistics.logistics_user  u on u.id=p.user_id\n", "    inner join lake_logistics.logistics_allocation_request lar on lar.label_value=lt.shipment_label_value and lar.state='COMPLETED' \n", "    inner join lake_logistics.logistics_task ltt on ltt.shipment_label_value=lar.allocation_batch_id \n", "        and ltt.type='EXPRESS_TRIP_TASK'and ltt.state='COMPLETED'\n", "        and eta::date=(tm.install_ts)::date\n", "    where lsl.label_value::int in {tuple(LM_Sch.order_id.unique())}\n", "    and (tm.install_ts)::date = (current_date + interval '330 minute') ::date-1\n", "    and lt.type='ORDER_DELIVERY_TASK'\n", "    group by 1,2,3,4,5),\n", "    ordertrip_map as \n", "--(\n", "--select \n", "--lsl.label_value as order_id , \n", "--lslt.label_value as trip_id,\n", "--(split_part(lslt.label_value,'_',2))::int as user_id,\n", "--rank() over(partition by lsl.label_value Order By lslt.install_ts desc) as rnk\n", "--from lake_logistics.logistics_shipment ls \n", "--     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "--     --inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "--     inner join lake_logistics.logistics_shipment_label lslt on lslt.shipment_id=ls.id  and lslt.label_type='TRIP_ID'\n", "--where delivery_start::date = (current_date + interval '330 minute') ::date-1),\n", "\n", "(\n", "select \n", "lsl.label_value as order_id , \n", "lslt.label_value as trip_id,\n", "user_id::int as user_id,\n", "rank() over(partition by lsl.label_value Order By lslt.install_ts desc) as rnk\n", "from lake_logistics.logistics_shipment ls \n", "     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "     --inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "     inner join lake_logistics.logistics_shipment_label lslt on lslt.shipment_id=ls.id  and lslt.label_type='TRIP_ID'\n", "     inner join lake_logistics.logistics_task lt on  lt.shipment_label_value=lslt.label_value \n", "     and lt.type in ('EXPRESS_TRIP_TASK','PRE_PROCURED_TRIP_TASK')\n", "     inner join lake_logistics.logistics_user_profile lup on lup.id=lt.user_profile_id\n", "where delivery_start::date = (current_date + interval '330 minute') ::date-1),\n", "\n", "assigned as \n", "(select \n", "    (json_extract_path_text(json_extract_path_text(ls.metadata,'merchant'),'city_id'))::int as city_id,\n", "    (json_extract_path_text(json_extract_path_text(ls.metadata,'station'),'name')) as station,\n", "    lsl.label_value as order_id , \n", "    lslc.label_value as cart_id,\n", "    t.trip_id,\n", "    t.user_id,\n", "    (case when employee_id like 'GSP%%' or employee_id like 'VSP%%' or employee_id like 'GEP%%' then 'GSP' \n", "                    else 'Grofers' \n", "                end)as fe_category,\n", "                employee_id,\n", "                name,\n", "    --user_profile_id,\n", "    --(split_part(lslt.label_value,'_',2))::int as user_id,\n", "    (delivery_start + interval '330 minute' )::date as sch_date,\n", "   -- (slog.install_ts + interval '330 minute') as assigned_time,\n", "   ls.weight/1000 as weight\n", "    from lake_logistics.logistics_shipment ls \n", "     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "     inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "     inner join ordertrip_map t on t.order_id=lsl.label_value and t.rnk=1\n", "     left join lake_logistics.logistics_user_profile up on up.user_id=t.user_id\n", "     left join lake_logistics.logistics_user u on u.id=up.user_id\n", "    -- inner join lake_logistics.shipments_shipmentstatelog slog on slog.shipment_id=ls.id and to_state='ALLOCATION_COMPLETE'\n", "     --inner join lake_logistics.logistics_task lt on lt.shipment_label_value=lslt.label_value\n", "     where delivery_start::date = (current_date + interval '330 minute') ::date-1) ,\n", "     delivered_details as\n", "(\n", "  \n", "    select lsl.label_value  as order_id,\n", "        (delivery_start + interval '330 minute')::date as sch_date,\n", "        (delivery_start  + interval '330 minute') as slot_start,\n", "        (delivery_end  + interval '330 minute') as slot_end,\n", "        (slog.install_ts + interval '330 minute') as delivery_ts,\n", "        lt.user_profile_id as fe_id,\n", "        employee_id\n", "     from lake_logistics.logistics_shipment ls \n", "    inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and label_type='ORDER_ID' and status='DELIVERED'\n", "    left join lake_logistics.shipments_shipmentstatelog slog on slog.shipment_id=ls.id and to_state='DELIVERED'\n", "    left join  lake_logistics.logistics_task lt on lt.shipment_label_value=lsl.label_value and lt.type='ORDER_DELIVERY_TASK' and state='COMPLETED'\n", "    inner join lake_logistics.logistics_user_profile p on p.id=lt.user_profile_id\n", "    inner join lake_logistics.logistics_user  u on u.id=p.user_id\n", "    where lsl.label_value::int in {tuple(LM_Sch.order_id.unique())}\n", "    and delivery_start::date = (current_date + interval '330 minute') ::date-1\n", "    and slog.install_ts::date=(current_date + interval '330 minute') ::date-1\n", "),\n", "base as\n", "(select\n", "    a.city_id,\n", "    a.station,\n", "    a.sch_date,\n", "    a.employee_id,\n", "    a.fe_category as category,\n", "    a.name as fe,\n", "    a.order_id  ,\n", "    a.trip_id as trips,\n", "    d.order_id as Delivered,\n", "    df.order_id as delivery_failed,\n", "    (case when d.delivery_ts between d.slot_start and d.slot_end then d.order_id end) as on_time,\n", "    (case when d.delivery_ts > d.slot_end then d.order_id end) as delayed,\n", "    (case when d.delivery_ts < d.slot_start then d.order_id end) as before_time,\n", "    (Case when (a.weight < 20) Then a.order_id end) as weight_1,\n", "    (Case when (a.weight >= 20 and a.weight < 25) Then a.order_id end) as weight_2,\n", "    (Case when (a.weight >= 25 and a.weight < 30) Then a.order_id end) as weight_3,\n", "    (Case when (a.weight >= 30 and a.weight < 35) Then a.order_id end) as weight_4,\n", "    (Case when (a.weight >=35) Then a.order_id end) as weight_5\n", "from assigned  a \n", "left join delivered_details d on d.order_id=a.order_id and d.sch_date=a.sch_date \n", "--and d.employee_id=a.employee_id\n", "left join delivery_failed df on df.order_id=a.order_id and df.sch_date=a.sch_date and df.sch_date=a.sch_date \n", "--and df.employee_id=a.employee_id \n", ")\n", "select * from base\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "logistic_base = pd.read_sql(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# join reschedules with logistics data\n", "resch_canc_after_billed = resch_canc_after_billed.drop(columns=\"station\")\n", "base = pd.merge(\n", "    logistic_base, resch_canc_after_billed, on=[\"order_id\", \"sch_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    \"RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_WEATHER_CONDITIONS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_AC\",\n", "    \"RESCHEDULE_DELIVERY_OTHERS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_LM\",\n", "    \"RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES\",\n", "    \"RESCHEDULE_CUSTOMER_ADDRESS_ISSUE\",\n", "]\n", "y = [\n", "    \"RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS\",\n", "    \"RESCHEDULE_CRM DRIVEN_OTHERS\",\n", "    \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\",\n", "    \"RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM\",\n", "    \"RESCHEDULE_CRM DRIVEN_PRE_ORDER\",\n", "]\n", "base.loc[base[\"event\"] == \"RESCHEDULED\", \"overall_reschedules\"] = base[\"order_id\"]\n", "base.loc[base[\"event\"] == \"CANCELLED\", \"overall_cancellations\"] = base[\"order_id\"]\n", "base.loc[\n", "    (base[\"event\"] == \"RESCHEDULED\") & (base[\"reason\"].isin(x)), \"lm_reschedules\"\n", "] = base[\"order_id\"]\n", "base.loc[\n", "    (base[\"event\"] == \"RESCHEDULED\") & (base[\"reason\"].isin(y)), \"crm_reschedules\"\n", "] = base[\"order_id\"]\n", "base.loc[\n", "    (base[\"event\"] == \"RESCHEDULED\")\n", "    & (base[\"reason\"] == \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\"),\n", "    \"crm_tech_res\",\n", "] = base[\"order_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# City name from OMS\n", "sql = \"\"\"select external_id from lake_logistics.logistics_node\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "node = pd.read_sql(sql=sql, con=con)\n", "node.loc[len(node.index)] = [\n", "    1000\n", "]  # adding 1000 to avoid error while creating tuple dude to presence of single node\n", "city = base.city_id\n", "city.loc[\n", "    len(city.index)\n", "] = 1000  # adding 1000 to avoid error while creating tuple dude to presence of single city_id\n", "sql = f\"\"\"select distinct city_id,city_name as city from lake_oms_bifrost.view_oms_merchant \n", "where city_id in {tuple(city.unique())}\n", "and external_id in {tuple(node.external_id.unique())}\n", "and city_name is not null\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "city_name = pd.read_sql(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_c = pd.merge(base, city_name, on=\"city_id\", how=\"inner\")\n", "base_c = base_c.drop(\n", "    columns=[\"city_id\"]\n", ")  # drop columns not required in final dataframe\n", "base_c.rename(columns={\"trips\": \"routes\", \"order_id\": \"assigned\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logistics_base = base_c.drop(\n", "    columns=[\n", "        \"cart_id\",\n", "        \"event_time\",\n", "        \"old_slot_start\",\n", "        \"old_slot_end\",\n", "        \"new_slot_start\",\n", "        \"new_slot_end\",\n", "        \"oms_event_id\",\n", "        \"oms_stage\",\n", "        \"event_type\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set dp_name,dp_id and dp_emp_id ' ' for orders from logistics\n", "logistics_base[\"station\"] = logistics_base[\"station\"].str[7:]\n", "logistics_base[\"station\"] = logistics_base[\"station\"].str.upper()\n", "logistics_base[\"dp_id\"] = \" \"\n", "logistics_base[\"dp_name\"] = \" \"\n", "logistics_base[\"dp_emp_id\"] = \" \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_base.rename(\n", "    columns={\n", "        \"emp_id\": \"dp_emp_id\",\n", "        \"assigned_orders\": \"assigned\",\n", "        \"fe_category\": \"category\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm_logistics_base = pd.concat([dp_base, logistics_base])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm_logistics_base.loc[lm_logistics_base[\"dp_id\"].isnull() == True, \"dp_id\"] = \" \"\n", "lm_logistics_base.loc[lm_logistics_base[\"dp_name\"].isnull() == True, \"dp_name\"] = \" \"\n", "lm_logistics_base.loc[\n", "    lm_logistics_base[\"dp_emp_id\"].isnull() == True, \"dp_emp_id\"\n", "] = \" \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = lm_logistics_base.groupby(\n", "    [\"city\", \"station\", \"dp_id\", \"dp_name\", \"dp_emp_id\", \"sch_date\"]\n", ").nunique()[\n", "    [\n", "        \"assigned\",\n", "        \"delivered\",\n", "        \"on_time\",\n", "        \"delayed\",\n", "        \"before_time\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"routes\",\n", "        \"weight_1\",\n", "        \"weight_2\",\n", "        \"weight_3\",\n", "        \"weight_4\",\n", "        \"weight_5\",\n", "        \"delivery_failed\",\n", "        \"crm_tech_res\",\n", "    ]\n", "]\n", "final.reset_index(level=[0, 1, 2, 3, 4, 5], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final[\n", "    [\n", "        \"city\",\n", "        \"station\",\n", "        \"dp_id\",\n", "        \"dp_name\",\n", "        \"dp_emp_id\",\n", "        \"assigned\",\n", "        \"delivered\",\n", "        \"on_time\",\n", "        \"delayed\",\n", "        \"before_time\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"routes\",\n", "        \"weight_1\",\n", "        \"weight_2\",\n", "        \"weight_3\",\n", "        \"weight_4\",\n", "        \"weight_5\",\n", "        \"delivery_failed\",\n", "        \"crm_tech_res\",\n", "        \"sch_date\",\n", "    ]\n", "]\n", "\n", "Previous_Date = datetime.datetime.today() - datetime.timedelta(days=1)\n", "day = Previous_Date.strftime(\"%d-%m-%Y\")\n", "\n", "final_df.to_csv(f\"DP wise raw data-{day}.csv\", encoding=\"utf8\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_df,\n", "    \"1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc\",\n", "    \"DP wise raw data.\",\n", "    service_account=\"priority_account\",\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["Fe wise-Daily performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fe_final = lm_logistics_base.groupby(\n", "    [\"city\", \"station\", \"sch_date\", \"employee_id\", \"category\", \"fe\"]\n", ").nunique()[\n", "    [\n", "        \"assigned\",\n", "        \"delivered\",\n", "        \"on_time\",\n", "        \"delayed\",\n", "        \"before_time\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"routes\",\n", "        \"weight_1\",\n", "        \"weight_2\",\n", "        \"weight_3\",\n", "        \"weight_4\",\n", "        \"weight_5\",\n", "        \"delivery_failed\",\n", "        \"crm_tech_res\",\n", "    ]\n", "]\n", "fe_final.reset_index(level=[0, 1, 2, 3, 4, 5], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fe_final_df = fe_final[\n", "    [\n", "        \"city\",\n", "        \"station\",\n", "        \"employee_id\",\n", "        \"fe\",\n", "        \"category\",\n", "        \"assigned\",\n", "        \"delivered\",\n", "        \"on_time\",\n", "        \"delayed\",\n", "        \"before_time\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"routes\",\n", "        \"weight_1\",\n", "        \"weight_2\",\n", "        \"weight_3\",\n", "        \"weight_4\",\n", "        \"weight_5\",\n", "        \"delivery_failed\",\n", "        \"crm_tech_res\",\n", "    ]\n", "]\n", "fe_final_df.to_csv(f\"fe wise raw data-{day}.csv\", encoding=\"utf8\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    fe_final_df,\n", "    \"1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc\",\n", "    \"fe wise raw data.\",\n", "    service_account=\"priority_account\",\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["Station Data "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "sql = f\"\"\"\n", "With \n", "LM_Sch as\n", "(\n", "    select distinct sch_slot_start_ist::date as sch_date,oms_station,order_id, category\n", "    from\n", "    (\n", "        select distinct\n", "            o.id as order_id,\n", "            case when o.type like 'InternalReverse%%' then 'internal' else 'billed' end as category,\n", "            os.id as suborder_id, \n", "            os.install_ts, \n", "            (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start))as int) * interval '1 second' + interval '5.5 hour') as sch_slot_start_ist,\n", "            (timestamp 'epoch' + cast(o.slot_end as int) * interval '1 second' + interval '5.5 hour') as sch_slot_end_ist,\n", "            (ose.install_ts + interval '5.5 hour') as event_ts_ist,\n", "            json_extract_path_text(ose.extra,'to',true) as event,\n", "            o.type as order_type,\n", "            case when o.station in ('NSD') then o.station\n", "                when o.station is null then 'NULL'\n", "                else upper(substring(o.station,8,length(o.station)-7))\n", "            end as oms_station\n", "        from lake_oms_bifrost.oms_order o\n", "        left join lake_oms_bifrost.oms_suborder os on os.order_id = o.id\n", "        left join lake_oms_bifrost.oms_suborder_event ose on os.id = ose.suborder_id\n", "        left join lake_oms_bifrost.oms_merchant m on o.merchant_id = m.id\n", "            and  m.virtual_merchant_type = 'superstore_merchant'\n", "        where \n", "        \n", "            ((os.type not like 'Digital%%' \n", "            and os.type not like 'Drop%%' \n", "            and lower(m.city_name) not in ('not in service area')\n", "            and lower(m.city_name) not like '%%b2b%%'\n", "            and m.id not in (28759,28760,26680)\n", "            and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'\n", "            and json_extract_path_text(ose.extra,'to',true) = 'BILLED')\n", "            or (o.type like 'InternalReverse%%'\n", "                    and lower(m.city_name) not like '%%b2b%%'\n", "                    and lower(o.station) not like 'dum%%'\n", "            and lower(o.station) not like '%%test%%'\n", "            and lower(o.station) not like '%%b2b%%'\n", "            and lower(o.station) not like '%%nsd%%'))\n", "            and (timestamp 'epoch' + cast((coalesce(os.slot_start,o.slot_start)) as int) * interval '1 second' + interval '5.5 hour')::date = current_date - 1\n", "            \n", "     )\n", "    -- union\n", "    -- (\n", "    --     select distinct sch_date::date as sch_date,oms_station,o.id as order_id,'internal' as category\n", "    --     from metrics.reschedules_and_cancellations_overall s\n", "    --     inner join lake_oms_bifrost.oms_order o on s.oms_order_id = o.id\n", "    --     where s.sch_date::date = current_date - 1\n", "    --     and ((s.event = 'RESCHEDULED' and s.event_type <> 'SAME DAY'))\n", "    --     and o.type like 'InternalReverse%%'\n", "    -- )\n", "),\n", "resch_canc_after_billed as \n", "(\n", "    Select distinct\n", "        sch_date::date as sch_date,\n", "        oms_station as station,\n", "        oms_order_id as order_id,\n", "        r.cart_id,\n", "        event_ts_ist as event_time,\n", "        event,\n", "        reason,\n", "        sch_slot_start_ist as old_slot_start,\n", "        sch_slot_end_ist as old_slot_end,\n", "        case when event = 'CANCELLED' then null else new_slot_start_ist end as new_slot_start,\n", "        case when event = 'CANCELLED' then null else new_slot_end_ist end as new_slot_end,\n", "        oms_event_id,\n", "        oms_stage,\n", "        event_type\n", "    from metrics.reschedules_and_cancellations_overall r \n", "    inner join lake_oms_bifrost.oms_order o on r.oms_order_id = o.id\n", "    where ((r.event = 'RESCHEDULED' and r.event_type <> 'SAME DAY') or r.event = 'CANCELLED')\n", "    and (r.oms_stage in ('BILLED BUT PENDING ENROUTE','ENROUTE') or o.type like 'InternalReverse%%') -- resch/canc after billing\n", "    and sch_date::date = current_date-1\n", "),\n", "delivered_details as \n", "( \n", "    Select distinct\n", "        (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as sch_date,\n", "        o.external_order_id as order_id,\n", "        o.route_id,\n", "        (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour') as slot_start,\n", "        (timestamp 'epoch' + cast(oo.slot_end as int) * interval '1 second' + interval '5.5 hour') as slot_end,\n", "        (oa.actual_event_time + interval '330 minute') as delivery_ts,\n", "        fe.id as fe_id,\n", "        fe.employee_id\n", "    from lake_last_mile.order o \n", "    inner join lake_oms_bifrost.oms_order oo on oo.id = o.external_order_id\n", "    Inner Join lake_last_mile.order_action oa on o.id = oa.order_id \n", "    Inner Join lake_last_mile.order_item oi on o.id = oi.order_id \n", "    Inner Join lake_last_mile.field_executive fe on oa.field_executive_id = fe.id\n", "    left join lake_last_mile.distribution_point dp on fe.id = dp.primary_fe_id \n", "    Where oa.action_type = 'DELIVERED'\n", "    AND  (oa.actual_event_time + interval '330 minute')::date = current_date-1\n", ")\n", "select l.sch_date as sch_date,\n", "        c.name as city,\n", "        s.name as station,\n", "        l.category,\n", "        l.order_id::text as l_order_id,\n", "       r.event ,\n", "       r.event_type ,\n", "       r.order_id,\n", "       r.reason,\n", "       o.order_status as current_status,\n", "       (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as slot_start,\n", "         (d.order_id) as delivered_orders\n", "from LM_Sch l\n", "inner join lake_last_mile.order o on l.order_id = o.external_order_id\n", "inner join lake_oms_bifrost.oms_order oo on oo.id = o.external_order_id\n", "inner join lake_last_mile.station s on o.station_id = s.id\n", "inner join lake_last_mile.city c on c.id = s.city_id\n", "left join resch_canc_after_billed r on l.sch_date = r.sch_date and l.order_id = r.order_id\n", "left join delivered_details d on l.sch_date = d.sch_date and l.order_id = d.order_id\n", "order by 1,2,3\n", "\"\"\"\n", "con = pb.get_connection(\"redshift_etl\")\n", "station_base = pd.read_sql(sql=sql, con=con)\n", "end = time.time()\n", "result = time.localtime(end - start)\n", "print(\"\\n time_min:\", result.tm_min)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    \"RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_WEATHER_CONDITIONS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_AC\",\n", "    \"RESCHEDULE_DELIVERY_OTHERS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_LM\",\n", "    \"RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES\",\n", "    \"RESCHEDULE_CUSTOMER_ADDRESS_ISSUE\",\n", "]\n", "y = [\n", "    \"RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS\",\n", "    \"RESCHEDULE_CRM DRIVEN_OTHERS\",\n", "    \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\",\n", "    \"RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM\",\n", "    \"RESCHEDULE_CRM DRIVEN_PRE_ORDER\",\n", "]\n", "station_base.loc[\n", "    (station_base[\"event\"] == \"RESCHEDULED\")\n", "    & (station_base[\"event_type\"] == \"LATER DATE\"),\n", "    \"overall_reschedules\",\n", "] = station_base[\"order_id\"]\n", "station_base.loc[\n", "    station_base[\"event\"] == \"CANCELLED\", \"overall_cancellations\"\n", "] = station_base[\"order_id\"]\n", "station_base.loc[\n", "    (station_base[\"event\"] == \"RESCHEDULED\")\n", "    & (station_base[\"event_type\"] == \"LATER DATE\")\n", "    & (station_base[\"reason\"].isin(x)),\n", "    \"lm_reschedules\",\n", "] = station_base[\"order_id\"]\n", "station_base.loc[\n", "    (station_base[\"event\"] == \"RESCHEDULED\")\n", "    & (station_base[\"event_type\"] == \"LATER DATE\")\n", "    & (station_base[\"reason\"].isin(y)),\n", "    \"crm_reschedules\",\n", "] = station_base[\"order_id\"]\n", "station_base.loc[\n", "    (station_base[\"event\"] == \"RESCHEDULED\")\n", "    & (station_base[\"event_type\"] == \"PRE-SCHEDULED DATE\"),\n", "    \"pre_sch_orders\",\n", "] = station_base[\"order_id\"]\n", "station_base.loc[station_base[\"category\"] == \"billed\", \"billed_orders\"] = station_base[\n", "    \"l_order_id\"\n", "]\n", "station_base.loc[\n", "    (station_base[\"event\"] == \"RESCHEDULED\")\n", "    & (station_base[\"event_type\"] == \"LATER DATE\")\n", "    & (station_base[\"current_status\"] == \"CANCELLED\")\n", "    & (station_base[\"sch_date\"] == station_base[\"slot_start\"]),\n", "    \"resch_canc_orders\",\n", "] = station_base[\"order_id\"]\n", "station_base.loc[\n", "    station_base[\"category\"] == \"internal\", \"internalreverseorders\"\n", "] = station_base[\"l_order_id\"]\n", "# station_base['lm_scheduled']=station_base['billed_orders']+station_base['internalreverseorders']"]}, {"cell_type": "raw", "metadata": {}, "source": ["(datetime.datetime.fromtimestamp(station_base[\"slot_start\"]).strftime('%Y-%m-%d'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = f\"\"\" with base as\n", "(select distinct  lsl.label_value as order_id,\n", "(json_extract_path_text(json_extract_path_text(ls.metadata,'merchant'),'city_id'))::int as city_id,\n", "    (json_extract_path_text(json_extract_path_text(ls.metadata,'station'),'name')) as station,\n", "     (delivery_start + interval '330 minute' )::date as sch_date\n", "from lake_logistics.logistics_shipment ls \n", "     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "     where lsl.label_value::int in {tuple(LM_Sch.order_id.unique())}\n", "     and  delivery_start::date = (current_date + interval '330 minute') ::date-1\n", "),\n", "ordertrip_map as \n", "(\n", "--select \n", "--lsl.label_value as order_id , \n", "--lslt.label_value as trip_id,\n", "--(split_part(lslt.label_value,'_',2))::int as user_id,\n", "--rank() over(partition by lsl.label_value Order By lslt.install_ts desc) as rnk\n", "--from lake_logistics.logistics_shipment ls \n", "--   inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", " --    --inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "--     inner join lake_logistics.logistics_shipment_label lslt on lslt.shipment_id=ls.id  and lslt.label_type='TRIP_ID'\n", "--where delivery_start::date = (current_date + interval '330 minute') ::date-1\n", "\n", "select \n", "lsl.label_value as order_id , \n", "lslt.label_value as trip_id,\n", "user_id::int as user_id,\n", "rank() over(partition by lsl.label_value Order By lslt.install_ts desc) as rnk\n", "from lake_logistics.logistics_shipment ls \n", "     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "     --inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "     inner join lake_logistics.logistics_shipment_label lslt on lslt.shipment_id=ls.id  and lslt.label_type='TRIP_ID'\n", "     inner join lake_logistics.logistics_task lt on  lt.shipment_label_value=lslt.label_value \n", "     and lt.type in ('EXPRESS_TRIP_TASK','PRE_PROCURED_TRIP_TASK')\n", "     inner join lake_logistics.logistics_user_profile lup on lup.id=lt.user_profile_id\n", "where delivery_start::date = (current_date + interval '330 minute') ::date-1\n", "\n", "\n", "\n", "),\n", "assigned as \n", "(select \n", "    (json_extract_path_text(json_extract_path_text(ls.metadata,'merchant'),'city_id'))::int as city_id,\n", "    (json_extract_path_text(json_extract_path_text(ls.metadata,'station'),'name')) as station,\n", "    lsl.label_value as order_id , \n", "    lslc.label_value as cart_id,\n", "    t.trip_id,\n", "    t.user_id,\n", "    (case when employee_id like 'GSP%%' or employee_id like 'VSP%%' or employee_id like 'GEP%%' then 'GSP' \n", "                    else 'Grofers' \n", "                end)as fe_category,\n", "                employee_id,\n", "                name,\n", "    --user_profile_id,\n", "    --(split_part(lslt.label_value,'_',2))::int as user_id,\n", "    (delivery_start + interval '330 minute' )::date as sch_date,\n", "   -- (slog.install_ts + interval '330 minute') as assigned_time,\n", "   ls.weight/1000 as weight\n", "    from lake_logistics.logistics_shipment ls \n", "     inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and lsl.label_type='ORDER_ID'\n", "     inner join lake_logistics.logistics_shipment_label lslc on lslc.shipment_id=ls.id  and lslc.label_type='CART_ID'\n", "     inner join ordertrip_map t on t.order_id=lsl.label_value and t.rnk=1\n", "          left join lake_logistics.logistics_user_profile up on up.user_id=t.user_id\n", "     left join lake_logistics.logistics_user u on u.id=up.user_id\n", "    -- inner join lake_logistics.shipments_shipmentstatelog slog on slog.shipment_id=ls.id and to_state='ALLOCATION_COMPLETE'\n", "     --inner join lake_logistics.logistics_task lt on lt.shipment_label_value=lslt.label_value\n", "     where delivery_start::date = (current_date + interval '330 minute') ::date-1),\n", "   delivered as   \n", "( \n", "   select (json_extract_path_text(json_extract_path_text(ls.metadata,'merchant'),'city_id'))::int as city_id,\n", "        (json_extract_path_text(json_extract_path_text(ls.metadata,'station'),'name')) as station,\n", "   lsl.label_value  as order_id,\n", "        (delivery_start + interval '330 minute')::date as sch_date,\n", "        (delivery_start  + interval '330 minute') as slot_start,\n", "        (delivery_end  + interval '330 minute') as slot_end,\n", "        (slog.install_ts + interval '330 minute') as delivery_ts,\n", "        lt.user_profile_id as fe_id,\n", "        employee_id\n", "     from lake_logistics.logistics_shipment ls \n", "    inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and label_type='ORDER_ID' and status='DELIVERED'\n", "    left join lake_logistics.shipments_shipmentstatelog slog on slog.shipment_id=ls.id and to_state='DELIVERED'\n", "    left join  lake_logistics.logistics_task lt on lt.shipment_label_value=lsl.label_value and lt.type='ORDER_DELIVERY_TASK' and state='COMPLETED'\n", "    inner join lake_logistics.logistics_user_profile p on p.id=lt.user_profile_id\n", "    inner join lake_logistics.logistics_user  u on u.id=p.user_id\n", "    where lsl.label_value::int in {tuple(LM_Sch.order_id.unique())}\n", "    and delivery_start::date = (current_date + interval '330 minute') ::date-1\n", "    and slog.install_ts::date=(current_date + interval '330 minute') ::date-1 \n", "    \n", "    \n", "    )\n", "    select b.order_id ,\n", "    a.order_id as assigned,\n", "    b.city_id,\n", "    b.station,\n", "    d.order_id as delivered_o,\n", "    b.sch_date,\n", "    d.slot_start,\n", "    d.slot_end,\n", "    d.delivery_ts,\n", "    d.fe_id,\n", "    d.employee_id\n", "    from base b \n", "    left join assigned a on  b.order_id=a.order_id\n", "    left join delivered d on d.order_id=a.order_id\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "delivered_details = pd.read_sql(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"select distinct id::text as order_id,\n", "current_status,\n", "(timestamp 'epoch' + cast(slot_start as int) * interval '1 second' + interval '5.5 hour') as  slot_start\n", "from lake_oms_bifrost.oms_order\n", "where id in {tuple(LM_Sch.order_id.unique())}\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "oms_order = pd.read_sql(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Sch_status = pd.merge(LM_Sch, oms_order, on=[\"order_id\"], how=\"inner\")\n", "lm_sch_resc = pd.merge(\n", "    LM_Sch_status, resch_canc_after_billed, on=[\"order_id\", \"sch_date\"], how=\"left\"\n", ")\n", "lm_sch_billed = pd.merge(\n", "    lm_sch_resc, delivered_details, on=[\"order_id\", \"sch_date\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    \"RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_WEATHER_CONDITIONS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_AC\",\n", "    \"RESCHEDULE_DELIVERY_OTHERS\",\n", "    \"RESCHEDULE_MERCHANT_MISSED_BY_LM\",\n", "    \"RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY\",\n", "    \"RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES\",\n", "    \"RESCHEDULE_CUSTOMER_ADDRESS_ISSUE\",\n", "]\n", "y = [\n", "    \"RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS\",\n", "    \"RESCHEDULE_CRM DRIVEN_OTHERS\",\n", "    \"RESCHEDULE_CRM DRIVEN_TECH_ISSUE\",\n", "    \"RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM\",\n", "    \"RESCHEDULE_CRM DRIVEN_PRE_ORDER\",\n", "]\n", "lm_sch_billed.loc[\n", "    (lm_sch_billed[\"event\"] == \"RESCHEDULED\")\n", "    & (lm_sch_billed[\"event_type\"] == \"LATER DATE\"),\n", "    \"overall_reschedules\",\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    lm_sch_billed[\"event\"] == \"CANCELLED\", \"overall_cancellations\"\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    (lm_sch_billed[\"event\"] == \"RESCHEDULED\")\n", "    & (lm_sch_billed[\"event_type\"] == \"LATER DATE\")\n", "    & (lm_sch_billed[\"reason\"].isin(x)),\n", "    \"lm_reschedules\",\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    (lm_sch_billed[\"event\"] == \"RESCHEDULED\")\n", "    & (lm_sch_billed[\"event_type\"] == \"LATER DATE\")\n", "    & (lm_sch_billed[\"reason\"].isin(y)),\n", "    \"crm_reschedules\",\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    (lm_sch_billed[\"event\"] == \"RESCHEDULED\")\n", "    & (lm_sch_billed[\"event_type\"] == \"PRE-SCHEDULED DATE\"),\n", "    \"pre_sch_orders\",\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    lm_sch_billed[\"category\"] == \"billed\", \"billed_orders\"\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    (lm_sch_billed[\"event\"] == \"RESCHEDULED\")\n", "    & (lm_sch_billed[\"event_type\"] == \"LATER DATE\")\n", "    & (lm_sch_billed[\"current_status\"] == \"CANCELLED\"),\n", "    \"resch_canc_orders\",\n", "] = lm_sch_billed[\"order_id\"]\n", "lm_sch_billed.loc[\n", "    lm_sch_billed[\"category\"] == \"internal\", \"internalreverseorders\"\n", "] = lm_sch_billed[\"order_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm_sch_billed.rename(\n", "    columns={\n", "        \"delivered_o\": \"delivered_orders\",\n", "        \"station_x\": \"station\",\n", "        \"slot_start_x\": \"slot_start\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm_sch_billed.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lm_sch_billed[\"station\"] = lm_sch_billed[\"station\"].str[7:]\n", "lm_sch_billed[\"station\"] = lm_sch_billed[\"station\"].str.upper()\n", "logistic_sch_billed = lm_sch_billed[\n", "    [\n", "        \"sch_date\",\n", "        \"city\",\n", "        \"station\",\n", "        \"category\",\n", "        \"event\",\n", "        \"event_type\",\n", "        \"order_id\",\n", "        \"reason\",\n", "        \"current_status\",\n", "        \"slot_start\",\n", "        \"delivered_orders\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"pre_sch_orders\",\n", "        \"billed_orders\",\n", "        \"resch_canc_orders\",\n", "        \"internalreverseorders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_station = pd.concat([logistic_sch_billed, station_base])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = final_station.groupby([\"sch_date\", \"city\", \"station\"]).nunique()[\n", "    [\n", "        \"billed_orders\",\n", "        \"internalreverseorders\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"resch_canc_orders\",\n", "        \"pre_sch_orders\",\n", "        \"delivered_orders\",\n", "    ]\n", "]\n", "x.reset_index(level=[0, 1, 2], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[\"lm_scheduled\"] = x[\"billed_orders\"] + x[\"internalreverseorders\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_df = x[\n", "    [\n", "        \"sch_date\",\n", "        \"city\",\n", "        \"station\",\n", "        \"billed_orders\",\n", "        \"internalreverseorders\",\n", "        \"lm_scheduled\",\n", "        \"overall_reschedules\",\n", "        \"overall_cancellations\",\n", "        \"lm_reschedules\",\n", "        \"crm_reschedules\",\n", "        \"resch_canc_orders\",\n", "        \"pre_sch_orders\",\n", "        \"delivered_orders\",\n", "    ]\n", "]\n", "x_df.to_csv(\n", "    f\"Station- LM scheduled+rescheduled+cancelled-{day}.csv\",\n", "    encoding=\"utf8\",\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    x_df,\n", "    \"1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc\",\n", "    \"Station- LM scheduled+rescheduled+cancelled.\",\n", "    service_account=\"priority_account\",\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["E-Mail "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Previous_Date = datetime.datetime.today() - datetime.timedelta(days=1)\n", "day = Previous_Date.strftime(\"%d-%m-%Y\")\n", "sheet_id = \"1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc\"\n", "sheet_name = \"DC overall.\"\n", "df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Delay %\"] = df[\"Delay %\"].astype(\"float\")\n", "df[\"Before Time%\"] = df[\"Before Time%\"].astype(\"float\")\n", "df[\"%on time\\n(on+before time)\"] = df[\"%on time\\n(on+before time)\"].astype(\"float\")\n", "df[\"LM reschedules\"] = df[\"LM reschedules\"].astype(\"float\")\n", "df[\"Overall Reschedules\"] = df[\"Overall Reschedules\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# conditional formatting\n", "significant = (\n", "    lambda x: '<div class=\"significant\">%s</div>' % \"{:,.2%}\".format(x)\n", "    if x >= 0.40\n", "    else str(\"{:,.2%}\".format(x))\n", ")\n", "significant_1 = (\n", "    lambda x: '<div class=\"significant_1\">%s</div>' % \"{:,.2%}\".format(x)\n", "    if x <= 0.95\n", "    else str(\"{:,.2%}\".format(x))\n", ")\n", "significant_2 = (\n", "    lambda x: '<div class=\"significant_2\">%s</div>' % \"{:,.2%}\".format(x)\n", "    if x >= 0.02\n", "    else str(\"{:,.2%}\".format(x))\n", ")\n", "significant_3 = (\n", "    lambda x: '<div class=\"significant_2\">%s</div>' % \"{:,.2%}\".format(x)\n", "    if x >= 0.03\n", "    else str(\"{:,.2%}\".format(x))\n", ")\n", "significant_4 = (\n", "    lambda x: '<div class=\"significant_2\">%s</div>' % \"{:,.2%}\".format(x)\n", "    if x >= 0.03\n", "    else str(\"{:,.2%}\".format(x))\n", ")\n", "y = df.to_html(\n", "    formatters={\n", "        \"Before Time%\": significant,\n", "        \"%on time\\n(on+before time)\": significant_1,\n", "        \"LM reschedules\": significant_2,\n", "        \"Delay %\": significant_3,\n", "        \"Overall Reschedules\": significant_4,\n", "    },\n", "    escape=False,\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# with open(\"css_style.css\", \"r\") as myfile:\n", "#     style = myfile.read()\n", "\n", "import os\n", "\n", "# cwd = os.getcwd()\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"css_style.css\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    style = myfile.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc\"\n", "sheet_name = \"mailing list\"\n", "mail_list = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mail_list.email_id.tolist()\n", "# to_email='<EMAIL>'\n", "subject = f\"Delivery Performance v3 - {day}\"\n", "html_content = f\"\"\"<html><head><p> Hi All,<br>Please find the updated performance report below which is refreshed daily at T-1 basis.<br>\n", "    The link to the sheet is <a href=\"https://docs.google.com/spreadsheets/d/1D24fRuRyzfGqU9tATpEvnFWWQiuSVPxuZE1lG43hzUc/edit#gid=1757055560\">here</a><br>\n", "    Find the link to the query with order level dump and manual filters <a href=\"https://redash-queries.grofers.com/queries/196582/\">here</a><br>\n", "    Also, please refer to the <a href=\"https://docs.google.com/spreadsheets/d/11VrVdpjmvk21Tc3nhm9L4eRD5tj71v_Ja-mzArYpp_w/edit#gid=0\">sheet</a> for all reschedules and cancellations data<br>\n", "    For any concerns/discrepancies related to data: Please raise a freshdesk ticket using the below link:<br>\n", "    https://lmopssupport.freshdesk.com<br>\n", "    {style}</head><div>{y}</div></html>\"\"\"\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[\n", "        f\"DP wise raw data-{day}.csv\",\n", "        f\"fe wise raw data-{day}.csv\",\n", "        f\"Station- LM scheduled+rescheduled+cancelled-{day}.csv\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:root] *", "language": "python", "name": "conda-root-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}