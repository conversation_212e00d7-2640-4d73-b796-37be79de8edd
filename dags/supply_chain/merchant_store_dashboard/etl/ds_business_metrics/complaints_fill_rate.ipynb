{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["date_filter = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = pd.to_datetime(date_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with\n", "\n", "abusive_list as (\n", "    select  \n", "        distinct id as customer_id \n", "    from \n", "        lake_grofers_db.view_gr_user\n", "    where \n", "        blocked=true\n", "        \n", "    union\n", "    \n", "    select  \n", "        distinct customer_id \n", "    from \n", "        lake_crm.crm_abuse_user\n", "),\n", "\n", "-- suborder_base as (\n", "--         SELECT DISTINCT\n", "--             ID,\n", "--             ORDER_ID,\n", "--             BACKEND_MERCHANT_ID\n", "--         from lake_oms_bifrost.oms_suborder \n", "--             as os\n", "        \n", "--         WHERE SLOT_START BETWEEN EXTRACT(EPOCH FROM (('{date_filter}'::DATE-3) - INTERVAL '5.5 HOUR')) \n", "--             AND EXTRACT(EPOCH FROM ('{date_filter}'::DATE + INTERVAL '1 DAY' - INTERVAL '5.5 HOUR'))\n", "--         AND os.install_ts between '{date_filter}'::DATE - INTERVAL '10 DAY' and '{date_filter}'::DATE\n", "--         AND TYPE = 'RetailSuborder'\n", "--         AND CURRENT_STATUS IN ('AT_DELIVERY_CENTER')\n", "-- ),\n", "\n", "delivered as (    \n", "    select distinct\n", "        (timestamp 'epoch' + cast(o.slot_start as int) * INTERVAL '1 second' + interval '5.5 hour')::date as sch_date,\n", "        dwbm.pos_outlet_city_name as city,\n", "        case \n", "            when (json_extract_path_text(o.slot_properties,'slot_handover_time',true)) <> ''\n", "            then (timestamp 'epoch' + (json_extract_path_text(o.slot_properties,'slot_handover_time',true))::int * INTERVAL '1 second' + interval '5.5 hour')\n", "        end as slot_handover_time ,\n", "        o.id as order_id,\n", "        o.cart_id,\n", "        \n", "        lp.billing_timestamp+ interval '5.5 hour' as billed_time,\n", "        \n", "        -- os.id as suborder_id,\n", "        \n", "        dwbm.frontend_merchant_id as frontend_merchant_id,\n", "        dwbm.frontend_merchant_name as frontend_merchant_name,\n", "        dwbm.backend_merchant_id,\n", "        ooi.product_id\n", "        --GRP.id as product_id\n", "        \n", "    from lake_oms_bifrost.oms_order o \n", "    \n", "    inner join lake_oms_bifrost.oms_order_item AS OOI\n", "            ON O.ID = OOI.order_ID \n", "    ---LEFT join lake_cms.gr_product AS GRP\n", "           ---- ON OOI.PRODUCT_ID = GRP.ID\n", "            \n", "    -- inner join \n", "    --     suborder_base \n", "    --         as  <PERSON> \n", "    --         ON OS.ORDER_ID = O.ID \n", "    \n", "    inner join \n", "        lake_oms_bifrost.view_oms_merchant \n", "            as m \n", "            on o.merchant_id = m.id\n", "    -- inner join \n", "    --     lake_logistics.logistics_node \n", "    --     as ln\n", "    --     on ln.external_id=m.external_id\n", "    --     and ln.id != 35\n", "        \n", "    inner join \n", "        lake_logistics.view_logistics_dark_store_projection \n", "            as lp \n", "            on lp.order_id=o.id and m.external_id = lp.dark_store_id\n", "    inner join dwh.dim_merchant_outlet_facility_mapping dwbm on m.external_id = dwbm.frontend_merchant_id\n", "    where\n", "    valid_to_utc >= '{date_filter}'\n", "            and is_current = True\n", "            and is_mapping_enabled = True and merchant_business_type_id=7 and\n", "    (o.SLOT_START BETWEEN EXTRACT(EPOCH FROM (('{date_filter}'::DATE-1) - INTERVAL '5.5 HOUR')) \n", "        AND EXTRACT(EPOCH FROM (('{date_filter}'::DATE) + INTERVAL '1 DAY' - INTERVAL '5.5 HOUR')))\n", "    AND date(o.install_ts) between '{date_filter}'::DATE - INTERVAL '1 DAY' and '{date_filter}'::DATE+1\n", "    AND date(m.install_ts) between '{date_filter}'::DATE - INTERVAL '1 DAY' and '{date_filter}'::DATE+1\n", "    AND (date(ooi.install_ts) between '{date_filter}'::DATE - INTERVAL '1 DAY' and '{date_filter}'::DATE+1)\n", "    and date(lp.install_ts) between '{date_filter}'::DATE - INTERVAL '1 DAY' and '{date_filter}'::DATE+1\n", "    and o.current_status = 'DELIVERED' \n", "    -- and m.virtual_merchant_type = 'superstore_merchant'\n", "    and o.type not like 'Digital%%'\n", "    and o.type not like 'Drop%%'\n", "    and o.direction = 'FORWARD'\n", ")\n", "\n", "-- null_category_duplicates_base as(\n", "--     select distinct \n", "--         cart_id,\n", "--         product_id,\n", "--         delivery_slot,\n", "--         complaint_category\n", "    \n", "--     from metrics.complaints \n", "--     where delivery_date >= ('{date_filter}'::DATE - INTERVAL '3 DAYS' + INTERVAL '5.5hrs') \n", "--         AND delivery_date <= ('{date_filter}'::DATE + INTERVAL '1 DAY' + INTERVAL '5.5hrs') \n", "--     and complaint_category in ('','Null') limit 10\n", "\n", "-- ),\n", "\n", "-- null_category_duplicates as (\n", "--     select \n", "--         distinct \n", "--         a.cart_id,\n", "--         a.product_id,\n", "--         a.delivery_slot,\n", "--         b.complaint_category as valid_complaint_type \n", "--     from \n", "--         null_category_duplicates_base \n", "--             as a\n", "--     join \n", "--         null_category_duplicates_base \n", "--             as  b\n", "--             on a.cart_id=b.cart_id \n", "--             and a.product_id=b.product_id  \n", "--             and a.delivery_slot=b.delivery_slot\n", "-- )\n", "\n", "select \n", "    d.sch_date as delivered_date_ist,\n", "    d.city,\n", "    d.backend_merchant_id,\n", "    d.frontend_merchant_name,\n", "    d.frontend_merchant_id,\n", "    \n", "    count(distinct d.order_id) as count_delivered_orders_non_abusive,\n", "    \n", "    count( DISTINCT \n", "        CASE\n", "            WHEN complaint_type = 'VALID'\n", "                AND lower(order_type) ILIKE 'retail%%' \n", "            THEN c.cart_id\n", "        END\n", "    ) AS all_complaints,\n", "              \n", "            -------------Item Missing-------------\n", "    count (  DISTINCT  \n", "        case \n", "            when C.COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') \n", "                        and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') \n", "                    )\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%extra%%item%%')\n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%')\n", "                )\n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%')\n", "                    )\n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%') \n", "                    and <PERSON><PERSON>COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  \n", "                    and <PERSON><PERSON>COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','')\n", "                )\n", "                or (\n", "                    (\n", "                        C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                        or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%')\n", "                    )\n", "                    and <PERSON>.COMPLAINT_SUB_CATEGORY in ('Null','','Item Missing','Combo Issue','Combo Issues','item_missing','Product Missing','Not_available'))\n", "            then c.cart_id \n", "        end\n", "    )  as count_Item_Missing,\n", "    \n", "    --------QNG----------------\n", "    count (\n", "        DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%quality%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%') \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%')   \n", "                )\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%smell%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%taste%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%foreign%%matter%%') \n", "            then    \n", "                c.cart_id \n", "            end\n", "    )  as count_qng,\n", "    \n", "    \n", "    ----------_Delay In delivery--------------------\n", "    count (\n", "        DISTINCT  \n", "        case \n", "            when   \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%')\n", "            then \n", "            c.cart_id \n", "        end\n", "    )  as count_delay_in_delivery,\n", "    \n", "    count ( \n", "        DISTINCT  \n", "        case \n", "            when ( \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%') \n", "                and billed_time>slot_handover_time) \n", "            then c.cart_id \n", "        end\n", "    )  as count_delay_in_delivery_with_handover\n", "    \n", "from delivered d\n", "LEFT JOIN \n", "    metrics.complaints \n", "    as c \n", "    on d.order_id = c.order_id \n", "    and d.cart_id = c.cart_id \n", "    and d.product_id=c.product_id\n", "    and (delivery_date >= ('{date_filter}'::DATE - INTERVAL '3 DAYS' + INTERVAL '5.5hrs') \n", "        AND delivery_date <= ('{date_filter}'::DATE + INTERVAL '1 DAY' + INTERVAL '5.5hrs'))\n", "LEFT JOIN \n", "    abusive_list \n", "    as al \n", "    ON c.customer_id=al.customer_id\n", "-- LEFT JOIN \n", "--     null_category_duplicates \n", "--     AS cd \n", "--     on  c.cart_id=cd.cart_id \n", "--     and c.product_id=cd.product_id  \n", "--     and c.delivery_slot=cd.delivery_slot\n", "    -- and (c.delivery_date >= ('{date_filter}'::DATE - INTERVAL '3 DAYS' + INTERVAL '5.5hrs') \n", "    --     AND c.delivery_date <= ('{date_filter}'::DATE + INTERVAL '1 DAY' + INTERVAL '5.5hrs'))\n", "    \n", "WHERE al.customer_id IS NULL\n", "group by 1,2,3,4,5\n", "\n", "\"\"\".format(\n", "    date_filter=date_filter\n", ")\n", "\n", "complaints = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["complaints.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select \n", "    dwbm.backend_merchant_id,\n", "    lp.dark_store_id as frontend_merchant_id,\n", "    lp.current_delivery_end::date as delivered_date_ist,\n", "    count(distinct lp.order_id) count_delivered_orders,\n", "    count(DISTINCT (case when ds.reason_key <> '' then ds.order_id else null end)) as count_unfulfilled_orders,\n", "    count(distinct case when json_extract_path_text ( lp.tags,'fresh',true )<>'true' then lp.order_id end ) as count_grocery_orders,\n", "    count(DISTINCT (case when json_extract_path_text ( lp.tags,'fresh',true )<>'true'  and ds.reason_key <> '' then ds.order_id else null end)) as count_unfulfilled_grocery_orders\n", "  \n", "    \n", "from lake_logistics.view_logistics_dark_store_projection\n", "        as lp\n", "left join \n", "metrics.fillrateattribution_darkstores \n", "        as ds\n", "        on lp.order_id=ds.order_id\n", "        and ds.slot_end=lp.current_delivery_end +interval '5.5hrs'\n", "         \n", "inner join dwh.dim_merchant_outlet_facility_mapping dwbm on lp.dark_store_id = dwbm.frontend_merchant_id\n", "inner join \n", "    lake_oms_bifrost.oms_order\n", "        as oo\n", "        on oo.id=lp.order_id\n", "        and oo.type not like 'Digital%%'\n", "        and oo.type not like 'Drop%%'\n", "        and oo.direction = 'FORWARD' \n", "WHERE \n", "    valid_to_utc >= current_date and current_state='DELIVERED'\n", "            and is_current = True\n", "            and is_mapping_enabled = True and merchant_business_type_id=7 and\n", "    lp.current_delivery_end >= '{date_filter}'::DATE - interval '1 days' + interval '5.5 hours'\n", "    and lp.current_delivery_end <= '{date_filter}'::DATE + interval '1 day' + interval '5.5 hours' \n", "    AND oo.install_ts >= '{date_filter}'::DATE - INTERVAL '1 DAY' \n", "    AND lp.install_ts >= '{date_filter}'::DATE - INTERVAL '1 DAY' \n", "group by 1,2,3\n", "\"\"\".format(\n", "    date_filter=date_filter\n", ")\n", "Fill_rate = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fill_rate[\"frontend_merchant_id\"] = Fill_rate[\"frontend_merchant_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pd.merge(\n", "    Fill_rate,\n", "    complaints,\n", "    how=\"inner\",\n", "    on=[\"delivered_date_ist\", \"frontend_merchant_id\", \"backend_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"frontend_merchant_id\",\n", "        \"delivered_date_ist\",\n", "        \"count_delivered_orders\",\n", "        \"count_unfulfilled_orders\",\n", "        \"count_grocery_orders\",\n", "        \"count_unfulfilled_grocery_orders\",\n", "        \"city\",\n", "        \"frontend_merchant_name\",\n", "        \"count_delivered_orders_non_abusive\",\n", "        \"all_complaints\",\n", "        \"count_item_missing\",\n", "        \"count_qng\",\n", "        \"count_delay_in_delivery\",\n", "        \"count_delay_in_delivery_with_handover\",\n", "        \"backend_merchant_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"marts_merchant_app\",\n", "    \"table_name\": \"darkstore_complaints_fill_rate\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"frontend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"frontend_merchant_id of the store\",\n", "        },\n", "        {\n", "            \"name\": \"delivered_date_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Date when orders were delivered\",\n", "        },\n", "        {\n", "            \"name\": \"count_delivered_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"count of delivered orders \",\n", "        },\n", "        {\n", "            \"name\": \"count_unfulfilled_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"count of unfulfilled orders\",\n", "        },\n", "        {\n", "            \"name\": \"count_grocery_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"count of delivered non fnv orders \",\n", "        },\n", "        {\n", "            \"name\": \"count_unfulfilled_grocery_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"count of unfulfilled non fnv orders\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"City name of the forntend merchant\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_name\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"Frontend merchant name or darkstore name in logistics\",\n", "        },\n", "        {\n", "            \"name\": \"count_delivered_orders_non_abusive\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of non abusive customer orders that were delivered\",\n", "        },\n", "        {\n", "            \"name\": \"all_complaints\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of all orders with complaints\",\n", "        },\n", "        {\n", "            \"name\": \"count_item_missing\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of item missing complaints\",\n", "        },\n", "        {\n", "            \"name\": \"count_qng\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of quality not good complaints\",\n", "        },\n", "        {\n", "            \"name\": \"count_delay_in_delivery\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of orders with delay in delivery complaints\",\n", "        },\n", "        {\n", "            \"name\": \"count_delay_in_delivery_with_handover\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Count of orders with delay in delivery complints where billed time grater    than handover time \",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"backend_merchant_id of the store\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"delivered_date_ist\", \"frontend_merchant_id\"],\n", "    \"sortkey\": [\"delivered_date_ist\"],\n", "    \"incremental_key\": \"delivered_date_ist\",\n", "    \"table_description\": \"Table contains dark stores business and operational metrics for merchant store dashboard\",\n", "    \"load_type\": \"upsert\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}