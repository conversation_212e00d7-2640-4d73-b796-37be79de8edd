{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["date_filter = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = pd.to_datetime(date_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with lm_base as \n", "(select distinct scheduled_slot,\n", "backend_merchant_id,frontend_merchant_id,\n", "frontend_merchant_name,\n", "order_id\n", "from \n", " (select distinct \n", "        dwbm.backend_merchant_id,\n", "        m.external_id as frontend_merchant_id,\n", "        dwbm.frontend_merchant_name,\n", "        o.id as order_id,\n", "    \n", "        (timestamp 'epoch' + o.slot_start * interval '1 second'+interval '5.5 hrs ') as scheduled_slot\n", "      \n", "        from lake_oms_bifrost.oms_order \n", "            as o\n", "        inner join lake_oms_bifrost.view_oms_merchant \n", "            as m \n", "            on o.merchant_id = m.id \n", "        inner join dwh.dim_merchant_outlet_facility_mapping dwbm on m.external_id = dwbm.frontend_merchant_id\n", "        \n", "    \n", "        \n", "        where \n", "            o.type in ('RetailForwardOrder','InternalForwardOrder') and\n", "             valid_to_utc >= current_date\n", "            and dwbm.is_current in (True,False)\n", "            and dwbm.is_mapping_enabled = True\n", "            and dwbm.is_frontend_merchant_active in (True,False)\n", "            and merchant_business_type_id in (7)\n", "            and o.slot_start >= extract(epoch from ('{start_date}'::DATE - interval '1 days' - interval '5.5 hour')) \n", "            and o.slot_start <= extract(epoch from ('{end_date}'::DATE + interval '1 day'- interval '5.5 hour'))\n", "            AND o.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "            AND m.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "            and current_status= 'DELIVERED'\n", "            \n", "            ) as a \n", "            ),\n", "            \n", "        \n", "\n", "total_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=1 then procurement_price*procurement_quantity end) as forward_sales\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        and pos.invoice_type_id in (1) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5) as a\n", "    group by 1),\n", " \n", "\n", "\n", "\n", "\n", "return_gmv as\n", "(\n", "select order_id, sum(return_gmv) as return_gmv\n", "from\n", "(select a.order_id::int, a.product_id,\n", "sum((procured_quantity-accepted_qty)*price) as return_gmv,\n", "sum((procured_quantity-accepted_qty)) as return_qty\n", "from lake_oms_bifrost.oms_order_item oi \n", "inner join \n", "(select distinct lsl.label_value as order_id, \n", "json_extract_path_text(lsi.product_metadata,'product_id',true) as product_id,\n", "(lsic.accepted_item_quantity) as accepted_qty,\n", "((lsi.value::int)/100) as price\n", "from \n", "lake_logistics.logistics_shipment_label lsl \n", "inner join lake_logistics.logistics_shipment_item lsi on lsl.shipment_id=lsi.shipment_id \n", "inner join lake_logistics.logistics_shipment_item_complaint lsic on lsi.id = lsic.shipment_item_id\n", "where label_type='ORDER_ID' and reason not in ('Item Missing') \n", "and date(lsl.install_ts)>'{start_date}'::date-interval '1 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '1 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '1 day') a\n", "on oi.product_id=a.product_id and oi.order_id = a.order_id\n", "where product_title not ilike '%%Smart Bachat Club Membership%%' and (date(oi.install_ts)>'{start_date}'::date-interval '1 day')\n", "group by 1,2\n", "\n", "union \n", "\n", "\n", "select sub.order_id::int,o.product_id,\n", "sum((procured_quantity-delivered_quantity)*price_per_unit) as return_gmv,\n", "sum((procured_quantity-delivered_quantity)) as return_qty\n", "from lake_last_mile.order_item o\n", "inner join lake_oms_bifrost.oms_suborder sub on sub.id = o.sub_order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = o.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where missing_reason is not null\n", "    and missing_reason not in ('MISSIGN') and sub.current_status not in ('REPROCURED','CANCELLED') and\n", "    (date(sub.install_ts)> '{start_date}'::DATE - INTERVAL '1 day') and\n", "    (date(updated_at)> '{start_date}'::DATE - INTERVAL '1 day')\n", "    group by 1,2\n", ") group by 1\n", "),\n", "\n", "\n", "refund_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=2 then procurement_price*procurement_quantity end) as refund_gmv\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        and pos.invoice_type_id in (2) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5) as a\n", "    group by 1),\n", "\n", "\n", "\n", "\n", "\n", "rpu_gmv as\n", "( \n", "select order_id, sum(rpu_gmv) as rpu_gmv\n", "from\n", "\n", "(select oo.original_order_id as order_id, oi.product_id,\n", "oi.selling_price*oi.quantity as rpu_gmv,\n", "oi.quantity\n", "from lake_oms_bifrost.oms_order o \n", "inner join lake_oms_bifrost.oms_internal_order_mapping oo on oo.internal_order_id = o.id\n", "inner join lm_base lm on lm.order_id = oo.original_order_id\n", "inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = o.id\n", "where o.type = 'InternalReverseOrder' and o.current_status = 'DELIVERED'\n", "and date(o.install_ts)>='{start_date}'::DATE - INTERVAL '1 day' \n", "and date(oo.install_ts)>='{start_date}'::DATE - INTERVAL '1 day' \n", "group by 1,2,3,4\n", ") as a\n", "group by 1\n", ")\n", "\n", "\n", "select \n", "to_char(scheduled_slot,'YYYY-MM-DD HH24:00:00') as scheduled_hour,\n", "backend_merchant_id,\n", "frontend_merchant_id,\n", "frontend_merchant_name,\n", "count(distinct a.order_id) as count_delivered_orders,\n", "sum(coalesce(forward_sales,0))-sum(coalesce(return_gmv,0)+coalesce(rpu_gmv,0)+coalesce(refund_gmv,0)) as total_delivered_gmv\n", "-- sum(actual_sales) as total_delivered_gmv\n", "\n", "from lm_base a \n", "left join total_gmv b on a.order_id=b.order_id\n", "left join return_gmv c on a.order_id=c.order_id\n", "left join rpu_gmv d on a.order_id=d.order_id\n", "left join refund_gmv ref on a.order_id = ref.order_id\n", "group by 1,2,3,4 \n", "\n", "\"\"\".format(\n", "    start_date=date_filter, end_date=date_filter\n", ")\n", "\n", "gmv_hourly = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_hourly.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_hourly = gmv_hourly[\n", "    [\n", "        \"scheduled_hour\",\n", "        \"frontend_merchant_id\",\n", "        \"frontend_merchant_name\",\n", "        \"count_delivered_orders\",\n", "        \"total_delivered_gmv\",\n", "        \"backend_merchant_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"marts_merchant_app\",\n", "    \"table_name\": \"darkstore_gmv_hourly\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"scheduled_hour\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Hour the order was scheduled for\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"ID of the frontend merchant\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_name\",\n", "            \"type\": \"varchar(200)\",\n", "            \"description\": \"Name of the frontend merchant\",\n", "        },\n", "        {\n", "            \"name\": \"count_delivered_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"count of delivered orders \",\n", "        },\n", "        {\n", "            \"name\": \"total_delivered_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Sum of actual sales amount\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"backend_merchant_id of the store\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"scheduled_hour\", \"frontend_merchant_id\"],\n", "    \"sortkey\": [\"scheduled_hour\"],\n", "    \"incremental_key\": \"scheduled_hour\",\n", "    \"table_description\": \"Table contains GMV delivered hourly in dark stores\",\n", "    \"load_type\": \"upsert\",\n", "}\n", "pb.to_redshift(gmv_hourly, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}