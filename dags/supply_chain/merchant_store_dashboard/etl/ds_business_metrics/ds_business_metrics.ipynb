{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["date_filter = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = pd.to_datetime(date_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "\n", "\n", "with lm_base as \n", "(select distinct date((sch_slot_start)) as sch_date_ist,city,\n", "backend_merchant_id,frontend_merchant_id,\n", "frontend_merchant_name,\n", "order_id,\n", "current_status\n", "from \n", " (select distinct \n", "        dwbm.pos_outlet_city_name as city,\n", "        dwbm.backend_merchant_id,\n", "        m.external_id as frontend_merchant_id,\n", "        dwbm.frontend_merchant_name,\n", "        o.id as order_id,\n", "    \n", "        date(timestamp 'epoch' + o.slot_start * interval '1 second'+interval '5.5 hrs ') as sch_slot_start,\n", "        o.current_status,\n", "        dwbm.pos_outlet_id as outlet_id,\n", "        dwbm.pos_outlet_name as outlet_name\n", "      \n", "        from lake_oms_bifrost.oms_order \n", "            as o\n", "        inner join lake_oms_bifrost.view_oms_merchant \n", "            as m \n", "            on o.merchant_id = m.id \n", "        inner join dwh.dim_merchant_outlet_facility_mapping dwbm on m.external_id = dwbm.frontend_merchant_id\n", "        \n", "    \n", "        \n", "        where \n", "            o.type in ('RetailForwardOrder','InternalForwardOrder') and\n", "             valid_to_utc >= current_date\n", "            and dwbm.is_current in (True)\n", "            and dwbm.is_mapping_enabled = True\n", "            and merchant_business_type_id in (7)\n", "            and o.slot_start >= extract(epoch from ('{start_date}'::DATE - interval '1 days' - interval '5.5 hour')) \n", "            and o.slot_start <= extract(epoch from ('{end_date}'::DATE + interval '1 day'- interval '5.5 hour'))\n", "            AND o.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "            AND m.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "            \n", "            ) as a \n", "            ),\n", "            \n", "        \n", "\n", "total_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=1 then procurement_price*procurement_quantity end) as forward_sales,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as fnv_forward_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as non_fnv_forward_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_quantity end) as fnv_forward_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_quantity end) as non_fnv_forward_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        and pos.invoice_type_id in (1) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6) as a\n", "    group by 1),\n", " \n", "\n", "\n", "\n", "\n", "return_gmv as\n", "(\n", "select order_id, sum(case when fnv_flag='FnV' then return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when fnv_flag='Non-FnV' then return_gmv else 0 end) as non_fnv_return_gmv,\n", "sum(case when fnv_flag='FnV' then return_qty else 0 end) as fnv_return_qty,\n", "sum(case when fnv_flag='Non-FnV' then return_qty else 0 end) as non_fnv_return_qty\n", "from\n", "(select a.order_id::int, a.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-accepted_qty)*price) as return_gmv,\n", "sum((procured_quantity-accepted_qty)) as return_qty\n", "from lake_oms_bifrost.oms_order_item oi \n", "inner join \n", "(select distinct lsl.label_value as order_id, \n", "json_extract_path_text(lsi.product_metadata,'product_id',true) as product_id,\n", "(lsic.accepted_item_quantity) as accepted_qty,\n", "((lsi.value::int)/100) as price\n", "from \n", "lake_logistics.logistics_shipment_label lsl \n", "inner join lake_logistics.logistics_shipment_item lsi on lsl.shipment_id=lsi.shipment_id \n", "inner join lake_logistics.logistics_shipment_item_complaint lsic on lsi.id = lsic.shipment_item_id\n", "where label_type='ORDER_ID' and reason not in ('Item Missing') \n", "and date(lsl.install_ts)>'{start_date}'::date-interval '1 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '1 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '1 day') a\n", "on oi.product_id=a.product_id and oi.order_id = a.order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = a.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where product_title not ilike '%%Smart Bachat Club Membership%%' and (date(oi.install_ts)>'{start_date}'::date-interval '1 day')\n", "group by 1,2,3\n", "\n", "union \n", "\n", "\n", "select sub.order_id::int,o.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-delivered_quantity)*price_per_unit) as return_gmv,\n", "sum((procured_quantity-delivered_quantity)) as return_qty\n", "from lake_last_mile.order_item o\n", "inner join lake_oms_bifrost.oms_suborder sub on sub.id = o.sub_order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = o.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where missing_reason is not null\n", "    and missing_reason not in ('MISSIGN') and sub.current_status not in ('REPROCURED','CANCELLED') and\n", "    (date(sub.install_ts)> '{start_date}'::DATE - INTERVAL '1 day') and\n", "    (date(updated_at)> '{start_date}'::DATE - INTERVAL '1 day')\n", "    group by 1,2,3\n", ") group by 1\n", "),\n", "\n", "\n", "refund_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=2 then procurement_price*procurement_quantity end) as refund_amount,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as fnv_reverse_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as non_fnv_reverse_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_quantity end) as fnv_reverse_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_quantity end) as non_fnv_reverse_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '1 day' \n", "        and pos.invoice_type_id in (2) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6) as a\n", "    group by 1),\n", "\n", "\n", "\n", "\n", "\n", "rpu_gmv as\n", "( \n", "select order_id, sum(case when fnv_flag='FnV' then rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when fnv_flag='Non-FnV' then rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "sum(case when fnv_flag='FnV' then quantity else 0 end) as fnv_rpu_qty,\n", "sum(case when fnv_flag='Non-FnV' then quantity else 0 end) as non_fnv_rpu_qty\n", "from\n", "\n", "(select oo.original_order_id as order_id, oi.product_id,\n", "case when cat.parent_category_id in (1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "oi.selling_price*oi.quantity as rpu_gmv,\n", "oi.quantity\n", "from lake_oms_bifrost.oms_order o \n", "inner join lake_oms_bifrost.oms_internal_order_mapping oo on oo.internal_order_id = o.id\n", "inner join lm_base lm on lm.order_id = oo.original_order_id\n", "inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = o.id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = oi.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where o.type = 'InternalReverseOrder' and o.current_status = 'DELIVERED'\n", "and date(o.install_ts)>='{start_date}'::DATE - INTERVAL '1 day' \n", "and date(oo.install_ts)>='{start_date}'::DATE - INTERVAL '1 day' \n", "group by 1,2,3,4,5\n", ") as a\n", "group by 1\n", ")\n", "\n", "\n", "select sch_date_ist,\n", "city,\n", "backend_merchant_id,\n", "frontend_merchant_id,\n", "frontend_merchant_name,\n", "sum(total_delivered_orders) as total_delivered_orders,\n", "sum(total_sch_orders) as total_sch_orders,\n", "sum(total_cancelled_orders) as total_cancelled_orders,\n", "sum(total_delivery_failed) as total_delivery_failed,\n", "sum(coalesce(fnv_forward_sales,0))-sum(coalesce(fnv_return_gmv,0)+coalesce(fnv_rpu_gmv,0)+coalesce(fnv_refund_gmv,0)) as total_fnv_gmv,\n", "sum(coalesce(non_fnv_forward_sales,0))-sum(coalesce(non_fnv_return_gmv,0)+coalesce(non_fnv_rpu_gmv,0)+coalesce(non_fnv_refund_gmv,0)) as total_non_fnv_gmv,\n", "\n", "sum(coalesce(forward_sales,0))-sum(coalesce(fnv_return_gmv,0)+coalesce(non_fnv_return_gmv,0)\n", "+coalesce(fnv_rpu_gmv,0)+coalesce(non_fnv_rpu_gmv,0)+coalesce(fnv_refund_gmv,0)+coalesce(non_fnv_refund_gmv,0)) as total_delivered_gmv,\n", "\n", "sum(coalesce(fnv_forward_item_count,0)+coalesce(non_fnv_forward_item_count,0))-sum(coalesce(fnv_return_qty,0)+coalesce(non_fnv_return_qty,0)\n", "+coalesce(fnv_rpu_qty,0)+coalesce(non_fnv_rpu_qty,0)+coalesce(fnv_refund_qty,0)+coalesce(non_fnv_refund_qty,0)) as total_items\n", "from \n", "\n", "(\n", "select \n", "sch_date_ist,\n", "city,\n", "backend_merchant_id, \n", "frontend_merchant_id,\n", "frontend_merchant_name,\n", "count(distinct a.order_id) as total_sch_orders,\n", "count(distinct case when current_status='DELIVERED' then a.order_id end) as total_delivered_orders,\n", "count(distinct case when current_status='CANCELLED' then a.order_id end) as total_cancelled_orders,\n", "count(distinct case when current_status='DELIVERY_FAILED' then a.order_id end) as total_delivery_failed,\n", "sum(case when current_status='DELIVERED' then forward_sales else 0 end) as forward_sales,\n", "sum(case when current_status='DELIVERED' then fnv_forward_sales else 0 end) as fnv_forward_sales,\n", "sum(case when current_status='DELIVERED' then non_fnv_forward_sales else 0 end) as non_fnv_forward_sales,\n", "sum(case when current_status='DELIVERED' then fnv_forward_item_count else 0 end) as fnv_forward_item_count,\n", "sum(case when current_status='DELIVERED' then non_fnv_forward_item_count else 0 end) as non_fnv_forward_item_count,\n", "sum(case when current_status='DELIVERED' then fnv_return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_return_gmv else 0 end) as non_fnv_return_gmv,\n", "sum(case when current_status='DELIVERED' then fnv_return_qty else 0 end) as fnv_return_qty,\n", "sum(case when current_status='DELIVERED' then non_fnv_return_qty else 0 end) as non_fnv_return_qty,\n", "sum(case when current_status='DELIVERED' then fnv_rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "sum(case when current_status='DELIVERED' then fnv_rpu_qty else 0 end) as fnv_rpu_qty,\n", "sum(case when current_status='DELIVERED' then non_fnv_rpu_qty else 0 end) as non_fnv_rpu_qty,\n", "\n", "sum(case when current_status='DELIVERED' and (fnv_return_qty is null or fnv_return_qty=0) and \n", "(fnv_rpu_qty is null or fnv_rpu_qty=0)\n", "then fnv_reverse_item_count else 0 end) as fnv_refund_qty,\n", "\n", "sum(case when current_status='DELIVERED' and \n", "(non_fnv_return_qty is null or non_fnv_return_qty=0) and \n", "(non_fnv_rpu_qty is null or non_fnv_rpu_qty=0)\n", "then non_fnv_reverse_item_count else 0 end) as non_fnv_refund_qty,\n", "\n", "sum(case when current_status='DELIVERED' and (fnv_return_gmv is null or fnv_return_gmv=0) and \n", "(fnv_rpu_gmv is null or fnv_rpu_gmv=0)\n", "then ref.fnv_reverse_sales\n", "when current_status='DELIVERED' and ref.fnv_reverse_sales-(coalesce(fnv_return_gmv,0)+coalesce(fnv_rpu_gmv,0))>0 then ref.fnv_reverse_sales-(coalesce(fnv_return_gmv,0)+coalesce(fnv_rpu_gmv,0))\n", "else 0 end) as fnv_refund_gmv,\n", "\n", "sum(case when current_status='DELIVERED' and \n", "(non_fnv_return_gmv is null or non_fnv_return_gmv=0) and \n", "(non_fnv_rpu_gmv is null or non_fnv_rpu_gmv=0)\n", "then ref.non_fnv_reverse_sales \n", "when current_status='DELIVERED' and ref.non_fnv_reverse_sales-(coalesce(non_fnv_return_gmv,0)+coalesce(non_fnv_rpu_gmv,0))>0 then ref.non_fnv_reverse_sales-(coalesce(non_fnv_return_gmv,0)+coalesce(non_fnv_rpu_gmv,0))\n", "else 0 end) as non_fnv_refund_gmv\n", "\n", "\n", "from lm_base a \n", "left join total_gmv b on a.order_id=b.order_id\n", "left join return_gmv c on a.order_id=c.order_id\n", "left join rpu_gmv d on a.order_id=d.order_id\n", "left join refund_gmv ref on a.order_id = ref.order_id\n", "group by 1,2,3,4,5    \n", ") as a group by 1,2,3,4,5\n", "\n", "\n", "        \n", "\"\"\".format(\n", "    start_date=date_filter, end_date=date_filter\n", ")\n", "\n", "\n", "logistics_metrics = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logistics_metrics.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logistics_metrics = logistics_metrics[~logistics_metrics[\"city\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# logistics_metrics[\"return_gmv\"] = logistics_metrics[\"return_gmv\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# logistics_metrics[\"total_delivered_gmv\"] = (\n", "#     logistics_metrics[\"total_delivered_gmv\"] - logistics_metrics[\"return_gmv\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH\n", "\n", "base_order_data AS (\n", "    SELECT\n", "        oo.id AS order_id,\n", "        os.id AS suborder_id,\n", "        \n", "        (dsp.current_delivery_start + interval '5.5 hour')::date as sch_date_ist,\n", "        oo.install_ts AS oms_order_install_ts_utc,\n", "        dwbm.backend_merchant_id,\n", "        ln.external_id as frontend_merchant_id,\n", "        ln.external_name AS frontend_merchant_name,\n", "        \n", "        dwbm.pos_outlet_city_name AS frontend_merchant_city\n", "\n", "    FROM \n", "        lake_logistics.view_logistics_dark_store_projection \n", "            AS dsp\n", "    INNER JOIN \n", "        lake_oms_bifrost.oms_order \n", "            AS oo \n", "            ON oo.id = dsp.order_id\n", "    INNER JOIN\n", "        lake_oms_bifrost.oms_suborder\n", "            AS os\n", "            ON os.order_id = oo.id\n", "    INNER JOIN\n", "        lake_logistics.logistics_node\n", "            AS ln\n", "            ON ln.external_id = dsp.dark_store_id\n", "            and ln.id != 35\n", "    INNER JOIN\n", "        lake_oms_bifrost.oms_merchant\n", "            AS om\n", "            ON om.id = oo.merchant_id\n", "    inner join dwh.dim_merchant_outlet_facility_mapping dwbm on ln.external_id = dwbm.frontend_merchant_id\n", "\n", "    WHERE\n", "     valid_to_utc >= current_date\n", "            and is_current = True\n", "            and is_mapping_enabled = True and \n", "            merchant_business_type_id=7 and\n", "        dsp.current_delivery_start >= '{start_date}'::DATE - interval '1 days'- interval '5.5 hour'\n", "        and dsp.current_delivery_start <= '{end_date}'::DATE + interval '1 day'- interval '5.5 hour'\n", "        and oo.install_ts >=  '{start_date}'::DATE - interval '1 days'\n", "        AND os.install_ts >= '{start_date}'::DATE - interval '1 days'\n", "        AND om.install_ts >= '{start_date}'::DATE - interval '1 days'\n", "        AND os.current_status = 'AT_DELIVERY_CENTER' \n", "                -- To get the latest suborder  that was delivered\n", "                -- duplicate entry for DARK_STORE with external_id = 29561\n", "),\n", "\n", "--\n", "-- Used For:\n", "-- PICKER_ASSIGNED\n", "--\n", "lake_logistics_task_data AS (\n", "    SELECT\n", "        base.order_id,\n", "        base.suborder_id,\n", "        \n", "        MAX(\n", "            CASE\n", "                WHEN picker_lt.type = 'BATCH_PROCUREMENT_TASK'\n", "                    AND picker_lt.state = 'COMPLETED'\n", "                    AND lar.state = 'COMPLETED'\n", "                THEN picker_lt.install_ts\n", "            END\n", "        ) AS picker_assigned_ts_utc,\n", "        \n", "        NVL(\n", "            ldsp.delivery_allocation_complete_timestamp, \n", "            ldsp.allocation_complete_timestamp\n", "        ) AS partner_assigned_ts_utc\n", "    \n", "    FROM\n", "        base_order_data\n", "            AS base\n", "    JOIN\n", "        lake_logistics.view_logistics_dark_store_projection \n", "            AS ldsp\n", "            ON ldsp.order_id = base.order_id\n", "    JOIN\n", "        lake_logistics.logistics_allocation_request\n", "            AS lar\n", "            ON lar.label_value = base.order_id\n", "    JOIN\n", "        lake_logistics.logistics_task\n", "            AS picker_lt\n", "            ON picker_lt.shipment_label_value = lar.allocation_batch_id\n", "\n", "            \n", "    WHERE\n", "        lar.install_ts >= '{start_date}'::DATE - interval '1 days'\n", "        AND ldsp.install_ts >= '{start_date}'::DATE - interval '1 days'\n", "        AND picker_lt.install_ts >= '{start_date}'::DATE - interval '1 days'\n", "            \n", "    GROUP BY\n", "        base.order_id,\n", "        base.suborder_id,\n", "        partner_assigned_ts_utc\n", "),\n", "\n", "--\n", "-- Used For:\n", "-- PICKING_STARTED, PICKING_COMPLETED, BILLING_STARTED\n", "--\n", "lake_logistics_warehouse_data AS (\n", "    SELECT\n", "        base.order_id,\n", "        base.suborder_id,\n", "        \n", "        wpl.picking_started_at AS picking_started_ts_utc,\n", "        pll.pos_timestamp AS picking_completed_ts_utc\n", "    FROM\n", "        base_order_data\n", "            AS base\n", "    JOIN\n", "        lake_warehouse_location.warehouse_pick_list_order_mapping \n", "            AS wplom\n", "            ON wplom.order_id = base.suborder_id -- this is not a mistake\n", "    JOIN\n", "        lake_warehouse_location.warehouse_pick_list \n", "            AS wpl\n", "            ON wpl.id = wplom.pick_list_id\n", "    JOIN\n", "        lake_warehouse_location.pick_list_log \n", "            AS pll\n", "            ON pll.pick_list_id = wpl.id\n", "                AND pll.picklist_state = 4 -- state 4 means picking completed\n", "    GROUP BY\n", "        base.order_id,\n", "        base.suborder_id,\n", "        picking_started_ts_utc,\n", "        picking_completed_ts_utc\n", "),\n", "\n", "--\n", "-- Used For:\n", "-- BILLING_COMPLETED\n", "--\n", "lake_logistics_shipping_data AS (\n", "    SELECT\n", "        base.order_id,\n", "        base.suborder_id,\n", "        \n", "        MAX(\n", "            CASE\n", "                WHEN slog.to_state = 'BILLED'\n", "                THEN slog.update_ts\n", "            END \n", "        ) AS billing_completed_ts_utc\n", "    \n", "    FROM\n", "        base_order_data\n", "            AS base\n", "    JOIN\n", "        lake_logistics.logistics_shipment_label \n", "            AS lsl \n", "            ON lsl.label_value = base.order_id\n", "                AND lsl.label_type = 'ORDER_ID'\n", "    JOIN\n", "        lake_logistics.logistics_shipment \n", "            AS ls \n", "            ON ls.id = lsl.shipment_id \n", "                AND ls.status = 'DELIVERED'\n", "    JOIN\n", "        lake_logistics.shipments_shipmentstatelog  \n", "            AS slog \n", "            ON slog.shipment_id = ls.id\n", "            \n", "    WHERE\n", "        lsl.install_ts >= '{start_date}'::DATE - INTERVAL '1 days'\n", "        AND ls.install_ts >= '{start_date}'::DATE - INTERVAL '1 days'\n", "        AND slog.install_ts >= '{start_date}'::DATE - INTERVAL '1 days'\n", "    \n", "    GROUP BY\n", "        base.order_id,\n", "        base.suborder_id\n", "),\n", "    \n", "--\n", "order_delivery_funnel AS (\n", "    SELECT\n", "        base.order_id,\n", "        base.suborder_id,\n", "        base.backend_merchant_id,\n", "        base.frontend_merchant_id,\n", "        base.frontend_merchant_name,\n", "        base.frontend_merchant_city,\n", "        \n", "        base.sch_date_ist,\n", "        base.oms_order_install_ts_utc AS order_checked_out_ts_utc,\n", "        \n", "        -- for stores where seperate picker is not enabled concider partner assigned as picerk assigned time \n", "        NVL(ll_task.picker_assigned_ts_utc, ll_task.partner_assigned_ts_utc) as picker_assigned_ts_utc, \n", "    \n", "        \n", "        ll_warehouse.picking_started_ts_utc,\n", "        ll_warehouse.picking_completed_ts_utc,\n", "        \n", "        ll_shipping.billing_completed_ts_utc\n", "\n", "\n", "        \n", "    FROM\n", "        base_order_data\n", "            AS base\n", "    JOIN\n", "        lake_logistics_task_data\n", "            AS ll_task\n", "            ON base.order_id = ll_task.order_id\n", "                AND base.suborder_id = ll_task.suborder_id\n", "    JOIN\n", "        lake_logistics_warehouse_data\n", "            AS ll_warehouse\n", "            ON base.order_id = ll_warehouse.order_id\n", "                AND base.suborder_id = ll_warehouse.suborder_id\n", "    JOIN\n", "        lake_logistics_shipping_data\n", "            AS ll_shipping\n", "            ON base.order_id = ll_shipping.order_id\n", "                AND base.suborder_id = ll_shipping.suborder_id\n", ")\n", "\n", "--\n", "SELECT\n", "    sch_date_ist,\n", "    backend_merchant_id,\n", "    frontend_merchant_id,\n", "    \n", "    \n", "    SUM(\n", "        DATEDIFF(MINU<PERSON><PERSON>, picker_assigned_ts_utc, picking_completed_ts_utc)\n", "    ) AS total_mins_picker_assigned_to_picking_completed,\n", "    \n", "    SUM(\n", "        DATEDIFF(MINUTES, picking_started_ts_utc, picking_completed_ts_utc)\n", "    ) AS total_mins_picking_start_to_picking_completed,\n", "    \n", "    SUM(\n", "        DATEDIFF(MINUTES, picking_completed_ts_utc, billing_completed_ts_utc)\n", "    ) AS total_mins_picking_completed_billing_completed\n", "    \n", "FROM\n", "\n", "    order_delivery_funnel\n", "    \n", "GROUP BY\n", "    1, 2,3\n", "\"\"\".format(\n", "    start_date=date_filter, end_date=date_filter\n", ")\n", "order_processing_metrics = pd.read_sql(sql=sql, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_processing_metrics.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_processing_metrics[\"frontend_merchant_id\"] = order_processing_metrics[\n", "    \"frontend_merchant_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = logistics_metrics.merge(\n", "    order_processing_metrics,\n", "    how=\"left\",\n", "    on=[\"sch_date_ist\", \"frontend_merchant_id\", \"backend_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"sch_date_ist\",\n", "        \"city\",\n", "        \"frontend_merchant_id\",\n", "        \"frontend_merchant_name\",\n", "        \"total_delivered_gmv\",\n", "        \"total_fnv_gmv\",\n", "        \"total_non_fnv_gmv\",\n", "        \"total_sch_orders\",\n", "        \"total_delivered_orders\",\n", "        \"total_cancelled_orders\",\n", "        \"total_delivery_failed\",\n", "        \"total_items\",\n", "        \"total_mins_picker_assigned_to_picking_completed\",\n", "        \"total_mins_picking_start_to_picking_completed\",\n", "        \"total_mins_picking_completed_billing_completed\",\n", "        \"backend_merchant_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"marts_merchant_app\",\n", "    \"table_name\": \"ds_business_metrics\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"sch_date_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Date when orders where scheduled \",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"City name of the store \",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"frontend_merchant_id of the store\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_name\",\n", "            \"type\": \"varchar(200)\",\n", "            \"description\": \"frontend_merchant_name of the store\",\n", "        },\n", "        {\n", "            \"name\": \"total_delivered_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sum of delivered GMV for the day\",\n", "        },\n", "        {\n", "            \"name\": \"total_fnv_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sum of delivered GMV of FNV products \",\n", "        },\n", "        {\n", "            \"name\": \"total_non_fnv_gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sum of delivered GMV of Non FNV products \",\n", "        },\n", "        {\n", "            \"name\": \"total_sch_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"total orders scheduled for sch_date_ist \",\n", "        },\n", "        {\n", "            \"name\": \"total_delivered_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"total orders delivered on sch_date_ist \",\n", "        },\n", "        {\n", "            \"name\": \"total_cancelled_orders\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Of the scheduled orders Total orders cancelled\",\n", "        },\n", "        {\n", "            \"name\": \"total_delivery_failed\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Of the scheduled orders where delivery failed\",\n", "        },\n", "        {\n", "            \"name\": \"total_Items\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sum of items in delivered orders\",\n", "        },\n", "        {\n", "            \"name\": \"total_mins_picker_assigned_to_picking_completed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total time in minutes from picker assigned to picking completed for all delivered orders\",\n", "        },\n", "        {\n", "            \"name\": \"total_mins_picking_start_to_picking_completed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total time  in minutes for picking all orders \",\n", "        },\n", "        {\n", "            \"name\": \"total_mins_picking_completed_billing_completed\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total time in minutes from picking completed to billing completed for all delivered orders\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_id\",\n", "            \"type\": \"varchar(50)\",\n", "            \"description\": \"backend_merchant_id of the store\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"sch_date_ist\", \"frontend_merchant_id\"],\n", "    \"sortkey\": [\"sch_date_ist\", \"frontend_merchant_id\"],\n", "    \"incremental_key\": \"sch_date_ist\",\n", "    \"table_description\": \"Table contains dark stores business and operational metrics for merchant store dashboard\",\n", "    \"load_type\": \"upsert\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}