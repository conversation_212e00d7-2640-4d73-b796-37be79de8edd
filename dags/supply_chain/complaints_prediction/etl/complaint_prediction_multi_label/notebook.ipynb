{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "import pytz\n", "\n", "date_now = (datetime.now(pytz.timezone(\"Asia/Kolkata\")) + timedelta(days=1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "\n", "curr_hour = (datetime.now(pytz.timezone(\"Asia/Kolkata\"))).strftime(\"%H\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_now, curr_hour"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn==0.21.3\n", "!pip install xgboost==1.2.1\n", "!pip install joblib==0.14.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "import random\n", "import pytz\n", "\n", "import xgboost\n", "from xgboost import XGBClassifier\n", "import joblib\n", "\n", "import zipfile\n", "import os\n", "import pencilbox as pb\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id = (26, 30, 39, 48, 92, 3, 15, 24, 34)\n", "\n", "facility_mapping = {\n", "    \"g3\": 26,\n", "    \"lucknow\": 30,\n", "    \"kundli\": 39,\n", "    \"mumbai2\": 48,\n", "    \"dasna2\": 92,\n", "    \"bengaluru\": 3,\n", "    \"jaipur\": 15,\n", "    \"kolkata\": 24,\n", "    \"kolkata_k2\": 34,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Collection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch backend merchants"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_query = f\"\"\"\n", "WITH base AS\n", "(\n", "SELECT DISTINCT\n", "    pco.facility_id,\n", "    cf.name AS facility_name,\n", "    pco.id AS outlet_id,\n", "    pco.name AS outlet_name,\n", "    pco.business_type_id AS outlet_type_id,\n", "    bom.external_id AS backend_merchant_id,\n", "    bom.name AS backend_merchant_name\n", "FROM lake_oms_bifrost.oms_merchant AS bom\n", "INNER JOIN lake_retail.console_outlet_cms_store AS cms\n", "    ON bom.external_id = cms.cms_store \n", "    AND cms.active = 1 \n", "    AND cms.cms_update_active = 1\n", "    AND virtual_merchant_type <> 'superstore_merchant'\n", "INNER JOIN lake_retail.view_console_outlet AS PCO\n", "    ON cms.outlet_id = pco.id\n", "INNER JOIN lake_crates.facility AS cf \n", "    ON cf.id = pco.facility_id\n", "WHERE pco.facility_id  in {facility_id}\n", ")\n", "SELECT DISTINCT\n", "    backend_merchant_id, facility_id, facility_name, outlet_id, outlet_type_id\n", "FROM base\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping = pd.read_sql_query(sql=merchant_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_ids = tuple(merchant_mapping.backend_merchant_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping.facility_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Orders data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_query = f\"\"\"\n", "SELECT DISTINCT \n", "     (OO.INSTALL_TS + interval '5.5 hour') as order_checkout_date,\n", "      CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_START * INTERVAL '1 SECOND')) as delivery_slot_start,\n", "      CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_END * INTERVAL '1 SECOND')) as delivery_slot_end,\n", "       OC.ID AS CART_ID,\n", "       OS.order_id,\n", "       OS.id as suborder_id,\n", "       OO.net_cost,\n", "       OO.customer_id,\n", "       OO.station,\n", "       OM.external_id AS backend_merchant_id,\n", "       json_extract_path_text(dp.meta,'unique_name',true) as dp_id,\n", "        case when Extract(hrs from (lmo.delivery_slot_start_time AT TIME ZONE 'UTC') AT TIME ZONE 'Asia/Kolkata') < 14 then 'Slot A'\n", "        else 'Slot B' end as slot_type,\n", "        lmo.distribution_point_id,\n", "        dp.name as dp_name\n", "       FROM lake_oms_bifrost.oms_suborder AS OS\n", "       INNER JOIN lake_oms_bifrost.oms_order AS OO ON OS.ORDER_ID = OO.ID\n", "       AND OS.TYPE = 'RetailSuborder'\n", "       AND OS.CURRENT_STATUS <> 'CANCELLED'\n", "       AND OO.TYPE = 'RetailForwardOrder'\n", "       AND OO.CUSTOMER_ID <> 673235\n", "       INNER JOIN lake_oms_bifrost.oms_cart AS OC ON OO.CART_ID = OC.ID\n", "       INNER JOIN lake_oms_bifrost.oms_merchant OM ON os.backend_merchant_id = om.id\n", "       AND OM.external_id in {backend_ids}\n", "       LEFT JOIN lake_last_mile.order lmo on lmo.external_order_id  = OS.order_id\n", "       LEFT JOIN lake_last_mile.order_item loi on loi.order_id = lmo.id\n", "       LEFT JOIN lake_last_mile.distribution_point dp ON dp.id = lmo.actual_dp_id\n", "    WHERE\n", "    CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_START * INTERVAL '1 SECOND'))::DATE = '{date_now}'\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "    \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.read_sql_query(sql=order_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.order_id.nunique(), orders_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch min product weight per order"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_product_weight_query = f\"\"\"select oo.id as order_id,\n", "       loi.total_weight,\n", "    loi.quantity,\n", "    loi.product_name\n", "       from lake_oms_bifrost.oms_order oo\n", "        inner join \n", "        lake_last_mile.order lmo\n", "        on oo.id = lmo.external_order_id\n", "        AND OO.TYPE = 'RetailForwardOrder'\n", "        AND OO.CUSTOMER_ID <> 673235\n", "        \n", "        and oo.id in {tuple(orders_data.order_id.unique())}\n", "        inner join lake_last_mile.order_item loi\n", "        on loi.order_id = lmo.id\n", "       \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_product_weight = pd.read_sql_query(order_product_weight_query, redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_product_weight.shape, order_product_weight.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_product_weight[\"weight_per_quantity\"] = (\n", "    order_product_weight[\"total_weight\"] / order_product_weight[\"quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_product_weight.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_min_weight_product = (\n", "    order_product_weight.groupby(\"order_id\")[\"weight_per_quantity\"]\n", "    .min()\n", "    .reset_index(name=\"min_product_weight(gms)\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_min_weight_product.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(orders_data, order_min_weight_product, on=\"order_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"min_product_weight(gms)\"] = orders_data[\"min_product_weight(gms)\"] * 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.shape, orders_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch weight, ipo, sku"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_query = f\"\"\"\n", "       SELECT\n", "        oo.id as order_id,\n", "        sum(loi.quantity) ipo,\n", "        count(distinct loi.product_id) as sku,\n", "        sum(case when loi.quantity > 0 and loi.total_weight/loi.quantity > 50 then cast(loi.total_weight as decimal(18,2))/1000\n", "        else loi.total_weight end) as weight\n", "        from lake_oms_bifrost.oms_order oo\n", "        inner join \n", "        lake_last_mile.order lmo\n", "        on oo.id = lmo.external_order_id\n", "        AND OO.TYPE = 'RetailForwardOrder'\n", "        AND OO.CUSTOMER_ID <> 673235\n", "        \n", "        and oo.id in {tuple(orders_data.order_id.unique())}\n", "        inner join lake_last_mile.order_item loi\n", "        on loi.order_id = lmo.id\n", "        GROUP BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_data = pd.read_sql_query(sql=order_config_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data, order_config_data, how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.order_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## FC mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    merchant_mapping,\n", "    how=\"left\",\n", "    left_on=\"backend_merchant_id\",\n", "    right_on=\"backend_merchant_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.drop_duplicates().shape, orders_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.loc[\n", "    (orders_data[\"min_product_weight(gms)\"].isna()) & (orders_data.facility_id == 3),\n", "    \"slot_type\",\n", "].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Number of merchant ids & suborder ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mer_ids = (\n", "    orders_data.groupby(\"order_id\")[\"backend_merchant_id\"]\n", "    .unique()\n", "    .reset_index(name=\"backend_merchant_ids\")\n", ")\n", "suborder_ids = (\n", "    orders_data.groupby(\"order_id\")[\"suborder_id\"]\n", "    .unique()\n", "    .reset_index(name=\"suborder_ids\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mer_ids.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp = orders_data[\n", "    [\"backend_merchant_id\", \"suborder_id\", \"outlet_id\", \"outlet_type_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.suborder_id.nunique(), temp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.drop(\n", "    columns=[\"backend_merchant_id\", \"suborder_id\", \"outlet_id\", \"outlet_type_id\"],\n", "    inplace=True,\n", ")\n", "orders_data.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.shape, orders_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(orders_data, mer_ids, on=\"order_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.shape, orders_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(orders_data, suborder_ids, on=\"order_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.shape, orders_data.order_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding diff types of suborder columns : grocery, cold, large"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# source : lake_retail.console_business_type {outlet_type_id = 1: Grocery, 2: Cold, 3:Large, 4:FnV}\n", "grocery_suborder_ids = temp.loc[temp.outlet_type_id == 1, \"suborder_id\"]\n", "cold_suborder_ids = temp.loc[temp.outlet_type_id == 2, \"suborder_id\"]\n", "large_suborder_ids = temp.loc[temp.outlet_type_id == 3, \"suborder_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"grocery_suborder_ids\"] = orders_data.apply(\n", "    lambda row: list(set(row[\"suborder_ids\"]).intersection(grocery_suborder_ids)),\n", "    axis=1,\n", ")\n", "orders_data[\"cold_suborder_ids\"] = orders_data.apply(\n", "    lambda row: list(set(row[\"suborder_ids\"]).intersection(cold_suborder_ids)), axis=1\n", ")\n", "orders_data[\"large_suborder_ids\"] = orders_data.apply(\n", "    lambda row: list(set(row[\"suborder_ids\"]).intersection(large_suborder_ids)), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"#backend_merchant_ids\"] = orders_data.apply(\n", "    lambda row: len(row.backend_merchant_ids), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(grocery_suborder_ids), len(cold_suborder_ids), len(large_suborder_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.loc[\n", "    (orders_data[\"min_product_weight(gms)\"].isna()) & (orders_data.facility_id == 3),\n", "    \"slot_type\",\n", "].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Products in cart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_query = f\"\"\"\n", "with CATEGORY_PRODUCT_TREE AS\n", "    (\n", "    SELECT DISTINCT\n", "       grp.ID as product_id,\n", "        L1_GRC.ID AS L1_CATEGORY_ID,\n", "         L1_GRC.NAME AS L1_CATEGORY_NAME,\n", "         L2_GRC.ID AS L2_CATEGORY_ID,\n", "         L2_GRC.NAME AS L2_CATEGORY_NAME,\n", "         L3_GRC.ID AS L3_CATEGORY_ID,\n", "         L3_GRC.NAME L3_CATEGORY_NAME,\n", "         grp.name as product_name,\n", "         handling_type,\n", "         storage_type,\n", "         CASE WHEN STORAGE_TYPE IN ('refrigerated','deep_freeze')\n", "         or upper(L1_GRC.NAME) like '%%FROZEN%%'\n", "       or upper(L2_GRC.NAME) like '%%FROZEN%%'\n", "       or upper(L3_GRC.NAME) like '%%FROZEN%%'\n", "         THEN 'Cold'\n", "       ELSE(CASE WHEN HANDLING_TYPE = 'large' THEN 'Large'\n", "                     ELSE 'Grocery'\n", "                 END)\n", "       END AS GENERAL_CATEGORY\n", "    FROM lake_cms.gr_product AS grp\n", "    INNER JOIN lake_cms.gr_product_category_mapping AS PCM\n", "        ON grp.ID = PCM.PRODUCT_ID\n", "        AND PCM.IS_PRIMARY = 'TRUE'\n", "    INNER JOIN lake_cms.gr_category  AS L3_GRC\n", "         ON PCM.CATEGORY_ID = L3_GRC.ID\n", "    INNER JOIN lake_cms.gr_category AS L2_GRC\n", "       ON L3_GRC.PARENT_CATEGORY_ID = L2_GRC.ID\n", "    INNER JOIN lake_cms.gr_category AS L1_GRC\n", "       ON L2_GRC.PARENT_CATEGORY_ID = L1_GRC.ID\n", "\n", "    ORDER BY 1,3,5,7\n", "    )\n", "\n", "select lo.order_id, lo.product_id, ct.general_category,  ct.product_name, ct.L1_CATEGORY_NAME, ct.L2_CATEGORY_NAME, ct.L3_CATEGORY_NAME  from lake_oms_bifrost.oms_order_item lo \n", "LEFT JOIN CATEGORY_PRODUCT_TREE ct on lo.product_id = ct.product_id where lo.order_id in {tuple(orders_data.order_id.unique())}\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = pd.read_sql_query(sql=products_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.dropna(subset=[\"product_id\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data.shape, products_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# products_data.loc[products_data.order_id==67542189]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Item Product Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_query = f\"\"\"\n", "\n", "select item_id, product_id from lake_rpc.item_product_mapping\n", "where product_id in {tuple(products_data.product_id.unique())}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = pd.read_sql_query(sql=mapping_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = item_product_mapping.dropna(subset=[\"item_id\"]).reset_index(\n", "    drop=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pl_query = f\"\"\"\n", "select item_id, perishable, is_pl from lake_rpc.product_product\n", "where item_id in {tuple(item_product_mapping.item_id.unique())}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_products = pd.read_sql_query(sql=pl_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = pd.merge(\n", "    item_product_mapping,\n", "    perishable_products,\n", "    how=\"left\",\n", "    left_on=\"item_id\",\n", "    right_on=\"item_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = pd.merge(\n", "    products_data,\n", "    item_product_mapping,\n", "    how=\"left\",\n", "    left_on=\"product_id\",\n", "    right_on=\"product_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.drop(\n", "    [\"item_id\", \"l2_category_name\", \"l3_category_name\"], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data[\"perishable\"] = products_data.perishable.fillna(0)\n", "products_data[\"is_pl\"] = products_data.is_pl.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data[\"perishable\"] = products_data.perishable.apply(lambda x: 1 if x else 0)\n", "products_data[\"is_pl\"] = products_data.is_pl.apply(lambda x: 1 if x else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data[~products_data.l1_category_name.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetching Historical Orders and Complaints of customers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_num_orders = f\"\"\"\n", "SELECT oo.customer_id, count(distinct(oo.id)) AS previous_order_count\n", "FROM lake_oms_bifrost.oms_suborder AS OS\n", "    INNER JOIN lake_oms_bifrost.oms_order AS OO ON OS.ORDER_ID = OO.ID\n", "       INNER JOIN lake_oms_bifrost.oms_merchant OM ON os.backend_merchant_id = om.id\n", "       AND OM.external_id in {backend_ids}\n", "AND OO.TYPE = 'RetailForwardOrder'\n", "AND OS.TYPE = 'RetailSuborder'\n", "AND OO.CUSTOMER_ID <> 673235\n", "WHERE \n", "  oo.customer_id in {tuple(orders_data.customer_id.unique())}\n", "  AND OO.CURRENT_STATUS IN ('DELIVERED')\n", "  AND (oo.INSTALL_TS + interval '5.5 hour')::date < '{date_now}'\n", "  GROUP BY 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_orders = pd.read_sql_query(sql=hist_num_orders, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_complaints = f\"\"\"\n", "select customer_id, count(distinct cart_id) as previous_complaints from metrics.complaints\n", "where customer_id in  {tuple(orders_data.customer_id.unique())}\n", "and complaint_date <='{date_now}':: timestamp - interval '1 day'\n", "GROUP BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_complaints = pd.read_sql_query(sql=hist_complaints, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_complaints.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    historical_orders,\n", "    how=\"left\",\n", "    left_on=\"customer_id\",\n", "    right_on=\"customer_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    historical_complaints,\n", "    how=\"left\",\n", "    left_on=\"customer_id\",\n", "    right_on=\"customer_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.order_id.nunique(), orders_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"order_checkout_date\"] = pd.to_datetime(orders_data[\"order_checkout_date\"])\n", "orders_data[\"delivery_slot_start\"] = pd.to_datetime(orders_data[\"delivery_slot_start\"])\n", "orders_data[\"delivery_slot_end\"] = pd.to_datetime(orders_data[\"delivery_slot_end\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"previous_complaints\"] = orders_data.previous_complaints.fillna(0)\n", "orders_data[\"previous_order_count\"] = orders_data.previous_order_count.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cal_ratio(row):\n", "    try:\n", "        complaint_order_ratio = row[\"previous_complaints\"] / row[\"previous_order_count\"]\n", "\n", "        if complaint_order_ratio > 1:\n", "            return 1\n", "        else:\n", "            return complaint_order_ratio\n", "    except:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"complaint_order_ratio\"] = orders_data.apply(cal_ratio, axis=1)\n", "orders_data[\"complaint_order_ratio\"] = orders_data.complaint_order_ratio.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishables_per_order = (\n", "    products_data.groupby(\"order_id\")\n", "    .agg({\"perishable\": \"sum\", \"is_pl\": \"sum\"})\n", "    .reset_index()\n", "    .rename({\"perishable\": \"num_perishable\", \"is_pl\": \"num_pl\"}, axis=1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "\n", "enc = OneHotEncoder(handle_unknown=\"ignore\", drop=None)\n", "l1_categories = enc.fit_transform(products_data[[\"l1_category_name\"]]).toarray()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_categories = pd.DataFrame(l1_categories)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data = pd.concat([products_data[[\"order_id\"]], l1_categories], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data.columns = [\"order_id\"] + enc.categories_[0].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data = (\n", "    l1_products_data.groupby(\"order_id\")\n", "    .agg(\n", "        {\n", "            \"Biscuits, Snacks & Chocolates\": \"sum\",\n", "            \"Breakfast & Dairy\": \"sum\",\n", "            \"Grocery & Staples\": \"sum\",\n", "            \"Home Furnishing and Decor\": \"sum\",\n", "            \"Home Improvement and Accessories\": \"sum\",\n", "            \"Household Items\": \"sum\",\n", "            \"Kitchen and Dining Needs\": \"sum\",\n", "            \"Noodles, Sauces & Instant Food\": \"sum\",\n", "            \"Specials\": \"sum\",\n", "            \"Vegetables & Fruits\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["enc = OneHotEncoder(handle_unknown=\"ignore\", drop=None)\n", "general_categories = enc.fit_transform(products_data[[\"general_category\"]]).toarray()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["general_categories = pd.DataFrame(general_categories)\n", "general_categories = pd.concat(\n", "    [products_data[[\"order_id\"]], general_categories], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["general_categories.columns = [\"order_id\"] + enc.categories_[0].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_large_count = (\n", "    general_categories.groupby(\"order_id\")\n", "    .agg({\"Cold\": \"sum\", \"Large\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_df1 = pd.merge(\n", "    orders_data,\n", "    perishables_per_order,\n", "    left_on=\"order_id\",\n", "    right_on=\"order_id\",\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_df2 = pd.merge(\n", "    temp_df1, l1_products_data, left_on=\"order_id\", right_on=\"order_id\", how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    temp_df2, cold_large_count, how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.order_id.nunique(), test_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.drop_duplicates(subset=[\"order_id\"]).shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"diff_chckout_delivery_slot\"] = (\n", "    test_data.delivery_slot_start - test_data.order_checkout_date\n", ").dt.days"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"chckout_day\"] = test_data.order_checkout_date.dt.day\n", "test_data[\"chckout_month\"] = test_data.order_checkout_date.dt.month\n", "test_data[\"chckout_hour\"] = test_data.order_checkout_date.dt.hour\n", "test_data[\"chckout_min\"] = test_data.order_checkout_date.dt.minute\n", "\n", "test_data[\"delivery_slot_day\"] = test_data.delivery_slot_start.dt.day\n", "test_data[\"delivery_slot_hour\"] = test_data.delivery_slot_start.dt.hour\n", "test_data[\"delivery_slot_month\"] = test_data.delivery_slot_start.dt.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.slot_type = test_data.slot_type.map({\"Slot A\": 0, \"Slot B\": 1})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.shape, test_data.order_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Selecting slot and facility wrt dag run time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '01':\n", "#     slot = 0\n", "#     slot_name = 'Slot_A'\n", "#     fac_id = 30\n", "#     fac_name = 'Lucknow'\n", "#     station = ['stationka1', 'stationall1']\n", "\n", "# elif (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '06':\n", "#     slot = 0\n", "#     slot_name = 'Slot_A'\n", "#     fac_id = 30\n", "#     fac_name = 'Lucknow'\n", "#     station = ['stationlko1']\n", "\n", "# elif (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '08':\n", "#     slot = 0\n", "#     slot_name = 'Slot_A'\n", "#     fac_id = 26\n", "#     fac_name = 'G3'\n", "#     station = ['stationdl2', 'stationdl23', 'stationhr24', 'stationdl5', 'stationdl34']\n", "\n", "# elif (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '13':\n", "#     slot = 1\n", "#     slot_name = 'Slot_B'\n", "#     fac_id = 30\n", "#     fac_name = 'Lucknow'\n", "#     station = ['stationka1']\n", "\n", "# elif (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '18':\n", "#     slot = 1\n", "#     slot_name = 'Slot_B'\n", "#     fac_id = 30\n", "#     fac_name = 'Lucknow'\n", "#     station = ['stationlko1']\n", "\n", "# elif (datetime.now(pytz.timezone('Asia/Kolkata'))).strftime(\"%H\") == '20':\n", "#     slot = 1\n", "#     fac_id = 26\n", "#     slot_name = 'Slot_B'\n", "#     fac_name = 'G3'\n", "#     station = ['stationdl2', 'stationdl23', 'stationhr24', 'stationdl5', 'stationdl34']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test_data.loc[test_data.station=='stationlkom2'][:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# removing stationlkom2 as this is a dark store\n", "test_data = test_data.loc[test_data.station != \"stationlkom2\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# curr_hour = (datetime.now(pytz.timezone(\"Asia/Kolkata\"))).strftime(\"%H\")\n", "check_audit_type = 0  # 0: fc_audit ; 1: lm_audit\n", "\n", "if curr_hour == \"11\":\n", "    slot = 0\n", "    slot_name = \"Slot_A\"\n", "    fac_id = 3\n", "    fac_name = \"Bengaluru\"\n", "    check_audit_type = 0\n", "\n", "elif curr_hour == \"23\":\n", "    slot = 1\n", "    fac_id = 3\n", "    slot_name = \"Slot_B\"\n", "    fac_name = \"Bengaluru\"\n", "    check_audit_type = 0\n", "\n", "elif curr_hour == \"18\":\n", "    fac_id = [26, 30, 48, 92, 15, 24, 34, 3]\n", "    fac_name = [\n", "        \"G3\",\n", "        \"Lucknow\",\n", "        \"Mumbai2\",\n", "        \"Dasna2\",\n", "        \"Jaipur\",\n", "        \"Kolkata\",\n", "        \"Kolkata_k2\",\n", "        \"Bengaluru\",\n", "    ]\n", "    check_audit_type = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# str(slot_name), slot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if check_audit_type == 0:\n", "    test_data = test_data.loc[\n", "        (test_data.slot_type == slot) & (test_data.facility_id == fac_id)\n", "    ]\n", "\n", "else:\n", "    test_data = test_data.loc[test_data.facility_id.isin(fac_id)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test_data = test_data.loc[(test_data.slot_type == 0) & (test_data.facility_id == 26)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test_data = test_data.loc[(test_data.slot_type == slot) & (test_data.facility_id == fac_id) &\n", "#                          (test_data.station.isin(station) )]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.shape, test_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# jaipur & lko orders\n", "test_data = test_data.loc[test_data.facility_id.isin([15, 30])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.shape, test_data.order_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading artifacts & predicting v1 (binary classification)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "local_filename = \"/tmp/artifacts_v1.zip\"\n", "cloud_filename = \"pencilbox/complaint_prediction/v1/artifacts/artifacts_v1\"\n", "\n", "dir_name = \"artifacts_v1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.from_s3(bucket_name, cloud_filename, local_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with zipfile.ZipFile(local_filename, \"r\") as zip_ref:\n", "    if not os.path.exists(dir_name):\n", "        os.makedirs(dir_name)\n", "        zip_ref.extractall(dir_name)\n", "    else:\n", "        zip_ref.extractall(dir_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xgb_mumbai_v1 = joblib.load(dir_name + \"/xgb_mumbai\")\n", "xgb_lucknow_v1 = joblib.load(dir_name + \"/xgb_lucknow\")\n", "xgb_dasna_v1 = joblib.load(dir_name + \"/xgb_dasna\")\n", "xgb_blr_v1 = joblib.load(dir_name + \"/xgb_blr\")\n", "xgb_g3_v1 = joblib.load(dir_name + \"/xgb_g3\")\n", "xgb_kundli_v1 = joblib.load(dir_name + \"/xgb_kundli\")\n", "xgb_kol_v1 = joblib.load(dir_name + \"/xgb_kolkata\")\n", "xgb_jaipur_v1 = joblib.load(dir_name + \"/xgb_jaipur\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["featurised_month_v1 = pd.read_csv(dir_name + \"/featurised_month.csv\")\n", "featurised_station_v1 = pd.read_csv(dir_name + \"/featurised_station.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    test_data,\n", "    featurised_month_v1,\n", "    how=\"left\",\n", "    left_on=[\"delivery_slot_month\", \"facility_id\"],\n", "    right_on=[\"month\", \"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    test_data,\n", "    featurised_station_v1,\n", "    how=\"left\",\n", "    left_on=[\"station\", \"facility_id\"],\n", "    right_on=[\"station\", \"facility_id\"],\n", ").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"month_enc1\"] = test_data.month_enc1.fillna(0.5)\n", "test_data[\"month_enc2\"] = test_data.month_enc2.fillna(0.5)\n", "\n", "test_data[\"station_enc1\"] = test_data.station_enc1.fillna(0.5)\n", "test_data[\"station_enc2\"] = test_data.station_enc2.fillna(0.5)\n", "\n", "test_data[\"ipo\"] = test_data.ipo.fillna(0)\n", "test_data[\"sku\"] = test_data.sku.fillna(0)\n", "test_data[\"weight\"] = test_data.weight.fillna(0)\n", "test_data[\"min_product_weight(gms)\"] = test_data[\"min_product_weight(gms)\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_v1 = [\n", "    \"complaint_order_ratio\",\n", "    \"num_perishable\",\n", "    \"Kitchen and Dining Needs\",\n", "    \"ipo\",\n", "    \"Noodles, Sauces & Instant Food\",\n", "    \"Home Improvement and Accessories\",\n", "    \"Home Furnishing and Decor\",\n", "    \"Specials\",\n", "    \"month_enc1\",\n", "    \"month_enc2\",\n", "    \"weight\",\n", "    \"Grocery & Staples\",\n", "    \"Breakfast & Dairy\",\n", "    \"station_enc1\",\n", "    \"station_enc2\",\n", "    \"Cold\",\n", "    \"Large\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_proba_config_v1 = {\n", "    26: 0.64,\n", "    30: 0.6,\n", "    39: 0.69,\n", "    48: 0.68,\n", "    92: 0.61,\n", "    3: 0.65,\n", "    15: 0.55,\n", "    24: 0.5,\n", "    34: 0.5,\n", "}\n", "\n", "facility_model_config_v1 = {\n", "    26: xgb_g3_v1,\n", "    30: xgb_lucknow_v1,\n", "    39: xgb_kundli_v1,\n", "    48: xgb_mumbai_v1,\n", "    92: xgb_dasna_v1,\n", "    3: xgb_blr_v1,\n", "    15: xgb_jaipur_v1,\n", "    24: xgb_kol_v1,\n", "    34: xgb_kol_v1,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1 = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_predictions(facility_id, df, model_features):\n", "\n", "    y_pred = facility_model_config_v1[facility_id].predict(df[model_features])\n", "    y_proba = facility_model_config_v1[facility_id].predict_proba(df[model_features])\n", "\n", "    predictions = [\n", "        1 if pred[1] >= facility_proba_config_v1[facility_id] else 0 for pred in y_proba\n", "    ]\n", "\n", "    df[\"complaint_prediction\"] = predictions\n", "    df[\"pred_proba\"] = y_proba[:, 1]\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for facility_id, group in test_data.groupby(\"facility_id\"):\n", "    facility_pred = get_predictions(facility_id, group, features_v1)\n", "    predictions_v1 = predictions_v1.append(facility_pred, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.loc[\n", "    test_data.facility_id.isin([15, 30]), \"order_id\"\n", "].nunique(), test_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1.facility_id.unique(), predictions_v1.station.unique(), predictions_v1.slot_type.unique(), predictions_v1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1.loc[\n", "    predictions_v1.facility_id == 30, \"complaint_prediction\"\n", "].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["739 / (739 + 2117)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading artifacts & predicting v2 (multi label)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "local_filename = \"/tmp/artifacts_v2.zip\"\n", "cloud_filename = \"pencilbox/complaint_prediction/v2/artifacts/artifacts_v2\"\n", "\n", "dir_name = \"artifacts_v2\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.from_s3(bucket_name, cloud_filename, local_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with zipfile.ZipFile(local_filename, \"r\") as zip_ref:\n", "    if not os.path.exists(dir_name):\n", "        os.makedirs(dir_name)\n", "        zip_ref.extractall(dir_name)\n", "    else:\n", "        zip_ref.extractall(dir_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "xgb_mumbai2_v2 = joblib.load(dir_name + \"/xgb_mumbai2_v2.pkl\")\n", "xgb_lucknow_v2 = joblib.load(dir_name + \"/xgb_lucknow_v2.pkl\")\n", "xgb_dasna2_v2 = joblib.load(dir_name + \"/xgb_dasna2_v2.pkl\")\n", "xgb_blr_v2 = joblib.load(dir_name + \"/xgb_blr_v2.pkl\")\n", "xgb_g3_v2 = joblib.load(dir_name + \"/xgb_g3_v2.pkl\")\n", "xgb_kol_v2 = joblib.load(dir_name + \"/xgb_kolkata_v2.pkl\")\n", "xgb_jaipur_v2 = joblib.load(dir_name + \"/xgb_jaipur_v2.pkl\")\n", "# xgb_kundli_v2 = joblib.load(dir_name + \"/xgb_kundli\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# featurised_month = pd.read_csv(dir_name + \"/featurised_month_v2.csv\")\n", "featurised_station_v2 = pd.read_csv(dir_name + \"/featurised_station_v2.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1 = pd.merge(\n", "    predictions_v1,\n", "    featurised_station_v2,\n", "    how=\"left\",\n", "    left_on=[\"station\", \"facility_id\"],\n", "    right_on=[\"station\", \"facility_id\"],\n", ").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1[\"station_enc1_v2\"] = predictions_v1.station_enc1_v2.fillna(0.25)\n", "predictions_v1[\"station_enc2_v2\"] = predictions_v1.station_enc2_v2.fillna(0.25)\n", "predictions_v1[\"station_enc3_v2\"] = predictions_v1.station_enc3_v2.fillna(0.25)\n", "predictions_v1[\"station_enc4_v2\"] = predictions_v1.station_enc4_v2.fillna(0.25)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_v2 = [\n", "    \"#backend_merchant_ids\",\n", "    \"ipo\",\n", "    \"sku\",\n", "    \"weight\",\n", "    \"previous_order_count\",\n", "    \"previous_complaints\",\n", "    \"complaint_order_ratio\",\n", "    \"num_perishable\",\n", "    \"num_pl\",\n", "    \"Biscuits, Snacks & Chocolates\",\n", "    \"Breakfast & Dairy\",\n", "    \"Home Furnishing and Decor\",\n", "    \"Home Improvement and Accessories\",\n", "    \"Household Items\",\n", "    \"Kitchen and Dining Needs\",\n", "    \"Noodles, Sauces & Instant Food\",\n", "    \"Specials\",\n", "    \"Cold\",\n", "    \"Large\",\n", "    \"diff_chckout_delivery_slot\",\n", "    \"chckout_day\",\n", "    \"chckout_month\",\n", "    \"chckout_hour\",\n", "    \"chckout_min\",\n", "    \"delivery_slot_day\",\n", "    \"delivery_slot_hour\",\n", "    \"station_enc1_v2\",\n", "    \"station_enc2_v2\",\n", "    \"station_enc3_v2\",\n", "    \"station_enc4_v2\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_proba_config_v2 = {26: 0.64, 30: 0.61, 39: 0.69, 48: 0.68, 92: 0.61}\n", "\n", "facility_model_config_v2 = {\n", "    26: xgb_g3_v2,\n", "    30: xgb_lucknow_v2,\n", "    3: xgb_blr_v2,\n", "    48: xgb_mumbai2_v2,\n", "    92: xgb_dasna2_v2,\n", "    15: xgb_jaipur_v2,\n", "    24: xgb_kol_v2,\n", "    34: xgb_kol_v2\n", "    #     39: xgb_kundli_v2\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2 = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for facility_id, group in predictions_v1.groupby(\"facility_id\"):\n", "    y_pred = facility_model_config_v2[facility_id].predict(group[features_v2])\n", "    y_prob = facility_model_config_v2[facility_id].predict_proba(group[features_v2])\n", "    group[\"complaint_type\"] = y_pred\n", "    predictions_v1_v2 = predictions_v1_v2.append(group, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.facility_id.unique(), predictions_v1_v2.station.unique(), predictions_v1_v2.slot_type.unique(), predictions_v1_v2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.complaint_type.value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Column 'complaint_type' classifies into 4 classes :\n", "- Class 0 : Neither damage nor missing i.e. other type of complains\n", "- Class 1 : Only missing\n", "- Class 2 : Only damaged\n", "- Class 3 : Both damage and missing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Column 'complaint_type_final' classifies into 2 classes :\n", "- Class 0 : Neither damage nor missing i.e. other type of complains\n", "- Class 1 : Either damage or missing or both"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2[\"complaint_type_final\"] = predictions_v1_v2.apply(\n", "    lambda row: 1 if row[\"complaint_type\"] != 0 else 0, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def replace_no_cmps(row):\n", "    if row[\"complaint_prediction\"] == 0:\n", "        row[\"complaint_type\"] = \"NULL\"\n", "        row[\"complaint_type_final\"] = \"NULL\"\n", "    return row"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2 = predictions_v1_v2.apply(replace_no_cmps, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.complaint_prediction.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.complaint_type_final.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.loc[\n", "    predictions_v1_v2.facility_id == 30, \"complaint_type_final\"\n", "].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["565 / (565 + 2117 + 174)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.loc[\n", "    predictions_v1_v2[\"min_product_weight(gms)\"].isna(), \"complaint_type_final\"\n", "].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2.order_id.nunique(), predictions_v1_v2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions_v1_v2 = predictions_v1_v2.sort_values(by=\"pred_proba\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tmp_lko = predictions_v1_v2.loc[predictions_v1_v2.facility_id == 30]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tmp_lko.complaint_type_final.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["testing = predictions_v1_v2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["testing.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.DataFrame()\n", "final_size = 500\n", "for f_id, df in testing.groupby(\"facility_id\"):\n", "    for loc in range(200, 700, 50):\n", "        try:\n", "            audit_number = df[:loc][\"complaint_type_final\"].sum()\n", "            if (audit_number > 250) and (audit_number < 310):\n", "                final_size = loc\n", "                final_audit_number = df[:final_size][\"complaint_type_final\"].sum()\n", "        except:\n", "            break\n", "    df[final_size:][\"complaint_prediction\"] = 0\n", "    df[final_size:][\"complaint_type\"] = \"NULL\"\n", "    df[final_size:][\"complaint_type_final\"] = \"NULL\"\n", "    final_df = final_df.append(df, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.groupby(\"facility_id\")[\"complaint_type_final\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_size = 500\n", "# for loc in range(200, 700, 50):\n", "#     try:\n", "#         audit_number = predictions_v1_v2[:loc][\"complaint_type_final\"].sum()\n", "#         if (audit_number > 250) and (audit_number < 310):\n", "#             final_size = loc\n", "#             final_audit_number = predictions_v1_v2[:final_size][\n", "#                 \"complaint_type_final\"\n", "#             ].sum()\n", "#     except:\n", "#         break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# predictions_v1_v2[final_size:][\"complaint_prediction\"] = 0\n", "# predictions_v1_v2[final_size:][\"complaint_type\"] = \"NULL\"\n", "# predictions_v1_v2[final_size:][\"complaint_type_final\"] = \"NULL\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.groupby(\"facility_id\")[\"order_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# predictions_v1_v2.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_attributes = [\n", "    \"delivery_slot_start\",\n", "    \"order_id\",\n", "    \"suborder_ids\",\n", "    \"grocery_suborder_ids\",\n", "    \"cold_suborder_ids\",\n", "    \"large_suborder_ids\",\n", "    \"cart_id\",\n", "    \"customer_id\",\n", "    \"net_cost\",\n", "    \"sku\",\n", "    \"ipo\",\n", "    \"weight\",\n", "    \"min_product_weight(gms)\",\n", "    \"station\",\n", "    \"slot_type\",\n", "    \"dp_id\",\n", "    \"dp_name\",\n", "    \"facility_id\",\n", "    \"facility_name\",\n", "    \"complaint_prediction\",\n", "    \"complaint_type\",\n", "    \"complaint_type_final\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df = final_df[table_attributes]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.loc[report_df.complaint_prediction == 0, \"complaint_type_final\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.slot_type = report_df.slot_type.map({0: \"Slot A\", 1: \"Slot B\"})\n", "report_df.complaint_prediction = report_df.complaint_prediction.map({0: \"No\", 1: \"Yes\"})\n", "report_df.complaint_type_final = report_df.complaint_type_final.map(\n", "    {0: \"other complaint\", 1: \"damage/missing\", \"NULL\": \"NULL\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.complaint_type_final.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.station.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_df = report_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# report_df.to_csv('sample_report_cmp_pred_v2.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# report_df.to_csv(\"/tmp/\" + str(date_now) +\"_\"+ str(fac_name) +\"_\"+ str(slot_name) + \"_complaints_prediction_v2.csv\", index=False )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# \"/tmp/\" + str(date_now) +\"_\"+ str(fac_name) +\"_\"+ str(slot_name) + \"_complaints_prediction_v2.csv\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Email to stakeholders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.rename(\n", "    columns={\"complaint_type_final\": \"complaint_prediction_type\"}, inplace=True\n", ")\n", "report_df.drop(columns=[\"complaint_type\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df[\"suborder_ids\"] = report_df.apply(\n", "    lambda row: \",\".join(map(str, row.suborder_ids)), axis=1\n", ")\n", "report_df[\"grocery_suborder_ids\"] = report_df.apply(\n", "    lambda row: \",\".join(map(str, row.grocery_suborder_ids)), axis=1\n", ")\n", "report_df[\"cold_suborder_ids\"] = report_df.apply(\n", "    lambda row: \",\".join(map(str, row.cold_suborder_ids)), axis=1\n", ")\n", "report_df[\"large_suborder_ids\"] = report_df.apply(\n", "    lambda row: \",\".join(map(str, row.large_suborder_ids)), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df[\"min_product_weight(gms)\"] = report_df.apply(\n", "    lambda row: round(row[\"min_product_weight(gms)\"], 3), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.complaint_prediction_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.drop_duplicates().shape, report_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# report_df['cold_suborder_ids'][0] == ''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df.facility_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dasna_jaipur_g3_m2_ids = [92, 15, 26, 48]\n", "kol_blr_lko_ids = [24, 34, 3, 30]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if check_audit_type == 1:\n", "#     dasna_jaipur_g3_m2_df = report_df.loc[\n", "#         report_df.facility_id.isin(dasna_jaipur_g3_m2_ids)\n", "#     ]\n", "#     kol_blr_lko_df = report_df.loc[report_df.facility_id.isin(kol_blr_lko_ids)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if check_audit_type == 1:\n", "    jaipur_df = report_df.loc[report_df.facility_id == 15]\n", "    lko_df = report_df.loc[report_df.facility_id == 30]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# report_df.to_csv('sam.csv', index=False)\n", "if check_audit_type == 0:\n", "    report_df.to_csv(\n", "        \"/tmp/\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\",\n", "        index=False,\n", "    )\n", "    files = [\n", "        \"/tmp/\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\"\n", "    ]\n", "\n", "\n", "elif check_audit_type == 1:\n", "    jaipur_df.to_csv(\n", "        \"/tmp/jaipur_\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\",\n", "        index=False,\n", "    )\n", "\n", "    lko_df.to_csv(\n", "        \"/tmp/lko_\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\",\n", "        index=False,\n", "    )\n", "\n", "    #     kol_blr_lko_df.to_csv(\n", "    #         \"kol_blr_lko_\"\n", "    #         + str(date_now)\n", "    #         + \"_\"\n", "    #         + str(curr_hour)\n", "    #         + \"_\"\n", "    #         + str(check_audit_type)\n", "    #         + \"_complaints_prediction_v2.csv\",\n", "    #         index=False,\n", "    #     )\n", "\n", "    files = [\n", "        \"/tmp/jaipur_\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\",\n", "        \"/tmp/lko_\"\n", "        + str(date_now)\n", "        + \"_\"\n", "        + str(curr_hour)\n", "        + \"_\"\n", "        + str(check_audit_type)\n", "        + \"_complaints_prediction_v2.csv\",\n", "        #         \"kol_blr_lko_\"\n", "        #         + str(date_now)\n", "        #         + \"_\"\n", "        #         + str(curr_hour)\n", "        #         + \"_\"\n", "        #         + str(check_audit_type)\n", "        #         + \"_complaints_prediction_v2.csv\",\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_name = [\"Jaipur\", \"Lucknow\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if check_audit_type == 0:  # FC_AUDITS\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "\n", "    subject = f\"Orders Leading to complaint ({date_now}) ({fac_name}) ({slot_name})\"\n", "\n", "    html_content = f\" Hi, <br><br>PFA the list of orders that would lead to complaint along with their types.<br>\\\n", "    Delivery date : <b>{date_now}</b><br>\\\n", "    Slot : <b>{slot_name}</b><br>\\\n", "    Facility : <b>{fac_name}</b><br>\\\n", "    <br> <br> Thanks & Regards,<br> <PERSON><PERSON><PERSON><p>\"\n", "\n", "elif check_audit_type == 1:  # LM_AUDITS\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "\n", "    subject = f\"Orders Leading to complaint ({date_now})\"\n", "\n", "    html_content = f\" Hi, <br><br>PFA the list of orders that would lead to complaint along with their types.<br>\\\n", "    Delivery date : <b>{date_now}</b><br>\\\n", "    Facility : <b>{tuple(fac_name)}</b><br>\\\n", "    <br> <br> Thanks & Regards,<br> <PERSON><PERSON><PERSON><p>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "# to_email = [\"<EMAIL>\"]\n", "pb.send_email(from_email, to_email, subject, html_content, files=files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Push to redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(frame):\n", "\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    frame = frame.astype({\"net_cost\": \"int64\", \"ipo\": \"int64\", \"sku\": \"int64\"})\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"delivery_slot_start\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"delivery slot start time\",\n", "        },\n", "        {\"name\": \"order_id\", \"type\": \"bigint\", \"description\": \"order id\"},\n", "        {\"name\": \"suborder_ids\", \"type\": \"varchar\", \"description\": \"suborder ids\"},\n", "        {\n", "            \"name\": \"grocery_suborder_ids\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"grocery suborder ids\",\n", "        },\n", "        {\n", "            \"name\": \"cold_suborder_ids\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cold suborder ids\",\n", "        },\n", "        {\n", "            \"name\": \"large_suborder_ids\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"large suborder ids\",\n", "        },\n", "        {\"name\": \"cart_id\", \"type\": \"bigint\", \"description\": \"cart id\"},\n", "        {\"name\": \"customer_id\", \"type\": \"bigint\", \"description\": \"customer id\"},\n", "        {\"name\": \"net_cost\", \"type\": \"bigint\", \"description\": \"net cost\"},\n", "        {\"name\": \"sku\", \"type\": \"bigint\", \"description\": \"sku\"},\n", "        {\"name\": \"ipo\", \"type\": \"bigint\", \"description\": \"ipo\"},\n", "        {\"name\": \"weight\", \"type\": \"float\", \"description\": \"weight\"},\n", "        {\n", "            \"name\": \"min_product_weight(gms)\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weight of the lightest product in that order\",\n", "        },\n", "        {\"name\": \"station\", \"type\": \"varchar\", \"description\": \"station\"},\n", "        {\"name\": \"slot_type\", \"type\": \"varchar\", \"description\": \"slot type\"},\n", "        {\"name\": \"dp_id\", \"type\": \"varchar\", \"description\": \"DP id\"},\n", "        {\"name\": \"dp_name\", \"type\": \"varchar\", \"description\": \"DP name\"},\n", "        {\"name\": \"facility_id\", \"type\": \"bigint\", \"description\": \"facility id\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility name\"},\n", "        {\n", "            \"name\": \"complaint_prediction\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"binary predictions\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"multi label predictions \",\n", "        },\n", "        {\n", "            \"name\": \"complaint_type_final\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"meaning of complaint_type column values\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"updated at time\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"complaints_prediction_multi_label\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"order_id\"],\n", "        \"sortkey\": [\"order_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Complaints prediction multi label model\",\n", "    }\n", "\n", "    pb.to_redshift(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# String length exceeds DDL length  error\n", "\n", "redshift_df[\"suborder_ids\"] = \"---\"\n", "redshift_df[\"grocery_suborder_ids\"] = \"---\"\n", "redshift_df[\"cold_suborder_ids\"] = \"---\"\n", "redshift_df[\"large_suborder_ids\"] = \"---\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(redshift_df) != 0:\n", "    push_to_redshift(redshift_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}