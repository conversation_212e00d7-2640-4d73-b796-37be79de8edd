{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scikit-learn==0.21.3\n", "!pip install xgboost==1.2.1\n", "!pip install joblib==0.14.1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "import random\n", "\n", "import xgboost\n", "from xgboost import XGBClassifier\n", "import joblib\n", "\n", "import zipfile\n", "import os\n", "import pencilbox as pb\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_now = (datetime.utcnow() + timedelta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id = (26, 30, 39, 48, 92)\n", "\n", "facility_id_mapping = {\"g3\": 26, \"lucknow\": 30, \"kundli\": 39, \"mumbai\": 48, \"dasna\": 92}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Collection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch Backend Merchants"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_query = f\"\"\"\n", "WITH base AS\n", "(\n", "SELECT DISTINCT\n", "    pco.facility_id,\n", "    cf.name AS facility_name,\n", "    pco.id AS outlet_id,\n", "    pco.name AS outlet_name,\n", "    bom.external_id AS backend_merchant_id,\n", "    bom.name AS backend_merchant_name\n", "FROM lake_oms_bifrost.oms_merchant AS bom\n", "INNER JOIN lake_retail.console_outlet_cms_store AS cms\n", "    ON bom.external_id = cms.cms_store \n", "    AND cms.active = 1 \n", "    AND cms.cms_update_active = 1\n", "    AND virtual_merchant_type <> 'superstore_merchant'\n", "INNER JOIN lake_retail.view_console_outlet AS PCO\n", "    ON cms.outlet_id = pco.id\n", "INNER JOIN lake_crates.facility AS cf \n", "    ON cf.id = pco.facility_id\n", "WHERE pco.facility_id  in {facility_id}\n", ")\n", "SELECT DISTINCT\n", "    backend_merchant_id, facility_id, facility_name\n", "FROM base\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping = pd.read_sql_query(sql=merchant_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_ids = tuple(merchant_mapping.backend_merchant_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_query = f\"\"\"\n", "SELECT DISTINCT \n", "      CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_START * INTERVAL '1 SECOND'))::DATE as delivery_date,\n", "       OC.ID AS CART_ID,\n", "       OS.order_id,\n", "       OO.net_cost,\n", "       OO.customer_id,\n", "       OO.station,\n", "       OM.external_id AS backend_merchant_id,\n", "       json_extract_path_text(dp.meta,'unique_name',true) as dp_id,\n", "        case when Extract(hrs from (lmo.delivery_slot_start_time AT TIME ZONE 'UTC') AT TIME ZONE 'Asia/Kolkata') < 14 then 'Slot A'\n", "        else 'Slot B' end as slot_type,\n", "        lmo.distribution_point_id,\n", "        dp.name as dp_name\n", "       FROM lake_oms_bifrost.oms_suborder AS OS\n", "       INNER JOIN lake_oms_bifrost.oms_order AS OO ON OS.ORDER_ID = OO.ID\n", "       AND OS.TYPE = 'RetailSuborder'\n", "       AND OS.CURRENT_STATUS <> 'CANCELLED'\n", "       AND OO.TYPE = 'RetailForwardOrder'\n", "       AND OO.CUSTOMER_ID <> 673235\n", "       INNER JOIN lake_oms_bifrost.oms_cart AS OC ON OO.CART_ID = OC.ID\n", "       INNER JOIN lake_oms_bifrost.oms_merchant OM ON os.backend_merchant_id = om.id\n", "       AND OM.external_id in {backend_ids}\n", "       LEFT JOIN lake_last_mile.order lmo on lmo.external_order_id  = OS.order_id\n", "       LEFT JOIN lake_last_mile.order_item loi on loi.order_id = lmo.id\n", "       LEFT JOIN lake_last_mile.distribution_point dp ON dp.id = lmo.actual_dp_id\n", "    WHERE\n", "    CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_START * INTERVAL '1 SECOND'))::DATE = '{date_now}'\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11\n", "    \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.read_sql_query(sql=order_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data.station.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fetch weight, ipo and sku"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_query = f\"\"\"\n", "       SELECT\n", "        oo.id as order_id,\n", "        sum(loi.quantity) ipo,\n", "        count(distinct loi.product_id) as sku,\n", "        sum(case when loi.quantity > 0 and loi.total_weight/loi.quantity > 50 then cast(loi.total_weight as decimal(18,2))/1000\n", "        else loi.total_weight end) as weight\n", "        from lake_oms_bifrost.oms_order oo\n", "        inner join \n", "        lake_last_mile.order lmo\n", "        on oo.id = lmo.external_order_id\n", "        AND OO.TYPE = 'RetailForwardOrder'\n", "        AND OO.CUSTOMER_ID <> 673235\n", "        and CONVERT_TIMEZONE('ASIA/KOLKATA',(TIMESTAMP 'EPOCH' + OO.SLOT_START * INTERVAL '1 SECOND'))::DATE = '{date_now}'\n", "        and oo.id in {tuple(orders_data.order_id.unique())}\n", "        inner join lake_last_mile.order_item loi\n", "        on loi.order_id = lmo.id\n", "        GROUP BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_data = pd.read_sql_query(sql=order_config_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_config_data.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data, order_config_data, how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FC Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    merchant_mapping,\n", "    how=\"left\",\n", "    left_on=\"backend_merchant_id\",\n", "    right_on=\"backend_merchant_id\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Products in cart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_query = f\"\"\"\n", "with CATEGORY_PRODUCT_TREE AS\n", "    (\n", "    SELECT DISTINCT\n", "       grp.ID as product_id,\n", "        L1_GRC.ID AS L1_CATEGORY_ID,\n", "         L1_GRC.NAME AS L1_CATEGORY_NAME,\n", "         L2_GRC.ID AS L2_CATEGORY_ID,\n", "         L2_GRC.NAME AS L2_CATEGORY_NAME,\n", "         L3_GRC.ID AS L3_CATEGORY_ID,\n", "         L3_GRC.NAME L3_CATEGORY_NAME,\n", "         grp.name as product_name,\n", "         handling_type,\n", "         storage_type,\n", "         CASE WHEN STORAGE_TYPE IN ('refrigerated','deep_freeze')\n", "         or upper(L1_GRC.NAME) like '%%FROZEN%%'\n", "       or upper(L2_GRC.NAME) like '%%FROZEN%%'\n", "       or upper(L3_GRC.NAME) like '%%FROZEN%%'\n", "         THEN 'Cold'\n", "       ELSE(CASE WHEN HANDLING_TYPE = 'large' THEN 'Large'\n", "                     ELSE 'Grocery'\n", "                 END)\n", "       END AS GENERAL_CATEGORY\n", "    FROM lake_cms.gr_product AS grp\n", "    INNER JOIN lake_cms.gr_product_category_mapping AS PCM\n", "        ON grp.ID = PCM.PRODUCT_ID\n", "        AND PCM.IS_PRIMARY = 'TRUE'\n", "    INNER JOIN lake_cms.gr_category  AS L3_GRC\n", "         ON PCM.CATEGORY_ID = L3_GRC.ID\n", "    INNER JOIN lake_cms.gr_category AS L2_GRC\n", "       ON L3_GRC.PARENT_CATEGORY_ID = L2_GRC.ID\n", "    INNER JOIN lake_cms.gr_category AS L1_GRC\n", "       ON L2_GRC.PARENT_CATEGORY_ID = L1_GRC.ID\n", "\n", "    ORDER BY 1,3,5,7\n", "    )\n", "\n", "select lo.order_id, lo.product_id, ct.general_category,  ct.product_name, ct.L1_CATEGORY_NAME, ct.L2_CATEGORY_NAME, ct.L3_CATEGORY_NAME  from lake_oms_bifrost.oms_order_item lo \n", "LEFT JOIN CATEGORY_PRODUCT_TREE ct on lo.product_id = ct.product_id where lo.order_id in {tuple(orders_data.order_id.unique())}\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = pd.read_sql_query(sql=products_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.dropna(subset=[\"product_id\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Item Product Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_query = f\"\"\"\n", "\n", "select item_id, product_id from lake_rpc.item_product_mapping\n", "where product_id in {tuple(products_data.product_id.unique())}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = pd.read_sql_query(sql=mapping_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = item_product_mapping.dropna(subset=[\"item_id\"]).reset_index(\n", "    drop=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pl_query = f\"\"\"\n", "select item_id, perishable, is_pl from lake_rpc.product_product\n", "where item_id in {tuple(item_product_mapping.item_id.unique())}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_products = pd.read_sql_query(sql=pl_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product_mapping = pd.merge(\n", "    item_product_mapping,\n", "    perishable_products,\n", "    how=\"left\",\n", "    left_on=\"item_id\",\n", "    right_on=\"item_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = pd.merge(\n", "    products_data,\n", "    item_product_mapping,\n", "    how=\"left\",\n", "    left_on=\"product_id\",\n", "    right_on=\"product_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.drop(\n", "    [\"item_id\", \"l2_category_name\", \"l3_category_name\"], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data[\"perishable\"] = products_data.perishable.fillna(0)\n", "products_data[\"is_pl\"] = products_data.is_pl.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data[\"perishable\"] = products_data.perishable.apply(lambda x: 1 if x else 0)\n", "products_data[\"is_pl\"] = products_data.is_pl.apply(lambda x: 1 if x else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data[~products_data.l1_category_name.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["products_data = products_data.drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetching Historical Orders and Complaints of customers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_num_orders = f\"\"\"\n", "SELECT oo.customer_id, count(distinct(oo.id)) AS previous_order_count\n", "FROM lake_oms_bifrost.oms_suborder AS OS\n", "    INNER JOIN lake_oms_bifrost.oms_order AS OO ON OS.ORDER_ID = OO.ID\n", "       INNER JOIN lake_oms_bifrost.oms_merchant OM ON os.backend_merchant_id = om.id\n", "       AND OM.external_id in {backend_ids}\n", "AND OO.TYPE = 'RetailForwardOrder'\n", "AND OS.TYPE = 'RetailSuborder'\n", "AND OO.CUSTOMER_ID <> 673235\n", "WHERE \n", "  oo.customer_id in {tuple(orders_data.customer_id.unique())}\n", "  AND OO.CURRENT_STATUS IN ('DELIVERED')\n", "  AND (oo.INSTALL_TS + interval '5.5 hour')::date < '{date_now}'\n", "  GROUP BY 1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_orders = pd.read_sql_query(sql=hist_num_orders, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_complaints = f\"\"\"\n", "select customer_id, count(distinct cart_id) as previous_complaints from metrics.complaints\n", "where customer_id in  {tuple(orders_data.customer_id.unique())}\n", "and complaint_date <='{date_now}':: timestamp - interval '1 day'\n", "GROUP BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_complaints = pd.read_sql_query(sql=hist_complaints, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["historical_complaints.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    historical_orders,\n", "    how=\"left\",\n", "    left_on=\"customer_id\",\n", "    right_on=\"customer_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data = pd.merge(\n", "    orders_data,\n", "    historical_complaints,\n", "    how=\"left\",\n", "    left_on=\"customer_id\",\n", "    right_on=\"customer_id\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preprocessing the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"delivery_date\"] = pd.to_datetime(orders_data[\"delivery_date\"])\n", "orders_data[\"month\"] = orders_data.delivery_date.dt.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"previous_complaints\"] = orders_data.previous_complaints.fillna(0)\n", "orders_data[\"previous_order_count\"] = orders_data.previous_order_count.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cal_ratio(row):\n", "    try:\n", "        complaint_order_ratio = row[\"previous_complaints\"] / row[\"previous_order_count\"]\n", "\n", "        if complaint_order_ratio > 1:\n", "            return 1\n", "        else:\n", "            return complaint_order_ratio\n", "    except:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"complaint_order_ratio\"] = orders_data.apply(cal_ratio, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_data[\"complaint_order_ratio\"] = orders_data.complaint_order_ratio.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishables_per_order = (\n", "    products_data.groupby(\"order_id\")\n", "    .agg({\"perishable\": \"sum\", \"is_pl\": \"sum\"})\n", "    .reset_index()\n", "    .rename({\"perishable\": \"num_perishable\", \"is_pl\": \"num_pl\"}, axis=1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "\n", "enc = OneHotEncoder(handle_unknown=\"ignore\", drop=None)\n", "l1_categories = enc.fit_transform(products_data[[\"l1_category_name\"]]).toarray()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_categories = pd.DataFrame(l1_categories)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data = pd.concat([products_data[[\"order_id\"]], l1_categories], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data.columns = [\"order_id\"] + enc.categories_[0].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_products_data = (\n", "    l1_products_data.groupby(\"order_id\")\n", "    .agg(\n", "        {\n", "            \"Biscuits, Snacks & Chocolates\": \"sum\",\n", "            \"Breakfast & Dairy\": \"sum\",\n", "            \"Grocery & Staples\": \"sum\",\n", "            \"Home Furnishing and Decor\": \"sum\",\n", "            \"Home Improvement and Accessories\": \"sum\",\n", "            \"Household Items\": \"sum\",\n", "            \"Kitchen and Dining Needs\": \"sum\",\n", "            \"Noodles, Sauces & Instant Food\": \"sum\",\n", "            \"Specials\": \"sum\",\n", "            \"Vegetables & Fruits\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["enc = OneHotEncoder(handle_unknown=\"ignore\", drop=None)\n", "general_categories = enc.fit_transform(products_data[[\"general_category\"]]).toarray()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["general_categories = pd.DataFrame(general_categories)\n", "general_categories = pd.concat(\n", "    [products_data[[\"order_id\"]], general_categories], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["general_categories.columns = [\"order_id\"] + enc.categories_[0].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_large_count = (\n", "    general_categories.groupby(\"order_id\")\n", "    .agg({\"Cold\": \"sum\", \"Large\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_df1 = pd.merge(\n", "    orders_data,\n", "    perishables_per_order,\n", "    left_on=\"order_id\",\n", "    right_on=\"order_id\",\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_df2 = pd.merge(\n", "    temp_df1, l1_products_data, left_on=\"order_id\", right_on=\"order_id\", how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    temp_df2, cold_large_count, how=\"left\", left_on=\"order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = test_data.drop_duplicates(subset=[\"order_id\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Number of orders are {test_data.order_id.nunique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Number of rows with null ipo are {test_data.ipo.isna().sum()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove rows having null ipo\n", "na_rows = test_data[test_data.ipo.isna()].reset_index(drop=True)\n", "na_rows[\"prediction\"] = 0\n", "\n", "na_rows[\"ipo\"] = na_rows.ipo.fillna(0)\n", "na_rows[\"sku\"] = na_rows.sku.fillna(0)\n", "na_rows[\"weight\"] = na_rows.weight.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = test_data[~test_data.order_id.isin(na_rows.order_id)].reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading the artifacts and predicting on the data fetched"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "local_filename = \"/tmp/artifacts.zip\"\n", "cloud_filename = \"pencilbox/complaint_prediction/v1/artifacts/artifacts_v1\"\n", "\n", "dir_name = \"artifacts\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.from_s3(bucket_name, cloud_filename, local_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with zipfile.ZipFile(local_filename, \"r\") as zip_ref:\n", "    if not os.path.exists(dir_name):\n", "        os.makedirs(dir_name)\n", "        zip_ref.extractall(dir_name)\n", "    else:\n", "        zip_ref.extractall(dir_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xgb_mumbai = joblib.load(dir_name + \"/xgb_mumbai\")\n", "xgb_lucknow = joblib.load(dir_name + \"/xgb_lucknow\")\n", "xgb_dasna = joblib.load(dir_name + \"/xgb_dasna\")\n", "\n", "xgb_g3 = joblib.load(dir_name + \"/xgb_g3\")\n", "xgb_kundli = joblib.load(dir_name + \"/xgb_kundli\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["featurised_month = pd.read_csv(dir_name + \"/featurised_month.csv\")\n", "featurised_station = pd.read_csv(dir_name + \"/featurised_station.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    test_data,\n", "    featurised_month,\n", "    how=\"left\",\n", "    left_on=[\"month\", \"facility_id\"],\n", "    right_on=[\"month\", \"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data = pd.merge(\n", "    test_data,\n", "    featurised_station,\n", "    how=\"left\",\n", "    left_on=[\"station\", \"facility_id\"],\n", "    right_on=[\"station\", \"facility_id\"],\n", ").reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_data[\"month_enc1\"] = test_data.month_enc1.fillna(0.5)\n", "test_data[\"month_enc2\"] = test_data.month_enc2.fillna(0.5)\n", "\n", "test_data[\"station_enc1\"] = test_data.station_enc1.fillna(0.5)\n", "test_data[\"station_enc2\"] = test_data.station_enc2.fillna(0.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features = [\n", "    \"complaint_order_ratio\",\n", "    \"num_perishable\",\n", "    \"Kitchen and Dining Needs\",\n", "    \"ipo\",\n", "    \"Noodles, Sauces & Instant Food\",\n", "    \"Home Improvement and Accessories\",\n", "    \"Home Furnishing and Decor\",\n", "    \"Specials\",\n", "    \"month_enc1\",\n", "    \"month_enc2\",\n", "    \"weight\",\n", "    \"Grocery & Staples\",\n", "    \"Breakfast & Dairy\",\n", "    \"station_enc1\",\n", "    \"station_enc2\",\n", "    \"Cold\",\n", "    \"Large\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_proba_config = {26: 0.66, 30: 0.61, 39: 0.69, 48: 0.64, 92: 0.56}\n", "\n", "facility_model_config = {\n", "    26: xgb_g3,\n", "    30: xgb_lucknow,\n", "    39: xgb_k<PERSON><PERSON>,\n", "    48: xgb_mumbai,\n", "    92: xgb_<PERSON>na,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_predictions(facility_id, df, model_features):\n", "\n", "    y_pred = facility_model_config[facility_id].predict(df[model_features])\n", "    y_proba = facility_model_config[facility_id].predict_proba(df[model_features])\n", "\n", "    predictions = [\n", "        1 if pred[1] >= facility_proba_config[facility_id] else 0 for pred in y_proba\n", "    ]\n", "    df[\"prediction\"] = predictions\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for facility_id, group in test_data.groupby(\"facility_id\"):\n", "\n", "    facility_pred = get_predictions(facility_id, group, features)\n", "    predictions = predictions.append(facility_pred, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_attributes = [\n", "    \"delivery_date\",\n", "    \"order_id\",\n", "    \"cart_id\",\n", "    \"customer_id\",\n", "    \"net_cost\",\n", "    \"sku\",\n", "    \"ipo\",\n", "    \"weight\",\n", "    \"station\",\n", "    \"slot_type\",\n", "    \"dp_id\",\n", "    \"dp_name\",\n", "    \"facility_id\",\n", "    \"facility_name\",\n", "    \"prediction\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_df = predictions[table_attributes]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_df = redshift_df.append(na_rows[table_attributes], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_df = redshift_df.rename({\"prediction\": \"complaint_prediction\"}, axis=1)\n", "redshift_df[\"complaint_prediction\"] = redshift_df.complaint_prediction.apply(\n", "    lambda x: \"Yes\" if x == 1 else \"No\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# g3_kundli_predictions = redshift_df[\n", "#     (redshift_df[\"facility_id\"] == 26) | (redshift_df[\"facility_id\"] == 39)\n", "# ].reset_index(drop=True)\n", "\n", "# mum_dasna_lk_predictions = redshift_df[\n", "#     (redshift_df[\"facility_id\"] == 30)\n", "#     | (redshift_df[\"facility_id\"] == 48)\n", "#     | (redshift_df[\"facility_id\"] == 92)\n", "# ].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# g3_kundli_predictions.to_csv(\n", "#     \"/tmp/\" + date_now + \"_g3_kundli_complaints_prediction.csv\", index=False\n", "# )\n", "\n", "# mum_dasna_lk_predictions.to_csv(\n", "#     \"/tmp/\" + date_now + \"_mum_dasna_lk_complaints_prediction.csv\", index=False\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Email to Stakeholders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email = \"<EMAIL>\"\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# subject = f\"Orders leading to complaint ({date_now})\"\n", "\n", "# html_content = f\" Dear Sir, <br><br>PFA the list of orders that would lead to complaint and are mapped to <b> G3, Kundli, Lucknow, Mumbai2 and Dasna2 Warehouses</b>.<br>\\\n", "# The delivery date for orders is <b>{date_now}</b><br>\\\n", "# <br> <br> <PERSON><PERSON>,<br> <PERSON><PERSON><PERSON><PERSON><p>\"\n", "\n", "# pb.send_email(\n", "#     from_email,\n", "#     to_email,\n", "#     subject,\n", "#     html_content,\n", "#     files=[\n", "#         \"/tmp/\" + date_now + \"_g3_kundli_complaints_prediction.csv\",\n", "#         \"/tmp/\" + date_now + \"_mum_dasna_lk_complaints_prediction.csv\",\n", "#     ],\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Push predictions to redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_redshift(frame):\n", "\n", "    frame[\"updated_at\"] = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    frame = frame.astype({\"net_cost\": \"int64\", \"ipo\": \"int64\", \"sku\": \"int64\"})\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"delivery_date\", \"type\": \"timestamp\"},\n", "        {\"name\": \"order_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"cart_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"customer_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"net_cost\", \"type\": \"bigint\"},\n", "        {\"name\": \"sku\", \"type\": \"bigint\"},\n", "        {\"name\": \"ipo\", \"type\": \"bigint\"},\n", "        {\"name\": \"weight\", \"type\": \"float\"},\n", "        {\"name\": \"station\", \"type\": \"varchar\"},\n", "        {\"name\": \"slot_type\", \"type\": \"varchar\"},\n", "        {\"name\": \"dp_id\", \"type\": \"varchar\"},\n", "        {\"name\": \"dp_name\", \"type\": \"varchar\"},\n", "        {\"name\": \"facility_id\", \"type\": \"bigint\"},\n", "        {\"name\": \"facility_name\", \"type\": \"varchar\"},\n", "        {\"name\": \"complaint_prediction\", \"type\": \"varchar\"},\n", "        {\"name\": \"updated_at\", \"type\": \"timestamp\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"complaints_prediction_v2\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"order_id\"],\n", "        \"sortkey\": [\"order_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",\n", "    }\n", "\n", "    pb.to_redshift(frame, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(redshift_df) != 0:\n", "    push_to_redshift(redshift_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}