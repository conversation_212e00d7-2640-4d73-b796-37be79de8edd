{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -U pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import requests\n", "import sqlalchemy as sqla\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import pencilbox as pb\n", "from dateutil import rrule\n", "from datetime import datetime, timedelta\n", "import json\n", "from pandas.io.json import json_normalize"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["rundate = datetime(2021, 3, 31)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_date = pd.to_datetime(rundate)\n", "print(\"rundate\", rundate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select *\n", "from return_flow_task_properties_info rf\n", "where  return_flow_task_id in \n", "(\n", "    select distinct id\n", "    from return_flow_task\n", "    where date(convert_tz(completed_at ,\"+00:00\",'+05:30')) = date('{rundate}')\n", "        and return_flow_task_state = 'COMPLETED'\n", ")\n", "and return_flow_task_collected_properties <> ''\n", "\"\"\".format(\n", "    rundate=run_date\n", ")\n", "con = pb.get_connection(\"last_mile\")\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"no. of return tasks:\", df[\"return_flow_task_id\"].nunique())\n", "print(\"df.shape\", df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_expected = df.loc[\n", "    :,\n", "    [\n", "        \"id\",\n", "        \"return_flow_task_expected_properties\",\n", "        \"return_flow_order_id\",\n", "        \"return_flow_task_id\",\n", "        \"created_at\",\n", "        \"updated_at\",\n", "    ],\n", "]\n", "df_expected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"shape\", df_expected.shape)\n", "print(\"dtypes:-\")\n", "df_expected.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_expected[\"return_flow_task_expected_properties\"] = df_expected[\n", "    \"return_flow_task_expected_properties\"\n", "].apply(lambda x: json.loads(x)[\"returnFlowOrderItemList\"])\n", "df_expected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"shape\", df_expected.shape)\n", "print(\"dtypes:-\")\n", "df_expected.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_expected = df_expected.explode(\"return_flow_task_expected_properties\")\n", "df_expected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    \"rows with nulls in json field: \",\n", "    len(\n", "        df_expected[df_expected[\"return_flow_task_expected_properties\"].isnull()].index\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_expected.drop(\n", "    df_expected[df_expected[\"return_flow_task_expected_properties\"].isnull()].index,\n", "    inplace=True,\n", ")\n", "print(\n", "    \"rows with nulls in json field: \",\n", "    len(\n", "        df_expected[df_expected[\"return_flow_task_expected_properties\"].isnull()].index\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"shape\", df_expected.shape)\n", "print(\"dtypes:-\")\n", "df_expected.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_exp = df_expected.copy(deep=True)\n", "df_exp[\"expected_qty\"] = df_exp[\"return_flow_task_expected_properties\"].apply(\n", "    lambda x: x[\"collectedQuantity\"]\n", ")\n", "df_exp[\"expected_returnFlowOrderItemId\"] = df_exp[\n", "    \"return_flow_task_expected_properties\"\n", "].apply(lambda x: x[\"returnFlowOrderItemId\"])\n", "df_exp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df_exp[\"return_flow_task_expected_properties\"]\n", "df_exp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_collected = df.loc[\n", "    :,\n", "    [\n", "        \"id\",\n", "        \"return_flow_task_collected_properties\",\n", "        \"return_flow_order_id\",\n", "        \"return_flow_task_id\",\n", "    ],\n", "]\n", "df_collected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"shape\", df_collected.shape)\n", "print(\"dtypes:-\")\n", "df_collected.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_collected[\"return_flow_task_collected_properties\"] = df_collected[\n", "    \"return_flow_task_collected_properties\"\n", "].apply(lambda x: json.loads(x)[\"returnFlowOrderItemList\"])\n", "df_collected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_collected = df_collected.explode(\"return_flow_task_collected_properties\")\n", "df_collected.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    \"rows with null values in json field: \",\n", "    len(\n", "        df_collected[\n", "            df_collected[\"return_flow_task_collected_properties\"].isnull()\n", "        ].index\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_collected.drop(\n", "    df_collected[df_collected[\"return_flow_task_collected_properties\"].isnull()].index,\n", "    inplace=True,\n", ")\n", "print(\n", "    \"rows with null values in json field: \",\n", "    len(\n", "        df_collected[\n", "            df_collected[\"return_flow_task_collected_properties\"].isnull()\n", "        ].index\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"shape\", df_collected.shape)\n", "print(\"dtypes:-\")\n", "df_collected.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_coll = df_collected.copy(deep=True)\n", "df_coll[\"collected_qty\"] = df_coll[\"return_flow_task_collected_properties\"].apply(\n", "    lambda x: x[\"collectedQuantity\"]\n", ")\n", "df_coll[\"collected_returnFlowOrderItemId\"] = df_coll[\n", "    \"return_flow_task_collected_properties\"\n", "].apply(lambda x: x[\"returnFlowOrderItemId\"])\n", "df_coll.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df_coll[\"return_flow_task_collected_properties\"]\n", "df_coll.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"df_exp - dtypes:-\")\n", "print(df_exp.dtypes)\n", "print(\"df_coll - dtypes:-\")\n", "print(df_coll.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"df_exp.shape: \", df_exp.shape)\n", "print(\"df_coll.shape: \", df_coll.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final = pd.merge(\n", "    df_exp,\n", "    df_coll,\n", "    left_on=[\n", "        \"id\",\n", "        \"return_flow_order_id\",\n", "        \"return_flow_task_id\",\n", "        \"expected_returnFlowOrderItemId\",\n", "    ],\n", "    right_on=[\n", "        \"id\",\n", "        \"return_flow_order_id\",\n", "        \"return_flow_task_id\",\n", "        \"collected_returnFlowOrderItemId\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "df_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"df_final.shape & dtypes: \", df_final.shape)\n", "print(df_final.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df_final[\"collected_returnFlowOrderItemId\"]\n", "df_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    \"rows with null collected_qty\",\n", "    len(df_final[df_final[\"collected_qty\"].isnull()].index),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["u = df_final.select_dtypes(exclude=[\"datetime64\"])\n", "df_final[u.columns] = u.fillna(0)\n", "print(\n", "    \"rows with null collected_qty\",\n", "    len(df_final[df_final[\"collected_qty\"].isnull()].index),\n", ")\n", "df_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"rows with null created_at\", len(df_final[df_final[\"created_at\"].isnull()].index))\n", "print(\"rows with null updated_at\", len(df_final[df_final[\"updated_at\"].isnull()].index))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["u = df_final.select_dtypes(include=[\"datetime64\"])\n", "df_final[u.columns] = u.fillna(pd.to_datetime(\"1970-01-01\"))\n", "print(\"rows with null created_at\", len(df_final[df_final[\"created_at\"].isnull()].index))\n", "print(\"rows with null updated_at\", len(df_final[df_final[\"updated_at\"].isnull()].index))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"df_final.shape & dtypes: \", df_final.shape)\n", "print(df_final.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final.rename(\n", "    columns={\"expected_returnFlowOrderItemId\": \"return_flow_order_item_id\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"df_final.shape & dtypes: \", df_final.shape)\n", "print(df_final.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final[\"inc_key\"] = df_final[\"id\"].astype(str) + df_final[\n", "    \"return_flow_order_item_id\"\n", "].astype(str)\n", "df_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"lm_return_flow_task_completed_flat\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"return_flow_task_properties_info id\",\n", "        },\n", "        {\n", "            \"name\": \"return_flow_order_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"return_flow_task_properties_info return_flow_order_id\",\n", "        },\n", "        {\n", "            \"name\": \"return_flow_task_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"return_flow_task_properties_info return_flow_task_id\",\n", "        },\n", "        {\n", "            \"name\": \"created_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"return_flow_task_properties_info created_at\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"return_flow_task_properties_info updated_at\",\n", "        },\n", "        {\n", "            \"name\": \"expected_qty\",\n", "            \"type\": \"int\",\n", "            \"description\": \"return_flow_task_properties_info return_flow_task_expected_properties json key-collectedQuantity\",\n", "        },\n", "        {\n", "            \"name\": \"return_flow_order_item_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"return_flow_task_properties_info return_flow_task_expected_properties json key-returnFlowOrderItemId\",\n", "        },\n", "        {\n", "            \"name\": \"collected_qty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"return_flow_task_properties_info return_flow_task_collected_properties json key-collectedQuantity\",\n", "        },\n", "        {\"name\": \"inc_key\", \"type\": \"varchar\", \"description\": \"incremental key\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"id\",\n", "        \"return_flow_order_id\",\n", "        \"return_flow_task_id\",\n", "        \"return_flow_order_item_id\",\n", "    ],\n", "    \"sortkey\": [\"id\"],\n", "    \"incremental_key\": \"inc_key\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"falt table of json(return_flow_task_expected_properties,return_flow_task_collected_properties) from return_flow_task_properties_info\",\n", "}\n", "print(\"table_name:\", kwargs[\"table_name\"])\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "pb.to_redshift(df_final, **kwargs)\n", "print(\"pushed data into db\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}