{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import requests\n", "import sqlalchemy as sqla\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import time\n", "import pencilbox as pb\n", "import os\n", "import string, sys"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["s_date = datetime(2022, 8, 15)\n", "e_date = datetime(2022, 8, 18)\n", "print(\"s_date:\", s_date)\n", "print(\"e_date:\", e_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_date = pd.to_datetime(s_date)\n", "e_date = pd.to_datetime(e_date)\n", "print(\"s_date:\", s_date)\n", "print(\"e_date:\", e_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# collate complaints from email and chat\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"email_chat_query.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    crm_email_chat_sql = myfile.read()\n", "crm_email_chat_sql = crm_email_chat_sql.format(START_DATE=s_date, END_DATE=e_date)\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "df_crm_email_chat = pd.read_sql(sql=crm_email_chat_sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# complaints from pid_complaints data this includes doorstep and in app complaints\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"pid_complaints.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    PID_COMPLAINTS = myfile.read()\n", "PID_COMPLAINTS = PID_COMPLAINTS.format(START_DATE=s_date, END_DATE=e_date)\n", "con = pb.get_connection(\"redshift_etl\")\n", "df_pid_complaints = pd.read_sql(sql=PID_COMPLAINTS, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query to get details of orders like station,fc,delivery FE etc\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"base_query.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    base_table = myfile.read()\n", "base_table = base_table.format(\n", "    START_DATE=s_date,\n", "    END_DATE=e_date,\n", "    cart_id_list=tuple(\n", "        set(df_crm_email_chat.cart_id.unique()).union(\n", "            df_pid_complaints.cart_id.unique()\n", "        )\n", "    ),\n", ").replace(\",)\", \")\")\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "df_base_table = pd.read_sql(sql=base_table, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pid_base = (\n", "    df_pid_complaints[\n", "        [\n", "            \"slot_start\",\n", "            \"slot_end\",\n", "            \"cart_id\",\n", "            \"product_id\",\n", "            \"general_category\",\n", "            \"handling_type\",\n", "            \"storage_type\",\n", "            \"complaint_category\",\n", "            \"complaint_sub_category\",\n", "            \"complaint_date\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .merge(\n", "        df_base_table[\n", "            [\n", "                \"facility_id\",\n", "                \"facility_name\",\n", "                \"backend_merchant_id\",\n", "                \"backend_merchant_name\",\n", "                \"outlet_id\",\n", "                \"outlet_name\",\n", "                \"frontend_merchant_id\",\n", "                \"frontend_merchant_name\",\n", "                \"delivery_date\",\n", "                \"parent_id\",\n", "                \"order_type\",\n", "                \"suborder_id\",\n", "                \"customer_id\",\n", "                \"l1_category_id\",\n", "                \"l1_category_name\",\n", "                \"l2_category_id\",\n", "                \"l2_category_name\",\n", "                \"l3_category_id\",\n", "                \"l3_category_name\",\n", "                \"product_name\",\n", "                \"selling_price\",\n", "                \"ordered_quantity\",\n", "                \"procured_quantity\",\n", "                \"status\",\n", "                \"cart_id\",\n", "                \"product_id\",\n", "                \"slot_start\",\n", "                \"slot_end\",\n", "            ]\n", "        ].drop_duplicates(),\n", "        on=[\"cart_id\", \"product_id\", \"slot_start\", \"slot_end\"],\n", "    )\n", ")\n", "df_pid_base = df_pid_base.assign(\n", "    primary_key=(\n", "        df_pid_base.cart_id.astype(str)\n", "        + df_pid_base.product_id.astype(str)\n", "        + df_pid_base.slot_start.astype(str)\n", "        + df_pid_base.slot_end.astype(str)\n", "        + df_pid_base.complaint_category\n", "        + df_pid_base.complaint_sub_category\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# only email/chat complints against which there are no complaints in PID complaints are pushed to metrics.complaints\n", "df_crm_pid_base1 = df_crm_email_chat[\n", "    [\"complaint_category\", \"complaint_sub_category\", \"complaint_date\", \"cart_id\"]\n", "].merge(\n", "    df_pid_complaints[[\"cart_id\", \"product_id\"]].drop_duplicates(),\n", "    on=\"cart_id\",\n", "    how=\"left\",\n", ")\n", "df_crm_pid_base1 = df_crm_pid_base1[df_crm_pid_base1.product_id.isna()].drop(\n", "    \"product_id\", axis=1\n", ")\n", "\n", "df_crm_pid_base1 = df_crm_pid_base1.merge(df_base_table, on=\"cart_id\")[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"backend_merchant_id\",\n", "        \"backend_merchant_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"frontend_merchant_id\",\n", "        \"frontend_merchant_name\",\n", "        \"delivery_date\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"parent_id\",\n", "        \"order_type\",\n", "        \"suborder_id\",\n", "        \"customer_id\",\n", "        \"l1_category_id\",\n", "        \"l1_category_name\",\n", "        \"l2_category_id\",\n", "        \"l2_category_name\",\n", "        \"l3_category_id\",\n", "        \"l3_category_name\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"selling_price\",\n", "        \"general_category\",\n", "        \"handling_type\",\n", "        \"storage_type\",\n", "        \"ordered_quantity\",\n", "        \"procured_quantity\",\n", "        \"status\",\n", "        \"complaint_category\",\n", "        \"complaint_sub_category\",\n", "        \"complaint_date\",\n", "        \"cart_id\",\n", "    ]\n", "]\n", "\n", "\n", "df_crm_pid_base1.loc[\n", "    df_crm_pid_base1.complaint_sub_category.isna(), \"complaint_sub_category\"\n", "] = \"\"\n", "\n", "df_crm_pid_base1 = df_crm_pid_base1.assign(\n", "    primary_key=(\n", "        df_crm_pid_base1.cart_id.astype(str)\n", "        + df_crm_pid_base1.product_id.astype(str)\n", "        + df_crm_pid_base1.slot_start.astype(str)\n", "        + df_crm_pid_base1.slot_end.astype(str)\n", "        + df_crm_pid_base1.complaint_category\n", "        + df_crm_pid_base1.complaint_sub_category\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# incase there are Delay In Delivery','FE Related','MDND' complaints against complaints that already\n", "# exist in pid_complaints then we need to push this data to final complaints table\n", "df_crm_email_chat_pid = (\n", "    df_crm_email_chat[\n", "        df_crm_email_chat[\"complaint_sub_category\"].isin(\n", "            [\"Delay In Delivery\", \"FE Related\", \"MDND\"]\n", "        )\n", "    ]\n", "    .drop_duplicates()\n", "    .merge(df_pid_complaints[[\"cart_id\"]], on=[\"cart_id\"], how=\"inner\")\n", ")\n", "df_crm_pid_base2 = df_crm_email_chat_pid.merge(\n", "    df_base_table[\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"backend_merchant_id\",\n", "            \"backend_merchant_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"frontend_merchant_id\",\n", "            \"frontend_merchant_name\",\n", "            \"delivery_date\",\n", "            \"parent_id\",\n", "            \"order_type\",\n", "            \"suborder_id\",\n", "            \"customer_id\",\n", "            \"l1_category_id\",\n", "            \"l1_category_name\",\n", "            \"l2_category_id\",\n", "            \"l2_category_name\",\n", "            \"l3_category_id\",\n", "            \"l3_category_name\",\n", "            \"product_name\",\n", "            \"selling_price\",\n", "            \"general_category\",\n", "            \"handling_type\",\n", "            \"storage_type\",\n", "            \"ordered_quantity\",\n", "            \"procured_quantity\",\n", "            \"status\",\n", "            \"cart_id\",\n", "            \"product_id\",\n", "            \"slot_start\",\n", "            \"slot_end\",\n", "        ]\n", "    ].drop_duplicates(),\n", "    on=[\"cart_id\"],\n", "    how=\"inner\",\n", ")\n", "df_crm_pid_base2 = df_crm_pid_base2.assign(\n", "    primary_key=(\n", "        df_crm_pid_base2.cart_id.astype(str)\n", "        + df_crm_pid_base2.product_id.astype(str)\n", "        + df_crm_pid_base2.slot_start.astype(str)\n", "        + df_crm_pid_base2.slot_end.astype(str)\n", "        + df_crm_pid_base2.complaint_category\n", "        + df_crm_pid_base2.complaint_sub_category\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# collate complaints from all sources\n", "columns_list = [\n", "    \"facility_id\",\n", "    \"facility_name\",\n", "    \"backend_merchant_id\",\n", "    \"backend_merchant_name\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"frontend_merchant_id\",\n", "    \"frontend_merchant_name\",\n", "    \"delivery_date\",\n", "    \"slot_start\",\n", "    \"slot_end\",\n", "    \"cart_id\",\n", "    \"parent_id\",\n", "    \"order_type\",\n", "    \"suborder_id\",\n", "    \"customer_id\",\n", "    \"l1_category_id\",\n", "    \"l1_category_name\",\n", "    \"l2_category_id\",\n", "    \"l2_category_name\",\n", "    \"l3_category_id\",\n", "    \"l3_category_name\",\n", "    \"product_id\",\n", "    \"product_name\",\n", "    \"selling_price\",\n", "    \"general_category\",\n", "    \"handling_type\",\n", "    \"storage_type\",\n", "    \"ordered_quantity\",\n", "    \"procured_quantity\",\n", "    \"status\",\n", "    \"complaint_category\",\n", "    \"complaint_sub_category\",\n", "    \"complaint_date\",\n", "    \"primary_key\",\n", "]\n", "\n", "complaints_union = pd.concat(\n", "    [\n", "        df_pid_base[columns_list],\n", "        df_crm_pid_base1[columns_list],\n", "        df_crm_pid_base2[columns_list],\n", "    ]\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["complaints_union[\"complaint_date\"] = complaints_union[\"complaint_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")\n", "complaints_union[\"delivery_date\"] = complaints_union[\"delivery_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["complaints_union[complaints_union.cart_id == 237436481]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"test\",\n", "    \"table_name\": \"complaints_union\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"FC_MAPPING facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"facility_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING facility_name\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"oms_merchant backend_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"oms_merchant backend_merchant_name\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"FC_MAPPING outlet_id\"},\n", "        {\n", "            \"name\": \"outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING outlet_name\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"FC_MAPPING frontend_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING frontend_merchant_name\",\n", "        },\n", "        {\n", "            \"name\": \"delivery_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"order delivery_date\",\n", "        },\n", "        {\n", "            \"name\": \"slot_start\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"order slot_start\",\n", "        },\n", "        {\"name\": \"slot_end\", \"type\": \"timestamp\", \"description\": \"order slot_end\"},\n", "        {\"name\": \"cart_id\", \"type\": \"int\", \"description\": \"OMS cart_id\"},\n", "        {\"name\": \"parent_id\", \"type\": \"int\", \"description\": \"OS order_id\"},\n", "        {\"name\": \"order_type\", \"type\": \"varchar\", \"description\": \"OS order_id\"},\n", "        {\"name\": \"suborder_id\", \"type\": \"int\", \"description\": \"OMS suborder_id\"},\n", "        {\"name\": \"customer_id\", \"type\": \"int\", \"description\": \"OMS customer_id\"},\n", "        {\"name\": \"l1_category_id\", \"type\": \"int\", \"description\": \"gr_category ID\"},\n", "        {\n", "            \"name\": \"l1_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"gr_category category_id\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"l3_category_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"gr_category category_id\",\n", "        },\n", "        {\n", "            \"name\": \"l3_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"oms_order_item Product ID\",\n", "        },\n", "        {\n", "            \"name\": \"product_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"oms_order_item Product name\",\n", "        },\n", "        {\n", "            \"name\": \"selling_price\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_order_item selling_price\",\n", "        },\n", "        {\n", "            \"name\": \"general_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Grocery/cold/large\",\n", "        },\n", "        {\n", "            \"name\": \"handling_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_product handling_type\",\n", "        },\n", "        {\n", "            \"name\": \"storage_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_product storage_type\",\n", "        },\n", "        {\n", "            \"name\": \"ordered_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_suborder_item quantity\",\n", "        },\n", "        {\n", "            \"name\": \"procured_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_suborder_item procured_quantity\",\n", "        },\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"oms order status\"},\n", "        {\n", "            \"name\": \"complaint_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"crm/pid_complaints complaint_category\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_sub_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"crm/pid_complaints complaint_sub_category \",\n", "        },\n", "        {\n", "            \"name\": \"complaint_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"crm/pid_complaints complaint_date\",\n", "        },\n", "        {\n", "            \"name\": \"primary_key\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cart_id||suborder_id||product_id||slot_start||slot_end\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"cart_id\",\n", "        \"product_id\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"complaint_category\",\n", "        \"complaint_sub_category\",\n", "    ],\n", "    \"sortkey\": [\"cart_id\"],\n", "    \"incremental_key\": \"primary_key\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"complaints unionfor metrics.complaints \",\n", "}\n", "print(\"tablename:\", kwargs[\"table_name\"])\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "complaints_union[\"l1_category_id\"] = complaints_union[\"l1_category_id\"].astype(\"Int64\")\n", "complaints_union[\"l2_category_id\"] = complaints_union[\"l2_category_id\"].astype(\"Int64\")\n", "complaints_union[\"l3_category_id\"] = complaints_union[\"l3_category_id\"].astype(\"Int64\")\n", "start = time.time()\n", "pb.to_redshift(complaints_union, **kwargs)\n", "end = time.time()\n", "result = time.localtime(end - start)\n", "print(\"\\n time_min:\", result.tm_min)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"metric_complaints_query.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    complaints_dump = myfile.read()\n", "complaints_dump = complaints_dump.format(START_DATE=s_date, END_DATE=e_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")\n", "start = time.time()\n", "df_complaints_dump = pd.read_sql(sql=complaints_dump, con=con)\n", "print(\"min delivery date:\", df_complaints_dump[\"delivery_date\"].min())\n", "print(\"max delivery date:\", df_complaints_dump[\"delivery_date\"].max())\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"complaints\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"FC_MAPPING facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"facility_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING facility_name\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"oms_merchant backend_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"backend_merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"oms_merchant backend_merchant_name\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"FC_MAPPING outlet_id\"},\n", "        {\n", "            \"name\": \"outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING outlet_name\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"FC_MAPPING frontend_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"frontend_merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FC_MAPPING frontend_merchant_name\",\n", "        },\n", "        {\"name\": \"station\", \"type\": \"varchar\", \"description\": \"station name\"},\n", "        {\n", "            \"name\": \"delivery_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"order delivery_date\",\n", "        },\n", "        {\"name\": \"route_type\", \"type\": \"varchar\", \"description\": \"route_type\"},\n", "        {\"name\": \"route_id\", \"type\": \"varchar\", \"description\": \"route_id\"},\n", "        {\n", "            \"name\": \"routed_via\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"routed_via DC/DP/GSP\",\n", "        },\n", "        {\n", "            \"name\": \"delivery_slot_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"SLOT A/SLOT B\",\n", "        },\n", "        {\n", "            \"name\": \"delivery_slot\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"delivery_slot\",\n", "        },\n", "        {\n", "            \"name\": \"fe_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"GSP/DP/Grofers FE\",\n", "        },\n", "        {\"name\": \"fe_id\", \"type\": \"varchar\", \"description\": \"FE_id\"},\n", "        {\n", "            \"name\": \"employee_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"FE/gsp employee_id\",\n", "        },\n", "        {\"name\": \"fe_name\", \"type\": \"varchar\", \"description\": \"FE/gsp name\"},\n", "        {\"name\": \"customer_id\", \"type\": \"int\", \"description\": \"oms customer_id\"},\n", "        {\n", "            \"name\": \"customer_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"ABUSIVE/NON_ABUSIVE\",\n", "        },\n", "        {\"name\": \"cart_id\", \"type\": \"int\", \"description\": \"OMS cart_id\"},\n", "        {\"name\": \"order_id\", \"type\": \"int\", \"description\": \"OMS order_id\"},\n", "        {\"name\": \"order_type\", \"type\": \"varchar\", \"description\": \"OS order_id\"},\n", "        {\"name\": \"suborder_id\", \"type\": \"int\", \"description\": \"OMS suborder_id\"},\n", "        {\"name\": \"l1_category_id\", \"type\": \"int\", \"description\": \"gr_category ID\"},\n", "        {\n", "            \"name\": \"l1_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"gr_category category_id\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"l3_category_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"gr_category category_id\",\n", "        },\n", "        {\n", "            \"name\": \"l3_category_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_category category name\",\n", "        },\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"oms_order_item Product ID\",\n", "        },\n", "        {\n", "            \"name\": \"product_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"oms_order_item Product name\",\n", "        },\n", "        {\n", "            \"name\": \"selling_price\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_order_item selling_price\",\n", "        },\n", "        {\n", "            \"name\": \"general_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Grocery/cold/large\",\n", "        },\n", "        {\n", "            \"name\": \"storage_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_product storage_type\",\n", "        },\n", "        {\n", "            \"name\": \"handling_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gr_product handling_type\",\n", "        },\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"oms order status\"},\n", "        {\n", "            \"name\": \"ordered_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_suborder_item quantity\",\n", "        },\n", "        {\n", "            \"name\": \"procured_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"oms_suborder_item procured_quantity\",\n", "        },\n", "        {\n", "            \"name\": \"fwd_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"Forward Billed quantity from pos_invoice \",\n", "        },\n", "        {\n", "            \"name\": \"rev_quantity\",\n", "            \"type\": \"float8\",\n", "            \"description\": \"Reverse Billed quantity from pos_invoice \",\n", "        },\n", "        {\n", "            \"name\": \"complaint_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"INVALID/INVALID\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_source\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"DOORSTEP/CUSTOMER_CRM_CHAT_EMAIL/CUSTOMER_IN_APP\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"crm/pid_complaints complaint_category\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_sub_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"crm/pid_complaints complaint_sub_category \",\n", "        },\n", "        {\n", "            \"name\": \"reason\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Reason for complaint\",\n", "        },\n", "        {\n", "            \"name\": \"complaint_date\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"crm/pid_complaints complaint_date\",\n", "        },\n", "        {\n", "            \"name\": \"primary_key\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cart_id||suborder_id||product_id||slot_start||slot_end\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"primary_key\"],\n", "    \"sortkey\": [\"delivery_date\"],\n", "    \"incremental_key\": \"primary_key\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \" Complaints flat tabel \",\n", "}\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "print(\"tablename:\", kwargs[\"table_name\"])\n", "\n", "df_complaints_dump[\"l1_category_id\"] = df_complaints_dump[\"l1_category_id\"].astype(\n", "    \"Int64\"\n", ")\n", "df_complaints_dump[\"l2_category_id\"] = df_complaints_dump[\"l2_category_id\"].astype(\n", "    \"Int64\"\n", ")\n", "df_complaints_dump[\"l3_category_id\"] = df_complaints_dump[\"l3_category_id\"].astype(\n", "    \"Int64\"\n", ")\n", "pb.to_redshift(df_complaints_dump, **kwargs)\n", "print(\"pushed data into db for:\", s_date)\n", "end = time.time()\n", "result = time.localtime(end - start)\n", "print(\"\\n time_min:\", result.tm_min)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"flat table updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}