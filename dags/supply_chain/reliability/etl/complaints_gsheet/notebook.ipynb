{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import requests\n", "import sqlalchemy as sqla\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH abusive_list AS\n", "  (SELECT DISTINCT id AS customer_id\n", "   FROM consumer.gr_user\n", "   WHERE blocked=TRUE\n", "   UNION SELECT DISTINCT customer_id\n", "   FROM consumer.oms_abuse_user),\n", "\n", "  null_category_duplicates as (select distinct a.cart_id,a.product_id,a.delivery_slot,b.complaint_category as valid_complaint_type from \n", "(select * from metrics.complaints where delivery_date::date between current_date-8 and current_date-2 and complaint_category in ('','Null'))a\n", " join (select * from metrics.complaints where delivery_date::date between current_date-8 and current_date-2 and complaint_category not in ('','Null')) b\n", "on a.cart_id=b.cart_id and a.product_id=b.product_id  and a.delivery_slot=b.delivery_slot\n", ")\n", "SELECT -- extract(week from delivery_date) as weeknum,\n", "distinct c.*,(extract(day from (date_trunc('week',delivery_date-interval'3 day'))+interval'3 days'))::text||'/'||(extract(month from (date_trunc('week',delivery_date-interval'3 day'))+interval'3 days'))::text||' - '||(extract(day from (date_trunc('week',delivery_date-interval'3 day'))+interval'9 days'))::text||'/'||(extract(month from (date_trunc('week',delivery_date-interval'3 day'))+interval'9 days'))::text as week ,\n", "\n", "\n", "\n", "-------------Item Missing-------------\n", "case\n", "\n", "   when  ( (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%doorstep%%') )\n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing'))\n", "  \n", "   then  'IM - Item Missing Doorstep'\n", "\n", "   when  (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%'))\n", "  or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%') and coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%lm%%')  and coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%doorstep%%')  \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "   then  'I<PERSON> - <PERSON><PERSON> Missing After Del'\n", "   \n", "\n", " \n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%freebie%%missing%%') \n", "or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%missing%%') and C.COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') )\n", "\n", " then  '<PERSON><PERSON> - <PERSON><PERSON>/<PERSON><PERSON> missing'\n", " \n", " when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%extra%%item%%') \n", " then  'IM - Excess received'\n", " \n", "  when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%')\n", " then  'IM - mdnd'\n", " \n", "\n", "----------_Wrong Item/product------------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%item%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%product%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%different%%item%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%different%%product%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%different%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%different%%') \n", "or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%product%%') or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%item%%') or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%product%%') or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%different%%') or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%product%%different%%') \n", "then  'Wrong Item'\n", "\n", "\n", "---------Expiry----------------\n", "\n", "\n", "\n", "              \n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%expired%%') \n", "or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%')  then  'EXP - Expired Product'\n", "when   coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%near%%expiry%%')  then  'EXP - Near Expiry Product'\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "----------_Delay In delivery--------------------\n", "when   ( coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') and C.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%')) then  'Delay In Delivery'\n", "\n", "\n", "\n", "\n", "--------QNG----------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%quality%%') \n", "or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%') \n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%smell%%')\n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%taste%%')\n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%foreign%%matter%%') then  'QNG'\n", "\n", "\n", "\n", "-----------Item damaged-------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%damage%%') \n", "or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%damage%%')  then  'Item Damaged'\n", "\n", "\n", "\n", "\n", "\n", "\n", "------WCS-----------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%fe%%relate%%')  then  'WCS'\n", "\n", "\n", "\n", "\n", "               \n", "               -------Content-----------------\n", " when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%description%%') \n", "                or  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%diff%%')\n", "                or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') and C.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')) then  'Content Related'\n", "\n", " when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%description%%') \n", "                 then  'CR - Incorrect Description'\n", " when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%diff%%')\n", "                or ( coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') and C.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')) then  'CR - Price Difference'\n", "\n", "\n", "------------EDLP--------------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%EDLP%%') \n", "              or  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%guarantee%%')  then  'EDLP'\n", "\n", "\n", "\n", "\n", "------------Others----------------\n", "when  (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "and  <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%erased%%batch%%')) then  'Others'\n", "\n", "\n", "\n", "\n", "--------NLR-------------------------\n", "when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%no%%longer%%') \n", "or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%no%%longer%%') \n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%size%%')\n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%change%%mind%%')\n", "or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%bought%%mistake%%') then  'No Longer Required' end as complaint_group\n", "\n", "FROM metrics.complaints c\n", "LEFT JOIN abusive_list al ON c.customer_id=al.customer_id\n", "left join null_category_duplicates cd on  c.cart_id=cd.cart_id and c.product_id=cd.product_id  and c.delivery_slot=cd.delivery_slot\n", "WHERE complaint_category not in ('','Null') --and complaint_group='Item Damaged'\n", "and al.customer_id IS NULL\n", "  AND (delivery_date + interval '330 minute')::date between current_date-8 and current_date-2\n", "  AND c.order_id||c.product_id NOT IN\n", "    (SELECT DISTINCT order_id|| product_id\n", "     FROM\n", "       (SELECT DISTINCT oo.id AS order_id,\n", "                        oc.payment_method,\n", "                        oo.current_status,\n", "                        ooi.product_id\n", "        FROM oms_order oo\n", "        INNER JOIN oms_order_item ooi ON oo.id = ooi.order_id\n", "        AND oo.type = 'RetailForwardOrder'\n", "        AND oo.current_status <> 'CANCELLED'\n", "        AND date(oo.install_ts + interval '5.5 hours') between current_date-8 and current_date-2\n", "        AND ooi.product_id IN\n", "          (SELECT DISTINCT product_id\n", "           FROM metrics.drop_shipment_base\n", "           WHERE order_type = 'DropShippingForwardOrder'\n", "             AND order_checkout_date between current_date-8 and current_date-2)\n", "        INNER JOIN oms_cart AS oc ON oo.cart_id = oc.id)a)\n", "\"\"\"\n", "con = pb.get_connection(\"redshift\")\n", "df = pd.read_sql(sql=sql, con=con)\n", "print(\"min delivery date:\", df[\"delivery_date\"].min())\n", "print(\"max delivery date:\", df[\"delivery_date\"].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_2 = df[\n", "    [\n", "        \"cart_id\",\n", "        \"outlet_name\",\n", "        \"station\",\n", "        \"delivery_date\",\n", "        \"week\",\n", "        \"route_type\",\n", "        \"delivery_slot_type\",\n", "        \"customer_type\",\n", "        \"fe_category\",\n", "        \"employee_id\",\n", "        \"fe_name\",\n", "        \"cart_id\",\n", "        \"suborder_id\",\n", "        \"l1_category_name\",\n", "        \"l2_category_name\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"selling_price\",\n", "        \"general_category\",\n", "        \"complaint_type\",\n", "        \"order_type\",\n", "        \"complaint_source\",\n", "        \"complaint_category\",\n", "        \"complaint_sub_category\",\n", "        \"reason\",\n", "        \"complaint_date\",\n", "        \"complaint_group\",\n", "        \"fwd_quantity\",\n", "        \"rev_quantity\",\n", "        \"ordered_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["df_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_2, \"1pwRnEalHSpjmUxwrMVSjLRllOb3GXZAWTLJRJHC-UTk\", \"Raw Complaints Dump\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}