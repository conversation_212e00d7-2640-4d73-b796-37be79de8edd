{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "real_time_complaints_pan_india = \"101TCCHcCvsjFPiwCDo5PImx798MQ-Kx6hpxJw_v0Mqg\"\n", "current_complaints = pb.from_sheets(real_time_complaints_pan_india, \"current_date\")\n", "current_complaints = current_complaints.astype(\n", "    {\n", "        \"complaint_time\": \"datetime64\",\n", "        \"counter\": int,\n", "        \"order_time\": \"datetime64\",\n", "        \"order_id\": int,\n", "        \"customer_id\": int,\n", "        \"cart_id\": int,\n", "        \"subject\": str,\n", "        \"quantity\": int,\n", "        \"item_id\": int,\n", "        \"prod_name\": str,\n", "        \"store\": str,\n", "    }\n", ")\n", "\n", "current_complaints = current_complaints[\n", "    [\n", "        \"complaint_time\",\n", "        \"counter\",\n", "        \"order_time\",\n", "        \"order_id\",\n", "        \"customer_id\",\n", "        \"cart_id\",\n", "        \"subject\",\n", "        \"quantity\",\n", "        \"item_id\",\n", "        \"prod_name\",\n", "        \"store\",\n", "    ]\n", "]\n", "current_complaints.head(5)\n", "current_complaints.dtypes\n", "\n", "latest_complaints = current_complaints[current_complaints[\"counter\"] == 1]\n", "latest_complaints_v_store = latest_complaints.groupby(\"store\")\n", "latest_complaints_v_store.groups\n", "\n", "for store, rows in latest_complaints_v_store:\n", "    print(store)\n", "    print(len(rows))\n", "\n", "store_email = pb.from_sheets(real_time_complaints_pan_india, \"store_emails\")\n", "store_email = store_email.astype({\"email\": str, \"store\": str})\n", "store_email.head(15)\n", "\n", "for email in store_email[\"email\"]:\n", "    print(email)\n", "\n", "for store, rows in latest_complaints_v_store:\n", "    emails = store_email[store_email[\"store\"] == store][[\"email\"]]\n", "    if len(emails) < 1:\n", "        continue\n", "    print(emails)\n", "    print(len(rows))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html(df):\n", "    html_table = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 700px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 57px;\">\n", "    \"\"\"\n", "    for x in df.columns:\n", "        html_table += \"\"\"<td style=\"height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table += x\n", "        html_table += \"\"\"</strong></span></td>\"\"\"\n", "    html_table += \"\"\"</tr>\"\"\"\n", "    for i, r in df.iterrows():\n", "        html_table += \"\"\"<tr style=\"height: 15px;\">\"\"\"\n", "        for x in df.columns:\n", "            html_table += (\n", "                \"\"\"<td style=\"width: 200px; height: 15px; text-align: center;\">\"\"\"\n", "            )\n", "            html_table += str(r[x])\n", "            html_table += \"\"\"</td>\"\"\"\n", "        html_table += \"\"\"</tr>\"\"\"\n", "    html_table += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table\n", "\n", "\n", "def send_complaint_emails(emails, complaints):\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    to_email = list(emails[\"email\"])\n", "\n", "    subject = \"F&V Live Complaints\"\n", "\n", "    message_text = f\"\"\"Hi Team, <br><br> \n", "    Please find the below F&V live complaint details for last one hour with complaint category.\n", "    Request you to please check and share correct RCA on priority. \n", "    <br><br>{df_to_html(complaints)}\n", "\n", "    <br><br>\n", "    Update the remarks or photo upload of current stock on Google form: <a href=\"https://docs.google.com/forms/d/e/1FAIpQLSdzYUl6nPS9HE-5f4tY5lYgoOJ1eC2CzPggRZWmZY5xLOTmCQ/viewform?vc=0&c=0&w=1&flr=0\">Google Form</a> <br><br>\n", "\n", "    <br><br> <PERSON><PERSON>,<br><PERSON><PERSON><PERSON>\"\"\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content=message_text,\n", "    )\n", "\n", "\n", "for store, rows in latest_complaints_v_store:\n", "    emails = store_email[store_email[\"store\"] == store][[\"email\"]]\n", "    if len(emails) < 1:\n", "        continue\n", "    send_complaint_emails(emails, rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}