{"cells": [{"cell_type": "code", "execution_count": null, "id": "fac267a0-c24d-4fb9-861a-e788dab9456f", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"JUPYTERHUB_USER\"] = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "3c37298a-e91c-4969-9c20-a26840cfc7cb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import math\n", "import hashlib\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "7f880372-71c6-435d-8be2-059cb933e9fa", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4f4294aa-62de-45ac-b9ab-6be99d7a6d08", "metadata": {}, "outputs": [], "source": ["now_ts = (datetime.now() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "95142800-2cb5-4504-bcfe-bb25a06ade53", "metadata": {}, "outputs": [], "source": ["url = \"https://capacity-planner-preprod-data.prod-sgp-k8s.grofer.io/api/v1/blinkit/storeops/3/forecasts/20/inputs\"\n", "data = pb.get_secret(\"data/services/planner-platform/preprod/backend/data_loader\")\n", "ACCESS_TOKEN = data[\"m2_access_token\"]\n", "REFRESH_TOKEN = data[\"m2_refresh_token\"]\n", "\n", "headers = {\n", "    \"accept\": \"application/json\",\n", "    \"Cookie\": f\"refresh_token={REFRESH_TOKEN}; access_token={ACCESS_TOKEN};\",\n", "}\n", "\n", "today = datetime.today()\n", "\n", "days_since_monday = today.weekday()\n", "start_date = today - <PERSON><PERSON><PERSON>(days=days_since_monday)\n", "\n", "end_date = start_date + <PERSON><PERSON><PERSON>(weeks=6)\n", "\n", "city_responses = []\n", "\n", "current_date = start_date\n", "while current_date < end_date:\n", "    formatted_date = current_date.strftime(\"%Y-%m-%d 00:00:00\")\n", "\n", "    params = {\n", "        \"start_time\": formatted_date,\n", "        \"time_grain\": \"weekly\",\n", "        \"multi_aggregation_grain\": \"entity\",\n", "    }\n", "\n", "    response = requests.get(url, params=params, headers=headers)\n", "\n", "    print(f\"Requesting week starting {formatted_date}\")\n", "    print(\"Status Code:\", response.status_code)\n", "\n", "    try:\n", "        data = response.json()\n", "        city_responses.append(data)\n", "    except ValueError:\n", "        print(\"Response is not valid JSON:\", response.text)\n", "\n", "    current_date += <PERSON><PERSON><PERSON>(weeks=1)\n", "\n", "print(f\"\\nTotal weeks collected: {len(city_responses)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "4c57b925-329f-4104-8557-7180d9f6ffa9", "metadata": {}, "outputs": [], "source": ["city_responses"]}, {"cell_type": "code", "execution_count": null, "id": "3b5f850c-ef3c-4320-8a23-fbc56db544f6", "metadata": {}, "outputs": [], "source": ["records = []\n", "\n", "for weekly_response in city_responses:\n", "    data_entries = weekly_response.get(\"data\", [])\n", "    for entry in data_entries:\n", "        row = {\n", "            \"entity_id\": entry.get(\"entity_id\"),\n", "            \"week_start_date\": entry.get(\"start_time\"),\n", "            \"status\": entry.get(\"status\"),\n", "            \"updated_at\": entry.get(\"updated_at\"),\n", "        }\n", "\n", "        for metric in entry.get(\"values\", []):\n", "            metric_name = metric[\"metric_name\"].lower()\n", "            row[metric_name] = metric[\"value\"]\n", "\n", "        records.append(row)\n", "\n", "df = pd.DataFrame(records)\n", "\n", "desired_columns = [\n", "    \"entity_id\",\n", "    \"week_start_date\",\n", "    \"status\",\n", "    \"updated_at\",\n", "    \"ppi\",\n", "    \"items_put_away_per_hour\",\n", "    \"picker_util_fixed_wt_avg\",\n", "]\n", "for col in desired_columns:\n", "    if col not in df.columns:\n", "        df[col] = None\n", "\n", "records = df[desired_columns]"]}, {"cell_type": "code", "execution_count": null, "id": "df460307-96ab-41c2-96e9-8e58b697da75", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"entity_id\",\n", "    \"week_start_date\",\n", "    \"ppi\",\n", "    \"items_put_away_per_hour\",\n", "    \"picker_util_fixed_wt_avg\",\n", "    \"status\",\n", "    \"updated_at\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "dbb61727-c554-4622-9152-4f89035678ae", "metadata": {}, "outputs": [], "source": ["records = records[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "505c21af-13d5-46d8-895f-f8c8efd4ae69", "metadata": {}, "outputs": [], "source": ["records.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9738c05c-8d64-49bd-8efd-962aeb41ee50", "metadata": {}, "outputs": [], "source": ["records[\"week_start_date\"] = pd.to_datetime(records[\"week_start_date\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "238a4a28-4a09-4500-8e59-f5593463798e", "metadata": {}, "outputs": [], "source": ["records[\"items_put_away_per_hour\"] = pd.to_numeric(\n", "    records[\"items_put_away_per_hour\"], errors=\"coerce\"\n", ")\n", "records[\"picker_util_fixed_wt_avg\"] = pd.to_numeric(\n", "    records[\"picker_util_fixed_wt_avg\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f3e4cb80-2d42-4e89-8b15-f835aefa56e2", "metadata": {}, "outputs": [], "source": ["records[records[\"entity_id\"] == 1419]"]}, {"cell_type": "code", "execution_count": null, "id": "1033f316-3232-4047-8195-74229bba9016", "metadata": {}, "outputs": [], "source": ["records.head()"]}, {"cell_type": "code", "execution_count": null, "id": "44e2d291-77d1-4aaf-98e7-0e8a1c2003ae", "metadata": {}, "outputs": [], "source": ["records = (\n", "    records.groupby([\"entity_id\", \"week_start_date\", \"status\"])\n", "    .agg(\n", "        {\n", "            \"ppi\": \"first\",\n", "            \"items_put_away_per_hour\": \"first\",\n", "            \"picker_util_fixed_wt_avg\": \"first\",\n", "            \"updated_at\": \"first\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a633a502-a50b-4126-9f22-9b92b1654f48", "metadata": {}, "outputs": [], "source": ["records[records[\"entity_id\"] == 1419]"]}, {"cell_type": "code", "execution_count": null, "id": "a3d1b76a-fa89-4bb0-8321-9f393cd0d7f7", "metadata": {}, "outputs": [], "source": ["records[\"updated_at\"] = pd.to_datetime(records[\"updated_at\"])\n", "\n", "records = (\n", "    records.sort_values(by=\"updated_at\", ascending=False)\n", "    .drop_duplicates(subset=[\"entity_id\", \"week_start_date\", \"status\"], keep=\"first\")\n", "    .sort_values(by=[\"entity_id\", \"week_start_date\"])\n", ")\n", "\n", "records[\"updated_at\"] = records[\"updated_at\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "records.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ecfa1470-468c-42e2-90f3-7ccd728d302c", "metadata": {}, "outputs": [], "source": ["records[records[\"entity_id\"] == 2848]"]}, {"cell_type": "code", "execution_count": null, "id": "d0f68633-37b5-498d-a331-ef32b1f03e81", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"planner_instore_city_responses\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"Unique ID for the outlet\"},\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD) starting from Monday\",\n", "        },\n", "        {\"name\": \"ppi\", \"type\": \"real\", \"description\": \"Picks per item (efficiency metric)\"},\n", "        {\n", "            \"name\": \"items_put_away_per_hour\",\n", "            \"type\": \"real\",\n", "            \"description\": \"item_putaway_in_an_hour\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Total orders\",\n", "        },\n", "        {\n", "            \"name\": \"status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"status of input\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"varchar\", \"description\": \"Update timestamp of the table\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\", \"status\", \"updated_at\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains city inputs for manpower planning\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(records, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "9539d739-cc77-4fd3-93ae-df9a94c1fc49", "metadata": {}, "outputs": [], "source": ["query_temp = \"\"\"\n", "with order_base as \n", "(\n", "select\n", "week_start_date, \n", "date, \n", "entity_id, \n", "instore_orders, \n", "items_per_order\n", "from storeops_etls.planner_core_forecasts_daily_m2\n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "city_inputs as \n", "(\n", "    select entity_id as outlet_id, week_start_date, max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status in ('pending')\n", "    group by 1,2\n", "), \n", "\n", "store_master as \n", "(select *\n", "from interim.planner_instore_store_details\n", "where outlet_id is not null\n", "),\n", "\n", "final_base as \n", "(\n", "select * from \n", "(select\n", "  d AS calendar_date,\n", "  date_trunc('week',d) AS week_start_date,\n", "  outlet_id,\n", "  outlet_name,\n", "  city_name,\n", "  mode_of_ops,\n", "  live_status,\n", "  live_date,\n", "  PPI_Difficulty,\n", "  IPH_Difficulty\n", "from UNNEST(\n", "       SEQUENCE(\n", "         date_trunc('month', current_date),\n", "         date_add('day', -1, date_trunc('month', date_add('month', 2, current_date)))\n", "       )\n", "     ) as t(d)\n", "cross join store_master\n", ")\n", ")\n", ",\n", "\n", "next_week_agg AS (\n", "  SELECT \n", "    entity_id,\n", "    week_start_date,\n", "    MAX(instore_orders) AS max_orders,\n", "    SUM(instore_orders) AS sum_orders\n", "  FROM order_base\n", "  GROUP BY 1,2\n", "), \n", "\n", "orders_details_1 as (\n", "  select\n", "    o1.entity_id,\n", "    o1.week_start_date,\n", "    max(o1.instore_orders) as proj_max_orders_l,\n", "    sum(o1.instore_orders) as proj_sum_orders_l,\n", "    GREATEST(\n", "      max(o1.instore_orders),\n", "      coalesce(nwa.max_orders, 0)\n", "    ) AS proj_max_orders_tbl,\n", "    GREATEST(\n", "      sum(o1.instore_orders),\n", "      coalesce(nwa.sum_orders, 0)\n", "    ) AS proj_sum_orders_tbl\n", "  FROM order_base o1\n", "  LEFT JOIN next_week_agg nwa\n", "    ON o1.entity_id = nwa.entity_id\n", "    AND nwa.week_start_date = o1.week_start_date + interval '7' day\n", "  GROUP BY o1.entity_id, o1.week_start_date, nwa.max_orders, nwa.sum_orders\n", "  )\n", "  \n", ",\n", "\n", "order_details_2 as \n", "(select \n", "o1.entity_id,\n", "o1.week_start_date,\n", "(case when live_status = 'Live' then proj_max_orders_l*ma.order_buffer \n", "else proj_max_orders_tbl*ma.od_assmp_picker_util  end) as proj_max_central_orders,\n", "(case when live_status = 'Live' then proj_sum_orders_l*ma.order_buffer \n", "else proj_sum_orders_tbl*ma.od_assmp_picker_util  end)as proj_sum_central_orders\n", "from orders_details_1 o1\n", "left join interim.planner_instore_misc_assumption ma\n", "on o1.entity_id = cast(ma.outlet_id as integer)\n", "and o1.week_start_date = ma.week_start_date\n", "where ma.week_start_date > date_trunc('month', current_date)\n", ")\n", ",\n", "\n", "\n", "ipo as \n", "(select outlet_id, week_start_date, \n", "sum(ipc_factor_product)/sum(instore_orders) as ipc_factor, \n", "sum(putaway_factor_product)/sum(instore_orders) as putaway_factor\n", "from\n", "(\n", "select \n", "ea.outlet_id,\n", "ea.date_,\n", "ea.week_start_date,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders) as ipc_factor_product,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders*putaway_factor) as putaway_factor_product, \n", "instore_orders\n", "from interim.planner_instore_event_assumption ea\n", "left join order_base o\n", "on ea.outlet_id = cast(o.entity_id as varchar)\n", "and ea.date_ = o.date\n", "where date_ >= date_trunc('month', current_date)\n", ")\n", "group by 1,2\n", "), \n", "\n", "planned_base as \n", "(\n", "select * from storeops_etls.planner_instore_sla_assumption \n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "ppi_calc_3 as\n", "(select\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "im.order_date,\n", "sum(picking_time) as picking_time, \n", "sum(total_items_quantity_ordered) as total_items_quantity_ordered\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '10'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", "), \n", "\n", "buffer as \n", "(\n", "select week_start_date, outlet_id,\n", "case when ppi_3 >= planned_ppi + 4 then 2 else 0 end as ppi_buffer\n", "from\n", "(\n", "select p.week_start_date, p.outlet_id, p.planned_ppi, coalesce(try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)),0) as ppi_3\n", "from \n", "planned_base p\n", "left join\n", "ppi_calc_3 c\n", "on c.order_date between cast(p.week_start_date - interval '8' day as varchar) and cast(p.week_start_date - interval '6' day as varchar)\n", "and p.outlet_id = c.outlet_id \n", "group by 1,2,3\n", ")\n", "), \n", "\n", "calc_1 as \n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "fixed_util_class, \n", "iph_class, \n", "ppi_class, \n", "case when iph_city is not null then iph_city else planned_iph end as planned_iph, \n", "case when ppi_city is not null then ppi_city else planned_ppi + ppi_buffer end as planned_ppi, \n", "case when util_city is not null then util_city else planned_picker_util end as planned_picker_util, \n", "ppi_city,\n", "util_city,\n", "iph_city,\n", "ppi_buffer\n", "from\n", "(select \n", "f.*,\n", "o.proj_max_central_orders as proj_max_orders,\n", "o.proj_sum_central_orders as proj_sum_orders,\n", "(i.ipc_factor*o.proj_max_central_orders) as max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders) as sum_items,\n", "(o.proj_max_central_orders*i.putaway_factor) as putaway_max_items,\n", "(o.proj_sum_central_orders*i.putaway_factor) as putaway_sum_items,\n", "i.putaway_factor,\n", "ea.week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "ma.od_peak_contribution,\n", "ma.od_average_contribution,\n", "ma.od_assmp_picker_util,\n", "sa.planned_ppi_od as od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "i.ipc_factor, \n", "planned_iph, \n", "planned_picker_util, \n", "planned_ppi,\n", "fixed_util_class,\n", "iph_class,\n", "ppi_class,\n", "ppi_city,\n", "util_city,\n", "iph_city, \n", "coalesce(ppi_buffer,0) as ppi_buffer\n", "from final_base f\n", "inner join order_details_2 o\n", "on f.outlet_id = o.entity_id \n", "and f.week_start_date = o.week_start_date\n", "inner join ipo i\n", "on f.outlet_id = cast(i.outlet_id as integer)\n", "and f.week_start_date = i.week_start_date\n", "inner join interim.planner_instore_event_assumption ea\n", "on f.outlet_id = cast(ea.outlet_id as integer)\n", "and f.calendar_date = cast(ea.date_ as date)\n", "and ea.date_ >= date_trunc('month', current_date)\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date > date_trunc('month', current_date)\n", "inner join storeops_etls.planner_instore_sla_assumption sa\n", "on f.week_start_date = sa.week_start_date\n", "and f.outlet_id = sa.outlet_id\n", "and sa.week_start_date > date_trunc('month', current_date)\n", "left join city_inputs as ci\n", "on f.week_start_date = ci.week_start_date\n", "and f.outlet_id = ci.outlet_id\n", "and ci.week_start_date > date_trunc('month', current_date)\n", "left join buffer as b\n", "on f.week_start_date = b.week_start_date\n", "and f.outlet_id = b.outlet_id\n", ")\n", "), \n", "\n", "\n", "\n", "\n", "classing as\n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "planned_iph, \n", "planned_ppi, \n", "planned_picker_util, \n", "case when ppi_city is not null or ppi_buffer > 0 then ppi_class_mid else ppi_class end as ppi_class, \n", "fixed_util_class_mid as fixed_util_class, \n", "iph_class_mid as iph_class\n", "from\n", "(select \n", "c.*,\n", "case \n", "when c.planned_ppi between 0 and 14.99 then 1\n", "when c.planned_ppi between 15 and 19.99 then 2\n", "when c.planned_ppi between 20 and 30 then 3\n", "when c.planned_ppi > 30 then 4\n", "end as ppi_class_mid,\n", "case \n", "when c.planned_picker_util between 0 and 0.299 then 1\n", "when c.planned_picker_util between 0.30 and 0.399 then 2\n", "when c.planned_picker_util between 0.400 and 0.55 then 3\n", "when c.planned_picker_util > 0.55 then 4\n", "end as fixed_util_class_mid,\n", "case\n", "when c.planned_iph between 0 and 119.999 then 1\n", "when c.planned_iph between 120 and 149.999 then 2\n", "when c.planned_iph between 150 and 200 then 3\n", "when c.planned_iph > 200 then 4\n", "end as iph_class_mid\n", "from calc_1 c\n", ")\n", "), \n", "\n", "calc_2 as \n", "(\n", "    select c.*,\n", "    fa.ppi_improvement,\n", "    fa1.other_flow_assumption,\n", "    case \n", "    when mandays_hc_outlet_assmp = 0 and mandays_hc_city_assmp = 0 then manday_hc_default\n", "    when mandays_hc_outlet_assmp <> 0 then mandays_hc_outlet_assmp\n", "    when mandays_hc_outlet_assmp = 0 then mandays_hc_city_assmp\n", "    end as att_factor\n", "    from classing c\n", "    left join interim.planner_instore_flow_assumption fa\n", "    on c.week_start_date = fa.week_start_date\n", "    and c.ppi_class = fa.ppi_class\n", "    and fa.week_start_date > date_trunc('month', current_date)\n", "    left join interim.planner_instore_flow_assumption fa1\n", "    on c.iph_class = fa1.iph_class\n", "    and c.fixed_util_class = fa1.p_util_class\n", "    and fa1.week_start_date is not null\n", "), \n", "\n", "calc_3 as \n", "(select c.*,\n", "c.od_peak_contribution*max_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_peak,\n", "c.od_average_contribution*sum_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_average,\n", "sum_items*(1-c.od_average_contribution)*(planned_ppi/3600)/planned_picker_util as picker_hours,\n", "putaway_sum_items*1.00/planned_iph as putter_hours\n", "from calc_2 c\n", ")\n", "\n", ",\n", "\n", "calc_4 as \n", "(select *,\n", "(picker_hours + putter_hours)/(1-other_flow_assumption) as total_mh,\n", "((picker_hours + putter_hours)/(1-other_flow_assumption))/workhours_to_workday_conv as total_manday\n", "from calc_3\n", "),\n", "\n", "calc_5 as \n", "(select distinct *,\n", "total_manday/att_factor as headcount\n", "from calc_4)\n", ",\n", "\n", "orders_with_max as \n", "(\n", "select\n", "  entity_id,\n", "  week_start_date,\n", "  date,\n", "  instore_orders,\n", "  max(instore_orders) over (partition by entity_id, week_start_date) AS max_orders\n", "from order_base\n", "order by 1,2,3\n", "),\n", "\n", "fixed_manday_base AS (\n", "  SELECT\n", "    entity_id,\n", "    week_start_date,\n", "    date,\n", "    instore_orders,\n", "    max_orders,\n", "    max(CASE WHEN instore_orders = max_orders THEN date END) \n", "      OVER (PARTITION BY entity_id, week_start_date) AS max_order_date\n", "  FROM orders_with_max\n", "), \n", "\n", "fixed_manday as\n", "(select c.*,\n", "case when live_status = 'Live' then ((b.instore_orders*(1+putaway_factor))/(proj_sum_orders*(1+putaway_factor)))*total_manday \n", "else 0 end as requirement_manday,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then 0\n", "    when live_status = 'Live' then (((b.instore_orders/proj_sum_orders)*headcount) + ((b.max_orders/proj_sum_orders)*(headcount/6)))*(week_off_percent)\n", "    else 0 end as week_off_manday\n", "from calc_5 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date),\n", "\n", "fixed_manday_2 as \n", "(select *,\n", "case when live_status = 'Live' then (headcount - week_off_manday)/(att_factor/6)\n", "else headcount*0.86 end as actual_manday,\n", "greatest((requirement_manday - (case when live_status = 'Live' then (headcount - week_off_manday)/(att_factor/6)\n", "else headcount*0.86 end)),0) as addition_od_manday\n", "from fixed_manday),\n", "\n", "od_mandays as \n", "(select *,\n", "manhours_od_average/8.00 as budgeted_od_manday_average,\n", "manhours_od_peak/8.00 as peak_od_manday\n", "from fixed_manday_2),\n", "\n", "od_manday_2 as\n", "(select om.*,\n", "case when live_status = 'Live' and b.date <> b.max_order_date\n", "then (((budgeted_od_manday_average)*b.instore_orders)/proj_sum_orders) + addition_od_manday\n", "when live_status = 'Live' and b.date = b.max_order_date\n", "then (peak_od_manday) + addition_od_manday\n", "when live_status = 'To_be_Live'\n", "then ((budgeted_od_manday_average)/(To_be_Live_Mandays_distribution)) + addition_od_manday\n", "end as bugeted_od_manday\n", "from od_mandays om\n", "left join fixed_manday_base b\n", "on om.outlet_id = b.entity_id \n", "and om.calendar_date = b.date),\n", "\n", "daily_fixed_manday as \n", "(select distinct c.*,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then headcount\n", "    when live_status = 'Live' then requirement_manday\n", "    else requirement_manday end as daily_fixed_mandays\n", "from od_manday_2 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date\n", ")\n", "\n", "select a.* from\n", "(\n", "select distinct \n", "outlet_id as entity_id, \n", "week_start_date,\n", "round(planned_ppi,1) as ppi,\n", "round(planned_ppi*sum_items,1) as total_picking_time_sec,\n", "round(sum_items) as total_items_quantity_ordered,\n", "planned_picker_util*100.0 as picker_util_fixed_wt_avg,\n", "round(planned_picker_util*sum_items,1) as picker_util_numerator_fixed,\n", "od_average_contribution*100.0 as od_contribution_pct,\n", "round(sum_items*od_average_contribution) as qty_ordered_od,\n", "round(sum_items*(1-od_average_contribution)) as qty_ordered_fixed,\n", "round(planned_iph) as items_put_away_per_hour,\n", "round(putaway_sum_items) as putaway_qty,\n", "round(putter_hours*60) as putter_active_time_mins,\n", "round(headcount) as headcount\n", "from daily_fixed_manday\n", ") a\n", "inner join\n", "(\n", "    select entity_id, week_start_date, \n", "    max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status = 'pending'\n", "    group by 1,2\n", ") b\n", "on a.entity_id = b.entity_id\n", "and a.week_start_date = b.week_start_date\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "02ba04d0-6572-4495-a7b9-62b3aa01259e", "metadata": {}, "outputs": [], "source": ["query_temp = pd.read_sql(query_temp, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "6af69b41-d338-4cbc-b3b2-50e12cae8c12", "metadata": {}, "outputs": [], "source": ["query_temp[\"updated_ts\"] = now_ts"]}, {"cell_type": "code", "execution_count": null, "id": "82aad100-4d3f-4b0c-96ce-396573eeaee3", "metadata": {}, "outputs": [], "source": ["query_temp"]}, {"cell_type": "code", "execution_count": null, "id": "4351904d-0acc-40a8-a669-8db6e36c3534", "metadata": {}, "outputs": [], "source": ["query_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6db6f208-6043-4599-bcdc-e4b2ecff3256", "metadata": {}, "outputs": [], "source": ["query_temp[\"week_start_date\"] = pd.to_datetime(query_temp[\"week_start_date\"], errors=\"coerce\")\n", "query_temp[\"entity_id\"] = (\n", "    pd.to_numeric(query_temp[\"entity_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61077191-5098-4103-9ded-d7a121db5d8d", "metadata": {}, "outputs": [], "source": ["numeric_cols = [\n", "    \"ppi\",\n", "    \"total_picking_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"picker_util_numerator_fixed\",\n", "    \"od_contribution_pct\",\n", "    \"qty_ordered_od\",\n", "    \"qty_ordered_fixed\",\n", "    \"items_put_away_per_hour\",\n", "    \"putaway_qty\",\n", "    \"putter_active_time_mins\",\n", "    \"headcount\",\n", "    \"picker_util_fixed_wt_avg\",\n", "]\n", "\n", "for col in numeric_cols:\n", "    query_temp[col] = (\n", "        pd.to_numeric(query_temp[col], errors=\"coerce\").fillna(0).astype(float).round(2)\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "fe5c52e4-ac91-4d49-a402-4cc878178f46", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_weekly_temp_m2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"Unique ID for the outlet\"},\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD) starting from Monday\",\n", "        },\n", "        {\"name\": \"ppi\", \"type\": \"real\", \"description\": \"Picks per item (efficiency metric)\"},\n", "        {\n", "            \"name\": \"total_picking_time_sec\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Time taken for picking in seconds\",\n", "        },\n", "        {\n", "            \"name\": \"total_items_quantity_ordered\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Total quantity of items ordered\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_numerator_fixed\",\n", "            \"type\": \"real\",\n", "            \"description\": \"picker utilization multiplied by item total_\",\n", "        },\n", "        {\n", "            \"name\": \"od_contribution_pct\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Contribution percentage from OD\",\n", "        },\n", "        {\"name\": \"qty_ordered_od\", \"type\": \"real\", \"description\": \"Quantity ordered from OD\"},\n", "        {\"name\": \"qty_ordered_fixed\", \"type\": \"real\", \"description\": \"Fixed order quantity\"},\n", "        {\n", "            \"name\": \"items_put_away_per_hour\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Items put away per hour per putter\",\n", "        },\n", "        {\"name\": \"putaway_qty\", \"type\": \"real\", \"description\": \"Total quantity of items put away\"},\n", "        {\n", "            \"name\": \"putter_active_time_mins\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Active putaway time in minutes\",\n", "        },\n", "        {\"name\": \"headcount\", \"type\": \"real\", \"description\": \"Headcount required\"},\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"real\",\n", "            \"description\": \"sum-product of qty x fixed_util\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"varchar\", \"description\": \"Update timestamp of the table\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains weekly forecasted manpower metrics for instore operations\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(query_temp, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "637d538a-282f-4cce-b5d0-f270dc75a50e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9b560a5-9418-4bf6-8542-34e0f2cad4b8", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with order_base as \n", "(\n", "select\n", "week_start_date, \n", "date, \n", "entity_id, \n", "instore_orders, \n", "items_per_order\n", "from storeops_etls.planner_core_forecasts_daily_m2\n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "city_inputs as \n", "(\n", "    select entity_id as outlet_id, week_start_date, max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status = 'approved'\n", "    group by 1,2\n", "), \n", "\n", "store_master as \n", "(select *\n", "from interim.planner_instore_store_details\n", "where outlet_id is not null\n", "),\n", "\n", "final_base as \n", "(select\n", "  d AS calendar_date,\n", "  date_trunc('week',d) AS week_start_date,\n", "  outlet_id,\n", "  outlet_name,\n", "  city_name,\n", "  mode_of_ops,\n", "  live_status,\n", "  live_date,\n", "  PPI_Difficulty,\n", "  IPH_Difficulty\n", "from UNNEST(\n", "       SEQUENCE(\n", "         date_trunc('month', current_date),\n", "         date_add('day', -1, date_trunc('month', date_add('month', 2, current_date)))\n", "       )\n", "     ) as t(d)\n", "cross join store_master\n", ")\n", ",\n", "\n", "next_week_agg AS (\n", "  SELECT \n", "    entity_id,\n", "    week_start_date,\n", "    MAX(instore_orders) AS max_orders,\n", "    SUM(instore_orders) AS sum_orders\n", "  FROM order_base\n", "  GROUP BY 1,2\n", "), \n", "\n", "orders_details_1 as (\n", "  select\n", "    o1.entity_id,\n", "    o1.week_start_date,\n", "    max(o1.instore_orders) as proj_max_orders_l,\n", "    sum(o1.instore_orders) as proj_sum_orders_l,\n", "    GREATEST(\n", "      max(o1.instore_orders),\n", "      coalesce(nwa.max_orders, 0)\n", "    ) AS proj_max_orders_tbl,\n", "    GREATEST(\n", "      sum(o1.instore_orders),\n", "      coalesce(nwa.sum_orders, 0)\n", "    ) AS proj_sum_orders_tbl\n", "  FROM order_base o1\n", "  LEFT JOIN next_week_agg nwa\n", "    ON o1.entity_id = nwa.entity_id\n", "    AND nwa.week_start_date = o1.week_start_date + interval '7' day\n", "  GROUP BY o1.entity_id, o1.week_start_date, nwa.max_orders, nwa.sum_orders\n", "  )\n", ",\n", "\n", "order_details_2 as \n", "(select \n", "o1.entity_id,\n", "o1.week_start_date,\n", "(case when live_status = 'Live' then proj_max_orders_l*ma.order_buffer \n", "else proj_max_orders_tbl*ma.od_assmp_picker_util  end) as proj_max_central_orders,\n", "(case when live_status = 'Live' then proj_sum_orders_l*ma.order_buffer \n", "else proj_sum_orders_tbl*ma.od_assmp_picker_util  end)as proj_sum_central_orders\n", "from orders_details_1 o1\n", "left join interim.planner_instore_misc_assumption ma\n", "on o1.entity_id = cast(ma.outlet_id as integer)\n", "and o1.week_start_date = ma.week_start_date\n", "where ma.week_start_date > date_trunc('month', current_date)\n", ")\n", "\n", ",\n", "\n", "\n", "ipo as \n", "(select outlet_id, week_start_date, \n", "sum(ipc_factor_product)/sum(instore_orders) as ipc_factor, \n", "sum(putaway_factor_product)/sum(instore_orders) as putaway_factor\n", "from\n", "(\n", "select \n", "ea.outlet_id,\n", "ea.date_,\n", "ea.week_start_date,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders) as ipc_factor_product,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders*putaway_factor) as putaway_factor_product, \n", "instore_orders\n", "from interim.planner_instore_event_assumption ea\n", "left join order_base o\n", "on ea.outlet_id = cast(o.entity_id as varchar)\n", "and ea.date_ = o.date\n", "where date_ >= date_trunc('month', current_date)\n", ")\n", "group by 1,2\n", "), \n", "\n", "planned_base as \n", "(\n", "select * from storeops_etls.planner_instore_sla_assumption \n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "ppi_calc_3 as\n", "(select\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "im.order_date,\n", "sum(picking_time) as picking_time, \n", "sum(total_items_quantity_ordered) as total_items_quantity_ordered\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '10'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", "), \n", "\n", "buffer as \n", "(\n", "select week_start_date, outlet_id,\n", "case when ppi_3 >= planned_ppi + 4 then 2 else 0 end as ppi_buffer\n", "from\n", "(\n", "select p.week_start_date, p.outlet_id, p.planned_ppi, coalesce(try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)),0) as ppi_3\n", "from \n", "planned_base p\n", "left join\n", "ppi_calc_3 c\n", "on c.order_date between cast(p.week_start_date - interval '8' day as varchar) and cast(p.week_start_date - interval '6' day as varchar)\n", "and p.outlet_id = c.outlet_id \n", "group by 1,2,3\n", ")\n", "), \n", "\n", "calc_1 as \n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "fixed_util_class, \n", "iph_class, \n", "ppi_class, \n", "case when iph_city is not null then iph_city else planned_iph end as planned_iph, \n", "case when ppi_city is not null then ppi_city else planned_ppi + ppi_buffer end as planned_ppi, \n", "case when util_city is not null then util_city else planned_picker_util end as planned_picker_util, \n", "ppi_city,\n", "util_city,\n", "iph_city,\n", "ppi_buffer\n", "from\n", "(select \n", "f.*,\n", "o.proj_max_central_orders as proj_max_orders,\n", "o.proj_sum_central_orders as proj_sum_orders,\n", "(i.ipc_factor*o.proj_max_central_orders) as max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders) as sum_items,\n", "(o.proj_max_central_orders*i.putaway_factor) as putaway_max_items,\n", "(o.proj_sum_central_orders*i.putaway_factor) as putaway_sum_items,\n", "i.putaway_factor,\n", "ea.week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "ma.od_peak_contribution,\n", "ma.od_average_contribution,\n", "ma.od_assmp_picker_util,\n", "sa.planned_ppi_od as od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "i.ipc_factor, \n", "planned_iph, \n", "planned_picker_util, \n", "planned_ppi,\n", "fixed_util_class,\n", "iph_class,\n", "ppi_class,\n", "ppi_city,\n", "util_city,\n", "iph_city, \n", "coalesce(ppi_buffer,0) as ppi_buffer\n", "from final_base f\n", "inner join order_details_2 o\n", "on f.outlet_id = o.entity_id \n", "and f.week_start_date = o.week_start_date\n", "inner join ipo i\n", "on f.outlet_id = cast(i.outlet_id as integer)\n", "and f.week_start_date = i.week_start_date\n", "inner join interim.planner_instore_event_assumption ea\n", "on f.outlet_id = cast(ea.outlet_id as integer)\n", "and f.calendar_date = cast(ea.date_ as date)\n", "and ea.date_ >= date_trunc('month', current_date)\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date > date_trunc('month', current_date)\n", "inner join storeops_etls.planner_instore_sla_assumption sa\n", "on f.week_start_date = sa.week_start_date\n", "and f.outlet_id = sa.outlet_id\n", "and sa.week_start_date > date_trunc('month', current_date)\n", "left join city_inputs as ci\n", "on f.week_start_date = ci.week_start_date\n", "and f.outlet_id = ci.outlet_id\n", "and ci.week_start_date > date_trunc('month', current_date)\n", "left join buffer as b\n", "on f.week_start_date = b.week_start_date\n", "and f.outlet_id = b.outlet_id\n", ")\n", "), \n", "\n", "classing as\n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "planned_iph, \n", "planned_ppi, \n", "planned_picker_util, \n", "case when ppi_city is not null or ppi_buffer > 0 then ppi_class_mid else ppi_class end as ppi_class, \n", "fixed_util_class_mid as fixed_util_class, \n", "iph_class_mid as iph_class\n", "from\n", "(select \n", "c.*,\n", "case \n", "when c.planned_ppi between 0 and 14.99 then 1\n", "when c.planned_ppi between 15 and 19.99 then 2\n", "when c.planned_ppi between 20 and 30 then 3\n", "when c.planned_ppi > 30 then 4\n", "end as ppi_class_mid,\n", "case \n", "when c.planned_picker_util between 0 and 0.299 then 1\n", "when c.planned_picker_util between 0.30 and 0.399 then 2\n", "when c.planned_picker_util between 0.400 and 0.55 then 3\n", "when c.planned_picker_util > 0.55 then 4\n", "end as fixed_util_class_mid,\n", "case\n", "when c.planned_iph between 0 and 119.999 then 1\n", "when c.planned_iph between 120 and 149.999 then 2\n", "when c.planned_iph between 150 and 200 then 3\n", "when c.planned_iph > 200 then 4\n", "end as iph_class_mid\n", "from calc_1 c\n", ")\n", "), \n", "\n", "calc_2 as \n", "(\n", "    select c.*,\n", "    fa.ppi_improvement,\n", "    fa1.other_flow_assumption,\n", "    case \n", "    when mandays_hc_outlet_assmp = 0 and mandays_hc_city_assmp = 0 then manday_hc_default\n", "    when mandays_hc_outlet_assmp <> 0 then mandays_hc_outlet_assmp\n", "    when mandays_hc_outlet_assmp = 0 then mandays_hc_city_assmp\n", "    end as att_factor\n", "    from classing c\n", "    left join interim.planner_instore_flow_assumption fa\n", "    on c.week_start_date = fa.week_start_date\n", "    and c.ppi_class = fa.ppi_class\n", "    and fa.week_start_date > date_trunc('month', current_date)\n", "    left join interim.planner_instore_flow_assumption fa1\n", "    on c.iph_class = fa1.iph_class\n", "    and c.fixed_util_class = fa1.p_util_class\n", "    and fa1.week_start_date is not null\n", "), \n", "\n", "calc_3 as \n", "(select c.*,\n", "c.od_peak_contribution*max_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_peak,\n", "c.od_average_contribution*sum_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_average,\n", "sum_items*(1-c.od_average_contribution)*(planned_ppi/3600)/planned_picker_util as picker_hours,\n", "putaway_sum_items*1.00/planned_iph as putter_hours\n", "from calc_2 c\n", ")\n", "\n", ",\n", "\n", "calc_4 as \n", "(select *,\n", "(picker_hours + putter_hours)/(1-other_flow_assumption) as total_mh,\n", "((picker_hours + putter_hours)/(1-other_flow_assumption))/workhours_to_workday_conv as total_manday\n", "from calc_3\n", "), \n", "\n", "calc_5 as \n", "(select distinct *,\n", "total_manday/att_factor as headcount\n", "from calc_4)\n", ",\n", "\n", "orders_with_max as \n", "(\n", "select\n", "  entity_id,\n", "  week_start_date,\n", "  date,\n", "  instore_orders,\n", "  max(instore_orders) over (partition by entity_id, week_start_date) AS max_orders\n", "from order_base\n", "order by 1,2,3\n", "),\n", "\n", "fixed_manday_base AS (\n", "  SELECT\n", "    entity_id,\n", "    week_start_date,\n", "    date,\n", "    instore_orders,\n", "    max_orders,\n", "    max(CASE WHEN instore_orders = max_orders THEN date END) \n", "      OVER (PARTITION BY entity_id, week_start_date) AS max_order_date\n", "  FROM orders_with_max\n", "), \n", "\n", "fixed_manday as\n", "(select c.*,\n", "case when live_status = 'Live' then ((b.instore_orders*(1+putaway_factor))/(proj_sum_orders*(1+putaway_factor)))*total_manday \n", "else 0 end as requirement_manday,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then 0\n", "    when live_status = 'Live' then (((b.instore_orders/proj_sum_orders)*headcount) + ((b.max_orders/proj_sum_orders)*(headcount/6)))*(week_off_percent)\n", "    else 0 end as week_off_manday\n", "from calc_5 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date),\n", "\n", "fixed_manday_2 as \n", "(select *,\n", "case when live_status = 'Live' then (headcount - week_off_manday)/(att_factor/6)\n", "else headcount*0.86 end as actual_manday,\n", "greatest((requirement_manday - (case when live_status = 'Live' then (headcount - week_off_manday)/(att_factor/6)\n", "else headcount*0.86 end)),0) as addition_od_manday\n", "from fixed_manday),\n", "\n", "od_mandays as \n", "(select *,\n", "manhours_od_average/8.00 as budgeted_od_manday_average,\n", "manhours_od_peak/8.00 as peak_od_manday\n", "from fixed_manday_2),\n", "\n", "od_manday_2 as\n", "(select om.*,\n", "case when live_status = 'Live' and b.date <> b.max_order_date\n", "then (((budgeted_od_manday_average)*b.instore_orders)/proj_sum_orders) + addition_od_manday\n", "when live_status = 'Live' and b.date = b.max_order_date\n", "then (peak_od_manday) + addition_od_manday\n", "when live_status = 'To_be_Live'\n", "then ((budgeted_od_manday_average)/(To_be_Live_Mandays_distribution)) + addition_od_manday\n", "end as bugeted_od_manday\n", "from od_mandays om\n", "left join fixed_manday_base b\n", "on om.outlet_id = b.entity_id \n", "and om.calendar_date = b.date),\n", "\n", "daily_fixed_manday as \n", "(select distinct c.*,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then headcount\n", "    when live_status = 'Live' then requirement_manday\n", "    else requirement_manday end as daily_fixed_mandays\n", "from od_manday_2 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date\n", ")\n", "\n", "\n", "select distinct \n", "outlet_id as entity_id, \n", "week_start_date,\n", "round(planned_ppi,1) as ppi,\n", "round(planned_ppi*sum_items,1) as total_picking_time_sec,\n", "round(sum_items) as total_items_quantity_ordered,\n", "planned_picker_util*100.0 as picker_util_fixed_wt_avg,\n", "round(planned_picker_util*sum_items,1) as picker_util_numerator_fixed,\n", "od_average_contribution*100.0 as od_contribution_pct,\n", "round(sum_items*od_average_contribution) as qty_ordered_od,\n", "round(sum_items*(1-od_average_contribution)) as qty_ordered_fixed,\n", "round(planned_iph) as items_put_away_per_hour,\n", "round(putaway_sum_items) as putaway_qty,\n", "round(putter_hours*60) as putter_active_time_mins,\n", "round(headcount) as headcount\n", "from daily_fixed_manday\n", "order by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8cda504e-b186-45e3-9147-19a70dad6300", "metadata": {}, "outputs": [], "source": ["weekly_forecast = pd.read_sql(query, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "ec026e5d-1d80-4577-83ef-f20113a75175", "metadata": {}, "outputs": [], "source": ["weekly_forecast[weekly_forecast[\"entity_id\"] == 1314]"]}, {"cell_type": "code", "execution_count": null, "id": "8b673097-367d-4feb-9b61-5c348a85e7d1", "metadata": {}, "outputs": [], "source": ["weekly_forecast_df = weekly_forecast[\n", "    [\n", "        \"entity_id\",\n", "        \"week_start_date\",\n", "        \"ppi\",\n", "        \"total_picking_time_sec\",\n", "        \"total_items_quantity_ordered\",\n", "        \"picker_util_numerator_fixed\",\n", "        \"od_contribution_pct\",\n", "        \"qty_ordered_od\",\n", "        \"qty_ordered_fixed\",\n", "        \"items_put_away_per_hour\",\n", "        \"putaway_qty\",\n", "        \"putter_active_time_mins\",\n", "        \"headcount\",\n", "        \"picker_util_fixed_wt_avg\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2022ff44-0969-4a30-b23b-ff5ec61fd5fe", "metadata": {}, "outputs": [], "source": ["weekly_forecast_df[\"week_start_date\"] = pd.to_datetime(\n", "    weekly_forecast_df[\"week_start_date\"], errors=\"coerce\"\n", ")\n", "weekly_forecast_df[\"entity_id\"] = (\n", "    pd.to_numeric(weekly_forecast_df[\"entity_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "11b96fb9-bea8-41fa-b6d3-849e47adcca1", "metadata": {}, "outputs": [], "source": ["numeric_cols = [\n", "    \"ppi\",\n", "    \"total_picking_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"picker_util_numerator_fixed\",\n", "    \"od_contribution_pct\",\n", "    \"qty_ordered_od\",\n", "    \"qty_ordered_fixed\",\n", "    \"items_put_away_per_hour\",\n", "    \"putaway_qty\",\n", "    \"putter_active_time_mins\",\n", "    \"headcount\",\n", "    \"picker_util_fixed_wt_avg\",\n", "]\n", "\n", "for col in numeric_cols:\n", "    weekly_forecast_df[col] = (\n", "        pd.to_numeric(weekly_forecast_df[col], errors=\"coerce\").fillna(0).astype(float).round(2)\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ca20e77f-c65e-4ec6-b08e-87cfc796e7d7", "metadata": {}, "outputs": [], "source": ["try:\n", "    existing_query = \"\"\"\n", "    select * from blinkit_staging.storeops_etls.planner_instore_forecasts_weekly_m2\n", "    where week_start_date > date_trunc('week', current_date)\n", "    \"\"\"\n", "    weekly_forecast_existing_df = pd.read_sql(existing_query, trino_con)\n", "\n", "except Exception as e:\n", "\n", "    weekly_forecast_existing_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "87266f9f-45c7-42f9-b1f7-c4b1db3708c0", "metadata": {}, "outputs": [], "source": ["required_cols = [\n", "    \"week_start_date\",\n", "    \"entity_id\",\n", "    \"ppi\",\n", "    \"total_picking_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"picker_util_numerator_fixed\",\n", "    \"od_contribution_pct\",\n", "    \"qty_ordered_od\",\n", "    \"qty_ordered_fixed\",\n", "    \"items_put_away_per_hour\",\n", "    \"putaway_qty\",\n", "    \"putter_active_time_mins\",\n", "    \"headcount\",\n", "    \"picker_util_fixed_wt_avg\",\n", "    \"updated_ts\",\n", "]\n", "for col in required_cols:\n", "    if col not in weekly_forecast_existing_df.columns:\n", "        weekly_forecast_existing_df[col] = pd.Series(dtype=\"object\")"]}, {"cell_type": "code", "execution_count": null, "id": "55df7da3-fd06-4828-8ac6-a4111481e60c", "metadata": {}, "outputs": [], "source": ["weekly_forecast_existing_df[\"week_start_date\"] = pd.to_datetime(\n", "    weekly_forecast_existing_df[\"week_start_date\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fe10b46c-b8a2-4315-9f81-542a67f2972e", "metadata": {}, "outputs": [], "source": ["weekly_forecast_existing_df[weekly_forecast_existing_df[\"entity_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "eda075bf-0def-47f1-a14e-45b79f5ef30c", "metadata": {}, "outputs": [], "source": ["weekly_forecast_existing_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bfe9a5a1-90c2-49a1-9e30-b661c535e377", "metadata": {}, "outputs": [], "source": ["weekly_forecast_existing_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "71a67e82-0811-47bf-8b5e-6f1033f7189b", "metadata": {}, "outputs": [], "source": ["def row_checksum(row, exclude_cols=[\"updated_ts\"]):\n", "    data = \"\".join(str(row[col]) for col in row.index if col not in exclude_cols)\n", "    return hashlib.sha256(data.encode()).hexdigest()\n", "\n", "\n", "weekly_forecast_df[\"checksum\"] = weekly_forecast_df.apply(row_checksum, axis=1)\n", "weekly_forecast_existing_df[\"checksum\"] = weekly_forecast_existing_df.apply(row_checksum, axis=1)\n", "\n", "weekly_forecast_existing_df = weekly_forecast_existing_df.rename(\n", "    columns={\"updated_ts\": \"updated_ts_old\"}\n", ")\n", "\n", "merged_df = weekly_forecast_df.merge(\n", "    weekly_forecast_existing_df[[\"week_start_date\", \"entity_id\", \"checksum\", \"updated_ts_old\"]],\n", "    on=[\"week_start_date\", \"entity_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"\", \"_old\"),\n", ")\n", "\n", "merged_df[\"updated_ts\"] = merged_df.apply(\n", "    lambda row: (\n", "        now_ts\n", "        if row[\"checksum\"] != row[\"checksum_old\"] or pd.isna(row[\"checksum_old\"])\n", "        else row[\"updated_ts_old\"]\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "weekly_forecast_df = merged_df.drop(columns=[\"checksum\", \"checksum_old\", \"updated_ts_old\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5ba914f3-cc49-4654-96ee-fcc962c69040", "metadata": {}, "outputs": [], "source": ["weekly_forecast_df[weekly_forecast_df[\"entity_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "97617ed1-1765-4ab3-a6fa-786259f086fa", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_weekly_m2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"Unique ID for the outlet\"},\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD) starting from Monday\",\n", "        },\n", "        {\"name\": \"ppi\", \"type\": \"real\", \"description\": \"Picks per item (efficiency metric)\"},\n", "        {\n", "            \"name\": \"total_picking_time_sec\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Time taken for picking in seconds\",\n", "        },\n", "        {\n", "            \"name\": \"total_items_quantity_ordered\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Total quantity of items ordered\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_numerator_fixed\",\n", "            \"type\": \"real\",\n", "            \"description\": \"picker utilization multiplied by item total_\",\n", "        },\n", "        {\n", "            \"name\": \"od_contribution_pct\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Contribution percentage from OD\",\n", "        },\n", "        {\"name\": \"qty_ordered_od\", \"type\": \"real\", \"description\": \"Quantity ordered from OD\"},\n", "        {\"name\": \"qty_ordered_fixed\", \"type\": \"real\", \"description\": \"Fixed order quantity\"},\n", "        {\n", "            \"name\": \"items_put_away_per_hour\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Items put away per hour per putter\",\n", "        },\n", "        {\"name\": \"putaway_qty\", \"type\": \"real\", \"description\": \"Total quantity of items put away\"},\n", "        {\n", "            \"name\": \"putter_active_time_mins\",\n", "            \"type\": \"real\",\n", "            \"description\": \"Active putaway time in minutes\",\n", "        },\n", "        {\"name\": \"headcount\", \"type\": \"real\", \"description\": \"Headcount required\"},\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"real\",\n", "            \"description\": \"sum-product of qty x fixed_util\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"varchar\", \"description\": \"Update timestamp of the table\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains weekly forecasted manpower metrics for instore operations\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(weekly_forecast_df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}