{"cells": [{"cell_type": "code", "execution_count": null, "id": "05cd6afa-69ac-4ecd-bd0f-e1e395634d88", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"JUPYTERHUB_USER\"] = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "8fde609b-6a84-42a6-a74b-8c0765aaf589", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import hashlib\n", "import math"]}, {"cell_type": "code", "execution_count": null, "id": "4289165d-15a2-4d34-be64-111476cf83aa", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "2674bdc9-d285-4e4f-b9f7-0fd68562a9fc", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with \n", "store_master as \n", "(select *\n", "from interim.planner_instore_store_details\n", "where outlet_id is not null\n", "),\n", "\n", "\n", "store_metrics as \n", "(\n", "with ppi_calc_24 as \n", "(select\n", "week_start_date,\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "try(sum(putter_active_time)*1.00/60) as putter_active_time,\n", "try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)) as ppi_24,\n", "try(sum(picker_surge_carts+both_surge_carts)*1.00/nullif(sum(carts),0)) as surge_seen\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '95'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", ")\n", ",\n", "\n", "putaway_24 as (\n", "select \n", "date_trunc('week',date_) week_start_date,\n", "outlet_id,\n", "sum(packaged_qty+perishable_qty) as putaway_qty\n", "from storeops_etls.employee_metrics_daily \n", "where date_ between (current_date - interval  '95'  day)  and (current_date - interval  '1'  day) \n", "group by 1,2\n", ")\n", ",\n", "\n", "p_util_24 as \n", "(\n", "select \n", "date_trunc('week', date_) as week_,\n", "outlet_id , \n", "sum(assigned_to_started) + sum(picking_time) + sum(billing_time)  as picker_busy_time,\n", "sum(picker_active_time)*60 as picker_active_time \n", "from storeops_etls.employee_metrics_daily\n", "where date_ between (current_date - interval  '95'  day)  and (current_date - interval  '1'  day) \n", "and designation in ('Captain','Fnv Executive','Express Picker','Assistant', 'Inventory Executive','Captain ERT')\n", "group by 1,2\n", ")\n", ",\n", "\n", "final_f as \n", "(select \n", "p24.week_start_date,\n", "p24.outlet_id,\n", "p24.outlet_name,\n", "p24.city,\n", "ppi_24,\n", "try(p.putaway_qty*1.00/nullif(p24.putter_active_time,0)) as iph,\n", "try(picker_busy_time*1.00/nullif(picker_active_time,0)) as fixed_util,\n", "surge_seen\n", "from ppi_calc_24 p24\n", "left join putaway_24 p \n", "on p24.week_start_date = cast(p.week_start_date as varchar)\n", "and p24.outlet_id = p.outlet_id\n", "left join p_util_24 pu\n", "on p24.week_start_date = cast(pu.week_ as varchar)\n", "and p24.outlet_id = pu.outlet_id\n", "order by 2,1)\n", ",\n", "\n", "ppi_calc_3 as\n", "(select\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "try(sum(putter_active_time)*1.00/60) as putter_active_time,\n", "try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)) as ppi_3\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '3'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3\n", ")\n", ",\n", "\n", "putaway_3 as (\n", "select \n", "outlet_id,\n", "sum(packaged_qty+perishable_qty) as putaway_qty\n", "from storeops_etls.employee_metrics_daily \n", "where date_ between (current_date - interval  '3'  day)  and (current_date - interval  '1'  day) \n", "group by 1\n", ")\n", ",\n", "\n", "p_util_3 as \n", "(\n", "select \n", "outlet_id , \n", "sum(assigned_to_started) + sum(picking_time) + sum(billing_time)  as picker_busy_time,\n", "sum(picker_active_time)*60 as picker_active_time \n", "from storeops_etls.employee_metrics_daily\n", "where date_ between (current_date - interval  '3'  day)  and (current_date - interval  '1'  day) \n", "and designation in ('Captain','Fnv Executive','Express Picker','Assistant', 'Inventory Executive','Captain ERT')\n", "group by 1 )\n", ",\n", "\n", "ppi_7 as \n", "(select\n", "outlet_id,\n", "try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)) as ppi_7\n", "from storeops_etls.instore_metrics_daily \n", "where order_date between cast((current_date - interval  '7'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1\n", "),\n", "\n", "final_f2 as \n", "(select \n", "p3.outlet_id,\n", "p3.outlet_name,\n", "p3.city,\n", "ppi_3,\n", "try(p.putaway_qty*1.00/nullif(p3.putter_active_time,0)) as iph_3,\n", "try(picker_busy_time*1.00/nullif(picker_active_time,0)) as fixed_util_3\n", "from ppi_calc_3 p3\n", "left join putaway_3 p \n", "on p3.outlet_id = p.outlet_id\n", "left join p_util_3 pu\n", "on p3.outlet_id = pu.outlet_id\n", ")\n", ",\n", "\n", "final_f3 as \n", "(select outlet_id,\n", "case when \n", "try(count(case when surge_seen < 0.01 then outlet_id end)*1.00/nullif(count(outlet_id),0))  > 0.5 then 'Yes'\n", "else 'No' end as surge_flag,\n", "approx_percentile(case when surge_seen < 0.01 then ppi_24 end, 0.5 ) as ppi_24,\n", "approx_percentile(case when surge_seen < 0.01 then iph end,0.5 ) as iph_24,\n", "approx_percentile(case when surge_seen < 0.01 then fixed_util end,0.5 ) as fixed_util_24\n", "from final_f\n", "group by 1)\n", "\n", "\n", "select \n", "a.outlet_id, \n", "ppi_24,\n", "iph_24,\n", "fixed_util_24,\n", "surge_flag,\n", "ppi_3,\n", "iph_3,\n", "fixed_util_3,\n", "ppi_7\n", "from\n", "(\n", "select \n", "f3.outlet_id, \n", "ppi_24,\n", "iph_24,\n", "fixed_util_24,\n", "surge_flag,\n", "ppi_3,\n", "iph_3,\n", "fixed_util_3,\n", "ppi_7\n", "from final_f3 f3\n", "left join final_f2 f2\n", "on f3.outlet_id = f2.outlet_id\n", "left join ppi_7 p\n", "on f3.outlet_id = p.outlet_id\n", ") a\n", "), \n", "\n", "final_base as \n", "(select \n", "    date_trunc('week', d) as week_start_date,\n", "    outlet_id,\n", "    outlet_name,\n", "    city_name,\n", "    mode_of_ops,\n", "    live_status,\n", "    live_date,\n", "    PPI_Difficulty,\n", "    IPH_Difficulty\n", "  from UNNEST(\n", "         SEQUENCE(\n", "           date_trunc('week', date_add('week', 1, current_date)),\n", "           date_trunc('week', date_trunc('month', date_add('month', 2, current_date))),\n", "           INTERVAL '7' DAY\n", "         )\n", "       ) as t(d)\n", "  cross join store_master\n", ")\n", ", \n", "\n", "\n", "calc_1 as \n", "(select \n", "f.*,\n", "case when sm.surge_flag = 'Yes' then ppi_24 \n", "     when sm.surge_flag = 'No' then ppi_3 \n", "     else 0 end as ppi_assmp,\n", "ppi_7,\n", "case when f.live_status = 'To_be_Live' then ma.p_util_fixed_tobelive else greatest(coalesce(fixed_util_24,0), coalesce(fixed_util_3,0)) end as p_util_assmp,\n", "case when f.live_status = 'To_be_Live' then ma.iph_range_to_be_live else greatest(coalesce(iph_24,0), coalesce(iph_3,0)) end as iph_assmp,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "ppi_to_be_live,\n", "store_ppi_min\n", "from final_base f\n", "inner join store_metrics sm\n", "on f.outlet_id = sm.outlet_id\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date >= date_trunc('week', current_date)\n", "), \n", "\n", "\n", "calc_2 as \n", "(select *,\n", "CASE\n", "  WHEN PPI_Difficulty = 1 AND p_util_assmp * picker_util_fixed_growth_factor < picker_util_fixed_range_max THEN \n", "    GREATEST(p_util_assmp * picker_util_fixed_growth_factor, picker_util_fixed_range_min)\n", "    WHEN PPI_Difficulty = 0 AND p_util_assmp * picker_util_fixed_growth_factor < picker_util_fixed_range_max THEN \n", "    GREATEST(p_util_assmp * picker_util_fixed_growth_factor, picker_util_fixed_range_min)\n", "    ELSE picker_util_fixed_range_max\n", "   end as p_util_assmp_final,\n", "  \n", "  CASE\n", "  WHEN IPH_Difficulty = 1 AND iph_assmp * iph_growth_factor < iph_range_max THEN \n", "    GREATEST(iph_assmp * iph_growth_factor, iph_range_min)\n", "  WHEN IPH_Difficulty = 0 AND iph_assmp * iph_growth_factor < iph_range_max THEN \n", "    GREATEST(iph_assmp * iph_growth_factor, iph_range_min)\n", "  ELSE iph_range_max\n", "  end as iph_assmp_final\n", "  from calc_1 \n", ")\n", ", \n", "  \n", "classing as \n", "(select \n", "c.*,\n", "case \n", "when c.ppi_assmp between 0 and 14.99 then 1\n", "when c.ppi_assmp between 15 and 19.99 then 2\n", "when c.ppi_assmp between 20 and 30 then 3\n", "when c.ppi_assmp > 30 then 4\n", "end as ppi_class,\n", "case \n", "when c.p_util_assmp_final between 0 and 0.299 then 1\n", "when c.p_util_assmp_final between 0.30 and 0.399 then 2\n", "when c.p_util_assmp_final between 0.400 and 0.55 then 3\n", "when c.p_util_assmp_final > 0.55 then 4\n", "end as fixed_util_class,\n", "case \n", "when c.iph_assmp_final between 0 and 119.999 then 1\n", "when c.iph_assmp_final between 120 and 149.999 then 2\n", "when c.iph_assmp_final between 150 and 200 then 3\n", "when c.iph_assmp_final > 200 then 4\n", "end as iph_class\n", "from calc_2 c\n", ")\n", ",   \n", "\n", "calc_3 as\n", "(select c.*,\n", "fa.ppi_improvement,\n", "fa1.other_flow_assumption,  \n", "case \n", "when live_status = 'To_be_live' then ppi_to_be_live\n", "when live_status = 'Live' and ppi_assmp*(1-fa.ppi_improvement) = 0 then store_ppi_min\n", "when live_status = 'Live' and ppi_assmp*(1-fa.ppi_improvement) <> 0 then ppi_assmp*(1-fa.ppi_improvement)\n", "end as ppi_assmp_final\n", "from classing c\n", "left join interim.planner_instore_flow_assumption fa\n", "on c.week_start_date = fa.week_start_date\n", "and c.ppi_class = fa.ppi_class\n", "and fa.week_start_date > date_trunc('week', current_date)\n", "left join interim.planner_instore_flow_assumption fa1\n", "on c.iph_class = fa1.iph_class\n", "and c.fixed_util_class = fa1.p_util_class\n", "and fa1.week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "\n", "\n", "store_metrics_od as \n", "(\n", "with ppi_calc_24 as \n", "(\n", "select date_trunc('week', date_) as week_start_date , \n", "outlet_id, \n", "outlet_name, \n", "city_name, \n", "try(sum(picking_time)*1.00/nullif(sum(total_quantity_picked),0)) as ppi_24\n", "from storeops_etls.employee_metrics_daily im\n", "where date_ between (current_date - interval  '95'  day)\n", "and (current_date - interval  '1'  day)\n", "and designation = 'Captain O<PERSON>'\n", "group by 1,2,3,4\n", "), \n", "\n", "\n", "surge_seen_24 as\n", "(\n", "select\n", "week_start_date,\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "try(sum(picker_surge_carts+both_surge_carts)*1.00/nullif(sum(carts),0)) as surge_seen\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '95'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", "), \n", "\n", "final_f as \n", "(select \n", "p24.week_start_date,\n", "p24.outlet_id,\n", "p24.outlet_name,\n", "p24.city_name,\n", "ppi_24,\n", "surge_seen\n", "from ppi_calc_24 p24\n", "left join\n", "surge_seen_24 s24\n", "on p24.week_start_date = cast(s24.week_start_date as date)\n", "and p24.outlet_id = s24.outlet_id\n", "order by 2,1)\n", ",\n", "\n", "ppi_calc_3 as\n", "(\n", "select\n", "outlet_id, \n", "outlet_name, \n", "city_name, \n", "try(sum(picking_time)*1.00/nullif(sum(total_quantity_picked),0)) as ppi_3\n", "from storeops_etls.employee_metrics_daily im\n", "where date_ between (current_date - interval  '3'  day)\n", "and (current_date - interval  '1'  day)\n", "and designation = 'Captain O<PERSON>'\n", "group by 1,2,3\n", "), \n", "\n", "final_f2 as \n", "(select \n", "p3.outlet_id,\n", "p3.outlet_name,\n", "p3.city_name,\n", "ppi_3\n", "from ppi_calc_3 p3\n", ")\n", ",\n", "\n", "final_f3 as \n", "(select outlet_id,\n", "case when \n", "try(count(case when surge_seen < 0.01 then outlet_id end)*1.00/nullif(count(outlet_id),0))  > 0.5 then 'Yes'\n", "else 'No' end as surge_flag,\n", "approx_percentile(case when surge_seen < 0.01 then ppi_24 end, 0.5 ) as ppi_24\n", "from final_f\n", "group by 1)\n", "\n", "select \n", "a.outlet_id, \n", "ppi_24,\n", "surge_flag,\n", "ppi_3\n", "from\n", "(\n", "select \n", "f3.outlet_id, \n", "ppi_24,\n", "surge_flag,\n", "ppi_3\n", "from final_f3 f3\n", "left join final_f2 f2\n", "on f3.outlet_id = f2.outlet_id\n", ") a\n", ")\n", ", \n", "\n", "final_base_od as \n", "(select \n", "    date_trunc('week', d) as week_start_date,\n", "    outlet_id,\n", "    outlet_name,\n", "    city_name,\n", "    mode_of_ops,\n", "    live_status,\n", "    live_date,\n", "    PPI_Difficulty,\n", "    IPH_Difficulty\n", "  from UNNEST(\n", "         SEQUENCE(\n", "           date_trunc('week', date_add('week', 1, current_date)),\n", "           date_trunc('week', date_trunc('month', date_add('month', 2, current_date))),\n", "           INTERVAL '7' DAY\n", "         )\n", "       ) as t(d)\n", "  cross join store_master\n", ")\n", ", \n", "\n", "\n", "calc_1_od as \n", "(select \n", "f.*,\n", "case when sm.surge_flag = 'Yes' then ppi_24 \n", "     when sm.surge_flag = 'No' then ppi_3 \n", "     else 0 end as ppi_assmp, \n", "     store_ppi_min, \n", "     ppi_to_be_live\n", "from final_base_od f\n", "inner join store_metrics_od sm\n", "on f.outlet_id = sm.outlet_id\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date > date_trunc('week', current_date)\n", ")\n", ", \n", "\n", "  \n", "classing_od as \n", "(select \n", "c.*,\n", "case \n", "when c.ppi_assmp between 0 and 14.99 then 1\n", "when c.ppi_assmp between 15 and 19.99 then 2\n", "when c.ppi_assmp between 20 and 30 then 3\n", "when c.ppi_assmp > 30 then 4\n", "end as ppi_class\n", "from calc_1_od c\n", ")\n", ",   \n", "\n", "calc_3_od as\n", "(\n", "select c.*,\n", "fa.ppi_improvement, \n", "case \n", "when live_status = 'To_be_live' then ppi_to_be_live\n", "when live_status = 'Live' and ppi_assmp*(1-fa.ppi_improvement) = 0 then store_ppi_min\n", "when live_status = 'Live' and ppi_assmp*(1-fa.ppi_improvement) <> 0 then ppi_assmp*(1-fa.ppi_improvement)\n", "end as ppi_assmp_final_od\n", "from classing_od c\n", "left join interim.planner_instore_flow_assumption fa\n", "on c.week_start_date = fa.week_start_date\n", "and c.ppi_class = fa.ppi_class\n", "and fa.week_start_date > date_trunc('week', current_date)\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", ")\n", "\n", "\n", "select \n", "c.week_start_date,\n", "c.outlet_id,\n", "p_util_assmp_final as planned_picker_util,\n", "iph_assmp_final as planned_iph,\n", "least(coalesce(greatest(ppi_assmp_final, 13), 13), 30) as planned_ppi,\n", "fixed_util_class,\n", "iph_class,\n", "c.ppi_class,\n", "least(coalesce(greatest(ppi_assmp_final_od, 13), 13), 30) as planned_ppi_od\n", "from calc_3 c\n", "left join\n", "calc_3_od co\n", "on c.outlet_id = cast(co.outlet_id as integer)\n", "and c.week_start_date = co.week_start_date\n", "where c.week_start_date > date_trunc('week', current_date)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d2f8a9bc-8938-4ac8-928e-4cd8a4b05628", "metadata": {}, "outputs": [], "source": ["sla_df = pd.read_sql(query, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "f148b376-0141-4f8a-924d-2b9fe6e56dfe", "metadata": {}, "outputs": [], "source": ["sla_df = sla_df[\n", "    [\n", "        \"week_start_date\",\n", "        \"outlet_id\",\n", "        \"planned_iph\",\n", "        \"planned_picker_util\",\n", "        \"planned_ppi\",\n", "        \"fixed_util_class\",\n", "        \"iph_class\",\n", "        \"ppi_class\",\n", "        \"planned_ppi_od\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3a8f4383-bc5d-4766-ac3a-c55c49902628", "metadata": {}, "outputs": [], "source": ["sla_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "2e76bf8f-d48f-498e-b919-52d73d9639be", "metadata": {}, "outputs": [], "source": ["sla_df[\"week_start_date\"] = pd.to_datetime(sla_df[\"week_start_date\"], errors=\"coerce\")\n", "\n", "sla_df[\"outlet_id\"] = pd.to_numeric(sla_df[\"outlet_id\"], errors=\"coerce\").fillna(0).astype(int)\n", "\n", "sla_df[\"planned_picker_util\"] = (\n", "    pd.to_numeric(sla_df[\"planned_picker_util\"], errors=\"coerce\").fillna(0).astype(float).round(2)\n", ")\n", "\n", "sla_df[\"planned_iph\"] = (\n", "    pd.to_numeric(sla_df[\"planned_iph\"], errors=\"coerce\").fillna(0).astype(float).round(2)\n", ")\n", "\n", "sla_df[\"planned_ppi\"] = (\n", "    pd.to_numeric(sla_df[\"planned_ppi\"], errors=\"coerce\").fillna(0).astype(float).round(2)\n", ")\n", "\n", "\n", "sla_df[\"planned_ppi_od\"] = (\n", "    pd.to_numeric(sla_df[\"planned_ppi_od\"], errors=\"coerce\").fillna(0).astype(float).round(2)\n", ")\n", "\n", "sla_df[\"fixed_util_class\"] = (\n", "    pd.to_numeric(sla_df[\"fixed_util_class\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "\n", "sla_df[\"iph_class\"] = pd.to_numeric(sla_df[\"iph_class\"], errors=\"coerce\").fillna(0).astype(int)\n", "\n", "sla_df[\"ppi_class\"] = pd.to_numeric(sla_df[\"ppi_class\"], errors=\"coerce\").fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "505c12d3-0a4f-44a1-9315-4052e0caf488", "metadata": {}, "outputs": [], "source": ["sla_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4adaf239-1a0b-41f2-875c-6baf7160a5b5", "metadata": {}, "outputs": [], "source": ["sla_df[sla_df[\"outlet_id\"] == 1393]"]}, {"cell_type": "code", "execution_count": null, "id": "9059423e-5c4f-425c-b5a9-1336ab9698b7", "metadata": {}, "outputs": [], "source": ["try:\n", "    existing_query = \"\"\"\n", "    SELECT * FROM blinkit_staging.storeops_etls.planner_instore_sla_assumption\n", "    WHERE week_start_date > date_trunc('week', current_date)\n", "    \"\"\"\n", "    sla_existing_df = pd.read_sql(existing_query, trino_con)\n", "\n", "except Exception as e:\n", "\n", "    sla_existing_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "86e7c190-6bc7-4234-bb56-7729e4fd5434", "metadata": {}, "outputs": [], "source": ["required_cols = [\n", "    \"week_start_date\",\n", "    \"outlet_id\",\n", "    \"planned_picker_util\",\n", "    \"planned_iph\",\n", "    \"planned_ppi\",\n", "    \"fixed_util_class\",\n", "    \"iph_class\",\n", "    \"ppi_class\",\n", "    \"planned_ppi_od\",\n", "    \"updated_ts\",\n", "]\n", "for col in required_cols:\n", "    if col not in sla_existing_df.columns:\n", "        sla_existing_df[col] = pd.Series(dtype=\"object\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd45b8a1-5260-40bb-9202-e3bb33ced507", "metadata": {}, "outputs": [], "source": ["sla_existing_df[sla_existing_df[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "fe95d2d5-defe-4f05-a0f9-dd822c8d34e1", "metadata": {}, "outputs": [], "source": ["sla_df[sla_df[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "55e509aa-1a87-4fb5-8b67-2659ee0c552f", "metadata": {}, "outputs": [], "source": ["sla_existing_df[\"week_start_date\"] = pd.to_datetime(\n", "    sla_existing_df[\"week_start_date\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aab4ea2e-4650-4aed-a2e2-64a14d8a9293", "metadata": {}, "outputs": [], "source": ["sla_existing_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d00c9136-083a-444d-989a-6f37339de4fb", "metadata": {}, "outputs": [], "source": ["def row_checksum(row, exclude_cols=[\"updated_ts\"]):\n", "    data = \"\".join(str(row[col]) for col in row.index if col not in exclude_cols)\n", "    return hashlib.sha256(data.encode()).hexdigest()\n", "\n", "\n", "sla_df[\"checksum\"] = sla_df.apply(row_checksum, axis=1)\n", "sla_existing_df[\"checksum\"] = sla_existing_df.apply(row_checksum, axis=1)\n", "\n", "sla_existing_df = sla_existing_df.rename(columns={\"updated_ts\": \"updated_ts_old\"})\n", "\n", "merged_df = sla_df.merge(\n", "    sla_existing_df[[\"week_start_date\", \"outlet_id\", \"checksum\", \"updated_ts_old\"]],\n", "    on=[\"week_start_date\", \"outlet_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"\", \"_old\"),\n", ")\n", "\n", "now_ts = (datetime.now() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "merged_df[\"updated_ts\"] = merged_df.apply(\n", "    lambda row: now_ts\n", "    if row[\"checksum\"] != row[\"checksum_old\"] or pd.isna(row[\"checksum_old\"])\n", "    else row[\"updated_ts_old\"],\n", "    axis=1,\n", ")\n", "\n", "final_df = merged_df.drop(columns=[\"checksum\", \"checksum_old\", \"updated_ts_old\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4560609f-40a5-4f83-8ada-0ff236ef60ab", "metadata": {}, "outputs": [], "source": ["final_df[final_df[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "6f49f8c3-7769-4b52-bb1f-5d7125eea7a4", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_sla_assumption\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD), week starting from Monday\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id for the outlet\"},\n", "        {\"name\": \"planned_iph\", \"type\": \"real\", \"description\": \"planned_iph\"},\n", "        {\n", "            \"name\": \"planned_picker_util\",\n", "            \"type\": \"real\",\n", "            \"description\": \"planned_picker_util\",\n", "        },\n", "        {\n", "            \"name\": \"planned_ppi\",\n", "            \"type\": \"real\",\n", "            \"description\": \"planned_ppi\",\n", "        },\n", "        {\"name\": \"fixed_util_class\", \"type\": \"integer\", \"description\": \"fixed_util_class\"},\n", "        {\"name\": \"iph_class\", \"type\": \"integer\", \"description\": \"iph_class\"},\n", "        {\"name\": \"ppi_class\", \"type\": \"integer\", \"description\": \"ppi_class\"},\n", "        {\n", "            \"name\": \"planned_ppi_od\",\n", "            \"type\": \"real\",\n", "            \"description\": \"planned_ppi\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"varchar\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"outlet_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"core instore metrics necessary for calculating headcount\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(final_df, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "a89948fd-f8dd-45b7-8fbc-a0de8abb7e1c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6dba76e7-86d7-4d98-9d12-56057671c576", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2dd060fd-4174-4b29-8815-488f2066ecfb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}