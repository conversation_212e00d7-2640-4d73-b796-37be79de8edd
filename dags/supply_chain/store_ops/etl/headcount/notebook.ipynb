{"cells": [{"cell_type": "code", "execution_count": null, "id": "99329db3-01b3-459c-baa3-4862e3b1910a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import math\n", "import pytz\n", "from sqlalchemy.exc import OperationalError, DatabaseError"]}, {"cell_type": "code", "execution_count": null, "id": "3e62a441-e3c8-4e24-9674-d8972f61d31b", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "2d0a5cd2-f29d-47ba-9ff4-7c49bb391719", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "updated_timestamp = pd.Timestamp.now(tz=\"UTC\").astimezone(ist)\n", "timestamp_str = updated_timestamp.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "updated_timestamp = pd.to_datetime(timestamp_str)\n", "current_date = updated_timestamp.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "954b262e-8101-49e5-b84d-4c03f05b82dc", "metadata": {}, "outputs": [], "source": ["updated_timestamp"]}, {"cell_type": "code", "execution_count": null, "id": "e35a67ca-66dc-47a4-a3b4-0fbbdc55c4a1", "metadata": {}, "outputs": [], "source": ["current_date"]}, {"cell_type": "code", "execution_count": null, "id": "3bc9bbfe-16f9-456c-bffd-47f6beefd053", "metadata": {}, "outputs": [], "source": ["approved_hc = pb.from_sheets(\n", "    \"1ux-q6T-iH5A3otWneJRKLSsrUtHejK2aZlsv49DEpb0\",\n", "    \"Approved_HC\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "22b9fca8-ff36-47e2-bf4d-999d66ca18c9", "metadata": {}, "outputs": [], "source": ["approved_hc = approved_hc.drop(columns=[\"Store Name\", \"City\", \"Status\", \"Hiring Flag\", \"Buffer\"])"]}, {"cell_type": "code", "execution_count": null, "id": "8356a8d5-58d4-4a42-9e44-ab59fa062907", "metadata": {}, "outputs": [], "source": ["approved_hc.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4fb7b700-51f4-4dfd-ad8b-d192a7848444", "metadata": {}, "outputs": [], "source": ["merchant_od_hire = pb.from_sheets(\n", "    \"1ux-q6T-iH5A3otWneJRKLSsrUtHejK2aZlsv49DEpb0\",\n", "    \"merchant_od\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "290cb6e3-7ca0-4615-a641-d0b122af4fa9", "metadata": {}, "outputs": [], "source": ["merchant_od_hire.head()"]}, {"cell_type": "code", "execution_count": null, "id": "64337976-b58d-47de-a009-b26eaedcfb6d", "metadata": {}, "outputs": [], "source": ["merchant_od_hire[\"outlet_id\"] = merchant_od_hire[\"outlet_id\"].fillna(0)\n", "merchant_od_hire[\"od_to_be_hired\"] = merchant_od_hire[\"od_to_be_hired\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "453d6a20-faaf-4109-a143-de54b6d17c75", "metadata": {}, "outputs": [], "source": ["approved_hc.rename(\n", "    columns={\n", "        \"Outlet ID\": \"outlet_id\",\n", "        \"Headcount Approved for Store\": \"approved_hc\",\n", "        \"To-be-Hired\": \"to_be_hired\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "86a562d4-df59-4760-9662-f60f9a55e78b", "metadata": {}, "outputs": [], "source": ["approved_hc[\"approved_hc\"] = approved_hc[\"approved_hc\"].fillna(0)\n", "approved_hc[\"to_be_hired\"] = approved_hc[\"to_be_hired\"].fillna(0)\n", "approved_hc[\"od_to_be_hired\"] = approved_hc[\"od_to_be_hired\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "4d6c1753-6f28-4eba-bd65-3305bdb8288a", "metadata": {}, "outputs": [], "source": ["approved_hc[\"approved_hc\"] = approved_hc[\"approved_hc\"].astype(str)\n", "approved_hc[\"to_be_hired\"] = approved_hc[\"to_be_hired\"].astype(str)\n", "approved_hc.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b02b5fd5-1e9a-457d-80a2-0f25f217d2b6", "metadata": {}, "outputs": [], "source": ["approved_hc = approved_hc[\n", "    (~approved_hc[\"outlet_id\"].isna())\n", "    & (~approved_hc[\"approved_hc\"].isna())\n", "    & (approved_hc[\"approved_hc\"].str.strip() != \"\")\n", "    & (~approved_hc[\"to_be_hired\"].isna())\n", "    & (approved_hc[\"approved_hc\"].str.strip() != \"\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d8d8c34e-04b2-494f-a729-c54af2443e36", "metadata": {}, "outputs": [], "source": ["approved_hc = approved_hc.astype(\n", "    {\n", "        \"outlet_id\": \"int\",\n", "        \"approved_hc\": \"float\",\n", "        \"to_be_hired\": \"float\",\n", "        \"week_start_date\": \"string\",\n", "        \"conversion_perc\": \"string\",\n", "        \"gap\": \"int\",\n", "        \"od_to_be_hired\": \"int\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "91939554-cd6a-44c1-a0fa-b537795cdb48", "metadata": {}, "outputs": [], "source": ["merchant_od_hire[\"date_\"] = current_date"]}, {"cell_type": "code", "execution_count": null, "id": "dae901ac-9b84-4964-bccf-0cfa4874adcc", "metadata": {}, "outputs": [], "source": ["approved_hc[\"date_\"] = current_date"]}, {"cell_type": "code", "execution_count": null, "id": "afaf660c-f2ce-44c9-a379-cd5d4198e7dd", "metadata": {}, "outputs": [], "source": ["merchant_od_hire[\"updated_timestamp\"] = updated_timestamp"]}, {"cell_type": "code", "execution_count": null, "id": "0426c2f7-8541-46f5-b277-0c8077dcadff", "metadata": {}, "outputs": [], "source": ["approved_hc[\"updated_timestamp\"] = updated_timestamp"]}, {"cell_type": "code", "execution_count": null, "id": "af1c5512-de67-4574-91d6-42cf1cd4121f", "metadata": {}, "outputs": [], "source": ["approved_hc.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "a7ac090c-1f77-496a-9263-7e9f82c0a00b", "metadata": {}, "outputs": [], "source": ["merchant_od_hire.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3f8d8d42-e8e0-4d9e-934a-4a1610be35bd", "metadata": {}, "outputs": [], "source": ["missing_cols = [\"approved_hc\", \"to_be_hired\", \"week_start_date\", \"conversion_perc\", \"gap\"]\n", "for col in missing_cols:\n", "    merchant_od_hire[col] = np.nan"]}, {"cell_type": "code", "execution_count": null, "id": "197d72e5-6755-467c-993a-bdb82a6fd09a", "metadata": {}, "outputs": [], "source": ["merchant_od_hire[\"approved_hc\"] = merchant_od_hire[\"approved_hc\"].fillna(0)\n", "merchant_od_hire[\"to_be_hired\"] = merchant_od_hire[\"to_be_hired\"].fillna(0)\n", "merchant_od_hire[\"od_to_be_hired\"] = merchant_od_hire[\"od_to_be_hired\"].fillna(0)\n", "merchant_od_hire[\"gap\"] = merchant_od_hire[\"gap\"].fillna(0)\n", "merchant_od_hire[\"week_start_date\"] = current_date\n", "merchant_od_hire[\"conversion_perc\"] = merchant_od_hire[\"conversion_perc\"].fillna(\"0%\")"]}, {"cell_type": "code", "execution_count": null, "id": "47c6f917-1b3d-402a-bbf2-6c509ebe8aca", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"outlet_id\",\n", "    \"approved_hc\",\n", "    \"to_be_hired\",\n", "    \"week_start_date\",\n", "    \"conversion_perc\",\n", "    \"gap\",\n", "    \"date_\",\n", "    \"od_to_be_hired\",\n", "    \"updated_timestamp\",\n", "]\n", "\n", "merchant_od_hire = merchant_od_hire[columns]"]}, {"cell_type": "code", "execution_count": null, "id": "cf520ad9-633e-4f14-a222-04ecf561b914", "metadata": {}, "outputs": [], "source": ["approved_hc = pd.concat([approved_hc, merchant_od_hire])"]}, {"cell_type": "code", "execution_count": null, "id": "7828c90a-611f-4eb0-9f6a-9644d1fe0704", "metadata": {}, "outputs": [], "source": ["# approved_hc.head()"]}, {"cell_type": "code", "execution_count": null, "id": "787a06ab-e144-4145-ba6e-262c5d983c23", "metadata": {}, "outputs": [], "source": ["try:\n", "    approved_headcount = \"\"\"\n", "\n", "        select * from interim.approved_hc_v9\n", "        where week_start_date < current_date \n", "\n", "        \"\"\"\n", "    approved_headcount = pd.read_sql(approved_headcount, trino_con)\n", "\n", "except ConnectionError as ce:\n", "    column_names = [\n", "        \"outlet_id\",\n", "        \"approved_hc\",\n", "        \"to_be_hired\",\n", "        \"week_start_date\",\n", "        \"conversion_perc\",\n", "        \"gap\",\n", "        \"updated_timestamp\",\n", "        \"od_to_be_hired\",\n", "    ]\n", "    approved_headcount = pd.DataFrame(columns=column_names)\n", "    approved_headcount = approved_headcount.astype(\n", "        {\n", "            \"outlet_id\": \"int\",\n", "            \"approved_hc\": \"float\",\n", "            \"to_be_hired\": \"float\",\n", "            \"week_start_date\": \"string\",\n", "            \"conversion_perc\": \"string\",\n", "            \"gap\": \"int\",\n", "            \"updated_timestamp\": \"datetime64[ns]\",\n", "            \"od_to_be_hired\": \"int\",\n", "        }\n", "    )\n", "\n", "except pd.io.sql.DatabaseError as de:\n", "    column_names = [\n", "        \"outlet_id\",\n", "        \"approved_hc\",\n", "        \"to_be_hired\",\n", "        \"week_start_date\",\n", "        \"conversion_perc\",\n", "        \"gap\",\n", "        \"updated_timestamp\",\n", "        \"od_to_be_hired\",\n", "    ]\n", "    approved_headcount = pd.DataFrame(columns=column_names)\n", "    approved_headcount = approved_headcount.astype(\n", "        {\n", "            \"outlet_id\": \"int\",\n", "            \"approved_hc\": \"float\",\n", "            \"to_be_hired\": \"float\",\n", "            \"week_start_date\": \"string\",\n", "            \"conversion_perc\": \"string\",\n", "            \"gap\": \"int\",\n", "            \"updated_timestamp\": \"datetime64[ns]\",\n", "            \"od_to_be_hired\": \"int\",\n", "        }\n", "    )\n", "\n", "except Exception as e:\n", "    column_names = [\n", "        \"outlet_id\",\n", "        \"approved_hc\",\n", "        \"to_be_hired\",\n", "        \"week_start_date\",\n", "        \"conversion_perc\",\n", "        \"gap\",\n", "        \"updated_timestamp\",\n", "        \"od_to_be_hired\",\n", "    ]\n", "    approved_headcount = pd.DataFrame(columns=column_names)\n", "    approved_headcount = approved_headcount.astype(\n", "        {\n", "            \"outlet_id\": \"int\",\n", "            \"approved_hc\": \"float\",\n", "            \"to_be_hired\": \"float\",\n", "            \"week_start_date\": \"string\",\n", "            \"conversion_perc\": \"string\",\n", "            \"gap\": \"int\",\n", "            \"updated_timestamp\": \"datetime64[ns]\",\n", "            \"od_to_be_hired\": \"int\",\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "8e15e22b-dd43-4e40-8dce-d5efc774766e", "metadata": {}, "outputs": [], "source": ["approved_hc[\"conversion_perc\"] = (\n", "    approved_hc[\"conversion_perc\"].str.replace(\"%\", \"\").str.strip().astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "04d2534b-3b67-4bfb-a820-c4390fd475e9", "metadata": {}, "outputs": [], "source": ["approved_headcount = pd.concat([approved_headcount, approved_hc], ignore_index=True)\n", "approved_headcount = approved_headcount.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "402ee3e8-3b0d-45df-ac54-67b768fdadd4", "metadata": {}, "outputs": [], "source": ["approved_headcount[approved_headcount[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "fad18a38-4e94-4666-9e2d-94fc571ea447", "metadata": {}, "outputs": [], "source": ["approved_headcount[\"updated_timestamp\"] = approved_headcount.apply(\n", "    lambda row: (\n", "        f\"{row['date_']} 00:00:00\"\n", "        if pd.isnull(row[\"updated_timestamp\"])\n", "        else row[\"updated_timestamp\"]\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "approved_headcount[\"updated_timestamp\"] = pd.to_datetime(approved_headcount[\"updated_timestamp\"])"]}, {"cell_type": "code", "execution_count": null, "id": "cfc471fc-16bd-44d7-a8f0-5bc5a0dec0fb", "metadata": {}, "outputs": [], "source": ["approved_headcount = approved_headcount.loc[\n", "    approved_headcount.groupby([\"date_\", \"outlet_id\"])[\"updated_timestamp\"].idxmax()\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0109449c-1392-4b27-9ba8-295ee09f9eb8", "metadata": {}, "outputs": [], "source": ["approved_headcount[\"date_\"] = pd.to_datetime(approved_headcount[\"date_\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "d53b0928-7aaf-491f-ab52-7490246ecd1d", "metadata": {}, "outputs": [], "source": ["approved_headcount[\"week_start_date\"] = pd.to_datetime(\n", "    approved_headcount[\"week_start_date\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5d82f473-df15-4ee7-858a-425633569766", "metadata": {}, "outputs": [], "source": ["approved_headcount[\"od_to_be_hired\"] = approved_headcount[\"od_to_be_hired\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "f28ad083-fd3d-44fc-adb4-69ce8880e59f", "metadata": {}, "outputs": [], "source": ["current_date"]}, {"cell_type": "code", "execution_count": null, "id": "c791ef94-31a5-4f6f-be64-1027857aa1f6", "metadata": {}, "outputs": [], "source": ["approved_headcount.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "a3c644c3-dca1-4f60-8605-60552bc9d205", "metadata": {}, "outputs": [], "source": ["approved_headcount = approved_headcount[\n", "    approved_headcount[\"date_\"] >= pd.Timestamp.today() - <PERSON><PERSON><PERSON>(days=6)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9af9d5bb-996c-490c-af3b-f4a6d7863637", "metadata": {}, "outputs": [], "source": ["approved_headcount.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "204a9dfc-94af-4857-ab22-dd041f7d50d4", "metadata": {}, "outputs": [], "source": ["approved_headcount = approved_headcount.astype(\n", "    {\n", "        \"outlet_id\": \"int\",\n", "        \"approved_hc\": \"int\",\n", "        \"to_be_hired\": \"int\",\n", "        \"conversion_perc\": \"int\",\n", "        \"gap\": \"int\",\n", "        \"od_to_be_hired\": \"int\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4864952b-a253-46ef-8df0-bb5a5feb4d26", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"approved_hc_v9\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date_\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date\",\n", "        },\n", "        {\n", "            \"name\": \"outlet_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Unique ID for the outlet\",\n", "        },\n", "        {\n", "            \"name\": \"approved_hc\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Approved Headcount for the outlet\",\n", "        },\n", "        {\n", "            \"name\": \"to_be_hired\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"To be Hired number for the outlet\",\n", "        },\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"week start date\",\n", "        },\n", "        {\n", "            \"name\": \"conversion_perc\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Approved Headcount for the outlet\",\n", "        },\n", "        {\n", "            \"name\": \"gap\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"To be Hired number for the outlet\",\n", "        },\n", "        {\n", "            \"name\": \"updated_timestamp\",\n", "            \"type\": \"timestamp(6)\",\n", "            \"description\": \"updated_timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"od_to_be_hired\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"To be Hired O<PERSON> number for the outlet\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"outlet_id\", \"date_\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"This table contains outlet slot data for on_demand pickers.\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c3beb169-2f9a-46e8-bd1c-a3ff6279b90f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(approved_headcount, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "478d146b-e307-4c99-86c1-ac8b36a1d223", "metadata": {}, "outputs": [], "source": ["# approved_hc[approved_hc[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "89228991-767b-4c91-b8bf-a535d8226751", "metadata": {}, "outputs": [], "source": ["store_performance = \"\"\"\n", "with dates as \n", "(\n", "select date\n", "from\n", "storeops_etls.fact_daily_instore_mandays\n", "where \n", "date >= current_date - interval '20' day\n", "group by 1\n", "),\n", "\n", "emp as\n", "(\n", "select employee_id\n", "from \n", "storeops_etls.fact_daily_instore_mandays\n", "where \n", "date >= current_date - interval '20' day\n", "and employee_id like 'GC%%'\n", "group by 1\n", "),\n", "\n", "de as \n", "(\n", "select * \n", "from dates \n", "join emp\n", "on 1=1\n", "),\n", "\n", "employee_active_base as \n", "(\n", "select date, employee_id, case when total_mandays_3_day >= 0.5 then 1 else 0 end as active_3_day_flag, case when total_mandays_7_day >= 0.5 then 1 else 0 end as active_7_day_flag\n", "from\n", "(select date, employee_id, sum(manday) over(PARTITION BY employee_id ORDER BY date RANGE BETWEEN INTERVAL '3' DAY PRECEDING AND CURRENT ROW) as total_mandays_3_day,\n", "sum(manday) over(PARTITION BY employee_id ORDER BY date RANGE BETWEEN INTERVAL '7' DAY PRECEDING AND CURRENT ROW) as total_mandays_7_day\n", "from\n", "(\n", "select d.date, d.employee_id, coalesce(manday,0) as manday\n", "from de d\n", "left join\n", "storeops_etls.fact_daily_instore_mandays m\n", "on d.date = m.date and d.employee_id = m.employee_id \n", ")\n", ")\n", "),\n", "\n", "final_base as \n", "(select e.date, e.employee_id, active_3_day_flag, active_7_day_flag,\n", "coalesce(designation,LAST_VALUE(designation) IGNORE NULLS OVER (\n", "                 PARTITION BY e.employee_id \n", "                 ORDER BY e.date \n", "                 ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW\n", "             )) as designation,\n", "coalesce(outlet_id,LAST_VALUE(outlet_id) IGNORE NULLS OVER (\n", "                 PARTITION BY e.employee_id \n", "                 ORDER BY e.date \n", "                 ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW\n", "             )) as outlet_id\n", "from employee_active_base e\n", "left join\n", "storeops_etls.fact_daily_instore_mandays m\n", "on e.date = m.date and e.employee_id = m.employee_id\n", "),\n", "\n", "inactive_base as \n", "(\n", "select employee_id, final_status, updated_date\n", "from\n", "(\n", "select date(update_ts + interval '330' minute) as updated_date, update_ts + interval '330' minute as updated_on,\n", "        json_extract_scalar(initial_value, '$.employee_id') as employee_id,\n", "        json_extract_scalar(initial_value, '$.status') as initial_status,\n", "        json_extract_scalar(final_value, '$.status') as final_status,\n", "        row_number() over (partition by json_extract_scalar(initial_value, '$.employee_id') order by update_ts desc) as rnk\n", "        from ops_management.ops_management_transaction_logs\n", "        where insert_ds_ist between cast(current_date - interval '90' day as varchar) and cast(current_date - interval '1' day as varchar)\n", "        and table_name = 'user'\n", "        and json_extract_scalar(initial_value, '$.status') <> json_extract_scalar(final_value, '$.status')\n", "        and json_extract_scalar(initial_value, '$.employee_id') like 'GC%%'\n", ")\n", "where final_status = 'DEACTIVATED'\n", "and rnk = 1\n", "), \n", "\n", "last_30_days as (select \n", "employee_id,\n", "max_by(outlet_id,date_) latest_outlet_id,\n", "max_by(outlet_name,date_) latest_outlet_name,\n", "max_by(city_name,date_) latest_city_name,\n", "count(distinct outlet_id) unique_outlets\n", "from storeops_etls.employee_metrics_daily\n", "where date_ between current_date - interval '30' day and current_date - interval '1' day\n", "and employee_id like 'GC%%'\n", "and designation = 'Captain ERT'\n", "group by 1\n", "having count(distinct outlet_id) = 1)\n", ",\n", "\n", "derived_mid as \n", "(\n", "select f.*, a.employee_id as employee_id1, coalesce(final_status, 'ACTIVE') as final_status\n", "from final_base f\n", "left join inactive_base a\n", "on f.employee_id = a.employee_id AND f.date >=  a.updated_date\n", "), \n", "\n", "derived as\n", "(\n", "select date, \n", "employee_id, \n", "active_3_day_flag, \n", "active_7_day_flag,\n", "case when fixed_ert is not null and designation = 'Captain ERT' then 'Fixed Captain ERT' else designation end as designation, \n", "outlet_id, \n", "employee_id1, \n", "final_status\n", "from \n", "(\n", "select d.*,  l.employee_id as fixed_ert\n", "from derived_mid d\n", "left join\n", "last_30_days l\n", "on d.employee_id = l.employee_id\n", ")\n", "), \n", "\n", "headcount as \n", "(\n", "select e.date, e.outlet_id, \n", "count(distinct case when manday>=0.5 and m.designation in ('Captain','Fnv Executive','Express Picker','Assistant','Inventory Executive') then m.employee_id end) as present_day_headcount, \n", "count(distinct case when manday>=0.5 and m.designation = 'Captain ERT' then m.employee_id end) as present_day_headcount_ERT, \n", "count(distinct case when manday>=0.5 AND m.designation = 'Captain O<PERSON>' then m.employee_id end) as present_day_headcount_OD,\n", "count(distinct case when e.designation in ('Captain','Fnv Executive','Express Picker','Assistant','Inventory Executive') and active_3_day_flag = 1 then e.employee_id end ) as active3_day_headcount,\n", "count(distinct case when e.designation = 'Captain ERT' and active_3_day_flag = 1 then e.employee_id end ) as active3_day_headcount_ERT,\n", "count(distinct case when e.designation = 'Captain O<PERSON>' and active_3_day_flag = 1 then e.employee_id end ) as active3_day_headcount_OD,\n", "count(distinct case when e.designation in ('Captain','Fnv Executive','Express Picker','Assistant','Inventory Executive') and active_7_day_flag = 1 then e.employee_id end ) as active7_day_headcount,\n", "count(distinct case when e.designation = 'Captain ERT' and active_7_day_flag = 1 then e.employee_id end ) as active7_day_headcount_ERT,\n", "count(distinct case when e.designation = 'Captain O<PERSON>' and active_7_day_flag = 1 then e.employee_id end ) as active7_day_headcount_OD,\n", "count(distinct case when e.designation = 'Fixed Captain ERT' and active_3_day_flag = 1 then e.employee_id end ) as active3_day_headcount_fixed_ERT\n", "from\n", "(\n", "select d.*\n", "from derived d\n", "-- where d.outlet_id = 1024\n", "-- and d.date = date'2024-10-06'\n", "where final_status != 'DEACTIVATED'\n", ") e\n", "left join \n", "storeops_etls.fact_daily_instore_mandays m\n", "on e.date = m.date and e.outlet_id = m.outlet_id\n", "where e.outlet_id is not null\n", "--and e.date = date('2024-10-06')\n", "group by 1,2), \n", "\n", "\n", "empStore AS\n", "  (SELECT employee_id,\n", "          site_id,\n", "          ROW_NUMBER() OVER (PARTITION BY employee_id\n", "                             ORDER BY update_ts DESC) AS rank_\n", "   FROM ops_management.user_management_usersites),\n", "\n", "BASE AS \n", "(SELECT \n", "distinct \n", "date(r.install_ts) as date_, \n", "r.id as resignationId, \n", "r.employee_id, \n", "us.site_id, \n", "u.name as employee<PERSON><PERSON>, \n", "m.name as StoreName, \n", "m.city_name, \n", "date_diff('day', date_of_joining, r.update_ts )/30.42 as ExperienceInMonths,\n", "r.meta as penaltyDetails, \n", "r.reason, \n", "r.status,\n", "cast(end_date as date) as lwd,\n", "json_value(r.meta, 'lax $.penalty_cost') as penalty,\n", "json_value(r.meta, 'lax $.penalty_days') as noticeGiven\n", "\n", "\n", "FROM ops_management.resignation_management r\n", "JOIN empStore us ON us.employee_id = r.employee_id and rank_ = 1 \n", "JOIN ops_management.user u ON u.employee_id = r.employee_id\n", "JOIN ops_management.master_site m ON us.site_id = m.site_id\n", "\n", "WHERE r.update_ts >= current_date - interval '37' day\n", "and r.install_ts >= current_date - interval '37' day\n", "and r.employee_id not in  ('GR6593') and r.status = 'Approved'),\n", "\n", "\n", "resign_3 as \n", "(select date, outlet_id, count(distinct b.employee_id) as resigned_3_day\n", "from headcount h\n", "join\n", "base b\n", "on b.lwd between h.date and h.date + interval '2' day and h.outlet_id = try_cast(b.site_id as integer)\n", "group by 1,2\n", "order by 1,2),\n", "\n", "resign_7 as \n", "(select date, outlet_id, count(distinct b.employee_id) as resigned_7_day\n", "from headcount h\n", "join \n", "base b\n", "on b.lwd between h.date and h.date + interval '6' day and h.outlet_id = try_cast(b.site_id as integer)\n", "group by 1,2\n", "order by 1,2),\n", "\n", "final3 as \n", "(\n", "select h.*, resigned_3_day, resigned_7_day  \n", "from headcount h\n", "left join\n", "resign_3 as r3\n", "on h.date = r3.date and h.outlet_id = r3.outlet_id\n", "left join\n", "resign_7 as r7\n", "on h.date = r7.date and h.outlet_id = r7.outlet_id\n", "),\n", "\n", "budgeted_headcount as \n", "(\n", "select outlet_id, date,\n", "max_by(city, updated_on) as city, \n", "max_by(tracker_max_headcount, updated_on) as budgeted_hc,\n", "max_by(tracker_hiring_trigger_headcount, updated_on) as hiring_trigger_headcount,\n", "max_by(picking_quantity, updated_on) as forecasted_picking_quantity,\n", "max_by(tracker_mandays, updated_on) as forecasted_tracker_mandays\n", "from storeops_etls.instore_forecasted_mandays\n", "where updated_on >= current_date - interval '200' day\n", "group by 1,2\n", "),\n", "\n", "return_final as \n", "(\n", "select f3.*\n", "from\n", "final3 as f3\n", "where f3.outlet_id is NOT null\n", "order by 2,1\n", "),\n", "\n", "Forecast_on_date as\n", "(\n", "  select a.outlet_id outlet_id, outlet_name, city, order_date, coalesce(orders,0) as projected_orders, \n", "  avg(orders) over(partition by a.outlet_id order by order_date RANGE BETWEEN INTERVAL '7' DAY PRECEDING AND INTERVAL '1' DAY PRECEDING) as avg_opd_projected_last_7_days\n", "  from\n", "    (\n", "      select date as order_date, outletid as outlet_id, max_by(carts, updated_on) as orders\n", "      from logistics_data_etls.cart_projections\n", "      where updated_on >= current_date - interval '35' day\n", "      and date between current_date - interval '30' day\n", "        and current_date + interval '10' day\n", "        and carts > 0\n", "        and outletid < 10000\n", "      group by 1,2\n", "    ) a\n", "     left join\n", "       (\n", "         select pos_outlet_city_name as city, pos_outlet_id as outlet_id, pos_outlet_name as outlet_name\n", "         from dwh.dim_merchant_outlet_facility_mapping where is_current=true\n", "         group by 1,2,3\n", "       ) b on a.outlet_id=b.outlet_id\n", "  order by 4,1\n", "),\n", "\n", "return_final2 as \n", "(select f.outlet_id, mode_of_ops, status, f.outlet_name, f.city, f.order_date, f.projected_orders,  f.avg_opd_projected_last_7_days\n", "from Forecast_on_date f\n", "left join\n", "storeops_etls.store_details s\n", "on f.outlet_id = s.outlet_id\n", "order by 1,4),\n", "\n", "C2A AS \n", "(\n", "     select order_checkout_dt_ist AS date_, outlet_id,\n", "     sum(date_diff('second', order_checkout_ts_ist,order_picker_assigned_ts_ist)) AS total_c2a_time_sec,\n", "     count(case when date_diff('second', order_checkout_ts_ist,order_picker_assigned_ts_ist) <= 10 then order_id end) AS order_with_c2a_under_10_sec_count\n", "     from dwh.fact_supply_chain_order_details\n", "     where order_checkout_dt_ist >= current_date - interval '36' day\n", "     and is_rescheduled=false\n", "     and order_type = 'RetailForwardOrder'\n", "     and order_picking_completed_ts_ist is not null\n", "     group by 1,2\n", "),\n", "\n", "surge_seen AS\n", "(\n", "     select\n", "     date date_,\n", "     city_name,\n", "     merchant_id,\n", "     pos_outlet_id as outlet_id,\n", "     pos_outlet_name as outlet_name,\n", "     sum(gsp_surge_carts) AS rider_surge,\n", "     sum(picker_surge_carts) AS picker_surge,\n", "     sum(rain_surge_carts) AS rain_surge,\n", "     sum(gsp_and_picker_surge_carts) AS both_surge\n", "     from\n", "     serviceability_etls.surge_seen_hourly s\n", "       join \n", "         (\n", "         select distinct pos_outlet_id, pos_outlet_name, frontend_merchant_id\n", "          from dwh.dim_merchant_outlet_facility_mapping\n", "          where is_current = TRUE\n", "                AND is_mapping_enabled = TRUE\n", "                AND is_current_mapping_active = TRUE\n", "                and pos_outlet_id not in (4146,4381)\n", "          ) m on m.frontend_merchant_id = s.merchant_id\n", "     where\n", "     date >= cast(current_date - interval '36' day as varchar)\n", "     and hour <> -1\n", "     group by\n", "     1,2,3,4,5\n", "),\n", "\n", "PPI AS\n", "(\n", "     select\n", "     order_date AS date_,\n", "     outlet_id,\n", "     picker_busy_time,\n", "     total_items_quantity_ordered,\n", "     orders_w_picker_pna,\n", "     instore_orders,\n", "     total_qty_store_ipp as total_item_picked_qty,\n", "     total_mandays_store_ipp as total_manday,\n", "     picking_time as picking_time_sec,\n", "     picker_active_time*60 as picker_active_time,\n", "     avg(instore_orders) over (partition by outlet_id order by cast(order_date as date) RANGE BETWEEN INTERVAL '7' DAY PRECEDING AND INTERVAL '1' DAY PRECEDING) as avg_opd_last_7_day\n", "     from storeops_etls.instore_metrics_daily\n", "     where order_date >= cast(current_date - interval '36' day as varchar)\n", "),\n", "\n", "final as \n", "(select c.date_, c.outlet_id, s.outlet_name, s.city_name, c.total_c2a_time_sec, c.order_with_c2a_under_10_sec_count, s.rider_surge, s.picker_surge,\n", "s.rain_surge, s.both_surge, p. picker_busy_time,  p.total_items_quantity_ordered, p.orders_w_picker_pna,\n", "p.instore_orders, p.total_item_picked_qty, p.total_manday, p.picker_active_time,p.picking_time_sec, p.avg_opd_last_7_day\n", "from\n", "C2A as c\n", "left join\n", "surge_seen s\n", "on c.date_ = cast(s.date_ as date) and c.outlet_id = s.outlet_id\n", "left join\n", "PPI p\n", "on c.date_ = cast(p.date_ as date) and c.outlet_id = p.outlet_id\n", "order by 1),\n", "\n", "return_final3 as \n", "(select f.*, s.status, s.mode_of_ops from final f\n", "left join\n", "storeops_etls.store_details as s\n", "on f.outlet_id = s.outlet_id),\n", "\n", "return_final4 as \n", "(\n", "select rf.date, rf.outlet_id\n", "from return_final as rf\n", "union\n", "select rf2.order_date , rf2.outlet_id\n", "from return_final2 as rf2\n", "union\n", "select rf3.date_, rf3.outlet_id\n", "from return_final3 as rf3\n", "), \n", "\n", "base_final as \n", "(\n", "select \n", "    rf4.date as snapshot_date_ist, \n", "    rf4.outlet_id as outlet_id, \n", "    name as outlet_name,\n", "    location as city_name,\n", "    rf.present_day_headcount,\n", "    rf.present_day_headcount_ERT as present_day_headcount_ert, \n", "    rf.present_day_headcount_OD as present_day_headcount_od, \n", "    rf.active3_day_headcount as active_3_day_headcount, \n", "    rf.active3_day_headcount_ERT as active_3_day_headcount_ert, \n", "    rf.active3_day_headcount_OD as active_3_day_headcount_od,\n", "    rf.active7_day_headcount as active_7_day_headcount, \n", "    rf.active7_day_headcount_ERT as active_7_day_headcount_ert, \n", "    rf.active7_day_headcount_OD as active_7_day_headcount_od,\n", "    rf.active3_day_headcount_fixed_ERT as active3_day_headcount_fixed_ert,\n", "    rf.resigned_3_day,\n", "    rf.resigned_7_day,\n", "    b.budgeted_hc as budgeted_headcount,\n", "    b.hiring_trigger_headcount as hiring_trigger_headcount,\n", "    rf2.projected_orders as last_updated_projected_orders,\n", "    rf2.avg_opd_projected_last_7_days as avg_projected_orders_last_7_day, \n", "    0 as initial_projected_orders,\n", "    rf3.total_c2a_time_sec,\n", "    rf3.order_with_c2a_under_10_sec_count,\n", "    rf3.rider_surge as rider_surge_seen_cart_count, \n", "    rf3.picker_surge picker_surge_seen_cart_count,\n", "    rf3.rain_surge rain_surge_seen_cart_count, \n", "    rf3.both_surge both_surge_seen_cart_count, \n", "    rf3.picker_busy_time as picker_busy_time_sec,\n", "    rf3.picker_active_time as picker_active_time_sec,\n", "    rf3.picking_time_sec,\n", "    rf3.total_items_quantity_ordered, \n", "    rf3.orders_w_picker_pna,\n", "    rf3.instore_orders, \n", "    rf3.avg_opd_last_7_day as avg_orders_last_7_day,\n", "    rf3.total_item_picked_qty, \n", "    rf3.total_manday,\n", "    b.forecasted_picking_quantity,\n", "    b.forecasted_tracker_mandays\n", "from return_final4 as rf4\n", "left join return_final as rf\n", "on rf4.date = rf.date and rf4.outlet_id = rf.outlet_id \n", "left join return_final2 as rf2\n", "on rf4.date = rf2.order_date and rf4.outlet_id = rf2.outlet_id\n", "left join return_final3 as rf3\n", "on rf4.date = rf3.date_ and rf4.outlet_id = rf3.outlet_id\n", "left join budgeted_headcount as b\n", "on rf4.date = b.date and rf4.outlet_id = b.outlet_id\n", "left join retail.console_outlet rc\n", "on rf4.outlet_id = rc.id\n", "order by 1,2\n", ") \n", "\n", "\n", "select \n", "    snapshot_date_ist, \n", "    outlet_id, \n", "    outlet_name,\n", "    city_name,\n", "    present_day_headcount,\n", "    present_day_headcount_ert, \n", "    present_day_headcount_od, \n", "    active_3_day_headcount, \n", "    active_3_day_headcount_ert, \n", "    active_3_day_headcount_od,\n", "    active_7_day_headcount, \n", "    active_7_day_headcount_ert, \n", "    active_7_day_headcount_od,\n", "    active3_day_headcount_fixed_ert,\n", "    resigned_3_day,\n", "    resigned_7_day,\n", "    budgeted_headcount,\n", "    hiring_trigger_headcount,\n", "    last_updated_projected_orders,\n", "    avg_projected_orders_last_7_day, \n", "    initial_projected_orders,\n", "    total_c2a_time_sec,\n", "    order_with_c2a_under_10_sec_count,\n", "    rider_surge_seen_cart_count, \n", "    picker_surge_seen_cart_count,\n", "    rain_surge_seen_cart_count, \n", "    both_surge_seen_cart_count, \n", "    picker_busy_time_sec,\n", "    picker_active_time_sec,\n", "    picking_time_sec,\n", "    total_items_quantity_ordered, \n", "    orders_w_picker_pna,\n", "    instore_orders, \n", "    avg_orders_last_7_day,\n", "    total_item_picked_qty, \n", "    total_manday,\n", "    forecasted_picking_quantity,\n", "    forecasted_tracker_mandays,\n", "    actual_ERT_3_day_headcount\n", "from    \n", "(\n", "select *, row_number() over(partition by outlet_id, snapshot_date_ist order by present_day_headcount desc) as rnk\n", "from\n", "(\n", "select bf.*, actual_ERT_3_day_headcount\n", "from base_final bf \n", "left join\n", "(\n", "select date, outlet_id, count(distinct case when active3_day_manday >= 0.5 then employee_id end) as actual_ERT_3_day_headcount\n", "from\n", "(\n", "select date, employee_id, outlet_id, COALESCE(SUM(manday) OVER(PARTITION BY employee_id, outlet_id ORDER BY date RANGE BETWEEN INTERVAL '3' DAY PRECEDING AND CURRENT ROW),0) as active3_day_manday\n", "from \n", "(\n", "select \n", "date, m.employee_id, designation , max_by(site_id, update_ts) as outlet_id, manday\n", "from \n", "ops_management.user_management_usersites u\n", "inner join\n", "storeops_etls.fact_daily_instore_mandays m \n", "on u.employee_id = m.employee_id\n", "group by 1,2,3,5\n", ")\n", "where date >= current_date - interval '36' day \n", "and designation = 'Captain ERT'\n", ")\n", "group by 1,2\n", ") a on bf.snapshot_date_ist = a.date and bf.outlet_id = cast(a.outlet_id as integer)\n", "where snapshot_date_ist >= current_date - interval '30' day\n", ")\n", ")\n", "where rnk = 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "336298e8-3949-4714-8ced-b3f4bd7d2b02", "metadata": {}, "outputs": [], "source": ["instore_data = pd.read_sql(store_performance, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "c8e2e0b9-6135-4f5b-abfc-862b8cb35e30", "metadata": {}, "outputs": [], "source": ["# instore_data[instore_data[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "188e2ec3-883d-40c9-9ba4-33077a51ff30", "metadata": {}, "outputs": [], "source": ["instore_data[\"snapshot_date_ist\"] = pd.to_datetime(instore_data[\"snapshot_date_ist\"])\n", "instore_data[\"outlet_id\"] = pd.to_numeric(instore_data[\"outlet_id\"]).astype(int)\n", "instore_data[\"outlet_name\"] = instore_data[\"outlet_name\"].astype(str)\n", "instore_data[\"city_name\"] = instore_data[\"city_name\"].astype(str)\n", "instore_data[\"present_day_headcount\"] = instore_data[\"present_day_headcount\"].fillna(0).astype(int)\n", "instore_data[\"present_day_headcount_ert\"] = (\n", "    instore_data[\"present_day_headcount_ert\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"present_day_headcount_od\"] = (\n", "    instore_data[\"present_day_headcount_od\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_3_day_headcount\"] = (\n", "    instore_data[\"active_3_day_headcount\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_3_day_headcount_ert\"] = (\n", "    instore_data[\"active_3_day_headcount_ert\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_3_day_headcount_od\"] = (\n", "    instore_data[\"active_3_day_headcount_od\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_7_day_headcount\"] = (\n", "    instore_data[\"active_7_day_headcount\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_7_day_headcount_ert\"] = (\n", "    instore_data[\"active_7_day_headcount_ert\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"active_7_day_headcount_od\"] = (\n", "    instore_data[\"active_7_day_headcount_od\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"actual_ERT_3_day_headcount\"] = (\n", "    instore_data[\"actual_ERT_3_day_headcount\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"resigned_3_day\"] = instore_data[\"resigned_3_day\"].fillna(0).astype(int)\n", "instore_data[\"resigned_7_day\"] = instore_data[\"resigned_7_day\"].fillna(0).astype(int)\n", "instore_data[\"budgeted_headcount\"] = instore_data[\"budgeted_headcount\"].fillna(0).astype(int)\n", "instore_data[\"hiring_trigger_headcount\"] = (\n", "    instore_data[\"hiring_trigger_headcount\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"last_updated_projected_orders\"] = (\n", "    pd.to_numeric(instore_data[\"last_updated_projected_orders\"], errors=\"coerce\")\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "instore_data[\"initial_projected_orders\"] = (\n", "    pd.to_numeric(instore_data[\"initial_projected_orders\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "instore_data[\"total_c2a_time_sec\"] = (\n", "    pd.to_numeric(instore_data[\"total_c2a_time_sec\"], errors=\"coerce\").fillna(0).astype(float)\n", ")\n", "instore_data[\"order_with_c2a_under_10_sec_count\"] = (\n", "    pd.to_numeric(instore_data[\"order_with_c2a_under_10_sec_count\"], errors=\"coerce\")\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "instore_data[\"rider_surge_seen_cart_count\"] = (\n", "    instore_data[\"rider_surge_seen_cart_count\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"picker_surge_seen_cart_count\"] = (\n", "    instore_data[\"picker_surge_seen_cart_count\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"rain_surge_seen_cart_count\"] = (\n", "    instore_data[\"rain_surge_seen_cart_count\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"both_surge_seen_cart_count\"] = (\n", "    instore_data[\"both_surge_seen_cart_count\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"picker_busy_time_sec\"] = (\n", "    pd.to_numeric(instore_data[\"picker_busy_time_sec\"], errors=\"coerce\").fillna(0).astype(float)\n", ")\n", "instore_data[\"picker_active_time_sec\"] = (\n", "    pd.to_numeric(instore_data[\"picker_active_time_sec\"], errors=\"coerce\").fillna(0).astype(float)\n", ")\n", "instore_data[\"picking_time_sec\"] = (\n", "    pd.to_numeric(instore_data[\"picking_time_sec\"], errors=\"coerce\").fillna(0).astype(float)\n", ")\n", "instore_data[\"total_items_quantity_ordered\"] = (\n", "    instore_data[\"total_items_quantity_ordered\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"orders_w_picker_pna\"] = instore_data[\"orders_w_picker_pna\"].fillna(0).astype(int)\n", "instore_data[\"instore_orders\"] = instore_data[\"instore_orders\"].fillna(0).astype(int)\n", "instore_data[\"total_item_picked_qty\"] = instore_data[\"total_item_picked_qty\"].fillna(0).astype(int)\n", "instore_data[\"total_manday\"] = (\n", "    pd.to_numeric(instore_data[\"total_manday\"], errors=\"coerce\").fillna(0).astype(float)\n", ")\n", "instore_data[\"forecasted_picking_quantity\"] = (\n", "    instore_data[\"forecasted_picking_quantity\"].fillna(0).astype(int)\n", ")\n", "instore_data[\"forecasted_tracker_mandays\"] = (\n", "    pd.to_numeric(instore_data[\"forecasted_tracker_mandays\"], errors=\"coerce\")\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "instore_data[\"avg_orders_last_7_day\"] = (\n", "    pd.to_numeric(instore_data[\"avg_orders_last_7_day\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "instore_data[\"avg_projected_orders_last_7_day\"] = (\n", "    pd.to_numeric(instore_data[\"avg_projected_orders_last_7_day\"], errors=\"coerce\")\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fe0aa2a4-ee03-483d-9623-fa8580e43a75", "metadata": {}, "outputs": [], "source": ["data = instore_data"]}, {"cell_type": "code", "execution_count": null, "id": "081825ee-2989-410a-805e-707a6a7aeb5e", "metadata": {}, "outputs": [], "source": ["outlet_mapping = \"\"\"select s.outlet_id, coalesce(mode_of_ops, 'Rest') as mode_of_ops, 'Live' as status_start_of_month, 'Live' current_status, outlet_name, c.city\n", "from storeops_etls.store_details s\n", "left join\n", "(\n", "select cl.name as city, co.id as outlet_id, co.name as outlet_name\n", "from retail.console_outlet co\n", "left join retail.console_location cl on co.tax_location_id = cl.id\n", "group by 1,2,3\n", ") c\n", "on s.outlet_id = c.outlet_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5368612d-9343-44d3-ac0c-d9af5e0f03b6", "metadata": {}, "outputs": [], "source": ["outlet_mapping = pd.read_sql(outlet_mapping, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "71f2d244-3431-4ef7-916c-d55b15e2d4a6", "metadata": {}, "outputs": [], "source": ["outlet_mapping[\"outlet_id\"] = (\n", "    pd.to_numeric(outlet_mapping[\"outlet_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6d882668-98ab-41a0-843e-0e64c73dd681", "metadata": {}, "outputs": [], "source": ["final_data = pd.merge(\n", "    data,\n", "    outlet_mapping,\n", "    left_on=[\"outlet_id\"],\n", "    right_on=[\"outlet_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "46fc861e-2ba4-4552-8dab-c34e9e4584d8", "metadata": {}, "outputs": [], "source": ["final_data.rename(columns={\"outlet_name_x\": \"outlet_name\"}, inplace=True)\n", "final_data.rename(columns={\"resigned_3_day\": \"resigned_next_3_day\"}, inplace=True)\n", "final_data.rename(columns={\"resigned_7_day\": \"resigned_next_7_day\"}, inplace=True)\n", "final_data.rename(\n", "    columns={\"actual_ERT_3_day_headcount\": \"actual_3_day_headcount_ert\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "052841b0-88b1-479f-b038-b93e8a11fa3c", "metadata": {}, "outputs": [], "source": ["final_data.drop(columns=[\"outlet_name_y\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "47540379-dfb6-4c56-ad80-20d08da00530", "metadata": {}, "outputs": [], "source": ["final_data[\"week_start_date\"] = final_data[\"snapshot_date_ist\"].dt.to_period(\"W\").dt.start_time"]}, {"cell_type": "code", "execution_count": null, "id": "f5daa1e4-dfcc-49a7-8e20-4aaa1c99dc65", "metadata": {}, "outputs": [], "source": ["# final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "cbf55c69-b8a0-47a0-8356-1f6af0ad2d9d", "metadata": {}, "outputs": [], "source": ["current_date = pd.to_datetime(current_date)"]}, {"cell_type": "code", "execution_count": null, "id": "618dd97b-44d7-4053-95ae-39e10f38e033", "metadata": {}, "outputs": [], "source": ["current_date"]}, {"cell_type": "code", "execution_count": null, "id": "e857c6bc-fb3b-408c-9c0d-b5315efc32c0", "metadata": {}, "outputs": [], "source": ["# final_data[final_data[\"outlet_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "b854a235-60c6-4553-b20e-40cab3c2d860", "metadata": {}, "outputs": [], "source": ["final_data[\"snapshot_date_for_merge\"] = final_data[\"snapshot_date_ist\"].apply(\n", "    lambda x: current_date if x >= current_date else x\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8ead5e5-1cce-4466-8e6e-802e826884fe", "metadata": {}, "outputs": [], "source": ["final_data[\"snapshot_date_for_merge\"] = pd.to_datetime(final_data[\"snapshot_date_for_merge\"])\n", "\n", "final_data = pd.merge(\n", "    final_data,\n", "    approved_headcount,\n", "    left_on=[\"snapshot_date_for_merge\", \"outlet_id\"],\n", "    right_on=[\"date_\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_data.drop(columns=[\"snapshot_date_for_merge\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "28108c83-d98c-4007-a584-b84b1979a17b", "metadata": {}, "outputs": [], "source": ["final_data[\"active3_day_headcount_fixed_ert\"] = (\n", "    final_data[\"active3_day_headcount_fixed_ert\"].fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f1c2c5ba-d2cd-4ecb-bd57-f8f7ed848b3e", "metadata": {}, "outputs": [], "source": ["final_data[\"approved_hc\"] = final_data[\"approved_hc\"].fillna(0).astype(int)\n", "final_data[\"to_be_hired\"] = final_data[\"to_be_hired\"].fillna(0).astype(int)\n", "final_data[\"conversion_perc\"] = final_data[\"conversion_perc\"].fillna(100).astype(int)\n", "final_data[\"gap\"] = final_data[\"gap\"].fillna(0).astype(int)\n", "final_data[\"od_to_be_hired\"] = final_data[\"od_to_be_hired\"].fillna(0).astype(int)\n", "final_data[\"od_mandays\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "1ca05342-65bd-4f6d-8671-674aad73a182", "metadata": {}, "outputs": [], "source": ["final_data.rename(columns={\"week_start_date_x\": \"week_start_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8ac41478-bd99-4e84-8002-f8c1bdaf63cc", "metadata": {}, "outputs": [], "source": ["required_columns = [\n", "    \"snapshot_date_ist\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"mode_of_ops\",\n", "    \"city_name\",\n", "    \"status_start_of_month\",\n", "    \"current_status\",\n", "    \"present_day_headcount\",\n", "    \"present_day_headcount_ert\",\n", "    \"present_day_headcount_od\",\n", "    \"active_3_day_headcount\",\n", "    \"active_3_day_headcount_ert\",\n", "    \"active_3_day_headcount_od\",\n", "    \"active_7_day_headcount\",\n", "    \"active_7_day_headcount_ert\",\n", "    \"active_7_day_headcount_od\",\n", "    \"actual_3_day_headcount_ert\",\n", "    \"resigned_next_3_day\",\n", "    \"resigned_next_7_day\",\n", "    \"lineups_3_day\",\n", "    \"selected_3_day\",\n", "    \"first_shift_start_3_day\",\n", "    \"lineups_7_day\",\n", "    \"selected_7_day\",\n", "    \"first_shift_start_7_day\",\n", "    \"instore_orders\",\n", "    \"avg_orders_last_7_day\",\n", "    \"total_c2a_time_sec\",\n", "    \"order_with_c2a_under_10_sec_count\",\n", "    \"rider_surge_seen_cart_count\",\n", "    \"picker_surge_seen_cart_count\",\n", "    \"rain_surge_seen_cart_count\",\n", "    \"both_surge_seen_cart_count\",\n", "    \"picker_busy_time_sec\",\n", "    \"picker_active_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"orders_w_picker_pna\",\n", "    \"total_item_picked_qty\",\n", "    \"total_manday\",\n", "    \"forecasted_picking_quantity\",\n", "    \"forecasted_tracker_mandays\",\n", "    \"last_updated_projected_orders\",\n", "    \"avg_projected_orders_last_7_day\",\n", "    \"initial_projected_orders\",\n", "    \"budgeted_headcount\",\n", "    \"hiring_trigger_headcount\",\n", "    \"l2_cluster_manager_name\",\n", "    \"l2_manager_email\",\n", "    \"city_ops_head\",\n", "    \"city_ops_email\",\n", "    \"picking_time_sec\",\n", "    \"week_start_date\",\n", "    \"approved_hc\",\n", "    \"to_be_hired\",\n", "    \"conversion_perc\",\n", "    \"gap\",\n", "    \"od_mandays\",\n", "    \"updated_timestamp\",\n", "    \"active3_day_headcount_fixed_ert\",\n", "    \"od_to_be_hired\",\n", "]\n", "\n", "\n", "for col in required_columns:\n", "    if col not in final_data.columns:\n", "        final_data[col] = 0\n", "\n", "\n", "final_data = final_data[required_columns]"]}, {"cell_type": "code", "execution_count": null, "id": "295ec4b0-61f6-47b1-bc90-d281f7313878", "metadata": {}, "outputs": [], "source": ["final_data = final_data[\n", "    [\n", "        \"snapshot_date_ist\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"mode_of_ops\",\n", "        \"city_name\",\n", "        \"status_start_of_month\",\n", "        \"current_status\",\n", "        \"present_day_headcount\",\n", "        \"present_day_headcount_ert\",\n", "        \"present_day_headcount_od\",\n", "        \"active_3_day_headcount\",\n", "        \"active_3_day_headcount_ert\",\n", "        \"active_3_day_headcount_od\",\n", "        \"active_7_day_headcount\",\n", "        \"active_7_day_headcount_ert\",\n", "        \"active_7_day_headcount_od\",\n", "        \"actual_3_day_headcount_ert\",\n", "        \"resigned_next_3_day\",\n", "        \"resigned_next_7_day\",\n", "        \"lineups_3_day\",\n", "        \"selected_3_day\",\n", "        \"first_shift_start_3_day\",\n", "        \"lineups_7_day\",\n", "        \"selected_7_day\",\n", "        \"first_shift_start_7_day\",\n", "        \"instore_orders\",\n", "        \"avg_orders_last_7_day\",\n", "        \"total_c2a_time_sec\",\n", "        \"order_with_c2a_under_10_sec_count\",\n", "        \"rider_surge_seen_cart_count\",\n", "        \"picker_surge_seen_cart_count\",\n", "        \"rain_surge_seen_cart_count\",\n", "        \"both_surge_seen_cart_count\",\n", "        \"picker_busy_time_sec\",\n", "        \"picker_active_time_sec\",\n", "        \"total_items_quantity_ordered\",\n", "        \"orders_w_picker_pna\",\n", "        \"total_item_picked_qty\",\n", "        \"total_manday\",\n", "        \"forecasted_picking_quantity\",\n", "        \"forecasted_tracker_mandays\",\n", "        \"last_updated_projected_orders\",\n", "        \"avg_projected_orders_last_7_day\",\n", "        \"initial_projected_orders\",\n", "        \"budgeted_headcount\",\n", "        \"hiring_trigger_headcount\",\n", "        \"l2_cluster_manager_name\",\n", "        \"l2_manager_email\",\n", "        \"city_ops_head\",\n", "        \"city_ops_email\",\n", "        \"picking_time_sec\",\n", "        \"week_start_date\",\n", "        \"approved_hc\",\n", "        \"to_be_hired\",\n", "        \"conversion_perc\",\n", "        \"gap\",\n", "        \"od_mandays\",\n", "        \"updated_timestamp\",\n", "        \"active3_day_headcount_fixed_ert\",\n", "        \"od_to_be_hired\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "188acb6a-a720-4719-a009-7800d2078afd", "metadata": {}, "outputs": [], "source": ["columns_to_cast = [\"l2_cluster_manager_name\", \"l2_manager_email\", \"city_ops_head\", \"city_ops_email\"]\n", "\n", "final_data[columns_to_cast] = final_data[columns_to_cast].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "85f4e98d-4318-47ba-9c74-d57357aa19c7", "metadata": {}, "outputs": [], "source": ["today = updated_timestamp.today().normalize()\n", "\n", "\n", "next_week_start_date = (today + pd.Timedelta(weeks=1)).normalize() - pd.Timedelta(\n", "    days=today.weekday()\n", ")\n", "current_date = pd.to_datetime(current_date)\n", "\n", "final_data = final_data[\n", "    (final_data[\"snapshot_date_ist\"] >= current_date - timedelta(days=3))\n", "    & (final_data[\"snapshot_date_ist\"] <= next_week_start_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "66d06806-d7cd-4196-966a-6c8da752cc62", "metadata": {}, "outputs": [], "source": ["final_data[\"updated_timestamp\"] = final_data[\"updated_timestamp\"].fillna(updated_timestamp)"]}, {"cell_type": "code", "execution_count": null, "id": "9fac5634-b12e-461a-a200-d5735bfc4bb2", "metadata": {}, "outputs": [], "source": ["final_data[\"updated_timestamp\"] = pd.to_datetime(final_data[\"updated_timestamp\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b107428-58c1-487c-8bda-fbffecce30a1", "metadata": {}, "outputs": [], "source": ["duplicate_keys = (\n", "    final_data.groupby([\"snapshot_date_ist\", \"outlet_id\"])\n", "    .size()\n", "    .reset_index(name=\"count\")\n", "    .query(\"count > 1\")\n", ")\n", "\n", "print(duplicate_keys)"]}, {"cell_type": "code", "execution_count": null, "id": "f369facc-ac2c-4214-8241-f8e0ab91669f", "metadata": {}, "outputs": [], "source": ["duplicates_df = final_data.merge(duplicate_keys, on=[\"snapshot_date_ist\", \"outlet_id\"])\n", "print(duplicates_df)"]}, {"cell_type": "code", "execution_count": null, "id": "c07b7dad-22cc-4014-a65d-b70760a08df2", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"instore_headcount_hiring_summary_v91\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"snapshot_date_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Snapshot date IST\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Outlet ID\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Outlet name\"},\n", "        {\"name\": \"mode_of_ops\", \"type\": \"varchar\", \"description\": \"mode of ops\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\"name\": \"status_start_of_month\", \"type\": \"varchar\", \"description\": \"Status\"},\n", "        {\"name\": \"current_status\", \"type\": \"varchar\", \"description\": \"current status\"},\n", "        {\n", "            \"name\": \"present_day_headcount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Present day headcount\",\n", "        },\n", "        {\n", "            \"name\": \"present_day_headcount_ert\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Present day headcount ERT\",\n", "        },\n", "        {\n", "            \"name\": \"present_day_headcount_od\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Present day headcount OD\",\n", "        },\n", "        {\n", "            \"name\": \"active_3_day_headcount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 3-day headcount\",\n", "        },\n", "        {\n", "            \"name\": \"active_3_day_headcount_ert\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 3-day headcount ERT\",\n", "        },\n", "        {\n", "            \"name\": \"active_3_day_headcount_od\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 3-day headcount OD\",\n", "        },\n", "        {\n", "            \"name\": \"active_7_day_headcount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 7-day headcount\",\n", "        },\n", "        {\n", "            \"name\": \"active_7_day_headcount_ert\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 7-day headcount ERT\",\n", "        },\n", "        {\n", "            \"name\": \"active_7_day_headcount_od\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Active 7-day headcount OD\",\n", "        },\n", "        {\n", "            \"name\": \"actual_3_day_headcount_ert\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"actual ert 3 day headcount\",\n", "        },\n", "        {\n", "            \"name\": \"resigned_next_3_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Resigned in 3 days\",\n", "        },\n", "        {\n", "            \"name\": \"resigned_next_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Resigned in 7 days\",\n", "        },\n", "        {\n", "            \"name\": \"hiring_trigger_headcount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Hiring trigger headcount\",\n", "        },\n", "        {\n", "            \"name\": \"lineups_3_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Leads in last 3 days\",\n", "        },\n", "        {\n", "            \"name\": \"selected_3_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Selected in last 3 days\",\n", "        },\n", "        {\n", "            \"name\": \"first_shift_start_3_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"First shift start 3 days\",\n", "        },\n", "        {\n", "            \"name\": \"lineups_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Leads in last 7 days\",\n", "        },\n", "        {\n", "            \"name\": \"selected_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Selected in last 7 days\",\n", "        },\n", "        {\n", "            \"name\": \"first_shift_start_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"First shift start 7 days\",\n", "        },\n", "        {\"name\": \"instore_orders\", \"type\": \"integer\", \"description\": \"In-store orders\"},\n", "        {\n", "            \"name\": \"avg_orders_last_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Average orders in last 7 days\",\n", "        },\n", "        {\n", "            \"name\": \"total_c2a_time_sec\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total C2A time in seconds\",\n", "        },\n", "        {\n", "            \"name\": \"order_with_c2a_under_10_sec_count\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Order count with C2A under 10 seconds\",\n", "        },\n", "        {\n", "            \"name\": \"rider_surge_seen_cart_count\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Rider surge seen carts\",\n", "        },\n", "        {\n", "            \"name\": \"picker_surge_seen_cart_count\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Picker surge seen carts\",\n", "        },\n", "        {\n", "            \"name\": \"rain_surge_seen_cart_count\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Rain surge seen carts\",\n", "        },\n", "        {\n", "            \"name\": \"both_surge_seen_cart_count\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Both surge seen carts\",\n", "        },\n", "        {\n", "            \"name\": \"picker_busy_time_sec\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Picker busy time\",\n", "        },\n", "        {\n", "            \"name\": \"picker_active_time_sec\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Picker active time\",\n", "        },\n", "        {\n", "            \"name\": \"total_items_quantity_ordered\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Total items quantity ordered\",\n", "        },\n", "        {\n", "            \"name\": \"orders_w_picker_pna\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Orders with picker PNA\",\n", "        },\n", "        {\n", "            \"name\": \"total_item_picked_qty\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Total quantity store IPP\",\n", "        },\n", "        {\n", "            \"name\": \"total_manday\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total mandays store IPP\",\n", "        },\n", "        {\n", "            \"name\": \"forecasted_picking_quantity\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Forecasted picking quantity\",\n", "        },\n", "        {\n", "            \"name\": \"forecasted_tracker_mandays\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total mandays store IPP\",\n", "        },\n", "        {\n", "            \"name\": \"last_updated_projected_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Last updated orders\",\n", "        },\n", "        {\n", "            \"name\": \"avg_projected_orders_last_7_day\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Avg of projected orders in last 7 days\",\n", "        },\n", "        {\n", "            \"name\": \"initial_projected_orders\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Initial projection\",\n", "        },\n", "        {\n", "            \"name\": \"budgeted_headcount\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Budgeted headcount\",\n", "        },\n", "        {\n", "            \"name\": \"l2_cluster_manager_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"L2 cluster manager name\",\n", "        },\n", "        {\n", "            \"name\": \"l2_manager_email\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"L2 manager email\",\n", "        },\n", "        {\"name\": \"city_ops_head\", \"type\": \"varchar\", \"description\": \"City ops head\"},\n", "        {\n", "            \"name\": \"city_ops_email\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"City ops head email\",\n", "        },\n", "        {\n", "            \"name\": \"picking_time_sec\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Picker busy time sec\",\n", "        },\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"week_start_date\",\n", "        },\n", "        {\n", "            \"name\": \"approved_hc\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"approved_hc\",\n", "        },\n", "        {\n", "            \"name\": \"to_be_hired\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"to_be_hired\",\n", "        },\n", "        {\n", "            \"name\": \"conversion_perc\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"conversion_perc\",\n", "        },\n", "        {\n", "            \"name\": \"gap\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"gap\",\n", "        },\n", "        {\n", "            \"name\": \"od_mandays\",\n", "            \"type\": \"double\",\n", "            \"description\": \"od_mandays\",\n", "        },\n", "        {\n", "            \"name\": \"updated_timestamp\",\n", "            \"type\": \"timestamp(6)\",\n", "            \"description\": \"updated_timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"active3_day_headcount_fixed_ert\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"active3_day_headcount_fixed_ert\",\n", "        },\n", "        {\n", "            \"name\": \"od_to_be_hired\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"od_to_be_hired\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"snapshot_date_ist\", \"outlet_id\"],\n", "    \"incremental_key\": \"snapshot_date_ist\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains outlet-level performance metrics and manpower requirements.\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ffbeea5e-70f1-4459-88dc-d5758a25bc08", "metadata": {}, "outputs": [], "source": ["pb.to_trino(final_data, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "b12770bb-2bed-4330-afbd-c4324a89b895", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}