{"cells": [{"cell_type": "code", "execution_count": null, "id": "370effdd-5ce6-4719-bcb0-71370d09a663", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime as dt\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "880ce27a-1ef0-49c7-b245-5f27c268c991", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "79f41f15-a3f9-4984-b2c1-27cc786217d5", "metadata": {}, "outputs": [], "source": ["base = f\"\"\"\n", "WITH base AS(\n", "SELECT \n", "    dt,\n", "    from_unixtime(timestamp/1000) AS timestamp_ts,\n", "    source, device_id, session_id, user_id, location , location_info, event_name,\n", "    event_value, page_name, event_type, app_version_code, app_version,\n", "    cast(location_info[1] as decimal(10,6)) AS latitude, cast(location_info[2] AS decimal(10,6)) as longitude\n", "FROM \n", "    zomato.blinkit_jumbo2.store_onboarding_app_events\n", "WHERE \n", "    dt >= '20250101'\n", "    AND user_id != ''\n", "\n", "),\n", "------------\n", "base_final AS(\n", "SELECT *\n", "FROM(\n", "SELECT \n", "    user_id,\n", "    MIN(case when page_name = 'onboarding_home' then dt END) AS app_open,\n", "    min_by(latitude, case when latitude<>0 then dt end) AS latitude,\n", "    min_by(longitude, case when longitude<>0 then dt end) AS longitude\n", " FROM \n", "    base\n", "GROUP BY 1\n", ")\n", "WHERE app_open is not null\n", "),\n", "---------------\n", "base_camaign AS\n", "(\n", "select user_id, min(campaign_status) campaign_status\n", "from storeops_etls.store_employee_onboarding_events_via_app\n", "where snapshot_date_ist >= current_date - interval '300' day\n", "group by 1\n", "),\n", "-----------------\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base_final AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.user_id = a.user_id\n", "),\n", "\n", "--------------\n", "outlet_mapping as(\n", "    select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "    merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant_outlet_facility_mapping fm\n", "    left join\n", "    (\n", "        select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "        from dwh.dim_merchant\n", "        where is_current = true\n", "        and is_enabled = true\n", "        and is_active = true\n", "    ) m on fm.frontend_merchant_id=m.merchant_id\n", "    where is_current = true\n", "    and is_mapping_enabled = true\n", "    and is_current_mapping_active = true\n", "    and merchant_business_type_id = 7\n", "    and pos_outlet_id not in (4146,4381)\n", "    group by 1,2,3,4,5,6,7\n", "),\n", "-------------\n", "final_query_base AS(\n", "SELECT \n", "    user_id,\n", "    date(date_parse(app_open,'%%Y%%m%%d')) AS app_open_date,\n", "    latitude,\n", "    longitude,\n", "    campaign_status,\n", "    city,\n", "    outlet_id,\n", "    outlet_name,\n", "    distance,\n", "    rnk\n", "FROM(\n", "SELECT\n", "    *,\n", "    row_number() over(partition by user_id order by distance asc) AS rnk\n", "FROM\n", "    (SELECT \n", "        x.*,\n", "        pos_outlet_city_name AS city,\n", "        pos_outlet_id AS outlet_id,\n", "        pos_outlet_name AS outlet_name,\n", "        CASE WHEN latitude > 0 AND longitude > 0 AND merchant_store_latitude > 0 AND merchant_store_longitude > 0\n", "        THEN st_Distance(st_point(latitude, longitude), st_point(merchant_store_latitude, merchant_store_longitude)) ELSE 9999 end AS distance\n", "    FROM\n", "        fianl_user_base AS x\n", "    LEFT JOIN\n", "        outlet_mapping AS om\n", "    ON 1 = 1\n", "    ))\n", "WHERE rnk = 1\n", ")\n", "\n", "select * from \n", "(\n", "SELECT \n", "app_open_date as date_ts,\n", "city,\n", "CASE \n", "     WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "     WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "     else 'Others' END AS channel_bucket,\n", "     'total' AS work_preference_bucket,\n", "     COUNT(distinct user_id) AS user_count\n", "FROM final_query_base\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "where date_ts >= current_date - interval '70' day\n", "\"\"\"\n", "base = pd.read_sql(base, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "81623c40-ecf3-4771-8594-a5980fafc8e7", "metadata": {}, "outputs": [], "source": ["base[\"date_ts\"] = pd.to_datetime(base[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1f7bc3dd-7a46-4337-b121-f4fc9f2c6d58", "metadata": {}, "outputs": [], "source": ["base.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "a8a7fcfd-252a-4871-b331-48cb10fdf568", "metadata": {}, "outputs": [], "source": ["result_1 = base.groupby(base[\"date_ts\"].dt.isocalendar().week, as_index=False)[\"user_count\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "4b4acc7e-f276-4ecc-a879-3db181a889b2", "metadata": {}, "outputs": [], "source": ["result_1"]}, {"cell_type": "code", "execution_count": null, "id": "8f8528a0-9b68-405c-8dbd-846dfb194c36", "metadata": {}, "outputs": [], "source": ["base_3 = base"]}, {"cell_type": "code", "execution_count": null, "id": "a61012ab-a1b0-444b-a1c4-549c7d08f53c", "metadata": {}, "outputs": [], "source": ["base_3"]}, {"cell_type": "code", "execution_count": null, "id": "3dbe3aa5-49e8-458c-89a1-1a709d61e8a4", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_install_user_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"user_count\", \"type\": \"integer\", \"description\": \"install user count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "340d3608-2848-4476-8454-140a8d8d70c5", "metadata": {}, "outputs": [], "source": ["pb.to_trino(base_3, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "3542901d-b17f-4dbb-b87c-9452469349c9", "metadata": {}, "outputs": [], "source": ["ob_base = f\"\"\"\n", "\n", "WITH base AS(\n", "WITH base AS(\n", "WITH base AS(\n", "select\n", "date_of_joining, cast(active_site_id as int) outlet_id, employee_id\n", "from \n", "(\n", "select u.date_of_joining, u.employee_id, u.designation, r.active_site_id, \n", "json_extract(meta, '$.job_preference') as job_preference\n", "from ops_management.user u\n", "left join ops_management.user_management_userrole r\n", "on u.employee_id = r.employee_id\n", "where u.employee_id like 'GC%%'\n", "and u.designation = 'Captain OD'\n", ")\n", "where date_of_joining >= current_date - interval '70' day\n", "),\n", "        \n", "outlet_mapping as(\n", "select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "merchant_store_latitude, merchant_store_longitude\n", "from dwh.dim_merchant_outlet_facility_mapping fm\n", "left join\n", "(\n", "    select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant\n", "    where is_current = true\n", "    and is_enabled = true\n", "    and is_active = true\n", ") m on fm.frontend_merchant_id=m.merchant_id\n", "where is_current = true\n", "and is_mapping_enabled = true\n", "and is_current_mapping_active = true\n", "and merchant_business_type_id = 7\n", "and pos_outlet_id not in (4146,4381)\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "SELECT *\n", "FROM(\n", "SELECT\n", "    employee_id,\n", "    pos_outlet_city_name AS city,\n", "    min(date_of_joining) AS date_of_joining\n", "FROM \n", "base AS a\n", "LEFT JOIN\n", "outlet_mapping AS b\n", "ON a.outlet_id = b.pos_outlet_id\n", "GROUP BY 1, 2\n", ")\n", "), \n", "\n", " user_base AS(\n", " SELECT \n", " employee_id,\n", " json_extract_scalar(meta, '$.onboarding_id') as onboarding_id,\n", " json_extract_scalar(meta, '$.job_preference[0]') AS job_preference,\n", " zsha1(phone) AS phone\n", " FROM ops_management.user\n", " WHERE created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", " AND date_of_joining >= date'2025-01-01'\n", " )\n", " \n", " SELECT \n", " b.*,\n", " onboarding_id,\n", " job_preference,\n", " phone\n", " FROM base AS b\n", " LEFT JOIN\n", " user_base AS a\n", " ON b.employee_id = a.employee_id\n", "),\n", "---\n", "base_camaign AS(\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "), \n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "---\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.onboarding_id = a.user_id\n", "), \n", "\n", "referral_base AS(\n", "select distinct phone_joinee\n", "  from \n", "      (\n", "     select rl.id, \n", "     cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_jonee,\n", "     cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "     cast(json_extract_scalar(config,'$.incentives[0].amount') as integer) as amount_1,\n", "     cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", "     cast(json_extract_scalar(config,'$.incentives[1].amount') as integer) as amount_2, \n", "     rl.lead_phone_number as phone_joinee,\n", "     em.employee_id as employee_id_ref,\n", "     cast(rl.insert_ds_ist as date) as referred_date, \n", "     cast(site_id as int) outlet_id_referred_for\n", "     from ops_management.referral_leads rl\n", "     left join ops_management.employee_tenant_map em\n", "     on rl.referer_id = em.id\n", "     where rl.insert_ds_ist >= cast(current_date - interval '200' day as varchar)\n", "     and em.insert_ds_ist is not null\n", "     )\n", "),\n", "\n", "final_base AS(      \n", "SELECT a.*,\n", "phone_joinee\n", "FROM fianl_user_base AS a\n", "LEFT JOIN\n", "referral_base AS b\n", "ON a.phone = b.phone_joinee\n", ")\n", "\n", "\n", "select *\n", "from \n", "(\n", "SELECT \n", "    date_of_joining,\n", "    city,\n", "    work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date_of_joining,\n", "        max(CASE \n", "            WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "            WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "        END) AS work_preference_bucket,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    WHERE job_preference IN ('part_time', 'PART_TIME', 'full_time', 'FULL_TIME')\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", "\n", "UNION ALL\n", "\n", "\n", "SELECT \n", "    date_of_joining,\n", "    city,\n", "    'total' AS work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date_of_joining,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "\n", "\"\"\"\n", "ob_base = pd.read_sql(ob_base, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "faa34fb6-941c-439e-a5d6-a9349c496bbf", "metadata": {}, "outputs": [], "source": ["ob_base = ob_base.rename(columns={\"user_count\": \"ob_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "49e003c8-88cc-4c02-93dd-5e09d6d1379e", "metadata": {}, "outputs": [], "source": ["ob_base = ob_base.rename(columns={\"date_of_joining\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "018826f0-9a73-46c8-999c-2a2580b74bea", "metadata": {}, "outputs": [], "source": ["ob_base[\"date_ts\"] = pd.to_datetime(ob_base[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "dcca6052-3502-4578-af2d-dbbe2159f12e", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_onboard_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"ob_count\", \"type\": \"integer\", \"description\": \"ob_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "505c5565-5cd1-41c6-b8f6-5f206a69373c", "metadata": {}, "outputs": [], "source": ["pb.to_trino(ob_base, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "b392602c-f3c1-4d78-af35-790d7bbe46d6", "metadata": {}, "outputs": [], "source": ["training_start = f\"\"\"\n", "WITH base AS(\n", "WITH base AS(\n", "SELECT\n", "    employee_id,\n", "    outlet_id,\n", "    user_start_time_training_ist\n", "FROM(\n", "SELECT \n", "        employee_id,\n", "        outlet_id,\n", "        user_start_time_training_ist,\n", "        cumulative_training_count,\n", "        ROW_NUMBER() OVER (PARTITION BY employee_id ORDER BY snapshot_date_ist ASC) AS rn\n", "    FROM \n", "        storeops_etls.agg_od_employee_slot_metrics\n", "    WHERE \n", "        snapshot_date_ist >= current_date - interval '70' day\n", "        AND user_start_time_training_ist IS NOT NULL\n", "        )\n", "    WHERE rn = 1\n", "),\n", " \n", " user_base AS(\n", " SELECT \n", " employee_id,\n", " json_extract_scalar(meta, '$.onboarding_id') as onboarding_id,\n", " json_extract_scalar(meta, '$.job_preference[0]') AS job_preference,\n", " zsha1(phone) AS phone\n", " FROM ops_management.user\n", " WHERE created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", " AND date_of_joining >= date'2025-01-01'\n", " )\n", " \n", " SELECT \n", " b.*,\n", " onboarding_id,\n", " job_preference,\n", " phone\n", " FROM base AS b\n", " LEFT JOIN\n", " user_base AS a\n", " ON b.employee_id = a.employee_id\n", "),\n", "---\n", "base_camaign AS(\n", "\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "), \n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "---\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.onboarding_id = a.user_id\n", "),\n", "\n", "referral_base AS(\n", "select distinct phone_joinee\n", "  from \n", "      (\n", "     select rl.id, \n", "     cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_jonee,\n", "     cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "     cast(json_extract_scalar(config,'$.incentives[0].amount') as integer) as amount_1,\n", "     cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", "     cast(json_extract_scalar(config,'$.incentives[1].amount') as integer) as amount_2, \n", "     rl.lead_phone_number as phone_joinee,\n", "     em.employee_id as employee_id_ref,\n", "     cast(rl.insert_ds_ist as date) as referred_date, \n", "     cast(site_id as int) outlet_id_referred_for\n", "     from ops_management.referral_leads rl\n", "     left join ops_management.employee_tenant_map em\n", "     on rl.referer_id = em.id\n", "     where rl.insert_ds_ist >= cast(current_date - interval '200' day as varchar)\n", "     and em.insert_ds_ist is not null\n", "     )\n", "),\n", "\n", "final_base_1 AS(      \n", "SELECT a.*,\n", "phone_joinee\n", "FROM fianl_user_base AS a\n", "LEFT JOIN\n", "referral_base AS b\n", "ON a.phone = b.phone_joinee\n", "),\n", "\n", "outlet_mapping as(\n", "    select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "    merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant_outlet_facility_mapping fm\n", "    left join\n", "    (\n", "        select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "        from dwh.dim_merchant\n", "        where is_current = true\n", "        and is_enabled = true\n", "        and is_active = true\n", "    ) m on fm.frontend_merchant_id=m.merchant_id\n", "    where is_current = true\n", "    and is_mapping_enabled = true\n", "    and is_current_mapping_active = true\n", "    and merchant_business_type_id = 7\n", "    and pos_outlet_id not in (4146,4381)\n", "    group by 1,2,3,4,5,6,7\n", "),\n", "\n", "final_base AS(\n", "SELECT \n", "a.*,\n", "pos_outlet_city_name AS city\n", "FROM \n", "final_base_1 AS a\n", "LEFT JOIN\n", "outlet_mapping AS b\n", "ON a.outlet_id = b.pos_outlet_id\n", ")\n", "\n", "\n", "select *\n", "from\n", "(\n", "SELECT \n", "    training_start_date,\n", "    city,\n", "    work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date(user_start_time_training_ist) AS training_start_date,\n", "        max(CASE \n", "            WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "            WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "        END) AS work_preference_bucket,\n", "        max(\n", "        CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    WHERE job_preference IN ('part_time', 'PART_TIME', 'full_time', 'FULL_TIME')\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "    training_start_date,\n", "    city,\n", "    'total' AS work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date(user_start_time_training_ist) AS training_start_date,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "\"\"\"\n", "\n", "training_start = pd.read_sql(training_start, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "fb14a524-fbad-4329-8c1a-1bf160000dbf", "metadata": {}, "outputs": [], "source": ["training_start = training_start.rename(columns={\"training_start_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "2786ac76-c6ba-4227-8238-80b1a92be263", "metadata": {}, "outputs": [], "source": ["training_start = training_start.rename(columns={\"user_count\": \"training_start_user_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "c2155487-0c7e-47b4-9097-b7d17fea7576", "metadata": {}, "outputs": [], "source": ["training_start[\"date_ts\"] = pd.to_datetime(training_start[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "55b231be-ddee-4b4f-87b3-18e204fdd037", "metadata": {}, "outputs": [], "source": ["training_start"]}, {"cell_type": "code", "execution_count": null, "id": "9e4812c5-bb46-40e6-8099-e0b772936e91", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_training_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"training_start_user_count\", \"type\": \"integer\", \"description\": \"ob_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8ec1bde0-03cf-40bc-9212-adc2d8920795", "metadata": {}, "outputs": [], "source": ["pb.to_trino(training_start, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "fec429f3-00eb-43ea-af07-2f339a9e33a1", "metadata": {}, "outputs": [], "source": ["training_end = f\"\"\"\n", "WITH base AS(\n", "WITH base AS(\n", "SELECT \n", "        employee_id,\n", "        outlet_id,\n", "        user_end_time_training_ist,\n", "        cumulative_training_count\n", "    FROM \n", "        storeops_etls.agg_od_employee_slot_metrics\n", "    WHERE \n", "        snapshot_date_ist >= current_date - interval '70' day\n", "        AND cumulative_training_count = 11\n", "), \n", "\n", " user_base AS(\n", " SELECT \n", " employee_id,\n", " json_extract_scalar(meta, '$.onboarding_id') as onboarding_id,\n", " json_extract_scalar(meta, '$.job_preference[0]') AS job_preference,\n", " zsha1(phone) AS phone\n", " FROM ops_management.user\n", " WHERE created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", " AND date_of_joining >= date'2025-01-01'\n", " )\n", " \n", " SELECT \n", " b.*,\n", " onboarding_id,\n", " job_preference,\n", " phone\n", " FROM base AS b\n", " LEFT JOIN\n", " user_base AS a\n", " ON b.employee_id = a.employee_id\n", "),\n", "---\n", "base_camaign AS(\n", "\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "), \n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "---\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.onboarding_id = a.user_id\n", "),\n", "\n", "referral_base AS(\n", "select distinct phone_joinee\n", "  from \n", "      (\n", "     select rl.id, \n", "     cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_jonee,\n", "     cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "     cast(json_extract_scalar(config,'$.incentives[0].amount') as integer) as amount_1,\n", "     cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", "     cast(json_extract_scalar(config,'$.incentives[1].amount') as integer) as amount_2, \n", "     rl.lead_phone_number as phone_joinee,\n", "     em.employee_id as employee_id_ref,\n", "     cast(rl.insert_ds_ist as date) as referred_date, \n", "     cast(site_id as int) outlet_id_referred_for\n", "     from ops_management.referral_leads rl\n", "     left join ops_management.employee_tenant_map em\n", "     on rl.referer_id = em.id\n", "     where rl.insert_ds_ist >= cast(current_date - interval '200' day as varchar)\n", "     and em.insert_ds_ist is not null\n", "     )\n", "),\n", "\n", "final_base_1 AS(\n", "SELECT a.*,\n", "phone_joinee\n", "FROM fianl_user_base AS a\n", "LEFT JOIN\n", "referral_base AS b\n", "ON a.phone = b.phone_joinee\n", "),\n", "\n", "outlet_mapping as(\n", "    select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "    merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant_outlet_facility_mapping fm\n", "    left join\n", "    (\n", "        select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "        from dwh.dim_merchant\n", "        where is_current = true\n", "        and is_enabled = true\n", "        and is_active = true\n", "    ) m on fm.frontend_merchant_id=m.merchant_id\n", "    where is_current = true\n", "    and is_mapping_enabled = true\n", "    and is_current_mapping_active = true\n", "    and merchant_business_type_id = 7\n", "    and pos_outlet_id not in (4146,4381)\n", "    group by 1,2,3,4,5,6,7\n", "),\n", "\n", "final_base AS(\n", "SELECT \n", "a.*,\n", "pos_outlet_city_name AS city\n", "FROM \n", "final_base_1 AS a\n", "LEFT JOIN\n", "outlet_mapping AS b\n", "ON a.outlet_id = b.pos_outlet_id\n", ")\n", "\n", "\n", "select *\n", "from\n", "(\n", "SELECT \n", "    training_end_date,\n", "    city,\n", "    work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date(user_end_time_training_ist) AS training_end_date,\n", "        max(CASE \n", "            WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "            WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "        END) AS work_preference_bucket,\n", "        max(CASE \n", "            WHEN phone IS NOT NULL AND phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    WHERE job_preference IN ('part_time', 'PART_TIME', 'full_time', 'FULL_TIME')\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "    training_end_date,\n", "    city,\n", "    'total' AS work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        date(user_end_time_training_ist) AS training_end_date,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "\n", "\"\"\"\n", "training_end = pd.read_sql(training_end, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "40fbbaf9-74b0-4b8d-a70b-a7b8676563b0", "metadata": {}, "outputs": [], "source": ["taining_end = training_end.rename(columns={\"training_end_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "9a01b5c2-6042-4162-95a3-bcc4717efae7", "metadata": {}, "outputs": [], "source": ["taining_end = taining_end.rename(columns={\"user_count\": \"training_end_user_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "a5183b16-4746-420e-8384-9620ce2f3573", "metadata": {}, "outputs": [], "source": ["taining_end[\"date_ts\"] = pd.to_datetime(taining_end[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1f99f3dc-3c4d-4120-b571-0a5e9dabe101", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_taining_end_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"training_end_user_count\", \"type\": \"integer\", \"description\": \"ob_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "fe5cc59f-e84a-4c32-b751-a9d8098b2e02", "metadata": {}, "outputs": [], "source": ["pb.to_trino(taining_end, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "3bda6ff0-188a-44fc-8796-2af797855221", "metadata": {}, "outputs": [], "source": ["fsc_base = f\"\"\"\n", "WITH base AS(\n", "WITH base AS(\n", "WITH base AS(\n", "select * from \n", "(\n", "SELECT\n", "    employee_id, \n", "    outlet_id,\n", "    date_ AS  fsc_date \n", "FROM\n", "( \n", "        SELECT\n", "        employee_id, \n", "        min_by(outlet_id, date_) as outlet_id,\n", "        MIN(date_) AS date_\n", "        FROM\n", "        storeops_etls.employee_metrics_daily\n", "        WHERE date_ >= current_date - interval '1' year\n", "        and designation = 'Captain O<PERSON>'\n", "        and total_active_time > 0\n", "        GROUP BY 1\n", "        )\n", "        )\n", "where fsc_date >= current_date - interval '200' day\n", "), \n", "    \n", "outlet_mapping as(\n", "select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "merchant_store_latitude, merchant_store_longitude\n", "from dwh.dim_merchant_outlet_facility_mapping fm\n", "left join\n", "(\n", "    select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant\n", "    where is_current = true\n", "    and is_enabled = true\n", "    and is_active = true\n", ") m on fm.frontend_merchant_id=m.merchant_id\n", "where is_current = true\n", "and is_mapping_enabled = true\n", "and is_current_mapping_active = true\n", "and merchant_business_type_id = 7\n", "and pos_outlet_id not in (4146,4381)\n", "group by 1,2,3,4,5,6,7\n", ")\n", "\n", "select *\n", "FROM(\n", "SELECT\n", "    employee_id,\n", "    min(pos_outlet_city_name) AS city,\n", "    min(fsc_date) AS fsc_date\n", "FROM \n", "base AS a\n", "LEFT JOIN\n", "outlet_mapping AS b\n", "ON a.outlet_id = b.pos_outlet_id\n", "GROUP BY 1\n", ")\n", "),\n", "\n", " user_base AS(\n", " SELECT \n", " employee_id,\n", " json_extract_scalar(meta, '$.onboarding_id') as onboarding_id,\n", " json_extract_scalar(meta, '$.job_preference[0]') AS job_preference,\n", " zsha1(phone) AS phone\n", " FROM ops_management.user\n", " WHERE created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", " AND date_of_joining >= date'2025-01-01'\n", " )\n", " \n", " SELECT \n", " b.*,\n", " onboarding_id,\n", " job_preference,\n", " phone\n", " FROM base AS b\n", " LEFT JOIN\n", " user_base AS a\n", " ON b.employee_id = a.employee_id\n", "),\n", "---\n", "base_camaign AS(\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "), \n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "---\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.onboarding_id = a.user_id\n", "), \n", "\n", "\n", "referral_base AS(\n", "select distinct phone_joinee\n", "  from \n", "      (\n", "     select rl.id, \n", "     cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_jonee,\n", "     cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", "     cast(json_extract_scalar(config,'$.incentives[0].amount') as integer) as amount_1,\n", "     cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", "     cast(json_extract_scalar(config,'$.incentives[1].amount') as integer) as amount_2, \n", "     rl.lead_phone_number as phone_joinee,\n", "     em.employee_id as employee_id_ref,\n", "     cast(rl.insert_ds_ist as date) as referred_date, \n", "     cast(site_id as int) outlet_id_referred_for\n", "     from ops_management.referral_leads rl\n", "     left join ops_management.employee_tenant_map em\n", "     on rl.referer_id = em.id\n", "     where rl.insert_ds_ist >= cast(current_date - interval '200' day as varchar)\n", "     and em.insert_ds_ist is not null\n", "     )\n", "), \n", "\n", "final_base AS(      \n", "SELECT a.*,\n", "phone_joinee\n", "FROM fianl_user_base AS a\n", "LEFT JOIN\n", "referral_base AS b\n", "ON a.phone = b.phone_joinee\n", ")\n", "\n", "select *\n", "from \n", "(\n", "SELECT \n", "    fsc_date,\n", "    city,\n", "    work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        fsc_date,\n", "        max(CASE \n", "            WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "            WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "        END) AS work_preference_bucket,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    WHERE job_preference IN ('part_time', 'PART_TIME', 'full_time', 'FULL_TIME')\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", "\n", "UNION ALL\n", "\n", "\n", "SELECT \n", "    fsc_date,\n", "    city,\n", "    'total' AS work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        fsc_date,\n", "        max(CASE \n", "            WHEN phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "\"\"\"\n", "fsc_base = pd.read_sql(fsc_base, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "2185a9ec-5244-48d1-bd03-b2720ffe7f32", "metadata": {}, "outputs": [], "source": ["fsc_base = fsc_base.rename(columns={\"user_count\": \"fsc_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "897659f5-f367-4edb-9f54-cc5800c74804", "metadata": {}, "outputs": [], "source": ["fsc_base = fsc_base.rename(columns={\"fsc_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "e3a2d216-5bf0-4a4b-ad43-84b218754d58", "metadata": {}, "outputs": [], "source": ["fsc_base[\"date_ts\"] = pd.to_datetime(fsc_base[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d2f2ca82-614f-4ed1-b20f-aca813d0eeb2", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_fsc_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"fsc_count\", \"type\": \"integer\", \"description\": \"fsc_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "79897739-08a0-4eda-b692-f479ebfa00f8", "metadata": {}, "outputs": [], "source": ["pb.to_trino(fsc_base, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "876b2cbd-ca4a-41ca-90c6-55eac0622a2a", "metadata": {}, "outputs": [], "source": ["fss_base = f\"\"\"\n", "with base as \n", "(\n", "with base as (\n", "SELECT\n", "    employee_id, \n", "    outlet_id,\n", "    fss_date\n", "FROM\n", "(\n", "SELECT\n", "    employee_id, \n", "    min(outlet_id) as outlet_id,\n", "    MIN(date_) AS fss_date\n", "FROM \n", "    storeops_etls.employee_metrics_daily \n", "WHERE  \n", "    date_ >= current_date - interval '1' year\n", "    AND designation in ('Captain','Fnv Executive','Express Picker','Assistant','Inventory Executive', 'Captain ERT') \n", "    AND total_active_time > 0\n", "    and employee_id like 'GC%%'\n", "GROUP BY 1\n", ")\n", "where  fss_date >= current_date - interval '200' day\n", "),\n", "        \n", "outlet_mapping as(\n", "select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "merchant_store_latitude, merchant_store_longitude\n", "from dwh.dim_merchant_outlet_facility_mapping fm\n", "left join\n", "(\n", "    select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant\n", "    where is_current = true\n", "    and is_enabled = true\n", "    and is_active = true\n", ") m on fm.frontend_merchant_id=m.merchant_id\n", "where is_current = true\n", "and is_mapping_enabled = true\n", "and is_current_mapping_active = true\n", "and merchant_business_type_id = 7\n", "and pos_outlet_id not in (4146,4381)\n", "group by 1,2,3,4,5,6,7\n", ") , \n", "\n", "\n", "final_fss as \n", "(\n", "SELECT\n", "    employee_id,\n", "    min(pos_outlet_city_name) AS city,\n", "    min(fss_date) as fss_date\n", "FROM\n", "base AS a\n", "LEFT JOIN\n", "outlet_mapping AS b\n", "ON a.outlet_id = b.pos_outlet_id\n", "group by 1\n", "), \n", "\n", "\n", "od_to_ft as \n", "(\n", "SELECT employee_id as employee_id_ft,\n", "designation as designation_ft, \n", "phone as phone_ft, \n", "date_of_joining as date_of_joining_ft, \n", "status as status_ft, \n", "json_extract_scalar(meta, '$.prev_employee_id') as previous_employee_id\n", "FROM ops_management.user \n", "where created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", "and json_extract_scalar(meta, '$.prev_employee_id')  is not null\n", "and ((json_extract_scalar(meta, '$.prev_employee_id')  like 'GCEBOD%%') or (json_extract_scalar(meta, '$.prev_employee_id')  like 'GC0%%'))\n", "), \n", "\n", "\n", "mid_base as \n", "(\n", "select f.*, previous_employee_id\n", "from final_fss f\n", "left join\n", "od_to_ft o\n", "on f.employee_id = o.employee_id_ft\n", "), \n", "\n", "\n", "user_base AS(\n", "SELECT \n", "employee_id,\n", "json_extract_scalar(meta, '$.onboarding_id') as onboarding_id,\n", "json_extract_scalar(meta, '$.job_preference[0]') AS job_preference,\n", "zsha1(phone) AS phone\n", "FROM ops_management.user\n", "WHERE created_by_id in ('SYSTEM', 'OB_SYSTEM')\n", "AND date_of_joining >= date'2025-01-01'\n", ")\n", "\n", "\n", "select m.*, onboarding_id , job_preference, phone\n", "from mid_base m\n", "left join\n", "user_base u\n", "on m.previous_employee_id = u.employee_id\n", "), \n", "\n", "---\n", "base_camaign AS(\n", "\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "),\n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.onboarding_id = a.user_id\n", "), \n", "\n", "referral_base AS(\n", "select distinct phone_joinee\n", "from \n", "  (\n", " select rl.id, \n", " cast(json_extract_scalar(rl.meta, '$.lead_name') as varchar)  as name_jonee,\n", " cast(json_extract_scalar(config,'$.incentives[0].qty') as integer) as qty_1, \n", " cast(json_extract_scalar(config,'$.incentives[0].amount') as integer) as amount_1,\n", " cast(json_extract_scalar(config,'$.incentives[1].qty') as integer) as qty_2, \n", " cast(json_extract_scalar(config,'$.incentives[1].amount') as integer) as amount_2, \n", " rl.lead_phone_number as phone_joinee,\n", " em.employee_id as employee_id_ref,\n", " cast(rl.insert_ds_ist as date) as referred_date, \n", " cast(site_id as int) outlet_id_referred_for\n", " from ops_management.referral_leads rl\n", " left join ops_management.employee_tenant_map em\n", " on rl.referer_id = em.id\n", " where rl.insert_ds_ist >= cast(current_date - interval '200' day as varchar)\n", " and em.insert_ds_ist is not null\n", " )\n", "),\n", "\n", "ft_phone AS \n", "(\n", "select employee_id,\n", "max_by(zsha1(phone), update_ts) as phone_ft\n", "from ops_management.user \n", "where date_of_joining >= current_date - interval '200' day\n", "group by 1\n", "),\n", "\n", "final_base AS(      \n", "SELECT a.*,\n", "phone_joinee,\n", "phone_ft\n", "FROM fianl_user_base AS a\n", "LEFT JOIN\n", "ft_phone AS c\n", "ON a.employee_id = c.employee_id\n", "LEFT JOIN\n", "referral_base AS b\n", "ON c.phone_ft = b.phone_joinee\n", ")\n", "\n", "\n", "select *\n", "from \n", "(\n", "SELECT \n", "    fss_date,\n", "    city,\n", "    work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        fss_date,\n", "        max(CASE \n", "            WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "            WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "        END) AS work_preference_bucket,\n", "        max(CASE \n", "            WHEN previous_employee_id IS NOT NULL AND phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN previous_employee_id IS  NULL AND phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            WHEN previous_employee_id IS  NULL and campaign_status is null and phone_joinee IS NULL THEN 'HR'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    WHERE job_preference IN ('part_time', 'PART_TIME', 'full_time', 'FULL_TIME')\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "    fss_date,\n", "    city,\n", "    'total' AS work_preference_bucket,\n", "    channel_bucket,\n", "    COUNT(distinct employee_id) AS user_count\n", "FROM (\n", "    SELECT \n", "        employee_id,\n", "        city,\n", "        fss_date,\n", "        max(CASE \n", "            WHEN previous_employee_id IS NOT NULL AND phone_joinee IS NOT NULL THEN 'Ref_by_app'\n", "            WHEN previous_employee_id IS  NULL AND phone_joinee IS NOT NULL THEN 'Referral'\n", "            WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "            WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "            WHEN previous_employee_id IS  NULL and campaign_status is null and phone_joinee IS NULL THEN 'HR'\n", "            else 'Others' END) AS channel_bucket\n", "    FROM final_base\n", "    group by 1,2,3\n", ") t\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "\"\"\"\n", "fss_base = pd.read_sql(fss_base, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "d8c0147f-2c83-48c0-b93e-27e6ad1416b1", "metadata": {}, "outputs": [], "source": ["fss_base = fss_base.rename(columns={\"user_count\": \"fss_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "c6e6a3a1-7a72-418a-82d3-6a6d97d9a1db", "metadata": {}, "outputs": [], "source": ["fss_base = fss_base.rename(columns={\"fss_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "099c5a26-a663-48c6-b905-03a8f8461122", "metadata": {}, "outputs": [], "source": ["fss_base[\"date_ts\"] = pd.to_datetime(fss_base[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "ee05d6b6-670b-4736-8e7f-d29e08b031b6", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_fss_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"fss_count\", \"type\": \"integer\", \"description\": \"fsc_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "87868550-da8c-4934-bcc6-816284bfa086", "metadata": {}, "outputs": [], "source": ["pb.to_trino(fss_base, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "528148da-a5df-40d7-8ca7-204d7a661369", "metadata": {}, "outputs": [], "source": ["job_pref = f\"\"\"\n", "WITH base AS(\n", "SELECT \n", "    dt,\n", "    from_unixtime(timestamp/1000) AS timestamp_ts,\n", "    source, device_id, session_id, user_id, location, location_info, event_name,\n", "    event_value, page_name, event_type, app_version_code, app_version,\n", "    cast(location_info[1] as decimal(10,6)) AS latitude, cast(location_info[2] AS decimal(10,6)) as longitude\n", "FROM \n", "    zomato.blinkit_jumbo2.store_onboarding_app_events\n", "WHERE \n", "    dt >= '20250101'\n", "    AND user_id != ''\n", "\n", "),\n", "\n", "base_final AS(\n", "select * from \n", "(\n", "SELECT \n", "    user_id,\n", "    MIN(CASE WHEN event_name = 'EMPLOY_TYPE_SELECTED' THEN dt END) AS dt,\n", "    MIN(CASE WHEN event_name = 'EMPLOY_TYPE_SELECTED' THEN event_type END) AS job_preference,\n", "    min_by(latitude, case when latitude<>0 then timestamp_ts end) as latitude,\n", "    min_by(longitude, case when longitude<>0 then timestamp_ts end) as longitude\n", " FROM \n", "    base\n", "    GROUP BY 1\n", "    )\n", "where dt is not null and job_preference is not null\n", "),\n", "\n", "base_camaign AS(\n", "WITH base AS(\n", " select \n", "device_id,\n", "dt, \n", "campaign_status, \n", "campaign_name, \n", "media_source\n", "from\n", "(\n", "    SELECT \n", "        device_id,\n", "        dt, \n", "        regexp_extract(event_value, 'af_status=([^,}}]+)', 1) AS campaign_status,\n", "        regexp_extract(event_value, 'campaign=([^,}}]+)', 1) AS campaign_name,\n", "        regexp_extract(event_value, 'media_source=([^,}}]+)', 1) AS media_source, \n", "        regexp_extract(event_value, 'is_first_launch=([^,}}]+)', 1) AS is_first_launch\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE\n", "        dt >= '20250101'\n", "        AND event_name = 'APPSFLYER_INSTALL_CONVERSION_EVENT'\n", ")\n", "where is_first_launch = 'true'\n", "), \n", "\n", "device_base AS (\n", "    SELECT \n", "        device_id,\n", "        campaign_status,\n", "        campaign_name,\n", "        media_source\n", "    FROM (\n", "        SELECT *,\n", "               ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY dt) AS rn\n", "        FROM base\n", "        WHERE campaign_status IS NOT NULL\n", "    ) t\n", "    WHERE rn = 1\n", "),\n", "\n", "campaign_base AS (\n", "    SELECT * FROM device_base\n", "),\n", "\n", "user_base AS (\n", "    SELECT \n", "        device_id,\n", "        user_id\n", "    FROM zomato.blinkit_jumbo2.store_onboarding_app_events\n", "    WHERE dt >= '20250101'\n", "    AND user_id != ''\n", "    GROUP BY 1, 2\n", ")\n", "\n", "SELECT \n", "    a.*,\n", "    b.user_id\n", "FROM campaign_base AS a\n", "LEFT JOIN user_base AS b\n", "ON a.device_id = b.device_id\n", "),\n", "\n", "fianl_user_base AS(\n", "SELECT \n", "b.*,\n", "campaign_status\n", "FROM base_final AS b\n", "LEFT JOIN \n", "base_camaign AS a\n", "ON b.user_id = a.user_id\n", "),\n", "    \n", "outlet_mapping as(\n", "    select frontend_merchant_id, pos_outlet_id, pos_outlet_name, pos_outlet_city_id, pos_outlet_city_name,\n", "    merchant_store_latitude, merchant_store_longitude\n", "    from dwh.dim_merchant_outlet_facility_mapping fm\n", "    left join\n", "    (\n", "        select merchant_id, merchant_store_latitude, merchant_store_longitude\n", "        from dwh.dim_merchant\n", "        where is_current = true\n", "        and is_enabled = true\n", "        and is_active = true\n", "    ) m on fm.frontend_merchant_id=m.merchant_id\n", "    where is_current = true\n", "    and is_mapping_enabled = true\n", "    and is_current_mapping_active = true\n", "    and merchant_business_type_id = 7\n", "    and pos_outlet_id not in (4146,4381)\n", "    group by 1,2,3,4,5,6,7\n", "),\n", "\n", "final_query_base AS(\n", "SELECT \n", "    user_id,\n", "    date(date_parse(dt,'%%Y%%m%%d')) as job_preference_date,\n", "    job_preference, \n", "    latitude,\n", "    longitude,\n", "    campaign_status,\n", "    city,\n", "    outlet_id,\n", "    outlet_name,\n", "    distance,\n", "    rnk\n", "FROM(\n", "SELECT\n", "    *,\n", "    row_number() over(partition by user_id order by distance asc) AS rnk\n", "FROM\n", "    (SELECT \n", "        x.*,\n", "        pos_outlet_city_name AS city,\n", "        pos_outlet_id AS outlet_id,\n", "        pos_outlet_name AS outlet_name,\n", "        CASE WHEN latitude > 0 AND longitude > 0 AND merchant_store_latitude > 0 AND merchant_store_longitude > 0\n", "        THEN st_Distance(st_point(latitude, longitude), st_point(merchant_store_latitude, merchant_store_longitude)) ELSE 9999 end AS distance\n", "    FROM\n", "        fianl_user_base AS x\n", "    LEFT JOIN\n", "        outlet_mapping AS om\n", "    ON 1 = 1\n", "    ))\n", "WHERE rnk = 1\n", ")\n", " \n", "select * from \n", "(\n", "SELECT \n", "job_preference_date,\n", "city,\n", "CASE \n", "    WHEN job_preference IN ('part_time', 'PART_TIME') THEN 'part_time'\n", "    WHEN job_preference IN ('full_time', 'FULL_TIME') THEN 'full_time'\n", "    ELSE 'total' END AS work_preference_bucket,\n", "CASE\n", "     WHEN campaign_status = 'Organic' THEN 'Playstore'\n", "     WHEN campaign_status = 'Non-organic' THEN 'Digital'\n", "     else 'Others' END AS channel_bucket,\n", "COUNT(DISTINCT user_id) AS user_count\n", "FROM final_query_base\n", "GROUP BY 1, 2, 3, 4\n", ")\n", "where job_preference_date >= current_date - interval '70' day\n", "\"\"\"\n", "job_pref = pd.read_sql(job_pref, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "d54fcdb0-e718-4c0e-b3ee-1e401242570e", "metadata": {}, "outputs": [], "source": ["job_pref = job_pref.rename(columns={\"user_count\": \"jb_count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "e7d92550-299e-4ff8-975c-ae39548d0bfe", "metadata": {}, "outputs": [], "source": ["job_pref = job_pref.rename(columns={\"job_preference_date\": \"date_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "4b88431d-c36b-432c-b1a4-72c8fcae25c0", "metadata": {}, "outputs": [], "source": ["job_pref[\"date_ts\"] = pd.to_datetime(job_pref[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "id": "53126128-0e1e-4256-9ad9-91cab76d82ec", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"campaign_level_absolute_jb_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ts\", \"type\": \"date\", \"description\": \"date\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "        {\n", "            \"name\": \"work_preference_bucket\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"work_preference_bucket\",\n", "        },\n", "        {\"name\": \"channel_bucket\", \"type\": \"varchar\", \"description\": \"channel_bucket\"},\n", "        {\"name\": \"jb_count\", \"type\": \"integer\", \"description\": \"jb_count\"},\n", "    ],\n", "    \"primary_key\": [\"date_ts\", \"city\", \"work_preference_bucket\", \"channel_bucket\"],\n", "    \"partition_key\": [\"date_ts\"],\n", "    \"incremental_key\": \"date_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This Table contain absolute onboarding split data\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1894f767-43d4-49b9-b399-f1ed4ad353a0", "metadata": {}, "outputs": [], "source": ["pb.to_trino(job_pref, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "e7ce179e-00a6-4277-a8e4-c24c554d1a70", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ff6b958a-75e0-4798-9f30-a253ea1c4bde", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4c354503-c7a3-4212-ae8d-abe4b9e042ba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}