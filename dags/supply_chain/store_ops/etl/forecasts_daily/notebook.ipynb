{"cells": [{"cell_type": "code", "execution_count": null, "id": "bf341c66-141b-4781-932e-dece2ba623b3", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"JUPYTERHUB_USER\"] = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "86199a62-7686-4548-9b22-5daa3d4b9e34", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import pencilbox as pb\n", "import numpy as np\n", "import itertools\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import math\n", "import hashlib"]}, {"cell_type": "code", "execution_count": null, "id": "71aa6740-c7fb-46e6-843e-8019786e0cd5", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "04b0c56e-45ca-4b0c-9f35-f8a27458b781", "metadata": {}, "outputs": [], "source": ["now_ts = (datetime.now() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "8db4c2a3-a8e6-4767-bb22-3aac88e144ac", "metadata": {}, "outputs": [], "source": ["query_temp = \"\"\"\n", "with order_base as \n", "(\n", "select\n", "week_start_date, \n", "date, \n", "entity_id, \n", "instore_orders, \n", "items_per_order\n", "from blinkit_staging.storeops_etls.planner_core_forecasts_daily_m2\n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "\n", "city_inputs as \n", "(\n", "    select entity_id as outlet_id, week_start_date, max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status in ('pending')\n", "    group by 1,2\n", "), \n", "\n", "store_master as \n", "(select *\n", "from interim.planner_instore_store_details\n", "where outlet_id is not null\n", "),\n", "\n", "final_base as \n", "(select\n", "  d AS calendar_date,\n", "  date_trunc('week',d) AS week_start_date,\n", "  outlet_id,\n", "  outlet_name,\n", "  city_name,\n", "  mode_of_ops,\n", "  live_status,\n", "  live_date,\n", "  PPI_Difficulty,\n", "  IPH_Difficulty\n", "from UNNEST(\n", "       SEQUENCE(\n", "         date_trunc('month', current_date),\n", "         date_add('day', -1, date_trunc('month', date_add('month', 2, current_date)))\n", "       )\n", "     ) as t(d)\n", "cross join store_master\n", ")\n", ",\n", "\n", "next_week_agg AS (\n", "  SELECT \n", "    entity_id,\n", "    week_start_date,\n", "    MAX(instore_orders) AS max_orders,\n", "    SUM(instore_orders) AS sum_orders\n", "  FROM order_base\n", "  GROUP BY 1,2\n", "), \n", "\n", "orders_details_1 as (\n", "  select\n", "    o1.entity_id,\n", "    o1.week_start_date,\n", "    max(o1.instore_orders) as proj_max_orders_l,\n", "    sum(o1.instore_orders) as proj_sum_orders_l,\n", "    GREATEST(\n", "      max(o1.instore_orders),\n", "      coalesce(nwa.max_orders, 0)\n", "    ) AS proj_max_orders_tbl,\n", "    GREATEST(\n", "      sum(o1.instore_orders),\n", "      coalesce(nwa.sum_orders, 0)\n", "    ) AS proj_sum_orders_tbl\n", "  FROM order_base o1\n", "  LEFT JOIN next_week_agg nwa\n", "    ON o1.entity_id = nwa.entity_id\n", "    AND nwa.week_start_date = o1.week_start_date + interval '7' day\n", "  GROUP BY o1.entity_id, o1.week_start_date, nwa.max_orders, nwa.sum_orders\n", "  )\n", "  \n", ",\n", "\n", "order_details_2 as \n", "(select \n", "o1.entity_id,\n", "o1.week_start_date,\n", "(case when live_status = 'Live' then proj_max_orders_l*ma.order_buffer \n", "else proj_max_orders_tbl*ma.od_assmp_picker_util  end) as proj_max_central_orders,\n", "(case when live_status = 'Live' then proj_sum_orders_l*ma.order_buffer \n", "else proj_sum_orders_tbl*ma.od_assmp_picker_util  end)as proj_sum_central_orders\n", "from orders_details_1 o1\n", "left join interim.planner_instore_misc_assumption ma\n", "on o1.entity_id = cast(ma.outlet_id as integer)\n", "and o1.week_start_date = ma.week_start_date\n", "where ma.week_start_date > date_trunc('month', current_date)\n", ")\n", ",\n", "\n", "ipo as \n", "(select outlet_id, week_start_date, sum(ipc_factor_product)/sum(instore_orders) as ipc_factor\n", "from\n", "(select \n", "ea.outlet_id,\n", "ea.date_,\n", "ea.week_start_date,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders) as ipc_factor_product,\n", "instore_orders,\n", "putaway_factor\n", "from interim.planner_instore_event_assumption ea\n", "left join order_base o\n", "on ea.outlet_id = cast(o.entity_id as varchar)\n", "and ea.date_ = o.date\n", "where date_ >= date_trunc('month', current_date)\n", ")\n", "group by 1,2\n", "),\n", "\n", "planned_base as \n", "(\n", "select * from blinkit_staging.storeops_etls.planner_instore_sla_assumption \n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "ppi_calc_3 as\n", "(select\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "im.order_date,\n", "sum(picking_time) as picking_time, \n", "sum(total_items_quantity_ordered) as total_items_quantity_ordered\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '10'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", "), \n", "\n", "buffer as \n", "(\n", "select week_start_date, outlet_id,\n", "case when ppi_3 >= planned_ppi + 4 then 2 else 0 end as ppi_buffer\n", "from\n", "(\n", "select p.week_start_date, p.outlet_id, p.planned_ppi, coalesce(try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)),0) as ppi_3\n", "from \n", "planned_base p\n", "left join\n", "ppi_calc_3 c\n", "on c.order_date between cast(p.week_start_date - interval '8' day as varchar) and cast(p.week_start_date - interval '6' day as varchar)\n", "and p.outlet_id = c.outlet_id \n", "group by 1,2,3\n", ")\n", "), \n", "\n", "calc_1 as \n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "fixed_util_class, \n", "iph_class, \n", "ppi_class, \n", "case when iph_city is not null then iph_city else planned_iph end as planned_iph, \n", "case when ppi_city is not null then ppi_city else planned_ppi + ppi_buffer end as planned_ppi, \n", "case when util_city is not null then util_city else planned_picker_util end as planned_picker_util, \n", "ppi_city,\n", "util_city,\n", "iph_city,\n", "ppi_buffer\n", "from\n", "(select \n", "f.*,\n", "o.proj_max_central_orders as proj_max_orders,\n", "o.proj_sum_central_orders as proj_sum_orders,\n", "(i.ipc_factor*o.proj_max_central_orders) as max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders) as sum_items,\n", "(i.ipc_factor*o.proj_max_central_orders*ea.putaway_factor) as putaway_max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders*ea.putaway_factor) as putaway_sum_items,\n", "ea.putaway_factor,\n", "ea.week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "ma.od_peak_contribution,\n", "ma.od_average_contribution,\n", "ma.od_assmp_picker_util,\n", "sa.planned_ppi_od as od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "i.ipc_factor, \n", "planned_iph, \n", "planned_picker_util, \n", "planned_ppi,\n", "fixed_util_class,\n", "iph_class,\n", "ppi_class,\n", "ppi_city,\n", "util_city,\n", "iph_city, \n", "coalesce(ppi_buffer,0) as ppi_buffer\n", "from final_base f\n", "inner join order_details_2 o\n", "on f.outlet_id = o.entity_id \n", "and f.week_start_date = o.week_start_date\n", "inner join ipo i\n", "on f.outlet_id = cast(i.outlet_id as integer)\n", "and f.week_start_date = i.week_start_date\n", "inner join interim.planner_instore_event_assumption ea\n", "on f.outlet_id = cast(ea.outlet_id as integer)\n", "and f.calendar_date = cast(ea.date_ as date)\n", "and ea.date_ >= date_trunc('month', current_date)\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date > date_trunc('month', current_date)\n", "inner join blinkit_staging.storeops_etls.planner_instore_sla_assumption sa\n", "on f.week_start_date = sa.week_start_date\n", "and f.outlet_id = sa.outlet_id\n", "and sa.week_start_date > date_trunc('month', current_date)\n", "left join city_inputs as ci\n", "on f.week_start_date = ci.week_start_date\n", "and f.outlet_id = ci.outlet_id\n", "and ci.week_start_date > date_trunc('month', current_date)\n", "left join buffer as b\n", "on f.week_start_date = b.week_start_date\n", "and f.outlet_id = b.outlet_id\n", ")\n", "), \n", "\n", "\n", "classing as\n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "planned_iph, \n", "planned_ppi, \n", "planned_picker_util, \n", "case when ppi_city is not null or ppi_buffer > 0 then ppi_class_mid else ppi_class end as ppi_class, \n", "fixed_util_class_mid as fixed_util_class, \n", "iph_class_mid as iph_class\n", "from\n", "(select \n", "c.*,\n", "case \n", "when c.planned_ppi between 0 and 14.99 then 1\n", "when c.planned_ppi between 15 and 19.99 then 2\n", "when c.planned_ppi between 20 and 30 then 3\n", "when c.planned_ppi > 30 then 4\n", "end as ppi_class_mid,\n", "case \n", "when c.planned_picker_util between 0 and 0.299 then 1\n", "when c.planned_picker_util between 0.30 and 0.399 then 2\n", "when c.planned_picker_util between 0.400 and 0.55 then 3\n", "when c.planned_picker_util > 0.55 then 4\n", "end as fixed_util_class_mid,\n", "case\n", "when c.planned_iph between 0 and 119.999 then 1\n", "when c.planned_iph between 120 and 149.999 then 2\n", "when c.planned_iph between 150 and 200 then 3\n", "when c.planned_iph > 200 then 4\n", "end as iph_class_mid\n", "from calc_1 c\n", ")\n", "), \n", "\n", "calc_2 as \n", "(\n", "    select c.*,\n", "    fa.ppi_improvement,\n", "    fa1.other_flow_assumption,\n", "    case \n", "    when mandays_hc_outlet_assmp = 0 and mandays_hc_city_assmp = 0 then manday_hc_default\n", "    when mandays_hc_outlet_assmp <> 0 then mandays_hc_outlet_assmp\n", "    when mandays_hc_outlet_assmp = 0 then mandays_hc_city_assmp\n", "    end as att_factor\n", "    from classing c\n", "    left join interim.planner_instore_flow_assumption fa\n", "    on c.week_start_date = fa.week_start_date\n", "    and c.ppi_class = fa.ppi_class\n", "    and fa.week_start_date > date_trunc('month', current_date)\n", "    left join interim.planner_instore_flow_assumption fa1\n", "    on c.iph_class = fa1.iph_class\n", "    and c.fixed_util_class = fa1.p_util_class\n", "    and fa1.week_start_date > date_trunc('month', current_date)\n", "), \n", "\n", "calc_3 as \n", "(select c.*,\n", "c.od_peak_contribution*max_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_peak,\n", "c.od_average_contribution*sum_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_average,\n", "sum_items*(1-c.od_average_contribution)*(planned_ppi/3600)/planned_picker_util as picker_hours,\n", "putaway_sum_items*1.00/planned_iph as putter_hours\n", "from calc_2 c\n", ")\n", "\n", ",\n", "\n", "calc_4 as \n", "(select *,\n", "(picker_hours + putter_hours)/(1-other_flow_assumption) as total_mh,\n", "((picker_hours + putter_hours)/(1-other_flow_assumption))/workhours_to_workday_conv as total_manday\n", "from calc_3\n", "),\n", "\n", "calc_5 as \n", "(select distinct *,\n", "total_manday/att_factor as headcount\n", "from calc_4)\n", ",\n", "\n", "orders_with_max as \n", "(\n", "select\n", "  entity_id,\n", "  week_start_date,\n", "  date,\n", "  instore_orders,\n", "  max(instore_orders) over (partition by entity_id, week_start_date) AS max_orders\n", "from order_base\n", "order by 1,2,3\n", "),\n", "\n", "fixed_manday_base AS (\n", "  SELECT\n", "    entity_id,\n", "    week_start_date,\n", "    date,\n", "    instore_orders,\n", "    max_orders,\n", "    max(CASE WHEN instore_orders = max_orders THEN date END) \n", "      OVER (PARTITION BY entity_id, week_start_date) AS max_order_date\n", "  FROM orders_with_max\n", "), \n", "\n", "fixed_manday as\n", "(select c.*,\n", "case when live_status = 'Live' then ((b.instore_orders*(1+putaway_factor))/(proj_sum_orders*(1+putaway_factor)))*total_manday \n", "else 0 end as requirement_manday,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then 0\n", "    when live_status = 'Live' then (((b.instore_orders/proj_sum_orders)*headcount) + ((b.max_orders/proj_sum_orders)*(headcount/6)))*(week_off_percent)\n", "    else 0 end as week_off_manday\n", "from calc_5 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date),\n", "\n", "fixed_manday_2 as \n", "(select *,\n", "case when live_status = 'Live' then (headcount - week_off_manday)*((att_factor/6))\n", "else headcount*0.86 end as actual_manday,\n", "greatest((requirement_manday - (case when live_status = 'Live' then (headcount - week_off_manday)*((att_factor/6))\n", "else headcount*0.86 end)),0) as addition_od_manday\n", "from fixed_manday),\n", "\n", "od_mandays as \n", "(select *,\n", "manhours_od_average/8.00 as budgeted_od_manday_average,\n", "manhours_od_peak/8.00 as peak_od_manday\n", "from fixed_manday_2),\n", "\n", "od_manday_2 as\n", "(select om.*,\n", "case when live_status = 'Live' and b.date <> b.max_order_date\n", "then (((budgeted_od_manday_average)*b.instore_orders)/proj_sum_orders) + addition_od_manday\n", "when live_status = 'Live' and b.date = b.max_order_date\n", "then (peak_od_manday) + addition_od_manday\n", "when live_status = 'To_be_Live'\n", "then ((budgeted_od_manday_average)/(To_be_Live_Mandays_distribution)) + addition_od_manday\n", "end as bugeted_od_manday\n", "from od_mandays om\n", "left join fixed_manday_base b\n", "on om.outlet_id = b.entity_id \n", "and om.calendar_date = b.date),\n", "\n", "daily_fixed_manday as \n", "(select distinct c.*,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then headcount\n", "    when live_status = 'Live' then requirement_manday\n", "    else requirement_manday end as daily_fixed_mandays\n", "from od_manday_2 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date\n", ")\n", "\n", "\n", "select a.* from\n", "(\n", "select distinct \n", "week_start_date,\n", "calendar_date as date,\n", "outlet_id as entity_id,\n", "round(actual_manday) as mandays_fixed,\n", "round(bugeted_od_manday*8.0) as manhours_od,\n", "round(daily_fixed_mandays) as headcount,\n", "round(week_off_manday) as week_offs\n", "from daily_fixed_manday\n", ") a\n", "inner join\n", "(\n", "    select entity_id, week_start_date, \n", "    max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status = 'pending'\n", "    group by 1,2\n", ") b\n", "on a.entity_id = b.entity_id\n", "and a.week_start_date = b.week_start_date\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "85177aeb-bd8e-4ca4-ab05-956fb3257c1c", "metadata": {}, "outputs": [], "source": ["query_temp = pd.read_sql(query_temp, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "bc3d259b-5a1c-4e24-90c8-ec23e9f263bf", "metadata": {}, "outputs": [], "source": ["query_temp = query_temp[\n", "    [\n", "        \"week_start_date\",\n", "        \"date\",\n", "        \"entity_id\",\n", "        \"mandays_fixed\",\n", "        \"manhours_od\",\n", "        \"headcount\",\n", "        \"week_offs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "36552d66-d942-45ed-aecd-6cc50e23f615", "metadata": {}, "outputs": [], "source": ["query_temp[\"updated_ts\"] = now_ts"]}, {"cell_type": "code", "execution_count": null, "id": "24190bc0-d8be-4e86-becb-6b0ad11dc945", "metadata": {}, "outputs": [], "source": ["query_temp"]}, {"cell_type": "code", "execution_count": null, "id": "8f88a8d3-3821-4658-a0d5-ef89770880f3", "metadata": {}, "outputs": [], "source": ["query_temp[\"entity_id\"] = query_temp[\"entity_id\"].astype(int)\n", "query_temp[\"week_start_date\"] = pd.to_datetime(query_temp[\"week_start_date\"], errors=\"coerce\")\n", "query_temp[\"date\"] = pd.to_datetime(query_temp[\"date\"], errors=\"coerce\")\n", "query_temp[\"mandays_fixed\"] = (\n", "    pd.to_numeric(query_temp[\"mandays_fixed\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "query_temp[\"manhours_od\"] = (\n", "    pd.to_numeric(query_temp[\"manhours_od\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "query_temp[\"headcount\"] = (\n", "    pd.to_numeric(query_temp[\"headcount\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "query_temp[\"week_offs\"] = (\n", "    pd.to_numeric(query_temp[\"week_offs\"], errors=\"coerce\").fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "69edb68a-b115-432c-a3a6-a08f39ca1878", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_daily_temp_m2\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD), week starting from monday\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"date\", \"description\": \"Date for which data is forecasted\"},\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"Unique ID for the outlet\"},\n", "        {\"name\": \"mandays_fixed\", \"type\": \"integer\", \"description\": \"Mandays required from fixed \"},\n", "        {\"name\": \"manhours_od\", \"type\": \"integer\", \"description\": \"Manhours required from OD\"},\n", "        {\"name\": \"headcount\", \"type\": \"integer\", \"description\": \"Required headcount\"},\n", "        {\"name\": \"week_offs\", \"type\": \"integer\", \"description\": \"week offs for the week\"},\n", "        {\"name\": \"updated_ts\", \"type\": \"varchar\", \"description\": \"Timestamp of last update (IST)\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"entity_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains headcount distribution at day level.\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(query_temp, **kwargs_trino)"]}, {"cell_type": "code", "execution_count": null, "id": "892c2b3b-b545-4308-a35a-bc2da1cefb60", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with order_base as \n", "(\n", "select\n", "week_start_date, \n", "date, \n", "entity_id, \n", "instore_orders, \n", "items_per_order\n", "from blinkit_staging.storeops_etls.planner_core_forecasts_daily_m2\n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "\n", "city_inputs as \n", "(\n", "    select entity_id as outlet_id, week_start_date, max_by(ppi,updated_at) as ppi_city, \n", "    max_by(items_put_away_per_hour, updated_at) as iph_city, \n", "    max_by(picker_util_fixed_wt_avg, updated_at) as util_city\n", "    from blinkit_staging.interim.planner_instore_city_responses\n", "    where week_start_date > date_trunc('week', current_date)\n", "    and status = 'approved'\n", "    group by 1,2\n", "), \n", "\n", "store_master as \n", "(select *\n", "from interim.planner_instore_store_details\n", "where outlet_id is not null\n", "),\n", "\n", "final_base as \n", "(select\n", "  d AS calendar_date,\n", "  date_trunc('week',d) AS week_start_date,\n", "  outlet_id,\n", "  outlet_name,\n", "  city_name,\n", "  mode_of_ops,\n", "  live_status,\n", "  live_date,\n", "  PPI_Difficulty,\n", "  IPH_Difficulty\n", "from UNNEST(\n", "       SEQUENCE(\n", "         date_trunc('month', current_date),\n", "         date_add('day', -1, date_trunc('month', date_add('month', 2, current_date)))\n", "       )\n", "     ) as t(d)\n", "cross join store_master\n", ")\n", ",\n", "\n", "next_week_agg AS (\n", "  SELECT \n", "    entity_id,\n", "    week_start_date,\n", "    MAX(instore_orders) AS max_orders,\n", "    SUM(instore_orders) AS sum_orders\n", "  FROM order_base\n", "  GROUP BY 1,2\n", "), \n", "\n", "orders_details_1 as (\n", "  select\n", "    o1.entity_id,\n", "    o1.week_start_date,\n", "    max(o1.instore_orders) as proj_max_orders_l,\n", "    sum(o1.instore_orders) as proj_sum_orders_l,\n", "    GREATEST(\n", "      max(o1.instore_orders),\n", "      coalesce(nwa.max_orders, 0)\n", "    ) AS proj_max_orders_tbl,\n", "    GREATEST(\n", "      sum(o1.instore_orders),\n", "      coalesce(nwa.sum_orders, 0)\n", "    ) AS proj_sum_orders_tbl\n", "  FROM order_base o1\n", "  LEFT JOIN next_week_agg nwa\n", "    ON o1.entity_id = nwa.entity_id\n", "    AND nwa.week_start_date = o1.week_start_date + interval '7' day\n", "  GROUP BY o1.entity_id, o1.week_start_date, nwa.max_orders, nwa.sum_orders\n", "  )\n", "  \n", ",\n", "\n", "order_details_2 as \n", "(select \n", "o1.entity_id,\n", "o1.week_start_date,\n", "(case when live_status = 'Live' then proj_max_orders_l*ma.order_buffer \n", "else proj_max_orders_tbl*ma.od_assmp_picker_util  end) as proj_max_central_orders,\n", "(case when live_status = 'Live' then proj_sum_orders_l*ma.order_buffer \n", "else proj_sum_orders_tbl*ma.od_assmp_picker_util  end)as proj_sum_central_orders\n", "from orders_details_1 o1\n", "left join interim.planner_instore_misc_assumption ma\n", "on o1.entity_id = cast(ma.outlet_id as integer)\n", "and o1.week_start_date = ma.week_start_date\n", "where ma.week_start_date > date_trunc('month', current_date)\n", ")\n", ",\n", "\n", "ipo as \n", "(select outlet_id, week_start_date, sum(ipc_factor_product)/sum(instore_orders) as ipc_factor\n", "from\n", "(select \n", "ea.outlet_id,\n", "ea.date_,\n", "ea.week_start_date,\n", "(o.items_per_order*(1+ea.ipo_factor)*instore_orders) as ipc_factor_product,\n", "instore_orders,\n", "putaway_factor\n", "from interim.planner_instore_event_assumption ea\n", "left join order_base o\n", "on ea.outlet_id = cast(o.entity_id as varchar)\n", "and ea.date_ = o.date\n", "where date_ >= date_trunc('month', current_date)\n", ")\n", "group by 1,2\n", "),\n", "\n", "planned_base as \n", "(\n", "select * from blinkit_staging.storeops_etls.planner_instore_sla_assumption \n", "where week_start_date > date_trunc('week', current_date)\n", "), \n", "\n", "ppi_calc_3 as\n", "(select\n", "im.outlet_id,\n", "im.outlet_name,\n", "im.city,\n", "im.order_date,\n", "sum(picking_time) as picking_time, \n", "sum(total_items_quantity_ordered) as total_items_quantity_ordered\n", "from storeops_etls.instore_metrics_daily im\n", "where order_date between cast((current_date - interval  '10'  day)  as varchar) \n", "and cast((current_date - interval  '1'  day)  as var<PERSON><PERSON>)\n", "group by 1,2,3,4\n", "), \n", "\n", "buffer as \n", "(\n", "select week_start_date, outlet_id,\n", "case when ppi_3 >= planned_ppi + 4 then 2 else 0 end as ppi_buffer\n", "from\n", "(\n", "select p.week_start_date, p.outlet_id, p.planned_ppi, coalesce(try(sum(picking_time)*1.00/nullif(sum(total_items_quantity_ordered),0)),0) as ppi_3\n", "from \n", "planned_base p\n", "left join\n", "ppi_calc_3 c\n", "on c.order_date between cast(p.week_start_date - interval '8' day as varchar) and cast(p.week_start_date - interval '6' day as varchar)\n", "and p.outlet_id = c.outlet_id \n", "group by 1,2,3\n", ")\n", "), \n", "\n", "calc_1 as \n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "fixed_util_class, \n", "iph_class, \n", "ppi_class, \n", "case when iph_city is not null then iph_city else planned_iph end as planned_iph, \n", "case when ppi_city is not null then ppi_city else planned_ppi + ppi_buffer end as planned_ppi, \n", "case when util_city is not null then util_city else planned_picker_util end as planned_picker_util, \n", "ppi_city,\n", "util_city,\n", "iph_city,\n", "ppi_buffer\n", "from\n", "(select \n", "f.*,\n", "o.proj_max_central_orders as proj_max_orders,\n", "o.proj_sum_central_orders as proj_sum_orders,\n", "(i.ipc_factor*o.proj_max_central_orders) as max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders) as sum_items,\n", "(i.ipc_factor*o.proj_max_central_orders*ea.putaway_factor) as putaway_max_items,\n", "(i.ipc_factor*o.proj_sum_central_orders*ea.putaway_factor) as putaway_sum_items,\n", "ea.putaway_factor,\n", "ea.week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "ma.od_peak_contribution,\n", "ma.od_average_contribution,\n", "ma.od_assmp_picker_util,\n", "sa.planned_ppi_od as od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "i.ipc_factor, \n", "planned_iph, \n", "planned_picker_util, \n", "planned_ppi,\n", "fixed_util_class,\n", "iph_class,\n", "ppi_class,\n", "ppi_city,\n", "util_city,\n", "iph_city, \n", "coalesce(ppi_buffer,0) as ppi_buffer\n", "from final_base f\n", "inner join order_details_2 o\n", "on f.outlet_id = o.entity_id \n", "and f.week_start_date = o.week_start_date\n", "inner join ipo i\n", "on f.outlet_id = cast(i.outlet_id as integer)\n", "and f.week_start_date = i.week_start_date\n", "inner join interim.planner_instore_event_assumption ea\n", "on f.outlet_id = cast(ea.outlet_id as integer)\n", "and f.calendar_date = cast(ea.date_ as date)\n", "and ea.date_ >= date_trunc('month', current_date)\n", "inner join interim.planner_instore_misc_assumption ma\n", "on f.outlet_id = cast(ma.outlet_id as integer)\n", "and f.week_start_date = ma.week_start_date\n", "and ma.week_start_date > date_trunc('month', current_date)\n", "inner join blinkit_staging.storeops_etls.planner_instore_sla_assumption sa\n", "on f.week_start_date = sa.week_start_date\n", "and f.outlet_id = sa.outlet_id\n", "and sa.week_start_date > date_trunc('month', current_date)\n", "left join city_inputs as ci\n", "on f.week_start_date = ci.week_start_date\n", "and f.outlet_id = ci.outlet_id\n", "and ci.week_start_date > date_trunc('month', current_date)\n", "left join buffer as b\n", "on f.week_start_date = b.week_start_date\n", "and f.outlet_id = b.outlet_id\n", ")\n", "), \n", "\n", "\n", "classing as\n", "(\n", "select \n", "calendar_date,\n", "week_start_date,\n", "outlet_id,\n", "outlet_name,\n", "city_name,\n", "mode_of_ops,\n", "live_status,\n", "live_date,\n", "PPI_Difficulty,\n", "IPH_Difficulty, \n", "proj_max_orders,\n", "proj_sum_orders,\n", "max_items,\n", "sum_items,\n", "putaway_max_items,\n", "putaway_sum_items,\n", "putaway_factor,\n", "week_off_percent,\n", "picker_util_fixed_growth_factor,\n", "picker_util_fixed_range_min,\n", "picker_util_fixed_range_max,\n", "iph_growth_factor,\n", "iph_range_max,\n", "iph_range_min,\n", "od_peak_contribution,\n", "od_average_contribution,\n", "od_assmp_picker_util,\n", "od_assmp_ppi,\n", "workhours_to_workday_conv,\n", "ppi_to_be_live,\n", "store_ppi_min,\n", "mandays_hc_outlet_assmp,\n", "mandays_hc_city_assmp,\n", "manday_hc_default,\n", "To_be_Live_Mandays_distribution, \n", "ipc_factor, \n", "planned_iph, \n", "planned_ppi, \n", "planned_picker_util, \n", "case when ppi_city is not null or ppi_buffer > 0 then ppi_class_mid else ppi_class end as ppi_class, \n", "fixed_util_class_mid as fixed_util_class, \n", "iph_class_mid as iph_class\n", "from\n", "(select \n", "c.*,\n", "case \n", "when c.planned_ppi between 0 and 14.99 then 1\n", "when c.planned_ppi between 15 and 19.99 then 2\n", "when c.planned_ppi between 20 and 30 then 3\n", "when c.planned_ppi > 30 then 4\n", "end as ppi_class_mid,\n", "case \n", "when c.planned_picker_util between 0 and 0.299 then 1\n", "when c.planned_picker_util between 0.30 and 0.399 then 2\n", "when c.planned_picker_util between 0.400 and 0.55 then 3\n", "when c.planned_picker_util > 0.55 then 4\n", "end as fixed_util_class_mid,\n", "case\n", "when c.planned_iph between 0 and 119.999 then 1\n", "when c.planned_iph between 120 and 149.999 then 2\n", "when c.planned_iph between 150 and 200 then 3\n", "when c.planned_iph > 200 then 4\n", "end as iph_class_mid\n", "from calc_1 c\n", ")\n", "), \n", "\n", "calc_2 as \n", "(\n", "    select c.*,\n", "    fa.ppi_improvement,\n", "    fa1.other_flow_assumption,\n", "    case \n", "    when mandays_hc_outlet_assmp = 0 and mandays_hc_city_assmp = 0 then manday_hc_default\n", "    when mandays_hc_outlet_assmp <> 0 then mandays_hc_outlet_assmp\n", "    when mandays_hc_outlet_assmp = 0 then mandays_hc_city_assmp\n", "    end as att_factor\n", "    from classing c\n", "    left join interim.planner_instore_flow_assumption fa\n", "    on c.week_start_date = fa.week_start_date\n", "    and c.ppi_class = fa.ppi_class\n", "    and fa.week_start_date > date_trunc('month', current_date)\n", "    left join interim.planner_instore_flow_assumption fa1\n", "    on c.iph_class = fa1.iph_class\n", "    and c.fixed_util_class = fa1.p_util_class\n", "    and fa1.week_start_date > date_trunc('month', current_date)\n", "), \n", "\n", "calc_3 as \n", "(select c.*,\n", "c.od_peak_contribution*max_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_peak,\n", "c.od_average_contribution*sum_items*((od_assmp_ppi/3600)/od_assmp_picker_util) as manhours_od_average,\n", "sum_items*(1-c.od_average_contribution)*(planned_ppi/3600)/planned_picker_util as picker_hours,\n", "putaway_sum_items*1.00/planned_iph as putter_hours\n", "from calc_2 c\n", ")\n", "\n", ",\n", "\n", "calc_4 as \n", "(select *,\n", "(picker_hours + putter_hours)/(1-other_flow_assumption) as total_mh,\n", "((picker_hours + putter_hours)/(1-other_flow_assumption))/workhours_to_workday_conv as total_manday\n", "from calc_3\n", "),\n", "\n", "calc_5 as \n", "(select distinct *,\n", "total_manday/att_factor as headcount\n", "from calc_4)\n", ",\n", "\n", "orders_with_max as \n", "(\n", "select\n", "  entity_id,\n", "  week_start_date,\n", "  date,\n", "  instore_orders,\n", "  max(instore_orders) over (partition by entity_id, week_start_date) AS max_orders\n", "from order_base\n", "order by 1,2,3\n", "),\n", "\n", "fixed_manday_base AS (\n", "  SELECT\n", "    entity_id,\n", "    week_start_date,\n", "    date,\n", "    instore_orders,\n", "    max_orders,\n", "    max(CASE WHEN instore_orders = max_orders THEN date END) \n", "      OVER (PARTITION BY entity_id, week_start_date) AS max_order_date\n", "  FROM orders_with_max\n", "), \n", "\n", "fixed_manday as\n", "(select c.*,\n", "case when live_status = 'Live' then ((b.instore_orders*(1+putaway_factor))/(proj_sum_orders*(1+putaway_factor)))*total_manday \n", "else 0 end as requirement_manday,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then 0\n", "    when live_status = 'Live' then (((b.instore_orders/proj_sum_orders)*headcount) + ((b.max_orders/proj_sum_orders)*(headcount/6)))*(week_off_percent)\n", "    else 0 end as week_off_manday\n", "from calc_5 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date),\n", "\n", "fixed_manday_2 as \n", "(select *,\n", "case when live_status = 'Live' then (headcount - week_off_manday)*((att_factor/6))\n", "else headcount*0.86 end as actual_manday,\n", "greatest((requirement_manday - (case when live_status = 'Live' then (headcount - week_off_manday)*((att_factor/6))\n", "else headcount*0.86 end)),0) as addition_od_manday\n", "from fixed_manday),\n", "\n", "od_mandays as \n", "(select *,\n", "manhours_od_average/8.00 as budgeted_od_manday_average,\n", "manhours_od_peak/8.00 as peak_od_manday\n", "from fixed_manday_2),\n", "\n", "od_manday_2 as\n", "(select om.*,\n", "case when live_status = 'Live' and b.date <> b.max_order_date\n", "then (((budgeted_od_manday_average)*b.instore_orders)/proj_sum_orders) + addition_od_manday\n", "when live_status = 'Live' and b.date = b.max_order_date\n", "then (peak_od_manday) + addition_od_manday\n", "when live_status = 'To_be_Live'\n", "then ((budgeted_od_manday_average)/(To_be_Live_Mandays_distribution)) + addition_od_manday\n", "end as bugeted_od_manday\n", "from od_mandays om\n", "left join fixed_manday_base b\n", "on om.outlet_id = b.entity_id \n", "and om.calendar_date = b.date),\n", "\n", "daily_fixed_manday as \n", "(select distinct c.*,\n", "case when live_status = 'Live' and c.week_start_date = b.week_start_date and b.date = b.max_order_date then headcount\n", "    when live_status = 'Live' then requirement_manday\n", "    else requirement_manday end as daily_fixed_mandays\n", "from od_manday_2 c\n", "left join fixed_manday_base b\n", "on c.outlet_id = b.entity_id\n", "and c.calendar_date = b.date\n", "and c.week_start_date = b.week_start_date\n", ")\n", "\n", "select distinct \n", "week_start_date,\n", "calendar_date as date,\n", "outlet_id as entity_id,\n", "round(actual_manday) as mandays_fixed,\n", "round(bugeted_od_manday*8.0) as manhours_od,\n", "round(daily_fixed_mandays) as headcount,\n", "round(week_off_manday) as week_offs\n", "from daily_fixed_manday\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "413d8186-91d9-41ef-a50e-3f56101f1c8e", "metadata": {}, "outputs": [], "source": ["daily_forecast = pd.read_sql(query, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "172cf596-631d-40f2-8360-fc13932fbc55", "metadata": {}, "outputs": [], "source": ["daily_forecast_df = daily_forecast[\n", "    [\n", "        \"week_start_date\",\n", "        \"date\",\n", "        \"entity_id\",\n", "        \"mandays_fixed\",\n", "        \"manhours_od\",\n", "        \"headcount\",\n", "        \"week_offs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9ad6d1ef-2ebd-4628-9214-f0c367e29e03", "metadata": {}, "outputs": [], "source": ["daily_forecast_df[\"entity_id\"] = daily_forecast_df[\"entity_id\"].astype(int)\n", "daily_forecast_df[\"week_start_date\"] = pd.to_datetime(\n", "    daily_forecast_df[\"week_start_date\"], errors=\"coerce\"\n", ")\n", "daily_forecast_df[\"date\"] = pd.to_datetime(daily_forecast_df[\"date\"], errors=\"coerce\")\n", "daily_forecast_df[\"mandays_fixed\"] = (\n", "    pd.to_numeric(daily_forecast_df[\"mandays_fixed\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "daily_forecast_df[\"manhours_od\"] = (\n", "    pd.to_numeric(daily_forecast_df[\"manhours_od\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "daily_forecast_df[\"headcount\"] = (\n", "    pd.to_numeric(daily_forecast_df[\"headcount\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "daily_forecast_df[\"week_offs\"] = (\n", "    pd.to_numeric(daily_forecast_df[\"week_offs\"], errors=\"coerce\").fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bc4c18b5-9feb-45e3-995a-9d9c361abf54", "metadata": {}, "outputs": [], "source": ["try:\n", "    existing_query = \"\"\"\n", "    select * from blinkit_staging.storeops_etls.planner_instore_forecasts_daily_m2\n", "    where date > date_trunc('week', current_date)\n", "    \"\"\"\n", "    daily_forecast_existing_df = pd.read_sql(existing_query, trino_con)\n", "\n", "except Exception as e:\n", "\n", "    daily_forecast_existing_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "c6ab607b-2a2d-4518-9a9f-be0aa13de724", "metadata": {}, "outputs": [], "source": ["required_cols = [\n", "    \"entity_id\",\n", "    \"week_start_date\",\n", "    \"date\",\n", "    \"mandays_fixed\",\n", "    \"manhours_od\",\n", "    \"headcount\",\n", "    \"week_offs\",\n", "    \"updated_ts\",\n", "]\n", "for col in required_cols:\n", "    if col not in daily_forecast_existing_df.columns:\n", "        daily_forecast_existing_df[col] = pd.Series(dtype=\"object\")"]}, {"cell_type": "code", "execution_count": null, "id": "c99d908b-96c8-4199-b9e4-7e775d5bf7f4", "metadata": {}, "outputs": [], "source": ["daily_forecast_existing_df[\"week_start_date\"] = pd.to_datetime(\n", "    daily_forecast_existing_df[\"week_start_date\"], errors=\"coerce\"\n", ")\n", "\n", "daily_forecast_existing_df[\"date\"] = pd.to_datetime(\n", "    daily_forecast_existing_df[\"date\"], errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "701be195-0abd-44c9-a03d-76aa8d63acc7", "metadata": {}, "outputs": [], "source": ["def row_checksum(row, exclude_cols=[\"updated_ts\"]):\n", "    data = \"\".join(str(row[col]) for col in row.index if col not in exclude_cols)\n", "    return hashlib.sha256(data.encode()).hexdigest()\n", "\n", "\n", "daily_forecast_df[\"checksum\"] = daily_forecast_df.apply(row_checksum, axis=1)\n", "daily_forecast_existing_df[\"checksum\"] = daily_forecast_existing_df.apply(row_checksum, axis=1)\n", "\n", "daily_forecast_existing_df = daily_forecast_existing_df.rename(\n", "    columns={\"updated_ts\": \"updated_ts_old\"}\n", ")\n", "\n", "merged_df = daily_forecast_df.merge(\n", "    daily_forecast_existing_df[\n", "        [\"week_start_date\", \"date\", \"entity_id\", \"checksum\", \"updated_ts_old\"]\n", "    ],\n", "    on=[\"week_start_date\", \"date\", \"entity_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"\", \"_old\"),\n", ")\n", "\n", "merged_df[\"updated_ts\"] = merged_df.apply(\n", "    lambda row: (\n", "        now_ts\n", "        if row[\"checksum\"] != row[\"checksum_old\"] or pd.isna(row[\"checksum_old\"])\n", "        else row[\"updated_ts_old\"]\n", "    ),\n", "    axis=1,\n", ")\n", "\n", "daily_forecast_df = merged_df.drop(columns=[\"checksum\", \"checksum_old\", \"updated_ts_old\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e9c8f3de-667a-470c-b096-01350b0c8c8f", "metadata": {}, "outputs": [], "source": ["daily_forecast_df[daily_forecast_df[\"entity_id\"] == 1024]"]}, {"cell_type": "code", "execution_count": null, "id": "3df8ac71-a8f1-42cd-b044-53a8b488c95c", "metadata": {}, "outputs": [], "source": ["kwargs_trino = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_daily_m2\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Start date of the week (YYYY-MM-DD), week starting from monday\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"date\", \"description\": \"Date for which data is forecasted\"},\n", "        {\"name\": \"entity_id\", \"type\": \"integer\", \"description\": \"Unique ID for the outlet\"},\n", "        {\"name\": \"mandays_fixed\", \"type\": \"integer\", \"description\": \"Mandays required from fixed \"},\n", "        {\"name\": \"manhours_od\", \"type\": \"integer\", \"description\": \"Manhours required from OD\"},\n", "        {\"name\": \"headcount\", \"type\": \"integer\", \"description\": \"Required headcount\"},\n", "        {\"name\": \"week_offs\", \"type\": \"integer\", \"description\": \"week offs for the week\"},\n", "        {\"name\": \"updated_ts\", \"type\": \"varchar\", \"description\": \"Timestamp of last update (IST)\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"entity_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"This table contains headcount distribution at day level.\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "pb.to_trino(daily_forecast_df, **kwargs_trino)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}