
WITH 
abusive_list AS
  (SELECT DISTINCT id AS customer_id
   FROM lake_grofers_db.view_gr_user
   WHERE blocked=TRUE
   UNION SELECT DISTINCT customer_id
   FROM lake_crm.crm_abuse_user
),

suborder_base AS
  (
    SELECT 
        ID,
        ORDER_ID,
        BACKEND_MERCHANT_ID
    FROM 
        lake_oms_bifrost.oms_suborder 
            AS os
        WHERE 
   
             TYPE = 'RetailSuborder'
            AND CURRENT_STATUS IN ('AT_DELIVERY_CENTER')
            AND os.install_ts>=('{start_date}'::TIMESTAMP - INTERVAL '2 DAYS' - INTERVAL '5.5 HOUR')
        GROUP BY 1,2,3
),

delivered AS 
  (select 
  order_checkout_ts_ist,
order_checkout_ts_ist::Date as sched_date,
sco.outlet_id as outlet_id,
co.name as outlet_name,
case when co.location='Ghaziabad' then 'UPNCR'
when co.location='Noida' then 'UPNCR'
when co.location='Meerut' then 'UPNCR'
when co.location='Bangalore' then 'Bengaluru'
when co.location='kanpur' then 'Kanpur'
else trim(co.location)
end as city,
order_id,
cart_id,
picker_id,
partner_id
from 
dwh.fact_supply_chain_order_details  sco join lake_retail.console_outlet co on sco.outlet_id=co.id
where order_checkout_ts_ist::Date between '{start_date}' and '{end_date}'
and order_current_status='DELIVERED'

),

complaints_all AS
(
   (WITH 
  disposition AS
     (select order_checkout_ts_ist AS sched_date_ts,
                                                                                            d.order_id AS order_id,
                                                                                            -- cdm.cart_id,
                                                                                            user_id AS customer_id,
                                                                                            CASE
                                                                                                WHEN combined_disposition IN ('Issue with an order-Delay in Delivery') THEN 'Delay in Delivery'
                                                                                                WHEN combined_disposition IN ('Issue with an order-Item Expired/Near Expiry',
                                                                                                                              'I have received **expired** item(s)') THEN 'Expired Items'
                                                                                                WHEN combined_disposition IN ('Issue with an order-Complaint against delivery partner/agent',
                                                                                                                              'Issue with an order-FE at Fault/Support Ninja at Fault') THEN 'FE related'
                                                                                                WHEN combined_disposition ILIKE '%%I have received damaged or poor quality items%%'
                                                                                                     OR combined_disposition ILIKE '%%Issue with an order-Item/Freebie Damaged%%'
                                                                                                     OR combined_disposition ILIKE '%%Item is defective or doesnt work%%'
                                                                                                     OR combined_disposition ILIKE '%%I have received spilled item(s)%%' THEN 'Item Damaged'
                                                                                                WHEN combined_disposition IN ('Issue with an order-Item/Freebie Missing',
                                                                                                                              'Item(s) are **missing** in the order',
                                                                                                                              'Item or parts are **missing**',
                                                                                                                              'Items are f**missing or incorrect**') THEN 'Item Missing'
                                                                                                WHEN combined_disposition IN ('I have **not received** my order',
                                                                                                                              'Issue with an order-MDND') THEN 'MDND'
                                                                                                WHEN combined_disposition IN ('Issue with an order-Incomplete/Incorrect Product Description/Price') THEN 'Price difference'
                                                                                                WHEN combined_disposition IN ('I have issue with item(s) **smell** or **taste**',
                                                                                                                              'Issue with an order-Foreign Item in Product',
                                                                                                                              'Issue with an order-Quality Issue (apart from bad smell/taste)',
                                                                                                                              'I have received **poor quality** item(s)',
                                                                                                                              'Issue with an order-Bad Smell/Bad Taste') THEN 'Quality not good'
                                                                                                WHEN combined_disposition IN ('I have received an **incorrect** item',
                                                                                                                              'Issue with an order-Wrong Item',
                                                                                                                              'I have received **incorrect** item(s)') THEN 'Wrong item'
                                                                                                WHEN combined_disposition IN ('Agent Inconclusive') THEN 'Agent Inconclusive'
                                                                                            END AS complaint_category,
                                                                                            cdm.order_type,
                                                                                            cdm.group_disposition,
                                                                                            concat(order_id,complaint_category) AS order_category
      FROM consumer.cd_master_table cdm
      JOIN delivered d ON cdm.tab_id=d.order_id
      WHERE sched_date_ts >= ('{start_date}'::date  ) and
      sched_date_ts<= ('{end_date}'::date  ))
      
      SELECT 
      (min(sched_date_ts))::Date AS date_,
        comp.tab_id::int AS order_id,
        comp.user_id::int as customer_id,
         co.name::varchar AS store_name, 
          (trim(co.location))::varchar AS city,
            disp.complaint_category::varchar AS complaint_category,
             count(DISTINCT order_category) AS complaint_count
             
            --  order_category,
            -- comp.cart_id as cart_id,
            --  comp.user_id as customer_id,
            -- comp.group_disposition,
            -- comp.order_type,
           
   FROM consumer.cd_master_table comp
   JOIN delivered d ON d.order_id=comp.tab_id
   JOIN dwh.fact_supply_chain_order_details od ON comp.tab_id=od.order_id
   JOIN lake_retail.console_outlet co ON co.id=od.outlet_id
   JOIN disposition disp ON disp.order_id=comp.tab_id
   WHERE complaint_category IS NOT NULL
     AND order_current_status='DELIVERED'
   GROUP BY 2,
            3,
            4,
            5,6)
            
union all
  (
  WITH delivered_details_log AS
     ( SELECT (ls.install_ts + interval '5.5hour') AS date,
             json_extract_path_text(ls.metadata,'merchant','city_id',TRUE) AS city_id,
             json_extract_path_text(metadata,'merchant','actual_name',TRUE) AS store_name,
             ls.id,
             lsl.label_value::int AS order_id,
             lsls.label_value::int AS sub_order_id,
             json_extract_path_text(ls.metadata,'cart',TRUE) AS cart_id
      FROM lake_logistics.logistics_shipment AS ls
      INNER JOIN lake_logistics.logistics_shipment_label AS lsl ON lsl.shipment_id=ls.id
      AND lsl.label_type='ORDER_ID'
      AND status='DELIVERED'
      INNER JOIN lake_logistics.logistics_shipment_label AS lsls ON lsls.shipment_id=ls.id
      AND lsls.label_type='SUB_ORDER_ID'
      AND status='DELIVERED'
      INNER JOIN lake_logistics.logistics_task lt ON lt.shipment_label_value=lsl.label_value
      AND lt.type='ORDER_DELIVERY_TASK'
      AND state='COMPLETED'
      INNER JOIN lake_logistics.logistics_user_profile AS p ON p.id=lt.user_profile_id
      INNER JOIN lake_logistics.logistics_user AS u ON u.id=p.user_id
      WHERE (ls.install_ts) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}' ::TIMESTAMP - interval'5.5hrs' )
        AND (lsl.install_ts) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}' ::TIMESTAMP - interval'5.5hrs' )
        AND (lsls.install_ts) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}' ::TIMESTAMP - interval'5.5hrs' )
        AND (lt.install_ts) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}' ::TIMESTAMP - interval'5.5hrs' ) 
),      
missing_log AS
     (SELECT  date, store_name,
                            ddl.order_id as order_id,
                            ddl.sub_order_id as sub_order_id,
                            cu.customer_id as customer_id,
                            concat(order_id,reason) AS order_category,
                            reason + '_doorstep' as complaint_category,
                            lsic.install_ts+interval '5.5hrs' AS complaint_time
      FROM delivered_details_log AS ddl
      INNER JOIN lake_logistics.logistics_shipment_item AS lsi ON ddl.id=lsi.shipment_id
      INNER JOIN lake_logistics.logistics_shipment_item_complaint AS lsic ON lsic.shipment_item_id=lsi.id
      join test.complaints_union cu on cu.suborder_id=ddl.sub_order_id
      WHERE reason NOT IN ('Item Missing',
                           '')
        AND (lsi.install_ts ) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}'::TIMESTAMP - interval'5.5hrs' )
        AND (lsic.install_ts) between ('{start_date}' ::TIMESTAMP - interval'5.5hrs' ) and ('{end_date}'::TIMESTAMP - interval'5.5hrs' ) )
        
    SELECT (min(date))::date AS date_,
            --   concat(log.order_id,reason) AS order_category,
               order_id::int,
              customer_id::int,
               store_name::varchar AS store_name,
               (substring(store_name,14,charindex(' ',substring(store_name,15))))::varchar AS city,
               complaint_category::varchar AS doorstep_complaint_category,
               count(distinct order_category) AS complaint_count
   FROM missing_log AS log 
   --where log.order_id=163,796,604
   GROUP BY 2,
            3,
            4,
            5,6)
    )
-----            
SELECT 
        d.sched_date AS order_delivery_date,
        d.order_id,
        d.cart_id,
        pid.product_id,
        pid.product_name,
        pid.l_cat as product_category,
        d.outlet_name,
        d.outlet_id,
        d.city,
        d.picker_id,
        d.partner_id,
cpd.complaint_category,
cpd.complaint_count
  
-- select * from consumer.pid_complaints limit 10

FROM delivered AS d
--  left 
 JOIN complaints_all AS cpd ON cpd.order_id=d.order_id
-- left join  complaints_doorstep AS cd on cd.order_id=d.order_id
 left outer JOIN abusive_list al on cpd.customer_id=al.customer_id 
 and al.customer_id IS NULL
 left join 
    metrics.crm_refund_denied_carts 
        AS crdc 
        on  d.order_id = crdc.order_id 
 join consumer.pid_complaints pid on pid.cart_id=d.cart_id

        
WHERE
d.sched_date >= ('{start_date}'::TIMESTAMP ) and
d.sched_date<= ('{end_date}'::TIMESTAMP)
