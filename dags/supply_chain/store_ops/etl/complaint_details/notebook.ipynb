{"cells": [{"cell_type": "code", "execution_count": null, "id": "61c5fbd3-509e-4bf3-a1ed-ea081add7c85", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "bec149d6-c991-4a68-8f50-67279a5ae916", "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "fcbeb785-57ad-4cdc-a5d6-8a90a8214a79", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "b25d375b-c75f-4eda-971e-00aae8ce204e", "metadata": {}, "outputs": [], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": null, "id": "0f928901-a37b-45a5-90e8-ff43d2be054e", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/etl/complaint_details\""]}, {"cell_type": "code", "execution_count": null, "id": "23ba10e0-d7a9-47d2-8f60-fe8f40116aea", "metadata": {}, "outputs": [], "source": ["# base data\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"Utilisation.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    sql_query = myfile.read()"]}, {"cell_type": "code", "execution_count": null, "id": "55708ccb-8ee8-438c-920d-ce7cec06a274", "metadata": {}, "outputs": [], "source": ["# s_date = (datetime.now() - timedelta(days=5)).strftime(\"%Y-%m-%d\")\n", "# e_date = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "s_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "e_date = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "print(\"s_date:\", s_date)\n", "print(\"e_date:\", e_date)\n", "sql_query = sql_query.format(start_date=s_date, end_date=e_date)\n", "t_data = pd.read_sql(sql=sql_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "id": "915bf53b-3edb-4692-bf80-f6e749233d92", "metadata": {}, "outputs": [], "source": ["# t_data_f = t_data.merge(\n", "#     store_data, left_on=[\"Store_ID\"], right_on=[\"outlet_id\"], how=\"left\"\n", "# )\n", "# t_data_f[\"ownership\"].fillna(\"OWN\", inplace=True)\n", "# t_data_f[\"Score\"] = t_data_f.Score.round(decimals=0)\n", "# t_data_f.drop(columns={\"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fb905094-a65b-42d0-9379-591b46377446", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"complaint_details\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"order_delivery_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Scheduled Order Date\",\n", "        },\n", "        {\"name\": \"Order_ID\", \"type\": \"int\", \"description\": \"Order Id \"},\n", "        {\"name\": \"Cart_ID\", \"type\": \"int\", \"description\": \"Cart Id \"},\n", "        {\"name\": \"Product_ID\", \"type\": \"int\", \"description\": \"Product Id \"},\n", "        {\"name\": \"Product_Name\", \"type\": \"varchar\", \"description\": \"Product Name \"},\n", "        {\n", "            \"name\": \"product_category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Product Category \",\n", "        },\n", "        {\"name\": \"Outlet_Name\", \"type\": \"varchar\", \"description\": \"Outlet Name \"},\n", "        {\"name\": \"Outlet_ID\", \"type\": \"int\", \"description\": \"Outlet Id\"},\n", "        {\"name\": \"City\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\"name\": \"Picker_<PERSON>\", \"type\": \"varchar\", \"description\": \"Picker Id\"},\n", "        {\"name\": \"Partner_<PERSON>\", \"type\": \"varchar\", \"description\": \"Partner Id\"},\n", "        {\n", "            \"name\": \"Complaint_Category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Complaint Category\",\n", "        },\n", "        {\"name\": \"<PERSON><PERSON><PERSON><PERSON>_Count\", \"type\": \"int\", \"description\": \"Complaint Count\"},\n", "    ],\n", "    \"primary_key\": [\"Order_ID\", \"Complaint_Category\"],\n", "    \"sortkey\": [\"order_delivery_date\"],\n", "    \"incremental_key\": \"order_delivery_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Order,PID level Complaints with Rider, Picker Details \",\n", "}\n", "\n", "pb.to_redshift(t_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "149f2d1a-2455-4b11-a870-8699108b453f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}