{"cells": [{"cell_type": "markdown", "id": "5d5b2b60-a707-4bf1-b582-db764b44694f", "metadata": {}, "source": ["## Importing Libraries and Getting Connection"]}, {"cell_type": "code", "execution_count": null, "id": "754dabbd-93f1-4718-af38-2f3a425dfbc5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "import jinja2\n", "from jinja2 import Template\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "a19bb0c2-f744-42b9-bad3-d1e1f5ca8097", "metadata": {}, "outputs": [], "source": ["today = (datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)).date()"]}, {"cell_type": "code", "execution_count": null, "id": "38373333-3292-4ad1-ab5f-fb9e2f4b0f73", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "c97d46b0-1656-48b6-8600-8c5d22f344c1", "metadata": {}, "outputs": [], "source": ["start_date = today - <PERSON><PERSON><PERSON>(days=3)\n", "end_date = today - <PERSON><PERSON><PERSON>(days=0)"]}, {"cell_type": "code", "execution_count": null, "id": "e5153449-3ad3-4c0e-bece-a51f2ab70c8f", "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "id": "9cf05ad4-6f91-42c8-9f8e-6bbe02ef9518", "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "code", "execution_count": null, "id": "7e70e198-f34e-468c-9ef6-3f2e4877144c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1RabCBwV2aIKx6QET1Y-ZqeRXf91cs610KiMi6oBjdNc\""]}, {"cell_type": "code", "execution_count": null, "id": "c6f28585-0f06-4172-a394-ca87889f99d3", "metadata": {}, "outputs": [], "source": ["outlet_data = pb.from_sheets(sheet_id, \"Target_40_Stores\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c3226059-ea44-44ba-ad18-085529a2e238", "metadata": {}, "outputs": [], "source": ["outlet_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a541672d-786b-48c3-abf6-878fd0a10949", "metadata": {}, "outputs": [], "source": ["outlet_list = list(outlet_data[\"Outlet ID\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "9e0facb4-f771-4ece-b660-bb76162d6b4b", "metadata": {}, "outputs": [], "source": ["adherence_q = \"\"\"\n", "select distinct a.*,c.id, c.updated_by as auditer,\n", "(c.started_at+interval '5.5 hours') as task_started_at,\n", "(c.updated_at+interval '5.5 hours') as task_completed_at,\n", "datediff(seconds,c.started_at,(case when c.state=3 then c.updated_at end))/60.0 as task_completion_time_in_min,\n", "start_ims_quantity,end_ims_quantity,start_ils_quantity,end_ils_quantity\n", "from metrics.putaway_item_audit_task_v2 a\n", "inner join (select max(updated_at_ts_ist) as max_updated_at_ts_ist from metrics.putaway_item_audit_task_v2) as b on a.updated_at_ts_ist = b.max_updated_at_ts_ist\n", "left join lake_warehouse_location.audit_task c on c.variant_id = a.variant_id and a.outlet = c.outlet_id\n", "and (c.created_at+interval '5.5 hours') >= current_date-1 and\n", "(c.created_at+interval '5.5 hours') <=(current_date::varchar + ' 09:00:00')\n", "and creation_source=4 and c.state=3\n", "where date(updated_at_ts_ist)=current_date-1 and outlet in {outlet_list}\n", "\n", "\n", "\n", "\"\"\".format(\n", "    outlet_list=tuple(outlet_list)\n", ")\n", "\n", "adherence_data = pd.read_sql(adherence_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "799ddd24-f461-405f-9c3b-3ce43c1a3185", "metadata": {}, "outputs": [], "source": ["adherence_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f109bffc-236c-4f6f-8847-691de25b6a67", "metadata": {}, "outputs": [], "source": ["adherence_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c3cd9ce1-f75d-4c9a-a8c2-97e5f5bf926e", "metadata": {}, "outputs": [], "source": ["adherence_data_total = (\n", "    adherence_data.groupby([\"outlet\"]).agg({\"upc\": \"nunique\"}).reset_index()\n", ")\n", "adherence_data_complete = adherence_data[~adherence_data[\"id\"].isnull()]\n", "adherence_data_pending = adherence_data[\n", "    adherence_data[\"task_completion_time_in_min\"].isnull()\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "460ac19c-13a5-4df4-a4ce-67a91f3a86d3", "metadata": {}, "outputs": [], "source": ["adherence_data_pending.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e1fd68ed-296b-4df5-bb43-0b54e50841b4", "metadata": {}, "outputs": [], "source": ["adherence_data_pending_new = adherence_data_pending[\n", "    [\"outlet\", \"outlet_name\", \"city\", \"item_id\", \"upc\", \"variant_id\", \"item_name\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "29679a22-b550-4a94-a99f-56976c7642f1", "metadata": {}, "outputs": [], "source": ["adherence_data_pending_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "22811eff-804e-420a-a6bb-9dda8bd81af7", "metadata": {}, "outputs": [], "source": ["adherence_data_complete[\"ils_ims_diff\"] = (\n", "    adherence_data_complete[\"end_ils_quantity\"]\n", "    - adherence_data_complete[\"end_ims_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bcda188a-eefa-41d6-aba1-9aa3e6cd6a93", "metadata": {}, "outputs": [], "source": ["adherence_data_ils_ims_diff = adherence_data_complete[\n", "    abs(adherence_data_complete[\"ils_ims_diff\"]) > 0\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d51f949a-a3f2-4e10-83a4-66efb33509e5", "metadata": {}, "outputs": [], "source": ["adherence_data_complete.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2810b8ea-cf69-40de-8efa-36e1372c1c80", "metadata": {}, "outputs": [], "source": ["adherence_data_ils_ims_diff_new = adherence_data_ils_ims_diff[\n", "    [\n", "        \"outlet\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"item_id\",\n", "        \"upc\",\n", "        \"item_name\",\n", "        \"start_ims_quantity\",\n", "        \"end_ims_quantity\",\n", "        \"start_ils_quantity\",\n", "        \"end_ils_quantity\",\n", "        \"ils_ims_diff\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "98401e58-e89b-44d1-84b1-ac1bd2b5e4cd", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    adherence_data,\n", "    \"15IqNtr9Lg_jiExf7bytvIoBWipBEbm8Vz-FRndVgn8c\",\n", "    \"Total\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    adherence_data_complete,\n", "    \"15IqNtr9Lg_jiExf7bytvIoBWipBEbm8Vz-FRndVgn8c\",\n", "    \"Completed\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    adherence_data_ils_ims_diff_new,\n", "    \"15IqNtr9Lg_jiExf7bytvIoBWipBEbm8Vz-FRndVgn8c\",\n", "    \"ILS/IMS Diff\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    adherence_data_pending_new,\n", "    \"15IqNtr9Lg_jiExf7bytvIoBWipBEbm8Vz-FRndVgn8c\",\n", "    \"Pending Tasks\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2b747a0d-b896-49d1-92f0-277f1145e086", "metadata": {}, "outputs": [], "source": ["adherence_data_complete_total = (\n", "    adherence_data_complete.groupby([\"outlet\"]).agg({\"upc\": \"nunique\"}).reset_index()\n", ")\n", "adherence_data_ils_ims_diff_total = (\n", "    adherence_data_ils_ims_diff.groupby([\"outlet\"])\n", "    .agg({\"upc\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "adherence_data_pending_new_total = (\n", "    adherence_data_pending_new.groupby([\"outlet\"]).agg({\"upc\": \"nunique\"}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abc3ab3a-eec1-4dab-b05f-65d91ca4c4f9", "metadata": {}, "outputs": [], "source": ["adherence_data_complete_total = adherence_data_complete_total.rename(\n", "    columns={\"upc\": \"complete_upc\"}\n", ")\n", "adherence_data_total = adherence_data_total.rename(columns={\"upc\": \"total_upc\"})\n", "adherence_data_ils_ims_diff_total = adherence_data_ils_ims_diff_total.rename(\n", "    columns={\"upc\": \"diff_upc\"}\n", ")\n", "adherence_data_pending_new_total = adherence_data_pending_new_total.rename(\n", "    columns={\"upc\": \"pending_upc\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5d20c4aa-773e-45a5-851c-d5004265e82a", "metadata": {}, "outputs": [], "source": ["adherence_data_complete_dict = adherence_data_complete_total.set_index(\n", "    \"outlet\"\n", ").T.to_dict(\"list\")\n", "adherence_data_total_dict = adherence_data_total.set_index(\"outlet\").T.to_dict(\"list\")\n", "adherence_data_ils_ims_diff_dict = adherence_data_ils_ims_diff_total.set_index(\n", "    \"outlet\"\n", ").T.to_dict(\"list\")\n", "adherence_data_pending_new_dict = adherence_data_pending_new_total.set_index(\n", "    \"outlet\"\n", ").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "c00806eb-568c-4452-b6bb-09042fa4b8eb", "metadata": {}, "outputs": [], "source": ["outlet_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bd02afa5-2d50-44fc-92de-aaa78d7b350e", "metadata": {}, "outputs": [], "source": ["outlet_dict = dict(\n", "    zip(outlet_data[\"Outlet ID\"], outlet_data[\">30 sec pick time per item store list\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09cd272b-e5b7-4e0c-addb-4eeba879d69a", "metadata": {}, "outputs": [], "source": ["mailing_list = (\n", "    outlet_data[\n", "        [\n", "            \"Outlet ID\",\n", "            \"SM\",\n", "            \"CM\",\n", "            \"Ops head\",\n", "            \"Additonal1\",\n", "            \"Additonal2\",\n", "            \"Additonal3\",\n", "            \"Additonal4\",\n", "        ]\n", "    ]\n", "    .set_index(\"Outlet ID\")\n", "    .T.to_dict(\"list\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3724e137-4e58-4d75-b311-d5cbb7a65dba", "metadata": {}, "outputs": [], "source": ["files_to_attach = [\"ils_ims_diff.csv\", \"pending_tasks.csv\"]"]}, {"cell_type": "code", "execution_count": null, "id": "39f07102-2652-4615-b7fc-ed79b1238d8f", "metadata": {}, "outputs": [], "source": ["adherence_data_pending_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aee812d8-250e-4512-839b-5005511bbf23", "metadata": {}, "outputs": [], "source": ["len(outlet_list)"]}, {"cell_type": "code", "execution_count": null, "id": "26836510-8e08-42a6-9fde-ad7d00e74ed9", "metadata": {}, "outputs": [], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": null, "id": "21190a52-7aed-427c-a596-807b3d2eee18", "metadata": {}, "outputs": [], "source": ["mail_triggered_to = []\n", "count = 0\n", "# try:\n", "for i in outlet_list:\n", "\n", "    adherence_data_ils_ims_diff_new[\n", "        adherence_data_ils_ims_diff_new[\"outlet\"] == int(i)\n", "    ].to_csv(\"ils_ims_diff.csv\", index=False)\n", "    adherence_data_pending_new[adherence_data_pending_new[\"outlet\"] == int(i)].to_csv(\n", "        \"pending_tasks.csv\", index=False\n", "    )\n", "    files_to_attach = [\"ils_ims_diff.csv\", \"pending_tasks.csv\"]\n", "    if (\n", "        adherence_data_ils_ims_diff_new.shape[0] != 0\n", "        and adherence_data_pending_new[\n", "            adherence_data_pending_new[\"outlet\"] == int(i)\n", "        ].shape[0]\n", "        != 0\n", "    ):\n", "        files_to_attach = [\"ils_ims_diff.csv\", \"pending_tasks.csv\"]\n", "    elif (\n", "        adherence_data_ils_ims_diff_new.shape[0] != 0\n", "        and adherence_data_pending_new[\n", "            adherence_data_pending_new[\"outlet\"] == int(i)\n", "        ].shape[0]\n", "        == 0\n", "    ):\n", "        files_to_attach = [\"ils_ims_diff.csv\"]\n", "    elif (\n", "        adherence_data_ils_ims_diff_new.shape[0] == 0\n", "        and adherence_data_pending_new[\n", "            adherence_data_pending_new[\"outlet\"] == int(i)\n", "        ].shape[0]\n", "        != 0\n", "    ):\n", "        files_to_attach = [\"pending_tasks.csv\"]\n", "    else:\n", "        files_to_attach = []\n", "    if adherence_data_total[adherence_data_total[\"outlet\"] == int(i)].shape[0] == 0:\n", "        continue\n", "    loader = jinja2.FileSystemLoader(\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/putaway_audit_adherence\"\n", "        # \"/home/<USER>/Development/grofers/airflow-dags/daggers/supply_chain/store_ops/report/putaway_audit_adherence\"\n", "        # \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/putaway_audit_adherence\"\n", "        # \"/shared/Shared/<PERSON><PERSON>hat <PERSON>/Analysis\"\n", "        # \"/home/<USER>/Development/grofers/airflow-dags/daggers/supply_chain/store_ops/report/putaway_audits\"\n", "        # \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/putaway_audits\"\n", "        # \"/home/<USER>/Development/grofers/airflow-dags/daggers/supply_chain/store_ops/report/putaway_audits\"\n", "        # \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/putaway_audits\"\n", "        # \"/shared/Shared/<PERSON><PERSON>hat <PERSON>/Analysis\"\n", "        # \"/home/<USER>/Development/merchant_payout_mailer\"\n", "        #         \"/usr/local/airflow/dags/repo/dags/supply_chain/merchant_sla/report/merchant_sla\"\n", "        #         \"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/merchant/etl/merchant_payout_mailer\"\n", "    )\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "    template = tmpl_environ.get_template(\"email_template.html\")\n", "    try:\n", "        if adherence_data_ils_ims_diff_dict[int(i)][0] == 0:\n", "            ils_ims_count1 = 0\n", "        else:\n", "            ils_ims_count1 = adherence_data_ils_ims_diff_dict[int(i)][0]\n", "    except:\n", "        ils_ims_count1 = 0\n", "    try:\n", "        if adherence_data_pending_new_dict[int(i)][0] == 0:\n", "            pending1 = 0\n", "        else:\n", "            pending1 = adherence_data_pending_new_dict[int(i)][0]\n", "    except:\n", "        pending1 = 0\n", "\n", "    try:\n", "        if adherence_data_complete_dict[int(i)][0] == 0:\n", "            complete1 = 0\n", "        else:\n", "            complete1 = adherence_data_complete_dict[int(i)][0]\n", "    except:\n", "        complete1 = 0\n", "\n", "    message_text = template.render(\n", "        Outlet_Name=outlet_dict[i],\n", "        yesterday=(today - <PERSON><PERSON><PERSON>(days=1)).strftime(\"%d-%b-%y\"),\n", "        total=adherence_data_total_dict[int(i)][0],\n", "        done=complete1,\n", "        ils_ims_count=ils_ims_count1,\n", "        pending=pending1,\n", "    )\n", "    subject = \"Audit Adherence Report | \" + outlet_dict[i]\n", "\n", "    pb.send_email(\n", "        from_email=\"<EMAIL>\",\n", "        to_email=mailing_list[i],\n", "        # [\"<EMAIL>\"],\n", "        # mailing_list[i],\n", "        # [\"<EMAIL>\"],\n", "        # mailing_list[i],\n", "        # mailing_list[i],\n", "        subject=subject,\n", "        html_content=message_text,\n", "        files=files_to_attach,\n", "        bcc=[\"<EMAIL>\"],\n", "        mime_subtype=\"mixed\",\n", "        mime_charset=\"utf-8\",\n", "    )\n", "    time.sleep(2)\n", "    mail_triggered_to.append(outlet_dict[i])\n", "    count += 1\n", "# except:\n", "#     pass"]}, {"cell_type": "code", "execution_count": null, "id": "1ad02481-cba5-44cf-a730-8bbae626676c", "metadata": {}, "outputs": [], "source": ["count"]}, {"cell_type": "code", "execution_count": null, "id": "39fc061a-394d-49dc-b052-ccc8ba6bdfdb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}