{"cells": [{"cell_type": "code", "execution_count": null, "id": "ab569328-813b-4aa3-933d-f8f5ab357214", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import calendar\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "9cc8701e-d30a-40c2-80ae-9e9e3f84ff9a", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "bb4d9b8d-57df-4e33-831c-3f85df17f9bc", "metadata": {}, "outputs": [], "source": ["outlet_map = pb.from_sheets(\n", "    \"1hEB1IWUiC1CdGUnDRNrq1iyp9269Y2sVorEbMk-yW3c\", \"Store Mapping\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61271665-cc6c-4e97-900d-373b48fedd28", "metadata": {}, "outputs": [], "source": ["outlet_map.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2b6e1b0d-74c1-4e1e-8583-06ef2e9e9811", "metadata": {}, "outputs": [], "source": ["outlet_map[\"Outlet ID\"] = outlet_map[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "eb5b670d-3886-4b89-9b22-9a141b0d95b2", "metadata": {}, "outputs": [], "source": ["region_dict = outlet_map.groupby(\"Region\")[\"Outlet ID\"].apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "f9082a8f-8ab7-431d-880f-0e994a0df10b", "metadata": {}, "outputs": [], "source": ["region_dict_new = region_dict.set_index(\"Region\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "b92637bd-f326-4463-a1c0-331cf72c49cc", "metadata": {}, "outputs": [], "source": ["sheet_mapping = pb.from_sheets(\n", "    \"1hEB1IWUiC1CdGUnDRNrq1iyp9269Y2sVorEbMk-yW3c\", \"Sheet4\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0b710cc6-9470-4569-b514-46acd93c3ea7", "metadata": {}, "outputs": [], "source": ["sheet_dict = sheet_mapping.set_index(\"Region\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "65096f0f-c151-4ab5-8cac-a9ce5b29fffb", "metadata": {}, "outputs": [], "source": ["# region_dict_new['Ahmedabad'][0]"]}, {"cell_type": "markdown", "id": "0c6f8ac7-bd29-4bf2-bbc6-c1511d97e518", "metadata": {}, "source": ["## Picker Metrics"]}, {"cell_type": "code", "execution_count": null, "id": "155a5c0c-b28a-432c-ab90-aa0983e987bf", "metadata": {}, "outputs": [], "source": ["first = \"\"\"\n", "\n", "with base as (select distinct store_id,employee_id,total_app_time,total_orders_picked,total_picked_quantity,score from metrics.instore_manpower_performance\n", "    where --role in ( 'Picker', 'Captain') and\n", " date = current_date-1\n", "and total_picked_quantity>0 ),\n", "\n", "base1 as\n", "(SELECT date(a.delivery_date+interval'5.5 hour'),\n", "       oo.picker_id,\n", "       a.outlet_id,\n", "       a.outlet_name,\n", "       count(distinct a.cart_id) as total_orders_with_complaints\n", "FROM metrics.complaints a\n", "Left Join dwh.fact_supply_chain_order_details AS oo ON oo.order_id=a.order_id\n", "WHERE (date(a.delivery_date+interval'5.5 hour') = current_date-1) \n", "--AND a.complaint_category in ('ITEM_MISSING_LM','DIFFERENT_ITEMS_RECEIVED','WRONG_ITEMS','ITEMS_MISSING')\n", "GROUP BY 1,2,3,4),\n", "base2 as\n", "(select picker_id,\n", "       outlet_id,\n", "       c.name as outlet_name,\n", "      (sum(DATEDIFF(seconds,order_picker_assigned_ts_ist,order_billing_completed_ts_ist)))/60.0/count(distinct suborder_id) as \"Avg Picking Time(in mins)\"\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id\n", "WHERE\n", "  date(order_checkout_ts_ist)= current_date-1\n", "and is_rescheduled=false\n", "and order_picking_completed_ts_ist is not null and\n", "order_type='super_express' \n", "  group by 1,2,3)\n", "  select base.store_id as store_id,\n", "  base.employee_id as employee_id,\n", "         b.first_name as employee_name,\n", "         base.total_app_time as total_app_time,\n", "         base.total_orders_picked as total_orders_picked,\n", "         base.total_picked_quantity as total_picked_quantity,\n", "         base1.total_orders_with_complaints as total_orders_with_complaints,\n", "         base2.\"avg picking time(in mins)\",\n", "         base.score as score,\n", "         base2.outlet_name as outlet_name\n", "    from base2\n", "    inner join base on base2.picker_id = base.employee_id\n", "    left join base1 on base1.picker_id = base2.picker_id\n", "    left join lake_storeops.user_details b on base.employee_id = b.employee_id\n", "   \n", "    group by 1,2,3,4,5,6,7,8,9,10\n", "    \n", "    \n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "228a9c80-75fe-4f5a-9f22-5fc31dc7d21a", "metadata": {}, "outputs": [], "source": ["picker_data = pd.read_sql(first, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "0b8a7c55-0d63-4f5a-922a-2f65bb3bf72b", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        picker_data[picker_data[\"store_id\"].isin(region_dict_new[i][0])][\n", "            [\n", "                \"employee_id\",\n", "                \"employee_name\",\n", "                \"total_app_time\",\n", "                \"total_orders_picked\",\n", "                \"total_picked_quantity\",\n", "                \"total_orders_with_complaints\",\n", "                \"avg picking time(in mins)\",\n", "                \"score\",\n", "                \"outlet_name\",\n", "            ]\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"picker\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "03a3c1c4-900f-4c78-88e6-627f9d8ba24d", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     picker_data,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"picker\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "6caaba0c-23d9-44fa-bece-b4e6b50e806a", "metadata": {}, "outputs": [], "source": ["picker_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "81d75713-225b-4938-b1a5-48f0a4d3993e", "metadata": {}, "outputs": [], "source": ["second = \"\"\"with base as (select distinct store_id,employee_id,\n", "                     employee_name,\n", "                     store_name,\n", "                total_app_time,\n", "       total_putaway_quantity\n", "from metrics.instore_manpower_performance\n", "where date = current_date-1\n", "--and employee_id = 'GCE122335'\n", "and total_putaway_quantity>0\n", "),\n", "\n", "base1 as (\n", "(SELECT a.site_id::int as outlet_id,\n", "a.last_modified_by as employee_id,\n", "       b.name as employee_name,\n", "       d.name as outlet_name,\n", "       count(distinct case when a.status='COMPLETED' then a.entity_id end) as total_audits\n", "FROM lake_storeops.activity a\n", "inner join lake_retail.warehouse_user b on a.last_modified_by = b.emp_id\n", "inner join lake_retail.console_outlet d on d.id = cast(a.site_id as int)\n", "where (date(created_date) = current_date- 1 and type = 'INV_AUDIT')\n", "group by 1,2,3,4)\n", "\n", "union all\n", "(select  a.outlet_id,\n", "a.updated_by as employee_id,\n", "         b.name as employee_name,\n", "         d.name as outlet_name,\n", "        count(distinct case when a.state = 3 then a.id end) as total_audits\n", "from lake_warehouse_location.audit_task a\n", "inner join lake_retail.warehouse_user b on a.updated_by = b.emp_id\n", "inner join lake_retail.console_outlet d on d.id = a.outlet_id\n", "where --emp_\n", "       date(a.created_at) = current_date - 1\n", "      group by 1,2,3,4)),\n", "      \n", "base2 as\n", "      ((select employee_id,\n", "        outlet_name,\n", "       (sum(delay_mins)) as TAT_FROM_GRN_in_min\n", "FROM\n", "(select date(created_at+interval '5.5 hours') as date,\n", "outlet_id,outlet_name,\n", "employee_id,\n", "putlist_id,\n", "(sum(datediff(seconds,created_at,updated_at))*1.0/60)/(count(distinct putlist_id)) as delay_mins\n", "from\n", "(\n", "SELECT\n", "put_list.outlet_id,outlet.name as outlet_name,\n", "put_list.id as putlist_id,\n", "put_list.assigned_to_name as employee_id,\n", "put_list.created_at,\n", "---put_list.updated_at as put_list_completed,\n", "min(put_list_item.updated_at) as updated_at\n", "--(lag(put_list_item.updated_at,1) over (partition by put_list.outlet_id,put_list.id order by put_list_item.updated_at)) as updated_at_lag\n", "FROM lake_warehouse_location.warehouse_put_list put_list\n", "INNER JOIN lake_retail.console_outlet outlet ON outlet.id=put_list.outlet_id\n", "INNER JOIN lake_retail.console_location l ON l.id=outlet.tax_location_id\n", "INNER JOIN lake_warehouse_location.warehouse_put_list_item put_list_item ON put_list_item.putlist_id=put_list.id\n", "---inner join lake_rpc.product_product pp on pp.item_id = put_list_item.item_id\n", "where put_list.current_state_id=4 and business_type_id=7 and\n", "date(put_list.created_at+interval '5.5 hours') = current_date-1\n", "--and put_list.id = 10964709\t\n", "group by 1,2,3,4,5\n", "having sum(quantity)>0) as a\n", "--where updated_at_lag is null\n", "group by 1,2,3,4,5\n", "--having employee_id = 'GR7731'\n", ")\n", "group by 1,2)\n", "\n", "union all\n", "\n", "(select a.assigned_to_employee_id as employee_id,\n", "b.name as outlet_name,\n", "sum(datediff('minute',a.created_date,a.start_time)) as TAT_FROM_GRN_in_min\n", "from lake_storeops.activity a\n", "inner join lake_retail.console_outlet b on b.id =cast(a.site_id as int)\n", "where a.type = 'PUTAWAY' and a.status = 'COMPLETED'\n", "and date(a.created_date+interval '5.5 hours') = current_date-1 and business_type_id=7\n", "group by 1,2))\n", "\n", "      select base.store_id as outlet_id,\n", "      base.employee_id,\n", "             base.employee_name,\n", "            base.total_app_time,\n", "            COALESCE(base.total_putaway_quantity,0) as total_putaway_quantity,\n", "            COALESCE(base1.total_audits,0) as total_audits,\n", "            COALESCE(base2.TAT_FROM_GRN_in_min,0) as TAT_FROM_GRN_in_min,\n", "            base.store_name\n", "        from base\n", "       left join base1 on base.employee_id = base1.employee_id\n", "        inner join base2 on base2.employee_id = base.employee_id\n", "        group by 1,2,3,4,5,6,7,8\n", "        \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c76d8688-3fd0-438e-bbb7-e1dbbd8fca55", "metadata": {}, "outputs": [], "source": ["putter_data = pd.read_sql(second, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "4ff4f9d3-039a-425d-81d0-c65d77e82b76", "metadata": {}, "outputs": [], "source": ["putter_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "4c2a5533-1835-45d6-bc04-dafe7a7f0c4a", "metadata": {}, "outputs": [], "source": ["putter_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a0b6385b-0b21-4c77-9111-82e513792f91", "metadata": {}, "outputs": [], "source": ["putter_data[putter_data[\"outlet_id\"].isin(region_dict_new[\"Hyderabad\"][0])]"]}, {"cell_type": "code", "execution_count": null, "id": "1666de32-6376-42da-85c1-73e4ff8e2769", "metadata": {}, "outputs": [], "source": ["putter_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "4966da3a-765f-4813-89fc-7536aa7dd3c8", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        putter_data[putter_data[\"outlet_id\"].isin(region_dict_new[i][0])][\n", "            [\n", "                \"employee_id\",\n", "                \"employee_name\",\n", "                \"total_app_time\",\n", "                \"total_putaway_quantity\",\n", "                \"total_audits\",\n", "                \"tat_from_grn_in_min\",\n", "                \"store_name\",\n", "            ]\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"putter\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "b7478ecb-e7d1-4a18-9111-ea4dcdb61f5b", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     putter_data,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"putter\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "f7fdd795-bc36-4ec0-8c9a-f5590e40c1f0", "metadata": {}, "outputs": [], "source": ["putter_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5f7c5f71-bbd9-4159-90a8-7b11f3422d9e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5a5817ae-0e27-4c5d-84f6-5341228d8ab0", "metadata": {}, "outputs": [], "source": ["third = \"\"\"select  x.date,\n", "       sum(y.inwarded_quantity) as total_grn_qty,\n", "        sum(case when datediff('minute',(vehicle_arrival_time) ,y.putaway_time)<=240 then y.inwarded_quantity end) as total_put_away_qty_with_in_four_hours,\n", "        x.receiver_outlet_name as outlet_name,\n", "        x.outlet_id\n", "from\n", "(SELECT   mr.indent,\n", "          a.receiving_outlet_id as outlet_id,\n", "          a.receiver_outlet_name,\n", "          date(a.grn_started_at) as date,\n", "          coalesce(min(a.consignment_received_at),min(a.grn_started_at)) as vehicle_arrival_time\n", "   FROM metrics.esto_details a\n", "  left join metrics.multiRep_data mr on mr.bulk_sto_id = a.bulk_sto_id  and  date(mr.grn_created_at) >= (CURRENT_DATE -15)\n", "   --AND mr.sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP\n", "   left join lake_ims.ims_inward_invoice bb on a.invoice_id = bb.vendor_invoice_id and bb.created_at >= (CURRENT_DATE -15)::TIMESTAMP\n", "    left join lake_warehouse_location.warehouse_put_list cc on cc.grn_id = bb.grn_id and cc.created_at >= (CURRENT_DATE -15)::TIMESTAMP\n", "    left join lake_warehouse_location.warehouse_put_list_item d on d.putlist_id=cc.id and a.item_id = d.item_id and d.created_at >= (CURRENT_DATE -15)::TIMESTAMP\n", "    where (date(a.grn_started_at) between current_date-15 and current_date-1)\n", "    and a.sto_state = 'Inward' and a.invoice_state in ('GRN Partial','GRN Complete')\n", "    and a.ars_mode='normal'\n", "    --and  a.receiver_outlet_name = 'Super Store Delhi Malviya Nagar ES38 (FNV)'\n", "  group by 1,2,3,4\n", "  ) x\n", "  inner join\n", "  (\n", "  select  a.sto_id,\n", "          a.receiving_outlet_id as outlet_id,\n", "          date(a.grn_started_at) as date,\n", "          mr.indent,\n", "          a.item_id,\n", "          a.receiving_outlet_id,\n", "          inwarded_quantity,\n", "          coalesce(a.putaway_time,d.completion_time) as putaway_time\n", "  FROM metrics.esto_details a\n", "  left join metrics.multiRep_data mr on mr.bulk_sto_id = a.bulk_sto_id  and  date(mr.grn_created_at) >= (CURRENT_DATE -15)\n", "   left join lake_storeops.activity cc on cc.entity_id = a.invoice_id and date(cc.created_date) >= (CURRENT_DATE -15)\n", "  left join lake_storeops.item_activity d on d.activity_id=cc.id and a.item_id = d.item_id and date(d.created_date) >= (CURRENT_DATE -15)\n", "  where (date(a.grn_started_at) between current_date-15 and current_date)\n", "    and a.sto_state = 'Inward' and a.invoice_state in ('GRN Partial','GRN Complete')\n", "    and a.ars_mode='normal'\n", "    group by 1,2,3,4,5,6,7,8\n", "    ) y on x.indent = y.indent and x.outlet_id = y.outlet_id and x.date = y.date\n", "    group by 1,4,5\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5c0020e0-b479-4b46-87f6-c93184cabab1", "metadata": {}, "outputs": [], "source": ["sto = pd.read_sql(third, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "010071ad-a927-4df0-a7ee-5ed7b39bb4fd", "metadata": {}, "outputs": [], "source": ["sto.head()"]}, {"cell_type": "code", "execution_count": null, "id": "be62fce7-8641-4c85-8b18-5e98f2d8b8cf", "metadata": {}, "outputs": [], "source": ["sto.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b69ab3b0-ab77-4edf-8bb0-94bf7497f2cd", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        sto[sto[\"outlet_id\"].isin(region_dict_new[i][0])][\n", "            [\n", "                \"date\",\n", "                \"total_grn_qty\",\n", "                \"total_put_away_qty_with_in_four_hours\",\n", "                \"outlet_name\",\n", "            ]\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"STO\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "089f1e97-a4e2-4d8d-b0b8-86cee63a8dea", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     sto,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"STO\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "f3e2e0f1-5a35-4837-9781-0e320486525d", "metadata": {}, "outputs": [], "source": ["fourth = \"\"\"\n", "with alpha as\n", "(\n", "select\n", "    (delivery_timestamp+interval '5.5 hours')::date as delivery_date,\n", "    delivery_fe_id,\n", "    order_id,\n", "    (delivery_timestamp+interval '5.5 hours') as delivery_ts_ist,\n", "    (leafeq.entry_ts+interval '5.5 hours') as entry_ts_ist,\n", "     datediff('minutes', delivery_ts_ist, entry_ts_ist) as time_taken_to_return\n", "from\n", "    lake_logistics.view_logistics_dark_store_projection ldsp\n", "inner join\n", "    lake_logistics.logistics_user_profile lup\n", "    on lup.employee_id=ldsp.delivery_fe_id\n", "left join\n", "    lake_logistics.logistics_express_allocation_field_executive_queue leafeq\n", "    on leafeq.user_profile_id=lup.id\n", "    and (leafeq.entry_ts+interval '5.5 hours')::date= (delivery_timestamp+interval '5.5 hours')::date\n", "    and leafeq.entry_ts>ldsp.delivery_timestamp\n", "where\n", "    ldsp.current_state='DELIVERED'\n", "    and ldsp.delivery_timestamp>=(current_date-1)::date-interval '5.5 hours'\n", "    and leafeq.entry_ts>=(current_date-1)::date-interval '5.5 hours'\n", "--    and ldsp.delivery_fe_id='LG88669'\n", "order by 2,3,4\n", "),\n", "ranked as\n", "(\n", "select\n", "    *,\n", "    row_number() over(partition by delivery_date, delivery_fe_id, order_id order by entry_ts_ist) as rnum\n", "from\n", "    alpha\n", "),\n", "final_base as\n", "(\n", "select\n", "    *\n", "from\n", "    ranked\n", "where rnum=1\n", "),\n", "data as\n", "(\n", "select\n", "    delivery_date,\n", "    delivery_fe_id,\n", "    count(distinct order_id) + 1 as tot_orders ,\n", "    avg(time_taken_to_return::float) as avg_time_taken_to_return\n", "from\n", "    final_base\n", "    where date(delivery_date) = current_date-1\n", "group by 1,2),\n", "base1 as\n", "(SELECT date(a.delivery_date),\n", "       oo.partner_id,\n", "       b.pos_outlet_id,\n", "       b.pos_outlet_name,\n", "       count(distinct a.cart_id) as total_orders_with_complaints\n", "FROM metrics.complaints a\n", "inner join dwh.dim_merchant_outlet_facility_mapping b on b.frontend_merchant_id = a.frontend_merchant_id and b.is_current = true\n", "Left Join dwh.fact_supply_chain_order_details AS oo ON oo.order_id=a.order_id\n", "WHERE (date(a.delivery_date+interval'5.5 hour') = current_date-1)\n", "GROUP BY 1,2,3,4),\n", "base2 as\n", "(select partner_id,\n", "       outlet_id,\n", "       c.name as outlet_name,\n", "       count(distinct order_id) as total_orders_delivered,\n", "      (sum(DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist)))/60.0/count(distinct suborder_id) as \"Avg delivery Time(in mins)\"\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id\n", "WHERE\n", "  date(order_checkout_ts_ist+interval'5.5 hour')= current_date-1\n", "and is_rescheduled=false\n", "and order_picking_completed_ts_ist is not null and\n", "order_type='super_express'\n", "  group by 1,2,3),\n", "  base3 as\n", "  (select partner_id,outlet_id,outlet_name,\n", "count(distinct case when (delivery_time/60.0)<=est_travel_duration_mins then order_id end) as orders_delivered_under_eta\n", "from\n", "(SELECT outlet_id,m.name as outlet_name,\n", "       date(order_checkout_ts_ist) as date,\n", "       order_id,\n", "       partner_id,\n", "    --   case when\n", "       DATEDIFF(seconds,order_enroute_ts_ist,order_delivered_ts_ist) as delivery_time,\n", "       est_travel_duration_mins\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "WHERE date(order_checkout_ts_ist) = current_date - 1\n", "  AND is_order_delivered = true and est_field_executive_eta_mins is not null\n", "  and business_type_id=7\n", "  group by 1,2,3,4,5,6,7)\n", "  group by 1,2,3)\n", "  select base2.partner_id,\n", "         k.name as employee_name,\n", "         --base.total_app_time,\n", "         COALESCE(base2.total_orders_delivered,0) as total_orders_delivered,\n", "         COALESCE(base1.total_orders_with_complaints,0) as total_orders_with_complaints,\n", "         --COALESCE(base2.\"Avg delivery Time(in mins)\",0),\n", "         data.avg_time_taken_to_return,\n", "         base3.orders_delivered_under_eta,\n", "         base2.outlet_name,\n", "         base2.outlet_id\n", "    from base2\n", "    left join base1 on base2.partner_id = base1.partner_id\n", "    inner join data on base2.partner_id = data.delivery_fe_id\n", "    left join base3 on base3.partner_id = base2.partner_id\n", "    left join lake_logistics.logistics_user_profile f on f.employee_id = base2.partner_id\n", "    left join lake_logistics.logistics_user k on k.id = f.user_id\n", "    group by 1,2,3,4,5,6,7,8\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b08115b0-700f-409f-bdfc-00145810ab05", "metadata": {}, "outputs": [], "source": ["rider_data = pd.read_sql(fourth, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "4722b566-c1ec-4ca9-99b3-635c1089b368", "metadata": {}, "outputs": [], "source": ["rider_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a5ba5d3b-a4f6-4096-ba8e-01008ae7c30a", "metadata": {}, "outputs": [], "source": ["rider_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "de5d3240-274a-4c68-999d-bd1b064c4fbe", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        rider_data[rider_data[\"outlet_id\"].isin(region_dict_new[i][0])][\n", "            [\n", "                \"partner_id\",\n", "                \"employee_name\",\n", "                \"total_orders_delivered\",\n", "                \"total_orders_with_complaints\",\n", "                \"avg_time_taken_to_return\",\n", "                \"orders_delivered_under_eta\",\n", "                \"outlet_name\",\n", "            ]\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"rider\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "92839d60-b8b7-4c13-ae86-ccc29ea52f58", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     rider_data,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"rider\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "3c162a3d-2a66-4184-ad24-21eccb303407", "metadata": {}, "outputs": [], "source": ["rider_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a9e4c84e-64a8-40ed-8f9d-f563f1baf7da", "metadata": {}, "outputs": [], "source": ["seventh = \"\"\"\n", "\n", "with order_scanned as (\n", "SELECT \n", "    external_order_id,\n", "    max(event_time) as scan_start_ist\n", "    ---(timestamp 'epoch' + (scan_start_timestamp/1000 * INTERVAL '1 second') + interval '5.5 hour') AS scan_start_ist,\n", "    ---row_number() OVER(PARTITION BY external_order_id ORDER BY scan_start_timestamp desc) as rn\n", "FROM \n", "    metrics.blinkit_z_order_scan_event\n", "WHERE \n", "    event_time>= current_date-7\n", "    group by 1\n", "),\n", "\n", "lost_order as\n", "(select \n", "z.at_date_ist as date_l,\n", "       --z.merchant_id,\n", "       x.outlet_id,\n", "       sum(z.lost_orders) as total_lost_orders\n", "from viz.view_agg_daily_lost_orders z\n", "inner join (select frontend_merchant_id,outlet_id,count(distinct order_id) as total_orders from dwh.fact_sales_order_details \n", "    where date(cart_checkout_ts_ist) = current_date-1\n", "    --and frontend_merchant_id = 31661\n", "    and outlet_id is not null\n", "    group by 1,2\n", "    having (count(distinct order_id))>75) x on x.frontend_merchant_id = z.merchant_id\n", "-- inner JOIN dwh.dim_merchant_outlet_facility_mapping AS dm ON z.merchant_id = dm.frontend_merchant_id\n", "--   where dm.is_current in (True,False)\n", "--          and dm.is_mapping_enabled = True\n", "--          and dm.is_frontend_merchant_active in (True,False)\n", "--          and merchant_business_type_id=7\n", "--          and z.at_date_ist = current_date-1\n", "    where (z.at_date_ist>= current_date-7)\n", "    group by 1,2),\n", "    \n", "surge_order as\n", "(select \n", "date(o.order_checkout_ts_ist) as date_s,\n", "       o.outlet_id as outlet_id,\n", "      count(distinct case when slot_charges>0 then x.order_id end) as orders_with_surge\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join dwh.fact_sales_order_details x on x.order_id = o.order_id\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id and business_type_id = 7\n", "WHERE \n", "  (date(o.order_checkout_ts_ist) >=current_date-7)\n", "and is_rescheduled=false \n", "and order_picking_completed_ts_ist is not null and \n", "o.order_type='super_express'\n", "and o.outlet_id not in (3950,3658,1851,2574,3945,1473,3230)\n", "  group by 1,2),\n", "\n", "base as\n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        total_lost_orders,\n", "       orders_with_surge,\n", "       count(distinct suborder_id) as \"Orders\",\n", "       count(distinct picker_id) as \"Pickers\",\n", "       count(distinct partner_id) as \"Riders\",\n", "       round((count(distinct Case when instore_time <= 120 then suborder_id end))*1.0/(nullif(count(distinct suborder_id),0)),4) as \"perc orders within 2 min\",\n", "       round((sum(instore_time)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg Instore Time\",\n", "       round((sum(checkout_to_picker_assigned)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg Assigned Time\",\n", "       round((sum(assigned_to_start)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg Assigned to start Time\",\n", "       round((sum(picking_start_to_complete)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg picking start to complete Time\",\n", "       round((sum(picking_complete_to_billing_start)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg picking complete to billing start Time\",\n", "       round((sum(billing_start_to_billing_complete)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg billing start to complete Time\",\n", "       round((sum(billing_complete_to_order_scan)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg billing complete to order scan Time\",\n", "       round((sum(order_scanned_to_enroute)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg order scan to enroute Time\",\n", "       round((sum(delivery_time)/60.0)/nullif(count(distinct suborder_id),0),2) as \"Avg delivery Time\",\n", "       sum(total_selling_price) as GMV,\n", "       round((count(distinct Case when delivery_time <= 900 and order_current_status = 'DELIVERED' then suborder_id end))*1.0/(nullif(count(distinct case when order_current_status = 'DELIVERED' then suborder_id end),0)),4) as \"perc orders Delivered within 15 min\"\n", "      from\n", "(\n", "SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       o.suborder_id,\n", "       o.cart_id,\n", "       o.order_id,\n", "       o.picker_id,\n", "       o.partner_id,\n", "       o.total_selling_price,\n", "       o.total_items_quantity_ordered as IPO,\n", "       o.order_current_status,\n", "       lo.total_lost_orders,\n", "       so.orders_with_surge,\n", "       ---\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist)\n", "       end as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "\n", "       case when order_picking_started_ts_ist is not null then DATEDIFF(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist)\n", "       end as assigned_to_start,\n", "\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist)\n", "       end as picking_start_to_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       DATEDIFF(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "    --   DATEDIFF(seconds,coalesce(order_picking_completed_ts_ist,order_billing_completed_ts_ist),\n", "    --   coalesce(order_scanned_ts_ist,scan_start_ist)) as picking_complete_to_order_scanned,\n", "    DATEDIFF(SECONDS,order_billing_completed_ts_ist,coalesce(order_scanned_ts_ist,scan_start_ist)) AS billing_complete_to_order_scan,\n", "       DATEDIFF(seconds,coalesce(order_scanned_ts_ist,scan_start_ist),order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       case when order_delivered_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) end as delivery_time\n", "    --   DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist) as picking_complete_to_billing_complete\n", "\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join dwh.fact_sales_order_details b on date(b.order_create_ts_ist) = date(o.order_checkout_ts_ist)\n", "-- and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id and business_type_id = 7\n", "LEFT JOIN \n", "        order_scanned os\n", "            ON os.external_order_id=b.order_id\n", "left join lost_order lo on lo.outlet_id=o.outlet_id and lo.date_l= date(o.order_checkout_ts_ist)\n", "left join surge_order so on so.outlet_id=o.outlet_id and so.date_s= date(o.order_checkout_ts_ist)\n", "WHERE \n", "  (date(order_checkout_ts_ist) >= current_date-14 and date(order_checkout_ts_ist) < current_date)\n", "and is_rescheduled=false \n", "\n", "and order_picking_completed_ts_ist is not null  \n", "-- o.order_type='super_express'\n", "and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "and (date(order_checkout_ts_ist + interval '5.5 hours') >= current_date-14 and date(order_checkout_ts_ist + interval '5.5 hours') <= current_date)\n", "and order_picking_completed_ts_ist is not null\n", "and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n", ")\n", "group by 1,2,3,4,5,6\n", "order by 1 desc),\n", "\n", "base1 as\n", "(select date(delivery_date) as date_ts,\n", "outlet_id,\n", "--trim(c.name) as city_name,\n", "       UPPER(case when c.name in ('Ghaziabad', 'Noida') then 'UP-NCR'\n", "            when c.name = 'Bangalore' then 'Bengaluru'\n", "            else c.name end) as city_name,\n", "       count(distinct order_id) as complaint_orders\n", "from metrics.complaints o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "inner join lake_retail.console_location c on c.id = m.tax_location_id\n", "where (date(delivery_date) >= current_date-14 and date(delivery_date) < current_date)\n", "group by 1,2,3),\n", "\n", "base2 as\n", "(\n", "with attribution as (SELECT ScheduledDate,\n", "      outlet_id,\n", "      facilityname,\n", "      count(distinct order_id) as unfulfilled_orders,\n", "      count(item_id) as unfulfilled_items,\n", "      sum(CASE\n", "      WHEN reason = 'FNV' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Fnv,\n", "      sum(CASE\n", "      WHEN reason = 'Metering' or reason = 'Nayala' THEN unfulfilled\n", "      ELSE 0\n", "      END) as \"Nyala & Metering\",\n", "      sum(CASE\n", "      WHEN reason = 'Reschedule_Impact' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reschedule_Impact,\n", "    --   sum(CASE\n", "    --   WHEN  THEN unfulfilled\n", "    --   ELSE 0\n", "    --   END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'GRN pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as GRN_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'return pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as return_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'badstock marked' THEN unfulfilled\n", "      ELSE 0\n", "      END) as badstock_marked,\n", "      sum(CASE\n", "      WHEN reason = 'Tail_Assortment' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'Checkout' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Checkout,\n", "      sum(CASE\n", "      WHEN reason = 'Partial Inventory' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Partial_Inventory,\n", "      sum(CASE\n", "      WHEN reason = 'Reprocure' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reprocure,\n", "      sum(CASE WHEN reason = 'Others' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Others\n", "      from \n", "(\n", "SELECT slot_start::date AS ScheduledDate,\n", "             facility_id,\n", "             facilityname,\n", "             outlet_name,\n", "             outlet_id,\n", "             order_id,\n", "             a.item_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled,\n", "             (case \n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and ((upper_limit>0) or (fnv_upper_limit>0)) then 'Metering'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Created') then 'Nayala'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Reprocure') then 'Reschedule_Impact'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,inward_timestamp,picking_time) between 0 and 120) then 'GRN pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,return_timestamp,picking_time) between 0 and 120) then 'return pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (badstock_qty > ordered_quantity and datediff(min,badstock_timestamp,picking_time) between -120 and 120) then 'badstock marked'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (actual_quantity < 5) then 'Tail_Assortment'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.outlet_type=1) then 'FNV' \n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' then 'Checkout'\n", "                  when reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' then 'Partial Inventory'\n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' then 'Reprocure'\n", "                  else 'Others' end) as reason\n", "      FROM metrics.fillrateattribution_darkstores a\n", "      inner join lake_rpc.product_product pp on pp.item_id = a.item_id\n", "      WHERE (slot_start::date >= current_date-14 and slot_start::date < current_date) and reason_key <> ''\n", "      and pp.outlet_type=2\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               10\n", "      order by 1,2,3,4,5,6,7,8)\n", "-- where outlet_id in (995,1056)\n", "group by 1,2,3),\n", "\n", "delivered_orders as\n", "( select \n", "    date(order_delivery_slot_start_ts_ist) as scheduled_date,\n", "    outlet_id,\n", "    b.name as outlet_name,\n", "    count(distinct order_id) as total_orders,\n", "    sum(distinct_items_ordered) as total_items\n", "    from dwh.fact_supply_chain_order_details a \n", "    inner join lake_retail.console_outlet b on a.outlet_id = b.id\n", "    where is_order_delivered=true and \n", "    (date(order_delivery_slot_start_ts_ist) >= current_date-14 and date(order_delivery_slot_start_ts_ist) < current_date)\n", "    and business_type_id = 7\n", "    group by 1,2,3\n", ")\n", "\n", "\n", "select b.scheduled_date as date_ts,\n", "b.outlet_id,\n", "      b.outlet_name,\n", "      sum(b.total_orders) as total_orders,\n", "      ---b.total_orders-a.unfulfilled_orders as fulfilled_orders,\n", "      sum(a.unfulfilled_orders) as unfulfilled_orders,\n", "      sum(a.unfulfilled_orders)*1.0/sum(b.total_orders) as fill_rate\n", "    --   b.total_items as total_items,\n", "    --   b.total_items-a.unfulfilled_items as fulfilled_items\n", "      from delivered_orders b\n", "      inner join attribution a on a.outlet_id = b.outlet_id and a.ScheduledDate = b.scheduled_date\n", "        group by 1,2,3\n", ")\n", "\n", "\n", "\n", "select a.date,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       a.\"Orders\",\n", "       \"Pickers\",\n", "       \"Riders\",\n", "       \"perc orders within 2 min\",\n", "       \"Avg Instore Time\",\n", "       \"Avg Assigned Time\",\n", "       \"Avg Assigned to start Time\",\n", "       \"Avg picking start to complete Time\",\n", "       \"Avg picking complete to billing start Time\",\n", "       \"Avg billing start to complete Time\",\n", "       \"Avg billing complete to order scan Time\",\n", "       \"Avg order scan to enroute Time\",\n", "       \"Avg delivery Time\",\n", "       (1-c.fill_rate) as fill_rate,\n", "       complaint_orders,\n", "        GMV,\n", "        \"perc orders Delivered within 15 min\",\n", "        a.total_lost_orders,\n", "       a.orders_with_surge\n", "from base a\n", "left join base2 c on c.date_ts = a.date and a.\"Store ID\" = c.outlet_id\n", "left join base1 b on a.date = b.date_ts and a.\"Store ID\" = b.outlet_id\n", "order by 1 desc\n", "\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2334a542-0610-435f-b538-883ed34f6035", "metadata": {}, "outputs": [], "source": ["store_per = pd.read_sql(seventh, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "b94d46c3-1ec0-4471-b574-23144d9679e1", "metadata": {}, "outputs": [], "source": ["fnv_sql = \"\"\"\n", "with data as(select \n", "row_number() over(partition by date(start_timestamp+interval '330' minute),employee_id) as serial_no,\n", "date(start_timestamp+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "outlet_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "max(case when type=2 then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is not null then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is null then start_timestamp+interval '990' minute\n", "when type=1 and end_timestamp is null then current_timestamp + interval '330' minute\n", "else end_timestamp+interval '330' minute\n", "end) as max_log_out_timestamp,\n", "min(start_timestamp+interval '330' minute) as min_log_in_timestamp\n", "from  lake_warehouse_location.user_attendance a\n", "inner join lake_retail.warehouse_user b on a.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = a.outlet_id\n", "where date(start_timestamp+interval '330' minute) >= current_date - 15\n", "and d.business_type_id = 7\n", "and c.name = 'F&V Executive'\n", "group by 2,3,4,5,6,7,8,9),\n", "\n", "data2 as (\n", "select \n", "row_number() over(partition by date(shift_start_time+interval '330' minute),employee_id) as serial_no,\n", "date(shift_start_time+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "cast(site_id as int) as outlet_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "max(case when shift_end_method='MANUAL' then shift_end_time + interval '330' minute\n", "when shift_end_method='AUTO' and shift_end_time is not null then shift_end_time + interval '330' minute\n", "when shift_end_method='AUTO' and shift_end_time is null then shift_start_time+interval '990' minute\n", "when shift_end_method='MANUAL' and shift_end_time is null then current_timestamp + interval '330' minute\n", "else shift_end_time+interval '330' minute\n", "end) as max_log_out_timestamp,\n", "\n", "min(shift_start_time+interval '330' minute) as min_log_in_timestamp\n", "from  lake_ops_management.shift s\n", "inner join lake_retail.warehouse_user b on s.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = cast(s.site_id as int)\n", "where date(shift_start_time + interval '330' minute) >= date('2022-06-01')\n", "and c.name = 'F&V Executive'\n", "group by 2,3,4,5,6,7,8,9\n", ")\n", "\n", "select date_ts,outlet_id,\n", "count(distinct employee_id) as fnv_count from data\n", "group by 1,2\n", "union all\n", "select date_ts,outlet_id,\n", "count(distinct employee_id) as fnv_count from data2\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9a8e0aab-2a87-47d7-be0d-74216866c062", "metadata": {}, "outputs": [], "source": ["fnv_data = pd.read_sql(fnv_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "7b45e53c-dede-41ca-b836-e8626f8b9635", "metadata": {}, "outputs": [], "source": ["fnv_data = fnv_data.rename(columns={\"date_ts\": \"date\", \"outlet_id\": \"store id\"})\n", "fnv_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b74b47a1-d876-42bf-b11e-8a5cca77f906", "metadata": {}, "outputs": [], "source": ["store_data_final = store_per.merge(fnv_data, on=[\"store id\", \"date\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "750eefba-f2e6-4e2d-9fb1-75b5062543e0", "metadata": {}, "outputs": [], "source": ["store_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "55b88b44-ee0e-4ab6-9e1a-916bcbdbf6e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "74e31e4c-d093-4cd9-9db7-24834d377f4c", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        store_data_final[store_data_final[\"store id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"store_raw\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "46abc61b-4fce-4c8b-9203-a52031f9df64", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     store_data_final,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"store_raw\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "4f444145-2339-4ec5-93ad-ec1c6be808c5", "metadata": {}, "outputs": [], "source": ["eighth = \"\"\"with data as (\n", "select date(created_at) as date_, outlet_id,\n", "count(distinct id) as total_task, \n", "count(distinct case when state = 3 then id end) as completed_task\n", "from\n", "lake_warehouse_location.audit_task\n", "where date(created_at) >= current_date - 15\n", "group by 1,2),\n", "\n", "data2 as (\n", "select date(created_date) as date_ ,\n", "cast(site_id as int) as site_id,entity_id,\n", "-- (case when reason_code='PNA' then 'PNA'\n", "-- when reason_code ='Bad Stock' then 'Expiry Audit'\n", "-- else 'Self Audit' end) as audit_category,\n", "-- date_diff('second',start_time,end_time) as completion_time,\n", "status,expected_count,processed_qty\n", "from lake_storeops.activity\n", "where date(created_date) >= current_date - 15\n", "and type = 'INV_AUDIT'\n", "),\n", "\n", "data3 as (\n", "select date_, site_id as outlet_id,\n", "count(entity_id) as total_task,\n", "count(case when status='COMPLETED' then entity_id end) as completed_task\n", "from data2\n", "group by 1,2\n", ")\n", "select * from data3\n", "union\n", "select * from data\n", "-- select d1.date_, d1.outlet_id,\n", "-- d1.total_task, d2.completed_task\n", "-- from data d1\n", "-- join data2 d2\n", "-- on d1.outlet_id = d2.outlet_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "02a3b5f3-a200-4bc2-adcc-dcd2c78a5925", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "55d46eab-b21b-4c75-8ade-4b394cfabf3b", "metadata": {}, "outputs": [], "source": ["store_data = pd.read_sql(eighth, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "8e92f9ee-1814-42a9-bfe0-f94b16ef3b4d", "metadata": {}, "outputs": [], "source": ["store_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1e82d5a7-96c5-4a10-b2a4-0f9dc328178f", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        store_data[store_data[\"outlet_id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"audit_data\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "db278a6a-40d0-436f-8ae4-3b52c523086f", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     store_data,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"audit_data\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "f201493a-520f-4b21-804e-45d9ce16a93c", "metadata": {}, "outputs": [], "source": ["nineth = \"\"\"\n", "with base as(\n", "select\n", "    oi.cart_checkout_ts_ist::date as date_,\n", "    oi.outlet_id,\n", "    c.name as outlet_name,\n", "    oi.cart_id,\n", "    oi.product_id,\n", "    case when l0_category_id in (1487) then 'fnv'\n", "      when l0_category_id in (888,15,4,14,12) and l1_category_id in (279,1425,1361,1362,1363,1388,922,123,923,1200,953,1184) \n", "        and l2_category_id in (1956,1425,63,1367,1369,1730,127,1094,949,138,1091,1185,123,1778,950,1389,31,1097,198,1093) then 'perishable'\n", "      else 'packaged' end as category,\n", "    sum(oi.unit_selling_price*procured_quantity) gmv\n", "\n", "from dwh.fact_sales_order_item_details oi\n", "join dwh.fact_sales_order_details fs on fs.order_id = oi.order_id\n", "left join dwh.dim_product dp on dp.product_key = oi.dim_product_key and is_current\n", "inner join lake_retail.console_outlet c on c.id = oi.outlet_id and business_type_id = 7\n", "where fs.cart_checkout_ts_ist >= current_date - 15\n", "    and fs.cart_checkout_ts_ist < current_date\n", "    and oi.cart_checkout_ts_ist >= current_date - 15\n", "    and oi.cart_checkout_ts_ist < current_date\n", "    and fs.order_current_status = 'DELIVERED'\n", "    and fs.is_internal_order = false\n", "    and procured_quantity > 0\n", "\n", "group by 1,2,3,4,5,6\n", ")\n", ",\n", "cat_gmv as(\n", "select\n", "date_,\n", "outlet_id,\n", "outlet_name,\n", "sum(case when category = 'fnv' then gmv end) as fnv_gmv,\n", "sum(case when category = 'perishable' then gmv end) as perishable_gmv,\n", "sum(case when category = 'packaged' then gmv end) as packaged_gmv,\n", "count(distinct case when category = 'fnv' then cart_id end) as fnv_cart,\n", "count(distinct case when category = 'perishable' then cart_id end) as perishable_cart,\n", "count(distinct case when category = 'packaged' then cart_id end) as packaged_cart\n", "\n", "from base\n", "group by 1,2,3\n", ")\n", ",\n", "og as(\n", "select\n", "date_,\n", "outlet_id,\n", "count(distinct cart_id) as orders\n", "\n", "from base\n", "group by 1,2\n", ")\n", "\n", "select\n", "a.date_ as date,\n", "a.outlet_id,\n", "outlet_name,\n", "orders,\n", "fnv_gmv,\n", "perishable_gmv,\n", "packaged_gmv,\n", "(fnv_gmv + perishable_gmv + packaged_gmv) gmv,\n", "fnv_cart,\n", "perishable_cart,\n", "packaged_cart,\n", "getdate() + interval '5.5 hours' as up\n", "\n", "\n", "from og a\n", "left join cat_gmv b on a.date_ = b.date_ and a.outlet_id = b.outlet_id\n", "order by 1,2\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6a85a082-9bc0-464a-8f34-5d81af54eb1a", "metadata": {}, "outputs": [], "source": ["GMV = pd.read_sql(nineth, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "17e7a524-ffcc-4a62-a886-51764da18750", "metadata": {}, "outputs": [], "source": ["GMV.head()"]}, {"cell_type": "code", "execution_count": null, "id": "64f248f7-b6d6-42b3-ab89-250760f24032", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        GMV[GMV[\"outlet_id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"gmv_raw\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "81475aad-9ea6-4728-9793-794ebb21f0c2", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     GMV,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"gmv_raw\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "bb804ef9-245d-489e-9e57-6e6bec5b7563", "metadata": {}, "outputs": [], "source": ["tenth = \"\"\"\n", "create temporary table map as(\n", "with view as (\n", "select distinct frontend_merchant_id,\n", "        date_ist,\n", "        frontend_merchant_name,\n", "        frontend_merchant_city,\n", "        pos_outlet_id  as outlet_id,\n", "        pos_outlet_name as outlet_name,\n", "        pos_outlet_city_name as outlet_city,\n", "        row_number() over(partition by frontend_merchant_id order by date_ist desc) as rn\n", "from dwh.agg_daily_supply_ops_cost\n", "where pos_outlet_id != 0\n", "and outlet_name is not null\n", "and date_ist >= '2021-12-01'\n", "group by 1,2,3,4,5,6,7\n", ")\n", ",\n", "wh as (\n", "select distinct NUll::int as frontend_merchant_id,\n", "        pos_timestamp::date as date_,\n", "        Null as frontend_merchant_name,\n", "        Null as frontend_merchant_city,\n", "        outlet_id,\n", "        outlet_name,\n", "        NUll as outlet_city,\n", "        row_number() over(partition by outlet_id order by date_ desc) as rn\n", " from lake_pos.pos_invoice\n", "where outlet_id not in  (select distinct outlet_id from view)\n", "and outlet_name is not null\n", "and pos_timestamp >= '2021-01-01 00:00:00'\n", "group by 1,2,3,4,5,6,7,pos_timestamp\n", ")\n", ",\n", "final as (\n", "select  frontend_merchant_id,\n", "        frontend_merchant_name,\n", "        frontend_merchant_city,\n", "        outlet_id,\n", "        outlet_name,\n", "        outlet_city,\n", "        'FE' as outlet_type\n", "from view\n", "where rn=1 \n", "\n", "UNION\n", "\n", "select distinct NUll::int as frontend_merchant_id,\n", "        Null as frontend_merchant_name,\n", "        Null as frontend_merchant_city,\n", "        outlet_id,\n", "        outlet_name,\n", "        NUll as outlet_city,\n", "        'BE' as outlet_type\n", " from wh\n", "where rn=1\n", ")\n", "select * from final\n", ");\n", "\n", "select \n", "a.snapshot_date_ist::date as date_,\n", "a.outlet_id,\n", "co.name as outlet_name,\n", "case when frontend_merchant_city is null \n", "    then (case when cl.name = 'Gurgaon' then 'HR-NCR' when cl.name = 'Noida' then 'UP-NCR' when cl.name = 'Bud<PERSON><PERSON>' then 'Delhi' else cl.name end)\n", "else frontend_merchant_city end as city,\n", "\n", "sum(crm_diff) crm,\n", "sum(crbs_diff) crbs,\n", "sum(loss_in_transit_diff) lit,\n", "sum(ifo_wo_iro_diff) ifo,\n", "sum(bad_stock_damage) bsd,\n", "sum(bad_stock_near_expiry) bsne,\n", "sum(bad_stock_expired) bse,\n", "sum(esto_transfer_loss_diff) estl,\n", "sum(esto_bad_stock_diff) estd,\n", "sum(manual_negative_update_diff) mun,\n", "sum(ifo_with_iro_diff) iro,\n", "sum(customer_return_wo_invoice_diff) cr,\n", "sum(manual_positive_update_diff) mpu,\n", "sum(reinventorization_diff) ri,\n", "sum(prn_diff) prn,\n", "sum(total_diff_loss_value) total,\n", "sum(total_diff_loss_value)- sum(esto_transfer_loss_diff) finalWastage,\n", "getdate() + interval '5.5 hours' as upd\n", " \n", "from dwh.view_agg_daily_category_diff_loss a\n", "left join lake_retail.console_outlet co on co.id = a.outlet_id and co.active = 1\n", "inner join lake_retail.console_location cl on cl.id = co.tax_location_id\n", "left join map on map.outlet_id = a.outlet_id\n", "where a.snapshot_date_ist >= current_date::timestamp - interval '15 days'\n", "and a.snapshot_date_ist < current_date::timestamp\n", "group by 1,2,3,4\n", "order by 1,2\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "23e86068-6c54-486f-96a4-569392576085", "metadata": {}, "outputs": [], "source": ["wastage = pd.read_sql(tenth, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "d2f5d4b8-736a-4091-9aff-c826a74315de", "metadata": {}, "outputs": [], "source": ["wastage.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "69676387-6193-493d-b66b-85228e35d598", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        wastage[wastage[\"outlet_id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"wastage_raw\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ede2ea18-2575-4e4c-984b-f64051569f50", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     wastage,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"wastage_raw\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "56b06dcb-3a0f-4d62-bf2f-1eb5f0866008", "metadata": {}, "outputs": [], "source": ["score_data = \"\"\"\n", "select date(date) as date_ts,\n", "store_id,\n", "count(distinct case when (login is not null or attendance_punched='Yes') and (role not ilike '%%Store Manager%%' and role not ilike '%%Supervisor%%' and role not ilike '%%Team Leader%%')\n", "then employee_id end) as total_employees_present,\n", "count(distinct case when score>0 then employee_id end) as employees_present_activity,\n", "count(distinct case when score<250 and score>0 then employee_id end) as employees_more_than_250_score,\n", "count(distinct case when score>700 then employee_id end) as employees_more_than_700_score\n", "from metrics.instore_manpower_performance\n", "where date(date)>=current_date-15\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "43740d40-29ce-4791-9bca-436d1c85dc5e", "metadata": {}, "outputs": [], "source": ["people_raw = pd.read_sql(score_data, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "8ad3a32c-629c-4a7c-b3c6-772d8a86e972", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        people_raw[people_raw[\"store_id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"people_metrics\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "5bf1357e-a7fc-43f3-9b67-a99e87430ed3", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     people_raw,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"people_metrics\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "758423de-534e-4e71-8458-42cfec692f9a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4edd5735-d420-48c7-9802-84e39ca8df75", "metadata": {"tags": []}, "outputs": [], "source": ["pna_q = \"\"\"\n", "\n", "   \n", "with FillRateRawData as (\n", "SELECT       slot_end::date AS ScheduledDate,\n", "             slot_end as Slot_End,\n", "             fa.facility_id,\n", "             fa.facilityname,\n", "             fa.outlet_name,\n", "             fa.outlet_id,\n", "             fa.order_id,\n", "             fa.item_id,\n", "             fa.ordered_quantity,\n", "             fa.procured_quantity,\n", "             avg(b.selling_price) as average_selling_price,\n", "             fa.reason_key,\n", "             case when pp.outlet_type = 1 then 'FnV' else 'Grocery/Cold' end as item_type,\n", "             max(pp.name) as item_name,\n", "             max(variant_mrp) as item_mrp,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "      FROM metrics.fillrateattribution_darkstores fa left join lake_rpc.product_product pp on pp.item_id=fa.item_id\n", "      left join lake_oms_bifrost.oms_order_item b on b.order_id = fa.order_id and b.product_id = fa.product_id\n", "      WHERE (slot_end::date=current_date-1)\n", "      and reason_key != ''\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9,\n", "               10,\n", "               12,\n", "               13\n", ")\n", "\n", "Select ScheduledDate, outlet_id,outlet_name,item_id,item_name,sum(ordered_quantity-procured_quantity) as pna_qty,\n", "count(distinct order_id) as order_count,\n", "row_number() over (partition by outlet_id order by pna_qty desc) as rankk\n", "from \n", "FillRateRawData FR \n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "\n", "\n", "pna_data = pd.read_sql(pna_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "4c4e8969-eeb1-4025-8894-1af63972d47b", "metadata": {}, "outputs": [], "source": ["pna_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "be92e51e-3ea5-4c8d-96f5-d4c5a05c3200", "metadata": {}, "outputs": [], "source": ["# location_q = \"\"\"\n", "# select outlet_id,pp.item_id, wsl.location_name,sum(wplil.quantity) as  picked_qty\n", "# from lake_warehouse_location.warehouse_pick_list_item_location wplil\n", "# inner join lake_rpc.product_product pp on pp.variant_id =wplil.variant_id\n", "# inner join lake_warehouse_location.warehouse_storage_location wsl on wsl.id = wplil.location_id\n", "# where pp.active=1\n", "# and date(wplil.created_at+interval '5.5 hours') between current_date-3 and current_date-1\n", "# group by 1,2,3\n", "# having sum(wplil.quantity)>0\n", "# \"\"\"\n", "\n", "# location_data = pd.read_sql(location_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "14c34b44-7c2a-4f8d-9b3d-a663253bcb22", "metadata": {}, "outputs": [], "source": ["location_q = \"\"\"\n", "select wom.outlet_id AS outlet_id,\n", "       wil.item_id,\n", "       wsl.location_name,\n", "       sum(wil.quantity) as quantity\n", "\n", "from lake_warehouse_location.warehouse_item_location wil \n", "inner join \n", "(select warehouse_id, item_id,variant_id,location_id, max(updated_at) as max_updated_at\n", "from lake_warehouse_location.warehouse_item_location\n", "group by 1,2,3,4) as a on wil.item_id = a.item_id and wil.variant_id = a.variant_id and\n", "wil.updated_at = max_updated_at and a.warehouse_id = wil.warehouse_id and wil.location_id = a.location_id\n", "inner join lake_warehouse_location.warehouse_storage_location wsl on wsl.id = wil.location_id\n", "inner join lake_warehouse_location.warehouse_storage_type wst on wst.id=wsl.storage_type_id\n", "inner join lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id=wil.warehouse_id\n", "INNER JOIN lake_retail.console_outlet o ON o.id=wom.outlet_id\n", "inner join lake_rpc.product_product pp on pp.item_id = wil.item_id and pp.variant_id = wil.variant_id\n", "where wsl.is_active=1 and pp.active=1 \n", "and wil.updated_at>=current_date-30\n", "group by 1,2,3\n", "\n", "\"\"\"\n", "location_data = pd.read_sql(location_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "b5aecd25-96f8-43a7-b76c-40c6ae3cd5db", "metadata": {}, "outputs": [], "source": ["location_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "31af9701-8022-47b7-b373-eaca26279832", "metadata": {}, "outputs": [], "source": ["location_data[\"key\"] = location_data.apply(\n", "    lambda x: str(x[\"outlet_id\"]) + str(x[\"item_id\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a1deb74b-0921-4cac-883e-8514f27b0583", "metadata": {}, "outputs": [], "source": ["location_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bcf82561-e4ce-4281-b3b9-eaf7395fb03d", "metadata": {}, "outputs": [], "source": ["location_dict = location_data.groupby(\"key\")[\"location_name\"].agg(list).to_dict()\n", "# location_data1[['key','location_name']].set_index(\"key\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "6e0ed8ad-d5a1-43b4-8ecb-ea2e07f80ab3", "metadata": {}, "outputs": [], "source": ["for keys in location_dict:\n", "    location_dict[keys] = str(location_dict[keys])"]}, {"cell_type": "code", "execution_count": null, "id": "0e77f30b-6680-4e1b-afa0-786c87a81bfb", "metadata": {}, "outputs": [], "source": ["location_data_new = pd.DataFrame.from_dict(\n", "    location_dict, orient=\"index\", columns=[\"location_name\"]\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "225a351a-7c84-4a9e-8d88-3216ece9dc26", "metadata": {}, "outputs": [], "source": ["location_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8466afef-de41-4e98-b02c-1206c7f84144", "metadata": {}, "outputs": [], "source": ["location_data_new = location_data_new.rename(columns={\"index\": \"key\"})"]}, {"cell_type": "code", "execution_count": null, "id": "45d1a1ff-af4d-4884-9433-f96d3ec0d611", "metadata": {}, "outputs": [], "source": ["location_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aba2f53b-6f78-4041-b1a7-feaf3fcd69b0", "metadata": {}, "outputs": [], "source": ["pna_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "591a9ec6-ea28-41cd-bdbc-7a7baeaf4d5a", "metadata": {}, "outputs": [], "source": ["pna_data[\"key\"] = pna_data.apply(\n", "    lambda x: str(x[\"outlet_id\"]) + str(x[\"item_id\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0039b872-5e40-47e3-9219-7ab4cfdc0ce1", "metadata": {}, "outputs": [], "source": ["pna_data_final = pna_data.merge(location_data_new, on=[\"key\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "7a4e3e2b-4cf1-46b3-b1e9-4b2155c8cc0a", "metadata": {}, "outputs": [], "source": ["pna_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b6121be4-d87e-4444-9102-2c0d968166c3", "metadata": {}, "outputs": [], "source": ["pna_data_final.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7a95fe4a-92ed-4981-8e0d-c3aabd981431", "metadata": {}, "outputs": [], "source": ["pna_data_final = pna_data_final[\n", "    [\n", "        \"scheduleddate\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"location_name\",\n", "        \"pna_qty\",\n", "        \"order_count\",\n", "        \"rankk\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "635479fe-878b-4ca0-b77b-6001c8de5ee3", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        pna_data_final[\n", "            (pna_data_final[\"outlet_id\"].isin(region_dict_new[i][0]))\n", "            & (pna_data_final[\"rankk\"] <= 15)\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"pna_raw\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "af01e755-0f62-4e02-a47f-a05e3953bad1", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     pna_data_final[pna_data_final[\"rankk\"] <= 15],\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"pna_raw\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "ef164bb0-70ad-472c-8c5b-acd37dee3ead", "metadata": {}, "outputs": [], "source": ["picking_q = \"\"\"\n", "\n", "\n", "with base as \n", "(\n", "\n", "SELECT date,\n", "       outlet,\n", "       outlet_name,\n", "     \n", "       city,\n", "       picklist_id,\n", "      \n", "       datediff(seconds,picking_started_at_ist,picking_completed_at_ist) AS picking_time,\n", "       item_id,\n", "\n", "       variant_id,\n", "       item_name,\n", "       location_name,\n", "       picked_quantity,\n", "       (CASE\n", "            WHEN sku_time IS NULL THEN datediff(seconds,picking_started_at_ist,updated_at)\n", "            ELSE sku_time\n", "        END) AS sku_time\n", "FROM\n", "  (SELECT DISTINCT outlet,\n", "                   outlet_name,\n", "                  \n", "                   date,\n", "                   city,\n", "                   picklist_id,\n", "                   picking_started_at_ist,\n", "                   picking_completed_at_ist,\n", "                   item_id,\n", "                  updated_at,\n", "                   variant_id,\n", "                   item_name,\n", "                   location_name,\n", "                   picked_quantity,\n", "                   datediff(seconds,(lag(updated_at,1) OVER (PARTITION BY outlet,picklist_id\n", "                                                             ORDER BY updated_at)),updated_at) AS sku_time       \n", "FROM\n", " (SELECT DISTINCT i.outlet,\n", "                      co.name AS outlet_name,\n", "                      date(i.created_at+ interval '5.5 hrs') as date,\n", "                    --   i.ancestor,\n", "                    replace(trim(location),'Bangalore','Bengaluru') as city,\n", "                      pl.id AS picklist_id,\n", "                      (pl.picking_started_at +interval '5.5 hrs') AS picking_started_at_ist,\n", "                      (pll.pos_timestamp +interval '5.5 hrs') AS picking_completed_at_ist,\n", "                      pli.item_id,\n", "                    \n", "                      pp.variant_id,\n", "\n", "                      pp.name as item_name,\n", "                      wsl.location_name,\n", "                      picked_quantity,\n", "                      (pli.updated_at +interval '5.5 hrs') AS updated_at\n", "      FROM lake_ims.ims_order_details i\n", "      INNER JOIN lake_retail.console_outlet co ON co.id = i.outlet\n", "      AND co.business_type_id = 7\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list_order_mapping plo ON plo.order_id = i.order_id\n", "      and (date(plo.created_at+ interval '5.5 hrs')=current_date-1)\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list pl ON pl.id = plo.pick_list_id and pl.outlet_id = i.outlet\n", "      AND pl.state IN (4,\n", "                       10) and (date(pl.created_at+ interval '5.5 hrs')=current_date-1)\n", "\n", "      LEFT JOIN lake_warehouse_location.pick_list_log pll ON pl.id = pll.pick_list_id\n", "      AND pll.picklist_state = 4 and (date(pll.pos_timestamp+ interval '5.5 hrs')=current_date-1)\n", "      LEFT JOIN lake_warehouse_location.pick_list_log pllh ON pl.id = pllh.pick_list_id\n", "      AND pllh.picklist_state = 12 and (date(pllh.pos_timestamp+ interval '5.5 hrs')=current_date-1)\n", "\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list_item PLI ON pli.pick_list_id = pl.id and pli.outlet_id = pl.outlet_id\n", "      and (date(pli.created_at+ interval '5.5 hrs')=current_date-1)\n", "      \n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list_item_variant pliv ON pliv.pick_list_item_id = pli.id and pli.outlet_id = pl.outlet_id\n", "      and (date(pliv.created_at+ interval '5.5 hrs')=current_date-1)\n", "      \n", "    LEFT JOIN lake_rpc.product_product pp ON pp.item_id = pli.item_id and pp.active=1 and pp.variant_id = pliv.variant_id\n", "       left join lake_rpc.item_product_mapping m on m.item_id = pp.item_id\n", "       left join dwh.dim_product d on d.product_id = m.product_id and d.is_current = true\n", "    left join lake_warehouse_location.warehouse_pick_list_item_location wplil on wplil.pick_list_item_id = pli.id and pli.outlet_id = wplil.outlet_id\n", "      left join lake_warehouse_location.warehouse_storage_location wsl on wsl.id = wplil.location_id\n", "      left join lake_warehouse_location.warehouse_storage_type wst on wst.id=wsl.storage_type_id\n", "      WHERE \n", "         pllh.pick_list_id IS NULL\n", "         and picked_quantity>0\n", "        and d.l0_category <> 'Vegetables & Fruits'\n", "\n", "         AND (date(i.scheduled_at+ interval '5.5 hrs')=current_date-1) ) as x) as xx\n", "\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,11,12\n", "            ),\n", "\n", "data2 as (\n", "select date, outlet, outlet_name,\n", "location_name,item_name, sum(picking_time)/sum(picked_quantity) as avg_time,\n", "sum(picked_quantity) as picked_quantity\n", "from base\n", "group by 1,2,3,4,5\n", "having avg_time>30),\n", "\n", "data3 as \n", "(\n", "select date, outlet, outlet_name,\n", "location_name, item_name, sum(total_picking_time_in_mins)/sum(picked_quantity) as avg_time,\n", "sum(picked_quantity) as picked_quantity\n", "from\n", "(select \n", "date(a.created_date+INTERVAL '5.5 HOURS') as date,\n", "b.id as outlet,\n", "b.name as outlet_name,\n", "ac.item_id,\n", "pp.name as item_name,\n", "location_details as location_name,\n", "sum(date_diff('second',id.pick_start_time,picked_time)) as total_picking_time_in_mins,\n", "sum(ac.processed_qty) as picked_quantity\n", "from lake_storeops.activity a\n", "inner join lake_storeops.item_activity ac on ac.activity_id = a.id\n", "inner join lake_retail.console_outlet b on b.id =cast(a.site_id as int)\n", "inner join lake_storeops.item_details id on id.item_activity_id = ac.id\n", "LEFT JOIN lake_rpc.product_product pp ON pp.item_id = ac.item_id and pp.active=1 and pp.variant_id = id.variant_id\n", "      left join lake_rpc.item_product_mapping m on m.item_id = pp.item_id\n", "      left join dwh.dim_product d on d.product_id = m.product_id and d.is_current = true\n", "INNER JOIN \n", "        lake_retail.console_location \n", "            AS l \n", "            ON l.id=b.tax_location_id\n", "where type in ('PICKING') and business_type_id=7\n", "and d.l0_category <> 'Vegetables & Fruits'\n", "and a.status='COMPLETED' and date(a.created_date + INTERVAL '5.5 HOURS')=current_date-1\n", "and date(ac.created_date + INTERVAL '5.5 HOURS')=current_date-1\n", "and date(id.picked_time + INTERVAL '5.5 HOURS')=current_date-1\n", "\n", "group by 1,2,3,4,5,6) as x\n", "group by 1,2,3,4,5\n", "having avg_time>30\n", "\n", "),\n", "\n", "\n", "data4 as\n", "(select * from data2\n", "union \n", "select * from data3)\n", "\n", "select *,\n", "row_number() over (partition by outlet order by (picked_quantity) desc) as rankk\n", "from data4\n", "\n", "\"\"\"\n", "\n", "\n", "picking_data = pd.read_sql(picking_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "67c35d61-9f9c-4750-9f0d-6ad2083ace5c", "metadata": {}, "outputs": [], "source": ["picking_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf19530a-8171-4b2f-9e4b-8a7b823de1ac", "metadata": {}, "outputs": [], "source": ["picking_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "adb7b8bd-5854-48ca-a9c7-9f3d82ce8306", "metadata": {}, "outputs": [], "source": ["picking_date_new = picking_data[\n", "    [\n", "        \"date\",\n", "        \"outlet\",\n", "        \"outlet_name\",\n", "        \"item_name\",\n", "        \"location_name\",\n", "        \"avg_time\",\n", "        \"picked_quantity\",\n", "        \"rankk\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "03368920-eb2d-405b-9f9f-10b148b29107", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        picking_date_new[\n", "            (picking_date_new[\"outlet\"].isin(region_dict_new[i][0]))\n", "            & (picking_date_new[\"rankk\"] <= 15)\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"picking_time\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "4edace7e-5d9c-4a04-995e-1174f67a765d", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     picking_date_new,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"picking_time\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "f5720c29-850a-46c2-8c55-9163def4486d", "metadata": {}, "outputs": [], "source": ["complaints_q = \"\"\"\n", "with data as(\n", "select outlet_id,\n", "outlet_name, round(item_id::int,0) as item_id, product_name,\n", "count(distinct order_id) as complaint_count,\n", "row_number() over (partition by outlet_id order by complaint_count desc) as rankk\n", "from metrics.complaints a\n", "left join lake_rpc.item_product_mapping pp on pp.product_id = a.product_id and active=1\n", "where date(complaint_date) =current_date -1\n", "group by 1,2,3,4)\n", "\n", "select * from data\n", "where rankk<=15\n", "\n", "\"\"\"\n", "\n", "complaints_data = pd.read_sql(complaints_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "26dff798-99de-4163-bb26-6076ec64b78e", "metadata": {}, "outputs": [], "source": ["complaints_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ab083f9f-9d26-4127-9007-88dd096b1897", "metadata": {}, "outputs": [], "source": ["complaints_data[\"key\"] = complaints_data.apply(\n", "    lambda x: str(x[\"outlet_id\"]) + str(x[\"item_id\"]).split(\".\")[0], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e9826715-29a9-435b-a74b-857d1f0d943d", "metadata": {}, "outputs": [], "source": ["complaints_data_final = complaints_data.merge(location_data_new, on=[\"key\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "7414b804-644c-4ba3-bc75-7ee0a6b72b6c", "metadata": {}, "outputs": [], "source": ["complaints_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7123afca-094d-42da-a2b9-ac8bf47714c7", "metadata": {}, "outputs": [], "source": ["complaints_data_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6fd02967-bc03-4581-86db-b3af61414a3d", "metadata": {}, "outputs": [], "source": ["complaints_data_final.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3ca64b42-fe79-4203-aed1-3bb61e04c794", "metadata": {}, "outputs": [], "source": ["complaints_data_final = complaints_data_final[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"product_name\",\n", "        \"location_name\",\n", "        \"complaint_count\",\n", "        \"rankk\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "41407c7e-33a7-4d85-8bed-c2ac61711721", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        complaints_data_final[\n", "            (complaints_data_final[\"outlet_id\"].isin(region_dict_new[i][0]))\n", "            & (complaints_data_final[\"rankk\"] <= 15)\n", "        ],\n", "        sheet_dict[i][0],\n", "        \"complaint_data\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "c7d09075-e6b2-4e77-8ec0-36f7a607b679", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     complaints_data_final[complaints_data_final[\"rankk\"] <= 15],\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"complaint_data\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "cfdf380b-6b8b-472f-97bb-f5a640033b75", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e30f93e-36f9-474b-b422-5de1e5d99d2a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b31fb184-20f8-4114-a88a-6db9a402615c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "751770b1-4039-4932-b970-4925a5c22bd6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60ca993c-9496-467e-b989-96e671305437", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}