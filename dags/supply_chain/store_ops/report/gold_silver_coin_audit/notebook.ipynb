{"cells": [{"cell_type": "code", "execution_count": null, "id": "2f01fd7c-92fb-4807-99a4-f5a96b099e5c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import boto3\n", "import requests\n", "import calendar\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "8f78fb5f-712a-44cd-971b-0fb140f28122", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "82015305-2d3e-4431-a688-a46513344d2d", "metadata": {}, "outputs": [], "source": ["top = \"\"\"\n", "(with data as (\n", "select cast(site_id as int) site_id, item_id,\n", "expiry_date,quantity\n", "from lake_storeops.location_inventory\n", "where supply_bucket = 'ON_SHELF'\n", "and quantity>0\n", ")\n", "select  item_id,\n", "        sum(quantity) as ims_qty,\n", "       'High Value Items for gold and silver coins' as reason_code,\n", "       'High Value Items for gold and silver coins' as reason_text,\n", "       'GR7721' as req_user_id,\n", "        site_id\n", "from data\n", "where item_id in (10048994,10109705,10106648,10106544,10106652,10109708,10106612,10106612,10106648,10116154,10116161,10106810,10106019,10111652,10116154,10116155,10116156,10106809,10109707,10106809,10106810,10106544,10106020,10116155,10106020)\n", "--and site_id = 2705\n", "group by 1,3,4,5,6)\n", "   \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b62ca05b-f2e1-4683-9e0d-e1a1eb212c82", "metadata": {}, "outputs": [], "source": ["audit_data = pd.read_sql(top, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "e73dafd6-ff49-4a96-9958-cdd52514ea78", "metadata": {}, "outputs": [], "source": ["json_records = audit_data.to_dict(orient=\"records\")"]}, {"cell_type": "code", "execution_count": null, "id": "a71e13f3-e5f5-4580-8aff-b12cce3167a7", "metadata": {}, "outputs": [], "source": ["for i in range(len(json_records)):\n", "    url = \"https://storeops-api.grofers.com/api/createInvAudit\"\n", "    payload = json.dumps(json_records[i])\n", "    headers = {\n", "        \"Content-type\": \"application/json\",\n", "        \"token\": \"c3RvcmVvcHMtMTExNAo=\",\n", "        \"tokenType\": \"BasicAuth\",\n", "    }\n", "    response = requests.request(\"POST\", headers=headers, data=payload, url=url)\n", "    print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "56f6d6eb-3fe6-401b-b4f2-1902c445eda6", "metadata": {}, "outputs": [], "source": ["SM_data = pb.from_sheets(\n", "    \"10r9mSzVvZowT6j9PWlMegKpGR6N1TKI6lytao_00b0s\", \"Master\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8b79e5f6-e9d7-47fb-81e2-1c6f7b922b66", "metadata": {}, "outputs": [], "source": ["SM_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "556ccd4d-97a7-4ffe-a3db-8f3db3b3378a", "metadata": {}, "outputs": [], "source": ["SM_data[\"Outlet ID\"] = pd.to_numeric(SM_data[\"Outlet ID\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c402b2a8-b24b-4167-9bf9-e5321628cff2", "metadata": {}, "outputs": [], "source": ["SM_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "942715ba-888b-4b6f-bf61-37b39cb03997", "metadata": {}, "outputs": [], "source": ["SM_details = SM_data[[\"Outlet ID\", \"L1 POC Name Email\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "710968e8-7ef7-48aa-b5d2-6b08d9d43599", "metadata": {}, "outputs": [], "source": ["SM_details.info"]}, {"cell_type": "code", "execution_count": null, "id": "3a2d8717-5baa-4acc-bda5-b163e91b980d", "metadata": {}, "outputs": [], "source": ["mailing_list = SM_details.set_index(\"Outlet ID\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b7f8bc4-be34-469f-975e-c50060dfe7e7", "metadata": {}, "outputs": [], "source": ["message1 = f\"\"\"<p>Hello,</p>\n", "\n", "<p>The audit tasks for high value items (Gold and Silver Coins) has been created for your respective stores. Please carry out the audit carefully as these are highly valuable items.\n", "\n", "<p>Thanks</p>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8d452b37-efa5-4ec0-882d-77194d8f92e1", "metadata": {}, "outputs": [], "source": ["for i in mailing_list:\n", "    audit_data[audit_data[\"site_id\"] == (i)].to_csv(\"audit_data.csv\", index=False)\n", "    # message2 = str(new_list[i]).strip(\"['']\")\n", "    # message2 = f\"\"\"<p>store_list[(0)]</p> <p><br></p>\"\"\"\n", "    # f\"\"\"<p>store_list[(0)]</p>\n", "    #     <p><br></p>\"\"\"\n", "    # message4 = message1+message3+ ' ' + message2+ '.'+ message5\n", "    files_to_attach = [\"audit_data.csv\"]\n", "    pb.send_email(\n", "        from_email=\"<EMAIL>\",\n", "        to_email=mailing_list[(i)],\n", "        # bcc = own_list[(i)],\n", "        subject=\"[Important] Gold Silver Coin Audit Tasks\",\n", "        html_content=message1,\n", "        files=files_to_attach,\n", "        mime_subtype=\"mixed\",\n", "        mime_charset=\"utf-8\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "3df648ca-3a4e-4c49-9571-9ed7f5c28377", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-sm-announcements\",\n", "    text=\"Hello SMs, The audit tasks for high value items (Gold and Silver Coins) has been created for your respective stores. Please carry out the audit carefully.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0fa36082-5722-4c24-a5c9-77c8aa5d6fae", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-cluster-manager-ds\",\n", "    text=\"Hello SMs, The audit tasks for high value items (Gold and Silver Coins) has been created for your respective stores. Please carry out the audit carefully.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ebbe2615-9455-41aa-b355-51f40020e57b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5336da15-f0fb-4642-8d9b-4cb34384d93e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a20b4fc-636f-4b3e-8328-282b3fb2c803", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eb61d3c7-d21d-4a6a-9c07-f0169526f96c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "01b71694-0355-4274-b80c-1e683e8d33e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1393d54-0004-4de7-b914-ebbe299f0778", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c326bc38-dcf7-4f28-af50-72fe58badc51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc17b912-89e8-41a3-b95f-1d0c8fd5b653", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f533235-5294-4f15-8cf4-32cb4081cd82", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6806ade4-0b1d-4175-a527-fe1c07ea72f0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d97d169-1dcf-4ca1-8ce9-90417605b6d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48f174fa-1e5a-409c-ba4a-a2716ba0dffd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "90722a1d-c7ff-4690-9e9e-f57df16127b1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3785032-528e-4de7-84fb-792d67bc2e2c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "36cfb140-55ac-4384-94b4-b7ec44568bd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8dac11d8-be27-478f-8562-182181d003bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fdeb6ff-88c8-4687-9794-b8dad308ab8a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab491adc-ecef-41a4-913a-7f2d95701182", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf269638-2210-4923-8fd4-92bd92524747", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a342cf1-81f7-4dd1-a562-7b172f5a45a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}