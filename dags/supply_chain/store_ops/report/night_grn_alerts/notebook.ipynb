{"cells": [{"cell_type": "code", "execution_count": null, "id": "fbd9ba0a-987e-403a-8f9b-f2b12a483010", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "\n", "start_time = time.time()\n", "\n", "city_list = [\n", "    \"Bengaluru\",\n", "    \"Kolkata\",\n", "    \"UP-NCR\",\n", "    \"East Delhi\",\n", "    \"HR-NCR\",\n", "    \"North Delhi\",\n", "    \"West Delhi\",\n", "    \"Mumbai\",\n", "    \"South Delhi\",\n", "    \"Faridabad\",\n", "    \"Lucknow\",\n", "    \"Kanpur\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cd0f7372-6e4e-4b26-9676-a9399cc0d584", "metadata": {}, "outputs": [], "source": ["sto_base_query = f\"\"\"WITH pre_esto AS\n", "  (SELECT distinct a.*,indent, indent_created_at,\n", "          b.facility_id AS sender_facility_id,\n", "          cf.name AS sender_facility_name,\n", "          c.facility_id AS receiver_facility_id,\n", "          f.name AS receiver_facility_name\n", "   FROM metrics.esto_details a\n", "   left join metrics.multiRep_data mr on mr.bulk_sto_id = a.bulk_sto_id and a.ars_mode='normal' and \n", "   mr.sto_created_at >= (CURRENT_DATE -7)::TIMESTAMP \n", "   AND mr.sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP\n", "   ---left join lake_ims.ims_inward_invoice bb on a.invoice_id = bb.vendor_invoice_id and bb.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list cc on cc.grn_id = bb.grn_id and cc.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list_item d on d.putlist_id=cc.id and a.item_id = d.item_id and d.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) b ON a.sender_outlet_id = b.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) cf ON cf.id = b.facility_id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) c ON a.receiving_outlet_id = c.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) f ON f.id = c.facility_id\n", "\n", "   WHERE b.name NOT LIKE '%%Infra%%'\n", "   AND a.sto_created_at >= (CURRENT_DATE -7)::TIMESTAMP \n", "   AND a.sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP \n", "   and a.ars_mode = 'normal'  \n", "   ),\n", "\n", "final_esto AS\n", "  (SELECT sto_id,\n", "          ars_run_id, item_id,indent, indent_created_at,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiving_outlet_id as receiver_outlet_id,\n", "          receiver_facility_name,\n", "          sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "         \n", "         avg(reserved_quantity) AS reserved_qty,\n", "         avg(billed_quantity) AS billed_quantity,\n", "         avg(inwarded_quantity) AS inwarded_qty,\n", "         avg(reserved_quantity_after_shortfall) AS req_qty_sf,\n", "         avg(putaway_quantity) AS putaway_quantity,\n", "         ---avg(placed_quantity) as putaway_quantity,\n", "         avg(disc_qty) as disc_qty,\n", "        \n", "         max(sto_type) AS sto_type,\n", "         max(sto_state) AS sto_state,\n", "         max(invoice_state) AS invoice_state, \n", " count(CASE\n", "           WHEN reserved_quantity > 0 THEN item_id\n", "       END) AS res_sku,\n", " count(CASE\n", "           WHEN billed_quantity > 0 THEN item_id\n", "       END) AS bil_sku,\n", " count(CASE\n", "           WHEN inwarded_quantity > 0 THEN item_id\n", "       END) AS inw_sku,\n", " count(CASE\n", "           WHEN reserved_quantity_after_shortfall > 0 THEN item_id\n", "       END) AS req_sf_sku\n", "   FROM pre_esto a\n", "   WHERE \n", "   ---STO_STATE IN ('Inward') \n", "   STO_STATE NOT IN ('Manual Expiry')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,6,7,8,9,10,11,12,13,14,15\n", "   HAVING sto_created_at >= (CURRENT_DATE -9) ::TIMESTAMP \n", "   AND sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP ),\n", "\n", "\n", "final_status AS\n", "  (SELECT *,\n", "          CASE\n", "              WHEN invoice_state = 'Raised' THEN 'In-Transit'\n", "              WHEN invoice_state != '' THEN 'Delivered'\n", "              WHEN sto_state = 'Expired' THEN 'Expired before Delivery'\n", "              WHEN sto_state = 'Created' THEN 'Need to Pick'\n", "              ELSE 'other'\n", "          END AS final_status\n", "   FROM final_esto a\n", "   LEFT JOIN\n", "     (SELECT DISTINCT facility_id,\n", "                      business_type_id\n", "      FROM lake_retail.console_outlet) bc ON a.receiver_facility_id = bc.facility_id\n", "   WHERE sender_facility_id NOT IN (153)\n", "     AND bc.business_type_id = 7 ),\n", "     \n", "\n", "\n", "summary AS\n", "  (SELECT sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "          ars_run_id, \n", "          sto_id, \n", "          item_id, \n", "          indent, indent_created_at,\n", "          -- final_status,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiver_outlet_id,\n", "          receiver_facility_name,\n", " sum(reserved_qty) AS total_reserved_qty,\n", " sum(req_qty_sf) AS total_req_qty_after_shortfall,\n", " sum(case when invoice_state<>'Raised' then billed_quantity end) AS total_billed_qty,\n", " sum(case when invoice_state<>'Raised' then putaway_quantity end) AS putaway_quantity,\n", " sum(inw_sku) AS inwd_sku,\n", " sum(case when invoice_state<>'Raised' then inwarded_qty end) AS grn_delivered,\n", "     sum(disc_qty) AS disc_qty\n", "   FROM final_status a\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,5,6,7,8,9,10,11,12,13,14,15),\n", "            \n", "zone_tbl_treatment AS\n", "(SELECT facility_id,\n", "  max(ZONE) AS ZONE\n", "FROM metrics.outlet_zone_mapping\n", "GROUP BY 1)\n", "\n", "\n", "SELECT DISTINCT b.zone,\n", "                a.*\n", "FROM summary a\n", "LEFT JOIN zone_tbl_treatment b ON a.sender_facility_id = b.facility_id\n", "\"\"\"\n", "\n", "sto_base_df = pd.read_sql(sto_base_query, CON_REDSHIFT)\n", "# sto_base_df[[\"inwd_sku\", \"grn_delivered\"]] = (\n", "#     sto_base_df[[\"inwd_sku\", \"grn_delivered\"]].fillna(0).astype(int)\n", "# )\n", "# sto_base_df = sto_base_df.rename(columns={\"ars_run_id\": \"run_id\"})\n", "\n", "sto_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d31a2ade-8121-494b-987d-bc569fdae799", "metadata": {"tags": []}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dff56774-f433-498f-8a52-bc461fe6b327", "metadata": {}, "outputs": [], "source": ["sto_base_df[sto_base_df[\"receiver_outlet_id\"] == 1407]"]}, {"cell_type": "code", "execution_count": null, "id": "8a7c27ab-eca8-484e-bfb1-be248c683920", "metadata": {}, "outputs": [], "source": ["sto_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "55c60131-164f-41bc-8546-2cdc3aad053f", "metadata": {}, "outputs": [], "source": ["sto_base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8c284dea-acc0-40d2-8a53-e5a3958427c2", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"sto_created_at\"] = pd.to_datetime(sto_base_df[\"sto_created_at\"])\n", "sto_base_df[\"sto_created_hour\"] = sto_base_df[\"sto_created_at\"].dt.hour\n", "sto_base_df[\"sto_created_dt\"] = sto_base_df[\"sto_created_at\"].dt.date\n", "sto_base_df[\"sto_created_minute\"] = sto_base_df[\"sto_created_at\"].dt.minute\n", "sto_base_df[\"sto_created_time\"] = sto_base_df[\"sto_created_at\"].dt.time\n", "\n", "sto_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "fb3f146c-baf7-49d5-a3b1-6d41d901b9a8", "metadata": {}, "outputs": [], "source": ["# sto_base_df_indent = sto_base_df.groupby([\"sto_created_dt\",\n", "#             \"sender_facility_id\",\n", "#             \"sender_outlet_id\",\n", "#             \"sender_facility_name\",\n", "#             \"receiver_facility_id\",\n", "#             \"receiver_outlet_id\",\n", "#             \"receiver_facility_name\"]).agg({'indent':'nunique'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "f27baf38-0364-4237-a196-9fa1a35f9a22", "metadata": {}, "outputs": [], "source": ["# sto_base_df_indent.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa3bfe77-2826-4406-ae9a-4ab1b43a5b18", "metadata": {}, "outputs": [], "source": ["sto_base_df.sender_facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "22d8b9b3-fc55-4390-a507-b5bb78aedbb3", "metadata": {}, "outputs": [], "source": ["# Bengaluru - >=5:30 PM (3)\n", "# Kolkata - >=6:00 PM (517,24)\n", "# <PERSON><PERSON>, G6, G7 - >=03:00 PM (554,555,92)\n", "# Mumbai - >=06:00 PM (513)\n", "# Lucknow - >=05:00 PM (1206)"]}, {"cell_type": "code", "execution_count": null, "id": "3154b925-14c4-4821-b091-8daee26b7b8a", "metadata": {}, "outputs": [], "source": ["sto_base_df[[\"sender_facility_name\", \"sender_facility_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "70887e3c-59ff-4470-8699-22f747d9f008", "metadata": {}, "outputs": [], "source": ["def indent_type(x):\n", "    if x[\"sender_facility_id\"] in [3]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 16:\n", "            return \"Night\"\n", "        elif x[\"sto_created_minute\"] <= 30 and x[\"sto_created_hour\"] == 17:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [517, 24, 513, 554, 555]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 10:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [92]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 14:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [1206]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 16:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [264]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 11:\n", "            return \"Night\"\n", "        elif x[\"sto_created_minute\"] <= 30 and x[\"sto_created_hour\"] == 12:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 18:\n", "        return \"Night\"\n", "    else:\n", "        return \"Day\""]}, {"cell_type": "code", "execution_count": null, "id": "19bedb64-48ea-4b65-abca-83dce2907d4d", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"sto_created_slot\"] = sto_base_df.apply(lambda x: indent_type(x), axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "d6c40230-6fd4-4e69-b665-9dbfca4250ac", "metadata": {}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c38b1ca9-35b0-43f2-9f6a-d5e3f328627e", "metadata": {}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "baa58d1b-284d-48a3-aad2-0012aabc0dde", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"disc_qty\"] = sto_base_df[\"disc_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "8e5ded43-0c3f-4ede-88d7-1d6685939411", "metadata": {}, "outputs": [], "source": ["sto_base_df[sto_base_df.sto_created_slot == \"Day\"].head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f533fee-dadc-431a-bd4d-8b4b53b440ef", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"vehicle_arrival_time\"] = np.where(\n", "    sto_base_df[\"consignment_received_at\"].isnull(),\n", "    sto_base_df[\"grn_created_at\"],\n", "    sto_base_df[\"consignment_received_at\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43bedc98-639b-442e-8887-5c09b275ff5b", "metadata": {}, "outputs": [], "source": ["sto_base_df_new = (\n", "    sto_base_df.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"sto_created_slot\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"vehicle_arrival_time\": \"min\"})\n", "    .reset_index()\n", ")\n", "\n", "sto_base_df_new = sto_base_df_new.rename(\n", "    columns={\"vehicle_arrival_time\": \"vehicle_arrival_time_min\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "16154308-81a3-44ad-b0e3-67274519fe3d", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp = sto_base_df.merge(\n", "    sto_base_df_new,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"sto_created_slot\",\n", "        \"indent\",\n", "    ],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c8302ee-49fe-4e9f-b2ac-6dc625b97a57", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d005c252-6145-4d45-abea-4a2d5d081ba7", "metadata": {}, "outputs": [], "source": ["sto_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e3fa8215-25b3-4d3a-b4df-785bfc0b9228", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "98b32c11-0f7d-48dd-ab80-8a8f4c4f62d6", "metadata": {}, "outputs": [], "source": ["def time_fix(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=6, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=6, minute=0, second=0)\n", "\n", "\n", "def time_fix_night(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=14, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=14, minute=0, second=0)\n", "\n", "\n", "def time_manual_upper(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=6, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=6, minute=0, second=0)\n", "\n", "\n", "def time_manual_lower(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x - <PERSON><PERSON><PERSON>(days=1)).replace(hour=19, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=19, minute=0, second=0)"]}, {"cell_type": "code", "execution_count": null, "id": "264553b4-1a52-4b37-a38c-599f9f1b3106", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"putaway_7pm\"] = sto_base_df_temp[\"vehicle_arrival_time_min\"].apply(\n", "    lambda x: time_manual_lower(x)\n", ")\n", "\n", "sto_base_df_temp[\"putaway_6am\"] = sto_base_df_temp[\"vehicle_arrival_time_min\"].apply(\n", "    lambda x: time_manual_upper(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eebf96c5-d4bb-4b47-904b-ec102cf73de9", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"inward_time_new\"] = sto_base_df_temp[\n", "    \"vehicle_arrival_time_min\"\n", "].apply(lambda x: time_fix_night(x))"]}, {"cell_type": "code", "execution_count": null, "id": "d2035c8e-e30c-4c1d-ae63-e160ae597713", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"vehicle_arrival_time_new\"] = sto_base_df_temp[\n", "    \"vehicle_arrival_time_min\"\n", "].apply(lambda x: time_fix(x))"]}, {"cell_type": "code", "execution_count": null, "id": "4200bf45-ac4c-4896-bddb-a00408863ae0", "metadata": {"tags": []}, "outputs": [], "source": ["## Day Indent\n", "day_stos = sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Day\"]\n", "\n", "days_agg_day_putaway_01 = day_stos[day_stos[\"vehicle_arrival_time_min\"].dt.hour >= 14]\n", "days_agg_day_putaway_02 = day_stos[day_stos[\"vehicle_arrival_time_min\"].dt.hour < 14]\n", "\n", "\n", "days_agg_day_putaway_01_temp = days_agg_day_putaway_01[\n", "    (\n", "        (\n", "            days_agg_day_putaway_01.putaway_time\n", "            >= days_agg_day_putaway_01.vehicle_arrival_time_min\n", "        )\n", "        & (\n", "            days_agg_day_putaway_01.putaway_time\n", "            <= days_agg_day_putaway_01.vehicle_arrival_time_min + timed<PERSON>ta(hours=3)\n", "        )\n", "    )\n", "]\n", "days_agg_day_putaway_02_temp = days_agg_day_putaway_02[\n", "    ((days_agg_day_putaway_02.putaway_time.dt.hour) < 17)\n", "]\n", "\n", "days_agg_day_putaway_temp = pd.concat(\n", "    [days_agg_day_putaway_01_temp, days_agg_day_putaway_02_temp]\n", ")\n", "\n", "day_indents = (\n", "    sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Day\"]\n", "    .groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"disc_qty\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"consignment_received_at\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    # .rename(\n", "    #     columns={\n", "    #         \"sto_id\": \"total_day_indents\",\n", "    #         \"putaway_quantity\": \"total_day_putaway_qty\",\n", "    #         \"grn_created_at\":\"day_grn_created_at\"\n", "    #     }\n", "    # )\n", ")\n", "\n", "days_agg_day_putaway_temp = days_agg_day_putaway_temp.fillna(0)\n", "days_agg_day_putaway = (\n", "    days_agg_day_putaway_temp.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"critical_putaway\"})\n", ")\n", "\n", "days_agg = pd.merge(\n", "    day_indents,\n", "    days_agg_day_putaway,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"indent\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "days_agg[\"indent_type\"] = \"Day\"\n", "\n", "days_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93314bce-9b79-4ad3-8ab7-f422e53ac7db", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "521edfab-42cd-4b61-952c-88872d246136", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "724fe374-9dd3-4760-81c8-28d02dc30d23", "metadata": {}, "outputs": [], "source": ["# night_stos.head()"]}, {"cell_type": "code", "execution_count": null, "id": "adaa4602-e7a5-48ae-9300-3fcfd61587de", "metadata": {}, "outputs": [], "source": ["night_stos = sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Night\"]\n", "night_stos[\"putaway_hour\"] = pd.to_datetime(night_stos[\"putaway_time\"]).dt.hour\n", "night_agg = (\n", "    night_stos.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"disc_qty\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"consignment_received_at\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    # .rename(\n", "    #     columns={\n", "    #         \"putaway_quantity\": \"night_putaway\",\n", "    #         \"sto_id\": \"total_night_indents\",\n", "    #         \"total_billed_qty\": \"night_billed_quantity\",\n", "    #         \"grn_created_at\":\"night_grn_created_at\"\n", "    #     }\n", "    # )\n", ")\n", "\n", "\n", "days_agg_night_putaway_01 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.vehicle_arrival_time_min)\n", "    & (night_stos.putaway_time <= night_stos.vehicle_arrival_time_new)\n", "]\n", "days_agg_night_putaway_02 = pd.DataFrame()\n", "# days_agg_night_putaway_02 = night_stos[(night_stos.putaway_hour >= 19)]\n", "\n", "days_agg_night_putaway_03 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.vehicle_arrival_time_min)\n", "    & (night_stos.putaway_time <= night_stos.inward_time_new)\n", "]\n", "\n", "days_agg_night_putaway_04 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.putaway_7pm)\n", "    & (night_stos.putaway_time <= night_stos.putaway_6am)\n", "]\n", "days_agg_night_putaway_05 = pd.DataFrame()\n", "# night_stos[(night_stos.putaway_time <= night_stos.putaway_6am)]\n", "# night_stos[(night_stos.putaway_hour >= 19)]\n", "\n", "days_agg_night_putaway_06 = pd.concat(\n", "    [days_agg_night_putaway_04, days_agg_night_putaway_05]\n", ")\n", "\n", "days_agg_night_putaway = pd.concat(\n", "    [days_agg_night_putaway_01, days_agg_night_putaway_02]\n", ")\n", "days_agg_night_putaway = days_agg_night_putaway.fillna(0)\n", "days_agg_night_putaway = (\n", "    days_agg_night_putaway.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"critical_putaway\"})\n", ")\n", "\n", "\n", "days_agg_night_putaway_03 = days_agg_night_putaway_03.fillna(0)\n", "days_agg_night_putaway1 = (\n", "    days_agg_night_putaway_03.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"grn_delivered\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"grn_delivered\": \"inward_qty_till_2pm\"})\n", ")\n", "\n", "\n", "days_agg_night_putaway_06 = days_agg_night_putaway_06.fillna(0)\n", "days_agg_night_putaway2 = (\n", "    days_agg_night_putaway_06.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"putaway_quantity_overall_7pm_till_6am\"})\n", ")\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway2,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"indent\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway1,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"indent\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"indent\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "night_agg = night_agg.fillna(0)\n", "\n", "# night_agg[\"putaway_share_night\"] = np.where(\n", "#     night_agg[\"putaway_quantity\"] == 0,\n", "#     0,\n", "#     night_agg[\"critical_putaway\"] / night_agg[\"putaway_quantity\"],\n", "# )\n", "\n", "night_agg[\"indent_type\"] = \"Night\""]}, {"cell_type": "code", "execution_count": null, "id": "ea5fa5e0-eb83-4a6e-bb8d-c7d08f9bd084", "metadata": {}, "outputs": [], "source": ["days_agg_night_putaway2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eeb25617-9ac5-41dd-aae1-4669ec709dbd", "metadata": {}, "outputs": [], "source": ["night_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "faaa9c0e-721d-49d8-9a46-92c797dfa417", "metadata": {}, "outputs": [], "source": ["days_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "67b68b62-2adf-4399-a331-5842b8cf0630", "metadata": {}, "outputs": [], "source": ["days_agg[\"inward_qty_till_12pm\"] = 0\n", "days_agg[\"putaway_quantity_overall_7pm_till_6am\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "34949a1b-40be-4363-b6a7-59b11b7dd3ea", "metadata": {}, "outputs": [], "source": ["overall_data = pd.concat([days_agg, night_agg])"]}, {"cell_type": "code", "execution_count": null, "id": "7949f7f4-707b-467b-8518-2464a48898ae", "metadata": {}, "outputs": [], "source": ["overall_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "968efb49-760d-4249-92e0-9bfddca52acf", "metadata": {}, "outputs": [], "source": ["overall_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e1b646d9-ebf6-47fd-9171-066e39c9ecc9", "metadata": {}, "outputs": [], "source": ["overall_data = overall_data[\n", "    ~overall_data[\"sender_facility_id\"].isin([24, 1111, 906, 366])\n", "]\n", "overall_data = overall_data[overall_data[\"total_billed_qty\"] >= 100]"]}, {"cell_type": "code", "execution_count": null, "id": "2b92ad03-6d00-4968-97a6-e418484fadbb", "metadata": {}, "outputs": [], "source": ["overall_data[\n", "    (overall_data[\"receiver_outlet_id\"] == 2398)\n", "    & (overall_data[\"sto_created_dt\"] == date(2022, 5, 13))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "53aeb0cc-ea2f-4cb9-af75-9c42e97db849", "metadata": {}, "outputs": [], "source": ["overall_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8013d55f-7044-40cc-94ba-eefaa341e832", "metadata": {}, "outputs": [], "source": ["# overall_data.iloc[500:550]"]}, {"cell_type": "code", "execution_count": null, "id": "b76b5f67-3f10-42bc-a6d0-65e70d00b719", "metadata": {}, "outputs": [], "source": ["city_type_mapping = pb.from_sheets(\n", "    \"1OKYLmukapOsGC7kXU66K2leXdNFs_zRo25QdPrqSaGo\", \"Master\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fac6f7d3-026f-46fe-9402-e2cab955c63a", "metadata": {}, "outputs": [], "source": ["city_type_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "37ba46c4-a8c9-46d0-95e0-678a99fed728", "metadata": {}, "outputs": [], "source": ["city_type_mapping.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e2f89a8a-0b46-4985-bda7-ed7fa8db1b24", "metadata": {}, "outputs": [], "source": ["city_type_mapping = city_type_mapping[[\"Outlet ID\", \"Operation Mode\", \"City Name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "0637a223-7f9f-4335-9796-d40fa3d7e59e", "metadata": {}, "outputs": [], "source": ["city_type_mapping[\"Outlet ID\"] = city_type_mapping[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a62a1768-dd68-4bf0-a5c9-eba43e77e555", "metadata": {}, "outputs": [], "source": ["overall_data_temp = overall_data.merge(\n", "    city_type_mapping,\n", "    left_on=[\"receiver_outlet_id\"],\n", "    right_on=[\"Outlet ID\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "584c6cf1-a923-4667-8100-7337169a9926", "metadata": {}, "outputs": [], "source": ["overall_data_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4ff1ec07-cebc-4e25-8e56-96ee730e6137", "metadata": {}, "outputs": [], "source": ["overall_data_temp = overall_data_temp[overall_data_temp[\"City Name\"].isin(city_list)]"]}, {"cell_type": "code", "execution_count": null, "id": "ea59de44-8a05-4400-9677-52a2f3deacb9", "metadata": {}, "outputs": [], "source": ["overall_data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "433e3be7-f4f7-4eba-939d-7e09a0cdad5d", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"DS Compliance\"] = (\n", "    overall_data_temp[\"critical_putaway\"]\n", "    * 100\n", "    / (overall_data_temp[\"inward_qty_till_2pm\"])\n", ")\n", "\n", "# overall_data_temp[\"DS Compliance Billed\"] = (\n", "#     overall_data_temp[\"critical_putaway\"] * 100 / (overall_data_temp[\"total_billed_qty\"])\n", "# )\n", "\n", "# overall_data_temp[\"DS Compliance\"] = (\n", "#     overall_data_temp[\"critical_putaway\"] * 100 / (overall_data_temp[\"grn_delivered\"])\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "e278dc45-045f-4f56-ada7-69c362b8b308", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b66fea0d-9568-45ed-8bc8-76ec56d14c56", "metadata": {}, "outputs": [], "source": ["def comp_bucket(x):\n", "    if x >= 95:\n", "        return \">95\"\n", "    elif x < 95 and x >= 80:\n", "        return \"80-95\"\n", "    elif x < 80:\n", "        return \"<80\"\n", "    else:\n", "        return \"0\""]}, {"cell_type": "code", "execution_count": null, "id": "d5718da0-3652-4597-8866-34d0f20e75eb", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"Compliance Bucket\"] = overall_data_temp[\"DS Compliance\"].apply(\n", "    lambda x: comp_bucket(x)\n", ")\n", "\n", "\n", "# overall_data_temp[\"Compliance Bucket till 12pm\"] = overall_data_temp[\"DS Compliance Till 12 pm\"].apply(\n", "#     lambda x: comp_bucket(x)\n", "# )\n", "\n", "# overall_data_temp[\"Compliance Bucket Billed\"] = overall_data_temp[\"DS Compliance Billed\"].apply(\n", "#     lambda x: comp_bucket(x)\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "fc20daa1-d096-4dd0-971a-79449b4eb36e", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f067cd0f-554b-4d6c-81c9-aff92e95fb54", "metadata": {}, "outputs": [], "source": ["overall_data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "700faf53-da17-42d6-af0e-4f22bae92111", "metadata": {}, "outputs": [], "source": ["overall_data_temp1 = (\n", "    overall_data_temp.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"City Name\",\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent_type\",\n", "            \"indent\",\n", "        ]\n", "    )\n", "    .agg({\"total_billed_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_data_temp2 = overall_data_temp1[overall_data_temp1[\"total_billed_qty\"] > 100]\n", "\n", "\n", "overall_data_temp2 = overall_data_temp2[overall_data_temp2[\"sender_facility_id\"] != 24]"]}, {"cell_type": "code", "execution_count": null, "id": "14186e6a-3bb0-4e85-b3de-073cf624e55d", "metadata": {}, "outputs": [], "source": ["overall_data_final = (\n", "    overall_data_temp2.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"City Name\",\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_facility_name\",\n", "            \"indent_type\",\n", "        ]\n", "    )\n", "    .agg({\"total_billed_qty\": \"sum\", \"indent\": \"nunique\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0129f1d9-fe63-4d7e-b69f-97ecd1096e21", "metadata": {}, "outputs": [], "source": ["overall_data_final[\"day_indent\"] = np.where(\n", "    overall_data_final[\"indent_type\"] == \"Day\",\n", "    overall_data_final[\"total_billed_qty\"],\n", "    0,\n", ")\n", "overall_data_final[\"night_indent\"] = np.where(\n", "    overall_data_final[\"indent_type\"] == \"Night\",\n", "    overall_data_final[\"total_billed_qty\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8110360-1fed-4f96-9b2b-75c987124770", "metadata": {}, "outputs": [], "source": ["overall_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e31ae133-5687-4096-8472-0260fbc8737e", "metadata": {}, "outputs": [], "source": ["# final_df1_raw[(final_df1_raw['receiver_facility_id']==1063) & (final_df1_raw['sto_created_dt']==date(2022,5,18))]"]}, {"cell_type": "code", "execution_count": null, "id": "8a2f2d60-d123-47e6-b30e-63efd5f6865b", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(overall_data_final[(overall_data_final['sender_facility_id']==264) & (overall_data_final['sto_created_dt']==date(2022,5,18))],sheet_id,'Sheet5',clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "74a7d7ee-4a5b-4c4c-8e65-10b10e2572fe", "metadata": {}, "outputs": [], "source": ["# overall_data_final_overall[(overall_data_final_overall['receiver_facility_id']==1033) & (overall_data_final_overall['sto_created_dt']==date(2022,5,18))]"]}, {"cell_type": "code", "execution_count": null, "id": "c6f90614-c412-4326-9134-26a008f9fd21", "metadata": {}, "outputs": [], "source": ["# overall_data_final['day_indent'] = np.where(overall_data_final['indent_type']=='Day',1,0)\n", "# overall_data_final['night_indent'] = np.where(overall_data_final['indent_type']=='Night',1,0)"]}, {"cell_type": "code", "execution_count": null, "id": "89fdeffe-fba2-4df8-974e-a0ba1714d43d", "metadata": {}, "outputs": [], "source": ["overall_data_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "0236ae79-7338-4771-84f9-6d35cd99d4c7", "metadata": {}, "outputs": [], "source": ["final_df1_raw = (\n", "    overall_data_final.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"City Name\",\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"indent\": \"sum\", \"day_indent\": \"sum\", \"night_indent\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "729d363a-b3b4-4f12-b3bc-d2e93e08e579", "metadata": {}, "outputs": [], "source": ["final_df1_raw.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "6eb90282-4b89-4809-b39e-c4306818cd0b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2c9928eb-5b8e-4083-b9c1-6e502dc54379", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall = (\n", "    overall_data_final.groupby([\"sto_created_dt\", \"receiver_facility_id\"])\n", "    .agg({\"day_indent\": \"sum\", \"night_indent\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f8787531-1a4c-429e-b26a-962aad9d70c9", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c0f12eed-a9dc-48a9-8caa-df7ef46094a5", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall[\"night_indent_per\"] = (\n", "    overall_data_final_overall[\"night_indent\"]\n", "    * 100\n", "    / (\n", "        overall_data_final_overall[\"night_indent\"]\n", "        + overall_data_final_overall[\"day_indent\"]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9871b0cc-db05-47e2-89b2-7cd8563c0fbb", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f47912fc-aad8-4a69-afcb-bd7d4351a440", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall[\"replenish_freq\"] = np.where(\n", "    (overall_data_final_overall[\"day_indent\"] > 0)\n", "    & (overall_data_final_overall[\"night_indent\"] > 0),\n", "    \"double\",\n", "    \"single\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ed06cde-31fc-4fc6-af04-abb260602678", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall.head()"]}, {"cell_type": "code", "execution_count": null, "id": "44ab5bf8-6dd9-4df1-bd2a-9343d2e0707a", "metadata": {}, "outputs": [], "source": ["data1 = (\n", "    overall_data_final_overall.groupby([\"sto_created_dt\", \"replenish_freq\"])\n", "    .agg({\"receiver_facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "data1 = data1.rename(columns={\"receiver_facility_id\": \"No of Stores\"})"]}, {"cell_type": "code", "execution_count": null, "id": "437b8567-3e83-466b-991e-060d1d0952e6", "metadata": {}, "outputs": [], "source": ["data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f3e3c732-3c74-4c0f-9866-479d2ed8b199", "metadata": {}, "outputs": [], "source": ["def night_indent_bucket(x):\n", "    if x > 70:\n", "        return \">70%\"\n", "    elif x >= 60 and x <= 70:\n", "        return \"60%-70%\"\n", "    elif x < 60:\n", "        return \"<60%\""]}, {"cell_type": "code", "execution_count": null, "id": "0e1eff93-6b3b-4cac-93dc-0c0e4cbde1f6", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall[\"flag\"] = overall_data_final_overall[\n", "    \"night_indent_per\"\n", "].apply(lambda x: night_indent_bucket(x))"]}, {"cell_type": "code", "execution_count": null, "id": "51b99193-602b-47e9-b749-589663001d3e", "metadata": {}, "outputs": [], "source": ["overall_data_final_overall.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7da9e37a-cb34-41dd-8238-bf122af01242", "metadata": {}, "outputs": [], "source": ["data2 = (\n", "    overall_data_final_overall[overall_data_final_overall[\"replenish_freq\"] == \"double\"]\n", "    .groupby([\"sto_created_dt\", \"flag\", \"replenish_freq\"])\n", "    .agg({\"receiver_facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "data2 = data2.rename(columns={\"receiver_facility_id\": \"Night Stores\"})"]}, {"cell_type": "code", "execution_count": null, "id": "42bc5d67-2978-41ca-b0e9-0222b208f361", "metadata": {}, "outputs": [], "source": ["data2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "84b5fee4-65d1-4cdb-9f4e-840171565502", "metadata": {}, "outputs": [], "source": ["data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "34dd4ab6-8f75-4cb8-a368-5db44b033496", "metadata": {}, "outputs": [], "source": ["final_df1 = data1.merge(data2, on=[\"sto_created_dt\", \"replenish_freq\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b5551dc-f17d-4f21-a92d-ab5df8cb3be7", "metadata": {}, "outputs": [], "source": ["final_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "365675eb-c70d-4a2e-9b98-c2c18e78e207", "metadata": {}, "outputs": [], "source": ["# overall_data_temp1 = overall_data_temp.groupby(['sto_created_dt','City Name','sender_facility_id','sender_facility_name','receiver_facility_id','indent_type','indent']).agg({'total_billed_qty':'sum'}).reset_index()\n", "# overall_data_final_new = overall_data_temp1[overall_data_temp1['total_billed_qty']>100]\n", "\n", "\n", "# overall_data_final_new =  overall_data_final_new[overall_data_final_new['sender_facility_id']!=24]"]}, {"cell_type": "code", "execution_count": null, "id": "80f70652-50aa-4058-9a09-fd57ddf58824", "metadata": {}, "outputs": [], "source": ["# overall_data_final_new.head()\n", "overall_data_final_new = overall_data_temp2.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "cdae625b-052b-4585-9a64-4f74bb6da422", "metadata": {}, "outputs": [], "source": ["overall_data_final_new[\"day_indent\"] = np.where(\n", "    overall_data_final_new[\"indent_type\"] == \"Day\",\n", "    overall_data_final_new[\"total_billed_qty\"],\n", "    0,\n", ")\n", "overall_data_final_new[\"night_indent\"] = np.where(\n", "    overall_data_final_new[\"indent_type\"] == \"Night\",\n", "    overall_data_final_new[\"total_billed_qty\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "02b69c5c-7a4a-40d6-9f49-328d106044b2", "metadata": {}, "outputs": [], "source": ["overall_data_final_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ead40bfd-cc10-4dc9-8b18-18971fd40188", "metadata": {}, "outputs": [], "source": ["# final_df2_raw =\n", "# final_df2_raw = overall_data_final_new.groupby(['sto_created_dt', 'City Name', 'sender_facility_id',\n", "#        'sender_facility_name', 'receiver_facility_id',\n", "#        'receiver_facility_name']).agg({'indent':'sum','day_indent':'sum','night_indent':'sum'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "d0d0f71e-0686-4d99-b417-8ba1434c5f8f", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new = (\n", "    overall_data_final_new.groupby(\n", "        [\"sto_created_dt\", \"sender_facility_name\", \"receiver_facility_id\"]\n", "    )\n", "    .agg({\"day_indent\": \"sum\", \"night_indent\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47ff6052-e0e4-4109-a856-2b9ef05de293", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bdc877a6-3ccc-4ab8-ad7f-a6f3ba2268b2", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new = overall_data_final_new_new[\n", "    (overall_data_final_new_new[\"day_indent\"] > 0)\n", "    & (overall_data_final_new_new[\"night_indent\"] > 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4e8d5eb8-c817-4173-927d-de0c31fd4491", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new[\"night_indent_per\"] = (\n", "    overall_data_final_new_new[\"night_indent\"]\n", "    * 100\n", "    / (\n", "        overall_data_final_new_new[\"day_indent\"]\n", "        + overall_data_final_new_new[\"night_indent\"]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "abb88a5c-ee29-4a7d-aa9c-da799657e294", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "88a2db13-16fe-43c8-ba5c-baca14ef0598", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new[\"flag\"] = overall_data_final_new_new[\n", "    \"night_indent_per\"\n", "].apply(lambda x: night_indent_bucket(x))"]}, {"cell_type": "code", "execution_count": null, "id": "3b2fd587-716a-4c22-9c2b-e98129b4c53f", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7ff78275-d287-4ef1-8d78-789ed71c4b1f", "metadata": {}, "outputs": [], "source": ["final_df2 = (\n", "    overall_data_final_new_new.groupby(\n", "        [\"sto_created_dt\", \"sender_facility_name\", \"flag\"]\n", "    )\n", "    .agg({\"receiver_facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "final_df2 = final_df2.rename(columns={\"receiver_facility_id\": \"Stores\"})"]}, {"cell_type": "code", "execution_count": null, "id": "af64e133-24df-4255-beaf-300412148e30", "metadata": {}, "outputs": [], "source": ["final_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c26f3f65-e26b-4528-b399-a456c0a8ee66", "metadata": {}, "outputs": [], "source": ["overall_data_temp.columns"]}, {"cell_type": "markdown", "id": "76826e07-880a-4fcc-8ed7-acb14cebc3c7", "metadata": {}, "source": ["# Vehicle Compliance"]}, {"cell_type": "code", "execution_count": null, "id": "7a2b5cc6-b9a3-44bb-80cb-6741fe467a7d", "metadata": {}, "outputs": [], "source": ["def indent_arrival_night(x):\n", "    try:\n", "        if (x[\"vehicle_arrival_time\"].date() == x[\"sto_created_dt\"]) and x[\n", "            \"vehicle_arrival_time\"\n", "        ].hour < 23:\n", "            return \"Yes\"\n", "        else:\n", "            return \"No\"\n", "    except:\n", "        return \"No\"\n", "\n", "\n", "def indent_arrival_day(x):\n", "    try:\n", "        if (\n", "            (x[\"vehicle_arrival_time\"].date() == x[\"sto_created_dt\"])\n", "            or (\n", "                x[\"vehicle_arrival_time\"].date()\n", "                == (x[\"sto_created_dt\"] + <PERSON><PERSON><PERSON>(days=1))\n", "            )\n", "        ) and x[\"vehicle_arrival_time\"].hour < 13:\n", "            return \"Yes\"\n", "        else:\n", "            return \"No\"\n", "    except:\n", "        return \"No\""]}, {"cell_type": "code", "execution_count": null, "id": "672afd9c-f532-4522-bb1a-a9c9a4ade2c1", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"night_indent_arrived_before_11pm\"] = overall_data_temp[\n", "    overall_data_temp[\"indent_type\"] == \"Night\"\n", "].apply(lambda x: indent_arrival_night(x), axis=1)\n", "\n", "overall_data_temp[\"day_indent_arrived_before_1pm\"] = overall_data_temp[\n", "    overall_data_temp[\"indent_type\"] == \"Day\"\n", "].apply(lambda x: indent_arrival_day(x), axis=1)\n", "# 'Yes' if (x['vehicle_arrival_time'].date()==x['sto_created_dt']) and x['vehicle_arrival_time'].hour<23 else 'No',axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "5231f54f-e602-408d-b461-41ed13701544", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8f4b14f3-ec9f-4463-ba76-5d2291b54d56", "metadata": {}, "outputs": [], "source": ["non_compliant_stores = pb.from_sheets(\n", "    \"1OKYLmukapOsGC7kXU66K2leXdNFs_zRo25QdPrqSaGo\",\n", "    \"Non-compliant Stores\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1cf92701-f15f-45fb-8c22-376cf317da01", "metadata": {}, "outputs": [], "source": ["non_compliant_stores.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c0442486-a5d2-4331-a4cc-5115d7509ccd", "metadata": {}, "outputs": [], "source": ["non_compliant_stores = non_compliant_stores.rename(\n", "    columns={\"outlet\": \"receiver_outlet_id\"}\n", ")\n", "non_compliant_stores[\"receiver_outlet_id\"] = non_compliant_stores[\n", "    \"receiver_outlet_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0e55e5f5-326e-4937-8d56-1d054d648650", "metadata": {}, "outputs": [], "source": ["final_df3_temp = overall_data_temp.merge(\n", "    non_compliant_stores, on=[\"receiver_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9d460e81-91ed-4484-888d-a6133a618121", "metadata": {}, "outputs": [], "source": ["final_df3_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "221f23d0-d1ba-4893-b1da-15d8542adb6b", "metadata": {}, "outputs": [], "source": ["final_df3_temp[\"Compliant Flag\"] = np.where(\n", "    final_df3_temp[\"flag\"].isin([\"N\", \"GPOS\"]), \"No\", \"Yes\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "940454f2-1ad9-4ef6-a85a-eda11be72b22", "metadata": {}, "outputs": [], "source": ["final_df3_temp = final_df3_temp[final_df3_temp[\"Compliant Flag\"] == \"Yes\"]"]}, {"cell_type": "code", "execution_count": null, "id": "20916aeb-c7af-4382-8dcc-93342a1df11e", "metadata": {}, "outputs": [], "source": ["final_df3_temp[\"day_arrival_no\"] = np.where(\n", "    final_df3_temp[\"day_indent_arrived_before_1pm\"] == \"No\", 1, 0\n", ")\n", "final_df3_temp[\"day_arrival_yes\"] = np.where(\n", "    final_df3_temp[\"day_indent_arrived_before_1pm\"] == \"Yes\", 1, 0\n", ")\n", "\n", "\n", "final_df3_temp[\"night_arrival_no\"] = np.where(\n", "    final_df3_temp[\"night_indent_arrived_before_11pm\"] == \"No\", 1, 0\n", ")\n", "final_df3_temp[\"night_arrival_yes\"] = np.where(\n", "    final_df3_temp[\"night_indent_arrived_before_11pm\"] == \"Yes\", 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2cf1aeaf-6f35-41bc-9cc7-445c51252959", "metadata": {}, "outputs": [], "source": ["final_df3_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a29b8636-17a9-48d5-8093-1ed2295cda12", "metadata": {}, "outputs": [], "source": ["final_df3_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "87d09974-255d-4d87-9bbf-29afed4f117a", "metadata": {}, "outputs": [], "source": ["final_df3_raw = final_df3_temp[\n", "    [\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"indent\",\n", "        \"sto_id\",\n", "        \"total_billed_qty\",\n", "        \"putaway_quantity\",\n", "        \"grn_delivered\",\n", "        \"disc_qty\",\n", "        \"grn_created_at\",\n", "        \"putaway_time\",\n", "        \"vehicle_arrival_time\",\n", "        \"consignment_received_at\",\n", "        \"critical_putaway\",\n", "        \"indent_type\",\n", "        \"inward_qty_till_2pm\",\n", "        \"Operation Mode\",\n", "        \"City Name\",\n", "        \"DS Compliance\",\n", "        \"Compliance Bucket\",\n", "        \"night_indent_arrived_before_11pm\",\n", "        \"day_indent_arrived_before_1pm\",\n", "        \"Compliant Flag\",\n", "        \"day_arrival_no\",\n", "        \"day_arrival_yes\",\n", "        \"night_arrival_no\",\n", "        \"night_arrival_yes\",\n", "    ]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "ea7b8d4d-64d9-4ff2-965c-2065fd968a83", "metadata": {}, "outputs": [], "source": ["final_df3 = (\n", "    final_df3_temp[~final_df3_temp[\"sender_facility_id\"].isin([24, 1111, 906, 366])]\n", "    .groupby([\"sto_created_dt\", \"City Name\"])\n", "    .agg(\n", "        {\n", "            \"day_arrival_no\": \"sum\",\n", "            \"day_arrival_yes\": \"sum\",\n", "            \"night_arrival_no\": \"sum\",\n", "            \"night_arrival_yes\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c8d2316b-f1b0-4c86-94a9-e2cf502d903c", "metadata": {}, "outputs": [], "source": ["final_df3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "24cc4e9d-99aa-4025-bde8-793b6479324c", "metadata": {}, "outputs": [], "source": ["final_df3[\"%day_arrival\"] = (\n", "    final_df3[\"day_arrival_yes\"]\n", "    * 100.0\n", "    / (final_df3[\"day_arrival_yes\"] + final_df3[\"day_arrival_no\"])\n", ")\n", "final_df3[\"%night_arrival\"] = (\n", "    final_df3[\"night_arrival_yes\"]\n", "    * 100.0\n", "    / (final_df3[\"night_arrival_yes\"] + final_df3[\"night_arrival_no\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e3661de-afbd-4c0b-9362-501a16baf126", "metadata": {}, "outputs": [], "source": ["final_df3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "096f7b62-09ac-412b-bd5c-d3df8c6b241d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9414c2d4-e326-4073-b06c-75cfcb964a44", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0043a8ca-8cef-429e-88d7-3eaa0d285db7", "metadata": {}, "source": ["# DS Compliance"]}, {"cell_type": "code", "execution_count": null, "id": "1d24bb56-4d7c-409c-a069-3b6f7eb45eeb", "metadata": {}, "outputs": [], "source": ["overall_data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "5b66b914-14f6-4b39-bae4-7822a0366c92", "metadata": {}, "outputs": [], "source": ["overall_data_temp3 = (\n", "    overall_data_temp.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"City Name\",\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"indent_type\",\n", "            \"indent\",\n", "            \"Compliance Bucket\",\n", "        ]\n", "    )\n", "    .agg({\"total_billed_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_data_final_new_temp2 = overall_data_temp3[\n", "    overall_data_temp3[\"total_billed_qty\"] > 100\n", "]\n", "\n", "\n", "overall_data_final_new_temp2 = overall_data_final_new_temp2[\n", "    ~overall_data_final_new_temp2[\"sender_facility_id\"].isin([24, 1111, 906, 366])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "702db07c-8c57-413e-893b-e078d7db20db", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_temp2[\"receiver_facility_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "24fe73d5-62cc-4e6c-a87b-7abf9cdfffba", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_temp2[\"sto_created_dt\"] = overall_data_final_new_temp2[\n", "    \"sto_created_dt\"\n", "].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "2e7ba66a-2ad4-4d8d-b328-ee5ffdd9369d", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_temp2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "777358dd-0307-443c-a6da-68e16c58026f", "metadata": {}, "outputs": [], "source": ["overall_data_final_new_temp2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "180f6a5d-f9a8-43d3-a359-da455c6b16fc", "metadata": {}, "outputs": [], "source": ["final_df4_temp = overall_data_final_new_temp2.merge(\n", "    non_compliant_stores, on=[\"receiver_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43cda4bb-8412-44a4-9e19-9d87a22751e0", "metadata": {}, "outputs": [], "source": ["final_df4_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e13fc3d1-52d6-49e7-8b95-9af609574e5e", "metadata": {}, "outputs": [], "source": ["final_df4_temp[\"Compliant Flag\"] = np.where(\n", "    final_df4_temp[\"flag\"].isin([\"N\", \"GPOS\"]), \"No\", \"Yes\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "803387f5-5d14-47ac-88f7-93f9272752d6", "metadata": {}, "outputs": [], "source": ["final_df4_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9efc2f25-38ae-4ecf-80e2-01acf21eee6a", "metadata": {}, "outputs": [], "source": ["final_df4_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "51096a9d-5ed9-4bb7-b576-9e5e6d0edf10", "metadata": {}, "outputs": [], "source": ["final_df4_temp = final_df4_temp[final_df4_temp[\"total_billed_qty\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "1b079ecc-b4b1-4abc-9480-69a8fb46a6a1", "metadata": {}, "outputs": [], "source": ["final_df4_temp = final_df4_temp[final_df4_temp[\"Compliant Flag\"] == \"Yes\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0d146329-43a1-4964-ac75-0b22453376e5", "metadata": {}, "outputs": [], "source": ["final_df4_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "56471101-f77c-485d-820e-1790d24df7be", "metadata": {}, "outputs": [], "source": ["final_df4_raw = final_df4_temp.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "755b78e6-f2bb-49b9-a3a0-1346506811cf", "metadata": {}, "outputs": [], "source": ["final_df4 = (\n", "    final_df4_temp[final_df4_temp[\"indent_type\"] == \"Night\"]\n", "    .groupby([\"sto_created_dt\", \"City Name\", \"Compliance Bucket\"])\n", "    .agg({\"receiver_facility_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "# final_df4_1 = final_df4_temp[final_df4_temp['indent_type']=='Night'].groupby(['sto_created_dt','City Name','Compliance Bucket till 12pm']).agg({'receiver_facility_id':'nunique'}).reset_index()\n", "# final_df4_2 = final_df4_temp[final_df4_temp['indent_type']=='Night'].groupby(['sto_created_dt','City Name','Compliance Bucket Billed']).agg({'receiver_facility_id':'nunique'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "9d0df479-8c9e-4775-b547-9217218bc3ae", "metadata": {}, "outputs": [], "source": ["final_df4 = final_df4.rename(columns={\"receiver_facility_id\": \"Stores\"})\n", "# final_df4_1 = final_df4_1.rename(columns={'receiver_facility_id':'Stores'})\n", "# final_df4_2 = final_df4_2.rename(columns={'receiver_facility_id':'Stores'})"]}, {"cell_type": "code", "execution_count": null, "id": "01c7047f-0cf3-4e64-857b-58e830ca80a6", "metadata": {}, "outputs": [], "source": ["final_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fbf263b9-70ae-4ad4-b2ea-79408a73752e", "metadata": {}, "outputs": [], "source": ["final_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c883d958-26fd-47d7-92e3-ac4a2c7b0484", "metadata": {}, "outputs": [], "source": ["final_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e5e6fd90-04cf-4019-9533-0489c521772c", "metadata": {}, "outputs": [], "source": ["final_df3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d342fec8-09d6-4fe3-9867-927573887df7", "metadata": {}, "outputs": [], "source": ["final_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6777ce35-cf1a-4331-b1ca-f93388b60743", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1-eCZFxWI8-hOr5w-MrfKgsgJjLSBQvQtuqDqiBidvEc\""]}, {"cell_type": "code", "execution_count": null, "id": "25d0f0bc-afa9-4ef4-b9c2-ac0095b82875", "metadata": {}, "outputs": [], "source": ["final_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4bd32bf4-0928-4068-8399-ad9b7c00db51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9abcc3bd-9892-4998-b22a-0f7453ca25d1", "metadata": {}, "outputs": [], "source": ["final_df1[\"Night Stores\"] = np.where(\n", "    final_df1[\"Night Stores\"].isnull(),\n", "    final_df1[\"No of Stores\"],\n", "    final_df1[\"Night Stores\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "03a5b0a4-9198-49ec-8d52-e93409a15ad9", "metadata": {}, "outputs": [], "source": ["final_df1[\"flag\"] = final_df1[\"flag\"].fillna(\"NA\")"]}, {"cell_type": "code", "execution_count": null, "id": "1a0a89f4-b91b-4ae9-b297-8c9868343c9c", "metadata": {}, "outputs": [], "source": ["final_df1_pivot = final_df1.pivot_table(\n", "    index=[\"sto_created_dt\", \"replenish_freq\"],\n", "    columns=[\"flag\"],\n", "    values=[\"Night Stores\"],\n", "    sort=True,\n", "    dropna=False,\n", ").reset_index()\n", "\n", "final_df1_pivot.columns = [\n", "    \" \".join(col).strip() for col in final_df1_pivot.columns.values\n", "]\n", "\n", "\n", "final_df2_pivot = final_df2.pivot_table(\n", "    index=[\"sto_created_dt\", \"sender_facility_name\"],\n", "    columns=[\"flag\"],\n", "    values=[\"Stores\"],\n", "    sort=True,\n", "    dropna=False,\n", ").reset_index()\n", "\n", "final_df2_pivot.columns = [\n", "    \" \".join(col).strip() for col in final_df2_pivot.columns.values\n", "]\n", "\n", "\n", "# final_df2_pivot = final_df2.pivot_table(index=['sto_created_dt','sender_facility_name'],columns=['flag'],values=['Stores'],sort=True,dropna=False).reset_index()\n", "\n", "# final_df2_pivot.columns = [' '.join(col).strip() for col in final_df2_pivot.columns.values]"]}, {"cell_type": "code", "execution_count": null, "id": "b9332546-3cc5-4cdf-9a11-0a26eb414873", "metadata": {}, "outputs": [], "source": ["final_df1_pivot = final_df1_pivot.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "01f1ef2a-3dae-4d8f-bd40-eab09016780c", "metadata": {}, "outputs": [], "source": ["final_df1_pivot[\"Night Stores NA\"] = final_df1_pivot.apply(\n", "    lambda x: (\n", "        x[\"Night Stores 60%-70%\"]\n", "        + x[\"Night Stores <60%\"]\n", "        + x[\"Night Stores >70%\"]\n", "        + x[\"Night Stores NA\"]\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9971230c-b785-454d-9efb-c4ceb272851c", "metadata": {}, "outputs": [], "source": ["final_df1_pivot = final_df1_pivot.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created Date\",\n", "        \"replenish_freq\": \"Replenish Frequency\",\n", "        \"Night Stores 60%-70%\": \"60%-70% Night Indent Compliance\",\n", "        \"Night Stores <60%\": \"<60% Night Indent Compliance\",\n", "        \"Night Stores >70%\": \">70% Night Indent Compliance\",\n", "        \"Night Stores NA\": \"Total Stores\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f7158903-142e-4b88-9d2a-a8253d8f406b", "metadata": {}, "outputs": [], "source": ["final_df2_pivot = final_df2_pivot.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created Date\",\n", "        \"sender_facility_name\": \"Sender Facility Name\",\n", "        \"Stores 60%-70%\": \"60%-70% Night Indent Compliance\",\n", "        \"Stores <60%\": \"<60% Night Indent Compliance\",\n", "        \"Stores >70%\": \">70% Night Indent Compliance\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0e3bc369-936d-45e2-941b-c1d6f3a4cea3", "metadata": {}, "outputs": [], "source": ["final_df2_pivot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f3547307-8aa6-4ba5-b0aa-ce6333f29a0f", "metadata": {}, "outputs": [], "source": ["final_df2_pivot = final_df2_pivot.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "a56f84ae-941d-4007-8ed1-d91006ec5caf", "metadata": {}, "outputs": [], "source": ["final_df3 = final_df3.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created Date\",\n", "        \"day_arrival_no\": \"Day Indent After 1 pm\",\n", "        \"day_arrival_yes\": \"Day Indent Before 1 pm\",\n", "        \"night_arrival_no\": \"Night Indent After 11 pm\",\n", "        \"night_arrival_yes\": \"Night Indent Before 11 pm\",\n", "        \"%day_arrival\": \"% Day Arrival Adherence\",\n", "        \"%night_arrival\": \"% Night Arrival Adherence\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bedc070e-15a8-4987-b497-58eabf9a1bd5", "metadata": {}, "outputs": [], "source": ["final_df4_pivot = final_df4.pivot_table(\n", "    index=[\"sto_created_dt\", \"City Name\"],\n", "    columns=[\"Compliance Bucket\"],\n", "    values=[\"Stores\"],\n", "    sort=True,\n", "    dropna=False,\n", ").reset_index()\n", "\n", "final_df4_pivot.columns = [\n", "    \" \".join(col).strip() for col in final_df4_pivot.columns.values\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f321895d-4580-4056-b687-8fbcafa61cdb", "metadata": {}, "outputs": [], "source": ["final_df4_pivot"]}, {"cell_type": "code", "execution_count": null, "id": "3355443e-e2f1-4af7-902c-5da111ab46fa", "metadata": {}, "outputs": [], "source": ["final_df4_pivot = final_df4_pivot.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created Date\",\n", "        \"Stores 80-95\": \"80%-95% DS Compliance\",\n", "        \"Stores <80\": \"<80% DS Compliance\",\n", "        \"Stores >95\": \">95% DS Compliance\",\n", "        \"Stores 0\": \"0% DS Compliance\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "76762946-5548-4e6c-9194-337504fa495c", "metadata": {}, "outputs": [], "source": ["final_df4_pivot = final_df4_pivot.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "8a03f9f1-4d86-40c5-af12-e1c8bfe00712", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(final_df1_pivot,sheet_id, 'Sheet1',clear_cache=False)\n", "# pb.to_sheets(final_df2_pivot,sheet_id, 'Sheet2',clear_cache=False)\n", "# pb.to_sheets(final_df3,sheet_id, 'Sheet3',clear_cache=False)\n", "# pb.to_sheets(final_df4,sheet_id, 'Compliance on Inward',clear_cache=False)\n", "# pb.to_sheets(final_df4_1,sheet_id, 'Compliance on Inward Till 12 pm',clear_cache=False)\n", "# pb.to_sheets(final_df4_2,sheet_id, 'Compliance on billed',clear_cache=False)"]}, {"cell_type": "code", "execution_count": null, "id": "769e926d-44ef-4920-b5c2-37ef5ded5064", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(final_df1_pivot,'1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q', 'Aggregate FC Compliance',clear_cache=False)\n", "# pb.to_sheets(final_df2_pivot,'1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q', 'FC Compliance',clear_cache=False)\n", "# pb.to_sheets(final_df3,'1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q', 'Fleet Compliance',clear_cache=False)\n", "# pb.to_sheets(final_df4,'1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q', 'DS Compliance',clear_cache=False)\n", "\n", "pb.to_sheets(final_df1_pivot, sheet_id, \"Aggregate FC Compliance\", clear_cache=False)\n", "pb.to_sheets(final_df2_pivot, sheet_id, \"FC Compliance\", clear_cache=False)\n", "pb.to_sheets(final_df3, sheet_id, \"Fleet Compliance\", clear_cache=False)\n", "pb.to_sheets(final_df4_pivot, sheet_id, \"DS Compliance\", clear_cache=False)\n", "\n", "# pb.to_sheets(final_df4_1,sheet_id, 'Compliance on Inward Till 12 pm',clear_cache=False)\n", "# pb.to_sheets(final_df4_2,sheet_id, 'Compliance on billed',clear_cache=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f4a128ed-f409-4a57-89f3-7463795c4ff1", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_df1_raw, sheet_id, \"fc_compliance_raw\", clear_cache=False)\n", "pb.to_sheets(final_df3_raw, sheet_id, \"fleet_compliance_raw\", clear_cache=False)\n", "pb.to_sheets(final_df4_raw, sheet_id, \"ds_compliance_raw\", clear_cache=False)"]}, {"cell_type": "code", "execution_count": null, "id": "61a81e92-9c1e-42bf-9e91-8b08ec9fe9cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef87b3a1-86b0-4675-ac0e-ca4edd30c0cd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}