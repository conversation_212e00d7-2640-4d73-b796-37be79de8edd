{"cells": [{"cell_type": "code", "execution_count": null, "id": "013be778-3560-434b-8a7e-029b9e030999", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "2cdc4dae-9b0b-4fbc-a5f3-703f14924a9a", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b41fe90-2c26-4a71-bc57-9c69d3c25a00", "metadata": {}, "outputs": [], "source": ["outlets_data = pb.from_sheets(\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\", \"Live Outlet\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "70e1e519-b82b-4947-80a1-9db5179825ff", "metadata": {}, "outputs": [], "source": ["outlets_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c4b09e33-e83b-4e5e-b5a9-3bfe53efb95f", "metadata": {}, "outputs": [], "source": ["outlets_list = list(outlets_data[\"Outlet ID\"].astype(int).unique())"]}, {"cell_type": "code", "execution_count": null, "id": "f75f3573-79fa-40c7-93e9-b77140d55794", "metadata": {}, "outputs": [], "source": ["# outlets_list"]}, {"cell_type": "code", "execution_count": null, "id": "64ff727a-8628-40d4-948c-4d479e64ea05", "metadata": {}, "outputs": [], "source": ["start_date = (datetime.now() - timed<PERSON>ta(days=14)).strftime(\"%Y-%m-%d\")\n", "end_date = (datetime.now() - timedelta(days=0)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "74e6a34e-8682-4b5c-a021-701d7cbf00bb", "metadata": {}, "outputs": [], "source": ["store_perf_q = \"\"\"\n", "with \n", "\n", "order_scanned as (\n", "SELECT \n", "    external_order_id,\n", "    max(event_time) as scan_start_ist\n", "    ---(timestamp 'epoch' + (scan_start_timestamp/1000 * INTERVAL '1 second') + interval '5.5 hour') AS scan_start_ist,\n", "    ---row_number() OVER(PARTITION BY external_order_id ORDER BY scan_start_timestamp desc) as rn\n", "FROM \n", "    metrics.blinkit_z_order_scan_event\n", "WHERE \n", "    event_time>=(('{start_date}'::timestamp)-interval '1 days')\n", "    group by 1\n", "),\n", "\n", "\n", "base as\n", "(SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       b.order_id,\n", "       o.cart_id,\n", "       o.picker_id,\n", "       coalesce(json_extract_path_text(json_extract_path_text(lt.payload,'driver_details'),'phone'),o.partner_id) as partner_id,\n", "       o.total_selling_price,\n", "       b.total_product_quantity as IPO,\n", "       o.total_items_quantity_ordered,\n", "       o.total_items_quantity_delivered,\n", "       o.order_current_status,\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) end as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "\n", "       case when order_picking_started_ts_ist is not null then DATEDIFF(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) end as assigned_to_start,\n", "\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) end as picking_start_to_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       DATEDIFF(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       DATEDIFF(seconds,coalesce(order_picking_completed_ts_ist,order_billing_completed_ts_ist),coalesce(order_scanned_ts_ist,scan_start_ist)) as picking_complete_to_order_scanned,\n", "       DATEDIFF(seconds,coalesce(order_scanned_ts_ist,scan_start_ist),order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       case when order_delivered_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) end as delivery_time,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist) as picking_complete_to_billing_complete\n", "FROM dwh.fact_sales_order_details b\n", "inner join dwh.fact_supply_chain_order_details o on date(b.order_create_ts_ist) = date(o.order_checkout_ts_ist)\n", "and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "left JOIN \n", "         lake_logistics.logistics_task\n", "            as lt\n", "            ON lt.shipment_label_value=o.order_id\n", "            AND lt.type='EXPRESS_3PL_ORDER_DELIVERY_TASK' \n", "            and lt.install_ts >= ('{start_date}'::date)-interval '1 DAYS'\n", "LEFT JOIN \n", "        order_scanned\n", "            AS os\n", "            ON os.external_order_id=b.order_id\n", "\n", "inner join lake_retail.console_outlet c on c.id = b.outlet_id and business_type_id = 7\n", "WHERE \n", "  (date(cart_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "  and b.outlet_id in {outlets_list}\n", "and is_rescheduled=False\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "and picker_id is not null and picker_id<>'' and\n", "(case when b.outlet_id=3671 then picker_id ilike '%%GW%%'\n", "else picker_id<>'' end)\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n", "),\n", "\n", "\n", "\n", "\n", "\n", "order_level_data as \n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct picker_id) as \"Pickers\",\n", "       count(distinct partner_id) as \"Riders\",\n", "       sum(IPO)*1.0/count(distinct order_id) as \"IPO\",\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end) as fully_filled_orders, \n", "       count(distinct case when order_current_status = 'DELIVERED' then order_id end) as delivered_orders,\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end)*1.0/nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0) as fillrate,\n", "       round((count(distinct Case when instore_time <= 150 then order_id end))*1.0/(nullif(count(distinct order_id),0)),4) as \"perc orders within 2.5 min\",\n", "       \n", "       sum(total_selling_price) as GMV,\n", "       round((count(distinct Case when delivery_time <= 900 and order_current_status = 'DELIVERED' then order_id end))*1.0/(nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0)),4) as \"perc orders Delivered within 15 min\"\n", "      from \n", "      base\n", "      group by 1,2,3,4\n", "),\n", "\n", "\n", "\n", "time_data as\n", "\n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        'AVG' as metric_type,\n", "round((sum(instore_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Instore Time\",\n", "       round((sum(checkout_to_picker_assigned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned Time\",\n", "       round((sum(assigned_to_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned to start Time\",\n", "       round((sum(picking_start_to_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking start to complete Time\",\n", "       round((sum(picking_complete_to_billing_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to billing start Time\",\n", "       round((sum(billing_start_to_billing_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg billing start to complete Time\",\n", "       round((sum(picking_complete_to_order_scanned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to order scan Time\",\n", "       round((sum(order_scanned_to_enroute)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg order scan to enroute Time\",\n", "       round((sum(delivery_time)/60.0)/nullif(count(distinct case when order_current_status='DELIVERED' then order_id end),0),2) as \"Avg delivery Time\"\n", "       from base\n", "       group by 1,2,3,4,5\n", "\n", "\n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '50th percentile' as metric_type,\n", "        percentile_disc(0.5) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.5) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.5) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.5) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.5) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.5) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.5) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.5) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.5) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "       \n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '75th percentile' as metric_type,\n", "        percentile_disc(0.75) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.75) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.75) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.75) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.75) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.75) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.75) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "\n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '95th percentile' as metric_type,\n", "        percentile_disc(0.95) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.95) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.95) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.95) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.95) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.95) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.95) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "),\n", "\n", "\n", "\n", "\n", "base1 as\n", "(select date(delivery_date) as date_ts,\n", "outlet_id,\n", "--trim(c.name) as city_name,\n", "       UPPER(case when c.name in ('Ghaziabad', 'Noida') then 'UP-NCR'\n", "            when c.name = 'Bangalore' then 'Bengaluru'\n", "            else c.name end) as city_name,\n", "       count(distinct order_id) as complaint_orders\n", "from metrics.complaints o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "inner join lake_retail.console_location c on c.id = m.tax_location_id\n", "where (date(delivery_date) between '{start_date}' and '{end_date}')\n", "group by 1,2,3)\n", "\n", "\n", "select a.date,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       c.metric_type,\n", "       a.\"Orders\",\n", "       \"Pickers\",\n", "       \"Riders\",\n", "       delivered_orders,\n", "       \"perc orders within 2.5 min\",\n", "       \"Avg Instore Time\",\n", "       \"Avg Assigned Time\",\n", "       \"Avg Assigned to start Time\",\n", "       \"Avg picking start to complete Time\",\n", "       \"Avg picking complete to billing start Time\",\n", "       \"Avg billing start to complete Time\",\n", "       \"Avg picking complete to order scan Time\",\n", "       \"Avg order scan to enroute Time\",\n", "       \"Avg delivery Time\",\n", "       fillrate,\n", "       coalesce(complaint_orders,0) as complaint_orders,\n", "        GMV,\n", "        \"perc orders Delivered within 15 min\",\n", "        \"IPO\"\n", "from order_level_data a\n", "inner join time_data c on a.date = c.date and a.\"Store ID\" = c.\"Store ID\"\n", "left join base1 b on a.date = b.date_ts and a.\"Store ID\" = b.outlet_id\n", "order by 1 desc\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "store_perf_data = pd.read_sql(store_perf_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "76adc113-89e4-4955-8e94-11d35a15c811", "metadata": {}, "outputs": [], "source": ["store_perf_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7f30ab2b-df2e-4f61-bc30-4aeb4a5fcc61", "metadata": {}, "outputs": [], "source": ["store_perf_data[\"week_start\"] = store_perf_data[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "store_perf_data[\"week_end\"] = store_perf_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d0d6b732-08fa-480f-a449-450cb829c034", "metadata": {}, "outputs": [], "source": ["store_perf_data[\"week\"] = (\n", "    store_perf_data[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + store_perf_data[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e297ab5-d6e8-4c8d-a7d1-b95cc6a13658", "metadata": {}, "outputs": [], "source": ["outlets_data[\"Outlet ID\"] = outlets_data[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "fb91b813-8458-4ff8-a5c8-1b4199d209d2", "metadata": {}, "outputs": [], "source": ["store_perf_data_new = store_perf_data.merge(\n", "    outlets_data, left_on=[\"store id\"], right_on=[\"Outlet ID\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e8587e7a-5811-4fff-a76b-db6551d7fb8b", "metadata": {}, "outputs": [], "source": ["store_perf_data_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7f49fb90-f687-434a-b086-5709697e71ce", "metadata": {}, "outputs": [], "source": ["store_perf_data_new[\"store id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "f3d039c5-9e26-43d7-ac12-d0cf8fff6525", "metadata": {}, "outputs": [], "source": ["# pd.set_option('display.max_columns', None)\n", "# pd.set_option('display.max_rows', None)"]}, {"cell_type": "code", "execution_count": null, "id": "44dfef71-2f30-488b-80a0-b2604cf01276", "metadata": {}, "outputs": [], "source": ["# store_perf_data_new[(store_perf_data_new['store id']==3671) & (store_perf_data_new['metric_type']=='AVG')]"]}, {"cell_type": "code", "execution_count": null, "id": "a1966d74-4afd-476e-937c-1807810b7d77", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    store_perf_data_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"store_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0b2a1641-5cfa-471f-ae53-f104761f3fa3", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_q = \"\"\"\n", "with\n", "\n", "order_scanned as (\n", "SELECT \n", "    external_order_id,\n", "    max(event_time) as scan_start_ist\n", "    ---(timestamp 'epoch' + (scan_start_timestamp/1000 * INTERVAL '1 second') + interval '5.5 hour') AS scan_start_ist,\n", "    ---row_number() OVER(PARTITION BY external_order_id ORDER BY scan_start_timestamp desc) as rn\n", "FROM \n", "    metrics.blinkit_z_order_scan_event\n", "WHERE \n", "    event_time>=(('{start_date}'::timestamp)-interval '1 days')\n", "    group by 1\n", "),\n", "\n", "\n", "\n", "base as\n", "\n", "(SELECT date(order_checkout_ts_ist) as date,\n", "extract(hour from order_checkout_ts_ist) as hour,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       b.order_id,\n", "       o.cart_id,\n", "       o.picker_id,\n", "       coalesce(json_extract_path_text(json_extract_path_text(lt.payload,'driver_details'),'phone'),o.partner_id) as partner_id,\n", "       o.total_selling_price,\n", "       b.total_product_quantity as IPO,\n", "       o.total_items_quantity_ordered,\n", "       o.total_items_quantity_delivered,\n", "       o.order_current_status,\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) end as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "\n", "       case when order_picking_started_ts_ist is not null then DATEDIFF(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) end as assigned_to_start,\n", "\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) end as picking_start_to_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       DATEDIFF(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       DATEDIFF(seconds,coalesce(order_picking_completed_ts_ist,order_billing_completed_ts_ist),coalesce(order_scanned_ts_ist,scan_start_ist)) as picking_complete_to_order_scanned,\n", "       DATEDIFF(seconds,coalesce(order_scanned_ts_ist,scan_start_ist),order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       case when order_delivered_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) end as delivery_time,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist) as picking_complete_to_billing_complete\n", "FROM dwh.fact_sales_order_details b\n", "inner join dwh.fact_supply_chain_order_details o on date(b.order_create_ts_ist) = date(o.order_checkout_ts_ist)\n", "and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "left JOIN \n", "         lake_logistics.logistics_task\n", "            as lt\n", "            ON lt.shipment_label_value=o.order_id\n", "            AND lt.type='EXPRESS_3PL_ORDER_DELIVERY_TASK' \n", "            and lt.install_ts >= ('{start_date}'::date)-interval '1 DAYS'\n", "LEFT JOIN \n", "        order_scanned\n", "            AS os\n", "            ON os.external_order_id=b.order_id\n", "\n", "inner join lake_retail.console_outlet c on c.id = b.outlet_id and business_type_id = 7\n", "WHERE \n", "  (date(cart_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "  and b.outlet_id in {outlets_list}\n", "and is_rescheduled=False\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "and picker_id is not null and picker_id<>''\n", "and (case when b.outlet_id=3671 then picker_id ilike '%%GW%%'\n", "else picker_id<>'' end)\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24\n", "),\n", "\n", "order_level_data as \n", "(select date,hour,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        sum(IPO)*1.0/count(distinct order_id) as \"IPO\",\n", "        sum(IPO) as total_qty_picked,\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct picker_id) as \"Pickers\",\n", "       count(distinct partner_id) as \"Riders\",\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end) as fully_filled_orders, \n", "       count(distinct case when order_current_status = 'DELIVERED' then order_id end) as delivered_orders,\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end)*1.0/nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0) as fillrate,\n", "       round((count(distinct Case when instore_time <= 150 then order_id end))*1.0/(nullif(count(distinct order_id),0)),4) as \"perc orders within 2.5 min\",\n", "       \n", "       sum(total_selling_price) as GMV,\n", "       round((count(distinct Case when delivery_time <= 900 and order_current_status = 'DELIVERED' then order_id end))*1.0/(nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0)),4) as \"perc orders Delivered within 15 min\"\n", "      from \n", "      base\n", "      group by 1,2,3,4,5\n", "),\n", "\n", "\n", "\n", "time_data as\n", "\n", "(select date,hour,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        'AVG' as metric_type,\n", "round((sum(instore_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Instore Time\",\n", "       round((sum(checkout_to_picker_assigned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned Time\",\n", "       round((sum(assigned_to_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned to start Time\",\n", "       round((sum(picking_start_to_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking start to complete Time\",\n", "       round((sum(picking_complete_to_billing_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to billing start Time\",\n", "       round((sum(billing_start_to_billing_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg billing start to complete Time\",\n", "       round((sum(picking_complete_to_order_scanned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to order scan Time\",\n", "       round((sum(order_scanned_to_enroute)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg order scan to enroute Time\",\n", "       round((sum(delivery_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg delivery Time\"\n", "       from base\n", "       group by 1,2,3,4,5\n", "\n", "\n", "union\n", "\n", "select distinct date, hour,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '50th percentile' as metric_type,\n", "        percentile_disc(0.5) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.5) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.5) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.5) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.5) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.5) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.5) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.5) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.5) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "       \n", "union\n", "\n", "select distinct date, hour,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '75th percentile' as metric_type,\n", "        percentile_disc(0.75) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.75) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.75) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.75) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.75) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.75) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.75) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "\n", "union\n", "\n", "select distinct date, hour,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        '95th percentile' as metric_type,\n", "        percentile_disc(0.95) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.95) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.95) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.95) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.95) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.95) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.95) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,hour)/60.0 as \"Avg delivery Time\"\n", "       from base\n", ")\n", "\n", "select a.date, a.hour,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       c.metric_type,\n", "       a.\"Orders\",\n", "       \"Pickers\",\n", "       \"Riders\",\n", "       delivered_orders,\n", "       \"perc orders within 2.5 min\",\n", "       \"Avg Instore Time\",\n", "       \"Avg Assigned Time\",\n", "       \"Avg Assigned to start Time\",\n", "       \"Avg picking start to complete Time\",\n", "       \"Avg picking complete to billing start Time\",\n", "       \"Avg billing start to complete Time\",\n", "       \"Avg picking complete to order scan Time\",\n", "       \"Avg order scan to enroute Time\",\n", "       \"Avg delivery Time\",\n", "        GMV,\n", "        \"perc orders Delivered within 15 min\",\n", "        \"IPO\",\n", "        total_qty_picked\n", "from order_level_data a\n", "inner join time_data c on a.date = c.date and a.\"Store ID\" = c.\"Store ID\" and a.hour=c.hour\n", "order by 1 desc\n", "  \"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "store_perf_hourly_data = pd.read_sql(store_perf_hourly_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "990d9778-31c8-432e-b21b-1692fbb7bd20", "metadata": {}, "outputs": [], "source": ["def peak_hours(x):\n", "    if x >= 7 and x <= 11:\n", "        return \"Morning Peak\"\n", "    elif x >= 17 and x <= 21:\n", "        return \"Evening Peak\"\n", "    else:\n", "        return \"Non-Peak\""]}, {"cell_type": "code", "execution_count": null, "id": "75494344-8781-46aa-bc3b-43716e4e2e5b", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data[\"peak_flag\"] = store_perf_hourly_data[\"hour\"].apply(\n", "    lambda x: peak_hours(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9f0b0940-7070-47ca-ac12-8a8fe2161a93", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1279da19-c275-4fd7-ae63-ba0005c7b6d7", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data[\"week_start\"] = store_perf_hourly_data[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "store_perf_hourly_data[\"week_end\"] = store_perf_hourly_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "023f78a3-3b0a-4b52-8ac4-e43fa3dab4cd", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data[\"week\"] = (\n", "    store_perf_hourly_data[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + store_perf_hourly_data[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d53153c-0ed4-498e-8477-b1409c50672e", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "23561a9f-86c9-40e0-9930-cb0a329db2f9", "metadata": {}, "outputs": [], "source": ["store_perf_hourly_data = store_perf_hourly_data[\n", "    [\n", "        \"date\",\n", "        \"hour\",\n", "        \"store id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"metric_type\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"riders\",\n", "        \"delivered_orders\",\n", "        \"perc orders within 2.5 min\",\n", "        \"avg instore time\",\n", "        \"avg assigned time\",\n", "        \"avg assigned to start time\",\n", "        \"avg picking start to complete time\",\n", "        \"avg picking complete to billing start time\",\n", "        \"avg billing start to complete time\",\n", "        \"avg picking complete to order scan time\",\n", "        \"avg order scan to enroute time\",\n", "        \"avg delivery time\",\n", "        \"gmv\",\n", "        \"perc orders delivered within 15 min\",\n", "        \"ipo\",\n", "        \"peak_flag\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"total_qty_picked\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "497b2b71-a9d5-46bc-b87e-7fbb9c2b1dc7", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    store_perf_hourly_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"store_hourly_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c26d443b-f873-4f87-a4e2-43e2dafa3a19", "metadata": {}, "outputs": [], "source": ["Availability_q = \"\"\"\n", "select\n", "    city,\n", "    co.id as outlet_id,\n", "    co.name as outlet_name,\n", "    date(date_) date_,\n", "    hour,\n", "    sum(live_sku) as live_skus,\n", "    sum(total_sku) as total_sku\n", "\n", "from \n", "    metrics.store_hourly_weighted_availability_logs a\n", "    inner join lake_retail.console_outlet co on co.facility_id = a.facility_id\n", "where \n", "co.id in {outlets_list}\n", "and date(date_) between '{start_date}' and '{end_date}'\n", "group by 1,2,3,4,5\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "availability_data = pd.read_sql(Availability_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "854f0074-a64c-4b21-8d0c-53982b408224", "metadata": {}, "outputs": [], "source": ["availability_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f57a8428-4b79-41ba-ba99-4a5197447394", "metadata": {}, "outputs": [], "source": ["availability_data[\"%Availability\"] = (\n", "    availability_data[\"live_skus\"] / availability_data[\"total_sku\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "915f67f8-4ede-4d49-be64-275f244628bd", "metadata": {}, "outputs": [], "source": ["availability_data[\"week_start\"] = availability_data[\"date_\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "availability_data[\"week_end\"] = availability_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "738a8ae1-81e5-4187-95ca-0c7fb4ab5d33", "metadata": {}, "outputs": [], "source": ["availability_data[\"week\"] = (\n", "    availability_data[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + availability_data[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ce4bb8f2-aaa8-40de-afb8-89eb22961730", "metadata": {}, "outputs": [], "source": ["availability_data_new = availability_data.merge(\n", "    outlets_data, left_on=[\"outlet_id\"], right_on=[\"Outlet ID\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "df7dcb22-3f3f-4086-9430-a414255be7d6", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    availability_data_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"availability_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "31e912e6-3fc5-4685-9b36-7581e0a078cb", "metadata": {}, "outputs": [], "source": ["picker_login_time = \"\"\"with app_login_time as\n", "(SELECT \n", "    DATE(pos_timestamp +interval'5.5 hours') AS date,\n", "    outlet_id,\n", "    user_name,\n", "    action,\n", "    role,\n", "    (pos_timestamp) AS start_ts,\n", "    --- people get force logged out after 1 hence using the UTC timestamp here for date partition\n", "    LEAD((pos_timestamp)) OVER (partition BY DATE(pos_timestamp +interval'5.5 hours'),user_name,outlet_id ORDER BY pos_timestamp) AS end_time\n", "        \n", "    -- DATE_DIFF('minute', from_unixtime(pos_timestamp/ 1000000),LEAD(from_unixtime(pos_timestamp/ 1000000)) OVER (partition BY DATE(from_unixtime(pos_timestamp/ 1000000)),user_name,outlet_id ORDER BY pos_timestamp) )AS time\n", "    \n", "FROM \n", "    lake_warehouse_location.warehouse_device_user_log \n", "        AS login\n", "WHERE \n", "    (pos_timestamp+interval'5.5 hours') >= '{start_date}'\n", "    and (pos_timestamp+interval'5.5 hours') <= '{end_date}'::date+interval '1 day'\n", "    AND action in ('LOGI<PERSON>','LOGOUT','FORCE_LOGOUT') \n", "    -- FORCE_LOGOUT done from GPOS, if the user is not able to find device where he was already logged\n", "    and role='PICKER' and outlet_id in {outlets_list}\n", "ORDER BY 4)\n", "\n", "\n", "\n", "\n", "SELECT \n", "date,\n", "outlet_id,\n", "b.name as outlet_name,\n", "user_name as employee_id,\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 0 and extract(hour from (end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 0 and extract(hour from (end_time + interval '5.5 hours')) > 0 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 0 and extract(hour from (end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 0 and extract(hour from (end_time + interval '5.5 hours')) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 1 and extract(hour from (end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 1 and extract(hour from (end_time + interval '5.5 hours')) > 1 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 1 and extract(hour from (end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 1 and extract(hour from (end_time + interval '5.5 hours')) > 1 \n", "                    then 60 else 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 2 and extract(hour from (end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 2 and extract(hour from (end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 2 and extract(hour from (end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 2 and extract(hour from (end_time + interval '5.5 hours')) > 2 \n", "                    then 60 else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 3 and extract(hour from (end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 3 and extract(hour from (end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 3 and extract(hour from (end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 3 and extract(hour from (end_time + interval '5.5 hours')) > 3 \n", "                    then 60 else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 4 and extract(hour from (end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 4 and extract(hour from (end_time + interval '5.5 hours')) > 4 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 4 and extract(hour from (end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 4 and extract(hour from (end_time + interval '5.5 hours')) > 4 \n", "                    then 60 else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 5 and extract(hour from (end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 5 and extract(hour from (end_time + interval '5.5 hours')) > 5 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 5 and extract(hour from (end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 5 and extract(hour from (end_time + interval '5.5 hours')) > 5 \n", "                    then 60 else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 6 and extract(hour from (end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 6 and extract(hour from (end_time + interval '5.5 hours')) > 6 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 6 and extract(hour from (end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 6 and extract(hour from (end_time + interval '5.5 hours')) > 6 \n", "                    then 60 else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 7 and extract(hour from (end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 7 and extract(hour from (end_time + interval '5.5 hours')) > 7 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 7 and extract(hour from (end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 7 and extract(hour from (end_time + interval '5.5 hours')) > 7 \n", "                    then 60 else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 8 and extract(hour from (end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 8 and extract(hour from (end_time + interval '5.5 hours')) > 8 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 8 and extract(hour from (end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 8 and extract(hour from (end_time + interval '5.5 hours')) > 8 \n", "                    then 60 else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 9 and extract(hour from (end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 9 and extract(hour from (end_time + interval '5.5 hours')) > 9 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 9 and extract(hour from (end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 9 and extract(hour from (end_time + interval '5.5 hours')) > 9 \n", "                    then 60 else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 10 and extract(hour from (end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 10 and extract(hour from (end_time + interval '5.5 hours')) > 10 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 10 and extract(hour from (end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 10 and extract(hour from (end_time + interval '5.5 hours')) > 10 \n", "                    then 60 else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 11 and extract(hour from (end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 11 and extract(hour from (end_time + interval '5.5 hours')) > 11 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 11 and extract(hour from (end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 11 and extract(hour from (end_time + interval '5.5 hours')) > 11 \n", "                    then 60 else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 12 and extract(hour from (end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 12 and extract(hour from (end_time + interval '5.5 hours')) > 12 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 12 and extract(hour from (end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 12 and extract(hour from (end_time + interval '5.5 hours')) > 12 \n", "                    then 60 else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 13 and extract(hour from (end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 13 and extract(hour from (end_time + interval '5.5 hours')) > 13 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 13 and extract(hour from (end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 13 and extract(hour from (end_time + interval '5.5 hours')) > 13 \n", "                    then 60 else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 14 and extract(hour from (end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 14 and extract(hour from (end_time + interval '5.5 hours')) > 14 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 14 and extract(hour from (end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 14 and extract(hour from (end_time + interval '5.5 hours')) > 14 \n", "                    then 60 else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 15 and extract(hour from (end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 15 and extract(hour from (end_time + interval '5.5 hours')) > 15 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 15 and extract(hour from (end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 15 and extract(hour from (end_time + interval '5.5 hours')) > 15 \n", "                    then 60 else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 16 and extract(hour from (end_time + interval '5.5 hours')) = 162 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 16 and extract(hour from (end_time + interval '5.5 hours')) > 16 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 16 and extract(hour from (end_time + interval '5.5 hours')) = 16 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 16 and extract(hour from (end_time + interval '5.5 hours')) > 16 \n", "                    then 60 else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 17 and extract(hour from (end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 17 and extract(hour from (end_time + interval '5.5 hours')) > 17 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 17 and extract(hour from (end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 17 and extract(hour from (end_time + interval '5.5 hours')) > 17 \n", "                    then 60 else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 18 and extract(hour from (end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 18 and extract(hour from (end_time + interval '5.5 hours')) > 18 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 18 and extract(hour from (end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 18 and extract(hour from (end_time + interval '5.5 hours')) > 18 \n", "                    then 60 else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 19 and extract(hour from (end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 19 and extract(hour from (end_time + interval '5.5 hours')) > 19 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 19 and extract(hour from (end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 19 and extract(hour from (end_time + interval '5.5 hours')) > 19 \n", "                    then 60 else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 20 and extract(hour from (end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 20 and extract(hour from (end_time + interval '5.5 hours')) > 20 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 20 and extract(hour from (end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 20 and extract(hour from (end_time + interval '5.5 hours')) > 20 \n", "                    then 60 else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 21 and extract(hour from (end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 21 and extract(hour from (end_time + interval '5.5 hours')) > 21 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 21 and extract(hour from (end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 21 and extract(hour from (end_time + interval '5.5 hours')) > 21 \n", "                    then 60 else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 22 and extract(hour from (end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 22 and extract(hour from (end_time + interval '5.5 hours')) > 22 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 22 and extract(hour from (end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 22 and extract(hour from (end_time + interval '5.5 hours')) > 22 \n", "                    then 60 else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 23 and extract(hour from (end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from end_time - start_ts)/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) = 23 and extract(hour from (end_time + interval '5.5 hours')) > 23 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_ts + interval '5.5 hours'))::int), (start_ts + interval '5.5 hours'))  - (start_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 23 and extract(hour from (end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from ((end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_time + interval '5.5 hours')))*-1, (end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_ts + interval '5.5 hours')) < 23 and extract(hour from (end_time + interval '5.5 hours')) > 23 \n", "                    then 60 else 0 end) as \"23\"\n", "   FROM app_login_time a\n", "inner join lake_retail.console_outlet b on a.outlet_id = b.id\n", "   where action='LOGIN'\n", "   group by 1,2,3,4\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "picker_login_time_data = pd.read_sql(picker_login_time, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "6131025c-d10e-45cb-9572-890872bda1ac", "metadata": {}, "outputs": [], "source": ["picker_login_time_data_melt = pd.melt(\n", "    picker_login_time_data,\n", "    id_vars=[\"outlet_id\", \"outlet_name\", \"date\", \"employee_id\"],\n", "    value_vars=[\n", "        \"0\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "    ],\n", "    var_name=\"hour\",\n", "    value_name=\"login_time\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "85064dd7-bd64-492e-a874-bad335c1028f", "metadata": {}, "outputs": [], "source": ["picker_login_time_data_melt.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6d37aca4-4efe-479e-a812-a4e60c28b8b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f2c8b553-cac9-4806-be81-410d725295b9", "metadata": {}, "outputs": [], "source": ["queue_time_q = \"\"\" with roster as \n", "(SELECT\n", "            date(q.entry_ts + INTERVAL '5.5 HOURS') as date,\n", "            d.pos_outlet_id as outlet_id,\n", "            lup.employee_id,\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 0 and extract(hour from (exit_ts + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 0 and extract(hour from (exit_ts + interval '5.5 hours')) > 0 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 0 and extract(hour from (exit_ts + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 0 and extract(hour from (exit_ts + interval '5.5 hours')) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 1 and extract(hour from (exit_ts + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 1 and extract(hour from (exit_ts + interval '5.5 hours')) > 1 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 1 and extract(hour from (exit_ts + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 1 and (extract(hour from (exit_ts + interval '5.5 hours')) > 1 )\n", "                    then 60 \n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 1 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "\t\t\t\t\telse 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 2 and extract(hour from (exit_ts + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 2 and extract(hour from (exit_ts + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 2 and extract(hour from (exit_ts + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 2 and (extract(hour from (exit_ts + interval '5.5 hours')) > 2 )\n", "                    then 60 \n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 2 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 3 and extract(hour from (exit_ts + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 3 and extract(hour from (exit_ts + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 3 and extract(hour from (exit_ts + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 3 and (extract(hour from (exit_ts + interval '5.5 hours')) > 3 )\n", "                    then 60 \n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 3 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60        \n", "                    else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 4 and extract(hour from (exit_ts + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 4 and extract(hour from (exit_ts + interval '5.5 hours')) > 4 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 4 and extract(hour from (exit_ts + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 4 and (extract(hour from (exit_ts + interval '5.5 hours')) > 4 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 4 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 5 and extract(hour from (exit_ts + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 5 and extract(hour from (exit_ts + interval '5.5 hours')) > 5 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 5 and extract(hour from (exit_ts + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 5 and (extract(hour from (exit_ts + interval '5.5 hours')) > 5 )\n", "                    then 60 \n", "                  when extract(hour from (entry_ts + interval '5.5 hours')) < 5 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 6 and extract(hour from (exit_ts + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 6 and extract(hour from (exit_ts + interval '5.5 hours')) > 6 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 6 and extract(hour from (exit_ts + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 6 and (extract(hour from (exit_ts + interval '5.5 hours')) > 6 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (entry_ts + interval '5.5 hours')) < 6 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 7 and extract(hour from (exit_ts + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 7 and extract(hour from (exit_ts + interval '5.5 hours')) > 7 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 7 and extract(hour from (exit_ts + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 7 and (extract(hour from (exit_ts + interval '5.5 hours')) > 7 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 7 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 8 and extract(hour from (exit_ts + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 8 and extract(hour from (exit_ts + interval '5.5 hours')) > 8 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 8 and extract(hour from (exit_ts + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 8 and (extract(hour from (exit_ts + interval '5.5 hours')) > 8 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (entry_ts + interval '5.5 hours')) < 8 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 9 and extract(hour from (exit_ts + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 9 and extract(hour from (exit_ts + interval '5.5 hours')) > 9 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 9 and extract(hour from (exit_ts + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 9 and (extract(hour from (exit_ts + interval '5.5 hours')) > 9 )\n", "                    then 60 \n", "                   when extract(hour from (entry_ts + interval '5.5 hours')) < 9 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 10 and extract(hour from (exit_ts + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 10 and extract(hour from (exit_ts + interval '5.5 hours')) > 10 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 10 and extract(hour from (exit_ts + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 10 and (extract(hour from (exit_ts + interval '5.5 hours')) > 10 )\n", "                    then 60 \n", "                   when extract(hour from (entry_ts + interval '5.5 hours')) < 10 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 11 and extract(hour from (exit_ts + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 11 and extract(hour from (exit_ts + interval '5.5 hours')) > 11 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 11 and extract(hour from (exit_ts + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 11 and (extract(hour from (exit_ts + interval '5.5 hours')) > 11 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (entry_ts + interval '5.5 hours')) < 11 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 12 and extract(hour from (exit_ts + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 12 and extract(hour from (exit_ts + interval '5.5 hours')) > 12 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 12 and extract(hour from (exit_ts + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 12 and (extract(hour from (exit_ts + interval '5.5 hours')) > 12 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 12 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 13 and extract(hour from (exit_ts + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 13 and extract(hour from (exit_ts + interval '5.5 hours')) > 13 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 13 and extract(hour from (exit_ts + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 13 and (extract(hour from (exit_ts + interval '5.5 hours')) > 13 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 13 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 14 and extract(hour from (exit_ts + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 14 and extract(hour from (exit_ts + interval '5.5 hours')) > 14 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 14 and extract(hour from (exit_ts + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 14 and (extract(hour from (exit_ts + interval '5.5 hours')) > 14 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (entry_ts + interval '5.5 hours')) < 14 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 15 and extract(hour from (exit_ts + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 15 and extract(hour from (exit_ts + interval '5.5 hours')) > 15 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 15 and extract(hour from (exit_ts + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 15 and (extract(hour from (exit_ts + interval '5.5 hours')) > 15 )\n", "                    then 60 \n", "                   when extract(hour from (entry_ts + interval '5.5 hours')) < 15 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 16 and extract(hour from (exit_ts + interval '5.5 hours')) = 16\n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 16 and extract(hour from (exit_ts + interval '5.5 hours')) > 16 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 16 and extract(hour from (exit_ts + interval '5.5 hours')) = 16 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 16 and (extract(hour from (exit_ts + interval '5.5 hours')) > 16 )\n", "                    then 60 \n", "                 when extract(hour from (entry_ts + interval '5.5 hours')) < 16 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 17 and extract(hour from (exit_ts + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 17 and extract(hour from (exit_ts + interval '5.5 hours')) > 17 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 17 and extract(hour from (exit_ts + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 17 and (extract(hour from (exit_ts + interval '5.5 hours')) > 17 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 17 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 18 and extract(hour from (exit_ts + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 18 and extract(hour from (exit_ts + interval '5.5 hours')) > 18 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 18 and extract(hour from (exit_ts + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 18 and (extract(hour from (exit_ts + interval '5.5 hours')) > 18 )\n", "                    then 60\n", "               when extract(hour from (entry_ts + interval '5.5 hours')) < 18 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 19 and extract(hour from (exit_ts + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 19 and extract(hour from (exit_ts + interval '5.5 hours')) > 19 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 19 and extract(hour from (exit_ts + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 19 and (extract(hour from (exit_ts + interval '5.5 hours')) > 19 )\n", "                    then 60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 19 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                     else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 20 and extract(hour from (exit_ts + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 20 and extract(hour from (exit_ts + interval '5.5 hours')) > 20 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 20 and extract(hour from (exit_ts + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 20 and (extract(hour from (exit_ts + interval '5.5 hours')) > 20 )\n", "                    then 60 \n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 20 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 21 and extract(hour from (exit_ts + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 21 and extract(hour from (exit_ts + interval '5.5 hours')) > 21 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 21 and extract(hour from (exit_ts + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 21 and (extract(hour from (exit_ts + interval '5.5 hours')) > 21 )\n", "                    then 60 \n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 21 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                   else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 22 and extract(hour from (exit_ts + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 22 and extract(hour from (exit_ts + interval '5.5 hours')) > 22 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 22 and extract(hour from (exit_ts + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 22 and (extract(hour from (exit_ts + interval '5.5 hours')) > 22 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 22 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                   else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 23 and extract(hour from (exit_ts + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from exit_ts - entry_ts)/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) = 23 and extract(hour from (exit_ts + interval '5.5 hours')) > 23 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (entry_ts + interval '5.5 hours'))::int), (entry_ts + interval '5.5 hours'))  - (entry_ts + interval '5.5 hours')))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 23 and extract(hour from (exit_ts + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from ((exit_ts + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (exit_ts + interval '5.5 hours')))*-1, (exit_ts + interval '5.5 hours'))))/60\n", "            when extract(hour from (entry_ts + interval '5.5 hours')) < 23 and (extract(hour from (exit_ts + interval '5.5 hours')) > 23 )\n", "                    then 60 \n", "                    when extract(hour from (entry_ts + interval '5.5 hours')) < 23 and date(exit_ts+interval '5.5 hours')>=date(entry_ts+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"23\"\n", "           FROM\n", "            lake_logistics.logistics_express_allocation_field_executive_queue\n", "                AS q\n", "        INNER JOIN \n", "            lake_logistics.logistics_user_profile\n", "                AS lup\n", "                on lup.id=cast(q.user_profile_id as int)\n", "        inner join dwh.dim_merchant_outlet_facility_mapping d on d.frontend_merchant_id = q.dark_store_id and is_current=true\n", "   WHERE \n", "        merchant_business_type_id=7 and\n", "            cast((q.entry_ts + INTERVAL '5.5 HOURS') as date)>=cast('{start_date}' as date)\n", "            and cast((q.entry_ts + INTERVAL '5.5 HOURS') as date)<=cast('{end_date}' as date)\n", "            and q.exit_ts is not null\n", "            and pos_outlet_id in {outlets_list}\n", "            and q.user_type='PICKER'\n", "   GROUP BY 1,2,3)\n", "   \n", "select * from roster order by 1,2,3\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "queue_time = pd.read_sql(queue_time_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "e8bafb8c-991b-4f80-b923-1bb69a8bb830", "metadata": {}, "outputs": [], "source": ["queue_time.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e2ecfb3-5e21-46a7-b2d6-9790716508a9", "metadata": {}, "outputs": [], "source": ["queue_time_melt = pd.melt(\n", "    queue_time,\n", "    id_vars=[\"outlet_id\", \"date\", \"employee_id\"],\n", "    value_vars=[\n", "        \"0\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "    ],\n", "    var_name=\"hour\",\n", "    value_name=\"queue_time\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67d10776-9a64-46ea-9ccb-946bfa3dd5db", "metadata": {}, "outputs": [], "source": ["queue_time_group = (\n", "    queue_time_melt.groupby([\"outlet_id\", \"date\", \"hour\", \"employee_id\"])\n", "    .agg({\"queue_time\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "054ca01d-01ad-4acc-b725-f298e5144538", "metadata": {}, "outputs": [], "source": ["queue_time_group = queue_time_group[queue_time_group[\"employee_id\"].str.contains(\"GW\")]"]}, {"cell_type": "code", "execution_count": null, "id": "e4fe2dd8-d9ba-4191-8240-c0798b1137f4", "metadata": {}, "outputs": [], "source": ["queue_time_group1 = (\n", "    queue_time_group[queue_time_group[\"queue_time\"] > 0]\n", "    .groupby([\"outlet_id\", \"date\", \"hour\"])\n", "    .agg({\"queue_time\": \"sum\", \"employee_id\": \"nunique\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bd29ac98-ba26-46ec-b382-6994d9443105", "metadata": {}, "outputs": [], "source": ["queue_time_group1[\"hour\"] = queue_time_group1[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "328c29b4-3246-42b6-a69b-ae13298ced63", "metadata": {}, "outputs": [], "source": ["queue_time_group1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "04ff3604-5aa3-4795-a569-b49a4aa3c856", "metadata": {}, "outputs": [], "source": ["q2 = \"\"\" with roster as \n", "(SELECT \n", "site_id as outlet_id,\n", "b.name as outlet_name,\n", "date(role_start_time+interval '330' minute) as date,\n", "employee_id,\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 0 and extract(hour from (role_end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 0 and extract(hour from (role_end_time + interval '5.5 hours')) > 0 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 0 and extract(hour from (role_end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 0 and extract(hour from (role_end_time + interval '5.5 hours')) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 1 and extract(hour from (role_end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 1 and extract(hour from (role_end_time + interval '5.5 hours')) > 1 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 1 and extract(hour from (role_end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 1 and (extract(hour from (role_end_time + interval '5.5 hours')) > 1 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 1 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "\t\t\t\t\telse 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 2 and extract(hour from (role_end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 2 and extract(hour from (role_end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 2 and extract(hour from (role_end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 2 and (extract(hour from (role_end_time + interval '5.5 hours')) > 2 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 2 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 3 and extract(hour from (role_end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 3 and extract(hour from (role_end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 3 and extract(hour from (role_end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 3 and (extract(hour from (role_end_time + interval '5.5 hours')) > 3 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 3 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60        \n", "                    else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 4 and extract(hour from (role_end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 4 and extract(hour from (role_end_time + interval '5.5 hours')) > 4 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 4 and extract(hour from (role_end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 4 and (extract(hour from (role_end_time + interval '5.5 hours')) > 4 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 4 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 5 and extract(hour from (role_end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 5 and extract(hour from (role_end_time + interval '5.5 hours')) > 5 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 5 and extract(hour from (role_end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 5 and (extract(hour from (role_end_time + interval '5.5 hours')) > 5 )\n", "                    then 60 \n", "                  when extract(hour from (role_start_time + interval '5.5 hours')) < 5 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 6 and extract(hour from (role_end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 6 and extract(hour from (role_end_time + interval '5.5 hours')) > 6 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 6 and extract(hour from (role_end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 6 and (extract(hour from (role_end_time + interval '5.5 hours')) > 6 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (role_start_time + interval '5.5 hours')) < 6 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 7 and extract(hour from (role_end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 7 and extract(hour from (role_end_time + interval '5.5 hours')) > 7 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 7 and extract(hour from (role_end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 7 and (extract(hour from (role_end_time + interval '5.5 hours')) > 7 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 7 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 8 and extract(hour from (role_end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 8 and extract(hour from (role_end_time + interval '5.5 hours')) > 8 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 8 and extract(hour from (role_end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 8 and (extract(hour from (role_end_time + interval '5.5 hours')) > 8 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (role_start_time + interval '5.5 hours')) < 8 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 9 and extract(hour from (role_end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 9 and extract(hour from (role_end_time + interval '5.5 hours')) > 9 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 9 and extract(hour from (role_end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 9 and (extract(hour from (role_end_time + interval '5.5 hours')) > 9 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '5.5 hours')) < 9 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 10 and extract(hour from (role_end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 10 and extract(hour from (role_end_time + interval '5.5 hours')) > 10 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 10 and extract(hour from (role_end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 10 and (extract(hour from (role_end_time + interval '5.5 hours')) > 10 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '5.5 hours')) < 10 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 11 and extract(hour from (role_end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 11 and extract(hour from (role_end_time + interval '5.5 hours')) > 11 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 11 and extract(hour from (role_end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 11 and (extract(hour from (role_end_time + interval '5.5 hours')) > 11 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (role_start_time + interval '5.5 hours')) < 11 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 12 and extract(hour from (role_end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 12 and extract(hour from (role_end_time + interval '5.5 hours')) > 12 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 12 and extract(hour from (role_end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 12 and (extract(hour from (role_end_time + interval '5.5 hours')) > 12 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 12 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 13 and extract(hour from (role_end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 13 and extract(hour from (role_end_time + interval '5.5 hours')) > 13 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 13 and extract(hour from (role_end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 13 and (extract(hour from (role_end_time + interval '5.5 hours')) > 13 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 13 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 14 and extract(hour from (role_end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 14 and extract(hour from (role_end_time + interval '5.5 hours')) > 14 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 14 and extract(hour from (role_end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 14 and (extract(hour from (role_end_time + interval '5.5 hours')) > 14 )\n", "                    then 60 \n", "\t\t\twhen extract(hour from (role_start_time + interval '5.5 hours')) < 14 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 15 and extract(hour from (role_end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 15 and extract(hour from (role_end_time + interval '5.5 hours')) > 15 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 15 and extract(hour from (role_end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 15 and (extract(hour from (role_end_time + interval '5.5 hours')) > 15 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '5.5 hours')) < 15 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 16 and extract(hour from (role_end_time + interval '5.5 hours')) = 16\n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 16 and extract(hour from (role_end_time + interval '5.5 hours')) > 16 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 16 and extract(hour from (role_end_time + interval '5.5 hours')) = 16 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 16 and (extract(hour from (role_end_time + interval '5.5 hours')) > 16 )\n", "                    then 60 \n", "                 when extract(hour from (role_start_time + interval '5.5 hours')) < 16 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 17 and extract(hour from (role_end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 17 and extract(hour from (role_end_time + interval '5.5 hours')) > 17 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 17 and extract(hour from (role_end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 17 and (extract(hour from (role_end_time + interval '5.5 hours')) > 17 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 17 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 18 and extract(hour from (role_end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 18 and extract(hour from (role_end_time + interval '5.5 hours')) > 18 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 18 and extract(hour from (role_end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 18 and (extract(hour from (role_end_time + interval '5.5 hours')) > 18 )\n", "                    then 60\n", "               when extract(hour from (role_start_time + interval '5.5 hours')) < 18 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 19 and extract(hour from (role_end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 19 and extract(hour from (role_end_time + interval '5.5 hours')) > 19 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 19 and extract(hour from (role_end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 19 and (extract(hour from (role_end_time + interval '5.5 hours')) > 19 )\n", "                    then 60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 19 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                     else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 20 and extract(hour from (role_end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 20 and extract(hour from (role_end_time + interval '5.5 hours')) > 20 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 20 and extract(hour from (role_end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 20 and (extract(hour from (role_end_time + interval '5.5 hours')) > 20 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 20 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 21 and extract(hour from (role_end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 21 and extract(hour from (role_end_time + interval '5.5 hours')) > 21 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 21 and extract(hour from (role_end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 21 and (extract(hour from (role_end_time + interval '5.5 hours')) > 21 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 21 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                   else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 22 and extract(hour from (role_end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 22 and extract(hour from (role_end_time + interval '5.5 hours')) > 22 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 22 and extract(hour from (role_end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 22 and (extract(hour from (role_end_time + interval '5.5 hours')) > 22 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 22 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                   else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 23 and extract(hour from (role_end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from role_end_time - role_start_time)/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) = 23 and extract(hour from (role_end_time + interval '5.5 hours')) > 23 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (role_start_time + interval '5.5 hours'))::int), (role_start_time + interval '5.5 hours'))  - (role_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 23 and extract(hour from (role_end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from ((role_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (role_end_time + interval '5.5 hours')))*-1, (role_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (role_start_time + interval '5.5 hours')) < 23 and (extract(hour from (role_end_time + interval '5.5 hours')) > 23 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '5.5 hours')) < 23 and date(role_end_time+interval '5.5 hours')>=date(role_start_time+interval '5.5 hours')+interval '1 day' then 60\n", "                    else 0 end) as \"23\"\n", "   FROM lake_ops_management.shift_roles a\n", "   inner join lake_retail.console_outlet b on a.site_id = b.id\n", "   where date(role_start_time+ interval '330 minutes') between cast('{start_date}' as date) and cast('{end_date}' as date)\n", "   and role_end_time >= role_start_time \n", "   --and role='PICKER'\n", "   and site_id in {outlets_list}\n", "   GROUP BY 1,2,3,4)\n", "   \n", "select * from roster order by 1,2,3,4\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "picking_time_new = pd.read_sql(q2, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "4b24e3a3-3968-4a62-b8ff-2dd620e6c8f0", "metadata": {}, "outputs": [], "source": ["picking_time_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "efe645f0-ac14-49a1-b75c-92efca3ce517", "metadata": {}, "outputs": [], "source": ["picker_login_time_data_melt_new = pd.melt(\n", "    picking_time_new,\n", "    id_vars=[\"outlet_id\", \"outlet_name\", \"date\", \"employee_id\"],\n", "    value_vars=[\n", "        \"0\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "    ],\n", "    var_name=\"hour\",\n", "    value_name=\"login_time\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47a911a4-bff8-43a1-8e6c-ed575383ea9a", "metadata": {}, "outputs": [], "source": ["picker_login_time_all = pd.concat(\n", "    [picker_login_time_data_melt, picker_login_time_data_melt_new]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "749f8b67-e84b-4072-9654-ea1a38a5ae0e", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group = (\n", "    picker_login_time_all.groupby(\n", "        [\"outlet_id\", \"outlet_name\", \"date\", \"employee_id\", \"hour\"]\n", "    )\n", "    .agg({\"login_time\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d6c4a66-d7cd-4627-85e4-7f5b76c1fa34", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group = picker_login_time_all_group[\n", "    picker_login_time_all_group[\"employee_id\"].str.contains(\"GW\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5ff1e25f-cf0c-4892-8174-07e1ae5f0f68", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1 = (\n", "    picker_login_time_all_group[picker_login_time_all_group[\"login_time\"] > 0]\n", "    .groupby([\"outlet_id\", \"outlet_name\", \"date\", \"hour\"])\n", "    .agg({\"employee_id\": \"nunique\", \"login_time\": [\"mean\", \"sum\"]})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1bca8ac3-c20b-4706-af9d-ba99d70e66b8", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1.columns = [\n", "    \" \".join(col).strip() for col in picker_login_time_all_group1.columns.values\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "adc14666-ed4d-4243-b9d0-127df0c47255", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1[\"hour\"] = picker_login_time_all_group1[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "07c54717-3a4b-43ed-8fcb-b52846f08e68", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1[\"peak_flag\"] = picker_login_time_all_group1[\"hour\"].apply(\n", "    lambda x: peak_hours(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6280b1ac-c275-4400-9cd1-c57191ede7ec", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4da49b08-b406-473e-b95b-cd4cd5061fd0", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1[\"week_start\"] = picker_login_time_all_group1[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "picker_login_time_all_group1[\"week_end\"] = picker_login_time_all_group1[\n", "    \"week_start\"\n", "].apply(lambda x: x + <PERSON><PERSON>ta(days=6))"]}, {"cell_type": "code", "execution_count": null, "id": "5dfd4355-defb-49ca-a376-d58880c13f52", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1[\"week\"] = (\n", "    picker_login_time_all_group1[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + picker_login_time_all_group1[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ee38dd55-6bd4-4935-af13-675318712e8a", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1 = picker_login_time_all_group1[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date\",\n", "        \"hour\",\n", "        \"employee_id nunique\",\n", "        \"login_time mean\",\n", "        \"peak_flag\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"login_time sum\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9c8e8beb-9f6b-4613-b11c-08511568f646", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1[\"outlet_id\"] = picker_login_time_all_group1[\n", "    \"outlet_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "ff35edbe-eace-45bb-9a90-fdb0f53cde7b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f563edb0-880b-4d87-b3b1-5b9d42ef4624", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1_new = picker_login_time_all_group1.merge(\n", "    queue_time_group1, on=[\"outlet_id\", \"date\", \"hour\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1211b922-703e-4530-bddc-8535221fa5b5", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group1_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d58cb422-ff25-414a-87e9-ae70beccc431", "metadata": {}, "outputs": [], "source": ["picker_login_time_all_group[\"outlet_id\"] = picker_login_time_all_group[\n", "    \"outlet_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6b61ad4c-e4ea-4c8b-bf99-96e57275081e", "metadata": {}, "outputs": [], "source": ["queue_time_group.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "38fbeaca-3ee6-4452-bc49-7535e78e36bc", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_login_time_all_group1_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"hourly_picker_queue_time_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bc4566fa-237e-47ef-9bae-2344e2108fa4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_login_time_all_group1,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"hourly_picker_login_raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "70edce80-a396-4bfa-90d1-dc0e7a48234b", "metadata": {}, "outputs": [], "source": ["attendance_q1 = \"\"\" \n", " with roster as \n", "(SELECT \n", "site_id::int as outlet_id,\n", "b.name as outlet_name,\n", "date(shift_start_time+interval '330' minute) as date,\n", "employee_id,\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 0 and extract(hour from (shift_end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 0 and extract(hour from (shift_end_time + interval '5.5 hours')) > 0 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 0 and extract(hour from (shift_end_time + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 0 and extract(hour from (shift_end_time + interval '5.5 hours')) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 1 and extract(hour from (shift_end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 1 and extract(hour from (shift_end_time + interval '5.5 hours')) > 1 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 1 and extract(hour from (shift_end_time + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 1 and extract(hour from (shift_end_time + interval '5.5 hours')) > 1 \n", "                    then 60 else 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 2 and extract(hour from (shift_end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 2 and extract(hour from (shift_end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 2 and extract(hour from (shift_end_time + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 2 and extract(hour from (shift_end_time + interval '5.5 hours')) > 2 \n", "                    then 60 else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 3 and extract(hour from (shift_end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 3 and extract(hour from (shift_end_time + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 3 and extract(hour from (shift_end_time + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 3 and extract(hour from (shift_end_time + interval '5.5 hours')) > 3 \n", "                    then 60 else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 4 and extract(hour from (shift_end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 4 and extract(hour from (shift_end_time + interval '5.5 hours')) > 4 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 4 and extract(hour from (shift_end_time + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 4 and extract(hour from (shift_end_time + interval '5.5 hours')) > 4 \n", "                    then 60 else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 5 and extract(hour from (shift_end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 5 and extract(hour from (shift_end_time + interval '5.5 hours')) > 5 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 5 and extract(hour from (shift_end_time + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 5 and extract(hour from (shift_end_time + interval '5.5 hours')) > 5 \n", "                    then 60 else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 6 and extract(hour from (shift_end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 6 and extract(hour from (shift_end_time + interval '5.5 hours')) > 6 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 6 and extract(hour from (shift_end_time + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 6 and extract(hour from (shift_end_time + interval '5.5 hours')) > 6 \n", "                    then 60 else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 7 and extract(hour from (shift_end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 7 and extract(hour from (shift_end_time + interval '5.5 hours')) > 7 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 7 and extract(hour from (shift_end_time + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 7 and extract(hour from (shift_end_time + interval '5.5 hours')) > 7 \n", "                    then 60 else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 8 and extract(hour from (shift_end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 8 and extract(hour from (shift_end_time + interval '5.5 hours')) > 8 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 8 and extract(hour from (shift_end_time + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 8 and extract(hour from (shift_end_time + interval '5.5 hours')) > 8 \n", "                    then 60 else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 9 and extract(hour from (shift_end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 9 and extract(hour from (shift_end_time + interval '5.5 hours')) > 9 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 9 and extract(hour from (shift_end_time + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 9 and extract(hour from (shift_end_time + interval '5.5 hours')) > 9 \n", "                    then 60 else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 10 and extract(hour from (shift_end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 10 and extract(hour from (shift_end_time + interval '5.5 hours')) > 10 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 10 and extract(hour from (shift_end_time + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 10 and extract(hour from (shift_end_time + interval '5.5 hours')) > 10 \n", "                    then 60 else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 11 and extract(hour from (shift_end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 11 and extract(hour from (shift_end_time + interval '5.5 hours')) > 11 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 11 and extract(hour from (shift_end_time + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 11 and extract(hour from (shift_end_time + interval '5.5 hours')) > 11 \n", "                    then 60 else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 12 and extract(hour from (shift_end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 12 and extract(hour from (shift_end_time + interval '5.5 hours')) > 12 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 12 and extract(hour from (shift_end_time + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 12 and extract(hour from (shift_end_time + interval '5.5 hours')) > 12 \n", "                    then 60 else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 13 and extract(hour from (shift_end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 13 and extract(hour from (shift_end_time + interval '5.5 hours')) > 13 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 13 and extract(hour from (shift_end_time + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 13 and extract(hour from (shift_end_time + interval '5.5 hours')) > 13 \n", "                    then 60 else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 14 and extract(hour from (shift_end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 14 and extract(hour from (shift_end_time + interval '5.5 hours')) > 14 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 14 and extract(hour from (shift_end_time + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 14 and extract(hour from (shift_end_time + interval '5.5 hours')) > 14 \n", "                    then 60 else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 15 and extract(hour from (shift_end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 15 and extract(hour from (shift_end_time + interval '5.5 hours')) > 15 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 15 and extract(hour from (shift_end_time + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 15 and extract(hour from (shift_end_time + interval '5.5 hours')) > 15 \n", "                    then 60 else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 16 and extract(hour from (shift_end_time + interval '5.5 hours')) = 162 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 16 and extract(hour from (shift_end_time + interval '5.5 hours')) > 16 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 16 and extract(hour from (shift_end_time + interval '5.5 hours')) = 16 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 16 and extract(hour from (shift_end_time + interval '5.5 hours')) > 16 \n", "                    then 60 else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 17 and extract(hour from (shift_end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 17 and extract(hour from (shift_end_time + interval '5.5 hours')) > 17 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 17 and extract(hour from (shift_end_time + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 17 and extract(hour from (shift_end_time + interval '5.5 hours')) > 17 \n", "                    then 60 else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 18 and extract(hour from (shift_end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 18 and extract(hour from (shift_end_time + interval '5.5 hours')) > 18 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 18 and extract(hour from (shift_end_time + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 18 and extract(hour from (shift_end_time + interval '5.5 hours')) > 18 \n", "                    then 60 else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 19 and extract(hour from (shift_end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 19 and extract(hour from (shift_end_time + interval '5.5 hours')) > 19 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 19 and extract(hour from (shift_end_time + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 19 and extract(hour from (shift_end_time + interval '5.5 hours')) > 19 \n", "                    then 60 else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 20 and extract(hour from (shift_end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 20 and extract(hour from (shift_end_time + interval '5.5 hours')) > 20 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 20 and extract(hour from (shift_end_time + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 20 and extract(hour from (shift_end_time + interval '5.5 hours')) > 20 \n", "                    then 60 else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 21 and extract(hour from (shift_end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 21 and extract(hour from (shift_end_time + interval '5.5 hours')) > 21 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 21 and extract(hour from (shift_end_time + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 21 and extract(hour from (shift_end_time + interval '5.5 hours')) > 21 \n", "                    then 60 else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 22 and extract(hour from (shift_end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 22 and extract(hour from (shift_end_time + interval '5.5 hours')) > 22 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 22 and extract(hour from (shift_end_time + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 22 and extract(hour from (shift_end_time + interval '5.5 hours')) > 22 \n", "                    then 60 else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 23 and extract(hour from (shift_end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from shift_end_time - shift_start_time)/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) = 23 and extract(hour from (shift_end_time + interval '5.5 hours')) > 23 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (shift_start_time + interval '5.5 hours'))::int), (shift_start_time + interval '5.5 hours'))  - (shift_start_time + interval '5.5 hours')))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 23 and extract(hour from (shift_end_time + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from ((shift_end_time + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (shift_end_time + interval '5.5 hours')))*-1, (shift_end_time + interval '5.5 hours'))))/60\n", "            when extract(hour from (shift_start_time + interval '5.5 hours')) < 23 and extract(hour from (shift_end_time + interval '5.5 hours')) > 23 \n", "                    then 60 else 0 end) as \"23\"\n", "   FROM lake_ops_management.shift a\n", "      inner join lake_retail.console_outlet b on a.site_id = b.id\n", "   where date(shift_start_time+ interval '330 minutes') between cast('{start_date}' as date) and cast('{end_date}' as date)\n", "   and shift_end_time >= shift_start_time \n", "   and site_id in {outlets_list}\n", "   and employee_id in (select distinct picker_id from dwh.fact_supply_chain_order_details where (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and outlet_id in {outlets_list})\n", "   GROUP BY 1,2,3,4\n", "   \n", "union\n", "\n", "SELECT \n", "a.outlet_id,\n", "b.name as outlet_name,\n", "date(start_timestamp+interval '330' minute) as date,\n", "employee_id,\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 0 and extract(hour from (end_timestamp + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 0 and extract(hour from (end_timestamp + interval '5.5 hours')) > 0 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 0 and extract(hour from (end_timestamp + interval '5.5 hours')) = 0 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 0 and extract(hour from (end_timestamp + interval '5.5 hours')) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 1 and extract(hour from (end_timestamp + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 1 and extract(hour from (end_timestamp + interval '5.5 hours')) > 1 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 1 and extract(hour from (end_timestamp + interval '5.5 hours')) = 1 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 1 and extract(hour from (end_timestamp + interval '5.5 hours')) > 1 \n", "                    then 60 else 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 2 and extract(hour from (end_timestamp + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 2 and extract(hour from (end_timestamp + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 2 and extract(hour from (end_timestamp + interval '5.5 hours')) = 2 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 2 and extract(hour from (end_timestamp + interval '5.5 hours')) > 2 \n", "                    then 60 else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 3 and extract(hour from (end_timestamp + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 3 and extract(hour from (end_timestamp + interval '5.5 hours')) > 2 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 3 and extract(hour from (end_timestamp + interval '5.5 hours')) = 3 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 3 and extract(hour from (end_timestamp + interval '5.5 hours')) > 3 \n", "                    then 60 else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 4 and extract(hour from (end_timestamp + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 4 and extract(hour from (end_timestamp + interval '5.5 hours')) > 4 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 4 and extract(hour from (end_timestamp + interval '5.5 hours')) = 4 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 4 and extract(hour from (end_timestamp + interval '5.5 hours')) > 4 \n", "                    then 60 else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 5 and extract(hour from (end_timestamp + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 5 and extract(hour from (end_timestamp + interval '5.5 hours')) > 5 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 5 and extract(hour from (end_timestamp + interval '5.5 hours')) = 5 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 5 and extract(hour from (end_timestamp + interval '5.5 hours')) > 5 \n", "                    then 60 else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 6 and extract(hour from (end_timestamp + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 6 and extract(hour from (end_timestamp + interval '5.5 hours')) > 6 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 6 and extract(hour from (end_timestamp + interval '5.5 hours')) = 6 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 6 and extract(hour from (end_timestamp + interval '5.5 hours')) > 6 \n", "                    then 60 else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 7 and extract(hour from (end_timestamp + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 7 and extract(hour from (end_timestamp + interval '5.5 hours')) > 7 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 7 and extract(hour from (end_timestamp + interval '5.5 hours')) = 7 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 7 and extract(hour from (end_timestamp + interval '5.5 hours')) > 7 \n", "                    then 60 else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 8 and extract(hour from (end_timestamp + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 8 and extract(hour from (end_timestamp + interval '5.5 hours')) > 8 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 8 and extract(hour from (end_timestamp + interval '5.5 hours')) = 8 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 8 and extract(hour from (end_timestamp + interval '5.5 hours')) > 8 \n", "                    then 60 else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 9 and extract(hour from (end_timestamp + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 9 and extract(hour from (end_timestamp + interval '5.5 hours')) > 9 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 9 and extract(hour from (end_timestamp + interval '5.5 hours')) = 9 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 9 and extract(hour from (end_timestamp + interval '5.5 hours')) > 9 \n", "                    then 60 else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 10 and extract(hour from (end_timestamp + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 10 and extract(hour from (end_timestamp + interval '5.5 hours')) > 10 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 10 and extract(hour from (end_timestamp + interval '5.5 hours')) = 10 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 10 and extract(hour from (end_timestamp + interval '5.5 hours')) > 10 \n", "                    then 60 else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 11 and extract(hour from (end_timestamp + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 11 and extract(hour from (end_timestamp + interval '5.5 hours')) > 11 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 11 and extract(hour from (end_timestamp + interval '5.5 hours')) = 11 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 11 and extract(hour from (end_timestamp + interval '5.5 hours')) > 11 \n", "                    then 60 else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 12 and extract(hour from (end_timestamp + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 12 and extract(hour from (end_timestamp + interval '5.5 hours')) > 12 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 12 and extract(hour from (end_timestamp + interval '5.5 hours')) = 12 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 12 and extract(hour from (end_timestamp + interval '5.5 hours')) > 12 \n", "                    then 60 else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 13 and extract(hour from (end_timestamp + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 13 and extract(hour from (end_timestamp + interval '5.5 hours')) > 13 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 13 and extract(hour from (end_timestamp + interval '5.5 hours')) = 13 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 13 and extract(hour from (end_timestamp + interval '5.5 hours')) > 13 \n", "                    then 60 else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 14 and extract(hour from (end_timestamp + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 14 and extract(hour from (end_timestamp + interval '5.5 hours')) > 14 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 14 and extract(hour from (end_timestamp + interval '5.5 hours')) = 14 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 14 and extract(hour from (end_timestamp + interval '5.5 hours')) > 14 \n", "                    then 60 else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 15 and extract(hour from (end_timestamp + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 15 and extract(hour from (end_timestamp + interval '5.5 hours')) > 15 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 15 and extract(hour from (end_timestamp + interval '5.5 hours')) = 15 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 15 and extract(hour from (end_timestamp + interval '5.5 hours')) > 15 \n", "                    then 60 else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 16 and extract(hour from (end_timestamp + interval '5.5 hours')) = 162 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 16 and extract(hour from (end_timestamp + interval '5.5 hours')) > 16 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 16 and extract(hour from (end_timestamp + interval '5.5 hours')) = 16 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 16 and extract(hour from (end_timestamp + interval '5.5 hours')) > 16 \n", "                    then 60 else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 17 and extract(hour from (end_timestamp + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 17 and extract(hour from (end_timestamp + interval '5.5 hours')) > 17 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 17 and extract(hour from (end_timestamp + interval '5.5 hours')) = 17 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 17 and extract(hour from (end_timestamp + interval '5.5 hours')) > 17 \n", "                    then 60 else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 18 and extract(hour from (end_timestamp + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 18 and extract(hour from (end_timestamp + interval '5.5 hours')) > 18 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 18 and extract(hour from (end_timestamp + interval '5.5 hours')) = 18 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 18 and extract(hour from (end_timestamp + interval '5.5 hours')) > 18 \n", "                    then 60 else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 19 and extract(hour from (end_timestamp + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 19 and extract(hour from (end_timestamp + interval '5.5 hours')) > 19 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 19 and extract(hour from (end_timestamp + interval '5.5 hours')) = 19 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 19 and extract(hour from (end_timestamp + interval '5.5 hours')) > 19 \n", "                    then 60 else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 20 and extract(hour from (end_timestamp + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 20 and extract(hour from (end_timestamp + interval '5.5 hours')) > 20 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 20 and extract(hour from (end_timestamp + interval '5.5 hours')) = 20 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 20 and extract(hour from (end_timestamp + interval '5.5 hours')) > 20 \n", "                    then 60 else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 21 and extract(hour from (end_timestamp + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 21 and extract(hour from (end_timestamp + interval '5.5 hours')) > 21 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 21 and extract(hour from (end_timestamp + interval '5.5 hours')) = 21 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 21 and extract(hour from (end_timestamp + interval '5.5 hours')) > 21 \n", "                    then 60 else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 22 and extract(hour from (end_timestamp + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 22 and extract(hour from (end_timestamp + interval '5.5 hours')) > 22 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 22 and extract(hour from (end_timestamp + interval '5.5 hours')) = 22 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 22 and extract(hour from (end_timestamp + interval '5.5 hours')) > 22 \n", "                    then 60 else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 23 and extract(hour from (end_timestamp + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from end_timestamp - start_timestamp)/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) = 23 and extract(hour from (end_timestamp + interval '5.5 hours')) > 23 \n", "                    then extract(epoch from (DATEADD(minute, (60 - extract(minutes from (start_timestamp + interval '5.5 hours'))::int), (start_timestamp + interval '5.5 hours'))  - (start_timestamp + interval '5.5 hours')))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 23 and extract(hour from (end_timestamp + interval '5.5 hours')) = 23 \n", "                    then extract(epoch from ((end_timestamp + interval '5.5 hours') - DATEADD(minute, (extract(minutes from (end_timestamp + interval '5.5 hours')))*-1, (end_timestamp + interval '5.5 hours'))))/60\n", "            when extract(hour from (start_timestamp + interval '5.5 hours')) < 23 and extract(hour from (end_timestamp + interval '5.5 hours')) > 23 \n", "                    then 60 else 0 end) as \"23\"\n", "   FROM lake_warehouse_location.user_attendance a\n", "   inner join lake_retail.console_outlet b on a.outlet_id = b.id\n", "   where date(start_timestamp+ interval '330 minutes') between cast('{start_date}' as date) and cast('{end_date}' as date)\n", "   and end_timestamp >= start_timestamp\n", "   and a.outlet_id in {outlets_list}\n", "   and employee_id in (select distinct picker_id from dwh.fact_supply_chain_order_details where (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and outlet_id in {outlets_list})\n", "   GROUP BY 1,2,3,4\n", "   \n", "   \n", "\n", "\n", "   )\n", "   \n", "select * from roster order by 1,2,3,4\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "attendance_data = pd.read_sql(attendance_q1, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "dd8f40b6-58f8-4bab-847e-a7c2b313aef1", "metadata": {}, "outputs": [], "source": ["attendance_data_melt_new = pd.melt(\n", "    attendance_data,\n", "    id_vars=[\"outlet_id\", \"outlet_name\", \"date\", \"employee_id\"],\n", "    value_vars=[\n", "        \"0\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "    ],\n", "    var_name=\"hour\",\n", "    value_name=\"login_time\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9d78a076-26f3-4b9b-b790-b30e095800b7", "metadata": {}, "outputs": [], "source": ["attendance_data_melt_new_group = (\n", "    attendance_data_melt_new[attendance_data_melt_new[\"login_time\"] > 0]\n", "    .groupby([\"outlet_id\", \"outlet_name\", \"date\", \"hour\"])\n", "    .agg({\"employee_id\": \"nunique\", \"login_time\": \"mean\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "160fbdcf-a5d8-460f-91e9-b7b9182ba9f1", "metadata": {}, "outputs": [], "source": ["attendance_data_melt_new_group[\"hour\"] = attendance_data_melt_new_group[\"hour\"].astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7497c378-609a-42c6-9396-2a2ec6b5ea7e", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    attendance_data_melt_new_group,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"hourly_attendance_login_raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9f0b2ccb-5d0d-4ee4-b115-09c2f656eb95", "metadata": {}, "outputs": [], "source": ["login_adher_time_q = \"\"\"with first_login_time as \n", "(select date(role_start_time+interval '5.5 hours') as date_ts,site_id::int as outlet_id,\n", "min(role_start_time+interval '5.5 hours') as first_login_time,\n", "max(role_end_time+interval '5.5 hours') as last_logout_time\n", "from lake_ops_management.shift_roles\n", "where role='PICKER' and site_id in {outlets_list}\n", "and employee_id ilike 'GW%%'\n", "and date(role_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "group by 1,2\n", "\n", "union\n", "\n", "SELECT \n", "    DATE(pos_timestamp +interval'5.5 hours') AS date_ts,\n", "    outlet_id,\n", "    min(case when action='LOGIN' then (pos_timestamp+interval '5.5 hours') end) AS first_login_time,\n", "    max(case when action in ('LOGOUT','FORCE_LOGOUT') then (pos_timestamp+interval '5.5 hours') end) AS last_logout_time\n", "FROM \n", "    lake_warehouse_location.warehouse_device_user_log \n", "        AS login\n", "WHERE \n", "    (pos_timestamp+interval'5.5 hours') >= '{start_date}'\n", "    and (pos_timestamp+interval'5.5 hours') <= '{end_date}'::date+interval '1 day'\n", "    AND action in ('LOGI<PERSON>','LOGOUT','FORCE_LOGOUT') \n", "    and user_name ilike 'GW%%'\n", "    and role='PICKER' and outlet_id in {outlets_list}\n", "group by 1,2\n", "),\n", "\n", "first_login_time_agg as\n", "(\n", "select date_ts, outlet_id,\n", "min(first_login_time) as first_login_time,\n", "max(last_logout_time) as last_logout_time\n", "from first_login_time\n", "group by 1,2\n", ")\n", "\n", "select date_ts,site_id::int as outlet_id,\n", "co.name as outlet_name,\n", "first_login_time,\n", "last_logout_time,\n", "count(distinct case when (role_start_time+interval '5.5 hours') between date_ts::varchar+' 06:00:00' and date_ts::varchar+' 06:30:00' then employee_id end) as total_employees_login_morning,\n", "count(distinct case when (role_end_time+interval '5.5 hours') between date_ts::varchar+' 23:30:00' and date_ts::varchar+' 24:00:00' then employee_id end) as total_employees_login_evening\n", "\n", "from lake_ops_management.shift_roles a\n", "inner join first_login_time_agg b on date(role_start_time+interval '5.5 hours') = b.date_ts and a.site_id = b.outlet_id\n", "inner join lake_retail.console_outlet co on co.id=a.site_id\n", "where role='PICKER' and site_id in {outlets_list}\n", "and employee_id ilike 'GW%%'\n", "and date(role_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "group by 1,2,3,4,5\n", "\n", "union\n", "\n", "\n", "select date_ts,a.outlet_id,\n", "co.name as outlet_name,\n", "first_login_time,\n", "last_logout_time,\n", "count(distinct case when (pos_timestamp+interval '5.5 hours') between date_ts::varchar+' 06:00:00' and date_ts::varchar+' 06:30:00' then a.user_name end) as total_employees_login_morning,\n", "count(distinct case when action in ('LOGOUT','FORCE_LOGOUT') and (pos_timestamp+interval '5.5 hours') between date_ts::varchar+' 23:30:00' and date_ts::varchar+' 24:00:00' then a.user_name end) as total_employees_login_evening\n", "\n", "from lake_warehouse_location.warehouse_device_user_log a\n", "inner join lake_retail.console_outlet co on co.id=a.outlet_id\n", "inner join first_login_time_agg b on date(pos_timestamp+interval '5.5 hours') = b.date_ts and a.outlet_id = b.outlet_id\n", "where role='PICKER' and a.outlet_id in {outlets_list} and action in ('LOGIN','LOGOUT','FORCE_LOGOUT') \n", "and user_name ilike 'GW%%'\n", "and date(pos_timestamp+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "group by 1,2,3,4,5\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "login_adher_time_data = pd.read_sql(login_adher_time_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "8e36ce5e-cbc7-4834-ab66-b83b09bdd438", "metadata": {}, "outputs": [], "source": ["login_adher_time_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "efa73e3d-0c13-49e1-b28b-a0487cecb5ef", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    login_adher_time_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"login_adherence_raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "794e501c-b9b5-4b20-8e47-bee9708ac4ab", "metadata": {}, "outputs": [], "source": ["audit_q = \"\"\"\n", "\n", "WITH DATA AS\n", "  (SELECT id,\n", "          state,\n", "          outlet_id AS store_id,\n", "          CASE\n", "              WHEN creation_source=3 THEN 'Expired'\n", "              WHEN creation_source=4 THEN 'Self'\n", "              WHEN creation_source=5 THEN 'PNA'\n", "              WHEN creation_source=6 THEN 'RAISE_AUDIT'\n", "              WHEN creation_source=8 THEN 'DATA_GENERATED_MED_PRIORITY'\n", "              WHEN creation_source=10 THEN 'Pickdelay'\n", "              WHEN creation_source=15 THEN 'Ims/ils diff'\n", "              WHEN creation_source=20 THEN'DATA_GENERATED_LOW_PRIORITY'\n", "              ELSE 'Unknown'   --Other creation sources can be added as and when they're generated\n", "          END AS creation_source,\n", "          date(created_at) as creation_date,\n", "          created_at AS audit_creation_ts,\n", "          created_by AS audit_creation_source,\n", "          updated_at AS audit_completion_ts\n", "   FROM lake_warehouse_location.audit_task\n", "   WHERE date(created_at + interval '330 MINUTES') BETWEEN '{start_date}' and '{end_date}'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,8), total AS\n", "  (SELECT creation_date,\n", "  store_id,\n", "          creation_source,\n", "          count(id) AS total_audit_tasks\n", "   FROM DATA\n", "   GROUP BY 1,\n", "            2,3),\n", "                completed AS\n", "  (SELECT creation_date,\n", "  store_id,\n", "          creation_source,\n", "          count(id) AS completed_audits\n", "   FROM DATA\n", "   WHERE state=3\n", "   GROUP BY 1,\n", "            2,3),\n", "                sla AS\n", "  (SELECT creation_date,store_id,\n", "          creation_source,\n", "          CASE\n", "              WHEN count(id)=NULL THEN 0\n", "              ELSE count(id)\n", "          END AS audits_in_sla\n", "   FROM DATA\n", "   WHERE state=3\n", "     AND audit_completion_ts + interval '330 MINUTES' BETWEEN audit_creation_ts + interval '330 MINUTES' AND audit_creation_ts + interval '570 MINUTES'\n", "   GROUP BY 1,\n", "            2,3),\n", "                data_new AS\n", "  (SELECT id,\n", "          site_id AS outlet_id,\n", "          case \n", "          when reason_code='Bad Stock' then 'Bad Stock'\n", "          when reason_code in('Business Request','Audit tasks for stock variance check') then 'Business Checks'\n", "          when reason_code in('STO Damage (From Origin)','Vendor Product Damage','STO Damage (In-transit issues)',\n", "          'STO Damage (Transport)','Packaging Damage','Rodent Damage','STO Damage (Source)','Vendor Damage') then 'Damage'\n", "          when reason_code in ('Near Expiry','STO Near Expiry','STO Expiry') then 'Expiry'\n", "          when reason_code in('Rotten','FnV - Weight Short','FnV - FR Dump','FnV Weight adjustment (SHORT weight)',\n", "          'NR (Not Received F&V)','Forward Rejection (F&V)','FnV - NR Dump') then 'FnV'\n", "          when reason_code in('GOOD_STOCK_MIGRATION','Good Return to the Vendor') then 'Good Stock'\n", "          when reason_code='MANUAL_AUDIT' then 'Manual'\n", "          when reason_code in('Mishandled at your facility','Double GRN','Pilferage Issue') then 'Mishandling'\n", "          when reason_code in('Missing Quantity Found','Quantity Mismatch','Variant Mismatch','Quantity Missing') then 'Missing Qty'\n", "          when reason_code='PNA' then 'PNA'\n", "          else 'Others'\n", "          end AS creation_source,\n", "          status,\n", "          created_date,\n", "          date(created_date) as creation_date,\n", "          last_modified_date --count(id) as total_audit_tasks\n", "\n", " FROM lake_storeops.activity  --join lake_storeops.item_activity ia on a.site_id=ia.site_id\n", "   WHERE TYPE='INV_AUDIT' \n", "   \n", "     AND date(created_date) BETWEEN '{start_date}' and '{end_date}'\n", "     AND reason_code IS NOT NULL --and a.status='COMPLETED' and outlet_id=3377\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,7 --limit 10\n", "),\n", "                total_new AS\n", "  (SELECT creation_date,outlet_id,\n", "          creation_source,\n", "          count(id) AS total_audit_tasks\n", "   FROM data_new\n", "   GROUP BY 1,\n", "            2,3),\n", "                completed_new AS\n", "  (SELECT creation_date,outlet_id,\n", "          creation_source,\n", "          count(id) AS completed_audits\n", "   FROM data_new\n", "   WHERE status='COMPLETED'\n", "   GROUP BY 1,\n", "            2,3),\n", "                sla_new AS\n", "  (SELECT creation_date,outlet_id,\n", "          creation_source,\n", "          CASE\n", "              WHEN count(id)=NULL THEN 0\n", "              ELSE count(id)\n", "          END AS audits_in_sla\n", "   FROM data_new\n", "   WHERE status='COMPLETED'\n", "     AND last_modified_date B<PERSON><PERSON><PERSON><PERSON> created_date + interval '330 MINUTES' AND created_date + interval '570 MINUTES'\n", "   GROUP BY 1,\n", "            2,3)\n", "            \n", "            \n", "SELECT DISTINCT date(d.audit_creation_ts),\n", "                cast(d.store_id AS integer),\n", "                co.name as outlet_name,\n", "                d.creation_source,\n", "                coalesce(t.total_audit_tasks,0) AS total_audit_tasks,\n", "                coalesce(c.completed_audits,0) AS completed_audits,\n", "                coalesce(s.audits_in_sla,0) AS audits_in_sla,\n", "                coalesce(cast(c.completed_audits AS decimal)/cast(t.total_audit_tasks AS decimal)*100,0) AS perc_completed,\n", "                coalesce(cast(s.audits_in_sla AS decimal)/cast(c.completed_audits AS decimal)*100,0) AS perc_completed_in4hrs\n", "FROM DATA d\n", "inner join lake_retail.console_outlet co on co.id = d.store_id\n", "JOIN total t ON d.store_id=t.store_id\n", "AND d.creation_source=t.creation_source and d.creation_date=t.creation_date\n", "JOIN completed c ON c.store_id=d.store_id\n", "AND c.creation_source=d.creation_source and c.creation_date=d.creation_date\n", "LEFT JOIN sla s ON s.store_id=c.store_id\n", "AND s.creation_source=c.creation_source and s.creation_date=c.creation_date\n", "WHERE date(audit_creation_ts + interval '330 MINUTES') BETWEEN '{start_date}' and '{end_date}' --and d.store_id=2579\n", "and co.id in {outlets_list}\n", "\n", "UNION ALL\n", "\n", "SELECT DISTINCT date(d2.created_date),\n", "                cast(d2.outlet_id AS integer),\n", "                co.name as outlet_name,\n", "                d2.creation_source,\n", "                coalesce(t2.total_audit_tasks,0) as total_audit_tasks,\n", "                coalesce(c2.completed_audits,0) as completed_audits,\n", "                coalesce(s2.audits_in_sla,0) as audits_in_sla,\n", "                coalesce(cast(c2.completed_audits AS decimal)/cast(t2.total_audit_tasks AS decimal)*100,0) AS perc_completed,\n", "                coalesce(cast(s2.audits_in_sla AS decimal)/cast(c2.completed_audits AS decimal)*100,0) AS perc_completed_in4hrs\n", "FROM data_new d2\n", "inner join lake_retail.console_outlet co on co.id = d2.outlet_id\n", "JOIN total_new t2 ON d2.outlet_id=t2.outlet_id\n", "AND d2.creation_source=t2.creation_source and d2.creation_date=t2.creation_date\n", "JOIN completed_new c2 ON d2.outlet_id=c2.outlet_id\n", "AND d2.creation_source=c2.creation_source and d2.creation_date=c2.creation_date\n", "RIGHT JOIN sla_new s2 ON s2.outlet_id=c2.outlet_id\n", "AND s2.creation_source=c2.creation_source and s2.creation_date=c2.creation_date\n", "where created_date + interval '330 MINUTES' BETWEEN '{start_date}' and '{end_date}'\n", "--and d2.outlet_id=2579\n", "and co.id in {outlets_list}\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "audit_data = pd.read_sql(audit_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "10f84239-62c2-4bb2-964f-8798edf4ea56", "metadata": {}, "outputs": [], "source": ["audit_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ec6d8e7d-6905-4ae3-860e-184a09df52ba", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    audit_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Audit_raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61f98169-49fd-450f-845c-0c4fa1bba810", "metadata": {}, "outputs": [], "source": ["# break_time as\n", "# ( select date(break_start_time+interval '5.5 hours') as date_ts, site_id, employee_id,\n", "#   coalesce(sum(datediff(seconds,break_start_time,coalesce(break_end_time,current_timestamp::timestamp)))/60.0,0) as total_break_time\n", "#   from lake_ops_management.break_time\n", "#   where (date(break_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "#   and site_id in {outlets_list}\n", "#   group by 1,2,3\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "63f88e11-86bf-481e-98e4-eb94bc233779", "metadata": {}, "outputs": [], "source": ["active_time_q = \"\"\"\n", "with\n", "active_time as\n", "( select date(role_start_time+interval '5.5 hours') as date_ts, site_id, employee_id,\n", "  coalesce(sum(datediff(seconds,role_start_time,coalesce(role_end_time,current_timestamp::timestamp)))/60.0,0) as total_active_time,\n", "min(role_start_time+interval '5.5 hours') as first_login_time,\n", "max(role_end_time+interval '5.5 hours') as last_logout_time\n", "  from lake_ops_management.shift_roles\n", "  where (date(role_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "  and site_id in {outlets_list}\n", "  group by 1,2,3\n", ")\n", "\n", "\n", "\n", "\n", "\n", "select date(date) as date,store_id,\n", "replace(store_name,' (NM)','') as store_name,city,mp.employee_id,(case when employee_name='' then first_name else employee_name end) as employee_name,\n", "(case when mp.role='' then u.role else mp.role end) as role ,total_putaway_quantity,total_picked_quantity,score,total_orders_picked,break_time,\n", "picking_logged_in_time,putaway_logged_in_time,login,logout,roster_on_time,total_login_time,wait_time_in_queue,attendance_punched,shift_type,total_app_time,\n", "picking_time,putaway_time,ownership,\n", "coalesce(total_active_time,0) as active_time,\n", "first_login_time,\n", "last_logout_time\n", "from metrics.instore_manpower_performance mp \n", "left join lake_storeops.user_details u on u.employee_id = mp.employee_id\n", "left join active_time a on a.employee_id = mp.employee_id and a.site_id = mp.store_id and a.date_ts = date(mp.date)\n", "\n", "----left join break_time b on a.employee_id = b.employee_id and a.site_id = b.site_id and a.date_ts = b.date_ts\n", "\n", "where\n", "date(date) between '{start_date}' and '{end_date}'\n", "and mp.store_id in {outlets_list}\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "active_time_data = pd.read_sql(active_time_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "135f197a-8c2a-4d97-88e1-e19ea109c34f", "metadata": {}, "outputs": [], "source": ["active_time_data[active_time_data[\"employee_id\"] == \"GW35\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2985e7fc-4d0d-4f58-9f98-64fbd6d76a66", "metadata": {}, "outputs": [], "source": ["audit_q = \"\"\"\n", "\n", "select date,outlet_id,employee_id,\n", "count(distinct case when audit_time_in_mins>1 then id end) as audit_more_than_1_min,\n", "count(distinct id) as total_audits\n", "from\n", "(select \n", "       date(a.started_at+interval '5.5 hours') as date,\n", "       a.outlet_id,\n", "       a.id,\n", "       updated_by as employee_id,\n", "       sum(datediff(seconds,a.started_at,a.updated_at))/60.0 as audit_time_in_mins\n", "from lake_warehouse_location.audit_task a\n", "where state=3 and outlet_id in {outlets_list}\n", "      and (date(a.started_at+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "     group by 1,2,3,4) as x\n", "     group by 1,2,3\n", "     \n", "     \n", "union\n", "\n", "select date,outlet_id,employee_id,\n", "count(distinct case when audit_time_in_mins>1 then id end) as audit_more_than_1_min,\n", "count(distinct id) as total_audits\n", "from\n", "(select \n", "date(created_date+interval '5.5 hours') as date,\n", "site_id::int as outlet_id, \n", "a.id,\n", "last_modified_by as employee_id,\n", "sum(datediff(seconds, created_date, completion_time)/60.0) as audit_time_in_mins\n", "from lake_storeops.item_activity a\n", "inner join lake_retail.console_outlet c on c.id =a.site_id \n", "where activity_type='INV_AUDIT' and status='PROCESSED' and business_type_id=7\n", "and site_id in {outlets_list}\n", "and date(created_date+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "group by 1,2,3,4) as x\n", "group by 1,2,3\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "audit_data = pd.read_sql(audit_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "80a61276-5f11-4e23-b8c6-ba2f35e31f0a", "metadata": {}, "outputs": [], "source": ["audit_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "420207fd-11d2-4715-9de5-c6c23fa2e4fe", "metadata": {}, "outputs": [], "source": ["stars_q = \"\"\"\n", "select date, outlet_id,outlet_name,employee_id, employee_name,\n", " count(distinct order_id) as total_orders,\n", "count(distinct case when picking_assign_to_billing_time<=(picked_quantity*18)+30 and required_quantity=picked_quantity then picklist_id end) as total_beat_orders,\n", "sum(picked_quantity) as picked_quantity,\n", "count(distinct case when required_quantity!=picked_quantity then picklist_id end) as pna_orders\n", "        from\n", "        (SELECT\n", "            date(wpl.assigned_at+interval '330 minutes') as date,\n", "            wpl.assigned_to_name as employee_id, wpl.outlet_id, co.name as outlet_name,\n", "            u.name as employee_name,\n", "            l.name as city,\n", "            wpl.id as picklist_id,\n", "            plo.order_id,\n", "            wpl.picking_started_at AS picking_started_ts_utc,\n", "            pll.pos_timestamp AS picking_completed_ts_utc,\n", "            datediff(seconds,assigned_at,wbl1.pos_timestamp) as picking_assign_to_billing_time,\n", "            sum(required_quantity) as required_quantity,\n", "            sum(picked_quantity) as picked_quantity\n", "       \n", "        FROM\n", "             lake_warehouse_location.warehouse_pick_list wpl\n", "        inner join lake_warehouse_location.warehouse_pick_list_order_mapping plo on plo.pick_list_id = wpl.id\n", "                \n", "        JOIN\n", "            lake_warehouse_location.pick_list_log \n", "                AS pll\n", "                ON pll.pick_list_id = wpl.id\n", "        inner join lake_retail.console_outlet co on co.id = wpl.outlet_id \n", "        -- and business_type_id=7\n", "        INNER JOIN \n", "        lake_retail.console_location \n", "            AS l \n", "            ON l.id=co.tax_location_id\n", "        left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = wpl.id\n", "        AND date(pli.created_at+INTERVAL '5.5 HOURS') between '{start_date}' and '{end_date}'\n", "        \n", "        LEFT JOIN \n", "        lake_retail.warehouse_user \n", "            AS u \n", "            ON lower(wpl.assigned_to_name) = lower(u.emp_id)\n", "        left join\n", "        (select max(pos_timestamp) as pos_timestamp, order_id from lake_warehouse_location.warehouse_order_billing_log \n", "        where action = 'BILLING_END'\n", "        and date(pos_timestamp+INTERVAL '5.5 HOURS') between '{start_date}' and '{end_date}'\n", "        group by 2) wbl1 on wbl1.order_id = plo.order_id\n", "        WHERE\n", "         pll.picklist_state in (4) -- state 4 means picking completed\n", "            AND date(pll.pos_timestamp+INTERVAL '5.5 HOURS') between '{start_date}' and '{end_date}'\n", "            AND date(wpl.created_at+INTERVAL '5.5 HOURS') between '{start_date}' and '{end_date}'\n", "            AND date(plo.created_at+INTERVAL '5.5 HOURS') between '{start_date}' and '{end_date}'\n", "            and wpl.outlet_id in {outlets_list}\n", "        GROUP BY\n", "            1, 2, 3, 4,5,6,7,8,9,10,11) as x\n", "            group by 1,2,3,4,5\n", "\n", "union\n", "\n", "select date_ts,outlet_id, outlet_name,employee_id,\n", "employee_name,\n", "count(distinct order_id) as total_orders,\n", "count(distinct case when picking_assign_to_billing_time<=(picked_quantity*18)+30 and required_quantity=picked_quantity then order_id end) as total_beat_orders,\n", "sum(picked_quantity) as picked_quantity,\n", "count(distinct case when required_quantity!=picked_quantity then order_id end) as pna_orders\n", "from\n", "(select \n", "date(a.created_date) as date_ts,\n", "b.id as outlet_id,\n", "b.name as outlet_name,\n", "assigned_to_employee_id as employee_id,\n", "u.name as employee_name,\n", "l.name as city,\n", "a.entity_id as order_id,\n", "sum(datediff(seconds,assigned_time,pos_update_time)) as picking_assign_to_billing_time, \n", "sum(expected_count) as required_quantity,\n", "sum(processed_qty) as picked_quantity,\n", "count(distinct er_id) as number_of_orders_picked\n", "from lake_storeops.activity a\n", "inner join lake_retail.console_outlet b on b.id =cast(a.site_id as int)\n", "INNER JOIN \n", "        lake_retail.console_location \n", "            AS l \n", "            ON l.id=b.tax_location_id\n", "left join lake_retail.warehouse_user u on u.emp_id = a.assigned_to_employee_id\n", "left join (select external_order_id, site_id, max(pos_update_time) as pos_update_time\n", "from lake_trade_finance.biz_txn where biz_txn_type='B2C_SALE' and\n", "date(pos_update_time+interval '5.5 hours') between ('{start_date}'::date)-interval '1 days' and ('{end_date}'::date)+interval '1 days'\n", "group by 1,2\n", ") tf on tf.site_id = a.site_id and tf.external_order_id = a.entity_id\n", "\n", "where type in ('PICKING') and business_type_id=7 and a.site_id in {outlets_list}\n", "and a.status='COMPLETED' and (date(a.created_date + INTERVAL '5.5 hours') between '{start_date}' and '{end_date}')\n", "group by 1,2,3,4,5,6,7) as x\n", "group by 1,2,3,4,5\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "stars_data = pd.read_sql(stars_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "28c02607-bbb4-4e9c-97e3-b5d42854c1f1", "metadata": {}, "outputs": [], "source": ["stars_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f47fb67e-dcbf-4ba0-b8b1-c0f9d1ab1824", "metadata": {}, "outputs": [], "source": ["stars_data[stars_data[\"outlet_id\"] == 3671].groupby(\"date\")[\"total_beat_orders\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "922efba0-2190-4298-83c7-6af7fe0513e6", "metadata": {}, "outputs": [], "source": ["complaints_q = \"\"\"\n", "\n", "\n", "select date,\n", "outlet_id, outlet_name,\n", "employee_id,\n", "count(distinct case when complaint=1 then order_id end) as complaints_order\n", "from\n", "(SELECT date(delivery_date) as date,\n", "        co.name as outlet_name,\n", "       c.outlet_id,\n", "       c.order_id,\n", "       picker_id as employee_id,\n", "        max(case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') \n", "                    )\n", "         \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%')\n", "                )\n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%')\n", "                )\n", "                or ((\n", "                    (C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%') \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%'))\n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','')\n", "                )\n", "              \n", "                or ( \n", "                        C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Missing%%')\n", "                )\n", "           \n", "                or (( \n", "                        (C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                        or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%'))\n", "\n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing'))\n", "                \n", " or ((C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%'))\n", "  or ((C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%') and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%')  \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "  or (( (C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%') )\n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing','Item Missing'))\n", "\n", "         \n", "\n", "or\n", "            (C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_CATEGORY  ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%product%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%product%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%size%%')\n", "            or (C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Wrong%%Item%%'))\n", "            then 1 else 0 end) as complaint\n", "       \n", "       \n", "FROM \n", "metrics.complaints c\n", "inner join dwh.fact_supply_chain_order_details f on f.order_id = c.order_id\n", "inner join lake_retail.console_outlet co on co.id = c.outlet_id and business_type_id=7\n", "\n", "WHERE \n", "c.outlet_id in {outlets_list} and\n", "date(delivery_date) between '{start_date}' and '{end_date}'\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "group by 1,2,3,4,5) as x\n", "group by 1,2,3,4\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "complaints_data = pd.read_sql(complaints_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "1eb4aa62-0847-413a-a9a2-7a26a8aabc65", "metadata": {}, "outputs": [], "source": ["active_time_data[\"date\"] = active_time_data[\"date\"].astype(str)\n", "stars_data[\"date\"] = stars_data[\"date\"].astype(str)\n", "audit_data[\"date\"] = audit_data[\"date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "397563b6-18aa-44dc-848c-5ee4e1a4a6f4", "metadata": {}, "outputs": [], "source": ["stars_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "be824b90-4d3c-49f1-8f94-48b1a03d97d3", "metadata": {}, "outputs": [], "source": ["final_data = active_time_data.merge(\n", "    stars_data[\n", "        [\n", "            \"date\",\n", "            \"outlet_id\",\n", "            \"employee_id\",\n", "            \"total_orders\",\n", "            \"total_beat_orders\",\n", "            \"picked_quantity\",\n", "            \"pna_orders\",\n", "        ]\n", "    ],\n", "    left_on=[\"date\", \"store_id\", \"employee_id\"],\n", "    right_on=[\"date\", \"outlet_id\", \"employee_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bda4ee09-d05e-4664-97d8-c4860afe344c", "metadata": {}, "outputs": [], "source": ["complaints_data[\"date\"] = complaints_data[\"date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "549f241b-1e57-4fdf-9eba-f6feb9cea7b1", "metadata": {}, "outputs": [], "source": ["audit_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c188dca7-241b-4b3b-acc9-bf82dbf7a718", "metadata": {}, "outputs": [], "source": ["# final_data_temp = final_data.merge(\n", "#     audit_data,\n", "#     left_on=[\"date\", \"store_id\", \"employee_id\"],\n", "#     right_on=[\"date\", \"outlet_id\", \"employee_id\"],\n", "#     how=\"left\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "9e506afc-ade7-45c3-8e88-b9f43cdc52d1", "metadata": {}, "outputs": [], "source": ["final_data1 = final_data.merge(\n", "    complaints_data[[\"date\", \"outlet_id\", \"employee_id\", \"complaints_order\"]],\n", "    left_on=[\"date\", \"store_id\", \"employee_id\"],\n", "    right_on=[\"date\", \"outlet_id\", \"employee_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f3a4aa01-f27f-4a63-9f49-30c05b673bdc", "metadata": {}, "outputs": [], "source": ["final_data1[\"picking_logged_in_time\"] = final_data1[\"picking_logged_in_time\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "11f1d3e5-392e-4ae9-bac1-fb7f1835ae87", "metadata": {}, "outputs": [], "source": ["final_data1[\"final_active_time\"] = np.where(\n", "    final_data1[\"active_time\"] > final_data1[\"picking_logged_in_time\"],\n", "    final_data1[\"active_time\"],\n", "    final_data1[\"picking_logged_in_time\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e52debcc-de99-4f2f-aac8-a7dbf75384bd", "metadata": {}, "outputs": [], "source": ["final_data1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c0bae731-6ca6-430f-9140-96def0d5f04e", "metadata": {}, "outputs": [], "source": ["final_data1[\n", "    [\"complaints_order\", \"total_beat_orders\", \"picked_quantity\", \"pna_orders\"]\n", "] = final_data1[\n", "    [\"complaints_order\", \"total_beat_orders\", \"picked_quantity\", \"pna_orders\"]\n", "].fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "db270f70-4f57-4b21-ac8d-38597731edea", "metadata": {}, "outputs": [], "source": ["# final_data1[[\"complaints_order\", \"%orders withhin 2.5 mins + no pna\",\"pna_orders\",\"audit_more_than_1_min\",\"total_audits\",\"total_orders\"]] = final_data1[\n", "#     [\"complaints_order\", \"%orders withhin 2.5 mins + no pna\",\"pna_orders\",\"audit_more_than_1_min\",\"total_audits\",\"total_orders\"]\n", "# ].<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "92d4723e-a529-4700-951c-c88c33a7e9ec", "metadata": {}, "outputs": [], "source": ["final_data1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9ed81e5f-1aae-4bf5-ad55-c00341edb044", "metadata": {}, "outputs": [], "source": ["final_data1 = final_data1[\n", "    [\n", "        \"date\",\n", "        \"store_id\",\n", "        \"store_name\",\n", "        \"city\",\n", "        \"employee_id\",\n", "        \"employee_name\",\n", "        \"role\",\n", "        \"total_putaway_quantity\",\n", "        \"total_picked_quantity\",\n", "        \"score\",\n", "        \"total_orders_picked\",\n", "        \"break_time\",\n", "        \"picking_logged_in_time\",\n", "        \"putaway_logged_in_time\",\n", "        \"login\",\n", "        \"logout\",\n", "        \"roster_on_time\",\n", "        \"total_login_time\",\n", "        \"wait_time_in_queue\",\n", "        \"attendance_punched\",\n", "        \"shift_type\",\n", "        \"total_app_time\",\n", "        \"picking_time\",\n", "        \"putaway_time\",\n", "        \"ownership\",\n", "        \"active_time\",\n", "        \"outlet_id_x\",\n", "        \"total_orders\",\n", "        \"total_beat_orders\",\n", "        \"picked_quantity\",\n", "        \"outlet_id_y\",\n", "        \"complaints_order\",\n", "        \"final_active_time\",\n", "        \"first_login_time\",\n", "        \"last_logout_time\",\n", "        \"pna_orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6f43d339-5c7b-474a-a725-766916f10f9f", "metadata": {}, "outputs": [], "source": ["final_data1[\n", "    [\n", "        \"break_time\",\n", "        \"picking_logged_in_time\",\n", "        \"putaway_logged_in_time\",\n", "        \"total_picked_quantity\",\n", "        \"total_putaway_quantity\",\n", "    ]\n", "] = final_data1[\n", "    [\n", "        \"break_time\",\n", "        \"picking_logged_in_time\",\n", "        \"putaway_logged_in_time\",\n", "        \"total_picked_quantity\",\n", "        \"total_putaway_quantity\",\n", "    ]\n", "].fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "853e238a-b1ca-470c-a742-75f734721873", "metadata": {}, "outputs": [], "source": ["final_data1[\"date\"] = pd.to_datetime(final_data1[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e288ffbb-035f-48e0-9d6d-3627e084d3d9", "metadata": {}, "outputs": [], "source": ["final_data1[\"week_start\"] = final_data1[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "final_data1[\"week_end\"] = final_data1[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7561865-6093-4950-88b2-42bf184c817a", "metadata": {}, "outputs": [], "source": ["final_data1[\"week\"] = (\n", "    final_data1[\"week_start\"].astype(str) + \" to \" + final_data1[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6e0a9db1-86d9-4bd6-86d8-a1eaaefab431", "metadata": {}, "outputs": [], "source": ["final_data1[\"date\"] = final_data1[\"date\"].apply(lambda x: x.date())"]}, {"cell_type": "code", "execution_count": null, "id": "c0f24823-f1a0-4da2-aa0c-c7e01885e4f2", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     final_data1,\n", "# '12dKDabuJKkxrA9vUAY7KF5R0walz8qm0j-BPN4Qv670',\n", "#     \"picker_raw_data\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "b9cd742d-9575-4e0a-8008-10b4357e6b6b", "metadata": {}, "outputs": [], "source": ["final_data1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "faddbebe-b5f1-4ad5-bea1-598ec9dd7fed", "metadata": {}, "outputs": [], "source": ["final_data1_new = final_data1.merge(\n", "    outlets_data, left_on=[\"store_id\"], right_on=[\"Outlet ID\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "71f2b6ff-c9e4-4f26-9c6e-fc6ad0d48890", "metadata": {}, "outputs": [], "source": ["final_data1_new = final_data1_new[\n", "    [\n", "        \"date\",\n", "        \"store_id\",\n", "        \"store_name\",\n", "        \"city\",\n", "        \"employee_id\",\n", "        \"employee_name\",\n", "        \"role\",\n", "        \"total_putaway_quantity\",\n", "        \"total_picked_quantity\",\n", "        \"score\",\n", "        \"total_orders_picked\",\n", "        \"break_time\",\n", "        \"picking_logged_in_time\",\n", "        \"putaway_logged_in_time\",\n", "        \"login\",\n", "        \"logout\",\n", "        \"roster_on_time\",\n", "        \"total_login_time\",\n", "        \"wait_time_in_queue\",\n", "        \"attendance_punched\",\n", "        \"shift_type\",\n", "        \"total_app_time\",\n", "        \"picking_time\",\n", "        \"putaway_time\",\n", "        \"ownership\",\n", "        \"active_time\",\n", "        \"outlet_id_x\",\n", "        \"total_orders\",\n", "        \"total_beat_orders\",\n", "        \"picked_quantity\",\n", "        \"outlet_id_y\",\n", "        \"complaints_order\",\n", "        \"final_active_time\",\n", "        \"first_login_time\",\n", "        \"last_logout_time\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"Outlet ID\",\n", "        \"Live date\",\n", "        \"gig\",\n", "        \"pna_orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7fb05730-7bc6-44e5-8ddf-915137d9ea73", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_data1_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"picker_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "522ae50c-f4bc-41a3-8880-256885e9dd44", "metadata": {}, "outputs": [], "source": ["order_assign_q = \"\"\"\n", "\n", "\n", "with base as\n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct case when order_current_status = 'DELIVERED' then order_id end) as delivered_orders,\n", "       (count(distinct Case when checkout_to_picker_assigned <= 30 then order_id end)) as \"orders within 30 sec\",\n", "       (count(distinct Case when checkout_to_picker_assigned > 30 and checkout_to_picker_assigned <= 60 then order_id end)) as \"orders within 1 min\",\n", "       (count(distinct Case when checkout_to_picker_assigned > 60 and checkout_to_picker_assigned <= 120 then order_id end)) as \"orders within 2 min\",\n", "       (count(distinct Case when checkout_to_picker_assigned > 120 then order_id end)) as \"orders more 2 min\"\n", "       \n", "      from\n", "(\n", "SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       b.order_id,\n", "       o.cart_id,\n", "       o.picker_id,\n", "       o.partner_id,\n", "       o.total_selling_price,\n", "       b.total_product_quantity as IPO,\n", "       o.total_items_quantity_ordered,\n", "       o.total_items_quantity_delivered,\n", "       o.order_current_status,\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) end as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "\n", "       case when order_picking_started_ts_ist is not null then DATEDIFF(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) end as assigned_to_start,\n", "\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) end as picking_start_to_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       DATEDIFF(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_scanned_ts_ist) as picking_complete_to_order_scanned,\n", "       DATEDIFF(seconds,order_scanned_ts_ist,order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       case when order_delivered_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) end as delivery_time,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist) as picking_complete_to_billing_complete\n", "FROM dwh.fact_sales_order_details b\n", "inner join dwh.fact_supply_chain_order_details o on date(b.order_create_ts_ist) = date(o.order_checkout_ts_ist)\n", "and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "inner join lake_retail.console_outlet c on c.id = b.outlet_id and business_type_id = 7\n", "WHERE \n", "  (date(cart_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "  and b.outlet_id in {outlets_list}\n", "\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "and order_picker_assigned_ts_ist is not null\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n", ")\n", "group by 1,2,3,4\n", "order by 1 desc)\n", "\n", "\n", "select a.date,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       a.\"Orders\",\n", "       delivered_orders,\n", "       \"orders within 30 sec\",\n", "       \"orders within 1 min\",\n", "       \"orders within 2 min\",\n", "       \"orders more 2 min\"\n", "from base a\n", "order by 1 desc\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "order_assign_data = pd.read_sql(order_assign_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "73bcf4b1-387f-4870-bbaf-ce7982cbc3b3", "metadata": {}, "outputs": [], "source": ["order_assign_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "55c9eb5f-385e-42d9-9d16-d3b93c1e9247", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    order_assign_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"order_assign_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e571800f-c45d-457e-8c01-beebbfc58b30", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48874088-abf4-4150-8c32-dd78c29e140e", "metadata": {}, "outputs": [], "source": ["picker_perf_q = \"\"\"\n", "with \n", "\n", "order_scanned as (\n", "SELECT \n", "    external_order_id,\n", "    max(event_time) as scan_start_ist\n", "    ---(timestamp 'epoch' + (scan_start_timestamp/1000 * INTERVAL '1 second') + interval '5.5 hour') AS scan_start_ist,\n", "    ---row_number() OVER(PARTITION BY external_order_id ORDER BY scan_start_timestamp desc) as rn\n", "FROM \n", "    metrics.blinkit_z_order_scan_event\n", "WHERE \n", "    event_time>=(('{start_date}'::timestamp)-interval '1 days')\n", "    group by 1\n", "),\n", "\n", "\n", "base as\n", "(SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       b.order_id,\n", "       o.cart_id,\n", "       o.picker_id,\n", "       u.name as picker_name,\n", "       coalesce(json_extract_path_text(json_extract_path_text(lt.payload,'driver_details'),'phone'),o.partner_id) as partner_id,\n", "       o.total_selling_price,\n", "       b.total_product_quantity as IPO,\n", "       o.total_items_quantity_ordered,\n", "       o.total_items_quantity_delivered,\n", "       o.order_current_status,\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) end as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "\n", "       case when order_picking_started_ts_ist is not null then DATEDIFF(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) end as assigned_to_start,\n", "\n", "       case when order_picking_completed_ts_ist is not null then DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) end as picking_start_to_complete,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       DATEDIFF(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       DATEDIFF(seconds,coalesce(order_picking_completed_ts_ist,order_billing_completed_ts_ist),coalesce(order_scanned_ts_ist,scan_start_ist)) as picking_complete_to_order_scanned,\n", "       DATEDIFF(seconds,coalesce(order_scanned_ts_ist,scan_start_ist),order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       case when order_delivered_ts_ist is not null then DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) end as delivery_time,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist) as picking_complete_to_billing_complete\n", "FROM dwh.fact_sales_order_details b\n", "inner join dwh.fact_supply_chain_order_details o on date(b.order_create_ts_ist) = date(o.order_checkout_ts_ist)\n", "and o.outlet_id = b.outlet_id and o.order_id = b.order_id\n", "left join lake_retail.warehouse_user u on u.emp_id = o.picker_id\n", "left JOIN \n", "         lake_logistics.logistics_task\n", "            as lt\n", "            ON lt.shipment_label_value=o.order_id\n", "            AND lt.type='EXPRESS_3PL_ORDER_DELIVERY_TASK' \n", "            and lt.install_ts >= ('{start_date}'::date)-interval '1 DAYS'\n", "LEFT JOIN \n", "        order_scanned\n", "            AS os\n", "            ON os.external_order_id=b.order_id\n", "\n", "inner join lake_retail.console_outlet c on c.id = b.outlet_id and business_type_id = 7\n", "WHERE \n", "  (date(cart_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "  and b.outlet_id in {outlets_list}\n", "\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "and picker_id is not null and picker_id<>''\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24\n", "),\n", "\n", "\n", "\n", "\n", "\n", "order_level_data as \n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        picker_id, picker_name,\n", "        sum(IPO)*1.0/count(distinct order_id) as \"IPO\",\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end) as fully_filled_orders, \n", "       count(distinct case when order_current_status = 'DELIVERED' then order_id end) as delivered_orders,\n", "       count(distinct case when order_current_status = 'DELIVERED' and total_items_quantity_ordered=total_items_quantity_delivered then order_id end)*1.0/nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0) as fillrate,\n", "       round((count(distinct Case when instore_time <= 150 then order_id end))*1.0/(nullif(count(distinct order_id),0)),4) as \"perc orders within 2.5 min\",\n", "       \n", "       sum(total_selling_price) as GMV,\n", "       round((count(distinct Case when delivery_time <= 900 and order_current_status = 'DELIVERED' then order_id end))*1.0/(nullif(count(distinct case when order_current_status = 'DELIVERED' then order_id end),0)),4) as \"perc orders Delivered within 15 min\"\n", "      from \n", "      base\n", "      group by 1,2,3,4,5,6\n", "),\n", "\n", "\n", "\n", "time_data as\n", "\n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "        picker_id,picker_name,\n", "        'AVG' as metric_type,\n", "round((sum(instore_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Instore Time\",\n", "       round((sum(checkout_to_picker_assigned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned Time\",\n", "       round((sum(assigned_to_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned to start Time\",\n", "       round((sum(picking_start_to_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking start to complete Time\",\n", "       round((sum(picking_complete_to_billing_start)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to billing start Time\",\n", "       round((sum(billing_start_to_billing_complete)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg billing start to complete Time\",\n", "       round((sum(picking_complete_to_order_scanned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg picking complete to order scan Time\",\n", "       round((sum(order_scanned_to_enroute)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg order scan to enroute Time\",\n", "       round((sum(delivery_time)/60.0)/nullif(count(distinct case when order_current_status='DELIVERED' then order_id end),0),2) as \"Avg delivery Time\"\n", "       from base\n", "       group by 1,2,3,4,5,6,7\n", "\n", "\n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "                picker_id,picker_name,\n", "        '50th percentile' as metric_type,\n", "        percentile_disc(0.50) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.50) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.50) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.50) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.50) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.50) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.50) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.50) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.50) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "       \n", "       \n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "                picker_id,picker_name,\n", "        '75th percentile' as metric_type,\n", "        percentile_disc(0.75) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.75) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.75) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.75) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.75) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.75) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.75) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.75) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "       \n", "\n", "union\n", "\n", "select distinct date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR' \n", "        else replace(city_name,'Bangalore','Bengaluru') end as \"City\",\n", "                picker_id,picker_name,\n", "        '95th percentile' as metric_type,\n", "        percentile_disc(0.95) within group (order by instore_time ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Instore Time\",\n", "        percentile_disc(0.95) within group (order by checkout_to_picker_assigned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned Time\",\n", "        percentile_disc(0.95) within group (order by assigned_to_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg Assigned to start Time\",\n", "        percentile_disc(0.95) within group (order by picking_start_to_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_billing_start ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to billing start Time\",\n", "        percentile_disc(0.95) within group (order by billing_start_to_billing_complete ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg billing start to complete Time\",\n", "        percentile_disc(0.95) within group (order by picking_complete_to_order_scanned ) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg picking complete to order scan Time\",\n", "        percentile_disc(0.95) within group (order by order_scanned_to_enroute) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg order scan to enroute Time\",\n", "        percentile_disc(0.95) within group (order by delivery_time) over(PARTITION BY DATE,outlet_id,picker_id)/60.0 as \"Avg delivery Time\"\n", "       from base\n", "       \n", "),\n", "\n", "\n", "\n", "\n", "base1 as\n", "(select date(delivery_date) as date_ts,\n", "outlet_id, employee_id,\n", "--trim(c.name) as city_name,\n", "       UPPER(case when c.name in ('Ghaziabad', 'Noida') then 'UP-NCR'\n", "            when c.name = 'Bangalore' then 'Bengaluru'\n", "            else c.name end) as city_name,\n", "       count(distinct order_id) as complaint_orders\n", "from metrics.complaints o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "inner join lake_retail.console_location c on c.id = m.tax_location_id\n", "where (date(delivery_date) between '{start_date}' and '{end_date}')\n", "group by 1,2,3,4)\n", "\n", "\n", "select a.date,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       a.picker_id,\n", "       a.picker_name,\n", "       c.metric_type,\n", "       a.\"Orders\",\n", "       delivered_orders,\n", "       \"perc orders within 2.5 min\",\n", "       \"Avg Instore Time\",\n", "       \"Avg Assigned Time\",\n", "       \"Avg Assigned to start Time\",\n", "       \"Avg picking start to complete Time\",\n", "       \"Avg picking complete to billing start Time\",\n", "       \"Avg billing start to complete Time\",\n", "       \"Avg picking complete to order scan Time\",\n", "       \"Avg order scan to enroute Time\",\n", "       \"Avg delivery Time\",\n", "       fillrate,\n", "       coalesce(complaint_orders,0) as complaint_orders,\n", "        GMV,\n", "        \"perc orders Delivered within 15 min\",\n", "        \"IPO\"\n", "from order_level_data a\n", "inner join time_data c on a.date = c.date and a.\"Store ID\" = c.\"Store ID\" and a.picker_id = c.picker_id\n", "left join base1 b on a.date = b.date_ts and a.\"Store ID\" = b.outlet_id and a.picker_id = b.employee_id\n", "order by 1 desc\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "picker_perf_data = pd.read_sql(picker_perf_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "fa88f56d-1329-4cb4-8168-651a78852b80", "metadata": {}, "outputs": [], "source": ["picker_perf_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e8fb6362-d0bb-4d27-817e-4d3f2d13077d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c8aa2c92-5392-4e05-a2e3-c33d611f7bfb", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_perf_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"picker_instore_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4208ec8-48b5-4818-b967-3535d7df1354", "metadata": {}, "outputs": [], "source": ["start_date1 = (datetime.now() - timed<PERSON>ta(days=21)).strftime(\"%Y-%m-%d\")\n", "end_date1 = (datetime.now() - timedelta(days=0)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "f7f9a330-cfc0-48ba-8840-62149f2df3c1", "metadata": {}, "outputs": [], "source": ["active_time_q1 = \"\"\"\n", "with\n", "active_time as\n", "( select date(role_start_time+interval '5.5 hours') as date_ts, site_id, employee_id,\n", "  coalesce(sum(datediff(seconds,role_start_time,coalesce(role_end_time,current_timestamp::timestamp)))/60.0,0) as total_active_time,\n", "min(role_start_time+interval '5.5 hours') as first_login_time,\n", "max(role_end_time+interval '5.5 hours') as last_logout_time\n", "  from lake_ops_management.shift_roles\n", "  where (date(role_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "  and site_id in {outlets_list}\n", "  group by 1,2,3\n", ")\n", "\n", "\n", "\n", "\n", "\n", "select date(date) as date,store_id,\n", "replace(store_name,' (NM)','') as store_name,city,mp.employee_id,(case when employee_name='' then first_name else employee_name end) as employee_name,\n", "(case when mp.role='' then u.role else mp.role end) as role ,total_putaway_quantity,total_picked_quantity,score,total_orders_picked,break_time,\n", "picking_logged_in_time,putaway_logged_in_time,login,logout,roster_on_time,total_login_time,wait_time_in_queue,attendance_punched,shift_type,total_app_time,\n", "picking_time,putaway_time,ownership,\n", "coalesce(total_active_time,0) as active_time,\n", "first_login_time,\n", "last_logout_time\n", "from metrics.instore_manpower_performance mp \n", "left join lake_storeops.user_details u on u.employee_id = mp.employee_id\n", "left join active_time a on a.employee_id = mp.employee_id and a.site_id = mp.store_id and a.date_ts = date(mp.date)\n", "\n", "----left join break_time b on a.employee_id = b.employee_id and a.site_id = b.site_id and a.date_ts = b.date_ts\n", "\n", "where\n", "date(date) between '{start_date}' and '{end_date}'\n", "and mp.store_id in {outlets_list}\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date1, end_date=end_date1, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "active_time_data1 = pd.read_sql(active_time_q1, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "bbbc09af-67e9-43fd-9392-5fc3597c81a4", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"week_start\"] = active_time_data1[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "active_time_data1[\"week_end\"] = active_time_data1[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e41b6121-68ed-4782-9e9a-3e98f328b29c", "metadata": {}, "outputs": [], "source": ["active_time_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "546f1c0a-7251-4487-8493-100a66a03d9e", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"week\"] = (\n", "    active_time_data1[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + active_time_data1[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a9fc81cf-4175-4bc6-a07a-677e89d1ad00", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    active_time_data1,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"picker_attendance_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "826f5bb1-9829-4c17-a93c-3361fbdc3777", "metadata": {}, "outputs": [], "source": ["complaints_q = \"\"\"\n", "select \n", "    date(c.delivery_date) as delivery_date,\n", "    outlet_id, m.name as outlet_name,\n", "    count(\n", "        DISTINCT \n", "        CASE WHEN \n", "            (complaint_type = 'VALID'\n", "            AND lower(order_type) ILIKE 'retail%%')\n", "            \n", "            THEN c.order_id\n", "        END\n", "    ) AS all_complaints,\n", "\n", "\n", "\n", "-------------Item Missing-------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') \n", "                    )\n", "           \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%')\n", "                )\n", "                or (\n", "                    (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%') \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%'))\n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','')\n", "                )\n", "              \n", "                or ( \n", "                        C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Missing%%')\n", "                )\n", "         \n", "                or ( \n", "                        (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                        or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%'))\n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing')\n", "                )\n", "                \n", "                or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%'))\n", "  or (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%') and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%')  \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "                \n", "                -- or cc.disposition = 'MDND'\n", "                then c.order_id end\n", "                )  as \"<PERSON><PERSON> Missing\",\n", "\n", "\n", "   \n", "   \n", "  count(DISTINCT  case when ( (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%') )\n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing','Item Missing'))\n", "  \n", "  then c.order_id end)  as \"Item Missing Doorstep\",\n", "\n", "\n", "count(DISTINCT CASE\n", "                  WHEN (C.COMPLAINT_CATEGORY ILIKE ('%%extra%%item%%') and ( C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%no%%longer%%') \n", "or C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%not%%required%%') ))THEN c.order_id\n", "              END) AS \"Excess received\",\n", " \n", " \n", "count(DISTINCT  case when <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "or (C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') and C.COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') )\n", "\n", " then c.order_id end)  as \"<PERSON><PERSON>/<PERSON>mbo missing\",\n", " \n", "\n", "  count(DISTINCT  \n", "  case \n", "    when \n", "        (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "        and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%'))\n", "        \n", " then c.order_id end)  as \"mdnd\",\n", " \n", " \n", " \n", "--------QNG----------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%quality%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%smell%%')\n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%taste%%')\n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%foreign%%matter%%')\n", "            or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Quality%%Not%%good%%'))\n", "            \n", "        then c.order_id end\n", "    )  as \"QNG\",\n", "    \n", "\n", "\n", "\n", "-----------Item damaged-------------\n", "count(\n", "        DISTINCT  \n", "            case \n", "                when C.COMPLAINT_CATEGORY ILIKE ('%%damage%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%damage%%')  \n", "                \n", "            then c.order_id end\n", "    )  as \"Item Damaged\",\n", "\n", "\n", "\n", "\n", "\n", "----------_Wrong Item/product------------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%product%%different%%') \n", "            or (C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') )\n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%product%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%size%%')\n", "            or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Wrong%%Item%%'))\n", "            \n", "        then \n", "            c.order_id \n", "    end)  as \"Wrong Item\",\n", "\n", "\n", "\n", "---------Expiry----------------\n", "count(DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%expired%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%') \n", "                or  C.COMPLAINT_CATEGORY ILIKE ('%%near%%expiry%%') \n", "                or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Expired%%'))\n", "                \n", "            then c.order_id \n", "        end\n", "    )  as \"Expiry\",\n", "\n", "\n", "\n", "              \n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%expired%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%')  \n", "        then c.order_id \n", "        end\n", "    )  as \"Expired Product\",\n", "    \n", "count(DISTINCT  case when  <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%near%%expiry%%')  then c.order_id end)  as \"Near Expiry Product\",\n", "\n", "\n", "\n", "\n", "----------_Delay In delivery--------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when  \n", "                (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%')\n", "                ) \n", "                \n", "            then c.order_id end\n", "        )  as \"Delay In Delivery\",\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "------FE/GSP behaviour-----------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%fe%%relate%%')  \n", "            \n", "            then c.order_id \n", "        end\n", "    )  as \"FE/GSP behaviour\",\n", "\n", "\n", "\n", "\n", "               \n", "              -------Content-----------------\n", "count(DISTINCT \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%product%%description%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%price%%diff%%')\n", "            or (\n", "                C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and C.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')\n", "            ) \n", "            or (C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') )\n", "            \n", "\n", "        then c.order_id end\n", "        )  as \"Content Related\",\n", "\n", " count(DISTINCT CAse when C.COMPLAINT_CATEGORY ILIKE ('%%product%%description%%') \n", "                 then c.order_id end)  as \"Incorrect Description\",\n", " count(\n", "        DISTINCT  \n", "            case \n", "                when C.COMPLAINT_CATEGORY ILIKE ('%%price%%diff%%')\n", "                or ( C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')) \n", "                \n", "                then c.order_id \n", "            end\n", "    )  as \"Price Difference\",\n", "\n", "\n", "------------EDLP--------------------\n", "count(DISTINCT  case when <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%EDLP%%') \n", "              or  C.COMPLAINT_CATEGORY ILIKE ('%%price%%guarantee%%')  then c.order_id end)  as \"EDLP\",\n", "\n", "\n", "\n", "\n", "------------Others----------------\n", "count(DISTINCT  case when (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "and  <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "or (C.COMPLAINT_CATEGORY ILIKE ('%%erased%%batch%%')) then c.order_id end)  as \"Others\",\n", "\n", "\n", "\n", "\n", "--------NLR-------------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%not%%required%%') \n", "   --             or <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%wrong%%size%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%change%%mind%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%bought%%mistake%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%no%%longer%%')\n", "                \n", "        then c.order_id \n", "        end\n", "    )  as \"<PERSON> Longer Required\"\n", "from \n", "  metrics.complaints\n", "        AS c\n", "    inner join lake_retail.console_outlet m on m.id = c.outlet_id\n", "inner join lake_retail.console_location co on co.id = m.tax_location_id\n", "where (date(delivery_date) between '{start_date}' and '{end_date}')\n", "and outlet_id in {outlets_list}\n", "group by 1,2,3\n", "order by 1,2,3\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "complaints_data_cat = pd.read_sql(complaints_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "fef9d0c1-5f96-4479-aa2f-f225d2ad6c99", "metadata": {}, "outputs": [], "source": ["complaints_data_cat.head()"]}, {"cell_type": "code", "execution_count": null, "id": "57b20df8-e868-48db-b18a-17c9a53c2abd", "metadata": {}, "outputs": [], "source": ["complaints_data_cat[\"week_start\"] = complaints_data_cat[\"delivery_date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "complaints_data_cat[\"week_end\"] = complaints_data_cat[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "80772a0b-4e0f-4885-8e3b-8cc13d9653db", "metadata": {}, "outputs": [], "source": ["complaints_data_cat.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6c013b6c-e511-4927-82fa-1da2de6ebc8d", "metadata": {}, "outputs": [], "source": ["complaints_data_cat[\"week\"] = (\n", "    complaints_data_cat[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + complaints_data_cat[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "91f9bc51-4c7a-48f4-b60b-beb2f3a59243", "metadata": {}, "outputs": [], "source": ["complaints_data_cat.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b9145b89-5577-413a-b7a6-b2cf42295031", "metadata": {}, "outputs": [], "source": ["outlets_data[\"Outlet ID\"] = outlets_data[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "e3eff7cc-a749-4f81-a025-1b846e9c0756", "metadata": {}, "outputs": [], "source": ["complaints_data_cat_new = complaints_data_cat.merge(\n", "    outlets_data, left_on=[\"outlet_id\"], right_on=[\"Outlet ID\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9f4b5781-6b22-45a4-96ee-1f724734ba6a", "metadata": {}, "outputs": [], "source": ["complaints_data_cat_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5984a605-55cb-41c7-900b-e44cdb8498ee", "metadata": {}, "outputs": [], "source": ["# complaints_data_cat_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "2f862662-af5b-4f8f-9ff3-2b3d28b884f2", "metadata": {}, "outputs": [], "source": ["complaints_data_cat_new[\"item_missing/wrong_item\"] = (\n", "    complaints_data_cat_new[\"item missing\"] + complaints_data_cat_new[\"wrong item\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e09f06a4-ee19-40a3-82ce-389926bb71cc", "metadata": {}, "outputs": [], "source": ["complaints_data_cat_new1 = complaints_data_cat_new.merge(\n", "    store_perf_data[store_perf_data[\"metric_type\"] == \"AVG\"][\n", "        [\"date\", \"store id\", \"delivered_orders\"]\n", "    ],\n", "    left_on=[\"delivery_date\", \"outlet_id\"],\n", "    right_on=[\"date\", \"store id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b10eb753-f862-4d62-9f65-ad36548aaec0", "metadata": {}, "outputs": [], "source": ["complaints_data_cat_new1[\"%item_missing/wrong_item\"] = (\n", "    complaints_data_cat_new1[\"item_missing/wrong_item\"]\n", "    / complaints_data_cat_new1[\"delivered_orders\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "af8f04e3-fa2b-4969-8182-e38534033425", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    complaints_data_cat_new1,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"complaints_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d41fa2a3-da9e-4f11-bab5-a920294d7dd3", "metadata": {}, "outputs": [], "source": ["complaints_picker_q = \"\"\"\n", "select \n", "    date(c.delivery_date) as delivery_date,\n", "    c.outlet_id, m.name as outlet_name,\n", "    picker_id,\n", "    count(\n", "        DISTINCT \n", "        CASE WHEN \n", "            (complaint_type = 'VALID'\n", "            AND lower(c.order_type) ILIKE 'retail%%')\n", "            \n", "            THEN c.order_id\n", "        END\n", "    ) AS all_complaints,\n", "\n", "\n", "\n", "-------------Item Missing-------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') \n", "                    )\n", "           \n", "                or (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%')\n", "                )\n", "                or (\n", "                    (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%') \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  \n", "                    AND C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%'))\n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','')\n", "                )\n", "              \n", "                or ( \n", "                        C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Missing%%')\n", "                )\n", "         \n", "                or ( \n", "                        (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') \n", "                        or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%'))\n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing')\n", "                )\n", "                \n", "                or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%'))\n", "  or (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%') and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%lm%%')  and C.COMPLAINT_CATEGORY not ILIKE ('%%item%%missing%%doorstep%%')  \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "                \n", "                -- or cc.disposition = 'MDND'\n", "                then c.order_id end\n", "                )  as \"<PERSON><PERSON> Missing\",\n", "\n", "\n", "   \n", "   \n", "  count(DISTINCT  case when ( (C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%lm%%') or C.COMPLAINT_CATEGORY ILIKE ('%%item%%missing%%doorstep%%') )\n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing','Item Missing'))\n", "  \n", "  then c.order_id end)  as \"Item Missing Doorstep\",\n", "\n", "\n", "count(DISTINCT CASE\n", "                  WHEN (C.COMPLAINT_CATEGORY ILIKE ('%%extra%%item%%') and ( C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%no%%longer%%') \n", "or C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%not%%required%%') ))THEN c.order_id\n", "              END) AS \"Excess received\",\n", " \n", " \n", "count(DISTINCT  case when <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%freebie%%missing%%') \n", "or (C.COMPLAINT_CATEGORY ILIKE ('%%missing%%') and C.COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') )\n", "\n", " then c.order_id end)  as \"<PERSON><PERSON>/<PERSON>mbo missing\",\n", " \n", "\n", "  count(DISTINCT  \n", "  case \n", "    when \n", "        (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "        and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%'))\n", "        \n", " then c.order_id end)  as \"mdnd\",\n", " \n", " \n", " \n", "--------QNG----------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%quality%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%smell%%')\n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%taste%%')\n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%foreign%%matter%%')\n", "            or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Quality%%Not%%good%%'))\n", "            \n", "        then c.order_id end\n", "    )  as \"QNG\",\n", "    \n", "\n", "\n", "\n", "-----------Item damaged-------------\n", "count(\n", "        DISTINCT  \n", "            case \n", "                when C.COMPLAINT_CATEGORY ILIKE ('%%damage%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%damage%%')  \n", "                \n", "            then c.order_id end\n", "    )  as \"Item Damaged\",\n", "\n", "\n", "\n", "\n", "\n", "----------_Wrong Item/product------------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%product%%different%%') \n", "            or (C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') )\n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%product%%different%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%wrong%%size%%')\n", "            or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Wrong%%Item%%'))\n", "            \n", "        then \n", "            c.order_id \n", "    end)  as \"Wrong Item\",\n", "\n", "\n", "\n", "---------Expiry----------------\n", "count(DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%expired%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%') \n", "                or  C.COMPLAINT_CATEGORY ILIKE ('%%near%%expiry%%') \n", "                or (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Expired%%'))\n", "                \n", "            then c.order_id \n", "        end\n", "    )  as \"Expiry\",\n", "\n", "\n", "\n", "              \n", "count(DISTINCT  \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%expired%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%')  \n", "        then c.order_id \n", "        end\n", "    )  as \"Expired Product\",\n", "    \n", "count(DISTINCT  case when  <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%near%%expiry%%')  then c.order_id end)  as \"Near Expiry Product\",\n", "\n", "\n", "\n", "\n", "----------_Delay In delivery--------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when  \n", "                (\n", "                    C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                    and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%')\n", "                ) \n", "                \n", "            then c.order_id end\n", "        )  as \"Delay In Delivery\",\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "------FE/GSP behaviour-----------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%fe%%relate%%')  \n", "            \n", "            then c.order_id \n", "        end\n", "    )  as \"FE/GSP behaviour\",\n", "\n", "\n", "\n", "\n", "               \n", "              -------Content-----------------\n", "count(DISTINCT \n", "    case \n", "        when \n", "            C.COMPLAINT_CATEGORY ILIKE ('%%product%%description%%') \n", "            or C.COMPLAINT_CATEGORY ILIKE ('%%price%%diff%%')\n", "            or (\n", "                C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and C.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')\n", "            ) \n", "            or (C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') )\n", "            \n", "\n", "        then c.order_id end\n", "        )  as \"Content Related\",\n", "\n", " count(DISTINCT CAse when C.COMPLAINT_CATEGORY ILIKE ('%%product%%description%%') \n", "                 then c.order_id end)  as \"Incorrect Description\",\n", " count(\n", "        DISTINCT  \n", "            case \n", "                when C.COMPLAINT_CATEGORY ILIKE ('%%price%%diff%%')\n", "                or ( C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "                and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')) \n", "                \n", "                then c.order_id \n", "            end\n", "    )  as \"Price Difference\",\n", "\n", "\n", "------------EDLP--------------------\n", "count(DISTINCT  case when <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%EDLP%%') \n", "              or  C.COMPLAINT_CATEGORY ILIKE ('%%price%%guarantee%%')  then c.order_id end)  as \"EDLP\",\n", "\n", "\n", "\n", "\n", "------------Others----------------\n", "count(DISTINCT  case when (C.COMPLAINT_CATEGORY ILIKE ('%%other%%') \n", "and  <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "or (C.COMPLAINT_CATEGORY ILIKE ('%%erased%%batch%%')) then c.order_id end)  as \"Others\",\n", "\n", "\n", "\n", "\n", "--------NLR-------------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when \n", "                C.COMPLAINT_CATEGORY ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%not%%required%%') \n", "   --             or <PERSON><PERSON>COMPLAINT_CATEGORY ILIKE ('%%wrong%%size%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%change%%mind%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%bought%%mistake%%')\n", "                or C.COMPLAINT_CATEGORY ILIKE ('%%no%%longer%%')\n", "                \n", "        then c.order_id \n", "        end\n", "    )  as \"<PERSON> Longer Required\"\n", "from \n", "  metrics.complaints\n", "        AS c\n", "   inner join dwh.fact_supply_chain_order_details f on f.order_id = c.order_id\n", "    inner join lake_retail.console_outlet m on m.id = c.outlet_id\n", "inner join lake_retail.console_location co on co.id = m.tax_location_id\n", "where (date(delivery_date) between '{start_date}' and '{end_date}')\n", "and (date(order_checkout_ts_ist) between '{start_date}' and '{end_date}')\n", "and c.outlet_id in {outlets_list}\n", "group by 1,2,3,4\n", "order by 1,2,3,4\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "complaints_data_cat_picker = pd.read_sql(complaints_picker_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "a788af4b-9a16-4c82-a7a9-e1a349cf3b17", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    complaints_data_cat_picker,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"complaints_picker_raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dfb10ee2-9460-445b-9efb-a46bc170254e", "metadata": {}, "outputs": [], "source": ["payout_q = \"\"\"select date(event_date) as date,\n", "node_id as store_id, \n", "co.name as outlet_name,\n", "user_id as employee_id,\n", "u.name as employee_name,\n", "sum(case when event_type='PICKER_BASE_PAY' then amount end) as base_payout,\n", "sum(case when event_type='PICKER_BASE_PAY' then metric_frequency end) as picked_qty,\n", "sum(case when event_type='BEAT_THE_TIMER' then points end) as stars_earned,\n", "sum(case when event_type='ATTENDANCE_INCENTIVE' then amount end) as attendance_bonus,\n", "sum(case when event_type='DAILY_MILESTONE' then amount end) as daily_milestone,\n", "sum(case when event_type in ('WRONG_ITEM','ITEM_MISSING') then total_count end) as complaints\n", "from lake_payouts_engine.payouts_picker_daily_pivots a\n", "left join lake_retail.warehouse_user u on a.user_id = u.emp_id\n", "left join lake_retail.console_outlet co on co.id = a.node_id\n", "where date(event_date) between current_date-40 and current_date\n", "and user_id ilike '%%GW%%'\n", "and node_id in {outlets_list}\n", "group by 1,2,3,4,5\"\"\".format(\n", "    outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "payout_data = pd.read_sql(payout_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "5cc0b086-5516-4164-9468-556e1b66c12e", "metadata": {}, "outputs": [], "source": ["payout_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7bbafa2d-1899-4151-80ce-e7592659dc01", "metadata": {}, "outputs": [], "source": ["payout_data = payout_data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "df3ed685-240d-4eee-86d5-2ac45cf2efe7", "metadata": {}, "outputs": [], "source": ["payout_data[\"week_start\"] = payout_data[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "payout_data[\"week_end\"] = payout_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3ad249c2-e58d-4c34-bae1-002bbdd8562e", "metadata": {}, "outputs": [], "source": ["payout_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6800b7e5-6262-4e73-a10d-3b61fcaec98a", "metadata": {}, "outputs": [], "source": ["payout_data[\"week\"] = (\n", "    payout_data[\"week_start\"].astype(str) + \" to \" + payout_data[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "60beb5c1-44cf-48f4-ad9d-c3f9cbaa6169", "metadata": {}, "outputs": [], "source": ["payout_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "84d8aa8f-6a18-46f0-840f-f5eabfb2aa0b", "metadata": {}, "outputs": [], "source": ["payout_data = payout_data[\n", "    [\n", "        \"date\",\n", "        \"store_id\",\n", "        \"outlet_name\",\n", "        \"employee_id\",\n", "        \"employee_name\",\n", "        \"base_payout\",\n", "        \"picked_qty\",\n", "        \"stars_earned\",\n", "        \"attendance_bonus\",\n", "        \"daily_milestone\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"complaints\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e6f03fdc-039e-4e25-8e3e-5c934d7407d2", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    payout_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"payout_raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65290da4-4e81-40b6-8922-abc4b1bf5b17", "metadata": {}, "outputs": [], "source": ["active_time_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "14f423f7-cdfc-4f62-bbb8-9bd953ef16a4", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"is_active\"] = active_time_data1[\"total_picked_quantity\"].apply(\n", "    lambda x: 1 if x > 0 else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "695d15ad-32d3-479f-bfa4-27ffe6c41a38", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"wait_time_in_queue\"] = active_time_data1[\n", "    \"wait_time_in_queue\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "6d4f16c0-92ac-4aeb-9f73-4863a351655b", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"%Utilisation\"] = 1 - (\n", "    active_time_data1[\"wait_time_in_queue\"] / active_time_data1[\"active_time\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dfcdc1c1-6dcf-4e8d-9f06-538df2678406", "metadata": {}, "outputs": [], "source": ["active_time_data1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "105a04d0-2cd9-40a8-b9b8-2fcb0fc3a9ea", "metadata": {}, "outputs": [], "source": ["active_time_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "62098f36-9868-4319-8394-6d964f0a25bc", "metadata": {}, "outputs": [], "source": ["def util(x):\n", "    if x < 0.3:\n", "        return \"<30%\"\n", "    elif x < 0.4:\n", "        return \"30%-40%\"\n", "    elif x < 0.5:\n", "        return \"40%-50%\"\n", "    elif x < 0.6:\n", "        return \"50%-60%\"\n", "    elif x < 0.7:\n", "        return \"60%-70%\"\n", "    elif x >= 0.7:\n", "        return \">=70%\"\n", "    else:\n", "        return \"NA\""]}, {"cell_type": "code", "execution_count": null, "id": "8ab2b808-bbf0-46e8-98d0-624384aa9b68", "metadata": {}, "outputs": [], "source": ["active_time_data1[\"util_bucket\"] = active_time_data1[\"%Utilisation\"].apply(\n", "    lambda x: util(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "946b4dfa-c275-4ecb-b00a-c60b5ec83a0a", "metadata": {}, "outputs": [], "source": ["active_time_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d2b6ca39-fd60-4f89-9d55-4c513c750363", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f28bb5c7-108c-47eb-9b7a-6c7a6be44ad5", "metadata": {}, "outputs": [], "source": ["picker_discipline1 = active_time_data1[\n", "    active_time_data1[\"employee_id\"].str.contains(\"GW\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "124aadcb-62e4-417b-b988-fa2231c87794", "metadata": {}, "outputs": [], "source": ["picker_discipline = (\n", "    picker_discipline1[picker_discipline1[\"total_picked_quantity\"] > 0]\n", "    .groupby([\"date\", \"store_id\", \"store_name\", \"week\"])\n", "    .agg(\n", "        {\n", "            \"is_active\": \"sum\",\n", "            \"employee_id\": \"nunique\",\n", "            \"active_time\": \"sum\",\n", "            \"wait_time_in_queue\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9c8aa6c1-d41e-4483-a68d-d9b6199b9d01", "metadata": {}, "outputs": [], "source": ["picker_discipline.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1fb9c588-f66d-4e34-841c-63e7ab587c44", "metadata": {}, "outputs": [], "source": ["picker_discipline[\"%Utilisation\"] = 1 - (\n", "    picker_discipline[\"wait_time_in_queue\"] / picker_discipline[\"active_time\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e3823c17-e2f6-43b2-833e-57a953da3455", "metadata": {}, "outputs": [], "source": ["picker_util = (\n", "    active_time_data1.groupby([\"date\", \"store_id\", \"store_name\", \"util_bucket\", \"week\"])\n", "    .agg({\"employee_id\": \"nunique\", \"is_active\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67d1b1e8-5ef8-4651-8563-1f412a00f0f7", "metadata": {}, "outputs": [], "source": ["final_data1[\"date\"] = pd.to_datetime(final_data1[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4978e9d7-8828-467a-8ffe-7e378b04b024", "metadata": {}, "outputs": [], "source": ["final_data1[\"week_start\"] = final_data1[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "final_data1[\"week_end\"] = final_data1[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a728646a-56ce-4b9b-b006-100924c75acf", "metadata": {}, "outputs": [], "source": ["final_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "df43c557-3710-474a-88b5-0fce8883df91", "metadata": {}, "outputs": [], "source": ["final_data1[\"week\"] = (\n", "    final_data1[\"week_start\"].astype(str) + \" to \" + final_data1[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1e5064e9-468a-424d-a578-55589414b870", "metadata": {}, "outputs": [], "source": ["payout_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "992c2bf7-0097-4d06-a4e5-337ed92570a9", "metadata": {}, "outputs": [], "source": ["payout_data[\"earnings\"] = (\n", "    payout_data[\"base_payout\"]\n", "    + (payout_data[\"stars_earned\"] * 2)\n", "    + payout_data[\"attendance_bonus\"]\n", "    + payout_data[\"daily_milestone\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d34bdd94-883b-4358-9f38-e2b97bead873", "metadata": {}, "outputs": [], "source": ["payout_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ea9cc0a8-4979-4c9a-81fd-873b1f053fae", "metadata": {}, "outputs": [], "source": ["# final_data1 = final_data1.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5f7027c9-f2a1-48f0-879f-d9ac0e8d713d", "metadata": {}, "outputs": [], "source": ["final_data1.iloc[0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "ef214207-b6c2-43a6-9775-fe3ebfa34a92", "metadata": {}, "outputs": [], "source": ["payout_data[\"date\"] = payout_data[\"date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "98ce2946-c210-4519-ac3d-49394400a40c", "metadata": {}, "outputs": [], "source": ["payout_data[\"store_id\"] = payout_data[\"store_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "8e1911a3-e87f-483e-a752-7006c0c7508a", "metadata": {}, "outputs": [], "source": ["final_data1[\"date\"] = final_data1[\"date\"].apply(lambda x: x.date())"]}, {"cell_type": "code", "execution_count": null, "id": "8f0d15dd-b1cc-4e55-9a9c-ba6672d3dff2", "metadata": {}, "outputs": [], "source": ["final_data1[\"date\"] = final_data1[\"date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "6a761201-adbc-4872-bc6b-24a5e5eb5da3", "metadata": {}, "outputs": [], "source": ["payout_data_new = payout_data.merge(\n", "    final_data1[\n", "        [\n", "            \"date\",\n", "            \"store_id\",\n", "            \"employee_id\",\n", "            \"total_orders_picked\",\n", "            \"break_time\",\n", "            \"picking_logged_in_time\",\n", "            \"putaway_logged_in_time\",\n", "            \"roster_on_time\",\n", "            \"total_login_time\",\n", "            \"wait_time_in_queue\",\n", "            \"active_time\",\n", "            \"total_orders\",\n", "            \"total_beat_orders\",\n", "            \"picked_quantity\",\n", "            \"complaints_order\",\n", "        ]\n", "    ],\n", "    on=[\"date\", \"store_id\", \"employee_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fc60bde9-6f87-4a3b-9a6b-bda674ecaecb", "metadata": {}, "outputs": [], "source": ["payout_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9aad7657-0e09-4cda-9819-b6f4d15ee9ed", "metadata": {}, "outputs": [], "source": ["payout_data_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9926b5a2-3fbf-40f0-86f4-4c0aeab8e135", "metadata": {}, "outputs": [], "source": ["payout_data_new[\"EPH\"] = (payout_data_new[\"earnings\"] * 60) / payout_data_new[\n", "    \"active_time\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9e6c3878-cc6c-4677-bd31-0d95e9da40a7", "metadata": {}, "outputs": [], "source": ["payout_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "19a33519-b8ce-4e25-9918-4cebdb2714b5", "metadata": {}, "outputs": [], "source": ["def iph_bucket(x):\n", "    if x < 30:\n", "        return \"<30\"\n", "    elif x < 40:\n", "        return \"30-40\"\n", "    elif x < 50:\n", "        return \"40-50\"\n", "    elif x < 60:\n", "        return \"50-60\"\n", "    elif x < 70:\n", "        return \"60-70\"\n", "    elif x >= 70:\n", "        return \">=70\"\n", "    else:\n", "        return \"NA\"\n", "\n", "\n", "def eph_bucket(x):\n", "    if x < 55:\n", "        return \"<55\"\n", "    elif x < 70:\n", "        return \"55-70\"\n", "    elif x < 90:\n", "        return \"70-90\"\n", "    elif x >= 90:\n", "        return \">=90\"\n", "    else:\n", "        return \"NA\"\n", "\n", "\n", "def earnings_bucket(x):\n", "    if x <= 400:\n", "        return \"<=400\"\n", "    elif x < 600:\n", "        return \"400-600\"\n", "    elif x < 800:\n", "        return \"600-800\"\n", "    elif x >= 800:\n", "        return \">=800\"\n", "    else:\n", "        return \"NA\""]}, {"cell_type": "code", "execution_count": null, "id": "1625010f-f899-4ca6-98fc-d8f6ae8da396", "metadata": {}, "outputs": [], "source": ["payout_data_new[\"eph_bucket\"] = payout_data_new[\"EPH\"].apply(lambda x: eph_bucket(x))\n", "payout_data_new[\"earnings_bucket\"] = payout_data_new[\"earnings\"].apply(\n", "    lambda x: earnings_bucket(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f65091c7-4233-4ee0-ae73-3b3b99a15745", "metadata": {}, "outputs": [], "source": ["payout_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bf783098-3283-48f5-8718-d39d58973cb2", "metadata": {}, "outputs": [], "source": ["final_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "258b3a2a-652a-46c8-95fd-101420cd8a01", "metadata": {}, "outputs": [], "source": ["outlets_data[\"Outlet ID\"] = outlets_data[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "449fbb72-f9cc-4565-b7e5-497516e85128", "metadata": {}, "outputs": [], "source": ["final_data_new = final_data1.merge(\n", "    outlets_data, left_on=[\"store_id\"], right_on=[\"Outlet ID\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e0d51d1d-e97e-4f37-a0c3-a3a1495a1546", "metadata": {}, "outputs": [], "source": ["final_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2108bc60-cb23-4a3d-9870-11c962a74156", "metadata": {}, "outputs": [], "source": ["final_data_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "062bf43a-b025-4451-95b4-3e8df2c8e428", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "262146c7-8245-4fb4-8ccc-a0196abc8ca1", "metadata": {}, "outputs": [], "source": ["final_data_new[\"IPH\"] = np.where(\n", "    final_data_new[\"gig\"] == \"yes\",\n", "    (final_data_new[\"picked_quantity\"] * 60) / final_data_new[\"active_time\"],\n", "    (\n", "        (\n", "            final_data_new[\"total_picked_quantity\"]\n", "            + final_data_new[\"total_putaway_quantity\"] / 2\n", "        )\n", "        * 60\n", "    )\n", "    / (\n", "        final_data_new[\"break_time\"]\n", "        + final_data_new[\"picking_logged_in_time\"]\n", "        + final_data_new[\"putaway_logged_in_time\"]\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d039ec52-676d-49f0-9f40-570e8d664aaa", "metadata": {}, "outputs": [], "source": ["final_data_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "22759e9c-0e9a-4798-8f10-2840987e4620", "metadata": {}, "outputs": [], "source": ["final_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03ebd620-017a-46e6-b627-016d383fb7c2", "metadata": {}, "outputs": [], "source": ["final_data_new[\"iph_bucket\"] = final_data_new[\"IPH\"].apply(lambda x: iph_bucket(x))"]}, {"cell_type": "code", "execution_count": null, "id": "7491090e-a24f-4af7-8d04-532cdef3993b", "metadata": {}, "outputs": [], "source": ["final_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "69f43a90-63cb-4ef1-8f43-8885b1e287d9", "metadata": {}, "outputs": [], "source": ["final_data_new[\"date\"] = pd.to_datetime(final_data_new[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c3bf7881-2537-4809-b4d0-111c6f77dcd1", "metadata": {}, "outputs": [], "source": ["final_data_new[\"date\"] = final_data_new[\"date\"].apply(lambda x: x.date())"]}, {"cell_type": "code", "execution_count": null, "id": "7c895b02-157c-4af8-a884-d0ae3a98a990", "metadata": {}, "outputs": [], "source": ["final_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f3bda3c7-2f90-4a4e-94f5-58fb6640afda", "metadata": {}, "outputs": [], "source": ["final_data_new[\"week_start\"] = final_data_new[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "final_data_new[\"week_end\"] = final_data_new[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "39005cb3-5ab7-4052-94b9-08fe72e17a8d", "metadata": {}, "outputs": [], "source": ["final_data_new[\"week\"] = (\n", "    final_data_new[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + final_data_new[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42b7585a-87f8-41ea-9e19-fa42d1964dec", "metadata": {}, "outputs": [], "source": ["final_data_new_new = final_data_new[\n", "    final_data_new[\"role\"].isin([\"Captain\", \"Picker\", \"captain\", \"picker\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "858aa4cc-00d1-4d6a-a72b-6d506bd64324", "metadata": {}, "outputs": [], "source": ["final_data_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5227d7af-73c0-463f-ab97-5ad922133438", "metadata": {}, "outputs": [], "source": ["final_data_new_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7a93e56d-b351-4cb5-bb7e-6a0b97ad4d34", "metadata": {}, "outputs": [], "source": ["final_data_new_new_group = (\n", "    final_data_new_new.groupby([\"date\", \"gig\"])\n", "    .agg(\n", "        {\n", "            \"total_picked_quantity\": \"sum\",\n", "            \"total_putaway_quantity\": \"sum\",\n", "            \"break_time\": \"sum\",\n", "            \"picking_logged_in_time\": \"sum\",\n", "            \"putaway_logged_in_time\": \"sum\",\n", "            \"active_time\": \"sum\",\n", "            \"picked_quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28ce140f-dbb4-40dd-b32c-4d80fabd5f9d", "metadata": {}, "outputs": [], "source": ["final_data_new_new_group[\"IPH\"] = np.where(\n", "    final_data_new_new_group[\"gig\"] == \"yes\",\n", "    (final_data_new_new_group[\"picked_quantity\"] * 60)\n", "    / final_data_new_new_group[\"active_time\"],\n", "    (\n", "        (\n", "            final_data_new_new_group[\"total_picked_quantity\"]\n", "            + final_data_new_new_group[\"total_putaway_quantity\"] / 2\n", "        )\n", "        * 60\n", "    )\n", "    / (\n", "        final_data_new_new_group[\"break_time\"]\n", "        + final_data_new_new_group[\"picking_logged_in_time\"]\n", "        + final_data_new_new_group[\"putaway_logged_in_time\"]\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4fe3e0bf-77c5-44da-ba85-c17399fbf666", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "55eb0851-daff-4aa7-af0e-58a01fbec25e", "metadata": {}, "outputs": [], "source": ["final_data_new_new_group[\"week_start\"] = final_data_new_new_group[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "final_data_new_new_group[\"week_end\"] = final_data_new_new_group[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8ce1affe-aba8-489a-b930-eab39f7f8707", "metadata": {}, "outputs": [], "source": ["final_data_new_new_group[\"week\"] = (\n", "    final_data_new_new_group[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + final_data_new_new_group[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65014bbc-cc1c-40df-8cee-bc70b186d0a4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_data_new_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Sheet44\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aeb1857d-3332-4b6c-bd15-2e6c55cc3020", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_data_new_new_group,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Sheet48\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7e322470-6901-491f-9e8c-5118e14be26d", "metadata": {}, "outputs": [], "source": ["# sns.histplot(payout_data_new[payout_data_new['store_id']==3671]['earnings'])"]}, {"cell_type": "code", "execution_count": null, "id": "6ccca634-b02a-48bc-aca7-bf142226c340", "metadata": {}, "outputs": [], "source": ["payout_data_new = payout_data_new[payout_data_new[\"employee_id\"].str.contains(\"GW\")]"]}, {"cell_type": "code", "execution_count": null, "id": "c938b408-cff6-47dd-8e9b-2a2c04ef4a22", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    payout_data_new,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Sheet45\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "732fa193-1477-4a7f-b53b-735a6e98a996", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_discipline,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Sheet46\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c151cd4-b5e8-4891-8dda-ca1347cebbed", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_util,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"Sheet47\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "49820366-6674-4426-b6b6-b3b29e37c968", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "117e6c71-bf19-405e-9240-71bd38602bfb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1aee9c24-875c-49c8-a1cf-4cf0c110a4c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "674f8503-8044-4945-9eba-14fcb787dd41", "metadata": {}, "outputs": [], "source": ["break_time_q = \"\"\"select date(break_start_time+interval '5.5 hours') as date_ts, site_id, employee_id,\n", "  coalesce(sum(datediff(seconds,break_start_time,coalesce(break_end_time,current_timestamp::timestamp)))/60.0,0) as total_break_time\n", "  from lake_ops_management.break_time\n", "  where (date(break_start_time+interval '5.5 hours') between '{start_date}' and '{end_date}')\n", "  and site_id in {outlets_list}\n", "  group by 1,2,3\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlets_list=tuple(outlets_list)\n", ")\n", "\n", "\n", "break_time_data = pd.read_sql(break_time_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "f64e13c8-2357-4c11-82e8-e24a4c63da93", "metadata": {}, "outputs": [], "source": ["break_time_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "901a02f5-5e9d-4058-ad40-9a5d50f01df2", "metadata": {}, "outputs": [], "source": ["break_time_data[\"week_start\"] = break_time_data[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday()))\n", ")\n", "\n", "break_time_data[\"week_end\"] = break_time_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a15b259f-1e8f-4f23-b9e9-db23630f6b9b", "metadata": {}, "outputs": [], "source": ["break_time_data[\"week\"] = (\n", "    break_time_data[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + break_time_data[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29426f50-e8fb-4120-b0be-f3b1395f55d4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    break_time_data,\n", "    \"1VgocSkUWwSnHmMSUkHqWgSn4UlM61twCM3CkLPuxWf0\",\n", "    \"break_time\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "364d3eba-6e08-4729-bf8d-7305c3f0fa47", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f523bfbc-fb26-4574-95ea-ed784a8be0ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "964bb04d-80c9-409f-b5f8-0ad45e09a6e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0f03188e-fdc8-4753-bc39-5668dce80785", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}