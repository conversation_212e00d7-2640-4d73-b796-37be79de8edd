{"cells": [{"cell_type": "code", "execution_count": null, "id": "6a33a8e3-7a6f-49dd-8166-44d423da8011", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "0821b065-5887-44bd-9df2-be1a646c7ed4", "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "s_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "e_date = (datetime.now() - timedelta(days=0)).date()\n", "print(\"s_date:\", s_date)\n", "print(\"e_date:\", e_date)"]}, {"cell_type": "code", "execution_count": null, "id": "2092104e-900b-4415-9976-058e7a9ce74a", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto_con = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "f636ae98-27d2-4358-b38b-9fc40b92e784", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with base as\n", "(select \n", "date as \"Date\",\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       replace(city_name,'Bangalore','Bengaluru') as \"City\",\n", "       hour_flag,\n", "       count(distinct order_id) as \"Orders\",\n", "       avg(checkout_to_picker_assigned) as avg_checkout_to_picker_assigned,\n", "       avg(billing_complete_to_order_scanned) as avg_billing_complete_to_order_scanned,\n", "       avg(instore_time) as avg_instore_time,\n", "       round(count(distinct case when delivery_time <= 900 then order_id end)*100.00/nullif(count(distinct order_id),0),2) as \"% orders Delivered within 15 Mins\"\n", "       \n", "from\n", "(\n", "SELECT date(order_checkout_ts_ist) as date,\n", "    c.name as outlet_name,\n", "       outlet_id,\n", "       trim(location) as city_name,\n", "       o.order_id,\n", "       case when extract(hour from order_checkout_ts_ist) between 6 and 11 then '6-12'\n", "       when extract(hour from order_checkout_ts_ist) between 16 and 21 then '16-22'\n", "       ----when extract(hour from order_checkout_ts_ist) between 18 and 23 then '18-24'\n", "       end as hour_flag,\n", "       (DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist)) as checkout_to_picker_assigned,\n", "       --DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) as picking_start_to_completed,\n", "       DATEDIFF(seconds,coalesce(order_billing_completed_ts_ist,order_picking_completed_ts_ist),order_scanned_ts_ist) as billing_complete_to_order_scanned,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) as delivery_time\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id\n", "WHERE \n", "  date(order_checkout_ts_ist) = (CURRENT_DATE)\n", "and is_rescheduled=false \n", "\n", "and order_picking_completed_ts_ist is not null and \n", "order_type='super_express'\n", "  group by 1,2,3,4,5,6,7,8,9,10\n", ")\n", "group by 1,2,3,4,5\n", "order by 1 desc)\n", "\n", "\n", "select a.\"Date\",\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       hour_flag,\n", "       a.\"Orders\",\n", "       avg_checkout_to_picker_assigned,\n", "       avg_billing_complete_to_order_scanned,\n", "       avg_instore_time,\n", "       \"% orders Delivered within 15 Mins\"\n", "from base a\n", "order by 1 desc\"\"\"\n", "\n", "orders_data = pd.read_sql(sqlalchemy.text(q), redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "e4c02bc8-302f-4cfa-97f4-1afe19a8a6a0", "metadata": {}, "outputs": [], "source": ["# orders_data['avg_billing_complete_to_order_scanned']"]}, {"cell_type": "code", "execution_count": null, "id": "d3320e7a-c861-43de-99fb-3bf8001eb5f4", "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f27473d5-c264-471c-a5ae-2096484c4a50", "metadata": {}, "outputs": [], "source": ["orders_data[\"poor_stores\"] = orders_data[\"% orders delivered within 15 mins\"].apply(\n", "    lambda x: 1 if int(x) < 40 else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e5958d77-aa82-42de-aa8e-354d927cb211", "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03b6824f-b7c3-4dc0-9714-f47db4dcb9d2", "metadata": {}, "outputs": [], "source": ["orders_data[\"orders_within_15_mins\"] = (\n", "    (100 - orders_data[\"% orders delivered within 15 mins\"]) * orders_data[\"orders\"]\n", ") / 100"]}, {"cell_type": "code", "execution_count": null, "id": "1eed342e-801b-4915-bfd8-6abef1b3a055", "metadata": {}, "outputs": [], "source": ["orders_data = orders_data.sort_values(\n", "    by=[\"poor_stores\", \"orders_within_15_mins\"], ascending=[False, False]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3eaa04d3-b3c7-405d-a54d-750664d83415", "metadata": {}, "outputs": [], "source": ["orders_data = orders_data[orders_data[\"poor_stores\"] == 1]\n", "# orders_data = orders_data[~orders_data['hour_flag'].isnull()]"]}, {"cell_type": "code", "execution_count": null, "id": "dea2bb03-64c8-4112-98b8-c6c09770ddbf", "metadata": {}, "outputs": [], "source": ["orders_data[\"constant\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "fcb44d86-012c-4897-bac7-af22a8e33c58", "metadata": {}, "outputs": [], "source": ["orders_data[\"rank\"] = orders_data.groupby([\"date\", \"hour_flag\"])[\"constant\"].apply(\n", "    lambda x: x.cumsum()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "12b25e1a-7dfc-454f-a1ce-db1336dd4413", "metadata": {}, "outputs": [], "source": ["orders_data[\"checkout_to_picker_assign_breach\"] = orders_data[\n", "    \"avg_checkout_to_picker_assigned\"\n", "].apply(lambda x: 0 if x < 40 else 1)"]}, {"cell_type": "code", "execution_count": null, "id": "3836b03f-5c02-4e90-8f94-7505ed693a43", "metadata": {}, "outputs": [], "source": ["orders_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4fa5ff7a-4d8e-4c68-9755-50cba6d2173e", "metadata": {}, "outputs": [], "source": ["score_q = \"\"\"\n", "\n", "with putaway_base AS (\n", "    \n", "    SELECT \n", "        date(put_list.created_at + INTERVAL '5.5 HOURS') AS date_ts,\n", "        \n", "        l.name AS city,\n", "        outlet.id AS outlet_id ,\n", "        outlet.name AS outlet_name,\n", "        put_list.id as putlist_id,\n", "        u.emp_id, \n", "        u.name AS employee_name,\n", "        case when extract(hour from (put_list.created_at+interval '5.5 hours')) between 6 and 11 then '6-12'\n", "       when extract(hour from (put_list.created_at+interval '5.5 hours')) between 16 and 21 then '16-22'\n", "       ----when extract(hour from (put_list.created_at+interval '5.5 hours')) between 18 and 23 then '18-24'\n", "       end as hour_flag,\n", "                \n", "        SUM(placed_quantity) as qty\n", "    FROM \n", "        lake_warehouse_location.warehouse_put_list \n", "            AS put_list\n", "            \n", "    INNER JOIN \n", "        lake_retail.console_outlet \n", "            AS outlet \n", "            ON outlet.id=put_list.outlet_id\n", "    INNER JOIN \n", "        lake_retail.console_location \n", "            AS l \n", "            ON l.id=outlet.tax_location_id\n", "            \n", "    INNER JOIN \n", "        lake_warehouse_location.warehouse_put_list_item \n", "        AS put_list_item \n", "        ON put_list_item.putlist_id=put_list.id\n", "        \n", "    INNER JOIN\n", "        lake_retail.warehouse_user \n", "            AS u \n", "            ON u.emp_id = put_list.assigned_to_name\n", "    WHERE\n", "        outlet.business_type_id = 7   -- filter for darkstores \n", "        -- AND put_list.creation_type in (10)  -- \n", "        and date(put_list.created_at + INTERVAL '5.5 hours')=current_date \n", "        AND emp_id is NOT NULL\n", "    GROUP BY \n", "        1,2,3,4,5,6,7,8\n", "        \n", "        \n", "    \n", "),\n", "\n", "\n", "---\n", "-- Putaway aggregation\n", "---\n", "\n", "putaway_agg as (\n", "\n", "SELECT\n", "    date_ts,\n", "    outlet_id,\n", "    outlet_name,\n", "    emp_id,\n", "    employee_name,\n", "    hour_flag,\n", "    city,\n", "    SUM(qty) as qty\n", "from putaway_base \n", "group by 1,2,3,4,5,6,7\n", "\n", "),\n", "\n", "\n", "\n", "--\n", "-- Order level picking time \n", "--\n", "picking_base as\n", "(\n", "        select date,employee_id, hour_flag, picklist_id,outlet_id,outlet_name, sum(picked_quantity) as picked_quantity\n", "        from \n", "        (SELECT\n", "            date(wpl.assigned_at+interval '5.5 hours') as date,\n", "            case when extract(hour from ((wpl.assigned_at) + interval '330 minute')) between 6 and 11 then '6-12'\n", "       when extract(hour from ((wpl.assigned_at) + interval '330 minute')) between 16 and 21 then '16-22'\n", "       ---when extract(hour from ((wpl.assigned_at) + interval '330 minute')) between 18 and 23 then '18-24'\n", "       end as hour_flag,            \n", "       wpl.assigned_to_name as employee_id,\n", "            item_id,\n", "            wpl.id as picklist_id,\n", "            wpl.outlet_id, co.name as outlet_name,\n", "            sum(pli.picked_quantity) as picked_quantity\n", "       \n", "        FROM\n", "             lake_oms_bifrost.oms_suborder \n", "                AS base\n", "        JOIN\n", "            lake_warehouse_location.warehouse_pick_list_order_mapping \n", "                AS wplom\n", "                ON (wplom.order_id) = (base.id)-- this is not a mistake\n", "        JOIN\n", "            lake_warehouse_location.warehouse_pick_list \n", "                AS wpl\n", "                ON wpl.id = wplom.pick_list_id\n", "        JOIN lake_retail.console_outlet co on co.id = wpl.outlet_id\n", "        JOIN\n", "            lake_warehouse_location.pick_list_log \n", "                AS pll\n", "                ON pll.pick_list_id = wpl.id\n", "                    AND pll.picklist_state in (4) -- state 4 means picking completed\n", "        left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = wpl.id\n", "        \n", "        WHERE\n", "             date(pll.pos_timestamp+interval '5.5 hours') =current_date\n", "            AND date(wpl.created_at+interval '5.5 hours') =current_date\n", "            AND date(wplom.created_at+interval '5.5 hours') =current_date\n", "        GROUP BY\n", "            1, 2, 3, 4,5,6,7 having sum(pli.picked_quantity)>0\n", "    ) group by 1,2,3,4,5,6\n", "    ),\n", "    \n", "    \n", "    \n", "    picking_agg as (\n", "\n", "select date as date_ts,\n", "       outlet_id,\n", "       outlet_name,\n", "       employee_id as emp_id,\n", "       hour_flag,\n", "       SUM(CAST(picked_quantity AS INT)) as picked_quantity\n", "from picking_base \n", "     group by 1,2,3,4,5\n", "),\n", "\n", "--\n", "-- Get a base on all employee's \n", "--\n", "\n", "emp_agg AS (\n", "\n", "            SELECT \n", "                emp_id,\n", "                date_ts,\n", "                outlet_id,\n", "                outlet_name,\n", "                hour_flag\n", "            FROM \n", "                putaway_agg\n", "            GROUP BY 1,2,3,4,5\n", "            \n", "         UNION all\n", "         \n", "            SELECT \n", "                emp_id,\n", "                date_ts,\n", "                outlet_id,\n", "                outlet_name,\n", "                hour_flag\n", "            FROM \n", "                picking_agg\n", "            GROUP BY 1,2,3,4,5\n", "),\n", "         \n", "emp_base AS (\n", "\n", "    SELECT \n", "        emp_id,\n", "        date_ts,\n", "        outlet_id,\n", "        outlet_name,\n", "        hour_flag\n", "    FROM\n", "        emp_agg\n", "        GROUP BY 1,2,3,4,5\n", ")\n", "\n", "\n", "select distinct d.*,b.picked_quantity, a.qty as putaway_qty\n", "from emp_base \n", "            AS d\n", "    LEFT JOIN \n", "        putaway_agg \n", "            AS a \n", "            ON d.emp_id = a.emp_id \n", "            AND d.date_ts = a.date_ts \n", "            AND d.outlet_id = a.outlet_id\n", "            and d.hour_flag = a.hour_flag\n", "    LEFT JOIN \n", "        picking_agg\n", "            AS  b \n", "            ON d.emp_id = b.emp_id \n", "            AND b.date_ts = d.date_ts\n", "            and d.hour_flag = b.hour_flag\n", "\n", "    \n", "\"\"\"\n", "\n", "\n", "productivity_data = pd.read_sql(score_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "94596159-e34d-4b77-a97d-661a6164ff37", "metadata": {}, "outputs": [], "source": ["productivity_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb7f2668-5de7-4837-893e-63515bf88442", "metadata": {}, "outputs": [], "source": ["productivity_data = productivity_data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "33770324-cb18-40a6-8007-fa48960da999", "metadata": {}, "outputs": [], "source": ["productivity_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "690720f3-5ff2-45fe-b299-ed5831018999", "metadata": {}, "outputs": [], "source": ["productivity_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "25e77064-80f5-412b-8c6a-62bd509de323", "metadata": {}, "outputs": [], "source": ["productivity_data[\"score\"] = (\n", "    productivity_data[\"picked_quantity\"] * 2.08 + productivity_data[\"putaway_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b7f1afcb-1604-4776-8c49-23b31a668bc7", "metadata": {}, "outputs": [], "source": ["productivity_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "efea0e21-c69c-439c-b994-0a0b9fb76180", "metadata": {}, "outputs": [], "source": ["productivity_data[\"score\"].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "9a5e32eb-6ff3-449f-94d3-497c23f014ef", "metadata": {}, "outputs": [], "source": ["productivity_data[\"is_score_thresh_breached\"] = productivity_data[\"score\"].apply(\n", "    lambda x: 1 if x < 500 else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5b8130e0-6972-4189-9c46-66d459abf87e", "metadata": {}, "outputs": [], "source": ["productivity_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a51223ed-b2aa-4e72-a5d3-22465180c57a", "metadata": {}, "outputs": [], "source": ["productivity_data.score.quantile(0.75)"]}, {"cell_type": "code", "execution_count": null, "id": "7e0931cc-ae77-466d-8df6-db8cbcbf4812", "metadata": {}, "outputs": [], "source": ["productivity_data[\n", "    (productivity_data[\"outlet_id\"] == 2738)\n", "    & (productivity_data[\"hour_flag\"] == \"6-12\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f9012c01-e75f-48c5-a430-784fce9a5d44", "metadata": {}, "outputs": [], "source": ["productivity_data_group = (\n", "    productivity_data.groupby([\"hour_flag\", \"outlet_id\", \"outlet_name\"])\n", "    .agg({\"emp_id\": \"nunique\", \"is_score_thresh_breached\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c925cc61-c0a4-4a67-9d6b-80cb669d7243", "metadata": {}, "outputs": [], "source": ["productivity_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6350ad5a-048a-4cef-9213-1de77285ed3f", "metadata": {}, "outputs": [], "source": ["productivity_data_group[\"% employees performing good\"] = 100 - (\n", "    productivity_data_group[\"is_score_thresh_breached\"]\n", "    * 100.0\n", "    / productivity_data_group[\"emp_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d24647df-cd5f-4a2e-b260-543e29bd4a37", "metadata": {}, "outputs": [], "source": ["productivity_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "042997b5-41d9-4e57-8d81-97fd7a0e12d4", "metadata": {}, "outputs": [], "source": ["productivity_data_group.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "b33303ac-90a1-4e6f-8ce5-34423734c1e6", "metadata": {}, "outputs": [], "source": ["productivity_data_group[productivity_data_group[\"outlet_id\"] == 2738]"]}, {"cell_type": "code", "execution_count": null, "id": "d8640925-043b-4d30-bae3-533b1c99c18a", "metadata": {}, "outputs": [], "source": ["productivity_data_group[\"is_productivity_low\"] = productivity_data_group[\n", "    \"% employees performing good\"\n", "].apply(lambda x: 1 if int(x) < 85 else 0)"]}, {"cell_type": "code", "execution_count": null, "id": "09dafb7b-dbb5-4edd-934f-3cfc53881cc3", "metadata": {}, "outputs": [], "source": ["actual_manpower_q = \"\"\"\n", "with all_data as\n", "(SELECT date(order_checkout_ts_ist) as date_ts,outlet_id,\n", "extract(hour from order_checkout_ts_ist) as hour,\n", "       \n", "    picker_id as employee_id\n", "      \n", "      \n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "WHERE (date(order_checkout_ts_ist)= CURRENT_DATE)\n", "and order_picking_completed_ts_ist is not null and business_type_id=7\n", "and extract(hour from order_checkout_ts_ist) between 6 and 23\n", "  group by 1,2,3,4 \n", "  ---having count(distinct order_id)>0\n", "  )\n", "  \n", "\n", "\n", "\n", "select date_ts, outlet_id, c.name as outlet_name,\n", "hour,\n", "count(distinct employee_id) as actual_picker_req from all_data a\n", "inner join lake_retail.console_outlet c on c.id = a.outlet_id\n", "where business_type_id=7\n", "group by 1,2,3,4\n", "\n", "  \n", "\"\"\"\n", "\n", "\n", "actual_manpower = pd.read_sql(actual_manpower_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "ad69b222-befa-49dd-8c9f-eb797dac75d7", "metadata": {}, "outputs": [], "source": ["actual_manpower.head()"]}, {"cell_type": "code", "execution_count": null, "id": "31f244a3-7a8a-4529-9b1c-a7f3a7e8b8de", "metadata": {}, "outputs": [], "source": ["actual_manpower[actual_manpower[\"outlet_id\"] == 2153]"]}, {"cell_type": "code", "execution_count": null, "id": "0a14481f-f58b-47ec-a4d3-155a3f1a5510", "metadata": {}, "outputs": [], "source": ["def hour_func(x):\n", "    if x >= 6 and x <= 11:\n", "        return \"6-12\"\n", "    elif x >= 16 and x <= 21:\n", "        return \"16-22\"\n", "    # elif x>=18 and x<=23:\n", "    #     return '18-24'\n", "    else:\n", "        return \"0\""]}, {"cell_type": "code", "execution_count": null, "id": "7f88ddba-aad0-4b81-88dc-a5b8bdfdbb1c", "metadata": {}, "outputs": [], "source": ["actual_manpower[\"hour_flag\"] = actual_manpower[\"hour\"].apply(lambda x: hour_func(x))"]}, {"cell_type": "code", "execution_count": null, "id": "d17d49eb-1382-48ec-93f9-e642f13e619f", "metadata": {}, "outputs": [], "source": ["actual_manpower.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d995aa7d-ffd3-4ac2-a524-c7fef9553ed5", "metadata": {}, "outputs": [], "source": ["actual_manpower_group = (\n", "    actual_manpower.groupby([\"outlet_id\", \"outlet_name\", \"date_ts\", \"hour_flag\"])\n", "    .agg({\"actual_picker_req\": \"max\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c29be6d-57d9-48f2-89c6-4395097b3dd9", "metadata": {}, "outputs": [], "source": ["actual_manpower_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2c1a897d-89e6-4fc1-9189-838b25a81654", "metadata": {}, "outputs": [], "source": ["actual_manpower_group[actual_manpower_group[\"outlet_id\"] == 2153]"]}, {"cell_type": "code", "execution_count": null, "id": "ad985af3-5fcb-446f-afc8-11fb6df842ab", "metadata": {}, "outputs": [], "source": ["suggested_manpower_q = \"\"\"\n", "select * from metrics.instore_manpower_requirement where date = '2022-05-08'\n", "\"\"\"\n", "\n", "suggested_manpower_data = pd.read_sql(suggested_manpower_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "aa996356-031a-49ad-ac10-4c31f07e4a72", "metadata": {}, "outputs": [], "source": ["suggested_manpower_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ab7ccd91-95b4-4b27-aba9-580033367df8", "metadata": {}, "outputs": [], "source": ["suggested_manpower_data[\"hour_flag\"] = suggested_manpower_data[\"hour\"].apply(\n", "    lambda x: hour_func(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bac77867-015d-4e3a-bdf5-cac42929429c", "metadata": {}, "outputs": [], "source": ["suggested_manpower_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60e4f25e-bc95-4b63-a04a-959d0b157485", "metadata": {}, "outputs": [], "source": ["suggested_manpower_data_group = (\n", "    suggested_manpower_data.groupby([\"outlet\", \"outlet_name\", \"date\", \"hour_flag\"])\n", "    .agg({\"picker_req\": \"max\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "797ed0a9-5c07-48e9-b79a-398d3da4e442", "metadata": {}, "outputs": [], "source": ["suggested_manpower_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "82f3caf2-405e-4d0e-af51-c3932cb5266c", "metadata": {}, "outputs": [], "source": ["manpower_final_data = actual_manpower_group.merge(\n", "    suggested_manpower_data_group,\n", "    left_on=[\"outlet_id\", \"outlet_name\", \"hour_flag\"],\n", "    right_on=[\"outlet\", \"outlet_name\", \"hour_flag\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3f69cb86-3ecb-4aa7-983a-16434fbc7819", "metadata": {}, "outputs": [], "source": ["manpower_final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c647c707-3f1b-4ad3-91c3-be81881b6ad5", "metadata": {}, "outputs": [], "source": ["manpower_final_data[\"is_manpower_low\"] = manpower_final_data.apply(\n", "    lambda x: 1 if x[\"actual_picker_req\"] < x[\"picker_req\"] else 0, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2859db5c-3bf2-4c8b-b50f-0ac03b70de9b", "metadata": {}, "outputs": [], "source": ["manpower_final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc6adbe1-d0a7-419f-92a8-7113852ce885", "metadata": {}, "outputs": [], "source": ["manpower_final_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7e7f0298-d878-4023-a9a8-4a5d3435f1e2", "metadata": {}, "outputs": [], "source": ["manpower_final_data = manpower_final_data[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"date_ts\",\n", "        \"hour_flag\",\n", "        \"actual_picker_req\",\n", "        \"picker_req\",\n", "        \"is_manpower_low\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "12dcec3c-9410-4faf-a23d-3bf346b1dc11", "metadata": {}, "outputs": [], "source": ["productivity_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1acfd182-95f1-4aa0-bd2b-06206b1d3d3e", "metadata": {}, "outputs": [], "source": ["orders_data[\"is_rider_availability_low\"] = orders_data[\n", "    \"avg_billing_complete_to_order_scanned\"\n", "].apply(lambda x: 1 if x > 40 else 0)"]}, {"cell_type": "code", "execution_count": null, "id": "16eb01e6-d509-4173-bdd0-b9149660ba22", "metadata": {}, "outputs": [], "source": ["orders_data = orders_data.rename(columns={\"store id\": \"outlet_id\"})"]}, {"cell_type": "code", "execution_count": null, "id": "5edf68cc-339f-4393-922a-b4d2504e0a58", "metadata": {}, "outputs": [], "source": ["order_data_new = orders_data.merge(\n", "    productivity_data_group, on=[\"outlet_id\", \"hour_flag\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "592f3b86-ba4d-488c-8ef7-6a25cdae631a", "metadata": {}, "outputs": [], "source": ["order_data_final = order_data_new.merge(\n", "    manpower_final_data, on=[\"outlet_id\", \"hour_flag\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09186ea0-4014-4453-b1ba-84a9eb50c0bd", "metadata": {}, "outputs": [], "source": ["order_data_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9be78437-bc87-459d-9b85-f296a5119a75", "metadata": {}, "outputs": [], "source": ["order_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4aa8aba0-228f-49fe-baa2-30d88d02504b", "metadata": {}, "outputs": [], "source": ["order_data_final[\"is_manpower_low\"] = np.where(\n", "    order_data_final[\"checkout_to_picker_assign_breach\"] == 0,\n", "    0,\n", "    order_data_final[\"is_manpower_low\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "44dcfef3-d099-44e4-ac8a-b745dfbcd77c", "metadata": {}, "outputs": [], "source": ["order_data_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "68a58232-b466-4a33-8f4a-632e9ad09c30", "metadata": {}, "outputs": [], "source": ["order_data_final1 = order_data_final[(order_data_final[\"hour_flag\"] == \"6-12\")]"]}, {"cell_type": "code", "execution_count": null, "id": "c503200f-f213-4118-b370-c66568eb4c20", "metadata": {}, "outputs": [], "source": ["order_data_final1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dff6a1cf-b928-4147-8df2-129a7a2d63bb", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet = order_data_final1[\n", "    (order_data_final1[\"checkout_to_picker_assign_breach\"] == 1)\n", "    | (order_data_final1[\"is_productivity_low\"] == 1)\n", "    | (order_data_final1[\"is_manpower_low\"] == 1)\n", "    | (order_data_final1[\"is_rider_availability_low\"] == 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "68184d17-3e30-4c68-bbee-e2b1a7876c8d", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet.columns"]}, {"cell_type": "code", "execution_count": null, "id": "1fd2519a-7b48-4d68-a26f-004476d603d5", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b1f206b4-43f4-402e-9d3f-68934e9436c5", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c88455cd-da8c-4023-b7d3-33ce71ee3f96", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet = order_data_final_to_sheet.rename(\n", "    columns={\n", "        \"date\": \"Date\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"store name\": \"Store Name\",\n", "        \"city\": \"City\",\n", "        \"hour_flag\": \"Hour Window\",\n", "        \"orders\": \"Orders\",\n", "        \"avg_checkout_to_picker_assigned\": \"Avg Checkout to Assign Time (mins)\",\n", "        \"avg_billing_complete_to_order_scanned\": \"Avg Billing Complete To Order Scanned (mins)\",\n", "        \"avg_instore_time\": \"Avg Instore Time (mins)\",\n", "        \"% orders delivered within 15 mins\": \"% Orders delivered within 15 mins\",\n", "        \"orders_within_15_mins\": \"Orders delivered more than 15 mins\",\n", "        \"is_manpower_low\": \"Instore Manpower Crunch\",\n", "        \"is_rider_availability_low\": \"Outstore Manpower Crunch\",\n", "        \"% employees performing good\": \"% Employees performing good (>500 Score)\",\n", "        \"is_productivity_low\": \"Low Manpower Productivity\",\n", "        \"actual_picker_req\": \"Actual Manpower\",\n", "        \"picker_req\": \"Required Manpower\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cc5b0952-f0ab-4551-b0d0-1d5d17669c3a", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7d350131-bad9-472d-862b-358eb303363c", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet = order_data_final_to_sheet[\n", "    [\n", "        \"Date\",\n", "        \"Outlet ID\",\n", "        \"Store Name\",\n", "        \"City\",\n", "        \"Hour Window\",\n", "        \"Orders\",\n", "        \"Avg Checkout to Assign Time (mins)\",\n", "        \"Avg Billing Complete To Order Scanned (mins)\",\n", "        \"Avg Instore Time (mins)\",\n", "        \"% Orders delivered within 15 mins\",\n", "        \"Orders delivered more than 15 mins\",\n", "        \"Instore Manpower Crunch\",\n", "        \"Actual Manpower\",\n", "        \"Required Manpower\",\n", "        \"Outstore Manpower Crunch\",\n", "        \"Low Manpower Productivity\",\n", "        \"% Employees performing good (>500 Score)\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d91a12dc-e601-458f-9b45-6a065d396772", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet[\"Instore Manpower Crunch\"] = np.where(\n", "    order_data_final_to_sheet[\"Instore Manpower Crunch\"] == 1, \"Yes\", \"No\"\n", ")\n", "order_data_final_to_sheet[\"Outstore Manpower Crunch\"] = np.where(\n", "    order_data_final_to_sheet[\"Outstore Manpower Crunch\"] == 1, \"Yes\", \"No\"\n", ")\n", "order_data_final_to_sheet[\"Low Manpower Productivity\"] = np.where(\n", "    order_data_final_to_sheet[\"Low Manpower Productivity\"] == 1, \"Yes\", \"No\"\n", ")\n", "\n", "order_data_final_to_sheet[\"Required Manpower\"] = order_data_final_to_sheet[\n", "    \"Required Manpower\"\n", "].fillna(0)\n", "\n", "order_data_final_to_sheet[\"Avg Checkout to Assign Time (mins)\"] = (\n", "    order_data_final_to_sheet[\"Avg Checkout to Assign Time (mins)\"] / 60\n", ")\n", "order_data_final_to_sheet[\"Avg Billing Complete To Order Scanned (mins)\"] = (\n", "    order_data_final_to_sheet[\"Avg Billing Complete To Order Scanned (mins)\"] / 60\n", ")\n", "order_data_final_to_sheet[\"Avg Instore Time (mins)\"] = (\n", "    order_data_final_to_sheet[\"Avg Instore Time (mins)\"] / 60\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1e677b18-cb12-4e07-a0b5-cf40621ab224", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet[\n", "    \"Orders delivered more than 15 mins\"\n", "] = order_data_final_to_sheet[\"Orders delivered more than 15 mins\"].round(0)\n", "order_data_final_to_sheet[\n", "    [\n", "        \"% Orders delivered within 15 mins\",\n", "        \"Avg Checkout to Assign Time (mins)\",\n", "        \"Avg Billing Complete To Order Scanned (mins)\",\n", "        \"% Employees performing good (>500 Score)\",\n", "        \"Avg Instore Time (mins)\",\n", "    ]\n", "] = order_data_final_to_sheet[\n", "    [\n", "        \"% Orders delivered within 15 mins\",\n", "        \"Avg Checkout to Assign Time (mins)\",\n", "        \"Avg Billing Complete To Order Scanned (mins)\",\n", "        \"% Employees performing good (>500 Score)\",\n", "        \"Avg Instore Time (mins)\",\n", "    ]\n", "].round(\n", "    2\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "215c5fe1-3850-45be-a25b-5050c7e21716", "metadata": {}, "outputs": [], "source": ["order_data_final_to_sheet.shape"]}, {"cell_type": "code", "execution_count": null, "id": "04603362-7420-4145-8ec3-4956f1179ecd", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    order_data_final_to_sheet,\n", "    \"1NFh_bf43pVAhsADZBZ80l4nwhmgO1PGlqq-Oec3PhB0\",\n", "    \"Morning SLA Orders within 15 min <=40%\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "markdown", "id": "f2c962a5-924d-41db-8405-fe9c44686fd8", "metadata": {}, "source": ["# Evening Data Push"]}, {"cell_type": "code", "execution_count": null, "id": "e8d23540-df5c-4ec2-9cd7-6ab9ccd08e1e", "metadata": {}, "outputs": [], "source": ["order_data_final2 = order_data_final[(order_data_final[\"hour_flag\"] == \"16-22\")]"]}, {"cell_type": "code", "execution_count": null, "id": "68b7f6e9-ff02-4ed4-a8e9-ff022e1cdfe6", "metadata": {}, "outputs": [], "source": ["order_data_final2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4c557ef8-ab5b-49ff-888b-bdf6ed3563f4", "metadata": {}, "outputs": [], "source": ["if order_data_final2.shape[0] != 0 and now.hour > 22:\n", "    order_data_final_to_sheet1 = order_data_final2[\n", "        (order_data_final2[\"checkout_to_picker_assign_breach\"] == 1)\n", "        | (order_data_final2[\"is_productivity_low\"] == 1)\n", "        | (order_data_final2[\"is_manpower_low\"] == 1)\n", "        | (order_data_final2[\"is_rider_availability_low\"] == 1)\n", "    ]\n", "    order_data_final_to_sheet1 = order_data_final_to_sheet1.rename(\n", "        columns={\n", "            \"date\": \"Date\",\n", "            \"outlet_id\": \"Outlet ID\",\n", "            \"store name\": \"Store Name\",\n", "            \"city\": \"City\",\n", "            \"hour_flag\": \"Hour Window\",\n", "            \"orders\": \"Orders\",\n", "            \"avg_checkout_to_picker_assigned\": \"Avg Checkout to Assign Time (mins)\",\n", "            \"avg_billing_complete_to_order_scanned\": \"Avg Billing Complete To Order Scanned (mins)\",\n", "            \"avg_instore_time\": \"Avg Instore Time (mins)\",\n", "            \"% orders delivered within 15 mins\": \"% Orders delivered within 15 mins\",\n", "            \"orders_within_15_mins\": \"Orders delivered more than 15 mins\",\n", "            \"is_manpower_low\": \"Instore Manpower Crunch\",\n", "            \"is_rider_availability_low\": \"Outstore Manpower Crunch\",\n", "            \"% employees performing good\": \"% Employees performing good (>500 Score)\",\n", "            \"is_productivity_low\": \"Low Manpower Productivity\",\n", "            \"actual_picker_req\": \"Actual Manpower\",\n", "            \"picker_req\": \"Required Manpower\",\n", "        }\n", "    )\n", "\n", "    order_data_final_to_sheet1 = order_data_final_to_sheet1[\n", "        [\n", "            \"Date\",\n", "            \"Outlet ID\",\n", "            \"Store Name\",\n", "            \"City\",\n", "            \"Hour Window\",\n", "            \"Orders\",\n", "            \"Avg Checkout to Assign Time (mins)\",\n", "            \"Avg Billing Complete To Order Scanned (mins)\",\n", "            \"Avg Instore Time (mins)\",\n", "            \"% Orders delivered within 15 mins\",\n", "            \"Orders delivered more than 15 mins\",\n", "            \"Instore Manpower Crunch\",\n", "            \"Actual Manpower\",\n", "            \"Required Manpower\",\n", "            \"Outstore Manpower Crunch\",\n", "            \"Low Manpower Productivity\",\n", "            \"% Employees performing good (>500 Score)\",\n", "        ]\n", "    ]\n", "    order_data_final_to_sheet1[\"Instore Manpower Crunch\"] = np.where(\n", "        order_data_final_to_sheet1[\"Instore Manpower Crunch\"] == 1, \"Yes\", \"No\"\n", "    )\n", "    order_data_final_to_sheet1[\"Outstore Manpower Crunch\"] = np.where(\n", "        order_data_final_to_sheet1[\"Outstore Manpower Crunch\"] == 1, \"Yes\", \"No\"\n", "    )\n", "    order_data_final_to_sheet1[\"Low Manpower Productivity\"] = np.where(\n", "        order_data_final_to_sheet1[\"Low Manpower Productivity\"] == 1, \"Yes\", \"No\"\n", "    )\n", "\n", "    order_data_final_to_sheet1[\"Required Manpower\"] = order_data_final_to_sheet1[\n", "        \"Required Manpower\"\n", "    ].fillna(0)\n", "\n", "    order_data_final_to_sheet1[\"Avg Checkout to Assign Time (mins)\"] = (\n", "        order_data_final_to_sheet1[\"Avg Checkout to Assign Time (mins)\"] / 60\n", "    )\n", "    order_data_final_to_sheet1[\"Avg Billing Complete To Order Scanned (mins)\"] = (\n", "        order_data_final_to_sheet1[\"Avg Billing Complete To Order Scanned (mins)\"] / 60\n", "    )\n", "    order_data_final_to_sheet1[\"Avg Instore Time (mins)\"] = (\n", "        order_data_final_to_sheet1[\"Avg Instore Time (mins)\"] / 60\n", "    )\n", "    order_data_final_to_sheet1[\n", "        \"Orders delivered more than 15 mins\"\n", "    ] = order_data_final_to_sheet1[\"Orders delivered more than 15 mins\"].round(0)\n", "    order_data_final_to_sheet1[\n", "        [\n", "            \"% Orders delivered within 15 mins\",\n", "            \"Avg Checkout to Assign Time (mins)\",\n", "            \"Avg Billing Complete To Order Scanned (mins)\",\n", "            \"% Employees performing good (>500 Score)\",\n", "            \"Avg Instore Time (mins)\",\n", "        ]\n", "    ] = order_data_final_to_sheet1[\n", "        [\n", "            \"% Orders delivered within 15 mins\",\n", "            \"Avg Checkout to Assign Time (mins)\",\n", "            \"Avg Billing Complete To Order Scanned (mins)\",\n", "            \"% Employees performing good (>500 Score)\",\n", "            \"Avg Instore Time (mins)\",\n", "        ]\n", "    ].round(\n", "        2\n", "    )\n", "    pb.to_sheets(\n", "        order_data_final_to_sheet1,\n", "        \"1NFh_bf43pVAhsADZBZ80l4nwhmgO1PGlqq-Oec3PhB0\",\n", "        \"Evening SLA Orders within 15 min <=40%\",\n", "        clear_cache=True,\n", "    )\n", "\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "fcefe2e9-cf5a-435e-8e2e-f145f6bb46e8", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(order_data_final_to_sheet[['date', 'outlet_id', 'store name', 'city', 'hour_flag', 'orders',\n", "#        '% orders delivered within 15 mins',\n", "#        'orders_within_15_mins',\n", "#        'checkout_to_picker_assign_breach', 'is_rider_availability_low',\n", "# 'is_productivity_low', 'is_manpower_low']],'1NFh_bf43pVAhsADZBZ80l4nwhmgO1PGlqq-Oec3PhB0','Alert',clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}