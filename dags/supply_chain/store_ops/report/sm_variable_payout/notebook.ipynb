{"cells": [{"cell_type": "code", "execution_count": null, "id": "3ac8aa33-7db1-49dc-84cf-36bf2c76b607", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "01e818d1-625d-48ee-840f-e876f80dc9db", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "c226fbf8-52a4-4dd5-b920-65878b7038de", "metadata": {}, "outputs": [], "source": ["# get partner stores data\n", "sheet_id = \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\"\n", "sheet_name = \"Store info\"\n", "df = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "df[\"ownership\"] = df[\"Operation Mode\"].apply(\n", "    lambda x: \"PARTNER\" if x != \"Own\" else \"OWN\"\n", ")\n", "df.rename(columns={\"Outlet ID\": \"outlet_id\"}, inplace=True)\n", "\n", "store_data = df[[\"outlet_id\", \"ownership\"]]\n", "store_data[\"outlet_id\"] = store_data[\"outlet_id\"].astype(int)\n", "store_data.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "09b737af-a0e0-4a09-820a-7ddf45197336", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83f0ab6e-cdd3-4ba5-a252-7c69dc3f500f", "metadata": {}, "outputs": [], "source": ["comp = \"\"\"WITH\n", "\n", "abusive_list as (\n", "    select  distinct id as customer_id from lake_grofers_db.view_gr_user where blocked=true\n", "        union\n", "    select  distinct customer_id from lake_crm.crm_abuse_user\n", "),\n", "\n", "suborder_base as (\n", "    SELECT \n", "        ID,\n", "        ORDER_ID,\n", "        BACKEND_MERCHANT_ID\n", "    FROM \n", "        lake_oms_bifrost.oms_suborder \n", "        WHERE \n", "        (update_ts + interval '5.5 hours')::date >= current_date - 28\n", "            AND TYPE = 'RetailSuborder'\n", "            AND CURRENT_STATUS IN ('AT_DELIVERY_CENTER')\n", "        GROUP BY 1,2,3\n", "),\n", "\n", "delivered as\n", "(    \n", "    select distinct\n", "        CASE WHEN FC.FACILITY_ID::integer IS NULL THEN 0 ELSE FC.FACILITY_ID::integer END AS FACILITY_ID,\n", "        FC.FACILITY_NAME,\n", "        CASE WHEN BOM.EXTERNAL_ID::integer IS NULL THEN 0 ELSE BOM.EXTERNAL_ID::integer END AS BACKEND_MERCHANT_ID,\n", "        BOM.NAME AS BACKEND_MERCHANT_NAME,\n", "        CASE WHEN FC.pos_outlet_id::integer IS NULL THEN 0 ELSE FC.pos_outlet_id::integer END AS OUTLET_ID,\n", "        FC.pos_outlet_id AS OUTLET_NAME,\n", "        (o.update_ts + interval '5.5 hours')::date as delivery_date,\n", "        (timestamp 'epoch' + cast(o.slot_start as int) * INTERVAL '1 second' + interval '5.5 hour')::date as sch_date,\n", "        o.station as station,\n", "        m.city_name as city,\n", "        o.current_status, \n", "        o.type, \n", "        o.direction,\n", "        o.id as order_id,\n", "        o.cart_id,\n", "        os.id as suborder_id,\n", "        m.external_id as frontend_merchant_id,\n", "        m.name as frontend_merchant_name,\n", "        GRP.id as product_id,\n", "        CASE WHEN l1_grc.id in (9,1487) AND GRP.STORAGE_TYPE IN ('refrigerated','deep_freeze') then 'FnV Cold'\n", "        WHEN GRP.STORAGE_TYPE IN ('refrigerated','deep_freeze') THEN 'Cold'\n", "        WHEN l1_grc.id in (9,1487) then 'FnV'\n", "                  WHEN HANDLING_TYPE = 'large' THEN 'Large'\n", "                          ELSE 'Grocery'\n", "                      \n", "            END AS GENERAL_CATEGORY\n", "    from lake_oms_bifrost.oms_order o \n", "    left join lake_oms_bifrost.oms_order_item AS OOI\n", "            ON O.ID = OOI.order_ID\n", "    INNER join lake_cms.gr_product_category_mapping AS PCM ON ooi.PRODUCT_ID = PCM.PRODUCT_ID AND PCM.IS_PRIMARY = 'TRUE'\n", "    INNER join lake_cms.gr_category AS L3_GRC ON PCM.CATEGORY_ID = L3_GRC.ID\n", "    INNER join lake_cms.gr_category AS L2_GRC ON L3_GRC.PARENT_CATEGORY_ID = L2_GRC.ID\n", "    INNER join lake_cms.gr_category AS L1_GRC ON L2_GRC.PARENT_CATEGORY_ID = L1_GRC.ID\n", "    LEFT JOIN lake_cms.gr_product AS GRP ON OOI.PRODUCT_ID = GRP.ID\n", "    INNER JOIN suborder_base AS OS ON OS.ORDER_ID = O.ID \n", "    LEFT JOIN lake_oms_bifrost.view_oms_merchant m on o.merchant_id = m.id and  m.virtual_merchant_type = 'superstore_merchant'\n", "    LEFT join lake_oms_bifrost.view_oms_merchant AS BOM\n", "        ON OS.BACKEND_MERCHANT_ID = BOM.ID\n", "        AND BOM.VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "     LEFT JOIN dwh.dim_merchant_outlet_facility_mapping AS FC \n", "            ON BOM.EXTERNAL_ID = FC.BACKEND_MERCHANT_ID\n", "            and FC.is_current=true\n", "    \n", "    where\n", "    (o.update_ts + interval '5.5 hours')::date >= current_date - 28\n", "    and o.current_status = 'DELIVERED'\n", "    and o.type not like 'Digital%%'\n", "    and o.type not like 'Drop%%'\n", "    and o.direction = 'FORWARD'\n", "    and m.id not in (28759,28760,26680) -- removing load testing IDs\n", "    and lower(m.city_name) not in ('not in service area')\n", "    and lower(m.city_name) not like '%%b2b%%'\n", "    and lower(m.city_name) not like '%%test%%'\n", "),\n", "  null_category_duplicates as (select distinct a.cart_id,a.product_id,a.delivery_slot,b.complaint_category as valid_complaint_type from \n", "(select * from metrics.complaints where delivery_date::date >= current_date - 28 and complaint_category in ('','Null'))a\n", " join (select * from metrics.complaints where delivery_date::date >= current_date - 28 and complaint_category not in ('','Null')) b\n", "on a.cart_id=b.cart_id and a.product_id=b.product_id  and a.delivery_slot=b.delivery_slot\n", "),\n", "calls_base as (\n", "  SELECT \n", "    comments,\n", "    REGEXP_SUBSTR(comments, '[0-9]*[0-9]+', 1) AS join_key,\n", "    CASE\n", "        -- WHEN complaint_category IN ('Query__Adhoc','Query__Ghost calls','Query__Order Status','Others__Query_Feedback','GOBD6_Queries related to GOBD6','Query__Query related to Slots_MOV_Area') THEN 'Query'\n", "        -- WHEN complaint_category IN ('Withdrawing Grofers cash','CA Test Call', 'Tech__ADONP_DD', 'Service No__ADONP_DD', 'Request__Cancellation', 'My wallet__Grofers Cash', 'Wrap up time exceeded :10', 'Request__Request for <PERSON>', 'Tech__App_web not working', 'Wrap up time exceeded :120', 'Tech__Abuse_Unable to login', 'Cashback_Refund__Refund Status', 'Cashback_Refund__Refund Missing', 'Cashback_Refund__Cashback Status', 'Cashback_Refund__Cashback Missing', 'Cashback_Refund__RPU Refund Status', 'Miscellaneous__Waiting on customer', 'Cashback_Refund__RPU Refund Missing', 'Cashback and Refund__Refund Status_CRM', 'Smart Bachat Club__Issue related to SBC', 'Offers_Promotions__Discount not Availed', 'Edit Order__Query Related to Edit Order', 'Smart Bachat Club__Query related to SBC', 'Miscellaneous__CSAT_DSAT acknowledgment', 'Edit Order__Issue Related to Edit Order', 'Query__Delivery slots and serviceable area', 'Offers_Promotions__Grofers cashback mismatch', 'Product Unavailability__Product not available', 'Grofers Entrepreneurship__Business with Grofers', 'Request__Reschedule_Cancellation_Delivery instruction', 'Offers_Promotions__Query related to Bank_Wallet Offers', 'Offers_Promotions__Query related to Grofers Offers_EDLP', 'Offers_Promotions__Issue related to Grofers offers_EDLP', 'Offers_Promotions__Unable to apply Bank_wallet promo code', 'Ongoing Experiments__Query to related to share deal_referral', 'Offers_Promotions__Unable to apply Grofers promo code_Voucher', 'Request__GC Transfer to OMP_IMPS_Wallet delink_Unsubscription','Wrap up time exceeded :25') THEN 'Rest'\n", "        WHEN \n", "            complaint_category IN ('Last Mile__MDND','Issue with an order__MDND') THEN 'MDND'\n", "        WHEN \n", "            complaint_category IN ('Retail__Fake Item','Retail__Wrong item','Category__Incomplete_Incorrect Product description','incomplete_product_description',\n", "            'Issue with an order__Incomplete_Incorrect Product Description_Price') \n", "            THEN 'Wrong Item'\n", "        WHEN \n", "            complaint_category IN ('fnv quality issue', 'item(s) is near expiry', 'quality of item(s) was not good', \n", "            'quality is not as per my expectation', 'foreign matter found in product', 'quality_issue_customer', \n", "            'quality not good', 'quality_not_good', 'quality_expectation_not_met', 'poor_bad_taste', 'bad_smell', \n", "            'foreign_matter_found_in_product', 'items_near_expiry', 'erased_batch_details', \n", "            'bad smell', 'poor/bad taste', 'rotten product', 'foreign objects/matter', 'quality expectation not met', 'item_missing_lm', \n", "            'bad quality product', 'bad quality', 'Poor_Bad Taste', 'Foreign Objects_Matter','Issue with an order__Quality Issue_apart from bad smell_taste',\n", "            'Issue with an order__Bad Smell_Bad Taste','Issue with an order__ Quality Issue apart from bad smell taste',\n", "            'Issue with an order__Foreign Item in Product')\n", "            THEN 'Quality Issues'\n", "        WHEN \n", "            complaint_category IN ('erased batch details', 'item(s) is expired', 'expired_product_customer',\n", "            'expired items', 'items_expired', 'expired_items', 'erased_batch_details', 'item expired/old manufactured',\n", "            'item_missing_lm', 'expired item', 'expired product', 'items_near_expiry', 'Expired item(s) received', 'FnV quality issue', \n", "            'Retail__Bad smell', 'Retail__Poor_Bad Taste', 'Retail__Rotten Product', 'Retail__Foreign Objects_Matter',\n", "            'Retail__Quality Expectation not met', 'Retail__Item expired_Old manufactured','Item expired_Old manufactured',\n", "            'Issue with an order__Item Expired_Near Expiry') \n", "            THEN 'Item Expired'\n", "        WHEN \n", "            complaint_category IN ('Last Mile__FE Related','Issue with an order__FE at Fault') THEN 'FE Related'\n", "        WHEN \n", "            complaint_category IN ('Service No__Out of TAT', 'Service No__Edit Order', 'Service No__Retail Related', \n", "            'Service No__Last Mile Related', 'Service No__Image validation failed',\n", "            'Service No__Denial and Feedback Taken_EDLPDenial', 'Service No__Reschedule_cancellation_delivery instruction','Service No__Service No')\n", "            THEN 'Service No'\n", "        WHEN \n", "            complaint_category IN ('Retail__Quantity Mismatch','Last Mile__Incomplete product',\n", "            'Last Mile__Item_Freebie missing','Retail__Item_Freebie not procured','Issue with an order__Item_Freebie Missing') \n", "            THEN 'Item Missing'\n", "        WHEN \n", "            complaint_category IN ('Category__Price difference') \n", "            THEN 'Price Difference'\n", "        WHEN \n", "            complaint_category IN ('Last Mile__Delay in Delivery','Issue with an order__Delay in Delivery',\n", "            '10_mins Challenge__Issue related to 10_mins Challenge') \n", "            THEN 'Delay In Delivery'\n", "        WHEN \n", "            complaint_category IN ('Last Mile__Item_Freebie damaged','Issue with an order__Item_Freebie Damaged') \n", "            THEN 'Item Damaged'\n", "        WHEN \n", "            complaint_category IN ('Request__No longer needs the item') \n", "            THEN 'No Longer Required'\n", "        ELSE complaint_category END AS disposition_f,\n", "    start_time::date \n", "    FROM \n", "        consumer.cd_calls_data \n", "    WHERE \n", "        start_time::date  IS NOT NULL\n", "        AND TRIM(comments)!='' AND comments IS NOT NULL AND call_type='Inbound'\n", "        AND  start_time >= current_date - 28\n", "        AND complaint_category not in ('Query__Adhoc','Query__Ghost calls','Query__Order Status','Others__Query_Feedback','GOBD6_Queries related to GOBD6','Query__Query related to Slots_MOV_Area',\n", "        'Withdrawing Grofers cash','CA Test Call', 'Tech__ADONP_DD', 'Service No__ADONP_DD', 'Request__Cancellation', 'My wallet__Grofers Cash', 'Wrap up time exceeded :10', 'Request__Request for <PERSON>', 'Tech__App_web not working', 'Wrap up time exceeded :120', 'Tech__Abuse_Unable to login', 'Cashback_Refund__Refund Status',\n", "        'Cashback_Refund__Refund Missing', 'Cashback_Refund__Cashback Status', 'Cashback_Refund__Cashback Missing', \n", "        'Cashback_Refund__RPU Refund Status', 'Miscellaneous__Waiting on customer', 'Cashback_Refund__RPU Refund Missing',\n", "        'Cashback and Refund__Refund Status_CRM', 'Smart Bachat Club__Issue related to SBC', 'Offers_Promotions__Discount not Availed', \n", "        'Edit Order__Query Related to Edit Order', 'Smart Bachat Club__Query related to SBC', 'Miscellaneous__CSAT_DSAT acknowledgment', \n", "        'Edit Order__Issue Related to Edit Order', 'Query__Delivery slots and serviceable area', 'Offers_Promotions__Grofers cashback mismatch', 'Product Unavailability__Product not available', 'Grofers Entrepreneurship__Business with Grofers', \n", "        'Request__Reschedule_Cancellation_Delivery instruction',\n", "        'Offers_Promotions__Query related to Bank_Wallet Offers', \n", "        'Offers_Promotions__Query related to Grofers Offers_EDLP', \n", "        'Offers_Promotions__Issue related to Grofers offers_EDLP', 'Offers_Promotions__Unable to apply Bank_wallet promo code', \n", "        'Ongoing Experiments__Query to related to share deal_referral', 'Offers_Promotions__Unable to apply Grofers promo code_Voucher', \n", "        'Request__GC Transfer to OMP_IMPS_Wallet delink_Unsubscription','Wrap up time exceeded :25',\n", "        'Cashback_Refund_Invoice','Non_Queries__Waiting on Customer','Cashback_Refund_Invoice__Request for Invoice_COD Payment Link','Wrap up time exceeded :30','Non_Queries__CSAT_DSAT Acknowledgment',\n", "        'Exception__Hyperlocal Exception','ManualCalling__OpsCalling','Issue before placing an order__App_Web Not Working','ManualCalling__CustomerCalling','Cashback_Refund__Cashback Missing',\n", "        'Ongoing Experiments__Query to related to share deal_referral','Miscellaneous__Miscellaneous','Issue before placing an order__Unable to Apply Promo Code_Query about Offer',\n", "        'Cashback_Refund_Invoice__Cashback_Refund_RPU Status','Issue with an order__Order Status','Issue before placing an order__Area Serviceability',\n", "        'Miscellaneous__Miscellaneous -> Wrap up time exceeded :10','Cashback_Refund',\n", "        'Issue with an order__Reschedule_Cancellation_Delivery Instruction','Cashback_Refund_Invoice__GC Transfer to OMP_IMPS',\n", "        'Grofers Entrepreneurship__Business with Grofers','Wrap up time exceeded :5','Issue with an order__Delivery_Surge_Convenience Charge',\n", "        '10_mins Challenge__Query related to 10_mins Challenge','Query','Others__Query_Feedback -> Wrap up time exceeded :10',\n", "        'Cashback_Refund_Invoice__Request for Wallet Delink_Unsubscribe from Blinkit Email_SMS','ManualCalling',\n", "        'Wrap up time exceeded :40','Business with Blinkit__Business with Blinkit',\n", "        'Issue before placing an order__ADONP_Dual Debit','REFUND_MISSING','Issue before placing an order__Product Not Available','INCORRECT_REFUND')\n", "),\n", "\n", "compalint_calls as ( \n", "\n", "    SELECT\n", "        start_time::date AS install_dt,\n", "        NULL AS product_id,\n", "        NULL AS product_title,\n", "        join_key,\n", "        disposition_f AS disposition\n", "    FROM calls_base  \n", "        AS ACD\n", "    JOIN lake_oms_bifrost.oms_order \n", "        AS ORD \n", "        ON ORD.id::varchar=join_key\n", "    WHERE \n", "        (ORD.update_ts + interval '5.5 hours')::date >= current_date - 28\n", "        AND current_status!='CANCELLED'\n", "        AND type IN ('RetailForwardOrder','InternalForwardOrder')\n", "        AND join_key IS NOT NULL\n", ")\n", "\n", "\n", "select \n", "    d.delivery_date,\n", "    d.city,\n", "    d.outlet_id,\n", "    d.FACILITY_NAME,\n", "    upper(substring(d.station,8)) as station,\n", "    count(distinct d.order_id) as delivered_orders ,\n", "    count(\n", "        DISTINCT \n", "        CASE WHEN \n", "            (complaint_type = 'VALID'\n", "            AND lower(order_type) ILIKE 'retail%%')\n", "            OR cc.disposition is not null  \n", "            THEN d.order_id\n", "        END\n", "    ) AS all_complaints,\n", "\n", "\n", "\n", "-------------Item Missing-------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when \n", "                coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%freebie%%missing%%') \n", "                or (\n", "                    coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%missing%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') \n", "                    )\n", "                -- or (\n", "                --     (\n", "                --         coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%extra%%item%%') \n", "                --         and ( \n", "                --                 <PERSON><PERSON>COMPLAINT_SUB_CATEGORY not ILIKE ('%%no%%longer%%') \n", "                --                 or <PERSON><PERSON>COMPLAINT_SUB_CATEGORY not ILIKE ('%%not%%required%%') \n", "                --             )\n", "                --         )\n", "                --     )\n", "                or (\n", "                    coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%')\n", "                )\n", "                or (\n", "                    coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%')\n", "                )\n", "                or (\n", "                    (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%') \n", "                    or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%lm%%')  \n", "                    or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%doorstep%%'))\n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Wrong%%Product%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Missing%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Damaged%%Product%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Bad%%Quality%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Combo%%Issue%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Expired%%Product%%')\n", "                )\n", "                or ( \n", "                        coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                    and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Not%%Required%%')\n", "                )\n", "                or ( \n", "                        (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') \n", "                        or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%doorstep%%'))\n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing')\n", "                )\n", "                or cc.disposition='Item Missing'\n", "                or cc.disposition = 'MDND'\n", "                then d.order_id end\n", "                )  as \"<PERSON><PERSON> Missing\",\n", "\n", "  count(DISTINCT  case when (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%missing%%'))\n", "  or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%') and coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%lm%%')  and coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) not ILIKE ('%%item%%missing%%doorstep%%')  \n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "  then d.order_id end)  as \"Item Missing After Del\",\n", "   \n", "   \n", "  count(DISTINCT  case when ( (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%lm%%') or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%missing%%doorstep%%') )\n", "  and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null','','Product Missing','Item Missing'))\n", "  \n", "  then d.order_id end)  as \"Item Missing Doorstep\",\n", "\n", "\n", "count(DISTINCT CASE\n", "                  WHEN (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%extra%%item%%') and ( C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%no%%longer%%') \n", "or C.COMPLAINT_SUB_CATEGORY not ILIKE ('%%not%%required%%') ))THEN d.order_id\n", "              END) AS \"Excess received\",\n", " \n", " \n", "count(DISTINCT  case when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%freebie%%missing%%') \n", "or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%missing%%') and C.COMPLAINT_SUB_CATEGORY ILIKE ('%%combo%%') )\n", "\n", " then d.order_id end)  as \"<PERSON><PERSON>/<PERSON>mbo missing\",\n", " \n", "\n", "  count(DISTINCT  \n", "  case \n", "    when \n", "        (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "        and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%mdnd%%'))\n", "        or cc.disposition = 'MDND'\n", " then d.order_id end)  as \"mdnd\",\n", " \n", " \n", " \n", "--------QNG----------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%quality%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%quality%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%smell%%')\n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%taste%%')\n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%foreign%%matter%%')\n", "            or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Quality%%Not%%good%%'))\n", "            or cc.disposition='Quality Issues'\n", "        then d.order_id end\n", "    )  as \"QNG\",\n", "    \n", "\n", "\n", "\n", "-----------Item damaged-------------\n", "count(\n", "        DISTINCT  \n", "            case \n", "                when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%damage%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%damage%%')  \n", "                or cc.disposition='Item Damaged'\n", "            then d.order_id end\n", "    )  as \"Item Damaged\",\n", "\n", "\n", "\n", "\n", "\n", "----------_Wrong Item/product------------------\n", "count(DISTINCT  \n", "    case \n", "        when \n", "            coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%item%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%product%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%different%%item%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%different%%product%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%item%%different%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%wrong%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%item%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%different%%product%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%item%%different%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%product%%different%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%size%%')\n", "            or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Wrong%%Item%%'))\n", "            or cc.disposition = 'Wrong Item'\n", "        then \n", "            d.order_id \n", "    end)  as \"Wrong Item\",\n", "\n", "\n", "\n", "---------Expiry----------------\n", "count(DISTINCT  \n", "        case \n", "            when \n", "                coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%expired%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%') \n", "                or  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%near%%expiry%%') \n", "                or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%Item%%Expired%%'))\n", "                or cc.disposition ='Item Expired'\n", "            then d.order_id \n", "        end\n", "    )  as \"Expiry\",\n", "\n", "\n", "\n", "              \n", "count(DISTINCT  \n", "    case \n", "        when \n", "            coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%expired%%') \n", "            or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%expired%%')  \n", "        then d.order_id \n", "        end\n", "    )  as \"Expired Product\",\n", "    \n", "count(DISTINCT  case when  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%near%%expiry%%')  then d.order_id end)  as \"Near Expiry Product\",\n", "\n", "\n", "\n", "\n", "----------_Delay In delivery--------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when  \n", "                (\n", "                    coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                    and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%delay%%del%%')\n", "                ) \n", "                or cc.disposition ilike 'Delay In Delivery'\n", "            then d.order_id end\n", "        )  as \"Delay In Delivery\",\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "------FE/GSP behaviour-----------------\n", "count(\n", "    DISTINCT  \n", "        case \n", "            when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "            and <PERSON><PERSON>COMPLAINT_SUB_CATEGORY ILIKE ('%%fe%%relate%%')  \n", "            or cc.disposition='FE Related'\n", "            then d.order_id \n", "        end\n", "    )  as \"FE/GSP behaviour\",\n", "\n", "\n", "\n", "\n", "               \n", "              -------Content-----------------\n", "count(DISTINCT \n", "    case \n", "        when \n", "            coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%description%%') \n", "            or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%diff%%')\n", "            or (\n", "                coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                and C.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')\n", "            ) \n", "            or cc.disposition='Price Difference'\n", "        then d.order_id end\n", "        )  as \"Content Related\",\n", "\n", " count(DISTINCT CAse when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%product%%description%%') \n", "                 then d.order_id end)  as \"Incorrect Description\",\n", " count(\n", "        DISTINCT  \n", "            case \n", "                when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%diff%%')\n", "                or ( coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "                and <PERSON>.COMPLAINT_SUB_CATEGORY ilike ('%%price%%diff%%')) \n", "                or cc.disposition='Price Difference'\n", "                then d.order_id \n", "            end\n", "    )  as \"Price Difference\",\n", "\n", "\n", "------------EDLP--------------------\n", "count(DISTINCT  case when coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%EDLP%%') \n", "              or  coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%price%%guarantee%%')  then d.order_id end)  as \"EDLP\",\n", "\n", "\n", "\n", "\n", "------------Others----------------\n", "count(DISTINCT  case when (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%other%%') \n", "and  <PERSON><PERSON>COMPLAINT_SUB_CATEGORY in ('Null',''))\n", "or (coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%erased%%batch%%')) then d.order_id end)  as \"Others\",\n", "\n", "\n", "\n", "\n", "--------NLR-------------------------\n", "count(\n", "        DISTINCT  \n", "        case \n", "            when \n", "                coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%no%%longer%%') \n", "                or C.COMPLAINT_SUB_CATEGORY ILIKE ('%%not%%required%%') \n", "   --             or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%wrong%%size%%')\n", "                or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%change%%mind%%')\n", "                or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%bought%%mistake%%')\n", "                or coalesce(cd.valid_complaint_type,C.COMPLAINT_CATEGORY) ILIKE ('%%no%%longer%%')\n", "                or cc.disposition='No Longer Required'\n", "        then d.order_id \n", "        end\n", "    )  as \"<PERSON> Longer Required\",\n", "\n", "count(DISTINCT CASE\n", "                  WHEN \n", "                    complaint_type = 'VALID'\n", "                    AND (\n", "                        lower(order_type) ILIKE 'retail%%' \n", "                        or cc.disposition ='Service No'\n", "                    )\n", "                    THEN crdc.cart_id\n", "              END) AS service_no_complaints\n", "    \n", "from \n", "    delivered \n", "        AS d\n", "left join \n", "    metrics.complaints\n", "        AS c \n", "        on d.order_id = c.order_id \n", "        and d.cart_id = c.cart_id \n", "        and d.product_id=c.product_id\n", "        and c.STATUS in ('Procured','Partially Procured')\n", "        and c.COMPLAINT_CATEGORY not in ('INCORRECT_REFUND','REFUND_MISSING','AMOUNT_DEBITED_TWICE')\n", "LEFT JOIN \n", "    abusive_list \n", "        AS al \n", "        ON c.customer_id=al.customer_id\n", "left join \n", "    null_category_duplicates \n", "        AS cd \n", "        on  c.cart_id=cd.cart_id \n", "        and c.product_id=cd.product_id \n", "        and c.delivery_slot=cd.delivery_slot\n", "left join \n", "    metrics.crm_refund_denied_carts \n", "        AS crdc \n", "        on d.cart_id=crdc.cart_id \n", "        and  d.order_id = crdc.order_id \n", "        and d.product_id=crdc.product_id \n", "left join \n", "    compalint_calls \n", "        AS cc \n", "        on cc.join_key=d.order_id\n", "WHERE\n", "    al.customer_id IS NULL\n", "   -- AND d.<PERSON>_CATEGORY in ('Cold')\n", "group by 1,2,3,4,5\n", "order by 1,2,3,4\n", "\"\"\"\n", "\n", "df = pd.read_sql(comp, pb.get_connection(\"[Warehouse] Redshift\"))\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2c06aa90-7660-4531-9074-fa42a3bc65fd", "metadata": {}, "outputs": [], "source": ["df[\"comp_perc\"] = df[\"all_complaints\"] / df[\"delivered_orders\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1fb575cc-735e-4667-965d-d63f67013310", "metadata": {}, "outputs": [], "source": ["instore_time = \"\"\"with \n", "base_cte as (\n", "SELECT distinct \n", "    order_checkout_ts_ist::date as checkout_date,\n", "    f.outlet_id,\n", "    c.name as outlet_name,\n", "    (case when upper(trim(c.location)) in ('GHAZIABAD','NOIDA') then 'UP-NCR'\n", "      when upper(trim(c.location)) = 'BANGALORE' then 'BENGALURU'\n", "      ELSE upper(trim(c.location)) end) as city,\n", "    f.order_id,\n", "    f.is_order_delivered,\n", "    f.eta_computed_mins,\n", "    datediff(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist)::float/60 as checkout_to_picker_assigned,\n", "    datediff(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist)::float/60 as picker_assigned_to_picking_start,\n", "    datediff(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist)::float/60 as picking_started_to_picking_completed,\n", "    datediff(seconds,order_picking_completed_ts_ist,order_billing_completed_ts_ist)::float/60 as picking_completed_to_billing_completed,\n", "    datediff(seconds,order_billing_completed_ts_ist,order_partner_assigned_ts_ist)::float/60 as billing_completed_to_partner_assigned,\n", "    datediff(seconds,order_partner_assigned_ts_ist,order_enroute_ts_ist)::float/60 as partner_assigned_to_enroute,\n", "    datediff(seconds,order_enroute_ts_ist,order_reached_doorstep_ts_ist)::float/60 as enroute_to_doorstep,\n", "    datediff(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist)::float/60 as instore_time,\n", "    datediff(seconds,order_checkout_ts_ist,order_delivered_ts_ist)::float/60 as checkout_to_delivered,\n", "    datediff(seconds,order_billing_completed_ts_ist,order_delivered_ts_ist)::float/60 as outstore_time,\n", "    datediff(seconds,order_checkout_ts_ist,order_reached_doorstep_ts_ist)::float/60 as checkout_to_doorstep,\n", "    f.total_selling_price as GMV\n", "FROM \n", "    dwh.fact_supply_chain_order_details f\n", "inner join dwh.fact_sales_order_details b on date(b.order_create_ts_ist) = date(order_checkout_ts_ist)\n", "and f.outlet_id = b.outlet_id and f.order_id = b.order_id\n", "inner join lake_retail.console_outlet c on f.outlet_id = c.id\n", "where \n", "is_rescheduled=false \n", "and \n", "b.order_type in ('RetailForwardOrder','DropShippingForwardOrder') and\n", "---order_picking_completed_ts_ist is not null and \n", "f.order_type='super_express' and\n", "     order_checkout_ts_ist::date >= current_date - 28\n", "     and order_create_ts_ist::date >= current_date - 28\n", "),\n", "\n", "checkouts as (\n", "(select city,\n", "    outlet_id,\n", "    outlet_name,\n", "    checkout_date,\n", "    count(distinct order_id) as orders,\n", "    count(distinct case when is_order_delivered=true then order_id end) as delivered_orders,\n", "    count(distinct case when instore_time <= 2.5 then order_id else null end) as \"within_2_point_5\",    \n", "    count(distinct case when instore_time <= 3 then order_id else null end) as \"within_3\",\n", "    count(distinct case when instore_time <= 3.5 then order_id else null end) as \"within_3_half\",\n", "    count(distinct case when checkout_to_doorstep <= 15 then order_id else null end) as \"within_15\",\n", "    sum(GMV) as GMV\n", "from base_cte\n", "group by 1,2,3,4)\n", "),\n", "\n", "percentiles as (\n", "(select distinct city,\n", "    outlet_id,\n", "    outlet_name,\n", "    checkout_date,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by eta_computed_mins) over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as computed_eta_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by checkout_to_delivered) over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as checkout_to_delivered_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by checkout_to_picker_assigned) over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as checkout_to_picker_assigned_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by picker_assigned_to_picking_start)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as picker_assigned_to_picking_start_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by picking_started_to_picking_completed)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as picking_started_to_picking_completed_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by picking_completed_to_billing_completed)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as picking_completed_to_billing_completed_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by instore_time)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as instore_time_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by outstore_time)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as outstore_time_75,\n", "    PERCENTILE_CONT(0.75) WITHIN GROUP (order by billing_completed_to_partner_assigned)  over(PARTITION BY city,outlet_id,outlet_name,checkout_date) as billing_completed_to_partner_assigned_75\n", "\n", "    \n", "from base_cte\n", ")\n", ")\n", "\n", "-- select * from percentiles\n", "\n", "select c.city,\n", "c.outlet_id,\n", "c.outlet_name,\n", "c.checkout_date,\n", "orders,\n", "delivered_orders,\n", "checkout_to_delivered_75,\n", "instore_time_75,\n", "billing_completed_to_partner_assigned_75,\n", "\"within_3\",\n", "\"within_2_point_5\",\n", "\"within_15\",\"within_3_half\",\n", "GMV\n", "from checkouts c \n", "inner join percentiles p on c.city = p.city and c.outlet_id = p.outlet_id\n", "    and c.checkout_date = p.checkout_date\n", "order by 1,4,5,6 desc\"\"\"\n", "\n", "df_instore1 = pd.read_sql(instore_time, pb.get_connection(\"[Warehouse] Redshift\"))\n", "df_instore1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0ad90689-dffc-4217-93bc-285f197c0c32", "metadata": {}, "outputs": [], "source": ["df_instore1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "a98f0fd8-9294-4a67-ae46-20e2c7032aa0", "metadata": {}, "outputs": [], "source": ["df_instore1_new = df_instore1.merge(store_data, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "61ae92e5-223a-41a9-8e44-fa928fd19b59", "metadata": {}, "outputs": [], "source": ["df_instore = df_instore1_new.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4510e7f5-2e84-48a7-9142-fa1dc3eb8f96", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a46d76f2-4839-4037-91d7-a6b07b85a019", "metadata": {}, "outputs": [], "source": ["df_merged = df_instore.merge(\n", "    df,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"checkout_date\"],\n", "    right_on=[\"outlet_id\", \"delivery_date\"],\n", ")\n", "df_merged.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f2e89c4c-c33b-4191-9ac4-cd034f00d398", "metadata": {}, "outputs": [], "source": ["fill_rate = \"\"\"with base_cte as (SELECT DISTINCT i.outlet,\n", "                (o.update_ts + interval '5.5 hours')::date as delivery_date,\n", "                        o.id as order_id,\n", "                        sum(required_quantity) as required_quantity_,\n", "                        sum(picked_quantity) as picked_quantity_\n", "    from lake_oms_bifrost.oms_order o\n", "    inner join lake_ims.ims_order_details i on o.id = i.ancestor\n", "        and (o.update_ts + interval '5.5 hours')::date >= current_date - 28\n", "        and o.current_status = 'DELIVERED'\n", "      INNER JOIN lake_retail.console_outlet co ON co.id = i.outlet\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list_order_mapping plo ON plo.order_id = i.order_id\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list pl ON pl.id = plo.pick_list_id\n", "      LEFT JOIN lake_warehouse_location.warehouse_pick_list_item PLI ON pli.pick_list_id = pl.id\n", "group by 1,2,3)\n", "\n", "select outlet as outlet_id, delivery_date,count(distinct order_id) as orders,\n", "count(distinct case when picked_quantity_ < required_quantity_ then order_id else null end) as unfulfilled_orders\n", "from base_cte\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f5411814-cf09-4177-aafe-db69b8458975", "metadata": {}, "outputs": [], "source": ["df_fill_rate = pd.read_sql(fill_rate, pb.get_connection(\"[Warehouse] Redshift\"))\n", "df_fill_rate.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0aa070c5-ae2f-40aa-a628-436e8a95c8ed", "metadata": {}, "outputs": [], "source": ["new_store_ops_q = \"\"\"\n", "WITH DATA AS\n", "  (SELECT date(created_date+interval '5.5 hours') AS date_,\n", "          cast(site_id AS int) AS site_id,\n", "          processed_qty,\n", "          expected_count,\n", "          entity_id as suborder\n", "   FROM lake_storeops.activity\n", "   WHERE TYPE = 'PICKING' -- and site_id = '3377'\n", "AND status='COMPLETED' and (date(created_date+interval '5.5 hours')>=current_date-28))\n", "\n", "SELECT date_,\n", "       site_id,\n", "       c.name,\n", "       count(distinct case when processed_qty <> expected_count then suborder end) as unfullfilled_orders,\n", "       count(distinct suborder) as total_orders,\n", "       count(distinct case when processed_qty <> expected_count then suborder end)*100.00/count(distinct suborder) AS percentage\n", "FROM DATA d\n", "LEFT JOIN lake_retail.console_outlet c -- group by 1,2,3,4\n", "ON d.site_id = c.id\n", "WHERE site_id IN (2396,\n", "                  1874)\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1 DESC\n", "\n", "\n", "\"\"\"\n", "\n", "new_df_fill_rate = pd.read_sql(\n", "    new_store_ops_q, pb.get_connection(\"[Warehouse] Redshift\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ddb547c5-5243-4577-aaf4-cace30125410", "metadata": {}, "outputs": [], "source": ["new_df_fill_rate.head()"]}, {"cell_type": "code", "execution_count": null, "id": "86d5c8ea-cb1a-4360-932d-6fca61aaafa4", "metadata": {}, "outputs": [], "source": ["new_df_fill_rate = new_df_fill_rate.rename(\n", "    columns={\"unfullfilled_orders\": \"unfullfilled_orders_new\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "72d25eaa-457e-4cd1-aae6-4a2b30a2e214", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp = df_fill_rate.merge(\n", "    new_df_fill_rate[[\"site_id\", \"date_\", \"unfullfilled_orders_new\"]],\n", "    left_on=[\"outlet_id\", \"delivery_date\"],\n", "    right_on=[\"site_id\", \"date_\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a189207-b7ff-4ee9-976d-7c7d2bee06e0", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "07bfd424-0342-48e5-9eac-a4aee53d7c4a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "657aa8ae-a4a8-46c9-8d77-2f6e539fc744", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp[\"unfulfilled_orders\"] = np.where(\n", "    df_fill_rate_temp[\"unfulfilled_orders\"] == df_fill_rate_temp[\"orders\"],\n", "    df_fill_rate_temp[\"unfullfilled_orders_new\"],\n", "    df_fill_rate_temp[\"unfulfilled_orders\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cce5dba3-41f6-46cf-b978-9d7cf69e6d5c", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6769fd74-e2fe-4667-9285-564f33aa0911", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c041738a-3cbf-49f1-81d8-f97be4a71bfe", "metadata": {}, "outputs": [], "source": ["df_fill_rate_temp = df_fill_rate_temp[\n", "    [\"outlet_id\", \"delivery_date\", \"orders\", \"unfulfilled_orders\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "78dc7ef6-eb77-4d3c-b784-c4bcc2223689", "metadata": {}, "outputs": [], "source": ["df_merged.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c6dabaf8-a96f-480f-9272-5b3cbc75eebc", "metadata": {}, "outputs": [], "source": ["df_merged = df_merged[\n", "    [\n", "        \"city_x\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"checkout_date\",\n", "        \"orders\",\n", "        \"checkout_to_delivered_75\",\n", "        \"instore_time_75\",\n", "        \"billing_completed_to_partner_assigned_75\",\n", "        \"within_3\",\n", "        \"within_2_point_5\",\n", "        \"within_15\",\n", "        \"within_3_half\",\n", "        \"gmv\",\n", "        \"ownership\",\n", "        \"all_complaints\",\n", "        \"comp_perc\",\n", "        \"delivered_orders_x\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "db56920d-39b4-4897-b330-0b6011343c29", "metadata": {}, "outputs": [], "source": ["df_merged_new = df_merged.merge(\n", "    df_fill_rate_temp,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"checkout_date\"],\n", "    right_on=[\"outlet_id\", \"delivery_date\"],\n", ")\n", "df_merged_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "be6ce378-ba68-4026-bd0d-187952c2d7f9", "metadata": {}, "outputs": [], "source": ["df_merged_new[\"weeknum\"] = df_merged_new[\"checkout_date\"].apply(\n", "    lambda x: pd.to_datetime(x).isocalendar()[1]\n", ")\n", "df_merged_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26a456c9-849c-4813-b456-cc94a7db7946", "metadata": {}, "outputs": [], "source": ["df_merged_new.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b2c1f95a-8d18-4985-9389-26d86f108840", "metadata": {}, "outputs": [], "source": ["df_merged_new[\"within_3_perc\"] = df_merged_new[\"within_3\"] / df_merged_new[\"orders_x\"]"]}, {"cell_type": "code", "execution_count": null, "id": "dce72108-c50d-4c50-8931-6aafba6da0ae", "metadata": {}, "outputs": [], "source": ["df_merged_new[\"unfulfilled_perc\"] = (\n", "    df_merged_new[\"unfulfilled_orders\"] / df_merged_new[\"orders_x\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ec566e3-1c15-43e3-ba9c-e5ce706cfe46", "metadata": {}, "outputs": [], "source": ["wastage_q = \"\"\"\n", "select \n", "a.snapshot_date_ist::date as date_,\n", "a.outlet_id,\n", "co.name as outlet_name,\n", "(sum(case when l0_category in ('Dairy, Bread & Eggs','Chicken, Meat & Fish') then total_diff_loss_value else 0 end)-sum(case when l0_category in ('Dairy, Bread & Eggs','Chicken, Meat & Fish') then esto_transfer_loss_diff else 0 end)) AS perishable_wastage,\n", "(sum(case when l0_category in ('Vegetables & Fruits') then total_diff_loss_value else 0 end) - sum(case when l0_category in ('Vegetables & Fruits') then esto_transfer_loss_diff else 0 end)) AS fnv_wastage,\n", "(sum(case when l0_category not in ('Vegetables & Fruits','Dairy, Bread & Eggs','Chicken, Meat & Fish') then total_diff_loss_value else 0 end)-sum(case when l0_category not in ('Vegetables & Fruits','Dairy, Bread & Eggs','Chicken, Meat & Fish') then esto_transfer_loss_diff else 0 end)) AS pack_wastage\n", " \n", "from dwh.view_agg_daily_category_diff_loss a\n", "left join lake_retail.console_outlet co on co.id = a.outlet_id and co.active = 1\n", "where a.snapshot_date_ist >= current_date::timestamp - interval '28 days'\n", "and a.snapshot_date_ist < current_date::timestamp\n", "group by 1,2,3\n", "order by 1,2\n", "\n", "\n", "\"\"\"\n", "\n", "\n", "wastage_data = pd.read_sql(wastage_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "4be11f9b-79ec-4f79-9b6b-9bd2c3184fb5", "metadata": {}, "outputs": [], "source": ["wastage_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "87d38d7c-6d6a-406a-826a-dd16ef188bed", "metadata": {}, "outputs": [], "source": ["df_final_data = df_merged_new.merge(\n", "    wastage_data,\n", "    left_on=[\"outlet_id\", \"checkout_date\"],\n", "    right_on=[\"outlet_id\", \"date_\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c6c5a9b-bab5-45c9-a4a4-9195eb2b7c7a", "metadata": {}, "outputs": [], "source": ["df_final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d21dda6a-982d-45ba-9a3b-18fee21bbc1b", "metadata": {}, "outputs": [], "source": ["df_final_data[\"ownership\"] = df_final_data[\"ownership\"].fillna(\"OWN\")"]}, {"cell_type": "code", "execution_count": null, "id": "cfd97154-bba9-4d9a-9104-462325b96ab2", "metadata": {}, "outputs": [], "source": ["df_final_data[\"week_start\"] = df_final_data[\"checkout_date\"].apply(\n", "    lambda x: x - timedelta(days=x.weekday())\n", ")\n", "\n", "df_final_data[\"week_end\"] = df_final_data[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eb1184a6-a937-4cfa-96ae-914c4b7d9138", "metadata": {}, "outputs": [], "source": ["df_final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a06116e9-aa1e-49a6-9ad1-100b4354acf9", "metadata": {}, "outputs": [], "source": ["df_final_data[\"week\"] = df_final_data.apply(\n", "    lambda x: x[\"week_start\"].strftime(\"%d %b\")\n", "    + \" - \"\n", "    + x[\"week_end\"].strftime(\"%d %b\"),\n", "    axis=1,\n", ")\n", "\n", "\n", "# (\n", "#     df_final_data[\"week_start\"].astype(str)\n", "#     + \" to \"\n", "#     + df_final_data[\"week_end\"].astype(str)\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "d2cb5b7f-11e0-4cc1-af8e-24ce9bdc2d2b", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_final_data[\n", "        [\n", "            \"city_x\",\n", "            \"outlet_id\",\n", "            \"outlet_name_x\",\n", "            \"ownership\",\n", "            \"checkout_date\",\n", "            \"orders_x\",\n", "            \"within_3\",\n", "            \"within_15\",\n", "            \"all_complaints\",\n", "            \"comp_perc\",\n", "            \"within_3_perc\",\n", "            \"unfulfilled_orders\",\n", "            \"unfulfilled_perc\",\n", "            \"weeknum\",\n", "            \"within_3_half\",\n", "            \"within_2_point_5\",\n", "            \"gmv\",\n", "            \"perishable_wastage\",\n", "            \"fnv_wastage\",\n", "            \"pack_wastage\",\n", "            \"delivered_orders_x\",\n", "            \"week\",\n", "        ]\n", "    ],\n", "    \"18ihuGHuU2itoM7VWXRt93JSTUqPY-gimOtw7b-dlNrI\",\n", "    \"raw\",\n", "    clear_cache=True,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}