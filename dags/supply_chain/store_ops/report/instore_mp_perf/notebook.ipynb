{"cells": [{"cell_type": "code", "execution_count": null, "id": "61c5fbd3-509e-4bf3-a1ed-ea081add7c85", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "bec149d6-c991-4a68-8f50-67279a5ae916", "metadata": {}, "outputs": [], "source": ["presto_con = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "fcbeb785-57ad-4cdc-a5d6-8a90a8214a79", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "afa9599b-792b-4f60-9bfc-c3cf23c35f3a", "metadata": {}, "outputs": [], "source": ["# get partner stores data\n", "sheet_id = \"19U2dE8shA5Ibh3Oar7sW_9A4sMTVJTGV-J9bpppvlPs\"\n", "sheet_name = \"Sheet24\"\n", "df = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "\n", "df[\"ownership\"] = \"PARTNER\"\n", "store_data = df[[\"outlet_id\", \"ownership\"]]\n", "store_data[\"outlet_id\"] = store_data[\"outlet_id\"].apply(\n", "    lambda x: x if len(x) in (3, 4) else \"0\"\n", ")\n", "store_data[\"outlet_id\"] = store_data[\"outlet_id\"].astype(\"int64\")\n", "store_data.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b25d375b-c75f-4eda-971e-00aae8ce204e", "metadata": {}, "outputs": [], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": null, "id": "0f928901-a37b-45a5-90e8-ff43d2be054e", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/instore_mp_perf\""]}, {"cell_type": "code", "execution_count": null, "id": "23ba10e0-d7a9-47d2-8f60-fe8f40116aea", "metadata": {}, "outputs": [], "source": ["# base data\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"Utlisation.sql\",\n", "    ),\n", "    \"r\",\n", ") as myfile:\n", "    sql_query = myfile.read()"]}, {"cell_type": "code", "execution_count": null, "id": "55708ccb-8ee8-438c-920d-ce7cec06a274", "metadata": {}, "outputs": [], "source": ["# s_date = (datetime.now() - timedelta(days=5)).strftime(\"%Y-%m-%d\")\n", "# e_date = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "s_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "e_date = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "print(\"s_date:\", s_date)\n", "print(\"e_date:\", e_date)\n", "sql_query = sql_query.format(Start_Date=s_date, End_Date=e_date)\n", "t_data = pd.read_sql(sql=sql_query, con=presto_con)"]}, {"cell_type": "code", "execution_count": null, "id": "915bf53b-3edb-4692-bf80-f6e749233d92", "metadata": {}, "outputs": [], "source": ["t_data_f = t_data.merge(\n", "    store_data, left_on=[\"Store_ID\"], right_on=[\"outlet_id\"], how=\"left\"\n", ")\n", "t_data_f[\"ownership\"].fillna(\"OWN\", inplace=True)\n", "t_data_f[\"Score\"] = t_data_f.Score.round(decimals=0)\n", "t_data_f.drop(columns={\"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fb905094-a65b-42d0-9379-591b46377446", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"instore_manpower_performance\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"Date\", \"type\": \"timestamp\", \"description\": \"Picker performance Date\"},\n", "        {\"name\": \"Store_ID\", \"type\": \"int\", \"description\": \"Outlet Id \"},\n", "        {\"name\": \"Store_Name\", \"type\": \"varchar\", \"description\": \"Outlet Name \"},\n", "        {\"name\": \"City\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\"name\": \"Employee_ID\", \"type\": \"varchar\", \"description\": \"Picker Employee ID\"},\n", "        {\"name\": \"Employee_Name\", \"type\": \"varchar\", \"description\": \"Picker Name\"},\n", "        {\n", "            \"name\": \"role\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Employee Role - Picker/putter/F&V Executive/SM\",\n", "        },\n", "        {\n", "            \"name\": \"Total_Putaway_Quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Quantity putaway completed\",\n", "        },\n", "        {\n", "            \"name\": \"Total_Picked_Quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Picked Quantity\",\n", "        },\n", "        {\n", "            \"name\": \"Score\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Picked Quantity *2 + Putaway Quantity\",\n", "        },\n", "        {\"name\": \"Total_orders_Picked\", \"type\": \"int\", \"description\": \"Orders pciked\"},\n", "        {\n", "            \"name\": \"Break_Time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Break taken by partner in mins\",\n", "        },\n", "        {\n", "            \"name\": \"Picking_Logged_in_Time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"<PERSON><PERSON> the picker was logged in as picker in the app\",\n", "        },\n", "        {\n", "            \"name\": \"Putaway_Logged_in_Time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"<PERSON><PERSON> the picker was logged in as putaway in the app\",\n", "        },\n", "        {\"name\": \"login\", \"type\": \"timestamp\", \"description\": \"First login of the day\"},\n", "        {\n", "            \"name\": \"logout\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Last logout of the day\",\n", "        },\n", "        {\n", "            \"name\": \"<PERSON><PERSON><PERSON>_On_Time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Working time based on roster in mins\",\n", "        },\n", "        {\n", "            \"name\": \"Total_Login_Time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Total of roster working mins and put away login time\",\n", "        },\n", "        {\n", "            \"name\": \"Wait_Time_In_queue\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Waiting time in queue\",\n", "        },\n", "        {\n", "            \"name\": \"Attendance_Punched\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Has the partner marked attendence\",\n", "        },\n", "        {\n", "            \"name\": \"Shift_Type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Login before 1 then first shift else second\",\n", "        },\n", "        {\n", "            \"name\": \"total_app_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Total time he was logged in to the app\",\n", "        },\n", "        {\n", "            \"name\": \"picking_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"avg picking time per quantity\",\n", "        },\n", "        {\n", "            \"name\": \"putaway_time\",\n", "            \"type\": \"float\",\n", "            \"description\": \"avg putaway time per quantity\",\n", "        },\n", "        {\"name\": \"ownership\", \"type\": \"varchar\", \"description\": \"PARTNER / OWN\"},\n", "    ],\n", "    \"primary_key\": [\"Date\", \"Store_ID\", \"Employee_ID\"],\n", "    \"sortkey\": [\"Date\"],\n", "    \"incremental_key\": \"Date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Instore Manpower performance\",\n", "}\n", "\n", "pb.to_redshift(t_data_f, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "149f2d1a-2455-4b11-a870-8699108b453f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}