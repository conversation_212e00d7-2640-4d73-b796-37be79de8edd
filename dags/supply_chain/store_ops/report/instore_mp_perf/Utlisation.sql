
with 


fc_mapping AS 
(
    SELECT 
        PCO.FACILITY_ID,
        CF.NAME AS FACILITY_NAME,
        BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,
        --BOM.NAME AS BACKEND_MERCHANT_NAME,
        PCO.ID AS OUTLET_ID,
        PCO.NAME AS OUTLET_NAME
    FROM lake_view_oms_bifrost.oms_merchant AS BOM
    INNER join  lake_retail.console_outlet_cms_store  AS CMS
        ON BOM.EXTERNAL_ID = CMS.CMS_STORE 
        AND CMS.ACTIVE = 1 
        AND CMS.CMS_UPDATE_ACTIVE = 1
        AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'
    INNER join  lake_retail.console_outlet  AS PCO
        ON CMS.OUTLET_ID = PCO.ID
    INNER join  lake_crates.facility  AS CF 
        ON CF.ID = PCO.FACILITY_ID
    where bom.insert_ds_ist >= cast(cast('{Start_Date}' as date)  - INTERVAL '3' DAY as varchar)
    GROUP BY 1,2,3,4,5
),


--
-- Putlist level , start time , end time 
--

    putaway_base AS (
    
    SELECT 
        date(put_list.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) AS date_ts,
        
        l.name AS city,
        outlet.id AS outlet_id ,
        outlet.name AS outlet_name,
        
        u.emp_id, 
        u.name AS employee_name,
        
        put_list.id as putlist_id, 
        
        (put_list.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) as putaway_start_ist,
        (case when put_list.current_state_id=4 then put_list.updated_at end) as putaway_end_ist,
        
        DATE_DIFF('second', put_list.created_at,put_list.updated_at )as putaway_time_sec,
                
        SUM(placed_quantity) as qty
    FROM 
        lake_view_warehouse_location.warehouse_put_list 
            AS put_list
            
    INNER JOIN 
        lake_retail.console_outlet 
            AS outlet 
            ON outlet.id=put_list.outlet_id
            AND put_list.current_state_id=4
    INNER JOIN 
        lake_retail.console_location 
            AS l 
            ON l.id=outlet.tax_location_id
            
    INNER JOIN 
        lake_view_warehouse_location.warehouse_put_list_item 
        AS put_list_item 
        ON put_list_item.putlist_id=put_list.id
        
    INNER JOIN
        lake_retail.warehouse_user 
            AS u 
            ON u.emp_id = put_list.assigned_to_name
    WHERE
        outlet.business_type_id = 7   -- filter for darkstores 
        --AND put_list.creation_type in (10)  -- 
        AND (put_list.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND (put_list.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY
        
        AND ((put_list_item.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND (put_list_item.created_at + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY)
        AND emp_id is NOT NULL
        and put_list_item.item_id not in (10075658,10035845,10035969,10110995,10035970,10047169,10110837,10111697,10035850,10111048,10110807,10109660,10035961,10035849,10109659,10044650,10092383,10109877,10035965,10108347,10110838,10111748,10108764,10044649,10108346,10108350,10108351,10109872,10109999,10111431,10111662,10108340,10108763,10108341,10050585,10045268,10111432,10044651,10109874,10111749,10111070,10106605,10109876,10035963,10035968,10035974,10045038,10109659,10109660,10112346,10112686,10110995,10111048,10113887,10092383,10112736,10112737,10112738,10112741,10112740,10115649,10115650,10006748,10114279,10115796,10115976)
    GROUP BY 
        1,2,3,4,5,6,7,8,9,10
        
        
    
),


-- putaway_base1 as(
-- select 
-- date(created_date) as date_ts,
-- cast(site_id as int) as outlet_id,
-- b.name as outlet_name,
-- assigned_to_employee_id,
-- concat(assigned_to_first_name,' ',assigned_to_last_name) as employee_name,
-- l.name as city,
-- sum(date_diff('second',start_time,end_time)) as total_putaway_time_in_sec, 
-- sum(processed_qty) as qty
-- from lake_view_storeops.activity a
-- inner join lake_view_retail.console_outlet b on b.id =cast(a.site_id as int)
-- INNER JOIN 
--         lake_view_retail.console_location 
--             AS l 
--             ON l.id=b.tax_location_id
-- left join lake_retail.warehouse_user u on u.emp_id = a.assigned_to_employee_id
-- where type in ('PUTAWAY') and business_type_id=7
-- and status='COMPLETED' and date(a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
--          AND date(a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)
--     group by 1,2,3,4,5,6
-- -- and fulfillment_type is null
-- ),


---
-- Putaway aggregation
---

putaway_agg as (
select date_ts,
outlet_id, outlet_name,
emp_id,employee_name,city,
sum(total_putaway_time_in_sec) as total_putaway_time_in_sec,
sum(qty) as qty
    from
(    
select 
date(a.created_date+ INTERVAL '5' HOUR + INTERVAL '30' MINUTE) as date_ts,
b.id as outlet_id,
b.name as outlet_name,
assigned_to_employee_id as emp_id,
u.name as employee_name,
l.name as city,
item_id,
sum(date_diff('second',id.pick_start_time,picked_time)) as total_putaway_time_in_sec, 
sum(aa.processed_qty) as qty
from lake_view_storeops.activity a
inner join lake_view_storeops.item_activity aa on aa.activity_id = a.id
inner join lake_view_storeops.item_details id on id.item_activity_id = aa.id
inner join lake_view_retail.console_outlet b on b.id =cast(a.site_id as int)
INNER JOIN 
        lake_view_retail.console_location 
            AS l 
            ON l.id=b.tax_location_id
left join lake_view_retail.warehouse_user u on u.emp_id = a.assigned_to_employee_id
where type in ('PUTAWAY') and business_type_id=7
    and activity_type in ('PUTAWAY') and
    aa.item_id not in ('10075658','10035845','10035969','10110995','10035970','10047169','10110837','10111697','10035850','10111048','10110807','10109660','10035961','10035849','10109659','10044650','10092383','10109877','10035965','10108347','10110838','10111748','10108764','10044649','10108346','10108350','10108351','10109872','10109999','10111431','10111662','10108340','10108763','10108341','10050585','10045268','10111432','10044651','10109874','10111749','10111070','10106605','10109876','10035963','10035968','10035974','10045038','10109659','10109660','10112346','10112686','10110995','10111048','10113887','10092383','10112736','10112737','10112738','10112741','10112740','10115649','10115650','10006748','10114279','10115796','10115976','10117545','10116797','10117546','10117104','10117106','10117778','10117108','10117547','10117548','10117777','10117105','10117779')
and a.status='COMPLETED' and ((a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND (a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY) AND
    ((aa.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND (aa.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY) AND
    ((id.pick_start_time + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND (id.pick_start_time + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY)
group by 1,2,3,4,5,6,7)
    group by 1,2,3,4,5,6

union
    
    
SELECT
    date_ts,
    outlet_id,
    outlet_name,
    emp_id,
    employee_name,
    city,
    SUM(putaway_time_sec) as total_putaway_time_in_sec,
    SUM(qty) as qty
from putaway_base 
group by 1,2,3,4,5,6

),



--
-- Order level picking time 
--

-- picking_base AS (
    
--     SELECT 
    
--         date(from_unixtime(lt.install_ts/pow(10,6), 'Asia/Kolkata')) as date,
        
--         fc.outlet_id,
--         fc.outlet_name,
--         l.name as city,
        
--         lup.employee_id as picker_id,
--         u.name,
        
--         lt.shipment_label_value as order_id,
--         JSON_EXTRACT_SCALAR(lt.form_data, '$.procured_item_count') as qty,
        
--         from_unixtime(tlog.install_ts / 1000000 + 5.5 * 3600) as order_picking_started_ts_ist,
--         from_unixtime(tlogp.install_ts / 1000000 + 5.5 * 3600) as order_picking_completed_ts_ist,
        
--         CAST(DATE_DIFF('second', from_unixtime(tlog.install_ts/1000000),
--         from_unixtime(tlogp.install_ts/1000000)) AS decimal) as picking_time_in_mins
        
--     FROM 
--         lake_logistics.logistics_task
--             AS lt
--     INNER JOIN 
--         lake_logistics.logistics_task
--             AS lts
--             ON lts.parent_task_id=lt.id
--             AND lts.type='EXPRESS_SUB_ORDER_PROCUREMENT_TASK'
--     INNER JOIN 
--         lake_oms_bifrost.oms_order
--             AS oo
--             ON oo.id=cast(lt.shipment_label_value as int)
--     INNER JOIN 
--         lake_oms_bifrost.oms_suborder
--             AS os
--             ON os.id=cast(lts.shipment_label_value as int)
--     INNER JOIN 
--         lake_view_oms_bifrost.oms_merchant
--             AS fm
--             on fm.id=oo.merchant_id
--     INNER JOIN 
--         lake_view_oms_bifrost.oms_merchant
--             AS om
--             ON om.id=os.backend_merchant_id
--             AND om.virtual_merchant_type <> 'superstore_merchant'
--     LEFT JOIN 
--         FC_MAPPING
--             AS fc
--             ON om.external_id = fc.backend_merchant_id
--     LEFT JOIN 
--         lake_logistics.logistics_user_profile
--             AS lup
--             ON lup.id=lt.user_profile_id
--     LEFT JOIN 
--         lake_logistics.task_management_taskstatelog
--             AS tlog
--             ON tlog.task_id=lt.id
--             AND tlog.to_state='ASSIGNED_TO_PICKER'
--     LEFT JOIN 
--         lake_logistics.task_management_taskstatelog
--             AS tlogp
--             ON tlogp.task_id=lt.id
--             AND tlogp.to_state='COMPLETED'
--     INNER JOIN 
--         lake_retail.warehouse_user 
--             AS u 
--             ON u.emp_id = lup.employee_id
--     INNER JOIN 
--         lake_retail.console_outlet 
--             AS outlet 
--             ON outlet.id=fc.outlet_id
--     INNER JOIN 
--         lake_retail.console_location 
--             AS l 
--             ON l.id=outlet.tax_location_id

--     WHERE 
    
--        lt.type='EXPRESS_ORDER_PROCUREMENT_TASK'
--        AND lt.state='COMPLETED'
--        AND from_unixtime(lt.install_ts/ 1000000 + 5.5 * 3600) >=  cast('{Start_Date}' as date) 
--        AND from_unixtime(lt.install_ts/ 1000000 + 5.5 * 3600) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY
       
--        AND oo.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY  AS VARCHAR)
--        AND lt.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY  AS VARCHAR)
--        AND lts.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)
--        AND os.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)
--        AND om.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)
--        AND fm.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)
--        AND tlog.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)
--        AND tlogp.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)

--        -- AND os.backend_merchant_id<>30787 -- 30 min store 
       
--        GROUP BY 1,2,3,4,5,6,7,8,9,10,11
-- ),

--
-- Date and outlet level orders picked and picking time 
--


picking_agg as (

-- select date as date_ts,
--        outlet_id,
--        outlet_name,
--        picker_id as emp_id,
--        name as employee_name,
--        city,
--        SUM(picking_time_in_mins) as total_picking_time_in_mins,
--        SUM(CAST(qty AS INT)) as qty,
--        COUNT(order_id) as number_of_orders_picked
-- from picking_base 
--      group by 1,2,3,4,5,6

-- union
    
select 
date(created_date) as date_ts,
b.id as outlet_id,
b.name as outlet_name,
assigned_to_employee_id as emp_id,
u.first_name as employee_name,
l.name as city,
sum(date_diff('second',start_time,end_time)) as total_picking_time_in_mins, 
sum(processed_qty) as qty,
count(distinct entity_id) as number_of_orders_picked
from lake_view_storeops.activity a
inner join lake_retail.console_outlet b on b.id =cast(a.site_id as int)
INNER JOIN 
        lake_retail.console_location 
            AS l 
            ON l.id=b.tax_location_id
left join lake_view_storeops.user_details u on u.employee_id = a.assigned_to_employee_id
where type in ('PICKING') and business_type_id=7
and status='COMPLETED' and date(a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) >= cast('{Start_Date}' as date)
         AND date(a.created_date + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) <=  cast('{End_Date}' as date)
group by 1,2,3,4,5,6

),


--
-- Get a base on all employee's 
--

emp_agg AS (

            SELECT 
                emp_id,
                employee_name,
                date_ts,
                outlet_id,
                outlet_name,
                city 
            FROM 
                putaway_agg
            GROUP BY 1,2,3,4,5,6
            
         UNION all
         
            SELECT 
                emp_id,
                employee_name,
                date_ts,
                outlet_id,
                outlet_name,
                city 
            FROM 
                picking_agg
            GROUP BY 1,2,3,4,5,6
),
         
emp_base AS (

    SELECT 
        emp_id,
        employee_name,
        date_ts,
        outlet_id,
        outlet_name,
        MAX(city) as city
    FROM
        emp_agg
        GROUP BY 1,2,3,4,5
),

--
-- App Login and logout times raw base
--

app_login_time AS (

SELECT 
    DATE(from_unixtime(pos_timestamp/ 1000000 + 5.5 * 3600)) AS date,
    outlet_id,
    user_name,
    from_unixtime(pos_timestamp/ 1000000 + 5.5 * 3600) AS ts_ist,
    action,
    role,
    
    --- people get force logged out after 1 hence using the UTC timestamp here for date partition
    LEAD(from_unixtime(pos_timestamp/ 1000000 + 5.5 * 3600)) OVER (partition BY DATE(from_unixtime(pos_timestamp/ 1000000)),user_name,outlet_id ORDER BY pos_timestamp) AS next_action_ts_ist,
        
    DATE_DIFF('minute', from_unixtime(pos_timestamp/ 1000000),LEAD(from_unixtime(pos_timestamp/ 1000000)) OVER (partition BY DATE(from_unixtime(pos_timestamp/ 1000000)),user_name,outlet_id ORDER BY pos_timestamp) )AS time
    
FROM 
    lake_warehouse_location.warehouse_device_user_log 
        AS login
WHERE 
    from_unixtime(pos_timestamp/ 1000000 + 5.5 * 3600) >=  cast('{Start_Date}' as date) 
    AND from_unixtime(pos_timestamp/ 1000000 + 5.5 * 3600) <  cast('{End_Date}' as date) + INTERVAL '1' DAY
    AND action in ('LOGIN','LOGOUT','FORCE_LOGOUT') 
    and login.insert_ds_ist >= cast( cast('{Start_Date}' as date)  - INTERVAL '3' DAY AS VARCHAR)

    
    -- FORCE_LOGOUT done from GPOS, if the user is not able to find device where he was already logged
    AND outlet_id <> 0 
ORDER BY 4
),

app_login_time_new as
(select DATE(ts_ist) as date, outlet_id, user_name,role,
ts_ist,'LOGIN' as action,
LEAD(ts_ist) OVER (partition BY DATE(ts_ist),user_name,outlet_id ORDER BY ts_ist) AS next_action_ts_ist,
        
    DATE_DIFF('minute', ts_ist,LEAD(ts_ist) OVER (partition BY DATE(ts_ist),user_name,outlet_id ORDER BY ts_ist))AS time
from app_login_time 
group by 1,2,3,4,5,6
having count(*) = count(case when next_action_ts_ist is null then user_name end)),


break_time as
(select date(break_start_time+interval '330' minute) as date_ts,
employee_id,
site_id,
sum(date_diff('second',(break_start_time+interval '330' minute),(break_end_time+interval '330' minute)))/60.0 as break_time
-- (role_end_time+interval '330' minute) as role_end_time,
-- lead((role_start_time+interval '330' minute),1) over(partition by date(role_start_time+interval '330' minute),employee_id, site_id,role order by role_start_time+interval '330' minute) as next_ts_ist,
-- date_diff('second',(role_end_time+interval '330' minute),lead((break_start_time+interval '330' minute),1) over(partition by date(break_start_time+interval '330' minute),employee_id, site_id,role order by role_start_time+interval '330' minute)) as break_time
from lake_view_ops_management.break_time 
where (break_start_time+interval '330' minute) >= cast('{Start_Date}' as date) and 
    (break_start_time+interval '330' minute)< cast('{End_Date}' as date)  + INTERVAL '1' DAY
group by 1,2,3
),


active_time as
(
select date(role_start_time+interval '330' minute) as date_ts,
employee_id,
role,
site_id,
sum(date_diff('second',(role_start_time+interval '330' minute),(role_end_time+interval '330' minute)))/60.0 as app_login_time,
min((role_start_time+interval '330' minute)) as login,
max((role_end_time+interval '330' minute)) as logout
from lake_view_ops_management.shift_roles
where (role_start_time+interval '330' minute) >= cast('{Start_Date}' as date) and 
    (role_start_time+interval '330' minute)< cast('{End_Date}' as date)  + INTERVAL '1' DAY
group by 1,2,3,4
),
-- select * from break_time where employee_id='GCE118682'



app_login_agg AS (
    
    
    select a.date_ts as date,
    cast(a.site_id as int) as outlet_id,
    a.employee_id as user_name,
    coalesce(sum(b.break_time),0) as breaktime,
    sum(case when a.role='PICKER' then app_login_time end) as picking_logged_in_time,
    sum(case when a.role='PUTTER' then app_login_time end) as putaway_logged_in_time,
    min(login) as login,
    max(logout) as  logout,
    sum(coalesce(app_login_time,0)) as total_app_time
    from active_time a
    left join break_time b on a.date_ts = b.date_ts and a.employee_id = b.employee_id and a.site_id = b.site_id
-- and a.role = b.role
    where a.role in ('PICKER','PUTTER')
    group by 1,2,3   
    
    union
    
 SELECT 
    
        a.date,
        a.outlet_id,
        a.user_name,
        
        coalesce(SUM ( CASE WHEN a.action in ('LOGOUT','FORCE_LOGOUT')  AND a.next_action_ts_ist IS NOT NULL AND extract(hour from a.next_action_ts_ist)>5 THEN a.time END ),0) AS  breaktime,
        coalesce(SUM ( CASE WHEN a.role='PICKER' AND a.action='LOGIN' THEN a.time END ),SUM ( CASE WHEN b.role='PICKER' AND b.action='LOGIN' THEN b.time END )) AS  picking_logged_in_time,
        
        coalesce(SUM ( CASE WHEN a.role='PUTAWAY' AND a.action='LOGIN'  THEN a.time END ),0) AS putaway_logged_in_time,
        MIN(CASE WHEN a.action in ('LOGIN') THEN a.ts_ist END) AS login,
        MAX(CASE WHEN a.action in ('LOGOUT','FORCE_LOGOUT') THEN a.ts_ist END) AS logout,
        coalesce(SUM(coalesce(a.time,b.time)),0) as total_app_time
    FROM 
        app_login_time a
        left join app_login_time_new b on a.date = b.date and a.outlet_id = b.outlet_id and a.user_name  = b.user_name and a.role = b.role
        and a.action = b.action
    GROUP BY 1,2,3
 
    
),


roster_time AS (

    SELECT
    
        DATE(from_unixtime(start_time/ 1000000 + 5.5 * 3600)) AS date,
        up.employee_id,
        n.external_id AS merchant_id,
        n.external_name AS merchant_name,
        SUM(CASE WHEN end_time<start_time THEN 0 ELSE DATE_DIFF('minute',from_unixtime(start_time/ 1000000),from_unixtime(end_time/ 1000000)) END) AS working_minutes
    FROM 
        lake_logistics.logistics_user_roster 
            AS r
    INNER JOIN 
        lake_logistics.logistics_user_profile
            AS up 
            ON up.id = r.user_profile_id
            AND up.type IN ('PICKER','PUTTER')
    INNER JOIN 
        lake_logistics.logistics_user
            AS u 
            ON up.user_id=u.id
    INNER JOIN 
        lake_logistics.logistics_user_profile_node 
            AS upn 
            ON upn.id = r.user_profile_node_id
    INNER JOIN 
        lake_logistics.logistics_node 
            AS n 
            ON n.id = upn.node_id
            AND n.id!=35
    WHERE 
        from_unixtime(start_time/ 1000000 + 5.5 * 3600) >=  cast('{Start_Date}' as date) 
        AND from_unixtime(start_time/ 1000000 + 5.5 * 3600) <  cast('{End_Date}' as date)  + INTERVAL '1' DAY
    GROUP BY 1,2,3,4
),


working_hours_final as (

SELECT 
    COALESCE(a.date,r.date) as date,
    a.outlet_id ,
    outlet.name AS outlet_name,
    COALESCE(a.user_name,r.employee_id) as user_name,
    a.breaktime,
    a.picking_logged_in_time,
    a.putaway_logged_in_time,
    a.login,
    a.logout,
    r.working_minutes AS roster_working_mins,
    COALESCE(r.working_minutes,0) + COALESCE(putaway_logged_in_time,0) AS total_loggedin_mins,
    sum(coalesce(a.total_app_time,0)) as total_app_time
FROM 
    app_login_agg
        AS a
LEFT JOIN 
    roster_time
        AS r 
            ON r.date=a.date
            AND r.employee_id=a.user_name
LEFT JOIN 
        lake_retail.console_outlet 
            AS outlet 
            ON outlet.id=a.outlet_id
group by 1,2,3,4,5,6,7,8,9,10,11
),
            
--
--
--
attendence_scanning as (

    SELECT 
        employee_id as emp_id,
        date(start_timestamp + interval '330' MINUTE) AS date_ts,
        outlet_id,
        d.name,
        replace(LOCATION,' ','') AS city_name,
        c.name as role,
        b.name AS employee_name,
        min(a.start_timestamp + interval '330' MINUTE) AS start_timstamp,
        max(a.end_timestamp+ interval '330' MINUTE) AS end_timestamp,
        DATE_DIFF('minute',min(a.start_timestamp),max(a.end_timestamp)) as attendence_time
    FROM 
        lake_view_warehouse_location.user_attendance 
            AS a
    LEFT JOIN 
        lake_view_retail.warehouse_user 
            AS b 
            ON lower(a.employee_id) = lower(b.emp_id)
    LEFT JOIN
        lake_view_retail.warehouse_user_role 
            AS c 
            ON b.role_id = c.id
    INNER JOIN 
        lake_retail.console_outlet 
            AS d 
            ON d.id = a.outlet_id
    WHERE 
        (start_timestamp + interval '330' MINUTE) >=  cast('{Start_Date}' as date) 
        AND (start_timestamp + interval '330' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY
GROUP BY 1, 2, 3, 4, 5, 6,7


union

SELECT 
        a.employee_id as emp_id,
        date(shift_start_time + interval '330' MINUTE) AS date_ts,
        cast(a.site_id as int) as outlet_id,
        d.name,
        replace(LOCATION,' ','') AS city_name,
        u.role,
        u.first_name AS employee_name,
        min(a.shift_start_time + interval '330' MINUTE) AS start_timstamp,
        max(a.shift_end_time+ interval '330' MINUTE) AS end_timestamp,
        DATE_DIFF('minute',min(a.shift_start_time),max(a.shift_end_time)) as attendence_time
    FROM 
        lake_view_ops_management.shift
            AS a
    left join lake_view_storeops.user_details u on u.employee_id = a.employee_id
    INNER JOIN 
        lake_view_retail.console_outlet 
            AS d 
            ON d.id = cast(a.site_id as int)
    WHERE 
        (shift_start_time + interval '330' MINUTE) >=  cast('{Start_Date}' as date) 
        AND (shift_start_time + interval '330' MINUTE) <=  cast('{End_Date}' as date)+INTERVAL '1' DAY
GROUP BY 1, 2, 3, 4, 5, 6,7



),
--
--
--
idle_time as (
       
 SELECT
            cast((q.entry_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) as date) as date,
            fm.OUTLET_ID,
            lup.employee_id,
            q.user_type,
            sum(date_diff('minute',q.entry_ts,q.exit_ts)) as queue_time
        FROM
            lake_view_logistics.logistics_express_allocation_field_executive_queue
                AS q
        INNER JOIN 
            lake_logistics.logistics_user_profile
                AS lup
                on lup.id=cast(q.user_profile_id as int)
        JOIN 
            lake_logistics.logistics_node
            AS ln
            ON ln.external_id=q.dark_store_id
            -- AND ln.external_id <> '30787'
        JOIN 
            lake_logistics.logistics_node_address
            AS lna 
            ON lna.id=ln.node_address_id
        LEFT JOIN 
             lake_cms.gr_virtual_to_real_merchant_mapping
            AS m
            on m.virtual_merchant_id=cast(ln.external_id as int)
            and  m.enabled_flag=true
        LEFT JOIN 
            fc_mapping
            AS fm
            on fm.BACKEND_MERCHANT_ID=m.real_merchant_id
            and BACKEND_MERCHANT_ID is not null
        WHERE 
            cast((q.entry_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) as date)>=cast('{Start_Date}' as date)
            and cast((q.entry_ts + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) as date)<=cast('{End_Date}' as date)+INTERVAL '1' DAY
            and q.exit_ts is not null
            and q.user_type='PICKER'
        GROUP BY 1,2,3,4

),

---
---
---

final as (

SELECT 
     COALESCE(d.date_ts,whf.date,att.date_ts) as Date,
    COALESCE(d.outlet_id,att.outlet_id,whf.outlet_id,a.outlet_id) as Store_ID,
    COALESCE(d.outlet_name,a.outlet_name,att.name,whf.outlet_name) as Store_Name,
    CASE 
        WHEN COALESCE(d.city,att.city_name,b.city,a.city) = 'HR-NCR' then 'Gurgaon' 
        WHEN COALESCE(d.city,att.city_name,b.city,a.city) = 'UP-NCR' then 'Noida' 
        WHEN COALESCE(d.city,att.city_name,b.city,a.city) ='Bangalore' then 'Bengaluru'
    ELSE
        trim(COALESCE(d.city,att.city_name,b.city,a.city)) 
    END AS City,
    
    COALESCE(d.emp_id,whf.user_name,att.emp_id) AS Employee_ID,
    COALESCE(d.employee_name,att.employee_name) AS Employee_Name,
    COALESCE(att.role,c.role) as role,
    COALESCE (a.qty,0) as Total_Putaway_Quantity,
    COALESCE (b.qty,0) as Total_Picked_Quantity,
    COALESCE (b.qty,0) *2 + COALESCE (a.qty,0) as Score,
    COALESCE (b.number_of_orders_picked,0) as Total_orders_Picked,
    whf.breaktime AS Break_Time,
    whf.picking_logged_in_time AS Picking_Logged_in_Time,
    whf.putaway_logged_in_time AS Putaway_Logged_in_Time,
    whf.login,
    whf.logout,
    whf.roster_working_mins AS Roster_On_Time,
    whf.total_loggedin_mins AS Total_Login_Time,
    i.queue_time as Wait_Time_In_queue,
        
    CASE WHEN att.attendence_time>0 or att.start_timstamp is not null
        THEN 'Yes'
        ELSE 'No' End AS Attendance_Punched,
        
    CASE 
        WHEN COALESCE(d.emp_id,whf.user_name,att.emp_id) LIKE 'GCESS%%' 
            THEN 'SPLIT SHIFT'
        WHEN extract(hour from LEAST(att.start_timstamp,whf.login))<13 
            THEN 'FIRST SHIFT'
        ELSE 'SECOND SHIFT'
    END AS Shift_Type,
        
      CASE 
        WHEN  (COALESCE(whf.picking_logged_in_time,0)+ COALESCE(whf.putaway_logged_in_time,0))>0 and (COALESCE(whf.picking_logged_in_time,0)+ COALESCE(whf.putaway_logged_in_time,0))<9*60
                THEN cast((COALESCE(whf.picking_logged_in_time,0)+ COALESCE(whf.putaway_logged_in_time,0))as DECIMAL)/60
        WHEN (COALESCE(whf.picking_logged_in_time,0)+ COALESCE(whf.putaway_logged_in_time,0))>9*60
        THEN 9
        END AS total_app_time,
        
       CASE 
        WHEN  b.total_picking_time_in_mins>0 and COALESCE (b.qty,0) >0 
                THEN  b.total_picking_time_in_mins/COALESCE (b.qty,0) 
        END as picking_time, 
        
       CASE WHEN  a.total_putaway_time_in_sec>0 and COALESCE (a.qty,0) >0
        THEN a.total_putaway_time_in_sec/COALESCE (a.qty,0) 
        END as putaway_time
        
    FROM 
        emp_base 
            AS d
    LEFT JOIN 
        putaway_agg 
            AS a 
            ON d.emp_id = a.emp_id 
            AND d.date_ts = a.date_ts 
            AND d.outlet_id = a.outlet_id
    LEFT JOIN 
        picking_agg
            AS  b 
            ON d.emp_id = b.emp_id 
            AND b.date_ts = d.date_ts
            AND d.outlet_id = b.outlet_id
    LEFT JOIN 
        working_hours_final
            AS whf
            ON whf.user_name=d.emp_id
            AND d.date_ts= whf.date
    FULL OUTER JOIN
        attendence_scanning
            AS att
            ON ( 
                att.emp_id=d.emp_id
                OR 
                att.emp_id=whf.user_name)
            AND ( 
                att.date_ts=d.date_ts
                OR
                att.date_ts=whf.date)
            AND att.outlet_id=d.outlet_id
        LEFT JOIN 
                idle_time
                AS i
                ON i.date=b.date_ts
                AND i.employee_id=b.emp_id
                AND i.OUTLET_ID=b.outlet_id
        left join lake_view_storeops.user_details c on c.employee_id = d.emp_id
                
    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
)

SELECT * FROM final
