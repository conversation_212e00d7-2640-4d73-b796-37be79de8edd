{"cells": [{"cell_type": "code", "execution_count": null, "id": "6dc0bede-3bfa-4947-ac1f-4d63cf9a7d98", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "74705f72-b948-42ae-bc6f-a4f57bf9a9ac", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "b73d157e-cb91-45b1-b6c2-988cd611a0d3", "metadata": {}, "outputs": [], "source": ["outlet_map = pb.from_sheets(\n", "    \"1hEB1IWUiC1CdGUnDRNrq1iyp9269Y2sVorEbMk-yW3c\", \"Store Mapping\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "73544287-58f8-42ff-9ead-7a5aae10a5e5", "metadata": {}, "outputs": [], "source": ["outlet_map.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "29fdb79a-028e-46da-80da-441b66c988c4", "metadata": {}, "outputs": [], "source": ["outlet_map[\"Outlet ID\"] = outlet_map[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "be682e4b-2645-458c-8a56-b6d481e01f74", "metadata": {}, "outputs": [], "source": ["region_dict = outlet_map.groupby(\"Region\")[\"Outlet ID\"].apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "b903ac88-6160-42ff-b01c-23ff03753415", "metadata": {}, "outputs": [], "source": ["region_dict_new = region_dict.set_index(\"Region\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3c65322-4df3-479d-94e1-de5858268c1d", "metadata": {}, "outputs": [], "source": ["sheet_mapping = pb.from_sheets(\n", "    \"1hEB1IWUiC1CdGUnDRNrq1iyp9269Y2sVorEbMk-yW3c\", \"Sheet4\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9a764182-689a-4f52-aa6a-5db65c0481ce", "metadata": {}, "outputs": [], "source": ["sheet_dict = sheet_mapping.set_index(\"Region\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "id": "707592e2-af14-403b-bc67-60119ef148c3", "metadata": {}, "outputs": [], "source": ["q = \"\"\"SELECT dt,\n", "       hr,\n", "       city,\n", "       outlet_id,\n", "       outlet_name,\n", "       orders,\n", "       delivered_orders,\n", "       orders_10_min,\n", "       orders_15_min,\n", "       checkout_pickup_assigned_time,\n", "       pickup_process_time,\n", "       billing_order_scanned_time,\n", "       order_scanned_order_enroute_time,\n", "       enroute_delivered_time,\n", "       instore_time,\n", "       pickers,\n", "       riders,\n", "       orders_lessthan_2mins,\n", "       1.0*orders_lessthan_2mins/nullif(delivered_orders,0) AS perc_orders_lessthan_2mins,\n", "       refresh_time\n", "FROM\n", "  (SELECT cart_checkout_ts_ist::date dt,\n", " \n", "          extract(hour\n", "                  FROM cart_checkout_ts_ist) hr,\n", "          b.city_name AS city,\n", "          a.outlet_id AS outlet_id,\n", "          co.name AS outlet_name,\n", "           max(cart_checkout_ts_ist) as refresh_time,\n", "          count(DISTINCT a.order_id) orders,\n", "          count(DISTINCT CASE\n", "                             WHEN a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) delivered_orders,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 10\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_10_min,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 15\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_15_min,\n", "          avg(datediff(seconds,c.order_checkout_ts_ist,c.order_picker_assigned_ts_ist)) checkout_pickup_assigned_time,\n", "          avg(datediff(seconds,c.order_picking_started_ts_ist,c.order_picking_completed_ts_ist)) pickup_process_time,\n", "          avg(datediff(seconds,c.order_billing_completed_ts_ist,c.order_scanned_ts_ist)) billing_order_scanned_time,\n", "          avg(datediff(seconds,c.order_scanned_ts_ist,c.order_enroute_ts_ist)) order_scanned_order_enroute_time,\n", "          avg(datediff(seconds,c.order_enroute_ts_ist,c.order_delivered_ts_ist)) enroute_delivered_time,\n", "          avg(DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)) AS instore_time,\n", "          count(DISTINCT picker_id) AS pickers,\n", "          count(DISTINCT partner_id) AS riders,\n", "          sum(CASE\n", "                  WHEN DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)<=150 THEN 1\n", "                  ELSE 0\n", "              END) AS orders_lessthan_2mins\n", "   FROM dwh.fact_sales_order_details a\n", "   JOIN dwh.dim_merchant b ON a.dim_frontend_merchant_key = b.merchant_key\n", "   AND is_current = TRUE\n", "   JOIN dwh.fact_supply_chain_order_details c ON a.order_id = c.order_id\n", "   AND is_rescheduled = FALSE\n", "   INNER JOIN lake_retail.console_outlet co ON co.id = a.outlet_id\n", "   AND business_type_id=7\n", "   WHERE a.cart_checkout_ts_ist::date >= CURRENT_DATE -3\n", "     AND a.order_type IN ('RetailForwardOrder',\n", "                          'DropShippingForwardOrder')\n", "\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5)\n", "\n", "UNION\n", "\n", "SELECT dt,\n", "       hr,\n", "       city,\n", "       outlet_id,\n", "       outlet_name,\n", "       orders,\n", "       delivered_orders,\n", "       orders_10_min,\n", "       orders_15_min,\n", "       checkout_pickup_assigned_time,\n", "       pickup_process_time,\n", "       billing_order_scanned_time,\n", "       order_scanned_order_enroute_time,\n", "       enroute_delivered_time,\n", "       instore_time,\n", "       pickers,\n", "       riders,\n", "       orders_lessthan_2mins,\n", "       1.0*orders_lessthan_2mins/nullif(delivered_orders,0) AS perc_orders_lessthan_2mins,\n", "       refresh_time\n", "FROM\n", "  (SELECT cart_checkout_ts_ist::date dt,\n", "          extract(hour\n", "                  FROM cart_checkout_ts_ist) hr,\n", "          b.city_name AS city,\n", "         -1 as outlet_id,\n", "'Overall' as outlet_name,\n", " max(cart_checkout_ts_ist) as refresh_time,\n", "          count(DISTINCT a.order_id) orders,\n", "          count(DISTINCT CASE\n", "                             WHEN a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) delivered_orders,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 10\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_10_min,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 15\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_15_min,\n", "          avg(datediff(seconds,c.order_checkout_ts_ist,c.order_picker_assigned_ts_ist)) checkout_pickup_assigned_time,\n", "          avg(datediff(seconds,c.order_picking_started_ts_ist,c.order_picking_completed_ts_ist)) pickup_process_time,\n", "          avg(datediff(seconds,c.order_billing_completed_ts_ist,c.order_scanned_ts_ist)) billing_order_scanned_time,\n", "          avg(datediff(seconds,c.order_scanned_ts_ist,c.order_enroute_ts_ist)) order_scanned_order_enroute_time,\n", "          avg(datediff(seconds,c.order_enroute_ts_ist,c.order_delivered_ts_ist)) enroute_delivered_time,\n", "          avg(DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)) AS instore_time,\n", "          count(DISTINCT picker_id) AS pickers,\n", "          count(DISTINCT partner_id) AS riders,\n", "          sum(CASE\n", "                  WHEN DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)<=150 THEN 1\n", "                  ELSE 0\n", "              END) AS orders_lessthan_2mins\n", "   FROM dwh.fact_sales_order_details a\n", "   JOIN dwh.dim_merchant b ON a.dim_frontend_merchant_key = b.merchant_key\n", "   AND is_current = TRUE\n", "   JOIN dwh.fact_supply_chain_order_details c ON a.order_id = c.order_id\n", "   AND is_rescheduled = FALSE\n", "   INNER JOIN lake_retail.console_outlet co ON co.id = a.outlet_id\n", "   AND business_type_id=7\n", "   WHERE a.cart_checkout_ts_ist::date >= CURRENT_DATE -3\n", "     AND a.order_type IN ('RetailForwardOrder',\n", "                          'DropShippingForwardOrder')\n", " \n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5)\n", "            \n", "UNION\n", "\n", "SELECT dt,\n", "       hr,\n", "       city_name,\n", "       outlet_id,\n", "       outlet_name,\n", "       orders,\n", "       delivered_orders,\n", "       orders_10_min,\n", "       orders_15_min,\n", "       checkout_pickup_assigned_time,\n", "       pickup_process_time,\n", "       billing_order_scanned_time,\n", "       order_scanned_order_enroute_time,\n", "       enroute_delivered_time,\n", "       instore_time,\n", "       pickers,\n", "       riders,\n", "       orders_lessthan_2mins,\n", "       1.0*orders_lessthan_2mins/nullif(delivered_orders,0) AS perc_orders_lessthan_2mins,\n", "       refresh_time\n", "FROM\n", "  (SELECT \n", "  cart_checkout_ts_ist::date dt,\n", "          extract(hour FROM cart_checkout_ts_ist) hr,\n", "       'Overall' city_name,\n", "-1  as outlet_id,\n", "'Overall' as outlet_name,\n", " max(cart_checkout_ts_ist) as refresh_time,\n", " \n", "          count(DISTINCT a.order_id) orders,\n", "          count(DISTINCT CASE\n", "                             WHEN a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) delivered_orders,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 10\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_10_min,\n", "          count(DISTINCT CASE\n", "                             WHEN datediff(MINUTE,c.order_checkout_ts_ist,c.order_delivered_ts_ist) <= 15\n", "                                  AND a.order_current_status = 'DELIVERED' THEN a.order_id\n", "                         END) orders_15_min,\n", "          avg(datediff(seconds,c.order_checkout_ts_ist,c.order_picker_assigned_ts_ist)) checkout_pickup_assigned_time,\n", "          avg(datediff(seconds,c.order_picking_started_ts_ist,c.order_picking_completed_ts_ist)) pickup_process_time,\n", "          avg(datediff(seconds,c.order_billing_completed_ts_ist,c.order_scanned_ts_ist)) billing_order_scanned_time,\n", "          avg(datediff(seconds,c.order_scanned_ts_ist,c.order_enroute_ts_ist)) order_scanned_order_enroute_time,\n", "          avg(datediff(seconds,c.order_enroute_ts_ist,c.order_delivered_ts_ist)) enroute_delivered_time,\n", "          avg(DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)) AS instore_time,\n", "          count(DISTINCT picker_id) AS pickers,\n", "          count(DISTINCT partner_id) AS riders,\n", "          sum(CASE\n", "                  WHEN DATEDIFF(seconds,c.order_checkout_ts_ist,c.order_billing_completed_ts_ist)<=150 THEN 1\n", "                  ELSE 0\n", "              END) AS orders_lessthan_2mins\n", "   FROM dwh.fact_sales_order_details a\n", "   JOIN dwh.dim_merchant b ON a.dim_frontend_merchant_key = b.merchant_key\n", "   AND is_current = TRUE\n", "   JOIN dwh.fact_supply_chain_order_details c ON a.order_id = c.order_id\n", "   AND is_rescheduled = FALSE\n", "   INNER JOIN lake_retail.console_outlet co ON co.id = a.outlet_id\n", "   AND business_type_id=7\n", "   WHERE a.cart_checkout_ts_ist::date >= CURRENT_DATE -3\n", "     AND a.order_type IN ('RetailForwardOrder',\n", "                          'DropShippingForwardOrder')\n", "    \n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5)\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "\n", "data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "85cf72e2-55d2-4c5c-aac1-d179631262a0", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "70574863-dd07-403f-b102-8d62dec4f535", "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "5e02f6ee-be4f-466d-b4d9-0a4ea8508cdc", "metadata": {}, "outputs": [], "source": ["for i in sheet_dict.keys():\n", "\n", "    pb.to_sheets(\n", "        data[data[\"outlet_id\"].isin(region_dict_new[i][0])],\n", "        sheet_dict[i][0],\n", "        \"hourly_data\",\n", "        clear_cache=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "0fa298ae-fa0c-48f1-922a-4985598a8085", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     data,\n", "#     \"1sdI-YDPkX3MSKtxUb4Bhy4Kszt9tFUZiO0Xd0v2fuNA\",\n", "#     \"hourly_data\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "cdf0d617-029c-49dc-ba8f-a04998c94203", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}