# -*- coding: utf-8 -*-

import os
import json
import logging
from contextlib import closing
from datetime import datetime, timedelta

import requests

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            node_selector={"nodetype": "spot"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image="442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable",
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "8G"},
                        requests={"cpu": 0.5, "memory": "8G"},
                    ),
                )
            ],
        )
    )
}


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter-service-data.prod-sgp-k8s.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "U03RRBD9SH4"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S03TEACQKDW> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2022-10-21T05:30:00", "%Y-%m-%dT%H:%M:%S"),
}

dag = DAG(
    dag_id="supply_chain_store_ops_report_sm_hourly_handbook_city_v4",
    default_args=args,
    schedule_interval="@hourly",
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "supply_chain_store_ops_report_sm_hourly_handbook_city_v4",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)

notebook_task = PapermillOperator(
    task_id="run_notebook",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/supply_chain_store_ops_report_sm_hourly_handbook_city_v4/{{ run_id }}/run_notebook.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/sm_hourly_handbook_city"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
