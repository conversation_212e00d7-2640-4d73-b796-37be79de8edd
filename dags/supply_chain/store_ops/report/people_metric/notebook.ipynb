{"cells": [{"cell_type": "code", "execution_count": null, "id": "4930023e-41ab-463f-9c67-d23117adfd55", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import calendar\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "ee3f9a29-90b7-42fd-8c16-3e03dd9af4a5", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "5d5f6e7a-34bd-4153-928d-c3a54d933a0c", "metadata": {}, "outputs": [], "source": ["manpower_more_than_9 = \"\"\"\n", "select  \n", "date_ts,\n", "        count(distinct employee_id) as total_employee,\n", "        count(distinct case when total_working_time>=540 then employee_id end) as emp_with_more_than_9_hr_login,\n", "        (count(distinct case when total_working_time>=540 then employee_id end)*100.0/count(distinct employee_id)) as \"%% employee\"\n", "from\n", "((select a.*,\n", "       b.total_working_time \n", "       --as \"Total working time (in mins)\"\n", "    from\n", "(select *\n", "from\n", "(select \n", "row_number() over(partition by date(start_timestamp+interval '330' minute),employee_id) as serial_no,\n", "date(start_timestamp+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "outlet_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "max(case when type=2 then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is not null then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is null then start_timestamp+interval '990' minute\n", "when type=1 and end_timestamp is null then current_timestamp + interval '330' minute\n", "else end_timestamp+interval '330' minute\n", "end) as max_log_out_timestamp,\n", "min(start_timestamp+interval '330' minute) as min_log_in_timestamp\n", "from  lake_warehouse_location.user_attendance a\n", "inner join lake_retail.warehouse_user b on a.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = a.outlet_id\n", "where date(start_timestamp+interval '330' minute) between current_date-21 and current_date\n", "and d.business_type_id = 7\n", "group by 2,3,4,5,6,7,8,9)\n", "where serial_no = 1) a \n", "inner join\n", "(with base as (select *\n", "from\n", "(select \n", "date(start_timestamp+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "outlet_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "a.type,\n", "(case when type=2 then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is not null then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is null then start_timestamp+interval '990' minute\n", "when type=1 and end_timestamp is null then current_timestamp + interval '330' minute\n", "else end_timestamp+interval '330' minute\n", "end) as end_timestamp,\n", "(start_timestamp+interval '330' minute) as start_time,\n", "datediff('minute',(start_timestamp+interval '330' minute)::timestamp,(case when type=2 then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is not null then end_timestamp + interval '330' minute\n", "when type=3 and end_timestamp is null then start_timestamp+interval '990' minute\n", "when type=1 and end_timestamp is null then current_timestamp + interval '330' minute\n", "else end_timestamp +interval '330' minute\n", "end)::timestamp) as working_time\n", "from  lake_warehouse_location.user_attendance a\n", "inner join lake_retail.warehouse_user b on a.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = a.outlet_id\n", "where date(start_timestamp+interval '330' minute) between current_date-21 and current_date\n", "and d.business_type_id = 7\n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", ")),\n", "base1 as\n", "(select employee_id,\n", "       date_ts,\n", "       sum(working_time) as total_working_time\n", "    from base \n", "    group by 1,2)\n", "    \n", "    select \n", "         employee_id,\n", "          date_ts,\n", "          (case when total_working_time>=540 then 540 else total_working_time end) as total_working_time\n", "        from base1) b on a.employee_id = b.employee_id and a.date_ts = b.date_ts\n", ")\n", "union all\n", "(select a.*,\n", "       b.total_working_time as \"Total working time (in mins)\"\n", "    from\n", "(select *\n", "from\n", "(select \n", "row_number() over(partition by date(shift_start_time+interval '330' minute),employee_id) as serial_no,\n", "date(shift_start_time+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "cast(site_id as int) as outlet_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "max(case when shift_end_method='MANUAL' then shift_end_time + interval '330' minute\n", "when shift_end_method='AUTO' and shift_end_time is not null then shift_end_time + interval '330' minute\n", "when shift_end_method='MANUAL' and shift_end_time is null then shift_start_time+interval '990' minute\n", "when shift_end_method='AUTO' and shift_end_time is null then current_timestamp + interval '330' minute\n", "else shift_end_time+interval '330' minute\n", "end) as max_log_out_timestamp,\n", "-- max(shift_end_time+interval '330' minute) as max_log_out_timestamp,\n", "min(shift_start_time+interval '330' minute) as min_log_in_timestamp\n", "from  lake_ops_management.shift s\n", "inner join lake_retail.warehouse_user b on s.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = cast(s.site_id as int)\n", "where date(shift_start_time + interval '330' minute) between current_date-21 and current_date\n", "-- and d.business_shift_end_method_id = 7\n", "group by 2,3,4,5,6,7,8,9)\n", "where serial_no = 1) a \n", "inner join\n", "(with base as (select *\n", "from\n", "(select \n", "date(shift_start_time+interval '330' minute) as date_ts,\n", "employee_id, \n", "b.name as employee_name,\n", "b.doj,\n", "site_id,\n", "d.name as outlet_name,\n", "replace(location,' ','') as city_name,\n", "c.name as role,\n", "s.shift_end_method,\n", "(case when shift_end_method='MANUAL' then shift_end_time + interval '330' minute\n", "when shift_end_method='AUTO' and shift_end_time is not null then shift_end_time + interval '330' minute\n", "when shift_end_method='MANUAL' and shift_end_time is null then shift_start_time+interval '990' minute\n", "when shift_end_method='AUTO' and shift_end_time is null then current_timestamp + interval '330' minute\n", "else shift_end_time+interval '330' minute\n", "end) as shift_end_time,\n", "-- (shift_end_time+interval '330' minute) as shift_end_time,\n", "(shift_start_time+interval '330' minute) as start_time,\n", "datediff('minute',(shift_start_time+interval '330' minute)::timestamp,(case when shift_end_method='MANUAL' then shift_end_time + interval '330' minute\n", "when shift_end_method='AUTO' and shift_end_time is not null then shift_end_time + interval '330' minute\n", "when shift_end_method='MANUAL' and shift_end_time is null then shift_start_time+interval '990' minute\n", "when shift_end_method='AUTO' and shift_end_time is null then current_timestamp + interval '330' minute\n", "else shift_end_time +interval '330' minute\n", "end)::timestamp) as working_time\n", "from  lake_ops_management.shift s\n", "inner join lake_retail.warehouse_user b on s.employee_id = b.emp_id\n", "inner join lake_retail.warehouse_user_role c on b.role_id = c.id\n", "inner join lake_retail.console_outlet d on d.id = cast(s.site_id as int)\n", "where date(shift_start_time+interval '330' minute) between current_date-21 and current_date\n", "-- and d.business_shift_end_method_id = 7\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12\n", ")),\n", "base1 as\n", "(select employee_id,\n", "       date_ts,\n", "       sum(working_time) as total_working_time\n", "    from base \n", "    group by 1,2)\n", "    \n", "    select \n", "         employee_id,\n", "          date_ts,\n", "          total_working_time\n", "          --(case when total_working_time>=540 then 540 else total_working_time end) as total_working_time\n", "        from base1) b on a.employee_id = b.employee_id and a.date_ts = b.date_ts\n", "))\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d66c3e69-8986-451b-89c5-d58636041da8", "metadata": {}, "outputs": [], "source": ["manpower_more_than_9df = pd.read_sql_query(sql=manpower_more_than_9, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "f9b44eb0-9d9e-4ec9-8342-bf79427220da", "metadata": {}, "outputs": [], "source": ["manpower_more_than_9df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4f2acaf1-a371-4949-8d65-81a3fb68b226", "metadata": {}, "outputs": [], "source": ["manpower_more_than_9df[\"week_start\"] = manpower_more_than_9df[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "manpower_more_than_9df[\"week_end\"] = manpower_more_than_9df[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "manpower_more_than_9df[\"week\"] = (\n", "    manpower_more_than_9df[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + manpower_more_than_9df[\"week_end\"].astype(str)\n", ")\n", "manpower_more_than_9df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7c479868-cbfe-41ab-89c4-53a86b8c5586", "metadata": {}, "outputs": [], "source": ["# new_emp = \"\"\"\n", "# (with\n", "# data as (\n", "# select distinct employee_id,outlet_id,\n", "# min(start_timestamp) as first_day\n", "# from lake_warehouse_location.user_attendance\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_warehouse_location.user_attendance\n", "# where date(start_timestamp) between current_date-7 and current_date\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-7 and current_date then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,outlet_id,\n", "# min(start_timestamp) as first_day\n", "# from lake_warehouse_location.user_attendance\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_warehouse_location.user_attendance\n", "# where date(start_timestamp) between current_date-14 and current_date-7\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-14 and current_date-7 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-7 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,outlet_id,\n", "# min(start_timestamp) as first_day\n", "# from lake_warehouse_location.user_attendance\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_warehouse_location.user_attendance\n", "# where date(start_timestamp) between current_date-21 and current_date-14\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-21 and current_date-14 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-14 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,outlet_id,\n", "# min(start_timestamp) as first_day\n", "# from lake_warehouse_location.user_attendance\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_warehouse_location.user_attendance\n", "# where date(start_timestamp) between current_date-28 and current_date-21\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-28 and current_date-21 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-21 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "357872e9-6f30-4b12-9dec-3c04ce948988", "metadata": {}, "outputs": [], "source": ["# new_empdf = pd.read_sql_query(sql=new_emp, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3e6571b9-ab53-4bc6-8a78-e9d19219b0c5", "metadata": {}, "outputs": [], "source": ["# new_empdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "63f6437b-302f-42c2-baef-857eacfee8db", "metadata": {}, "outputs": [], "source": ["# new_empdf[\"week_start\"] = new_empdf[\"date\"].apply(\n", "#     lambda x: x - timed<PERSON>ta(days=(x.weekday() + 1) % 7)\n", "\n", "\n", "# new_empdf[\"week_end\"] = new_empdf[\"week_start\"].apply(lambda x: x + <PERSON>elta(days=6))"]}, {"cell_type": "code", "execution_count": null, "id": "16a6fd7b-8e37-4a22-ac74-2a3097508d2f", "metadata": {}, "outputs": [], "source": ["# new_empdf[\"week\"] = (\n", "#     new_empdf[\"week_start\"].astype(str) + \" to \" + new_empdf[\"week_end\"].astype(str)\n", "# )\n", "# new_empdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b8111667-af6f-49ad-a9a0-23b1010d7a6f", "metadata": {}, "outputs": [], "source": ["# new_store_emp = \"\"\"\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,site_id,\n", "# min(shift_start_time) as first_day\n", "# from lake_ops_management.shift\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_ops_management.shift\n", "# where date(shift_start_time) between current_date-7 and current_date\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-7 and current_date then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,site_id,\n", "# min(shift_start_time) as first_day\n", "# from lake_ops_management.shift\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_ops_management.shift\n", "# where date(shift_start_time) between current_date-14 and current_date-7\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-14 and current_date-7 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-7 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,site_id,\n", "# min(shift_start_time) as first_day\n", "# from lake_ops_management.shift\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_ops_management.shift\n", "# where date(shift_start_time) between current_date-21 and current_date-14\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-21 and current_date-14 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-14 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "\n", "# union\n", "\n", "# (with\n", "# data as (\n", "# select distinct employee_id,site_id,\n", "# min(shift_start_time) as first_day\n", "# from lake_ops_management.shift\n", "# group by 1,2),\n", "\n", "# data2 as (\n", "# select current_date as date,\n", "# count(distinct employee_id) as total_emp\n", "# from lake_ops_management.shift\n", "# where date(shift_start_time) between current_date-28 and current_date-21\n", "\n", "# ),\n", "# data3 as (\n", "# select current_date as date,\n", "# count(distinct case when date(first_day) between current_date-28 and current_date-21 then employee_id end) as new_employees\n", "# from data)\n", "\n", "# select current_date-21 as date,\n", "# d3.new_employees,\n", "# d2.total_emp,\n", "# d3.new_employees*100.0/d2.total_emp as \"%%new employees\"\n", "# from data3 d3\n", "# join data2 d2\n", "# on d3.date=d2.date)\n", "\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e42943fc-2469-4553-bbaf-e641ae11a32d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c03ba5c0-d852-49e3-aa79-7b9b414da3d8", "metadata": {}, "outputs": [], "source": ["new_store_emp = \"\"\"\n", "(with\n", "data as (\n", "select employee_id,\n", "       outlet_id,\n", "       min(first_day) as first_day\n", "   from    \n", "(\n", "(select distinct employee_id,outlet_id,\n", "min(start_timestamp + interval '5.5 hour') as first_day\n", "from lake_warehouse_location.user_attendance\n", "group by 1,2)\n", "union all\n", "(select distinct employee_id,cast(site_id as int) as outlet_id,\n", "min(shift_start_time + interval '5.5 hour') as first_day\n", "from lake_ops_management.shift\n", "group by 1,2)\n", ")\n", "group by 1,2\n", "),\n", "\n", "data2 as (\n", "select outlet_id,\n", "       count(distinct employee_id) as total_emp\n", "from\n", "(\n", "(select \n", "outlet_id,\n", "employee_id,\n", "date(start_timestamp + interval '5.5 hour') as date_ts\n", "from lake_warehouse_location.user_attendance)\n", "union all\n", "(select cast(site_id as int) as outlet_id,\n", " employee_id,\n", " date(shift_start_time + interval '5.5 hour') as date_ts\n", "from lake_ops_management.shift)\n", ")\n", "where date_ts between current_date-7 and current_date\n", "group by 1\n", "\n", "),\n", "data3 as (\n", "select \n", "--current_date as date,\n", "outlet_id,\n", "count(distinct case when date(first_day) between current_date-7 and current_date then employee_id end) as new_employees\n", "from data\n", "group by 1)\n", "\n", "select current_date as date,\n", "--d3.outlet_id,\n", "sum(d3.new_employees) as new_employees,\n", "sum(d2.total_emp) as total_emp,\n", "1.0*sum(d3.new_employees)/sum(d2.total_emp) as \"%%new employees\"\n", "from data3 d3\n", "join data2 d2\n", "on \n", "--d3.date=d2.date and \n", "d2.outlet_id=d3.outlet_id\n", "-- group by 1,)\n", ")\n", "union\n", "\n", "(with\n", "data as (\n", "select employee_id,\n", "       outlet_id,\n", "       min(first_day) as first_day\n", "   from    \n", "(\n", "(select distinct employee_id,outlet_id,\n", "min(start_timestamp + interval '5.5 hour') as first_day\n", "from lake_warehouse_location.user_attendance\n", "group by 1,2)\n", "union all\n", "(select distinct employee_id,cast(site_id as int) as outlet_id,\n", "min(shift_start_time + interval '5.5 hour') as first_day\n", "from lake_ops_management.shift\n", "group by 1,2)\n", ")\n", "group by 1,2\n", "),\n", "\n", "data2 as (\n", "select outlet_id,\n", "       count(distinct employee_id) as total_emp\n", "from\n", "(\n", "(select \n", "outlet_id,\n", "employee_id,\n", "date(start_timestamp + interval '5.5 hour') as date_ts\n", "from lake_warehouse_location.user_attendance)\n", "union all\n", "(select cast(site_id as int) as outlet_id,\n", " employee_id,\n", " date(shift_start_time + interval '5.5 hour') as date_ts\n", "from lake_ops_management.shift)\n", ")\n", "where date_ts between current_date-14 and current_date-7\n", "group by 1\n", "),\n", "data3 as (\n", "select \n", "--current_date as date,\n", "outlet_id,\n", "count(distinct case when date(first_day) between current_date-14 and current_date-7 then employee_id end) as new_employees\n", "from data\n", "group by 1)\n", "\n", "select current_date-7 as date,\n", "--d3.outlet_id,\n", "sum(d3.new_employees) as new_employees,\n", "sum(d2.total_emp) as total_emp,\n", "1.0*sum(d3.new_employees)/sum(d2.total_emp) as \"%%new employees\"\n", "from data3 d3\n", "join data2 d2\n", "on \n", "--d3.date=d2.date and \n", "d3.outlet_id=d2.outlet_id)\n", "\n", "union\n", "\n", "(with\n", "data as (\n", "select employee_id,\n", "       outlet_id,\n", "       min(first_day) as first_day\n", "   from    \n", "(\n", "(select distinct employee_id,outlet_id,\n", "min(start_timestamp + interval '5.5 hour') as first_day\n", "from lake_warehouse_location.user_attendance\n", "group by 1,2)\n", "union all\n", "(select distinct employee_id,cast(site_id as int) as outlet_id,\n", "min(shift_start_time + interval '5.5 hour') as first_day\n", "from lake_ops_management.shift\n", "group by 1,2)\n", ")\n", "group by 1,2\n", "),\n", "\n", "data2 as (\n", "select outlet_id,\n", "       count(distinct employee_id) as total_emp\n", "from\n", "(\n", "(select \n", "outlet_id,\n", "employee_id,\n", "date(start_timestamp + interval '5.5 hour') as date_ts\n", "from lake_warehouse_location.user_attendance)\n", "union all\n", "(select cast(site_id as int) as outlet_id,\n", " employee_id,\n", " date(shift_start_time + interval '5.5 hour') as date_ts\n", "from lake_ops_management.shift)\n", ")\n", "where date_ts between current_date-21 and current_date-14\n", "group by 1\n", "),\n", "data3 as (\n", "select \n", "--current_date as date,\n", "outlet_id,\n", "count(distinct case when date(first_day) between current_date-21 and current_date-14 then employee_id end) as new_employees\n", "from data\n", "group by 1)\n", "\n", "select current_date-14 as date,\n", "--d3.outlet_id,\n", "sum(d3.new_employees) as new_employees,\n", "sum(d2.total_emp) as total_emp,\n", "1.0*sum(d3.new_employees)/sum(d2.total_emp) as \"%%new employees\"\n", "from data3 d3\n", "join data2 d2\n", "on \n", "--d3.date=d2.date and \n", "d3.outlet_id=d2.outlet_id)\n", "\n", "union\n", "\n", "(with\n", "data as (\n", "select employee_id,\n", "       outlet_id,\n", "       min(first_day) as first_day\n", "   from    \n", "(\n", "(select distinct employee_id,outlet_id,\n", "min(start_timestamp + interval '5.5 hour') as first_day\n", "from lake_warehouse_location.user_attendance\n", "group by 1,2)\n", "union all\n", "(select distinct employee_id,cast(site_id as int) as outlet_id,\n", "min(shift_start_time + interval '5.5 hour') as first_day\n", "from lake_ops_management.shift\n", "group by 1,2)\n", ")\n", "group by 1,2\n", "),\n", "\n", "data2 as (\n", "select outlet_id,\n", "       count(distinct employee_id) as total_emp\n", "from\n", "(\n", "(select \n", "outlet_id,\n", "employee_id,\n", "date(start_timestamp + interval '5.5 hour') as date_ts\n", "from lake_warehouse_location.user_attendance)\n", "union all\n", "(select cast(site_id as int) as outlet_id,\n", " employee_id,\n", " date(shift_start_time + interval '5.5 hour') as date_ts\n", "from lake_ops_management.shift)\n", ")\n", "where date_ts between current_date-28 and current_date-21\n", "group by 1\n", "),\n", "data3 as (\n", "select \n", "--current_date as date,\n", "outlet_id,\n", "count(distinct case when date(first_day) between current_date-28 and current_date-21 then employee_id end) as new_employees\n", "from data\n", "group by 1)\n", "\n", "select current_date-21 as date,\n", "--d3.outlet_id,\n", "sum(d3.new_employees) as new_employees,\n", "sum(d2.total_emp) as total_emp,\n", "1.0*sum(d3.new_employees)/sum(d2.total_emp) as \"%%new employees\"\n", "from data3 d3\n", "join data2 d2\n", "on \n", "--d3.date=d2.date and \n", "d3.outlet_id=d2.outlet_id)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "18aa81a2-3102-4e11-8bc6-043898146da3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1035b2f7-9c48-4a4a-89f6-d6abf2840032", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5364fae2-a67b-4bb0-9a62-779547c7409b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3d155452-202a-4d19-9c56-9729840ee266", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "10285dbb-85bb-4e88-8334-3d2025138d36", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ba61c0f-1843-46ef-b33a-8d8fc926e34b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f1243c56-afc4-47a7-9eb0-2db61694af22", "metadata": {}, "outputs": [], "source": ["new_store_empdf = pd.read_sql_query(sql=new_store_emp, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "ade19428-bdbf-49e6-aae6-f57ca22eee55", "metadata": {}, "outputs": [], "source": ["new_store_empdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3ca25294-1b89-4332-8e1e-d59e1549f68b", "metadata": {}, "outputs": [], "source": ["new_store_empdf[\"week_start\"] = new_store_empdf[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "new_store_empdf[\"week_end\"] = new_store_empdf[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c97213b1-dadb-4a52-9172-84ce22bdce7e", "metadata": {}, "outputs": [], "source": ["new_store_empdf[\"week\"] = (\n", "    new_store_empdf[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + new_store_empdf[\"week_end\"].astype(str)\n", ")\n", "new_store_empdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d1266481-8ec0-4b55-b2f4-ea64108ed76e", "metadata": {}, "outputs": [], "source": ["# new_store_empdf = pd.concat([new_store_empdf, new_empdf])\n", "# new_store_empdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4877137b-9301-41c2-b420-341ea45f245e", "metadata": {}, "outputs": [], "source": ["manual_logout = \"\"\"\n", "with data as (\n", "select date(start_timestamp) as date_,\n", "count(distinct case when type = 2 then employee_id end) as manual,\n", "count(distinct case when type = 3 then employee_id end) as auto,\n", "count(distinct employee_id) as total\n", "from lake_warehouse_location.user_attendance\n", "where date(start_timestamp) between current_date-21 and current_date\n", "group by 1),\n", "\n", "\n", "data2 as (\n", "select date(shift_start_time) as date_,\n", "count(distinct case when shift_end_method = 'MANUAL' then employee_id end) as manual,\n", "count(distinct case when shift_end_method = 'AUTO' then employee_id end) as auto,\n", "count(distinct employee_id) as total\n", "from lake_ops_management.shift\n", "where date(shift_start_time) between current_date-21 and current_date\n", "group by 1)\n", "\n", "\n", "select date_,\n", "sum(manual) as manual,\n", "sum(auto) as auto,\n", "sum(total) as total,\n", "sum(manual)*100.0/sum(total)*1.0 as manual_percentage\n", "\n", "from \n", "(select date_,\n", "manual,\n", "auto,\n", "total\n", "from data\n", "union\n", "select date_,\n", "manual,\n", "auto,\n", "total\n", "from data2\n", ")\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "93335590-473e-410d-a022-a4fe14c1ca8b", "metadata": {}, "outputs": [], "source": ["manual_logoutdf = pd.read_sql_query(sql=manual_logout, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3793cf84-8b35-44c9-ac85-234fb9610b5d", "metadata": {}, "outputs": [], "source": ["manual_logoutdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a56daf3b-6ac6-4ff7-aed9-572825bb7491", "metadata": {}, "outputs": [], "source": ["manual_logoutdf[\"week_start\"] = manual_logoutdf[\"date_\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "manual_logoutdf[\"week_end\"] = manual_logoutdf[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9452a3fa-ac7a-4f8c-9b5f-f395ed3d7435", "metadata": {}, "outputs": [], "source": ["manual_logoutdf[\"week\"] = (\n", "    manual_logoutdf[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + manual_logoutdf[\"week_end\"].astype(str)\n", ")\n", "manual_logoutdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "69793c51-bed3-4aee-8a06-9a9bcdcb1916", "metadata": {}, "outputs": [], "source": ["stores_56 = \"\"\"\n", "select date_ts,\n", "       count(distinct case when emp>=2 then outlet_id end) as outlet_with_more_than_two_punch_in\n", "from\n", "(select date(start_time) as date_ts,\n", "       outlet_id,\n", "       count(distinct employee_id) as emp\n", "from\n", "((select employee_id,\n", "       outlet_id,\n", "       (start_timestamp+interval '330 minute') as start_time\n", "from lake_warehouse_location.user_attendance\n", "where (date(start_timestamp+interval '330 minute') between current_date-21 and current_date)\n", "and (extract(hour from start_timestamp+interval '330 minute') = 5))\n", "union\n", "(select\n", "employee_id,\n", "cast(site_id as int) as outlet_id,\n", "(shift_start_time+interval '330 minute') as end_time\n", "from lake_ops_management.shift\n", "where date(shift_start_time+interval '330 minute') between current_date-21 and current_date\n", "and (extract(hour from shift_start_time+interval '330 minute') = 5))\n", ")\n", "group by 1,2)\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2c03d1a1-bef1-4fdb-8553-a9a0ef75d888", "metadata": {}, "outputs": [], "source": ["stores_56df = pd.read_sql_query(sql=stores_56, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "1e6ec7f2-c00b-436e-9ca0-fb1b66a53123", "metadata": {}, "outputs": [], "source": ["stores_56df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "68cc5f72-f79d-4a63-bc9b-3b28bc8175ae", "metadata": {}, "outputs": [], "source": ["stores_56df[\"week_start\"] = stores_56df[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "stores_56df[\"week_end\"] = stores_56df[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "stores_56df[\"week\"] = (\n", "    stores_56df[\"week_start\"].astype(str) + \" to \" + stores_56df[\"week_end\"].astype(str)\n", ")\n", "stores_56df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8252007e-56d6-4a55-bd57-5ee3649b2c9d", "metadata": {}, "outputs": [], "source": ["stores_23 = \"\"\"\n", "select date_ts,\n", "       count(distinct case when emp>=2 then outlet_id end) as outlet_with_more_than_two_punch_in\n", "from\n", "(select date(end_time) as date_ts,\n", "      outlet_id,\n", "       count(distinct employee_id) as emp\n", "from\n", "((select employee_id,\n", "       outlet_id,\n", "       (end_timestamp+interval '330 minute') as end_time\n", "from lake_warehouse_location.user_attendance\n", "where date(start_timestamp+interval '330 minute') between current_date-21 and current_date\n", "and (extract(hour from end_timestamp+interval '330 minute')>=23 and extract(minute from end_timestamp+interval '330 minute')>=30)\n", "and type in(1,2)\n", ")\n", "union\n", "(select\n", "employee_id,\n", "cast(site_id as int) as outlet_id,\n", "(shift_end_time+interval '330 minute') as end_time\n", "from lake_ops_management.shift\n", "where date(shift_end_time+interval '330 minute') between current_date-21 and current_date\n", "and (extract(hour from shift_end_time+interval '330 minute')>=23 and extract(minute from shift_end_time+interval '330 minute')>=30)\n", "and shift_end_method = 'MANUAL'\n", "))\n", "group by 1,2)\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3b5f4c2d-b7ce-4536-9b41-c0edb11c646d", "metadata": {}, "outputs": [], "source": ["stores_23df = pd.read_sql_query(sql=stores_23, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "67f8cefb-0e7f-479e-a0d5-e8de8e01ed0f", "metadata": {}, "outputs": [], "source": ["stores_23df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9eb9f645-87a3-4808-b3c6-a6c3a2cb0632", "metadata": {}, "outputs": [], "source": ["stores_23df[\"week_start\"] = stores_23df[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "stores_23df[\"week_end\"] = stores_23df[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "stores_23df[\"week\"] = (\n", "    stores_23df[\"week_start\"].astype(str) + \" to \" + stores_23df[\"week_end\"].astype(str)\n", ")\n", "stores_23df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "045a60d8-3235-4c13-a581-7597d5cb4010", "metadata": {}, "outputs": [], "source": ["orders_55 = \"\"\"\n", "select\n", "date(date) as date_ts,\n", "employee_id,\n", "total_orders_picked\n", "from metrics.instore_manpower_performance\n", "where (date(date) between current_date-21 and current_date) and score > 0\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "55fe310a-326e-42e7-9de0-b854aec4c03a", "metadata": {}, "outputs": [], "source": ["orders_55df = pd.read_sql_query(sql=orders_55, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "5d97c994-c716-4751-9f29-f0ed1fcbe871", "metadata": {}, "outputs": [], "source": ["orders_55df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "994d5b21-0968-4619-b253-64d1b09682cf", "metadata": {}, "outputs": [], "source": ["orders_55df[\"week_start\"] = orders_55df[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "orders_55df[\"week_end\"] = orders_55df[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "orders_55df[\"week\"] = (\n", "    orders_55df[\"week_start\"].astype(str) + \" to \" + orders_55df[\"week_end\"].astype(str)\n", ")\n", "orders_55df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0b05d912-d93f-4bad-bdeb-d07c3ccb7c7d", "metadata": {}, "outputs": [], "source": ["pickers_20 = \"\"\"\n", "select date(date),\n", "       employee_id,\n", "       picking_time,\n", "       total_orders_picked\n", "    from metrics.instore_manpower_performance\n", "    where date(date) between current_date-21 and current_date\n", "    and total_picked_quantity > 0\n", "    and picking_time is not null\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "67bcbcf0-3e93-4c25-9d5a-8ffed7437bfc", "metadata": {}, "outputs": [], "source": ["pickers_20df = pd.read_sql_query(sql=pickers_20, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "727478e6-a3fc-43d1-9587-de60fa71e4a1", "metadata": {}, "outputs": [], "source": ["pickers_20df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "90a4cae3-5cb8-49cf-98da-80305107afcc", "metadata": {}, "outputs": [], "source": ["pickers_20df[\"week_start\"] = pickers_20df[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "pickers_20df[\"week_end\"] = pickers_20df[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "pickers_20df[\"week\"] = (\n", "    pickers_20df[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + pickers_20df[\"week_end\"].astype(str)\n", ")\n", "pickers_20df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "81c05f83-9105-4d50-a902-bd8f97ad0c7e", "metadata": {}, "outputs": [], "source": ["complaints_sql = \"\"\"\n", "WITH complaints AS\n", "  (SELECT date(delivery_date) AS date,\n", "          outlet_id,\n", "          outlet_name AS \"outlet::filter\",\n", "          order_id,\n", "          suborder_id,\n", "          product_id,\n", "          product_name,\n", "          complaint_category,\n", "          complaint_source,\n", "          employee_id,\n", "          fe_name\n", "   FROM metrics.complaints\n", "   WHERE (date(delivery_date) BETWEEN current_date-21 AND current_date) )\n", "SELECT date(order_checkout_ts_ist) AS date,\n", "       o.outlet_id,\n", "       picker_id,\n", "       count(DISTINCT o.suborder_id) AS total_orders,\n", "       count(DISTINCT case when complaint_category in ('ITEM_MISSING_LM','DIFFERENT_ITEMS_RECEIVED','WRONG_ITEMS','ITEMS_MISSING') then c.suborder_id end) AS complaint_orders,\n", "       (count(DISTINCT case when complaint_category in ('ITEM_MISSING_LM','DIFFERENT_ITEMS_RECEIVED','WRONG_ITEMS','ITEMS_MISSING') then c.suborder_id end)*100.0/count(DISTINCT o.suborder_id)) AS \"%%complaints%%\"\n", "FROM dwh.fact_supply_chain_order_details o\n", "left JOIN complaints c ON c.suborder_id = o.suborder_id\n", "left JOIN lake_retail.warehouse_user u ON u.emp_id = o.picker_id\n", "WHERE (date(order_checkout_ts_ist) BETWEEN current_date-21 AND current_date)\n", "and picker_id is not null\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e8657876-5b45-4be7-bf6f-2222d00bcd84", "metadata": {}, "outputs": [], "source": ["complaints_sqldf = pd.read_sql_query(sql=complaints_sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "aadeb718-e311-4452-83b2-36750c645508", "metadata": {}, "outputs": [], "source": ["complaints_sqldf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9ec49256-99b2-43de-be26-5c46436c3d0c", "metadata": {}, "outputs": [], "source": ["complaints_sqldf[\"week_start\"] = complaints_sqldf[\"date\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "complaints_sqldf[\"week_end\"] = complaints_sqldf[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "complaints_sqldf[\"week\"] = (\n", "    complaints_sqldf[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + complaints_sqldf[\"week_end\"].astype(str)\n", ")\n", "complaints_sqldf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fd3f56a6-59d9-4f03-9e7f-f007cd399c57", "metadata": {}, "outputs": [], "source": ["picker_aging = \"\"\"\n", "with base as\n", "(\n", "SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       outlet_id,\n", "       trim(location) as city_name,\n", "       o.order_id,\n", "       suborder_id,\n", "       picker_id,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) as instore_time\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id and c.business_type_id = 7\n", "WHERE\n", "  date(order_checkout_ts_ist) between current_date-21 and current_date\n", "  AND is_order_delivered = true\n", "  group by 1,2,3,4,5,6,7,8),\n", "--   base2 as (\n", "--       select  distinct outlet_id,\n", "--           (PERCENTILE_DISC(75*1.0/100) within group (order by instore_time) over(partition by outlet_id))/60.0 as \"75th_instore_time_in_min\"\n", "--           from base),\n", "  base1 as\n", "  (select --outlet_id,\n", "          date_ts,\n", "          count(distinct case when ((date(date_ts) - first_picking_date) < 30) then picker_id end) as pickers_with_less_than_1_months\n", "        from\n", "  (select a.picker_id,\n", "          a.outlet_id,\n", "          (base.date) as date_ts,\n", "           min(date(a.order_picking_started_ts_ist)) as first_picking_date\n", "        from dwh.fact_supply_chain_order_details a\n", "        inner join base on a.picker_id = base.picker_id\n", "        where a.outlet_id<>0 and a.picker_id <> ' '\n", "        group by 1,2,3\n", "        having (min(date(order_picking_started_ts_ist))) is not null\n", "        )\n", "        group by 1\n", "  ),\n", "  base3 as\n", "  (select base1.date_ts,\n", "          base1.pickers_with_less_than_1_months,\n", "          count(distinct picker_id) as total_pickers\n", "        from\n", "        base\n", "        inner join base1 on base.date = base1.date_ts\n", "        group by 1,2\n", "  )\n", "  select * from base3\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3b522fbd-4b2f-4002-b3f5-b5736eb93f64", "metadata": {}, "outputs": [], "source": ["picker_agingdf = pd.read_sql_query(sql=picker_aging, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "224e50e1-925c-4560-9c31-6532c37fb402", "metadata": {}, "outputs": [], "source": ["picker_agingdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ac1306f2-0aed-4bec-bcd1-93eb69951844", "metadata": {}, "outputs": [], "source": ["picker_agingdf[\"week_start\"] = picker_agingdf[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "picker_agingdf[\"week_end\"] = picker_agingdf[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")\n", "\n", "picker_agingdf[\"week\"] = (\n", "    picker_agingdf[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + picker_agingdf[\"week_end\"].astype(str)\n", ")\n", "picker_agingdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "46b7f23b-3f44-4338-bfc5-86cd9debe7b3", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    new_store_empdf,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"new_emp\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad42539b-2367-4b2a-84c7-556708bd08ec", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    manual_logoutdf,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"manual_logout\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "30dfb630-f2f3-4637-bdde-1baab64bf5ce", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    stores_56df,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"stores_56\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "46c91313-4335-48bb-a4e6-60f62797bafb", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    stores_23df,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"stores_23\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "651cabd2-e8ff-41b2-bc2f-68120b6a9aad", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_55df,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"orders_55\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "87afdab8-344f-47cd-bd92-3feef4eb31bd", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    pickers_20df,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"pickers_20\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d5815cdd-fc83-4bc5-b048-d679a1023070", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    complaints_sqldf,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"complaints\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "88d142ce-5c86-40e3-a006-73964e736eed", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    picker_agingdf,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"picker_aging\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d1b6f6a7-050c-4dab-a500-186b92f9bbe2", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    manpower_more_than_9df,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"more_than_9\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65818062-ef72-4106-8b8b-4daa199c69f7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}