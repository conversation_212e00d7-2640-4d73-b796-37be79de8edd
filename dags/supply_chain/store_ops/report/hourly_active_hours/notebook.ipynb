{"cells": [{"cell_type": "code", "execution_count": null, "id": "e01de5a2-6b03-441b-a934-becb203f91f5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta, date"]}, {"cell_type": "code", "execution_count": null, "id": "de28b880-cbd9-44cd-9e63-1a727870b338", "metadata": {}, "outputs": [], "source": ["datetime.now()"]}, {"cell_type": "code", "execution_count": null, "id": "b2522840-755a-4c98-8eb2-c70a1abb9d41", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "31532db3-403f-47cf-ade1-99d765aa9bc2", "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "SELECT \n", "site_id as outlet_id,\n", "b.name as outlet_name,\n", "case when trim(b.location) in ('kanpur','kanpur') then 'Kanpur'\n", "when trim(b.location) in ('Bangalore') then 'Bengaluru'\n", "else trim(b.location) end as city,\n", "date(role_start_time+interval '330' minute) as date,\n", "role,\n", "employee_id,\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 0 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 0 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 0 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 0 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 0 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 0 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 0 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 0 \n", "                    then 60 else 0 end) as \"0\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 1 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 1 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 1 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 1 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 1 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 1 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 1 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 1 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 1 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"1\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 2 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 2 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 2 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 2 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 2 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 2 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 2 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 2 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 2 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"2\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 3 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 3 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 3 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 2 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 3 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 3 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 3 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 3 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 3 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60        \n", "                    else 0 end) as \"3\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 4 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 4 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 4 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 4 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 4 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 4 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 4 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 4 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 4 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"4\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 5 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 5 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 5 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 5 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 5 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 5 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 5 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 5 )\n", "                    then 60 \n", "                  when extract(hour from (role_start_time + interval '330' minute)) < 5 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"5\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 6 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 6 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 6 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 6 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 6 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 6 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 6 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 6 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 6 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"6\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 7 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 7 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 7 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 7 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 7 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 7 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 7 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 7 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 7 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"7\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 8 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 8 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 8 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 8 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 8 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 8 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 8 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 8 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 8 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"8\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 9 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 9 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 9 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 9 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 9 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 9 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 9 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 9 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '330' minute)) < 9 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"9\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 10 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 10 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 10 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 10 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 10 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 10 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 10 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 10 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '330' minute)) < 10 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"10\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 11 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 11 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 11 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 11 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 11 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 11 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 11 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 11 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 11 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"11\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 12 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 12 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 12 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 12 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 12 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 12 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 12 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 12 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 12 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"12\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 13 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 13 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 13 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 13 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 13 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 13 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 13 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 13 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 13 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"13\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 14 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 14 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 14 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 14 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 14 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 14 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 14 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 14 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 14 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"14\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 15 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 15 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 15 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 15 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 15 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 15 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 15 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 15 )\n", "                    then 60 \n", "                   when extract(hour from (role_start_time + interval '330' minute)) < 15 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"15\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 16 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 16\n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 16 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 16 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 16 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 16 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 16 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 16 )\n", "                    then 60 \n", "                 when extract(hour from (role_start_time + interval '330' minute)) < 16 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"16\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 17 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 17 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 17 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 17 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 17 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 17 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 17 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 17 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 17 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"17\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 18 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 18 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 18 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 18 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 18 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 18 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 18 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 18 )\n", "                    then 60\n", "               when extract(hour from (role_start_time + interval '330' minute)) < 18 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"18\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 19 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 19 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 19 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 19 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 19 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 19 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 19 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 19 )\n", "                    then 60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 19 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                     else 0 end) as \"19\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 20 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 20 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 20 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 20 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 20 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 20 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 20 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 20 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 20 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"20\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 21 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 21 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 21 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 21 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 21 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 21 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 21 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 21 )\n", "                    then 60 \n", "            when extract(hour from (role_start_time + interval '330' minute)) < 21 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                   else 0 end) as \"21\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 22 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 22 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 22 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 22 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 22 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 22 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 22 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 22 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 22 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                   else 0 end) as \"22\",\n", "        sum (case\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 23 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 23 \n", "                    then date_diff('second',role_start_time,coalesce(role_end_time,current_timestamp))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) = 23 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 23 \n", "                    then date_diff('second',(role_start_time + interval '330' minute),DATE_ADD('minute', (60 - cast(extract(minute from (role_start_time + interval '330' minute)) as int)), (role_start_time + interval '330' minute)))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 23 and extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) = 23 \n", "                    then date_diff('second',DATE_ADD('minute', (extract(minute from (coalesce(role_end_time,current_timestamp) + interval '330' minute)))*-1, (coalesce(role_end_time,current_timestamp) + interval '330' minute)),(coalesce(role_end_time,current_timestamp) + interval '330' minute))/60\n", "            when extract(hour from (role_start_time + interval '330' minute)) < 23 and (extract(hour from (coalesce(role_end_time,current_timestamp) + interval '330' minute)) > 23 )\n", "                    then 60 \n", "                    when extract(hour from (role_start_time + interval '330' minute)) < 23 and date(coalesce(role_end_time,current_timestamp)+interval '330' minute)>=cast((date(role_start_time+interval '330' minute)+interval '1' day) as date) then 60\n", "                    else 0 end) as \"23\"\n", "   FROM lake_view_ops_management.shift_roles a\n", "   inner join lake_retail.console_outlet b on cast(a.site_id as int) = cast(b.id as int)\n", "   where date(role_start_time+ interval '330' minute) in (current_date, current_date-interval '1' day, current_date-interval '7' day, current_date-interval '14' day, current_date-interval '21' day)\n", "    and cast(coalesce(role_end_time,current_timestamp) as timestamp) >= cast(role_start_time as timestamp)\n", "   ---and role='PICKER'\n", "   GROUP BY 1,2,3,4,5,6\n", "\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "\n", "active_time_data = pd.read_sql(q2, presto)"]}, {"cell_type": "code", "execution_count": null, "id": "b22474e8-25dd-46d5-8662-5b7570f27292", "metadata": {}, "outputs": [], "source": ["active_time_data_melt = pd.melt(\n", "    active_time_data,\n", "    id_vars=[\"outlet_id\", \"outlet_name\", \"city\", \"date\", \"employee_id\", \"role\"],\n", "    value_vars=[\n", "        \"0\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "    ],\n", "    var_name=\"hour\",\n", "    value_name=\"active_time\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "28e0de62-bc3f-4493-91be-4ce9e5e4b0dc", "metadata": {}, "outputs": [], "source": ["active_time_data_melt.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0aa10cb9-de41-468c-a3c9-d553335bdd42", "metadata": {}, "outputs": [], "source": ["active_time_data_melt_group = (\n", "    active_time_data_melt[active_time_data_melt[\"active_time\"] > 0]\n", "    .groupby([\"date\", \"outlet_id\", \"outlet_name\", \"city\", \"hour\", \"role\"])\n", "    .agg({\"employee_id\": \"nunique\", \"active_time\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2a8e5451-aa3b-4315-9fbe-706271cd8ebf", "metadata": {}, "outputs": [], "source": ["active_time_data_melt_group[\"active_time\"] = (\n", "    active_time_data_melt_group[\"active_time\"] / 60\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "caada275-5596-4400-b7bb-4baf29f96018", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    active_time_data_melt_group,\n", "    \"1O4tMrX9KDAEVrH6q_iAp5sZ1Dmj1wwrguUbGBP_Hmv8\",\n", "    \"raw\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "534d7f45-b725-4f48-9389-874400ee386b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}