{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import os\n", "import numpy as np\n", "import calendar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import calendar\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto_con = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_query = \"\"\"\n", " with\n", "\n", "fc_mapping AS \n", "(\n", "    SELECT \n", "        PCO.FACILITY_ID,\n", "        CF.NAME AS FACILITY_NAME,\n", "        BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,\n", "        --B<PERSON><PERSON>NAME AS BACKEND_MERCHANT_NAME,\n", "        PCO.ID AS OUTLET_ID,\n", "        PCO.NAME AS OUTLET_NAME\n", "    FROM lake_view_oms_bifrost.oms_merchant AS BOM\n", "    INNER join  lake_retail.console_outlet_cms_store  AS CMS\n", "        ON BOM.EXTERNAL_ID = CMS.CMS_STORE \n", "        AND CMS.ACTIVE = 1 \n", "        AND CMS.CMS_UPDATE_ACTIVE = 1\n", "        --AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "    INNER join  lake_retail.console_outlet  AS PCO\n", "        ON CMS.OUTLET_ID = PCO.ID and business_type_id = 7\n", "    INNER join  lake_crates.facility  AS CF \n", "        ON CF.ID = PCO.FACILITY_ID\n", "    GROUP BY 1,2,3,4,5\n", "),\n", "\n", "base as (\n", "     select city_name,\n", "        emp_id,\n", "        outlet_id,\n", "        outlet_name,\n", "        role,\n", "        employee_name,\n", "        date_ts\n", "    from\n", " (\n", " (SELECT \n", "        replace(LOCATION,' ','') AS city_name,\n", "        employee_id as emp_id,\n", "        outlet_id,\n", "        d.name as outlet_name,\n", "        c.name as role,\n", "        b.name AS employee_name,\n", "        date(start_timestamp + interval '330' MINUTE) AS date_ts\n", "    FROM \n", "        lake_view_warehouse_location.user_attendance \n", "            AS a\n", "    INNER JOIN \n", "        lake_view_retail.warehouse_user \n", "            AS b \n", "            ON lower(a.employee_id) = lower(b.emp_id)\n", "    INNER JOIN\n", "        lake_view_retail.warehouse_user_role \n", "            AS c \n", "            ON b.role_id = c.id\n", "            and c.name in ('<PERSON>er', 'Putter','<PERSON><PERSON>','<PERSON>er','Helper','On roll','Packer','F&V Executive',\n", "            'Supervisor','Admin')\n", "    INNER JOIN \n", "        lake_retail.console_outlet \n", "            AS d \n", "            ON d.id = a.outlet_id and business_type_id = 7\n", "    WHERE \n", "        date(start_timestamp + interval '330' MINUTE) >=  (cast(current_date as date)-interval '30' day) \n", "        AND date(start_timestamp + interval '330' MINUTE) <=  (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7)\n", "\n", "union all\n", "\n", " (SELECT \n", "        replace(LOCATION,' ','') AS city_name,\n", "        employee_id as emp_id,\n", "        cast(a.site_id as int) as outlet_id,\n", "        d.name as outlet_name,\n", "        c.name as role,\n", "        b.name AS employee_name,\n", "        date(shift_start_time + interval '330' MINUTE) AS date_ts\n", "    FROM \n", "        lake_view_ops_management.shift\n", "            AS a\n", "    INNER JOIN \n", "        lake_view_retail.warehouse_user \n", "            AS b \n", "            ON lower(a.employee_id) = lower(b.emp_id)\n", "    INNER JOIN\n", "        lake_view_retail.warehouse_user_role \n", "            AS c \n", "            ON b.role_id = c.id\n", "            and c.name in ('<PERSON>er', 'Putter','<PERSON><PERSON>','<PERSON>er','Helper','On roll','Packer','F&V Executive',\n", "            'Supervisor','Admin')\n", "    INNER JOIN \n", "        lake_retail.console_outlet \n", "            AS d \n", "            ON d.id = cast(a.site_id as int) and business_type_id = 7\n", "    WHERE \n", "        date(shift_start_time + interval '330' MINUTE) >=  (cast(current_date as date)-interval '30' day) \n", "        AND date(shift_start_time + interval '330' MINUTE) <=  (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7)\n", ")\n", "group by 1,2,3,4,5,6,7\n", "\n", ")\n", "\n", "SELECT \n", "    b.date_ts,\n", "   (Case when b.city_name='Bangalore' Then 'Bengaluru'\n", "    when b.city_name = 'Gurgaon' then 'HR-NCR'\n", "    when b.city_name = 'Ghaziabad' then 'UP-NCR'\n", "    when b.city_name = 'kanpur' then 'Kanpur'\n", "    when b.city_name = 'Noida' then 'UP-NCR'\n", "    when b.city_name = 'Vadodara' then 'Ahmedabad'\n", "    else b.city_name END) as city,\n", "    b.outlet_id as outlet_id,\n", "    b.outlet_name as outlet_name,\n", "    count(distinct b.emp_id) as manpower\n", "FROM \n", "    base\n", "        as b\n", "GROUP BY 1,2,3,4\n", "\"\"\"\n", "df = pd.read_sql(sql=sql_query, con=presto_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_churn = \"\"\"\n", "\n", "WITH base as (\n", "    SELECT \n", "        replace(LOCATION,' ','') AS city_name,\n", "        employee_id as emp_id,\n", "        date(start_timestamp + interval '330' MINUTE) AS date_ts,\n", "        outlet_id,\n", "        d.name,\n", "        c.name as role,\n", "        b.name AS employee_name\n", "    FROM \n", "        lake_view_warehouse_location.user_attendance \n", "            AS a\n", "    INNER JOIN \n", "        lake_view_retail.warehouse_user \n", "            AS b \n", "            ON lower(a.employee_id) = lower(b.emp_id)\n", "    INNER JOIN\n", "        lake_view_retail.warehouse_user_role \n", "            AS c \n", "            ON b.role_id = c.id\n", "            and c.name in ('<PERSON><PERSON>','Captain', 'Putter','<PERSON><PERSON>','<PERSON>er','Helper','On roll','Packer','Supervisor','Admin','F&V Executive')\n", "    INNER JOIN \n", "        lake_retail.console_outlet \n", "            AS d \n", "            ON d.id = a.outlet_id and business_type_id = 7\n", "    WHERE \n", "        date(start_timestamp + interval '330' MINUTE) >=  (cast(current_date as date)-interval '30' day)\n", "        AND date(start_timestamp + interval '330' MINUTE) <=  (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7\n", "),\n", "\n", "base1 as\n", "(\n", "    SELECT \n", "        replace(LOCATION,' ','') AS city_name,\n", "        employee_id as emp_id,\n", "        date(shift_start_time + interval '330' MINUTE) AS date_ts,\n", "        cast(a.site_id as int) outlet_id,\n", "        d.name,\n", "        c.name as role,\n", "        b.name AS employee_name\n", "    FROM \n", "        lake_view_ops_management.shift \n", "            AS a\n", "    INNER JOIN \n", "        lake_view_retail.warehouse_user \n", "            AS b \n", "            ON lower(a.employee_id) = lower(b.emp_id)\n", "    INNER JOIN\n", "        lake_view_retail.warehouse_user_role \n", "            AS c \n", "            ON b.role_id = c.id\n", "            and c.name in ('<PERSON><PERSON>','Captain', 'Putter','<PERSON><PERSON>','<PERSON>er','Helper','On roll','Packer','Supervisor','Admin','F&V Executive')\n", "    INNER JOIN \n", "        lake_retail.console_outlet \n", "            AS d \n", "            ON d.id = cast(a.site_id as int) and business_type_id = 7\n", "    WHERE \n", "        date(shift_start_time + interval '330' MINUTE) >=  (cast(current_date as date)-interval '30' day)\n", "        AND date(shift_start_time + interval '330' MINUTE) <=  (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7\n", "),\n", "final_base as\n", "(\n", "select * from base\n", "union all\n", "select * from base1\n", ")\n", "\n", "SELECT \n", "    city_name as city,\n", "    emp_id,\n", "    outlet_id,\n", "    name,\n", "    role,\n", "    employee_name,\n", "    max(date_ts) as max_working_date,\n", "    cast(max(date_ts) as date) + interval '5' DAY as date_ts\n", "\n", "FROM \n", "    final_base\n", "GROUP BY 1,2,3,4,5,6\n", "\n", "\n", "\"\"\"\n", "\n", "df_churn = pd.read_sql(sql=sql_churn, con=presto_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn[\n", "    df_churn[\"date_ts\"]\n", "    <= (datetime.now() + timed<PERSON>ta(hours=5, minutes=30)).strftime(\"%Y-%m-%d\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn = (\n", "    df_churn[\n", "        df_churn[\"date_ts\"]\n", "        <= (datetime.now() + timed<PERSON>ta(hours=5, minutes=30)).strftime(\"%Y-%m-%d\")\n", "    ]\n", "    .groupby([\"outlet_id\", \"date_ts\"])[\"emp_id\"]\n", "    .nunique()\n", "    .reset_index(name=\"inactive\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f = df.merge(df_churn, on=[\"outlet_id\", \"date_ts\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f[\"inactive\"] = df_churn_f[\"inactive\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql2 = \"\"\"\n", "WITH base AS\n", "  (SELECT replace(LOCATION,' ','') AS city_name,\n", "          employee_id AS emp_id,\n", "          outlet_id,\n", "          d.name AS outlet_name,\n", "          c.name AS ROLE,\n", "          b.name AS employee_name,\n", "          date(start_timestamp + interval '330' MINUTE) AS date_ts\n", "   FROM lake_view_warehouse_location.user_attendance AS a\n", "   INNER JOIN lake_view_retail.warehouse_user AS b ON lower(a.employee_id) = lower(b.emp_id)\n", "   INNER JOIN lake_view_retail.warehouse_user_role AS c ON b.role_id = c.id\n", "   AND c.name IN ('Picker',\n", "                  'Captain',\n", "                  'Putter',\n", "                  '<PERSON><PERSON>',\n", "                  'Packer',\n", "                  'Helper',\n", "                  'On roll',\n", "                  'Packer',\n", "                  'Supervisor',\n", "                  'Admin',\n", "                  'F&V Executive')\n", "   INNER JOIN lake_retail.console_outlet AS d ON d.id = a.outlet_id\n", "   AND business_type_id = 7\n", "   WHERE date(start_timestamp + interval '330' MINUTE) >= (cast(CURRENT_DATE AS date)-interval '30' DAY)\n", "     AND date(start_timestamp + interval '330' MINUTE) <= (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7),\n", "     base1 AS\n", "  (SELECT replace(LOCATION,' ','') AS city_name,\n", "          employee_id AS emp_id,\n", "          cast(site_id AS int) AS outlet_id,\n", "          d.name AS outlet_name,\n", "          c.name AS ROLE,\n", "          b.name AS employee_name,\n", "          date(shift_start_time + interval '330' MINUTE) AS date_ts\n", "   FROM lake_view_ops_management.shift AS a\n", "   INNER JOIN lake_view_retail.warehouse_user AS b ON lower(a.employee_id) = lower(b.emp_id)\n", "   INNER JOIN lake_view_retail.warehouse_user_role AS c ON b.role_id = c.id\n", "   AND c.name IN ('Picker',\n", "                  'Captain',\n", "                  'Putter',\n", "                  '<PERSON><PERSON>',\n", "                  'Packer',\n", "                  'Helper',\n", "                  'On roll',\n", "                  'Packer',\n", "                  'Supervisor',\n", "                  'Admin',\n", "                  'F&V Executive')\n", "   INNER JOIN lake_retail.console_outlet AS d ON d.id = cast(site_id AS int)\n", "   AND business_type_id = 7\n", "   WHERE date(shift_start_time + interval '330' MINUTE) >= (cast(CURRENT_DATE AS date)-interval '30' DAY)\n", "     AND date(shift_start_time + interval '330' MINUTE) <= (cast(CURRENT_DATE AS date)-interval '1' DAY)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7),\n", "     final_base AS\n", "  (SELECT city_name,\n", "          emp_id,\n", "          outlet_id,\n", "          outlet_name,\n", "          ROLE,\n", "          employee_name,\n", "          date_ts,\n", "        LEAD (date_ts,1) OVER (PARTITION BY city_name,outlet_id,outlet_name,ROLE,emp_id,employee_name ORDER BY date_ts) AS next_present_date\n", "   FROM\n", "     (SELECT *\n", "      FROM base\n", "      UNION ALL \n", "      SELECT *\n", "      FROM base1)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7)\n", "SELECT outlet_id,\n", "       emp_id,\n", "       outlet_name,\n", "       date_ts,\n", "       ROLE,\n", "       next_present_date,\n", "       CASE\n", "           WHEN next_present_date IS NOT NULL\n", "                AND next_present_date > cast(date_ts AS date) + interval '1' DAY THEN cast(date_ts AS date) + INTERVAL '1' DAY\n", "           WHEN next_present_date IS NULL\n", "                AND date_ts >= cast(CURRENT_DATE AS date) - interval '4' DAY THEN cast(date_ts AS date) + interval '1' DAY\n", "           ELSE NULL\n", "       END AS absent_from,\n", "       CASE\n", "           WHEN next_present_date IS NOT NULL\n", "                AND next_present_date > cast(date_ts AS date) + interval '1' DAY THEN cast(next_present_date AS date) - INTERVAL '1' DAY\n", "           WHEN next_present_date IS NULL\n", "                AND date_ts >= cast(CURRENT_DATE AS date) - interval '4' DAY THEN cast(CURRENT_DATE AS date)\n", "           ELSE NULL\n", "       END AS absent_till\n", "FROM final_base\n", "\"\"\"\n", "df1 = pd.read_sql(sql=sql2, con=presto_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[df1[\"next_present_date\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[(df1[\"absent_from\"].notnull()) & (df1[\"absent_till\"].notnull())].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = df1[(df1[\"absent_from\"].notnull()) & (df1[\"absent_till\"].notnull())].reset_index(\n", "    drop=True\n", ")\n", "\n", "df1[\"absent_list\"] = None\n", "for index, row in df1.iterrows():\n", "    temp_list = []\n", "    if (\n", "        pd.to_datetime(row[\"absent_till\"]) - pd.to_datetime(row[\"absent_from\"])\n", "    ).days < 4:\n", "        for i in range(\n", "            (\n", "                pd.to_datetime(row[\"absent_till\"]) - pd.to_datetime(row[\"absent_from\"])\n", "            ).days\n", "            + 1\n", "        ):\n", "            temp_date = pd.to_datetime(row[\"absent_from\"]) + timed<PERSON>ta(days=i)\n", "            temp_list.append(temp_date.strftime(\"%Y-%m-%d\"))\n", "        # print(temp_list)\n", "        df1.at[index, \"absent_list\"] = temp_list\n", "df1 = df1[df1[\"absent_list\"].notnull()].reset_index(drop=True)\n", "df1 = (\n", "    df1[[\"outlet_id\", \"emp_id\", \"outlet_name\", \"absent_list\"]]\n", "    .explode(\"absent_list\")\n", "    .reset_index(drop=True)\n", ")\n", "df1.rename(columns={\"absent_list\": \"date_ts\"}, inplace=True)\n", "df1 = (\n", "    df1.groupby([\"outlet_id\", \"date_ts\"])[\"emp_id\"].nunique().reset_index(name=\"absent\")\n", ")\n", "df1.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f = df_churn_f.merge(df1, on=[\"outlet_id\", \"date_ts\"], how=\"left\")\n", "df_churn_f.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f[\"absent\"] = df_churn_f[\"absent\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f[\"date_ts\"] = pd.to_datetime(df_churn_f[\"date_ts\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f[\"week_start\"] = df_churn_f[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "df_churn_f[\"week_end\"] = df_churn_f[\"week_start\"].apply(lambda x: x + <PERSON><PERSON>ta(days=6))\n", "\n", "df_churn_f[\"week\"] = (\n", "    df_churn_f[\"week_start\"].astype(str) + \" to \" + df_churn_f[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f[df_churn_f[\"outlet_id\"] == 3894]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_churn_f.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_churn_f,\n", "    \"15_ygMZ0WfNhgadvv8DiwJTa4hFUDPFEeIB-C5RF8p5k\",\n", "    \"absentism_attrition\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}