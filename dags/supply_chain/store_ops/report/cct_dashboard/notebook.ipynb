{"cells": [{"cell_type": "code", "execution_count": null, "id": "a814e001-cee3-4485-8131-0c0687708642", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "dcfb69f9-84a0-452d-8a34-4c28127e00c7", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "ed354634-0d7d-42dc-a7dc-1c0f8a5102d9", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "with daily_dau as\n", "(select \n", "        \n", "        dwbm.pos_outlet_id as outlet_id,\n", "        date(order_scheduled_date_ist) as date_ts,\n", "        avg(count_dau) as dau\n", "        \n", "    from \n", "        dwh.tableau_hourly_frontend_merchant_metrics o\n", "        inner join dwh.dim_merchant_outlet_facility_mapping dwbm on o.frontend_merchant_id = dwbm.frontend_merchant_id\n", "        WHERE\n", "        \n", "         valid_to_utc >= current_date\n", "            and is_current = True\n", "            and is_mapping_enabled = True and merchant_business_type_id=7\n", "            \n", "            and agg_type='Avg' and date(order_scheduled_date_ist) between current_date-34 and current_date\n", "    group by 1,2),\n", "\n", "\n", "base as\n", "(select date,\n", "       outlet_id  as \"Store ID\",\n", "       outlet_name as \"Store Name\",\n", "       (case when upper(city_name) in ('GHAZIABAD','NOIDA') then 'UP-NCR'\n", "      when upper(city_name) = 'BANGALORE' then 'BENGALURU'\n", "      ELSE upper(city_name) end) as \"City\",\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct case when order_current_status = 'DELIVERED' then order_id end) as \"Delivered Orders\",\n", "       count(distinct picker_id) as \"Pickers\",\n", "       count(distinct partner_id) as \"Riders\",\n", "       round(sum(IPO)/count(distinct order_id),0) as \"Avg IPO\",\n", "       count(distinct case when slot_charge_flag='Slot_charge' then order_id end) as \"Orders with Surge\",\n", "       round(count(distinct Case when delivery_time <= 900 and order_current_status = 'DELIVERED' then order_id end)) as \"orders within 15 min\",\n", "       round((sum(instore_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Instore Time\",\n", "       round(count(distinct Case when instore_time <= 150 then order_id end)) as \"orders within 2.5 Min\",\n", "       round((sum(checkout_to_picker_assigned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Assigned Time\",\n", "       round(count(distinct Case when checkout_to_picker_assigned <= 30 then order_id end)) as \"orders within 30sec\",\n", "       round((sum(picking_time)/60.0)/count(distinct order_id),2) as \"Avg Picking Time\",\n", "       round(count(distinct Case when picking_time <= 90 then order_id end)) as \"orders within 1.5Min\",\n", "       round(count(distinct Case when picking_time <= 60 and IPO <=4 then order_id end)) as \"orders with IPO 4 within 1 Min\",\n", "       count(distinct case when IPO<=4 then order_id end) as \"Orders with IPO 4\",\n", "       round((sum(picking_complete_to_order_scanned)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Picking Complete Order Scan\",\n", "       round((sum(delivery_time)/60.0)/nullif(count(distinct order_id),0),2) as \"Avg Delivery Time\"\n", "       \n", "from\n", "(\n", "SELECT date(order_checkout_ts_ist) as date,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       o.suborder_id,\n", "       o.cart_id,\n", "       o.order_id,\n", "       o.picker_id,\n", "       o.partner_id,\n", "       o.total_items_quantity_ordered as IPO,\n", "       o.order_current_status,\n", "       case when oo.slot_charges>0 then 'Slot_charge'\n", "       end as slot_charge_flag,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) as instore_time,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "       DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) as picking_time,\n", "       DATEDIFF(seconds,order_picking_completed_ts_ist,order_scanned_ts_ist) as picking_complete_to_order_scanned,\n", "       DATEDIFF(seconds,order_scanned_ts_ist,order_enroute_ts_ist) as order_scanned_to_enroute,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) as delivery_time\n", "FROM dwh.fact_supply_chain_order_details o\n", "left join dwh.fact_sales_order_details oo on oo.order_id = o.order_id and date(o.order_checkout_ts_ist) = date(oo.cart_checkout_ts_ist)\n", "and date(cart_checkout_ts_ist) BETWEEN CURRENT_DATE-34 AND CURRENT_DATE\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id\n", "WHERE \n", "  date(o.order_checkout_ts_ist) BETWEEN CURRENT_DATE-34 AND CURRENT_DATE\n", "and is_rescheduled=false \n", "\n", "and order_picking_completed_ts_ist is not null and \n", "o.order_type='super_express'\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18\n", ")\n", "group by 1,2,3,4\n", "order by 1 desc\n", "),\n", "\n", "required_manpower as\n", "(select outlet,outlet_name,date,\n", "max(case when hour in (8,9,10,11,17,18,19,20) then picker_req else null end) as peak_req,\n", "max(case when hour not in (8,9,10,11,17,18,19,20) then picker_req else null end) as non_peak_req\n", "from metrics.instore_manpower_requirement\n", "where date = current_date\n", "group by 1,2,3\n", ")\n", "\n", "\n", "select a.date,\n", "       a.\"Store ID\",\n", "       a.\"Store Name\",\n", "       a.\"City\",\n", "       b.dau,\n", "       a.\"Orders\",\n", "       \"Pickers\",\n", "       peak_req,\n", "       non_peak_req,\n", "       \"Riders\",\n", "       \"Avg IPO\",\n", "       \"Orders with Surge\",\n", "       \"orders within 15 min\",\n", "       \"Avg Instore Time\",\n", "       \"orders within 2.5 Min\",\n", "       \"Avg Assigned Time\",\n", "       \"orders within 30sec\",\n", "       \"Avg Picking Time\",\n", "       \"orders within 1.5Min\",\n", "       \"orders with IPO 4 within 1 Min\",\n", "       \"Orders with IPO 4\",\n", "       \"Avg Picking Complete Order Scan\",\n", "       \"Avg Delivery Time\",\n", "       \"Delivered Orders\"\n", "from base a\n", "left join daily_dau b on a.date = b.date_ts and a.\"Store ID\" = b.outlet_id\n", "left join required_manpower r on a.\"Store ID\" = r.outlet and a.date = r.date\n", "order by 1 desc\n", "\n", " \"\"\"\n", "\n", "data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "b5def403-d155-434f-a939-d165125d22bc", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "304dfed1-d5dc-4eaf-8372-1b73a85c973e", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d98cc645-85d3-4ff5-ae19-108075a974b3", "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9240b3b7-7853-4805-9d9c-f34e97b9a962", "metadata": {}, "outputs": [], "source": ["data_city_overall = data[\n", "    [\n", "        \"date\",\n", "        \"store id\",\n", "        \"store name\",\n", "        \"dau\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"peak_req\",\n", "        \"non_peak_req\",\n", "        \"riders\",\n", "        \"avg ipo\",\n", "        \"orders with surge\",\n", "        \"orders within 15 min\",\n", "        \"avg instore time\",\n", "        \"orders within 2.5 min\",\n", "        \"avg assigned time\",\n", "        \"orders within 30sec\",\n", "        \"avg picking time\",\n", "        \"orders within 1.5min\",\n", "        \"orders with ipo 4 within 1 min\",\n", "        \"orders with ipo 4\",\n", "        \"avg picking complete order scan\",\n", "        \"avg delivery time\",\n", "        \"delivered orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "accd84fe-384b-4cd6-854d-fadc006db2e9", "metadata": {}, "outputs": [], "source": ["data_city_overall[\"city\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "id": "0c15e7fd-0e34-404f-a772-b6d6df4ebd42", "metadata": {}, "outputs": [], "source": ["data_city_overall_new = pd.concat([data, data_city_overall])"]}, {"cell_type": "code", "execution_count": null, "id": "74c800c5-7c05-4341-b95b-1a547a33cacb", "metadata": {}, "outputs": [], "source": ["data_store_overall = data_city_overall_new[\n", "    [\n", "        \"date\",\n", "        \"store id\",\n", "        \"city\",\n", "        \"dau\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"peak_req\",\n", "        \"non_peak_req\",\n", "        \"riders\",\n", "        \"avg ipo\",\n", "        \"orders with surge\",\n", "        \"orders within 15 min\",\n", "        \"avg instore time\",\n", "        \"orders within 2.5 min\",\n", "        \"avg assigned time\",\n", "        \"orders within 30sec\",\n", "        \"avg picking time\",\n", "        \"orders within 1.5min\",\n", "        \"orders with ipo 4 within 1 min\",\n", "        \"orders with ipo 4\",\n", "        \"avg picking complete order scan\",\n", "        \"avg delivery time\",\n", "        \"delivered orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "280b46dc-ca81-4a57-8397-ad324b7147d0", "metadata": {}, "outputs": [], "source": ["# data_store_overall['store id'] = 'Overall'\n", "data_store_overall[\"store name\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "id": "aa01680c-821e-4402-aa98-e13f385fb1ee", "metadata": {}, "outputs": [], "source": ["data_store_overall_new = pd.concat([data_city_overall_new, data_store_overall])"]}, {"cell_type": "code", "execution_count": null, "id": "1461df05-30e3-4c17-836f-3df887664ce9", "metadata": {}, "outputs": [], "source": ["data_store_type = pb.from_sheets(\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\", \"Store info\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7764abb5-aeb0-4ebd-bfd5-0ecd3de2ece0", "metadata": {}, "outputs": [], "source": ["data_store_type.head()"]}, {"cell_type": "code", "execution_count": null, "id": "547cf2a9-94da-4ba9-bced-4fc8f14ebe83", "metadata": {}, "outputs": [], "source": ["data_store_type[\"Outlet ID\"] = data_store_type[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "b092b457-86d6-468c-9420-9a4dec105752", "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type[[\"Outlet ID\", \"Operation Mode\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "ed77d11e-a9f1-4f63-b0ba-712b8e294344", "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type.rename(\n", "    columns={\"Outlet ID\": \"store id\", \"Operation Mode\": \"store type\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "63ec97b6-2d28-4e9a-a047-6089e00bd3ca", "metadata": {}, "outputs": [], "source": ["data_store_overall_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "1ba7798a-ffc9-4f97-a9eb-58f31f2566c8", "metadata": {}, "outputs": [], "source": ["data_store_type_overall = data_store_overall_new.merge(\n", "    data_store_type, on=[\"store id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b1523580-4f48-43aa-afd9-65e3da854c4c", "metadata": {}, "outputs": [], "source": ["data_store_type_overall.shape"]}, {"cell_type": "code", "execution_count": null, "id": "29e45149-9907-45f8-94f0-4f8abbedcb47", "metadata": {}, "outputs": [], "source": ["data_store_type_overall.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b3bd9302-83d5-4266-8dbe-435a8d39bce1", "metadata": {}, "outputs": [], "source": ["data_store_type_overall.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c40477dd-596e-4447-9bed-7f6e72aaec84", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new = data_store_type_overall[\n", "    [\n", "        \"date\",\n", "        \"store id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"dau\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"peak_req\",\n", "        \"non_peak_req\",\n", "        \"riders\",\n", "        \"avg ipo\",\n", "        \"orders with surge\",\n", "        \"orders within 15 min\",\n", "        \"avg instore time\",\n", "        \"orders within 2.5 min\",\n", "        \"avg assigned time\",\n", "        \"orders within 30sec\",\n", "        \"avg picking time\",\n", "        \"orders within 1.5min\",\n", "        \"orders with ipo 4 within 1 min\",\n", "        \"orders with ipo 4\",\n", "        \"avg picking complete order scan\",\n", "        \"avg delivery time\",\n", "        \"delivered orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "83ba1ef6-a289-4953-8faf-6b23c098132b", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new[\"store type\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "id": "740f9cfb-658d-4b9b-b7c0-40a3d67fdb36", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new = pd.concat(\n", "    [data_store_type_overall, data_store_type_overall_new]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c696bb96-118f-4f73-ab36-26f417cd81cc", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6a4de4f3-ba0d-4ae2-b6ce-6ebdbf7d85f0", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0d6dfce0-2f5d-4eb5-b7fa-f9dc84128147", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new[\"week_start\"] = data_store_type_overall_new_new[\n", "    \"date\"\n", "].apply(lambda x: x - timedelta(days=(x.weekday() + 1) % 7))\n", "\n", "data_store_type_overall_new_new[\"week_end\"] = data_store_type_overall_new_new[\n", "    \"week_start\"\n", "].apply(lambda x: x + <PERSON><PERSON>ta(days=6))"]}, {"cell_type": "code", "execution_count": null, "id": "c465cab9-43c3-4af5-8a75-896df024ce0b", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e98281c1-e405-469e-8574-baaae03e5e9b", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new[\"week\"] = (\n", "    data_store_type_overall_new_new[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + data_store_type_overall_new_new[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c82de449-71f1-4b40-b442-dc8f9326a53e", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c0fe0517-ba7b-44a9-8120-4a0dfda865b5", "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new = data_store_type_overall_new_new[\n", "    [\n", "        \"date\",\n", "        \"store id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"dau\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"peak_req\",\n", "        \"non_peak_req\",\n", "        \"riders\",\n", "        \"avg ipo\",\n", "        \"orders with surge\",\n", "        \"orders within 15 min\",\n", "        \"avg instore time\",\n", "        \"orders within 2.5 min\",\n", "        \"avg assigned time\",\n", "        \"orders within 30sec\",\n", "        \"avg picking time\",\n", "        \"orders within 1.5min\",\n", "        \"orders with ipo 4 within 1 min\",\n", "        \"orders with ipo 4\",\n", "        \"avg picking complete order scan\",\n", "        \"avg delivery time\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"store type\",\n", "        \"delivered orders\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6996612a-2e94-4e25-9bf6-daa9710f47b7", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_store_type_overall_new_new,\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\",\n", "    \"data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a0dc62f2-21af-4465-88ab-66831c67179e", "metadata": {}, "outputs": [], "source": ["# data_store_type_overall_new_new['date'].iloc[0]-timedelta(days=data_store_type_overall_new_new['date'].iloc[0].weekday())\n", "# data_store_type_overall_new_new['date'].iloc[0]-timedelta(days=data_store_type_overall_new_new['date'].iloc[0].weekday()) + timedelta(days=6)"]}, {"cell_type": "code", "execution_count": null, "id": "bdad8d0d-d794-47fd-9706-bee7f4a246ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}