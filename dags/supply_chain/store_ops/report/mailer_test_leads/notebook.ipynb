{"cells": [{"cell_type": "code", "execution_count": null, "id": "312b1bd3-cd64-424e-9020-5553fea87b9b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from email.mime.text import MIMEText\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "aa82c0f2-cf26-46dc-860a-00fedbd0ddde", "metadata": {"tags": []}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d6dce613-d0e6-4989-a339-a91c49acd804", "metadata": {}, "outputs": [], "source": ["hr_df = pb.from_sheets(\n", "    \"1KcFotxVnyxWbFC6zubJ29Kzu-8CVQqp0kRJfXhuuKNU\",\n", "    \"hr_spoc\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d7c916b8-ea6e-4f49-9de7-75c291b8bcb8", "metadata": {"tags": []}, "outputs": [], "source": ["hr_dfd = hr_df.drop(columns=[\"City\"])"]}, {"cell_type": "code", "execution_count": null, "id": "384e04d7-ab75-440a-a312-f4a1bb7311b4", "metadata": {"tags": []}, "outputs": [], "source": ["hr_dfd[\"outlet_id\"] = hr_dfd[\"outlet_id\"].astype(int)\n", "hr_dfd[\"email_id_spoc\"] = hr_dfd[\"email_id_spoc\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "8428dafa-aec9-4942-be9f-832d026eafd2", "metadata": {"tags": []}, "outputs": [], "source": ["hr_dfd.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa804328-082f-44cc-b7fa-7bbdb1e38619", "metadata": {}, "outputs": [], "source": ["yesterday = (datetime.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "87391d29-9ecd-4f28-a16a-9fe439ed436e", "metadata": {}, "outputs": [], "source": ["hr_dfd[\"designation\"] = \"HR_POC\""]}, {"cell_type": "code", "execution_count": null, "id": "9685dbea-6f9b-42f0-882d-0f613d52cf42", "metadata": {"tags": []}, "outputs": [], "source": ["hr_dfd[\"date_\"] = yesterday"]}, {"cell_type": "code", "execution_count": null, "id": "1b58c871-5316-42a7-b27e-e444eb1e722b", "metadata": {}, "outputs": [], "source": ["hr_dfd.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "dcb1b456-a20d-4504-b1d9-53ed531b0758", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with hiring_gap as \n", "(select \n", "date_ts,\n", "outlet_id,\n", "max_by(gap,updated_timestamp) as ft_gap,\n", "max_by(od_to_be_hired, updated_timestamp) as od_gap\n", "from interim.od_to_ft_hiring_gap_logs\n", "where date_ts = current_date \n", "group by 1,2\n", ")\n", ",\n", "\n", "base as \n", "(select distinct a.employee_id, email_id,name,phone, designation,outlet_ids\n", "from storeops_etls.user_access_mapping a\n", "left join (\n", "  select employee_id, max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id\n", "where designation in \n", "('assistant-store-manager', \n", "'store-manager',\n", "'cluster-manager',\n", "'senior-store-manager',\n", "'cluster-manager-instore',\n", "'city-support',\n", "'senior-store-manager-instore',\n", "'ops-head',\n", "'ops-head-instore')\n", "\n", "and is_mapping_active = true\n", "\n", ")\n", ",\n", "\n", "mid_base as \n", "(\n", "select email_id, designation, outlet_id\n", "from base b\n", "cross join unnest(outlet_ids) as  t (outlet_id)\n", "),\n", "\n", "final_base as (\n", "               select hg.date_ts, hg.outlet_id, mb.email_id , mb.designation , hg.ft_gap, hg.od_gap \n", "               from mid_base mb \n", "               left join hiring_gap hg \n", "               on mb.outlet_id = hg.outlet_id \n", "              where  hg.date_ts is not null \n", "              and hg.outlet_id is not null\n", "              )\n", "              \n", "              select\n", "              date_ts, \n", "              outlet_id,\n", "              case when email_id = '<EMAIL>' then '<EMAIL>' else email_id end as email_id,\n", "              designation,\n", "              ft_gap,\n", "              od_gap\n", "              from final_base \n", "              \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "42769453-f57d-4a13-8113-f40aedf520d5", "metadata": {}, "outputs": [], "source": ["applied = \"\"\"\n", "\n", "\n", "with base_mapping as \n", "(select distinct a.employee_id, email_id,name,phone, designation,outlet_ids\n", "from storeops_etls.user_access_mapping a\n", "left join (\n", "  select employee_id, max_by(name, update_ts) as employee_name ,max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id\n", "where designation in \n", "('assistant-store-manager', \n", "'store-manager',\n", "'cluster-manager',\n", "'senior-store-manager',\n", "'cluster-manager-instore',\n", "'city-support',\n", "'senior-store-manager-instore',\n", "'ops-head',\n", "'ops-head-instore')\n", "and is_mapping_active = true\n", "),\n", "\n", "\n", "mid_base as\n", "(\n", "select email_id, name, designation, outlet_id, phone\n", "from base_mapping b\n", "cross join unnest(outlet_ids) as  t (outlet_id)\n", "),\n", "\n", "final_mapping as (\n", "  select *  \n", "  from mid_base\n", "), \n", "\n", "ft_base as \n", "(\n", "select site_id, a.employee_id, b.phone, employee_name, 'FULL_TIME' as job_preference\n", "from\n", "(\n", "select employee_id, site_id\n", "from ops_management.job_application a\n", "left join ops_management.job_vacancy v\n", "on a.job_vacancy_id = v.id\n", "where a.insert_ds_ist >= cast(current_date - interval '1' day as varchar)\n", "and a.status = 'APPLIED'\n", ") a\n", "left join\n", "(\n", "  select employee_id,max_by(name, update_ts) as employee_name, max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id \n", "),\n", "\n", "\n", "od_base as \n", "(\n", "select site_id, a.employee_id, phone, employee_name,'PART_TIME' as job_preference\n", "from\n", "(\n", "select employee_id, site_id\n", "from ops_management.slot_bookings sb\n", "left join ops_management.slots s\n", "on sb.slot_id = s.id\n", "where s.slot_type = 'ON_DEMAND_PICKING_TRAINING'\n", "and s.insert_ds_ist >= cast(current_date - interval '5' day as varchar)\n", "and date(sb.install_ts + interval '330' minute) >= current_date - interval '5' day\n", "and sb.insert_ds_ist = cast(current_date as varchar)\n", ") a\n", "left join\n", "(\n", "  select employee_id,max_by(name, update_ts) as employee_name, max_by(phone, update_ts) as phone\n", "  from ops_management.user\n", "  group by 1\n", ") b\n", "on a.employee_id = b.employee_id \n", "),\n", "\n", "final_base as \n", "(\n", "select *\n", "from ft_base\n", "union\n", "select *\n", "from od_base\n", "), \n", "\n", "gap as \n", "(\n", "select outlet_id,\n", "outlet_name, \n", "sum(case when gap <= 0 then 0 else gap end) as gap\n", "  from\n", "(\n", "select\n", "  outlet_id,\n", "  outlet_name,\n", "  max_by(approved_hc, updated_timestamp) as approved_hc, \n", "  max_by(gap, updated_timestamp) as gap, \n", "  max_by(od_to_be_hired, updated_timestamp) as od_to_be_hired\n", " from\n", "  interim.od_to_ft_hiring_gap_logs\n", "where\n", "  date_ts = current_date\n", " group by 1,2\n", ")\n", "group by 1, 2\n", "),\n", "\n", "final_data as \n", "(\n", "select a.*, gap\n", "from\n", "(\n", "select email_id, site_id, employee_id, phone, employee_name, job_preference\n", "from \n", "(\n", "select email_id, site_id, employee_id, phone, employee_name, job_preference\n", "from\n", "(\n", "select f.email_id, site_id, employee_id, e.phone, employee_name, job_preference\n", "from \n", "final_mapping f\n", "left join\n", "final_base e\n", "on f.outlet_id = cast(e.site_id as int)\n", ")\n", ")\n", "where site_id is not null\n", ") a\n", "left join\n", "gap g\n", "on a.site_id = cast(g.outlet_id as varchar)\n", ")\n", "\n", "\n", "select case when email_id = '<EMAIL>' then '<EMAIL>' else email_id end as email_id, \n", "outlet_id , employee_id, phone, employee_name, job_preference\n", "from\n", "(\n", "select email_id, site_id as outlet_id , employee_id, phone, employee_name, job_preference\n", "from final_data\n", "where gap >= 0 \n", ")\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ca90ce8f-0091-4c10-89a6-732bade30cac", "metadata": {"tags": []}, "outputs": [], "source": ["df = pd.read_sql(query, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "9b4ac494-936d-4a9a-8146-410301c6569d", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f38b564e-d37e-418d-8c02-2fa0d070b7db", "metadata": {}, "outputs": [], "source": ["df[df[\"email_id\"] == \"<EMAIL>\"]"]}, {"cell_type": "code", "execution_count": null, "id": "73a35952-1d6b-479d-9150-cbe537d1ce5f", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "fc838b18-5ac4-481a-8411-0b1e98dcc7b1", "metadata": {}, "outputs": [], "source": ["hr_dfd.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4270811c-36c1-44ce-8fe6-7ca7f298ac2f", "metadata": {}, "outputs": [], "source": ["merged_df = df.merge(hr_dfd, on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "eef3c316-1679-4df7-9168-78fdd7e36dcb", "metadata": {}, "outputs": [], "source": ["merged_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9a353017-c9ae-4c6c-b3c9-c8501ed534d7", "metadata": {}, "outputs": [], "source": ["result_df = merged_df[(merged_df[\"email_id_spoc\"].notnull())][\n", "    [\"date_\", \"outlet_id\", \"email_id_spoc\", \"designation_y\", \"ft_gap\", \"od_gap\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "f6c17dd9-0a19-441c-ab16-9a316aada8fb", "metadata": {}, "outputs": [], "source": ["result_df"]}, {"cell_type": "code", "execution_count": null, "id": "9f4d3abf-742d-4ef3-961c-c4109b03b12b", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6260798d-1a3f-40bb-be8b-2d6267781a94", "metadata": {}, "outputs": [], "source": ["result_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d5c7a74b-fad6-4d88-9ed5-a3c622582467", "metadata": {}, "outputs": [], "source": ["result_df = result_df.rename(\n", "    columns={\"date_\": \"date_ts\", \"email_id_spoc\": \"email_id\", \"designation_y\": \"designation\"}\n", ")\n", "\n", "result_df = result_df[df.columns]\n", "\n", "df = pd.concat([df, result_df], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "338848d3-b886-4dd6-8f1d-6578890996c5", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "ce0924f0-9ef5-413f-bfd1-0506b5c5de01", "metadata": {}, "outputs": [], "source": ["df_1 = pd.read_sql(applied, trino_con)"]}, {"cell_type": "code", "execution_count": null, "id": "e989ffd5-615b-4f58-ab9a-be87aa8d4a4a", "metadata": {}, "outputs": [], "source": ["df_1[df_1[\"email_id\"] == \"<EMAIL>\"]"]}, {"cell_type": "code", "execution_count": null, "id": "f4c2cfba-7961-4533-a6c5-1edb4a69e9b5", "metadata": {}, "outputs": [], "source": ["df_1"]}, {"cell_type": "code", "execution_count": null, "id": "8450d258-3d35-442d-ad86-18d1ba850d98", "metadata": {}, "outputs": [], "source": ["df_1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "a65ddc87-0a42-4331-9625-696fde0a7a11", "metadata": {}, "outputs": [], "source": ["df_1[\"outlet_id\"] = df_1[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "76c34549-53bc-467e-84bd-df5094af8d28", "metadata": {}, "outputs": [], "source": ["df_1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1cf57872-365b-419a-9de7-225281a35771", "metadata": {}, "outputs": [], "source": ["merged_df_1 = hr_dfd.merge(df_1, on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "682d2f6b-4929-4451-ae8b-2ef610e091c7", "metadata": {}, "outputs": [], "source": ["merged_df_1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "45542890-96f8-48dd-a2f2-cdce6154b1d0", "metadata": {}, "outputs": [], "source": ["result_df_1 = merged_df_1[merged_df_1[\"employee_id\"].notnull()][\n", "    [\"email_id_spoc\", \"outlet_id\", \"employee_id\", \"phone\", \"employee_name\", \"job_preference\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "9af27dc8-6b60-4fd6-9903-bc08e13dc275", "metadata": {}, "outputs": [], "source": ["result_df_1"]}, {"cell_type": "code", "execution_count": null, "id": "b2b58587-cb99-4a03-a737-fef361892c9c", "metadata": {}, "outputs": [], "source": ["result_df_1 = result_df_1.rename(\n", "    columns={\n", "        \"email_id_spoc\": \"email_id\",\n", "    }\n", ")\n", "\n", "result_df_1 = result_df_1[df_1.columns]\n", "\n", "df_1 = pd.concat([df_1, result_df_1], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d8298c51-bfda-4dc8-8415-4cd1c6cab543", "metadata": {}, "outputs": [], "source": ["df_1"]}, {"cell_type": "code", "execution_count": null, "id": "ae0e0cfe-73aa-4fe3-82cd-ce3e1d9633bc", "metadata": {"tags": []}, "outputs": [], "source": ["grouped = df.groupby(\"email_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "9283b3d1-78f3-4984-8a1f-f2feb25e7a50", "metadata": {}, "outputs": [], "source": ["# grouped.head()"]}, {"cell_type": "code", "execution_count": null, "id": "876237f2-9814-4380-98cd-5b03a23cb8bc", "metadata": {}, "outputs": [], "source": ["# df.to_csv(\"data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "2f3a57fb-ae21-4099-b029-101e25a36219", "metadata": {}, "outputs": [], "source": ["def generate_table_html(df, headers):\n", "    table_style = (\n", "        \"border-collapse: collapse; width: auto; max-width: 600px; \"\n", "        \"font-family: Arial, sans-serif; font-size: 12px; margin: 20px 0; \"\n", "        \"border: 1px solid #ccc; color: #333;\"\n", "    )\n", "    th_style = (\n", "        \"padding: 8px 12px; text-align: left; background-color: #e6f2ff; \"\n", "        \"border: 1px solid #ccc; font-weight: bold; color: #003366;\"\n", "    )\n", "    td_style = \"padding: 8px 12px; text-align: left; border: 1px solid #ccc;\"\n", "\n", "    table_html = f'<table style=\"{table_style}\" cellpadding=\"0\" cellspacing=\"0\">'\n", "    table_html += \"<thead><tr>\"\n", "    table_html += \"\".join(f'<th style=\"{th_style}\">{col}</th>' for col in headers)\n", "    table_html += \"</tr></thead><tbody>\"\n", "\n", "    for i, (_, row) in enumerate(df.iterrows()):\n", "        row_style = td_style\n", "        row_style += \" background-color: #f9f9f9;\" if i % 2 else \" background-color: #ffffff;\"\n", "        table_html += (\n", "            \"<tr>\" + \"\".join(f'<td style=\"{row_style}\">{val}</td>' for val in row) + \"</tr>\"\n", "        )\n", "\n", "    table_html += \"</tbody></table>\"\n", "    return table_html"]}, {"cell_type": "code", "execution_count": null, "id": "2100b70a-efea-40f2-876c-78365ffbd5b7", "metadata": {"tags": []}, "outputs": [], "source": ["# Define test email\n", "email_id = \"<EMAIL>\"\n", "\n", "# Normalize and fix types\n", "df[\"outlet_id\"] = df[\"outlet_id\"].astype(str)\n", "df_1[\"outlet_id\"] = df_1[\"outlet_id\"].astype(str)\n", "df.columns = df.columns.str.strip().str.lower()\n", "df_1.columns = df_1.columns.str.strip().str.lower()\n", "\n", "# Check if email_id exists in grouped\n", "if email_id in grouped.groups:\n", "    group_df = grouped.get_group(email_id)\n", "    outlet_ids = group_df[\"outlet_id\"].astype(str).unique()\n", "\n", "    print(f\"\\n[DEBUG] Processing: {email_id}\")\n", "    print(f\"[DEBUG] Outlets for email: {outlet_ids}\")\n", "\n", "    gap_df = df[df[\"outlet_id\"].isin(outlet_ids)][\n", "        [\"outlet_id\", \"ft_gap\", \"od_gap\"]\n", "    ].drop_duplicates()\n", "    print(f\"[DEBUG] GAP rows: {len(gap_df)}\")\n", "\n", "    applied_filtered = df_1[df_1[\"outlet_id\"].isin(outlet_ids)]\n", "    print(f\"[DEBUG] Applied rows before merge: {len(applied_filtered)}\")\n", "\n", "    merged_df = applied_filtered.merge(gap_df, on=\"outlet_id\", how=\"left\")\n", "    print(f\"[DEBUG] Merged rows: {len(merged_df)}\")\n", "\n", "    # GAP table\n", "    gap_table_html = generate_table_html(\n", "        gap_df, headers=[\"Site ID\", \"Full-Time Gap\", \"Part-Time Gap\"]\n", "    )\n", "\n", "    # Applied table (exclude gap columns)\n", "    applied_table_df = merged_df[\n", "        [\"outlet_id\", \"employee_id\", \"employee_name\", \"phone\", \"job_preference\"]\n", "    ].drop_duplicates()\n", "\n", "    applied_table_html = generate_table_html(\n", "        applied_table_df, headers=[\"Site ID\", \"Employee ID\", \"Name\", \"Phone\", \"Role Applied\"]\n", "    )\n", "\n", "    contact_msg = \"<p>For any queries, reach out to <br>\" \"<b>Ankit:</b> <br>\" \"<b>Shriya:</b> </p>\"\n", "\n", "    # Compose message\n", "    message = f\"\"\"\n", "    <html><body>\n", "    <p>Please find below the list of employees who have booked appointments at your store(s).</p>\n", "    <p><strong>Action Required:</strong> Kindly reach out to these employees at the earliest to complete the necessary onboarding steps and close the hiring gap.</p>\n", "\n", "    \n", "   \n", "         <p>👉 <a href=\"https://script.google.com/a/macros/grofers.com/s/AKfycbwVU66OqrPQh8Z4vLSaN5yQ2nJAVFGhjO0PUVHrsrpvBIivLMP4SJnMirsuXlYFRcFAIg/exec\"><b>Click here</b></a> to access all\n", "         the referral and direct channel leads for your stores.</p>\n", "\n", "    <h3>Hiring Gaps by Outlet</h3>\n", "    {gap_table_html}\n", "\n", "    <h3>Applied Employee Details</h3>\n", "    {applied_table_html}\n", "\n", "    \n", "\n", "    <p>All the best,<br>Blinkit</p>\n", "    <p><i>Note - This is an automated email, please do not reply.</i></p>\n", "    </body></html>\n", "    \"\"\"\n", "    try:\n", "        print(\"[DEBUG] Attempting to send email...\")\n", "        response = pb.send_email(\n", "            from_email=\"<EMAIL>\",\n", "            to_email=[\"<EMAIL>\"],\n", "            subject=\"Employee Mapping\",\n", "            html_content=message,\n", "            files=[],\n", "            cc=[],\n", "            mime_subtype=\"mixed\",\n", "            mime_charset=\"utf-8\",\n", "        )\n", "        print(\"[DEBUG] Email sent successfully.\")\n", "        print(f\"[DEBUG] Send response: {response}\")\n", "\n", "    except Exception as email_error:\n", "        print(f\"[ERROR] Email failed to send: {email_error}\")\n", "\n", "else:\n", "    print(f\"[DEBUG] Email ID '{email_id}' not found in grouped.groups. No email will be sent.\")"]}, {"cell_type": "code", "execution_count": null, "id": "17deaa86-04d0-401d-9402-9175210bc0cb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}