{"cells": [{"cell_type": "code", "execution_count": null, "id": "983e1351-84d1-42d5-93ea-332dadc4a9e3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import date\n", "from datetime import timedelta\n", "import os\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "494d496e-e174-4679-92ba-4f77c2d234d9", "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca2971a2-ed64-4638-8939-22d3b2aa324b", "metadata": {}, "outputs": [], "source": ["sql_query = \"\"\"\n", "\n", "with role_ as (select distinct employee_id,role, rank() over(partition by employee_id order by date::date desc) as rnk\n", "from \n", "metrics.instore_manpower_performance\n", "WHERE date::date > current_date - 8)\n", "\n", "\n", "SELECT distinct\n", "    date::date as date,\n", "    max(store_name) as store_name,\n", "    max(city) as city,\n", "    --max(ownership) as ownership,\n", "    p.employee_id,\n", "    max(employee_name) as employee_name,\n", "    max(doj) as doj,\n", "    r.role,\n", "    sum(total_putaway_quantity) as total_putaway_quantity,\n", "    sum(total_picked_quantity) as total_picked_quantity,\n", "    max(store_id) as store_id,\n", "    case when sum(score)=0 and max(attendance_punched) not like 'Yes' then 'AB' else sum(score)::text end as score,\n", "    sum(score) as score_x\n", "FROM \n", "    metrics.instore_manpower_performance p\n", "    inner join role_ r on p.employee_id = r.employee_id\n", "        and r.rnk = 1\n", "    left join lake_retail.warehouse_user u on p.employee_id = u.emp_id\n", "WHERE \n", "    date::date > current_date - 8\n", "    and date::date < current_date\n", "group by 1,4,7\n", "order by 12 desc\n", "\"\"\"\n", "df1 = pd.read_sql(sql=sql_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "id": "a22126fe-bfc6-4f7b-bf83-59de6b2ec241", "metadata": {}, "outputs": [], "source": ["data_store_type = pb.from_sheets(\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\", \"Store info\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a2b0660c-9927-4c7e-bfe3-ef26eb27472f", "metadata": {}, "outputs": [], "source": ["data_store_type[\"Outlet ID\"] = data_store_type[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "264a9851-90d2-4756-8956-4be6fd176af4", "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type[[\"Outlet ID\", \"Operation Mode\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c5ea803a-5571-471b-9784-10857f8bb659", "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type.rename(\n", "    columns={\"Outlet ID\": \"store_id\", \"Operation Mode\": \"ownership\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e1ee3d74-f07e-44ab-bf28-c12dd45b787e", "metadata": {}, "outputs": [], "source": ["df = df1.merge(data_store_type, on=[\"store_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "41284acf-487a-4e70-b0cc-70050d519a0e", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "027649d6-4700-481a-8224-1b4a39117e7b", "metadata": {}, "outputs": [], "source": ["df[\"date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "4f238753-4c99-44b8-b546-0f9aef5da281", "metadata": {}, "outputs": [], "source": ["df[df[\"employee_id\"] == \"GCE124571\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1affde44-df9b-4cee-bd95-bc94ad632384", "metadata": {}, "outputs": [], "source": ["df[df[\"score\"] == \"AB\"]"]}, {"cell_type": "code", "execution_count": null, "id": "fe291294-01d9-48e2-a28d-15f57be50436", "metadata": {}, "outputs": [], "source": ["df.date = df.date.astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "id": "492a626e-dd4a-4aae-97fb-b6868557f860", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "47f565ec-203e-431f-8991-4b430d4d0a07", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "05780718-ee0b-4f5c-9d56-8a4f959e4c09", "metadata": {}, "outputs": [], "source": ["final = pd.pivot_table(\n", "    df,\n", "    values=[\"score\"],\n", "    index=[\n", "        \"city\",\n", "        \"store_name\",\n", "        \"store_id\",\n", "        \"ownership\",\n", "        \"employee_id\",\n", "        \"employee_name\",\n", "        \"doj\",\n", "        \"role\",\n", "    ],\n", "    columns=[\"date\"],\n", "    aggfunc=np.sum,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "d45db1fe-4592-4905-83d2-a81b3a3fa1a9", "metadata": {}, "outputs": [], "source": ["final = pd.DataFrame(final.to_records())\n", "final.head()\n", "\n", "final.columns = final.columns.str.replace(\"\\('\", \"\")\n", "final.columns = final.columns.str.replace(\"'\\)\", \"\")\n", "final.columns = final.columns.str.replace(\"', '\", \"\")\n", "final.columns = final.columns.str.replace(\"score\", \"\")\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7322cd9b-76b1-453c-8125-3ffe7b876db3", "metadata": {}, "outputs": [], "source": ["final.fillna(\"AB\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4630015f-6026-465c-9c37-b2d6645357c7", "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b4b7f571-fa12-4b0c-b606-bb4dd0dcef29", "metadata": {}, "outputs": [], "source": ["# df_avg_lw = df[(df[\"date\"] >= '2022-04-25') & (df[\"date\"] <= '2022-05-01')].groupby(['store_id',\n", "#                                                                                     'employee_id',\n", "#                                                                                     'role']).apply(lambda x: x[x['score_x'] > -1][['score_x']].mean()).reset_index()\n", "# df_avg_lw.head()\n", "\n", "# df_avg_lw.rename(columns = {\"score_x\":\"score_lw\"},inplace = True)"]}, {"cell_type": "code", "execution_count": null, "id": "35dc34ec-ec53-4afc-8eea-7ef46aa293fd", "metadata": {}, "outputs": [], "source": ["df_f = (\n", "    df.groupby(\n", "        [\n", "            \"city\",\n", "            \"store_name\",\n", "            \"store_id\",\n", "            \"ownership\",\n", "            \"employee_id\",\n", "            \"doj\",\n", "            \"employee_name\",\n", "            \"role\",\n", "        ]\n", "    )\n", "    .apply(lambda x: x[x[\"score_x\"] > 0][[\"score_x\"]].mean())\n", "    .reset_index()\n", ")\n", "df_f.head()"]}, {"cell_type": "code", "execution_count": null, "id": "99a59abb-7ff8-4b12-a385-7cc509d5caa1", "metadata": {}, "outputs": [], "source": ["# df_avg_lw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "827898b3-e907-4761-b9bb-5fd9e08503b0", "metadata": {}, "outputs": [], "source": ["# df_f = df_f.merge(df_avg_lw, how = \"left\", on = [\"store_id\",\"employee_id\",\"role\"])\n", "# df_f.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2de12d8d-04b7-48de-a652-6e1a651a5ef9", "metadata": {}, "outputs": [], "source": ["df_f.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c2387c1f-a9a6-43c7-85b9-829a8eca70f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9bf535f-a64c-4a18-afee-fc563ba05cba", "metadata": {}, "outputs": [], "source": ["final_df = final.merge(\n", "    df_f,\n", "    on=[\n", "        \"city\",\n", "        \"store_name\",\n", "        \"store_id\",\n", "        \"ownership\",\n", "        \"employee_id\",\n", "        \"employee_name\",\n", "        \"doj\",\n", "        \"role\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "# .drop(columns=['store_name',\n", "#             'store_id',\n", "#             'ownership',\n", "#             'employee_id',\n", "#             'employee_name',\n", "#             'role'])"]}, {"cell_type": "code", "execution_count": null, "id": "76fce736-0eca-46e8-a031-179da2339eb2", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0edd61a7-98f2-45ad-abb1-5b0668a88ebd", "metadata": {}, "outputs": [], "source": ["final_df.drop([\"index\"], axis=\"columns\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8cbe19be-7163-4117-864b-99e0270ff5e4", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "63b160de-ff48-4bb1-b9ec-0961662d7e42", "metadata": {}, "outputs": [], "source": ["final_df[\"score_x\"] = final_df[\"score_x\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "1a010fb6-6894-42e3-9b95-df99a40d5e58", "metadata": {}, "outputs": [], "source": ["checkouts = \"\"\"\n", "with base as (select o.id as order_id,o.cart_id as cart_id,\n", "m.external_id as merchant_id,\n", "ln.external_name as merchant_name,\n", "date(o.install_ts + interval '5.5 hours') as checkout_date,\n", "DATEPART(weekday,date(o.install_ts + interval '5.5 hours')) as dow,\n", "(o.install_ts + interval '5.5 hours') as checkout_time,\n", "(o.update_ts + interval '5.5 hours') as delivery_time,\n", "extract(hour from (o.install_ts+interval '5.5 hours')) as hour\n", "from lake_oms_bifrost.oms_order o\n", "inner join lake_oms_bifrost.oms_merchant m on o.merchant_id = m.id\n", "and date(o.install_ts + interval '5.5 hours') between current_date - 7 and current_date - 1\n", "--and DATEPART(weekday,date(o.install_ts + interval '5.5 hours')) = 0\n", "and o.type = 'RetailForwardOrder'\n", "inner join lake_logistics.logistics_node ln on m.external_id = ln.external_id\n", "),\n", "\n", "outlets as (select distinct frontend_merchant_city_name,frontend_merchant_id,frontend_merchant_name,pos_outlet_id\n", "from\n", "dwh.dim_merchant_outlet_facility_mapping f\n", "where is_frontend_merchant_active = true\n", "    and is_express_store = true and is_current = true\n", "    and backend_merchant_id <> 30787\n", "group by 1,2,3,4\n", "order by 1,3)\n", "\n", "select store_id,max(orders) as orders from\n", "(select pos_outlet_id as store_id,merchant_name,checkout_date,dow,count(distinct order_id) as orders\n", "from base b\n", "inner join outlets o on b.merchant_id = o.frontend_merchant_id\n", "group by 1,2,3,4)\n", "group by 1\n", "order by 2\"\"\"\n", "df_checkouts = pd.read_sql(checkouts, pb.get_connection(\"[Warehouse] Redshift\"))\n", "df_checkouts.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b6f209e6-1b8d-46c0-bde0-8eeeaafa583b", "metadata": {}, "outputs": [], "source": ["# df_checkouts[\"checkout_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a2b2b772-4230-4daf-8e2f-a8f39067e7c4", "metadata": {}, "outputs": [], "source": ["final_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "1abb439b-165e-4929-8937-2cd29e8d4f06", "metadata": {}, "outputs": [], "source": ["final_df = final_df.merge(\n", "    df_checkouts[[\"store_id\", \"orders\"]], how=\"inner\", on=[\"store_id\"]\n", ")\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4f4b5132-22dd-40dc-96c6-867e4a368684", "metadata": {}, "outputs": [], "source": ["final_df[\"order_bucket\"] = final_df[\"orders\"].apply(\n", "    lambda x: \"< 400\"\n", "    if (x < 400)\n", "    else (\n", "        \"400 - 800\"\n", "        if ((x >= 400) & (x < 800))\n", "        else (\"800 - 1200\" if ((x >= 800) & (x < 1200)) else \">= 1200\")\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "532648af-74ec-4eca-be4d-8047950e0a8e", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "55870ea3-aa71-4fae-b442-bbb25d177abd", "metadata": {}, "outputs": [], "source": ["final_df[\"city\"] = final_df[\"city\"].apply(\n", "    lambda x: \"Zirakpur\" if (x == \"ZIRAKPUR\") else (\"Kanpur\" if (x == \"kanpur\") else x)\n", ")\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "id": "ddb752fe-9091-428f-8ace-a72746ef632e", "metadata": {}, "outputs": [], "source": ["df_checkouts[df_checkouts[\"store_id\"] == 2799]"]}, {"cell_type": "code", "execution_count": null, "id": "c3ff2b3f-fddf-41be-a4c7-0b11cd7da622", "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e2126039-3506-4152-b234-cd56a22cee99", "metadata": {}, "outputs": [], "source": ["sheet_id = \"16Xu4O_EypqkKu01Y1N6u0YwzTAZI3RnpoVw_zBw1Cao\"\n", "sheet_name = \"Sheet1\"\n", "pb.to_sheets(\n", "    final_df,\n", "    sheet_id,\n", "    sheet_name,\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5c15d35b-d31b-4ce0-bdb1-43d7c51952df", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}