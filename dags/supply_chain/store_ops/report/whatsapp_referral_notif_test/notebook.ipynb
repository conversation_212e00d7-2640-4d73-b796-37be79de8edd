{"cells": [{"cell_type": "code", "execution_count": null, "id": "env-check-setup", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"AIRFLOW_WEBSERVER_SERVICE_HOST\"] = \"some_value\"\n", "\n", "for var in [\n", "    \"AIRFLOW_DE_WEBSERVER_SERVICE_HOST\",\n", "    \"AIRFLOW_ARS_WEBSERVER_SERVICE_HOST\",\n", "    \"AIRFLOW_PREP_WEBSERVER_SERVICE_HOST\",\n", "    \"JUPYTERHUB_USER\"\n", "]:\n", "    if var in os.environ:\n", "        del os.environ[var]\n", "\n", "IS_AIRFLOW_COMMON_ENV = \"AIRFLOW_WEBSERVER_SERVICE_HOST\" in os.environ\n", "IS_AIRFLOW_DE_ENV = \"AIRFLOW_DE_WEBSERVER_SERVICE_HOST\" in os.environ\n", "IS_AIRFLOW_ARS_ENV = \"AIRFLOW_ARS_WEBSERVER_SERVICE_HOST\" in os.environ\n", "IS_PREP_ENV = \"AIRFLOW_PREP_WEBSERVER_SERVICE_HOST\" in os.environ\n", "IS_JHUB = \"JUPYTERHUB_USER\" in os.environ\n", "IS_PROD_ENV = IS_AIRFLOW_DE_ENV\n", "IS_STAGING_ENV = IS_PREP_ENV or IS_JHUB\n", "\n", "print(f\"IS_AIRFLOW_COMMON_ENV: {IS_AIRFLOW_COMMON_ENV}\")\n", "print(f\"IS_AIRFLOW_DE_ENV: {IS_AIRFLOW_DE_ENV}\")\n", "print(f\"IS_AIRFLOW_ARS_ENV: {IS_AIRFLOW_ARS_ENV}\")\n", "print(f\"IS_PREP_ENV: {IS_PREP_ENV}\")\n", "print(f\"IS_JHUB: {IS_JHUB}\")\n", "print(f\"IS_PROD_ENV: {IS_PROD_ENV}\")\n", "print(f\"IS_STAGING_ENV: {IS_STAGING_ENV}\")"]}, {"cell_type": "code", "execution_count": null, "id": "import-pb-setup", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "read-sql-after-import", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "query = \"\"\"\n", "SELECT 1 as id\n", "\"\"\"\n", "pd.read_sql(query, trino_connection)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}