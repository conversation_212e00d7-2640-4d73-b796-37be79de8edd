{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "import calendar\n", "import time\n", "import os\n", "import jinja2\n", "from jinja2 import Template\n", "import numpy as np\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# presto_connection = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["date_filter = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = pd.to_datetime(date_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_leverage = 0.15\n", "pna_leverage = 0.25\n", "variance_leverage = 0.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (datetime.now().month - 2) == 0:\n", "    start_month = 12\n", "elif (datetime.now().month - 2) == -1:\n", "    start_month = 11\n", "else:\n", "    start_month = datetime.now().month - 2\n", "\n", "if start_month in (11, 12):\n", "    start_year = datetime.now().year - 1\n", "else:\n", "    start_year = end_year = datetime.now().year\n", "\n", "start_date = date(start_year, start_month, 26)\n", "# datetime.today().replace(day=1).date()\n", "end_date = date(\n", "    (start_date + timedelta(days=30)).year, (start_date + timedelta(days=30)).month, 25\n", ")\n", "if end_date.year != start_date.year:\n", "    start_date = date(datetime.now().year - 1, start_month, 26)\n", "    end_date = date(\n", "        (start_date + <PERSON><PERSON>ta(days=30)).year,\n", "        (start_date + timed<PERSON>ta(days=30)).month,\n", "        25,\n", "    )\n", "elif start_date.month == 12 and end_date.month == 1:\n", "    start_date = date(datetime.now().year - 1, start_month, 26)\n", "    end_date = date(\n", "        (start_date + <PERSON><PERSON>ta(days=30)).year,\n", "        (start_date + timed<PERSON>ta(days=30)).month,\n", "        25,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["automate_data = pb.from_sheets(\n", "    \"1haV71JdPu3RhNi801bC2JZ-iE1Q27iQDh9lGGaTZHS0\",\n", "    \"Merchant Deduction\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_filter_list = list(\n", "    automate_data[automate_data[\"outlet_id\"] != \"\"][\"outlet_id\"].unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_gmv_q = \"\"\"select \"outlet id\" as \"outlet_id\", \"outlet name\" as \"outlet_name\",\"last ob date as partner store\" as \"last OB date\", \"forward sales\", \"reverse sales\", \"net sales\" \n", "from metrics.merchant_payout_details where extract(month from updated_time_ist) = extract(month from '{end_date}'::date)\n", "and \"outlet id\" in {merchant_id}\n", "\"\"\".format(\n", "    end_date=end_date, merchant_id=tuple(merchant_filter_list)\n", ")\n", "\n", "gmv_data = pd.read_sql(last_gmv_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchants_outlet_list = list(gmv_data[\"outlet_id\"].astype(str).unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ob_date = gmv_data[[\"outlet_id\", \"last ob date\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ob_date.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ob_date.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ob_date[\"last ob date\"] = ob_date[\"last ob date\"].apply(\n", "    lambda x: datetime.strptime(x, \"%Y-%m-%d\").date() if x != \"0\" else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data[gmv_data[\"outlet_id\"] == 3989]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PNA(Tail Assortment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_q = \"\"\"\n", "With  LandingPrice AS\n", "  (SELECT w.item_id,\n", "          avg(wlp) landing_price\n", "   FROM dwh.logs_weighted_landing_price w\n", "   where source='pricing_logs' and date(end_ts_ist)='2099-01-01' group by 1),\n", "   \n", "FillRateRawData as (\n", "SELECT       slot_end::date AS ScheduledDate,\n", "             slot_end as Slot_End,\n", "             fa.facility_id,\n", "             fa.facilityname,\n", "             fa.outlet_name,\n", "             fa.outlet_id,\n", "             fa.order_id,\n", "             fa.item_id,\n", "             f.picker_id,\n", "             fa.ordered_quantity,\n", "             fa.procured_quantity,\n", "             avg(b.selling_price) as average_selling_price,\n", "             fa.reason_key,\n", "             case when pp.outlet_type = 1 then 'FnV' else 'Grocery/Cold' end as item_type,\n", "             max(pp.name) as item_name,\n", "             max(variant_mrp) as item_mrp,\n", "             max(unfulfilled_order) AS Unfulfilled,\n", "             (case \n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (upper_limit>0) then 'Metering'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.description = 'Created') then 'Nayala'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.description = 'Reprocure') then 'Reschedule_Impact'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,inward_timestamp,picking_time) between 0 and 120) then 'GRN pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,return_timestamp,picking_time) between 0 and 120) then 'return pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (badstock_qty > ordered_quantity and datediff(min,badstock_timestamp,picking_time) between -120 and 120) then 'badstock marked'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (actual_quantity < 5) then 'Tail_Assortment'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (fa.outlet_type=1) then 'FNV' \n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' then 'Checkout'\n", "                  when reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' then 'Partial Inventory'\n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' then 'Reprocure'\n", "                  else 'Others' end) as reason\n", "      FROM metrics.fillrateattribution_darkstores fa left join lake_rpc.product_product pp on pp.item_id=fa.item_id\n", "      left join lake_oms_bifrost.oms_order_item b on b.order_id = fa.order_id and b.product_id = fa.product_id\n", "      left join dwh.fact_supply_chain_order_details f on f.order_id = fa.order_id and f.outlet_id = fa.outlet_id and \n", "      (date(order_checkout_ts_ist) between %(start_date)s and %(end_date)s)\n", "      WHERE (slot_end::date between %(start_date)s and %(end_date)s)\n", "      and reason_key != '' \n", "      and fa.outlet_id in %(merchants_outlet_list)s\n", "      and fa.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976)\n", "      and fa.item_id not in (10045262,10045263,10045264,10045265,10045266,10045267,10045268,10035959,10045276,10045277,10045278,10045279,10045280,10045281,10045282,10045283,10045284,10045285,10045286,10045287,10045288,10045289,10045517,10047169,10048866,10048873,10050578,10050579,10050580,10050581,10050582,10050583,10050584,10050585,10050586,10050570,10050571,10050572,10050573,10050574,10050575,10050576,10050577,10052001,10053757,10053761,10053752,10053753,10053754,10053785,10053786,10053787,10053788,10053790,10053800,10053801,10053802,10053803,10053804,10053805,10053806,10053839,10054536,10055688,10055689,10055792,10073451,10075658,10077221,10092383,10106605,10111431,10111432,10108327,10108328,10108329,10108330,10108331,10108332,10108333,10108334,10108335,10108336,10108337,10108338,10108339,10108340,10108341,10108342,10108343,10108344,10108345,10108346,10108347,10108348,10108349,10108350,10108351,10108763,10108764,10109659,10109660,10109971,10109972,10109973,10109974,10109975,10109976,10109977,10109978,10110003,10109979,10109980,10109981,10109982,10109983,10109984,10109985,10109986,10109987,10109988,10109989,10109990,10109991,10109992,10109993,10109994,10109995,10109996,10109997,10109998,10109999,10110000,10110001,10110002,10110254,10110807,10110837,10110838,10110995,10111048,10111070,10111071,10111098,10111099,10111100,10111662,10111697,10111748,10111749,10111882,10112346,10112686,10112736,10112737,10112738,10112739,10112740,10112741,10113119,10113120,10113121,10113122,10113123,10113887,10114279,10114443,10115247,10115331,10115479,10115649,10115650,10115794,10116079,10116080,10115796,10115976,10116472,10116485,10116548,10117113,10117109,10117110,10117111,10117112,10117122,10117374,10117375,10117376,10117377,10117378,10117379,10117388,10117884,10117853,10117854,10118042,10118035,10118316,10118394,10118395,10118396,10118397,10118398,10118399,10118400,10118401,10118402,10118403,10118404,10118405,10118406,10118393,10118771,10118772,10118773,10118927,10118928,10118929,10118930,10118931,10118932,10118933,10118934,10118935,10119069,10119070,10119071,10119072,10119073,10119095,10119096,10119097,10119098,10119099,10119100,10119352,10119353,10119354,10119355,10119384,10119383,10120756,10120574,10120575,10120576,10120577,10120623,10120624,10120622,10120625,10120626,10120752,10120754,10120755,10121012,10120987,10120988,10120989,10120990,10120991,10120992,10120993,10120994,10120995,10120996,10120997,10120998,10120999,10121000,10121001,10121002,10121003,10121004,10121005,10121006,10121007,10121008,10121009,10121010,10121011,10121026,10121027,10121287,10121288,10121289,10121290,10121291,10121292,10121293,10121294,10121295,10121465,10121466,10121485,10121486,10121786,10121874,10121875,10121876,10121877,10122338,10122339,10122340,10122341,10122392,10122393,10122394,10122395,10122396,10122397,10123751,10123752,10123753,10123754,10123755,10123756,10123757,10123758,10123759,10123760,10123761,10123762,10123763,10123764,10123765,10123766,10123767,10123768,10123769,10123770,10123771,10123772,10123773,10123774,10123775,10123776,10123777,10123778,10123779,10123780,10123781,10123782,10123783,10123784,10123785,10123786,10123787,10123788,10123789,10123790,10123791,10123792,10123793,10123794,10123795,10123796,10123797,10123798,10123799,10123800,10123801,10123802,10123803,10123804,10123805,10123806,10123807,10123808,10123809,10123810,10123811,10123812,10123813,10123814,10123985,10123986,10123987,10123988,10124242,10124243,10124245,10124246)\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9,\n", "               10,\n", "               11,\n", "               13,\n", "               14,\n", "               18\n", ")\n", "\n", "Select FR.*,lp.landing_price, ordered_quantity-procured_quantity as pna_qty from FillRateRawData FR left join LandingPrice lp on FR.item_id = lp.item_id\n", "where FR.outlet_id in %(merchants_outlet_list)s and reason='Tail_Assortment'\n", "and item_type<>'FnV'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19\n", "\"\"\"\n", "\n", "\n", "fill_rate_ds_impact_data = pd.read_sql(\n", "    fill_rate_ds_impact_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[fill_rate_ds_impact_data[\"landing_price\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[fill_rate_ds_impact_data[\"landing_price\"].isnull()].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[\"scheduleddate\"].iloc[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[\"pna_loss\"] = (\n", "    fill_rate_ds_impact_data[\"average_selling_price\"]\n", "    * fill_rate_ds_impact_data[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ob_flag_func(x):\n", "    if x[\"last ob date\"] == 0:\n", "        return 1\n", "    elif x[\"scheduleddate\"] <= x[\"last ob date\"]:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp = ob_date.merge(\n", "    fill_rate_ds_impact_data, on=[\"outlet_id\"], how=\"right\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ob_date[\"last ob date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp = fill_rate_ds_impact_data_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp[\"ob_flag\"] = fill_rate_ds_impact_data_temp.apply(\n", "    lambda x: ob_flag_func(x), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp = fill_rate_ds_impact_data_temp[\n", "    fill_rate_ds_impact_data_temp[\"ob_flag\"] == 1\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new = fill_rate_ds_impact_data_temp.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"pna_loss\": \"sum\", \"order_id\": \"nunique\", \"pna_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new.rename(\n", "    columns={\n", "        \"order_id\": \"fill_rate_impact_order_count\",\n", "        \"item_count\": \"fill_rate_impact_item_count\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fill_rate_ds_impact_data_new[fill_rate_ds_impact_data_new['outlet_id'].isin([1485,2703])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new[fill_rate_ds_impact_data_new[\"outlet_id\"] == 1946]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data[\"pna_loss\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fill_rate_ds_impact_data.to_csv('pna_raw_data.csv',index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Damage "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_q = \"\"\"\n", "with t1 as (\n", " SELECT \n", "        fc.id as facility_id,\n", "        fc.name as facility_name,\n", "    i.outlet_id as outlet_id,\n", "    ro.name AS Outlet_name,\n", "     r.item_id as item,\n", "     i.variant_id as variant_id,\n", "     i.upc_id as upc,\n", "     r.name AS Product,\n", "     case when b.item_id is not null and r.perishable=1 then 'perishable' end as perishables,\n", "    (CASE WHEN r.outlet_type = 1 THEN 'F&V_type' WHEN r.outlet_type = 2 THEN 'Grocery_type' ELSE 'Not Assigned' END) AS Item_Type,\n", "     SUM(i.\"delta\") AS quantity,\n", "     r.variant_mrp AS Mrp,\n", "     r.variant_uom_text AS grammage,\n", "     convert_timezone('Asia/Kolkata',i.pos_timestamp) AS POS_Timestamp,\n", "     i2.name AS Update_type,\n", "     ibur.name,\n", "     i.created_by_name as employee_id,\n", "     r.is_pl,\n", "     i.weighted_lp as \"weighted_landing_price\",\n", "     coalesce(tax.cess,0) as cess,\n", "     coalesce(tax.cgst,0) as cgst,\n", "     coalesce(tax.sgst,0) as sgst,\n", "     i.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) as cost_price\n", "     \n", " FROM\n", "    lake_ims.ims_inventory_log i\n", "         INNER JOIN\n", "     lake_rpc.product_product r ON r.variant_id = i.variant_id \n", "     \n", "     Left join \n", "     -----(select distinct item_id from metrics.item_wise_categories where L0 in ('Breakfast & Dairy', 'Eggs, Meat, Fish & Frozen')) b\n", "        (select distinct item_id from lake_rpc.item_category_details where L0 in ('chicken, meat & fish', 'dairy, bread & eggs','Dairy, Bread & Eggs','Chicken, Meat & Fish')) b\n", "        on r.item_id=b.item_id\n", "\n", "     \n", "         INNER JOIN\n", "     lake_retail.console_outlet ro ON ro.id = i.outlet_id and business_type_id =7\n", "     left join lake_crates.facility fc on ro.facility_id=fc.id\n", "         INNER JOIN\n", "     lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         LEFT JOIN\n", "     lake_rpc.product_tax tax ON r.item_id = tax.item_id and ro.tax_location_id = tax.location\n", "       LEFT JOIN\n", "       lake_ims.ims_bad_inventory_update_log ibil on i.inventory_update_id=ibil.inventory_update_id\n", "       LEFT join \n", "       lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", " WHERE\n", "     i.inventory_update_type_id in (11) and i.outlet_id in %(merchants_outlet_list)s\n", "      AND (date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "      and Update_type in ('bad_stock_damage_product') and ibil.reason_id not in (6,7,8,9)\n", "    GROUP BY 1,2,3,4,5, 6,7, 8 , 9,10,12,13 ,14,15,16,17,18,19,20,21,22,23\n", "  ) \n", "  \n", "  \n", "Select distinct\n", " t1.*,\n", " Mrp*quantity as Total_mrp,\n", " weighted_landing_price*quantity as Total_landing_Price,\n", " cost_price*quantity as Total_cost_price,\n", " (weighted_landing_price*quantity) - (cost_price*quantity) as Tax\n", "from t1 where outlet_id in %(merchants_outlet_list)s and item_type not in ('perishable','F&V_type')\n", "and perishables is null and item not in (10110215,10000436,10109019,10002963,10109881,10109878,10109880,10109879,10109877,10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10115796,10115976) \n", "and item not in (10045262,10045263,10045264,10045265,10045266,10045267,10045268,10035959,10045276,10045277,10045278,10045279,10045280,10045281,10045282,10045283,10045284,10045285,10045286,10045287,10045288,10045289,10045517,10047169,10048866,10048873,10050578,10050579,10050580,10050581,10050582,10050583,10050584,10050585,10050586,10050570,10050571,10050572,10050573,10050574,10050575,10050576,10050577,10052001,10053757,10053761,10053752,10053753,10053754,10053785,10053786,10053787,10053788,10053790,10053800,10053801,10053802,10053803,10053804,10053805,10053806,10053839,10054536,10055688,10055689,10055792,10073451,10075658,10077221,10092383,10106605,10111431,10111432,10108327,10108328,10108329,10108330,10108331,10108332,10108333,10108334,10108335,10108336,10108337,10108338,10108339,10108340,10108341,10108342,10108343,10108344,10108345,10108346,10108347,10108348,10108349,10108350,10108351,10108763,10108764,10109659,10109660,10109971,10109972,10109973,10109974,10109975,10109976,10109977,10109978,10110003,10109979,10109980,10109981,10109982,10109983,10109984,10109985,10109986,10109987,10109988,10109989,10109990,10109991,10109992,10109993,10109994,10109995,10109996,10109997,10109998,10109999,10110000,10110001,10110002,10110254,10110807,10110837,10110838,10110995,10111048,10111070,10111071,10111098,10111099,10111100,10111662,10111697,10111748,10111749,10111882,10112346,10112686,10112736,10112737,10112738,10112739,10112740,10112741,10113119,10113120,10113121,10113122,10113123,10113887,10114279,10114443,10115247,10115331,10115479,10115649,10115650,10115794,10116079,10116080,10115796,10115976,10116472,10116485,10116548,10117113,10117109,10117110,10117111,10117112,10117122,10117374,10117375,10117376,10117377,10117378,10117379,10117388,10117884,10117853,10117854,10118042,10118035,10118316,10118394,10118395,10118396,10118397,10118398,10118399,10118400,10118401,10118402,10118403,10118404,10118405,10118406,10118393,10118771,10118772,10118773,10118927,10118928,10118929,10118930,10118931,10118932,10118933,10118934,10118935,10119069,10119070,10119071,10119072,10119073,10119095,10119096,10119097,10119098,10119099,10119100,10119352,10119353,10119354,10119355,10119384,10119383,10120756,10120574,10120575,10120576,10120577,10120623,10120624,10120622,10120625,10120626,10120752,10120754,10120755,10121012,10120987,10120988,10120989,10120990,10120991,10120992,10120993,10120994,10120995,10120996,10120997,10120998,10120999,10121000,10121001,10121002,10121003,10121004,10121005,10121006,10121007,10121008,10121009,10121010,10121011,10121026,10121027,10121287,10121288,10121289,10121290,10121291,10121292,10121293,10121294,10121295,10121465,10121466,10121485,10121486,10121786,10121874,10121875,10121876,10121877,10122338,10122339,10122340,10122341,10122392,10122393,10122394,10122395,10122396,10122397,10123751,10123752,10123753,10123754,10123755,10123756,10123757,10123758,10123759,10123760,10123761,10123762,10123763,10123764,10123765,10123766,10123767,10123768,10123769,10123770,10123771,10123772,10123773,10123774,10123775,10123776,10123777,10123778,10123779,10123780,10123781,10123782,10123783,10123784,10123785,10123786,10123787,10123788,10123789,10123790,10123791,10123792,10123793,10123794,10123795,10123796,10123797,10123798,10123799,10123800,10123801,10123802,10123803,10123804,10123805,10123806,10123807,10123808,10123809,10123810,10123811,10123812,10123813,10123814,10123985,10123986,10123987,10123988,10124242,10124243,10124245,10124246)\n", "  \"\"\"\n", "\n", "\n", "damage_data = pd.read_sql(\n", "    damage_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data[damage_data[\"item\"] == 10110215]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data[\"date_ts\"] = damage_data[\"pos_timestamp\"].apply(lambda x: x.date())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp = (\n", "    damage_data.groupby([\"outlet_id\", \"outlet_name\", \"date_ts\"])\n", "    .agg({\"quantity\": \"sum\", \"total_landing_price\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp = damage_data_temp.rename(columns={\"quantity\": \"damage_item_count\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ob_flag_func_damage(x):\n", "    if x[\"last ob date\"] == 0:\n", "        return 1\n", "    elif x[\"date_ts\"] <= x[\"last ob date\"]:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp_temp = ob_date.merge(damage_data_temp, on=[\"outlet_id\"], how=\"right\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp_temp = damage_data_temp_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp_temp[\"ob_flag\"] = damage_data_temp_temp.apply(\n", "    lambda x: ob_flag_func_damage(x), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_temp_temp = damage_data_temp_temp[damage_data_temp_temp[\"ob_flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new = (\n", "    damage_data_temp_temp.groupby([\"outlet_id\"])\n", "    .agg({\"damage_item_count\": \"sum\", \"total_landing_price\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new[damage_data_new[\"outlet_id\"] == 995]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_data_new.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_new.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Stock Variance Loss"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_q = \"\"\"\n", "\n", "\n", "SELECT\n", "        date(pos_timestamp +interval '5.5 hours') as pos_timestamp,\n", "        (pos_timestamp +interval '5.5 hours') as pos_time,\n", "        -- date_trunc('{{day_week_month}}',(iil.pos_timestamp + interval '5.5 hrs')::date) AS {{day_week_month}},\n", "        iil.outlet_id as outlet_id,\n", "        o.name as outlet_name,\n", "        o.location as city,\n", "        p.item_id as \"Item ID\",\n", "        p.name as item_name,\n", "        inventory_operation as \"Operation\",\n", "        iil.\"delta\" as \"Change Quantity\",\n", "        iil.weighted_lp as landing_price,\n", "        iiut.name as \"Inventory Update Type\",\n", "        iil.created_by_name as \"Employee ID\", \n", "        CASE WHEN l0_id in (1487) then 'fnv'\n", "                   WHEN l0_id in (888,15,4,14,12) and l1_id in (279,1425,1361,1362,1363,1388,922,123,923,1200,953,1184) \n", "                    and l2_id in (1956,1425,63,1367,1369,1730,127,1094,949,138,1091,1185,123,1778,950,1389,31,1097,198,1093) then 'perishable'\n", "                   ELSE 'packaged' end as category,\n", "        (iil.\"delta\"*iil.weighted_lp) as Total_landing_Price\n", "        \n", "        \n", "        FROM \n", "        consumer.view_ims_non_infra_inventory_log iil\n", "        INNER JOIN\n", "        lake_rpc.product_product p ON iil.variant_id=p.variant_id and p.active=1\n", "         LEFT JOIN \n", "                    lake_rpc.item_category_details cd\n", "                on p.item_id = cd.item_id\n", "        \n", "        INNER JOIN\n", "        lake_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "        INNER JOIN\n", "        lake_retail.console_outlet o ON o.id=iil.outlet_id and business_type_id =7\n", "        INNER JOIN lake_crates.facility fc on o.facility_id=fc.id\n", "        LEFT JOIN lake_rpc.product_tax tax ON p.item_id = tax.item_id and o.tax_location_id = tax.location\n", "        \n", "        WHERE  (date(convert_timezone('Asia/Kolkata',iil.pos_timestamp)) between %(start_date)s and %(end_date)s)\n", "            and p.item_id not in (10109881,10109878,10109880,10109879,10109877,10110807,10045279,10077221,10045517,10115649,10092383,10043981,10115650,10045278,10044590,10048873,10045267,10075658,10044589,10035971,10050584,10045277,10112686,10047169,10109659,10108351,10112346,10109660,10109877,10115796,10115976)\n", "            and p.item_id not in (10045262,10045263,10045264,10045265,10045266,10045267,10045268,10035959,10045276,10045277,10045278,10045279,10045280,10045281,10045282,10045283,10045284,10045285,10045286,10045287,10045288,10045289,10045517,10047169,10048866,10048873,10050578,10050579,10050580,10050581,10050582,10050583,10050584,10050585,10050586,10050570,10050571,10050572,10050573,10050574,10050575,10050576,10050577,10052001,10053757,10053761,10053752,10053753,10053754,10053785,10053786,10053787,10053788,10053790,10053800,10053801,10053802,10053803,10053804,10053805,10053806,10053839,10054536,10055688,10055689,10055792,10073451,10075658,10077221,10092383,10106605,10111431,10111432,10108327,10108328,10108329,10108330,10108331,10108332,10108333,10108334,10108335,10108336,10108337,10108338,10108339,10108340,10108341,10108342,10108343,10108344,10108345,10108346,10108347,10108348,10108349,10108350,10108351,10108763,10108764,10109659,10109660,10109971,10109972,10109973,10109974,10109975,10109976,10109977,10109978,10110003,10109979,10109980,10109981,10109982,10109983,10109984,10109985,10109986,10109987,10109988,10109989,10109990,10109991,10109992,10109993,10109994,10109995,10109996,10109997,10109998,10109999,10110000,10110001,10110002,10110254,10110807,10110837,10110838,10110995,10111048,10111070,10111071,10111098,10111099,10111100,10111662,10111697,10111748,10111749,10111882,10112346,10112686,10112736,10112737,10112738,10112739,10112740,10112741,10113119,10113120,10113121,10113122,10113123,10113887,10114279,10114443,10115247,10115331,10115479,10115649,10115650,10115794,10116079,10116080,10115796,10115976,10116472,10116485,10116548,10117113,10117109,10117110,10117111,10117112,10117122,10117374,10117375,10117376,10117377,10117378,10117379,10117388,10117884,10117853,10117854,10118042,10118035,10118316,10118394,10118395,10118396,10118397,10118398,10118399,10118400,10118401,10118402,10118403,10118404,10118405,10118406,10118393,10118771,10118772,10118773,10118927,10118928,10118929,10118930,10118931,10118932,10118933,10118934,10118935,10119069,10119070,10119071,10119072,10119073,10119095,10119096,10119097,10119098,10119099,10119100,10119352,10119353,10119354,10119355,10119384,10119383,10120756,10120574,10120575,10120576,10120577,10120623,10120624,10120622,10120625,10120626,10120752,10120754,10120755,10121012,10120987,10120988,10120989,10120990,10120991,10120992,10120993,10120994,10120995,10120996,10120997,10120998,10120999,10121000,10121001,10121002,10121003,10121004,10121005,10121006,10121007,10121008,10121009,10121010,10121011,10121026,10121027,10121287,10121288,10121289,10121290,10121291,10121292,10121293,10121294,10121295,10121465,10121466,10121485,10121486,10121786,10121874,10121875,10121876,10121877,10122338,10122339,10122340,10122341,10122392,10122393,10122394,10122395,10122396,10122397,10123751,10123752,10123753,10123754,10123755,10123756,10123757,10123758,10123759,10123760,10123761,10123762,10123763,10123764,10123765,10123766,10123767,10123768,10123769,10123770,10123771,10123772,10123773,10123774,10123775,10123776,10123777,10123778,10123779,10123780,10123781,10123782,10123783,10123784,10123785,10123786,10123787,10123788,10123789,10123790,10123791,10123792,10123793,10123794,10123795,10123796,10123797,10123798,10123799,10123800,10123801,10123802,10123803,10123804,10123805,10123806,10123807,10123808,10123809,10123810,10123811,10123812,10123813,10123814,10123985,10123986,10123987,10123988,10124242,10124243,10124245,10124246)\n", "            and iil.outlet_id in %(merchants_outlet_list)s\n", "            and l0_id <> 1487\n", "            and\n", "            CASE \n", "                WHEN \n", "                    -- On below dates ESTO transfer loss was atrributed in \n", "                    -- inventory update_id = 42\n", "                       iil.pos_timestamp BETWEEN ('2022-07-30 00:00:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-07-30 23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-05 23:00:00'::TIMESTAMP -  interval '5.5 hrs') AND ('2022-08-06  23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-15 23:00:00'::TIMESTAMP -  interval '5.5 hrs') AND ('2022-08-18 23:59:59'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-23 23:45:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-24 01:15:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-24 22:00:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-25 03:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-26 23:30:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-27 04:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR iil.pos_timestamp BETWEEN ('2022-08-28 23:30:00'::TIMESTAMP - interval '5.5 hrs') AND ('2022-08-29 02:00:00'::TIMESTAMP - interval '5.5 hrs')\n", "                    OR (iil.created_by_name = 'GR0001' AND o.business_type_id = 7)\n", "                    \n", "                    \n", "                THEN \n", "                    iil.inventory_update_type_id IN (\n", "                        14, 26, 44, 45, 46, 47, 48, 74, 118, --Positive\n", "                        15, 27, 39, 40, 41, 43, 73, 117, 129, 130, 131, 132 --negative\n", "                    )\n", "                ELSE\n", "                    iil.inventory_update_type_id IN (\n", "                        14, 26, 44, 45, 46, 47, 48, 74, 118, --Positive\n", "                        15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132 --negative\n", "                    )\n", "            END\n", "         \n", "        -- Group by 1,2,3,4,5,6,7,8,9\n", "   \n", "\n", "        \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "\n", "\n", "\"\"\"\n", "\n", "inventory_variance_data1 = pd.read_sql(\n", "    inventory_variance_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data1[\"operation\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ob_flag_func_stock(x):\n", "    if x[\"last ob date\"] == 0:\n", "        return 1\n", "    elif x[\"pos_timestamp\"] <= x[\"last ob date\"]:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_temp = ob_date.merge(\n", "    inventory_variance_data1, on=[\"outlet_id\"], how=\"right\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_temp.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_temp = inventory_variance_data_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_temp[\"ob_flag\"] = inventory_variance_data_temp.apply(\n", "    lambda x: ob_flag_func_stock(x), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_temp = inventory_variance_data_temp[\n", "    inventory_variance_data_temp[\"ob_flag\"] == 1\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data = inventory_variance_data_temp.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"-\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative[\"total_price_change\"] = (\n", "    inventory_variance_data_negative[\"landing_price\"]\n", "    * inventory_variance_data_negative[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_new = inventory_variance_data_negative.groupby(\n", "    [\"outlet_id\", \"item id\"], as_index=False\n", ").agg({\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_temp = fill_rate_ds_impact_data_temp.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_temp[\"pna_qty\"] = (\n", "    fill_rate_ds_impact_data_temp_temp[\"ordered_quantity\"]\n", "    - fill_rate_ds_impact_data_temp_temp[\"procured_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_temp.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group = fill_rate_ds_impact_data_temp_temp.groupby(\n", "    [\"outlet_id\", \"item_id\"], as_index=False\n", ").agg({\"order_id\": \"nunique\", \"pna_qty\": \"sum\", \"pna_loss\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_ds_impact_data_temp_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_new.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final = inventory_variance_data_negative_new.merge(\n", "    fill_rate_ds_impact_data_temp_group,\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    left_on=[\"outlet_id\", \"item id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\n", "    \"pna_qty\"\n", "] = inventory_variance_data_negative_final[\"pna_qty\"].fillna(0)\n", "inventory_variance_data_negative_final[\n", "    \"pna_loss\"\n", "] = inventory_variance_data_negative_final[\"pna_loss\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"qty_to_consider\"] = (\n", "    inventory_variance_data_negative_final[\"change quantity\"]\n", "    - inventory_variance_data_negative_final[\"pna_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"qty_to_consider\"] = np.where(\n", "    inventory_variance_data_negative_final[\"qty_to_consider\"] < 0,\n", "    0,\n", "    inventory_variance_data_negative_final[\"qty_to_consider\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"negative_change\"] = (\n", "    inventory_variance_data_negative_final[\"total_price_change\"]\n", "    - inventory_variance_data_negative_final[\"pna_loss\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\"negative_change\"] = np.where(\n", "    inventory_variance_data_negative_final[\"negative_change\"] < 0,\n", "    0,\n", "    inventory_variance_data_negative_final[\"negative_change\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final[\n", "    inventory_variance_data_negative_final[\"pna_qty\"] > 0\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_group = (\n", "    inventory_variance_data_negative_final.groupby([\"outlet_id\"])[\"negative_change\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive = inventory_variance_data[\n", "    inventory_variance_data[\"operation\"] == \"+\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive[\"total_price_change\"] = (\n", "    inventory_variance_data_positive[\"landing_price\"]\n", "    * inventory_variance_data_positive[\"change quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_variance_raw = pd.concat(\n", "    [inventory_variance_data_negative_final, inventory_variance_data_positive]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_group = inventory_variance_data_positive.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg({\"total_price_change\": \"sum\", \"change quantity\": \"sum\", \"landing_price\": \"mean\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_group = inventory_variance_data_positive_group.fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final = inventory_variance_data_negative_group.merge(\n", "    inventory_variance_data_positive_group, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"] = (\n", "    inventory_variance_data_final[\"total_price_change\"]\n", "    - inventory_variance_data_final[\"negative_change\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"] = np.where(\n", "    inventory_variance_data_final[\"stock_variance_loss\"] > 0,\n", "    0,\n", "    inventory_variance_data_final[\"stock_variance_loss\"] * -1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final = inventory_variance_data_final[\n", "    [\"outlet_id\", \"stock_variance_loss\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[\"stock_variance_loss\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final[inventory_variance_data_final[\"outlet_id\"] == 2946]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_final.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkout To <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_q = \"\"\"\n", "\n", "with base as\n", "(select distinct date(order_checkout_ts_ist) as date_ts,\n", "order_checkout_ts_ist as checkout_timestamp,\n", "a.order_id,\n", "        a.cart_id,\n", "        m.location as city,\n", "        a.picker_id,\n", "        distinct_items_ordered as total_skus,\n", "       m.id as outlet_id,\n", "       m.name as outlet_name,\n", "       a.total_items_quantity_delivered as ipo,\n", "       datediff(seconds,a.order_checkout_ts_ist,order_picker_assigned_ts_ist) as checkout_to_picker_assigned,\n", "       datediff(seconds,order_picker_assigned_ts_ist,order_picking_started_ts_ist) as picker_assigned_picking_start,\n", "       datediff(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) as picking_start_to_picking_complete,\n", "       datediff(seconds,order_picking_completed_ts_ist,order_billing_started_ts_ist) as picking_complete_to_billing_start,\n", "       datediff(seconds,order_billing_started_ts_ist,order_billing_completed_ts_ist) as billing_start_to_billing_complete,\n", "       datediff(seconds,a.order_checkout_ts_ist,a.order_billing_completed_ts_ist) as checkout_to_bill_time\n", "    from dwh.fact_supply_chain_order_details a\n", "    inner join dwh.fact_sales_order_details b on date(b.order_create_ts_ist) = date(a.order_checkout_ts_ist)\n", "    and a.outlet_id = b.outlet_id and a.order_id = b.order_id\n", "    inner join lake_retail.console_outlet m on m.id = a.outlet_id\n", "    where is_order_delivered = true and m.business_type_id = 7 and is_rescheduled = false\n", "    and (date(a.order_checkout_ts_ist) between %(start_date)s and %(end_date)s)\n", "    and (date(b.order_create_ts_ist) between %(start_date)s and %(end_date)s)\n", "    and b.order_type in ('RetailForwardOrder','DropShippingForwardOrder')\n", "    and order_picking_completed_ts_ist is not null\n", "    and m.id in %(merchants_outlet_list)s\n", "    ),\n", "    r as \n", "    ( select distinct date_ts,\n", "    checkout_timestamp,\n", "    order_id,\n", "             cart_id,\n", "             outlet_id,\n", "             outlet_name,\n", "             city,\n", "             total_skus,\n", "             picker_id,\n", "             1.00*(checkout_to_picker_assigned)/60 as checkout_to_picker_assigned,\n", "             1.00*(picker_assigned_picking_start)/60 as picker_assigned_picking_start,\n", "             1.00*(picking_start_to_picking_complete)/60 as picking_start_to_picking_complete,\n", "             1.00*(picking_complete_to_billing_start)/60 as picking_complete_to_billing_start,\n", "             1.00*(billing_start_to_billing_complete)/60 as billing_start_to_billing_complete,\n", "             1.00*(checkout_to_bill_time)/60 as checkout_to_bill_time,\n", "             ipo\n", "        from \n", "        base \n", "    )\n", "    select * from r\n", "\"\"\"\n", "\n", "checkout_to_billed_data = pd.read_sql(\n", "    checkout_to_billed_q,\n", "    redshift,\n", "    params={\n", "        \"merchants_outlet_list\": tuple(merchants_outlet_list),\n", "        \"start_date\": start_date,\n", "        \"end_date\": end_date,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data[\"not_delayed_<=3(mins)\"] = checkout_to_billed_data.apply(\n", "    lambda x: 1 if (x[\"ipo\"] <= 8 and x[\"checkout_to_bill_time\"] <= 3) else 0,\n", "    axis=1,\n", ")\n", "\n", "checkout_to_billed_data[\"total_orders_<=8_ipo\"] = checkout_to_billed_data[\"ipo\"].apply(\n", "    lambda x: 1 if (x <= 8) else 0,\n", ")\n", "\n", "\n", "checkout_to_billed_data[\"total_orders_>8_ipo\"] = checkout_to_billed_data[\"ipo\"].apply(\n", "    lambda x: 1 if (x > 8) else 0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ob_flag_func_checkout(x):\n", "    if x[\"last ob date\"] == 0:\n", "        return 1\n", "    elif x[\"date_ts\"] <= x[\"last ob date\"]:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_temp = ob_date.merge(\n", "    checkout_to_billed_data, on=[\"outlet_id\"], how=\"right\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_temp.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_temp = checkout_to_billed_data_temp.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_temp[\"ob_flag\"] = checkout_to_billed_data_temp.apply(\n", "    lambda x: ob_flag_func_checkout(x), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_temp = checkout_to_billed_data_temp[\n", "    checkout_to_billed_data_temp[\"ob_flag\"] == 1\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new = checkout_to_billed_data_temp.groupby(\n", "    [\"outlet_id\"], as_index=False\n", ").agg(\n", "    {\n", "        \"not_delayed_<=3(mins)\": \"sum\",\n", "        \"total_orders_<=8_ipo\": \"sum\",\n", "        \"total_orders_>8_ipo\": \"sum\",\n", "        \"order_id\": \"nunique\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new = checkout_to_billed_data_new.rename(\n", "    columns={\"order_id\": \"order_count\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"%Checkout to <PERSON> Within 3 mins\"] = (\n", "    checkout_to_billed_data_new[\"not_delayed_<=3(mins)\"] * 100\n", ") / checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"per_penalty_orders\"] = (\n", "    70 - checkout_to_billed_data_new[\"%Checkout to <PERSON> Within 3 mins\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"per_penalty_orders\"] = np.where(\n", "    checkout_to_billed_data_new[\"per_penalty_orders\"] < 0,\n", "    0,\n", "    checkout_to_billed_data_new[\"per_penalty_orders\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"TAT Breach Penalty\"] = (\n", "    checkout_to_billed_data_new[\"per_penalty_orders\"]\n", "    * checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]\n", ") / 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"per_penalty_orders_overall\"] = (\n", "    100 - checkout_to_billed_data_new[\"%Checkout to <PERSON> Within 3 mins\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"per_penalty_orders_overall\"] = np.where(\n", "    checkout_to_billed_data_new[\"per_penalty_orders_overall\"] < 0,\n", "    0,\n", "    checkout_to_billed_data_new[\"per_penalty_orders_overall\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new[\"TAT Breach Loss\"] = (\n", "    checkout_to_billed_data_new[\"per_penalty_orders_overall\"]\n", "    * checkout_to_billed_data_new[\"total_orders_<=8_ipo\"]\n", ") / 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_to_billed_data_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DN Process"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_q = \"\"\"\n", "with consignment_reach as(\n", "with base AS(\n", "SELECT \n", "    *, coalesce(vehicle_type2, vehicle_type1) AS truck_type\n", "  \n", "FROM (\n", "        SELECT \n", "            t.id AS consignment_id,\n", "            d.external_id,\n", "            t.state AS current_state,\n", "            trip.trip_id,\n", "            t.source_store_id::int AS facility_id,\n", "            n.external_name AS facility_name,\n", "            m.outlet_id AS ds_outlet_id,\n", "            co.name AS ds_outlet_name,\n", "            UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "            split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "            json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS num_containers,\n", "            trip.truck_entry_wh,\n", "            trip.truck_handshake,\n", "            max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "            max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "            max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "            max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "            max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "            max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed,\n", "            trip.truck_return_wh\n", "        \n", "        FROM lake_transit_server.transit_consignment t\n", "        JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "        JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "        JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "        JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "        JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "        JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "        JOIN (\n", "                SELECT  t.id AS consignment_id,\n", "                        tsl.label_value AS trip_id\n", "                 \n", "                FROM lake_transit_server.transit_consignment t\n", "                JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "                JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id AND tsl.label_type = 'TRIP_ID'\n", "                WHERE t.install_ts >= '{start_date}'::timestamp - interval '5.5 hours'\n", "                group by 1,2\n", "             ) tc ON tc.consignment_id = t.id\n", "JOIN (\n", "        SELECT tsl.label_value AS trip_id,\n", "                min(q.entry_ts) AS truck_entry_wh,\n", "                min(tts.install_ts) AS truck_return_wh,\n", "                min(t.install_ts) AS truck_handshake\n", "        FROM lake_transit_server.transit_consignment t\n", "        JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "        JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id AND tsl.label_type = 'TRIP_ID'\n", "        JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value AND tt.shipment_label_type = 'TRIP_ID'\n", "        LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id AND tts.to_state = 'COMPLETED'\n", "        JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "        \n", "        WHERE t.install_ts >= ('{start_date}')::timestamp - interval '5.5 hours'\n", "        GROUP BY 1\n", "     ) trip ON trip.trip_id = tc.trip_id\n", "\n", "WHERE t.install_ts >= ('{start_date}')::timestamp - interval '5.5 hours'\n", "GROUP BY 1,2,3,4,5,6,7,8,t.metadata,14,15,22\n", ")\n", ")\n", ",\n", "pos AS(\n", "    SELECT \n", "        td.consignment_id AS csmt_id,\n", "        s.id sto_id,\n", "        pi.invoice_id,\n", "        max(s.dispatch_time) AS dispatch_time\n", "      \n", "    FROM lake_pos.pos_invoice pi\n", "    JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "    JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "    JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id AND co.business_type_id IN (1,12)\n", "    \n", "    WHERE pi.created_at >= ('{start_date}')::timestamp - interval '5.5 hours'\n", "    AND invoice_type_id IN (5,14,16)\n", "    GROUP BY 1,2,3\n", ")\n", ",\n", "base2 as(\n", "SELECT \n", "    (truck_handshake + interval '5.5 hrs')::date AS consignment_date,\n", "    consignment_id,\n", "    trip_id,\n", "    sto_id,\n", "    external_id invoice_id,\n", "    facility_id,\n", "    facility_name,\n", "    ds_outlet_id,\n", "    ds_outlet_name,\n", "    truck_number,\n", "    truck_type,\n", "    current_state,\n", "    num_invoices,\n", "    num_containers,\n", "    (ds_reached + interval '5.5 hrs') AS ds_reached\n", "\n", "FROM base\n", "JOIN pos ON base.consignment_id = pos.csmt_id and base.external_id = pos.invoice_id\n", "where current_state not ilike '%%cancel%%'\n", ")\n", "\n", "select\n", "    sto_id,\n", "    invoice_id,\n", "    min(ds_reached) ds_reached\n", "\n", "from base2\n", "group by 1,2\n", ")\n", "\n", "select \n", "    invoice_date_ist as date_,\n", "    a.ds_unloaded_at_ist as unloading_ts, \n", "    c.ds_reached as consignment_received_at,\n", "    case when ds_unloaded_at_ist = 0 or ds_unloaded_at_ist is null then ds_reached else ds_unloaded_at_ist end as inward_time,\n", "    grn_at_ist as grn_timestamp,\n", "    dn_at_ist as disc_created_at,\n", "    case when (inward_time <= dn_at_ist and datediff(seconds,dn_at_ist,inward_time) <= 12*60*60 or inward_time is null or inward_time = 0) then 1 else 0 end as disc_within_tat,\n", "    case when (inward_time <= grn_at_ist and datediff(seconds,grn_at_ist,inward_time) <= 12*60*60 or inward_time is null or inward_time = 0) then 1 else 0 end as within_tat,\n", "    receiver_outlet_id as rec_outlet_id, \n", "    a.receiver_outlet_name as rec_outlet_name,\n", "    a.sto_id, \n", "    a.invoice_id,\n", "    a.item_id, \n", "    item_type,\n", "    billed_qty as sto_qty, \n", "    billed_amt as sto_amt,\n", "    dispatch_qty as sto_dispatch_qty, \n", "    dispatch_amt as sto_dispatch_amt,\n", "    grn_qty as inwarded_qty,\n", "    grn_amt as inwarded_amt, \n", "    (coalesce(dn_short_qty,0) + coalesce(dn_other_qty,0)) as ttl_dn_qty, \n", "    (coalesce(dn_short_amt,0) + coalesce(dn_other_amt,0)) as ttl_dn_amt,\n", "    b2b_amt as ttl_b2b_ret_amount, \n", "    b2b_qty as ttl_b2b_ret_qty, \n", "    coalesce(dn_other_amt,0) as dn_other_amounts,\n", "    case when (dn_other_amounts - dn_other_part_excess_amt) < 0 then 0 else (dn_other_amounts - dn_other_part_excess_amt) end as dn_other_amt,\n", "    coalesce(dn_short_amt,0) as dn_short_amt,\n", "    coalesce(dn_other_qty,0) as dn_other_qty,\n", "    coalesce(dn_short_qty,0) as dn_short_qty,\n", "    (coalesce(open_dn_short_amt,0) + coalesce(open_dn_other_amt,0)) as open_dn_amt,\n", "    coalesce(open_dn_short_amt,0) as open_dn_short_amt,\n", "    coalesce(open_dn_other_amt,0) as open_dn_other_amts,\n", "    case when (open_dn_other_amts - dn_other_part_excess_amt) < 0 then 0 else (open_dn_other_amts - dn_other_part_excess_amt) end as open_dn_other_amt,\n", "    coalesce(dn_other_part_excess_amt,0) as dn_other_part_excess_amt,\n", "    coalesce(dn_other_part_damage_amt,0) as dn_other_part_damage_amt,\n", "    case when (dn_short_qty > 0 and dn_other_qty > 0) or (dn_short_amt > 0 and dn_other_amt > 0) then 1 else 0 end as dn_flag\n", "\n", "from consumer.transfer_loss_v3 a\n", "left join consignment_reach c on a.invoice_id::text = c.invoice_id::text\n", "where (invoice_date_ist between '{start_date}' and '{end_date}')\n", "and item_type in ('packaged','perishable')\n", "and rec_outlet_id in {merchants_outlet_list}\n", "and item_id not in (select distinct item_id from lake_rpc.item_tag_mapping where tag_type_id = 7 and tag_value = 2 and active = 1)\n", "and item_id not in (10045262,10045263,10045264,10045265,10045266,10045267,10045268,10035959,10045276,10045277,10045278,10045279,10045280,10045281,10045282,10045283,10045284,10045285,10045286,10045287,10045288,10045289,10045517,10047169,10048866,10048873,10050578,10050579,10050580,10050581,10050582,10050583,10050584,10050585,10050586,10050570,10050571,10050572,10050573,10050574,10050575,10050576,10050577,10052001,10053757,10053761,10053752,10053753,10053754,10053785,10053786,10053787,10053788,10053790,10053800,10053801,10053802,10053803,10053804,10053805,10053806,10053839,10054536,10055688,10055689,10055792,10073451,10075658,10077221,10092383,10106605,10111431,10111432,10108327,10108328,10108329,10108330,10108331,10108332,10108333,10108334,10108335,10108336,10108337,10108338,10108339,10108340,10108341,10108342,10108343,10108344,10108345,10108346,10108347,10108348,10108349,10108350,10108351,10108763,10108764,10109659,10109660,10109971,10109972,10109973,10109974,10109975,10109976,10109977,10109978,10110003,10109979,10109980,10109981,10109982,10109983,10109984,10109985,10109986,10109987,10109988,10109989,10109990,10109991,10109992,10109993,10109994,10109995,10109996,10109997,10109998,10109999,10110000,10110001,10110002,10110254,10110807,10110837,10110838,10110995,10111048,10111070,10111071,10111098,10111099,10111100,10111662,10111697,10111748,10111749,10111882,10112346,10112686,10112736,10112737,10112738,10112739,10112740,10112741,10113119,10113120,10113121,10113122,10113123,10113887,10114279,10114443,10115247,10115331,10115479,10115649,10115650,10115794,10116079,10116080,10115796,10115976,10116472,10116485,10116548,10117113,10117109,10117110,10117111,10117112,10117122,10117374,10117375,10117376,10117377,10117378,10117379,10117388,10117884,10117853,10117854,10118042,10118035,10118316,10118394,10118395,10118396,10118397,10118398,10118399,10118400,10118401,10118402,10118403,10118404,10118405,10118406,10118393,10118771,10118772,10118773,10118927,10118928,10118929,10118930,10118931,10118932,10118933,10118934,10118935,10119069,10119070,10119071,10119072,10119073,10119095,10119096,10119097,10119098,10119099,10119100,10119352,10119353,10119354,10119355,10119384,10119383,10120756,10120574,10120575,10120576,10120577,10120623,10120624,10120622,10120625,10120626,10120752,10120754,10120755,10121012,10120987,10120988,10120989,10120990,10120991,10120992,10120993,10120994,10120995,10120996,10120997,10120998,10120999,10121000,10121001,10121002,10121003,10121004,10121005,10121006,10121007,10121008,10121009,10121010,10121011,10121026,10121027,10121287,10121288,10121289,10121290,10121291,10121292,10121293,10121294,10121295,10121465,10121466,10121485,10121486,10121786,10121874,10121875,10121876,10121877,10122338,10122339,10122340,10122341,10122392,10122393,10122394,10122395,10122396,10122397,10123751,10123752,10123753,10123754,10123755,10123756,10123757,10123758,10123759,10123760,10123761,10123762,10123763,10123764,10123765,10123766,10123767,10123768,10123769,10123770,10123771,10123772,10123773,10123774,10123775,10123776,10123777,10123778,10123779,10123780,10123781,10123782,10123783,10123784,10123785,10123786,10123787,10123788,10123789,10123790,10123791,10123792,10123793,10123794,10123795,10123796,10123797,10123798,10123799,10123800,10123801,10123802,10123803,10123804,10123805,10123806,10123807,10123808,10123809,10123810,10123811,10123812,10123813,10123814,10123985,10123986,10123987,10123988,10124242,10124243,10124245,10124246)\n", "and dn_flag=0\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    merchants_outlet_list=tuple(merchants_outlet_list),\n", ")\n", "\n", "dn_data_temp = pd.read_sql(dn_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp[\"open_dn_other_amt\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data = dn_data_temp.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data[\"grn_within_tat\"] = np.where(\n", "    dn_data[\"within_tat\"] == 1, dn_data[\"inwarded_amt\"], 0\n", ") + np.where(dn_data[\"disc_within_tat\"] == 1, dn_data[\"ttl_dn_amt\"], 0)\n", "dn_data[\"dn_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"ttl_dn_amt\"], 0\n", ")\n", "\n", "dn_data[\"open_dn_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"open_dn_amt\"], 0\n", ")\n", "\n", "dn_data[\"open_dn_short_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"open_dn_short_amt\"], 0\n", ")\n", "\n", "dn_data[\"open_dn_other_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"open_dn_other_amt\"], 0\n", ")\n", "\n", "dn_data[\"dn_short_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"dn_short_amt\"], 0\n", ")\n", "\n", "dn_data[\"dn_other_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"dn_other_amt\"], 0\n", ")\n", "\n", "dn_data[\"dn_other_part_excess_within_tat\"] = np.where(\n", "    dn_data[\"disc_within_tat\"] == 1, dn_data[\"dn_other_part_excess_amt\"], 0\n", ")\n", "\n", "dn_data[\"grn_overall\"] = dn_data[\"ttl_dn_amt\"] + dn_data[\"inwarded_amt\"]\n", "\n", "dn_data[\"amt_to_ds\"] = np.where(\n", "    dn_data[\"sto_dispatch_amt\"] == 0, dn_data[\"sto_amt\"], dn_data[\"sto_dispatch_amt\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data = (\n", "    dn_data.groupby([\"rec_outlet_id\"])\n", "    .agg(\n", "        {\n", "            \"grn_within_tat\": \"sum\",\n", "            \"amt_to_ds\": \"sum\",\n", "            \"dn_within_tat\": \"sum\",\n", "            \"ttl_b2b_ret_amount\": \"sum\",\n", "            \"grn_overall\": \"sum\",\n", "            \"ttl_dn_amt\": \"sum\",\n", "            \"dn_other_amt\": \"sum\",\n", "            \"open_dn_amt\": \"sum\",\n", "            \"open_dn_within_tat\": \"sum\",\n", "            \"open_dn_short_within_tat\": \"sum\",\n", "            \"open_dn_short_amt\": \"sum\",\n", "            \"open_dn_other_within_tat\": \"sum\",\n", "            \"open_dn_other_amt\": \"sum\",\n", "            \"dn_other_part_excess_amt\": \"sum\",\n", "            \"dn_other_part_excess_within_tat\": \"sum\",\n", "            \"dn_short_amt\": \"sum\",\n", "            \"dn_short_within_tat\": \"sum\",\n", "            \"dn_other_within_tat\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"open_dn_other_amt\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dn_loss_data.to_csv('dn_data_dec.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"Within TAT DN other without B2B return\"] = dn_loss_data[\n", "    \"open_dn_other_within_tat\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"Post TAT DN others without B2B\"] = (\n", "    dn_loss_data[\"open_dn_other_amt\"] - dn_loss_data[\"open_dn_other_within_tat\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"open_dn_other_amt\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"dn_other_part_excess_amt\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data = dn_loss_data.rename(columns={\"rec_outlet_id\": \"outlet_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data = dn_loss_data[\n", "    [\n", "        \"outlet_id\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "    ]\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"Post TAT DN others without B2B\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_loss_data[\"Within TAT DN other without B2B return\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data[\"outlet_id\"] = gmv_data[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new = gmv_data.merge(fill_rate_ds_impact_data_new, on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new = gmv_data_new.merge(damage_data_new, on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_temp = gmv_data_new_new.merge(\n", "    inventory_variance_data_final, on=\"outlet_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_temp1 = gmv_data_new_new_temp.merge(\n", "    checkout_to_billed_data_new[[\"outlet_id\", \"TAT Breach Penalty\", \"TAT Breach Loss\"]],\n", "    on=\"outlet_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new = gmv_data_new_new_temp1.merge(\n", "    dn_loss_data, on=\"outlet_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new = gmv_data_new_new_new.rename(\n", "    columns={\"stock_variance_amount\": \"stock_variance_loss\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new[\"PNA Leverage\"] = gmv_data_new_new_new[\"net sales\"] * (\n", "    pna_leverage / 100\n", ")\n", "gmv_data_new_new_new[\"Damage Leverage\"] = gmv_data_new_new_new[\"net sales\"] * (\n", "    damage_leverage / 100\n", ")\n", "gmv_data_new_new_new[\"Variance Leverage\"] = gmv_data_new_new_new[\"net sales\"] * (\n", "    variance_leverage / 100\n", ")\n", "\n", "\n", "gmv_data_new_new_new[\"PNA Penalty\"] = gmv_data_new_new_new.apply(\n", "    lambda x: x[\"pna_loss\"] - x[\"PNA Leverage\"]\n", "    if (x[\"pna_loss\"] > x[\"PNA Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "\n", "gmv_data_new_new_new[\"Damage Penalty\"] = gmv_data_new_new_new.apply(\n", "    lambda x: x[\"total_landing_price\"] - x[\"Damage Leverage\"]\n", "    if (x[\"total_landing_price\"] > x[\"Damage Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "\n", "gmv_data_new_new_new[\"Stock Variance Penalty\"] = gmv_data_new_new_new.apply(\n", "    lambda x: x[\"stock_variance_loss\"] - x[\"Variance Leverage\"]\n", "    if (x[\"stock_variance_loss\"] > x[\"Variance Leverage\"])\n", "    else 0,\n", "    axis=1,\n", ")\n", "\n", "gmv_data_new_new_new[\"PNA %\"] = (\n", "    gmv_data_new_new_new[\"pna_loss\"] * 100\n", ") / gmv_data_new_new_new[\"net sales\"]\n", "gmv_data_new_new_new[\"Damage %\"] = (\n", "    gmv_data_new_new_new[\"total_landing_price\"] * 100\n", ") / gmv_data_new_new_new[\"net sales\"]\n", "gmv_data_new_new_new[\"Variance %\"] = (\n", "    gmv_data_new_new_new[\"stock_variance_loss\"] * 100\n", ") / gmv_data_new_new_new[\"net sales\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new[gmv_data_new_new_new[\"outlet_id\"] == 3989]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new[\n", "    gmv_data_new_new_new[\"Stock Variance Penalty\"]\n", "    == gmv_data_new_new_new[\"Stock Variance Penalty\"].max()\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new = gmv_data_new_new_new[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"net sales\",\n", "        \"PNA %\",\n", "        \"Damage %\",\n", "        \"Variance %\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"pna_loss\",\n", "        \"fill_rate_impact_order_count\",\n", "        \"pna_qty\",\n", "        \"damage_item_count\",\n", "        \"total_landing_price\",\n", "        \"stock_variance_loss\",\n", "        \"TAT Breach Penalty\",\n", "        \"PNA Leverage\",\n", "        \"Damage Leverage\",\n", "        \"V<PERSON>ce Leverage\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_data_new_new_new.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final = gmv_data_new_new_new[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"net sales\",\n", "        \"pna_loss\",\n", "        \"PNA %\",\n", "        \"total_landing_price\",\n", "        \"Damage %\",\n", "        \"stock_variance_loss\",\n", "        \"Variance %\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"TAT Breach Penalty\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final = deduction_final.rename(\n", "    columns={\n", "        \"net sales\": \"Delivered GMV\",\n", "        \"pna_loss\": \"PNA Loss\",\n", "        \"total_landing_price\": \"Damage Loss\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final[\"Total Penalty\"] = (\n", "    deduction_final[\"PNA Penalty\"]\n", "    + deduction_final[\"Damage Penalty\"]\n", "    + deduction_final[\"TAT Breach Penalty\"]\n", "    + deduction_final[\"Stock Variance Penalty\"]\n", "    + deduction_final[\"Within TAT DN other without B2B return\"]\n", "    + deduction_final[\"Post TAT DN others without B2B\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# deduction_final.to_csv('Nov-Dec deduction.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    deduction_final,\n", "    \"1XA0TuEaiVdQp-aui-5I78UANtCzHGxeXVJ6Q4_PJAaE\",\n", "    \"Deduction\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final[deduction_final[\"outlet_id\"] == 995]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(deduction_final[\"outlet_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final = deduction_final.round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final = deduction_final.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["automate_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["automate_list = dict(zip(automate_data[\"outlet_id\"], automate_data[\"deduction_flag\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data = pb.from_sheets(\n", "    \"1haV71JdPu3RhNi801bC2JZ-iE1Q27iQDh9lGGaTZHS0\", \"Email ids\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data = merchant_rollout_data[\n", "    [\"Outlet id\", \"Partner's Email\", \"POC 1\", \"POC 2\", \"Extra\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_rollout_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailing_list = merchant_rollout_data.set_index(\"Outlet id\").T.to_dict(\"list\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_dict = dict(zip(deduction_final.outlet_id, deduction_final.outlet_name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_to_attach = [\n", "    \"pna_raw_data.csv\",\n", "    \"damage_raw_data.csv\",\n", "    \"stock_variance_raw.xlsx\",\n", "    \"checkout_to_billed_data_raw_data.zip\",\n", "    \"dn_raw_data.zip\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_positive_raw = inventory_variance_data_positive[\n", "    [\n", "        \"outlet_id\",\n", "        \"last ob date\",\n", "        \"pos_timestamp\",\n", "        \"pos_time\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"item id\",\n", "        \"item_name\",\n", "        \"operation\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"inventory update type\",\n", "        \"employee id\",\n", "        \"category\",\n", "        \"total_landing_price\",\n", "        \"ob_flag\",\n", "        \"total_price_change\",\n", "    ]\n", "]\n", "\n", "inventory_variance_data_negative_raw1 = inventory_variance_data_negative[\n", "    [\n", "        \"outlet_id\",\n", "        \"last ob date\",\n", "        \"pos_timestamp\",\n", "        \"pos_time\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"item id\",\n", "        \"item_name\",\n", "        \"operation\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"inventory update type\",\n", "        \"employee id\",\n", "        \"category\",\n", "        \"total_landing_price\",\n", "        \"ob_flag\",\n", "        \"total_price_change\",\n", "    ]\n", "]\n", "\n", "inventory_variance_data_negative_raw = inventory_variance_data_negative_final.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw = inventory_variance_data_negative_raw[\n", "    [\n", "        \"outlet_id\",\n", "        \"item id\",\n", "        \"total_price_change\",\n", "        \"change quantity\",\n", "        \"landing_price\",\n", "        \"item_id\",\n", "        \"order_id\",\n", "        \"pna_qty\",\n", "        \"qty_to_consider\",\n", "        \"negative_change\",\n", "        \"pna_loss\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_raw = inventory_variance_data_negative_raw.rename(\n", "    columns={\n", "        \"change quantity\": \"Negative Qty\",\n", "        \"qty_to_consider\": \"Negative_qty - pna_qty\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_data_positive_raw.to_csv('stock_variance_positive_raw.csv',index=False)\n", "# # inventory_variance_data_negative_raw.to_csv('stock_variance_negative_raw.csv',index=False)\n", "# inventory_variance_data_negative_raw.to_csv('stock_variance_negative_pna_raw.csv',index=False)\n", "# inventory_variance_data_negative_raw1.to_csv('stock_variance_negative_raw.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_variance_data_negative_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file = pd.read_csv('stock_variance_raw.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file = inventory_variance_raw_data_file[['outlet_id', 'item id', 'change quantity',\n", "#        'landing_price', 'pna_qty', 'qty_to_consider',\n", "#         'positive_change_quantity', 'net_negative_qty',\n", "#        'net_negative_qty_final', 'stock_variance_loss', 'negative_loss',\n", "#        'positive_loss']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file = inventory_variance_raw_data_file.rename(columns={'change quantity':'negative_qty','qty_to_consider':'negative_qty - pna_qty','positive_change_quantity':'positive_quantity'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inventory_variance_raw_data_file['landing_price']=inventory_variance_raw_data_file['landing_price'].round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final = deduction_final.round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final[\n", "    [\n", "        \"Delivered GMV\",\n", "        \"PNA Loss\",\n", "        \"Damage Loss\",\n", "        \"stock_variance_loss\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"TAT Breach Penalty\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "        \"Total Penalty\",\n", "    ]\n", "] = deduction_final[\n", "    [\n", "        \"Delivered GMV\",\n", "        \"PNA Loss\",\n", "        \"Damage Loss\",\n", "        \"stock_variance_loss\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"TAT Breach Penalty\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "        \"Total Penalty\",\n", "    ]\n", "].astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl\n", "!pip install xlsxwriter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(mailing_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dn_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(merchants_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if automate_data[\"script_run\"].unique()[0].lower() == \"yes\":\n", "    mail_triggered_to = []\n", "    count = 0\n", "    for i in merchants_outlet_list:\n", "\n", "        if automate_list[i].lower() == \"yes\":\n", "            for f in files_to_attach:\n", "                #         print('running')\n", "                if os.path.exists(f):\n", "                    os.remove(f)\n", "            fill_rate_ds_impact_data[\n", "                fill_rate_ds_impact_data[\"outlet_id\"] == int(i)\n", "            ].to_csv(\"pna_raw_data.csv\", index=False)\n", "            damage_data[damage_data[\"outlet_id\"] == int(i)].to_csv(\n", "                \"damage_raw_data.csv\", index=False\n", "            )\n", "            # dn_data[dn_data['rec_outlet_id']==int(i)].to_csv(\"dn_raw_data.csv\",index=False)\n", "            dn_data[dn_data[\"rec_outlet_id\"] == int(i)].to_csv(\n", "                \"dn_raw_data.zip\",\n", "                compression={\"method\": \"zip\", \"archive_name\": \"dn_raw_data.csv\"},\n", "                index=False,\n", "            )\n", "            # inventory_variance_raw_data_file[inventory_variance_raw_data_file['outlet_id']==int(i)].to_csv(\"stock_variance_raw_data.zip\",compression={'method': 'zip', 'archive_name': 'stock_variance_raw_data.csv'},index=False)\n", "            inventory_variance_data_positive_raw[\n", "                inventory_variance_data_positive_raw[\"outlet_id\"] == int(i)\n", "            ].to_csv(\n", "                \"stock_variance_positive_raw_data.zip\",\n", "                compression={\n", "                    \"method\": \"zip\",\n", "                    \"archive_name\": \"stock_variance_positive_raw_data.csv\",\n", "                },\n", "                index=False,\n", "            )\n", "            inventory_variance_data_negative_raw[\n", "                inventory_variance_data_negative_raw[\"outlet_id\"] == int(i)\n", "            ].to_csv(\n", "                \"stock_variance_negative_raw_data.zip\",\n", "                compression={\n", "                    \"method\": \"zip\",\n", "                    \"archive_name\": \"stock_variance_negative_raw_data.csv\",\n", "                },\n", "                index=False,\n", "            )\n", "            checkout_to_billed_data[\n", "                checkout_to_billed_data[\"outlet_id\"] == int(i)\n", "            ].to_csv(\n", "                \"checkout_to_billed_data_raw_data.zip\",\n", "                compression={\n", "                    \"method\": \"zip\",\n", "                    \"archive_name\": \"checkout_to_billed_data_raw_data.csv\",\n", "                },\n", "                index=False,\n", "            )\n", "\n", "            fieldnames = [\"positive_dump\", \"Negative dump\", \"Negative Dump with PNA\"]\n", "\n", "            writer = pd.ExcelWriter(\"stock_variance_raw.xlsx\")\n", "\n", "            # for col in fieldnames:\n", "            # assuming your behavior here replace with desired result.\n", "            inventory_variance_data_positive_raw[\n", "                inventory_variance_data_positive_raw[\"outlet_id\"] == int(i)\n", "            ].to_excel(writer, sheet_name=\"positive_dump\")\n", "            inventory_variance_data_negative_raw1[\n", "                inventory_variance_data_negative_raw1[\"outlet_id\"] == int(i)\n", "            ].to_excel(writer, sheet_name=\"Negative dump\")\n", "            inventory_variance_data_negative_raw[\n", "                inventory_variance_data_negative_raw[\"outlet_id\"] == int(i)\n", "            ].to_excel(writer, sheet_name=\"Negative Dump with PNA\")\n", "            writer.save()\n", "\n", "            loader = jinja2.FileSystemLoader(\n", "                searchpath=\n", "                # \"/shared/Shared/<PERSON><PERSON>hat <PERSON>/Merchant\"\n", "                \"/usr/local/airflow/dags/repo/dags/supply_chain/store_ops/report/test1\"\n", "                #         \"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/merchant/etl/merchant_payout_mailer\"\n", "            )\n", "            tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "            template = tmpl_environ.get_template(\"deduction_email_template.html\")\n", "            if (start_date.month + 1) > 12:\n", "                next_start_month = 1\n", "                next_end_month = next_start_month + 1\n", "            else:\n", "                next_start_month = start_date.month + 1\n", "                next_end_month = next_start_month % 12 + 1\n", "            if (start_date.month - 1) == 0:\n", "                end_month_deduct = 1\n", "            else:\n", "                end_month_deduct = end_date.month - 1\n", "\n", "            message_text = template.render(\n", "                rca_details=deduction_final[\n", "                    deduction_final[\"outlet_id\"] == int(i)\n", "                ].to_dict(\"records\"),\n", "                Outlet_Name=merchant_dict[int(i)],\n", "                Outlet_ID=i,\n", "                next_month_start=str(calendar.month_abbr[next_start_month]),\n", "                next_month_end=str(calendar.month_abbr[next_end_month]),\n", "                deduction_month_start=str(calendar.month_abbr[start_date.month]),\n", "                deduction_month_end=str(calendar.month_abbr[end_date.month]),\n", "            )\n", "            deduction_month_start = str(calendar.month_abbr[start_date.month])\n", "            deduction_month_end = str(calendar.month_abbr[end_date.month])\n", "            subject = (\n", "                \"Penalty charges for underperformance (\"\n", "                + deduction_month_start\n", "                + \"-\"\n", "                + deduction_month_end\n", "                + \") || \"\n", "                + str(i)\n", "            )\n", "            pb.send_email(\n", "                from_email=\"<EMAIL>\",\n", "                to_email=list(filter(None, mailing_list[i])),\n", "                # mailing_list[i],\n", "                subject=subject,\n", "                html_content=message_text,\n", "                files=files_to_attach,\n", "                # cc=cc_mail_list,\n", "                bcc=[\n", "                    \"<EMAIL>\",\n", "                    \"<EMAIL>\",\n", "                    \"<EMAIL>\",\n", "                ],\n", "                mime_subtype=\"mixed\",\n", "                mime_charset=\"utf-8\",\n", "            )\n", "            time.sleep(2)\n", "            mail_triggered_to.append(merchant_dict[int(i)])\n", "            count += 1\n", "            # print(i)\n", "            # break\n", "            # if count == 3:\n", "            # break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-merchant-sla-alert\",\n", "    text=f\"\"\"Deduction Email Sent To {len(mail_triggered_to)} Merchants: {mail_triggered_to} \n", "\"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(mail_triggered_to)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deduction_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table = deduction_final.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table = data_to_push_in_table.rename(\n", "    columns={\n", "        \"PNA %\": \"PNA_per\",\n", "        \"Damage %\": \"Damage_per\",\n", "        \"Variance %\": \"stock_variance_per\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table[\"updated_time_ist\"] = datetime.now() + timed<PERSON>ta(minutes=330)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table[\n", "    [\n", "        \"Delivered GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA_per\",\n", "        \"Damage Loss\",\n", "        \"Damage_per\",\n", "        \"stock_variance_loss\",\n", "        \"stock_variance_per\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"TAT Breach Penalty\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "        \"Total Penalty\",\n", "    ]\n", "] = data_to_push_in_table[\n", "    [\n", "        \"Delivered GMV\",\n", "        \"PNA Loss\",\n", "        \"PNA_per\",\n", "        \"Damage Loss\",\n", "        \"Damage_per\",\n", "        \"stock_variance_loss\",\n", "        \"stock_variance_per\",\n", "        \"PNA Penalty\",\n", "        \"Damage Penalty\",\n", "        \"Stock Variance Penalty\",\n", "        \"TAT Breach Penalty\",\n", "        \"TAT Breach Loss\",\n", "        \"Within TAT DN other without B2B return\",\n", "        \"Post TAT DN others without B2B\",\n", "        \"Total Penalty\",\n", "    ]\n", "].astype(\n", "    float\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_to_push_in_table.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_deduction_details\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"outlet_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"Outlet ID\",\n", "        },\n", "        {\n", "            \"name\": \"outlet_name\",\n", "            \"type\": \"varchar(300)\",\n", "            \"description\": \"Outlet name\",\n", "        },\n", "        {\n", "            \"name\": \"Delivered GMV\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Delivered GMV\",\n", "        },\n", "        {\n", "            \"name\": \"PNA Loss\",\n", "            \"type\": \"float\",\n", "            \"description\": \"PNA Loss\",\n", "        },\n", "        {\n", "            \"name\": \"PNA_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"% PNA of the Delivered GMV\",\n", "        },\n", "        {\n", "            \"name\": \"Damage Loss\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Loss for Damages\",\n", "        },\n", "        {\n", "            \"name\": \"Damage_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"% PNA of the Delivered GMV\",\n", "        },\n", "        {\n", "            \"name\": \"stock_variance_loss\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Stock Variance Loss\",\n", "        },\n", "        {\n", "            \"name\": \"stock_variance_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"% Stock Variance of the Delivered GMV\",\n", "        },\n", "        {\n", "            \"name\": \"PNA Penalty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"PNA Penalty\",\n", "        },\n", "        {\n", "            \"name\": \"Damage Penalty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Damage Penalty\",\n", "        },\n", "        {\n", "            \"name\": \"Stock Variance Penalty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Stock Variance Penalty\",\n", "        },\n", "        {\n", "            \"name\": \"TAT Breach Penalty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"TAT Breach Penalty\",\n", "        },\n", "        {\n", "            \"name\": \"TAT Breach Loss\",\n", "            \"type\": \"float\",\n", "            \"description\": \"TAT Breach Loss\",\n", "        },\n", "        {\n", "            \"name\": \"Within TAT DN other without B2B return\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Within TAT DN other without B2B return\",\n", "        },\n", "        {\n", "            \"name\": \"Post TAT DN others without B2B\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Post TAT DN others without B2B\",\n", "        },\n", "        {\n", "            \"name\": \"Total Penalty\",\n", "            \"type\": \"float\",\n", "            \"description\": \"Total Penalty\",\n", "        },\n", "        {\n", "            \"name\": \"updated_time_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"Updated time\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"outlet_id\", \"updated_time_ist\"],\n", "    \"sortkey\": [\"updated_time_ist\"],\n", "    \"incremental_key\": \"updated_time_ist\",\n", "    \"table_description\": \"Table contains Merchant Dark stores Deduction Details\",\n", "    \"load_type\": \"upsert\",\n", "}\n", "# pb.to_redshift(data_to_push_in_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}