<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    border-radius: 5px 5px 0 0;
    width: 100%;
    max-width: 900px;
}
.tg td {
    font-family: courier new;
    font-size: 14px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #333;
    background-color: #fff;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: normal;
    padding: 10px 5px;
    border-style: solid;
    border-width: 0px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    border: 1px solid black;
    color: #fff;
    background-color: #009879;
}
.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}
.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}  
  

 

 

    

</style>
</head>
<p>Dear Partner,</p>

<p>This email is to inform you about your store’s performance which has fallen short of certain SLAs basis set benchmarks for an ideal store.</p>
<p>Attaching your performance summary under the heads ‘Order Processing’  and ‘Inventory’ along with the penalty amount:</p>

<p style="font-size:80%;"><b>Calculation Period: 26 {{deduction_month_start}} 2022  to 25 {{deduction_month_end}} 2022</b></p>

<table class="tg">
    <thead>
    <tr>
      <th>SLA</th>
<!--       <th>GMV</th> -->
      <th>Total Loss</th>
      <th>Penalty</th>
    </tr>
  </thead>
    <tbody>
<!--         <tr>
            <th class="tg-0lax"><span style="font-weight:bold">SLA</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Outlet Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Delivered GMV</span></th>

             
        </tr> -->
        {% for row in rca_details %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>In-store time, IPO <=8 (TAT)</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['TAT Breach Loss'] }}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['TAT Breach Penalty'] }}</b>
            </td>
         <tr/>   
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>PNA</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['PNA Loss'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['PNA Penalty'] }}</b>
            </td>
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>Damages</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Damage Loss'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Damage Penalty'] }}</b>
            </td> 
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>Stock Variance</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['stock_variance_loss'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Stock Variance Penalty'] }}</b>
            </td> 
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>DN others within TAT but no B2B return</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Within TAT DN other without B2B return'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Within TAT DN other without B2B return'] }}</b>
            </td> 
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>DN others post TAT but no B2B return</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Post TAT DN others without B2B'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Post TAT DN others without B2B'] }}</b>
            </td> 
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>Total</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b></b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Total Penalty'] }}</b>
            </td> 
        <tr/>
             
        {% endfor %}
    
    </tbody>
</table>

<p><i><span>&#42;</span>Stock Variance penalty is calculated on a value basis excluding F&V. This also includes the positive variance of the inventory.</i></p>


<p> The penalty amount will be deducted from your payout in this payment cycle. We recommend taking immediate measures to improve the store's performance and avoid any performance-related penalty charges in the future.</p>

<!-- <p>Further, the loss due to non-adherence to the DN process is summarized below.</p>


<table class="tg">
    <thead>
    <tr>
      <th>DN SLA</th>
      <th>Actual loss Amount</th>
      <th>Actual deduction this time </th>
    </tr>
  </thead>
    <tbody>

        {% for row in rca_details %}  
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>Total DN post TAT</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['DN Post TAT'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Actual DN post TAT'] }}</b>
            </td>
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>DN others within TAT but no B2B return</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['DN others Without B2B return'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['DN others Without B2B return'] }}</b>
            </td> 
        <tr/>
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>Total</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Total DN Loss'] }}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ row['Total DN Deduction'] }}</b>
            </td> 
        <tr/>
             
        {% endfor %}

    
    </tbody>
</table>

<p>Although as conveyed earlier we were to impose a penalty on the entire DN post TAT amount, however, as a goodwill gesture this time there will be no penalty imposed on you for the entire DN post TAT amount but only on DN others without B2B return. 
Please perform DN within scheduled TAT, this might have implications from the next payout.</p>

 -->
 
<p>Kindly reach out to us if you have any queries.</p>

    


<br/>
<p><i>Please find the attached raw data for PNA, Damage, Stock Variance, Instore Time (TAT), DN Loss</i></p>

<br/>


<p>Regards,<br />
    Team Blinkit</p>
<p></p>