{"cells": [{"cell_type": "code", "execution_count": null, "id": "fbd9ba0a-987e-403a-8f9b-f2b12a483010", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "cd0f7372-6e4e-4b26-9676-a9399cc0d584", "metadata": {}, "outputs": [], "source": ["sto_base_query = f\"\"\"WITH pre_esto AS\n", "  (SELECT distinct a.*,\n", "          b.facility_id AS sender_facility_id,\n", "          cf.name AS sender_facility_name,\n", "          c.facility_id AS receiver_facility_id,\n", "          f.name AS receiver_facility_name\n", "   FROM metrics.esto_details a\n", "---   left join metrics.multiRep_data mr on mr.bulk_sto_id = a.bulk_sto_id and a.ars_mode='normal' and \n", "   ---mr.sto_created_at >= (CURRENT_DATE -7)::TIMESTAMP \n", "---   AND mr.sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP\n", "   ---left join lake_ims.ims_inward_invoice bb on a.invoice_id = bb.vendor_invoice_id and bb.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list cc on cc.grn_id = bb.grn_id and cc.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list_item d on d.putlist_id=cc.id and a.item_id = d.item_id and d.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) b ON a.sender_outlet_id = b.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) cf ON cf.id = b.facility_id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) c ON a.receiving_outlet_id = c.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) f ON f.id = c.facility_id\n", "\n", "   WHERE b.name NOT LIKE '%%Infra%%'\n", "   AND a.sto_created_at >= (CURRENT_DATE -7)::TIMESTAMP \n", "   AND a.sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP \n", "   and a.ars_mode = 'normal'  \n", "   ),\n", "\n", "final_esto AS\n", "  (SELECT sto_id,\n", "          ars_run_id, item_id,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiving_outlet_id as receiver_outlet_id,\n", "          receiver_facility_name,\n", "          sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "         \n", "         avg(reserved_quantity) AS reserved_qty,\n", "         avg(billed_quantity) AS billed_quantity,\n", "         avg(inwarded_quantity) AS inwarded_qty,\n", "         avg(reserved_quantity_after_shortfall) AS req_qty_sf,\n", "         avg(putaway_quantity) AS putaway_quantity,\n", "         ---avg(placed_quantity) as putaway_quantity,\n", "         avg(disc_qty) as disc_qty,\n", "        \n", "         max(sto_type) AS sto_type,\n", "         max(sto_state) AS sto_state,\n", "         max(invoice_state) AS invoice_state, \n", " count(CASE\n", "           WHEN reserved_quantity > 0 THEN item_id\n", "       END) AS res_sku,\n", " count(CASE\n", "           WHEN billed_quantity > 0 THEN item_id\n", "       END) AS bil_sku,\n", " count(CASE\n", "           WHEN inwarded_quantity > 0 THEN item_id\n", "       END) AS inw_sku,\n", " count(CASE\n", "           WHEN reserved_quantity_after_shortfall > 0 THEN item_id\n", "       END) AS req_sf_sku\n", "   FROM pre_esto a\n", "   WHERE \n", "   ---STO_STATE IN ('Inward') \n", "   STO_STATE NOT IN ('Manual Expiry')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,6,7,8,9,10,11,12,13\n", "   HAVING sto_created_at >= (CURRENT_DATE -9) ::TIMESTAMP \n", "   AND sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP ),\n", "\n", "\n", "final_status AS\n", "  (SELECT *,\n", "          CASE\n", "              WHEN invoice_state = 'Raised' THEN 'In-Transit'\n", "              WHEN invoice_state != '' THEN 'Delivered'\n", "              WHEN sto_state = 'Expired' THEN 'Expired before Delivery'\n", "              WHEN sto_state = 'Created' THEN 'Need to Pick'\n", "              ELSE 'other'\n", "          END AS final_status\n", "   FROM final_esto a\n", "   LEFT JOIN\n", "     (SELECT DISTINCT facility_id,\n", "                      business_type_id\n", "      FROM lake_retail.console_outlet) bc ON a.receiver_facility_id = bc.facility_id\n", "   WHERE sender_facility_id NOT IN (153)\n", "     AND bc.business_type_id = 7 ),\n", "     \n", "\n", "\n", "summary AS\n", "  (SELECT sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "          ars_run_id, \n", "          sto_id, \n", "          item_id, \n", "          -- final_status,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiver_outlet_id,\n", "          receiver_facility_name,\n", " sum(reserved_qty) AS total_reserved_qty,\n", " sum(req_qty_sf) AS total_req_qty_after_shortfall,\n", " sum(case when invoice_state<>'Raised' then billed_quantity end) AS total_billed_qty,\n", " sum(case when invoice_state<>'Raised' then putaway_quantity end) AS putaway_quantity,\n", " sum(inw_sku) AS inwd_sku,\n", " sum(case when invoice_state<>'Raised' then inwarded_qty end) AS grn_delivered,\n", "     sum(disc_qty) AS disc_qty\n", "   FROM final_status a\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,5,6,7,8,9,10,11,12,13),\n", "            \n", "zone_tbl_treatment AS\n", "(SELECT facility_id,\n", "  max(ZONE) AS ZONE\n", "FROM metrics.outlet_zone_mapping\n", "GROUP BY 1)\n", "\n", "\n", "SELECT DISTINCT b.zone,\n", "                a.*\n", "FROM summary a\n", "LEFT JOIN zone_tbl_treatment b ON a.sender_facility_id = b.facility_id\n", "\"\"\"\n", "\n", "sto_base_df = pd.read_sql(sto_base_query, CON_REDSHIFT)\n", "# sto_base_df[[\"inwd_sku\", \"grn_delivered\"]] = (\n", "#     sto_base_df[[\"inwd_sku\", \"grn_delivered\"]].fillna(0).astype(int)\n", "# )\n", "# sto_base_df = sto_base_df.rename(columns={\"ars_run_id\": \"run_id\"})\n", "\n", "sto_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "d31a2ade-8121-494b-987d-bc569fdae799", "metadata": {"tags": []}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8a7c27ab-eca8-484e-bfb1-be248c683920", "metadata": {}, "outputs": [], "source": ["sto_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "55c60131-164f-41bc-8546-2cdc3aad053f", "metadata": {}, "outputs": [], "source": ["sto_base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8c284dea-acc0-40d2-8a53-e5a3958427c2", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"sto_created_at\"] = pd.to_datetime(sto_base_df[\"sto_created_at\"])\n", "sto_base_df[\"sto_created_hour\"] = sto_base_df[\"sto_created_at\"].dt.hour\n", "sto_base_df[\"sto_created_dt\"] = sto_base_df[\"sto_created_at\"].dt.date\n", "sto_base_df[\"sto_created_minute\"] = sto_base_df[\"sto_created_at\"].dt.minute\n", "sto_base_df[\"sto_created_time\"] = sto_base_df[\"sto_created_at\"].dt.time\n", "\n", "sto_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "fb3f146c-baf7-49d5-a3b1-6d41d901b9a8", "metadata": {}, "outputs": [], "source": ["# sto_base_df_indent = sto_base_df.groupby([\"sto_created_dt\",\n", "#             \"sender_facility_id\",\n", "#             \"sender_outlet_id\",\n", "#             \"sender_facility_name\",\n", "#             \"receiver_facility_id\",\n", "#             \"receiver_outlet_id\",\n", "#             \"receiver_facility_name\"]).agg({'indent':'nunique'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "f27baf38-0364-4237-a196-9fa1a35f9a22", "metadata": {}, "outputs": [], "source": ["# sto_base_df_indent.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa3bfe77-2826-4406-ae9a-4ab1b43a5b18", "metadata": {}, "outputs": [], "source": ["sto_base_df.sender_facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "22d8b9b3-fc55-4390-a507-b5bb78aedbb3", "metadata": {}, "outputs": [], "source": ["# Bengaluru - >=5:30 PM (3)\n", "# Kolkata - >=6:00 PM (517,24)\n", "# <PERSON><PERSON>, G6, G7 - >=03:00 PM (554,555,92)\n", "# Mumbai - >=06:00 PM (513)\n", "# Lucknow - >=05:00 PM (1206)"]}, {"cell_type": "code", "execution_count": null, "id": "3154b925-14c4-4821-b091-8daee26b7b8a", "metadata": {}, "outputs": [], "source": ["sto_base_df[[\"sender_facility_name\", \"sender_facility_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "70887e3c-59ff-4470-8699-22f747d9f008", "metadata": {}, "outputs": [], "source": ["def indent_type(x):\n", "    if x[\"sender_facility_id\"] in [1872, 1, 603]:\n", "        if x[\"sto_created_hour\"] >= 6 and x[\"sto_created_hour\"] <= 9:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [92, 554, 555, 1206, 517, 513, 43]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 10:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [264]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 11:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [1873]:\n", "        if x[\"sto_created_hour\"] >= 7 and x[\"sto_created_hour\"] <= 16:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    elif x[\"sender_facility_id\"] in [1320]:\n", "        if x[\"sto_created_hour\"] >= 6 and x[\"sto_created_hour\"] <= 10:\n", "            return \"Night\"\n", "        else:\n", "            return \"Day\"\n", "    else:\n", "        return \"Day\""]}, {"cell_type": "code", "execution_count": null, "id": "19bedb64-48ea-4b65-abca-83dce2907d4d", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"sto_created_slot\"] = sto_base_df.apply(lambda x: indent_type(x), axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "d6c40230-6fd4-4e69-b665-9dbfca4250ac", "metadata": {}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "63ac1151-fb8d-44f9-8b2f-57bb17ce45ca", "metadata": {}, "outputs": [], "source": ["# sto_base_df[\"sto_created_slot\"] = np.where(\n", "#     sto_base_df[\"sender_facility_id\"] == 264,\n", "#     np.where(\n", "#         (sto_base_df[\"sto_created_hour\"] >= 7)\n", "#         & (sto_base_df[\"sto_created_hour\"] <= 12),\n", "#         \"Night\",\n", "#         \"Day\",\n", "#     ),\n", "#     np.where(\n", "#         (sto_base_df[\"sto_created_hour\"] >= 7) & (sto_base_df[\"sto_created_hour\"] < 19),\n", "#         \"Night\",\n", "#         \"Day\",\n", "#     ),\n", "# )\n", "\n", "# sto_base_df[\"sto_created_slot\"] = np.where(\n", "#     sto_base_df[\"sender_facility_id\"] == 264,\n", "#     np.where(\n", "#         (sto_base_df[\"sto_created_hour\"] == 12)\n", "#         & (sto_base_df[\"sto_created_minute\"] >= 30),\n", "#         \"Day\",\n", "#         sto_base_df[\"sto_created_slot\"],\n", "#     ),\n", "#     sto_base_df[\"sto_created_slot\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "c38b1ca9-35b0-43f2-9f6a-d5e3f328627e", "metadata": {}, "outputs": [], "source": ["sto_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "baa58d1b-284d-48a3-aad2-0012aabc0dde", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"disc_qty\"] = sto_base_df[\"disc_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "8e5ded43-0c3f-4ede-88d7-1d6685939411", "metadata": {}, "outputs": [], "source": ["sto_base_df[sto_base_df.sto_created_slot == \"Day\"].head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f533fee-dadc-431a-bd4d-8b4b53b440ef", "metadata": {}, "outputs": [], "source": ["sto_base_df[\"vehicle_arrival_time\"] = np.where(\n", "    sto_base_df[\"consignment_received_at\"].isnull(),\n", "    sto_base_df[\"grn_created_at\"],\n", "    sto_base_df[\"consignment_received_at\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9b5b2f16-bad3-45c7-ab6d-3f5ca888d20c", "metadata": {}, "outputs": [], "source": ["sto_base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "afd32465-622b-42e2-ac47-2339a1518c2c", "metadata": {}, "outputs": [], "source": ["sto_base_df_raw = (\n", "    sto_base_df.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"sto_created_slot\",\n", "            \"sto_id\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8ae877fb-8951-497b-85fc-af601e84e84a", "metadata": {}, "outputs": [], "source": ["datetime.now().date()"]}, {"cell_type": "code", "execution_count": null, "id": "68bdfa68-f4d8-4142-ba66-7fe77b50b348", "metadata": {}, "outputs": [], "source": ["sto_base_df_raw = sto_base_df_raw[\n", "    (sto_base_df_raw[\"sto_created_dt\"] <= datetime.now().date() - timed<PERSON>ta(days=3))\n", "    & (sto_base_df_raw[\"sto_created_dt\"] <= datetime.now().date())\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "074ae8fa-d44c-459a-acbb-6193768bdcef", "metadata": {}, "outputs": [], "source": ["sto_base_df_raw.shape"]}, {"cell_type": "code", "execution_count": null, "id": "391dc611-71ec-4e3b-943c-90037fe9d9d3", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sto_base_df_raw,\n", "    \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\",\n", "    \"STO raw data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "43bedc98-639b-442e-8887-5c09b275ff5b", "metadata": {}, "outputs": [], "source": ["sto_base_df_new = (\n", "    sto_base_df.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "            \"sto_created_slot\",\n", "        ]\n", "    )\n", "    .agg({\"vehicle_arrival_time\": \"min\"})\n", "    .reset_index()\n", ")\n", "\n", "sto_base_df_new = sto_base_df_new.rename(\n", "    columns={\"vehicle_arrival_time\": \"vehicle_arrival_time_min\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "16154308-81a3-44ad-b0e3-67274519fe3d", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp = sto_base_df.merge(\n", "    sto_base_df_new,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "        \"sto_created_slot\",\n", "    ],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c8302ee-49fe-4e9f-b2ac-6dc625b97a57", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d005c252-6145-4d45-abea-4a2d5d081ba7", "metadata": {}, "outputs": [], "source": ["sto_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e3fa8215-25b3-4d3a-b4df-785bfc0b9228", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "98b32c11-0f7d-48dd-ab80-8a8f4c4f62d6", "metadata": {}, "outputs": [], "source": ["def time_fix(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=6, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=6, minute=0, second=0)\n", "\n", "\n", "def time_fix_night(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=14, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=14, minute=0, second=0)\n", "\n", "\n", "def time_manual_upper(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=6, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=1)).replace(hour=6, minute=0, second=0)\n", "\n", "\n", "def time_manual_lower(x):\n", "    if x.hour >= 0 and x.hour <= 5:\n", "        return (x - <PERSON><PERSON><PERSON>(days=1)).replace(hour=19, minute=0, second=0)\n", "    elif x.hour <= 23 and x.hour >= 6:\n", "        return (x + <PERSON><PERSON><PERSON>(days=0)).replace(hour=19, minute=0, second=0)"]}, {"cell_type": "code", "execution_count": null, "id": "264553b4-1a52-4b37-a38c-599f9f1b3106", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"putaway_7pm\"] = sto_base_df_temp[\"vehicle_arrival_time_min\"].apply(\n", "    lambda x: time_manual_lower(x)\n", ")\n", "\n", "sto_base_df_temp[\"putaway_6am\"] = sto_base_df_temp[\"vehicle_arrival_time_min\"].apply(\n", "    lambda x: time_manual_upper(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eebf96c5-d4bb-4b47-904b-ec102cf73de9", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"inward_time_new\"] = sto_base_df_temp[\n", "    \"vehicle_arrival_time_min\"\n", "].apply(lambda x: time_fix_night(x))"]}, {"cell_type": "code", "execution_count": null, "id": "d2035c8e-e30c-4c1d-ae63-e160ae597713", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp[\"vehicle_arrival_time_new\"] = sto_base_df_temp[\n", "    \"vehicle_arrival_time_min\"\n", "].apply(lambda x: time_fix(x))"]}, {"cell_type": "code", "execution_count": null, "id": "4200bf45-ac4c-4896-bddb-a00408863ae0", "metadata": {"tags": []}, "outputs": [], "source": ["## Day Indent\n", "day_stos = sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Day\"]\n", "\n", "days_agg_day_putaway_01 = day_stos[day_stos[\"vehicle_arrival_time_min\"].dt.hour >= 14]\n", "days_agg_day_putaway_02 = day_stos[day_stos[\"vehicle_arrival_time_min\"].dt.hour < 14]\n", "\n", "\n", "days_agg_day_putaway_01_temp = days_agg_day_putaway_01[\n", "    (\n", "        (\n", "            days_agg_day_putaway_01.putaway_time\n", "            >= days_agg_day_putaway_01.vehicle_arrival_time_min\n", "        )\n", "        & (\n", "            days_agg_day_putaway_01.putaway_time\n", "            <= days_agg_day_putaway_01.vehicle_arrival_time_min + timed<PERSON>ta(hours=3)\n", "        )\n", "    )\n", "]\n", "days_agg_day_putaway_02_temp = days_agg_day_putaway_02[\n", "    ((days_agg_day_putaway_02.putaway_time.dt.hour) < 17)\n", "]\n", "\n", "days_agg_day_putaway_temp = pd.concat(\n", "    [days_agg_day_putaway_01_temp, days_agg_day_putaway_02_temp]\n", ")\n", "\n", "day_indents = (\n", "    sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Day\"]\n", "    .groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"disc_qty\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"consignment_received_at\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    # .rename(\n", "    #     columns={\n", "    #         \"sto_id\": \"total_day_indents\",\n", "    #         \"putaway_quantity\": \"total_day_putaway_qty\",\n", "    #         \"grn_created_at\":\"day_grn_created_at\"\n", "    #     }\n", "    # )\n", ")\n", "\n", "days_agg_day_putaway_temp = days_agg_day_putaway_temp.fillna(0)\n", "days_agg_day_putaway = (\n", "    days_agg_day_putaway_temp.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"critical_putaway\"})\n", ")\n", "\n", "days_agg = pd.merge(\n", "    day_indents,\n", "    days_agg_day_putaway,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "days_agg[\"indent_type\"] = \"Day\"\n", "\n", "days_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93314bce-9b79-4ad3-8ab7-f422e53ac7db", "metadata": {}, "outputs": [], "source": ["sto_base_df_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "724fe374-9dd3-4760-81c8-28d02dc30d23", "metadata": {}, "outputs": [], "source": ["# night_stos.head()"]}, {"cell_type": "code", "execution_count": null, "id": "adaa4602-e7a5-48ae-9300-3fcfd61587de", "metadata": {}, "outputs": [], "source": ["night_stos = sto_base_df_temp[sto_base_df_temp.sto_created_slot == \"Night\"]\n", "night_stos[\"putaway_hour\"] = pd.to_datetime(night_stos[\"putaway_time\"]).dt.hour\n", "night_agg = (\n", "    night_stos.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"disc_qty\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"consignment_received_at\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    # .rename(\n", "    #     columns={\n", "    #         \"putaway_quantity\": \"night_putaway\",\n", "    #         \"sto_id\": \"total_night_indents\",\n", "    #         \"total_billed_qty\": \"night_billed_quantity\",\n", "    #         \"grn_created_at\":\"night_grn_created_at\"\n", "    #     }\n", "    # )\n", ")\n", "\n", "\n", "days_agg_night_putaway_01 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.vehicle_arrival_time_min)\n", "    & (night_stos.putaway_time <= night_stos.vehicle_arrival_time_new)\n", "]\n", "days_agg_night_putaway_02 = pd.DataFrame()\n", "# days_agg_night_putaway_02 = night_stos[(night_stos.putaway_hour >= 19)]\n", "\n", "days_agg_night_putaway_03 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.vehicle_arrival_time_min)\n", "    & (night_stos.putaway_time <= night_stos.inward_time_new)\n", "]\n", "\n", "days_agg_night_putaway_04 = night_stos[\n", "    (night_stos.putaway_time >= night_stos.putaway_7pm)\n", "    & (night_stos.putaway_time <= night_stos.putaway_6am)\n", "]\n", "days_agg_night_putaway_05 = pd.DataFrame()\n", "# night_stos[(night_stos.putaway_time <= night_stos.putaway_6am)]\n", "# night_stos[(night_stos.putaway_hour >= 19)]\n", "\n", "days_agg_night_putaway_06 = pd.concat(\n", "    [days_agg_night_putaway_04, days_agg_night_putaway_05]\n", ")\n", "\n", "days_agg_night_putaway = pd.concat(\n", "    [days_agg_night_putaway_01, days_agg_night_putaway_02]\n", ")\n", "days_agg_night_putaway = days_agg_night_putaway.fillna(0)\n", "days_agg_night_putaway = (\n", "    days_agg_night_putaway.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"critical_putaway\"})\n", ")\n", "\n", "\n", "days_agg_night_putaway_03 = days_agg_night_putaway_03.fillna(0)\n", "days_agg_night_putaway1 = (\n", "    days_agg_night_putaway_03.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"grn_delivered\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"grn_delivered\": \"inward_qty_till_2pm\"})\n", ")\n", "\n", "\n", "days_agg_night_putaway_06 = days_agg_night_putaway_06.fillna(0)\n", "days_agg_night_putaway2 = (\n", "    days_agg_night_putaway_06.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"putaway_quantity_overall_7pm_till_6am\"})\n", ")\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway2,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway1,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "night_agg = pd.merge(\n", "    night_agg,\n", "    days_agg_night_putaway,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "night_agg = night_agg.fillna(0)\n", "\n", "# night_agg[\"putaway_share_night\"] = np.where(\n", "#     night_agg[\"putaway_quantity\"] == 0,\n", "#     0,\n", "#     night_agg[\"critical_putaway\"] / night_agg[\"putaway_quantity\"],\n", "# )\n", "\n", "night_agg[\"indent_type\"] = \"Night\""]}, {"cell_type": "code", "execution_count": null, "id": "ea5fa5e0-eb83-4a6e-bb8d-c7d08f9bd084", "metadata": {}, "outputs": [], "source": ["days_agg_night_putaway2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eeb25617-9ac5-41dd-aae1-4669ec709dbd", "metadata": {}, "outputs": [], "source": ["night_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "faaa9c0e-721d-49d8-9a46-92c797dfa417", "metadata": {}, "outputs": [], "source": ["days_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "67b68b62-2adf-4399-a331-5842b8cf0630", "metadata": {}, "outputs": [], "source": ["days_agg[\"inward_qty_till_2pm\"] = 0\n", "days_agg[\"putaway_quantity_overall_7pm_till_6am\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "34949a1b-40be-4363-b6a7-59b11b7dd3ea", "metadata": {}, "outputs": [], "source": ["overall_data = pd.concat([days_agg, night_agg])"]}, {"cell_type": "code", "execution_count": null, "id": "7949f7f4-707b-467b-8518-2464a48898ae", "metadata": {}, "outputs": [], "source": ["overall_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "968efb49-760d-4249-92e0-9bfddca52acf", "metadata": {}, "outputs": [], "source": ["overall_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8013d55f-7044-40cc-94ba-eefaa341e832", "metadata": {}, "outputs": [], "source": ["# overall_data.iloc[500:550]"]}, {"cell_type": "code", "execution_count": null, "id": "b76b5f67-3f10-42bc-a6d0-65e70d00b719", "metadata": {}, "outputs": [], "source": ["city_type_mapping = pb.from_sheets(\n", "    \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\", \"Master\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fac6f7d3-026f-46fe-9402-e2cab955c63a", "metadata": {}, "outputs": [], "source": ["city_type_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "37ba46c4-a8c9-46d0-95e0-678a99fed728", "metadata": {}, "outputs": [], "source": ["city_type_mapping.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e2f89a8a-0b46-4985-bda7-ed7fa8db1b24", "metadata": {}, "outputs": [], "source": ["city_type_mapping = city_type_mapping[[\"Outlet ID\", \"Operation Mode\", \"City Name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "0637a223-7f9f-4335-9796-d40fa3d7e59e", "metadata": {}, "outputs": [], "source": ["city_type_mapping[\"Outlet ID\"] = city_type_mapping[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a62a1768-dd68-4bf0-a5c9-eba43e77e555", "metadata": {}, "outputs": [], "source": ["overall_data_temp = overall_data.merge(\n", "    city_type_mapping,\n", "    left_on=[\"receiver_outlet_id\"],\n", "    right_on=[\"Outlet ID\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "584c6cf1-a923-4667-8100-7337169a9926", "metadata": {}, "outputs": [], "source": ["overall_data_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b360c9a5-a1bc-4e45-b695-050e1e6a4cd1", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2e05942a-9287-4bf4-a18d-ca17d197271c", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"DS Compliance\"] = (\n", "    overall_data_temp[\"critical_putaway\"]\n", "    * 100\n", "    / (overall_data_temp[\"inward_qty_till_2pm\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ba2539fc-e4db-4802-b8a6-d25e7f42fc85", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ec9263c7-1446-4078-89b6-ed09b875a59d", "metadata": {}, "outputs": [], "source": ["def comp_bucket(x):\n", "    if x >= 100:\n", "        return \"100\"\n", "    elif x < 100 and x >= 95:\n", "        return \"95-100\"\n", "    elif x < 95 and x >= 80:\n", "        return \"80-95\"\n", "    elif x < 80:\n", "        return \"<80\"\n", "    else:\n", "        return \"0\""]}, {"cell_type": "code", "execution_count": null, "id": "85cc0a66-9a1b-4052-8821-c505652d61f5", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"Compliance Bucket\"] = overall_data_temp[\"DS Compliance\"].apply(\n", "    lambda x: comp_bucket(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bac6c42e-dcf5-43aa-a5c6-52c56a73865f", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c26f3f65-e26b-4528-b399-a456c0a8ee66", "metadata": {}, "outputs": [], "source": ["overall_data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "23cdf8bb-93ec-45a4-904b-de111d0ef59c", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"vehicle_arrival_time\"].iloc[0].date()"]}, {"cell_type": "code", "execution_count": null, "id": "7a2b5cc6-b9a3-44bb-80cb-6741fe467a7d", "metadata": {}, "outputs": [], "source": ["def indent_arrival(x):\n", "    try:\n", "        if (x[\"vehicle_arrival_time\"].date() == x[\"sto_created_dt\"]) and x[\n", "            \"vehicle_arrival_time\"\n", "        ].hour < 23:\n", "            return \"Yes\"\n", "        else:\n", "            return \"No\"\n", "    except:\n", "        return \"No\"\n", "\n", "\n", "def indent_arrival_new(x):\n", "    try:\n", "        if (x[\"vehicle_arrival_time\"].date() == x[\"sto_created_dt\"]) and x[\n", "            \"vehicle_arrival_time\"\n", "        ].hour < 23:\n", "            return \"Yes\"\n", "        elif (\n", "            (x[\"vehicle_arrival_time\"].date() == x[\"sto_created_dt\"])\n", "            and x[\"vehicle_arrival_time\"].hour == 23\n", "            and x[\"vehicle_arrival_time\"].minute <= 30\n", "        ):\n", "            return \"Yes\"\n", "        else:\n", "            return \"No\"\n", "    except:\n", "        return \"No\""]}, {"cell_type": "code", "execution_count": null, "id": "672afd9c-f532-4522-bb1a-a9c9a4ade2c1", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"night_indent_arrived_before_11pm\"] = overall_data_temp.apply(\n", "    lambda x: indent_arrival(x), axis=1\n", ")\n", "\n", "overall_data_temp[\"night_indent_arrived_before_11_30pm\"] = overall_data_temp.apply(\n", "    lambda x: indent_arrival_new(x), axis=1\n", ")\n", "# 'Yes' if (x['vehicle_arrival_time'].date()==x['sto_created_dt']) and x['vehicle_arrival_time'].hour<23 else 'No',axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "5231f54f-e602-408d-b461-41ed13701544", "metadata": {}, "outputs": [], "source": ["overall_data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "24fe73d5-62cc-4e6c-a87b-7abf9cdfffba", "metadata": {}, "outputs": [], "source": ["overall_data_temp[\"sto_created_dt\"] = overall_data_temp[\"sto_created_dt\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "2e7ba66a-2ad4-4d8d-b328-ee5ffdd9369d", "metadata": {}, "outputs": [], "source": ["overall_data_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8f4b14f3-ec9f-4463-ba76-5d2291b54d56", "metadata": {}, "outputs": [], "source": ["non_compliant_stores = pb.from_sheets(\n", "    \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\",\n", "    \"Non-compliant Stores\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1cf92701-f15f-45fb-8c22-376cf317da01", "metadata": {}, "outputs": [], "source": ["non_compliant_stores.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c0442486-a5d2-4331-a4cc-5115d7509ccd", "metadata": {}, "outputs": [], "source": ["non_compliant_stores = non_compliant_stores.rename(\n", "    columns={\"outlet\": \"receiver_outlet_id\"}\n", ")\n", "non_compliant_stores[\"receiver_outlet_id\"] = non_compliant_stores[\n", "    \"receiver_outlet_id\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "180f6a5d-f9a8-43d3-a359-da455c6b16fc", "metadata": {}, "outputs": [], "source": ["overall_data_final = overall_data_temp.merge(\n", "    non_compliant_stores, on=[\"receiver_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ca11f80d-aa47-4668-8589-f4dced6a14c6", "metadata": {}, "outputs": [], "source": ["# overall_data_final = overall_data_temp.copy()\n", "# pd.merge(overall_data_temp,vehicle_arrival_data_group,on=['receiver_outlet_id','sto_created_dt'],how='left')"]}, {"cell_type": "code", "execution_count": null, "id": "fe2d509e-4eb6-4ccf-8b5c-1fb455b90343", "metadata": {}, "outputs": [], "source": ["overall_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e13fc3d1-52d6-49e7-8b95-9af609574e5e", "metadata": {}, "outputs": [], "source": ["overall_data_final[\"Compliant Flag\"] = np.where(\n", "    overall_data_final[\"flag\"].isin([\"N\", \"GPOS\"]), \"No\", \"Yes\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "803387f5-5d14-47ac-88f7-93f9272752d6", "metadata": {}, "outputs": [], "source": ["overall_data_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8c1f779d-2cfc-46ac-a5d8-eb1bcde87418", "metadata": {}, "outputs": [], "source": ["overall_data_final = overall_data_final.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "40cddb02-954a-4255-bcb6-84137cc2218d", "metadata": {}, "outputs": [], "source": ["overall_data_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3a139a45-85dc-441d-86c7-47679fc741ba", "metadata": {}, "outputs": [], "source": ["overall_data_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "af52b5dc-ff90-42af-ac10-7cfb7c0b9fdf", "metadata": {}, "outputs": [], "source": ["overall_data_final = overall_data_final.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created At\",\n", "        \"sender_facility_id\": \"Sender Facility ID\",\n", "        \"sender_outlet_id\": \"Sender Outlet ID\",\n", "        \"sender_facility_name\": \"Sender Facility Name\",\n", "        \"receiver_facility_id\": \"Receiver Facility ID\",\n", "        \"receiver_outlet_id\": \"Receiver Outlet ID\",\n", "        \"receiver_facility_name\": \"Receiver Facility Name\",\n", "        \"sto_id\": \"STO Count\",\n", "        \"total_billed_qty\": \"Total Billed Qty\",\n", "        \"putaway_quantity\": \"Putaway Qty\",\n", "        \"grn_delivered\": \"Inward Qty\",\n", "        \"disc_qty\": \"Discrepancy Qty\",\n", "        \"grn_created_at\": \"First GRN Created At\",\n", "        \"putaway_time\": \"Last GRN Completed At\",\n", "        \"critical_putaway\": \"Critical Putaway\",\n", "        \"indent_type\": \"Indent Type\",\n", "        \"Operation Mode\": \"Store Type\",\n", "        \"night_indent_arrived_before_11pm\": \"Night indent arrived before 11 pm\",\n", "        \"night_indent_arrived_before_11_30pm\": \"Night indent arrived before 11:30 pm\",\n", "        \"vehicle_arrival_time\": \"Vehicle Arrival Time\",\n", "        \"consignment_received_at\": \"Vehicle Arrival Time Captured\",\n", "        \"inward_qty_till_2pm\": \"Inward Qty Till 2 pm\",\n", "        \"putaway_quantity_overall_7pm_till_6am\": \"Putaway Qty from 7pm to 6am\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "94725651-4a09-49f0-af8f-62f903eb5857", "metadata": {}, "outputs": [], "source": ["overall_data_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "69d655ff-f09b-4441-8763-56d4ee518b4e", "metadata": {}, "outputs": [], "source": ["overall_data_final = overall_data_final[\n", "    [\n", "        \"STO Created At\",\n", "        \"Sender Facility ID\",\n", "        \"Sender Outlet ID\",\n", "        \"Sender Facility Name\",\n", "        \"Receiver Facility ID\",\n", "        \"Receiver Outlet ID\",\n", "        \"Receiver Facility Name\",\n", "        \"City Name\",\n", "        \"Store Type\",\n", "        \"STO Count\",\n", "        \"Total Billed Qty\",\n", "        \"Putaway Qty\",\n", "        \"Inward Qty\",\n", "        \"Inward Qty Till 2 pm\",\n", "        \"Discrepancy Qty\",\n", "        \"First GRN Created At\",\n", "        \"Last GRN Completed At\",\n", "        \"Putaway Qty from 7pm to 6am\",\n", "        \"Critical Putaway\",\n", "        \"DS Compliance\",\n", "        \"Compliance Bucket\",\n", "        \"Indent Type\",\n", "        \"Night indent arrived before 11 pm\",\n", "        \"Night indent arrived before 11:30 pm\",\n", "        \"Vehicle Arrival Time\",\n", "        \"Vehicle Arrival Time Captured\",\n", "        \"Compliant Flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0c1c7551-f296-4c3c-b114-8e339f9aec49", "metadata": {}, "outputs": [], "source": ["overall_data_final = overall_data_final.sort_values(\n", "    by=[\"STO Created At\", \"Receiver Outlet ID\", \"Sender Outlet ID\"],\n", "    ascending=[False, True, True],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5ce53cd9-87c7-47c5-beb4-61806ce4c1b5", "metadata": {}, "outputs": [], "source": ["overall_data_final[\"DS Compliance\"] = overall_data_final[\"DS Compliance\"].fillna(0)\n", "overall_data_final[\"DS Compliance\"] = overall_data_final[\"DS Compliance\"].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cfb27c08-fa65-4866-b3fb-de70d4a46429", "metadata": {}, "outputs": [], "source": ["overall_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "78a0038a-7292-483f-9205-a41008b7f57b", "metadata": {}, "outputs": [], "source": ["overall_data_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b5eba052-f310-476e-b06d-006b6aad899f", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\""]}, {"cell_type": "code", "execution_count": null, "id": "3a77b410-7b2a-48db-9b2f-5fb550d16b4e", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    overall_data_final[overall_data_final[\"Indent Type\"] == \"Night\"],\n", "    sheet_id,\n", "    \"Night\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    overall_data_final[overall_data_final[\"Indent Type\"] == \"Day\"],\n", "    sheet_id,\n", "    \"Day\",\n", "    clear_cache=True,\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_data_final,\n", "    sheet_id,\n", "    \"Overall Data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fd9d7140-d3fa-4da3-a5ae-c0988da83667", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     overall_data_final[overall_data_final[\"Indent Type\"] == \"Night\"],\n", "#     \"1OKYLmukapOsGC7kXU66K2leXdNFs_zRo25QdPrqSaGo\",\n", "#     \"Night\",\n", "#     clear_cache=True,\n", "# )\n", "# pb.to_sheets(\n", "#     overall_data_final[overall_data_final[\"Indent Type\"] == \"Day\"],\n", "#     \"1OKYLmukapOsGC7kXU66K2leXdNFs_zRo25QdPrqSaGo\",\n", "#     \"Day\",\n", "#     clear_cache=True,\n", "# )"]}, {"cell_type": "markdown", "id": "d791ff36-32f3-439e-8586-dfa5f5c62dd9", "metadata": {}, "source": ["## Perishable"]}, {"cell_type": "code", "execution_count": null, "id": "15ee339f-0458-47c9-8dd5-361559d94c93", "metadata": {}, "outputs": [], "source": ["sto_base_query = f\"\"\"WITH pre_esto AS\n", "  (SELECT distinct a.*,\n", "          b.facility_id AS sender_facility_id,\n", "          cf.name AS sender_facility_name,\n", "          c.facility_id AS receiver_facility_id,\n", "          f.name AS receiver_facility_name\n", "   FROM metrics.esto_details a\n", "   ---left join lake_ims.ims_inward_invoice bb on a.invoice_id = bb.vendor_invoice_id and bb.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list cc on cc.grn_id = bb.grn_id and cc.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "    ---left join lake_warehouse_location.warehouse_put_list_item d on d.putlist_id=cc.id and a.item_id = d.item_id and d.created_at >= (CURRENT_DATE -9)::TIMESTAMP \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) b ON a.sender_outlet_id = b.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) cf ON cf.id = b.facility_id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(facility_id) AS facility_id,\n", "             max(name) AS name\n", "      FROM lake_retail.console_outlet\n", "      GROUP BY 1) c ON a.receiving_outlet_id = c.id\n", "   \n", "   LEFT JOIN\n", "     (SELECT id,\n", "             max(name) AS name\n", "      FROM lake_crates.facility\n", "      GROUP BY 1) f ON f.id = c.facility_id\n", "\n", "   WHERE b.name NOT LIKE '%%Infra%%'\n", "   AND sto_created_at >= (CURRENT_DATE -7)::TIMESTAMP \n", "   AND sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP \n", "   and ars_mode = 'perishable'\n", "   ),\n", "\n", "final_esto AS\n", "  (SELECT sto_id,\n", "          ars_run_id, item_id,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiving_outlet_id as receiver_outlet_id,\n", "          receiver_facility_name,\n", "          sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "         \n", "         avg(reserved_quantity) AS reserved_qty,\n", "         avg(billed_quantity) AS billed_quantity,\n", "         avg(inwarded_quantity) AS inwarded_qty,\n", "         avg(reserved_quantity_after_shortfall) AS req_qty_sf,\n", "         avg(putaway_quantity) AS putaway_quantity,\n", "         ---avg(placed_quantity) as putaway_quantity,\n", "         avg(disc_qty) as disc_qty,\n", "        \n", "         max(sto_type) AS sto_type,\n", "         max(sto_state) AS sto_state,\n", "         max(invoice_state) AS invoice_state, \n", " count(CASE\n", "           WHEN reserved_quantity > 0 THEN item_id\n", "       END) AS res_sku,\n", " count(CASE\n", "           WHEN billed_quantity > 0 THEN item_id\n", "       END) AS bil_sku,\n", " count(CASE\n", "           WHEN inwarded_quantity > 0 THEN item_id\n", "       END) AS inw_sku,\n", " count(CASE\n", "           WHEN reserved_quantity_after_shortfall > 0 THEN item_id\n", "       END) AS req_sf_sku\n", "   FROM pre_esto a\n", "   WHERE \n", "   ---STO_STATE IN ('Inward') \n", "   STO_STATE NOT IN ('Manual Expiry')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,6,7,8,9,10,11,12,13\n", "   HAVING sto_created_at >= (CURRENT_DATE -9) ::TIMESTAMP \n", "   AND sto_created_at <=(CURRENT_DATE+1)::TIMESTAMP ),\n", "\n", "\n", "final_status AS\n", "  (SELECT *,\n", "          CASE\n", "              WHEN invoice_state = 'Raised' THEN 'In-Transit'\n", "              WHEN invoice_state != '' THEN 'Delivered'\n", "              WHEN sto_state = 'Expired' THEN 'Expired before Delivery'\n", "              WHEN sto_state = 'Created' THEN 'Need to Pick'\n", "              ELSE 'other'\n", "          END AS final_status\n", "   FROM final_esto a\n", "   LEFT JOIN\n", "     (SELECT DISTINCT facility_id,\n", "                      business_type_id\n", "      FROM lake_retail.console_outlet) bc ON a.receiver_facility_id = bc.facility_id\n", "   WHERE sender_facility_id NOT IN (153)\n", "     AND bc.business_type_id = 7 ),\n", "     \n", "\n", "\n", "summary AS\n", "  (SELECT sto_created_at,\n", "          grn_created_at,\n", "          putaway_time,\n", "          consignment_received_at,\n", "          ars_run_id, \n", "          sto_id, \n", "          item_id, \n", "          -- final_status,\n", "          sender_facility_id,\n", "          sender_outlet_id,\n", "          sender_facility_name,\n", "          receiver_facility_id,\n", "          receiver_outlet_id,\n", "          receiver_facility_name,\n", " sum(reserved_qty) AS total_reserved_qty,\n", " sum(req_qty_sf) AS total_req_qty_after_shortfall,\n", " sum(case when invoice_state<>'Raised' then billed_quantity end) AS total_billed_qty,\n", " sum(case when invoice_state<>'Raised' then putaway_quantity end) AS putaway_quantity,\n", " sum(inw_sku) AS inwd_sku,\n", " sum(case when invoice_state<>'Raised' then inwarded_qty end) AS grn_delivered,\n", "     sum(disc_qty) AS disc_qty\n", "   FROM final_status a\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,5,6,7,8,9,10,11,12,13),\n", "            \n", "zone_tbl_treatment AS\n", "(SELECT facility_id,\n", "  max(ZONE) AS ZONE\n", "FROM metrics.outlet_zone_mapping\n", "GROUP BY 1)\n", "\n", "\n", "SELECT DISTINCT b.zone,\n", "                a.*\n", "FROM summary a\n", "LEFT JOIN zone_tbl_treatment b ON a.sender_facility_id = b.facility_id\n", "\"\"\"\n", "\n", "sto_perishable_base_df = pd.read_sql(sto_base_query, CON_REDSHIFT)\n", "# sto_base_df[[\"inwd_sku\", \"grn_delivered\"]] = (\n", "#     sto_base_df[[\"inwd_sku\", \"grn_delivered\"]].fillna(0).astype(int)\n", "# )\n", "# sto_base_df = sto_base_df.rename(columns={\"ars_run_id\": \"run_id\"})\n", "\n", "sto_perishable_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1d35df47-2603-48bb-a04a-877ac10cb3a1", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "39165695-9935-40d3-b7b0-4d4c109ecbe2", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df[\"sto_created_at\"] = pd.to_datetime(\n", "    sto_perishable_base_df[\"sto_created_at\"]\n", ")\n", "sto_perishable_base_df[\"sto_created_hour\"] = sto_perishable_base_df[\n", "    \"sto_created_at\"\n", "].dt.hour\n", "sto_perishable_base_df[\"sto_created_dt\"] = sto_perishable_base_df[\n", "    \"sto_created_at\"\n", "].dt.date\n", "sto_perishable_base_df[\"sto_created_minute\"] = sto_perishable_base_df[\n", "    \"sto_created_at\"\n", "].dt.minute\n", "sto_perishable_base_df[\"sto_created_time\"] = sto_perishable_base_df[\n", "    \"sto_created_at\"\n", "].dt.time\n", "\n", "sto_perishable_base_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8de17dc1-2e1a-4e8b-bff2-da4fcfb5aa38", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df[\"disc_qty\"] = sto_perishable_base_df[\"disc_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "702fee1a-41e1-412e-949a-f11231c596fd", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9c93fc21-074c-4186-ac61-f71698626ec0", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df[\"vehicle_arrival_time\"] = np.where(\n", "    sto_perishable_base_df[\"consignment_received_at\"].isnull(),\n", "    sto_perishable_base_df[\"grn_created_at\"],\n", "    sto_perishable_base_df[\"consignment_received_at\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4e91efee-e602-413c-bdea-17f16d4a8e07", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df_new = (\n", "    sto_perishable_base_df.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"vehicle_arrival_time\": \"min\"})\n", "    .reset_index()\n", ")\n", "\n", "sto_perishable_base_df_new = sto_perishable_base_df_new.rename(\n", "    columns={\"vehicle_arrival_time\": \"vehicle_arrival_time_min\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4dce50fc-8c88-45e9-86d8-a9020b23255e", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df_temp = sto_perishable_base_df.merge(\n", "    sto_perishable_base_df_new,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79918a78-8ec4-471c-97de-74dc8542c47a", "metadata": {}, "outputs": [], "source": ["sto_perishable_base_df_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2a62c11c-19fd-48e3-99fa-0fee449bf188", "metadata": {"tags": []}, "outputs": [], "source": ["## Day Indent\n", "\n", "# days_agg_day_putaway_01 = day_stos[day_stos['grn_created_at'].dt.hour>=14]\n", "# days_agg_day_putaway_02 = day_stos[day_stos['grn_created_at'].dt.hour<14]\n", "\n", "\n", "days_agg_day_putaway_01 = sto_perishable_base_df_temp[\n", "    (\n", "        (\n", "            sto_perishable_base_df_temp.putaway_time\n", "            >= sto_perishable_base_df_temp.vehicle_arrival_time_min\n", "        )\n", "        & (\n", "            sto_perishable_base_df_temp.putaway_time\n", "            <= sto_perishable_base_df_temp.vehicle_arrival_time_min + timedelta(hours=1)\n", "        )\n", "    )\n", "]\n", "\n", "\n", "sto_perishable_base_df_group = (\n", "    sto_perishable_base_df_temp.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"putaway_quantity\": \"sum\",\n", "            \"grn_delivered\": \"sum\",\n", "            \"disc_qty\": \"sum\",\n", "            \"grn_created_at\": \"min\",\n", "            \"putaway_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"consignment_received_at\": \"min\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "sto_perishable_base_df_group = sto_perishable_base_df_group.fillna(0)\n", "agg_day_critical_putaway = (\n", "    days_agg_day_putaway_01.groupby(\n", "        [\n", "            \"sto_created_dt\",\n", "            \"sender_facility_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_facility_name\",\n", "            \"receiver_facility_id\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"putaway_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"putaway_quantity\": \"critical_putaway\"})\n", ")\n", "\n", "sto_perishable_agg = pd.merge(\n", "    sto_perishable_base_df_group,\n", "    agg_day_critical_putaway,\n", "    on=[\n", "        \"sto_created_dt\",\n", "        \"sender_facility_id\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "sto_perishable_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f961a3e2-1568-4a7a-99e1-075747cb604f", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp = sto_perishable_agg.merge(\n", "    city_type_mapping,\n", "    left_on=[\"receiver_outlet_id\"],\n", "    right_on=[\"Outlet ID\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ffc39713-3268-406e-8e27-df527bb1a2e6", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4eab74b7-af4f-44b1-bbf7-e9b64f925b8b", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e28fddfd-8344-4d9a-87b3-742c39a75aac", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp[\"DS Compliance\"] = (\n", "    sto_perishable_agg_temp[\"critical_putaway\"]\n", "    * 100\n", "    / (sto_perishable_agg_temp[\"grn_delivered\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e343a97a-f29b-47bf-a0d0-64e08ec07fa6", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "da90e1ea-7714-42e9-b071-9e6c47f51a91", "metadata": {}, "outputs": [], "source": ["def comp_bucket(x):\n", "    if x >= 100:\n", "        return \"100\"\n", "    elif x < 100 and x >= 95:\n", "        return \"95-100\"\n", "    elif x < 95 and x >= 80:\n", "        return \"80-95\"\n", "    elif x < 80:\n", "        return \"<80\"\n", "    else:\n", "        return \"0\""]}, {"cell_type": "code", "execution_count": null, "id": "df2f0bcf-9003-4339-9c40-a89c27af9812", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp[\"Compliance Bucket\"] = sto_perishable_agg_temp[\n", "    \"DS Compliance\"\n", "].apply(lambda x: comp_bucket(x))"]}, {"cell_type": "code", "execution_count": null, "id": "a686ad6b-8b56-4028-9d73-f61a53fb7052", "metadata": {}, "outputs": [], "source": ["sto_perishable_agg_temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e03653e9-4dc6-4f7e-a008-7aaaf85b8bda", "metadata": {}, "outputs": [], "source": ["sto_perishable_final = sto_perishable_agg_temp.merge(\n", "    non_compliant_stores, on=[\"receiver_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "27d60322-79aa-4df5-8a50-dec1f8bf5b8c", "metadata": {}, "outputs": [], "source": ["sto_perishable_final[\"Compliant Flag\"] = np.where(\n", "    sto_perishable_final[\"flag\"].isin([\"N\", \"GPOS\"]), \"No\", \"Yes\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2c949015-345f-43c2-91e8-135e648bee4d", "metadata": {}, "outputs": [], "source": ["sto_perishable_final = sto_perishable_final.rename(\n", "    columns={\n", "        \"sto_created_dt\": \"STO Created At\",\n", "        \"sender_facility_id\": \"Sender Facility ID\",\n", "        \"sender_outlet_id\": \"Sender Outlet ID\",\n", "        \"sender_facility_name\": \"Sender Facility Name\",\n", "        \"receiver_facility_id\": \"Receiver Facility ID\",\n", "        \"receiver_outlet_id\": \"Receiver Outlet ID\",\n", "        \"receiver_facility_name\": \"Receiver Facility Name\",\n", "        \"sto_id\": \"STO Count\",\n", "        \"total_billed_qty\": \"Total Billed Qty\",\n", "        \"putaway_quantity\": \"Putaway Qty\",\n", "        \"grn_delivered\": \"Inward Qty\",\n", "        \"disc_qty\": \"Discrepancy Qty\",\n", "        \"grn_created_at\": \"First GRN Created At\",\n", "        \"putaway_time\": \"Last GRN Completed At\",\n", "        \"critical_putaway\": \"Critical Putaway\",\n", "        \"indent_type\": \"Indent Type\",\n", "        \"Operation Mode\": \"Store Type\",\n", "        \"night_indent_arrived_before_11\": \"Night indent arrived before 11 pm\",\n", "        \"vehicle_arrival_time\": \"Vehicle Arrival Time\",\n", "        \"consignment_received_at\": \"Vehicle Arrival Time Captured\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eeeed1d4-65b8-40b9-b26d-a5e0e564d994", "metadata": {}, "outputs": [], "source": ["sto_perishable_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "aa25efe8-89f9-4984-8d44-1dd7dbe0b454", "metadata": {}, "outputs": [], "source": ["sto_perishable_final = sto_perishable_final[\n", "    [\n", "        \"STO Created At\",\n", "        \"Sender Facility ID\",\n", "        \"Sender Outlet ID\",\n", "        \"Sender Facility Name\",\n", "        \"Receiver Facility ID\",\n", "        \"Receiver Outlet ID\",\n", "        \"Receiver Facility Name\",\n", "        \"City Name\",\n", "        \"Store Type\",\n", "        \"STO Count\",\n", "        \"Total Billed Qty\",\n", "        \"Putaway Qty\",\n", "        \"Inward Qty\",\n", "        \"Discrepancy Qty\",\n", "        \"First GRN Created At\",\n", "        \"Last GRN Completed At\",\n", "        \"Critical Putaway\",\n", "        \"DS Compliance\",\n", "        \"Compliance Bucket\",\n", "        \"Vehicle Arrival Time\",\n", "        \"Vehicle Arrival Time Captured\",\n", "        \"Compliant Flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "296a9605-13a6-401b-8a9c-cc5e286a1ade", "metadata": {}, "outputs": [], "source": ["sto_perishable_final[[\"Critical Putaway\", \"DS Compliance\"]] = sto_perishable_final[\n", "    [\"Critical Putaway\", \"DS Compliance\"]\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "dd9cd55d-c56f-43da-b51b-40f12aee78bc", "metadata": {}, "outputs": [], "source": ["sto_perishable_final.head()"]}, {"cell_type": "markdown", "id": "242d4604-48dc-4f38-a4af-306b2ee0e196", "metadata": {}, "source": ["## FnV STO"]}, {"cell_type": "code", "execution_count": null, "id": "95d588d7-a26b-48cc-b912-961533bdf639", "metadata": {}, "outputs": [], "source": ["fnv_sto_q = \"\"\"\n", "\n", "select  \n", "        b.*\n", "from\n", "(select receiving_outlet_id as outlet_id, receiver_outlet_name as name, sto_id, sender_outlet_id as backend_outlet_id,\n", "        sto_created_at, date(sto_created_at) as sto_date, \n", "        cast(sto_created_at as time) as sto_time, \n", "        extract(hour from sto_created_at) as sto_hour, \n", "        cast(sto_billed_at as time) as sto_billed_time,\n", "        coalesce(vehicle_arrival_time,min_grn_details_created_at) as vehicle_arrival_time,\n", "        min_grn_details_created_at as min_grn_time,\n", "        max_grn_details_created_at as max_grn_time,\n", "        date(max_grn_details_created_at) as grn_date,\n", "        max_grn_details_created_at as grn_date_time,\n", "        (case when sto_state=1 then 'Created'\n", "            when sto_state=2 then 'Billed'\n", "            when sto_state=3 then 'Expired'\n", "            when sto_state=4 then 'Inward'\n", "            when sto_state=5 then 'Force Billed'\n", "        else '' end) as sto_state,\n", "        'sto' as \"po/sto\",\n", "        reserved_quantity as indent_qty\n", "\n", "from\n", "(select receiving_outlet_id, receiver_outlet_name, sto_id, sender_outlet_id, sto_created_at, sto_state, \n", "min(consignment_received_at) as vehicle_arrival_time,\n", "    max(sto_billed_at) as sto_billed_at, \n", "    min(grn_details_created_at) as min_grn_details_created_at,\n", "    max(grn_details_created_at) as max_grn_details_created_at, \n", "    sum(reserved_quantity) as reserved_quantity from\n", "        (select distinct receiving_outlet_id, receiver_outlet_name, sto_id, sender_outlet_id, sto_created_at, sto_state, \n", "            sto_billed_at,consignment_received_at, grn_details_created_at, reserved_quantity\n", "            from metrics.esto_details sto\n", "            INNER JOIN lake_rpc.product_product pp on sto.item_id = pp.item_id and pp.outlet_type=1\n", "            where date(sto_created_at)  >= current_date-INTERVAL '7 days'\n", "            and sto.sender_outlet_id in (1674,1280,2472,4135,2669,2675,2672,2666,1785,2790,2729,2925,3229,3886,2544)\n", "        )\n", "    group by 1,2,3,4,5,6\n", ")\n", ") b\n", "\n", "\n", "order by sto_date\n", "\n", "\"\"\"\n", "\n", "\n", "fnv_sto_data = pd.read_sql(fnv_sto_q, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "2f19f685-67ae-42c9-ad37-635cbbeaf791", "metadata": {}, "outputs": [], "source": ["fnv_sto_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "97c9acc2-1f70-4fd5-b604-13317f6a1478", "metadata": {}, "outputs": [], "source": ["fnv_sto_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "22e3ebf4-20c7-405f-ace5-8ea1aed326bc", "metadata": {}, "outputs": [], "source": ["fnv_sto_data_new = fnv_sto_data[\n", "    [\n", "        \"outlet_id\",\n", "        \"name\",\n", "        \"sto_id\",\n", "        \"sto_created_at\",\n", "        \"sto_billed_time\",\n", "        \"min_grn_time\",\n", "        \"max_grn_time\",\n", "        \"grn_date\",\n", "        \"grn_date_time\",\n", "        \"po/sto\",\n", "        \"vehicle_arrival_time\",\n", "        \"indent_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a28e4334-3ae4-43d5-a464-5658a0a0ad72", "metadata": {}, "outputs": [], "source": ["fnv_po_q = \"\"\"\n", "\n", "with city_ds as (\n", "select distinct co.facility_id,cl.name as city\n", "from lake_retail.console_outlet co\n", "inner join (select facility_id, max(updated_at) as updated_at\n", "            from lake_retail.console_outlet\n", "            group by 1) cc on cc.facility_id=co.facility_id  and cc.updated_at=co.updated_at\n", "left join lake_retail.console_location cl on cl.id=co.tax_location_id\n", ")\n", "\n", "\n", "select distinct\n", "-- date(issue_date + interval '330 minute') as issue_date,\n", "case when (ps.schedule_date_time + interval '5.5 Hour') is null and extract(hour from issue_date + interval '5.5 Hour') between 19 and 23\n", "                        then date(issue_date + interval '5.5 Hour' + interval '1 day') \n", "                when (ps.schedule_date_time + interval '5.5 Hour') is null and extract(hour from issue_date + interval '5.5 Hour') between 0 and 15\n", "                        then date(issue_date + interval '5.5 Hour') \n", "                else date(ps.schedule_date_time + interval '5.5 Hour') end as date_,\n", "p.id as po_id,\n", "cd.city,\n", "p.outlet_id,\n", "o.name as outlet_name,\n", "---cf.name as facility_name,\n", "poi.item_id,\n", "pp.name as item_name,\n", "pp.uom as grammage,\n", " (grn_time.min_grn_time)  as min_grn_time,\n", "(grn_time.max_grn_time) as  max_grn_time,\n", "sum(poi.units_ordered) as po_qty,\n", "sum(grn.quan) as grn_qty\n", "\n", "from lake_po.purchase_order p\n", "left join lake_po.po_schedule ps on p.id=ps.po_id_id\n", "inner join lake_po.purchase_order_items poi on p.id=poi.po_id\n", "inner join lake_retail.console_outlet o on o.id=p.outlet_id\n", "inner join lake_po.purchase_order_status posa on posa.po_id = p.id\n", "inner join lake_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "left join (select po_id, item_id, sum(quantity) quan from lake_po.po_grn grn  group by 1,2 ) grn on  grn.item_id = poi.item_id and grn.po_id = p.id\n", "left join (select po_id, min(grn.created_at + interval '330 minute') as min_grn_time, \n", "                            max(grn.created_at + interval '330 minute') as max_grn_time from lake_po.po_grn grn group by 1 ) grn_time on grn_time.po_id = p.id\n", "\n", "inner join (select item_id,  active, max(name) as name, max(variant_uom_text) as uom,max(id) from lake_rpc.product_product\n", "                group by 1,2) pp on pp.item_id = poi.item_id and pp.active=1\n", "left join lake_crates.facility cf on cf.id=o.facility_id\n", "left join city_ds cd on cd.facility_id = o.facility_id\n", "\n", "where issue_date >= current_date - 7\n", "and(posta.name not in ('Cancelled', 'Rejected', 'Cancelled post Creation')) -- or (posta.name in ('Expired') and grn.quan is not null)\n", "and p.vendor_name in ('Zomato Internet Private Limited')\n", "and (cf.name like ('%%Chennai%%') or cf.name like ('%%Ahmedabad%%') or cf.name like ('%%Hyderabad%%') or cf.name like ('%%Pune%%'))\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "\n", "order by 1 desc,2,4\n", "\n", "\n", "\"\"\"\n", "\n", "fnv_po_data = pd.read_sql(fnv_po_q, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "d1b1260c-3065-4752-8bcc-43945a08eba4", "metadata": {}, "outputs": [], "source": ["fnv_po_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1cc6e939-9bfe-4adc-bd3b-15f10e988e7a", "metadata": {}, "outputs": [], "source": ["fnv_po_data[\"po/sto\"] = \"po\""]}, {"cell_type": "code", "execution_count": null, "id": "da0554c5-55fd-4c42-8201-2fc9cb695440", "metadata": {}, "outputs": [], "source": ["fnv_po_data[\"vehicle_arrival_time\"] = fnv_po_data[\"min_grn_time\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0ce33f4a-216e-49c5-9909-b24cd0d1c1e7", "metadata": {}, "outputs": [], "source": ["fnv_po_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "cf6946c8-21b3-4bca-834b-4a9d728df2b9", "metadata": {}, "outputs": [], "source": ["fnv_po_data_group = (\n", "    fnv_po_data.groupby([\"date_\", \"outlet_id\", \"outlet_name\", \"po/sto\"])\n", "    .agg(\n", "        {\n", "            \"min_grn_time\": \"min\",\n", "            \"max_grn_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"po_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ebb4a02e-4256-472c-bc2f-b17e370f679c", "metadata": {}, "outputs": [], "source": ["fnv_sto_data_group = (\n", "    fnv_sto_data.groupby([\"sto_date\", \"outlet_id\", \"name\", \"po/sto\"])\n", "    .agg(\n", "        {\n", "            \"min_grn_time\": \"min\",\n", "            \"max_grn_time\": \"max\",\n", "            \"vehicle_arrival_time\": \"min\",\n", "            \"indent_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6273aa39-5057-4b7a-8f2c-26e09256200e", "metadata": {}, "outputs": [], "source": ["fnv_sto_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "01b75109-e4ee-41f7-9bd0-7d78905fc42d", "metadata": {}, "outputs": [], "source": ["fnv_sto_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "28048706-7235-4735-a22c-7ef97d413fb0", "metadata": {}, "outputs": [], "source": ["fnv_po_data_group.head()"]}, {"cell_type": "code", "execution_count": null, "id": "984222b6-594b-4f96-a841-3b3d83e1f9da", "metadata": {}, "outputs": [], "source": ["fnv_sto_data_group = fnv_sto_data_group.rename(\n", "    columns={\"sto_date\": \"date_ts\", \"name\": \"outlet_name\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "15533fd5-8409-4808-897c-51e6311ff250", "metadata": {}, "outputs": [], "source": ["fnv_po_data_group = fnv_po_data_group.rename(\n", "    columns={\"date_\": \"date_ts\", \"po_qty\": \"indent_qty\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3d69449d-6a46-4e43-9797-ba66d3b153bc", "metadata": {}, "outputs": [], "source": ["fnv_data = pd.concat([fnv_sto_data_group, fnv_po_data_group])"]}, {"cell_type": "code", "execution_count": null, "id": "aaa470d7-5e0e-4c35-974f-35ef5a2f861d", "metadata": {}, "outputs": [], "source": ["fnv_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d467d968-0d2c-4db0-9181-bf51ea332634", "metadata": {}, "outputs": [], "source": ["sto_perishable_final.columns"]}, {"cell_type": "code", "execution_count": null, "id": "5b42237e-02a6-43d4-b678-59d6a3096c73", "metadata": {}, "outputs": [], "source": ["sto_perishable_final[sto_perishable_final[\"Receiver Outlet ID\"] == 976]"]}, {"cell_type": "code", "execution_count": null, "id": "9c4647b1-96bb-458a-8404-5a6f5a5cb19f", "metadata": {}, "outputs": [], "source": ["sto_perishable_final[\"DS Compliance\"] = sto_perishable_final[\"DS Compliance\"].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "13f13b64-da3b-443f-8ee2-317ca469ab59", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sto_perishable_final,\n", "    \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\",\n", "    \"Perishable\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    fnv_data, \"1eEimF3zY0y6deS_WH9H9nlaiJ38HCwJMhdqesOlAc4Q\", \"FnV\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "57734634-d258-464b-b0f5-de52bba264d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}