{"cells": [{"cell_type": "code", "execution_count": null, "id": "ebc0954b-f485-4e8d-9ca1-56dad521b4d2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "fb0370d4-e103-4a70-adcc-89f69669892d", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a1376da1-5308-41f1-a970-b6a34fa65cb0", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "65234f61-ae38-4974-9530-5087ead868cb", "metadata": {}, "outputs": [], "source": ["esto_week1_sql = f\"\"\"\n", "WITH sto_billed_grn_raw AS\n", "  (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (CASE\n", "               WHEN id.outlet_type = 1 THEN 'F&V type'\n", "               WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN\n", "     (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-01-10'\n", "      and inventory_update_type_id in (1,28,76,90,93)\n", "      -- and merchant_invoice_id = 'BHM1580T21005442'\n", "      group by 1,2\n", "    ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-01-15'\n", "     AND extract(day from date(pi.pos_timestamp + interval '5.5 hour'))::int in (1,2,3,4,5,6,7) \n", "    and extract(month from date(pi.pos_timestamp + interval '5.5 hour'))=extract(month from current_date)\n", "    and extract(year from date(pi.pos_timestamp + interval '5.5 hour'))=extract(year from current_date)\n", "     -- and sto_id = 1130248\n", "   group by 1,2,3,4,5,6,7,8,9,13),\n", "sto_billed_grn AS\n", "  (SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.item_type,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            17,\n", "            18),\n", "discrepancy AS\n", "  (SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at + interval '5.5 hour')) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE extract(day from date(created_at))::int in (1,2,3,4,5,6,7) \n", "            and extract(month from date(created_at))=extract(month from current_date)\n", "            and extract(year from date(created_at))=extract(year from current_date)\n", "            GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN\n", "        (SELECT DISTINCT item_id,\n", "                         upc\n", "         FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", ") TEMP\n", "   GROUP BY 1,\n", "            2),\n", "return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "             sum(pipd.quantity) AS b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2021-01-15' -- and pi.original_invoice_id='BJC334T210003243'\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4)tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (23,35) THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (24,36) THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (25,37) THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (66,68) THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id -- where  tab1.ret_invoice_id='BGC340U200000134'\n", "GROUP BY 1,\n", "         2),\n", "FINAL AS\n", "  (SELECT sbg.sto_id,\n", "          sbg.outlet_id,\n", "          ozm1.outlet_name AS sender_outlet,\n", "          sbg.merchant_outlet_id,\n", "          ozm2.outlet_name AS rec_outlet,\n", "          sbg.sto_created_at,\n", "          sbg.item_id,\n", "          sbg.billing_unit_price,\n", "          hot_landing_price,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.Item_name,\n", "          sbg.item_type,\n", "          sbg.billed_qty,\n", "          sbg.invoice_creation,\n", "          sbg.grn_quantity,\n", "          sbg.grn_timestamp,\n", "          sbg.billing_price,\n", "          sbg.diff_qty diff_qty_billed_grn,\n", "          sbg.diff_amt diff_amt_billed_grn,\n", "          COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "          disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          b2b.return_timestamp,\n", " COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", " COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", " COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", " COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", " COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", " COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", " COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", ")\n", "\n", "\n", "    SELECT \n", "           case\n", "                when lower(trim(co.location))='bangalore' then 'bengaluru'\n", "                else lower(trim(co.location))\n", "           end as city,\n", "           fc.id AS facility_id,\n", "           fc.name AS facility_name,\n", "           sto_id,\n", "           outlet_id,\n", "           sender_outlet,\n", "           merchant_outlet_id,\n", "           rec_outlet,\n", "           sto_created_at,\n", "           grn_timestamp,\n", "           item_id,\n", "           item_name,\n", "           invoice_id,\n", "           invoice_creation,\n", "           billing_unit_price,\n", "           hot_landing_price,\n", "           CASE\n", "               WHEN transfer_state = 1 THEN 'Raised'\n", "               WHEN transfer_state = 2 THEN 'GRN Complete'\n", "               WHEN transfer_state = 3 THEN 'GRN Partial'\n", "               WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "               WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "               ELSE 'NA'\n", "           END AS Invoice_State,\n", "           billing_price,\n", "           billed_qty,\n", "           grn_quantity,\n", "           discrepancy_qty::int as total_discrepancy_qty,\n", "           disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          return_timestamp,\n", "          tot_returned,\n", "          b2breturn_good,\n", "          b2breturn_expired,\n", "          b2breturn_damaged,\n", "          b2breturn_near_expiry,\n", "          lost_in_transit_negative,\n", "          diff_qty_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "          diff_amt_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "          hot_landing_price*tot_returned AS b2b_return_amount\n", "    FROM FINAL f\n", "    LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "    LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "    WHERE billed_qty>0\n", "    and diff_amt !=0\n", "    and sender_outlet not like '%%Infra%%'\n", "    and rec_outlet not like '%%Infra%%'\n", "    order by city,facility_id,sto_created_at,invoice_creation\n", "\n", "\"\"\"\n", "\n", "esto_week1 = read_sql_query(esto_week1_sql, redshift)\n", "esto_week1.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "ba120337-1abb-4359-a5bd-275640326585", "metadata": {}, "outputs": [], "source": ["esto_week2_sql = f\"\"\"\n", "WITH sto_billed_grn_raw AS\n", "  (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (CASE\n", "               WHEN id.outlet_type = 1 THEN 'F&V type'\n", "               WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN\n", "     (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-01-10'\n", "      and inventory_update_type_id in (1,28,76,90,93)\n", "      -- and merchant_invoice_id = 'BHM1580T21005442'\n", "      group by 1,2\n", "    ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-01-15'\n", "     AND extract(day from date(pi.pos_timestamp + interval '5.5 hour'))::int in (8,9,10,11,12,13,14) \n", "    and extract(month from date(pi.pos_timestamp + interval '5.5 hour'))=extract(month from current_date)\n", "    and extract(year from date(pi.pos_timestamp + interval '5.5 hour'))=extract(year from current_date)\n", "     -- and sto_id = 1130248\n", "   group by 1,2,3,4,5,6,7,8,9,13),\n", "sto_billed_grn AS\n", "  (SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.item_type,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            17,\n", "            18),\n", "discrepancy AS\n", "  (SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at + interval '5.5 hour')) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE extract(day from date(created_at))::int in (8,9,10,11,12,13,14) \n", "            and extract(month from date(created_at))=extract(month from current_date)\n", "            and extract(year from date(created_at))=extract(year from current_date)\n", "            GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN\n", "        (SELECT DISTINCT item_id,\n", "                         upc\n", "         FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", ") TEMP\n", "   GROUP BY 1,\n", "            2),\n", "return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "             sum(pipd.quantity) AS b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2021-01-15' -- and pi.original_invoice_id='BJC334T210003243'\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4)tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (23,35) THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (24,36) THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (25,37) THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (66,68) THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id -- where  tab1.ret_invoice_id='BGC340U200000134'\n", "GROUP BY 1,\n", "         2),\n", "FINAL AS\n", "  (SELECT sbg.sto_id,\n", "          sbg.outlet_id,\n", "          ozm1.outlet_name AS sender_outlet,\n", "          sbg.merchant_outlet_id,\n", "          ozm2.outlet_name AS rec_outlet,\n", "          sbg.sto_created_at,\n", "          sbg.item_id,\n", "          sbg.billing_unit_price,\n", "          hot_landing_price,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.Item_name,\n", "          sbg.item_type,\n", "          sbg.billed_qty,\n", "          sbg.invoice_creation,\n", "          sbg.grn_quantity,\n", "          sbg.grn_timestamp,\n", "          sbg.billing_price,\n", "          sbg.diff_qty diff_qty_billed_grn,\n", "          sbg.diff_amt diff_amt_billed_grn,\n", "          COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "          disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          b2b.return_timestamp,\n", " COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", " COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", " COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", " COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", " COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", " COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", " COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", ")\n", "\n", "        SELECT \n", "           case\n", "                when lower(trim(co.location))='bangalore' then 'bengaluru'\n", "                else lower(trim(co.location))\n", "           end as city,\n", "           fc.id AS facility_id,\n", "           fc.name AS facility_name,\n", "           sto_id,\n", "           outlet_id,\n", "           sender_outlet,\n", "           merchant_outlet_id,\n", "           rec_outlet,\n", "           sto_created_at,\n", "           grn_timestamp,\n", "           item_id,\n", "           item_name,\n", "           invoice_id,\n", "           invoice_creation,\n", "           billing_unit_price,\n", "           hot_landing_price,\n", "           CASE\n", "               WHEN transfer_state = 1 THEN 'Raised'\n", "               WHEN transfer_state = 2 THEN 'GRN Complete'\n", "               WHEN transfer_state = 3 THEN 'GRN Partial'\n", "               WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "               WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "               ELSE 'NA'\n", "           END AS Invoice_State,\n", "           billing_price,\n", "           billed_qty,\n", "           grn_quantity,\n", "           discrepancy_qty::int as total_discrepancy_qty,\n", "           disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          return_timestamp,\n", "          tot_returned,\n", "          b2breturn_good,\n", "          b2breturn_expired,\n", "          b2breturn_damaged,\n", "          b2breturn_near_expiry,\n", "          lost_in_transit_negative,\n", "          diff_qty_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "          diff_amt_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "          hot_landing_price*tot_returned AS b2b_return_amount\n", "    FROM FINAL f\n", "    LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "    LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "    WHERE billed_qty>0\n", "    and diff_amt !=0\n", "    and sender_outlet not like '%%Infra%%'\n", "    and rec_outlet not like '%%Infra%%'\n", "    order by city,facility_id,sto_created_at,invoice_creation\n", "\"\"\"\n", "\n", "esto_week2 = read_sql_query(esto_week2_sql, redshift)\n", "esto_week2.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "6e0655fe-9961-4c9f-a720-c1ba9f8b8cf5", "metadata": {}, "outputs": [], "source": ["esto_week3_sql = f\"\"\"\n", "WITH sto_billed_grn_raw AS\n", "  (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (CASE\n", "               WHEN id.outlet_type = 1 THEN 'F&V type'\n", "               WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN\n", "     (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-01-10'\n", "      and inventory_update_type_id in (1,28,76,90,93)\n", "      -- and merchant_invoice_id = 'BHM1580T21005442'\n", "      group by 1,2\n", "    ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-01-15'\n", "     AND extract(day from date(pi.pos_timestamp + interval '5.5 hour'))::int in (15,16,17,18,19,20,21) \n", "    and extract(month from date(pi.pos_timestamp + interval '5.5 hour'))=extract(month from current_date)\n", "    and extract(year from date(pi.pos_timestamp + interval '5.5 hour'))=extract(year from current_date)\n", "     -- and sto_id = 1130248\n", "   group by 1,2,3,4,5,6,7,8,9,13),\n", "sto_billed_grn AS\n", "  (SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            16,\n", "            17),\n", "discrepancy AS\n", "  (SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at + interval '5.5 hour')) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE extract(day from date(created_at))::int in (15,16,17,18,19,20,21) \n", "            and extract(month from date(created_at))=extract(month from current_date)\n", "            and extract(year from date(created_at))=extract(year from current_date)\n", "            GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN\n", "        (SELECT DISTINCT item_id,\n", "                         upc\n", "         FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", ") TEMP\n", "   GROUP BY 1,\n", "            2),\n", "return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "             sum(pipd.quantity) AS b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2021-01-15' -- and pi.original_invoice_id='BJC334T210003243'\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4)tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (23,35) THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (24,36) THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (25,37) THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (66,68) THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id -- where  tab1.ret_invoice_id='BGC340U200000134'\n", "GROUP BY 1,\n", "         2),\n", "FINAL AS\n", "  (SELECT sbg.sto_id,\n", "          sbg.outlet_id,\n", "          ozm1.outlet_name AS sender_outlet,\n", "          sbg.merchant_outlet_id,\n", "          ozm2.outlet_name AS rec_outlet,\n", "          sbg.sto_created_at,\n", "          sbg.item_id,\n", "          sbg.billing_unit_price,\n", "          hot_landing_price,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.Item_name,\n", "          sbg.billed_qty,\n", "          sbg.invoice_creation,\n", "          sbg.grn_quantity,\n", "          sbg.grn_timestamp,\n", "          sbg.billing_price,\n", "          sbg.diff_qty diff_qty_billed_grn,\n", "          sbg.diff_amt diff_amt_billed_grn,\n", "          COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "          disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          b2b.return_timestamp,\n", " COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", " COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", " COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", " COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", " COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", " COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", " COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", ")\n", "\n", "        SELECT \n", "           case\n", "                when lower(trim(co.location))='bangalore' then 'bengaluru'\n", "                else lower(trim(co.location))\n", "           end as city,\n", "           fc.id AS facility_id,\n", "           fc.name AS facility_name,\n", "           sto_id,\n", "           outlet_id,\n", "           sender_outlet,\n", "           merchant_outlet_id,\n", "           rec_outlet,\n", "           sto_created_at,\n", "           grn_timestamp,\n", "           item_id,\n", "           item_name,\n", "           invoice_id,\n", "           invoice_creation,\n", "           billing_unit_price,\n", "           hot_landing_price,\n", "           CASE\n", "               WHEN transfer_state = 1 THEN 'Raised'\n", "               WHEN transfer_state = 2 THEN 'GRN Complete'\n", "               WHEN transfer_state = 3 THEN 'GRN Partial'\n", "               WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "               WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "               ELSE 'NA'\n", "           END AS Invoice_State,\n", "           billing_price,\n", "           billed_qty,\n", "           grn_quantity,\n", "           discrepancy_qty::int as total_discrepancy_qty,\n", "           disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          return_timestamp,\n", "          tot_returned,\n", "          b2breturn_good,\n", "          b2breturn_expired,\n", "          b2breturn_damaged,\n", "          b2breturn_near_expiry,\n", "          lost_in_transit_negative,\n", "          diff_qty_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "          diff_amt_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "          hot_landing_price*tot_returned AS b2b_return_amount\n", "    FROM FINAL f\n", "    LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "    LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "    WHERE billed_qty>0\n", "    and diff_amt !=0\n", "    and sender_outlet not like '%%Infra%%'\n", "    and rec_outlet not like '%%Infra%%'\n", "    order by city,facility_id,sto_created_at,invoice_creation\n", "\n", "\"\"\"\n", "\n", "esto_week3 = read_sql_query(esto_week3_sql, redshift)\n", "esto_week3.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "6f09db9d-c486-4a82-a133-74401387564b", "metadata": {}, "outputs": [], "source": ["esto_week4_sql = f\"\"\"\n", "WITH sto_billed_grn_raw AS\n", "  (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (CASE\n", "               WHEN id.outlet_type = 1 THEN 'F&V type'\n", "               WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN\n", "     (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-01-10'\n", "      and inventory_update_type_id in (1,28,76,90,93)\n", "      -- and merchant_invoice_id = 'BHM1580T21005442'\n", "      group by 1,2\n", "    ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-01-15'\n", "     AND extract(day from date(pi.pos_timestamp + interval '5.5 hour'))::int in (22,23,24,25,26,27,28) \n", "    and extract(month from date(pi.pos_timestamp + interval '5.5 hour'))=extract(month from current_date)\n", "    and extract(year from date(pi.pos_timestamp + interval '5.5 hour'))=extract(year from current_date)\n", "     -- and sto_id = 1130248\n", "   group by 1,2,3,4,5,6,7,8,9,13),\n", "sto_billed_grn AS\n", "  (SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            16,\n", "            17),\n", "discrepancy AS\n", "  (SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at + interval '5.5 hour')) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE extract(day from date(created_at))::int in (22,23,24,25,26,27,28)\n", "            and extract(month from date(created_at))=extract(month from current_date)\n", "            and extract(year from date(created_at))=extract(year from current_date)\n", "            GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN\n", "        (SELECT DISTINCT item_id,\n", "                         upc\n", "         FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", ") TEMP\n", "   GROUP BY 1,\n", "            2),\n", "return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "             sum(pipd.quantity) AS b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2021-01-15' -- and pi.original_invoice_id='BJC334T210003243'\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4)tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (23,35) THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (24,36) THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (25,37) THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (66,68) THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id -- where  tab1.ret_invoice_id='BGC340U200000134'\n", "GROUP BY 1,\n", "         2),\n", "FINAL AS\n", "  (SELECT sbg.sto_id,\n", "          sbg.outlet_id,\n", "          ozm1.outlet_name AS sender_outlet,\n", "          sbg.merchant_outlet_id,\n", "          ozm2.outlet_name AS rec_outlet,\n", "          sbg.sto_created_at,\n", "          sbg.item_id,\n", "          sbg.billing_unit_price,\n", "          hot_landing_price,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.Item_name,\n", "          sbg.billed_qty,\n", "          sbg.invoice_creation,\n", "          sbg.grn_quantity,\n", "          sbg.grn_timestamp,\n", "          sbg.billing_price,\n", "          sbg.diff_qty diff_qty_billed_grn,\n", "          sbg.diff_amt diff_amt_billed_grn,\n", "          COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "          disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          b2b.return_timestamp,\n", " COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", " COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", " COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", " COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", " COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", " COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", " COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", ")\n", "\n", "        SELECT \n", "           case\n", "                when lower(trim(co.location))='bangalore' then 'bengaluru'\n", "                else lower(trim(co.location))\n", "           end as city,\n", "           fc.id AS facility_id,\n", "           fc.name AS facility_name,\n", "           sto_id,\n", "           outlet_id,\n", "           sender_outlet,\n", "           merchant_outlet_id,\n", "           rec_outlet,\n", "           sto_created_at,\n", "           grn_timestamp,\n", "           item_id,\n", "           item_name,\n", "           invoice_id,\n", "           invoice_creation,\n", "           billing_unit_price,\n", "           hot_landing_price,\n", "           CASE\n", "               WHEN transfer_state = 1 THEN 'Raised'\n", "               WHEN transfer_state = 2 THEN 'GRN Complete'\n", "               WHEN transfer_state = 3 THEN 'GRN Partial'\n", "               WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "               WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "               ELSE 'NA'\n", "           END AS Invoice_State,\n", "           billing_price,\n", "           billed_qty,\n", "           grn_quantity,\n", "           discrepancy_qty::int as total_discrepancy_qty,\n", "           disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          return_timestamp,\n", "          tot_returned,\n", "          b2breturn_good,\n", "          b2breturn_expired,\n", "          b2breturn_damaged,\n", "          b2breturn_near_expiry,\n", "          lost_in_transit_negative,\n", "          diff_qty_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "          diff_amt_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "          hot_landing_price*tot_returned AS b2b_return_amount\n", "    FROM FINAL f\n", "    LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "    LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "    WHERE billed_qty>0\n", "    and diff_amt !=0\n", "    and sender_outlet not like '%%Infra%%'\n", "    and rec_outlet not like '%%Infra%%'\n", "    order by city,facility_id,sto_created_at,invoice_creation\n", "\"\"\"\n", "\n", "esto_week4 = read_sql_query(esto_week4_sql, redshift)\n", "esto_week4.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "af1e5df0-e816-415a-9402-3d7a40d2fe75", "metadata": {}, "outputs": [], "source": ["esto_week5_sql = f\"\"\"\n", "WITH sto_billed_grn_raw AS\n", "  (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "          pi.grofers_order_id AS sto_id,\n", "          sto.outlet_id,\n", "          sto.merchant_outlet_id,\n", "          pi.invoice_id,\n", "          pi.transfer_state,\n", "          r.item_id AS Item_ID,\n", "          id.name AS Item_name,\n", "          (CASE\n", "               WHEN id.outlet_type = 1 THEN 'F&V type'\n", "               WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          sum(pipd.quantity) AS billed_qty,\n", "          coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "          max(i.post + interval '5.5 hour') grn_timestamp,\n", "          (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "          avg(pipd.selling_price) AS billing_price,\n", "          avg(lp.landing_price) AS hot_landing_price\n", "   FROM lake_pos.pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "   LEFT JOIN\n", "     (SELECT merchant_invoice_id,variant_id, sum(\"delta\") as grn, max(pos_timestamp) as post\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-01-10'\n", "      and inventory_update_type_id in (1,28,76,90,93)\n", "      -- and merchant_invoice_id = 'BHM1580T21005442'\n", "      group by 1,2\n", "    ) i ON pi.invoice_id=i.merchant_invoice_id AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-01-15'\n", "     AND extract(day from date(pi.pos_timestamp + interval '5.5 hour'))::int in (29,30,31) \n", "    and extract(month from date(pi.pos_timestamp + interval '5.5 hour'))=extract(month from current_date)\n", "    and extract(year from date(pi.pos_timestamp + interval '5.5 hour'))=extract(year from current_date)\n", "     -- and sto_id = 1130248\n", "   group by 1,2,3,4,5,6,7,8,9,13),\n", "sto_billed_grn AS\n", "  (SELECT sto_created_at,\n", "          outlet_id,\n", "          merchant_outlet_id,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.item_name,\n", "          sbg.item_id,\n", "          sbg.sto_id,\n", "          (sbg.billed_qty) billed_qty,\n", "          max(sbg.invoice_creation) invoice_creation,\n", "          sum(sbg.grn_quantity) grn_quantity,\n", "          max(sbg.grn_timestamp) grn_timestamp,\n", "          sum(sbg.billing_price*billed_qty) billing_price,\n", "          (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "          (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "          sbg.billing_price AS billing_unit_price,\n", "          sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            16,\n", "            17),\n", "discrepancy AS\n", "  (SELECT vendor_invoice_id,\n", "          item_id,\n", "          max((temp.created_at + interval '5.5 hour')) as disc_created_at,\n", "          sum(disc_qty) as disc_qty,\n", "          sum(case when disc_remark in ('UPC is invalid','Wrong item') then disc_qty else 0 end) as wrong_item_qty,\n", "          sum(case when disc_remark in ('MRP Mismatch') then disc_qty else 0 end) as variant_mismatch_qty,\n", "          sum(case when disc_remark in ('UPC not scannable','UPC not printed on SKU') then disc_qty else 0 end) as UPC_not_scannable_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)',\n", "          'STO Short Quantity (Transport)','STO Short Quantity (Source)') then disc_qty else 0 end) as short_qty,\n", "          sum(case when disc_remark in ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') then disc_qty else 0 end) as quality_issue_qty,\n", "          sum(case when disc_remark in ('Shelf Life guidelines breached') then disc_qty else 0 end) as expiry_nte_qty,\n", "          sum(case when disc_remark in ('Freebies missing') then disc_qty else 0 end) as freebies_missing_qty,\n", "          sum(case when disc_remark in ('Rcpt Qty > Invoice Qty') then disc_qty else 0 end) as excess_qty,\n", "          sum(case when disc_remark in ('Damaged','Packaging issue','Labelling issue') then disc_qty else 0 end) as damage_qty\n", "   FROM\n", "     (SELECT dn.id,\n", "             (dn.created_at + interval '5.5 hour') created_at,\n", "             dn.vendor_name,\n", "             dn.vendor_invoice_id,\n", "             dn.net_amount,\n", "             dn.outlet_id,\n", "             dnpd.upc_id,\n", "             dnpd.name,\n", "             dnpd.quantity as disc_qty,\n", "             dnpd.cost_price,\n", "             dnpd.landing_price,\n", "             dnpd.remark,\n", "             pp.item_id,\n", "             dnr.name as disc_remark\n", "      FROM\n", "        (SELECT d.*\n", "         FROM lake_pos.discrepancy_note d\n", "         INNER JOIN\n", "           (SELECT vendor_invoice_id,\n", "                   max(created_at) AS created_at\n", "            FROM lake_pos.discrepancy_note\n", "            WHERE extract(day from date(created_at))::int in (29,30,31)\n", "            and extract(month from date(created_at))=extract(month from current_date)\n", "            and extract(year from date(created_at))=extract(year from current_date)\n", "            GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "         AND d.created_at = d1.created_at) dn\n", "      LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "      left join lake_pos.discrepancy_reason dnr on dnr.id = dnpd.reason_code\n", "      LEFT JOIN\n", "        (SELECT DISTINCT item_id,\n", "                         upc\n", "         FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", ") TEMP\n", "   GROUP BY 1,\n", "            2),\n", "return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          max(return_timestamp) as return_timestamp,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             max(pi.pos_timestamp+interval'5.5 hrs') as return_timestamp,\n", "             sum(pipd.quantity) AS b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2021-01-15' -- and pi.original_invoice_id='BJC334T210003243'\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4)tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (23,35) THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (24,36) THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (25,37) THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id IN (66,68) THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id -- where  tab1.ret_invoice_id='BGC340U200000134'\n", "GROUP BY 1,\n", "         2),\n", "FINAL AS\n", "  (SELECT sbg.sto_id,\n", "          sbg.outlet_id,\n", "          ozm1.outlet_name AS sender_outlet,\n", "          sbg.merchant_outlet_id,\n", "          ozm2.outlet_name AS rec_outlet,\n", "          sbg.sto_created_at,\n", "          sbg.item_id,\n", "          sbg.billing_unit_price,\n", "          hot_landing_price,\n", "          sbg.invoice_id,\n", "          sbg.transfer_state,\n", "          sbg.Item_name,\n", "          sbg.billed_qty,\n", "          sbg.invoice_creation,\n", "          sbg.grn_quantity,\n", "          sbg.grn_timestamp,\n", "          sbg.billing_price,\n", "          sbg.diff_qty diff_qty_billed_grn,\n", "          sbg.diff_amt diff_amt_billed_grn,\n", "          COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "          disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          b2b.return_timestamp,\n", " COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", " COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", " COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", " COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", " COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", " COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", " COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id AND dis.item_id = sbg.item_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id AND b2b.item_id=sbg.item_id\n", ")\n", "\n", "        SELECT \n", "           case\n", "                when lower(trim(co.location))='bangalore' then 'bengaluru'\n", "                else lower(trim(co.location))\n", "           end as city,\n", "           fc.id AS facility_id,\n", "           fc.name AS facility_name,\n", "           sto_id,\n", "           outlet_id,\n", "           sender_outlet,\n", "           merchant_outlet_id,\n", "           rec_outlet,\n", "           sto_created_at,\n", "           grn_timestamp,\n", "           item_id,\n", "           item_name,\n", "           invoice_id,\n", "           invoice_creation,\n", "           billing_unit_price,\n", "           hot_landing_price,\n", "           CASE\n", "               WHEN transfer_state = 1 THEN 'Raised'\n", "               WHEN transfer_state = 2 THEN 'GRN Complete'\n", "               WHEN transfer_state = 3 THEN 'GRN Partial'\n", "               WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "               WHEN transfer_state = 5 THEN 'DISCREPANCY_NOTE_GENERATED'\n", "               ELSE 'NA'\n", "           END AS Invoice_State,\n", "           billing_price,\n", "           billed_qty,\n", "           grn_quantity,\n", "           discrepancy_qty::int as total_discrepancy_qty,\n", "           disc_created_at,\n", "          wrong_item_qty,\n", "          variant_mismatch_qty,\n", "          UPC_not_scannable_qty,\n", "          short_qty,\n", "          quality_issue_qty,\n", "          expiry_nte_qty,\n", "          freebies_missing_qty,\n", "          excess_qty,\n", "          damage_qty,\n", "          return_timestamp,\n", "          tot_returned,\n", "          b2breturn_good,\n", "          b2breturn_expired,\n", "          b2breturn_damaged,\n", "          b2breturn_near_expiry,\n", "          lost_in_transit_negative,\n", "          diff_qty_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned) as diff_qty,\n", "          diff_amt_billed_grn,\n", "          (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt,\n", "          hot_landing_price*tot_returned AS b2b_return_amount\n", "    FROM FINAL f\n", "    LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "    LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "    WHERE billed_qty>0\n", "    and diff_amt !=0\n", "    and sender_outlet not like '%%Infra%%'\n", "    and rec_outlet not like '%%Infra%%'\n", "    order by city,facility_id,sto_created_at,invoice_creation\n", "    \"\"\"\n", "\n", "esto_week5 = read_sql_query(esto_week5_sql, redshift)\n", "esto_week5.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "25de2902-6632-4b4d-866b-c4df8f5dcbfc", "metadata": {}, "outputs": [], "source": ["print(esto_week1.shape)\n", "print(esto_week2.shape)\n", "print(esto_week3.shape)\n", "print(esto_week4.shape)\n", "print(esto_week5.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "84ece31a-4573-456c-a3fd-30c081ac1932", "metadata": {}, "outputs": [], "source": ["esto_monthly = esto_week1.append(esto_week2, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2b081d14-ca93-4431-9143-541300020102", "metadata": {}, "outputs": [], "source": ["esto_monthly = esto_monthly.append(esto_week3, ignore_index=True)\n", "print(esto_monthly.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "95bc2141-7a15-457a-b998-bafb87674c48", "metadata": {}, "outputs": [], "source": ["esto_monthly = esto_monthly.append(esto_week4, ignore_index=True)\n", "esto_monthly = esto_monthly.append(esto_week5, ignore_index=True)\n", "print(esto_monthly.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "332f60ad-9642-4020-b643-9349e05c55b8", "metadata": {}, "outputs": [], "source": ["esto_monthly[\"key\"] = (\n", "    esto_monthly[\"sto_id\"].astype(str) + \"|\" + esto_monthly[\"item_id\"].astype(str)\n", ")\n", "print(esto_monthly.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "032b16e3-a4fc-4bad-b3d5-b6c599c57689", "metadata": {}, "outputs": [], "source": ["esto_monthly.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ee7a772e-5913-4108-945c-3834ed1797eb", "metadata": {}, "outputs": [], "source": ["esto_monthly = esto_monthly.fillna(0)\n", "esto_monthly.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4b51b5e8-69bf-4329-aa39-3a75e401943a", "metadata": {}, "outputs": [], "source": ["esto_monthly.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "fe4048f0-61ef-46b2-a4e1-195806d9e451", "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"city\": \"Column name sufficient for description\",\n", "    \"facility_id\": \"Column name sufficient for description\",\n", "    \"facility_name\": \"Column name sufficient for description\",\n", "    \"sto_id\": \"Column name sufficient for description\",\n", "    \"outlet_id\": \"Column name sufficient for description\",\n", "    \"sender_outlet\": \"Column name sufficient for description\",\n", "    \"merchant_outlet_id\": \"Column name sufficient for description\",\n", "    \"rec_outlet\": \"Column name sufficient for description\",\n", "    \"sto_created_at\": \"Column name sufficient for description\",\n", "    \"grn_timestamp\": \"Column name sufficient for description\",\n", "    \"item_id\": \"Column name sufficient for description\",\n", "    \"item_name\": \"Column name sufficient for description\",\n", "    \"invoice_id\": \"Column name sufficient for description\",\n", "    \"invoice_creation\": \"Column name sufficient for description\",\n", "    \"billing_unit_price\": \"Column name sufficient for description\",\n", "    \"hot_landing_price\": \"Column name sufficient for description\",\n", "    \"invoice_state\": \"Column name sufficient for description\",\n", "    \"billing_price\": \"Column name sufficient for description\",\n", "    \"billed_qty\": \"Column name sufficient for description\",\n", "    \"grn_quantity\": \"Column name sufficient for description\",\n", "    \"total_discrepancy_qty\": \"Column name sufficient for description\",\n", "    \"disc_created_at\": \"Column name sufficient for description\",\n", "    \"wrong_item_qty\": \"Column name sufficient for description\",\n", "    \"variant_mismatch_qty\": \"Column name sufficient for description\",\n", "    \"upc_not_scannable_qty\": \"Column name sufficient for description\",\n", "    \"short_qty\": \"Column name sufficient for description\",\n", "    \"quality_issue_qty\": \"Column name sufficient for description\",\n", "    \"expiry_nte_qty\": \"Column name sufficient for description\",\n", "    \"freebies_missing_qty\": \"Column name sufficient for description\",\n", "    \"excess_qty\": \"Column name sufficient for description\",\n", "    \"damage_qty\": \"Column name sufficient for description\",\n", "    \"return_timestamp\": \"Column name sufficient for description\",\n", "    \"tot_returned\": \"Column name sufficient for description\",\n", "    \"b2breturn_good\": \"Column name sufficient for description\",\n", "    \"b2breturn_expired\": \"Column name sufficient for description\",\n", "    \"b2breturn_damaged\": \"Column name sufficient for description\",\n", "    \"b2breturn_near_expiry\": \"Column name sufficient for description\",\n", "    \"lost_in_transit_negative\": \"Column name sufficient for description\",\n", "    \"diff_qty_billed_grn\": \"Column name sufficient for description\",\n", "    \"diff_qty\": \"Column name sufficient for description\",\n", "    \"diff_amt_billed_grn\": \"Column name sufficient for description\",\n", "    \"diff_amt\": \"Column name sufficient for description\",\n", "    \"b2b_return_amount\": \"Column name sufficient for description\",\n", "    \"key\": \"Unique key\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b78903e9-3ea4-467e-b71f-7108dda9c35c", "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "column_dtypes = redshift_schema(esto_monthly)\n", "column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c75030d0-9362-4eff-a979-e18de8ea9339", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"esto_diff\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"city\"],\n", "    \"incremental_key\": \"sto_created_at\",\n", "    \"load_type\": \"upsert\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"ESTO Diff\",  # Description of the table being sent to redshift\n", "}\n", "if esto_monthly.shape[0] > 0:\n", "    start_r = time.time()\n", "    pb.to_redshift(esto_monthly, **kwargs)\n", "    end_r = time.time()\n", "    print(round((end_r - start_r) / 60, 2))\n", "else:\n", "    print(\"Not updated\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3e06bae-5068-4481-b17f-d8b97dd04a02", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}