{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import psycopg2\n", "import pandas as pd\n", "import scipy\n", "import sklearn\n", "import numpy as np\n", "import logging\n", "import pencilbox as pb\n", "from tabulate import tabulate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "\n", "perishable_list as\n", "(\n", "select \n", "--facility_id,\n", "item_id,\n", "'true' as is_persh\n", "from metrics.ap_inventory_perishable_items\n", "group by 1,2\n", "),\n", "\n", "\n", "tbl_treatment as\n", "(\n", "select\n", "f.zone,\n", "facility_name,\n", "a.facility_id,\n", "a.item_id,\n", "uom as variant,\n", "l0,\n", "is_gb,\n", "bucket_x,\n", "buckets,\n", "date(order_date) as date_of_snapshot,\n", "assortment_date,\n", "coalesce(actual_quantity,0) as actual_quantity,\n", "coalesce(blocked_quantity,0) as blocked_quantity,\n", "coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) as unblocked_quantity,\n", "inv_flag as inventory_available,\n", "app_live, \n", "per.is_persh ,\n", "new_substate\n", "from \n", "rpc_daily_availability a\n", "join(select date(order_date) as order_dt, max(order_date)as m_order_date from consumer.rpc_daily_availability group by 1)b \n", "on a.order_date = b.m_order_date\n", "\n", "left join zone_tbl_treatment f on a.facility_id = f.facility_id\n", "left join perishable_list per on a.item_id = per.item_id \n", "\n", "where date(order_date) = current_date\n", "and new_substate = 1\n", "and (date_of_activation <= order_date or inv_flag = 1)\n", "--and assortment_date <= order_date\n", "AND a.facility_id not in (64,52,41,35,27,56,37,9,54,50,5,18,4,11,21,13,31,120,40,45,118,141,151,33,38,68,51,47,69,210,204)\n", "and a.facility_id in (376,388,392,387,435,377,375,417,237,209,161,201,370,227,219,247,391,448,285,228,397,403,197,246,135,223,238,271,319)\n", "and facility_name not ilike '%%bulk%%'\n", "and facility_name not ilike '%%b2b%%'\n", "and facility_name not ilike '%%os%%'\n", "and facility_name not ilike '%%feeder%%'\n", "AND facility_name not ilike '%%Grofers Market%%'\n", "--and facility_name not ilike '%%Dark%%'\n", "-- and facility_name not ilike '%%EC%%'\n", "AND facility_name not ilike '%%Ninja%%'\n", "AND facility_name not ilike '%%donate%%'\n", "AND facility_name not ilike '%%amazon%%'\n", "--AND facility_name not ilike '%%milestone%%'\n", "AND (facility_name ilike '%% ES%%')\n", "--and e.facility_id is null AND f.facility_id is null\n", "AND (((unreliable is null or unreliable = 0 or unreliable = 'NA') AND (unorderable is null or unorderable = 0 or unorderable = 'NA')) \n", "or coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) > 0)\n", "),\n", "\n", "\n", "metrics_calculation as\n", "(\n", "select\n", "date_of_snapshot,\n", "zone,\n", "facility_id,\n", "facility_name,\n", "\n", "count(distinct case when (app_live = 1 or inventory_available = 1) and new_substate = 1 then item_id end) as live_articles,\n", "count(distinct case when new_substate = 1 then item_id end) as all_articles,\n", "\n", "count(distinct case when (app_live = 1 or inventory_available = 1) and bucket_x = 'Y' and new_substate = 1 then item_id end) as live_articles_bucket_x,\n", "count(distinct case when bucket_x = 'Y' and new_substate = 1 then item_id end) as all_bucket_x_articles,\n", "\n", "count(distinct case when (app_live = 1 or inventory_available = 1) and buckets = 'A' and new_substate = 1 then item_id end) as live_articles_bucket_a,\n", "count(distinct case when buckets = 'A' and new_substate = 1 then item_id end) as all_bucket_a_articles,\n", "\n", "count(distinct case when (app_live = 1 or inventory_available = 1) and buckets <> 'A' and new_substate = 1 then item_id end) as live_articles_bucket_b,\n", "count(distinct case when buckets  <> 'A' and new_substate = 1 then item_id end) as all_bucket_b_articles,\n", "\n", "count(distinct case when (app_live = 1 or inventory_available = 1) and is_persh = 'true' and new_substate = 1 then item_id end) as live_articles_persh,\n", "count(distinct case when is_persh = 'true' and new_substate = 1 then item_id end) as all_persh_articles\n", "\n", "from tbl_treatment\n", "group by 1,2,3,4\n", "),\n", "\n", "adding_level as\n", "(\n", "select\n", "date_of_snapshot,\n", "zone,\n", "facility_id,\n", "facility_name,\n", "sum(live_articles) as live_articles,\n", "sum(all_articles) as all_articles,\n", "\n", "sum(live_articles_bucket_x) as live_articles_bucket_x,\n", "sum(all_bucket_x_articles) as all_bucket_x_articles,\n", "\n", "sum(live_articles_bucket_a) as live_articles_bucket_a,\n", "sum(all_bucket_a_articles) as all_bucket_a_articles,\n", "\n", "sum(live_articles_bucket_b) as live_articles_bucket_b,\n", "sum(all_bucket_b_articles) as all_bucket_b_articles\n", "\n", "from\n", "metrics_calculation\n", "where \n", "facility_name ilike '%% es%%'\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "final as\n", "(\n", "select\n", "date_of_snapshot,\n", "zone,\n", "facility_id,\n", "facility_name,\n", "\n", "live_articles*100.000/(all_articles + 0.000001) as Overall_availability,\n", "\n", "live_articles_bucket_x*100.000/(all_bucket_x_articles + 0.000001) as bucket_x_availability,\n", "\n", "live_articles_bucket_a*100.000/(all_bucket_a_articles + 0.000001) as bucket_a_availability\n", "\n", "from adding_level \n", "where facility_name <> 'NA'\n", ")\n", "\n", "select\n", "facility_name,\n", "Overall_availability,\n", "bucket_x_availability,\n", "bucket_a_availability\n", "from final\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(query, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "with pre_esto as (\n", "select a.*,\n", "b.facility_id as sender_facility_id, \n", "cf.name as sender_facility_name,\n", "c.facility_id as receiver_facility_id, \n", "f.name as receiver_facility_name\n", "\n", "from metrics.esto_details a\n", "left join lake_retail.console_outlet b on a.sender_outlet_id = b.id\n", "left join lake_crates.facility cf on cf.id = b.facility_id\n", "left join lake_retail.console_outlet c on a.receiving_outlet_id = c.id\n", "left join lake_crates.facility f on f.id = c.facility_id\n", ")\n", ",\n", "base as (\n", "select\n", "sto_id,\t\n", "sender_facility_id,\t\n", "sender_facility_name,\t\n", "receiver_facility_id,\t\n", "receiver_facility_name,\n", "MAX(date(sto_created_at)) as sto_created_at ,\t\n", "MAX(date(sto_invoice_created_at)) as sto_invoice_created_at , \n", "MAX(date(delivery_date)) as delivery_date_expiry,\n", "MAX(date(grn_created_at)) as grn_created_at,\n", "max(sto_type) as sto_type, \n", "max(sto_state) as sto_state, \n", "max(invoice_state) as invoice_state,\n", "sum(reserved_quantity) as reserved_qty ,\n", "sum(billed_quantity) as billed_qty ,\n", "sum(inwarded_quantity) as inwarded_qty,\n", "max(cast(sto_created_at as time)) as sto_created_tm ,\n", "max(cast(grn_created_at as time)) as grn_created_tm,\n", "max(invoice_id) as invoice_id \n", "\n", "from pre_esto a\n", "where date(sto_created_at) between current_date-14  and current_date\n", "AND STO_STATE NOT IN ('Manual Expiry')\n", "AND receiver_facility_id in (397,209,247,223,370,237,219,197,135,227,161,201,228,403,246,238,450,271,448,285,404,319,391,479,478,480,449) \n", "and sender_facility_id  in (30,15,29,12,92,494,264,236,398)\n", "group by 1,2,3,4,5\n", "-- order by 5\n", ")\n", ",\n", "doi_in_transit as (\n", "select\n", "receiver_facility_name,\n", "-- sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' \n", "-- and receiver_facility_name='Super Store Gurgaon 32 Milestone ES3' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "case when receiver_facility_name='Super Store Gurgaon 32 Milestone ES3' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Darks - Delhi Jhilmil ES1' then\n", "sum(case when sender_facility_name='Super Store Dasna 2 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Delhi Kakrola ES5 Holisol' then \n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Delhi Lado Sarai ES6 Holisol' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Delhi Nasirpur ES4 Holisol' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Ghaziabad Indirapuram ES1' then\n", "sum(case when sender_facility_name='Super Store Dasna 2 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Ghaziabad Nehru Nagar ES2' then\n", "sum(case when sender_facility_name='Super Store Dasna 2 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Ardee City ES16' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Ashok Vihar ES1' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Badshahpur Sector 69 ES19' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Basai ES15' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Begumpur ES20M' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Chakkarpur ES6' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Hero Honda Chowk ES9' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Laxman Vihar ES17' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Maneshar ES8' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Palam Vihar ES7' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Prem Nagar ES21' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 112 ES14' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 18 ES22' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 39 ES10' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 53 ES11' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 64 ES12' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Sukhrali Enclave ES18' then\n", "sum(case when sender_facility_name='Farukhnagar - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Gurgaon Vijay Park ES4' then\n", "sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Jaipur VKI ES1' then\n", "sum(case when sender_facility_name='Super Store Jaipur - Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "\n", "when receiver_facility_name='Super Store Lucknow Balaganj ES1' then\n", "sum(case when sender_facility_name='Lucknow - SR Feeder Warehouse' and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "end\n", "as doi_in_transit\n", "from base\n", "-- where sto_created_at = current_date\n", "-- and sto_created_tm between '00:00:00' and '12:00:00'\n", "group by 1\n", ")\n", ",\n", "fill_rate as (\n", "select\n", "receiver_facility_name,\n", "\n", "case when receiver_facility_name='Super Store Gurgaon 32 Milestone ES3' \n", "and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "-- -----\n", "when receiver_facility_name='Darks - Delhi Jhilmil ES1' and sender_facility_name='Super Store Dasna 2 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Delhi Kakrola ES5 Holisol' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Delhi Lado Sarai ES6 Holisol' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Delhi Nasirpur ES4 Holisol' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Ghaziabad Indirapuram ES1' and sender_facility_name='Super Store Dasna 2 - Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Ghaziabad Nehru Nagar ES2' and sender_facility_name='Super Store Dasna 2 - Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Ardee City ES16' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Ashok Vihar ES1' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Badshahpur Sector 69 ES19' \n", "and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "-- sum(case when  and invoice_state = 'Raised' then reserved_qty else 0 end)\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Basai ES15' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Begumpur ES20M' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Chakkarpur ES6' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Hero Honda Chowk ES9' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Laxman Vihar ES17' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Maneshar ES8' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Palam Vihar ES7' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Prem Nagar ES21' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 112 ES14' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 18 ES22' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 39 ES10' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 53 ES11' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sector 64 ES12' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Sukhrali Enclave ES18' and sender_facility_name='Farukhnagar - SR Feeder Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Gurgaon Vijay Park ES4' and sender_facility_name='Super Store Gurgaon G4 - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Jaipur VKI ES1' and sender_facility_name='Super Store Jaipur - Warehouse' then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "\n", "when receiver_facility_name='Super Store Lucknow Balaganj ES1' and (sender_facility_name='Lucknow - SR Feeder Warehouse' or\n", "sender_facility_name='Bulk Lucknow 3SC - Warehouse' or sender_facility_name='Super Store Lucknow - Warehouse')\n", "then\n", "sum(inwarded_qty)*100::float/\n", "sum(reserved_qty) \n", "-- ------\n", "end as fill_rate\n", "\n", "-- sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' and receiver_facility_name='Super Store Gurgaon 32 Milestone ES3' \n", "-- then inwarded_qty else -1 end)*100::float/sum(case when sender_facility_name='Super Store Gurgaon G4 - Warehouse' \n", "-- and receiver_facility_name='Super Store Gurgaon 32 Milestone ES3' then reserved_qty else 1 end) as fill_rate\n", "from base\n", "-- where receiver_facility_name='Super Store Gurgaon 32 Milestone ES3'   ---- to make alerts for this dark store only \n", "group by 1, sender_facility_name\n", ")\n", ",\n", "fill_rate1 as (\n", "select\n", "receiver_facility_name,\n", "avg(fill_rate) as fill_rate\n", "from fill_rate\n", "group by 1\n", ")\n", "select\n", "d.receiver_facility_name,\n", "doi_in_transit,\n", "fill_rate\n", "from doi_in_transit d\n", "join fill_rate1 f on d.receiver_facility_name=f.receiver_facility_name\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_sql(query1, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.rename(columns={\"receiver_facility_name\": \"facility_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = df1.round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query3 = \"\"\"\n", "with zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "it_pd as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "multiplier,\n", "updated_on,\n", "max(updated_on) over (partition by product_id) as last_updated\n", "from \n", "it_pd_log\n", "),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from it_pd\n", "where last_updated = updated_on\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "P.UNIT AS Unit,\n", "C2.NAME AS L2,\n", "(case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "C.NAME AS L0,\n", "P.BRAND AS brand,\n", "P.MANUFACTURER AS manf,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "), \n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l0,\n", "cat.l1,\n", "cat.l2,\n", "cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l0) as l0,\n", "max(l1) as l1,\n", "max(l2) as l2,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "\n", "tbl_treatment as\n", "(\n", "select\n", "a.*,\n", "date(a.install_ts + interval '5.5 Hours') as checkout_date,\n", "om.city_name,\n", "b.product_id,\n", "c.item_id,\n", "d.is_pl,\n", "b.quantity*multiplier as quantity,\n", "multiplier,\n", "b.selling_price,\n", "om.external_id as virtual_merchant_id,\n", "d.variant_mrp,\n", "sum(d.variant_mrp) over (partition by a.id, b.product_id) as total_mrp\n", "from lake_oms_bifrost.oms_order a\n", "left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') \n", "between current_date - 40 AND current_date-1) b \n", "on a.id = b.order_id\n", "left join mapping c on b.product_id = c.product_id\n", "left join (select * from item_level_info where row_rank = 1) d on c.item_id = d.item_id\n", "left join ptype_tbl e on c.item_id = e.item_id\n", "left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "(a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "where\n", "date(a.install_ts + interval '5.5 Hours') between current_date-35 and current_date-1\n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "--and current_status = 'CANCELLED'\n", "and lq.pid is null\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "-- l0 not in ('Fruits & Vegetables', 'Vegetables & Fruits')\n", "),\n", "\n", "merchant_and_store_join as\n", "(\n", "select \n", "a.*,\n", "selling_price*variant_mrp*1.000/(total_mrp*multiplier) as item_selling_price,\n", "bb.facility_id\n", "from tbl_treatment a\n", "left join ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts where forecast_carts >0) bb on a.virtual_merchant_id = bb.merchant_id and date(a.checkout_date) = bb.f_date\n", "where rnk =1\n", "),\n", "\n", "final_Act as\n", "(\n", "select \n", "a.facility_id,\n", "\n", "sum(CASE when date(checkout_date)  = current_date-1 then a.quantity end) as act_qty_yt,\n", "sum(CASE when date(checkout_date)  between (current_date-7) and (current_date-1) then a.quantity end) as act_qty_l7,\n", "sum(CASE when date(checkout_date)   between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then a.quantity end) as act_qty_mtd,\n", "\n", "count(distinct CASE when date(checkout_date)  = current_date-1 then id end) as act_carts_yt,\n", "count(distinct CASE when date(checkout_date) between (current_date-7) and (current_date-1) then id end) as act_carts_l7,\n", "count(distinct CASE when date(checkout_date)  between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then  id end) as act_carts_mtd\n", "\n", "from merchant_and_store_join a\n", "group by 1\n", "),\n", "\n", "order_delivery as\n", "(\n", "select \n", "-- order_id, min(date(delivered_ts)) as delivery_date \n", "-- from order_data\n", "-- where date(delivered_ts) between current_date - 60 AND current_date\n", "order_id, \n", "min(date(install_ts)) as delivery_date \n", "from lake_oms_bifrost.oms_order_event\n", "where date(install_ts) between current_date - 40 AND current_date-1\n", "group by 1\n", "), \n", "\n", "tbl_del as\n", "(\n", "select\n", "a.*,\n", "date(a.install_ts + interval '5.5 Hours') as checkout_date,\n", "(TIMESTAMP 'epoch' + slot_end * INTERVAL '1 Second' + interval '5.5 hour')::DATE as delivery_date,\n", "om.city_name,\n", "b.product_id,\n", "c.item_id,\n", "b.quantity*multiplier as quantity,\n", "multiplier,\n", "b.selling_price,\n", "om.external_id as virtual_merchant_id,\n", "d.variant_mrp,\n", "sum(d.variant_mrp) over (partition by a.id, b.product_id) as total_mrp\n", "from lake_oms_bifrost.oms_order a\n", "left join lake_oms_bifrost.view_oms_merchant om on a.merchant_id = om.id\n", "left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') \n", "between current_date - 60 AND current_date) b \n", "on a.id = b.order_id\n", "left join mapping c on b.product_id = c.product_id\n", "left join (select * from item_level_info where row_rank = 1) d on c.item_id = d.item_id\n", "--left join order_delivery f on a.id = f.order_id\n", "where\n", "date(a.install_ts + interval '5.5 Hours') between current_date - 60 AND current_date-1\n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "and current_status = 'DELIVERED'\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "),\n", "\n", "merchant_and_store_join_del as\n", "(\n", "select \n", "a.*,\n", "selling_price*variant_mrp*1.000/(total_mrp*multiplier) as item_selling_price,\n", "b.real_merchant_id,\n", "c.outlet_id,\n", "--d.warehouse_id as hot_outlet_id,\n", "e.facility_id\n", "from tbl_del a\n", "left join\n", "(select * from lake_cms.gr_virtual_to_real_merchant_mapping where priority_order = 1 AND enabled_flag = true) b on a.virtual_merchant_id = b.virtual_merchant_id\n", "left join (select * from  lake_retail.console_outlet_cms_store  where active = true) c on b.real_merchant_id = c.cms_store\n", "--left join  lake_retail.warehouse_outlet_mapping  d on c.outlet_id = d.cloud_store_id\n", "left join  lake_retail.console_outlet  e on c.outlet_id = e.id\n", "where a.delivery_date between current_date-35 and current_date-1\n", "),\n", "\n", "final_del as\n", "(\n", "select \n", "facility_id, \n", "sum(CASE when date(delivery_date)  = current_date-1 then a.quantity end) as del_qty_yt,\n", "sum(CASE when date(delivery_date)  between (current_date-7) and (current_date-1) then a.quantity end) as del_qty_l7,\n", "sum(CASE when date(delivery_date)   between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then a.quantity end) as del_qty_mtd,\n", "\n", "count(distinct CASE when date(delivery_date)  = current_date-1 then id end) as del_carts_yt,\n", "count(distinct CASE when date(delivery_date) between (current_date-7) and (current_date-1) then id end) as del_carts_l7,\n", "count(distinct CASE when date(delivery_date)  between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then  id end) as del_carts_mtd\n", "\n", "from merchant_and_store_join_del a\n", "group by 1\n", "),\n", "\n", "f_data as\n", "(\n", "select * from ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts where forecast_carts>0)\n", "where rnk =1 and date(f_date) BETWEEN  current_date-35 and current_date-1\n", "),\n", "\n", "forecast_carts as \n", "(\n", "select facility_id, \n", "sum(case when date(f_date) = current_date-1 then forecast_carts  end) as fc_carts_yt,\n", "sum(case when date(f_date) between (current_date-7) and (current_date-1)  then forecast_carts end) as fc_carts_l7,\n", "sum(case when date(f_date)  between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1)  then forecast_carts end) as fc_carts_mtd\n", "\n", "from f_data f\n", "group by 1\n", "),\n", "\n", "Active_Base as\n", "(   \n", "select a.item_id,a.facility_id\n", "from  lake_rpc.product_facility_master_assortment  a\n", "where a.master_assortment_substate_id=1 and a.active=1\n", "group by 1,2\n", "),\n", "\n", "<PERSON><PERSON><PERSON> as\n", "(\n", "select \n", "a.item_id ,\n", "facility_id,\n", "sum(case when date(date) = current_date-1 then consumption end) as fc_qty_yt,\n", "sum(case when date(date) between (current_date-7) and (current_date-1) then consumption end) as fc_qty_l7,\n", "sum(case when date(date)  between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1)  then consumption end) as fc_qty_mtd\n", "\n", "from  lake_snorlax.date_wise_consumption  a\n", "left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "group by 1,2\n", "),\n", "\n", "fc_data as\n", "(\n", "select a.facility_id,\n", "sum(fc_qty_yt) as cons_qty_yt,\n", "sum(fc_qty_l7) as cons_qty_l7,\n", "sum(fc_qty_mtd) as cons_qty_mtd\n", "from snorlax a\n", "inner join active_base b on a.item_id = b.item_id and a.facility_id = b.facility_id\n", " group by 1\n", "),\n", " \n", "grn_data as\n", "(     \n", "select\n", "date(a.created_at + interval '5.5 Hours') as date_grn,\n", "item_id,\n", "o.facility_id,  \n", "sum(a.quantity) as grn_qty, \n", "sum(a.landing_price*a.quantity) as grn_value \n", "from lake_po.po_grn a\n", "left join lake_retail.console_outlet o on a.outlet_id = o.id\n", "where date(a.created_at + interval '5.5 Hours') between  current_date-35 and current_date-1\n", "group by 1,2,3\n", "),\n", "\n", "final_grn as\n", "(\n", "select facility_id,\n", "sum(case when date_grn = current_date-1 then grn_qty end) as grn_qty_yt,\n", "sum(case when date_grn between  (current_date-7) and (current_date-1) then grn_qty end) as grn_qty_l7,\n", "sum(case when date_grn between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then grn_qty end) as grn_qty_mtd\n", "--sum(grn_value) as grn_val\n", "from grn_data\n", "group by 1\n", "),\n", "\n", "esto_grn as \n", "(\n", "select o.facility_id, \n", "sum(case when date(grn_details_created_at) = current_date-1 then inwarded_quantity end) as esto_inw_qty_yt,\n", "sum(case when date(grn_details_created_at) between  (current_date-7) and (current_date-1) then inwarded_quantity end) as esto_inw_qty_l7,\n", "sum(case when date(grn_details_created_at) between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then inwarded_quantity end) as esto_inw_qty_mtd\n", "from metrics.esto a\n", "left join (select id,facility_id from lake_retail.console_outlet) o on a.receiving_outlet_id = o.id\n", "where date(grn_details_created_at) between  current_date-35 and current_date-1\n", "group by 1\n", "),\n", "\n", "esto_billed as \n", "(\n", "select o.facility_id, \n", "sum(case when date(sto_billed_at) = current_date-1 then billed_quantity end) as esto_bil_qty_yt,\n", "sum(case when date(sto_billed_at) between  (current_date-7) and (current_date-1) then billed_quantity end) as esto_bil_qty_l7,\n", "sum(case when date(sto_billed_at) between ((select date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0) \n", "and (current_date-1) then billed_quantity end) as esto_bil_qty_mtd\n", "from metrics.esto a\n", "left join (select id,facility_id from lake_retail.console_outlet) o on a.sender_outlet_id = o.id\n", "where date(sto_billed_at) between  current_date-40 and current_date-1\n", "group by 1\n", "),\n", "\n", "final_cal as\n", "(\n", "select type, zone, a.id as facility_id, a.name as facility,\n", "\n", "sum(fc_carts_yt) as fc_carts_yt,\n", "sum(act_carts_yt) as actl_carts_yt, \n", "sum(cons_qty_yt) as fc_qty_yt,\n", "sum(act_qty_yt) as actl_qty_yt, \n", "sum(grn_qty_yt) as po_grn_qty_yt,\n", "sum(esto_inw_qty_yt) as esto_grn_qty_yt,\n", "sum(esto_bil_qty_yt) as esto_billed_qty_yt,\n", "sum(del_qty_yt) as dell_qty_yt, \n", "\n", "sum(fc_carts_l7) as fc_carts_l7,\n", "sum(act_carts_l7) as actl_carts_l7, \n", "sum(cons_qty_l7) as fc_qty_l7,\n", "sum(act_qty_l7) as actl_qty_l7, \n", "sum(grn_qty_l7) as po_grn_qty_l7,\n", "sum(esto_inw_qty_l7) as esto_grn_qty_l7,\n", "sum(esto_bil_qty_l7) as esto_billed_qty_l7,\n", "sum(del_qty_l7) as dell_qty_l7, \n", "\n", "sum(fc_carts_mtd) as fc_carts_mtd,\n", "sum(act_carts_mtd) as actl_carts_mtd, \n", "sum(cons_qty_mtd) as fc_qty_mtd,\n", "sum(act_qty_mtd) as actl_qty_mtd, \n", "sum(grn_qty_mtd) as po_grn_qty_mtd,\n", "sum(esto_inw_qty_mtd) as esto_grn_qty_mtd,\n", "sum(esto_bil_qty_mtd) as esto_billed_qty_mtd,\n", "sum(del_qty_mtd) as dell_qty_mtd\n", "\n", "from lake_crates.facility a\n", "left join forecast_carts aa on a.id = aa.facility_id\n", "left join fc_data b on a.id = b.facility_id\n", "left join final_grn c on a.id = c.facility_id\n", "left join final_Act d on a.id = d.facility_id\n", "left join final_del dd on a.id = dd.facility_id\n", "left join esto_grn e on  a.id = e.facility_id\n", "left join esto_billed f on  a.id = f.facility_id\n", "\n", "--left join lake_crates.facility cf on cf.id = a.facility_id\n", "left join zone_tbl_treatment zz on a.id = zz.facility_id\n", "left join (select facility_id , type from consumer.city_facility_split group by 1,2) fs on a.id = fs.facility_id\n", "where a.name not ilike '%%Dark%%'\n", "group by 1,2,3,4\n", ")\n", "\n", "select\n", "facility as facility_name,\n", "case when (actl_qty_yt > dell_qty_yt) or (actl_qty_yt is not null and dell_qty_yt is null) then actl_qty_yt\n", "     when (actl_qty_yt < dell_qty_yt) or (actl_qty_yt is null and dell_qty_yt is not null) then dell_qty_yt\n", "     else 1 end as outward_qty\n", "from final_cal\n", "--where facility='Super Store Gurgaon 32 Milestone ES3'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dff3 = pd.read_sql(query3, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dff4 = pd.merge(df1, dff3, on=\"facility_name\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dff4[\"doi_in_transit_ratio\"] = dff4[\"doi_in_transit\"] / dff4[\"outward_qty\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dff4 = dff4[[\"facility_name\", \"doi_in_transit_ratio\", \"fill_rate\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dff4 = dff4.round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "with zone as (\n", "    select\n", "    facility_id,\n", "    max(zone) as zone\n", "    from metrics.outlet_zone_mapping\n", "    group by 1\n", "),\n", "\n", "doi_table as (\n", "    select zone, \n", "          (case when facility_name ilike '%% ES%%' then 'Dark Store' \n", "          when facility_name ilike '%%dark%%' then 'Franchise Store' \n", "          when a.facility_id = 29 then 'Dark Store' else 'NDD' end) as store_type, \n", "          a.*\n", "    from metrics.doi_daily_detail a\n", "    left join zone b on a.facility_id = b.facility_id\n", "    where date(date_of_snapshot) = current_date-1\n", "),\n", "\n", "on_facility as (\n", "    select \n", "    a.zone, \n", "    a.store_type, \n", "    a.facility_id, \n", "    facility_name,\n", "    sum(actual_quantity*average_landing_price) as \"Inventory Net Worth\", \n", "    sum((actual_quantity-blocked_quantity)*average_landing_price) as \"Unblocked Inventory Net Worth\",\n", "    sum(avg_30_days_forecast_sales)*0.95 as \"Total Forecasted Sales\",\n", "    (case when \"Total Forecasted Sales\" > 0 then \"Inventory Net Worth\"/\"Total Forecasted Sales\" else 0 end) as \"Forecast DOI\",\n", "    (case when \"Total Forecasted Sales\" > 0 then \"Unblocked Inventory Net Worth\"/\"Total Forecasted Sales\" else 0 end) as \"Unblocked Forecast DOI\",\n", "    \n", "    coalesce(sum(case when bucket='X' then actual_quantity*average_landing_price end),0) as \"Bucket X Inventory Net Worth\",\n", "    coalesce(sum(case when bucket='X' then (actual_quantity-blocked_quantity)*average_landing_price end),0) as \"Bucket X Unblocked Inventory Net Worth\",\n", "    coalesce(sum(case when bucket='X' then avg_30_days_forecast_sales end),0)*0.95 as \"Bucket X Total Forecasted Sales\",\n", "    (case when \"Bucket X Total Forecasted Sales\" > 0 then \"Bucket X Inventory Net Worth\"/\"Bucket X Total Forecasted Sales\" else 0 end) as \"Bucket_X_Forecast_DOI\",\n", "    (case when \"Bucket X Total Forecasted Sales\" > 0 then \"Bucket X Unblocked Inventory Net Worth\"/\"Bucket X Total Forecasted Sales\" else 0 end) as \"Bucket X Unblocked Forecast DOI\",\n", "    \n", "    coalesce(sum(case when bucket in ('X','A') then actual_quantity*average_landing_price end),0) as \"Bucket A Inventory Net Worth\",\n", "    coalesce(sum(case when bucket in ('X','A') then (actual_quantity-blocked_quantity)*average_landing_price end),0) as \"Bucket A Unblocked Inventory Net Worth\",\n", "    coalesce(sum(case when bucket in ('X','A') then avg_30_days_forecast_sales end),0)*0.95 as \"Bucket A Total Forecasted Sales\",\n", "    (case when \"Bucket A Total Forecasted Sales\" > 0 then \"Bucket A Inventory Net Worth\"/\"Bucket A Total Forecasted Sales\" else 0 end) as \"Bucket_A_Forecast_DOI\",\n", "    (case when \"Bucket A Total Forecasted Sales\" > 0 then \"Bucket A Unblocked Inventory Net Worth\"/\"Bucket A Total Forecasted Sales\" else 0 end) as \"Bucket A Unblocked Forecast DOI\"\n", "    \n", "    from doi_table a\n", "    group by 1,store_type,3,4\n", "    having \"Inventory Net Worth\" > 0\n", "    and (case when facility_name not ilike '%%bulk%%' then sum(checkout_sales) > 0 else \"Total Forecasted Sales\" >= 0 end)\n", "    )\n", "    ,\n", "base as (\n", "select * from(\n", "    (\n", "    select \n", "    '00 - Pan India' as \"Zone\", \n", "    '00 - Pan India' as store_type, \n", "    -999 as \"Facility ID\", \n", "    '00 - Pan India' as \"Facility_Name\",\n", "    sum(\"Inventory Net Worth\") as \"Inventory Net Worth\", \n", "    sum(\"Unblocked Inventory Net Worth\") as \"Unblocked Inventory Net Worth\",\n", "    sum(\"Total Forecasted Sales\") as \"Total Forecasted Sales\", \n", "    sum(\"Inventory Net Worth\")/sum(\"Total Forecasted Sales\") as \"Forecast DOI\",\n", "    sum(\"Unblocked Inventory Net Worth\")/sum(\"Total Forecasted Sales\") as \"Unblocked Forecast DOI\",\n", "    \n", "    sum(\"Bucket X Inventory Net Worth\") as \"Bucket X Inventory Net Worth\",\n", "    sum(\"Bucket X Unblocked Inventory Net Worth\") as \"Bucket X Unblocked Inventory Net Worth\",\n", "    sum(\"Bucket X Total Forecasted Sales\") as \"Bucket X Total Forecasted Sales\", \n", "    sum(\"Bucket X Inventory Net Worth\")/sum(\"Bucket X Total Forecasted Sales\") as \"Bucket_X_Forecast_DOI\",\n", "    sum(\"Bucket X Unblocked Inventory Net Worth\")/sum(\"Bucket X Total Forecasted Sales\") as \"Bucket X Unblocked Forecast DOI\",\n", "    \n", "    sum(\"Bucket A Inventory Net Worth\") as \"Bucket A Inventory Net Worth\", \n", "    sum(\"Bucket A Unblocked Inventory Net Worth\") as \"Bucket A Unblocked Inventory Net Worth\",\n", "    sum(\"Bucket A Total Forecasted Sales\") as \"Bucket A Total Forecasted Sales\",\n", "    sum(\"Bucket A Inventory Net Worth\")/sum(\"Bucket A Total Forecasted Sales\") as \"Bucket_A_Forecast_DOI\", \n", "    sum(\"Bucket A Unblocked Inventory Net Worth\")/sum(\"Bucket A Total Forecasted Sales\") as \"Bucket A Unblocked Forecast DOI\"\n", "    \n", "    from on_facility a\n", "    group by 1,2,3,4\n", "    )\n", "    union\n", "    (\n", "    select \n", "    zone, \n", "    zone as store_type, \n", "    -999 as facility_id, \n", "    zone as facility_name,\n", "    sum(\"Inventory Net Worth\") as \"Inventory Net Worth\",\n", "    sum(\"Unblocked Inventory Net Worth\") as \"Unblocked Inventory Net Worth\",\n", "    sum(\"Total Forecasted Sales\") as \"Total Forecasted Sales\",\n", "    sum(\"Inventory Net Worth\")/sum(\"Total Forecasted Sales\") as \"Forecast DOI\",\n", "    sum(\"Unblocked Inventory Net Worth\")/sum(\"Total Forecasted Sales\") as \"Unblocked Forecast DOI\",\n", "    \n", "    sum(\"Bucket X Inventory Net Worth\") as \"Bucket X Inventory Net Worth\", \n", "    sum(\"Bucket X Unblocked Inventory Net Worth\") as \"Bucket X Inventory Net Worth\", \n", "    sum(\"Bucket X Total Forecasted Sales\") as \"Bucket X Total Forecasted Sales\",\n", "    sum(\"Bucket X Inventory Net Worth\")/sum(\"Bucket X Total Forecasted Sales\") as \"Bucket X Forecast DOI\",\n", "    sum(\"Bucket X Unblocked Inventory Net Worth\")/sum(\"Bucket X Total Forecasted Sales\") as \"Bucket X Unblocked Forecast DOI\",\n", "    \n", "    sum(\"Bucket A Inventory Net Worth\") as \"Bucket A Inventory Net Worth\", \n", "    sum(\"Bucket A Unblocked Inventory Net Worth\") as \"Bucket A Unblocked Inventory Net Worth\", \n", "    sum(\"Bucket A Total Forecasted Sales\") as \"Bucket A Total Forecasted Sales\", \n", "    sum(\"Bucket A Inventory Net Worth\")/sum(\"Bucket A Total Forecasted Sales\") as \"Bucket_A_Forecast_DOI\",\n", "    sum(\"Bucket A Unblocked Inventory Net Worth\")/sum(\"Bucket A Total Forecasted Sales\") as \"Bucket A Unblocked Forecast DOI\"\n", "    from on_facility a\n", "    group by 1,2,3,4\n", "    )\n", "    union\n", "    (select * from on_facility)\n", ")\n", "\n", "-- order by \n", "-- case when \"Facility ID\" = -999 then 'A' end,\n", "-- case\n", "-- when zone = '00 - Pan India' then 'A1'\n", "-- when zone = 'North' then 'A2'\n", "-- when zone = 'South' then 'A3'\n", "-- when zone = 'West' then 'A4'\n", "-- when zone = 'East' then 'A5' end,\n", "-- store_type,\n", "-- \"Facility ID\",\n", "-- \"Facility Name\"\n", ")\n", "\n", "select\n", "facility_name,\n", "bucket_x_forecast_doi,\n", "bucket_a_forecast_doi\n", "from base \n", "where facility_name = 'Super Store Gurgaon 32 Milestone ES3'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3 = pd.merge(df, dff4, on=\"facility_name\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5 = df3[\n", "    (round(df3[\"bucket_x_availability\"]) < 80)\n", "    | (round(df3[\"bucket_a_availability\"]) < 70)\n", "    | (round(df3[\"fill_rate\"]) < 90)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df6 = df3[df3[\"doi_in_transit_ratio\"] < 0.5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df55 = pd.merge(\n", "    df5,\n", "    df6,\n", "    on=[\n", "        \"facility_name\",\n", "        \"overall_availability\",\n", "        \"bucket_x_availability\",\n", "        \"bucket_a_availability\",\n", "        \"doi_in_transit_ratio\",\n", "        \"fill_rate\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df8 = df55"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if not df8.empty:\n", "df8 = df8.T.reset_index().T.reset_index(drop=True)\n", "from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"Availability Status Report\"\n", "html_content = \"\"\"<html>\n", "<head>\n", "<style> \n", "table, th, td {{ border: 1px solid black; border-collapse: collapse; }}\n", "th, td {{ padding: 5px; }}\n", "</style>\n", "</head>\n", "<body><p>Hello team,</p>\n", "<p>Here is your data:</p>\n", "{table}\n", "<p><PERSON><PERSON>,</p>\n", "<p><PERSON><PERSON></p>\n", "</body></html>\"\"\"\n", "html_content = html_content.format(\n", "    table=tabulate(df8, showindex=False, tablefmt=\"html\")\n", ")\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}