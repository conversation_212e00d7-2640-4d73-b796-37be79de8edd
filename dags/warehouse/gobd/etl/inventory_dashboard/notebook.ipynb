{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conn = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_conn = pb.get_connection(\"retail\")\n", "# retail_conn.schema = \"ims\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["-- ------ Blocked Order Quantity split for days ------------ -- "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_order_query = \"\"\"\n", "select item_id,outlet as outlet_id,\n", "sum(case when date(current_date)=date(scheduled_date)then blocked_quantity else 0 end) as t_blocked_order_qty,\n", "sum(case when date(current_date+1)=date(scheduled_date) then blocked_quantity else 0 end) as blocked_order_qty_1,\n", "sum(case when date(current_date+1)<date(scheduled_date) then blocked_quantity else 0 end) as blocked_order_qty_more_than_2 from \n", "\n", "(select     o.outlet,\n", "           date(convert_timezone('Asia/Kolkata',o.scheduled_at)) as scheduled_date,\n", "           item_id,\n", "           sum(quantity) as blocked_quantity\n", "                       \n", "from  consumer.ims_order_details o\n", "inner join consumer.ims_order_items ioi on o.id = ioi.order_details_id\n", "where o.status_id in (1,3) and ioi.billed_at_pos= 0 -- and iod.outlet in (116) \n", "group by 1,2,3\n", ")a\n", "group by 1,2\n", "order by 1,3,2;\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_order = pd.read_sql(sql=blocked_order_query, con=conn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_order.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "-- ------ Blocked STO Quantity split for days ------------ -- "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_qty_query = \"\"\"\n", "select item_id,outlet_id,sum(case when (date(current_date)=date(scheduled_date)) and sto_type=1 then sto_reserved_quantity else 0 end) as t_sto_1_blocked_curr_date,\n", "sum(case when (date(current_date+1)=date(scheduled_date)) and sto_type=1 then sto_reserved_quantity else 0 end) as sto_blocked_1_day_1,\n", "sum(case when (date(current_date+1)<date(scheduled_date)) and sto_type=1 then sto_reserved_quantity else 0 end) as sto_blocked_1_more_than_2 ,\n", "sum(case when (date(current_date)=date(scheduled_date)) and sto_type=2 then sto_reserved_quantity else 0 end) as t_sto_2_blocked_curr_date,\n", "sum(case when (date(current_date+1)=date(scheduled_date)) and sto_type=2 then sto_reserved_quantity else 0 end) as sto_blocked_2_day_1,\n", "sum(case when (date(current_date+1)<date(scheduled_date)) and sto_type=2 then sto_reserved_quantity else 0 end) as sto_blocked_2_more_than_2 from \n", "(Select sto.outlet_id,\n", "       date(convert_timezone('Asia/Kolkata',delivery_date)) as scheduled_date,\n", "       sto_type,\n", "       item_id,\n", "       sum(reserved_quantity) as sto_reserved_quantity\n", "from consumer.ims_ims_sto_details sto\n", "inner join consumer.ims_ims_sto_item sto_item on sto.sto_id = sto_item.sto_id\n", "where sto.sto_state in (1) \n", "group by 1,2,3,4\n", ")\n", "group by 1,2\n", "order by 1,2;\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_qty = pd.read_sql(sql=blocked_qty_query, con=conn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_qty.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-- Pending Putaway Inventory -- Inventory received from feeder -- 76 = stock_in_priority_pending_putaway. -- 77 = priority_put_away_inventory\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Pending_Putaway_query = \"\"\"\n", "select outlet_id,outlet_name,item_id,sum(Pending_Putaway_Inventory) as Pending_Putaway_Inventory,sum(Inventory_received_from_feeder) as Inventory_received_from_feeder\n", "from \n", "(SELECT \n", "good_inv.outlet_id,\n", "outlet.name as outlet_name,\n", "product.item_id,\n", "good_inv.upc_id,\n", "good_inv.variant_id,\n", "\n", "\n", "sum(case when good_inv.inventory_update_type_id = 28 then good_inv.quantity end ) as Pending_Putaway_Inventory,\n", "sum(case when good_inv.inventory_update_type_id = 76 then good_inv.quantity end ) as Inventory_received_from_feeder\n", "\n", "FROM consumer.pos_ims_good_inventory good_inv \n", "INNER JOIN\n", "pos_console_outlet outlet on outlet.id = good_inv.outlet_id\n", "INNER JOIN\n", "pos_product_product product on product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1 AND good_inv.inventory_update_type_id = 28 \n", "group by 1,2,3,4,5)a\n", "group by 1,2,3;\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Pending_Putaway = pd.read_sql(sql=Pending_Putaway_query, con=conn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cms_update_physical_inventory_query = \"\"\"Select tt.*,\n", "CASE when feeder_live = 1 then CMS_update_inventory_for_feeder_enabled_items\n", "     when fnv_upper_limit is not null then CMS_update_inventory_fnv_upper_limit\n", "     when upper_limit is not null then CMS_update_inventory_upper_limit\n", "     else CMS_update_inventory end as CMS_Inventory_Update_Sent\n", "From (SELECT o.name AS outlet,\n", "       T.*\n", "FROM\n", "  (SELECT inv.outlet_id,\n", "          inv.item_id,\n", "          inv.quantity,\n", "          inv.feeder_quantity,\n", "          coalesce(order_blocked_quantity,0) AS order_blocked_quantity,\n", "          coalesce(esto_blocked_quantity,0) AS esto_blocked_quantity,\n", "          coalesce(tat_blocked_quantity,0) AS tat_blocked_quantity ,\n", "          feeder_live,\n", "          (CASE WHEN feeder_live = 1\n", "           AND (inv.quantity > tat_blocked_quantity + esto_blocked_quantity) THEN (inv.quantity + inv.feeder_quantity - order_blocked_quantity - esto_blocked_quantity) ELSE 0 END)AS CMS_update_inventory_for_feeder_enabled_items,\n", "          upper_limit,\n", "          (CASE WHEN upper_limit IS NOT NULL THEN (upper_limit - order_blocked_quantity - esto_blocked_quantity) ELSE 0 END)AS CMS_update_inventory_upper_limit,\n", "          fnv_upper_limit,\n", "          (CASE WHEN fnv_upper_limit IS NOT NULL THEN (fnv_upper_limit - order_blocked_quantity - esto_blocked_quantity) ELSE 0 END)AS CMS_update_inventory_fnv_upper_limit,\n", "          (CASE WHEN feeder_live = 0\n", "           AND upper_limit IS NULL\n", "           AND fnv_upper_limit IS NULL THEN (inv.quantity - order_blocked_quantity - esto_blocked_quantity) ELSE 0 END)AS CMS_update_inventory,\n", "          inv.quantity - coalesce(bloc_inv.order_blocked_quantity,0) - coalesce(bloc_inv.esto_blocked_quantity,0) as Physical_Inventory_at_the_outlet\n", "   FROM\n", "     (SELECT i.outlet_id,\n", "             i.item_id,\n", "             i.quantity,\n", "             i.feeder_live,\n", "             i.feeder_quantity,\n", "             i.upper_limit,\n", "             i.fnv_upper_limit\n", "      FROM ims.ims_item_inventory i\n", "      WHERE i.active = 1\n", "        AND i.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "           FROM retail.console_outlet_cms_store\n", "           WHERE active = 1\n", "             AND cms_update_active =1))inv\n", "   LEFT JOIN\n", "     (SELECT outlet_id,\n", "             item_id,\n", "             sum(order_blocked_quantity) AS order_blocked_quantity,\n", "             sum(esto_blocked_quantity) AS esto_blocked_quantity,\n", "             sum(tat_blocked_quantity) AS tat_blocked_quantity\n", "      FROM\n", "        (SELECT i.outlet_id,\n", "                i.item_id,\n", "                i.updated_at,\n", "                CASE WHEN i.blocked_type = 1 THEN i.quantity ELSE 0 END AS order_blocked_quantity,\n", "                                                                           CASE WHEN i.blocked_type = 2 THEN i.quantity ELSE 0 END AS esto_blocked_quantity,\n", "                                                                                                                                      CASE WHEN i.blocked_type = 3 THEN i.quantity ELSE 0 END AS tat_blocked_quantity\n", "         FROM ims.ims_item_blocked_inventory i\n", "         WHERE i.active = 1\n", "         ORDER BY 1,\n", "                  2) T2\n", "      GROUP BY 1,\n", "               2)bloc_inv ON inv.outlet_id = bloc_inv.outlet_id\n", "   AND inv.item_id = bloc_inv.item_id ) T\n", "LEFT JOIN retail.console_outlet o ON T.outlet_id = o.id\n", "ORDER BY Physical_Inventory_at_the_outlet ASC)tt;\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cms_update_physical_inventory = pd.read_sql_query(\n", "    sql=Cms_update_physical_inventory_query, con=retail_conn\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cms_update_physical_inventory.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_query = \"\"\"\n", "select item_id,facility_id,date(order_date) as order_date,new_substate\n", "from rpc_daily_availability\n", "where date(order_date)>=date(assortment_date)\n", "and date(order_date)= current_Date\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = pd.read_sql(sql=assortment_query, con=conn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-- In Transit and Reserved Quantity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["In_Transit_Reserved_Quantity_query = \"\"\"\n", "\n", "Select outlet_id, item_id,\n", "sum(case when sto_type=1 and inventory_type=1 then quantity else 0 end) as sto_1_inventory_1,\n", "sum(case when sto_type=1 and inventory_type=2 then quantity else 0 end) as sto_1_inventory_2,\n", "sum(case when sto_type=2 and inventory_type=1 then quantity else 0 end) as sto_2_inventory_1,\n", "sum(case when sto_type=2 and inventory_type=2 then quantity else 0 end) as sto_2_inventory_2\n", "from ims_ims_sto_mapping_inventory\n", "group by 1,2;\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["In_Transit_Reserved_Quantity = pd.read_sql(\n", "    sql=In_Transit_Reserved_Quantity_query, con=conn\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["In_Transit_Reserved_Quantity.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Pending_Putaway.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cms_update_physical_inventory.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_inv_pending_putaway = Cms_update_physical_inventory.merge(\n", "    Pending_Putaway, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_qty.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_order.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_inv_pending_putaway.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_data = blocked_qty.merge(\n", "    blocked_order, on=[\"item_id\", \"outlet_id\"], how=\"outer\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blocked_data.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_pending_blocked_orders = current_inv_pending_putaway.merge(\n", "    blocked_data, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_pending_blocked_orders.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_facility_mapping_query = \"\"\"\n", "select pco.id as outlet_id,facility_id,wf.name as facility_name from pos_console_outlet pco\n", "left join retail_warehouse_facility wf\n", "on pco.facility_id=wf.id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_facility_mapping = pd.read_sql(sql=outlet_facility_mapping_query, con=conn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_pending_blocked_orders_facility = ci_pending_blocked_orders.merge(\n", "    outlet_facility_mapping, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_pending_blocked_orders_facility.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_ci_pending_blocked_orders = ci_pending_blocked_orders_facility.merge(\n", "    assortment, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = pd.Timestamp(date, tz=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_ci_pending_blocked_orders[\"date_of_run\"] = date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_ci_pending_blocked_orders.date_of_run.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_ci_pending_blocked_orders_df = (\n", "    assortment_ci_pending_blocked_orders.drop_duplicates()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit = assortment_ci_pending_blocked_orders_df.merge(\n", "    In_Transit_Reserved_Quantity, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df = assortment_intransit.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df.to_csv(\"assortment_intransit_df.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df = assortment_intransit_df.rename(\n", "    columns={\n", "        \"quantity\": \"ims_available_inventory\",\n", "        \"feeder_quantity\": \"ims_feeder_quantity\",\n", "        \"CMS_Inventory_Update_Sent\": \"CMS_final_inventory_quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"quantity\", \"type\": \"integer\"},\n", "    {\"name\": \"feeder_quantity\", \"type\": \"integer\"},\n", "    {\"name\": \"order_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"esto_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"tat_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"feeder_live\", \"type\": \"integer\"},\n", "    {\"name\": \"CMS_update_inventory_for_feeder_enabled_items\", \"type\": \"float\"},\n", "    {\"name\": \"upper_limit\", \"type\": \"float\"},\n", "    {\"name\": \"CMS_update_inventory_upper_limit\", \"type\": \"float\"},\n", "    {\"name\": \"fnv_upper_limit\", \"type\": \"float\"},\n", "    {\"name\": \"CMS_update_inventory_fnv_upper_limit\", \"type\": \"float\"},\n", "    {\"name\": \"CMS_update_inventory\", \"type\": \"float\"},\n", "    {\"name\": \"Physical_Inventory_at_the_outlet\", \"type\": \"float\"},\n", "    {\"name\": \"CMS_Inventory_Update_Sent\", \"type\": \"float\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"pending_putaway_inventory\", \"type\": \"float\"},\n", "    {\"name\": \"inventory_received_from_feeder\", \"type\": \"float\"},\n", "    {\"name\": \"t_sto_1_blocked_curr_date\", \"type\": \"float\"},\n", "    {\"name\": \"sto_blocked_1_day_1\", \"type\": \"float\"},\n", "    {\"name\": \"sto_blocked_1_more_than_2\", \"type\": \"float\"},\n", "    {\"name\": \"t_sto_2_blocked_curr_date\", \"type\": \"float\"},\n", "    {\"name\": \"sto_blocked_2_day_1\", \"type\": \"float\"},\n", "    {\"name\": \"sto_blocked_2_more_than_2\", \"type\": \"float\"},\n", "    {\"name\": \"t_blocked_order_qty\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_order_qty_1\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_order_qty_more_than_2\", \"type\": \"float\"},\n", "    {\"name\": \"facility_id\", \"type\": \"float\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"new_substate\", \"type\": \"float\"},\n", "    {\"name\": \"date_of_run\", \"type\": \"timestamp\"},\n", "    {\"name\": \"sto_1_inventory_1\", \"type\": \"float\"},\n", "    {\"name\": \"sto_1_inventory_2\", \"type\": \"float\"},\n", "    {\"name\": \"sto_2_inventory_1\", \"type\": \"float\"},\n", "    {\"name\": \"sto_2_inventory_2\", \"type\": \"float\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inventory_dashboard\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"outlet_id\", \"date_of_run\"],\n", "    \"sortkey\": [\"date_of_run\", \"item_id\", \"outlet_id\"],\n", "    \"incremental_key\": \"date_of_run\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_intransit_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(assortment_intransit_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"done\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}