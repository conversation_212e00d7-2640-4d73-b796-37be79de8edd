{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = \"2021-10-22\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins_item_ids = [10106019, 10106020, 10106544, 10106612, 10106648, 10106652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select coalesce(inv_snap.facility_id, good_inv_snap.facility_id) as facility_id,\n", "       coalesce(inv_snap.facility_name, good_inv_snap.facility_name) as facility_name,\n", "       coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) as outlet_id,\n", "       coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name) as outlet_name,\n", "       coalesce(inv_snap.item_id, good_inv_snap.item_id) as item_id,\n", "       coalesce(inv_snap.name, good_inv_snap.name) as name,\n", "       coalesce(inv_snap.brand, good_inv_snap.brand) as brand,\n", "       coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer) as manufacturer,\n", "       coalesce(inv_snap.opening_qty, 0) + coalesce(good_inv_snap.opening_qty, 0) as opening_qty\n", "from\n", "  (select f.id as facility_id,\n", "          f.name as facility_name,\n", "          ris.outlet_id,\n", "          co.name as outlet_name,\n", "          pp.item_id,\n", "          id.name as name,\n", "          pb.name as brand,\n", "          pm.name as manufacturer,\n", "          sum(ris.quantity) as opening_qty\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_inventory_snapshot\n", "      where snapshot_datetime::date = '{start_date}'\n", "        and active = 1) ris\n", "   inner join\n", "     (select variant_id,\n", "             item_id\n", "      from lake_rpc.product_product\n", "      where active = 1\n", "        and item_id in {*coins_item_ids,}) pp on pp.variant_id = ris.variant_id\n", "   left join\n", "     (select item_id,\n", "             name,\n", "             brand_id\n", "      from lake_rpc.item_details) id on id.item_id = pp.item_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             manufacturer_id\n", "      from lake_rpc.product_brand) pb on pb.id = id.brand_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             facility_id\n", "      from lake_retail.console_outlet) co on co.id = ris.outlet_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_crates.facility) f on f.id = co.facility_id\n", "   group by 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) inv_snap\n", "full outer join\n", "  (select f.id as facility_id,\n", "          f.name as facility_name,\n", "          rgis.outlet_id,\n", "          co.name as outlet_name,\n", "          pp.item_id,\n", "          id.name as name,\n", "          pb.name as brand,\n", "          pm.name as manufacturer,\n", "          sum(rgis.quantity) as opening_qty\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_good_inventory_snapshot\n", "      where snapshot_datetime::date = '{start_date}'\n", "        and active = 1) rgis\n", "   inner join\n", "     (select variant_id,\n", "             item_id\n", "      from lake_rpc.product_product\n", "      where active = 1\n", "        and item_id in {*coins_item_ids,}) pp on pp.variant_id = rgis.variant_id\n", "   left join\n", "     (select item_id,\n", "             name,\n", "             brand_id\n", "      from lake_rpc.item_details) id on id.item_id = pp.item_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             manufacturer_id\n", "      from lake_rpc.product_brand) pb on pb.id = id.brand_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             facility_id\n", "      from lake_retail.console_outlet) co on co.id = rgis.outlet_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_crates.facility) f on f.id = co.facility_id\n", "   group by 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) good_inv_snap on inv_snap.outlet_id = good_inv_snap.outlet_id\n", "and inv_snap.item_id = good_inv_snap.item_id\n", "\"\"\"\n", "\n", "opening_snap = pd.read_sql_query(query, con=redshift)\n", "opening_snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select coalesce(inv_snap.facility_id, good_inv_snap.facility_id) as facility_id,\n", "       coalesce(inv_snap.facility_name, good_inv_snap.facility_name) as facility_name,\n", "       coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) as outlet_id,\n", "       coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name) as outlet_name,\n", "       coalesce(inv_snap.item_id, good_inv_snap.item_id) as item_id,\n", "       coalesce(inv_snap.name, good_inv_snap.name) as name,\n", "       coalesce(inv_snap.brand, good_inv_snap.brand) as brand,\n", "       coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer) as manufacturer,\n", "       coalesce(inv_snap.closing_qty, 0) + coalesce(good_inv_snap.closing_qty, 0) as closing_qty\n", "from\n", "  (select f.id as facility_id,\n", "          f.name as facility_name,\n", "          ris.outlet_id,\n", "          co.name as outlet_name,\n", "          pp.item_id,\n", "          id.name as name,\n", "          pb.name as brand,\n", "          pm.name as manufacturer,\n", "          sum(ris.quantity) as closing_qty\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_inventory_snapshot\n", "      where snapshot_datetime::date = current_date - 1\n", "        and active = 1) ris\n", "   inner join\n", "     (select variant_id,\n", "             item_id\n", "      from lake_rpc.product_product\n", "      where active = 1\n", "        and item_id in {*coins_item_ids,}) pp on pp.variant_id = ris.variant_id\n", "   left join\n", "     (select item_id,\n", "             name,\n", "             brand_id\n", "      from lake_rpc.item_details) id on id.item_id = pp.item_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             manufacturer_id\n", "      from lake_rpc.product_brand) pb on pb.id = id.brand_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             facility_id\n", "      from lake_retail.console_outlet) co on co.id = ris.outlet_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_crates.facility) f on f.id = co.facility_id\n", "   group by 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) inv_snap\n", "full outer join\n", "  (select f.id as facility_id,\n", "          f.name as facility_name,\n", "          rgis.outlet_id,\n", "          co.name as outlet_name,\n", "          pp.item_id,\n", "          id.name as name,\n", "          pb.name as brand,\n", "          pm.name as manufacturer,\n", "          sum(rgis.quantity) as closing_qty\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_good_inventory_snapshot\n", "      where snapshot_datetime::date = current_date - 1\n", "        and active = 1) rgis\n", "   inner join\n", "     (select variant_id,\n", "             item_id\n", "      from lake_rpc.product_product\n", "      where active = 1\n", "        and item_id in {*coins_item_ids,}) pp on pp.variant_id = rgis.variant_id\n", "   left join\n", "     (select item_id,\n", "             name,\n", "             brand_id\n", "      from lake_rpc.item_details) id on id.item_id = pp.item_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             manufacturer_id\n", "      from lake_rpc.product_brand) pb on pb.id = id.brand_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "   left join\n", "     (select id,\n", "             name,\n", "             facility_id\n", "      from lake_retail.console_outlet) co on co.id = rgis.outlet_id\n", "   left join\n", "     (select id,\n", "             name\n", "      from lake_crates.facility) f on f.id = co.facility_id\n", "   group by 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) good_inv_snap on inv_snap.outlet_id = good_inv_snap.outlet_id\n", "and inv_snap.item_id = good_inv_snap.item_id\n", "\"\"\"\n", "\n", "closing_snap = pd.read_sql_query(query, con=redshift)\n", "closing_snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# opening_snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# closing_snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snap = closing_snap.merge(\n", "    opening_snap,\n", "    how=\"outer\",\n", "    on=[\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"brand\",\n", "        \"manufacturer\",\n", "    ],\n", ")\n", "snap = snap.fillna(\n", "    {\n", "        \"facility_name\": \"-\",\n", "        \"outlet_name\": \"-\",\n", "        \"name\": \"-\",\n", "        \"brand\": \"-\",\n", "        \"manufacturer\": \"-\",\n", "    }\n", ").<PERSON>na(0)\n", "snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_ids = 1, 28, 76, 90\n", "sale_ids = 22, 75\n", "sale_return_ids = (54,)\n", "customer_sale_ids = 2, 50\n", "customer_return_ids = 3, 4, 5, 6, 23, 29, 30, 31, 32, 35, 81, 82\n", "bad_stock_ids = 11, 12, 13, 64\n", "stock_update_positive_ids = 14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122\n", "stock_update_negative_ids = 15, 27, 39, 40, 41, 42, 43, 73, 117, 129\n", "liquidation_ids = 16, 127\n", "esto_ids = 17, 92, 91, 124, 125\n", "repackaged_ids = (18,)\n", "vendor_return_ids = 19, 126"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select iil.outlet_id,\n", "       pp.item_id,\n", "       iil.inventory_update_type_id,\n", "       iii.source_type,\n", "       iil.\"delta\" as \"delta\"\n", "from\n", "  (select inventory_update_id,\n", "          variant_id,\n", "          \"delta\",\n", "          invoice_id,\n", "          inventory_update_type_id,\n", "          outlet_id,\n", "          transaction_lp\n", "   from lake_ims.ims_inventory_log\n", "   where pos_timestamp between '{start_date}'::timestamp - interval '5.5 hrs' + interval '1 day' and (current_date - 1)::timestamp - interval '5.5 hrs' + interval '1 day') iil\n", "inner join\n", "  (select variant_id,\n", "          item_id\n", "   from lake_rpc.product_product\n", "   where active = 1\n", "     and item_id in {*coins_item_ids,}) pp on pp.variant_id = iil.variant_id\n", "left join\n", "  (select inventory_update_id,\n", "          grn_id\n", "   from lake_ims.ims_inventory_stock_details) isd on isd.inventory_update_id = iil.inventory_update_id\n", "left join\n", "  (select grn_id,\n", "          source_type\n", "   from lake_ims.ims_inward_invoice) iii on iii.grn_id = isd.grn_id\n", "\"\"\"\n", "\n", "inv_log = pd.read_sql_query(query, con=redshift)\n", "inv_log.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv_log.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_grn = inv_log[inv_log[\"inventory_update_type_id\"].isin(grn_ids)]\n", "po_grn = po_grn[po_grn[\"source_type\"] == 1]\n", "po_grn = (\n", "    po_grn.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"delta\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"delta\": \"po_grn\"})\n", ")\n", "po_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = snap.merge(po_grn, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_grn = inv_log[inv_log[\"inventory_update_type_id\"].isin(grn_ids)]\n", "sto_grn = sto_grn[sto_grn[\"source_type\"].isin([2, 3])]\n", "sto_grn = (\n", "    sto_grn.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"delta\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"delta\": \"sto_grn\"})\n", ")\n", "sto_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.merge(sto_grn, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv_recon.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def split_by_id(inv_log, ids, merged, col):\n", "    inv_log_ids = inv_log[inv_log[\"inventory_update_type_id\"].isin(ids)]\n", "    inv_log_ids = (\n", "        inv_log_ids.groupby([\"outlet_id\", \"item_id\"])\n", "        .agg({\"delta\": sum})\n", "        .reset_index()\n", "        .rename(columns={\"delta\": col})\n", "    )\n", "    merged = merged.merge(inv_log_ids, how=\"left\", on=[\"outlet_id\", \"item_id\"])\n", "    return merged"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = split_by_id(inv_log, grn_ids, inv_recon, \"grn\")\n", "inv_recon = split_by_id(inv_log, sale_ids, inv_recon, \"sale\")\n", "inv_recon = split_by_id(inv_log, sale_return_ids, inv_recon, \"sale_return\")\n", "inv_recon = split_by_id(inv_log, customer_sale_ids, inv_recon, \"customer_sale\")\n", "inv_recon = split_by_id(inv_log, customer_return_ids, inv_recon, \"customer_return\")\n", "inv_recon = split_by_id(inv_log, bad_stock_ids, inv_recon, \"bad_stock\")\n", "inv_recon = split_by_id(\n", "    inv_log, stock_update_positive_ids, inv_recon, \"stock_update_positive\"\n", ")\n", "inv_recon = split_by_id(\n", "    inv_log, stock_update_negative_ids, inv_recon, \"stock_update_negative\"\n", ")\n", "inv_recon = split_by_id(inv_log, liquidation_ids, inv_recon, \"liquidation\")\n", "inv_recon = split_by_id(inv_log, esto_ids, inv_recon, \"esto\")\n", "inv_recon = split_by_id(inv_log, repackaged_ids, inv_recon, \"repackaged\")\n", "inv_recon = split_by_id(inv_log, vendor_return_ids, inv_recon, \"vendor_return\")\n", "inv_recon.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select sender_outlet_id as outlet_id,\n", "       item_id,\n", "       sum(inwarded_quantity) as esto_received,\n", "       sum(billed_quantity - inwarded_quantity) as esto_in_transit\n", "from metrics.esto_details\n", "where sto_billed_at between '{start_date}'::timestamp - interval '5.5 hrs' + interval '1 day' and (current_date - 1)::timestamp - interval '5.5 hrs' + interval '1 day'\n", "and sto_state in ('Billed', 'Inward')\n", "and item_id in {*coins_item_ids,}\n", "group by 1,\n", "         2\n", "\"\"\"\n", "esto = pd.read_sql_query(query, con=redshift)\n", "esto.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.merge(esto, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"brand\",\n", "        \"manufacturer\",\n", "        \"opening_qty\",\n", "        \"grn\",\n", "        \"po_grn\",\n", "        \"sto_grn\",\n", "        \"sale\",\n", "        \"sale_return\",\n", "        \"customer_sale\",\n", "        \"customer_return\",\n", "        \"bad_stock\",\n", "        \"stock_update_positive\",\n", "        \"stock_update_negative\",\n", "        \"liquidation\",\n", "        \"esto\",\n", "        \"esto_received\",\n", "        \"esto_in_transit\",\n", "        \"repackaged\",\n", "        \"vendor_return\",\n", "        \"closing_qty\",\n", "    ]\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"calculated_closing_qty\"] = (\n", "    inv_recon[\"opening_qty\"]\n", "    + inv_recon[\"po_grn\"]\n", "    + inv_recon[\"sto_grn\"]\n", "    - inv_recon[\"sale\"]\n", "    - inv_recon[\"customer_sale\"]\n", "    + inv_recon[\"sale_return\"]\n", "    + inv_recon[\"customer_return\"]\n", "    - inv_recon[\"bad_stock\"]\n", "    + inv_recon[\"stock_update_positive\"]\n", "    - inv_recon[\"stock_update_negative\"]\n", "    - inv_recon[\"liquidation\"]\n", "    - inv_recon[\"esto\"]\n", "    + inv_recon[\"repackaged\"]\n", "    - inv_recon[\"vendor_return\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"diff\"] = abs(inv_recon[\"closing_qty\"] - inv_recon[\"calculated_closing_qty\"])\n", "inv_recon.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad = inv_recon[inv_recon[\"diff\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad.shape[0], inv_recon.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.sort_values(\n", "    by=[\"facility_id\", \"outlet_id\", \"item_id\"]\n", ").reset_index(drop=True)\n", "inv_recon.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1qGPazlmLLUQzIF1UPu4M_qB8u2wB5jRXOLuOlPlXMxo\"\n", "sheet_name = \"coins_recon\"\n", "\n", "pb.to_sheets(inv_recon, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}