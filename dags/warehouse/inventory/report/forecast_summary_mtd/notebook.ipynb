{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os as os\n", "import datetime as dt\n", "from calendar import monthrange\n", "from datetime import timedelta\n", "import pencilbox as pb\n", "\n", "retail = pb.get_connection(\"retail\")\n", "redshift_con = pb.get_connection(\"redshift\").connect()\n", "end_date = (pd.to_datetime(dt.datetime.now()) - pd.DateOffset(days=1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "s3_date = (pd.to_datetime(dt.datetime.now()) - pd.DateOffset(days=4)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "s7_date = (pd.to_datetime(dt.datetime.now()) - pd.DateOffset(days=7)).strftime(\n", "    \"%Y-%m-%d\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cal_cpd_accuracy(df):\n", "    if df[\"avg_f_cpd\"] == 0 and df[\"actual_cpd\"] == 0:\n", "        val = 1\n", "    elif df[\"actual_cpd\"] == 0 or df[\"avg_f_cpd\"] == 0:\n", "        val = 10\n", "    else:\n", "        val = df[\"avg_f_cpd\"] / df[\"actual_cpd\"]\n", "    return str(round(val * 100, 2)) + \"%\"\n", "\n", "\n", "def cal_qpc_accuracy(df):\n", "    if df[\"forecasted QPC\"] == 0 and df[\"actual QPC\"] == 0:\n", "        val = 1\n", "    elif df[\"actual QPC\"] == 0 or df[\"forecasted QPC\"] == 0:\n", "        val = 10\n", "    else:\n", "        val = df[\"forecasted QPC\"] / df[\"actual QPC\"]\n", "    return str(round(val * 100, 2)) + \"%\"\n", "\n", "\n", "def cal_excess(df):\n", "    if float(df[\"cpd_accuracy\"][:-1]) > 120 and df[\"avg_f_cpd\"] != 0:\n", "        val = abs(df[\"avg_f_cpd\"] - df[\"actual_cpd\"]) * df[\"avg_sp\"]\n", "    else:\n", "        val = 0\n", "    return val\n", "\n", "\n", "def cal_shortfall(df):\n", "    if float(df[\"cpd_accuracy\"][:-1]) < 80 and df[\"actual_cpd\"] != 0:\n", "        val = abs(df[\"avg_f_cpd\"] - df[\"actual_cpd\"]) * df[\"avg_sp\"]\n", "    else:\n", "        val = 0\n", "    return val\n", "\n", "\n", "def qpc_flag(df):\n", "\n", "    if float(df[\"qpc_accuracy\"][:-1]) < 80:\n", "        val = 0\n", "    elif float(df[\"qpc_accuracy\"][:-1]) > 120:\n", "        val = 2\n", "    else:\n", "        val = 1\n", "    return val\n", "\n", "\n", "def cpd_flag(df):\n", "\n", "    if float(df[\"cpd_accuracy\"][:-1]) < 80:\n", "        val = 0\n", "    elif float(df[\"cpd_accuracy\"][:-1]) > 120:\n", "        val = 2\n", "    else:\n", "        val = 1\n", "    return val\n", "\n", "\n", "def combine_tables(actual_cpd_tbl, f_cpd_tbl):\n", "\n", "    w_f_carts = pd.merge(\n", "        actual_cpd_tbl, f_cpd_tbl[[\"union\", \"avg_f_cpd\"]], on=[\"union\"], how=\"left\"\n", "    )\n", "    w_f_carts[\"avg_f_cpd\"] = w_f_carts[\"avg_f_cpd\"].fillna(0)\n", "\n", "    w_f_carts[\"actual QPC\"] = w_f_carts[\"actual_cpd\"] / w_f_carts[\"total_carts\"]\n", "    w_f_carts[\"forecasted QPC\"] = w_f_carts[\"avg_f_cpd\"] / w_f_carts[\"forecast_carts\"]\n", "\n", "    w_f_carts[\"cpd_accuracy\"] = w_f_carts.apply(cal_cpd_accuracy, axis=1)\n", "    w_f_carts[\"qpc_accuracy\"] = w_f_carts.apply(cal_qpc_accuracy, axis=1)\n", "\n", "    w_f_carts[\"excess\"] = w_f_carts.apply(cal_excess, axis=1)\n", "    w_f_carts[\"shortfall\"] = w_f_carts.apply(cal_shortfall, axis=1)\n", "\n", "    w_f_carts[\"qpc_acc_bw_80_120\"] = w_f_carts.apply(qpc_flag, axis=1)\n", "    w_f_carts[\"cpd_acc_bw_80_120\"] = w_f_carts.apply(cpd_flag, axis=1)\n", "    w_f_carts[\"sale\"] = w_f_carts.actual_cpd * w_f_carts.live_days * w_f_carts.avg_sp\n", "    w_f_carts = w_f_carts.loc[\n", "        :,\n", "        [\n", "            \"union\",\n", "            \"facility\",\n", "            \"zone\",\n", "            \"item_id\",\n", "            \"l0\",\n", "            \"is_gm\",\n", "            \"is_gb\",\n", "            \"bucket_x\",\n", "            \"bucket\",\n", "            \"avg_sp\",\n", "            \"live_days\",\n", "            \"sale\",\n", "            \"actual_cpd\",\n", "            \"avg_f_cpd\",\n", "            \"total_carts\",\n", "            \"forecast_carts\",\n", "            \"actual QPC\",\n", "            \"forecasted QPC\",\n", "            \"cpd_accuracy\",\n", "            \"qpc_accuracy\",\n", "            \"excess\",\n", "            \"shortfall\",\n", "            \"qpc_acc_bw_80_120\",\n", "            \"cpd_acc_bw_80_120\",\n", "        ],\n", "    ]\n", "    return w_f_carts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def zone_accuracy(zone, zone_qpc, zone_cpd):\n", "    zone_accu = pd.merge(\n", "        left=zone, right=zone_qpc, left_on=\"zone\", right_on=\"zone\", how=\"left\"\n", "    )\n", "    zone_accu[\"qpc_accu\"] = zone_accu.qpc_zone / zone_accu.s_zone\n", "    zone_accu = pd.merge(\n", "        left=zone_accu.loc[:, [\"zone\", \"qpc_accu\", \"s_zone\"]],\n", "        right=zone_cpd,\n", "        left_on=\"zone\",\n", "        right_on=\"zone\",\n", "        how=\"left\",\n", "    )\n", "    zone_accu[\"cpd_accu\"] = zone_accu.cpd_zone / zone_accu.s_zone\n", "    zone_accu1 = zone_accu.loc[:, [\"zone\", \"cpd_accu\", \"qpc_accu\"]]\n", "    return zone_accu1\n", "\n", "\n", "def facility_accuracy(facility, facility_qpc, facility_cpd):\n", "    facility_accu = pd.merge(\n", "        left=facility,\n", "        right=facility_qpc,\n", "        left_on=\"facility\",\n", "        right_on=\"facility\",\n", "        how=\"left\",\n", "    )\n", "    facility_accu[\"qpc_accu\"] = facility_accu.qpc_facility / facility_accu.s_facility\n", "    facility_accu = pd.merge(\n", "        left=facility_accu.loc[:, [\"facility\", \"qpc_accu\", \"s_facility\"]],\n", "        right=facility_cpd,\n", "        left_on=\"facility\",\n", "        right_on=\"facility\",\n", "        how=\"left\",\n", "    )\n", "    facility_accu[\"cpd_accu\"] = facility_accu.cpd_facility / facility_accu.s_facility\n", "    facility_accu1 = facility_accu.loc[:, [\"facility\", \"cpd_accu\", \"qpc_accu\"]]\n", "    return facility_accu1\n", "\n", "\n", "def join_zone(overall_zone, Bucket_x_zone):\n", "    join_zone = pd.merge(\n", "        left=overall_zone,\n", "        right=Bucket_x_zone,\n", "        left_on=\"zone\",\n", "        right_on=\"zone\",\n", "        how=\"left\",\n", "    )\n", "    return join_zone\n", "\n", "\n", "def join_facility(overall_facility, Bucket_x_facility):\n", "    join_facility = pd.merge(\n", "        left=overall_facility,\n", "        right=Bucket_x_facility,\n", "        left_on=\"facility\",\n", "        right_on=\"facility\",\n", "        how=\"left\",\n", "    )\n", "    return join_facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def summary(final_adjusted_df):\n", "    # Over all working\n", "    # Overall\n", "    zone = final_adjusted_df.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    zone = zone.rename(columns={\"item_id\": \"s_zone\"})  # zone\n", "    facility = (\n", "        final_adjusted_df.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    facility = facility.rename(columns={\"item_id\": \"s_facility\"})  # facility\n", "    # Buket_x\n", "    bucket_x = final_adjusted_df.loc[final_adjusted_df.bucket_x == \"Bucket X\"]\n", "    bucket_x_zone = bucket_x.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    bucket_x_zone = bucket_x_zone.rename(columns={\"item_id\": \"s_zone\"})  # zone_buket_x\n", "    bucket_x_facility = (\n", "        bucket_x.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_x_facility = bucket_x_facility.rename(\n", "        columns={\"item_id\": \"s_facility\"}\n", "    )  # facility_Buket_x\n", "    # Buket_A\n", "    bucket_a = final_adjusted_df.loc[final_adjusted_df.bucket == \"Bucket A\"]\n", "    bucket_a_zone = bucket_a.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    bucket_a_zone = bucket_a_zone.rename(columns={\"item_id\": \"s_zone\"})  # zone_buket_a\n", "    bucket_a_facility = (\n", "        bucket_a.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_a_facility = bucket_a_facility.rename(\n", "        columns={\"item_id\": \"s_facility\"}\n", "    )  # facility_Buket_a\n", "    # Buket_B\n", "    bucket_b = final_adjusted_df.loc[final_adjusted_df.bucket == \"Bucket B\"]\n", "    bucket_b_zone = bucket_b.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    bucket_b_zone = bucket_b_zone.rename(columns={\"item_id\": \"s_zone\"})  # zone_buket_b\n", "    bucket_b_facility = (\n", "        bucket_b.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_b_facility = bucket_b_facility.rename(\n", "        columns={\"item_id\": \"s_facility\"}\n", "    )  # facility_Buket_b\n", "    # GB\n", "    bucket_gb = final_adjusted_df.loc[final_adjusted_df.is_gb == 1]\n", "    bucket_gb_zone = bucket_gb.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    bucket_gb_zone = bucket_gb_zone.rename(\n", "        columns={\"item_id\": \"s_zone\"}\n", "    )  # zone_buket_gb\n", "    bucket_gb_facility = (\n", "        bucket_gb.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_gb_facility = bucket_gb_facility.rename(\n", "        columns={\"item_id\": \"s_facility\"}\n", "    )  # facility_Buket_gb\n", "    # QPC\n", "    # Overall\n", "    qpc = final_adjusted_df.loc[final_adjusted_df.qpc_acc_bw_80_120 == 1]\n", "    zone_qpc = qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    zone_qpc = zone_qpc.rename(columns={\"item_id\": \"qpc_zone\"})  # zone\n", "    facility_qpc = qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    facility_qpc = facility_qpc.rename(columns={\"item_id\": \"qpc_facility\"})  # facility\n", "    # Buket_x\n", "    bucket_x_qpc = qpc.loc[qpc.bucket_x == \"Bucket X\"]\n", "    bucket_x_zone_qpc = (\n", "        bucket_x_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_x_zone_qpc = bucket_x_zone_qpc.rename(\n", "        columns={\"item_id\": \"qpc_zone\"}\n", "    )  # zone_buket_x\n", "    bucket_x_facility_qpc = (\n", "        bucket_x_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_x_facility_qpc = bucket_x_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility_Buket_x\n", "    # Buket_A\n", "    bucket_a_qpc = qpc.loc[qpc.bucket == \"Bucket A\"]\n", "    bucket_a_zone_qpc = (\n", "        bucket_a_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_a_zone_qpc = bucket_a_zone_qpc.rename(\n", "        columns={\"item_id\": \"qpc_zone\"}\n", "    )  # zone_buket_a\n", "    bucket_a_facility_qpc = (\n", "        bucket_a_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_a_facility_qpc = bucket_a_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility_Buket_a\n", "    # Buket_B\n", "    bucket_b_qpc = qpc.loc[qpc.bucket == \"Bucket B\"]\n", "    bucket_b_zone_qpc = (\n", "        bucket_b_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_b_zone_qpc = bucket_b_zone_qpc.rename(\n", "        columns={\"item_id\": \"qpc_zone\"}\n", "    )  # zone_buket_b\n", "    bucket_b_facility_qpc = (\n", "        bucket_b_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_b_facility_qpc = bucket_b_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility_Buket_b\n", "    # GB\n", "    bucket_gb_qpc = qpc.loc[qpc.is_gb == 1]\n", "    bucket_gb_zone_qpc = (\n", "        bucket_gb_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_gb_zone_qpc = bucket_gb_zone_qpc.rename(\n", "        columns={\"item_id\": \"qpc_zone\"}\n", "    )  # zone_buket_gb\n", "    bucket_gb_facility_qpc = (\n", "        bucket_gb_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_gb_facility_qpc = bucket_gb_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility_Buket_gb\n", "    # GMV Loss\n", "    GMV_qpc = final_adjusted_df.loc[final_adjusted_df.qpc_acc_bw_80_120 == 0]\n", "    GMV_zone_qpc = GMV_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    GMV_zone_qpc = GMV_zone_qpc.rename(columns={\"item_id\": \"qpc_zone\"})  # zone GMV\n", "    GMV_facility_qpc = (\n", "        GMV_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    GMV_facility_qpc = GMV_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility GMV\n", "    # Excess Inventory\n", "    Ex_qpc = final_adjusted_df.loc[final_adjusted_df.qpc_acc_bw_80_120 == 2]\n", "    Ex_zone_qpc = Ex_qpc.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    Ex_zone_qpc = Ex_zone_qpc.rename(columns={\"item_id\": \"qpc_zone\"})  # zone Excess\n", "    Ex_facility_qpc = (\n", "        Ex_qpc.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    Ex_facility_qpc = Ex_facility_qpc.rename(\n", "        columns={\"item_id\": \"qpc_facility\"}\n", "    )  # facility Excess\n", "    # CPD\n", "    # Overall\n", "    cpd = final_adjusted_df.loc[final_adjusted_df.cpd_acc_bw_80_120 == 1]\n", "    zone_cpd = cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    zone_cpd = zone_cpd.rename(columns={\"item_id\": \"cpd_zone\"})  # zone\n", "    facility_cpd = cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    facility_cpd = facility_cpd.rename(columns={\"item_id\": \"cpd_facility\"})  # facility\n", "    # Buket_x\n", "    bucket_x_cpd = cpd.loc[cpd.bucket_x == \"Bucket X\"]\n", "    bucket_x_zone_cpd = (\n", "        bucket_x_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_x_zone_cpd = bucket_x_zone_cpd.rename(\n", "        columns={\"item_id\": \"cpd_zone\"}\n", "    )  # zone_buket_x\n", "    bucket_x_facility_cpd = (\n", "        bucket_x_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_x_facility_cpd = bucket_x_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility_Buket_x\n", "    # Buket_A\n", "    bucket_a_cpd = cpd.loc[cpd.bucket == \"Bucket A\"]\n", "    bucket_a_zone_cpd = (\n", "        bucket_a_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_a_zone_cpd = bucket_a_zone_cpd.rename(\n", "        columns={\"item_id\": \"cpd_zone\"}\n", "    )  # zone_buket_a\n", "    bucket_a_facility_cpd = (\n", "        bucket_a_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_a_facility_cpd = bucket_a_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility_Buket_a\n", "    # Buket_B\n", "    bucket_b_cpd = cpd.loc[cpd.bucket == \"Bucket B\"]\n", "    bucket_b_zone_cpd = (\n", "        bucket_b_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_b_zone_cpd = bucket_b_zone_cpd.rename(\n", "        columns={\"item_id\": \"cpd_zone\"}\n", "    )  # zone_buket_b\n", "    bucket_b_facility_cpd = (\n", "        bucket_b_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_b_facility_cpd = bucket_b_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility_Buket_b\n", "    # GB\n", "    bucket_gb_cpd = cpd.loc[cpd.is_gb == 1]\n", "    bucket_gb_zone_cpd = (\n", "        bucket_gb_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_gb_zone_cpd = bucket_gb_zone_cpd.rename(\n", "        columns={\"item_id\": \"cpd_zone\"}\n", "    )  # zone_buket_gb\n", "    bucket_gb_facility_cpd = (\n", "        bucket_gb_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    bucket_gb_facility_cpd = bucket_gb_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility_Buket_gb\n", "    # GMV Loss\n", "    GMV_cpd = final_adjusted_df.loc[final_adjusted_df.cpd_acc_bw_80_120 == 0]\n", "    GMV_zone_cpd = GMV_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    GMV_zone_cpd = GMV_zone_cpd.rename(columns={\"item_id\": \"cpd_zone\"})  # zone GMV\n", "    GMV_facility_cpd = (\n", "        GMV_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    GMV_facility_cpd = GMV_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility GMV\n", "    # Excess Inventory\n", "    Ex_cpd = final_adjusted_df.loc[final_adjusted_df.cpd_acc_bw_80_120 == 2]\n", "    Ex_zone_cpd = Ex_cpd.groupby([\"zone\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    Ex_zone_cpd = Ex_zone_cpd.rename(columns={\"item_id\": \"cpd_zone\"})  # zone Excess\n", "    Ex_facility_cpd = (\n", "        Ex_cpd.groupby([\"facility\"])[[\"item_id\"]].agg(\"count\").reset_index()\n", "    )\n", "    Ex_facility_cpd = Ex_facility_cpd.rename(\n", "        columns={\"item_id\": \"cpd_facility\"}\n", "    )  # facility Excess\n", "    overall_zone = zone_accuracy(zone, zone_qpc, zone_cpd)\n", "    overall_zone = overall_zone.rename(\n", "        columns={\"cpd_accu\": \"Over_all_cpd\", \"qpc_accu\": \"Over_all_qpc\"}\n", "    )\n", "    Bucket_x_zone = zone_accuracy(bucket_x_zone, bucket_x_zone_qpc, bucket_x_zone_cpd)\n", "    Bucket_x_zone = Bucket_x_zone.rename(\n", "        columns={\"cpd_accu\": \"bucket_x_cpd\", \"qpc_accu\": \"bucket_x_qpc\"}\n", "    )\n", "    Bucket_a_zone = zone_accuracy(bucket_a_zone, bucket_a_zone_qpc, bucket_a_zone_cpd)\n", "    Bucket_a_zone = Bucket_a_zone.rename(\n", "        columns={\"cpd_accu\": \"bucket_a_cpd\", \"qpc_accu\": \"bucket_a_qpc\"}\n", "    )\n", "    Bucket_b_zone = zone_accuracy(bucket_b_zone, bucket_b_zone_qpc, bucket_b_zone_cpd)\n", "    Bucket_b_zone = Bucket_b_zone.rename(\n", "        columns={\"cpd_accu\": \"bucket_b_cpd\", \"qpc_accu\": \"bucket_b_qpc\"}\n", "    )\n", "    gb_zone = zone_accuracy(bucket_gb_zone, bucket_gb_zone_qpc, bucket_gb_zone_cpd)\n", "    gb_zone = gb_zone.rename(columns={\"cpd_accu\": \"GB_cpd\", \"qpc_accu\": \"GB_qpc\"})\n", "    GMV_zone = zone_accuracy(zone, GMV_zone_qpc, GMV_zone_cpd)\n", "    GMV_zone = GMV_zone.rename(columns={\"cpd_accu\": \"GMV_cpd\", \"qpc_accu\": \"GMV_qpc\"})\n", "    Excess_zone = zone_accuracy(zone, Ex_zone_qpc, Ex_zone_cpd)\n", "    Excess_zone = Excess_zone.rename(\n", "        columns={\"cpd_accu\": \"Excess_cpd\", \"qpc_accu\": \"Excess_qpc\"}\n", "    )\n", "    zone_wise = join_zone(\n", "        join_zone(\n", "            join_zone(\n", "                join_zone(\n", "                    join_zone(join_zone(overall_zone, Bucket_x_zone), Bucket_a_zone),\n", "                    Bucket_b_zone,\n", "                ),\n", "                gb_zone,\n", "            ),\n", "            GMV_zone,\n", "        ),\n", "        Excess_zone,\n", "    )\n", "    pan_zone = pd.DataFrame(\n", "        {\n", "            \"zone\": [\"Pan_India\", \"-\"],\n", "            \"Over_all_cpd\": [\n", "                len(final_adjusted_df[final_adjusted_df[\"cpd_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "            \"Over_all_qpc\": [\n", "                len(final_adjusted_df[final_adjusted_df[\"qpc_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "            \"bucket_x_cpd\": [\n", "                len(bucket_x_cpd[bucket_x_cpd[\"cpd_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket_x == \"Bucket X\"]),\n", "                \"-\",\n", "            ],\n", "            \"bucket_x_qpc\": [\n", "                len(bucket_x_qpc[bucket_x_qpc[\"qpc_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket_x == \"Bucket X\"]),\n", "                \"-\",\n", "            ],\n", "            \"bucket_a_cpd\": [\n", "                len(bucket_a_cpd[bucket_a_cpd[\"cpd_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket == \"Bucket A\"]),\n", "                \"-\",\n", "            ],\n", "            \"bucket_a_qpc\": [\n", "                len(bucket_a_cpd[bucket_a_cpd[\"qpc_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket == \"Bucket A\"]),\n", "                \"-\",\n", "            ],\n", "            \"bucket_b_cpd\": [\n", "                len(bucket_b_cpd[bucket_b_cpd[\"cpd_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket == \"Bucket B\"]),\n", "                \"-\",\n", "            ],\n", "            \"bucket_b_qpc\": [\n", "                len(bucket_b_cpd[bucket_b_cpd[\"qpc_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.bucket == \"Bucket B\"]),\n", "                \"-\",\n", "            ],\n", "            \"GB_cpd\": [\n", "                len(bucket_gb_cpd[bucket_gb_cpd[\"cpd_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.is_gb == 1]),\n", "                \"-\",\n", "            ],\n", "            \"GB_qpc\": [\n", "                len(bucket_gb_cpd[bucket_gb_cpd[\"qpc_acc_bw_80_120\"] == 1])\n", "                / len(final_adjusted_df[final_adjusted_df.is_gb == 1]),\n", "                \"-\",\n", "            ],\n", "            \"GMV_cpd\": [\n", "                len(GMV_cpd[GMV_cpd[\"cpd_acc_bw_80_120\"] == 0])\n", "                / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "            \"GMV_qpc\": [\n", "                len(GMV_cpd[GMV_cpd[\"qpc_acc_bw_80_120\"] == 0])\n", "                / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "            \"Excess_cpd\": [\n", "                len(Ex_cpd[Ex_cpd[\"cpd_acc_bw_80_120\"] == 2]) / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "            \"Excess_qpc\": [\n", "                len(Ex_cpd[Ex_cpd[\"qpc_acc_bw_80_120\"] == 2]) / len(final_adjusted_df),\n", "                \"-\",\n", "            ],\n", "        }\n", "    )\n", "    zone_wise = zone_wise.append(pan_zone)\n", "    zone_wise = zone_wise.rename(columns={\"zone\": \"facility\"})\n", "    overall_facility = facility_accuracy(facility, facility_qpc, facility_cpd)\n", "    overall_facility = overall_facility.rename(\n", "        columns={\"cpd_accu\": \"Over_all_cpd\", \"qpc_accu\": \"Over_all_qpc\"}\n", "    )\n", "    Bucket_x_facility = facility_accuracy(\n", "        bucket_x_facility, bucket_x_facility_qpc, bucket_x_facility_cpd\n", "    )\n", "    Bucket_x_facility = Bucket_x_facility.rename(\n", "        columns={\"cpd_accu\": \"bucket_x_cpd\", \"qpc_accu\": \"bucket_x_qpc\"}\n", "    )\n", "    Bucket_a_facility = facility_accuracy(\n", "        bucket_a_facility, bucket_a_facility_qpc, bucket_a_facility_cpd\n", "    )\n", "    Bucket_a_facility = Bucket_a_facility.rename(\n", "        columns={\"cpd_accu\": \"bucket_a_cpd\", \"qpc_accu\": \"bucket_a_qpc\"}\n", "    )\n", "    Bucket_b_facility = facility_accuracy(\n", "        bucket_b_facility, bucket_b_facility_qpc, bucket_b_facility_cpd\n", "    )\n", "    Bucket_b_facility = Bucket_b_facility.rename(\n", "        columns={\"cpd_accu\": \"bucket_b_cpd\", \"qpc_accu\": \"bucket_b_qpc\"}\n", "    )\n", "    gb_facility = facility_accuracy(\n", "        bucket_gb_facility, bucket_gb_facility_qpc, bucket_gb_facility_cpd\n", "    )\n", "    gb_facility = gb_facility.rename(\n", "        columns={\"cpd_accu\": \"GB_cpd\", \"qpc_accu\": \"GB_qpc\"}\n", "    )\n", "    GMV_facility = facility_accuracy(facility, GMV_facility_qpc, GMV_facility_cpd)\n", "    GMV_facility = GMV_facility.rename(\n", "        columns={\"cpd_accu\": \"GMV_cpd\", \"qpc_accu\": \"GMV_qpc\"}\n", "    )\n", "    Excess_facility = facility_accuracy(facility, Ex_facility_qpc, Ex_facility_cpd)\n", "    Excess_facility = Excess_facility.rename(\n", "        columns={\"cpd_accu\": \"Excess_cpd\", \"qpc_accu\": \"Excess_qpc\"}\n", "    )\n", "    facility_wise = join_facility(\n", "        join_facility(\n", "            join_facility(\n", "                join_facility(\n", "                    join_facility(\n", "                        join_facility(overall_facility, Bucket_x_facility),\n", "                        Bucket_a_facility,\n", "                    ),\n", "                    Bucket_b_facility,\n", "                ),\n", "                gb_facility,\n", "            ),\n", "            GMV_facility,\n", "        ),\n", "        Excess_facility,\n", "    )\n", "    facility_wise = zone_wise.append(facility_wise)\n", "    return facility_wise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def asp_work(live_days_actual_cpd):\n", "    Q2 = \"\"\"\n", "    SELECT \n", "         pp.item_id as item_id,\n", "         ipd.selling_price as sp,\n", "         outlet.name \"Outlet\",\n", "         outlet.facility_id as facility_id,\n", "         mm.facility,\n", "         count(distinct od.order_id) as Order_Count,\n", "         sum(ipd.quantity) as Sales_Quantity\n", "    FROM consumer.pos_pos_invoice p\n", "    JOIN ims_order_details od ON od.order_id = p.grofers_order_id\n", "    JOIN pos_console_outlet outlet ON outlet.id = od.outlet and outlet.type_id = 2\n", "    JOIN pos_pos_invoice_product_details ipd ON p.id = ipd.invoice_id\n", "    JOIN pos_product_product pp ON pp.variant_id = ipd.variant_id\n", "    left join (select* from(select city_id,facility_id, city,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "            from city_facility_split) where rnk=1) mm on mm.facility_id=outlet.facility_id\n", "    WHERE date(p.pos_timestamp + interval '5.5 hours') BETWEEN date(Current_Date-30) AND date(Current_Date-1)\n", "    AND p.invoice_type_id = '1' and mm.facility is not null\n", "    GROUP BY 1,2,3,4,5\n", "    \"\"\"\n", "    sales_asp = pd.read_sql(Q2, con=redshift_con)\n", "    ##\n", "    Qlp = \"\"\"\n", "    WITH \n", "\n", "    item_city_state as \n", "      (\n", "        select b.item_id, ro.facility_id, ast.name as state\n", "            from product_master_assortment b\n", "            left join product_master_assortment_substate ast on ast.id = b.master_assortment_substate_id\n", "            inner join\n", "                  (select item_id, city_id, max(updated_at) as max from product_master_assortment group by 1,2) a\n", "            on a.item_id = b.item_id and a.city_id = b.city_id and a.max = b.updated_at\n", "            left join view_pos_console_outlet ro on ro.tax_location_id = a.city_id\n", "           where b.master_assortment_substate_id in (1,5)  \n", "            group by 1,2,3\n", "    ) ,\n", "\n", "     PRODUCT_Category AS\n", "      (SELECT P.ID AS PID,\n", "              P.NAME AS PRODUCT,\n", "              P.UNIT AS Unit,\n", "              C2.NAME AS L2,\n", "              (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "              C.NAME AS L0,\n", "              P.BRAND AS brand,\n", "              P.MANUFACTURER AS manf,\n", "              pt.name as product_type\n", "       FROM GR_PRODUCT P\n", "       INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "       INNER JOIN GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "       AND PCM.IS_PRIMARY=TRUE\n", "       INNER JOIN GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "       INNER JOIN GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "       inner join gr_product_type pt on p.type_id=pt.id\n", "\n", "       ),\n", "\n", "       final_lo as\n", "       (\n", "\n", "    SELECT item_id,\n", "           product_id,\n", "           (cat.product || ' ' || cat.unit) AS name,\n", "           cat.L0,\n", "           cat.l1,\n", "           cat.l2 ,\n", "           cat.brand,\n", "           cat.manf,\n", "           cat.product_type\n", "    FROM rpc_item_product_mapping rpc\n", "    INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "\n", "    ),\n", "\n", "\n", "    lp_1 as\n", "    (\n", "    select \n", "    a.item_id,\n", "    outlet_id,\n", "    b.warehouse_id,\n", "    facility_id,\n", "    landing_price,\n", "    row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "    row_number() over (partition by a.item_id order by dt_ist desc) as row_Rank1\n", "    from weighted_landing_price a\n", "    left join (select * from warehouse_outlet_mapping where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "    left join (select * from pos_console_outlet where active = true) c on b.warehouse_id = c.id\n", "    where landing_price is not null\n", "    ),\n", "\n", "    final as\n", "    (\n", "    select a.item_id , a.facility_id ,l0, coalesce(e.landing_price, ee.landing_price) as landing_price\n", "    from item_city_state a\n", "    left join (select * from lp_1 where row_rank = 1) e on e.item_id = a.item_id and a.facility_id = e.facility_id\n", "    left join (select * from lp_1 where row_rank1 = 1) ee on ee.item_id = a.item_id\n", "    left join final_lo b on b.item_id = a.item_id\n", "    )\n", "\n", "    select a.*,m.facility,\n", "    case \n", "    when l0= 'Grocery & Staples' then (landing_price * 0.065) + landing_price\n", "    when l0= 'Beverages' then (landing_price * 0.08) + landing_price\n", "     when l0= 'Household Needs' then (landing_price * 0.1) + landing_price\n", "     when l0=  'Personal Care' then (landing_price * 0.1) + landing_price\n", "     when l0= 'Baby Care' then (landing_price * 0.065) + landing_price\n", "     when l0= 'Noodles, Sauces & Instant Food' then (landing_price * 0.12) + landing_price\n", "     when l0= 'Biscuits, Namkeen & Chocolates' then (landing_price * 0.13) + landing_price\n", "     when l0=  'Breakfast & Dairy' then (landing_price * 0.12) + landing_price\n", "     when l0=  'Furnishing & Home Needs' then (landing_price * 0.19) + landing_price\n", "     when l0=  'Home & Kitchen' then (landing_price * 0.2) + landing_price\n", "     when l0= 'Fresh & Frozen Food' then (landing_price * 0.19) + landing_price\n", "     when l0= 'Pet Care' then (landing_price * 0.14) + landing_price\n", "     when l0= 'Household Items' then (landing_price * 0.1) + landing_price\n", "     when l0= 'Biscuits, Snacks & Chocolates' then (landing_price * 0.13) + landing_price\n", "     end as for_asp\n", "    from final a \n", "    left join (select* from(select city_id,facility_id, city,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "        from city_facility_split)\n", "        where rnk=1) m on m.facility_id=a.facility_id\n", "    where facility is not null\"\"\"\n", "    asp_lp = pd.read_sql(Qlp, con=redshift_con)\n", "    #\n", "    Q1 = \"\"\"with data as (select item_id,name,variant_mrp,\n", "        row_number()over(partition by item_id order by variant_mrp ) as rnk,\n", "        max(updated_at)from rpc_product_product \n", "        where  active = 1  group by 1,2,3)\n", "        select item_id,name,Variant_mrp from data\n", "        where rnk =1\n", "        \"\"\"\n", "    iteem_l0 = pd.read_sql(Q1, con=redshift_con)\n", "    cpd = live_days_actual_cpd[{\"item_id\", \"facility\"}]\n", "    cpd[\"item_id\"] = cpd[\"item_id\"].astype(str)\n", "    cpd[\"Union\"] = cpd[\"item_id\"].str.cat(cpd[\"facility\"].copy())\n", "    Mapped_outlet = sales_asp.loc[:, [\"item_id\", \"sp\", \"facility\", \"sales_quantity\"]]\n", "    new = Mapped_outlet[\"facility\"].copy()\n", "    Mapped_outlet[\"item_id\"] = Mapped_outlet[\"item_id\"].astype(str)\n", "    Mapped_outlet[\"Union\"] = Mapped_outlet[\"item_id\"].str.cat(new)  # display\n", "    Mapped_outlet[\"GMV\"] = Mapped_outlet[\"sales_quantity\"] * Mapped_outlet[\"sp\"]\n", "    temp = Mapped_outlet.groupby([\"item_id\"])\n", "    res = temp[[\"GMV\", \"sales_quantity\"]].agg(\"sum\").reset_index()\n", "    res = res.rename(columns={\"GMV\": \"SumOfgmv\", \"sales_quantity\": \"SumOfQty\"})\n", "    res[\"asp_item\"] = res[\"SumOfgmv\"] / res[\"SumOfQty\"]  #\n", "    temp1 = Mapped_outlet.groupby([\"Union\"])\n", "    res1 = temp1[[\"GMV\", \"sales_quantity\"]].agg(\"sum\").reset_index()\n", "    res1 = res1.rename(columns={\"GMV\": \"SumOfgmv\", \"sales_quantity\": \"SumOfQty\"})\n", "    res1[\"asp_Union\"] = res1[\"SumOfgmv\"] / res1[\"SumOfQty\"]\n", "    asp_cperd = cpd.loc[:, [\"Union\", \"item_id\"]]\n", "    asp_cperd = asp_cperd.rename(columns={\"item_id\": \"Item ID\"})\n", "    asp_cperd1 = pd.merge(\n", "        left=asp_cperd, right=res1, left_on=\"Union\", right_on=\"Union\", how=\"left\"\n", "    )\n", "    asp_cperd1[\"Item ID\"] = asp_cperd1[\"Item ID\"].astype(str)\n", "    res[\"item_id\"] = res[\"item_id\"].astype(str)\n", "    asp_cperd3 = pd.merge(\n", "        left=asp_cperd1.loc[:, [\"Union\", \"Item ID\", \"asp_Union\"]],\n", "        right=res.loc[:, [\"item_id\", \"asp_item\"]],\n", "        left_on=\"Item ID\",\n", "        right_on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    asp_cperd4 = asp_cperd3.iloc[:, [0, 1, 2, 4]]\n", "    asp_cperd4[\"final_asp1\"] = asp_cperd4[\"asp_Union\"].fillna(asp_cperd4[\"asp_item\"])\n", "    asp_lp[\"item_id\"] = asp_lp[\"item_id\"].astype(str)\n", "    asp_lp[\"Union\"] = asp_lp[\"item_id\"].str.cat(asp_lp[\"facility\"].copy())\n", "    asp_cperd5 = pd.merge(\n", "        left=asp_cperd4,\n", "        right=asp_lp.loc[:, [\"Union\", \"for_asp\"]],\n", "        left_on=\"Union\",\n", "        right_on=\"Union\",\n", "        how=\"left\",\n", "    )\n", "    ASP_LP1 = asp_lp.rename(columns={\"item_id\": \"Item ID\", \"for_asp\": \"lp_asp_item\"})\n", "    ASP_LP2 = ASP_LP1.loc[:, [\"Item ID\", \"lp_asp_item\"]]\n", "    ASP_LP2.drop_duplicates(subset=[\"Item ID\"], keep=\"first\", inplace=True)\n", "    asp_cperd6 = pd.merge(\n", "        left=asp_cperd5,\n", "        right=ASP_LP2,\n", "        left_on=\"Item ID\",\n", "        right_on=\"Item ID\",\n", "        how=\"left\",\n", "    )\n", "    asp_cperd6[\"final_asp1\"].fillna(asp_cperd6[\"for_asp\"])\n", "    asp_cperd6[\"final_asp1\"].fillna(asp_cperd6[\"lp_asp_item\"])\n", "    asp_cperd6[\"final_asp1\"] = np.where(\n", "        asp_cperd6[\"final_asp1\"] == 0, np.nan, asp_cperd6[\"final_asp1\"]\n", "    )\n", "    iteem_l0.drop_duplicates(subset=[\"item_id\"], keep=\"first\", inplace=True)\n", "    item_1code = iteem_l0.loc[:, [\"item_id\", \"Variant_mrp\"]]\n", "    item_1code[\"item_id\"] = item_1code[\"item_id\"].astype(str)\n", "    asp_cperd7 = pd.merge(\n", "        left=asp_cperd6,\n", "        right=item_1code,\n", "        left_on=\"Item ID\",\n", "        right_on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    asp_cperd7[\"final_asp\"] = asp_cperd7[\"final_asp1\"].fillna(\n", "        asp_cperd7[\"Variant_mrp\"] * 0.7\n", "    )\n", "    asp_cperd7[\"final_asp\"] = np.where(\n", "        asp_cperd7[\"final_asp\"] == 0, np.nan, asp_cperd7[\"final_asp\"]\n", "    )\n", "    asp_cperd7[\"final_asp\"].fillna(80, inplace=True)\n", "    asp_cperd_final = asp_cperd7.loc[:, [\"Union\", \"final_asp\"]]\n", "    ASP = pd.merge(\n", "        left=asp_cperd_final, right=cpd, left_on=\"Union\", right_on=\"Union\", how=\"left\"\n", "    )\n", "    ASP = ASP.drop(columns={\"Union\"})\n", "    live_days_actual_cpd1 = pd.merge(\n", "        left=live_days_actual_cpd,\n", "        right=ASP,\n", "        left_on=[\"item_id\", \"facility\"],\n", "        right_on=[\"item_id\", \"facility\"],\n", "        how=\"left\",\n", "    )\n", "    return live_days_actual_cpd1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def actual_sales(start_date, end_date, faci_faci1):\n", "    sql_actual_cpd = \"\"\"\n", "    with ast as\n", "    (select * from\n", "    (select distinct date(install_ts) as date_ from view_gr_order\n", "    where date(install_ts) between '{start_date}' and '{end_date}'\n", "    )a\n", "    cross join\n", "    (select distinct product_id from order_item_data\n", "    where date(install_ts) between '{start_date}' and '{end_date}'\n", "    and merchant_store_type = 'grofers'\n", "    and merchant_name not ilike '%%grocery%%mart%%'\n", "    and (merchant_business_type is not null or merchant_business_type <> 'b2b')\n", "    and merchant_name not ilike '%%kids%%' and merchant_name not ilike '%%return%%' \n", "    and merchant_name not ilike '%%donation%%'\n", "    )b\n", "    cross join\n", "    (select distinct merchant_id from view_order_data\n", "    where date(install_ts) between '{start_date}' and '{end_date}'\n", "    and merchant_store_type = 'grofers'\n", "    and merchant_name not ilike '%%grocery%%mart%%'\n", "    and (merchant_business_type is not null or merchant_business_type <> 'b2b')\n", "    and merchant_name not ilike '%%kids%%' and merchant_name not ilike '%%return%%'\n", "    and merchant_name not ilike '%%donation%%'\n", "    )c\n", "    ),\n", "    PRODUCT_Category AS\n", "      (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM GR_PRODUCT P\n", "   INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ) ,\n", "    sale as\n", "    (select\n", "    date(convert_timezone('Asia/Kolkata',oi.install_ts)) as date_,\n", "    oi.merchant_id,\n", "    oi.product_id,\n", "    sum(f.lq_margin) as lq_margin,\n", "    sum(oi.quantity) as qty_sold\n", "    \n", "    from order_item_data oi \n", "    left join (select * from all_margins where lq_margin >= 0.01) f on oi.city = f.city AND oi.product_id = f.pid and \n", "    date(oi.install_ts + interval '5.5 Hours') between date(f.start_date) AND date(f.end_date)\n", "    where date(convert_timezone('Asia/Kolkata',oi.install_ts)) between '{start_date}' and '{end_date}'\n", "    and oi.merchant_store_type = 'grofers'\n", "    and oi.merchant_name not ilike '%%grocery%%mart%%'\n", "    and (oi.merchant_business_type is not null or oi.merchant_business_type <> 'b2b')\n", "    and type_id not in (11780,11778,11791,11927,11943,11934 )\n", "    and price > 1\n", "    group by 1,2,3\n", "    ),\n", "\n", "    live as   \n", "    (select distinct\n", "    date(gp.install_ts) as date_,\n", "    gp.merchant_id,\n", "    gp.product_id\n", "\n", "    from gr_product_price_history gp\n", "\n", "    where date(gp.install_ts) between '{start_date}' and '{end_date}'\n", "  \n", "    ),\n", "\n", "    data as\n", "    (select distinct\n", "    a.date_,\n", "    a.merchant_id,\n", "    a.product_id,\n", "    (case when (l.date_ is null and s.date_ is null)\n", "         then 'not live'\n", "         else 'live'\n", "    end) as live_status,\n", "    COALESCE(s.qty_sold,0) as qty_sold,\n", "    case when (lq_margin is null or lq_margin=0) then 0 else 1 end as lq_margin\n", "\n", "    from ast a\n", "    left join live l on date(a.date_) = date(l.date_)\n", "                and a.merchant_id = l.merchant_id\n", "                and a.product_id = l.product_id\n", "    left join sale s on date(a.date_) = date(s.date_)\n", "                and a.merchant_id = s.merchant_id\n", "                and a.product_id = s.product_id\n", "    ),\n", "    logs as\n", "    (select \n", "    product_id,\n", "    item_id,\n", "    updated_on,\n", "    multiplier,\n", "    max(updated_on) over(partition by product_id)  as last_update\n", "\n", "    from it_pd_log\n", "    group by 1,2,3,4\n", "    ),\n", "\n", "    last_up as\n", "    (select distinct\n", "    product_id,\n", "    item_id,\n", "    max(multiplier) as multiplier\n", "\n", "    from logs\n", "    where updated_on = last_update\n", "    group by 1,2\n", "    ),\n", "    d1 as (select d.date_,d.merchant_id,d.product_id,lu.item_id,d.live_status,d.qty_sold,multiplier,lq_margin\n", "    from data d\n", "    left join last_up lu on lu.product_id=d.product_id\n", "    where lu.item_id is not null\n", "    ),\n", "\n", "    final as\n", "    (select \n", "    date_,\n", "    merchant_id,\n", "    product_id,\n", "    item_id,\n", "    qty_sold,\n", "    multiplier,\n", "    lq_margin,\n", "    dense_rank() over (partition by merchant_id, item_id order by date_ desc) as live_days\n", "\n", "    from d1 \n", "\n", "    where live_status = 'live'\n", "    ),\n", "\n", "    fin1 as\n", "    (select *\n", "    from final \n", "    where date(date_) between  '{start_date}' AND '{end_date}'\n", "    ),\n", "     \n", "    yo as\n", "    (select date_,\n", "    merchant_id,\n", "    item_id,\n", "    lq_margin,\n", "    max(live_days) AS live_days,\n", "    sum(qty_sold*multiplier) AS qty_sold\n", "    \n", "    from fin1\n", "    where live_days <= datediff(day, '{start_date}', '{end_date}')+1\n", "    group by 1,2,3,4\n", "    ),\n", "    mapping as ( with data as (\n", "                    select * from ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                    from Merchant_forecast_carts)\n", "                    where rnk =1 and date(f_date) between  '{start_date}' AND '{end_date}')\n", "                    select date(f_date) as date_, facility,facility_id,merchant_id\n", "                    from data\n", "                    group by 1,2,3,4),\n", "\n", "    bucket_x_mapping as (\n", "    select distinct item_id, facility_id, tag_value \n", "        from( select item_id,o.facility_id,tag_value,\n", "        row_number() over (partition by item_id, outlet_id order by a.created_at desc) as row_rank\n", "        from rpc_item_outlet_tag_mapping_log a\n", "        left join retail_console_outlet o on a.outlet_id = o.id\n", "        where \n", "        tag_value in ('Y', 'N')\n", "        and a.active = true\n", "        and date(a.created_at) < ('{start_date}'))\n", "    where row_rank = 1\n", "    ),\n", "\n", "    bucket_a_b_mapping as (\n", "    select distinct item_id, facility_id, tag_value from(\n", "        select item_id, facility_id, tag_value, row_number() over (partition by item_id,facility_id order by created_at desc) as row_rank\n", "        from( select item_id,o.facility_id,tag_value,a.created_at\n", "            from rpc_item_outlet_tag_mapping_log a\n", "            left join retail_console_outlet o on a.outlet_id = o.id\n", "            where tag_value in ('A', 'B')\n", "            and a.active = true\n", "            and date(a.created_at) < ('{start_date}'))\n", "        )\n", "        where row_rank = 1\n", "        ),\n", "\n", "    actual1_cart as( with it_pd as\n", "    (select distinct item_id, product_id,multiplier, updated_on, max(updated_on) over (partition by product_id) as last_updated from it_pd_log ),\n", "\n", "    mapping as (select distinct item_id, product_id,coalesce(multiplier,1) as multiplier from it_pd\n", "        where last_updated = updated_on ),\n", "\n", "    tbl_treatment as\n", "        (select a.*,date(a.install_ts + interval '5.5 Hours') as checkout_date,om.city_name,b.product_id,c.item_id,om.external_id as virtual_merchant_id\n", "        from consumer.oms_order a\n", "        left join consumer.view_oms_merchant om on a.merchant_id = om.id\n", "        left join (select * from oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') between current_date - 220 AND current_date) b on a.id = b.order_id\n", "        left join mapping c on b.product_id = c.product_id\n", "        left join rpc_item_details d on c.item_id = d.item_id\n", "        where (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "            and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "            and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)),\n", "\n", "    merchant_and_store_join as (\n", "        select a.*,b.real_merchant_id,c.outlet_id,d.warehouse_id as hot_outlet_id,e.facility_id,f.name as facility_name from tbl_treatment a\n", "        left join (select * from gr_virtual_to_real_merchant_mapping where priority_order = 1 AND enabled_flag = true) b on a.virtual_merchant_id = b.virtual_merchant_id\n", "        left join (select * from rt_console_outlet_cms_store where active = true) c on b.real_merchant_id = c.cms_store\n", "        left join warehouse_outlet_mapping d on c.outlet_id = d.cloud_store_id\n", "        left join pos_console_outlet e on d.warehouse_id = e.id\n", "        left join crates_facility f on e.facility_id = f.id)\n", "\n", "    select \n", "    checkout_date as date_,\n", "    virtual_merchant_id as merchant_id,\n", "     count(distinct id) as orders\n", "     from merchant_and_store_join\n", "     where date(checkout_date) between '{start_date}' AND '{end_date}'\n", "    group by 1,2),\n", "\n", "\n", "\n", "    carts as ( with data as (\n", "                select * from ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts)\n", "                where rnk =1 and date(f_date) between  '{start_date}' AND '{end_date}')\n", "\n", "                select mo.facility_id,sum(ac.orders) as actual_carts,sum(forecast_carts) as forecast_carts\n", "                from data mo\n", "                left join actual1_cart ac on mo.f_date=ac.date_ and mo.merchant_id = ac.merchant_id\n", "                group by 1),\n", "\n", "    s_data as(select \n", "            yo.date_,\n", "            yo.merchant_id,\n", "            m.facility_id,\n", "            m.facility,\n", "            yo.item_id,\n", "            lq_margin,\n", "            max(live_days) as live_days,\n", "            sum(qty_sold) as avg_cpd\n", "    from yo \n", "    left join mapping m on yo.merchant_id = m.merchant_id and yo.date_ = m.date_\n", "    where lq_margin = 0 and facility is not null\n", "    group by 1,2,3,4,5,6\n", "    order by live_days),\n", "\n", "    sdata as (select facility_id,date_,facility,item_id,max(live_days) as live_days,sum(avg_cpd) as avg_cpd \n", "    from s_data group by 1,2,3,4)\n", "\n", "    select oo.facility,oo.facility_id,oo.item_id,map.L0,\n", "         (case when map.L0 ilike '%%%%home%%%%' then 1 else 0 end) as is_gm, \n", "         (case when ff.is_pl is True then 1 else 0 end) as is_gb, \n", "         (case when  x.tag_value = 'Y' then 'Bucket X' else '-' end) as bucket_x, \n", "         (case when ab.tag_value = 'A' then 'Bucket A' else 'Bucket B' end) as bucket,\n", "        p.actual_carts,p.forecast_carts,max(live_days) as live_days,avg(avg_cpd) as avg_cpd \n", "        from sdata oo\n", "        left join carts p on p.facility_id= oo.facility_id\n", "        left join bucket_x_mapping x on x.item_id=oo.item_id and x.facility_id =oo.facility_id  \n", "        left join bucket_a_b_mapping ab on ab.item_id=oo.item_id and ab.facility_id =oo.facility_id  \n", "        left join(select item_id, is_pl,max(name) as product_name\n", "        from rpc_product_product group by 1,2) ff on oo.item_id = ff.item_id\n", "        left join (SELECT item_id,cat.L0 FROM rpc_item_product_mapping rpc\n", "            INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "            AND rpc.offer_id IS NULL AND rpc.item_id IS NOT NULL  AND rpc.product_id IS NOT NULL) map on oo.item_id = map.item_id\n", "     where l0 not in ('Vegetables & Fruits','Fashion')\n", "        and facility is not null and facility not ilike '%%%%dark%%%%' \n", "    group by 1,2,3,4,5,6,7,8,9,10\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=end_date\n", "    )\n", "\n", "    # print(sql_actual_cpd)\n", "    live_days_actual_cpd = pd.read_sql(sql=sql_actual_cpd, con=redshift_con)\n", "\n", "    live_days_actual_cpd[\"facility_id\"] = live_days_actual_cpd[\"facility_id\"].astype(\n", "        str\n", "    )\n", "    live1_days_actual_cpd = pd.merge(\n", "        left=live_days_actual_cpd,\n", "        right=faci_faci1[{\"cfacility_id\", \"zone\"}],\n", "        left_on=\"facility_id\",\n", "        right_on=\"cfacility_id\",\n", "        how=\"left\",\n", "    )\n", "    live_days_actual_cpd = live1_days_actual_cpd.drop(columns={\"cfacility_id\"})\n", "    live_days_actual_cpd = live_days_actual_cpd.loc[\n", "        live_days_actual_cpd.facility != \"Bamnoli\"\n", "    ]\n", "    live_days_actual_cpd = live_days_actual_cpd.loc[\n", "        live_days_actual_cpd.facility != \"Mundka\"\n", "    ]\n", "    live_days_actual_cpd1 = asp_work(live_days_actual_cpd)\n", "    live_days_actual_cpd1[\"item_id\"] = live_days_actual_cpd1[\"item_id\"].astype(str)\n", "    live_days_actual_cpd1[\"union\"] = live_days_actual_cpd1[\"item_id\"].str.cat(\n", "        live_days_actual_cpd1[\"facility\"].copy()\n", "    )\n", "    live_days_actual_cpd1 = live_days_actual_cpd1.rename(\n", "        columns={\n", "            \"avg_cpd\": \"actual_cpd\",\n", "            \"actual_carts\": \"total_carts\",\n", "            \"final_asp\": \"avg_sp\",\n", "        }\n", "    )\n", "    live_days_actual_cpd1 = live_days_actual_cpd1.loc[\n", "        (\n", "            (live_days_actual_cpd1.live_days > 3)\n", "            & (live_days_actual_cpd1.actual_cpd > 0)\n", "        ),\n", "        [\n", "            \"union\",\n", "            \"facility\",\n", "            \"item_id\",\n", "            \"l0\",\n", "            \"is_gm\",\n", "            \"is_gb\",\n", "            \"bucket_x\",\n", "            \"bucket\",\n", "            \"zone\",\n", "            \"total_carts\",\n", "            \"forecast_carts\",\n", "            \"live_days\",\n", "            \"actual_cpd\",\n", "            \"avg_sp\",\n", "        ],\n", "    ]\n", "    return live_days_actual_cpd1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def adjusted1_forecasted_cpd(start_date, end_date, faci_faci1):\n", "    sql_f_cpd = \"\"\"\n", "    with snorlax_tbl_treatment as(\n", "    select\n", "    item_id,\n", "    outlet_id,\n", "    date(date + interval '5.5 Hours') as date_of_prediction,\n", "    (case when consumption=0 then null else consumption end) as consumption1,\n", "    row_number() over (partition by item_id,outlet_id, date(date) order by updated_at desc) as update_rank\n", "    from\n", "    snorlax_date_wise_consumption\n", "    where date_of_prediction between '{start_date}' and '{end_date}')\n", "\n", "    select \n", "    b.item_id,po.facility_id as f_id,mp.facility,\n", "    avg(consumption1) as cpd\n", "    from snorlax_tbl_treatment b\n", "    left join (select id as Outlet_id, facility_id, rnk from \n", "\n", "                (select m.*,row_number() over(partition by updated_at,id order by updated_at desc) as rnk \n", "                from consumer.pos_console_outlet m\n", "                where active ='True')\n", "                where rnk =1 ) po on po.Outlet_id= b.outlet_id\n", "    left join(select* from(select facility_id,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "                from city_facility_split)\n", "              where rnk=1) mp on mp.facility_id=po.facility_id\n", "\n", "    where  po.facility_id is not null and mp.facility not ilike '%%%%dark%%%%' and po.facility_id <> 64\n", "    Group by 1,2,3\n", "\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=end_date\n", "    )\n", "\n", "    adjusted_forecasted = pd.read_sql(sql=sql_f_cpd, con=redshift_con)\n", "\n", "    adjusted_forecasted1 = pd.merge(\n", "        left=adjusted_forecasted,\n", "        right=faci_faci1[{\"cfacility\", \"facility\"}],\n", "        left_on=[\"facility\"],\n", "        right_on=[\"facility\"],\n", "        how=\"left\",\n", "    )\n", "    adjusted_forecasted1.dropna(subset=[\"cfacility\"], inplace=True)\n", "    adjusted_forecasted2 = adjusted_forecasted1.drop(columns={\"facility\"})\n", "    adjusted_forecasted_cpd = adjusted_forecasted2.rename(\n", "        columns={\"cfacility\": \"facility\"}\n", "    )\n", "    adjusted_forecasted_cpd = adjusted_forecasted_cpd.rename(\n", "        columns={\"cpd\": \"avg_f_cpd\"}\n", "    )\n", "    adjusted_forecasted_cpd[\"item_id\"] = adjusted_forecasted_cpd[\"item_id\"].astype(str)\n", "    adjusted_forecasted_cpd[\"union\"] = adjusted_forecasted_cpd[\"item_id\"].str.cat(\n", "        adjusted_forecasted_cpd[\"facility\"].copy()\n", "    )\n", "    return adjusted_forecasted_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def forecasted1_ci_cpd(start_date, end_date):\n", "    sql_ci_cpd = \"\"\"\n", "    select \n", "    CONCAT(\"item_id \", facility) as union,\n", "    \"item_id \", \n", "    facility_id, \n", "    facility,\n", "    avg(qty) as avg_f_cpd\n", "    from\n", "        (select \n", "        \"item_id \", \n", "        facility_id, \n", "        facility,\n", "        qty,\n", "        f_date,\n", "        update_at,\n", "        rank() over (partition by \"item_id \", facility_id, f_date order by update_at desc) as rnk\n", "        from\n", "        category_inputs_db\n", "        where f_date between '{start_date}' and '{end_date}')\n", "    where rnk = 1\n", "    group by 1,2,3,4\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=end_date\n", "    )\n", "\n", "    forecasted_ci_cpd = pd.read_sql(sql=sql_ci_cpd, con=redshift_con)\n", "    return forecasted_ci_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def adjusted1_cpd(start_date, end_date):\n", "    sql_adj_cpd = \"\"\"select \n", "    item_id, \n", "    facility_id, \n", "    facility,\n", "    avg(consumption1) as avg_f_cpd\n", "    from\n", "        (select \n", "        item_id, \n", "        facility_id, \n", "        facility,\n", "        consumption,\n", "        (case when consumption=0 then null else consumption end) as consumption1,\n", "        rank() over (partition by item_id, facility_id, consumption_date order by updated_at desc) as rnk\n", "        from\n", "         consumer.day_wise_adjustment_db\n", "        where consumption_date between '{start_date}' and '{end_date}')\n", "    where rnk = 1\n", "    group by 1,2,3\n", "\n", "    \"\"\".format(\n", "        start_date=start_date, end_date=end_date\n", "    )\n", "    adjusted_cpd = pd.read_sql(sql=sql_adj_cpd, con=redshift_con)\n", "    adjusted_cpd[\"item_id\"] = adjusted_cpd[\"item_id\"].astype(str)\n", "    adjusted_cpd[\"union\"] = adjusted_cpd[\"item_id\"].str.cat(\n", "        adjusted_cpd[\"facility\"].copy()\n", "    )\n", "    return adjusted_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1LkzcTCjt_J5ILplOZ-iK3wmCAklqbYlCV-0zad-syVM\"\n", "mappings = pb.from_sheets(sheet_id, \"Mappings\")\n", "dq = \"\"\"\n", "select date( date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY('{date}')))))\n", "\"\"\".format(\n", "    date=end_date\n", ")\n", "sdate1 = pd.read_sql(dq, con=redshift_con)\n", "sdate1[\"date\"] = sdate1[\"date\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "start_date = sdate1.date[0]\n", "faci_faci = mappings.loc[\n", "    mappings[\"5.correct_facility\"] != \"\",\n", "    [\"5.correct_fid\", \"5.correct_facility\", \"5.f_id\", \"5.facility\", \"5.Zone\"],\n", "]\n", "faci_faci1 = faci_faci.rename(\n", "    columns={\n", "        \"5.correct_fid\": \"cfacility_id\",\n", "        \"5.correct_facility\": \"cfacility\",\n", "        \"5.f_id\": \"facility_id\",\n", "        \"5.facility\": \"facility\",\n", "        \"5.Zone\": \"zone\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_days_actual_cpd1 = actual_sales(start_date, end_date, faci_faci1)\n", "# Final snorlax cpd\n", "adjusted_forecasted_cpd = adjusted1_forecasted_cpd(start_date, end_date, faci_faci1)\n", "final_adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_forecasted_cpd)\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"is_gm\"] == 0]\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"live_days\"] > 3]\n", "final_adjusted_df = final_adjusted_df[pd.notnull(final_adjusted_df[\"zone\"])]\n", "summary_v = summary(final_adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"MTD_final_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"MTD_fianl_qpc\")\n", "# for adjusted data\n", "adjusted_cpd = adjusted1_cpd(start_date, end_date)\n", "adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_cpd)\n", "adjusted_df = adjusted_df[adjusted_df[\"is_gm\"] == 0]\n", "adjusted_df = adjusted_df[pd.notnull(adjusted_df[\"zone\"])]\n", "summary_v = summary(adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"MTD_adj_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"MTD_adj_qpc\")\n", "# for category input cpd\n", "forecasted_ci_cpd = forecasted1_ci_cpd(start_date, end_date)\n", "ci_model_df = combine_tables(live_days_actual_cpd1, forecasted_ci_cpd)\n", "ci_model_df = ci_model_df[ci_model_df[\"is_gm\"] == 0]\n", "ci_model_df = ci_model_df[ci_model_df[\"live_days\"] > 3]\n", "ci_model_df = ci_model_df[pd.notnull(ci_model_df[\"zone\"])]\n", "ci_model_df = ci_model_df[ci_model_df[\"avg_f_cpd\"] > 0]\n", "summary_v = summary(ci_model_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"MTD_cat_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"MTD_cat_qpc\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_days_actual_cpd1 = actual_sales(s7_date, end_date, faci_faci1)\n", "# Final snorlax cpd\n", "adjusted_forecasted_cpd = adjusted1_forecasted_cpd(s7_date, end_date, faci_faci1)\n", "final_adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_forecasted_cpd)\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"is_gm\"] == 0]\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"live_days\"] > 3]\n", "final_adjusted_df = final_adjusted_df[pd.notnull(final_adjusted_df[\"zone\"])]\n", "summary_v = summary(final_adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"t_1_final_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"t_1_final_qpc\")\n", "# for adjusted data\n", "adjusted_cpd = adjusted1_cpd(s7_date, end_date)\n", "adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_cpd)\n", "adjusted_df = adjusted_df[adjusted_df[\"is_gm\"] == 0]\n", "adjusted_df = adjusted_df[pd.notnull(adjusted_df[\"zone\"])]\n", "summary_v = summary(adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"t-1_adj_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"t-1_adj_qpc\")\n", "# for category input cpd\n", "forecasted_ci_cpd = forecasted1_ci_cpd(s7_date, end_date)\n", "ci_model_df = combine_tables(live_days_actual_cpd1, forecasted_ci_cpd)\n", "ci_model_df = ci_model_df[ci_model_df[\"is_gm\"] == 0]\n", "ci_model_df = ci_model_df[ci_model_df[\"live_days\"] > 3]\n", "ci_model_df = ci_model_df[pd.notnull(ci_model_df[\"zone\"])]\n", "ci_model_df = ci_model_df[ci_model_df[\"avg_f_cpd\"] > 0]\n", "summary_v = summary(ci_model_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"t-1_cat_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"t-1_cat_qpc\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_days_actual_cpd1 = actual_sales(s3_date, end_date, faci_faci1)\n", "# Final snorlax cpd\n", "adjusted_forecasted_cpd = adjusted1_forecasted_cpd(s3_date, end_date, faci_faci1)\n", "final_adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_forecasted_cpd)\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"is_gm\"] == 0]\n", "final_adjusted_df = final_adjusted_df[final_adjusted_df[\"live_days\"] > 3]\n", "final_adjusted_df = final_adjusted_df[pd.notnull(final_adjusted_df[\"zone\"])]\n", "summary_v = summary(final_adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"3day_final_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"3day_final_qpc\")\n", "# for adjusted data\n", "adjusted_cpd = adjusted1_cpd(s3_date, end_date)\n", "adjusted_df = combine_tables(live_days_actual_cpd1, adjusted_cpd)\n", "adjusted_df = adjusted_df[adjusted_df[\"is_gm\"] == 0]\n", "adjusted_df = adjusted_df[pd.notnull(adjusted_df[\"zone\"])]\n", "summary_v = summary(adjusted_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"3day_adj_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"3day_adj_qpc\")\n", "# for category input cpd\n", "forecasted_ci_cpd = forecasted1_ci_cpd(s3_date, end_date)\n", "ci_model_df = combine_tables(live_days_actual_cpd1, forecasted_ci_cpd)\n", "ci_model_df = ci_model_df[ci_model_df[\"is_gm\"] == 0]\n", "ci_model_df = ci_model_df[ci_model_df[\"live_days\"] > 3]\n", "ci_model_df = ci_model_df[pd.notnull(ci_model_df[\"zone\"])]\n", "ci_model_df = ci_model_df[ci_model_df[\"avg_f_cpd\"] > 0]\n", "summary_v = summary(ci_model_df)\n", "facility_wise_cpd = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_cpd\",\n", "        \"bucket_x_cpd\",\n", "        \"bucket_a_cpd\",\n", "        \"bucket_b_cpd\",\n", "        \"GB_cpd\",\n", "        \"GMV_cpd\",\n", "        \"Excess_cpd\",\n", "    ],\n", "]\n", "facility_wise_qpc = summary_v.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"Over_all_qpc\",\n", "        \"bucket_x_qpc\",\n", "        \"bucket_a_qpc\",\n", "        \"bucket_b_qpc\",\n", "        \"GB_qpc\",\n", "        \"GMV_qpc\",\n", "        \"Excess_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_cpd, sheetid=sheet_id1, sheetname=\"3day_cat_cpd\")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(facility_wise_qpc, sheetid=sheet_id1, sheetname=\"3day_cat_qpc\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time = pd.DataFrame(\n", "    {\n", "        \"zone\": [\n", "            (dt.datetime.now() + timedelta(hours=5) + timedelta(minutes=30)).strftime(\n", "                \"%Y-%m-%d %H:%M:%S\"\n", "            )\n", "        ]\n", "    }\n", ")\n", "sheet_id1 = \"1NBXxkg-ybbEg9FeeVFJVsDUSseMwxUo21Ki6JX7i9e8\"\n", "pb.to_sheets(time, sheetid=sheet_id1, sheetname=\"MTD_up_time\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}