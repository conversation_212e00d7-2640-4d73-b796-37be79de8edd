{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "import openpyxl\n", "\n", "import time\n", "import pytz\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1_summary = \"\"\"\n", "\n", "\n", "\n", "\n", "select outlet.name outlet_name,result.* from (\n", "select t1.outlet_id,t1.audit_created_at,t1.total_audits_created,t1.total_audits_done,t2.audits_for_BS,audits_for_QM\n", ",floor((t1.total_audits_done/t1.total_audits_created)*100) as adoption_rate\n", "\n", "from (select outlet_id,audit_created_at,sum(audit_created) total_audits_created,sum(audit_done) as total_audits_done\n", "-- ,case when reason='Bad Stock' then count(reason) else 0 end as audits_for_BS\n", "-- ,case when reason='Quantity mismatch' then count(reason) else 0 end as audits_for_QM\n", "from\n", "(select  outlet_id,date(reported_timestamp) as audit_created_at\n", ",count(outlet_id) as audit_created, count(comp_by) as audit_done,reason\n", "from\n", "(\n", "select * from\n", "(\n", "select final.* from\n", "(\n", "select \n", "t1.*\n", ",case when t2.comp_time >t1.reported_timestamp then t2.comp_time else NULL end as audit_done_at \n", ",case when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=3 and reason='Quantity mismatch' THEN \"Audit positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and reason='Quantity mismatch' THEN \"Audit negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and reason='Bad Stock' THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=5 and reason='Quantity mismatch' THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and reason='Quantity mismatch' THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and reason='Bad Stock' THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and reason='Bad Stock' THEN \"Invalid\"\n", "else '' end as comp_update_type\n", ",case when t2.comp_time >t1.reported_timestamp then t2.comp_by else NULL end as comp_by\n", ",case when t2.comp_time >t1.reported_timestamp then timestampdiff(minute,t1.reported_timestamp,t2.comp_time)/60 else NULL end as time_taken_hrs\n", "from (SELECT \n", "      log.outlet_id,\n", "      log.created_by as reported_by,\n", "       convert_tz(pos_created_at,\"+00:00\",\"+05:30\") as reported_timestamp,\n", "      concat(pro.name,\"--\",pro.variant_description) as product_description,\n", "      log.variant_id,\n", "      wsl.location_name,\n", "      log.location_id,\n", "      log.warehouse_id,\n", "       (CASE\n", "            WHEN log.reason=1 THEN 'Quantity mismatch'\n", "            ELSE 'Bad Stock'\n", "        END) AS reason\n", "FROM warehouse_location.warehouse_audit_request_log log\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "          created_by,\n", "          variant_id,\n", "          item_id,\n", "          location_id,\n", "          reason,\n", "          max(pos_created_at) AS pos_ts\n", "   FROM warehouse_location.warehouse_audit_request_log group by 1,2,3,4,5,6)x1 ON \n", "    log.pos_created_at=x1.pos_ts\n", "AND log.outlet_id=x1.outlet_id\n", "AND log.variant_id=x1.variant_id\n", "AND log.location_id=x1.location_id\n", "inner join rpc.product_product pro on pro.variant_id=log.variant_id\n", "inner join warehouse_location.warehouse_storage_location wsl on log.location_id=wsl.id\n", "\n", "-- update the date range condition\n", "WHERE date(convert_tz(log.pos_created_at,\"+00:00\",\"+05:30\"))=date_sub(current_date(),interval 1 DAY)\n", "-- and x1.outlet_id=369\n", "-- and log.variant_id='af5f68b9-ebc0-8cf3-227d-e1f0b4a0f481'\n", "-- WHERE convert_tz(pos_created_at,\"+00:00\",\"+05:30\")>current_date()-1\n", "order by reported_timestamp asc) t1\n", "\n", "left join\n", "\n", "(select \n", "audit_raised.item_id as req_item_id,\n", "audit_raised.variant_id as req_variant_id,\n", "convert_tz(audit_raised.created_at ,\"+00:00\",\"+05:30\") req_created_at,\n", "audit_raised.location_id as req_location_id,\n", "audit_raised.warehouse_id as req_warehouse_id,\n", "    audit_done.item_id as comp_item_id, \n", "    audit_done.variant_id as comp_variant_id, \n", "    -- convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")\n", "    min(convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")) as comp_time, \n", "    audit_done.location_id as comp_location_id, \n", "    audit_done.warehouse_id as comp_warehouse_id, \n", "    audit_done.created_by as comp_by,\n", "    audit_done.item_location_update_type_id,\n", "    -- case when audit_done.item_location_update_type_id=  3 then 'Add quantity' else 'Remove quantity' end as  comp_update_type\n", "    -- case when audit_done.item_location_update_type_id=  3 then 3 else 4 end as  comp_update_type\n", "    audit_done.item_location_update_type_id as comp_update_type\n", "from warehouse_location.warehouse_audit_request_log audit_raised  \n", "inner join warehouse_location.warehouse_item_location_log audit_done \n", "on  audit_raised.variant_id=audit_done.variant_id \n", "and audit_raised.item_id=audit_done.item_id \n", "and audit_raised.location_id=audit_done.location_id\n", "and audit_raised.warehouse_id=audit_done.warehouse_id\n", "and audit_raised.created_at<audit_done.pos_timestamp\n", "where audit_done.id>\n", "(select  min(id) from warehouse_location.warehouse_item_location_log where \n", "date(convert_tz(pos_timestamp,\"+00:00\",\"+05:30\"))=date(convert_tz(date_sub(current_date(),interval 2 DAY) ,\"+00:00\",\"+05:30\"))\n", "and id>274706149\n", ")  \n", "and audit_done.item_location_update_type_id in (3,4,5,6)\n", "and date(convert_tz(audit_raised.pos_created_at,\"+00:00\",\"+05:30\"))=date_sub(current_date(),interval 1 DAY) \n", "\n", "-- and audit_raised.outlet_id=369\n", "-- and audit_raised.variant_id='af5f68b9-ebc0-8cf3-227d-e1f0b4a0f481'\n", "-- and audit_raised.location_id ='542626'\n", "group by 1,2,3,4,5,6,7,9,10,11,12) t2\n", "on t1.variant_id=t2.req_variant_id\n", "and t1.location_id=t2.req_location_id\n", "and t1.warehouse_id=t2.req_warehouse_id\n", "and t1.reported_timestamp<t2.comp_time\n", "-- and t1.item_id=t2.req_item_id\n", "-- where t2.comp_time is not Null\n", "order by  t1.reported_timestamp desc\n", ") final\n", "group by  1,2,3,4,5,6,7,8,9,10,11,12,13\n", "order by  reported_timestamp desc\n", ") tt\n", "where (comp_update_type!='Invalid') \n", "\n", ") final_table group by 1,2,5 order by 1,2) last_table group by outlet_id,audit_created_at) t1\n", "\n", "-- ******************************--\n", "\n", "\n", "left join\n", "\n", "(\n", "-- select outlet_id,audit_created_at,sum(audit_created) total_audits_created,sum(audit_done) as total_audits_done\n", "-- -- ,case when reason='Bad Stock' then count(reason) else 0 end as audits_for_BS\n", "-- -- ,case when reason='Quantity mismatch' then count(reason) else 0 end as audits_for_QM\n", "-- from\n", "-- (\n", "-- select  outlet_id,date(reported_timestamp) as audit_created_at\n", "-- ,count(outlet_id) as audit_created, count(comp_by) as audit_done,reason\n", "-- from\n", "-- (\n", "select outlet_id, audit_created_at, sum(audits_for_BS) audits_for_BS,sum(audits_for_QM) audits_for_QM from\n", "(\n", "select  outlet_id,date(reported_timestamp) as audit_created_at,reason\n", ",case when reason='Bad Stock' then count(reason) else 0 end as audits_for_BS\n", ",case when reason='Quantity mismatch' then count(reason) else 0 end as audits_for_QM from\n", "-- select * from\n", "(\n", "select final.* from\n", "(\n", "select \n", "t1.*\n", ",case when t2.comp_time >t1.reported_timestamp then t2.comp_time else NULL end as audit_done_at \n", ",case when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=3 and reason='Quantity mismatch' THEN \"Audit positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and reason='Quantity mismatch' THEN \"Audit negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and reason='Bad Stock' THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=5 and reason='Quantity mismatch' THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and reason='Quantity mismatch' THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and reason='Bad Stock' THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and reason='Bad Stock' THEN \"Invalid\"\n", "else '' end as comp_update_type\n", ",case when t2.comp_time >t1.reported_timestamp then t2.comp_by else NULL end as comp_by\n", ",case when t2.comp_time >t1.reported_timestamp then timestampdiff(minute,t1.reported_timestamp,t2.comp_time)/60 else NULL end as time_taken_hrs\n", "from (SELECT \n", "      log.outlet_id,\n", "      log.created_by as reported_by,\n", "       convert_tz(pos_created_at,\"+00:00\",\"+05:30\") as reported_timestamp,\n", "      concat(pro.name,\"--\",pro.variant_description) as product_description,\n", "      log.variant_id,\n", "      wsl.location_name,\n", "      log.location_id,\n", "      log.warehouse_id,\n", "       (CASE\n", "            WHEN log.reason=1 THEN 'Quantity mismatch'\n", "            ELSE 'Bad Stock'\n", "        END) AS reason\n", "FROM warehouse_location.warehouse_audit_request_log log\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "          created_by,\n", "          variant_id,\n", "          item_id,\n", "          location_id,\n", "          reason,\n", "          max(pos_created_at) AS pos_ts\n", "   FROM warehouse_location.warehouse_audit_request_log group by 1,2,3,4,5,6)x1 ON \n", "    log.pos_created_at=x1.pos_ts\n", "AND log.outlet_id=x1.outlet_id\n", "AND log.variant_id=x1.variant_id\n", "AND log.location_id=x1.location_id\n", "inner join rpc.product_product pro on pro.variant_id=log.variant_id\n", "inner join warehouse_location.warehouse_storage_location wsl on log.location_id=wsl.id\n", "\n", "-- update the date range condition\n", "WHERE date(convert_tz(log.pos_created_at,\"+00:00\",\"+05:30\"))=date_sub(current_date(),interval 1 DAY)\n", "-- and x1.outlet_id=287\n", "-- and log.variant_id='af5f68b9-ebc0-8cf3-227d-e1f0b4a0f481'\n", "-- WHERE convert_tz(pos_created_at,\"+00:00\",\"+05:30\")>current_date()-1\n", "order by reported_timestamp asc) t1\n", "\n", "left join\n", "\n", "(select \n", "audit_raised.item_id as req_item_id,\n", "audit_raised.variant_id as req_variant_id,\n", "convert_tz(audit_raised.created_at ,\"+00:00\",\"+05:30\") req_created_at,\n", "audit_raised.location_id as req_location_id,\n", "audit_raised.warehouse_id as req_warehouse_id,\n", "    audit_done.item_id as comp_item_id, \n", "    audit_done.variant_id as comp_variant_id, \n", "    -- convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")\n", "    min(convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")) as comp_time, \n", "    audit_done.location_id as comp_location_id, \n", "    audit_done.warehouse_id as comp_warehouse_id, \n", "    audit_done.created_by as comp_by,\n", "    audit_done.item_location_update_type_id,\n", "    -- case when audit_done.item_location_update_type_id=  3 then 'Add quantity' else 'Remove quantity' end as  comp_update_type\n", "    -- case when audit_done.item_location_update_type_id=  3 then 3 else 4 end as  comp_update_type\n", "    audit_done.item_location_update_type_id as comp_update_type\n", "from warehouse_location.warehouse_audit_request_log audit_raised  \n", "inner join warehouse_location.warehouse_item_location_log audit_done \n", "on  audit_raised.variant_id=audit_done.variant_id \n", "and audit_raised.item_id=audit_done.item_id \n", "and audit_raised.location_id=audit_done.location_id\n", "and audit_raised.warehouse_id=audit_done.warehouse_id\n", "and audit_raised.created_at<audit_done.pos_timestamp\n", "where audit_done.id>\n", "(select  min(id) from warehouse_location.warehouse_item_location_log where \n", "date(convert_tz(pos_timestamp,\"+00:00\",\"+05:30\"))=date(convert_tz(date_sub(current_date(),interval 2 DAY) ,\"+00:00\",\"+05:30\"))\n", "and id>274706149\n", ")  \n", "and audit_done.item_location_update_type_id in (3,4,5,6)\n", "and date(convert_tz(audit_raised.pos_created_at,\"+00:00\",\"+05:30\"))=date_sub(current_date(),interval 1 DAY) \n", "\n", "-- and audit_raised.outlet_id=287\n", "-- and audit_raised.variant_id='af5f68b9-ebc0-8cf3-227d-e1f0b4a0f481'\n", "-- and audit_raised.location_id ='542626'\n", "group by 1,2,3,4,5,6,7,9,10,11,12) t2\n", "on t1.variant_id=t2.req_variant_id\n", "and t1.location_id=t2.req_location_id\n", "and t1.warehouse_id=t2.req_warehouse_id\n", "and t1.reported_timestamp<t2.comp_time\n", "-- and t1.item_id=t2.req_item_id\n", "-- where t2.comp_time is not Null\n", "order by  t1.reported_timestamp desc\n", ") final\n", "group by  1,2,3,4,5,6,7,8,9,10,11,12,13\n", "order by  reported_timestamp desc\n", ") tt\n", "where (comp_update_type!='Invalid') \n", "\n", "-- ) final_table group by 1,2,5 order by 1,2) last_table group by outlet_id,audit_created_at\n", "\n", "group by 1,2,3\n", ") final_table group by 1,2\n", "\n", ") t2\n", "on t1.outlet_id=t2.outlet_id and t1.audit_created_at=t2.audit_created_at group by 2,1 order by 2,1\n", ")result\n", "\n", "left join retail.console_outlet outlet  on outlet.id=result.outlet_id\n", "-- where result.outlet_id in (367,369)\n", "group by 1,2,3\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2_rawdata = \"\"\"\n", "\n", "select final.* from(select \n", "t1.*\n", ",case when t2.comp_update_type >t1.reported_timestamp then t2.comp_update_type else NULL end as audit_done_at \n", ",case when t2.comp_update_type >t1.reported_timestamp then t2.comp_update_type else NULL end as comp_update_type\n", ",case when t2.comp_update_type >t1.reported_timestamp then t2.comp_update_type else NULL end as comp_by\n", ",case when t2.comp_update_type >t1.reported_timestamp then timestampdiff(minute,t1.reported_timestamp,t2.comp_time)/60 else NULL end as time_taken_hrs\n", "from (SELECT \n", "      log.outlet_id,\n", "      log.created_by as reported_by,\n", "       convert_tz(pos_created_at,\"+00:00\",\"+05:30\") as reported_timestamp,\n", "      concat(pro.name,\"--\",pro.variant_description) as product_description,\n", "      log.variant_id,\n", "      wsl.location_name,\n", "      log.location_id,\n", "      log.warehouse_id,\n", "       (CASE\n", "            WHEN log.reason=1 THEN 'Quantity mismatch'\n", "            ELSE 'Bad Stock'\n", "        END) AS reason\n", "FROM warehouse_location.warehouse_audit_request_log log\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "          created_by,\n", "          variant_id,\n", "          item_id,\n", "          location_id,\n", "          reason,\n", "          max(pos_created_at) AS pos_ts\n", "   FROM warehouse_location.warehouse_audit_request_log group by 1,2,3,4,5,6)x1 ON \n", "    log.pos_created_at=x1.pos_ts\n", "AND log.outlet_id=x1.outlet_id\n", "AND log.variant_id=x1.variant_id\n", "AND log.location_id=x1.location_id\n", "inner join rpc.product_product pro on pro.variant_id=log.variant_id\n", "inner join warehouse_location.warehouse_storage_location wsl on log.location_id=wsl.id\n", "\n", "-- update the date range condition\n", "WHERE date(convert_tz(log.pos_created_at,\"+00:00\",\"+05:30\"))>date_sub(current_date(),interval 3 DAY)\n", "\n", "-- WHERE convert_tz(pos_created_at,\"+00:00\",\"+05:30\")>current_date()-1\n", "order by reported_timestamp asc) t1\n", "\n", "left join\n", "\n", "(select \n", "audit_raised.item_id as req_item_id,\n", "audit_raised.variant_id as req_variant_id,\n", "audit_raised.created_at as req_created_at,\n", "audit_raised.location_id as req_location_id,\n", "audit_raised.warehouse_id as req_warehouse_id,\n", "    audit_done.item_id as comp_item_id, \n", "    audit_done.variant_id as comp_variant_id, \n", "    -- convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")\n", "    max(convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")) as comp_time, \n", "    audit_done.location_id as comp_location_id, \n", "    audit_done.warehouse_id as comp_warehouse_id, \n", "    audit_done.created_by as comp_by,\n", "    case when audit_done.item_location_update_type_id=  3 then 'Add quantity' else 'Remove quantity' end as  comp_update_type\n", "from warehouse_location.warehouse_audit_request_log audit_raised  \n", "inner join warehouse_location.warehouse_item_location_log audit_done \n", "on  audit_raised.variant_id=audit_done.variant_id \n", "and audit_raised.item_id=audit_done.item_id \n", "and audit_raised.location_id=audit_done.location_id\n", "and audit_raised.warehouse_id=audit_done.warehouse_id\n", "and audit_raised.created_at<audit_done.pos_timestamp\n", "where audit_done.id> (select  min(id) from warehouse_location.warehouse_item_location_log where \n", "date(convert_tz(pos_timestamp,\"+00:00\",\"+05:30\"))=date(convert_tz(date_sub(current_date(),interval 2 DAY) ,\"+00:00\",\"+05:30\"))\n", "and id>274706149)    \n", "and audit_done.item_location_update_type_id in (3,4)\n", "group by 1,2,3,4,5,6,7,9,10,11,12) t2\n", "on\n", "t1.variant_id=t2.req_variant_id\n", "and t1.location_id=t2.req_location_id\n", "and t1.warehouse_id=t2.req_warehouse_id\n", "-- and t1.item_id=t2.req_item_id\n", "-- where t2.comp_time is not Null\n", "order by  t1.reported_timestamp desc)final \n", "-- where outlet_id=367\n", "group by  1,2,3,4,5,6,7,8,9,10,11,12,13\n", "order by  reported_timestamp desc\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1 = pd.read_sql_query(sql=query1_summary, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data2 = pd.read_sql_query(sql=query2_rawdata, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_audits = len(data2[\"outlet_id\"])\n", "\n", "total_audits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data1.rename(\n", "#   columns={\n", "#      \"order id\": \"order_id\",\n", "#     \"total reschedules\": \"total_reschedules\",\n", "#    \"last mile\": \"last_mile\",\n", "#   \"last reschedule time\": \"last_reschedule_time\",\n", "#  \"last reason key\": \"last_reason_key\",\n", "# \"frontend merchant\": \"frontend_merchant\",\n", "# },\n", "# inplace=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = data1.to_dict(orient=\"rows\")\n", "items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz)).strftime(\"%Y-%m-%d\")\n", "today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data2.to_excel(\"audit_\" + today + \".xlsx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_audits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# cwd=pathlib.Path().absolute()\n", "# cwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to:[\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"]\n", "\n", "# cc:[\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",  \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",  \"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\"]\n", "\n", "# ,\"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",  \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",  \"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1DU4g8PEXcK73WO5vv2Jlo4T_GdGTlCz_d5Bll2cShq4\"\n", "outlet_data = pb.from_sheets(sheet_id, \"Outlet LIst\", service_account=\"service_account\")\n", "\n", "a = outlet_data.columns[1:2]\n", "outlet_list = outlet_data[outlet_data.columns[0:2]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_list.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_query_list = []\n", "\n", "for current_outlet in outlet_list:\n", "    outlet_query_list.append(\"query_rawdata_\" + str(current_outlet))\n", "\n", "print(outlet_query_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["audit_running_outlet = \"\"\"\n", "\n", "\n", "SELECT outlet_id as \"Outlet ID\",\n", "      max(a.created_at) as  'last_audit_raised'\n", "FROM warehouse_location.warehouse_audit_request_log a\n", "left join retail.console_outlet b on a.outlet_id=b.id\n", "GROUP BY 1 \n", "order by 1 desc\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_running_audit = pd.read_sql_query(sql=audit_running_outlet, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_running_audit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t1 = outlet_list\n", "t2 = outlet_running_audit\n", "# result.merge\n", "t1[\"Outlet ID\"] = t1[\"Outlet ID\"].astype(\"int\")\n", "result = t1.merge(t2, on=\"Outlet ID\", how=\"left\")\n", "result[\"last_audit_raised\"] = result[\"last_audit_raised\"].dt.date\n", "result = result.sort_values(\n", "    by=\"last_audit_raised\", axis=0, ascending=False, inplace=False, na_position=\"last\"\n", ")\n", "result[\"last_audit_raised\"] = result[\"last_audit_raised\"].fillna(\"Never audited\")\n", "\n", "result = result.reset_index(drop=True)\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.rename(\n", "    columns={\"Outlet Name\": \"Outlet_Name\", \"Outlet ID\": \"Outlet_Id\"}, inplace=True\n", ")\n", "items2 = result.to_dict(orient=\"rows\")\n", "items2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table1 = result\n", "table2 = data1\n", "table1.rename(columns={\"Outlet_Id\": \"outlet_id\"}, inplace=True)\n", "# result.merge\n", "# t1[\"Outlet ID\"]=t1[\"Outlet ID\"].astype(\"int\")\n", "final_result = table1.merge(table2, on=\"outlet_id\", how=\"left\")\n", "# result['last_audit_raised']=result['last_audit_raised'].dt.date\n", "# result=result.sort_values(by='last_audit_raised', axis=0, ascending=False, inplace=False, na_position='last')\n", "# result['last_audit_raised']=result['last_audit_raised'].fillna('Never audited')\n", "\n", "final_result = final_result.reset_index(drop=True)\n", "final_result = final_result.replace(np.nan, \"\", regex=True)\n", "final_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_result.fillna('')\n", "items3 = final_result.to_dict(orient=\"rows\")\n", "final_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "to_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fc_mail = outlet_data[\"Mail Group\"]\n", "fc_mail = fc_mail.reset_index(drop=True)\n", "fc_mail"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len = fc_mail.size\n", "len"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rec = to_email\n", "for i in range(0, len):\n", "    x = fc_mail[i]\n", "    final_rec.append(x)\n", "\n", "final_rec\n", "# final_rec  =''\n", "# final_rec=to_email\n", "# final_rec.append(fc_mail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "to_email = final_rec\n", "subject = \"Audit summary for \" + today\n", "# to_email=['<EMAIL>','<EMAIL>']\n", "with open(os.path.join(cwd, \"email_template.html\"), \"r\") as f:\n", "    t = Template(f.read())\n", "\n", "# rendered = t.render(products=items2, total_audits=total_audits)\n", "# rendered = t.render(products2=items2,products=items, total_audits=total_audits)\n", "rendered = t.render(products=items3, total_audits=total_audits)\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\"audit_\" + today + \".xlsx\"],\n", ")\n", "#\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# End of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}