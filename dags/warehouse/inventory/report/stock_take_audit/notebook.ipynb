{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import os\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import sqlalchemy as sqla\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = datetime.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Weights = [0.05, 0.1, 0.05, 0.2, 0.2, 0.175, 0.1, 0.125]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def SetFrequency(Score):\n", "    if Score > Threshold3:\n", "        return 3\n", "    elif Score > Threshold1 and Score < Threshold3:\n", "        return 2\n", "    else:\n", "        return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["GLOBAL_BASE_DIR = \"Stock_take_Path/Stock_take_Primary\"\n", "OUTPUT_BASE_DIR = \"Stock_take_Path/Stock_take_Secondary\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"auditdate\": \"Date on which Audit is Requested/Performed\",\n", "    \"facility\": \" \",\n", "    \"frequency\": \"An internal metric to measure importance location\",\n", "    \"location_id\": \"Unique Identity of Location\",\n", "    \"rankfinal\": \" Internal metric\",\n", "    \"weightedscore\": \"internal metric\",\n", "    \"key\": \"unique key for updating table\",\n", "    \"Audit Status (Yes/No)\": \" Whether audit was done or not\",\n", "    \"Discrepancy (Quantity Mismatch/BadStock)\": \" Discrepancy found during audit\",\n", "    \"Remarks\": \"Any Remarks given by auditor\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_df_Primary(df, name):\n", "    fpath = os.path.join(GLOBAL_BASE_DIR, \"ThisCycle\", name)\n", "    ret = df.to_csv(fpath, index=False, encoding=\"utf8\")\n", "    return ret"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_df_Secondary(df, name):\n", "    fpath = os.path.join(OUTPUT_BASE_DIR, \"ThisCycle\", name)\n", "    ret = df.to_csv(fpath, index=False, encoding=\"utf8\")\n", "    return ret"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_df_Primary(name):\n", "    return pd.read_csv(\n", "        os.path.join(GLOBAL_BASE_DIR, \"ThisCycle\", name), encoding=\"utf8\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_df_Secondary(name):\n", "    return pd.read_csv(\n", "        os.path.join(OUTPUT_BASE_DIR, \"ThisCycle\", name), encoding=\"utf8\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Primary Query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AudQuery = \"\"\"\n", "WITH table_1 AS\n", "  (SELECT DISTINCT wl.id AS location_id,\n", "                   wl.location_name,\n", "                   split_part(wl.location_name,'-',2) AS Aisle,\n", "                   wl.updated_at AS location_last_updated,\n", "                   REPLACE(REPLACE(fc.name,'Super Store',''),' - Warehouse','') AS Facility,\n", "                   wom.outlet_id,\n", "                   wil.item_id AS itemid,\n", "                   sum(wil.quantity) Qty,\n", "                   min(wil.batch) AS ExpiryDate\n", "   FROM lake_warehouse_location.warehouse_storage_location wl\n", "   LEFT JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wl.warehouse_id=wom.warehouse_id\n", "   LEFT JOIN lake_warehouse_location.warehouse_item_location wil ON wl.id=wil.location_id\n", "   LEFT JOIN lake_retail.console_outlet co ON co.id=wom.outlet_id\n", "   LEFT JOIN lake_crates.facility fc ON co.facility_id = fc.id\n", "   WHERE wl.is_active=TRUE\n", "     AND wl.quantity >0\n", "     AND wl.location_type=1\n", "     AND wl.storage_type_id IN (1,\n", "                                2,\n", "                                6)\n", "     AND wom.outlet_id=%(outlet_id)s                          \n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7\n", "   HAVING Qty>0),\n", "     Demand AS\n", "  (SELECT pp.item_id,\n", "          ord_det.outlet,\n", "          sum(prod_details.quantity) AS ItemDemand,\n", "          avg(prod_details.selling_price) AS SellPrice\n", "   FROM lake_ims.ims_order_details ord_det\n", "   LEFT JOIN lake_pos.view_pos_invoice inv ON inv.grofers_order_id=ord_det.order_id\n", "   LEFT JOIN lake_pos.pos_invoice_product_details prod_details ON inv.id=prod_details.invoice_id\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=prod_details.variant_id\n", "   WHERE ord_det.outlet=%(outlet_id)s\n", "     AND date(pos_timestamp)>= CURRENT_DATE-30\n", "     AND store_offer_id IS NULL\n", "     AND inv.invoice_type_id=1\n", "   GROUP BY 1,\n", "            2),\n", "     Bad_Inventory AS\n", "  (SELECT pp.item_id,\n", "          ibiul.outlet_id,\n", "          sum(ibiul.\"delta\") AS qty_lost\n", "   FROM lake_ims.ims_bad_inventory_update_log ibiul\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=ibiul.variant_id\n", "   WHERE date(convert_timezone('Asia/Kolkata',ibiul.created_at)) >=CURRENT_DATE-30\n", "     AND ibiul.outlet_id=%(outlet_id)s\n", "     AND ibiul.inventory_update_type_id IN (11,\n", "                                            12,\n", "                                            13,\n", "                                            64)\n", "   GROUP BY 1,\n", "            2),\n", "     Landing_Price AS\n", "  (SELECT outlet_id,\n", "          pp.item_id,\n", "          avg(landing_price) AS landingprice\n", "   FROM lake_ims.ims_inventory_landing_price lp\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=lp.variant_id\n", "   WHERE outlet_id=%(outlet_id)s\n", "   GROUP BY 1,\n", "            2),\n", "     table2 AS\n", "  (SELECT          table_1.facility,\n", "                   table_1.outlet_id,\n", "                   table_1.location_id,\n", "                   table_1.location_name,\n", "                   table_1.aisle,\n", "                   table_1.location_last_updated,\n", "                   table_1.itemid,\n", "                   min(table_1.ExpiryDate) as Expiry_Date,\n", "                   min(pp.skip_expiry::int) AS skip_expiry,\n", "                    sum(table_1.Qty) AS Item_Available_Quantity,\n", "                    sum(Bad_Inventory.qty_lost) AS Bad_Inventory_Update,\n", "                    sum(Demand.ItemDemand) AS ItemDemand,\n", "                    avg(Demand.SellPrice) AS SellPrice,\n", "                    avg(LP.landingprice) as LandingPrice\n", "   FROM table_1\n", " LEFT JOIN lake_rpc.product_product pp ON table_1.itemid=pp.item_id\n", "   LEFT JOIN Bad_Inventory ON Bad_Inventory.outlet_id=table_1.outlet_id\n", "   AND Bad_Inventory.item_id=table_1.itemid\n", "   LEFT JOIN Demand ON Demand.outlet=table_1.outlet_id\n", "   AND Demand.item_id=table_1.itemid\n", "   LEFT JOIN Landing_Price LP ON LP.outlet_id=table_1.outlet_id\n", "   AND LP.item_id=table_1.itemid\n", "   Group by 1,2,3,4,5,6,7)\n", "\n", "\n", "SELECT facility,\n", "       outlet_id,\n", "       location_id,\n", "       location_name,\n", "       aisle,\n", "       location_last_updated,\n", "       itemid,\n", "       Expiry_Date,\n", "       skip_expiry,\n", "       CASE\n", "                      WHEN skip_expiry=1 or expiry_date is null THEN 9999\n", "                      WHEN skip_expiry=0\n", "                            AND datediff(days,CURRENT_DATE,(Expiry_Date))<=0 THEN 0\n", "                      WHEN skip_expiry=0\n", "                            AND datediff(days,CURRENT_DATE,(Expiry_Date))>9999 THEN 9999\n", "                      ELSE datediff(days,CURRENT_DATE,(Expiry_Date))\n", "                  END AS days_to_expiry,\n", "        Item_Available_Quantity,\n", "       coalesce (Bad_Inventory_Update,0) as Bad_Inventory_Update,\n", "       coalesce(ItemDemand,0) as ItemDemand,\n", "       coalesce( SellPrice,0) as SellPrice,\n", "       coalesce( LandingPrice,0) as LandingPrice \n", "FROM table2\n", "\"\"\"\n", "\n", "\n", "Query2 = \"\"\" \n", "SELECT outlet_id,\n", "          location_id AS loc_id,\n", "          count(location_name) AS no_of_touches\n", "FROM\n", "(SELECT f.id AS facility_id,\n", "        f.name AS facility_name,\n", "        wo.outlet_id,\n", "        ro.name AS outlet_name,\n", "        \"delta\" AS CHANGE,\n", "        putlist_id,\n", "        picklist_id,\n", "        convert_timezone('Asia/Kolkata',wl.pos_timestamp) AS time_stamp,\n", "        item_location_update_type_id,\n", "        location_id,\n", "        s.location_name\n", "FROM lake_warehouse_location.warehouse_item_location_log wl\n", "INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wo ON wl.warehouse_id=wo.warehouse_id\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=wo.outlet_id\n", "INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN lake_warehouse_location.warehouse_storage_location s ON wl.location_id=s.id\n", "        WHERE s.location_type=1\n", "        AND s.storage_type_id IN (1,\n", "                                  2,\n", "                                  6)\n", "        AND s.is_active=1\n", "        AND wo.outlet_id=%(outlet_id)s\n", "AND date(convert_timezone('Asia/Kolkata',wl.pos_timestamp)) >=CURRENT_DATE-30 --  AND wl.item_location_update_type_id IN (1, 2)\n", " )\n", "   GROUP BY 1,\n", "            2\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Secondary Query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AudQuerySec = \"\"\"\n", "WITH table_1 AS\n", "  (SELECT DISTINCT wl.id AS location_id,\n", "                   wl.location_name,\n", "                   split_part(wl.location_name,'-',2) AS Aisle,\n", "                   wl.updated_at AS location_last_updated,\n", "                   REPLACE(REPLACE(fc.name,'Super Store',''),' - Warehouse','') AS Facility,\n", "                   wom.outlet_id,\n", "                   wil.item_id AS itemid,\n", "                   sum(wil.quantity) Qty,\n", "                   min(wil.batch) AS ExpiryDate\n", "   FROM lake_warehouse_location.warehouse_storage_location wl\n", "   LEFT JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wl.warehouse_id=wom.warehouse_id\n", "   LEFT JOIN lake_warehouse_location.warehouse_item_location wil ON wl.id=wil.location_id\n", "   LEFT JOIN lake_retail.console_outlet co ON co.id=wom.outlet_id\n", "   LEFT JOIN lake_crates.facility fc ON co.facility_id = fc.id\n", "   WHERE wl.is_active=TRUE\n", "     AND wl.quantity >0\n", "     AND wl.location_type=2\n", "     AND wl.storage_type_id IN (1,\n", "                                2,\n", "                                6)\n", "     AND wom.outlet_id=%(outlet_id)s                           \n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7\n", "   HAVING Qty>0),\n", "     Demand AS\n", "  (SELECT pp.item_id,\n", "          ord_det.outlet,\n", "          sum(prod_details.quantity) AS ItemDemand,\n", "          avg(prod_details.selling_price) AS SellPrice\n", "  FROM lake_ims.ims_order_details ord_det\n", "   LEFT JOIN lake_pos.view_pos_invoice inv ON inv.grofers_order_id=ord_det.order_id\n", "   LEFT JOIN lake_pos.pos_invoice_product_details prod_details ON inv.id=prod_details.invoice_id\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=prod_details.variant_id\n", "   WHERE ord_det.outlet=%(outlet_id)s\n", "     AND date(pos_timestamp)>= CURRENT_DATE-30\n", "     AND store_offer_id IS NULL\n", "     AND inv.invoice_type_id=1\n", "   GROUP BY 1,\n", "            2),\n", "     Bad_Inventory AS\n", "  (SELECT pp.item_id,\n", "          ibiul.outlet_id,\n", "          sum(ibiul.\"delta\") AS qty_lost\n", "   FROM lake_ims.ims_bad_inventory_update_log ibiul\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=ibiul.variant_id\n", "    WHERE date(convert_timezone('Asia/Kolkata',ibiul.created_at)) >=CURRENT_DATE-30\n", "     AND ibiul.outlet_id=%(outlet_id)s\n", "     AND ibiul.inventory_update_type_id IN (11,\n", "                                            12,\n", "                                            13,\n", "                                            64)\n", "   GROUP BY 1,\n", "            2),\n", "     Landing_Price AS\n", "  (SELECT outlet_id,\n", "          pp.item_id,\n", "          avg(landing_price) AS landingprice\n", "  FROM lake_ims.ims_inventory_landing_price lp\n", "   LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=lp.variant_id\n", "  WHERE outlet_id=%(outlet_id)s\n", "   GROUP BY 1,\n", "            2),\n", "     table2 AS\n", "  (SELECT          table_1.facility,\n", "                   table_1.outlet_id,\n", "                   table_1.location_id,\n", "                   table_1.location_name,\n", "                   table_1.aisle,\n", "                   table_1.location_last_updated,\n", "                   table_1.itemid,\n", "                   min(table_1.ExpiryDate) as Expiry_Date,\n", "                   min(pp.skip_expiry::int) AS skip_expiry,\n", "                    sum(table_1.Qty) AS Item_Available_Quantity,\n", "                    sum(Bad_Inventory.qty_lost) AS Bad_Inventory_Update,\n", "                    sum(Demand.ItemDemand) AS ItemDemand,\n", "                    avg(Demand.SellPrice) AS SellPrice,\n", "                    avg(LP.landingprice) as LandingPrice\n", "   FROM table_1\n", " LEFT JOIN lake_rpc.product_product  pp ON table_1.itemid=pp.item_id\n", "--   LEFT JOIN consumer.rpc_product_product_category ppc ON ppc.product_id=pp.id\n", "--   LEFT JOIN consumer.rpc_product_category cat_name ON ppc.category_id=cat_name.id\n", "   LEFT JOIN Bad_Inventory ON Bad_Inventory.outlet_id=table_1.outlet_id\n", "   AND Bad_Inventory.item_id=table_1.itemid\n", "   LEFT JOIN Demand ON Demand.outlet=table_1.outlet_id\n", "   AND Demand.item_id=table_1.itemid\n", "   LEFT JOIN Landing_Price LP ON LP.outlet_id=table_1.outlet_id\n", "   AND LP.item_id=table_1.itemid\n", "   Group by 1,2,3,4,5,6,7)\n", "\n", "\n", "SELECT facility,\n", "       outlet_id,\n", "       location_id,\n", "       location_name,\n", "       aisle,\n", "       location_last_updated,\n", "       itemid,\n", "       Expiry_Date,\n", "       skip_expiry,\n", "       CASE\n", "                      WHEN skip_expiry=1 or expiry_date is null THEN 9999\n", "                      WHEN skip_expiry=0\n", "                            AND datediff(days,CURRENT_DATE,(Expiry_Date))<=0 THEN 0\n", "                      WHEN skip_expiry=0\n", "                            AND datediff(days,CURRENT_DATE,(Expiry_Date))>9999 THEN 9999\n", "                      ELSE datediff(days,CURRENT_DATE,(Expiry_Date))\n", "                  END AS days_to_expiry,\n", "        Item_Available_Quantity,\n", "       coalesce (Bad_Inventory_Update,0) as Bad_Inventory_Update,\n", "       coalesce(ItemDemand,0) as ItemDemand,\n", "       coalesce( SellPrice,0) as SellPrice,\n", "       coalesce( LandingPrice,0) as LandingPrice \n", "FROM table2\n", "\"\"\"\n", "\n", "\n", "Query2Sec = \"\"\" \n", "SELECT outlet_id,\n", "          location_id AS loc_id,\n", "          count(location_name) AS no_of_touches\n", "FROM\n", "(SELECT f.id AS facility_id,\n", "        f.name AS facility_name,\n", "        wo.outlet_id,\n", "        ro.name AS outlet_name,\n", "        \"delta\" AS CHANGE,\n", "        putlist_id,\n", "        picklist_id,\n", "        convert_timezone('Asia/Kolkata',wl.pos_timestamp) AS time_stamp,\n", "        item_location_update_type_id,\n", "        location_id,\n", "        s.location_name\n", "FROM lake_warehouse_location.warehouse_item_location_log wl\n", "INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wo ON wl.warehouse_id=wo.warehouse_id\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=wo.outlet_id\n", "INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN lake_warehouse_location.warehouse_storage_location s ON wl.location_id=s.id\n", "        WHERE s.location_type=2\n", "        AND s.storage_type_id IN (1,\n", "                                  2,\n", "                                  6)\n", "        AND s.is_active=1\n", "        AND wo.outlet_id=%(outlet_id)s\n", "AND date(convert_timezone('Asia/Kolkata',wl.pos_timestamp)) >=CURRENT_DATE-30 --  AND wl.item_location_update_type_id IN (1, 2)\n", " )\n", "   GROUP BY 1,\n", "            2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"10QlvL_GeRuU7PCysssGRY1jHxhUY6TiLSCoCBC_4nDo\"\n", "outlets = pb.from_sheets(sheet_id, \"Stock Take Updated\")\n", "outlets = outlets[[\"outlet_id\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"outlet_id\"], inplace=True)\n", "\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "email_list = pb.from_sheets(sheet_id, \"Stock Take Updated\")\n", "email_list = email_list[[\"Group Mail Id\", \"outlet_id\"]]\n", "email_list[\"Group Mail Id\"] = email_list[\"Group Mail Id\"].astype(str)\n", "# email_list[\"outlet_id\"]=email_list[\"outlet_id\"].astype(int)\n", "outlets = list(outlets.outlet_id.unique())\n", "# outlets\n", "# email_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Primary = read_df_Primary(\"AuditsThisCycle\" + str(100) + \".csv\")\n", "# Secondary = read_df_Secondary(\"AuditsThisCycleSecondary\" + str(100) + \".csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Primary.truncate(-1,-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for outlet_id in outlets:\n", "#     Primary=Primary.truncate(-1,-1)\n", "#     Secondary=Secondary.truncate(-1,-1)\n", "#     save_df_Primary(Primary, \"AuditsThisCycle\" + str(outlet_id) + \".csv\")\n", "#     save_df_Secondary(Secondary, \"AuditsThisCycleSecondary\" + str(outlet_id) + \".csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loop for Primary and Secondary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in outlets:\n", "    try:\n", "        message_text = \"Working for outlet_id - \" + str(outlet_id)\n", "        print(message_text)\n", "        #### Primary Locations\n", "        AuditsThisCycle = read_df_Primary(\"AuditsThisCycle\" + str(outlet_id) + \".csv\")\n", "        CycleDay = len(AuditsThisCycle[\"auditdate\"].unique())\n", "\n", "        if CycleDay > 59:\n", "            CycleDay = 0\n", "\n", "        My_Raw_Data = pd.read_sql_query(\n", "            sql=AudQuery, con=redshift, params={\"outlet_id\": int(outlet_id)}\n", "        )\n", "        Touches = pd.read_sql_query(\n", "            sql=Query2, con=redshift, params={\"outlet_id\": int(outlet_id)}\n", "        )\n", "\n", "        My_Raw_Data.fillna(0, inplace=True)\n", "        My_Raw_Data[\"location_id\"] = My_Raw_Data[\"location_id\"].astype(int)\n", "        Touches[\"loc_id\"] = Touches[\"loc_id\"].astype(int)\n", "        My_Raw_Data = pd.merge(\n", "            My_Raw_Data, <PERSON><PERSON>, how=\"left\", left_on=\"location_id\", right_on=\"loc_id\"\n", "        )\n", "\n", "        My_Raw_Data[\"itemvalue\"] = (\n", "            My_Raw_Data[\"item_available_quantity\"] * My_Raw_Data[\"landingprice\"]\n", "        )\n", "        My_Raw_Data[\"gmv\"] = My_Raw_Data[\"itemdemand\"] * My_Raw_Data[\"sellprice\"]\n", "        My_Raw_Data[\"badinventoryvalue\"] = (\n", "            My_Raw_Data[\"bad_inventory_update\"] * My_Raw_Data[\"landingprice\"]\n", "        )\n", "\n", "        My_Raw_Data_Grouped = My_Raw_Data.groupby(\n", "            [\"facility\", \"outlet_id_x\", \"location_id\", \"location_name\", \"aisle\"],\n", "            as_index=False,\n", "        )\n", "\n", "        AuditScoring_Location = My_Raw_Data_Grouped.agg(\n", "            {\n", "                \"item_available_quantity\": sum,\n", "                \"bad_inventory_update\": sum,\n", "                \"itemdemand\": sum,\n", "                \"days_to_expiry\": min,\n", "                \"itemvalue\": sum,\n", "                \"gmv\": sum,\n", "                \"badinventoryvalue\": sum,\n", "                \"no_of_touches\": max,\n", "            }\n", "        )\n", "\n", "        AuditScoring_Location = AuditScoring_Location.sort_values(\n", "            \"days_to_expiry\", ascending=True\n", "        )\n", "\n", "        AuditScoring_Location_P1 = AuditScoring_Location.filter(\n", "            [\"facility\", \"outlet_id_x\", \"location_id\", \"location_name\", \"aisle\"]\n", "        )\n", "        AuditScoring_Location_P2 = AuditScoring_Location.filter(\n", "            [\n", "                \"item_available_quantity\",\n", "                \"bad_inventory_update\",\n", "                \"itemdemand\",\n", "                \"itemvalue\",\n", "                \"gmv\",\n", "                \"badinventoryvalue\",\n", "                \"no_of_touches\",\n", "            ]\n", "        )\n", "        AuditScoring_Location_P3 = AuditScoring_Location.filter([\"days_to_expiry\"])\n", "\n", "        Scaler1 = StandardScaler()\n", "        Scaler2 = MinMaxScaler()\n", "\n", "        X1 = Scaler1.fit_transform(AuditScoring_Location_P2)\n", "\n", "        AuditScoring_Location_P2 = pd.DataFrame(\n", "            X1, columns=AuditScoring_Location_P2.columns\n", "        )\n", "\n", "        X2 = Scaler2.fit_transform(AuditScoring_Location_P3)\n", "        X2 = 1 - X2\n", "        AuditScoring_Location_P3 = pd.DataFrame(\n", "            X2, columns=AuditScoring_Location_P3.columns\n", "        )\n", "\n", "        AuditScoring_Location_Normalized = pd.concat(\n", "            [\n", "                AuditScoring_Location_P1,\n", "                AuditScoring_Location_P2,\n", "                AuditScoring_Location_P3,\n", "            ],\n", "            axis=1,\n", "        )\n", "\n", "        AuditScoring_Location_Normalized[\"weightedscore\"] = (\n", "            AuditScoring_Location_Normalized[\"item_available_quantity\"] * Weights[0]\n", "            + AuditScoring_Location_Normalized[\"bad_inventory_update\"] * Weights[1]\n", "            + AuditScoring_Location_Normalized[\"itemdemand\"] * Weights[2]\n", "            + AuditScoring_Location_Normalized[\"itemvalue\"] * Weights[3]\n", "            + AuditScoring_Location_Normalized[\"gmv\"] * Weights[4]\n", "            + AuditScoring_Location_Normalized[\"badinventoryvalue\"] * Weights[5]\n", "            + AuditScoring_Location_Normalized[\"no_of_touches\"] * Weights[6]\n", "            + AuditScoring_Location_Normalized[\"days_to_expiry\"] * Weights[7]\n", "        )\n", "\n", "        AuditScoring_Location_Normalized_final = (\n", "            AuditScoring_Location_Normalized.filter(\n", "                [\n", "                    \"facility\",\n", "                    \"outlet_id_x\",\n", "                    \"location_id\",\n", "                    \"location_name\",\n", "                    \"aisle\",\n", "                    \"weightedscore\",\n", "                ]\n", "            )\n", "        )\n", "\n", "        AuditScoring_Location_Normalized_final[\n", "            \"rankfinal\"\n", "        ] = AuditScoring_Location_Normalized_final[\"weightedscore\"].rank(method=\"min\")\n", "        AuditScoring_Location_Normalized_final.sort_values(\n", "            \"rankfinal\", ascending=False, inplace=True\n", "        )\n", "\n", "        TotalPrimaryLocations = len(\n", "            AuditScoring_Location_Normalized_final[\"location_id\"]\n", "        )\n", "        AvgLocationsPerDay = TotalPrimaryLocations / (60)\n", "        AvgLocationsPerDay = round(AvgLocationsPerDay, 0)\n", "        Num = int(AvgLocationsPerDay)\n", "\n", "        if CycleDay > 0:\n", "            FilterAuditedLocations = ~AuditScoring_Location_Normalized_final[\n", "                \"location_id\"\n", "            ].isin(AuditsThisCycle[\"location_id\"])\n", "            AuditScoring_Location_Normalized_final = (\n", "                AuditScoring_Location_Normalized_final[FilterAuditedLocations]\n", "            )\n", "\n", "        TotalPrimaryLocations = len(\n", "            AuditScoring_Location_Normalized_final[\"location_id\"]\n", "        )\n", "        AvgLocationsPerDay = TotalPrimaryLocations / (60 - CycleDay)\n", "        AvgLocationsPerDay = round(AvgLocationsPerDay, 0)\n", "        Num = int(AvgLocationsPerDay)\n", "\n", "        AuditLocations = AuditScoring_Location_Normalized_final[0:Num].sort_values(\n", "            \"aisle\"\n", "        )\n", "\n", "        if CycleDay == 0:\n", "            Threshold1 = (\n", "                AuditScoring_Location_Normalized_final.weightedscore.mean()\n", "                + AuditScoring_Location_Normalized_final.weightedscore.std()\n", "            )\n", "            Threshold2 = (\n", "                AuditScoring_Location_Normalized_final.weightedscore.mean()\n", "                + 2 * AuditScoring_Location_Normalized_final.weightedscore.std()\n", "            )\n", "            Threshold3 = (\n", "                AuditScoring_Location_Normalized_final.weightedscore.mean()\n", "                + 3 * AuditScoring_Location_Normalized_final.weightedscore.std()\n", "            )\n", "            Thresholds = pd.DataFrame([Threshold1, Threshold2, Threshold3])\n", "            Thresholds[\"outlet_id\"] = outlet_id\n", "            Thresholds.columns = [\"thresh\", \"outlet_id\"]\n", "            save_df_Primary(Thresholds, \"ThresholdsPrimary\" + str(outlet_id) + \".csv\")\n", "        else:\n", "            Thresholds = read_df_Primary(\"ThresholdsPrimary\" + str(outlet_id) + \".csv\")\n", "            Threshold1 = Thresholds[\"thresh\"][0]\n", "            Threshold2 = Thresholds[\"thresh\"][1]\n", "            Threshold3 = Thresholds[\"thresh\"][2]\n", "\n", "        AuditLocations[\"frequency\"] = list(\n", "            map(SetFrequency, AuditLocations[\"weightedscore\"])\n", "        )\n", "        AuditLocations[\"auditdate\"] = (today).strftime(\"%Y-%m-%d\")\n", "        AuditLocations.reset_index(inplace=True)\n", "\n", "        if CycleDay == 0:\n", "            AuditsThisCycle = AuditLocations.truncate(-1, -1)\n", "\n", "            AuditsThisCycle = pd.concat([AuditsThisCycle, AuditLocations], axis=0)\n", "            AuditsThisCycle.drop(\"index\", inplace=True, axis=1)\n", "            save_df_Primary(\n", "                AuditsThisCycle, \"AuditsThisCycle\" + str(outlet_id) + \".csv\"\n", "            )\n", "\n", "        if CycleDay > 15:\n", "\n", "            print(\"Do Something About the Data with Frequency more than 1\")\n", "            Repeats = AuditsThisCycle.frequency > 1\n", "            AuditsThisCycle_Repeats = AuditsThisCycle[Repeats]\n", "            AuditsThisCycle_NotRepeats = AuditsThisCycle[~Repeats]\n", "            AuditsThisCycle_Repeats.sort_values(\n", "                \"weightedscore\", ascending=False, inplace=True\n", "            )\n", "            AuditsThisCycle_Repeats.reset_index(inplace=True, drop=True)\n", "            RepeatsPerDay = int(\n", "                round(len(AuditsThisCycle_Repeats[\"location_id\"]) / 30, 0)\n", "            )\n", "\n", "            import random\n", "\n", "            Indeces = random.choices(\n", "                list(AuditsThisCycle_Repeats.index), k=RepeatsPerDay\n", "            )\n", "            TodayRepeats = AuditsThisCycle_Repeats.iloc[Indeces]\n", "\n", "            def FrequencyUpdate(Data):\n", "                Updated_Index = Data.index.isin(Indeces)\n", "                NotUpdated = Data.iloc[~Updated_Index]\n", "                Updated = Data.iloc[Updated_Index]\n", "                Updated[\"frequency\"] = Updated[\"frequency\"] - 1\n", "                FinalData = pd.concat([Updated, NotUpdated], axis=0)\n", "                FinalData.sort_values(\"weightedscore\", ascending=False, inplace=True)\n", "                return FinalData\n", "\n", "            AuditsThisCycle_Repeats = FrequencyUpdate(AuditsThisCycle_Repeats)\n", "            AuditsThisCycle = pd.concat(\n", "                [AuditsThisCycle_Repeats, AuditsThisCycle_NotRepeats], axis=0\n", "            )\n", "            save_df_Primary(\n", "                AuditsThisCycle, \"AuditsThisCycle\" + str(outlet_id) + \".csv\"\n", "            )\n", "            AuditLocations.drop(\"index\", inplace=True, axis=1)\n", "            AuditLocations = pd.concat([AuditLocations, TodayRepeats], axis=0)\n", "            AuditLocations.sort_values(\"aisle\", inplace=True)\n", "            AuditLocations[\"auditdate\"] = (today).strftime(\"%Y-%m-%d\")\n", "\n", "        AuditLocations.to_csv(\n", "            \"/tmp/StockTakeAuditsPrimary_\"\n", "            + str(outlet_id)\n", "            + \"_\"\n", "            + today.strftime(\"%Y-%m-%d\")\n", "            + \".csv\"\n", "        )\n", "\n", "        save_df_Primary(AuditLocations, \"AuditsTodayPrimary\" + str(outlet_id) + \".csv\")\n", "\n", "        PrimaryData = AuditLocations\n", "\n", "        PrimaryData[\"Key\"] = (\n", "            PrimaryData[\"auditdate\"].astype(str)\n", "            + \"|\"\n", "            + PrimaryData[\"location_id\"].astype(str)\n", "        )\n", "\n", "        PrimaryData = PrimaryData[\n", "            [\n", "                \"aisle\",\n", "                \"auditdate\",\n", "                \"facility\",\n", "                \"frequency\",\n", "                \"location_id\",\n", "                \"location_name\",\n", "                \"outlet_id_x\",\n", "                \"rankfinal\",\n", "                \"weightedscore\",\n", "                \"Key\",\n", "                \"index\",\n", "            ]\n", "        ]\n", "\n", "        column_dtypes_primary = redshift_schema(PrimaryData)\n", "\n", "        kwargs = {\n", "            \"schema_name\": \"metrics\",\n", "            \"table_name\": \"CyclicAuditPrimary_Suggested\",\n", "            \"column_dtypes\": column_dtypes_primary,\n", "            \"primary_key\": [\"Key\"],\n", "            \"sortkey\": [\"outlet_id_x\"],\n", "            \"incremental_key\": \"auditdate\",\n", "            \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "            \"table_description\": \"Stores suggested primary locations for cyclic audit\",\n", "        }\n", "        pb.to_redshift(PrimaryData, **kwargs)\n", "\n", "        #### Secondary Locations\n", "\n", "        if outlet_id not in [337, 475, 343, 292, 512, 491, 537]:\n", "\n", "            AuditsThisCycleSecondary = read_df_Secondary(\n", "                \"AuditsThisCycleSecondary\" + str(outlet_id) + \".csv\"\n", "            )\n", "            CycleDay = len(AuditsThisCycleSecondary[\"auditdate\"].unique())\n", "\n", "            if CycleDay > 59:\n", "                CycleDay = 0\n", "\n", "            My_Raw_Data = pd.read_sql_query(\n", "                sql=AudQuerySec, con=redshift, params={\"outlet_id\": int(outlet_id)}\n", "            )\n", "            Touches = pd.read_sql_query(\n", "                sql=Query2Sec, con=redshift, params={\"outlet_id\": int(outlet_id)}\n", "            )\n", "\n", "            My_Raw_Data.fillna(0, inplace=True)\n", "            My_Raw_Data[\"location_id\"] = My_Raw_Data[\"location_id\"].astype(int)\n", "            Touches[\"loc_id\"] = Touches[\"loc_id\"].astype(int)\n", "            My_Raw_Data = pd.merge(\n", "                My_Raw_Data,\n", "                Touches,\n", "                how=\"left\",\n", "                left_on=\"location_id\",\n", "                right_on=\"loc_id\",\n", "            )\n", "\n", "            My_Raw_Data[\"itemvalue\"] = (\n", "                My_Raw_Data[\"item_available_quantity\"] * My_Raw_Data[\"landingprice\"]\n", "            )\n", "            My_Raw_Data[\"gmv\"] = My_Raw_Data[\"itemdemand\"] * My_Raw_Data[\"sellprice\"]\n", "            My_Raw_Data[\"badinventoryvalue\"] = (\n", "                My_Raw_Data[\"bad_inventory_update\"] * My_Raw_Data[\"landingprice\"]\n", "            )\n", "\n", "            My_Raw_Data_Grouped = My_Raw_Data.groupby(\n", "                [\n", "                    \"facility\",\n", "                    \"outlet_id_x\",\n", "                    \"location_id\",\n", "                    \"location_name\",\n", "                    \"aisle\",\n", "                    \"itemid\",\n", "                ],\n", "                as_index=False,\n", "            )\n", "\n", "            AuditScoring_Location = My_Raw_Data_Grouped.agg(\n", "                {\n", "                    \"item_available_quantity\": sum,\n", "                    \"bad_inventory_update\": sum,\n", "                    \"itemdemand\": sum,\n", "                    \"days_to_expiry\": min,\n", "                    \"itemvalue\": sum,\n", "                    \"gmv\": sum,\n", "                    \"badinventoryvalue\": sum,\n", "                    \"no_of_touches\": sum,\n", "                }\n", "            )\n", "\n", "            AuditScoring_Location = AuditScoring_Location.sort_values(\n", "                \"days_to_expiry\", ascending=True\n", "            )\n", "\n", "            AuditScoring_Location_P1 = AuditScoring_Location.filter(\n", "                [\n", "                    \"facility\",\n", "                    \"outlet_id_x\",\n", "                    \"location_id\",\n", "                    \"location_name\",\n", "                    \"aisle\",\n", "                    \"itemid\",\n", "                ]\n", "            )\n", "            AuditScoring_Location_P2 = AuditScoring_Location.filter(\n", "                [\n", "                    \"item_available_quantity\",\n", "                    \"bad_inventory_update\",\n", "                    \"itemdemand\",\n", "                    \"itemvalue\",\n", "                    \"gmv\",\n", "                    \"badinventoryvalue\",\n", "                    \"no_of_touches\",\n", "                ]\n", "            )\n", "            AuditScoring_Location_P3 = AuditScoring_Location.filter([\"days_to_expiry\"])\n", "\n", "            Scaler1 = StandardScaler()\n", "            Scaler2 = MinMaxScaler()\n", "\n", "            X1 = Scaler1.fit_transform(AuditScoring_Location_P2)\n", "\n", "            AuditScoring_Location_P2 = pd.DataFrame(\n", "                X1, columns=AuditScoring_Location_P2.columns\n", "            )\n", "\n", "            X2 = Scaler2.fit_transform(AuditScoring_Location_P3)\n", "            X2 = 1 - X2\n", "            AuditScoring_Location_P3 = pd.DataFrame(\n", "                X2, columns=AuditScoring_Location_P3.columns\n", "            )\n", "\n", "            AuditScoring_Location_Normalized = pd.concat(\n", "                [\n", "                    AuditScoring_Location_P1,\n", "                    AuditScoring_Location_P2,\n", "                    AuditScoring_Location_P3,\n", "                ],\n", "                axis=1,\n", "            )\n", "\n", "            AuditScoring_Location_Normalized[\"weightedscore\"] = (\n", "                AuditScoring_Location_Normalized[\"item_available_quantity\"] * Weights[0]\n", "                + AuditScoring_Location_Normalized[\"bad_inventory_update\"] * Weights[1]\n", "                + AuditScoring_Location_Normalized[\"itemdemand\"] * Weights[2]\n", "                + AuditScoring_Location_Normalized[\"itemvalue\"] * Weights[3]\n", "                + AuditScoring_Location_Normalized[\"gmv\"] * Weights[4]\n", "                + AuditScoring_Location_Normalized[\"badinventoryvalue\"] * Weights[5]\n", "                + AuditScoring_Location_Normalized[\"no_of_touches\"] * Weights[6]\n", "                + AuditScoring_Location_Normalized[\"days_to_expiry\"] * Weights[7]\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final = (\n", "                AuditScoring_Location_Normalized.filter(\n", "                    [\n", "                        \"facility\",\n", "                        \"outlet_id_x\",\n", "                        \"location_id\",\n", "                        \"location_name\",\n", "                        \"aisle\",\n", "                        \"itemid\",\n", "                        \"weightedscore\",\n", "                    ]\n", "                )\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final[\n", "                \"rankfinal\"\n", "            ] = AuditScoring_Location_Normalized_final[\"weightedscore\"].rank(\n", "                method=\"min\"\n", "            )\n", "            AuditScoring_Location_Normalized_final.sort_values(\n", "                \"rankfinal\", ascending=False, inplace=True\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final[\"uniquekey\"] = (\n", "                AuditScoring_Location_Normalized_final[\"location_id\"].astype(str)\n", "                + \"|\"\n", "                + AuditScoring_Location_Normalized_final[\"itemid\"].astype(str)\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final_Original = (\n", "                AuditScoring_Location_Normalized_final\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final_Item = (\n", "                AuditScoring_Location_Normalized_final.groupby(\"itemid\", as_index=False)\n", "                .agg({\"weightedscore\": \"mean\"})\n", "                .sort_values(\"weightedscore\", ascending=False)\n", "            )\n", "\n", "            if CycleDay == 0:\n", "                Threshold1 = (\n", "                    AuditScoring_Location_Normalized_final_Item.weightedscore.mean()\n", "                    + AuditScoring_Location_Normalized_final_Item.weightedscore.std()\n", "                )\n", "                Threshold2 = (\n", "                    AuditScoring_Location_Normalized_final_Item.weightedscore.mean()\n", "                    + 2\n", "                    * AuditScoring_Location_Normalized_final_Item.weightedscore.std()\n", "                )\n", "                Threshold3 = (\n", "                    AuditScoring_Location_Normalized_final_Item.weightedscore.mean()\n", "                    + 3\n", "                    * AuditScoring_Location_Normalized_final_Item.weightedscore.std()\n", "                )\n", "                Thresholds = pd.DataFrame([Threshold1, Threshold2, Threshold3])\n", "                Thresholds[\"outlet_id\"] = outlet_id\n", "                Thresholds.columns = [\"thresh\", \"outlet_id\"]\n", "                save_df_Secondary(\n", "                    <PERSON><PERSON><PERSON><PERSON><PERSON>, \"ThresholdsSecondary\" + str(outlet_id) + \".csv\"\n", "                )\n", "            else:\n", "                Thresholds = read_df_Secondary(\n", "                    \"ThresholdsSecondary\" + str(outlet_id) + \".csv\"\n", "                )\n", "                Threshold1 = Thresholds[\"thresh\"][0]\n", "                Threshold2 = Thresholds[\"thresh\"][1]\n", "                Threshold3 = Thresholds[\"thresh\"][2]\n", "\n", "            if CycleDay > 0:\n", "                FilterAuditedLocations = ~AuditScoring_Location_Normalized_final[\n", "                    \"uniquekey\"\n", "                ].isin(AuditsThisCycleSecondary[\"uniquekey\"])\n", "                AuditScoring_Location_Normalized_final = (\n", "                    AuditScoring_Location_Normalized_final[FilterAuditedLocations]\n", "                )\n", "\n", "            TotalSecondaryItems = len(\n", "                AuditScoring_Location_Normalized_final[\"itemid\"].unique()\n", "            )\n", "            AvgItemsPerDay = TotalSecondaryItems / (60 - CycleDay)\n", "            AvgItemsPerDay = round(AvgItemsPerDay, 0)\n", "            Num = int(AvgItemsPerDay)\n", "            AuditScoring_Location_Normalized_final_Item = (\n", "                AuditScoring_Location_Normalized_final.groupby(\"itemid\", as_index=False)\n", "                .agg({\"weightedscore\": \"mean\"})\n", "                .sort_values(\"weightedscore\", ascending=False)\n", "            )\n", "\n", "            AuditScoring_Location_Normalized_final_Item[\"frequency\"] = list(\n", "                map(\n", "                    SetFrequency,\n", "                    AuditScoring_Location_Normalized_final_Item[\"weightedscore\"],\n", "                )\n", "            )\n", "\n", "            ListofItemsToday = AuditScoring_Location_Normalized_final_Item[\"itemid\"][\n", "                0:<PERSON><PERSON>\n", "            ]\n", "\n", "            AuditLocations = AuditScoring_Location_Normalized_final[\n", "                AuditScoring_Location_Normalized_final[\"itemid\"].isin(ListofItemsToday)\n", "            ]\n", "\n", "            AuditLocations.sort_values([\"itemid\", \"aisle\"], inplace=True)\n", "\n", "            AuditLocations[\"auditdate\"] = (today).strftime(\"%Y-%m-%d\")\n", "\n", "            AuditLocations = pd.merge(\n", "                AuditLocations,\n", "                AuditScoring_Location_Normalized_final_Item[[\"itemid\", \"frequency\"]],\n", "                on=\"itemid\",\n", "                how=\"left\",\n", "            )\n", "\n", "            AuditsThisCycleSecondary = pd.concat(\n", "                [AuditsThisCycleSecondary, AuditLocations], axis=0\n", "            )\n", "\n", "            save_df_Secondary(\n", "                AuditsThisCycleSecondary,\n", "                \"AuditsThisCycleSecondary\" + str(outlet_id) + \".csv\",\n", "            )\n", "\n", "            AuditLocations.drop(\"frequency\", inplace=True, axis=1)\n", "\n", "            if CycleDay > 15:\n", "\n", "                print(\"Do Something About the Data with Frequency more than 2\")\n", "\n", "                Repeats = AuditsThisCycleSecondary.frequency > 2\n", "\n", "                AuditsThisCycle_Repeats = AuditsThisCycleSecondary[Repeats]\n", "                AuditsThisCycle_NotRepeats = AuditsThisCycleSecondary[~Repeats]\n", "\n", "                AuditScoring_Location_Normalized_final_Item_Repeats = (\n", "                    AuditsThisCycle_Repeats.groupby(\"itemid\", as_index=False)\n", "                    .agg({\"weightedscore\": \"mean\"})\n", "                    .sort_values(\"weightedscore\", ascending=False)\n", "                )\n", "\n", "                AuditScoring_Location_Normalized_final_Item_Repeats.reset_index(\n", "                    inplace=True, drop=True\n", "                )\n", "\n", "                RepeatedItems = (\n", "                    AuditScoring_Location_Normalized_final_Item_Repeats.itemid\n", "                )\n", "\n", "                Totalrepeatitems = int(round(len(RepeatedItems) / 30, 0))\n", "\n", "                import random\n", "\n", "                Indeces = random.choices(\n", "                    list(AuditsThisCycle_Repeats.index), k=Totalrepeatitems\n", "                )\n", "\n", "                TodayRepeats = RepeatedItems[Indeces]\n", "\n", "                ItemLocsRepeats = AuditScoring_Location_Normalized_final_Original[\n", "                    AuditScoring_Location_Normalized_final_Original.itemid.isin(\n", "                        TodayRepeats\n", "                    )\n", "                ]\n", "\n", "                ItemLocsRepeats.sort_values([\"itemid\", \"aisle\"], inplace=True)\n", "\n", "                ItemLocsRepeats[\"auditdate\"] = (today).strftime(\"%Y-%m-%d\")\n", "\n", "                ItemLocsRepeats.reset_index(inplace=True, drop=True)\n", "\n", "                AuditLocations = pd.concat([AuditLocations, ItemLocsRepeats], axis=0)\n", "\n", "                AuditLocations.sort_values([\"itemid\", \"aisle\"], inplace=True)\n", "\n", "                def FrequencyUpdate(Data):\n", "                    Updated_Index = Data.itemid.isin(TodayRepeats)\n", "                    NotUpdated = Data[~Updated_Index]\n", "                    Updated = Data[Updated_Index]\n", "                    Updated[\"frequency\"] = Updated[\"frequency\"] - 1\n", "                    FinalData = pd.concat([Updated, NotUpdated], axis=0)\n", "                    return FinalData\n", "\n", "                AuditsThisCycle_Repeats = FrequencyUpdate(AuditsThisCycle_Repeats)\n", "\n", "                AuditsThisCycleSecondary = pd.concat(\n", "                    [AuditsThisCycle_Repeats, AuditsThisCycle_NotRepeats], axis=0\n", "                )\n", "\n", "                save_df_Secondary(\n", "                    AuditsThisCycleSecondary,\n", "                    \"AuditsThisCycleSecondary\" + str(outlet_id) + \".csv\",\n", "                )\n", "\n", "            AuditLocations.to_csv(\n", "                \"/tmp/StockTakeAuditsSecondary_\"\n", "                + str(outlet_id)\n", "                + \"_\"\n", "                + today.strftime(\"%Y-%m-%d\")\n", "                + \".csv\"\n", "            )\n", "\n", "            save_df_Secondary(\n", "                AuditLocations,\n", "                \"AuditsTodaySecondary\" + str(outlet_id) + \".csv\",\n", "            )\n", "\n", "            SecondaryData = AuditLocations\n", "            SecondaryData[\"Key\"] = (\n", "                SecondaryData[\"auditdate\"].astype(str)\n", "                + \"|\"\n", "                + SecondaryData[\"location_id\"].astype(str)\n", "                + SecondaryData[\"itemid\"].astype(str)\n", "            )\n", "\n", "            SecondaryData = SecondaryData[\n", "                [\n", "                    \"facility\",\n", "                    \"outlet_id_x\",\n", "                    \"location_id\",\n", "                    \"location_name\",\n", "                    \"aisle\",\n", "                    \"itemid\",\n", "                    \"weightedscore\",\n", "                    \"rankfinal\",\n", "                    \"uniquekey\",\n", "                    \"auditdate\",\n", "                    \"Key\",\n", "                ]\n", "            ]\n", "\n", "            column_dtypes_secondary = redshift_schema(SecondaryData)\n", "\n", "            kwargs = {\n", "                \"schema_name\": \"metrics\",\n", "                \"table_name\": \"CyclicAuditSecondary_Suggested\",\n", "                \"column_dtypes\": column_dtypes_secondary,\n", "                \"primary_key\": [\"Key\"],\n", "                \"sortkey\": [\"outlet_id_x\"],\n", "                \"incremental_key\": \"auditdate\",\n", "                \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "                \"table_description\": \"Stores suggested secondary locations for cyclic audit\",\n", "            }\n", "            pb.to_redshift(SecondaryData, **kwargs)\n", "\n", "        else:\n", "            AuditLocations = pd.DataFrame()\n", "            AuditLocations.to_csv(\n", "                \"/tmp/StockTakeAuditsSecondary_\"\n", "                + str(outlet_id)\n", "                + \"_\"\n", "                + today.strftime(\"%Y-%m-%d\")\n", "                + \".csv\"\n", "            )\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        from_email = \"<EMAIL>\"\n", "        subject = (\n", "            \"Stock take task for outlet \"\n", "            + str(outlet_id)\n", "            + \" for \"\n", "            + today.strftime(\"%Y-%m-%d\")\n", "        )\n", "        #         to_email = [\"<EMAIL>\"]\n", "\n", "        to_email = email_list[email_list[\"outlet_id\"] == str(outlet_id)][\n", "            \"Group Mail Id\"\n", "        ].tolist()\n", "\n", "        message_text = \"Please find attached files for today's Audits for Primary and Secondary Locations\"\n", "\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject,\n", "            cc=[\n", "                \"<EMAIL>\" \"<EMAIL>\",\n", "                \"<EMAIL>\",\n", "                \"<EMAIL>\",\n", "            ],\n", "            bcc=[],\n", "            html_content=message_text,\n", "            files=[\n", "                \"/tmp/StockTakeAuditsPrimary_\"\n", "                + str(outlet_id)\n", "                + \"_\"\n", "                + today.strftime(\"%Y-%m-%d\")\n", "                + \".csv\",\n", "                \"/tmp/StockTakeAuditsSecondary_\"\n", "                + str(outlet_id)\n", "                + \"_\"\n", "                + today.strftime(\"%Y-%m-%d\")\n", "                + \".csv\",\n", "            ],\n", "        )\n", "    except:\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        message_text = \"Failed for outlet_id - \" + str(outlet_id)\n", "        print(message_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}