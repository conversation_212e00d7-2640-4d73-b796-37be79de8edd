{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STO Level Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateQuery = \"\"\"\n", "    select isd.sto_id,\n", "            isd.outlet_id,\n", "            isd.merchant_outlet_id,\n", "            co.facility_id,\n", "            co1.name as outlet_name,\n", "            co2.name as merchant_outlet_name,\n", "            f.name as facility_name,\n", "            case \n", "                when co.business_type_id = 1 then 'Grocery'\n", "                when co.business_type_id = 2 then 'Cold'\n", "                when co.business_type_id = 3 then 'Large'\n", "                when co.business_type_id = 4 then 'F&V'\n", "                when co.business_type_id = 5 then 'Infra'\n", "                when co.business_type_id = 6 then 'Liquidation'\n", "                when co.business_type_id = 7 then 'Dark Store - Grofers'\n", "                when co.business_type_id = 8 then 'Grofers Market'\n", "                when co.business_type_id = 9 then 'Dropshipment'\n", "                when co.business_type_id = 10 then 'B2B Offline'\n", "                when co.business_type_id = 11 then 'B2B Franchise'\n", "                when co.business_type_id = 12 then 'B2B_Bulk'\n", "                when co.business_type_id = 13 then 'Marketplace'\n", "                when co.business_type_id = 14 then 'Test'\n", "                when co.business_type_id = 15 then 'Training'\n", "                when co.business_type_id = 16 then 'Hyperlocal'\n", "                when co.business_type_id = 17 then 'City Warehouse'\n", "                else 'Others'\n", "            end as Type_WH,\n", "            date(convert_timezone('Asia/Kolkata',isd.delivery_date)) as delivery_date,\n", "            date(convert_timezone('Asia/Kolkata',isd.created_at)) as created_at_date,\n", "            sum(isi.expected_quantity) as expected_qty,\n", "            sum(isi.reserved_quantity) as reserved_qty,\n", "            sum(isi.billed_quantity) as billed_qty,\n", "            ((sum(billed_quantity)*100.0)/(sum(expected_quantity))) as STO_Billed_FillRate\n", "    from lake_ims.ims_sto_details isd\n", "    join lake_ims.ims_sto_item isi\n", "    on isd.sto_id = isi.sto_id\n", "    join lake_retail.console_merchant cm\n", "    on isd.merchant_id = cm.id\n", "    join lake_retail.console_outlet co\n", "    on cm.outlet_id = co.id\n", "    join lake_crates.facility f\n", "    on co.facility_id = f.id\n", "    join lake_retail.console_outlet co1\n", "    on isd.outlet_id = co1.id\n", "    join lake_retail.console_outlet co2\n", "    on isd.merchant_outlet_id = co2.id\n", "    where date(convert_timezone('Asia/Kolkata',isd.created_at)) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "    group by 1,2,3,4,5,6,7,8,9,10\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateData = pd.read_sql_query(QtyFillRateQuery, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateData.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateData.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QtyFillRateData.columns, QtyFillRateData.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerQuery = \"\"\"\n", "select sto_id,\n", "        count(distinct assigned_to_name) as no_of_pickers\n", "from ims.ims_sto_details isd\n", "join lake_warehouse_location.warehouse_pick_list wpl\n", "on isd.outlet_id = wpl.outlet_id\n", "where date(convert_timezone('Asia/Kolkata',isd.created_at)) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerData = pd.read_sql_query(PickerQ<PERSON><PERSON>, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerData.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerData.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PickerData.columns, PickerData.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Missing_Boxes_Query = \"\"\"\n", "select sto_id,\n", "       source_outlet_id,\n", "       destination_outlet_id,\n", "       date(sto_delivery_date) as delivery_date,\n", "       sum(box_count) as total_boxes,\n", "       sum(dispatched_box_count) as dispatched_boxes,\n", "       ((sum(box_count) - sum(dispatched_box_count))*100.0/sum(box_count)) as percent_missing_boxes\n", "from lake_warehouse_location.dispatch_indent\n", "where date(convert_timezone('Asia/Kolkata',created_at)) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "group by 1,2,3,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Missing_Boxes_data = pd.read_sql_query(Missing_Boxes_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Missing_Boxes_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Missing_Boxes_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Missing_Boxes_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Missing_Boxes_data.columns, Missing_Boxes_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_query = \"\"\"\n", "with \n", "sto_billed_grn_raw as\n", "(\n", "SELECT pi.grofers_order_id as sto_id,\n", "       pipd.invoice_id,\n", "       sto.outlet_id,\n", "       sto.merchant_outlet_id,\n", "        CASE \n", "            WHEN i.inventory_update_type_id in (1,28) THEN  i.\"delta\" \n", "            ELSE 0 \n", "        END AS GRN_Quantity,\n", "        sum(pipd.quantity) as billed_qty\n", "FROM lake_pos.view_pos_invoice  pi\n", "LEFT JOIN lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id \n", "LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "LEFT JOIN \n", "    (\n", "        select * \n", "        from lake_ims.ims_inventory_log \n", "        --where (pos_timestamp) >'2020-08-10' -- and inventory_update_type_id in (1,28)\n", "    ) i  ON pi.invoice_id=i.merchant_invoice_id and pipd.variant_id::varchar=i.variant_id::varchar  \n", "LEFT JOIN lake_pos.pos_invoice_product_lp lp on lp.invoice_id=pi.id and lp.variant_id::varchar=pipd.variant_id::varchar\n", "LEFT JOIN lake_ims.ims_sto_details  sto on sto.sto_id = pi.grofers_order_id\n", "where date(convert_timezone('Asia/Kolkata',sto.created_at)) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "GROUP BY 1,2,3,4,5\n", "),\n", "\n", "sto_billed_grn as\n", "(\n", "select distinct outlet_id , \n", "                merchant_outlet_id,\n", "                sbg.sto_id,\n", "                invoice_id,\n", "                sum(sbg.billed_qty) billed_qty,\n", "                sum(sbg.grn_quantity) grn_quantity\n", "from sto_billed_grn_raw sbg\n", "group by 1,2,3,4\n", ")\n", "\n", "select sto_id,\n", "       outlet_id,\n", "       merchant_outlet_id,\n", "       sum(billed_qty) as billed_qty,\n", "       sum(grn_quantity) as grn_qty,\n", "       ((sum(grn_quantity)*100.0)/NULLIF(sum(billed_qty),0)) AS STO_GRNed_FillRate\n", "from sto_billed_grn\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data = pd.read_sql_query(STO_GRNqty_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged1 = QtyFillRateData.merge(PickerData, how=\"left\", on=[\"sto_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2 = merged1.merge(Missing_Boxes_data, how=\"left\", on=[\"sto_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2 = merged2.drop(\n", "    columns=[\"source_outlet_id\", \"destination_outlet_id\", \"delivery_date_y\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged2.columns, merged2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data[\"sto_id\"] = STO_GRNqty_data[\"sto_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_GRNqty_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3 = merged2.merge(STO_GRNqty_data, how=\"left\", on=[\"sto_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3 = merged3.drop(columns=[\"outlet_id_y\", \"merchant_outlet_id_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3 = merged3.rename(\n", "    columns={\n", "        \"outlet_id_x\": \"outlet_id\",\n", "        \"merchant_outlet_id_x\": \"merchant_outlet_id\",\n", "        \"delivery_date_x\": \"delivery_date\",\n", "        \"billed_qty_x\": \"billed_qty\",\n", "        \"billed_qty_y\": \"billed_qty_pos\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.columns, merged3.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.loc[merged3[\"billed_qty\"] != merged3[\"billed_qty_pos\"]].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.loc[merged3[\"billed_qty\"] == merged3[\"billed_qty_pos\"]].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# merged3.to_csv(\"sto_level_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3[[\"facility_name\", \"type_wh\"]] = merged3[[\"facility_name\", \"type_wh\"]].astype(\n", "    str\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3[[\"total_boxes\", \"dispatched_boxes\"]] = merged3[\n", "    [\"total_boxes\", \"dispatched_boxes\"]\n", "].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged3.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"sto_id\", \"type\": \"int\", \"description\": \"sto_id\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"source outlet_id\"},\n", "    {\n", "        \"name\": \"merchant_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"destination outlet_id\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"sender outlet_name\"},\n", "    {\n", "        \"name\": \"merchant_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"receiver outlet_name\",\n", "    },\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\n", "        \"name\": \"type_wh\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"type of warehouse based on business type id\",\n", "    },\n", "    {\"name\": \"delivery_date\", \"type\": \"date\", \"description\": \"delivery date of sto\"},\n", "    {\"name\": \"created_at_date\", \"type\": \"date\", \"description\": \"created date of sto\"},\n", "    {\n", "        \"name\": \"expected_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"expected quantity (indent)\",\n", "    },\n", "    {\n", "        \"name\": \"reserved_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"reserved quantity (reserved by IMS)\",\n", "    },\n", "    {\"name\": \"billed_qty\", \"type\": \"int\", \"description\": \"billed quantity\"},\n", "    {\n", "        \"name\": \"sto_billed_fillrate\",\n", "        \"type\": \"float\",\n", "        \"description\": \"STO Billed Fill Rate (Expected Qty Vs Billed)\",\n", "    },\n", "    {\"name\": \"no_of_pickers\", \"type\": \"int\", \"description\": \"Number of pickers\"},\n", "    {\"name\": \"total_boxes\", \"type\": \"float\", \"description\": \"Total boxes\"},\n", "    {\"name\": \"dispatched_boxes\", \"type\": \"float\", \"description\": \"Dispatched boxes\"},\n", "    {\n", "        \"name\": \"percent_missing_boxes\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Percent of Missing boxes\",\n", "    },\n", "    {\"name\": \"billed_qty_pos\", \"type\": \"float\", \"description\": \"Billed quantityt\"},\n", "    {\n", "        \"name\": \"grn_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Quantity GRNed (at destination)\",\n", "    },\n", "    {\n", "        \"name\": \"sto_grned_fillrate\",\n", "        \"type\": \"float\",\n", "        \"description\": \"STO GRNed Fill Rate (Billed at Source Vs GRNed at Destination)\",\n", "    },\n", "]\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"sto_details_b2b_dashboard\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"sto_id\"],\n", "    \"sortkey\": [\"sto_id\", \"outlet_id\", \"merchant_outlet_id\", \"delivery_date\"],\n", "    \"incremental_key\": \"delivery_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This tables maps all the metrics associated with sto for B2B\",\n", "}\n", "pb.to_redshift(merged3, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Outlet level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MHE_query = \"\"\"\n", "# select distinct outlet_id,\n", "#         co.name as outlet_name,\n", "#         count(distinct mhe_id) as Types,\n", "#         sum(case when mhe_id = 1 then 1 else 0 end) as Shopping_Basket,\n", "#         sum(case when mhe_id = 2 then 1 else 0 end) as Shopping_Cart,\n", "#         sum(case when mhe_id = 3 then 1 else 0 end) as <PERSON><PERSON>_<PERSON>,\n", "#         sum(case when mhe_id = 4 then 1 else 0 end) as <PERSON><PERSON>_Trolley_4,\n", "#         sum(case when mhe_id = 5 then 1 else 0 end) as <PERSON><PERSON><PERSON>,\n", "#         sum(case when mhe_id = 6 then 1 else 0 end) as <PERSON><PERSON><PERSON>,\n", "#         sum(case when mhe_id = 7 then 1 else 0 end) as <PERSON><PERSON>_Trolley_7,\n", "#         sum(case when mhe_id = 8 then 1 else 0 end) as Six_Crate_MHE\n", "# from lake_warehouse_location.warehouse_mhe_outlet_mapping wmom\n", "# join lake_warehouse_location.warehouse_mhe wm\n", "# on wmom.mhe_id = wm.id\n", "# join lake_retail.console_outlet co\n", "# on wmom.outlet_id = co.id\n", "# group by 1,2\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MHE_data = pd.read_sql_query(MHE_query, con = redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MHE_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MHE_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STOshortfall_query = \"\"\"\n", "# select outlet_id,\n", "#         co.name as outlet_name,\n", "#         date(convert_timezone('Asia/Kolkata',wave.created_at)) as CreatedAt,\n", "#         sum(sku_quantity) as shortfall_qty\n", "# from lake_warehouse_location.warehouse_wave wave\n", "# join lake_retail.console_outlet co\n", "# on wave.outlet_id = co.id\n", "# where wave_type = 8\n", "# group by 1,2,3\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STOshortfall_data = pd.read_sql_query(STOshortfall_query, con = redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STOshortfall_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STOshortfall_data.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Item level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SKUs_query = \"\"\"\n", "# select isd.sto_id,\n", "#        isi.item_id,\n", "#        isd.outlet_id,\n", "#        isd.merchant_outlet_id,\n", "#        date(isd.delivery_date) as delivery_date,\n", "#        date(isd.created_at) as created_at_date,\n", "#        sum(isi.billed_quantity) as billed_qty,\n", "#        sum(isi.expected_quantity) as expected_qty,\n", "#        sum(isi.reserved_quantity) as reserved_qty\n", "# from lake_ims.ims_sto_details isd\n", "# join lake_ims.ims_sto_item isi\n", "# on isd.sto_id = isi.sto_id\n", "# where date(isd.created_at) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "# group by 1,2,3,4,5,6\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SKUs_data = pd.read_sql_query(SKUs_query, con = redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SKUs_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STO TAT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q1 = \"\"\"SELECT  sto1.*,\n", "            max(CASE\n", "                    WHEN log.sto_state=1 THEN convert_timezone('Asia/Kolkata',log.created_at)\n", "                END) AS sto_Created_at,\n", "            max(CASE\n", "                    WHEN log.sto_state=2 THEN convert_timezone('Asia/Kolkata',log.created_at)\n", "                END) AS sto_Billed_at,\n", "            max(CASE\n", "                    WHEN log.sto_state=3 THEN convert_timezone('Asia/Kolkata',log.created_at)\n", "                END) AS sto_Expired_at,\n", "            max(CASE\n", "                    WHEN log.sto_state=4 THEN convert_timezone('Asia/Kolkata',log.created_at)\n", "                END) AS sto_Inward_at\n", "    FROM\n", "        (\n", "        SELECT  sto.sto_id,\n", "                sto.outlet_id AS sender_outlet_id,\n", "                o.name AS sender_outlet_name,\n", "                sto.merchant_outlet_id AS receiving_outlet_id,\n", "                o1.name AS receiver_outlet_name,\n", "                convert_timezone('Asia/Kolkata',sto.delivery_date) AS delivery_date,\n", "                convert_timezone('Asia/Kolkata',sto.created_at) AS created_date,\n", "                convert_timezone('Asia/Kolkata',sto.updated_at) AS sto_status_updated_at,\n", "                p_inv.invoice_id,\n", "                convert_timezone('Asia/Kolkata',p_inv.pos_timestamp) AS STO_Invoice_created_at,\n", "                max(convert_timezone('Asia/Kolkata',inward.created_at)) AS grn_created_at\n", "        FROM lake_ims.ims_sto_details sto\n", "        INNER JOIN lake_retail.console_outlet o ON sto.outlet_id = o.id\n", "        INNER JOIN lake_retail.console_outlet o1 ON sto.merchant_outlet_id = o1.id\n", "        LEFT JOIN lake_pos.pos_invoice p_inv ON sto.sto_id = p_inv.grofers_order_id\n", "        -- AND invoice_type_id = 5\n", "        LEFT JOIN lake_ims.ims_inward_invoice inward ON p_inv.invoice_id = inward.vendor_invoice_id\n", "        -- AND inward.source_type = 2\n", "        WHERE date(convert_timezone('Asia/Kolkata',sto.created_at)) BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "        -- AND sto.sto_type = 2\n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "        )sto1\n", "    INNER JOIN lake_ims.ims_sto_state_log log ON sto1.sto_id = log.sto_id\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11  \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d1 = pd.read_sql_query(q1, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "select di.sto_id,\n", "    --dib.box_id,\n", "    max(case when dib.state = 2 then convert_timezone('Asia/Kolkata',dib.created_at) end) as Dispatched_At\n", "from lake_warehouse_location.dispatch_indent di\n", "join lake_warehouse_location.dispatch_indent_box dib\n", "on di.id = dib.dispatch_indent_id\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d2 = pd.read_sql_query(q2, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q3 = \"\"\"\n", "select distinct w.entity_id as sto_id,\n", "    --sto.sto_id as sto_sto_id,\n", "    pl.id as picklist_id,\n", "    (pl.picking_started_at +interval '5.5 hrs') AS picking_started_at_ist,\n", "    (pll.pos_timestamp +interval '5.5 hrs') AS picking_completed_at_ist,\n", "    DATEDIFF(SECOND,picking_started_at_ist,picking_completed_at_ist)/60.0 AS PickingTimeMins\n", "from lake_ims.ims_sto_details sto\n", "join lake_warehouse_location.warehouse_wave w\n", "on sto.sto_id = w.entity_id and w.wave_type = 4\n", "join lake_warehouse_location.warehouse_pick_list pl\n", "on w.id = pl.wave_id\n", "join lake_warehouse_location.pick_list_log pll \n", "on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "where date(convert_timezone('Asia/Kolkata',sto.created_at)) \n", "        BETWEEN date(CURRENT_DATE - interval '30 days') AND date(CURRENT_DATE)\n", "order by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3 = pd.read_sql_query(q3, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT1 = d1.merge(d2, on=[\"sto_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT2 = STO_TAT1.merge(d3, on=[\"sto_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT2.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STO_TAT2.columns, STO_TAT2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"sto_id\", \"type\": \"int\", \"description\": \"sto_id\"},\n", "    {\"name\": \"sender_outlet_id\", \"type\": \"int\", \"description\": \"source outlet_id\"},\n", "    {\n", "        \"name\": \"sender_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"seource outlet_name\",\n", "    },\n", "    {\n", "        \"name\": \"receiving_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"destination outlet_id\",\n", "    },\n", "    {\n", "        \"name\": \"receiver_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"destination outlet_name\",\n", "    },\n", "    {\n", "        \"name\": \"delivery_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"delivery date of sto\",\n", "    },\n", "    {\"name\": \"created_date\", \"type\": \"datetime\", \"description\": \"created date of sto\"},\n", "    {\n", "        \"name\": \"sto_status_updated_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"sto status updated at timestamp\",\n", "    },\n", "    {\"name\": \"invoice_id\", \"type\": \"varchar\", \"description\": \"invoice id\"},\n", "    {\n", "        \"name\": \"sto_invoice_created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"sto invoice created at timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"grn_created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"grn created at timestamp\",\n", "    },\n", "    {\"name\": \"sto_created_at\", \"type\": \"datetime\", \"description\": \"sto created at\"},\n", "    {\"name\": \"sto_billed_at\", \"type\": \"datetime\", \"description\": \"sto billed at\"},\n", "    {\"name\": \"sto_expired_at\", \"type\": \"datetime\", \"description\": \"sto expired at\"},\n", "    {\"name\": \"sto_inward_at\", \"type\": \"datetime\", \"description\": \"sto inward at\"},\n", "    {\"name\": \"dispatched_at\", \"type\": \"datetime\", \"description\": \"sto dispatched at\"},\n", "    {\"name\": \"picklist_id\", \"type\": \"float\", \"description\": \"bulk pick list id)\"},\n", "    {\n", "        \"name\": \"picking_started_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"picking started at)\",\n", "    },\n", "    {\n", "        \"name\": \"picking_completed_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"picking completed at\",\n", "    },\n", "    {\n", "        \"name\": \"pickingtimemins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking time in minutes\",\n", "    },\n", "]\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"sto_tat\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"sto_id\"],\n", "    \"sortkey\": [\"sto_id\", \"sender_outlet_id\", \"receiving_outlet_id\"],\n", "    \"incremental_key\": \"delivery_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table maps all the different timestamps associated with an sto\",\n", "}\n", "pb.to_redshift(STO_TAT2, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}