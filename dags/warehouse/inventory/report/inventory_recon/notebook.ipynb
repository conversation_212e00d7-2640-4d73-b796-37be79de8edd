{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"select max(end_timestamp_ist) from metrics.inventory_recon\"\n", "\n", "end_obj = pd.read_sql_query(query, con=redshift)\n", "start_datetime = end_obj.iloc[0, 0].to_pydatetime()\n", "start_datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_datetime = datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)\n", "end_datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with ris as\n", "  (select variant_id,\n", "          outlet_id\n", "   from lake_reports.reports_inventory_snapshot\n", "   where date(snapshot_datetime) between date('{start_datetime}'::timestamp - interval '1 day') and date('{end_datetime}'::timestamp - interval '1 day')\n", "     and active = 1),\n", "     \n", "     rgis as\n", "  (select variant_id,\n", "          outlet_id\n", "   from lake_reports.reports_good_inventory_snapshot\n", "   where date(snapshot_datetime) between date('{start_datetime}'::timestamp - interval '1 day') and date('{end_datetime}'::timestamp - interval '1 day')\n", "     and active = 1),\n", "     \n", "     pp as\n", "  (select variant_id,\n", "          item_id\n", "   from lake_rpc.product_product\n", "   where active = 1),\n", "     \n", "     id as\n", "  (select item_id,\n", "          name,\n", "          brand_id\n", "   from lake_rpc.item_details),\n", "     \n", "     pb as\n", "  (select id,\n", "          name,\n", "          manufacturer_id\n", "   from lake_rpc.product_brand),\n", "     \n", "     pm as\n", "  (select id,\n", "          name\n", "   from lake_rpc.product_manufacturer),\n", "     \n", "     co as\n", "  (select id,\n", "          name\n", "   from lake_retail.console_outlet\n", "   where active = 1\n", "     and device_id <> 47)\n", "\n", "select coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) as outlet_id,\n", "       coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name, '-') as outlet_name,\n", "       coalesce(inv_snap.item_id, good_inv_snap.item_id) as item_id,\n", "       coalesce(inv_snap.name, good_inv_snap.name, '-') as name,\n", "       coalesce(inv_snap.brand, good_inv_snap.brand, '-') as brand,\n", "       coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer, '-') as manufacturer\n", "from\n", "  (select distinct ris.outlet_id,\n", "                   co.name as outlet_name,\n", "                   pp.item_id,\n", "                   id.name as name,\n", "                   pb.name as brand,\n", "                   pm.name as manufacturer\n", "   from ris\n", "   inner join pp on pp.variant_id = ris.variant_id\n", "   left join id on id.item_id = pp.item_id\n", "   left join pb on pb.id = id.brand_id\n", "   left join pm on pm.id = pb.manufacturer_id\n", "   inner join co on co.id = ris.outlet_id) inv_snap\n", "full outer join\n", "  (select distinct rgis.outlet_id,\n", "                   co.name as outlet_name,\n", "                   pp.item_id,\n", "                   id.name as name,\n", "                   pb.name as brand,\n", "                   pm.name as manufacturer\n", "   from rgis\n", "   inner join pp on pp.variant_id = rgis.variant_id\n", "   left join id on id.item_id = pp.item_id\n", "   left join pb on pb.id = id.brand_id\n", "   left join pm on pm.id = pb.manufacturer_id\n", "   inner join co on co.id = rgis.outlet_id) good_inv_snap on inv_snap.outlet_id = good_inv_snap.outlet_id\n", "and inv_snap.item_id = good_inv_snap.item_id\n", "\"\"\"\n", "\n", "snap = pd.read_sql_query(query, con=redshift)\n", "snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snap_outlet_ids = set(snap.outlet_id)\n", "snap_item_ids = set(snap.item_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with pp as\n", "  (select variant_id,\n", "          item_id\n", "   from lake_rpc.product_product\n", "   where active = 1)\n", "\n", "select coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) as outlet_id,\n", "       coalesce(inv_snap.item_id, good_inv_snap.item_id) as item_id,\n", "       coalesce(inv_snap.quantity, 0) + coalesce(good_inv_snap.quantity, 0) as {col}\n", "from\n", "  (select ris.outlet_id,\n", "          pp.item_id,\n", "          sum(ris.quantity) as quantity\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_inventory_snapshot\n", "      where date(snapshot_datetime) = date('{snap_datetime}'::timestamp - interval '1 day')\n", "        and active = 1) ris\n", "   inner join pp on pp.variant_id = ris.variant_id\n", "   group by 1,\n", "            2) inv_snap\n", "full outer join\n", "  (select rgis.outlet_id,\n", "          pp.item_id,\n", "          sum(rgis.quantity) as quantity\n", "   from\n", "     (select variant_id,\n", "             quantity,\n", "             outlet_id\n", "      from lake_reports.reports_good_inventory_snapshot\n", "      where date(snapshot_datetime) = date('{snap_datetime}'::timestamp - interval '1 day')\n", "        and active = 1) rgis\n", "   inner join pp on pp.variant_id = rgis.variant_id\n", "   group by 1,\n", "            2) good_inv_snap on inv_snap.outlet_id = good_inv_snap.outlet_id\n", "and inv_snap.item_id = good_inv_snap.item_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["opening_snap = pd.read_sql_query(\n", "    query.format(snap_datetime=start_datetime, col=\"opening_quantity\"), con=redshift\n", ")\n", "opening_snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["closing_snap = pd.read_sql_query(\n", "    query.format(snap_datetime=end_datetime, col=\"closing_quantity\"), con=redshift\n", ")\n", "closing_snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["opening_snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["closing_snap.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["border_snap = opening_snap.merge(closing_snap, how=\"outer\", on=[\"outlet_id\", \"item_id\"])\n", "border_snap = border_snap.fillna(0)\n", "border_snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set(border_snap.item_id) - set(snap.item_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# might include outlets having device id 47\n", "# set(border_snap.outlet_id) - set(snap.outlet_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snap = snap.merge(border_snap, how=\"left\", on=[\"outlet_id\", \"item_id\"])\n", "snap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_ids = 1, 28, 76, 90\n", "sale_ids = 22, 75\n", "sale_return_ids = (54,)\n", "customer_sale_ids = 2, 50\n", "customer_return_ids = 3, 4, 5, 6, 23, 29, 30, 31, 32, 35, 81, 82\n", "bad_stock_ids = 11, 12, 13, 64\n", "stock_update_positive_ids = 14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122\n", "stock_update_negative_ids = 15, 27, 39, 40, 41, 42, 43, 73, 117, 129\n", "liquidation_ids = 16, 127\n", "esto_ids = 17, 92, 91, 124, 125\n", "repackaged_ids = (18,)\n", "vendor_return_ids = 19, 126"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_update_type_ids = (\n", "    grn_ids\n", "    + sale_ids\n", "    + sale_return_ids\n", "    + customer_sale_ids\n", "    + customer_return_ids\n", "    + bad_stock_ids\n", "    + stock_update_positive_ids\n", "    + stock_update_negative_ids\n", "    + liquidation_ids\n", "    + esto_ids\n", "    + repackaged_ids\n", "    + vendor_return_ids\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select iil.outlet_id,\n", "       pp.item_id,\n", "       iil.inventory_update_type_id,\n", "       iii.source_type,\n", "       iil.\"delta\" as \"delta\"\n", "from\n", "  (select inventory_update_id,\n", "          variant_id,\n", "          \"delta\",\n", "          inventory_update_type_id,\n", "          outlet_id\n", "   from lake_ims.ims_inventory_log\n", "   where pos_timestamp between '{start_datetime}'::timestamp - interval '5.5 hrs' and '{end_datetime}'::timestamp - interval '5.5 hrs'\n", "     and outlet_id in {*snap_outlet_ids,}\n", "     and inventory_update_type_id in {*inv_update_type_ids,}) iil\n", "inner join\n", "  (select variant_id,\n", "          item_id\n", "   from lake_rpc.product_product\n", "   where active = 1\n", "     and item_id in {*snap_item_ids,}) pp on pp.variant_id = iil.variant_id\n", "left join\n", "  (select inventory_update_id,\n", "          grn_id\n", "   from lake_ims.ims_inventory_stock_details) isd on isd.inventory_update_id = iil.inventory_update_id\n", "left join\n", "  (select distinct grn_id,\n", "                   source_type\n", "   from lake_ims.ims_inward_invoice) iii on iii.grn_id = isd.grn_id\n", "\"\"\"\n", "\n", "chunks = list()\n", "for chunk in pd.read_sql_query(query, redshift, chunksize=50000):\n", "    chunks.append(chunk)\n", "\n", "inv_log = pd.concat(chunks).reset_index(drop=True)\n", "inv_log.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_log.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn = inv_log[inv_log[\"inventory_update_type_id\"].isin(grn_ids)]\n", "po_grn = grn[grn[\"source_type\"] == 1]\n", "sto_grn = grn[grn[\"source_type\"] == 2]\n", "customer_return_grn = grn[grn[\"source_type\"] == 3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_grn = (\n", "    po_grn.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"delta\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"delta\": \"po_grn\"})\n", ")\n", "po_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = snap.merge(po_grn, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_grn = (\n", "    sto_grn.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"delta\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"delta\": \"sto_grn\"})\n", ")\n", "sto_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.merge(sto_grn, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["customer_return_grn = (\n", "    customer_return_grn.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"delta\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"delta\": \"customer_return_no_inv_grn\"})\n", ")\n", "customer_return_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.merge(\n", "    customer_return_grn, how=\"left\", on=[\"outlet_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def split_by_id(inv_log, ids, merged, col):\n", "    inv_log_ids = inv_log[inv_log[\"inventory_update_type_id\"].isin(ids)]\n", "    inv_log_ids = (\n", "        inv_log_ids.groupby([\"outlet_id\", \"item_id\"])\n", "        .agg({\"delta\": sum})\n", "        .reset_index()\n", "        .rename(columns={\"delta\": col})\n", "    )\n", "    merged = merged.merge(inv_log_ids, how=\"left\", on=[\"outlet_id\", \"item_id\"])\n", "    return merged"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = split_by_id(inv_log, grn_ids, inv_recon, \"grn\")\n", "inv_recon = split_by_id(inv_log, sale_ids, inv_recon, \"sale\")\n", "inv_recon = split_by_id(inv_log, sale_return_ids, inv_recon, \"sale_return\")\n", "inv_recon = split_by_id(inv_log, customer_sale_ids, inv_recon, \"customer_sale\")\n", "inv_recon = split_by_id(inv_log, customer_return_ids, inv_recon, \"customer_return\")\n", "inv_recon = split_by_id(inv_log, bad_stock_ids, inv_recon, \"bad_stock\")\n", "inv_recon = split_by_id(\n", "    inv_log, stock_update_positive_ids, inv_recon, \"stock_update_positive\"\n", ")\n", "inv_recon = split_by_id(\n", "    inv_log, stock_update_negative_ids, inv_recon, \"stock_update_negative\"\n", ")\n", "inv_recon = split_by_id(inv_log, liquidation_ids, inv_recon, \"liquidation\")\n", "inv_recon = split_by_id(inv_log, esto_ids, inv_recon, \"esto\")\n", "inv_recon = split_by_id(inv_log, repackaged_ids, inv_recon, \"repackaged\")\n", "inv_recon = split_by_id(inv_log, vendor_return_ids, inv_recon, \"vendor_return\")\n", "inv_recon.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select sender_outlet_id as outlet_id,\n", "       item_id,\n", "       sum(inwarded_quantity) as esto_received,\n", "       sum(billed_quantity - inwarded_quantity) as esto_in_transit\n", "from metrics.esto_details\n", "where sto_billed_at between '{start_datetime}'::timestamp - interval '5.5 hrs' and '{end_datetime}'::timestamp - interval '5.5 hrs'\n", "  and sto_state in ('Billed',\n", "                    'Inward')\n", "group by 1,\n", "         2\n", "\"\"\"\n", "\n", "esto = pd.read_sql_query(query, con=redshift)\n", "esto.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.merge(esto, how=\"left\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"sum\"] = 0\n", "inv_recon[\"sum\"] = inv_recon.iloc[:, 6:].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon[inv_recon[\"sum\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"start_timestamp_ist\"] = start_datetime\n", "inv_recon[\"end_timestamp_ist\"] = end_datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon = inv_recon[\n", "    [\n", "        \"start_timestamp_ist\",\n", "        \"end_timestamp_ist\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"brand\",\n", "        \"manufacturer\",\n", "        \"opening_quantity\",\n", "        \"grn\",\n", "        \"po_grn\",\n", "        \"sto_grn\",\n", "        \"customer_return_no_inv_grn\",\n", "        \"sale\",\n", "        \"sale_return\",\n", "        \"customer_sale\",\n", "        \"customer_return\",\n", "        \"bad_stock\",\n", "        \"stock_update_positive\",\n", "        \"stock_update_negative\",\n", "        \"liquidation\",\n", "        \"esto\",\n", "        \"esto_received\",\n", "        \"esto_in_transit\",\n", "        \"repackaged\",\n", "        \"vendor_return\",\n", "        \"closing_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"calculated_closing_quantity\"] = (\n", "    inv_recon[\"opening_quantity\"]\n", "    + inv_recon[\"grn\"]\n", "    - inv_recon[\"sale\"]\n", "    - inv_recon[\"customer_sale\"]\n", "    + inv_recon[\"sale_return\"]\n", "    + inv_recon[\"customer_return\"]\n", "    - inv_recon[\"bad_stock\"]\n", "    + inv_recon[\"stock_update_positive\"]\n", "    - inv_recon[\"stock_update_negative\"]\n", "    - inv_recon[\"liquidation\"]\n", "    - inv_recon[\"esto\"]\n", "    + inv_recon[\"repackaged\"]\n", "    - inv_recon[\"vendor_return\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"diff\"] = abs(\n", "    inv_recon[\"closing_quantity\"] - inv_recon[\"calculated_closing_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad = inv_recon[inv_recon[\"diff\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad.shape[0], inv_recon.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert (\n", "    inv_recon[[\"outlet_id\", \"item_id\"]].drop_duplicates().shape[0] == inv_recon.shape[0]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[inv_recon.columns[8:]] = inv_recon[inv_recon.columns[8:]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_recon[\"etl_timestamp_utc\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"start_timestamp_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"start timestamp ist\",\n", "    },\n", "    {\n", "        \"name\": \"end_timestamp_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"end timestamp ist\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet name\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item id\",\n", "    },\n", "    {\n", "        \"name\": \"name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item name\",\n", "    },\n", "    {\n", "        \"name\": \"brand\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item brand\",\n", "    },\n", "    {\n", "        \"name\": \"manufacturer\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item manufacturer\",\n", "    },\n", "    {\n", "        \"name\": \"opening_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"opening quantity\",\n", "    },\n", "    {\n", "        \"name\": \"grn\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity stocked in i.e. po_grn + sto_grn + customer_return_no_inv_grn\",\n", "    },\n", "    {\n", "        \"name\": \"po_grn\",\n", "        \"type\": \"int\",\n", "        \"description\": \"po grn quantity\",\n", "    },\n", "    {\n", "        \"name\": \"sto_grn\",\n", "        \"type\": \"int\",\n", "        \"description\": \"sto grn quantity\",\n", "    },\n", "    {\n", "        \"name\": \"customer_return_no_inv_grn\",\n", "        \"type\": \"int\",\n", "        \"description\": \"customer returned no invoice grn quantity\",\n", "    },\n", "    {\n", "        \"name\": \"sale\",\n", "        \"type\": \"int\",\n", "        \"description\": \"b2b sale quantity\",\n", "    },\n", "    {\n", "        \"name\": \"sale_return\",\n", "        \"type\": \"int\",\n", "        \"description\": \"b2b return quantity\",\n", "    },\n", "    {\n", "        \"name\": \"customer_sale\",\n", "        \"type\": \"int\",\n", "        \"description\": \"consumer sale quantity\",\n", "    },\n", "    {\n", "        \"name\": \"customer_return\",\n", "        \"type\": \"int\",\n", "        \"description\": \"stock returned by customer\",\n", "    },\n", "    {\n", "        \"name\": \"bad_stock\",\n", "        \"type\": \"int\",\n", "        \"description\": \"damaged/expired stock\",\n", "    },\n", "    {\n", "        \"name\": \"stock_update_positive\",\n", "        \"type\": \"int\",\n", "        \"description\": \"physical inventory > system inventory\",\n", "    },\n", "    {\n", "        \"name\": \"stock_update_negative\",\n", "        \"type\": \"int\",\n", "        \"description\": \"physical inventory < system inventory\",\n", "    },\n", "    {\n", "        \"name\": \"liquidation\",\n", "        \"type\": \"int\",\n", "        \"description\": \"liquidation stock\",\n", "    },\n", "    {\n", "        \"name\": \"esto\",\n", "        \"type\": \"int\",\n", "        \"description\": \"excess stock transfer orders quantity including in transit and received\",\n", "    },\n", "    {\n", "        \"name\": \"esto_received\",\n", "        \"type\": \"int\",\n", "        \"description\": \"esto quantity received\",\n", "    },\n", "    {\n", "        \"name\": \"esto_in_transit\",\n", "        \"type\": \"int\",\n", "        \"description\": \"esto quantity in transit including lost in transit\",\n", "    },\n", "    {\n", "        \"name\": \"repackaged\",\n", "        \"type\": \"int\",\n", "        \"description\": \"bad stock repackaged and sold\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_return\",\n", "        \"type\": \"int\",\n", "        \"description\": \"stock returned to vendor\",\n", "    },\n", "    {\n", "        \"name\": \"closing_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"closing quantity\",\n", "    },\n", "    {\n", "        \"name\": \"calculated_closing_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"expected closing quantity\",\n", "    },\n", "    {\n", "        \"name\": \"diff\",\n", "        \"type\": \"int\",\n", "        \"description\": \"diff between calculated closing quantity and closing quantity\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp_utc\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"timestamp at which row entry is made\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inventory_recon\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"etl_timestamp_utc\", \"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"etl_timestamp_utc\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"etl_timestamp_utc\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Inventory reconciliation at outlet item level across PAN India\",\n", "}\n", "\n", "pb.to_redshift(inv_recon, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}