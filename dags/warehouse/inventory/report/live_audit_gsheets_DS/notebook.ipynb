{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ap-1gQKzYp-NV0mWEmmQUsygVh_q1FD7kRkrhTcWdUY\"\n", "outlet_data = pb.from_sheets(sheet_id, \"Outlet List\")\n", "# , service_account=\"service_account\")\n", "\n", "outlet_list = outlet_data[\"Outlet ID\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_query_list = []\n", "\n", "for current_outlet in outlet_list:\n", "    outlet_query_list.append(\"query_rawdata_\" + str(current_outlet))\n", "\n", "print(outlet_query_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_raw_data = \"\"\"\n", "\n", "\n", "select final.*,\n", "case \n", "when error_type=5 and audit_done_at is null then 1\n", "when error_type=6 and audit_done_at is null then 2\n", "when error_type=2 and audit_done_at is null then 3\n", "when error_type=4 and audit_done_at is null then 4\n", "when error_type=3 and audit_done_at is null then 5\n", "when error_type=7 and audit_done_at is null then 6\n", "when audit_done_at is not null then 100\n", "end as priority\n", "from\n", "(\n", "select \n", "t1.*,\n", "case when t2.comp_time >t1.reported_timestamp then t2.comp_time else NULL end as audit_done_at,\n", "case when t2.comp_time >t1.reported_timestamp then t2.comp_update_type else NULL end as comp_update_type_id, \n", "case  when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=3 and t1.error_type in (3,6,7) THEN \"Audit positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and t1.error_type in (3,6,7) THEN \"Audit negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and t1.error_type=4 THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=5 and t1.error_type in (3,6,7) THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and t1.error_type in (3,6,7) THEN \"Migration negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and t1.error_type =5 THEN \"PNA positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (4,6) and t1.error_type =5 THEN \"PNA negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and t1.error_type=1 THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and t1.error_type=4 THEN \"Invalid\"\n", "else '' end as comp_update_type,\n", "\n", "case when t2.comp_time >t1.reported_timestamp then ceil(timestampdiff(minute,t1.reported_timestamp,t2.comp_time)/60) else NULL end as time_taken_hrs\n", "from (\n", "SELECT \n", "      log.outlet_id,\n", "      log.picker_name as reported_by,\n", "      convert_tz(log.created_at,\"+00:00\",\"+05:30\") as reported_timestamp,\n", "      concat(pro.name,\"--\",pro.variant_description) as product_description,\n", "      log.variant_suggested ,\n", "      log.item_id,\n", "      wsl.location_name,\n", "      log.location_suggested ,\n", "      log.error_type,\n", "        \n", "      (CASE\n", "            WHEN log.error_type=1 THEN  'Discipline Issue' \n", "            WHEN log.error_type=2 THEN  'Item Location Issue' \n", "            WHEN log.error_type=3 THEN  'Qty mismatch' \n", "            WHEN log.error_type=4 THEN  'Product Damaged' \n", "            WHEN log.error_type=5 THEN  'PNA' \n", "            WHEN log.error_type=6 THEN  'less_picked' \n", "            WHEN log.error_type=7 THEN  'Asked_for_location' \n", "            \n", "            ELSE  ''\n", "        END) AS reason\n", "\n", "FROM warehouse_location.warehouse_pick_list_item_location_error_log log\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "         item_id,\n", "          picker_name as created_by,\n", "          variant_suggested variant_id,\n", "          location_suggested ,\n", "          error_type ,\n", "          max(created_at) AS pos_ts\n", "  FROM warehouse_location.warehouse_pick_list_item_location_error_log where \n", "   created_at >= DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", "  and error_type in (2,3,4,5,6) \n", "--  and outlet_id=334\n", "  and outlet_id = %(outlet_id)s\n", "  group by 1,2,3,4,5,6)x1 ON \n", "    log.created_at=x1.pos_ts\n", "-- AND log.outlet_id=x1.outlet_id\n", "AND log.variant_suggested=x1.variant_id\n", "-- AND log.item_id=x1.item_id\n", "AND log.location_suggested=x1.location_suggested\n", "and log.error_type=x1.error_type\n", "inner join rpc.product_product pro on pro.variant_id=log.variant_suggested\n", "inner join warehouse_location.warehouse_storage_location wsl on log.location_suggested=wsl.id\n", "-- update the date range condition\n", "where log.created_at >= DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", " and x1.outlet_id = %(outlet_id)s\n", "-- and x1.outlet_id=334 \n", "and log.error_type in (2,3,4,5,6)\n", "-- WHERE convert_tz(pos_created_at,\"+00:00\",\"+05:30\")>current_date()-1\n", "-- order by reported_timestamp asc\n", ") t1\n", "\n", "left join\n", "(\n", "select \n", "audit_raised.item_id as req_item_id,\n", "audit_raised.variant_suggested as req_variant_id,\n", "convert_tz(audit_raised.created_at ,\"+00:00\",\"+05:30\") req_created_at,\n", "audit_raised.location_suggested as req_location_id,\n", "audit_raised.error_type,\n", "-- audit_raised.warehouse_id as req_warehouse_id,\n", "    audit_done.item_id as comp_item_id, \n", "    audit_done.variant_id as comp_variant_id, \n", "    min(convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")) as comp_time, \n", "    audit_done.location_id as comp_location_id, \n", "\n", "    audit_done.item_location_update_type_id as comp_update_type\n", "from warehouse_location.warehouse_pick_list_item_location_error_log audit_raised  \n", "inner join \n", "(\n", "    select \n", "    audit_done.id,\n", "        audit_done.item_id,\n", "        audit_done.variant_id,\n", "        audit_done.pos_timestamp,\n", "        audit_done.location_id ,\n", "        audit_done.item_location_update_type_id\n", "    from\n", "    warehouse_location.warehouse_item_location_log audit_done \n", "            where audit_done.id>364643512 and audit_done.pos_timestamp>current_date-2  and audit_done.item_location_update_type_id in (3,4,5,6)\n", "\n", ") audit_done\n", "\n", "-- warehouse_location.warehouse_item_location_log audit_done \n", "on  audit_raised.variant_suggested=audit_done.variant_id \n", "and audit_raised.item_id=audit_done.item_id \n", "and audit_raised.location_suggested=audit_done.location_id\n", "and not (audit_done.item_location_update_type_id in (3,5) and audit_raised.error_type=4)\n", "-- and audit_raised.warehouse_id=audit_done.warehouse_id\n", "and audit_raised.created_at<audit_done.pos_timestamp\n", "where\n", " audit_raised.created_at >= DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", "\n", "and audit_raised.error_type in (2,3,4,5,6)\n", " and audit_raised.outlet_id = %(outlet_id)s\n", "-- and audit_raised.outlet_id=334\n", "group by 1,2,3,4,5,6,7,9,10\n", ") t2\n", "on\n", "t1.variant_suggested=t2.req_variant_id\n", "and t1.error_type=t2.error_type\n", "and t1.location_suggested=t2.req_location_id\n", "-- and t1.warehouse_id=t2.req_warehouse_id\n", "and t1.reported_timestamp<t2.comp_time\n", "and not (comp_update_type in (3,5) and t1.error_type=4)\n", "-- and t1.item_id=t2.req_item_id\n", "-- where t2.comp_time is not Null\n", "order by  t1.reported_timestamp desc\n", "\n", ") final\n", "-- where date(reported_timestamp)=current_date-1\n", "group by  1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "order by priority asc,  reported_timestamp desc\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(outlet_list)\n", "# outlet_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# for current_outlet in outlet_list:\n", "    print(f\"Pushing data for outlet {current_outlet}\")\n", "    data1 = pd.read_sql_query(\n", "        sql=query_raw_data, con=retail, params={\"outlet_id\": current_outlet}\n", "    )\n", "    data1_1 = data1[data1[\"reason\"] != \"PNA\"]\n", "    data1_2 = data1[data1[\"reason\"] == \"PNA\"]\n", "    sheet_name = \"Raise Audit\"\n", "    sheet_name1 = \"Raise Audit PNA\"\n", "    print(data1_1.shape, data1_2.shape, data1.shape)\n", "    sheet_id = outlet_data[outlet_data[\"Outlet ID\"] == str(current_outlet)][\"Sheet ID\"]\n", "    sh_id = sheet_id.tolist()[0]\n", "    try:\n", "        pb.to_sheets(data1_1, sh_id, sheet_name)\n", "        #                      , service_account=\"service_account\")\n", "        pb.to_sheets(data1_2, sh_id, sheet_name1)\n", "    #                      , service_account=\"service_account\")\n", "    except APIError as e:\n", "        exception_code = eval(e.args[0])[\"error\"][\"code\"]\n", "        if exception_code == 503:\n", "            pass\n", "        elif exception_code == 429:\n", "            time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# '314','287','451','334','116','520','340','369','367'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# pushing adherence reports\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_raw_data = \"\"\"\n", "select final.*,\n", "case \n", "when error_type=5 and audit_done_at is null then 1\n", "when error_type=6 and audit_done_at is null then 2\n", "when error_type=2 and audit_done_at is null then 3\n", "when error_type=4 and audit_done_at is null then 4\n", "when error_type=3 and audit_done_at is null then 5\n", "when error_type=7 and audit_done_at is null then 6\n", "when audit_done_at is not null then 100\n", "end as priority\n", "from\n", "(\n", "select \n", "t1.*,\n", "case when t2.comp_time >t1.reported_timestamp then t2.comp_time else NULL end as audit_done_at,\n", "case when t2.comp_time >t1.reported_timestamp then t2.comp_update_type else NULL end as comp_update_type_id, \n", "case  when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=3 and t1.error_type in (3,6,7) THEN \"Audit positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and t1.error_type in (3,6,7) THEN \"Audit negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=4 and t1.error_type=4 THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=5 and t1.error_type in (3,6,7) THEN \"Migration positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and t1.error_type in (3,6,7) THEN \"Migration negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and t1.error_type =5 THEN \"PNA positive\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (4,6) and t1.error_type =5 THEN \"PNA negative\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type=6 and t1.error_type=1 THEN \"Bad Stock Update\"\n", "      when t2.comp_time >t1.reported_timestamp and  t2.comp_update_type in (3,5) and t1.error_type=4 THEN \"Invalid\"\n", "else '' end as comp_update_type,\n", "\n", "case when t2.comp_time >t1.reported_timestamp then ceil(timestampdiff(minute,t1.reported_timestamp,t2.comp_time)/60) else NULL end as time_taken_hrs\n", "from (\n", "SELECT \n", "      log.outlet_id,\n", "      log.picker_name as reported_by,\n", "      convert_tz(log.created_at,\"+00:00\",\"+05:30\") as reported_timestamp,\n", "      concat(pro.name,\"--\",pro.variant_description) as product_description,\n", "      log.variant_suggested ,\n", "      log.item_id,\n", "      wsl.location_name,\n", "      log.location_suggested ,\n", "      log.error_type,\n", "        \n", "      (CASE\n", "            WHEN log.error_type=1 THEN  'Discipline Issue' \n", "            WHEN log.error_type=2 THEN  'Item Location Issue' \n", "            WHEN log.error_type=3 THEN  'Qty mismatch' \n", "            WHEN log.error_type=4 THEN  'Product Damaged' \n", "            WHEN log.error_type=5 THEN  'PNA' \n", "            WHEN log.error_type=6 THEN  'less_picked' \n", "            WHEN log.error_type=7 THEN  'Asked_for_location' \n", "            \n", "            ELSE  ''\n", "        END) AS reason\n", "\n", "FROM warehouse_location.warehouse_pick_list_item_location_error_log log\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "         item_id,\n", "          picker_name as created_by,\n", "          variant_suggested variant_id,\n", "          location_suggested ,\n", "          error_type ,\n", "          max(created_at) AS pos_ts\n", "  FROM warehouse_location.warehouse_pick_list_item_location_error_log where \n", "   created_at between DATE_sub(date_sub(current_date(), INTERVAL '2 5' DAY_HOUR) ,INTERVAL 30 MINUTE) and DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", "  and error_type in (2,3,4,5,6) \n", "--  and outlet_id=334\n", "  and outlet_id in ({outlets})\n", "  group by 1,2,3,4,5,6)x1 ON \n", "    log.created_at=x1.pos_ts\n", "-- AND log.outlet_id=x1.outlet_id\n", "AND log.variant_suggested=x1.variant_id\n", "-- AND log.item_id=x1.item_id\n", "AND log.location_suggested=x1.location_suggested\n", "and log.error_type=x1.error_type\n", "inner join rpc.product_product pro on pro.variant_id=log.variant_suggested\n", "inner join warehouse_location.warehouse_storage_location wsl on log.location_suggested=wsl.id\n", "-- update the date range condition\n", "where log.created_at between DATE_sub(date_sub(current_date(), INTERVAL '2 5' DAY_HOUR) ,INTERVAL 30 MINUTE) and DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", " and x1.outlet_id in ({outlets})\n", "-- and x1.outlet_id=334 \n", "and log.error_type in (2,3,4,5,6)\n", "-- WHERE convert_tz(pos_created_at,\"+00:00\",\"+05:30\")>current_date()-1\n", "-- order by reported_timestamp asc\n", ") t1\n", "\n", "left join\n", "(\n", "select \n", "audit_raised.item_id as req_item_id,\n", "audit_raised.variant_suggested as req_variant_id,\n", "convert_tz(audit_raised.created_at ,\"+00:00\",\"+05:30\") req_created_at,\n", "audit_raised.location_suggested as req_location_id,\n", "audit_raised.error_type,\n", "-- audit_raised.warehouse_id as req_warehouse_id,\n", "    audit_done.item_id as comp_item_id, \n", "    audit_done.variant_id as comp_variant_id, \n", "    min(convert_tz(audit_done.pos_timestamp,\"+00:00\",\"+05:30\")) as comp_time, \n", "    audit_done.location_id as comp_location_id, \n", "\n", "    audit_done.item_location_update_type_id as comp_update_type\n", "from warehouse_location.warehouse_pick_list_item_location_error_log audit_raised  \n", "inner join \n", "(\n", "    select \n", "    audit_done.id,\n", "        audit_done.item_id,\n", "        audit_done.variant_id,\n", "        audit_done.pos_timestamp,\n", "        audit_done.location_id ,\n", "        audit_done.item_location_update_type_id\n", "    from\n", "    warehouse_location.warehouse_item_location_log audit_done \n", "            where audit_done.id>364643512 and audit_done.pos_timestamp>current_date-2  and audit_done.item_location_update_type_id in (3,4,5,6)\n", "\n", ") audit_done\n", "\n", "-- warehouse_location.warehouse_item_location_log audit_done \n", "on  audit_raised.variant_suggested=audit_done.variant_id \n", "and audit_raised.item_id=audit_done.item_id \n", "and audit_raised.location_suggested=audit_done.location_id\n", "and not (audit_done.item_location_update_type_id in (3,5) and audit_raised.error_type=4)\n", "-- and audit_raised.warehouse_id=audit_done.warehouse_id\n", "and audit_raised.created_at<audit_done.pos_timestamp\n", "where\n", " audit_raised.created_at between DATE_sub(date_sub(current_date(), INTERVAL '2 5' DAY_HOUR) ,INTERVAL 30 MINUTE) and DATE_sub(date_sub(current_date(), INTERVAL '1 5' DAY_HOUR) ,INTERVAL 30 MINUTE)\n", "\n", "and audit_raised.error_type in (2,3,4,5,6)\n", " and audit_raised.outlet_id in ({outlets})\n", "-- and audit_raised.outlet_id=334\n", "group by 1,2,3,4,5,6,7,9,10\n", ") t2\n", "on\n", "t1.variant_suggested=t2.req_variant_id\n", "and t1.error_type=t2.error_type\n", "and t1.location_suggested=t2.req_location_id\n", "-- and t1.warehouse_id=t2.req_warehouse_id\n", "and t1.reported_timestamp<t2.comp_time\n", "and not (comp_update_type in (3,5) and t1.error_type=4)\n", "-- and t1.item_id=t2.req_item_id\n", "-- where t2.comp_time is not Null\n", "order by  t1.reported_timestamp desc\n", "\n", ") final\n", "-- where date(reported_timestamp)=current_date-1\n", "group by  1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "order by priority asc,  reported_timestamp desc\n", "\"\"\".format(\n", "    outlets=\",\".join([str(x) for x in outlet_list.unique()])\n", ")\n", "data1 = pd.read_sql_query(\n", "    sql=query_raw_data, con=retail, params={\"outlet_id\": current_outlet}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1[\"key\"] = (\n", "    data1[\"outlet_id\"].astype(str)\n", "    + data1[\"reported_timestamp\"].astype(str)\n", "    + data1[\"variant_suggested\"].astype(str)\n", "    + data1[\"location_suggested\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"outlet_id\": \"Outlet_id\",\n", "    \"reported_by\": \"Name of the user who reported\",\n", "    \"reported_timestamp\": \"Timestamp when it was reported\",\n", "    \"product_description\": \"Description of the product\",\n", "    \"variant_suggested\": \" Suggested variant of the product\",\n", "    \"item_id\": \"Item id\",\n", "    \"location_name\": \"Location of the audit\",\n", "    \"location_suggested\": \"Location suggested for audit\",\n", "    \"error_type\": \"Type of error\",\n", "    \"reason\": \"Description of the error\",\n", "    \"audit_done_at\": \"Date at which audit was done\",\n", "    \"comp_update_type_id\": \"Audit type id\",\n", "    \"comp_update_type\": \"Audit type\",\n", "    \"time_taken_hrs\": \"Time taken\",\n", "    \"priority\": \"Priority of the location\",\n", "    \"key\": \"Primary key of the table\",\n", "}\n", "\n", "\n", "def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "column_dtypes_primary = redshift_schema(data1)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"RaiseAuditAdherence\",\n", "    \"column_dtypes\": column_dtypes_primary,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"reported_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Audit Adherence\",\n", "}\n", "pb.to_redshift(data1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}