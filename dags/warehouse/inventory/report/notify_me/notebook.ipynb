{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### changes for this commit\n", "### adding provision for MID and city level exclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import pandas as pd\n", "import pandasql as ps\n", "import pencilbox as pb\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### live products"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with all_prod as (select product_id from dwh.dim_product where is_current = True and is_product_enabled = True group by 1),\n", "\n", "tbex as (select distinct product_id from \n", "(select product_id from dwh.dim_product where is_current = True and is_product_enabled = True \n", "and product_name ilike '%%pack%%' and product_name ilike '%%of%%')\n", "union all\n", "(select product_id from dwh.dim_product where is_current = True and is_product_enabled = True \n", "and product_name ilike '%%buy%%' and product_name ilike '%%get%%' )\n", "union all\n", "(select product_id from dwh.dim_product where is_current = True and is_product_enabled = True \n", "and product_name ilike '%%hamper%%' or product_Type ilike '%%hamper%%')\n", "union all\n", "(select product_id from dwh.dim_product where is_current = True and is_product_enabled = True \n", "and product_name ilike '%%gift%%' or product_Type ilike '%%gift%%')),\n", "\n", "live_prod as (\n", "select\n", "a.product_id\n", "from\n", "all_prod a\n", "left join\n", "tbex b on a.product_id = b.product_id where b.product_id is null\n", ")\n", "\n", "select* from live_prod\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_prod = pd.read_sql(sql=sql, con=redshift)\n", "df_prod.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_prod.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### live merchants"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "with live_mids as (\n", "select \n", "a.frontend_merchant_id merchant_id,\n", "b.frontend_merchant_city_id city_id\n", "from \n", "dwh.fact_sales_order_details a\n", "join\n", "dwh.dim_merchant_outlet_facility_mapping b on a.frontend_merchant_id = b.frontend_merchant_id\n", "where order_create_ts_ist between current_date-60 and current_date and order_current_status <> 'CANCELLED'\n", "and b.is_mapping_enabled and b.is_express_store\n", "group by 1,2\n", ")\n", "\n", "select* from live_mids\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mid = pd.read_sql(sql=sql, con=redshift)\n", "df_mid.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mid.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### final cross join"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mid[\"key\"] = 0\n", "df_prod[\"key\"] = 0\n", "\n", "\n", "final_upload = df_mid.merge(df_prod, how=\"outer\", on=\"key\")\n", "del final_upload[\"key\"]\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["### removing items from exclusion sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### merchant_id sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1X5gv6BV2o6tsAG8Wk4ZoMBRquAfe2dAiFRRL9dK2Ss0\"\n", "sheet_name = \"merchant_id_level\"\n", "mid_exclusions = pb.from_sheets(sheet_id, sheet_name)\n", "mid_exclusions[\"merchant_id\"] = mid_exclusions[\"merchant_id\"].astype(int)\n", "mid_exclusions[\"product_id\"] = mid_exclusions[\"product_id\"].astype(int)\n", "mid_exclusions[\"merchantkey\"] = (\n", "    mid_exclusions[\"merchant_id\"].astype(str)\n", "    + \"-\"\n", "    + mid_exclusions[\"product_id\"].astype(str)\n", ")\n", "mid_exclusions.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1X5gv6BV2o6tsAG8Wk4ZoMBRquAfe2dAiFRRL9dK2Ss0\"\n", "sheet_name = \"city_id_level\"\n", "city_exclusions = pb.from_sheets(sheet_id, sheet_name)\n", "city_exclusions[\"city_id\"] = city_exclusions[\"city_id\"].astype(int)\n", "city_exclusions[\"product_id\"] = city_exclusions[\"product_id\"].astype(int)\n", "city_exclusions[\"citykey\"] = (\n", "    city_exclusions[\"city_id\"].astype(str)\n", "    + \"-\"\n", "    + city_exclusions[\"product_id\"].astype(str)\n", ")\n", "city_exclusions.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload[\"citykey\"] = (\n", "    final_upload[\"city_id\"].astype(str) + \"-\" + final_upload[\"product_id\"].astype(str)\n", ")\n", "final_upload[\"merchantkey\"] = (\n", "    final_upload[\"merchant_id\"].astype(str)\n", "    + \"-\"\n", "    + final_upload[\"product_id\"].astype(str)\n", ")\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload = final_upload.merge(mid_exclusions, how=\"left\", on=\"merchantkey\")\n", "final_upload = final_upload[final_upload.merchant_id_y.isnull()]\n", "final_upload = final_upload.merge(city_exclusions, how=\"left\", on=\"citykey\")\n", "final_upload = final_upload[final_upload.city_id_y.isnull()]\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### deleting extra column\n", "del final_upload[\"city_id_x\"]\n", "del final_upload[\"citykey\"]\n", "del final_upload[\"merchantkey\"]\n", "del final_upload[\"merchant_id_y\"]\n", "del final_upload[\"product_id_y\"]\n", "del final_upload[\"city_id_y\"]\n", "del final_upload[\"product_id\"]\n", "final_upload[\"product_id\"] = final_upload[\"product_id_x\"]\n", "final_upload[\"merchant_id\"] = final_upload[\"merchant_id_x\"]\n", "del final_upload[\"merchant_id_x\"]\n", "del final_upload[\"product_id_x\"]\n", "final_upload = final_upload[[\"merchant_id\", \"product_id\"]]\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### upload to redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"notify_me_list\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"merchant_id\", \"type\": \"int\", \"description\": \"Merchant_ID\"},\n", "        {\"name\": \"product_id\", \"type\": \"int\", \"description\": \"Product_ID\"},\n", "    ],\n", "    \"primary_key\": [\"product_id\"],\n", "    \"sortkey\": [\"product_id\"],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Notify Me List\",\n", "}\n", "pb.to_redshift(final_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### adding string OOS column\n", "final_upload[\"out_of_stock\"] = \"out_of_stock\"\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### manual additions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1k5Qu5rYYaPHb3VY1YU9SXfbqV_k-HFNTo4jXbjLzG5Q\"\n", "sheet_name = \"manual_additions\"\n", "df_addons = pb.from_sheets(sheet_id, sheet_name)\n", "df_addons[\"merchant_id\"] = df_addons[\"merchant_id\"].astype(int)\n", "df_addons[\"product_id\"] = df_addons[\"product_id\"].astype(int)\n", "df_addons.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_addons.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frames = [final_upload, df_addons]\n", "final_upload = pd.concat(frames)\n", "final_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_upload.to_csv(\"internal_tags.csv\", header=None, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["12872 in final_upload.product_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "key = secrets.get(\"aws_s3_bucket_Access_key\")\n", "id = secrets.get(\"aws_s3_bucket_secret_Access\")\n", "\n", "key = secrets[\"application-dse-notifyme-user\"].get(\"aws_key\")\n", "id = secrets[\"application-dse-notifyme-user\"].get(\"aws_secret\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "session = boto3.Session(\n", "    aws_access_key_id=key,\n", "    aws_secret_access_key=id,\n", ")\n", "s3 = session.resource(\"s3\")\n", "bucket = \"grofers-search-indexing-assets\"\n", "filename = \"internal_tags.csv\"\n", "s3.meta.client.upload_file(Filename=filename, Bucket=bucket, Key=\"internal_tags.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.remove(\"internal_tags.csv\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}