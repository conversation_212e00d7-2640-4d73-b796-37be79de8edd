{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing Modules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import pickle\n", "import psycopg2\n", "import pandas as pd\n", "import scipy\n", "import sklearn\n", "import numpy as np\n", "import pickle\n", "import logging\n", "from datetime import datetime\n", "from pytz import timezone\n", "import datetime\n", "from datetime import datetime\n", "import pencilbox as pb\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)\n", "# Importing queries\n", "\n", "from excess_queries import *\n", "\n", "# fore_back_30,sales_back_30,assot_state,doi_vendor,item_cat,lp_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1. Raw data from queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_ageing_report = pd.read_sql(ageing, con)\n", "# df_ageing_report = pb.from_sheets('197DC092VUK-Ssi_bgIUpcEMDUMT352s7asK1y8rNlus', 'june')\n", "len(df_ageing_report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_ageing_cutoff = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Ageing cutoff\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Storing query outputs to DF\n", "\n", "fore_fwd_60_df = pd.read_sql(fore_fwd_60, con)\n", "# fore_fwd_60_df = pd.read_sql(fore_fwd_60_p.format(i = 77),con)\n", "\n", "fore_back_30_df = pd.read_sql(fore_back_30, con)\n", "\n", "sales_back_30_df = pd.read_sql(sales_back_30, con)\n", "# sales_back_30_df = pd.read_sql(sales_back_30_p.format(i = 77),con)\n", "\n", "assot_state_df = pd.read_sql(assot_state, con)\n", "doi_vendor_df = pd.read_sql(doi_vendor, con)\n", "item_cat_df = pd.read_sql(item_cat, con)\n", "lp_info_df = pd.read_sql(lp_info, con)\n", "outlets_df = pd.read_sql(outlets, con)\n", "pos_df = pd.read_sql(opne_pos, con)\n", "bucket_df = pd.read_sql(bucket_info, con)\n", "tat_df = pd.read_sql(tat_info, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_ageing_report = df_ageing_report.replace(\"#NAME?\", 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_ageing_report.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_ageing_report = df_ageing_report.astype({'item_id' : int,\n", "# 'outlet_id' : int,\n", "# 'item_mrp' : float,\n", "# '< 1 days' : float,\n", "# '< 3 days' : float,\n", "# '< 5 days' : float,\n", "# '< 10 days' : float,\n", "# '< 15 days' : float,\n", "# '< 30 days' : float,\n", "# '< 45 days' : float,\n", "# '< 60 days' : float,\n", "# '< 75 days' : float,\n", "# '< 90 days' : float,\n", "# '< 120 days' : float,\n", "# '< 150 days' : float,\n", "# '> 150 days' : float}, errors = 'ignore')\n", "# # .dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fore_fwd_60_df\n", "# sd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Preparing raw data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["io_base = df_ageing_report[[\"item_id\", \"outlet_id\"]].merge(\n", "    outlets_df, how=\"left\", left_on=[\"outlet_id\"], right_on=[\"outlet_id\"]\n", ")[[\"item_id\", \"outlet_id\", \"facility_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["io_base[\"facility_id\"] = io_base[\"facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fore_fwd_60_w = fore_fwd_60_df.merge(\n", "    io_base,\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", ")\n", "fore_back_30_w = fore_back_30_df.merge(\n", "    io_base,\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", ")\n", "sales_back_30_w = sales_back_30_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"outlet_id\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", ")\n", "assot_state_w = assot_state_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"outlet_id\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", ")\n", "doi_vendor_w = doi_vendor_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"outlet_id\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", ")\n", "item_cat_w = item_cat_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", ")\n", "lp_info_w = lp_info_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"outlet_id\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", ")\n", "pos_w = pos_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"outlet_id\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", ")\n", "bucket_w = bucket_df.merge(\n", "    io_base,\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", ")\n", "tat_w = tat_df.merge(\n", "    io_base,\n", "    how=\"right\",\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_w = outlets_df.merge(\n", "    io_base[[\"item_id\", \"outlet_id\"]],\n", "    how=\"right\",\n", "    left_on=[\"outlet_id\"],\n", "    right_on=[\"outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# io_base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fore_fwd_60_w[\"key\"] = (\n", "    fore_fwd_60_w[\"item_id\"].astype(str) + \"_\" + fore_fwd_60_w[\"outlet_id\"].astype(str)\n", ")\n", "fore_back_30_w[\"key\"] = (\n", "    fore_back_30_w[\"item_id\"].astype(str)\n", "    + \"_\"\n", "    + fore_back_30_w[\"outlet_id\"].astype(str)\n", ")\n", "sales_back_30_w[\"key\"] = (\n", "    sales_back_30_w[\"item_id\"].astype(str)\n", "    + \"_\"\n", "    + sales_back_30_w[\"outlet_id\"].astype(str)\n", ")\n", "assot_state_w[\"key\"] = (\n", "    assot_state_w[\"item_id\"].astype(str) + \"_\" + assot_state_w[\"outlet_id\"].astype(str)\n", ")\n", "doi_vendor_w[\"key\"] = (\n", "    doi_vendor_w[\"item_id\"].astype(str) + \"_\" + doi_vendor_w[\"outlet_id\"].astype(str)\n", ")\n", "item_cat_w[\"key\"] = (\n", "    item_cat_w[\"item_id\"].astype(str) + \"_\" + item_cat_w[\"outlet_id\"].astype(str)\n", ")\n", "lp_info_w[\"key\"] = (\n", "    lp_info_w[\"item_id\"].astype(str) + \"_\" + lp_info_w[\"outlet_id\"].astype(str)\n", ")\n", "df_ageing_report[\"key\"] = (\n", "    df_ageing_report[\"item_id\"].astype(str)\n", "    + \"_\"\n", "    + df_ageing_report[\"outlet_id\"].astype(str)\n", ")\n", "io_base[\"key\"] = io_base[\"item_id\"].astype(str) + \"_\" + io_base[\"outlet_id\"].astype(str)\n", "outlets_w[\"key\"] = (\n", "    outlets_w[\"item_id\"].astype(str) + \"_\" + outlets_w[\"outlet_id\"].astype(str)\n", ")\n", "pos_w[\"key\"] = pos_w[\"item_id\"].astype(str) + \"_\" + pos_w[\"outlet_id\"].astype(str)\n", "bucket_w[\"key\"] = (\n", "    bucket_w[\"item_id\"].astype(str) + \"_\" + bucket_w[\"outlet_id\"].astype(str)\n", ")\n", "tat_w[\"key\"] = tat_w[\"item_id\"].astype(str) + \"_\" + tat_w[\"outlet_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fore_fwd_60_w.set_index(\"key\", inplace=True, drop=True)\n", "fore_back_30_w.set_index(\"key\", inplace=True, drop=True)\n", "sales_back_30_w.set_index(\"key\", inplace=True, drop=True)\n", "assot_state_w.set_index(\"key\", inplace=True, drop=True)\n", "doi_vendor_w.set_index(\"key\", inplace=True, drop=True)\n", "item_cat_w.set_index(\"key\", inplace=True, drop=True)\n", "lp_info_w.set_index(\"key\", inplace=True, drop=True)\n", "df_ageing_report.set_index(\"key\", inplace=True, drop=True)\n", "io_base.set_index(\"key\", inplace=True, drop=True)\n", "outlets_w.set_index(\"key\", inplace=True, drop=True)\n", "pos_w.set_index(\"key\", inplace=True, drop=True)\n", "bucket_w.set_index(\"key\", inplace=True, drop=True)\n", "tat_w.set_index(\"key\", inplace=True, drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fore_fwd_60_w = fore_fwd_60_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "fore_back_30_w = fore_back_30_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "sales_back_30_w = sales_back_30_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "assot_state_w = assot_state_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "doi_vendor_w = doi_vendor_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "item_cat_w = item_cat_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "df_ageing_report = df_ageing_report.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "lp_info_w = lp_info_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "pos_w = pos_w.drop([\"item_id\", \"outlet_id\"], axis=1)\n", "bucket_w = bucket_w.drop([\"item_id\", \"outlet_id\", \"facility_id\"], axis=1)\n", "tat_w = tat_w.drop([\"item_id\", \"outlet_id\", \"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_for_avg\n", "del fore_back_30_w[\"facility_id\"]\n", "del fore_fwd_60_w[\"facility_id\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Calculations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### i. CPD Variation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doi_vendor_w\n", "df_for_avg = (\n", "    fore_back_30_w[list(fore_back_30_w.columns[:30])]\n", "    .mean(axis=1)\n", "    .to_frame()\n", "    .rename(columns={0: \"for_avg\"})\n", ")\n", "df_back_avg = (\n", "    sales_back_30_w[list(fore_back_30_w.columns[:30])]\n", "    .mean(axis=1)\n", "    .to_frame()\n", "    .rename(columns={0: \"back_avg\"})\n", ")\n", "df_fro_fwd_avg = (\n", "    fore_fwd_60_w[list(fore_fwd_60_w.columns[:30])]\n", "    .mean(axis=1)\n", "    .to_frame()\n", "    .rename(columns={0: \"fore_fwd_avg\"})\n", ")\n", "# sales_back_30_w[list(fore_back_30_w.columns[:30])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accu_df = (\n", "    df_for_avg.join(df_back_avg).join(df_fro_fwd_avg).replace(np.nan, 0.0, regex=True)\n", ")\n", "accu_df[\"Variation\"] = np.where(\n", "    (accu_df[\"fore_fwd_avg\"] != 0.0) & (accu_df[\"back_avg\"] != 0.0),\n", "    accu_df[\"fore_fwd_avg\"] / accu_df[\"back_avg\"],\n", "    1.0,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### ii. DOI for excess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_po_doi = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"PO cycle DOI\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item = item_cat_w.join(doi_vendor_w)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"po_cycle\"].fillna(\"Weekly\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doi_vendor_item\n", "# tat_w"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item = (\n", "    doi_vendor_item.reset_index()\n", "    .merge(\n", "        tat_w.reset_index()[[\"key\", \"vendor_name\", \"tat_days\"]],\n", "        how=\"left\",\n", "        left_on=[\"key\", \"vendor_name\"],\n", "        right_on=[\"key\", \"vendor_name\"],\n", "    )\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_po_doi[\"po_cycle\"] = df_po_doi[\"po_cycle\"].astype(str)\n", "df_po_doi[\"trigger_doi_po\"] = df_po_doi[\"trigger_doi\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df_po_doi[\"trigger_doi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_po_doi\n", "doi_vendor_item = (\n", "    doi_vendor_item.reset_index()\n", "    .merge(df_po_doi, how=\"left\", left_on=[\"po_cycle\"], right_on=[\"po_cycle\"])\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi\"] = doi_vendor_item[\n", "    [\"trigger_doi\", \"max_ordering_doi\", \"trigger_doi_po\"]\n", "].values.max(1)\n", "# outlets_w"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# run_doi # doi_vendor_item # accu_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item = doi_vendor_item.join(\n", "    outlets_w[[\"outlet_id\", \"outlet\", \"facility_id\", \"facility\"]]\n", ").join(assot_state_w[[\"master_assortment_substate_id\", \"assortment_status\"]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del doi_vendor_item[\"outlet_name\"]\n", "del doi_vendor_item[\"physical_facility_id\"]\n", "del doi_vendor_item[\"item_name\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doi_vendor_item\n", "\n", "gm_l0s = [\n", "    \"Furnishing & Home Needs\",\n", "    \"Home & Kitchen\",\n", "    \"Home Appliances\",\n", "    \"Home Furnishing and Decor\",\n", "    \"Home Improvement and Accessories\",\n", "    \"Home and Kitchen\",\n", "    \"Household Items\",\n", "    \"Kitchen and Dining Needs\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"GM\"] = np.where(doi_vendor_item[\"l0\"].isin(gm_l0s), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi\"] = np.where(\n", "    doi_vendor_item[\"assortment_status\"] == \"inactive\", 30, doi_vendor_item[\"run_doi\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_po_cycle_tat = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"PO Cycle TAT\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_po_cycle_tat[\"PO CYCLE\"] = df_po_cycle_tat[\"PO CYCLE\"].astype(str)\n", "df_po_cycle_tat[\"Default TAT\"] = df_po_cycle_tat[\"Default TAT\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item = (\n", "    doi_vendor_item.reset_index()\n", "    .merge(df_po_cycle_tat, how=\"left\", left_on=[\"po_cycle\"], right_on=[\"PO CYCLE\"])\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del doi_vendor_item[\"PO CYCLE\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"tat_days\"] = np.where(\n", "    doi_vendor_item[\"assortment_status\"] == \"inactive\", 0, doi_vendor_item[\"tat_days\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item = doi_vendor_item.rename(columns={\"Default TAT\": \"default_tat\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item.tat_days.fillna(doi_vendor_item.default_tat, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["day_num = {\n", "    \"monday\": max(0 - datetime.today().weekday(), 0),\n", "    \"tuesday\": max(1 - datetime.today().weekday(), 0),\n", "    \"wednesday\": max(2 - datetime.today().weekday(), 0),\n", "    \"thursday\": max(3 - datetime.today().weekday(), 0),\n", "    \"friday\": max(4 - datetime.today().weekday(), 0),\n", "    \"saturday\": max(5 - datetime.today().weekday(), 0),\n", "    \"sunday\": max(6 - datetime.today().weekday(), 0),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"po_days_diff\"] = 0\n", "indx_lst = list(doi_vendor_item.index)\n", "for i in indx_lst:\n", "\n", "    try:\n", "        diff = min(\n", "            np.trim_zeros(\n", "                list(\n", "                    map(\n", "                        day_num.get,\n", "                        [\n", "                            item.strip().lower()\n", "                            for item in doi_vendor_item.loc[i][\"po_days\"].split(\",\")\n", "                        ],\n", "                    )\n", "                )\n", "            )\n", "        )\n", "    except:\n", "        diff = 0\n", "        continue\n", "\n", "    doi_vendor_item.at[i, \"po_days_diff\"] = int(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"po_days_diff\"] = np.where(\n", "    doi_vendor_item[\"assortment_status\"] == \"inactive\",\n", "    0,\n", "    doi_vendor_item[\"po_days_diff\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi\"].fillna(19, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi\"] = np.where(\n", "    doi_vendor_item[\"l0\"].isin(gm_l0s), 45, doi_vendor_item[\"run_doi\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi\"] = doi_vendor_item[\"run_doi\"].apply(np.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"category_buffer\"] = 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_vendor_item[\"run_doi_tat\"] = (\n", "    doi_vendor_item[\"run_doi\"]\n", "    + doi_vendor_item[\"tat_days\"]\n", "    + doi_vendor_item[\"po_days_diff\"]\n", "    + doi_vendor_item[\"category_buffer\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doi_vendor_item[['run_doi_tat','run_doi','tat_days','po_days_diff']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### iii. Actual and forecast sale due to DOI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Actual sales DF\n", "act_sales_doi = doi_vendor_item[[\"run_doi\", \"run_doi_tat\"]].join(\n", "    sales_back_30_w[list(sales_back_30_w.columns)[3:]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m_a = (\n", "    np.arange(len(act_sales_doi[list(act_sales_doi.columns)[2:]].columns))\n", "    < act_sales_doi[\"run_doi\"][:, None]\n", ")\n", "act_sales_doi[\"act_doi_sale\"] = np.where(\n", "    m_a,\n", "    act_sales_doi[list(act_sales_doi.columns)[2:]]\n", "    .replace(np.nan, 0.0, regex=True)\n", "    .values,\n", "    0,\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Forecast sales DF\n", "fore_sales_doi = doi_vendor_item[[\"run_doi\", \"run_doi_tat\"]].join(\n", "    fore_fwd_60_w[list(fore_fwd_60_w.columns)]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Total sales on forecast CPD and run_doi\n", "m_f = (\n", "    np.arange(len(fore_sales_doi[list(fore_sales_doi.columns)[2:]].columns))\n", "    < fore_sales_doi[\"run_doi\"][:, None]\n", ")\n", "fore_sales_doi[\"fore_doi_sale\"] = np.where(\n", "    m_f,\n", "    fore_sales_doi[list(fore_sales_doi.columns)[2:]]\n", "    .replace(np.nan, 0.0, regex=True)\n", "    .values,\n", "    0,\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m_a_rt = (\n", "    np.arange(len(act_sales_doi[list(act_sales_doi.columns)[2:]].columns))\n", "    < act_sales_doi[\"run_doi_tat\"][:, None]\n", ")\n", "act_sales_doi[\"act_doi_tat_sale\"] = np.where(\n", "    m_a_rt,\n", "    act_sales_doi[list(act_sales_doi.columns)[2:]]\n", "    .replace(np.nan, 0.0, regex=True)\n", "    .values,\n", "    0,\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Total sales on forecast CPD and run_doi + TAT + PO days diff\n", "m_f_rt = (\n", "    np.arange(len(fore_sales_doi[list(fore_sales_doi.columns)[2:]].columns))\n", "    < fore_sales_doi[\"run_doi_tat\"][:, None]\n", ")\n", "fore_sales_doi[\"fore_doi_tat_sale\"] = np.where(\n", "    m_f_rt,\n", "    fore_sales_doi[list(fore_sales_doi.columns)[2:]]\n", "    .replace(np.nan, 0.0, regex=True)\n", "    .values,\n", "    0,\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accu_df = accu_df.join(fore_sales_doi[[\"fore_doi_sale\", \"fore_doi_tat_sale\"]]).join(\n", "    act_sales_doi[[\"act_doi_sale\", \"act_doi_tat_sale\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accu_df = accu_df.join(act_sales_doi[[\"run_doi\", \"run_doi_tat\"]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Total sales on actual CPD and run_doi\n", "accu_df[\"act_doi_sale\"] = accu_df[\"back_avg\"] * accu_df[\"run_doi\"]\n", "\n", "# Total sales on actual CPD and run_doi + TAT + PO days diff\n", "accu_df[\"act_doi_tat_sale\"] = accu_df[\"back_avg\"] * accu_df[\"run_doi_tat\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del accu_df[\"run_doi\"]\n", "del accu_df[\"run_doi_tat\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### iv. Excess due to DOI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df['final_sale_tag'] = np.where(accu_df['Variation'] > 2.0, 'actual', 'forecast')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accu_df[\"final_sale\"] = np.where(\n", "    accu_df[\"fore_doi_sale\"] > 0, accu_df[\"fore_doi_sale\"], accu_df[\"act_doi_sale\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accu_df[\"final_doi_tat_sale\"] = np.where(\n", "    accu_df[\"fore_doi_tat_sale\"] > 0,\n", "    accu_df[\"fore_doi_tat_sale\"],\n", "    accu_df[\"act_doi_tat_sale\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df['final_sale'] = np.where((accu_df['back_avg'] > 0.0) & (accu_df['fore_fwd_avg'] == 0.0), accu_df['act_doi_sale'], accu_df['final_sale'])\n", "accu_df[\"final_sale\"] = np.where(\n", "    accu_df[\"fore_fwd_avg\"] > 0, accu_df[\"final_sale\"], accu_df[\"act_doi_sale\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df['final_doi_tat_sale'] = np.where((accu_df['back_avg'] > 0.0) & (accu_df['fore_fwd_avg'] == 0.0), accu_df['act_doi_tat_sale'], accu_df['fore_doi_tat_sale'])\n", "accu_df[\"final_doi_tat_sale\"] = np.where(\n", "    accu_df[\"fore_fwd_avg\"] > 0.0,\n", "    accu_df[\"fore_doi_tat_sale\"],\n", "    accu_df[\"act_doi_tat_sale\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df['final_sale_tag'] = np.where((accu_df['back_avg'] > 0.0) & (accu_df['fore_fwd_avg'] == 0.0), 'actual', accu_df['final_sale_tag'])\n", "accu_df[\"final_sale_tag\"] = np.where(\n", "    accu_df[\"fore_fwd_avg\"] > 0.0, \"forecast\", \"actual\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# accu_df\n", "df_ageing_report[\"ci\"] = df_ageing_report[list(df_ageing_report.columns)[1:]].sum(\n", "    axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w = df_ageing_report.join(doi_vendor_item).join(accu_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Excess on run_doi\n", "excess_w[\"excess_doi\"] = np.where(\n", "    excess_w[\"final_sale\"] < excess_w[\"ci\"], excess_w[\"ci\"] - excess_w[\"final_sale\"], 0\n", ")\n", "\n", "# Excess on run_doi + TAT + PO days diff\n", "excess_w[\"excess_doi_tat\"] = np.where(\n", "    excess_w[\"final_doi_tat_sale\"] < excess_w[\"ci\"],\n", "    excess_w[\"ci\"] - excess_w[\"final_doi_tat_sale\"],\n", "    0,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### v. Excess due to Ageing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w = (\n", "    excess_w.reset_index()\n", "    .merge(df_ageing_cutoff, how=\"left\", left_on=[\"l2\"], right_on=[\"l2\"])\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cut_dict = {\n", "    \"1\": 0,\n", "    \"3\": 1,\n", "    \"5\": 2,\n", "    \"10\": 3,\n", "    \"15\": 4,\n", "    \"30\": 5,\n", "    \"45\": 6,\n", "    \"60\": 7,\n", "    \"75\": 8,\n", "    \"90\": 9,\n", "    \"120\": 10,\n", "    \"150\": 11,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w[\"cutoff\"].fillna(0, inplace=True)\n", "excess_w[\"cutoff\"] = excess_w[\"cutoff\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w[\"cut_map\"] = excess_w[\"cutoff\"]\n", "excess_w[\"cut_ma\"] = excess_w[\"cut_map\"].replace(cut_dict, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del excess_w[\"cut_ma\"]\n", "del excess_w[\"l0_y\"]\n", "del excess_w[\"l1_y\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m_c = (\n", "    np.arange(len(excess_w[list(excess_w.columns)[1:14]].columns))\n", "    > excess_w[\"cut_map\"][:, None]\n", ")\n", "excess_w[\"excess_ageing\"] = np.where(\n", "    m_c, excess_w[list(excess_w.columns)[1:14]].values, 0\n", ").sum(axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### vi. Final excess inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Total Excess on run_doi\n", "excess_w[\"final_excess_inv\"] = excess_w[[\"excess_doi\", \"excess_ageing\"]].values.max(1)\n", "\n", "# Total Excess on run_doi + TAT + PO days diff\n", "excess_w[\"final_excess_inv_doi_tat\"] = excess_w[\n", "    [\"excess_doi_tat\", \"excess_ageing\"]\n", "].values.max(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w = excess_w.join(bucket_w)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2 = excess_w.join(io_base[[\"item_id\"]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. Prioritisation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### i. Credit days"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_vendor_cd = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Vendor credit days\"\n", ")\n", "df_vendor_mcd = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Mfg credit days\"\n", ")\n", "df_vendor_md = pb.from_sheets(\"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Max-DOI\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_vendor_mcd[[\"Facility name\", \"Mfg\", \"Credit Days\"]] = df_vendor_mcd[\n", "    [\"Facility name\", \"Mfg\", \"Credit Days\"]\n", "].astype(str)\n", "df_vendor_mcd[\"Credit Days\"] = df_vendor_mcd[\"Credit Days\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def revised_cd(cd_df, cd_col, suff):\n", "\n", "    col_nam = \"revised_credit_days\" + suff\n", "    cd_df[col_nam] = 0.0\n", "    for i in range(len(cd_df)):\n", "        cd = cd_df.loc[i][cd_col]\n", "        try:\n", "            cd = float(cd)\n", "            if cd <= 1.0:\n", "                rcd = 1.0\n", "            if cd > 1.0 and cd <= 3.0:\n", "                rcd = 3.0\n", "            if cd > 3.0 and cd <= 5.0:\n", "                rcd = 5.0\n", "            if cd > 5.0 and cd <= 10.0:\n", "                rcd = 10.0\n", "            if cd > 10.0 and cd <= 15.0:\n", "                rcd = 15.0\n", "            if cd > 15.0 and cd <= 30.0:\n", "                rcd = 30.0\n", "            if cd > 30.0 and cd <= 45.0:\n", "                rcd = 45.0\n", "            if cd > 45.0 and cd <= 60.0:\n", "                rcd = 60.0\n", "            if cd > 60.0 and cd <= 75.0:\n", "                rcd = 75.0\n", "            if cd > 75.0 and cd <= 90.0:\n", "                rcd = 90.0\n", "            if cd > 90.0 and cd <= 120.0:\n", "                rcd = 120.0\n", "            if cd > 120.0 and cd <= 150.0:\n", "                rcd = 150.0\n", "\n", "        except:\n", "            continue\n", "\n", "        cd_df.at[i, col_nam] = int(rcd)\n", "\n", "    return cd_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_vendor_cd = revised_cd(df_vendor_cd, \"Credit Days\", \"_vendor\")\n", "df_vendor_mcd = revised_cd(df_vendor_mcd, \"Credit Days\", \"_mfg\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_vendor_cd[\"SSC outlet_id\"] = df_vendor_cd[\"SSC outlet_id\"].astype(int)\n", "df_vendor_cd[\"Vendor Name\"] = df_vendor_cd[\"Vendor Name\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2 = (\n", "    excess_w2.reset_index()\n", "    .merge(\n", "        df_vendor_cd[\n", "            [\n", "                \"SSC outlet_id\",\n", "                \"Vendor Name\",\n", "                \"Credit Days\",\n", "                \"revised_credit_days_vendor\",\n", "            ]\n", "        ],\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"vendor_name\"],\n", "        right_on=[\"SSC outlet_id\", \"Vendor Name\"],\n", "    )\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2 = (\n", "    excess_w2.reset_index()\n", "    .merge(\n", "        df_vendor_mcd[\n", "            [\"Facility name\", \"Mfg\", \"Credit Days\", \"revised_credit_days_mfg\"]\n", "        ],\n", "        how=\"left\",\n", "        left_on=[\"facility\", \"manufacturer_name\"],\n", "        right_on=[\"Facility name\", \"Mfg\"],\n", "    )\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2 = excess_w2.loc[~excess_w2.index.duplicated(keep=\"first\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del excess_w2[\"Vendor Name\"]\n", "del excess_w2[\"SSC outlet_id\"]\n", "del excess_w2[\"Facility name\"]\n", "del excess_w2[\"Mfg\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"run_credit_days\"] = np.where(\n", "    excess_w2[\"revised_credit_days_mfg\"] == np.nan,\n", "    excess_w2[\"revised_credit_days_vendor\"],\n", "    excess_w2[\"revised_credit_days_mfg\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"run_credit_days\"].fillna(30, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"run_credit_days\"] = excess_w2[\"run_credit_days\"].astype(int).astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"cut_map_cd\"] = excess_w2[\"run_credit_days\"]\n", "excess_w2[\"cut_ma\"] = excess_w2[\"cut_map_cd\"].replace(cut_dict, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del excess_w2[\"cut_ma\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m_cd = (\n", "    np.arange(len(excess_w2[list(excess_w2.columns)[1:14]].columns))\n", "    > excess_w2[\"cut_map_cd\"][:, None]\n", ")\n", "excess_w2[\"excess_credit_days\"] = np.where(\n", "    m_cd, excess_w2[list(excess_w2.columns)[1:14]].values, 0\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"excess_credit_days_share\"] = (\n", "    excess_w2[\"excess_credit_days\"] / excess_w2[\"ci\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w2[\"excess_credit_days_final\"] = np.where(\n", "    excess_w2[\"excess_credit_days_share\"] > 0.6, 1, 0\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### ii. sales contribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con_w = (\n", "    excess_w2[[\"item_id\", \"facility_id\", \"l0_x\"]].join(df_back_avg).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con_w = sales_con_w.sort_values(\n", "    by=[\"facility_id\", \"l0_x\", \"back_avg\"], ascending=[True, True, False]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con_c = sales_con_w.join(\n", "    sales_con_w.groupby([\"facility_id\", \"l0_x\"])[[\"back_avg\"]]\n", "    .cumsum()\n", "    .rename(columns={\"back_avg\": \"cumm\"})\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con_t = (\n", "    sales_con_w.groupby([\"facility_id\", \"l0_x\"])\n", "    .sum()[[\"back_avg\"]]\n", "    .reset_index()\n", "    .rename(columns={\"back_avg\": \"tot\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con = sales_con_c.merge(\n", "    sales_con_t,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"l0_x\"],\n", "    right_on=[\"facility_id\", \"l0_x\"],\n", ").set_index(\"key\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_con[\"sale_contribution\"] = sales_con[\"cumm\"] / sales_con[\"tot\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w3 = excess_w2.join(sales_con[[\"cumm\", \"tot\", \"sale_contribution\"]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w3[\"sale_contribution\"].fillna(1.0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w3[\"excess_sale_contribution\"] = np.where(\n", "    excess_w3[\"sale_contribution\"] < 0.8, excess_w3[\"final_excess_inv\"], 0\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### iii. storage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_storage = pb.from_sheets(\"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Storage\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_storage_w = df_storage[[\"facility\", \"Regular\", \"Heavy\", \"Cold\", \"Frozen\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_storage_w[\"Regular\"] = df_storage_w[\"Regular\"].astype(float)\n", "df_storage_w[\"Heavy\"] = df_storage_w[\"Heavy\"].astype(float)\n", "df_storage_w[\"Cold\"] = df_storage_w[\"Cold\"].astype(float)\n", "df_storage_w[\"Frozen\"] = df_storage_w[\"Frozen\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_storage_w1 = pd.melt(\n", "    df_storage_w, id_vars=[\"facility\"], var_name=\"storage type\"\n", ").rename(columns={\"value\": \"capacity utilised\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w3[\"storage_type\"].fillna(\"Regular\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4 = (\n", "    excess_w3.reset_index()\n", "    .merge(\n", "        df_storage_w1,\n", "        how=\"left\",\n", "        left_on=[\"facility\", \"storage_type\"],\n", "        right_on=[\"facility\", \"storage type\"],\n", "    )\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"excess_sf\"] = np.where(\n", "    excess_w4[\"capacity utilised\"] > 0.9, excess_w4[\"final_excess_inv_doi_tat\"], 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DOI Priority\n", "# df_vendor_md['Max DOI'] = df_vendor_md['Max DOI'].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_vendor_md"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['Max DOI'] = ((excess_w4['run_doi']+1)/15).apply(np.ceil)*15"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['doi_check_po_tat'] = excess_w4['trigger_doi_po'] + excess_w4['tat_days']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['doi_check_po_tat_7'] = excess_w4['trigger_doi_po'] + excess_w4['tat_days'] + 7\n", "# excess_w4[['trigger_doi','max_ordering_doi','threshold_doi','trigger_doi_po','run_doi','tat_days','doi_check_po_tat','doi_check_po_tat_7']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['Max DOI'] = ((excess_w4['run_doi']+1)/15).apply(np.ceil)*15"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# list(excess_w3.columns)\n", "# excess_w4 = excess_w4.reset_index().merge(df_vendor_md[['PO CYCLE','Max DOI']], how = 'left', left_on=['po_cycle'], right_on=['PO CYCLE']).set_index('key')\n", "# del excess_w4[\"PO CYCLE\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fore_sales_doi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# m_ma = np.arange(len(fore_sales_doi.join(excess_w4['doi_check_po_tat_7'])[list(fore_sales_doi.columns)[2:62]].columns)) < fore_sales_doi.join(excess_w4['doi_check_po_tat_7'])['doi_check_po_tat_7'][:, None]\n", "# fore_sales_doi['fore_doi_check_sale'] = np.where(m_ma, fore_sales_doi[list(fore_sales_doi.columns)[2:62]].values, 0).sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fore_sales_doi\n", "# m_ms = np.arange(len(act_sales_doi.join(excess_w4['Max DOI'])[list(act_sales_doi.columns)[1:31]].columns)) < act_sales_doi.join(excess_w4['Max DOI'])['Max DOI'][:, None]\n", "# act_sales_doi['act_max_doi_sale'] = np.where(m_ms, act_sales_doi[list(act_sales_doi.columns)[1:31]].replace(np.nan,0.0, regex=True).values, 0).sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fore_sales_doi\n", "# excess_w4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4 = excess_w4.join(fore_sales_doi[['fore_doi_check_sale']])\n", "# excess_w4['act_doi_check_sale'] = excess_w4['back_avg']*excess_w4['doi_check_po_tat_7']\n", "# excess_w4['final_doi_check_sale'] = np.where(excess_w4['final_sale_tag'] == 'actual', excess_w4['act_doi_check_sale'], excess_w4['fore_doi_check_sale'])\n", "# excess_w4['excess_doi_check'] = np.where(excess_w4['final_doi_check_sale'] < excess_w4['ci'], excess_w4['ci'] - excess_w4['final_doi_check_sale'], 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### iv. ptype"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['assortment_status','facility','ptype','ci','']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ptype_f = (\n", "    excess_w4.loc[excess_w4[\"assortment_status\"] == \"active\"]\n", "    .groupby([\"facility\", \"ptype\"])\n", "    .sum()[[\"ci\", \"final_doi_tat_sale\"]]\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ptype_f[\"ptype_excess_inv\"] = np.where(\n", "    ptype_f[\"final_doi_tat_sale\"] < ptype_f[\"ci\"],\n", "    ptype_f[\"ci\"] - ptype_f[\"final_doi_tat_sale\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4 = (\n", "    excess_w4.reset_index()\n", "    .merge(\n", "        ptype_f[[\"facility\", \"ptype\", \"ptype_excess_inv\"]],\n", "        how=\"left\",\n", "        left_on=[\"facility\", \"ptype\"],\n", "        right_on=[\"facility\", \"ptype\"],\n", "    )\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"excess_ptype\"] = np.where(excess_w4[\"ptype_excess_inv\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# quantity to value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4 = excess_w4.join(lp_info_w)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"landing_price\"] = np.where(\n", "    excess_w4[\"landing_price\"] == 0.0, 80, excess_w4[\"landing_price\"]\n", ")\n", "excess_w4[\"landing_price\"].fillna(80, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"excess_value\"] = (\n", "    excess_w4[\"final_excess_inv_doi_tat\"] * excess_w4[\"landing_price\"]\n", ")\n", "excess_w4[\"excess_value_doi\"] = (\n", "    excess_w4[\"final_excess_inv\"] * excess_w4[\"landing_price\"]\n", ")\n", "excess_w4[\"inv_value\"] = excess_w4[\"ci\"] * excess_w4[\"landing_price\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### v. excess contribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exc_con_w = excess_w4[[\"facility_id\", \"excess_value\"]].sort_values(\n", "    by=[\"facility_id\", \"excess_value\"], ascending=[True, False]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# exc_con_w\n", "exc_con_c = exc_con_w.join(\n", "    exc_con_w.groupby([\"facility_id\"])[[\"excess_value\"]]\n", "    .cumsum()\n", "    .rename(columns={\"excess_value\": \"exc_cumm\"})\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exc_con_t = (\n", "    exc_con_w.groupby([\"facility_id\"])\n", "    .sum()[[\"excess_value\"]]\n", "    .reset_index()\n", "    .rename(columns={\"excess_value\": \"exc_tot\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exc_con = exc_con_c.merge(\n", "    exc_con_t, how=\"left\", left_on=[\"facility_id\"], right_on=[\"facility_id\"]\n", ").set_index(\"key\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exc_con[\"excess_contribution\"] = exc_con[\"exc_cumm\"] / exc_con[\"exc_tot\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exc_con[\"80_per_contribution\"] = np.where(exc_con[\"excess_contribution\"] <= 0.8, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del exc_con[\"facility_id\"]\n", "del exc_con[\"excess_value\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4 = excess_w4.join(exc_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['priority'] = np.where(excess_w4['80_per_contribution'] == 1, excess_w4['priority']+1, excess_w4['priority'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['excess_credit_days'] = np.where(excess_w4['excess_credit_days']>0,1,0)\n", "excess_w4[\"excess_sale_contribution\"] = np.where(\n", "    excess_w4[\"excess_sale_contribution\"] > 0, 1, 0\n", ")\n", "excess_w4[\"excess_sf\"] = np.where(excess_w4[\"excess_sf\"] > 0, 1, 0)\n", "# excess_w4['excess_doi_check'] = np.where(excess_w4['excess_doi_check']>0,1,0)\n", "# excess_w4['excess_contribution'] = np.where(excess_w4['excess_contribution']>0,1,0)\n", "excess_w4[\"excess_ptype\"] = np.where(excess_w4[\"excess_ptype\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"excess_credit_days_final\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"excess_credit_days_final\"]\n", ")\n", "excess_w4[\"excess_sale_contribution\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"excess_sale_contribution\"]\n", ")\n", "excess_w4[\"excess_sf\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"excess_sf\"]\n", ")\n", "# excess_w4['excess_doi_check'] = np.where(excess_w4['excess_value']==0, 0, excess_w4['excess_doi_check'])\n", "excess_w4[\"80_per_contribution\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"80_per_contribution\"]\n", ")\n", "excess_w4[\"excess_ptype\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"excess_ptype\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"priority\"] = (\n", "    (\n", "        excess_w4[\n", "            [\n", "                \"excess_credit_days_final\",\n", "                \"excess_sale_contribution\",\n", "                \"excess_sf\",\n", "                \"80_per_contribution\",\n", "                \"excess_ptype\",\n", "            ]\n", "        ]\n", "        > 0\n", "    )\n", "    * 1\n", ").sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['priority'] = np.where((excess_w4['excess_doi']==0) & (excess_w4['final_excess_inv']>0), 0, excess_w4['priority'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4['priority'] = ((excess_w4[['excess_credit_days','excess_sale_contribution','excess_sf']]>0)*1).sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"priority\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, 0, excess_w4[\"priority\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### vi. actionable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"actionable\"] = np.where(\n", "    excess_w4[\"priority\"].isin([3, 4, 5]), \"Actionable\", \"Non actionable\"\n", ")\n", "# excess_w4['actionable'] = np.where(excess_w4['priority'].isin([0,1,2]), 'Non actionable', excess_w4['actionable'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"actionable\"] = np.where(\n", "    excess_w4[\"excess_value\"] == 0, np.nan, excess_w4[\"actionable\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4[\"actionable\"] = np.where(\n", "    (excess_w4[\"excess_doi\"] == 0) & (excess_w4[\"excess_value_doi\"] > 0),\n", "    \"excess due to ageing not DOI\",\n", "    excess_w4[\"actionable\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### vii. data filteration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w4 = excess_w4.join(pos_w)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excess_w4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_facility_fil = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Facility Name\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_facility_fil = df_facility_fil.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# list(df_facility_fil['facility'].values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w5 = excess_w4.loc[\n", "    excess_w4[\"facility\"].isin(list(df_facility_fil[\"facility\"].values))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w5 = (\n", "    excess_w5.reset_index()\n", "    .merge(df_facility_fil, how=\"left\", left_on=[\"facility\"], right_on=[\"facility\"])\n", "    .set_index(\"key\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# list(excess_w5.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_report_column = pb.from_sheets(\n", "    \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\", \"Report column\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_report_column = df_report_column.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w6 = excess_w5[list(df_report_column[\"columns\"].values)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7 = excess_w6.rename(\n", "    columns=df_report_column.set_index(\"columns\").to_dict()[\"revised name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7 = excess_w7.replace([\"nan\", \"None\"], np.nan)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7 = excess_w7.loc[\n", "    ~excess_w7[\"l0\"].isin([\"Vegetables & Fruits\", \"Fruits & Vegetables\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7[\"bucket_a_b\"].fillna(\"B\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ld = datetime.now().strftime(\"%Y-%m-%d\")\n", "filename = \"excess_report_\" + ld + \".csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7.to_csv(filename, index=False)\n", "logging.warning(\"\\n|| Data written to csv... ||\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# break\n", "excess_w7[\"captured_at\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excess_w7[\"facility_id\"] = excess_w7[\"facility_id\"].astype(pd.Int64Dtype())\n", "excess_w7[\"master_assortment_substate_id\"] = excess_w7[\n", "    \"master_assortment_substate_id\"\n", "].astype(pd.Int64Dtype())\n", "excess_w7[\"ordered_quantity\"] = excess_w7[\"ordered_quantity\"].astype(pd.Int64Dtype())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# list(excess_w7.columns)\n", "column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\"},\n", "    {\"name\": \"outlet\", \"type\": \"varchar\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"facility name\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_id\", \"type\": \"bigint\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"variant_description\", \"type\": \"varchar\"},\n", "    {\"name\": \"uom\", \"type\": \"varchar\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\"},\n", "    {\"name\": \"ptype\", \"type\": \"varchar\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"manufacturer_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"varchar\"},\n", "    {\"name\": \"master_assortment_substate_id\", \"type\": \"integer\"},\n", "    {\"name\": \"assortment_status\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_a_b\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_transfer_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"gm\", \"type\": \"integer\"},\n", "    {\"name\": \"item_mrp\", \"type\": \"varchar\"},\n", "    {\"name\": \"< 1 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 3 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 5 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 10 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 15 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 30 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 45 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 60 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 75 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 90 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 120 days\", \"type\": \"integer\"},\n", "    {\"name\": \"< 150 days\", \"type\": \"integer\"},\n", "    {\"name\": \"> 150 days\", \"type\": \"integer\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"integer\"},\n", "    {\"name\": \"po_days\", \"type\": \"varchar\"},\n", "    {\"name\": \"trigger_doi\", \"type\": \"float\"},\n", "    {\"name\": \"max_ordering_doi\", \"type\": \"float\"},\n", "    {\"name\": \"default_po_doi\", \"type\": \"float\"},\n", "    {\"name\": \"run_doi\", \"type\": \"float\"},\n", "    {\"name\": \"tat_days\", \"type\": \"float\"},\n", "    {\"name\": \"po_days_diff\", \"type\": \"float\"},\n", "    {\"name\": \"category_buffer\", \"type\": \"float\"},\n", "    {\"name\": \"final_run_doi\", \"type\": \"float\"},\n", "    {\"name\": \"last_30_live_days_actual_cpd\", \"type\": \"float\"},\n", "    {\"name\": \"next_30_fore_cpd\", \"type\": \"float\"},\n", "    {\"name\": \"variation\", \"type\": \"float\"},\n", "    {\"name\": \"fore_sale_qty\", \"type\": \"float\"},\n", "    {\"name\": \"act_sale_qty\", \"type\": \"float\"},\n", "    {\"name\": \"final_sale_tag\", \"type\": \"varchar\"},\n", "    {\"name\": \"final_sale_qty\", \"type\": \"float\"},\n", "    {\"name\": \"excess_doi_qty\", \"type\": \"float\"},\n", "    {\"name\": \"ageing_cutoff\", \"type\": \"integer\"},\n", "    {\"name\": \"excess_ageing_qty\", \"type\": \"float\"},\n", "    {\"name\": \"final_excess_qty\", \"type\": \"float\"},\n", "    {\"name\": \"vendor_credit_days\", \"type\": \"float\"},\n", "    {\"name\": \"revised_credit_days_vendor\", \"type\": \"float\"},\n", "    {\"name\": \"mfg_credit_days\", \"type\": \"float\"},\n", "    {\"name\": \"revised_credit_days_mfg\", \"type\": \"float\"},\n", "    {\"name\": \"run_credit_days\", \"type\": \"float\"},\n", "    {\"name\": \"excess_inv_credit_days\", \"type\": \"float\"},\n", "    {\"name\": \"excess_credit_days_share\", \"type\": \"float\"},\n", "    {\"name\": \"priority_credit_days\", \"type\": \"integer\"},\n", "    {\"name\": \"sale_contribution\", \"type\": \"float\"},\n", "    {\"name\": \"priority_sale_contribution\", \"type\": \"integer\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"capacity_utilised\", \"type\": \"float\"},\n", "    {\"name\": \"priority_storage\", \"type\": \"integer\"},\n", "    {\"name\": \"excess_contribution\", \"type\": \"float\"},\n", "    {\"name\": \"priority_excess_contribution\", \"type\": \"integer\"},\n", "    {\"name\": \"ptype_excess_inv\", \"type\": \"float\"},\n", "    {\"name\": \"priority_ptype\", \"type\": \"integer\"},\n", "    {\"name\": \"priority\", \"type\": \"integer\"},\n", "    {\"name\": \"actionable\", \"type\": \"varchar\"},\n", "    {\"name\": \"landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"excess_value\", \"type\": \"float\"},\n", "    {\"name\": \"inv_value\", \"type\": \"float\"},\n", "    {\"name\": \"open_po\", \"type\": \"varchar\"},\n", "    {\"name\": \"schedules\", \"type\": \"varchar\"},\n", "    {\"name\": \"expiries\", \"type\": \"varchar\"},\n", "    {\"name\": \"ordered_quantity\", \"type\": \"integer\"},\n", "    {\"name\": \"captured_at\", \"type\": \"timestamp\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"excess_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"item_id\", \"captured_at\"],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"append\",\n", "}\n", "pb.to_redshift(excess_w7, **kwargs)\n", "\n", "logging.warning(\"\\n|| Data written to the table... ||\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "\n", "\n", "def create_presigned_url(file_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\"s3\")\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1HO0-G6mxE7XsWCryXpLAARpo2rnWjLwy5dHgmcwRmmo\"\n", "email_id = pb.from_sheets(sheet_id, \"email\")\n", "\n", "cloud_filepath = \"/\" + filename\n", "pb.to_s3(filename, bucket_name, cloud_filepath)\n", "file_name_link = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 24 * 7\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = list(email_id[\"email\"])\n", "\n", "subject = \"Excess report \" + ld\n", "html_content = (\n", "    \"\"\"Hi Team,<br/><br/>\n", "                    <p> Please find the link to Excess report below. <br/>\n", "                         Kindly go through the same and let me know in case of any query.<br/>\n", "                             <br/> <br/><b> link: </b><br/></p>\"\"\"\n", "    + file_name_link\n", "    + \"\"\"\n", "                            <p style=\"color:red\"><br/><br/> Note: Above link will be expire in next 7 days<br/></p>\n", "                            <p><br/> <br/>\"\"\"\n", ")\n", "\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.remove(filename)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}