{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Outward guidelines Breaches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "import numpy as np\n", "\n", "!pip install matplotlib\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(datetime.now(tz).hour)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if datetime.now(tz).hour in [*range(11, 13)]:\n", "    day = datetime.now(tz).date()\n", "    start = 0\n", "    end = 8\n", "    start_ts = (\n", "        datetime.today()\n", "        .replace(hour=start, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "    end_ts = (\n", "        datetime.today()\n", "        .replace(hour=end, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "elif datetime.now(tz).hour in [*range(20, 24)]:\n", "    day = datetime.now(tz).date()\n", "    start = 8\n", "    end = 16\n", "    start_ts = (\n", "        datetime.today()\n", "        .replace(hour=start, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "    end_ts = (\n", "        datetime.today()\n", "        .replace(hour=end, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "elif datetime.now(tz).hour in [*range(4, 9)]:\n", "    day = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=1)\n", "    start = 16\n", "    end = 0\n", "    start_ts = (\n", "        (datetime.today() - <PERSON><PERSON><PERSON>(days=1))\n", "        .replace(hour=start, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "    end_ts = (\n", "        datetime.today()\n", "        .replace(hour=end, minute=0, second=0)\n", "        .strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    )\n", "print(day, start_ts, end_ts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(start_ts, end_ts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT \n", "   p.outlet_id,\n", "   p.name as outlet_name,\n", "   pp.item_id,\n", "   pp.variant_id,\n", "   idd.name as item_name,\n", "   pli.quantity,\n", "   wi.batch as expiry_date,\n", "   pp.shelf_life,\n", "   datediff(DAY,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(DAY,-pp.outward_guidelines,wi.batch)) AS nte_in_days,\n", "   ww.entity_id as sto_id,\n", "   isi.merchant_outlet_id as DS_outlet_id,\n", "   con1.name as DS_outlet_name,\n", "   (pli.updated_at + interval '5.5 hrs') as picking_time,\n", "   wii.qty as batch_quantity_remaining,\n", "   wiii.qty as total_quantity_remaining,\n", "   coalesce(i.quantity,0) as grn_quantity\n", "FROM (select p.*,c.name from lake_warehouse_location.warehouse_pick_list p\n", "      join lake_retail.console_outlet c on c.id = p.outlet_id and c.active = 1 and c.device_id <> 47\n", "      and c.business_type_id = 1\n", "      where p.picking_started_at between '{start_ts}'::timestamp - interval '5.5 hrs' and '{end_ts}'::timestamp - interval '5.5 hrs'\n", "     ) p\n", "LEFT JOIN lake_warehouse_location.warehouse_wave ww on ww.id = p.wave_id \n", "LEFT JOIN lake_warehouse_location.warehouse_pick_list_item pi ON pi.pick_list_id = p.id\n", "LEFT JOIN lake_warehouse_location.warehouse_pick_list_item_location pli ON pli.pick_list_item_id = pi.id\n", "LEFT JOIN lake_warehouse_location.warehouse_outlet_mapping wom on wom.outlet_id = p.outlet_id\n", "LEFT JOIN lake_warehouse_location.warehouse_item_location wi ON wi.variant_id = pli.variant_id AND wi.location_id = pli.location_id\n", "LEFT JOIN (select warehouse_id, variant_id, batch, sum(quantity) as qty from\n", "                 lake_warehouse_location.warehouse_item_location \n", "                 group by 1,2,3) wii on wii.warehouse_id = wom.warehouse_id and wii.variant_id = pli.variant_id and wii.batch::date = wi.batch::date\n", "LEFT JOIN (select warehouse_id, variant_id, sum(quantity) as qty from\n", "                 lake_warehouse_location.warehouse_item_location \n", "                 group by 1,2) wiii on wiii.warehouse_id = wom.warehouse_id and wiii.variant_id = pli.variant_id\n", "LEFT JOIN (select outlet_id, variant_id, coalesce(sum(\"delta\"),0) as quantity\n", "                    from lake_ims.ims_inventory_log \n", "                    where inventory_update_type_id in (1,28,76,90)\n", "                    and pos_timestamp between '{start_ts}'::timestamp - interval '5.5 hrs' and '{end_ts}'::timestamp - interval '5.5 hrs'\n", "                group by 1,2) i on i.outlet_id = p.outlet_id and i.variant_id = pli.variant_id\n", "LEFT JOIN \n", "(select \n", "    variant_id, item_id, shelf_life, \n", "        (case\n", "             when shelf_life <= 10 then ceil(0.33*shelf_life + 0.25) \n", "             when shelf_life > 10 and shelf_life < 180 then ceil(0.4*shelf_life)\n", "             when shelf_life >= 180 and shelf_life < 360 then 70\n", "             when shelf_life >= 360 then 80 else 80 end)::int as outward_guidelines\n", "from lake_rpc.product_product \n", "where active = 1) pp ON pp.variant_id = wi.variant_id\n", "LEFT JOIN lake_rpc.item_details idd on idd.item_id = pp.item_id and idd.active = 1\n", "LEFT JOIN lake_ims.ims_sto_details isi on cast(isi.sto_id as integer) = cast(ww.entity_id as integer)\n", "LEFT JOIN lake_retail.console_outlet con1 on con1.id = isi.merchant_outlet_id and con1.active = 1 and con1.device_id <> 47\n", "-- LEFT JOIN lake_warehouse_location.pick_list_log plog on plog.pick_list_id = p.id and plog.picklist_state = 10\n", "WHERE datediff(DAY,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(DAY,-pp.outward_guidelines,wi.batch)) < 0\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_data = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary = (\n", "    outward_data.groupby([\"outlet_id\", \"outlet_name\"])\n", "    .agg({\"item_id\": \"count\", \"quantity\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"instances\", \"quantity\": \"total quantity\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_data.picking_time.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\n", "            \"Outlet ID\",\n", "            \"Outlet Name\",\n", "            \"Guidelines Breached Instances\",\n", "            \"Total Quantity\",\n", "        ],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_t = pd.to_datetime(start_ts).strftime(\"%I %p\")\n", "end_t = pd.to_datetime(end_ts).strftime(\"%I %p\")\n", "print(day, start_ts, end_ts, start_t, end_t)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# store rawdata in a table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_data[\"expiry_date\"] = pd.to_datetime(outward_data[\"expiry_date\"])\n", "outward_data[\"etl_timestamp\"] = pd.to_datetime(\n", "    datetime.now(tz).strftime(\"%Y-%m-%d %H:%M:%S\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet_id of the warehouse\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"name of the outlet\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item_id of the \",\n", "    },\n", "    {\n", "        \"name\": \"variant_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"variant id of the picked item\",\n", "    },\n", "    {\n", "        \"name\": \"item_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item name for the picked item\",\n", "    },\n", "    {\n", "        \"name\": \"quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"picked quantity\",\n", "    },\n", "    {\n", "        \"name\": \"expiry_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"expiry date of the variant picked\",\n", "    },\n", "    {\n", "        \"name\": \"shelf_life\",\n", "        \"type\": \"int\",\n", "        \"description\": \"shelf_life of the variant picked\",\n", "    },\n", "    {\n", "        \"name\": \"nte_in_days\",\n", "        \"type\": \"int\",\n", "        \"description\": \"days left for near expiry\",\n", "    },\n", "    {\n", "        \"name\": \"sto_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"sto_id\",\n", "    },\n", "    {\n", "        \"name\": \"ds_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"darkstore outlet_id\",\n", "    },\n", "    {\n", "        \"name\": \"ds_outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"darkstore name\",\n", "    },\n", "    {\n", "        \"name\": \"picking_time\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"picking_time of the variant\",\n", "    },\n", "    {\n", "        \"name\": \"batch_quantity_remaining\",\n", "        \"type\": \"int\",\n", "        \"description\": \"batch quantity remaining for the same variant & batch\",\n", "    },\n", "    {\n", "        \"name\": \"total_quantity_remaining\",\n", "        \"type\": \"int\",\n", "        \"description\": \"total quantity left for that variant\",\n", "    },\n", "    {\n", "        \"name\": \"grn_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"grn quantity in the last 8 hrs for the variant\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"table records created at\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import os\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import matplotlib.pyplot as plt\n", "\n", "text = f\"\"\"Alert for {day} : {start_t} to {end_t} for instances where Outward guidelines are breached while picking STOs From Warehouses\n", "\\nTo view the raw data, use this query: https://redash-queries.grofers.com/queries/222440\"\"\"\n", "fig, ax = render_mpl_table(outward_guidelines_summary, header_columns=0)\n", "fig.savefig(\"outward_breach.png\")\n", "token = \"******************************************************\"\n", "client = WebClient(token=token)\n", "file_check = \"./outward_breach.png\"\n", "channel = \"outward_guideline_alerts\"\n", "try:\n", "    response = client.chat_postMessage(channel=channel, text=text)\n", "    filepath = file_check\n", "    response = client.files_upload(channels=channel, file=filepath)\n", "except SlackApiError as e:\n", "    assert e.response[\"ok\"] is False\n", "    assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "    print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"outward_guideline_breaches\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"sto_id\", \"variant_id\", \"picking_time\", \"expiry_date\"],\n", "    \"sortkey\": [\"sto_id\", \"item_id\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"captures the outward guidelines breaches in warehouses\",\n", "}\n", "pb.to_redshift(outward_data, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inward guidelines Breaches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select \n", "    i.outlet_id,\n", "    c.name as outlet_name,\n", "    pp.item_id,\n", "    i.variant_id,\n", "    id.name as item_name,\n", "    i.\"delta\" as quantity,\n", "    i.expiry_date,\n", "    pp.shelf_life,\n", "    datediff(DAY,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(DAY,-pp.inward_guidelines,i.expiry_date)) as inward_breach_days,\n", "    i.grn_id,\n", "    p.po_id,\n", "    o.vendor_id,\n", "    o.vendor_name,\n", "    i.created_at + interval '5.5 hrs' as inwarding_time\n", "from lake_ims.ims_inventory_stock_details i \n", "join lake_po.po_grn p on p.grn_id = i.grn_id and p.variant_id = i.variant_id \n", "join lake_po.purchase_order o on o.id = p.po_id\n", "join lake_retail.console_outlet c on c.id = i.outlet_id and c.device_id <> 47 and c.active = 1 and c.business_type_id = 1\n", "join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "join lake_rpc.item_details id on id.item_id = pp.item_id and id.active = 1\n", "where datediff(DAY,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(DAY,-pp.inward_guidelines,i.expiry_date)) < 0\n", "and i.created_at between '{start_ts}'::timestamp - interval '5.5 hrs' and '{end_ts}'::timestamp - interval '5.5 hrs'\n", "\"\"\"\n", "inward_data = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inward_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inward_guidelines_summary = (\n", "    inward_data.groupby([\"outlet_id\", \"outlet_name\"])\n", "    .agg({\"item_id\": \"count\", \"quantity\": sum})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"instances\", \"quantity\": \"total quantity\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inward_guidelines_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inward_data[\"expiry_date\"] = pd.to_datetime(inward_data[\"expiry_date\"])\n", "inward_data[\"etl_timestamp\"] = pd.to_datetime(\n", "    datetime.now(tz).strftime(\"%Y-%m-%d %H:%M:%S\")\n", ")\n", "inward_data[\"quantity\"] = inward_data[\"quantity\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inward_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet_id of the warehouse\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"name of the outlet\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item_id of the \",\n", "    },\n", "    {\n", "        \"name\": \"variant_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"variant id of the picked item\",\n", "    },\n", "    {\n", "        \"name\": \"item_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item name for the picked item\",\n", "    },\n", "    {\n", "        \"name\": \"quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"picked quantity\",\n", "    },\n", "    {\n", "        \"name\": \"expiry_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"expiry date of the variant picked\",\n", "    },\n", "    {\n", "        \"name\": \"shelf_life\",\n", "        \"type\": \"int\",\n", "        \"description\": \"shelf_life of the variant picked\",\n", "    },\n", "    {\n", "        \"name\": \"inward_breach_days\",\n", "        \"type\": \"int\",\n", "        \"description\": \"days after inward guidelines passed\",\n", "    },\n", "    {\n", "        \"name\": \"grn_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"grn_id of inward\",\n", "    },\n", "    {\n", "        \"name\": \"po_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"po_id for the inwarded items\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"vendor_id\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"vendor_name\",\n", "    },\n", "    {\n", "        \"name\": \"inwarding_time\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"inwarding_time of the variant\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"table records created at\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if inward_guidelines_summary.shape[0] > 0:\n", "    import requests\n", "    import os\n", "    from slack_sdk import WebClient\n", "    from slack_sdk.errors import SlackApiError\n", "    import matplotlib.pyplot as plt\n", "\n", "    text = f\"\"\"Alert for {day} : {start_t} to {end_t} for instances where Inward guidelines are breached while Inwarding POs in Warehouses\n", "    \\nTo view the raw data, use this query: https://redash-queries.grofers.com/queries/222844\"\"\"\n", "    fig, ax = render_mpl_table(inward_guidelines_summary, header_columns=0)\n", "    fig.savefig(\"inward_breach.png\")\n", "    token = \"******************************************************\"\n", "    client = WebClient(token=token)\n", "    file_check = \"./inward_breach.png\"\n", "    channel = \"inward_guideline_alerts\"\n", "    try:\n", "        response = client.chat_postMessage(channel=channel, text=text)\n", "        filepath = file_check\n", "        response = client.files_upload(channels=channel, file=filepath)\n", "    except SlackApiError as e:\n", "        assert e.response[\"ok\"] is False\n", "        assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "        print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inward_guideline_breaches\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"po_id\", \"variant_id\", \"inwarding_time\", \"expiry_date\"],\n", "    \"sortkey\": [\"po_id\", \"item_id\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"captures the inward guidelines breaches in warehouses\",\n", "}\n", "pb.to_redshift(inward_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}