{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "import numpy as np\n", "from datetime import datetime, date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Excess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_qu = \"\"\"\n", "with snorlax_pred as \n", "(\n", "select fc.id as facility_id,dwc.item_id, sum(dwc.consumption)*1.0/(datediff(day,min(\"date\"),max(\"date\"))+1) as cpd\n", "from lake_snorlax.date_wise_consumption dwc\n", "left join lake_retail.console_outlet co on co.id = outlet_id\n", "left join lake_crates.facility fc on fc.id = co.facility_id\n", "where \"date\" between current_date and dateadd(DAY,29, getdate())\n", "group by 1,2\n", "),\n", "product_category as (\n", "SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "TEA_tagging as \n", "(SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'),\n", "CategoryFinal as (\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL   \n", ")\n", "select fc.id as facility_id,fc.name as facility,cast(pp.item_id as int) as item_id,cf.product_id,cf.L0,cf.L1,cf.L2,id.name as product_name,sum(ii.quantity) as quantity,sp.cpd as cpd,avg(pp.variant_mrp) as mrp,avg(lp.landing_price) as landing_price\n", "from lake_ims.ims_inventory ii \n", "inner join lake_rpc.product_product pp on pp.variant_id = ii.variant_id\n", "inner join lake_retail.console_outlet co on co.id = ii.outlet_id and co.name not ilike '%%HOT%%'\n", "left join lake_rpc.item_details id on id.item_id = pp.item_id\n", "left join lake_crates.facility fc on fc.id = co.facility_id\n", "left join snorlax_pred sp on sp.facility_id  = fc.id and sp.item_id = pp.item_id\n", "left join CategoryFinal cf on cf.item_id = pp.item_id\n", "left join TEA_tagging tea on tea.item_id = pp.item_id and tea.frontend_facility_id = fc.id\n", "left join lake_ims.ims_inventory_landing_price lp ON lp.variant_id=ii.variant_id AND lp.outlet_id=ii.outlet_id\n", "where ii.active = 1 and ii.quantity > 0 and pp.outlet_type != 1 and fc.id not in (38,39,154)\n", "and co.business_type_id in (1,2,3,4,7,8,12)\n", "-- and ((lp.landing_price <= 50) or (pp.item_id in (10062321,10062320,10073557,10063442,10073558,10063250,10063253,10064665,10063254)))\n", "-- and fc.name not ilike '%%Dark%%'\n", "group by 1,2,3,4,5,6,7,8,10\n", "\"\"\"\n", "inv_data = pd.read_sql_query(sql=inv_qu, con=redshift)\n", "print(inv_data.shape)\n", "block_quantity = \"\"\"SELECT co.facility_id,x.item_id, SUM(x.Blocked_Quantity) as Blocked_Quantity\n", "FROM (\n", "select outlet_id,item_id,date(scheduled_at + interval '5.5 Hours') as delivery_date, quantity as Blocked_Quantity\n", "FROM\n", "(SELECT id, order_id, outlet as outlet_id, scheduled_at\n", "FROM lake_ims.ims_order_details\n", "where status_id  = 1) order_details\n", "LEFT JOIN\n", "lake_ims.ims_order_items as order_items\n", "ON order_details.id = order_items.order_details_id) x\n", "left join lake_retail.console_outlet co on co.id = x.outlet_id\n", "GROUP BY 1,2\"\"\"\n", "block_quantity_data = pd.read_sql_query(sql=block_quantity, con=redshift)\n", "\n", "block_quantity_data = block_quantity_data[\n", "    (block_quantity_data[\"item_id\"] > 0) & (block_quantity_data[\"blocked_quantity\"] > 0)\n", "]\n", "\n", "block_quantity_data[\"item_id\"] = block_quantity_data[\"item_id\"].astype(int)\n", "block_quantity_data[\"blocked_quantity\"] = block_quantity_data[\n", "    \"blocked_quantity\"\n", "].astype(int)\n", "\n", "oquery = \"\"\"SELECT \n", "        c.facility_id,\n", "        item_id,\n", "       sum(CASE\n", "           WHEN sto_picking_type=2 THEN MOD(expected_quantity,case_size)\n", "           ELSE expected_quantity\n", "       END) AS sto_blocked\n", "FROM\n", "  (SELECT si.*,\n", "          s.sto_picking_type,\n", "          s.outlet_id,\n", "          CASE\n", "              WHEN cz.outer_case_size IS NULL THEN 1\n", "              ELSE cz.outer_case_size\n", "          END AS case_size\n", "   FROM po.sto s\n", "   INNER JOIN po.sto_items si ON s.id=si.sto_id\n", "   LEFT JOIN\n", "     (SELECT item_id, max(CASE WHEN (outer_case_size = 0 OR outer_case_size IS NULL) THEN 1 ELSE outer_case_size END) AS outer_case_size\n", "      FROM rpc.product_product\n", "      WHERE active = 1\n", "      GROUP BY 1)AS cz ON cz.item_id=si.item_id\n", "   WHERE \n", "     s.sto_state_id=2\n", "     AND sto_picking_type IN (2,3))AS a\n", "  inner join retail.console_outlet c on a.outlet_id = c.id\n", "  group by 1,2\n", "\"\"\"\n", "opensto = pd.read_sql_query(sql=oquery, con=retail)\n", "opensto.head(2)\n", "\n", "inv_data = inv_data.merge(\n", "    block_quantity_data, how=\"left\", on=[\"facility_id\", \"item_id\"]\n", ")\n", "inv_data = inv_data.merge(opensto, how=\"left\", on=[\"facility_id\", \"item_id\"])\n", "inv_data[\"blocked_quantity\"] = inv_data[\"blocked_quantity\"].fillna(0).astype(int)\n", "inv_data[\"sto_blocked\"] = inv_data[\"sto_blocked\"].fillna(0).astype(int)\n", "inv_data[\"quantity\"] = (\n", "    inv_data[\"quantity\"] - inv_data[\"blocked_quantity\"] - inv_data[\"sto_blocked\"]\n", ")\n", "inv_data = inv_data[inv_data[\"quantity\"] > 0]\n", "inv_data = inv_data.drop(columns=[\"blocked_quantity\", \"sto_blocked\"])\n", "print(inv_data.shape)\n", "inv_data[\"val\"] = inv_data[\"quantity\"] * inv_data[\"landing_price\"]\n", "inv_data[inv_data[\"facility_id\"] == 428].val.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# nocpd = inv_data[inv_data.pred_consumption.isnull()]\n", "qu = \"\"\"\n", "    SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\"\"\"\n", "tea = pd.read_sql_query(sql=qu, con=redshift)\n", "a = pd.merge(\n", "    inv_data,\n", "    tea[[\"item_id\", \"frontend_facility_id\", \"backend_facility_id\"]],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"backend_facility_id\", \"item_id\"],\n", ")\n", "print(a.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = a.drop(columns=[\"backend_facility_id\"])\n", "b = pd.merge(\n", "    a,\n", "    inv_data[[\"facility_id\", \"item_id\", \"cpd\"]],\n", "    how=\"left\",\n", "    left_on=[\"frontend_facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "b[b.frontend_facility_id.notnull()].head()\n", "c = (\n", "    b.groupby(\n", "        [\n", "            \"facility_id_x\",\n", "            \"facility\",\n", "            \"item_id\",\n", "            \"product_id\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_name\",\n", "            \"quantity\",\n", "            \"mrp\",\n", "            \"landing_price\",\n", "        ]\n", "    )\n", "    .agg({\"cpd_x\": sum, \"cpd_y\": sum})\n", "    .reset_index()\n", ")\n", "c.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["darkstores = pd.read_sql_query(\n", "    sql=\"\"\"select facility_id from lake_retail.console_outlet co where co.business_type_id in (7,8)\"\"\",\n", "    con=redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark = c[c.facility_id_x.isin(darkstores.facility_id.unique())]\n", "\n", "ndd = c[~c.facility_id_x.isin(darkstores.facility_id.unique())]\n", "\n", "ndd.shape, dark.shape\n", "\n", "# dark[(dark[\"facility_id\"]==428) & (dark[\"item_id\"]==10000005)]\n", "\n", "ndd[\"cpd\"] = ndd[\"cpd_x\"].fillna(0) + ndd[\"cpd_y\"].fillna(0)\n", "\n", "ndd = ndd.rename(columns={\"facility_id_x\": \"facility_id\"})\n", "\n", "ndd = ndd.drop(columns={\"cpd_x\", \"cpd_y\"})\n", "\n", "dark = dark.rename(columns={\"facility_id_x\": \"facility_id\"})\n", "dark[\"cpd\"] = dark[\"cpd_x\"].fillna(0)\n", "\n", "dark = dark.drop(columns={\"cpd_x\", \"cpd_y\"})\n", "\n", "ndd.shape, dark.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"select co.facility_id,item_id, max(threshold_doi) as threshold_doi\n", "            from lake_po.outlet_item_threshold_doi x\n", "            left join lake_retail.console_outlet co on co.id = x.outlet_id \n", "            where x.active = 1 and co.business_type_id not in (7,8)\n", "            group by 1,2\"\"\"\n", "thre_ndd = pd.read_sql_query(sql=qu, con=redshift)\n", "qu_tat_po = \"\"\"select co.facility_id, x.item_id, max(x.tat_days) as tat, max(v.po_cycle) as po_cyc\n", "from lake_po.item_outlet_vendor_tat x\n", "inner join lake_retail.console_outlet co on co.id = x.outlet_id \n", "left join lake_vms.vendor_manufacturer_physical_facility_attributes v on v.facility_id = co.facility_id and v.vendor_id = x.vendor_id\n", "where x.active = 1 and v.active= 1 and co.business_type_id not in (7,8)\n", "group by 1,2\n", "\"\"\"\n", "tat_po = pd.read_sql_query(sql=qu_tat_po, con=redshift)\n", "query = \"\"\"select co.facility_id,item_id, \n", "            max(case when threshold_doi is null then 10 else (0.5*((round(threshold_doi*1.5) + 10) + ABS(round(threshold_doi*1.5) - 10))) end) as doi\n", "            from lake_po.outlet_item_threshold_doi x\n", "            left join lake_retail.console_outlet co on co.id = x.outlet_id \n", "            where x.active = 1 and co.business_type_id in (7,8)\n", "            group by 1,2\n", "            \"\"\"\n", "thre = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark = dark.merge(thre, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "dark.doi = dark.doi.fillna(10)\n", "\n", "ndd = ndd.merge(thre_ndd, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "ndd = ndd.merge(tat_po, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "ndd.head(2)\n", "\n", "ndd[\"doi\"] = ndd[\"threshold_doi\"] + ndd[\"po_cyc\"] + ndd[\"tat\"]\n", "\n", "ndd[\"doi\"] = np.where(ndd[\"doi\"] > 30, 30, ndd[\"doi\"])\n", "\n", "ndd[\"doi\"] = ndd[\"doi\"].fill<PERSON>(30)\n", "\n", "dark.shape, ndd.shape\n", "\n", "ndd = ndd.drop(columns=[\"threshold_doi\", \"tat\", \"po_cyc\"])\n", "\n", "total = pd.concat([ndd, dark], axis=0)\n", "\n", "total[\"pred_consumption\"] = total[\"cpd\"] * total[\"doi\"]\n", "\n", "total.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# add case limitations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tea.shape, total.shape)\n", "\n", "total1 = total.merge(\n", "    tea[[\"frontend_facility_id\", \"item_id\", \"backend_facility_id\"]],\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"frontend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "total1.shape\n", "\n", "ordering_query = \"\"\"\n", "select tuv.*, kuv.vendor_name, kuv.vendor_id, kuv.case_sensitivity_type, kuv.tat_days\n", "from\n", "\n", "(select x.*,y.ordering_facility_id,\n", "(case when y.ordering_facility_id is null then 'Non-TEA' else 'TEA' end) as TEA_tag,\n", "(case when y.ordering_facility_id is null then x.facility_id else y.ordering_facility_id end) as ordering_facility\n", "from\n", "(select a.item_id,a.name as Item_name, b.facility_id,c.name as manufacturer_name,a.manufacturer_id from\n", "    (select distinct p.item_id,p.name,p.weight_in_gm, p.inner_case_size, p.outer_case_size,q.manufacturer_id, max(p.updated_at) \n", "    from lake_rpc.item_details p\n", "    inner join lake_rpc.product_brand q on p.brand_id = q.id\n", "    where p.active = 1 and p.approved = 1\n", "    group by 1,2,3,4,5,6) a\n", "inner join \n", "(select item_id,city_id,facility_id,max(updated_at) from lake_rpc.product_facility_master_assortment\n", "where master_assortment_substate_id\t = 1 and facility_id in (1,3,12,15,22,24,26,29,30,32,34,42,43,48,49,63,92,140,149,268,299,513,554,555,517)\n", "and item_id not in (10063805,10063810,10045801)\n", "group by 1,2,3) b\n", "on a.item_id=b.item_id\n", "inner join lake_rpc.product_manufacturer c on a.manufacturer_id = c.id\n", "group by 1,2,3,4,5) x\n", "left join\n", "    (select distinct ab.item_id, cd.name as frontend_facility, ab.facility_id, ab.ordering_outlet,\n", "     ab.ordering_facility,ab.ordering_facility_id, ab.active\n", "     from\n", "     (select a.item_id, b.name as facility_name, b.facility_id,a.tag_value as ordering_outlet,\n", "     a.ordering_facility,a.ordering_facility_id, a.active\n", "     from\n", "     (select c.item_id, c.outlet_id, c.tag_value,d.name as ordering_facility,d.facility_id as ordering_facility_id,\n", "     c.active from lake_rpc.item_outlet_tag_mapping c\n", "     inner join\n", "     lake_retail.console_outlet d\n", "     on c.tag_value = d.id\n", "     where c.tag_type_id = 8 and c.active = 1 and c.outlet_id not in (582)\n", "     and d.facility_id is not null\n", "     group by 1,2,3,4,5,6) a\n", "     inner join \n", "     (select id, name, facility_id from lake_retail.console_outlet\n", "     where facility_id is not null) b\n", "     on\n", "     a.outlet_id = b.id\n", "     group by 3,1,2,4,5,6,7) ab\n", "     inner join\n", "     lake_retail.warehouse_facility cd\n", "     on \n", "     ab.facility_id = cd.id ) y\n", "on x.item_id = y.item_id\n", "and x.facility_id = y.facility_id ) tuv\n", "\n", "left join\n", "\n", "     (select  ab.item_id,ab.facility_id,ab.vendor_name, ab.vendor_id,ab.case_sensitivity_type,bc.tat_days\n", "     from \n", "     \n", "     (select k.*,p.vendor_name\n", "     from \n", "     (select h.item_id,h.facility_id,h.vendor_id,i.case_sensitivity_type, max(h.updated_at), max(i.updated_at) from \n", "     lake_vms.vms_vendor_facility_alignment h\n", "          left join\n", "     lake_vms.vendor_item_physical_facility_attributes i\n", "     on h.item_id = i.item_id\n", "     and h.facility_id = i.facility_id\n", "     and h.vendor_id = i.vendor_id\n", "     group by 1,2,3,4) k inner join lake_vms.vms_vendor p on k.vendor_id = p.id ) ab\n", "\n", "left join \n", "\n", "(select a.item_id,b.facility_id,a.vendor_id,a.tat_days, max(a.updated_at)\n", "from\n", "lake_po.item_outlet_vendor_tat a\n", "inner join\n", "lake_retail.console_outlet b\n", "on a.outlet_id = b.id\n", "where a.outlet_id in (435,413,466,476,852,855,713,410,432,436,521,979,604,483,1002,434,1103,\n", "427,445,527,437,1158,1052,513,581,906,429,1000,1111,1118,682,1146,448,419,479,861,864,715,418,433,\n", "440,523,606,484,529,428,446,611,1161,515,684,908,431,1113,1565,1568,1515,1623,1626,1643,1646,1576,1579,442,443)\n", "group by 1,2,3,4) bc\n", "on ab.vendor_id = bc.vendor_id\n", "and ab.facility_id = bc.facility_id\n", "and ab.item_id = bc.item_id ) kuv\n", "on tuv.item_id = kuv.item_id\n", "and tuv.ordering_facility = kuv.facility_id \n", "\"\"\"\n", "ordering = pd.read_sql_query(\n", "    sql=ordering_query, con=pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "\n", "ordering = ordering[ordering[\"tea_tag\"] == \"Non-TEA\"]\n", "\n", "ordering1 = ordering[\n", "    [\"ordering_facility\", \"tea_tag\", \"item_id\", \"case_sensitivity_type\"]\n", "].drop_duplicates()\n", "\n", "case_size = \"\"\"select distinct item_id,max(inner_case_size) as inner, max(outer_case_size) as outer from lake_rpc.product_product where active = 1\n", "group by 1\"\"\"\n", "cases = pd.read_sql_query(sql=case_size, con=pb.get_connection(\"[Warehouse] Redshift\"))\n", "\n", "tea_transfers = \"\"\"select distinct co1.facility_id as backend_facility_id,co2.facility_id as frontend_facility_id,max(case_sensitivity_type) as case_sensitivity_type\n", "from lake_po.bulk_facility_transfer_days blt\n", "left join lake_retail.console_outlet co1 on co1.id = backend_outlet_id\n", "left join lake_retail.console_outlet co2 on co2.id = frontend_outlet_id\n", "group by 1,2\n", "\"\"\"\n", "tea_case = pd.read_sql_query(\n", "    sql=tea_transfers, con=pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "\n", "ordering1.shape\n", "\n", "total2 = total1.merge(cases, on=[\"item_id\"], how=\"left\")\n", "\n", "x = total2.merge(\n", "    tea_case,\n", "    left_on=[\"backend_facility_id\", \"frontend_facility_id\"],\n", "    right_on=[\"backend_facility_id\", \"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "ordering1.head(2)\n", "\n", "y = x.merge(\n", "    ordering1,\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"ordering_facility\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "y[\"case_sensitivity\"] = y[\"case_sensitivity_type_y\"].fillna(0) + y[\n", "    \"case_sensitivity_type_x\"\n", "].fillna(0)\n", "\n", "y = y.drop(\n", "    columns=[\n", "        \"frontend_facility_id\",\n", "        \"backend_facility_id\",\n", "        \"case_sensitivity_type_x\",\n", "        \"ordering_facility\",\n", "        \"tea_tag\",\n", "        \"case_sensitivity_type_y\",\n", "    ]\n", ")\n", "\n", "y[\"pred_consumption\"] = np.where(\n", "    y[\"case_sensitivity\"] == 2,\n", "    np.ceil(y[\"pred_consumption\"] / y[\"outer\"]) * y[\"outer\"],\n", "    y[\"pred_consumption\"],\n", ")\n", "\n", "y[\"pred_consumption\"] = np.where(\n", "    y[\"case_sensitivity\"] == 1,\n", "    np.ceil(y[\"pred_consumption\"] / y[\"inner\"]) * y[\"inner\"],\n", "    y[\"pred_consumption\"],\n", ")\n", "\n", "total = y.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total[\"excess\"] = total[\"quantity\"] - total[\"pred_consumption\"]\n", "\n", "total[\"pred_consumption_value\"] = total[\"pred_consumption\"] * total[\"landing_price\"]\n", "total[\"unblocked_value\"] = total[\"landing_price\"] * total[\"quantity\"]\n", "\n", "total[\"excess_value\"] = total[\"unblocked_value\"] - total[\"pred_consumption_value\"]\n", "\n", "# g[g[\"facility_id\"].isin([264, 29, 92, 236, 15, 212, 30, 26, 3, 43, 116, 213, 48, 22, 56, 24, 50])][[\"facility_id\",\"facility\",\"item_id\",\"pred_consumption\"]]\n", "\n", "total.loc[total[\"excess\"] < 0, \"excess\"] = 0\n", "\n", "total.loc[total[\"excess_value\"] < 0, \"excess_value\"] = 0\n", "\n", "h = (\n", "    total.groupby(\n", "        [\"facility_id\", \"facility\", \"item_id\", \"product_name\", \"l0\", \"l1\", \"l2\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"quantity\": \"sum\",\n", "            \"pred_consumption\": \"sum\",\n", "            \"excess\": \"sum\",\n", "            \"landing_price\": \"mean\",\n", "            \"unblocked_value\": \"sum\",\n", "            \"pred_consumption_value\": \"sum\",\n", "            \"cpd\": \"sum\",\n", "            \"doi\": \"mean\",\n", "            \"excess_value\": \"sum\",\n", "            \"inner\": \"mean\",\n", "            \"outer\": \"mean\",\n", "            \"case_sensitivity\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "bucket = pd.read_sql_query(\n", "    sql=\"\"\"select facility_id,item_id,case when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A' else 'B' end as buckets from consumer.rpc_daily_availability where order_date = (select max(order_date) from  consumer.rpc_daily_availability)\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", ")\n", "\n", "bucket.head(2)\n", "\n", "h = h.merge(bucket, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "k = h[h[\"excess\"] > 0].drop(columns=[\"inner\", \"outer\", \"case_sensitivity\"])\n", "\n", "x = (\n", "    k.groupby([\"facility_id\", \"buckets\"])\n", "    .agg(\n", "        {\n", "            \"excess_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "x.head(2)\n", "\n", "x = pd.DataFrame(\n", "    x.pivot(index=\"facility_id\", columns=\"buckets\", values=\"excess_value\")\n", "    .reset_index()\n", "    .to_records()\n", ")\n", "\n", "x = x[[\"facility_id\", \"A\", \"B\", \"X\"]]\n", "\n", "i = (\n", "    h.groupby([\"facility_id\", \"facility\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"quantity\": \"sum\",\n", "            \"excess\": \"sum\",\n", "            \"unblocked_value\": \"sum\",\n", "            \"pred_consumption\": \"sum\",\n", "            \"pred_consumption_value\": \"sum\",\n", "            \"excess_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "l = (\n", "    k.groupby([\"facility_id\", \"facility\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"quantity\": \"sum\",\n", "            \"excess\": \"sum\",\n", "            \"unblocked_value\": \"sum\",\n", "            \"pred_consumption\": \"sum\",\n", "            \"pred_consumption_value\": \"sum\",\n", "            \"cpd\": \"sum\",\n", "            \"excess_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "l.head(2)\n", "\n", "l = l.rename(\n", "    columns={\n", "        \"item_id\": \"excess_assortment\",\n", "        \"quantity\": \"excess_unblocked_quantity\",\n", "        \"excess\": \"excess_quantity\",\n", "        \"pred_consumption\": \"excess_pred_consumption\",\n", "    }\n", ")\n", "\n", "i.shape, l.shape\n", "\n", "i.columns\n", "\n", "j = i[\n", "    [\n", "        \"facility_id\",\n", "        \"facility\",\n", "        \"item_id\",\n", "        \"quantity\",\n", "        \"unblocked_value\",\n", "        \"pred_consumption\",\n", "        \"pred_consumption_value\",\n", "    ]\n", "].merge(l, on=[\"facility_id\", \"facility\"], how=\"left\")\n", "\n", "j.head(2)\n", "\n", "j = j.rename(\n", "    columns={\n", "        \"item_id\": \"current_assortment\",\n", "        \"quantity\": \"current_quantity\",\n", "        \"unblocked_value_x\": \"current_value\",\n", "        \"excess_assortment\": \"excess_assortment\",\n", "        \"excess_quantity\": \"excess_quantity\",\n", "        \"excess_value\": \"excess_value\",\n", "    }\n", ")\n", "\n", "m = j[\n", "    [\n", "        \"facility_id\",\n", "        \"facility\",\n", "        \"current_assortment\",\n", "        \"current_quantity\",\n", "        \"current_value\",\n", "        \"excess_assortment\",\n", "        \"excess_quantity\",\n", "        \"cpd\",\n", "        \"excess_pred_consumption\",\n", "        \"excess_value\",\n", "    ]\n", "]\n", "\n", "m.excess_value.sum()\n", "\n", "m = m[m[\"cpd\"] > 0]\n", "\n", "h = h[h[\"facility_id\"].isin(m.facility_id.unique())]\n", "\n", "m[\"%excess_assortment\"] = m[\"excess_assortment\"] / m[\"current_assortment\"] * 100\n", "m[\"%excess_quantity\"] = m[\"excess_quantity\"] / m[\"current_quantity\"] * 100\n", "m[\"%excess_value\"] = m[\"excess_value\"] / m[\"current_value\"] * 100\n", "m[\"excess_doi\"] = m[\"excess_quantity\"] / m[\"cpd\"]\n", "\n", "m = m.merge(x, on=[\"facility_id\"])\n", "\n", "m[\"%bucket_X\"] = m[\"X\"] / m[\"excess_value\"] * 100\n", "m[\"%bucket_A\"] = m[\"A\"] / m[\"excess_value\"] * 100\n", "m[\"%bucket_B\"] = m[\"B\"] / m[\"excess_value\"] * 100\n", "\n", "m = round(m, 0)\n", "m = m.sort_values(by=[\"excess_value\"], ascending=False)\n", "tot = m.apply(np.sum)\n", "tot[\"facility_id\"] = \"\"\n", "tot[\"facility\"] = \"Grand_total\"\n", "tot[\"%excess_assortment\"] = tot[\"excess_assortment\"] / tot[\"current_assortment\"] * 100\n", "tot[\"%excess_quantity\"] = tot[\"excess_quantity\"] / tot[\"current_quantity\"] * 100\n", "tot[\"%excess_value\"] = tot[\"excess_value\"] / tot[\"current_value\"] * 100\n", "tot[\"excess_doi\"] = tot[\"excess_quantity\"] / tot[\"cpd\"]\n", "tot[\"%bucket_X\"] = tot[\"X\"] / tot[\"excess_value\"] * 100\n", "tot[\"%bucket_A\"] = tot[\"A\"] / tot[\"excess_value\"] * 100\n", "tot[\"%bucket_B\"] = tot[\"B\"] / tot[\"excess_value\"] * 100\n", "m = m.append(pd.DataFrame(tot.values, index=tot.keys()).T, ignore_index=True)\n", "\n", "m = m.drop(columns=[\"A\", \"B\", \"X\"])\n", "\n", "# m.to_csv(\"excess_min_30_or_po_thre.csv\")\n", "\n", "# m.to_csv(\"excess_30.csv\")\n", "\n", "pb.to_sheets(\n", "    m,\n", "    \"11CqeLp3X4xixWHjJjIfFP6_74dNM-7e0pt8Yh_NBO5w\",\n", "    \"Excess_inventory_summary\",\n", "    service_account=\"service_account\",\n", ")\n", "\n", "# h[h[\"facility_id\"]==1].to_csv(\"ndd_min_30_or_po_thre.csv\")\n", "\n", "h.head(2)\n", "\n", "h = h[\n", "    [\n", "        \"facility_id\",\n", "        \"facility\",\n", "        \"item_id\",\n", "        \"product_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"quantity\",\n", "        \"landing_price\",\n", "        \"pred_consumption\",\n", "        \"doi\",\n", "        \"excess\",\n", "        \"excess_value\",\n", "        \"buckets\",\n", "        \"inner\",\n", "        \"outer\",\n", "        \"case_sensitivity\",\n", "    ]\n", "]\n", "\n", "to_sheets(\n", "    h[h[\"excess\"] > 0],\n", "    \"1DhJER2pgKJZuJ7IZKL4dcsLcaefPzCt-HEFnP80AaQY\",\n", "    \"Excess_inventory_rawdata\",\n", "    service_account=\"service_account\",\n", ")\n", "\n", "h[\"run_date\"] = date.today()\n", "\n", "l = h.drop(columns=[\"inner\", \"outer\", \"case_sensitivity\"])\n", "\n", "l.shape, l[l[\"excess\"] > 0].shape\n", "\n", "l.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"facility_id\": \"facility identification id\",\n", "    \"facility\": \"name of the facility\",\n", "    \"item_id\": \"item identifier for the product\",\n", "    \"product_name\": \"name of the product\",\n", "    \"l0\": \"L0 category name of the item\",\n", "    \"l1\": \"L1 category name of the item\",\n", "    \"l2\": \"L2 category name of the item\",\n", "    \"quantity\": \"unblocked quantity of that item present in the facility\",\n", "    \"pred_consumption\": \"predicted consumption of the item\",\n", "    \"doi\": \"doi considered for the predicted consumption\",\n", "    \"excess\": \"excess quantity of the item as per predicted consumption\",\n", "    \"excess_value\": \"excess value of the item\",\n", "    \"buckets\": \"inventory bucket of the item\",\n", "    \"run_date\": \"date of the excess calculation\",\n", "}\n", "\n", "\n", "def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "\n", "column_dtypes = redshift_schema(l)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"excess_inventory\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"run_date\"],\n", "    \"sortkey\": [\"facility_id\"],\n", "    \"incremental_key\": \"run_date\",\n", "    \"table_description\": \"Excess inventory data at facility & item level stored daily\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "pb.to_redshift(l, **kwargs)\n", "\n", "print(\"Done for Outlets\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}