{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "day = current_date.weekday()\n", "day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandas-explode\n", "import pandas_explode\n", "\n", "pandas_explode.patch()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extracting parameters from sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1IjkWEXox9wW8kbmiZ-BhLtpkAAay-im63lsqwBM_aPA\"\n", "input_data = pb.from_sheets(sheet_id, \"input_sheet\", clear_cache=True)\n", "input_data[\n", "    [\"transfer_outlet\", \"destination_outlet\", \"picking_capacity\", \"truck_load\"]\n", "] = input_data[\n", "    [\"transfer_outlet\", \"destination_outlet\", \"picking_capacity\", \"truck_load\"]\n", "].astype(\n", "    \"int\"\n", ")\n", "input_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining heavy capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_data[\"picking_capacity_heavy\"] = input_data[\"picking_capacity\"] * 0.2\n", "input_data[\"truck_load_heavy\"] = input_data[\"truck_load\"] * 0.2\n", "input_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list(input_data[\"transfer_outlet\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Destination and transfer outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_outlet = list(input_data[\"transfer_outlet\"])\n", "destination_outlet = list(input_data[\"transfer_outlet\"])\n", "\n", "destination_outlet, transfer_outlet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility outlet mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id, facility_id\n", "from retail.console_outlet\n", "\"\"\"\n", "\n", "facility = pd.read_sql_query(\n", "    query,\n", "    retail,\n", ")\n", "facility = facility.dropna()\n", "facility.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Case size of items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_size_of_item = \"\"\"select item_id,\n", "    max(case when (outer_case_size = 0 or outer_case_size is NULL)  then 1 else outer_case_size end) as outer_case_size \n", "    from rpc.product_product\n", "    where active = 1\n", "    group by 1\n", "    \"\"\"\n", "\n", "case_size = pd.read_sql_query(case_size_of_item, retail)\n", "case_size.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.item_id,\n", "                r.name as item_name\n", "FROM rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\"\"\"\n", "\n", "item_name = pd.read_sql_query(query, retail)\n", "item_name.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weight of items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.item_id,\n", "                r.weight_in_gm\n", "FROM rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\"\"\"\n", "\n", "weight = pd.read_sql_query(query, retail)\n", "weight.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transfer and destination facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_facility = input_data.merge(\n", "    facility, left_on=[\"transfer_outlet\"], right_on=[\"outlet_id\"], how=\"inner\"\n", ")\n", "transfer_facility[\"facility_id\"] = transfer_facility[\"facility_id\"].astype(\"int\")\n", "transfer_facility = list(transfer_facility[\"facility_id\"])\n", "facility[\"outlet_id\"] = facility[\"outlet_id\"].astype(\"int\")\n", "input_data[\"destination_outlet\"] = input_data[\"destination_outlet\"].astype(\"int\")\n", "\n", "destination_facility = input_data.merge(\n", "    facility, left_on=[\"destination_outlet\"], right_on=[\"outlet_id\"], how=\"inner\"\n", ")\n", "destination_facility[\"facility_id\"] = destination_facility[\"facility_id\"].astype(\"int\")\n", "destination_facility = list(destination_facility[\"facility_id\"])\n", "\n", "destination_facility, transfer_facility"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet active items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT o.id as outlet_id,\n", "                item_id,\n", "                master_assortment_substate_id\n", "FROM rpc.product_facility_master_assortment r\n", "INNER JOIN retail.console_outlet o ON r.facility_id=o.facility_id\n", "WHERE o.id IN %(outlets)s\n", "and master_assortment_substate_id in (1,3)\n", "\"\"\"\n", "\n", "active_items = pd.read_sql_query(\n", "    query, retail, params={\"outlets\": tuple(transfer_outlet)}\n", ")\n", "active_items = active_items.drop_duplicates()\n", "active_items.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ims inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct \n", "          a.item_id ,\n", "   a.outlet_id,\n", "          quantity  AS actual_quantity\n", "\n", "   FROM ims.ims_item_inventory as a\n", "   inner join\n", "   ( select item_id,outlet_id,max(updated_at)as update_at from ims.ims_item_inventory group by 1,2 ) as b\n", "   on a.item_id=b.item_id\n", "   and a.outlet_id=b.outlet_id\n", "   and a.updated_at=b.update_at\n", "   where  a.outlet_id IN %(outlet)s\n", "   group by 1,2,3\n", "   \"\"\"\n", "Total_inventory = pd.read_sql_query(\n", "    query, retail, params={\"outlet\": tuple(transfer_outlet)}\n", ")\n", "\n", "blocked = \"\"\"\n", "    select item_id ,\n", "     outlet_id ,\n", "     max(CASE\n", "         WHEN blocked_type = 1 THEN quantity\n", "         ELSE 0\n", "     END) AS order_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 2 THEN quantity\n", "         ELSE 0\n", "     END )AS sto_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 4 THEN quantity\n", "         ELSE 0\n", "     END )AS pro_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 5 THEN quantity\n", "         ELSE 0\n", "     END )AS sso_blocked\n", "FROM ims.ims_item_blocked_inventory\n", "where outlet_id IN %(outlet)s\n", "group by 1,2\n", "             \"\"\"\n", "block_quantity = pd.read_sql_query(\n", "    blocked, retail, params={\"outlet\": tuple(transfer_outlet)}\n", ")\n", "\n", "inv_data = Total_inventory.merge(\n", "    block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"outer\"\n", ")\n", "inv_data[\"blocked_quantity\"] = (\n", "    inv_data.order_blocked\n", "    + inv_data.sto_blocked\n", "    + inv_data.pro_blocked\n", "    + inv_data.sso_blocked\n", ")\n", "\n", "inv_data[\"available\"] = inv_data[\"actual_quantity\"] - inv_data[\"blocked_quantity\"]\n", "inv_data = inv_data[[\"outlet_id\", \"item_id\", \"available\"]].drop_duplicates()\n", "\n", "inv_data[\"available\"] = np.where(inv_data[\"available\"] < 0, 0, inv_data[\"available\"])\n", "inv_data = inv_data.drop_duplicates()\n", "inv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_facility = inv_data.merge(facility, on=[\"outlet_id\"], how=\"left\")\n", "inventory_facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Pending putaway items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select * from \n", "(SELECT \n", "o.facility_id,\n", "product.item_id,\n", "sum(case when good_inv.inventory_update_type_id in (28,76) then good_inv.quantity end ) as 'Pending_Putaway_qty'\n", "FROM ims.ims_good_inventory good_inv \n", "INNER JOIN\n", "rpc.product_product product on product.variant_id = good_inv.variant_id\n", "inner join retail.console_outlet o on o.id=good_inv.outlet_id\n", "WHERE good_inv.active = 1 AND o.facility_id in (12,26)\n", "group by 1,2)a\n", "where Pending_Putaway_qty>0\n", "\n", "\n", "\"\"\"\n", "pending_putaway = pd.read_sql_query(query, retail)\n", "pending_putaway = (\n", "    pending_putaway.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"Pending_Putaway_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "pending_putaway.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = inventory_facility.merge(\n", "    pending_putaway, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "inventory[\"Pending_Putaway_qty\"] = inventory[\"Pending_Putaway_qty\"].fillna(0)\n", "inventory[\"net_available\"] = inventory[\"available\"] - inventory[\"Pending_Putaway_qty\"]\n", "inventory[\"net_available\"] = np.where(\n", "    inventory[\"net_available\"] < 0, 0, inventory[\"net_available\"]\n", ")\n", "inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = inventory[\n", "    [\"outlet_id\", \"item_id\", \"available\", \"Pending_Putaway_qty\", \"net_available\"]\n", "].drop_duplicates()\n", "inv_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.groupby([\"outlet_id\"])[[\"available\", \"net_available\"]].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active items inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_inventory = active_items.merge(inv_data, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "active_inventory = active_inventory.fillna(0)\n", "active_inventory.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Excluding fnv, cold and perishable items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct r.item_id, \n", "r.outlet_type\n", "from rpc.product_product r\n", "inner join (select item_id,max(created_at) as dated from rpc.product_product\n", "group by 1) a on a.item_id=r.item_id\n", "where r.created_at=a.dated\n", "and r.outlet_type=2\n", "and r.perishable=0\n", "\"\"\"\n", "grocery = pd.read_sql_query(query, retail)\n", "grocery.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Grocery active inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grocery_inventory = grocery.merge(active_inventory, on=[\"item_id\"], how=\"inner\")\n", "grocery_inventory.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Storage type of the items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT item_id,\n", "          CASE\n", "              WHEN storage_type_id IN (1,\n", "                                       8) THEN 'Regular'\n", "              WHEN storage_type_id IN (2,\n", "                                       6) THEN 'Cold'\n", "              WHEN storage_type_id IN (4,\n", "                                       5) THEN 'Heavy'\n", "              WHEN storage_type_id IN (3,\n", "                                       7) THEN 'Frozen'\n", "          END AS storage_type\n", "   FROM\n", "     (SELECT item_id,\n", "             max(storage_type) AS storage_type_id\n", "      FROM lake_rpc.product_product\n", "      GROUP BY 1)x2\n", "\"\"\"\n", "\n", "storage = pd.read_sql(query, redshift)\n", "storage.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapping storage to items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_storage = grocery_inventory.merge(storage, on=[\"item_id\"], how=\"left\")\n", "inventory_storage.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CPD of the items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as outlet_id,\n", "dwc.date as delivery_date,\n", "dwc.item_id, \n", "dwc.consumption as final_cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date + interval 0 day\n", "and current_date + interval 2 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd = pd.read_sql_query(cpd_query, retail, params={\"outlet\": tuple(transfer_outlet)})\n", "cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding qty to reserve for transfer outlets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Open po (created state), considered expiry date of po"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT wom.cloud_store_id AS outlet_id,\n", "                date(convert_tz(po.expiry_date,\"+00:00\",\"+05:30\")) AS expiry_date,\n", "                pi.item_id\n", "FROM po.purchase_order po\n", "INNER JOIN po.purchase_order_status ps ON po.id=ps.po_id\n", "INNER JOIN po.purchase_order_items pi ON po.id=pi.po_id\n", "INNER JOIN retail.warehouse_outlet_mapping wom ON po.outlet_id = wom.warehouse_id\n", "WHERE wom.cloud_store_id in %(outlets)s\n", "  AND po.active=1\n", "  AND ps.po_state_id IN (2)\n", "  AND date(convert_tz(po.expiry_date,\"+00:00\",\"+05:30\")) > current_date \"\"\"\n", "\n", "open_po = pd.read_sql_query(query, retail, params={\"outlets\": tuple(transfer_outlet)})\n", "open_po = open_po.groupby([\"outlet_id\", \"item_id\"])[[\"expiry_date\"]].min().reset_index()\n", "open_po.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scheduled PO (considered scheduled po date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT f.id AS facility_id,\n", "       date(convert_timezone('Asia/Kolkata',ps.schedule_date_time)) AS schedule_date,\n", "       poi.item_id\n", "FROM lake_po.purchase_order po\n", "INNER JOIN lake_po.po_schedule ps ON po.id=ps.po_id_id\n", "INNER JOIN lake_po.purchase_order_items poi ON poi.po_id=po.id\n", "INNER JOIN lake_retail.console_outlet o ON o.id=po.outlet_id\n", "RIGHT JOIN lake_crates.facility f ON o.facility_id = f.id\n", "INNER JOIN lake_retail.console_location l ON o.tax_location_id = l.id\n", "INNER JOIN\n", "  (SELECT po_id,\n", "          sum(po_state_id) AS po_state_id\n", "   FROM\n", "     (SELECT po_id,\n", "             po_state_id\n", "      FROM lake_po.purchase_order_log\n", "      GROUP BY 1,\n", "               2)a\n", "   WHERE po_state_id IN (2,\n", "                         5,\n", "                         10)\n", "   GROUP BY 1\n", "   HAVING sum(po_state_id) =2) pol ON po.id=pol.po_id\n", "WHERE date(convert_timezone('Asia/Kolkata',ps.schedule_date_time)) >= current_date\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "\n", "scheduled_po = pd.read_sql(query, redshift)\n", "scheduled_po = (\n", "    scheduled_po.groupby([\"facility_id\", \"item_id\"])[[\"schedule_date\"]]\n", "    .min()\n", "    .reset_index()\n", ")\n", "scheduled_po.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scheduled PO Outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scheduled_po_outlet = scheduled_po.merge(facility, on=[\"facility_id\"], how=\"left\")\n", "scheduled_po_outlet = scheduled_po_outlet.dropna()\n", "scheduled_po_outlet[\"outlet_id\"] = scheduled_po_outlet[\"outlet_id\"].astype(\"int\")\n", "scheduled_po_outlet.drop([\"facility_id\"], axis=1, inplace=True)\n", "scheduled_po_outlet.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Defining functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def final_po_date(x):\n", "    if x[\"expiry_date\"] == 0:\n", "        return x[\"schedule_date\"]\n", "\n", "    elif x[\"schedule_date\"] == 0:\n", "        return x[\"expiry_date\"]\n", "\n", "    else:\n", "        return x[\"schedule_date\"]\n", "\n", "\n", "def spliter(x):\n", "    return x.split(\",\")\n", "\n", "\n", "def day(x):\n", "    if x[\"po_days\"] == \"Monday\":\n", "        return 0\n", "    if x[\"po_days\"] == \"Tuesday\":\n", "        return 1\n", "    if x[\"po_days\"] == \"Wednesday\":\n", "        return 2\n", "    if x[\"po_days\"] == \"Thursday\":\n", "        return 3\n", "    if x[\"po_days\"] == \"Friday\":\n", "        return 4\n", "    if x[\"po_days\"] == \"Saturday\":\n", "        return 5\n", "    if x[\"po_days\"] == \"Sunday\":\n", "        return 6\n", "\n", "\n", "def po_string_transform(x):\n", "    return x.lower().title()\n", "\n", "\n", "def remove_space(x):\n", "    return x.replace(\" \", \"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All open PO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_final = scheduled_po_outlet.merge(\n", "    open_po, on=[\"outlet_id\", \"item_id\"], how=\"outer\"\n", ")\n", "open_po_final = open_po_final[open_po_final[\"outlet_id\"].isin(transfer_outlet)]\n", "\n", "open_po_final = open_po_final.fillna(0)\n", "open_po_final[\"latest_po_date\"] = open_po_final.apply(final_po_date, axis=1)\n", "open_po_final = open_po_final.drop_duplicates()\n", "open_po_final = (\n", "    open_po_final.groupby([\"outlet_id\", \"item_id\"])[[\"latest_po_date\"]]\n", "    .min()\n", "    .reset_index()\n", ")\n", "open_po_final.sort_values(by=[\"outlet_id\", \"latest_po_date\"], inplace=True)\n", "\n", "items_open_po = open_po_final.copy()\n", "items_open_po[\"is_open\"] = 1\n", "items_open_po.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Not open PO (Expected incoming)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Vendot TAT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT x2.item_id,\n", "                x2.vendor_id,\n", "                ro.facility_id,\n", "                max(x2.updated_at) as updated_at\n", "FROM lake_po.item_outlet_vendor_tat x2\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=x2.outlet_id\n", "WHERE ro.facility_id in %(facility)s\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "vendor_tat_max = pd.read_sql(\n", "    query, redshift, params={\"facility\": tuple(transfer_facility)}\n", ")\n", "\n", "\n", "query = \"\"\"SELECT DISTINCT x2.item_id,\n", "                x2.vendor_id,\n", "                ro.facility_id,\n", "                x2.tat_days,\n", "                x2.updated_at\n", "FROM lake_po.item_outlet_vendor_tat x2\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=x2.outlet_id\n", "WHERE ro.facility_id in %(facility)s\n", "\"\"\"\n", "\n", "vendor_tat = pd.read_sql(query, redshift, params={\"facility\": tuple(transfer_facility)})\n", "\n", "vendor_item_tat = vendor_tat.merge(\n", "    vendor_tat_max,\n", "    on=[\"facility_id\", \"updated_at\", \"item_id\", \"vendor_id\"],\n", "    how=\"inner\",\n", ")\n", "vendor_item_tat.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Vendor info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT x4.vendor_id,\n", "       x4.facility_id,\n", "       po_days,\n", "       po_cycle,\n", "       po_cycle_type_id\n", "FROM lake_vms.vendor_manufacturer_physical_facility_attributes x4\n", "INNER JOIN\n", "  (SELECT vendor_id,\n", "          facility_id,\n", "          max(updated_at) AS updated_at\n", "   FROM lake_vms.vendor_manufacturer_physical_facility_attributes\n", "   GROUP BY 1,\n", "            2) x3 ON x3.vendor_id=x4.vendor_id\n", "AND x3.facility_id=x4.facility_id\n", "AND x3.updated_at=x4.updated_at\n", "WHERE x4.facility_id in %(facility)s\"\"\"\n", "\n", "\n", "vendor_po = pd.read_sql(query, redshift, params={\"facility\": tuple(transfer_facility)})\n", "vendor_po.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Items having no open PO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_item_tat_po = vendor_item_tat.merge(\n", "    vendor_po, on=[\"facility_id\", \"vendor_id\"], how=\"left\"\n", ")\n", "vendor_item_tat_po = vendor_item_tat_po[\n", "    [\"facility_id\", \"item_id\", \"po_days\", \"tat_days\", \"po_cycle\"]\n", "].drop_duplicates()\n", "vendor_item_tat_po_outlet = vendor_item_tat_po.merge(\n", "    facility, on=[\"facility_id\"], how=\"left\"\n", ")\n", "vendor_item_tat_po_outlet.drop([\"facility_id\"], axis=1, inplace=True)\n", "\n", "vendor_item_tat_po_outlet[\"po_days\"] = vendor_item_tat_po_outlet[\"po_days\"].astype(\n", "    \"str\"\n", ")\n", "vendor_item_tat_po_outlet[\"po_days\"] = vendor_item_tat_po_outlet[\"po_days\"].apply(\n", "    spliter\n", ")\n", "vendor_item_tat_po_outlet = vendor_item_tat_po_outlet[\n", "    vendor_item_tat_po_outlet[\"outlet_id\"].isin(transfer_outlet)\n", "]\n", "items_not_open_po = vendor_item_tat_po_outlet.merge(\n", "    items_open_po, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "items_not_open_po[\"is_open\"] = items_not_open_po[\"is_open\"].fillna(0)\n", "items_not_open_po = items_not_open_po[items_not_open_po[\"is_open\"] == 0]\n", "items_not_open_po = items_not_open_po.drop([\"latest_po_date\", \"is_open\"], axis=1)\n", "items_not_open_po = (\n", "    items_not_open_po.groupby([\"outlet_id\", \"item_id\"]).min().reset_index()\n", ")\n", "\n", "items_not_open_po = items_not_open_po.explode(\"po_days\").reset_index(drop=True)\n", "items_not_open_po[\"po_days\"] = items_not_open_po[\"po_days\"].apply(po_string_transform)\n", "items_not_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_not_open_po = items_not_open_po.dropna()\n", "items_not_open_po[\"po_days\"] = items_not_open_po[\"po_days\"].apply(remove_space)\n", "items_not_open_po[\"po_days\"] = items_not_open_po[\"po_days\"].astype(\"str\")\n", "\n", "items_not_open_po[\"day_of_week\"] = items_not_open_po.apply(day, axis=1)\n", "items_not_open_po[\"current_day\"] = current_date.weekday()\n", "items_not_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_not_open_po[\"latest_po_day_old\"] = (\n", "    items_not_open_po[\"day_of_week\"] - items_not_open_po[\"current_day\"]\n", ")\n", "items_not_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_not_open_po[\"latest_po_day_old\"] = np.where(\n", "    items_not_open_po[\"latest_po_day_old\"] < 0,\n", "    items_not_open_po[\"latest_po_day_old\"] + 7,\n", "    items_not_open_po[\"latest_po_day_old\"],\n", ")\n", "items_not_open_po = (\n", "    items_not_open_po.groupby([\"outlet_id\", \"item_id\"])[\n", "        [\"po_cycle\", \"tat_days\", \"latest_po_day_old\"]\n", "    ]\n", "    .min()\n", "    .reset_index()\n", ")\n", "items_not_open_po[\"latest_po_day\"] = (\n", "    items_not_open_po[\"latest_po_day_old\"] + items_not_open_po[\"tat_days\"]\n", ")\n", "items_not_open_po.drop([\"latest_po_day_old\"], axis=1, inplace=True)\n", "# items_not_open_po=items_not_open_po.groupby([\"item_id\"]).agg({\"latest_po_day\":\"min\"}).reset_index()\n", "items_not_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = inventory_storage.merge(\n", "    open_po_final, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data[\"latest_po_date\"] = np.where(\n", "    final_data[\"latest_po_date\"].isna(),\n", "    current_date - <PERSON><PERSON><PERSON>(days=1),\n", "    final_data[\"latest_po_date\"],\n", ")\n", "final_data[\"current_date\"] = current_date\n", "final_data[\"tat\"] = (final_data[\"latest_po_date\"] - final_data[\"current_date\"]).dt.days\n", "final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data1 = final_data.merge(\n", "    items_not_open_po, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_data1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data1[\"final_tat\"] = np.where(\n", "    final_data1[\"current_date\"] > final_data1[\"latest_po_date\"],\n", "    final_data1[\"latest_po_day\"],\n", "    final_data1[\"tat\"],\n", ")\n", "final_data1[\"po_cycle\"] = round(\n", "    final_data1[\"po_cycle\"].fillna(final_data1[\"po_cycle\"].mean()), 0\n", ")\n", "final_data1[\"latest_po_day\"] = round(\n", "    final_data1[\"latest_po_day\"].fillna(final_data1[\"latest_po_day\"].mean()), 0\n", ")\n", "final_data1[\"final_tat\"] = round(\n", "    final_data1[\"final_tat\"].fillna(final_data1[\"final_tat\"].mean()), 0\n", ")\n", "final_data1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_tat = final_data1[[\"outlet_id\", \"item_id\", \"final_tat\"]].drop_duplicates()\n", "new_tat = new_tat.groupby([\"item_id\"])[[\"final_tat\"]].min().reset_index()\n", "new_tat.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data2 = final_data1[\n", "    [\n", "        \"item_id\",\n", "        \"outlet_type\",\n", "        \"outlet_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"available\",\n", "        \"Pending_Putaway_qty\",\n", "        \"net_available\",\n", "        \"storage_type\",\n", "        \"latest_po_date\",\n", "        \"current_date\",\n", "    ]\n", "].drop_duplicates()\n", "final_data3 = final_data2.merge(new_tat, on=[\"item_id\"], how=\"left\")\n", "final_data3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transfer items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tarnsfer_items = final_data3[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"storage_type\",\n", "        \"available\",\n", "        \"Pending_Putaway_qty\",\n", "        \"net_available\",\n", "        \"final_tat\",\n", "    ]\n", "].drop_duplicates()\n", "tarnsfer_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tarnsfer_itemsv2 = tarnsfer_items.merge(cpd, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "tarnsfer_itemsv2.sort_values(by=[\"outlet_id\", \"item_id\", \"delivery_date\"], inplace=True)\n", "tarnsfer_itemsv2[\"constant\"] = 1\n", "tarnsfer_itemsv2[\"row_number\"] = tarnsfer_itemsv2.groupby([\"outlet_id\", \"item_id\"])[\n", "    \"constant\"\n", "].apply(lambda x: x.cumsum())\n", "tarnsfer_itemsv2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transfer_flag(x):\n", "    if x[\"row_number\"] <= max(1, min(x[\"final_tat\"], 6)):\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "def required_flag(x):\n", "    if x[\"row_number\"] <= x[\"final_tat\"]:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "def required_eligible(x):\n", "    return (\n", "        np.ceil(\n", "            max(0, x[\"required_qty\"] - x[\"destination_total\"]) / x[\"outer_case_size\"]\n", "        )\n", "        * x[\"outer_case_size\"]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding catering total and reserved qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer = tarnsfer_itemsv2.merge(case_size, on=[\"item_id\"], how=\"left\")\n", "transfer[\"transfer_flag\"] = transfer.apply(transfer_flag, axis=1)\n", "transfer[\"saved\"] = transfer[\"final_cpd\"] * transfer[\"transfer_flag\"]\n", "transfer.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_transfer = (\n", "    transfer.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"storage_type\",\n", "            \"available\",\n", "            \"Pending_Putaway_qty\",\n", "            \"net_available\",\n", "        ]\n", "    )[[\"saved\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"outlet_id\": \"transfer_outlet\",\n", "            \"saved\": \"reserved_qty\",\n", "            \"net_available\": \"catering_total\",\n", "            \"Pending_Putaway_qty\": \"Pending_Putaway_qty_catering\",\n", "        }\n", "    )\n", ")\n", "final_transfer.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_transfer[\"transfer_available\"] = np.where(\n", "    final_transfer[\"reserved_qty\"] > final_transfer[\"catering_total\"],\n", "    0,\n", "    final_transfer[\"catering_total\"] - final_transfer[\"reserved_qty\"],\n", ")\n", "final_transfer.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_transfer[final_transfer[\"item_id\"] == 10000192]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_transfer.groupby([\"transfer_outlet\"])[[\"transfer_available\"]].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining required qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["required = tarnsfer_itemsv2.merge(case_size, on=[\"item_id\"], how=\"left\")\n", "required[\"required_flag\"] = required.apply(required_flag, axis=1)\n", "required[\"required\"] = required[\"final_cpd\"] * required[\"required_flag\"]\n", "final_required = (\n", "    required.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"storage_type\",\n", "            \"available\",\n", "            \"Pending_Putaway_qty\",\n", "            \"net_available\",\n", "            \"outer_case_size\",\n", "        ]\n", "    )[[\"required\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"outlet_id\": \"destination_outlet\",\n", "            \"required\": \"required_qty\",\n", "            \"available\": \"destination_total\",\n", "            \"Pending_Putaway_qty\": \"Pending_Putaway_qty_destination\",\n", "        }\n", "    )\n", ")\n", "final_required.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_required[\"required_eligible\"] = final_required.apply(required_eligible, axis=1)\n", "final_required.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_required.groupby([\"destination_outlet\"])[\n", "    [\"required_eligible\"]\n", "].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapping transfer and destination outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_transfer = final_transfer.merge(\n", "    input_data[[\"transfer_outlet\", \"destination_outlet\"]],\n", "    on=[\"transfer_outlet\"],\n", "    how=\"left\",\n", ")\n", "final_data = final_transfer.merge(\n", "    final_required, on=[\"destination_outlet\", \"item_id\", \"storage_type\"], how=\"left\"\n", ")\n", "final_data = final_data.fillna(0)\n", "final_data.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining functions for transfer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def indent_estimated_qty(x):\n", "    return min(x[\"transfer_available\"], x[\"required_eligible\"])\n", "\n", "\n", "def final_indent_qty(x):\n", "    if x[\"final_indent_qty_ceil\"] <= x[\"transfer_available\"]:\n", "        return x[\"final_indent_qty_ceil\"]\n", "    else:\n", "        return x[\"final_indent_qty_floor\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data[\"indent_estimated_qty\"] = final_data.apply(indent_estimated_qty, axis=1)\n", "final_data_new = final_data.copy()\n", "\n", "final_data_new[\"final_indent_qty_floor\"] = (\n", "    np.floor(final_data_new[\"indent_estimated_qty\"] / final_data_new[\"outer_case_size\"])\n", "    * final_data_new[\"outer_case_size\"]\n", ")\n", "\n", "final_data_new[\"final_indent_qty_ceil\"] = (\n", "    np.ceil(final_data_new[\"indent_estimated_qty\"] / final_data_new[\"outer_case_size\"])\n", "    * final_data_new[\"outer_case_size\"]\n", ")\n", "\n", "final_data_new[\"final_indent_qty\"] = final_data_new.apply(final_indent_qty, axis=1)\n", "\n", "final_data_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_new.groupby([\"transfer_outlet\"])[[\"final_indent_qty\"]].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_new = final_data_new.merge(item_name, on=[\"item_id\"], how=\"left\")\n", "\n", "final_indent = final_data_new[\n", "    [\n", "        \"transfer_outlet\",\n", "        \"destination_outlet\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"storage_type\",\n", "        \"outer_case_size\",\n", "        \"catering_total\",\n", "        \"Pending_Putaway_qty_catering\",\n", "        \"net_available\",\n", "        \"reserved_qty\",\n", "        \"transfer_available\",\n", "        \"available\",\n", "        \"Pending_Putaway_qty_destination\",\n", "        \"destination_total\",\n", "        \"required_qty\",\n", "        \"required_eligible\",\n", "        \"indent_estimated_qty\",\n", "        \"final_indent_qty\",\n", "    ]\n", "].drop_duplicates()\n", "final_indent = final_indent[final_indent[\"final_indent_qty\"] > 0]\n", "final_indent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_mean = (\n", "    cpd.groupby([\"outlet_id\", \"item_id\"])[[\"final_cpd\"]]\n", "    .mean()\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"destination_outlet\"})\n", ")\n", "cpd_mean.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_doi = final_indent.merge(\n", "    cpd_mean, on=[\"destination_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "final_indent_doi[\"indent_doi\"] = (\n", "    final_indent_doi[\"final_indent_qty\"] / final_indent_doi[\"final_cpd\"]\n", ")\n", "final_indent_doi.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transfer_doi(x):\n", "    if x[\"final_cpd\"] >= 1500:\n", "        return min(x[\"indent_doi\"], 0.8)\n", "\n", "    elif x[\"final_cpd\"] < 1500 and x[\"final_cpd\"] >= 1000:\n", "        return min(x[\"indent_doi\"], 1)\n", "\n", "    elif x[\"final_cpd\"] < 1000 and x[\"final_cpd\"] >= 500:\n", "        return min(x[\"indent_doi\"], 12)\n", "\n", "    elif x[\"final_cpd\"] > 200 and x[\"final_cpd\"] <= 500:\n", "        return x[\"indent_doi\"]\n", "    else:\n", "        return x[\"indent_doi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_doi[\"transfer_doi\"] = final_indent_doi.apply(transfer_doi, axis=1)\n", "final_indent_doi[\"indent_qty\"] = (\n", "    final_indent_doi[\"final_cpd\"] * final_indent_doi[\"transfer_doi\"]\n", ")\n", "\n", "final_indent_doi[\"indent_qty\"] = (\n", "    np.floor(final_indent_doi[\"indent_qty\"] / final_indent_doi[\"outer_case_size\"])\n", "    * final_indent_doi[\"outer_case_size\"]\n", ")\n", "final_indent_doi.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_case = final_indent_doi.copy()\n", "final_indent_case[\"no_of_cases\"] = (\n", "    final_indent_case[\"indent_qty\"] / final_indent_case[\"outer_case_size\"]\n", ")\n", "final_indent_case.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight = final_indent_case.merge(weight, on=[\"item_id\"], how=\"left\")\n", "final_indent_weight[\"total_wt_kg\"] = round(\n", "    (final_indent_weight[\"indent_qty\"] * final_indent_weight[\"weight_in_gm\"]) / 1000, 2\n", ")\n", "final_indent_weight = (\n", "    final_indent_weight.groupby([\"transfer_outlet\", \"destination_outlet\", \"item_id\"])\n", "    .max()\n", "    .reset_index()\n", ")\n", "final_indent_weight = final_indent_weight.dropna()\n", "final_indent_weight[[\"indent_qty\", \"no_of_cases\"]] = final_indent_weight[\n", "    [\"indent_qty\", \"no_of_cases\"]\n", "].astype(\"int\")\n", "\n", "final_indent_weight.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight.groupby([\"destination_outlet\", \"storage_type\"]).agg(\n", "    {\n", "        \"item_id\": \"nunique\",\n", "        \"indent_qty\": \"sum\",\n", "        \"no_of_cases\": \"sum\",\n", "        \"total_wt_kg\": \"sum\",\n", "    }\n", ").reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding items rank"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT a.item_id,\n", "b.cloud_store_id as destination_outlet,\n", "                (CASE\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value ='Y' THEN 'X'\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value IS NULL THEN 'A'\n", "                     ELSE 'B'\n", "                 END) AS Final_bucket\n", "FROM rpc.item_outlet_tag_mapping a\n", "INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "AND a1.outlet_id=a.outlet_id\n", "AND a1.tag_type_id=6\n", "AND a1.tag_value='Y'\n", "WHERE b.cloud_store_id IN (100,314)\n", "  AND a.tag_type_id IN (1)\n", "  AND a.active=1\"\"\"\n", "\n", "buckets = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def priority(x):\n", "    if x[\"Final_bucket\"] == \"X\":\n", "        return 0\n", "    elif x[\"Final_bucket\"] == \"A\":\n", "        return 1\n", "    else:\n", "        return 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_buckets = final_data_new.merge(\n", "    buckets, on=[\"destination_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "item_buckets[\"priority\"] = item_buckets.apply(priority, axis=1)\n", "item_buckets = item_buckets[item_buckets[\"final_indent_qty\"] > 0]\n", "item_buckets.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_buckets_cpd = item_buckets.merge(\n", "    cpd_mean, on=[\"destination_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "item_buckets_cpd[\"ratio\"] = (\n", "    item_buckets_cpd[\"destination_total\"] / item_buckets_cpd[\"final_cpd\"]\n", ")\n", "item_buckets_cpd.sort_values(\n", "    by=[\"destination_outlet\", \"ratio\", \"priority\"],\n", "    ascending=[True, True, True],\n", "    inplace=True,\n", ")\n", "item_buckets_cpd[\"constant\"] = 1\n", "item_buckets_cpd[\"rank\"] = item_buckets_cpd.groupby([\"destination_outlet\"])[\n", "    \"constant\"\n", "].apply(lambda x: x.cumsum())\n", "\n", "item_rank = item_buckets_cpd[\n", "    [\"transfer_outlet\", \"destination_outlet\", \"item_id\", \"rank\"]\n", "].drop_duplicates()\n", "item_rank.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Picking Constraint on items "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_rank = final_indent_weight.merge(\n", "    item_rank, on=[\"transfer_outlet\", \"destination_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "final_indent_rank.sort_values(by=[\"destination_outlet\", \"rank\"], inplace=True)\n", "final_indent_rank = final_indent_rank.dropna()\n", "final_indent_rank.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Picking constraint of heavy items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_heavy = final_indent_rank[final_indent_rank[\"storage_type\"] == \"Heavy\"]\n", "final_indent_heavy.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_heavy = input_data[[\"destination_outlet\", \"picking_capacity_heavy\"]]\n", "picking_heavy.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Picking capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_heavy2 = final_indent_heavy.merge(\n", "    picking_heavy, on=[\"destination_outlet\"], how=\"left\"\n", ")\n", "final_indent_heavy2[\"cumsum_qty\"] = final_indent_heavy2.groupby([\"destination_outlet\"])[\n", "    \"indent_qty\"\n", "].apply(lambda x: x.cumsum())\n", "final_indent_heavy2[\"final_indent_qty\"] = 0\n", "final_indent_heavy2.head(2)\n", "\n", "\n", "df_heavy = pd.DataFrame()\n", "\n", "for outlet in final_indent_heavy2[\"destination_outlet\"].unique():\n", "    df1 = final_indent_heavy2[final_indent_heavy2[\"destination_outlet\"] == outlet]\n", "    df1 = df1.reset_index()\n", "    df1.drop([\"index\"], axis=1, inplace=True)\n", "    rows = df1[\"item_id\"].nunique()\n", "    indent_limit = df1[\"final_indent_qty\"].sum()\n", "    picking_capacity = df1.ix[0, \"picking_capacity_heavy\"]\n", "    for i in range(rows):\n", "        if i == 0:\n", "            df1.ix[i, \"final_indent_qty\"] = df1.ix[i, \"indent_qty\"]\n", "        elif i > 0:\n", "            df1.ix[i, \"cumsum_qty\"] = (\n", "                df1.ix[i, \"indent_qty\"] + df1.ix[i - 1, \"cumsum_qty\"]\n", "            )\n", "            if df1.ix[i, \"cumsum_qty\"] >= df1.ix[i, \"picking_capacity_heavy\"]:\n", "                df1.ix[i, \"final_indent_qty\"] = (\n", "                    np.floor(\n", "                        (\n", "                            df1.ix[i, \"picking_capacity_heavy\"]\n", "                            - df1.ix[i - 1, \"cumsum_qty\"]\n", "                        )\n", "                        / df1.ix[i, \"outer_case_size\"]\n", "                    )\n", "                    * df1.ix[i, \"outer_case_size\"]\n", "                )\n", "\n", "                break\n", "\n", "            else:\n", "                df1.ix[i, \"final_indent_qty\"] = df1.ix[i, \"indent_qty\"]\n", "                indent_limit = indent_limit + df1.ix[i, \"final_indent_qty\"]\n", "\n", "    df_heavy = pd.concat([df_heavy, df1])\n", "\n", "df_heavy = df_heavy.drop_duplicates()\n", "\n", "final_indent_heavy = df_heavy[df_heavy[\"final_indent_qty\"] > 0]\n", "final_indent_heavy[\"total_wt_kgs\"] = round(\n", "    (final_indent_heavy[\"final_indent_qty\"] * final_indent_heavy[\"weight_in_gm\"])\n", "    / 1000,\n", "    2,\n", ")\n", "final_indent_heavy.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_heavy.groupby([\"destination_outlet\"]).agg(\n", "    {\"item_id\": \"nunique\", \"final_indent_qty\": \"sum\", \"total_wt_kgs\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weight constraint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight_heavy = final_indent_heavy.copy()\n", "final_indent_weight_heavy.sort_values(by=[\"destination_outlet\", \"rank\"], inplace=True)\n", "final_indent_weight_heavy = final_indent_weight_heavy.dropna()\n", "final_indent_weight_heavy.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Heavy capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_weight_heavy = input_data[[\"destination_outlet\", \"truck_load_heavy\"]]\n", "item_weight_heavy.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight_heavy2 = final_indent_weight_heavy.merge(\n", "    item_weight_heavy, on=[\"destination_outlet\"], how=\"left\"\n", ")\n", "final_indent_weight_heavy2[\"cumsum_weight\"] = final_indent_weight_heavy2.groupby(\n", "    [\"destination_outlet\"]\n", ")[\"total_wt_kgs\"].apply(lambda x: x.cumsum())\n", "final_indent_weight_heavy2[\"indent_qty_v3\"] = 0\n", "final_indent_weight_heavy2.head(2)\n", "\n", "df2_heavy = pd.DataFrame()\n", "\n", "for outlet in final_indent_weight_heavy2[\"destination_outlet\"].unique():\n", "    df3 = final_indent_weight_heavy2[\n", "        final_indent_weight_heavy2[\"destination_outlet\"] == outlet\n", "    ]\n", "    df3 = df3.reset_index()\n", "    df3.drop([\"index\"], axis=1, inplace=True)\n", "    rows = df3[\"item_id\"].nunique()\n", "    for i in range(rows):\n", "        if i == 0:\n", "            df3.ix[i, \"cumsum_weight\"] = df3.ix[i, \"total_wt_kgs\"]\n", "            df3.ix[i, \"indent_qty_v3\"] = df3.ix[i, \"final_indent_qty\"]\n", "        elif i > 0:\n", "            df3.ix[i, \"cumsum_weight\"] = (\n", "                df3.ix[i, \"total_wt_kgs\"] + df3.ix[i - 1, \"cumsum_weight\"]\n", "            )\n", "            if df3.ix[i, \"cumsum_weight\"] > df3.ix[i, \"truck_load_heavy\"]:\n", "                quantity = (\n", "                    df3.ix[i, \"cumsum_weight\"] - df3.ix[i, \"truck_load_heavy\"]\n", "                ) * (df3.ix[i, \"final_indent_qty\"] / df3.ix[i, \"total_wt_kgs\"])\n", "                df3.ix[i, \"indent_qty_v3\"] = (\n", "                    np.floor(\n", "                        (df3.ix[i, \"final_indent_qty\"] - quantity)\n", "                        / df3.ix[i, \"outer_case_size\"]\n", "                    )\n", "                    * df3.ix[i, \"outer_case_size\"]\n", "                )\n", "                df3.ix[i, \"total_wt_kgs\"] = df3.ix[i, \"indent_qty_v3\"] * (\n", "                    df3.ix[i, \"total_wt_kgs\"] / df3.ix[i, \"final_indent_qty\"]\n", "                )\n", "            else:\n", "                df3.ix[i, \"indent_qty_v3\"] = df3.ix[i, \"final_indent_qty\"]\n", "    df2_heavy = pd.concat([df2_heavy, df3])\n", "\n", "df2_heavy = df2_heavy.drop_duplicates()\n", "df2_heavy = df2_heavy[df2_heavy[\"indent_qty_v3\"] > 0]\n", "\n", "final_heavy = df2_heavy.copy()\n", "final_heavy[\"no_of_cases\"] = (\n", "    final_heavy[\"indent_qty_v3\"] / final_heavy[\"outer_case_size\"]\n", ")\n", "final_heavy.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_heavy.groupby([\"destination_outlet\"])[\n", "    [\"indent_qty_v3\", \"total_wt_kgs\"]\n", "].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Picking constraint on regular items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_used = (\n", "    final_heavy.groupby([\"destination_outlet\"])\n", "    .agg({\"indent_qty_v3\": \"sum\", \"total_wt_kgs\": \"sum\"})\n", "    .reset_index()\n", ")\n", "picking_total = input_data[[\"destination_outlet\", \"picking_capacity\", \"truck_load\"]]\n", "capacity_regular = picking_total.merge(\n", "    capacity_used, on=[\"destination_outlet\"], how=\"left\"\n", ")\n", "\n", "capacity_regular = capacity_regular.fillna(0)\n", "\n", "capacity_regular"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_regular[\"picking_capacity_regular\"] = (\n", "    capacity_regular[\"picking_capacity\"] - capacity_regular[\"indent_qty_v3\"]\n", ")\n", "capacity_regular[\"truck_load_regular\"] = (\n", "    capacity_regular[\"truck_load\"] - capacity_regular[\"total_wt_kgs\"]\n", ")\n", "\n", "picking_regular = capacity_regular[[\"destination_outlet\", \"picking_capacity_regular\"]]\n", "picking_regular"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_regular = final_indent_rank[final_indent_rank[\"storage_type\"] == \"Regular\"]\n", "final_indent_regular.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quanitty filter on the regular items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_regular2 = final_indent_regular.merge(\n", "    picking_regular, on=[\"destination_outlet\"], how=\"left\"\n", ")\n", "final_indent_regular2[\"cumsum_qty\"] = final_indent_regular2.groupby(\n", "    [\"destination_outlet\"]\n", ")[\"indent_qty\"].apply(lambda x: x.cumsum())\n", "final_indent_regular2[\"final_indent_qty\"] = 0\n", "final_indent_regular2.head(2)\n", "\n", "\n", "df_regular = pd.DataFrame()\n", "\n", "for outlet in final_indent_regular2[\"destination_outlet\"].unique():\n", "    df1 = final_indent_regular2[final_indent_regular2[\"destination_outlet\"] == outlet]\n", "    df1 = df1.reset_index()\n", "    df1.drop([\"index\"], axis=1, inplace=True)\n", "    rows = df1[\"item_id\"].nunique()\n", "    indent_limit = df1[\"final_indent_qty\"].sum()\n", "    picking_capacity = df1.ix[0, \"picking_capacity_regular\"]\n", "    for i in range(rows):\n", "        if i == 0:\n", "            df1.ix[i, \"cumsum_qty\"] <= df1.ix[i, \"picking_capacity_regular\"]\n", "            df1.ix[i, \"final_indent_qty\"] = df1.ix[i, \"indent_qty\"]\n", "        elif i > 0:\n", "            df1.ix[i, \"cumsum_qty\"] = (\n", "                df1.ix[i, \"indent_qty\"] + df1.ix[i - 1, \"cumsum_qty\"]\n", "            )\n", "            if df1.ix[i, \"cumsum_qty\"] >= df1.ix[i, \"picking_capacity_regular\"]:\n", "                df1.ix[i, \"final_indent_qty\"] = (\n", "                    np.floor(\n", "                        (\n", "                            df1.ix[i, \"picking_capacity_regular\"]\n", "                            - df1.ix[i - 1, \"cumsum_qty\"]\n", "                        )\n", "                        / df1.ix[i, \"outer_case_size\"]\n", "                    )\n", "                    * df1.ix[i, \"outer_case_size\"]\n", "                )\n", "\n", "                break\n", "\n", "            else:\n", "                df1.ix[i, \"final_indent_qty\"] = df1.ix[i, \"indent_qty\"]\n", "                indent_limit = indent_limit + df1.ix[i, \"final_indent_qty\"]\n", "\n", "    df_regular = pd.concat([df_regular, df1])\n", "\n", "df_regular = df_regular.drop_duplicates()\n", "\n", "final_indent_regular = df_regular[df_regular[\"final_indent_qty\"] > 0]\n", "final_indent_regular[\"total_wt_kgs\"] = round(\n", "    (final_indent_regular[\"final_indent_qty\"] * final_indent_regular[\"weight_in_gm\"])\n", "    / 1000,\n", "    2,\n", ")\n", "final_indent_regular.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_regular.groupby([\"destination_outlet\"]).agg(\n", "    {\"item_id\": \"nunique\", \"final_indent_qty\": \"sum\", \"total_wt_kgs\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weight filter regular"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight_regular = final_indent_regular.copy()\n", "final_indent_weight_regular.sort_values(by=[\"destination_outlet\", \"rank\"], inplace=True)\n", "final_indent_weight_regular = final_indent_weight_regular.dropna()\n", "final_indent_weight_regular.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Regular truck load size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_weight_regular = capacity_regular[[\"destination_outlet\", \"truck_load_regular\"]]\n", "item_weight_regular.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weight filter on regular items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent_weight_regular2 = final_indent_weight_regular.merge(\n", "    item_weight_regular, on=[\"destination_outlet\"], how=\"left\"\n", ")\n", "final_indent_weight_regular2[\"cumsum_weight\"] = final_indent_weight_regular2.groupby(\n", "    [\"destination_outlet\"]\n", ")[\"total_wt_kgs\"].apply(lambda x: x.cumsum())\n", "final_indent_weight_regular2[\"indent_qty_v3\"] = 0\n", "final_indent_weight_regular2.head(2)\n", "\n", "\n", "df2_regular = pd.DataFrame()\n", "\n", "for outlet in final_indent_weight_regular2[\"destination_outlet\"].unique():\n", "    df3 = final_indent_weight_regular2[\n", "        final_indent_weight_regular2[\"destination_outlet\"] == outlet\n", "    ]\n", "    df3 = df3.reset_index()\n", "    df3.drop([\"index\"], axis=1, inplace=True)\n", "    rows = df3[\"item_id\"].nunique()\n", "    for i in range(rows):\n", "        if i == 0:\n", "            df3.ix[i, \"cumsum_weight\"] = df3.ix[i, \"total_wt_kgs\"]\n", "            df3.ix[i, \"indent_qty_v3\"] = df3.ix[i, \"final_indent_qty\"]\n", "        elif i > 0:\n", "            df3.ix[i, \"cumsum_weight\"] = (\n", "                df3.ix[i, \"total_wt_kgs\"] + df3.ix[i - 1, \"cumsum_weight\"]\n", "            )\n", "            if df3.ix[i, \"cumsum_weight\"] > df3.ix[i, \"truck_load_regular\"]:\n", "                quantity = (\n", "                    df3.ix[i, \"cumsum_weight\"] - df3.ix[i, \"truck_load_regular\"]\n", "                ) * (df3.ix[i, \"final_indent_qty\"] / df3.ix[i, \"total_wt_kgs\"])\n", "                df3.ix[i, \"indent_qty_v3\"] = (\n", "                    np.floor(\n", "                        (df3.ix[i, \"final_indent_qty\"] - quantity)\n", "                        / df3.ix[i, \"outer_case_size\"]\n", "                    )\n", "                    * df3.ix[i, \"outer_case_size\"]\n", "                )\n", "                df3.ix[i, \"total_wt_kgs\"] = df3.ix[i, \"indent_qty_v3\"] * (\n", "                    df3.ix[i, \"total_wt_kgs\"] / df3.ix[i, \"final_indent_qty\"]\n", "                )\n", "            else:\n", "                df3.ix[i, \"indent_qty_v3\"] = df3.ix[i, \"final_indent_qty\"]\n", "\n", "    df2_regular = pd.concat([df2_regular, df3])\n", "\n", "df2_regular = df2_regular.drop_duplicates()\n", "\n", "final_regular = df2_regular[df2_regular[\"indent_qty_v3\"] > 0]\n", "final_regular[\"no_of_cases\"] = (\n", "    final_regular[\"indent_qty_v3\"] / final_regular[\"outer_case_size\"]\n", ")\n", "final_regular.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final indent Regular + heavy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent = pd.concat([final_regular, final_heavy])\n", "final_indent = final_indent.drop_duplicates()\n", "final_indent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_indent = final_indent[\n", "    [\n", "        \"transfer_outlet\",\n", "        \"destination_outlet\",\n", "        \"item_id\",\n", "        \"outer_case_size\",\n", "        \"storage_type\",\n", "        \"weight_in_gm\",\n", "        \"catering_total\",\n", "        \"destination_total\",\n", "        \"indent_qty_v3\",\n", "        \"total_wt_kgs\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "final_indent[\"no_of_cases\"] = (\n", "    final_indent[\"indent_qty_v3\"] / final_indent[\"outer_case_size\"]\n", ")\n", "final_indent = final_indent.merge(item_name, on=[\"item_id\"], how=\"left\")\n", "final_indent.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = (\n", "    final_indent.groupby([\"transfer_outlet\", \"destination_outlet\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"indent_qty_v3\": \"sum\",\n", "            \"no_of_cases\": \"sum\",\n", "            \"total_wt_kgs\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "summary.rename(\n", "    columns={\"item_id\": \"Total_skus\", \"indent_qty_v3\": \"final_indent_qty\"}, inplace=True\n", ")\n", "summary[\"total_wt_kgs\"] = summary[\"total_wt_kgs\"].astype(\"int\")\n", "summary[\"final_indent_qty\"] = summary[\"final_indent_qty\"].astype(\"int\")\n", "summary[\"no_of_cases\"] = summary[\"no_of_cases\"].astype(\"int\")\n", "summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sending mailers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "current_Date = datetime.date.today() + datetime.timedelta(days=0)\n", "current_Date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html(df):\n", "    html_table = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 700px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 57px;\">\n", "    \"\"\"\n", "    for x in df.columns:\n", "        html_table += \"\"\"<td style=\"height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table += x\n", "        html_table += \"\"\"</strong></span></td>\"\"\"\n", "    html_table += \"\"\"</tr>\"\"\"\n", "    for i, r in df.iterrows():\n", "        html_table += \"\"\"<tr style=\"height: 15px;\">\"\"\"\n", "        for x in df.columns:\n", "            html_table += (\n", "                \"\"\"<td style=\"width: 150px; height: 15px; text-align: center;\">\"\"\"\n", "            )\n", "            html_table += str(r[x])\n", "            html_table += \"\"\"</td>\"\"\"\n", "        html_table += \"\"\"</tr>\"\"\"\n", "    html_table += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in final_indent[\"transfer_outlet\"].unique():\n", "    if outlet not in x:\n", "        x.append(outlet)\n", "x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = pb.from_sheets(sheet_id, \"email_id\", clear_cache=True)\n", "email.head()\n", "to_email = email[email[\"outlet_id\"] == str(100)]\n", "to_email.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = (\n", "    final_indent[\n", "        [\n", "            \"transfer_outlet\",\n", "            \"destination_outlet\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"indent_qty_v3\",\n", "            \"no_of_cases\",\n", "            \"total_wt_kgs\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(columns={\"indent_qty_v3\": \"indent_qty\"})\n", ")\n", "\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "unique_id = str(uuid.uuid1())\n", "unique_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet in x:\n", "\n", "    final_indent = final[final[\"transfer_outlet\"] == outlet]\n", "    final_indent[\"unique_id\"] = unique_id\n", "    final_indent.to_csv(\"/tmp/summary.csv\", index=False)\n", "\n", "    summary = (\n", "        final_indent.groupby([\"transfer_outlet\", \"destination_outlet\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"nunique\",\n", "                \"indent_qty\": \"sum\",\n", "                \"no_of_cases\": \"sum\",\n", "                \"total_wt_kgs\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    summary.rename(columns={\"item_id\": \"Total_skus\"}, inplace=True)\n", "    summary[\"total_wt_kgs\"] = summary[\"total_wt_kgs\"].astype(\"int\")\n", "    summary[\"indent_qty\"] = summary[\"indent_qty\"].astype(\"int\")\n", "    summary[\"no_of_cases\"] = summary[\"no_of_cases\"].astype(\"int\")\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    subject = (\n", "        \"[Inventory Rebalancing Replenishment Report] Outlet ID -\"\n", "        + str(outlet)\n", "        + \" | \"\n", "        + str(current_Date)\n", "    )\n", "\n", "    to_email = email[(email[\"outlet_id\"] == str(outlet))][\"email_id\"].tolist()\n", "\n", "    message_text = f\"\"\"Hi <PERSON>, <br><br> \n", "\n", "    Please find the attached indent <br><br> {df_to_html(summary)} \n", "    \n", "    <br><br> Unique ID: {unique_id}\n", "\n", "    <br><br> <PERSON><PERSON>,<br><PERSON><PERSON><PERSON>\"\"\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content=message_text,\n", "        files=[\"/tmp/summary.csv\"],\n", "    )\n", "\n", "    print(\"mail sent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" SELECT date(convert_tz(sto.created_at,'+00:00','+05:30')) AS indent_date,\n", "       sto.sto_id,\n", "       sto.outlet_id AS sender_outlet_id,\n", "       o.name AS sender_outlet_name,\n", "       sto.merchant_outlet_id AS receiving_outlet_id,\n", "       o1.name AS receiver_outlet_name,\n", "       convert_tz(sto.delivery_date,'+00:00','+05:30') AS delivery_date,\n", "       convert_tz(sto.created_at,'+00:00','+05:30') AS created_date,\n", "       convert_tz(sto.updated_at,'+00:00','+05:30') AS sto_status_updated_at,\n", "       sto.sto_type AS sto_type_id,\n", "       CASE\n", "           WHEN sto.sto_type = 1 THEN 'Manual'\n", "           WHEN sto.sto_type = 2 THEN 'Automated'\n", "           WHEN sto.sto_type = 3 THEN 'No INV Check STO'\n", "           WHEN sto.sto_type = 4 THEN 'B2B STO'\n", "       END AS sto_type,\n", "       sto.sto_state AS sto_state_id,\n", "       CASE\n", "           WHEN sto.sto_state = 1 THEN 'Created'\n", "           WHEN sto.sto_state = 2 THEN 'Billed'\n", "           WHEN sto.sto_state = 5 THEN 'Partial-Billed'\n", "           WHEN sto.sto_state = 3 THEN 'Expired'\n", "           WHEN sto.sto_state = 4 THEN 'Inward'\n", "           ELSE 'New State'\n", "       END AS sto_state\n", "FROM ims.ims_sto_details sto\n", "INNER JOIN retail.console_outlet o ON sto.outlet_id = o.id\n", "INNER JOIN retail.console_outlet o1 ON sto.merchant_outlet_id = o1.id\n", "WHERE date(sto.created_at) >= '2020-10-27' \n", "\n", "AND sto.outlet_id IN (314,\n", "                        100)\n", "  AND sto.merchant_outlet_id IN (314,\n", "                                 100)\n", "ORDER BY 6,\n", "         2,\n", "         1\n", "\"\"\"\n", "sto = pd.read_sql_query(query, retail)\n", "sto = sto[\n", "    [\"indent_date\", \"sto_id\", \"receiving_outlet_id\", \"sender_outlet_id\"]\n", "].drop_duplicates()\n", "sto.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" Select    \n", "      p_inv.grofers_order_id as sto_id,\n", "      p_inv.invoice_id,\n", "      convert_tz(p_inv.pos_timestamp,'+00:00','+05:30') as STO_Invoice_created_at ,\n", "      p_inv.actual_sales,\n", "            \n", "            \n", "       CASE\n", "            WHEN transfer_state = 1 THEN 'Raised'\n", "            WHEN transfer_state = 2 THEN 'GRN Complete'\n", "            WHEN transfer_state = 3 THEN 'GRN Partial'\n", "            WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "            ELSE 'NA'\n", "        END AS Invoice_State\n", "              \n", "\n", "from pos.pos_invoice p_inv  \n", "\n", "where date(p_inv.pos_timestamp)  >= '2020-10-27' \n", "and invoice_type_id in (5,14,16) and grofers_order_id != ' '\n", "\n", "\n", " \n", "\"\"\"\n", "invoice = pd.read_sql_query(query, retail)\n", "invoice.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invoice[\"sto_id\"] = invoice[\"sto_id\"].astype(\"int\")\n", "sto_invoice = sto.merge(invoice[[\"sto_id\", \"invoice_id\"]], on=[\"sto_id\"], how=\"left\")\n", "sto_invoice = sto_invoice.drop_duplicates()\n", "sto_invoice.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"Select i.sto_id,\n", "        i.item_id,\n", "       i.expected_quantity as indent_qty\n", "                    \n", "from ims.ims_sto_item i\n", "\"\"\"\n", "\n", "items = pd.read_sql_query(query, retail)\n", "items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_items = sto_invoice.merge(items, on=[\"sto_id\"], how=\"left\").rename(\n", "    columns={\"invoice_id\": \"vendor_invoice_id\"}\n", ")\n", "sto_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_items.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT date(grn_details_created_at) AS grn_date,\n", "       date(grn_details_created_at) AS putaway_date,\n", "       vendor_invoice_id,\n", "       item_id,\n", "       grn_details_created_at,\n", "       inwarded_quantity AS grn_qty\n", "FROM\n", "  (SELECT inward.vendor_invoice_id,\n", "          p.item_id,\n", "          max(convert_timezone('Asia/Kolkata',sd.created_at)) AS grn_details_created_at,\n", "          sum(coalesce(sd.\"delta\",0)) AS inwarded_quantity\n", "   FROM lake_ims.ims_inward_invoice inward\n", "   INNER JOIN lake_ims.ims_inventory_stock_details sd ON inward.grn_id = sd.grn_id\n", "   AND inward.source_type = 2\n", "   INNER JOIN lake_rpc.product_product p ON sd.variant_id = p.variant_id\n", "   WHERE date(sd.created_at) >= '2020-10-27'\n", "   GROUP BY 1,\n", "            2) x1\n", "\"\"\"\n", "\n", "grn = pd.read_sql_query(query, redshift)\n", "grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn[\"grn_date\"] = pd.to_datetime(grn[\"grn_date\"])\n", "grn[\"putaway_date\"] = pd.to_datetime(grn[\"putaway_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = pd.to_datetime(\"2099-01-01\")\n", "d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def hr_func(ts):\n", "    return ts.hour\n", "\n", "\n", "sto_items_grn = sto_items.merge(grn, on=[\"vendor_invoice_id\", \"item_id\"], how=\"left\")\n", "sto_items_grn[\"grn_hour\"] = sto_items_grn[\"grn_details_created_at\"].apply(hr_func)\n", "sto_items_grn = (\n", "    sto_items_grn[\n", "        [\n", "            \"receiving_outlet_id\",\n", "            \"sender_outlet_id\",\n", "            \"sto_id\",\n", "            \"indent_date\",\n", "            \"grn_date\",\n", "            \"grn_hour\",\n", "            \"grn_details_created_at\",\n", "            \"putaway_date\",\n", "            \"item_id\",\n", "            \"indent_qty\",\n", "            \"grn_qty\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(columns={\"indent_date\": \"sto_date\", \"indent_qty\": \"sto_qty\"})\n", ")\n", "\n", "sto_items_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id, facility_id\n", "from retail.console_outlet\n", "where id in (100,314)\n", "\"\"\"\n", "\n", "facility = pd.read_sql_query(\n", "    query,\n", "    retail,\n", ")\n", "facility = facility.dropna()\n", "facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT a.item_id,\n", "b.cloud_store_id as receiving_outlet_id,\n", "                (CASE\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value ='Y' THEN 'X'\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value IS NULL THEN 'A'\n", "                     ELSE 'B'\n", "                 END) AS Final_bucket\n", "FROM rpc.item_outlet_tag_mapping a\n", "INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "AND a1.outlet_id=a.outlet_id\n", "AND a1.tag_type_id=6\n", "AND a1.tag_value='Y'\n", "WHERE b.cloud_store_id IN (100,314)\n", "  AND a.tag_type_id IN (1)\n", "  AND a.active=1\"\"\"\n", "\n", "buckets = pd.read_sql_query(query, retail)\n", "buckets.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_buckets = sto_items_grn.merge(\n", "    buckets, on=[\"receiving_outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "sto_buckets[\"Final_bucket\"] = sto_buckets[\"Final_bucket\"].fillna(\"B\")\n", "sto_buckets.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1IjkWEXox9wW8kbmiZ-BhLtpkAAay-im63lsqwBM_aPA\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"sto_details\"\n", "pb.to_sheets(sto_buckets, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_buckets = sto_buckets.dropna()\n", "sto_new = (\n", "    sto_buckets.groupby(\n", "        [\n", "            \"receiving_outlet_id\",\n", "            \"sto_date\",\n", "            \"grn_date\",\n", "            \"putaway_date\",\n", "            \"item_id\",\n", "            \"Final_bucket\",\n", "        ]\n", "    )\n", "    .agg({\"sto_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "sto_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT date(order_date) AS putaway_date,\n", "                facility_id,\n", "                item_id,\n", "                CASE\n", "                    WHEN actual_quantity-blocked_quantity<0 THEN 0\n", "                    ELSE actual_quantity-blocked_quantity\n", "                END AS available_qty\n", "FROM\n", "  (SELECT order_date,\n", "          item_id,\n", "          facility_id,\n", "          facility_name,\n", "          actual_quantity,\n", "          blocked_quantity,\n", "          COUNT(CASE\n", "                    WHEN (date_of_activation <= order_date\n", "                          AND availability ='live') THEN order_date\n", "                END) AS live_hours\n", "   FROM rpc_daily_availability\n", "   WHERE date(order_date) >='2020-10-27'\n", "     AND DATEPART(hour,order_date)<2\n", "     AND DATEPART(hour,order_date)>=1\n", "     AND facility_id in (12,26)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6) x1\n", "ORDER BY 4,\n", "         1\"\"\"\n", "\n", "inventory = pd.read_sql(query, redshift)\n", "inventory.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_outlet = inventory.merge(facility, on=[\"facility_id\"], how=\"left\").rename(\n", "    columns={\"outlet_id\": \"receiving_outlet_id\"}\n", ")\n", "inventory_outlet[\"putaway_date\"] = pd.to_datetime(inventory_outlet[\"putaway_date\"])\n", "inventory_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_buckets[\"putaway_date\"] = pd.to_datetime(sto_buckets[\"putaway_date\"])\n", "sto_inventory = sto_new.merge(\n", "    inventory_outlet, on=[\"receiving_outlet_id\", \"putaway_date\", \"item_id\"], how=\"left\"\n", ")\n", "sto_inventory.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS putaway_date,\n", "                o.outlet AS receiving_outlet_id,\n", "                i.item_id,\n", "                sum(i.quantity) AS order_qty\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "WHERE date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) >='2020-10-27'\n", "  AND status_id NOT IN (3,\n", "                        5,\n", "                        6,\n", "                        7,\n", "                        8)\n", "  AND o.outlet IN (100,314)\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "order = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order[\"putaway_date\"] = pd.to_datetime(order[\"putaway_date\"])\n", "sto_order = sto_inventory.merge(\n", "    order, on=[\"receiving_outlet_id\", \"putaway_date\", \"item_id\"], how=\"left\"\n", ")\n", "sto_order = sto_order.fillna(0)\n", "sto_order.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT \n", "        case when extract(hour from stock_in_time)>=18 then stock_in_date+1\n", "        else stock_in_date end as putaway_date,\n", "       facility_id,\n", "       item_id,\n", "       sum(grn_quantity) AS grn_quantity_other\n", "FROM\n", "  (SELECT stock_in_date,\n", "          stock_in_time,\n", "          facility_id,\n", "          facility_name,\n", "          outlet_id,\n", "          outlet_name,\n", "          city_id,\n", "          city_name,\n", "          merchant,\n", "          po_received,\n", "          stock_in_type,\n", "          item_id,\n", "          sum(grn_quantity) AS grn_quantity\n", "   FROM\n", "     (SELECT i.\"delta\"-i3.return_quantity AS Grn_quantity,\n", "             r.item_id AS item_id,\n", "             ro.id AS outlet_id,\n", "             ro.name AS outlet_name,\n", "             f.id AS facility_id,\n", "             f.name AS facility_name,\n", "             lo.id AS city_id,\n", "             lo.name AS city_name,\n", "             i3.purchase_order_id AS po_received,\n", "             CASE\n", "                 WHEN iii.source_type=2 THEN 'ESTO Stock-In'\n", "                 WHEN iii.source_type=1\n", "                      AND iii.valid_po=1 THEN 'Normal Stock-In'\n", "                 WHEN iii.source_type = 3 THEN 'Customer Returns Without Invoice'\n", "                 ELSE 'In-Valid Po'\n", "             END AS stock_in_type,\n", "             rcm.name AS merchant,\n", "             pi.invoice_type_id,\n", "             convert_timezone('Asia/Kolkata',i.pos_timestamp) as stock_in_time,\n", "             date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) AS stock_in_date\n", "      FROM lake_ims.ims_inventory_log i\n", "      INNER JOIN lake_rpc.product_product r ON r.variant_id= i.variant_id\n", "      LEFT JOIN lake_rpc.product_brand br ON br.id=r.brand_id\n", "      LEFT JOIN lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "      INNER JOIN consumer.retail_console_location lo ON ro.tax_location_id=lo.id\n", "      LEFT JOIN lake_crates.facility f ON ro.facility_id = f.id\n", "      INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "      INNER JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id::varchar = i.inventory_update_id::varchar\n", "      INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "      INNER JOIN lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "      LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "      LEFT JOIN lake_pos.view_pos_invoice pi ON pi.invoice_id=i.merchant_invoice_id\n", "      WHERE i.inventory_update_type_id IN (1,\n", "                                           28,\n", "                                           76,\n", "                                           90,\n", "                                           93)\n", "        AND date(convert_timezone('Asia/Kolkata',i.created_at)) >='2020-10-27'\n", "        AND i.\"delta\" > 0\n", "        AND rcm.name NOT LIKE '%%HOT%%'\n", "        AND f.id in (12,26)\n", "        AND rcm.id not IN (10543,5981)\n", "                 \n", "        AND rcm.name NOT IN ('FNV Dummy GRN Pan India',\n", "                             'Customer Returns Without Invoice',\n", "                             'Store Infra Merchant',\n", "                             'HOT Warehouse Bulk FNV',\n", "                             'F&V Dump stock - Jaipur',\n", "                             'Customer Return without Invoice',\n", "                             'SSC Warehouse Bulk FNV'))x1\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12)x2\n", "WHERE outlet_name NOT LIKE '%%Infra Outlet%%'\n", "  AND stock_in_type NOT LIKE '%%In-Valid Po%%'\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "grn_other = pd.read_sql(query, redshift)\n", "grn_other.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_other_outlet = grn_other.merge(facility, on=[\"facility_id\"], how=\"left\").rename(\n", "    columns={\"outlet_id\": \"receiving_outlet_id\"}\n", ")\n", "grn_other_outlet.drop([\"facility_id\"], axis=1, inplace=True)\n", "grn_other_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_other_outlet[\"putaway_date\"] = pd.to_datetime(grn_other_outlet[\"putaway_date\"])\n", "\n", "sto_other_grn = sto_order.merge(\n", "    grn_other_outlet, on=[\"putaway_date\", \"receiving_outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "sto_other_grn = sto_other_grn.fillna(0)\n", "sto_other_grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT order_date as putaway_date,\n", "       outlet AS receiving_outlet_id,\n", "       product_id,\n", "       avg(selling_price) AS selling_price\n", "FROM\n", "  (SELECT date(convert_timezone('Asia/Kolkata',od.created_at)) AS order_date,\n", "          oi.id,\n", "          od.outlet,\n", "          od.order_id AS sub_order_id,\n", "          oi.order_id AS ancestor_order_id,\n", "          product_id,\n", "          (product_name || ' ' || p.unit) AS product_name,\n", "          quantity,\n", "          oi.price AS selling_price,\n", "          landing_price,\n", "          city\n", "   FROM consumer.order_item_data oi\n", "   LEFT JOIN lake_cms.gr_product p ON oi.product_id = p.id\n", "   LEFT JOIN lake_ims.ims_order_details od ON od.ancestor=oi.order_id\n", "   WHERE date(convert_timezone('Asia/Kolkata',od.created_at))>='2020-10-27'\n", "     AND outlet IN (100,\n", "                    314) )x1\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "selling_price = pd.read_sql(query, redshift)\n", "selling_price.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "\n", "item_product_mapping = pd.read_sql(query, redshift)\n", "item_product_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["selling_price_item = selling_price.merge(\n", "    item_product_mapping[[\"item_id\", \"product_id\"]], how=\"left\"\n", ")\n", "selling_price_item = selling_price_item.dropna()\n", "selling_price_item = selling_price_item[\n", "    [\n", "        \"putaway_date\",\n", "        \"receiving_outlet_id\",\n", "        \"item_id\",\n", "        \"selling_price\",\n", "    ]\n", "].drop_duplicates()\n", "selling_price_item.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["selling_price_item[\"putaway_date\"] = pd.to_datetime(selling_price_item[\"putaway_date\"])\n", "\n", "sto_gmv = sto_other_grn.merge(\n", "    selling_price_item,\n", "    on=[\"putaway_date\", \"receiving_outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "sto_gmv[\"selling_price\"] = sto_gmv[\"selling_price\"].fillna(80)\n", "sto_gmv = sto_gmv.fillna(0)\n", "sto_gmv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def gmv(x):\n", "    if x[\"grn_quantity_other\"] + x[\"available_qty\"] >= x[\"order_qty\"]:\n", "        return 0\n", "    else:\n", "        return round(\n", "            min(\n", "                x[\"order_qty\"] - (x[\"grn_quantity_other\"] + x[\"available_qty\"]),\n", "                x[\"grn_qty\"],\n", "            )\n", "            * x[\"selling_price\"]\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_gmv[\"gmv\"] = sto_gmv.apply(gmv, axis=1)\n", "sto_gmv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" SELECT distinct date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) as putaway_date,\n", "                   o.outlet as receiving_outlet_id,\n", "                   o.ancestor as order_id,\n", "                   i.item_id\n", "            FROM ims.ims_order_details o\n", "            INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "            WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\"))  >='2020-10-27'\n", "              AND status_id NOT IN (3,\n", "                                    5,\n", "                                    6,\n", "                                    7,\n", "                                    8)\n", "                                    and o.outlet in (314,100)                 \n", "     \"\"\"\n", "\n", "orders = pd.read_sql_query(query, retail)\n", "orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders[\"putaway_date\"] = pd.to_datetime(orders[\"putaway_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders = (\n", "    orders.groupby([\"receiving_outlet_id\", \"putaway_date\"])[[\"order_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"total_checkout_orders\"})\n", ")\n", "total_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_total_orders = sto_gmv.merge(\n", "    total_orders, on=[\"putaway_date\", \"receiving_outlet_id\"], how=\"left\"\n", ")\n", "sto_total_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"raw\"\n", "pb.to_sheets(sto_total_orders, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv = (\n", "    sto_total_orders.groupby(\n", "        [\"sto_date\", \"receiving_outlet_id\", \"total_checkout_orders\"]\n", "    )[[\"gmv\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "gmv[\"AOV\"] = gmv[\"gmv\"] / gmv[\"total_checkout_orders\"]\n", "aov = gmv.groupby([\"sto_date\", \"receiving_outlet_id\"])[[\"AOV\"]].max().reset_index()\n", "aov"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"aov\"\n", "pb.to_sheets(aov, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_net = gmv.groupby([\"sto_date\", \"receiving_outlet_id\"])[[\"gmv\"]].sum().reset_index()\n", "gmv_net.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"gmv\"\n", "pb.to_sheets(gmv_net, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "select a.*,date(order_date) as putaway_date\n", "\n", "from rpc_daily_availability as a\n", "\n", "inner join\n", "\n", "(SELECT date(order_date) as date,\n", "       max(order_date) AS maxi\n", "FROM rpc_daily_availability\n", "WHERE date(order_date) >= '2020-10-27'\n", "GROUP BY 1 \n", ") as b\n", "\n", "on date(a.order_date)=b.date and a.order_date = b.maxi\n", "\n", "where a.facility_id in (12,26)\n", "\"\"\"\n", "availability_value = pd.read_sql(query, redshift)\n", "availability_value.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unorderable_unreliable_items = availability_value[\n", "    (availability_value.new_substate == 1)\n", "    & (\n", "        ((availability_value.unreliable == \"1\") & (availability_value.inv_flag <= 0))\n", "        | ((availability_value.unorderable == \"1\") & (availability_value.inv_flag <= 0))\n", "    )\n", "    & (availability_value.date_of_activation <= availability_value.order_date)\n", "]\n", "\n", "unorderable_unreliable_items = unorderable_unreliable_items[\n", "    [\"putaway_date\", \"facility_id\", \"item_id\"]\n", "].drop_duplicates()\n", "unorderable_unreliable_items = unorderable_unreliable_items.merge(\n", "    facility, on=[\"facility_id\"], how=\"left\"\n", ")\n", "unorderable_unreliable_items[\"un_flag\"] = 1\n", "unorderable_unreliable_items.drop([\"facility_id\"], axis=1, inplace=True)\n", "unorderable_unreliable_items[\"putaway_date\"] = pd.to_datetime(\n", "    unorderable_unreliable_items[\"putaway_date\"]\n", ")\n", "unorderable_unreliable_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_item = (\n", "    availability_value[\n", "        (availability_value.new_substate == 1)\n", "        & (\n", "            (\n", "                (availability_value.unreliable != \"1\")\n", "                & (availability_value.unorderable != \"1\")\n", "            )\n", "            | (\n", "                (availability_value.unreliable == \"1\")\n", "                & (availability_value.unorderable == \"1\")\n", "                & (availability_value.inv_flag > 0)\n", "            )\n", "            | (\n", "                (availability_value.unreliable == \"1\")\n", "                & (availability_value.inv_flag > 0)\n", "            )\n", "            | (\n", "                (availability_value.unorderable == \"1\")\n", "                & (availability_value.inv_flag > 0)\n", "            )\n", "        )\n", "        & (availability_value.date_of_activation <= availability_value.order_date)\n", "    ]\n", "    .groupby([\"putaway_date\", \"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "\n", "active_item = active_item.rename(columns={\"item_id\": \"total_active_item\"})\n", "active_item = active_item.merge(facility, on=[\"facility_id\"], how=\"left\")\n", "active_item.drop([\"facility_id\"], axis=1, inplace=True)\n", "active_item[\"putaway_date\"] = pd.to_datetime(active_item[\"putaway_date\"])\n", "active_item.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_gmv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_availability = sto_gmv[\n", "    (sto_gmv[\"gmv\"] > 0)\n", "    | ((sto_gmv[\"available_qty\"] + sto_gmv[\"grn_quantity_other\"]) == 0)\n", "]\n", "item_availability.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_availability2 = (\n", "    item_availability[[\"receiving_outlet_id\", \"sto_date\", \"putaway_date\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"receiving_outlet_id\": \"outlet_id\"})\n", ")\n", "indent_items = (\n", "    item_availability2.groupby([\"outlet_id\", \"sto_date\", \"putaway_date\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "indent_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unorderable_transfer = item_availability2.merge(\n", "    unorderable_unreliable_items,\n", "    on=[\"outlet_id\", \"putaway_date\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "unorderable_transfer = unorderable_transfer.fillna(0)\n", "unorderable_transfer = unorderable_transfer[unorderable_transfer[\"un_flag\"] == 1]\n", "unorderable_transfer = (\n", "    unorderable_transfer.groupby([\"putaway_date\", \"outlet_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "unorderable_transfer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_active_items = active_item.merge(\n", "    unorderable_transfer, on=[\"putaway_date\", \"outlet_id\"], how=\"left\"\n", ")\n", "total_active_items = total_active_items.fillna(0)\n", "total_active_items[\"active_items\"] = (\n", "    total_active_items[\"total_active_item\"] + total_active_items[\"item_id\"]\n", ")\n", "total_active_items = total_active_items[\n", "    [\"outlet_id\", \"putaway_date\", \"active_items\"]\n", "].drop_duplicates()\n", "total_active_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability = indent_items.merge(\n", "    total_active_items, on=[\"outlet_id\", \"putaway_date\"], how=\"left\"\n", ")\n", "availability[\"availability\"] = (\n", "    availability[\"item_id\"] / availability[\"active_items\"] * 100\n", ")\n", "availability = (\n", "    availability.groupby([\"outlet_id\", \"sto_date\"])\n", "    .agg({\"availability\": \"max\"})\n", "    .reset_index()\n", ")\n", "availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"availability\"\n", "pb.to_sheets(availability, sheet_id, sheet_name, clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}