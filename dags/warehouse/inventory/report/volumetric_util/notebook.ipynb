{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime as datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import warnings\n", "\n", "if not sys.warnoptions:\n", "    warnings.simplefilter(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletList = [\n", "    337,\n", "    116,\n", "    363,\n", "    475,\n", "    714,\n", "    520,\n", "    603,\n", "    481,\n", "    334,\n", "    369,\n", "    287,\n", "    100,\n", "    314,\n", "    340,\n", "    451,\n", "    343,\n", "    512,\n", "    292,\n", "]\n", "# OutletList = [343]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# # validating shelf volume\n", "\n", "# # 116 -> using from script\n", "# # 603 -> using from script\n", "# # vol_603 = 0\n", "# vol_116 = float(\n", "#     Item_Locations_Final6.loc[Item_Locations_Final6[\"outlet_id\"] == 116].iloc[:, 3]\n", "# ) + float(\n", "#     Item_Locations_Final6.loc[Item_Locations_Final6[\"outlet_id\"] == 116].iloc[:, 4]\n", "# )\n", "# vol_603 = float(\n", "#     Item_Locations_Final6.loc[Item_Locations_Final6[\"outlet_id\"] == 603].iloc[:, 3]\n", "# ) + float(\n", "#     Item_Locations_Final6.loc[Item_Locations_Final6[\"outlet_id\"] == 603].iloc[:, 4]\n", "# )\n", "# vol_314 = (\n", "#     2410 * 1200 * 2438.4 * 188\n", "#     + 1301 * 600 * 2438.48 * 346\n", "#     + 914.4 * 457.2 * 1981.2 * 1542\n", "# )\n", "# vol_337 = (\n", "#     2410 * 1200 * 2438.4 * 57\n", "#     + 1301 * 600 * 2438.4 * 112\n", "#     + 914.4 * 457.2 * 1981.2 * 1648\n", "# )\n", "# vol_369 = (\n", "#     2410 * 1200 * 2438.4 * 84\n", "#     + 1301 * 600 * 2438.4 * 358\n", "#     + 914.4 * 457.2 * 1981.2 * 1369\n", "# )\n", "# vol_287 = (\n", "#     2410 * 1200 * 2438.4 * 96\n", "#     + 1301 * 600 * 2438.4 * 456\n", "#     + 914.4 * 457.2 * 1981.2 * 1722\n", "# )\n", "# vol_512 = (\n", "#     2410 * 1200 * 2438.4 * 203 + 1301 * 600 * 2438.4 * 1430 + 914.4 * 457.2 * 1981.2 * 0\n", "# )\n", "# vol_363 = (\n", "#     2410 * 1200 * 2438.4 * 86 + 1301 * 600 * 2438.4 * 788 + 914.4 * 457.2 * 1981.2 * 469\n", "# )\n", "# vol_334 = (\n", "#     2410 * 1200 * 2438.4 * 71\n", "#     + 1301 * 600 * 2438.4 * 408\n", "#     + 914.4 * 457.2 * 1981.2 * 1052\n", "# )\n", "# vol_292 = (\n", "#     2410 * 1200 * 2438.4 * 79\n", "#     + 1301 * 600 * 2438.4 * 254\n", "#     + 914.4 * 457.2 * 1981.2 * 1720\n", "# )\n", "# vol_343 = (\n", "#     2410 * 1200 * 2438.4 * 94 + 1301 * 600 * 2438.4 * 54 + 914.4 * 457.2 * 1981.2 * 674\n", "# )\n", "# vol_340 = (\n", "#     2410 * 1200 * 2438.4 * 58\n", "#     + 1301 * 600 * 2438.4 * 109\n", "#     + 914.4 * 457.2 * 1981.2 * 1470\n", "# )\n", "# vol_520 = (\n", "#     2410 * 1200 * 2438.4 * 108 + 1301 * 600 * 2438.4 * 539 + 914.4 * 457.2 * 1981.2 * 0\n", "# )\n", "# vol_451 = (\n", "#     2410 * 1200 * 2500 * 228\n", "#     + 1410 * 1200 * 2500 * 4\n", "#     + 1301 * 600 * 2500 * 1784\n", "#     + 1301 * 600 * 2500 * 168\n", "# )\n", "# vol_481 = (\n", "#     2410 * 1200 * 2500 * 294\n", "#     + 2715 * 1200 * 2500 * 24\n", "#     + 3223 * 1200 * 2500 * 45\n", "#     + 1410 * 1200 * 2500 * 3\n", "#     + 1301 * 600 * 2500 * 2145\n", "# )\n", "# vol_475 = 2410 * 1200 * 2500 * 162 + 1301 * 600 * 2500 * 762\n", "# vol_100 = 1422 * 609 * 2116 * 1452 + 2590 * 1193 * 2116 * 144\n", "# vol_714 = (\n", "#     2420 * 1200 * 2499 * (57 + 57)\n", "#     + 2420 * 1200 * 2194 * 57\n", "#     + 2220 * 1200 * 2499 * (17 + 17)\n", "#     + 2220 * 1200 * 2194 * 17\n", "#     + 1300 * 600 * 2499 * (624 + 624)\n", "#     + 1300 * 600 * 2194 * 624\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cal_volume = pb.from_sheets(\"1M6Y_umc5_MvaW-C3fmodCOQGjPEwLHxUoaMwsdQR53w\", \"Sheet1\")\n", "cal_volume[\"cal_vol\"] = pd.to_numeric(cal_volume[\"cal_vol\"])\n", "cal_volume[\"cal_vol\"] = cal_volume[\"cal_vol\"] / 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["Query = \"\"\" WITH raw_data AS \n", "  (SELECT co.id AS outlet_id, \n", "          co.name, \n", "          ws.name AS rack_type, \n", "          rt.id, \n", "          wsl.id AS location_id, \n", "          wsl.location_name, \n", "          wsl.bin_id, \n", "          wsl.shelf_id, \n", "          s.rack_id, \n", "          s.shelf_id AS shelf_level, \n", "          r.rack_id AS rack_no, \n", "          CASE \n", "              WHEN wsl.location_type=1 THEN 'Primary' \n", "              ELSE 'Secondary' \n", "          END AS location_type, \n", "          il.item_id, \n", "          product.name AS item_name, \n", "          rt.size AS rack_size_ft, \n", "          100*rt.length rack_length_cm, \n", "          100*rt.width rack_width_cm, \n", "          250 rack_height_cm, \n", "          100*rt.length shelf_length_cm, \n", "          100*rt.width shelf_width_cm, \n", "          s.height AS shelf_height_cm, \n", "          rt.length, \n", "          rt.width, \n", "          left(wsl.location_name,2) as Floor,\n", " --       s.height, \n", " -- 100*rt.length*100*rt.width*250 as rack_volume,\n", " -- 100*rt.length*100*rt.width * (case when s.height<= .71 then  s.height*100 \n", " --                                  when s.height<= 4.30 then s.height*30.48\n", " --                                  when s.height> 4.30 then s.height*1\n", " --                                  end)\n", " --                                  as shelf_volume,\n", " product.length_in_cm,\n", " product.breadth_in_cm,\n", " product.height_in_cm,\n", " product.weight_in_gm, \n", " sum(il.quantity) AS item_quantity, \n", " sum(il.quantity)*product.weight_in_gm item_weight, \n", " sum(il.quantity)*(product.length_in_cm*product.breadth_in_cm*product.height_in_cm) item_volume, \n", " (product.weight_in_gm)/(product.length_in_cm*product.breadth_in_cm*product.height_in_cm) density\n", "   FROM lake_warehouse_location.warehouse_storage_location wsl\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id=wsl.warehouse_id\n", "   LEFT JOIN lake_warehouse_location.warehouse_item_location il ON il.location_id=wsl.id\n", "   INNER JOIN lake_warehouse_location.warehouse_shelf s ON wsl.shelf_id=s.id\n", "   INNER JOIN lake_warehouse_location.warehouse_rack r ON r.id=s.rack_id\n", "   INNER JOIN lake_warehouse_location.warehouse_rack_type rt ON rt.id=r.rack_type_id\n", "   INNER JOIN lake_warehouse_location.warehouse_storage_type ws ON ws.id=wsl.storage_type_id\n", "   INNER JOIN lake_retail.console_outlet co ON co.id=wom.outlet_id\n", "   LEFT JOIN  metrics.outlet_zone_mapping ozm ON ozm.outlet_id=co.id\n", "   INNER JOIN \n", "     (SELECT DISTINCT r.item_id, \n", "                      r.name, \n", "                      r.breadth_in_cm, \n", "                      r.length_in_cm, \n", "                      r.height_in_cm, \n", "                      weight_in_gm \n", "      FROM lake_rpc.product_product r \n", "      INNER JOIN \n", "        (SELECT item_id,\n", "                max(created_at) AS dated\n", "         FROM consumer.rpc_product_product \n", "         GROUP BY 1) a ON a.item_id=r.item_id \n", "      WHERE r.created_at=a.dated) product ON product.item_id=il.item_id \n", "   WHERE wsl.is_active=1 \n", "     AND ws.name IN ('Rack', \n", "                     '<PERSON>_Rack')\n", "     AND ozm.business_type_id=1\n", "     AND co.id = %(outlet)s\n", "   GROUP BY 1, \n", "            2,\n", "            3, \n", "            4, \n", "            5, \n", "            6, \n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12,\n", "            13,\n", "            14,\n", "            15,\n", "            16,\n", "            17,\n", "            18,\n", "            19,\n", "            20,\n", "            21,\n", "            22,\n", "            23,\n", "            24,\n", "            25,\n", "            26,\n", "            27,\n", "            28), \n", "               PRODUCT_Category AS \n", "  (SELECT P.ID AS PID, \n", "          P.NAME AS PRODUCT, \n", "          P.UNIT AS Unit, \n", "          C2.NAME AS L2, \n", "          (CASE \n", "               WHEN C1.NAME = C.name THEN C2.name \n", "               ELSE C1.name \n", "           END) AS L1, \n", "          C.NAME AS L0, --   P.BRAND AS brand,  P.MANUFACTURER AS manf,\n", " pt.name AS product_type \n", "   FROM lake_cms.gr_product P \n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID \n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE \n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID \n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID \n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id ), \n", "               category_data AS \n", "  ( SELECT rpc.item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "      -- cat.brand,\n", "      -- cat.manf,\n", "       cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   INNER JOIN \n", "     (SELECT item_id,\n", "             max(name),\n", "             max(inner_case_size) inner_case_size,\n", "             max(outer_case_size) outer_case_size, \n", "             max(length_in_cm) length_in_cm,\n", "             max(breadth_in_cm) breadth_in_cm,\n", "             max(height_in_cm) height_in_cm, \n", "             max(storage_type) storage_type, \n", "             max(weight_in_gm) AS weight \n", "      FROM lake_rpc.product_product \n", "      WHERE active = 1 \n", "      GROUP BY 1)pp ON pp.item_id=rpc.item_id\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL), \n", "               summary_data AS \n", "  (SELECT rd.outlet_id, \n", "          location_id,\n", "          location_name,\n", "          rd.name,\n", "          shelf_id,\n", "          rack_id,\n", "          shelf_level,\n", "          rack_no,\n", "          rack_type,\n", "          rd.item_id,\n", "          rd.length, \n", "          rd.width, \n", "          case \n", "               when rack_type='HD_Rack' then 2.49/3\n", "               when rack_type='Rack' then 2.49/5 end as height,\n", "--          rd.height, \n", "          rd.item_name, \n", "--          cd.category,\n", "          cd.l0,\n", "          cd.l1,\n", "          cd.l2, \n", "          rd.breadth_in_cm, \n", "          rd.length_in_cm, \n", "          rd.height_in_cm, \n", "          rd.weight_in_gm, \n", "          item_quantity,\n", "          item_weight,\n", "          item_volume,\n", "          location_type\n", "   FROM raw_data rd\n", "   LEFT JOIN category_data cd ON rd.item_id=cd.item_id \n", "--   WHERE rd.item_volume IS NOT NULL\n", "--     AND rd.weight_in_gm IS NOT NULL\n", "--     AND cd.product_type IS NOT NULL\n", ")\n", "SELECT *\n", "FROM summary_data\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Vol_util = pd.DataFrame()\n", "for outlet in OutletList:\n", "    Item_Locations = pd.DataFrame()\n", "    Item_Locations = pd.read_sql_query(\n", "        sql=Query, con=redshift, params={\"outlet\": outlet}\n", "    )\n", "    print(outlet, Item_Locations.shape)\n", "    A = Item_Locations.groupby(\n", "        [\"outlet_id\", \"shelf_id\", \"rack_type\", \"length\", \"width\", \"height\"],\n", "        as_index=False,\n", "    ).agg({\"item_quantity\": sum})\n", "    Zero_Volume_Shelves = A[A[\"item_quantity\"] == 0]\n", "    Item_Locations[[\"rack_type\", \"length\", \"width\", \"height\"]].drop_duplicates()\n", "    Zero_Volume_Shelves[\"Volume\"] = (\n", "        Zero_Volume_Shelves[\"length\"]\n", "        * Zero_Volume_Shelves[\"width\"]\n", "        * Zero_Volume_Shelves[\"height\"]\n", "        * 1000000\n", "    )\n", "    EmptyShelvesOutletLevel = Zero_Volume_Shelves.groupby(\n", "        [\"outlet_id\"], as_index=False\n", "    ).agg({\"shelf_id\": \"nunique\", \"Volume\": sum})\n", "    Item_Locations[\"shelf_volume\"] = (\n", "        Item_Locations[\"length\"]\n", "        * Item_Locations[\"width\"]\n", "        * Item_Locations[\"height\"]\n", "        * 1000000\n", "    )\n", "    Item_Locations_Final = Item_Locations.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"name\",\n", "            \"shelf_id\",\n", "            \"item_id\",\n", "            \"rack_type\",\n", "            \"shelf_level\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"item_weight\",\n", "        ],\n", "        as_index=False,\n", "    ).agg(\n", "        {\n", "            \"shelf_volume\": max,\n", "            \"item_volume\": sum,\n", "            \"item_quantity\": sum,\n", "            \"breadth_in_cm\": max,\n", "            \"length_in_cm\": max,\n", "            \"height_in_cm\": max,\n", "        }\n", "    )\n", "    Filter = ~Item_Locations_Final[\"shelf_id\"].isin(Zero_Volume_Shelves[\"shelf_id\"])\n", "    Item_Locations_Final1 = Item_Locations_Final[Filter]\n", "    Item_Locations_Final2 = Item_Locations_Final1[\n", "        Item_Locations_Final1[\"item_quantity\"] > 0\n", "    ]\n", "    Item_Locations_Final2[\"perunitvolume\"] = (\n", "        Item_Locations_Final2[\"item_volume\"] / Item_Locations_Final2[\"item_quantity\"]\n", "    )\n", "    Item_Locations_Final2[\"density\"] = (\n", "        Item_Locations_Final2[\"item_weight\"] / Item_Locations_Final2[\"item_volume\"]\n", "    )\n", "    Item_Locations_Final3 = Item_Locations_Final2[\n", "        (Item_Locations_Final2[\"density\"] >= 0.005)\n", "        & (Item_Locations_Final2[\"density\"] <= 10)\n", "        & (Item_Locations_Final2[\"perunitvolume\"] >= 0)\n", "        & (Item_Locations_Final2[\"perunitvolume\"] <= 20000)\n", "    ]\n", "    Item_Locations_Final4 = Item_Locations_Final3.groupby(\n", "        [\"outlet_id\", \"name\", \"shelf_id\", \"shelf_level\", \"rack_type\"], as_index=False\n", "    ).agg(\n", "        {\n", "            \"item_volume\": sum,\n", "            \"shelf_volume\": max,\n", "            \"item_quantity\": sum,\n", "            \"item_weight\": sum,\n", "        }\n", "    )\n", "    Item_Locations_Final5 = Item_Locations_Final4.groupby(\n", "        [\"outlet_id\", \"name\"], as_index=False\n", "    ).agg(\n", "        {\n", "            \"item_volume\": sum,\n", "            \"shelf_volume\": sum,\n", "            \"item_quantity\": sum,\n", "            \"shelf_id\": \"nunique\",\n", "        }\n", "    )\n", "    Item_Locations_Final6 = Item_Locations_Final5.merge(\n", "        EmptyShelvesOutletLevel, on=\"outlet_id\", how=\"left\"\n", "    )\n", "    Item_Locations_Final6[\"cal_volume\"] = 0\n", "    Item_Locations_Final6 = Item_Locations_Final6.drop(\n", "        columns={\"item_quantity\", \"shelf_id_x\", \"shelf_id_y\"}\n", "    )\n", "    if outlet == 116 or outlet == 603:\n", "        Item_Locations_Final6[\"cal_volume\"] = float(\n", "            Item_Locations_Final6.iloc[:, 3] + Item_Locations_Final6.iloc[:, 4]\n", "        )\n", "    else:\n", "        Item_Locations_Final6[\"cal_volume\"] = float(\n", "            cal_volume.loc[cal_volume[\"outlet\"] == str(outlet)].iloc[:, 1]\n", "        )\n", "    Item_Locations_Final6[\"VolumeUtilization\"] = round(\n", "        (Item_Locations_Final6[\"item_volume\"] / Item_Locations_Final6[\"cal_volume\"])\n", "        * 100,\n", "        0,\n", "    )\n", "    Item_Locations_Final7 = Item_Locations_Final6.rename(\n", "        columns={\n", "            \"item_volume\": \"TotalItemVolume\",\n", "            \"shelf_volume\": \"ShelfVolume\",\n", "            \"Volume\": \"EmptyShelvesVolume\",\n", "            \"cal_volume\": \"CalculatedVolume\",\n", "        }\n", "    )\n", "    Item_Locations_Final7[\"TotalItemVolume\"] = np.round(\n", "        Item_Locations_Final7[\"TotalItemVolume\"] / 1000000, 0\n", "    )\n", "    Item_Locations_Final7[\"ShelfVolume\"] = np.round(\n", "        Item_Locations_Final7[\"ShelfVolume\"] / 1000000, 0\n", "    )\n", "    Item_Locations_Final7[\"EmptyShelvesVolume\"] = np.round(\n", "        Item_Locations_Final7[\"EmptyShelvesVolume\"] / 1000000, 0\n", "    )\n", "    Item_Locations_Final7[\"CalculatedVolume\"] = np.round(\n", "        Item_Locations_Final7[\"CalculatedVolume\"] / 1000000, 0\n", "    )\n", "    Item_Locations_Final7[\"VolumeUtilization\"] = np.round(\n", "        Item_Locations_Final7[\"VolumeUtilization\"], 0\n", "    )\n", "    Vol_util = pd.concat([Vol_util, Item_Locations_Final7], axis=0)\n", "    print(Item_Locations_Final7[\"VolumeUtilization\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Vol_util"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Vol_util = Vol_util.sort_values(by=[\"VolumeUtilization\"], ascending=False)\n", "Vol_util[\"TotalItemVolume\"] = Vol_util.apply(\n", "    lambda x: \"{:,}\".format(x[\"TotalItemVolume\"]), axis=1\n", ")\n", "Vol_util[\"ShelfVolume\"] = Vol_util.apply(\n", "    lambda x: \"{:,}\".format(x[\"ShelfVolume\"]), axis=1\n", ")\n", "Vol_util[\"EmptyShelvesVolume\"] = Vol_util.apply(\n", "    lambda x: \"{:,}\".format(x[\"EmptyShelvesVolume\"]), axis=1\n", ")\n", "Vol_util[\"CalculatedVolume\"] = Vol_util.apply(\n", "    lambda x: \"{:,}\".format(x[\"CalculatedVolume\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = []\n", "for i in range(1, len(Vol_util) + 1):\n", "    index.append(i)\n", "Vol_util[\"index\"] = index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Vol_util = Vol_util[\n", "    [\n", "        \"index\",\n", "        \"outlet_id\",\n", "        \"name\",\n", "        \"TotalItemVolume\",\n", "        \"ShelfVolume\",\n", "        \"EmptyShelvesVolume\",\n", "        \"CalculatedVolume\",\n", "        \"VolumeUtilization\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = Vol_util.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date\n", "\n", "end = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = \"/usr/local/airflow/dags/repo/dags/warehouse/inventory/report/volumetric_util/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"Volume Utilization Report \" + str(end)\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "email_template = \"email_template.html\"\n", "loader = jinja2.FileSystemLoader(searchpath=path)\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "rendered = template.render(products1=items1)\n", "\n", "pb.send_email(from_email, to_email, subject, html_content=rendered)\n", "print(\"mail sent\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}