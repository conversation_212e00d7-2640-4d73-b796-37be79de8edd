{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "\n", "#  sheet variable\n", "\n", "sheet_id = \"1PopqtEVlJP5OsLBEvV-bnAvs9SpwcuAIlZeqQ1lzcdM\"\n", "\n", "# Date Time Variable\n", "\n", "NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "RUN_TIMESTAMP = str(\n", "    NOW_IST.replace(minute=0, second=0, microsecond=0) + <PERSON><PERSON><PERSON>(hours=1)\n", ")\n", "RUN_HOUR = str((NOW_IST + timedelta(hours=1)).hour)\n", "pd.options.mode.chained_assignment = None\n", "NOW_IST\n", "int(RUN_HOUR)\n", "RUN_DATE\n", "\n", "# RUN_DATE=RUN_DATE + pd.DateOffset(-1)\n", "# type(RUN_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "select  \n", "outlet_id, outlet,created_at checkout_date,\n", "count(distinct order_id) subord_cnt,count(distinct customer_id) customer_ids,\n", "sum(skus) skus,sum(quantity) quantity,(sum(skus)/count(distinct order_id) ) spo, (sum(quantity)/count(distinct order_id) ) ipo,\n", "(sum(quantity)/count(distinct order_id) )/(sum(skus)/count(distinct order_id) ) sku_Density,\n", "time(convert_tz(now(),\"+00:00\",\"+05:30\")) as last_updated\n", "from(\n", "select \n", "outlet_id, outlet,fac_id,facility,created_at,order_id,ancestor as ancestor_id,customer_id,\n", "count(distinct order_id ) ord_cnt , count(distinct ancestor ) ancestor_cnt,\n", "count(distinct order_id )- count(distinct ancestor ) as diff,\n", "count(distinct item_id) as skus,sum(quantity)  quantity\n", "from(\n", "select  o.order_id,o.outlet outlet_id,\n", "date(convert_tz(o.scheduled_at,\"+00:00\",\"+05:30\")) as delivery_date,\n", "date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) as created_at,\n", "date(convert_tz(o.updated_at,\"+00:00\",\"+05:30\")) as updated_at,\n", "o.status_id,\n", "case when o.status_id=1 then\t'Created'\n", "when o.status_id=2 then\t'Billed'\n", "when o.status_id=3\tthen 'Cancelled'\n", "when o.status_id=4\tthen 'Discarded'\n", "when o.status_id=5\t then 'Cancelled Nonbillable'\n", "when o.status_id=6\tthen 'Cancelled Billed'\n", "when o.status_id=7\tthen 'Cancelled Discarded' end as order_status_ims,\n", "date(convert_tz(o.slot_handover_time,\"+00:00\",\"+05:30\")) as slot_handover_time,\n", "o.ancestor,o.customer_id,\n", "i.item_id,i.quantity,\n", "rco.name outlet,\n", "fac.id fac_id,fac.name facility\n", " \n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id \n", "INNER JOIN retail.console_outlet rco ON o.outlet = rco.id\n", "left join  retail.console_location  rcl ON rco.tax_location_id=rcl.id\n", "left join    crates.facility fac on fac.id=rco.facility_id\n", "where o.id>'53716760'  and o.status_id not in (3,5) and   i.id>'*********' \n", "and fac.name not like \"%%DS%%\" and rco.name not like ('%%cold%%')  and rco.name not like  ('%%Dark%%') and  rco.name not like  ('%%large%%')\n", "-- and rco.id=238\n", "-- and fac.id=12\n", "-- and date(convert_tz(o.scheduled_at,\"+00:00\",\"+05:30\"))=current_date+1\n", "and date(convert_tz(o.created_at,\"+00:00\",\"+05:30\"))={cond}\n", "and\n", " o.ancestor in\n", "(select ancestor from\n", "(\n", "select  outlet,ancestor,count(id) cnt,min(date(convert_tz(created_at,\"+00:00\",\"+05:30\"))) frst_created\n", "from ims.ims_order_details where  ancestor in\n", "(\n", "select distinct ancestor\n", "from ims.ims_order_details o\n", "where date(convert_tz(o.created_at,\"+00:00\",\"+05:30\"))={cond}\n", "and o.id>53716760  \n", "-- and o.id=58634320\n", "-- and outlet=100\n", ") \n", "-- and outlet=100\n", "-- and ancestor=58634320\n", "group by 1,2 having frst_created={cond}\n", ")\n", "temp1)\n", "\n", "-- and o.order_id=89227587\n", "-- and o.ancestor=58501412\n", "-- 88173011 \n", ")order_details\n", "group by 1,2,3,4,5,6,7,8\n", ")summary\n", "group by 1,2,3,11\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if int(RUN_HOUR)<=2:\n", "#     dat_cond='current_date-2'\n", "# else:\n", "#     dat_cond='current_date'\n", "\n", "\n", "# orders=pd.read_sql_query(query,retail,params={\"cond\":dat_cond})\n", "\n", "# orders=pd.read_sql_query(query.format(cond=dat_cond),retail)\n", "\n", "# orders=pd.read_sql_query(query ,retail)\n", "\n", "# d1=orders[orders['checkout_date']==RUN_DATE]\n", "# d1.head()\n", "\n", "\n", "# orders.head()\n", "# dat_cond\n", "\n", "#"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#   T sheet\n", "\n", "sheet_name = \"T\"\n", "dat_cond = \"current_date\"\n", "\n", "d1 = pd.read_sql_query(query.format(cond=dat_cond), retail)\n", "\n", "pb.to_sheets(d1, sheet_id, sheet_name, service_account=\"service_account\")\n", "print(\"updated sheet for T\")\n", "\n", "#  T-1 sheet\n", "\n", "if int(RUN_HOUR) <= 12:\n", "    sheet_name = \"T-1\"\n", "    dat_cond = \"current_date-1\"\n", "    d2 = pd.read_sql_query(query.format(cond=dat_cond), retail)\n", "\n", "    #     d2=orders[orders['checkout_date']==(RUN_DATE - pd.DateOffset(1)).date()]\n", "    d2.head()\n", "    pb.to_sheets(d2, sheet_id, sheet_name, service_account=\"service_account\")\n", "    print(\"updated sheet for T-1\")\n", "\n", "    #  T-2 sheet\n", "\n", "    sheet_name = \"T-2\"\n", "    dat_cond = \"current_date-2\"\n", "    d3 = pd.read_sql_query(query.format(cond=dat_cond), retail)\n", "\n", "    #     d3=orders[orders['checkout_date']==(RUN_DATE - pd.DateOffset(2)).date()]\n", "    d3.head()\n", "    pb.to_sheets(d3, sheet_id, sheet_name, service_account=\"service_account\")\n", "    print(\"updated sheet for T-2\")\n", "\n", "\n", "else:\n", "    print(\"updated not required for back date\")\n", "    pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}