{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "# outlet_id=116\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz)).strftime(\"%Y-%m-%d\")\n", "# today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"10QlvL_GeRuU7PCysssGRY1jHxhUY6TiLSCoCBC_4nDo\"\n", "outlets = pb.from_sheets(sheet_id, \"Stock Take\")\n", "outlets = outlets[[\"outlet_id\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"outlet_id\"], inplace=True)\n", "\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "email_list = pb.from_sheets(sheet_id, \"Stock Take\")\n", "email_list = email_list[[\"Group Mail Id\", \"outlet_id\"]]\n", "email_list[\"Group Mail Id\"] = email_list[\"Group Mail Id\"].astype(str)\n", "# email_list[\"outlet_id\"]=email_list[\"outlet_id\"].astype(int)\n", "outlets = list(outlets.outlet_id.unique())\n", "# outlets\n", "# email_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in outlets:\n", "    try:\n", "        outlet = int(outlet)\n", "        x.append(outlet)\n", "\n", "    except:\n", "        pass\n", "print(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_query = \"\"\"\n", "WITH racks AS\n", "  (SELECT wl.location_type,\n", "          o.outlet_id,\n", "          r.name AS outlet_name,\n", "          left(wl.location_name,10) AS LOCATION\n", "   FROM consumer.warehouse_location_warehouse_storage_location wl\n", "   INNER JOIN consumer.warehouse w ON w.id=wl.warehouse_id\n", "   INNER JOIN consumer.warehouse_location_warehouse_outlet_mapping o ON o.warehouse_id=w.id\n", "   INNER JOIN consumer.retail_console_outlet r ON r.id=o.outlet_id\n", "   WHERE wl.is_active=1\n", "     AND wl.location_type IN (1)\n", "     AND wl.storage_type_id IN (1,\n", "                                2,\n", "                                6)\n", "                                and o.outlet_id=%(outlet_id)s\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4),\n", "     row_number AS\n", "  (SELECT outlet_id,\n", "          outlet_name,\n", "          LOCATION,\n", "          row_number() over(PARTITION BY outlet_id\n", "                            ORDER BY LOCATION) AS ROW\n", "   FROM racks),\n", "     racks_count AS\n", "  (SELECT outlet_id,\n", "          outlet_name,\n", "          count(DISTINCT LOCATION) AS no_of_locations\n", "   FROM row_number\n", "   GROUP BY 1,\n", "            2),\n", "     racks_per_day AS\n", "  (SELECT row_number.outlet_id,\n", "          row_number.outlet_name,\n", "          row_number.LOCATION,\n", "          row_number.row,\n", "          racks_count.no_of_locations,\n", "          racks_count.no_of_locations/59 AS racks_per_day\n", "   FROM row_number\n", "   INNER JOIN racks_count ON row_number.outlet_id=racks_count.outlet_id)\n", "SELECT outlet_id,\n", "       outlet_name,\n", "       LOCATION AS LOCATION,\n", "                   ROW,\n", "                   racks_per_day,\n", "                   floor(ROW/racks_per_day) AS day_for_task\n", "FROM racks_per_day\n", "WHERE day_for_task=DATEDIFF(DAY, '2020/04/14', CURRENT_DATE)\n", "ORDER BY day_for_task,\n", "         LOCATION \n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in x:\n", "\n", "    try:\n", "\n", "        df = pd.read_sql(stock_query, redshift, params={\"outlet_id\": outlet_id})\n", "        df1 = df.copy()\n", "        df2 = df1.drop([\"row\", \"racks_per_day\", \"day_for_task\"], axis=1)\n", "        df2[\"location\"] = df2[\"location\"].astype(str)\n", "        df2[\"new_location\"] = np.where(\n", "            df2[\"location\"].str[-1:] == \"-\", df2[\"location\"].str[:-1], df2[\"location\"]\n", "        )\n", "        df3 = df2.copy()\n", "        df3.drop([\"location\"], axis=1, inplace=True)\n", "        df5 = df3.rename(columns={\"new_location\": \"location\"})\n", "        #         file_name= \"stock_take_task_\" + str(facility) +\".csv\"\n", "        df4 = (\n", "            df3.groupby([\"outlet_id\", \"outlet_name\"])\n", "            .agg({\"new_location\": \"count\"})\n", "            .rename(columns={\"new_location\": \"Total_racks\"})\n", "            .reset_index()\n", "        )\n", "        df5.to_csv(\"stock_take.csv\", index=False)\n", "\n", "        # Sending mailers\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        from_email = \"<EMAIL>\"\n", "        subject = \"Stock take task for %s\" % today\n", "        #         to_email = email_list[email_list.outlet_id == outlet_id][\"Group Mail Id\"]\n", "        #         to_email=email_list[email_list.outlet_id==str(outlet_id)][\"Group Mail Id\"]\n", "        to_email = email_list[email_list[\"outlet_id\"] == str(outlet_id)][\n", "            \"Group Mail Id\"\n", "        ].tolist()\n", "        #         to_email=['<EMAIL>','<EMAIL>']\n", "\n", "        loader = jinja2.FileSystemLoader(\n", "            searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/inventory/report/stock_take/\"\n", "            #              searchpath=\"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/inventory/report/stock_take\"\n", "        )\n", "        tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "        template = tmpl_environ.get_template(\"email_template.html\")\n", "\n", "        df12 = df4\n", "        message_text = template.render(\n", "            products=df12.to_dict(orient=\"records\"), today=today, outlet=outlet_id\n", "        )\n", "\n", "        #         test_to='<EMAIL>'\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject,\n", "            bcc=[\n", "                \"<EMAIL>\",\n", "                \"<EMAIL>\",\n", "                \"<EMAIL>\",\n", "            ],\n", "            html_content=message_text,\n", "            files=[\"stock_take.csv\"],\n", "        )\n", "\n", "    #         print(\"sending for outlet: \" + str(outlet_id))\n", "    except:\n", "        message = \"failed for outlet_id - \" + str(outlet_id)\n", "        print(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cd stock_take/"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}