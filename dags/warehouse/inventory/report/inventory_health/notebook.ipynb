{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "9V8fr05xqMha"}, "outputs": [], "source": ["# pip install boto"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "N1tZJb_RqMhf"}, "outputs": [], "source": ["import random\n", "import pickle\n", "import psycopg2\n", "import pandas as pd\n", "import scipy\n", "import sklearn\n", "import numpy as np\n", "import pickle\n", "import logging\n", "\n", "# import boto\n", "# from boto.s3.key import Key\n", "from datetime import datetime\n", "from pytz import timezone\n", "import datetime\n", "from datetime import datetime\n", "import sqlalchemy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "CZvYbe-aqMhk"}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "now = datetime.now()\n", "india = timezone(\"Asia/Kolkata\")\n", "red_con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "w-fWYZr_qMhr"}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "with bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            -- b.outlet_id,\n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from  lake_rpc.item_outlet_tag_mapping b\n", "        -- Left Join lake_warehouse_location.warehouse_outlet_mapping c on b.outlet_id = c.warehouse_id\n", "        join lake_retail.view_console_outlet po ON b.outlet_id = po.id\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", "),\n", "\n", "bucketab as (\n", "    select \n", "        item_id,\n", "        facility_id,\n", "        case when tag_A = 1 then 'A'\n", "             when tag_b = 1 then 'B'\n", "             else 'None' end as bucket_a_b\n", "        -- tag_x + tag_A + tag_b + tag_transfer as sum_check\n", "    from bucket_info\n", "),\n", "\n", "bucketx as (\n", "    select \n", "        item_id,\n", "        facility_id,\n", "        case when tag_x = 1 then 'X' else 'None' end as bucket_x\n", "    from bucket_info\n", "),\n", "\n", "bucket_tf as (\n", "    select \n", "        item_id,\n", "        facility_id,\n", "        case when tag_transfer = 1 then 'transfer' else 'None' end as bucket_transfer_type\n", "    from bucket_info\n", "),\n", "\n", "assortment_status as (\n", "    select \n", "        a.item_id::int as item_id,\n", "        b.facility_id::int as facility_id,\n", "        b.city,\n", "        -- a.city_id,\n", "        a.active,\n", "        date(a.created_at + interval '5.5 hours') as created_at,\n", "        min(a.master_assortment_substate_id) as master_assortment_substate_id\n", "    from lake_rpc.product_master_assortment a\n", "    join (\n", "        select \n", "            l.name as city,\n", "            o.tax_location_id as city_id,\n", "            o.facility_id\n", "        from lake_retail.console_outlet o\n", "        join lake_retail.console_location l on o.tax_location_id = l.id and o.facility_id not in (154,38)\n", "        where (o.name like '%%SSC%%' or o.name like '%%MODI%%'or o.name like '%%DarkS%%' or o.id = 948)\n", "        group by 1,2,3\n", "    )b on a.city_id = b.city_id\n", "    -- where active = 1\n", "    -- and item_id = 10000818\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "rpc_prod as (\n", "    select \n", "        item_id::int as item_id,\n", "        name,\n", "        variant_description,\n", "        inner_case_size,\n", "        outer_case_size,\n", "        storage_type,\n", "        weight_in_gm,\n", "        storage_factor\n", "    from (\n", "        select\n", "            item_id,\n", "            name,\n", "            variant_description,\n", "            inner_case_size,\n", "            outer_case_size, \n", "            CASE \n", "                WHEN p.storage_type = 1 THEN 'Regular' \n", "                WHEN p.storage_type = 2 THEN 'Fridge'\n", "                WHEN p.storage_type = 3 THEN 'Freezer' \n", "                WHEN p.storage_type = 4 THEN 'Large'\n", "                WHEN p.storage_type = 5 THEN 'Heavy'\n", "                WHEN p.storage_type = 6 THEN 'Non_Veg_Cold' \n", "                WHEN p.storage_type = 7 THEN 'Non_Veg_Frozen' \n", "                WHEN p.storage_type = 8 THEN 'Fast_Moving' \n", "                ELSE 'Not Assigned' \n", "            END AS storage_type,\n", "            weight_in_gm,\n", "            case \n", "                when p.storage_type in  (6,2) and weight_in_gm < 50 then '0.2'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 100 and weight_in_gm >=50 then '0.4'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 150 and weight_in_gm >=100 then '0.6'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 200 and weight_in_gm >=150 then '0.8'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 250 and weight_in_gm >=200 then '1'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 300 and weight_in_gm >=250 then '1.2'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 350 and weight_in_gm >=300 then '1.4'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 400 and weight_in_gm >=350 then '1.6'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 450 and weight_in_gm >=400 then '1.8'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 500 and weight_in_gm >=450 then '2'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 550 and weight_in_gm >=500 then '2.2'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 600 and weight_in_gm >=550 then '2.4'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 650 and weight_in_gm >=600 then '2.6'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 700 and weight_in_gm >=650 then '2.8'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 750 and weight_in_gm >=700 then '3'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 800 and weight_in_gm >=750 then '3.2'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 850 and weight_in_gm >=800 then '3.4'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 900 and weight_in_gm >=850 then '3.6'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 950 and weight_in_gm >=900 then '3.8'\n", "                when p.storage_type in  (6,2) and weight_in_gm < 1000 and weight_in_gm >=950 then '4'\n", "                when p.storage_type in  (6,2) and weight_in_gm >=1000 then '5'\n", "                when p.storage_type in (7,1,3,8) and handling_type <> 4 then '1'\n", "                when p.storage_type = 5 and handling_type <> 4 then (weight_in_gm*1.0/1000)\n", "                when p.storage_type in (1,8,5,4) and handling_type = 4  then 1.5\n", "            end as storage_factor,\n", "            row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "        from rpc_product_product p\n", "        where active=1\n", "        and approved=1\n", "    )\n", "    where row_rank = 1\n", "    group by 1,2,3,4,5,6,7,8\n", "),\n", "\n", "it_pd AS(\n", "    SELECT \n", "        item_id,\n", "        product_id,\n", "        updated_on,\n", "        multiplier,\n", "        aspr,\n", "        flag,\n", "        max(updated_on) over (partition BY product_id) AS last_updated\n", "    FROM it_pd_log\n", "    GROUP BY 1,2,3,4,5,6\n", "),\n", "\n", "mapping AS(\n", "    SELECT \n", "        item_id,\n", "        product_id,\n", "        multiplier,\n", "        aspr,\n", "        flag\n", "    FROM it_pd\n", "    WHERE last_updated = updated_on\n", "    GROUP BY 1,2,3,4,5\n", "),\n", "\n", "fac_base as(\n", "    select\n", "        date(oo.install_ts + interval '5.5 hours') as sale_date,\n", "        pl.name as city,\n", "        po.id as outlet_id,\n", "        po.name as outlet,\n", "        po.facility_id,\n", "        cf.name as facility,\n", "        osi.suborder_id,\n", "        oo.cart_id,\n", "        map.item_id,\n", "        ooi.product_id,\n", "        -- product_title,\n", "        ooi.quantity,\n", "        (ooi.quantity*map.multiplier) as itm_quantity,\n", "        -- ooi.selling_price\n", "        (ooi.selling_price*map.aspr) as selling_price\n", "    from lake_oms_bifrost.oms_order_item ooi\n", "    inner join lake_oms_bifrost.oms_suborder_item osi on ooi.id = osi.order_item_id\n", "    inner join lake_oms_bifrost.oms_order oo on ooi.order_id=oo.id\n", "    -- inner join consumer.fulfilment_order fo on oo.id = fo.order_id\n", "    inner join consumer.view_gr_order go on osi.suborder_id = go.id and oo.cart_id = go.cart_id and go.current_state_id <> 10\n", "    inner join lake_ims.ims_order_details imd on go.id = imd.order_id\n", "    left join lake_oms_bifrost.oms_merchant om on oo.merchant_id = om.id\n", "    left join mapping map on map.product_id = ooi.product_id\n", "    left join lake_retail.console_outlet po on imd.outlet = po.id and po.facility_id is not null\n", "    left join lake_retail.console_location pl ON po.tax_location_id = pl.id\n", "    left join lake_crates.facility cf on po.facility_id = cf.id\n", "    where date(oo.install_ts + interval '5.5 hours') between current_date - 30 and current_date - 1\n", "    and (oo.\"type\" is null or oo.\"type\"  not ilike '%%internal%%')\n", "    and om.city_name not in ('Not in service area','Hapur','Haridwar')\n", "    and ooi.product_title not ilike '%%membership%%'\n", "    and om.city_name not ilike '%%B2B%%'\n", "    and (po.name like '%%SSC%%' or po.name like '%%MODI%%'or po.name like '%%DarkS%%' or po.id = 948)\n", "    -- and oo.cart_id = 49629476\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "base as (\n", "    select\n", "        f.city,\n", "        f.sale_date,\n", "        -- f.outlet_id,\n", "        -- f.outlet,\n", "        f.facility_id,\n", "        f.facility,\n", "        f.item_id,\n", "        sum(f.itm_quantity) as qty,\n", "        sum(f.quantity*f.selling_price) as gmv\n", "    from fac_base f\n", "    group by 1,2,3,4,5--,6,7\n", "),\n", "\n", "sales_data as (\n", "    select\n", "        f.city,\n", "        -- f.outlet_id,\n", "        -- f.outlet::varchar as outlet,\n", "        f.facility_id,\n", "        f.facility::varchar as facility,\n", "        f.item_id::int as item_id,\n", "        -- f.cart_id,\n", "        -- count(distinct case when f.sale_date between current_date - 7 and current_date - 1 then f.cart_id else null end) as carts,\n", "        -- count(distinct case when f.sale_date between current_date - 7 and current_date - 1 then f.cart_id else null end) as carts,\n", "        -- count(distinct case when f.sale_date between current_date - 7 and current_date - 1 then f.cart_id else null end) as carts,\n", "        sum(case when f.sale_date between current_date - 7 and current_date - 1 then gmv else 0 end) as last_7_days_gmv,\n", "        sum(case when f.sale_date between current_date - 14 and current_date - 1 then gmv else 0 end) as last_14_days_gmv,\n", "        sum(case when f.sale_date between current_date - 30 and current_date - 1 then gmv else 0 end) as last_30_days_gmv,\n", "        sum(case when f.sale_date between current_date - 7 and current_date - 1 then qty else 0 end) as last_7_days_qty,\n", "        sum(case when f.sale_date between current_date - 14 and current_date - 1 then qty else 0 end) as last_14_days_qty,\n", "        sum(case when f.sale_date between current_date - 30 and current_date - 1 then qty else 0 end) as last_30_days_qty\n", "        -- sum(f.itm_quantity) as qty,\n", "        -- sum(f.quantity*f.selling_price) as gmv\n", "    from base f\n", "    group by 1,2,3,4--,5,6\n", "),\n", "\n", "inventory_data as (\n", "    with inventory_base as (\n", "        select \n", "            item_id,\n", "            outlet_id,\n", "            max(id) as id\n", "        from consumer.ims_item_inventory_log\n", "        where date(created_at + interval '5.5 hours') <= current_date -- and current_date\n", "        -- and date(created_at + interval '5.5 hours') > current_date - 60\n", "        group by 1,2--,3\n", "    ),\n", "    \n", "    \n", "    blckd_inv as (\n", "        Select \n", "            item_id,\n", "            outlet_id,\n", "            -- po.facility_id,\n", "            sum(Quantity) as blocked\n", "        from lake_ims.ims_item_blocked_inventory b\n", "        Where date((updated_at + interval'330 minute')) = current_date\n", "        Group By 1,2\n", "    ),\n", "        \n", "    ci_base as (\n", "        SELECT\n", "            pl.name as city,\n", "            -- a.outlet_id,\n", "            -- po.name as outlet_name,\n", "            po.facility_id,\n", "            cf.name AS facility_name,\n", "            a.item_id,\n", "            sum(l.actual_quantity) as current_inventory,\n", "            sum(l.blocked_quantity) as blocked,\n", "            max(im.upper_limit) as upper_limit\n", "        from inventory_base a\n", "        join consumer.ims_item_inventory_log l on a.id = l.id \n", "        join lake_ims.ims_item_inventory im on a.item_id = im.item_id and a.outlet_id = im.outlet_id\n", "        join lake_retail.view_console_outlet po ON l.outlet_id = po.id and po.facility_id not in (154,38)\n", "        left join lake_retail.console_location pl ON po.tax_location_id = pl.id\n", "        -- left join blckd_inv bv on a.item_id = bv.item_id and a.outlet_id = bv.outlet_id\n", "        left join lake_crates.facility cf on po.facility_id = cf.id\n", "        where (po.name like '%%SSC%%' or po.name like '%%MODI%%'or po.name like '%%DarkS%%' or po.id = 948)\n", "        and po.name not like '%%(Crof)%%'\n", "        and po.name not like '%%(LA)%%'\n", "        -- po.facility_id = 29\n", "        GROUP BY 1,2,3,4 -- ,5,6,7,8\n", "    )\n", "    \n", "    select \n", "        c.city,\n", "        -- c.outlet_id,\n", "        -- c.outlet_name,\n", "        c.facility_id,\n", "        c.facility_name,\n", "        c.item_id,\n", "        c.current_inventory,\n", "        c.blocked,\n", "        -- bv.blocked,\n", "        c.upper_limit\n", "    from ci_base c\n", "    -- left join blckd_inv bv on c.item_id = bv.item_id and c.outlet_id = bv.outlet_id\n", "    group by 1,2,3,4,5,6,7 -- ,8,9\n", "),\n", "\n", "sales_inv as(\n", "    select\n", "        case when i.city is null then s.city else i.city end as city,\n", "        case when i.item_id is null then s.item_id else i.item_id end as item_id,\n", "        case when i.facility_id is null then s.facility_id else i.facility_id end as facility_id,\n", "        case when i.facility_name is null then s.facility else i.facility_name end as facility_name,\n", "        s.last_7_days_gmv,\n", "        s.last_14_days_gmv,\n", "        s.last_30_days_gmv,\n", "        s.last_7_days_qty,\n", "        s.last_14_days_qty,\n", "        s.last_30_days_qty,\n", "        i.current_inventory,\n", "        i.upper_limit,\n", "        i.blocked\n", "    from inventory_data i\n", "    full outer join sales_data s on i.item_id = s.item_id and i.facility_id = s.facility_id -- and i.outlet_id = s.outlet_id\n", "    where i.facility_name not ilike '%%Closed%'\n", "    and i.facility_name not ilike '%%(LA)%%'\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13 -- ,14,15\n", "),\n", "\n", "fin_vew as (\n", "    select \n", "        -- a.city,\n", "        (case when a.city is null then t.city else a.city end) as city,\n", "        -- t.outlet_id,\n", "        -- t.outlet_name,\n", "        -- a.facility_id,\n", "        (case when a.facility_id is null then t.facility_id else a.facility_id end) as facility_id,\n", "        cf.name as facility_name,\n", "        -- a.item_id,\n", "        (case when a.item_id is null then t.item_id else a.item_id end) as item_id,\n", "        e.name as item_name,\n", "        e.variant_description,\n", "        -- x.bucket_x,\n", "        -- ab.bucket_a_b,\n", "        -- tf.bucket_transfer_type,\n", "        t.upper_limit,\n", "        t.last_7_days_gmv,\n", "        t.last_14_days_gmv,\n", "        t.last_30_days_gmv,\n", "        t.last_7_days_qty,\n", "        t.last_14_days_qty,\n", "        t.last_30_days_qty,\n", "        t.current_inventory,\n", "        t.blocked,\n", "        (case \n", "            when a.master_assortment_substate_id = 1 then 'active'\n", "            when a.master_assortment_substate_id = 5 then 'unorderable'\n", "            else 'inactive' \n", "        end) as assortment_status\n", "    from assortment_status a\n", "    full outer join sales_inv t on t.item_id = a.item_id and t.facility_id = a.facility_id\n", "    left JOIN rpc_prod e ON a.item_id = e.item_id\n", "    left join lake_crates.facility cf on (case when a.facility_id is null then t.facility_id else a.facility_id end) = cf.id\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--,17,18,19,20,21\n", "),\n", "\n", "irr_inv as (\n", "    select \n", "        *,\n", "        'irrelevant'  as irr\n", "    from fin_vew\n", "    where last_30_days_gmv is null\n", "    and (current_inventory = 0 or current_inventory is null)\n", "    and assortment_status not in ('active','unorderable')\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17--,18,19,20,21,22\n", "),\n", "\n", "rr_inv as (\n", "    select \n", "        f.*\n", "    from fin_vew f\n", "    left join irr_inv i on f.item_id = i.item_id and f.facility_id = i.facility_id\n", "    where irr is null\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--,17,18,19,20,21\n", "),\n", "\n", "cat_map as (\n", "    Select\n", "        a.item_id,\n", "        -- a.product_id,\n", "        max(b.category_id) as category_id,\n", "        max(c.name) as l2,\n", "        max(d.name) as l1,\n", "        max(e.name) as l0,\n", "        max(p.type) as ptype \n", "    from mapping a\n", "    Left Join lake_cms.gr_product p on a.product_id = p.id \n", "    Left Join lake_cms.gr_product_category_mapping b on a.product_id = b.product_id and b.is_primary = true  \n", "    Left Join lake_cms.gr_category c on b.category_id = c.id and c.enabled_flag = 1\n", "    Left Join lake_cms.gr_category d on c.parent_category_id = d.id and d.enabled_flag =1 \n", "    Left Join lake_cms.gr_category e on d.parent_category_id = e.id  and e.enabled_flag =1 \n", "    Where flag = 'base'\n", "    GROUP BY 1\n", "),\n", "\n", "open_pos as (\n", "  SELECT\n", "      -- outlet AS outlet_id,\n", "      facility_id,\n", "      item_id ,\n", "      listagg(id ,';') AS open_po,\n", "      count(id) AS num_open_po,\n", "      listagg(schedule_time , ';') AS schedules,\n", "      listagg(expiry_date , ';') AS expiries,\n", "      sum(units_ordered) AS ordered_quantity\n", "  FROM (\n", "      SELECT\n", "          po.id,\n", "          po.outlet_id,\n", "          wom.cloud_store_id AS outlet,\n", "          poo.facility_id,\n", "          poi.item_id,\n", "          date((po.created_at + interval'330 minute')) AS created_date,\n", "          po.expiry_date,\n", "          po.delivery_date,\n", "          pos.po_state_id ,\n", "          (posc.schedule_date_time + interval'330 minute') AS schedule_time,\n", "          (poi.units_ordered - coalesce(pg.grn_qty,0)) as units_ordered\n", "      FROM lake_po.purchase_order po\n", "      LEFT JOIN lake_po.purchase_order_items poi ON po.id = poi.po_id\n", "      LEFT JOIN lake_po.purchase_order_status pos ON po.id = pos.po_id\n", "      LEFT JOIN lake_po.po_schedule posc ON po.id = posc.po_id_id\n", "      LEFT JOIN lake_retail.warehouse_outlet_mapping wom ON po.outlet_id = wom.warehouse_id\n", "      join lake_retail.view_console_outlet poo ON po.outlet_id = poo.id\n", "      inner join lake_po.purchase_order_state posta on posta.id = pos.po_state_id\n", "      left join (select po_id, item_id, sum(quantity) as grn_qty from lake_po.po_grn group by 1,2)pg\n", "      on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "      where\n", "      ( posta.name in ('Created','Scheduled','Unscheduled','Rescheduled') or po.is_multiple_grn = 1) and posta.name <> 'Expired'\n", "       and date(po.issue_date + interval '5.5 hours') between '2019-10-01' and Current_date\n", "      group by 1,2,3,4,5,6,7,8,9,10,11\n", "  ) a\n", "  WHERE outlet IS NOT NULL\n", "  GROUP BY 1,2\n", "),\n", "\n", "vendor_details as (\n", "    with item_vd_tat as (\n", "        SELECT \n", "            a.item_id,\n", "            b.facility_id,\n", "            a.vendor_id,\n", "            max(a.tat_days) AS tat_days\n", "        FROM (\n", "            SELECT \n", "                a.item_id,\n", "                a.outlet_id,\n", "                a.vendor_id,\n", "                b.tat_days\n", "            FROM(\n", "                SELECT \n", "                    item_id,\n", "                    outlet_id,\n", "                    vendor_id,\n", "                    max(updated_at) AS MAX\n", "                FROM lake_po.item_outlet_vendor_tat\n", "                GROUP BY 1,2,3\n", "            ) a\n", "            LEFT JOIN lake_po.item_outlet_vendor_tat b ON a.item_id = b.item_id\n", "            AND a.outlet_id = b.outlet_id\n", "            AND a.vendor_id = b.vendor_id\n", "            AND a.MAX = b.updated_at\n", "            WHERE b.active = 1\n", "            GROUP BY 1,2,3,4\n", "        )a\n", "        JOIN lake_retail.console_outlet b ON a.outlet_id = b.id\n", "        JOIN lake_retail.console_location c ON b.tax_location_id = c.id\n", "        WHERE b.active = 1 -- AND c.id IN ('1')\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    item_p_info as(\n", "        SELECT \n", "            a.item_id,\n", "            fac.facility_id,\n", "            a.city_id ,\n", "            b.brand_id,\n", "            bd.manufacturer_id,\n", "            mf.name,\n", "            va.vendor_id ,\n", "            vv.vendor_name\n", "    \n", "        FROM(   \n", "            SELECT \n", "                item_id,\n", "                city_id\n", "            FROM lake_rpc.product_master_assortment\n", "            GROUP BY 1,2\n", "        )a\n", "        \n", "        LEFT JOIN (\n", "            SELECT \n", "                item_id,\n", "                brand_id\n", "            FROM rpc_product_product\n", "            WHERE active = 'true'\n", "            GROUP BY 1,2\n", "        )b ON a.item_id = b.item_id\n", "        \n", "        LEFT JOIN (\n", "            SELECT \n", "                id,\n", "                manufacturer_id\n", "            FROM lake_rpc.product_brand\n", "            WHERE active = 1\n", "            GROUP BY 1,2\n", "        ) bd ON b.brand_id = bd.id\n", "        \n", "        LEFT JOIN(\n", "            SELECT\n", "                id,\n", "                name\n", "            FROM lake_rpc.product_manufacturer\n", "            GROUP BY 1,2\n", "        )mf ON bd.manufacturer_id = mf.id\n", "        \n", "        LEFT JOIN (\n", "            SELECT \n", "                tax_location_id as city_id ,\n", "                facility_id\n", "            FROM lake_retail.console_outlet\n", "            Where active = 1\n", "            and (name like '%%SSC%%' or name like '%%MODI%%'or name like '%%DarkS%%' or id = 948)\n", "            GROUP BY 1,2\n", "        ) fac ON a.city_id = fac.city_id\n", "        \n", "        LEFT JOIN lake_vms.vms_vendor_alignment va ON a.item_id = va.item_id \n", "            AND a.city_id = va.city_id \n", "            and va.priority = 1 \n", "            and va.is_aligned = 1 \n", "            and va.active = 1\n", "        \n", "        LEFT JOIN lake_vms.vms_vendor vv ON va.vendor_id = vv.id\n", "        -- WHERE a.item_id = 10000818\n", "        GROUP BY 1,2,3,4,5,6,7,8\n", "    )\n", "    \n", "    select \n", "        a.item_id ,\n", "        a.facility_id ,\n", "        a.name AS manufacturer_name ,\n", "        a.vendor_name,\n", "        vc.identifier as po_cycle_name,\n", "        c.tat_days,\n", "        d.load_size,\n", "        d.load_type,\n", "        a.vendor_id\n", "    from item_p_info a\n", "    LEFT JOIN (\n", "        SELECT \n", "            vendor_id ,\n", "            manufacturer_id ,\n", "            facility_id,\n", "            po_cycle_type_id\n", "        FROM lake_vms.vendor_manufacturer_physical_facility_attributes\n", "        GROUP BY 1,2,3,4\n", "    )b ON a.vendor_id = b.vendor_id\n", "        AND a.manufacturer_id = b.manufacturer_id\n", "        AND a.facility_id = b.facility_id\n", "    LEFT JOIN lake_vms.po_cycle_types vc ON b.po_cycle_type_id = vc.id\n", "    LEFT JOIN item_vd_tat c ON a.item_id = c.item_id\n", "        AND a.facility_id = c.facility_id\n", "        AND a.vendor_id = c.vendor_id\n", "    Left Join lake_vms.vendor_physical_facility_attributes d on a.facility_id = d.facility_id and a.vendor_id = d.vendor_id\n", "    -- WHERE a.item_id = 10000818\n", "    GROUP BY 1,2,3,4,5,6,7,8,9\n", "),\n", "\n", "put_away as (\n", "    SELECT \n", "        item_id,\n", "        facility_id ,\n", "        sum(pending_putaway) AS pending_putaway,\n", "        sum(putaway_in_progress) AS putaway_in_progress\n", "    FROM (\n", "        SELECT \n", "        a.item_id,\n", "        a.pending_putaway,\n", "        a.putaway_in_progress,\n", "        b.facility_id\n", "        FROM (\n", "            SELECT \n", "                put_list.outlet_id,\n", "                item.item_id AS item_id,\n", "                CASE WHEN put_list.current_state_id IN (1,2) THEN sum(item.quantity) END AS pending_putaway,\n", "                CASE WHEN put_list.current_state_id IN (3) THEN sum(item.quantity) END AS putaway_in_progress\n", "            FROM lake_warehouse_location.warehouse_put_list put_list\n", "            INNER JOIN lake_warehouse_location.warehouse_put_list_item item ON item.putlist_id=put_list.id\n", "            WHERE date((put_list.created_at + interval'330 minute')) >= CURRENT_DATE-1\n", "            AND put_list.current_state_id IN (1,2,3)\n", "            GROUP BY 1,2,put_list.current_state_id\n", "            ) a\n", "        LEFT JOIN lake_retail.console_outlet b ON a.outlet_id = b.id AND b.active = 1\n", "        JOIN lake_retail.console_location c ON b.tax_location_id = c.id\n", "    )a\n", "    GROUP BY 1,2\n", "),\n", "\n", "sto_info as(\n", "    SELECT \n", "        facility_id ,\n", "        item_id ,\n", "        sto_id,\n", "        (billed - inward) AS in_transit\n", "    FROM(\n", "        SELECT facility_id ,\n", "               item_id ,\n", "               listagg(sto_id , ',') AS sto_id,\n", "               sum(billed_quantity) AS billed ,\n", "               sum(inward_quantity) AS inward\n", "        FROM(\n", "            SELECT a.Sto_id,\n", "                  a.merchant_outlet_id,\n", "                  b.facility_id,\n", "                  c.item_id,\n", "                  date((a.created_at + interval'330 minute')) AS created_date,\n", "                  a.delivery_date,\n", "                  c.expected_quantity,\n", "                  c.reserved_quantity,\n", "                  c.billed_quantity,\n", "                  c.inward_quantity,\n", "                  a.sto_type,\n", "                  a.sto_state\n", "           FROM lake_ims.ims_sto_details a\n", "           LEFT JOIN lake_retail.console_outlet b ON a.merchant_outlet_id = b.id\n", "           LEFT JOIN lake_ims.ims_sto_item c ON a.sto_id = c.sto_id\n", "           GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12\n", "        )a\n", "        WHERE sto_state IN (1,2,3)\n", "        AND date(delivery_date + interval'330 minutes') >= current_date\n", "        GROUP BY 1,2\n", "    ) a\n", "    GROUP BY 1,2,3,4\n", "    order by 1,2\n", "),\n", "\n", "po_fill_rate as(\n", "       with po_data_30 as (\n", "        select\n", "            a.po_id,\n", "            b.outlet_id,\n", "            b.vendor_id,\n", "            a.item_id,\n", "            a.name as item_name,\n", "            a.landing_rate as landing_price,\n", "            a.units_ordered,\n", "            a.total_amount as item_total_amount,\n", "            a.is_pl,\n", "            b.po_type_id,\n", "            date(a.created_at) as po_creation_date,\n", "            date(b.expiry_date) as po_expiry_date,\n", "            date(b.delivery_date) as expected_po_delivery_date\n", "        from lake_po.purchase_order_items a\n", "        left join lake_po.purchase_order b on a.po_id = b.id\n", "        left join lake_po.purchase_order_status c on a.po_id = c.po_id and c.po_state_id in (4,5,10)\n", "        where date(a.created_at + INTERVAL '5.5 hours') >= CURRENT_DATE - 240\n", "        and date(b.expiry_date + INTERVAL '5.5 hours') >= current_date - 210\n", "        and c.po_id is null\n", "        \n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "    ),\n", "    \n", "    \n", "    grn_data_30 as (\n", "        select\n", "            item_id,\n", "            po_id,\n", "            sum(quantity) as grn_quantity,\n", "            max(landing_price) as grn_landing_price,\n", "            min(date(created_at)) as latest_grn_date\n", "        from lake_po.po_grn\n", "        where date(created_at) >= current_date - 210\n", "        group by 1,2\n", "    ),\n", "    \n", "    \n", "    grn_info_30 as(\n", "        select\n", "            a.*,\n", "            extract(month from a.po_creation_date) as po_creation_month,\n", "            extract(year from a.po_creation_date) as po_creation_year,\n", "            extract(month from current_date) as current_month,\n", "            extract(year from current_date) as current_year,\n", "            case\n", "                when b.grn_quantity is not null AND b.grn_quantity <> 0 then 1\n", "                else 0 \n", "            end as grn_flag,\n", "            b.grn_quantity,\n", "            b.grn_landing_price,\n", "            b.latest_grn_date as actual_po_delivery_date,\n", "            extract(month from b.latest_grn_date) as actual_po_delivery_month,\n", "            extract(year from b.latest_grn_date) as actual_po_delivery_year\n", "        from po_data_30 a\n", "        left join grn_data_30 b on a.item_id = b.item_id AND a.po_id = b.po_id\n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n", "    ),\n", "    \n", "    parent_po_child_po_30 as (\n", "        select \n", "            a.*,\n", "            case when b.child_po_id is not null then b.parent_po_id end as parent_po,\n", "            case when c.parent_po_id is not null then c.child_po_id end as child_po\n", "        from grn_info_30 a\n", "        left join lake_po.purchase_order_relations b on a.po_id = b.child_po_id\n", "        left join lake_po.purchase_order_relations c on a.po_id = c.parent_po_id\n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n", "    ),\n", "    \n", "    parent_po_child_po_join_30 as (\n", "        select\n", "            a.po_id,\n", "            a.outlet_id,\n", "            a.vendor_id,\n", "            a.item_id,\n", "            a.item_name,\n", "            a.landing_price,\n", "            a.units_ordered,\n", "            a.item_total_amount,\n", "            a.po_creation_date,\n", "            a.po_expiry_date,\n", "            a.expected_po_delivery_date,\n", "            a.current_month,\n", "            a.current_year,\n", "            a.po_type_id,\n", "            pc.facility_id,\n", "            case when a.grn_flag = 1 or b.grn_flag = 1 then 1 else 0 end as grn_flag,\n", "            coalesce(a.grn_quantity,b.grn_quantity) as grn_quantity,\n", "            coalesce(a.grn_landing_price,b.grn_landing_price) as grn_landing_price,\n", "            coalesce(a.actual_po_delivery_date,b.actual_po_delivery_date) as actual_po_delivery_date,\n", "            coalesce(a.actual_po_delivery_year,b.actual_po_delivery_year) as actual_po_delivery_year,\n", "            coalesce(a.actual_po_delivery_month,b.actual_po_delivery_month) as actual_po_delivery_month,\n", "            a.child_po,\n", "            case when a.parent_po is not null then 1 else 0 end as discard_flag\n", "        from parent_po_child_po_30 a\n", "        left join parent_po_child_po_30 b on a.child_po = b.po_id and a.item_id = b.item_id\n", "        left join  lake_retail.console_outlet pc ON pc.id = a.outlet_id\n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n", "    ),\n", "    \n", "    \n", "    po_expiry_30 as (\n", "        select\n", "            a.*,\n", "            case when b.po_grns > 0 then 1 else 0 end as po_serviced_flag,\n", "            po_expiry_date - po_creation_date as tat_days,\n", "            case when grn_flag = 1 then actual_po_delivery_date - po_creation_date end as delivery_days,\n", "            extract(year from expected_po_delivery_date) as expected_delivery_year,\n", "            extract(month from expected_po_delivery_date) as expected_delivery_month,\n", "            extract(year from po_expiry_date) as po_expiry_year,\n", "            extract(month from po_expiry_date) as po_expiry_month\n", "        from parent_po_child_po_join_30 a\n", "        left join (\n", "            select \n", "                po_id, \n", "                sum(grn_flag) as po_grns \n", "            from parent_po_child_po_join_30 \n", "            where discard_flag = 0 \n", "            group by po_id\n", "        ) b on a.po_id = b.po_id\n", "        where a.discard_flag = 0\n", "        GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30\n", "    ),\n", "    \n", "    po_released_30 as(\n", "        select\n", "            (current_year - po_expiry_year)*12 + (current_month - po_expiry_month) as lag_months,\n", "            item_id,\n", "            facility_id,\n", "            po_expiry_year,\n", "            po_expiry_month,\n", "            po_expiry_date,\n", "            count(distinct case when po_type_id <= 9 or po_type_id = 7 then po_id end) as po_tbd,\n", "            count(distinct case when po_type_id <= 9 or po_type_id = 7 then concat(po_id, item_id) end) as items_tbd,\n", "            count(case when po_serviced_flag = 1 then concat(po_id, item_id) end) as items_tbd_serviced_po,\n", "            sum(case when po_type_id <= 9 or po_type_id = 7 then units_ordered end) as units_tbd,\n", "            sum(case when grn_flag = 1 then units_ordered else 0 end) as units_tbd_articles_received,\n", "            sum(item_total_amount) as po_value_tbd,\n", "            count(distinct case when po_serviced_flag = 1 then po_id end) as po_received,\n", "            count(distinct case when grn_flag = 1 then concat(po_id, item_id) end) as items_received,\n", "            sum(case when grn_flag = 1 then grn_quantity end) as units_received,\n", "            count(distinct case when po_serviced_flag = 0 then po_id end) as po_expired,\n", "            count(distinct case when delivery_days*3.000 <= tat_days then po_id end) as po_delivery_1by3_tat,\n", "            count(distinct case when delivery_days*3.000 <= tat_days*2.000 then po_id end) as po_delivery_2by3_tat,\n", "            count(distinct case when delivery_days <= tat_days then po_id end) as po_delivery_tat,\n", "            count(distinct case when delivery_days > tat_days then po_id end) as po_delivery_after_tat,\n", "            avg(delivery_days) as actual_tat\n", "        from po_expiry_30\n", "        where date(po_expiry_date) between current_date - 30 AND current_date - 1 \n", "        GROUP BY 1,2,3,4,5,6\n", "    ),\n", "    \n", "    po_released_15 as(\n", "        select\n", "            (current_year - po_expiry_year)*12 + (current_month - po_expiry_month) as lag_months,\n", "            item_id,\n", "            facility_id,\n", "            po_expiry_year,\n", "            po_expiry_month,\n", "            po_expiry_date,\n", "            count(distinct case when po_type_id <= 9 or po_type_id = 7 then po_id end) as po_tbd,\n", "            count(distinct case when po_type_id <= 9 or po_type_id = 7 then concat(po_id, item_id) end) as items_tbd,\n", "            count(case when po_serviced_flag = 1 then concat(po_id, item_id) end) as items_tbd_serviced_po,\n", "            sum(case when po_type_id <= 9 or po_type_id = 7 then units_ordered end) as units_tbd,\n", "            sum(case when grn_flag = 1 then units_ordered else 0 end) as units_tbd_articles_received,\n", "            sum(item_total_amount) as po_value_tbd,\n", "            count(distinct case when po_serviced_flag = 1 then po_id end) as po_received,\n", "            count(distinct case when grn_flag = 1 then concat(po_id, item_id) end) as items_received,\n", "            sum(case when grn_flag = 1 then grn_quantity end) as units_received,\n", "            count(distinct case when po_serviced_flag = 0 then po_id end) as po_expired,\n", "            count(distinct case when delivery_days*3.000 <= tat_days then po_id end) as po_delivery_1by3_tat,\n", "            count(distinct case when delivery_days*3.000 <= tat_days*2.000 then po_id end) as po_delivery_2by3_tat,\n", "            count(distinct case when delivery_days <= tat_days then po_id end) as po_delivery_tat,\n", "            count(distinct case when delivery_days > tat_days then po_id end) as po_delivery_after_tat,\n", "            avg(delivery_days) as actual_tat\n", "        from po_expiry_30\n", "        where date(po_expiry_date) between current_date - 15 AND current_date - 1 \n", "        GROUP BY 1,2,3,4,5,6\n", "    ),\n", "    \n", "    po_grn_30 as (\n", "        SELECT \n", "            item_id,\n", "            facility_id,\n", "            sum(units_tbd) AS po_qty_30,\n", "            sum(units_received) AS grn_qty_30\n", "        FROM po_released_30\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    po_grn_15 as (\n", "        SELECT \n", "            item_id,\n", "            facility_id,\n", "            sum(units_tbd) AS po_qty_15,\n", "            sum(units_received) AS grn_qty_15\n", "        FROM po_released_15\n", "        GROUP BY 1,2\n", "    )\n", "    \n", "    select\n", "        coalesce(a.item_id,b.item_id)::int as item_id,\n", "        coalesce(a.facility_id,b.facility_id)::int as facility_id,\n", "        po_qty_30,\n", "        grn_qty_30,\n", "        po_qty_15,\n", "        grn_qty_15\n", "    from po_grn_30 a\n", "    full outer join po_grn_15 b on a.item_id = b.item_id and a.facility_id = b.facility_id\n", "    group by 1,2,3,4,5,6\n", "),\n", "\n", "forecast as(\n", "    select\n", "        item_id,\n", "        case \n", "            when facility_id = 18 then 48\n", "            when facility_id = 21 then 12\n", "            when facility_id = 40 then 33\n", "            when facility_id = 118 then 26\n", "            when facility_id = 31 then 30\n", "            else facility_id \n", "        end as facility_id, \n", "        sum(forward_forecast_7_days) as next_7_days,\n", "        sum(forward_forecast_30_days) as next_30_Days,\n", "        sum(backward_forecast_7_days) as  previous_forecast_7_days\n", "        \n", "    from (\n", "        select \n", "            a.item_id,\n", "            facility_id,\n", "            sum(Case when (date between current_date and current_date + 7) Then a.consumption end) as forward_forecast_7_days,\n", "            sum(Case when (date between current_date and current_date + 30) Then a.consumption end) as forward_forecast_30_days,\n", "            sum(Case when (date between current_date - 8 and current_date - 1) Then a.consumption end) as backward_forecast_7_days\n", "        from lake_snorlax.date_wise_consumption a\n", "        left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "        where facility_id not in (48,12,33,26,30)\n", "        and date between current_date - 8 and current_Date + 30\n", "        group by 1,2\n", "    )\n", "    group by 1,2\n", "),\n", "\n", "lp_info as(\n", "    select \n", "        -- lp.outlet_id::int as outlet_id,\n", "        po.facility_id,\n", "        item_id::int as item_id,\n", "        avg(landing_price) as landing_price\n", "    from (\n", "        select \n", "            *,\n", "            max(date(dt_ist)) over (partition BY item_id,backend_merchant) AS max_updated_on\n", "        from consumer.weighted_landing_price lp\n", "        where date(dt_ist) <= current_date\n", "    ) lp\n", "    LEFT JOIN lake_retail.console_outlet po ON lp.outlet_id = po.id\n", "    where lp.dt_ist=max_updated_on\n", "    group by 1,2\n", "),\n", "\n", "item_live as (\n", "    with mer_fac as (\n", "        WITH FC_MAPPING AS (\n", "            SELECT\n", "                pco.facility_id,\n", "                cf.name AS facility_name,\n", "                bom.external_id AS backend_merchant_id,\n", "                bom.name AS backend_merchant_name,\n", "                pco.id AS outlet_id,\n", "                pco.name AS outlet_name\n", "            FROM lake_oms_bifrost.view_oms_merchant AS bom\n", "            INNER JOIN lake_retail.console_outlet_cms_store AS cms ON bom.external_id = cms.cms_store \n", "                AND cms.active = 1 \n", "                AND cms.cms_update_active = 1\n", "                AND virtual_merchant_type <> 'superstore_merchant'\n", "            INNER JOIN lake_retail.console_outlet AS pco ON cms.outlet_id = pco.id\n", "            INNER JOIN lake_crates.facility AS cf ON cf.id = pco.facility_id\n", "            GROUP BY 1,2,3,4,5,6\n", "        ),\n", "        \n", "        CAPACITY_BASE AS(\n", "            SELECT\n", "                c.cms_city_id AS city_id,\n", "                c.name AS city,\n", "                w.backend_merchant_id,\n", "                w.name AS backend_merchant_name,\n", "                m.frontend_merchant_id,\n", "                m.name AS frontend_merchant_name,\n", "                s.name AS station\n", "            FROM lake_supply_orchestrator.sco_network_path AS np\n", "            INNER JOIN lake_supply_orchestrator.sco_warehouse AS w ON np.warehouse_id = w.id\n", "                AND np.active = true AND w.active = true\n", "            INNER JOIN lake_supply_orchestrator.sco_merchant AS m ON np.merchant_id = m.id AND m.active = true\n", "            INNER JOIN lake_supply_orchestrator.sco_city AS c ON m.city_id = c.id AND c.active = true\n", "            INNER JOIN lake_supply_orchestrator.sco_station AS s ON np.station_id = s.id AND s.active = true\n", "            GROUP BY 1,2,3,4,5,6,7\n", "        )\n", "        \n", "        SELECT\n", "            facility_id,\n", "            facility_name,\n", "            fc.backend_merchant_id,\n", "            fc.backend_merchant_name,\n", "            frontend_merchant_id,\n", "            frontend_merchant_name\n", "        FROM fc_mapping AS fc\n", "        INNER JOIN capacity_base AS cb ON fc.backend_merchant_id = cb.backend_merchant_id\n", "        GROUP BY 1,2,3,4,5,6\n", "    ),\n", "    \n", "    price_history as (\n", "        select\n", "            date(gp.install_ts+interval '5.5 hours') as date,\n", "            mf.facility_id,\n", "            -- gp.merchant_id,\n", "            m.item_id,\n", "            gp.product_id\n", "        from gr_product_price_history gp\n", "        INNER JOIN mer_fac mf on gp.merchant_id = mf.frontend_merchant_id\n", "        left join mapping m on gp.product_id = m.product_id\n", "        where date(gp.install_ts+interval '5.5 hours') BETWEEN current_date - 30 and current_date\n", "        group by 1,2,3,4 -- ,5\n", "    )\n", "    \n", "    SELECT \n", "        item_id,\n", "        facility_id,\n", "        count(distinct case when (date between current_date-7 and current_date-1) Then date end ) as live_days_7,\n", "        count(distinct case when (date between current_date-14 and current_date-1) Then date end ) as live_days_14,\n", "        count(distinct case when (date between current_date-30 and current_date-1) Then date end ) as live_days_30\n", "    from price_history\n", "    group by 1,2\n", "),\n", "\n", "unreliable_unorderable as (\n", "        select\n", "        item_id,\n", "        facility_id,\n", "        facility,\n", "        sum(Unreliable)::int as Unreliable,\n", "        sum(Unorderable)::int as Unorderable\n", "    from(\n", "        select \n", "            a.item_id,\n", "            pos.facility_id,\n", "            cf.name as facility,\n", "            case when tag_type_id=9 then tag_value end as Unreliable,\n", "            case when tag_type_id=10 then tag_value end as Unorderable\n", "        from lake_rpc.item_outlet_tag_mapping as a\n", "        inner join lake_retail.console_outlet as pos on a.outlet_id=pos.id\n", "        left join lake_crates.facility cf on cf.id = pos.facility_id \n", "        where tag_value = 1\n", "        group by 1,2,3,4,5\n", "    )\n", "    group by 1,2,3\n", "),\n", "\n", "deliver_base as(\n", "    select\n", "        date(ooe.install_ts + interval '5.5 hours') as date_,\n", "        po.facility_id,\n", "        -- om.name as merchant,\n", "        -- outlet,\n", "        cf.name as facility,\n", "        -- po.name as outlet,\n", "        oo.cart_id,\n", "        ooi.product_id,\n", "        map.item_id,\n", "        ooi.quantity,\n", "        -- (ooi.quantity*map.multiplier) as itm_quantity,\n", "        (ooi.procured_quantity*map.multiplier) as itm_quantity,\n", "        -- ooi.selling_price,\n", "        -- (ooi.selling_price*map.aspr) as selling_price\n", "        (ooi.procurement_price*map.aspr) as selling_price\n", "        -- om.virtual_merchant_type\n", "    from lake_oms_bifrost.oms_order_item ooi\n", "    inner join lake_oms_bifrost.oms_suborder_item osi on ooi.id = osi.order_item_id\n", "    inner join lake_oms_bifrost.oms_order oo on ooi.order_id = oo.id -- and oo.cart_id = 88603694\n", "    inner join consumer.view_gr_order go on osi.suborder_id = go.id and oo.cart_id = go.cart_id and go.current_state_id <> 10\n", "    inner join lake_ims.ims_order_details imd on go.id = imd.order_id\n", "    inner join lake_oms_bifrost.oms_order_event ooe on ooi.order_id = ooe.order_id\n", "    left join lake_oms_bifrost.oms_merchant om on oo.merchant_id = om.id -- AND om.virtual_merchant_type <> 'superstore_merchant'\n", "    -- left join consumer.rt_console_outlet_cms_store cs on go.actual_merchant_id = cs.cms_store AND cs.active = 1\n", "        -- AND cs.cms_update_active = 1\n", "    left join lake_retail.console_outlet po on imd.outlet = po.id and po.facility_id is not null\n", "    left join lake_crates.facility cf on po.facility_id = cf.id\n", "    left join mapping map on map.product_id = ooi.product_id\n", "    where date(ooe.install_ts + interval '5.5 hours') between current_date - 30 and current_date - 1\n", "    AND Json_extract_path_text(ooe.extra,'to',true) = 'DELIVERED'\n", "    and oo.current_status <> 'CANCELLED'\n", "    and (oo.\"type\" is null or oo.\"type\"  not ilike '%%internal%%')\n", "    and om.city_name not in ('Not in service area','Hapur','Haridwar')\n", "    and ooi.product_title not ilike '%%membership%%'\n", "    and om.city_name not ilike '%%B2B%%'\n", "    and (po.name like '%%SSC%%' or po.name like '%%MODI%%'or po.name like '%%DarkS%%' or po.id = 948)\n", "    and po.name not like '%%(Crof)%%'\n", "    and po.name not like '%%(LA)%%'\n", "    group by 1,2,3,4,5,6,7,8,9--,10,11,12\n", "),\n", "\n", "deliver_sales as(\n", "    select\n", "        -- f.city,\n", "        -- f.sale_date,\n", "        -- f.outlet_id,\n", "        -- f.outlet,\n", "        -- cart_id,\n", "        f.facility_id,\n", "        -- f.facility,\n", "        f.item_id,\n", "        sum(f.itm_quantity) as last_30_days_del_qty,\n", "        sum(f.quantity*f.selling_price) as last_30_days_del_gmv\n", "    from deliver_base f\n", "    group by 1,2--,3 --,4,5,6,7\n", "),\n", "\n", "case_sensitivity as (\n", "    select \n", "        item_id, \n", "        vendor_id, \n", "        facility_id,\n", "        --is_case_sensitive,\n", "        case \n", "            when case_sensitivity_type = 0 then 'not case sensitive'\n", "            when case_sensitivity_type = 1 then 'inner case sensitive'\n", "            when case_sensitivity_type = 2 then 'outer case sensitive'\n", "        end as case_sensitivity_type\n", "    from lake_vms.vendor_item_physical_facility_attributes \n", "    group by 1,2,3,4--,5\n", ")\n", "\n", "select\n", "    f.city,\n", "    -- f.outlet_id,\n", "    -- f.outlet_name,\n", "    f.facility_id,\n", "    f.facility_name,\n", "    -- f.backend_facility_id as feeder_id,\n", "    -- f.backend_facility as feeder_name,\n", "    f.item_id,\n", "    r.name as item_name,\n", "    r.variant_description,\n", "    f.assortment_status,\n", "    x.bucket_x,\n", "    ab.bucket_a_b,\n", "    tf.bucket_transfer_type,\n", "    cm.l0,\n", "    cm.l1,\n", "    cm.l2,\n", "    cm.ptype,\n", "    vd.load_size,\n", "    -- vd.load_type,\n", "    case \n", "        when vd.load_type = 1 then 'VALUE'\n", "        when vd.load_type = 2 then 'WEIGHT'\n", "        when vd.load_type = 3 then 'CASE'\n", "        else null\n", "    end as load_type,  \n", "    f.upper_limit,\n", "    f.current_inventory,\n", "    -- coalesce(f.backend_inventory,0) as feeder_quantity,\n", "    il.live_days_7::int as live_days_7,\n", "    il.live_days_14::int as live_days_14,\n", "    il.live_days_30::int as live_days_30,\n", "    f.blocked::int as blocked,\n", "    po.open_po,\n", "    po.num_open_po,\n", "    po.schedules,\n", "    po.expiries,\n", "    po.ordered_quantity as open_po_qty,\n", "    vd.vendor_name,\n", "    vd.manufacturer_name,\n", "    vd.po_cycle_name,\n", "    vd.tat_days,\n", "    pa.pending_putaway,\n", "    pa.putaway_in_progress,\n", "    -- pob.ordered_quantity as open_po_feeder,\n", "    si.sto_id,\n", "    si.in_transit,\n", "    -- pr.grn_quantity as last_30_days_grn_qty,\n", "    -- pr.units_ordered as last_30_days_po_qty,\n", "    pr.grn_qty_30 as last_30_days_grn_qty,\n", "    pr.po_qty_30 as last_30_days_po_qty,\n", "    lp.landing_price,\n", "    f.last_7_days_qty,\n", "    f.last_14_days_qty,\n", "    f.last_30_days_qty,\n", "    f.last_7_days_gmv,\n", "    f.last_14_days_gmv,\n", "    f.last_30_days_gmv,\n", "    fr.next_7_days,\n", "    fr.next_30_Days,\n", "    fr.previous_forecast_7_days,\n", "    current_timestamp as captured_at,\n", "    r.outer_case_size,\n", "    r.storage_type,\n", "    r.weight_in_gm,\n", "    r.storage_factor,\n", "    u.Unreliable,\n", "    u.Unorderable,\n", "    cs.case_sensitivity_type as case_sensitivity,\n", "    pr.grn_qty_15 as last_15_days_grn_qty,\n", "    pr.po_qty_15 as last_15_days_po_qty,\n", "    d.last_30_days_del_qty,\n", "    d.last_30_days_del_gmv,\n", "    r.inner_case_size\n", "from rr_inv f\n", "left join cat_map cm on f.item_id = cm.item_id\n", "left join open_pos po on f.item_id = po.item_id and f.facility_id = po.facility_id\n", "-- left join open_pos pob on f.item_id = pob.item_id and f.backend_facility_id = pob.outlet_id\n", "left join vendor_details vd on f.item_id = vd.item_id and f.facility_id = vd.facility_id\n", "left join item_live il on f.item_id = il.item_id and f.facility_id = il.facility_id\n", "left join put_away pa on f.item_id = pa.item_id and f.facility_id = pa.facility_id\n", "left join sto_info si on f.item_id = si.item_id and f.facility_id = si.facility_id\n", "left join po_fill_rate pr on f.item_id = pr.item_id and f.facility_id = pr.facility_id\n", "left join forecast fr on f.item_id = fr.item_id and f.facility_id = fr.facility_id\n", "left join lp_info lp on f.item_id = lp.item_id and f.facility_id = lp.facility_id\n", "left join rpc_prod r on f.item_id = r.item_id\n", "left join case_sensitivity cs on f.item_id = cs.item_id and f.facility_id = cs.facility_id and vd.vendor_id = cs.vendor_id\n", "left join unreliable_unorderable u on f.item_id = u.item_id and f.facility_id = u.facility_id\n", "left join deliver_sales d on f.item_id = d.item_id and f.facility_id = d.facility_id \n", "left join bucketab ab on f.facility_id = ab.facility_id and f.item_id = ab.item_id\n", "left join bucketx x on f.facility_id = x.facility_id and f.item_id = x.item_id\n", "left join bucket_tf tf on f.facility_id = tf.facility_id and f.item_id = tf.item_id\n", "Where facility_name <> 'Not In Service'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60\n", "\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "bfTmiN2hqMhw"}, "outputs": [], "source": ["print(datetime.now(india).strftime(\"%Y-%m-%d %H:%M:%S\"), \"running queries.........\")\n", "# df = pd.read_sql(q,con)\n", "df = pd.read_sql(sqlalchemy.text(q), red_con)\n", "print(datetime.now(india).strftime(\"%Y-%m-%d %H:%M:%S\"), \"done!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "xFN4di_oqMhz"}, "outputs": [], "source": ["col_lst = [\n", "    \"upper_limit\",\n", "    \"current_inventory\",\n", "    \"live_days_7\",\n", "    \"live_days_14\",\n", "    \"live_days_30\",\n", "    \"blocked\",\n", "    \"num_open_po\",\n", "    \"open_po_qty\",\n", "    \"tat_days\",\n", "    \"pending_putaway\",\n", "    \"putaway_in_progress\",\n", "    \"in_transit\",\n", "    \"last_30_days_grn_qty\",\n", "    \"last_30_days_po_qty\",\n", "    \"last_7_days_qty\",\n", "    \"last_14_days_qty\",\n", "    \"last_30_days_qty\",\n", "    \"inner_case_size\",\n", "    \"outer_case_size\",\n", "    \"weight_in_gm\",\n", "    \"unreliable\",\n", "    \"unorderable\",\n", "    \"last_15_days_grn_qty\",\n", "    \"last_15_days_po_qty\",\n", "    \"last_30_days_del_qty\",\n", "]\n", "for col in col_lst:\n", "    df[col] = df[col].astype(pd.Int64Dtype())\n", "\n", "col_lst = [\n", "    \"last_30_days_del_gmv\",\n", "    \"storage_factor\",\n", "    \"last_7_days_gmv\",\n", "    \"last_14_days_gmv\",\n", "    \"last_30_days_gmv\",\n", "    \"next_7_days\",\n", "    \"next_30_days\",\n", "    \"previous_forecast_7_days\",\n", "    \"landing_price\",\n", "]\n", "for col in col_lst:\n", "    df[col] = df[col].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "-IXjOOxrqMh5"}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"variant_description\", \"type\": \"varchar\"},\n", "    {\"name\": \"assortment_status\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_a_b\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_transfer_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\"},\n", "    {\"name\": \"ptype\", \"type\": \"varchar\"},\n", "    {\"name\": \"load_size\", \"type\": \"varchar\"},\n", "    {\"name\": \"load_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"upper_limit\", \"type\": \"integer\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"integer\"},\n", "    {\"name\": \"live_days_7\", \"type\": \"integer\"},\n", "    {\"name\": \"live_days_14\", \"type\": \"integer\"},\n", "    {\"name\": \"live_days_30\", \"type\": \"integer\"},\n", "    {\"name\": \"blocked\", \"type\": \"integer\"},\n", "    {\"name\": \"open_po\", \"type\": \"varchar(65000)\"},\n", "    {\"name\": \"num_open_po\", \"type\": \"integer\"},\n", "    {\"name\": \"schedules\", \"type\": \"varchar(65000)\"},\n", "    {\"name\": \"expiries\", \"type\": \"varchar(65000)\"},\n", "    {\"name\": \"open_po_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"manufacturer_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"po_cycle_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"tat_days\", \"type\": \"integer\"},\n", "    {\"name\": \"pending_putaway\", \"type\": \"integer\"},\n", "    {\"name\": \"putaway_in_progress\", \"type\": \"integer\"},\n", "    {\"name\": \"sto_id\", \"type\": \"varchar(65000)\"},\n", "    {\"name\": \"in_transit\", \"type\": \"integer\"},\n", "    {\"name\": \"last_30_days_grn_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"last_30_days_po_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"last_7_days_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"last_14_days_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"last_30_days_qty\", \"type\": \"bigint\"},\n", "    {\"name\": \"last_7_days_gmv\", \"type\": \"float\"},\n", "    {\"name\": \"last_14_days_gmv\", \"type\": \"float\"},\n", "    {\"name\": \"last_30_days_gmv\", \"type\": \"float\"},\n", "    {\"name\": \"next_7_days\", \"type\": \"float\"},\n", "    {\"name\": \"next_30_days\", \"type\": \"float\"},\n", "    {\"name\": \"previous_forecast_7_days\", \"type\": \"float\"},\n", "    {\"name\": \"captured_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"outer_case_size\", \"type\": \"integer\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"integer\"},\n", "    {\"name\": \"storage_factor\", \"type\": \"float\"},\n", "    {\"name\": \"unreliable\", \"type\": \"integer\"},\n", "    {\"name\": \"unorderable\", \"type\": \"integer\"},\n", "    {\"name\": \"case_sensitivity\", \"type\": \"varchar\"},\n", "    {\"name\": \"last_15_days_grn_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"last_15_days_po_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"last_30_days_del_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"last_30_days_del_gmv\", \"type\": \"float\"},\n", "    {\"name\": \"inner_case_size\", \"type\": \"integer\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "ko5qK2PvqMh8"}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"inventory_health\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"captured_at\"],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"append\",\n", "}\n", "pb.to_redshift(df, **kwargs)\n", "\n", "logging.warning(\"\\n|| Data written to the table... ||\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "d18u5wMVqMh_"}, "outputs": [], "source": ["df2 = df.loc[\n", "    df[\"facility_id\"].isin(\n", "        [\n", "            13,\n", "            52,\n", "            116,\n", "            145,\n", "            3,\n", "            29,\n", "            133,\n", "            22,\n", "            24,\n", "            69,\n", "            34,\n", "            149,\n", "            39,\n", "            140,\n", "            142,\n", "            32,\n", "            48,\n", "            12,\n", "            43,\n", "            161,\n", "            63,\n", "            132,\n", "            1,\n", "            26,\n", "            141,\n", "            147,\n", "            15,\n", "            30,\n", "            144,\n", "            143,\n", "            49,\n", "            125,\n", "            152,\n", "            139,\n", "            64,\n", "            92,\n", "            51,\n", "            47,\n", "            42,\n", "            33,\n", "            150,\n", "            135,\n", "        ]\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "y__5SVe6qMiM"}, "outputs": [], "source": ["ld = datetime.now().strftime(\"%Y-%m-%d\")\n", "filename = \"inv_health_\" + ld + \".csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "BPF8QmNAqMiP"}, "outputs": [], "source": ["df2.to_csv(filename, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "E7pM7Lb-qMiW"}, "outputs": [], "source": ["import boto3\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "\n", "\n", "def create_presigned_url(file_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "3gLVjiASqMiZ"}, "outputs": [], "source": ["sheet_id = \"1VBcmA-8XkeYpVNn6BlOttzWLklAJYuBE8tDzDP7sE8I\"\n", "email_id = pb.from_sheets(sheet_id, \"email\")\n", "\n", "cloud_filepath = \"pencilbox/shubham/\" + filename\n", "pb.to_s3(filename, bucket_name, cloud_filepath)\n", "file_name_link = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 24 * 7\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "2uPAaW8jqMic"}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = list(email_id[\"email\"])\n", "# [\"<EMAIL>\"]\n", "subject = \"Inventory Health \" + ld\n", "html_content = (\n", "    \"\"\"Hi Team,<br/><br/>\n", "                    <p> Please find the link to Inventory health below. <br/>\n", "                         Kindly go through the same and let me know in case of any query.<br/>\n", "                             <br/> <br/><b> link: </b><br/></p>\"\"\"\n", "    + file_name_link\n", "    + \"\"\"\n", "                            <p style=\"color:red\"><br/><br/> Note: Above link will be expire in next 7 days<br/></p>\n", "                            <p><br/> <br/>\"\"\"\n", ")\n", "\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "D5ZqNw2zqMig"}, "outputs": [], "source": ["import os\n", "\n", "os.remove(filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "5uLGrzuVqMij"}, "outputs": [], "source": ["df = pd.DataFrame()\n", "df2 = pd.DataFrame()\n", "\n", "\n", "dfrm = [df, df2]\n", "\n", "\n", "del df, df2\n", "del dfrm\n", "\n", "import gc\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "notebook.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}