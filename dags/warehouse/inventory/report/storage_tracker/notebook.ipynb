{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "import time\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletList = pb.from_sheets(\n", "    sheetid=\"1rE-dh2LD-5WXa9xE8PaSrosDe2vL-TiA6-u5qfmTovg\", sheetname=\"outlets_input\"\n", ")[\"OutletID\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletList = list(map(int, OutletList))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for outlet_id in tqdm(set(OutletList)):\n", "\n", "Query = f\"\"\"SELECT DISTINCT x1.outlet_id,\n", "       x1.outlet,\n", "       x1.item_id,\n", "       x1.name,\n", "       x1.Storage_Type,\n", "       x1.Item_Type,\n", "       x1.handling_type,\n", "       icd.l0_id,\n", "       icd.l1_id,\n", "       icd.l2_id,\n", "       description,\n", "       avg(x1.weight_in_kgs) as weight_in_kgs,\n", "       sum(x1.ios) AS \"On-Shelf\",\n", "       sum(x1.ppi) AS \"Pending_PutAway\",\n", "       avg(blocked.blocked_quantity) AS blocked,\n", "       sum(x1.ti) AS \"Total Inventory\",\n", "       sum(x1.ti*x1.weight_in_kgs) as \"Total Weight\"\n", "FROM\n", "  (SELECT inv.outlet_id,\n", "          outlet.name AS outlet,\n", "          product.item_id,\n", "          inv.variant_id,\n", "          id.name,\n", "          product.variant_uom_text as description,\n", "          product.handling_type,\n", "          (CASE\n", "               WHEN product.outlet_type = 1 THEN 'F&V type'\n", "               WHEN product.outlet_type = 2 THEN 'Grocery type'\n", "               ELSE 'Not Assigned'\n", "           END) AS Item_Type,\n", "          (CASE\n", "               WHEN cast(product.storage_type as integer) IN (1,8) THEN 'Regular'\n", "               WHEN cast(product.storage_type as integer) IN (2,6) THEN 'Cold'\n", "               WHEN cast(product.storage_type as integer) = 4 THEN 'Large'\n", "               WHEN cast(product.storage_type as integer) = 5 THEN 'Heavy'\n", "               WHEN cast(product.storage_type as integer) IN (3,7) THEN 'Frozen'\n", "               ELSE 'Regular'\n", "           END) AS \"Storage_Type\",\n", "          inv.quantity ios,\n", "          coalesce(good_inv1.quantity,0) AS ppi,\n", "          inv.quantity + coalesce(good_inv1.quantity,0) AS ti,\n", "          product.weight_in_gm*1.0/1000 as weight_in_kgs\n", "   FROM lake_ims.ims_inventory inv\n", "   LEFT JOIN\n", "     (SELECT good_inv.outlet_id,\n", "             good_inv.variant_id,\n", "             good_inv.quantity\n", "      FROM lake_ims.ims_good_inventory good_inv\n", "      WHERE \n", "      good_inv.inventory_update_type_id = 28) good_inv1 ON inv.variant_id = good_inv1.variant_id\n", "   AND inv.outlet_id = good_inv1.outlet_id\n", "   INNER JOIN lake_retail.console_outlet outlet ON outlet.id=inv.outlet_id\n", "   INNER JOIN lake_rpc.product_product product ON product.variant_id=inv.variant_id\n", "   INNER JOIN lake_rpc.item_details id ON id.item_id=product.item_id\n", "   WHERE \n", "     inv.active=1\n", "     AND inv.quantity + coalesce(good_inv1.quantity,0) > 0) x1\n", "LEFT JOIN\n", "  (SELECT iod.outlet,\n", "          item_id,\n", "          sum(quantity) AS blocked_quantity\n", "   FROM lake_ims.ims_order_details iod\n", "   INNER JOIN lake_ims.ims_order_items ioi ON iod.id=ioi.order_details_id\n", "   WHERE iod.status_id=1\n", "     AND ioi.billed_at_pos=0\n", "   GROUP BY 1,\n", "            2) blocked ON x1.item_id = blocked.item_id and x1.outlet_id=blocked.outlet\n", "LEFT JOIN lake_rpc.item_category_details icd on icd.item_id = x1.item_id \n", "WHERE x1.ti > 0\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11\"\"\"\n", "Data = pd.read_sql_query(sql=Query, con=presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DataList = set(Data[\"outlet_id\"].astype(int).values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = Data[Data.outlet_id.isin(OutletList)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"Total Weight\"] = Data[\"Total Weight\"].fillna(0).astype(float).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data2 = Data[Data[\"item_type\"] == \"F&V type\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MissingList = list(set(OutletList) - DataList)\n", "<PERSON><PERSON>(MissingList)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Filter = Data[\"item_type\"] == \"Grocery type\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = Data[Filter]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Regular : 1<br>\n", "Heavy : Same as description :- conversion in kgs<br>\n", "Frozen : 1<br>\n", "Large : 1.5 :- SKUs that have their handling type as Large<br>\n", "Cold : Cold : Corresponding value from the table after conversion of description in grams<br>\n", "\n", "<table align=\"left\">\n", "<tbody>\n", "<tr>\n", "<th>Description Bucket</th>\n", "<th>Factor</th>\n", "</tr>\n", "<tr>\n", "<td>&lt;50</td>\n", "<td>0.2</td>\n", "</tr>\n", "<tr>\n", "<td>51-100</td>\n", "<td>0.4</td>\n", "</tr>\n", "<tr>\n", "<td>101-150</td>\n", "<td>0.6</td>\n", "</tr>\n", "<tr>\n", "<td>151-200</td>\n", "<td>0.8</td>\n", "</tr>\n", "<tr>\n", "<td>201-250</td>\n", "<td>1</td>\n", "</tr>\n", "<tr>\n", "<td>251-300</td>\n", "<td>1.2</td>\n", "</tr>\n", "<tr>\n", "<td>301-350</td>\n", "<td>1.4</td>\n", "</tr>\n", "<tr>\n", "<td>351-400</td>\n", "<td>1.6</td>\n", "</tr>\n", "<tr>\n", "<td>401-450</td>\n", "<td>1.8</td>\n", "</tr>\n", "<tr>\n", "<td>451-500</td>\n", "<td>2</td>\n", "</tr>\n", "<tr>\n", "<td>501-550</td>\n", "<td>2.2</td>\n", "</tr>\n", "<tr>\n", "<td>551-600</td>\n", "<td>2.4</td>\n", "</tr>\n", "<tr>\n", "<td>601-650</td>\n", "<td>2.6</td>\n", "</tr>\n", "<tr>\n", "<td>651-700</td>\n", "<td>2.8</td>\n", "</tr>\n", "<tr>\n", "<td>701-750</td>\n", "<td>3</td>\n", "</tr>\n", "<tr>\n", "<td>751-800</td>\n", "<td>3.2</td>\n", "</tr>\n", "<tr>\n", "<td>801-850</td>\n", "<td>3.4</td>\n", "</tr>\n", "<tr>\n", "<td>851-900</td>\n", "<td>3.6</td>\n", "</tr>\n", "<tr>\n", "<td>901-950</td>\n", "<td>3.8</td>\n", "</tr>\n", "<tr>\n", "<td>951-1000</td>\n", "<td>4</td>\n", "</tr>\n", "<tr>\n", "<td>&gt;1001</td>\n", "<td>5</td>\n", "</tr>\n", "</tbody>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cold_item_factor(num):\n", "    pos = math.ceil((num - 50) / 50) + 1\n", "    return 5 if pos > 20 else pos * 0.2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"weight_in_kgs\"] = Data[\"weight_in_kgs\"].fillna(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def deduce_factor(r):\n", "    description = r[\"description\"].lower()\n", "    weight = float(r[\"weight_in_kgs\"])\n", "    if r[\"l2_id\"] == 2039:\n", "        return 0\n", "    if r[\"handling_type\"] == \"4\":\n", "        return weight\n", "    elif r[\"storage_type\"] == \"Heavy\":\n", "        return weight\n", "        # # special cases\n", "        # if description == \"15 kg (16.48 l)\":\n", "        #     return 15\n", "        # elif \"kg\" in description or \"l\" in description:\n", "        #     factor = eval(\n", "        #         description.replace(\"kg\", \"\")\n", "        #         .replace(\"l\", \"\")\n", "        #         .replace(\"x\", \"*\")\n", "        #         .replace(\"g\", \"/1000\")\n", "        #     )\n", "        #     return 1 if factor > 25 else factor\n", "        # elif \"g\" in description:\n", "        #     # check if kgs is in g\n", "        #     return (\n", "        #         1\n", "        #         if eval(description.replace(\"g\", \"\")) < 1000\n", "        #         else eval(description.replace(\"g\", \"\")) / 1000\n", "        #     )\n", "        # else:\n", "        #     # description in units\n", "        #     return 1\n", "    elif r[\"storage_type\"] == \"Cold\":\n", "        # special cases\n", "        if description in (\"5 units\", \"5 pieces\", \"390 g (6 servings)\"):\n", "            #             return cold_item_factor(400)\n", "            return 1.6\n", "        elif description == \"2+1 unit\":\n", "            #             return cold_item_factor(285)\n", "            return 1.2\n", "        elif description in (\"1 unit\", \"80 g - rs 90\", \"100 g (10 x 10 g)\"):\n", "            return 0.4\n", "        elif \"kg\" in description:\n", "            return cold_item_factor(eval(description.replace(\"kg\", \"\")) * 1000)\n", "        elif \"gm\" in description or \"g\" in description or \"ml\" in description:\n", "            return cold_item_factor(\n", "                eval(\n", "                    description.replace(\"gm\", \"\")\n", "                    .replace(\"g\", \"\")\n", "                    .replace(\"ml\", \"\")\n", "                    .replace(\"x\", \"*\")\n", "                )\n", "            )\n", "        elif \"l\" in description:\n", "            return cold_item_factor(eval(description.replace(\"l\", \"\")) * 1000)\n", "        elif \"pack\" in description:\n", "            return 1\n", "        else:\n", "            return cold_item_factor(eval(description))\n", "\n", "    else:\n", "        return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# heavy = Data[(Data['storage_type'] == 'Heavy') & (Data['handling_type'] != '4')]\n", "# heavy.description.unique()\n", "\n", "# heavy['factor'] = heavy.apply(deduce_factor, axis='columns')\n", "# heavy.groupby(['item_id']).factor.nunique().value_counts()\n", "\n", "# cold = Data[(Data['storage_type'] == 'Cold') & (Data['handling_type'] != '4')]\n", "# cold.description.unique()\n", "\n", "# cold['factor'] = cold.apply(deduce_factor, axis='columns')\n", "# cold.groupby(['item_id']).factor.nunique().value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_factors_to_redshift(Data):\n", "    Data[\"factor\"] = Data.apply(deduce_factor, axis=\"columns\")\n", "    item_factors = Data.groupby(\"item_id\").agg({\"factor\": \"mean\"}).reset_index()\n", "    print(item_factors.shape[0])\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"storage_item_factors\",\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"item_id\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Item id\",\n", "            },\n", "            {\n", "                \"name\": \"factor\",\n", "                \"type\": \"float\",\n", "                \"description\": \"Item factor\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"item_id\"],\n", "        \"sortkey\": [\"item_id\"],\n", "        \"incremental_key\": \"item_id\",\n", "        \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores item factors for storage tracker\",\n", "    }\n", "    pb.to_redshift(item_factors, **kwargs)\n", "    return item_factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_factors = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "if datetime.now(tz).hour == 0:\n", "    item_factors = load_factors_to_redshift(Data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if item_factors.empty:\n", "    sql = \"select * from metrics.storage_item_factors\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    item_factors = pd.read_sql_query(sql=sql, con=con)\n", "    print(\"reading from redshift\")\n", "else:\n", "    Data = Data.drop([\"factor\"], axis=\"columns\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = Data.drop([\"description\", \"handling_type\"], axis=\"columns\")\n", "Data = Data.drop_duplicates()\n", "Data[\"blocked\"] = Data[\"blocked\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = (\n", "    Data.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"item_id\",\n", "            \"name\",\n", "            \"storage_type\",\n", "            \"item_type\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"On-Shelf\": \"sum\",\n", "            \"Pending_PutAway\": \"sum\",\n", "            \"blocked\": \"mean\",\n", "            \"Total Inventory\": \"sum\",\n", "            \"Total Weight\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data2 = (\n", "    Data2.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"item_id\",\n", "            \"name\",\n", "            \"storage_type\",\n", "            \"item_type\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"On-Shelf\": \"sum\",\n", "            \"Pending_PutAway\": \"sum\",\n", "            \"blocked\": \"mean\",\n", "            \"Total Inventory\": \"sum\",\n", "            \"Total Weight\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"item_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data2[\"item_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = Data.merge(item_factors, how=\"left\", on=\"item_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data2[\"factor\"] = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = pd.concat([Data, Data2], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet in MissingList:\n", "    Data = Data.append(\n", "        {\"outlet_id\": int(outlet), \"outlet\": \"New Outlet\", \"item_type\": \"Grocery type\"},\n", "        ignore_index=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"Current_Inventory_with_Factor\"] = Data[\"On-Shelf\"] * Data[\"factor\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"blocked_with_Factor\"] = Data[\"blocked\"] * Data[\"factor\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"Total Weight\"] = Data[\"Total Weight\"].astype(float).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data[\"Key\"] = Data[\"outlet_id\"].astype(str) + \"|\" + Data[\"item_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = datetime.now(tz)\n", "Data[\"updated_at\"] = str(today)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {}\n", "\n", "\n", "def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "column_dtypes = redshift_schema(Data)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"storage_raw_data\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"Key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Table for Storage Utilization Related Tracker\",\n", "}\n", "\n", "pb.to_redshift(Data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}