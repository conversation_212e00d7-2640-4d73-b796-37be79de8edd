{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from gspread.exceptions import APIError\n", "from datetime import datetime, date\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Expiry Tasks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if datetime.now(tz).hour == 8:\n", "#     # Expiry Tasks\n", "#     query_raw_data = \"\"\"\n", "#     SELECT\n", "#         outlet.facility_id,\n", "#         outlet.name as \"outlet_name\",\n", "#         outlet.id as \"outlet_id\",\n", "#         p.item_id as \"item_id\",\n", "#         p.upc as \"UPC\",\n", "#         il.variant_id as \"variant_id\",\n", "#         p.name as \"product_name\",\n", "#         p.variant_uom_text as \"description\",\n", "#         p.variant_mrp as \"mrp\",\n", "#         lp.landing_price as \"landing_price\",\n", "#         pb.name as \"brand_name\",\n", "#         sum(il.quantity) as \"quantity\",\n", "#         il.batch as \"expiry_date\",\n", "#         il.location_id,\n", "#         wsl.location_name,\n", "#         datediff(day,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(day,-p.outward_guidelines,il.batch)) as \"NTE_in_days\",\n", "#         datediff(day,CURRENT_DATE,il.batch) as \"days_to_expire\",\n", "#         case when p.outlet_type=1 then 'FnV' when  p.outlet_type=2 then 'Groc.' else 'other' end as item_type,\n", "#         p.shelf_life as product_shelf_life,\n", "#         (datediff(day,current_date,il.batch)::float/p.shelf_life)*100 as \"perc_shelf_life_remaining\"\n", "#     FROM lake_warehouse_location.warehouse_item_location il\n", "#     INNER JOIN lake_warehouse_location.warehouse_outlet_mapping m on m.warehouse_id = il.warehouse_id\n", "#     INNER JOIN lake_rpc.product_product p ON p.variant_id= il.variant_id\n", "#     INNER JOIN lake_retail.console_outlet outlet on outlet.id= m.outlet_id\n", "#     INNER JOIN lake_warehouse_location.warehouse_storage_location wsl on il.location_id=wsl.id\n", "#     INNER JOIN lake_ims.ims_inventory_landing_price lp ON lp.variant_id= il.variant_id AND lp.outlet_id=m.outlet_id\n", "#     INNER JOIN lake_rpc.product_brand pb on pb.id=p.brand_id\n", "#     WHERE outlet.business_type_id in (1)\n", "#     and il.batch <= (dateadd(day,120,current_date)) and p.active=1 and p.skip_expiry=0\n", "#     and days_to_expire = 7\n", "#     and il.quantity > 0\n", "#     GROUP BY 1,2,3,4,5,6,7,8,9,10,11,13,14,15,16,17,18,19,20\n", "#     ORDER BY outlet.name,datediff(day,il.batch,CURRENT_DATE) asc\n", "#     \"\"\"\n", "#     expiry = pd.read_sql_query(query_raw_data, redshift)\n", "#     expiry1 = expiry[expiry[\"facility_id\"].isin([264, 3, 92, 517])]\n", "#     data1 = expiry1.merge(rtv_items, how=\"left\", on=[\"facility_id\", \"item_id\"])\n", "#     data1[\"rtv_flag\"] = data1.rtv_flag.fillna(0).replace({1: \"RTV\", 0: \"Expiry\"})\n", "#     data1[\"remark\"] = np.where(\n", "#         data1[\"rtv_flag\"] == \"RTV\",\n", "#         \"Check expiry date. Item is about to expire (RTV Eligible)\",\n", "#         \"Check expiry date. Item is about to expire\",\n", "#     )\n", "#     data_final = data1[\n", "#         [\"item_id\", \"variant_id\", \"outlet_id\", \"remark\"]\n", "#     ].drop_duplicates()\n", "#     data_final[\"creation_source\"] = 3\n", "#     final = data_final[\n", "#         [\"item_id\", \"variant_id\", \"outlet_id\", \"creation_source\", \"remark\"]\n", "#     ]\n", "#     data1.to_csv(\"Expiry_Audit_tasks.csv\")\n", "#     json_records = final.to_dict(orient=\"records\")\n", "#     today = date.today()\n", "#     import json\n", "#     import requests\n", "\n", "#     batch_size = 1\n", "#     for index in range(0, len(json_records), batch_size):\n", "#         json_records_batch = json_records[index : index + batch_size]\n", "#         url = \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/audit-task-create/\"\n", "#         payload = json.dumps(json_records_batch[0])\n", "#         headers = {\"Content-Type\": \"application/json\"}\n", "#         response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "#         # print(response.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Adherence_mailer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if datetime.now(tz).hour == 8:\n", "    presto = pb.get_connection(\"[Warehouse] Presto\")\n", "    Audit_adherence = pd.read_sql_query(\n", "        \"\"\"SELECT c.facility_id as facility_id, f.name as facility_name, \n", "    cast(x.created_at as date) as audit_date,\n", "    count(distinct case when (creation_source = 3) and (state != 5) then x.id else null end) as Total_nte_tasks,\n", "    count(distinct case when (creation_source = 3) and (state = 3) then x.id else null end) as Total_completed_nte_tasks,\n", "    (case when count(case when (creation_source = 3) and (state != 5) then x.id else null end) != 0 then\n", "    count(distinct case when (state = 3) and (creation_source = 3) then x.id else null end)*100/count(case when (creation_source = 3) and (state != 5) then x.id else null end) \n", "    else null end) as Audit_adherence_perc\n", "    FROM lake_view_warehouse_location.audit_task x\n", "    JOIN lake_view_retail.console_outlet c ON c.id = x.outlet_id AND c.device_id <> 47 AND c.active = 1 AND business_type_id <> 7\n", "    JOIN lake_crates.facility f on f.id = c.facility_id\n", "    left join (select outlet_id, variant_id, sum(transaction_lp*delta) as value, sum(delta) as qty \n", "    from lake_view_ims.ims_inventory_log \n", "    where cast(insert_ds_ist AS DATE) = cast(current_date as date) and inventory_update_type_id in (65)\n", "    group by 1,2) prn on prn.outlet_id = x.outlet_id and prn.variant_id = x.variant_id\n", "    WHERE c.facility_id not IN  (3,15,19,22,24,42,48,1766) and x.creation_source = 3 and remark like '%%Near%%'\n", "    AND cast(x.created_at as timestamp) BETWEEN  \n", "    (cast(current_timestamp as timestamp) - INTERVAL '49' HOUR) AND\n", "    cast(current_timestamp as timestamp)\n", "    GROUP BY 1,2,3\n", "    ORDER BY 1,3\"\"\",\n", "        presto,\n", "    )\n", "    PRNMarked = pd.read_sql_query(\n", "        \"\"\"SELECT\n", "    co.facility_id,\n", "    f.name as facility_name,\n", "    (pi.pos_timestamp + interval '5.5 hrs')::date as PRN_Date,\n", "    pp.item_id,\n", "    id.name as item_name,\n", "    iiut.name AS \"PRN Bucket\",\n", "    sum(iil.qty) as \"PRN Quantity\",\n", "    sum(iil.transaction_lp*iil.qty) as \"PRN value\"\n", "    from lake_pos.pos_invoice pi\n", "    INNER JOIN lake_pos.pos_invoice_product_details pip ON pi.id = pip.invoice_id\n", "    INNER JOIN lake_rpc.product_product pp ON pp.variant_id = pip.variant_id\n", "    INNER JOIN lake_rpc.item_details id on id.item_id = pp.item_id\n", "    INNER JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id and co.business_type_id <> 7 and device_id <> 47 and co.active = 1\n", "    INNER JOIN lake_crates.facility f on f.id = co.facility_id\n", "    JOIN \n", "    (\n", "    select invoice_id, variant_id, inventory_update_type_id, transaction_lp,\"delta\" as qty from lake_ims.ims_inventory_log \n", "    where (pos_timestamp + interval '5.5 hrs')::date BETWEEN current_date-15 and current_date and inventory_update_type_id in (65) \n", "    ) iil on iil.invoice_id = pi.invoice_id and iil.variant_id = pip.variant_id\n", "    LEFT JOIN  lake_ims.ims_inventory_update_type  iiut ON iiut.id = iil.inventory_update_type_id \n", "    WHERE co.facility_id not IN  (3,15,19,22,24,42,48,1766) and pi.invoice_type_id = 6 AND  \n", "    (pi.pos_timestamp + interval '5.5 hrs')::date = current_date-1\n", "    group by 1,2,3,4,5,6\n", "    order by 3\"\"\",\n", "        redshift,\n", "    )\n", "    PRNMarked.to_csv(\"PRN_Marked_\" + str(date.today()) + \".csv\")\n", "    prn_summary = (\n", "        PRNMarked.groupby([\"facility_id\", \"facility_name\", \"prn_date\"])\n", "        .agg({\"item_id\": \"count\", \"prn quantity\": sum, \"prn value\": sum})\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"item_id\": \"prn skus\",\n", "            }\n", "        )\n", "    )\n", "    audit_task_raw_data = pd.read_sql_query(\n", "        \"\"\"SELECT c.facility_id AS facility_id,\n", "       f.name AS facility_name,\n", "       x.id as audit_task_id,\n", "       x.remark,\n", "       (case \n", "            when x.state = 3 then 'Completed'\n", "            when x.state = 2 then 'In Progress'\n", "            when x.state = 1 then 'Created'\n", "            when x.state = 4 then 'Expired'\n", "            when x.state = 5 then 'Discarded'\n", "        end) as audit_task_state,\n", "        cast(x.created_at + interval '5' HOUR + interval '30' MINUTE AS date) as audit_task_date,\n", "        x.variant_id,\n", "        pp.name as variant_name,\n", "        pp.shelf_life,\n", "        x.start_ims_quantity,\n", "        x.end_ims_quantity,\n", "        x.start_ils_quantity,\n", "        x.end_ils_quantity\n", "        FROM lake_view_warehouse_location.audit_task x\n", "        JOIN lake_view_retail.console_outlet c ON c.id = x.outlet_id AND c.device_id <> 47 AND c.active = 1 AND business_type_id <> 7\n", "        JOIN lake_crates.facility f ON f.id = c.facility_id\n", "        JOIN lake_rpc.product_product pp on pp.variant_id = x.variant_id \n", "        WHERE c.facility_id not IN  (3,15,19,22,24,42,48,1766) and x.creation_source = 3\n", "          AND remark LIKE '%%Near%%'\n", "          AND cast(x.created_at as timestamp) BETWEEN  \n", "          (cast(current_timestamp as timestamp) - INTERVAL '48' HOUR) AND\n", "          cast(current_timestamp as timestamp)\n", "        order by 1,5\"\"\",\n", "        presto,\n", "    )\n", "    audit_task_raw_data.to_csv(\n", "        \"Audit_tasks_status(last 48 hours)_\" + str(date.today()) + \".csv\"\n", "    )\n", "    pb.send_email(\n", "        from_email=\"<EMAIL>\",\n", "        to_email=[\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"dev.b<PERSON><EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ],\n", "        subject=\"Near Expiry Audit Tasks Adherence for Last 48 Hours - \"\n", "        + str(date.today()),\n", "        html_content=\"\"\"Hi,<br> <br> \"\"\"\n", "        + \"\"\" Please find the Audit Adherence for NTE Audit tasks created in the last 48 hours in following table <br> <br>\"\"\"\n", "        + Audit_adherence.to_html(index=False)\n", "        .replace(\"<td>\", '<td align=\"center\">')\n", "        .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "        + \"\"\" <br> <br> FC Wise PRN Marked (for Near Expiry) Summary for yesterday <br> <br>\"\"\"\n", "        + prn_summary.to_html(index=False)\n", "        .replace(\"<td>\", '<td align=\"center\">')\n", "        .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "        + \"\"\"<br>Thanks,<br>Warehouse data team.\"\"\",\n", "        files=[\n", "            \"PRN_Marked_\" + str(date.today()) + \".csv\",\n", "            \"Audit_tasks_status(last 48 hours)_\" + str(date.today()) + \".csv\",\n", "        ],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["if datetime.now(tz).hour == 8:\n", "    # Near Expiry Tasks\n", "    query_raw_data = \"\"\"\n", "    SELECT \n", "        outlet.facility_id,\n", "        outlet.name as \"outlet_name\",\n", "        outlet.id as \"outlet_id\",\n", "        p.item_id as \"item_id\",\n", "        p.upc as \"UPC\",\n", "        il.variant_id as \"variant_id\",\n", "        p.name as \"product_name\",\n", "        p.variant_uom_text as \"description\",\n", "        p.variant_mrp as \"mrp\",\n", "        lp.landing_price as \"landing_price\",\n", "        pb.name as \"brand_name\",\n", "        sum(il.quantity) as \"quantity\",\n", "        il.batch as \"expiry_date\",\n", "        il.location_id,\n", "        wsl.location_name,\n", "        datediff(DAY,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(DAY,-p1.outward_guidelines,il.batch)) as \"NTE_in_days\",\n", "        datediff(day,CURRENT_DATE,il.batch) as \"days_to_expire\",\n", "        case when p.outlet_type=1 then 'FnV' when  p.outlet_type=2 then 'Groc.' else 'other' end as item_type,\n", "        p.shelf_life as product_shelf_life,\n", "        (datediff(day,current_date,il.batch)::float/p.shelf_life)*100 as \"perc_shelf_life_remaining\",\n", "        (case when il.batch > current_date then 'near_exp' else 'expired' end) as task_type\n", "    FROM lake_warehouse_location.warehouse_item_location il\n", "    INNER JOIN lake_warehouse_location.warehouse_outlet_mapping m on m.warehouse_id = il.warehouse_id \n", "    INNER JOIN lake_rpc.product_product p ON p.variant_id= il.variant_id\n", "    INNER JOIN\n", "  (SELECT variant_id,\n", "          (CASE\n", "               WHEN shelf_life <= 10 THEN ceil(0.33*shelf_life + 0.25)\n", "               WHEN shelf_life > 10\n", "                    AND shelf_life < 180 THEN ceil(0.4*shelf_life)\n", "               WHEN shelf_life >= 180\n", "                    AND shelf_life < 360 THEN 70\n", "               WHEN shelf_life >= 360 THEN 80\n", "               ELSE 80\n", "           END)::int AS outward_guidelines\n", "   FROM lake_rpc.product_product) p1 ON p1.variant_id = il.variant_id\n", "    INNER JOIN lake_retail.console_outlet outlet on outlet.id= m.outlet_id\n", "    INNER JOIN lake_warehouse_location.warehouse_storage_location wsl on il.location_id=wsl.id\n", "    INNER JOIN lake_ims.ims_inventory_landing_price lp ON lp.variant_id= il.variant_id AND lp.outlet_id=m.outlet_id\n", "    INNER JOIN lake_rpc.product_brand pb on pb.id=p.brand_id\n", "    WHERE outlet.facility_id not IN  (3,15,19,22,24,42,48,1766) and outlet.business_type_id in (1,12)\n", "    and il.batch <= (dateadd(day,120,current_date)) and p.active=1 and p.skip_expiry=0\n", "    and outlet.id <> 451\n", "    and ((nte_in_days <= 3 and product_shelf_life > 20)\n", "    or (nte_in_days <= 1 and product_shelf_life <= 20))\n", "    and il.quantity > 0\n", "    and days_to_expire > 0\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,13,14,15,16,17,18,19,20\n", "    ORDER BY outlet.name,datediff(day,il.batch,CURRENT_DATE) asc\n", "    \"\"\"\n", "    data = pd.read_sql_query(query_raw_data, pb.get_connection(\"[Warehouse] Redshift\"))\n", "    rtv_items = pd.read_sql_query(\n", "        \"select item_id, facility_id, 1 as rtv_flag from metrics.rtv_elg_inputs\",\n", "        redshift,\n", "    )\n", "    data1 = data.merge(rtv_items, how=\"left\", on=[\"facility_id\", \"item_id\"])\n", "    data1[\"rtv_flag\"] = data1.rtv_flag.fillna(0).replace({1: \"RTV\", 0: \"Expiry\"})\n", "    data1[\"remark\"] = np.where(\n", "        data1[\"rtv_flag\"] == \"RTV\", \"Near Expiry (RTV Eligible)\", \"Near Expiry\"\n", "    )\n", "    # data1[\"remark\"] = np.where(\n", "    #     (data1[\"rtv_flag\"] == \"RTV\") & (data1['task_type']== \"near_exp\"),\n", "    #     \"Near To Expiry (RTV Eligible)\",\n", "    #      np.where((data1[\"rtv_flag\"] != \"RTV\") & (data1['task_type']== \"near_exp\"), \"Near To Expiry\",\n", "    #               np.where((data1[\"rtv_flag\"] == \"RTV\") & (data1['task_type']== \"expired\"), \"Expired (RTV Eligible)\",\n", "    #                        \"Expired\")))\n", "    d1 = (\n", "        data1.groupby([\"outlet_id\", \"variant_id\", \"item_id\", \"remark\"])[\"location_name\"]\n", "        .apply(lambda x: \", \".join(x))\n", "        .reset_index()\n", "    )\n", "    data_final = d1[\n", "        [\"item_id\", \"variant_id\", \"outlet_id\", \"location_name\", \"remark\"]\n", "    ].drop_duplicates()\n", "    data_final[\"remark\"] = data_final[\"remark\"] + \" - \" + data_final[\"location_name\"]\n", "    data_final.drop(columns=[\"location_name\"], inplace=True)\n", "    data_final[\"creation_source\"] = 3\n", "    final = data_final[\n", "        [\"item_id\", \"variant_id\", \"outlet_id\", \"creation_source\", \"remark\"]\n", "    ]\n", "    print(\"Total NTE Tasks: \", final.shape[0])\n", "    json_records = final.to_dict(orient=\"records\")\n", "    today = date.today()\n", "    import json\n", "    import requests\n", "\n", "    batch_size = 1\n", "    for index in range(0, len(json_records), batch_size):\n", "        json_records_batch = json_records[index : index + batch_size]\n", "        url = \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/audit-task-create/\"\n", "        payload = json.dumps(json_records_batch[0])\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "        response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "        if response.status_code != 200:\n", "            print(response.text)\n", "            response = requests.request(\"POST\", url, headers=headers, data=payload)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GSC Audits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins = pb.from_sheets(\"1NMayfbmxR-ehA1IytHja2hMwJ9tPKjYa_8-T5knVSzI\", \"Coins\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if datetime.now(tz).hour == 8:\n", "    # Coin Tasks\n", "    coins = pd.read_sql_query(\n", "        f\"\"\"\n", "            with inv as (select c.id as outlet_id, f.name as facility_name, pp.variant_id, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as quantity\n", "            from lake_view_ims.ims_inventory i \n", "            join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "            and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "            join lake_rpc.item_details id on id.item_id = pp.item_id \n", "            Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1\n", "            and c.business_type_id in (1,12)\n", "            join lake_crates.facility f on f.id = c.facility_id\n", "            join lake_retail.console_location l on l.id = c.tax_location_id\n", "            and quantity > 0 \n", "            where i.active = 1\n", "            group by 1,2,3,4,5,6),\n", "            good_inv as (select c.id as outlet_id, f.name as facility_name, pp.variant_id, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as good_qty\n", "            from lake_view_ims.ims_good_inventory i \n", "            join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "            and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "            join lake_rpc.item_details id on id.item_id = pp.item_id \n", "            Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1 and c.business_type_id in (1,12)\n", "            join lake_crates.facility f on f.id = c.facility_id\n", "            join lake_retail.console_location l on l.id = c.tax_location_id\n", "            and quantity > 0 \n", "            where i.active = 1\n", "            group by 1,2,3,4,5,6)\n", "            select \n", "                coalesce(inv.city, good_inv.city) as city,\n", "                coalesce(inv.outlet_id, good_inv.outlet_id) as outlet_id,\n", "                coalesce(inv.facility_name, good_inv.facility_name) as facility_name,\n", "                coalesce(inv.variant_id, good_inv.variant_id) as variant_id,\n", "                coalesce(inv.item_id, good_inv.item_id) as item_id,\n", "                coalesce(inv.item_name, good_inv.item_name) as item_name,\n", "                coalesce(inv.quantity,0) as on_shelf_inventory,\n", "                coalesce(good_inv.good_qty,0) as good_inventory\n", "            from inv\n", "            full outer join good_inv on good_inv.outlet_id = inv.outlet_id and inv.variant_id = good_inv.variant_id\n", "            \"\"\",\n", "        presto,\n", "    )\n", "    coins = coins[(coins[\"on_shelf_inventory\"] + coins[\"good_inventory\"]) > 0]\n", "    coins[\"creation_source\"] = 3\n", "    coins[\"remark\"] = \"Gold & Silver Coins Audit\"\n", "    xx = coins[[\"item_id\", \"variant_id\", \"outlet_id\", \"creation_source\", \"remark\"]]\n", "    print(\"Total Gold & Silver Audit Tasks: \", xx.shape[0])\n", "    json_records = xx.to_dict(orient=\"records\")\n", "    today = date.today()\n", "    import json\n", "    import requests\n", "\n", "    batch_size = 1\n", "    for index in range(0, len(json_records), batch_size):\n", "        json_records_batch = json_records[index : index + batch_size]\n", "        url = \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/audit-task-create/\"\n", "        payload = json.dumps(json_records_batch[0])\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "        response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "        if response.status_code != 200:\n", "            print(response.text)\n", "            response = requests.request(\"POST\", url, headers=headers, data=payload)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data.outlet_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# whl = pb.get_connection(\"[Replica] RDS Warehouse Location\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# audit_task_raw_data = pd.read_sql_query(\n", "#     \"\"\"SELECT\n", "#        x.outlet_id,\n", "#        x.id as audit_task_id,\n", "#        x.remark,\n", "#        (case\n", "#             when x.state = 3 then 'Completed'\n", "#             when x.state = 2 then 'In Progress'\n", "#             when x.state = 1 then 'Created'\n", "#             when x.state = 4 then 'Expired'\n", "#             when x.state = 5 then 'Discarded'\n", "#         end) as audit_task_state,\n", "#         date(convert_tz(x.created_at,\"+00:00\",\"+05:30\")) as audit_task_date,\n", "#         x.variant_id,\n", "#         x.start_ims_quantity,\n", "#         x.end_ims_quantity,\n", "#         x.start_ils_quantity,\n", "#         x.end_ils_quantity\n", "#         FROM warehouse_location.audit_task x\n", "#         WHERE x.creation_source = 3\n", "#           AND remark LIKE '%%Near%%'\n", "#           AND x.created_at between\n", "#           (now() - INTERVAL '48' HOUR) AND\n", "#           now()\n", "#         order by 1,5\"\"\",\n", "#     whl,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# audit_task_raw_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if datetime.now(tz).hour == 8:\n", "#     mail_summ = (\n", "#         data1.groupby([\"outlet_id\", \"outlet_name\"])\n", "#         .agg({\"variant_id\": \"nunique\", \"item_id\": \"nunique\", \"quantity\": sum})\n", "#         .reset_index()\n", "#     )\n", "\n", "#     mail_summ.shape\n", "#     data1.to_csv(\"NearExpiry_Audit_tasks_rawdata.csv\")\n", "#     data_rtv = data1[data1[\"rtv_flag\"] == \"RTV\"]\n", "#     data_rtv_summ = (\n", "#         data_rtv.groupby([\"outlet_id\", \"outlet_name\"])\n", "#         .agg({\"variant_id\": \"nunique\", \"item_id\": \"nunique\", \"quantity\": sum})\n", "#         .reset_index()\n", "#     )\n", "#     mail_summ1 = mail_summ.merge(\n", "#         data_rtv_summ, on=[\"outlet_id\", \"outlet_name\"], how=\"left\"\n", "#     )\n", "#     mail_summary = (\n", "#         mail_summ1.rename(\n", "#             columns={\n", "#                 \"variant_id_x\": \"Total Audit Tasks\",\n", "#                 \"item_id_x\": \"Total SKUs\",\n", "#                 \"quantity_x\": \"Total Qty\",\n", "#                 \"variant_id_y\": \"RTV Eligible Tasks\",\n", "#                 \"item_id_y\": \"RTV Eligible SKUs\",\n", "#                 \"quantity_y\": \"RTV Eligible Qty\",\n", "#             }\n", "#         )\n", "#         .reset_index(drop=True)\n", "#         .<PERSON>na(0)\n", "#     )\n", "#     mail_summary[\n", "#         [\n", "#             \"Total Audit Tasks\",\n", "#             \"Total SKUs\",\n", "#             \"Total Qty\",\n", "#             \"RTV Eligible Tasks\",\n", "#             \"RTV Eligible SKUs\",\n", "#             \"RTV Eligible Qty\",\n", "#         ]\n", "#     ] = mail_summary[\n", "#         [\n", "#             \"Total Audit Tasks\",\n", "#             \"Total SKUs\",\n", "#             \"Total Qty\",\n", "#             \"RTV Eligible Tasks\",\n", "#             \"RTV Eligible SKUs\",\n", "#             \"RTV Eligible Qty\",\n", "#         ]\n", "#     ].astype(\n", "#         int\n", "#     )\n", "#     print(mail_summary)\n", "#     pb.send_email(\n", "#         from_email=\"<EMAIL>\",\n", "#         to_email=[\n", "#             \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"shubhanka<PERSON>.<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\"\n", "#             # \"dev.b<PERSON><EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\"\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\"\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\",\n", "#             # \"<EMAIL>\"\n", "#         ],\n", "#         subject=\"Near Expiry Audit Tasks for \" + str(today),\n", "#         html_content=\"\"\"Hi,<br> <br> Near Expiry Audit tasks for \"\"\"\n", "#         + str(today)\n", "#         + \"\"\" have been sent to the facilities\"\"\"\n", "#         + \"\"\" <br> <br> Please find the Audit tasks summary for today in the following table <br> <br>\"\"\"\n", "#         + mail_summary.to_html(index=False)\n", "#         .replace(\"<td>\", '<td align=\"center\">')\n", "#         .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "#         + \"\"\"<br>Thanks,<br>Warehouse data team.\"\"\",\n", "#         files=[\"NearExpiry_Audit_tasks_rawdata.csv\"],\n", "#     )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}