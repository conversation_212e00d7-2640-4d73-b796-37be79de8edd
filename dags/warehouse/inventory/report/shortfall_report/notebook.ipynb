{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import pickle\n", "import psycopg2\n", "import pandas as pd\n", "import scipy\n", "import sklearn\n", "import numpy as np\n", "import pickle\n", "import logging\n", "\n", "# import boto\n", "# from boto.s3.key import Key\n", "import datetime\n", "import time\n", "from pytz import timezone\n", "import datetime\n", "from datetime import datetime, date, time, timedelta\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dags.warehouse.inventory.report.shortfall_report.sf_query import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1T4lK8JNRXQWMk5MrA4gw6_yPUVUqgFOoC5mgwd8gR9U\"\n", "sheet_name = \"backend-frontend mapping\"\n", "sheet_name2 = \"email id\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_sf = pb.from_sheets(sheet_id, sheet_name)  # import from g sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_list = pb.from_sheets(sheet_id, sheet_name2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_sf = outlets_sf.replace(r\"^\\s*$\", np.nan, regex=True)  # data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlets_df.dtypes correcting the data types.\n", "outlets_sf[\"facility_id - frontend\"] = (\n", "    outlets_sf[\"facility_id - frontend\"].astype(\"float\").astype(\"Int64\")\n", ")\n", "outlets_sf[\"facility - frontend\"] = outlets_sf[\"facility - frontend\"].astype(\"str\")\n", "\n", "outlets_sf[\"outlet_id - frontend\"] = outlets_sf[\"outlet_id - frontend\"].replace(\",\", \"\")\n", "\n", "outlets_sf[\"outlet_id - frontend\"] = (\n", "    outlets_sf[\"outlet_id - frontend\"].astype(\"float\").astype(\"Int64\")\n", ")\n", "outlets_sf[\"outlet - frontend\"] = outlets_sf[\"outlet - frontend\"].astype(\"str\")\n", "\n", "outlets_sf[\"facility_id - backend\"] = (\n", "    outlets_sf[\"facility_id - backend\"].astype(\"float\").astype(\"Int64\")\n", ")\n", "outlets_sf[\"facility - backend\"] = outlets_sf[\"facility - backend\"].astype(\"str\")\n", "\n", "\n", "outlets_sf[\"outlet_id - backend\"] = (\n", "    outlets_sf[\"outlet_id - backend\"].astype(\"float\").astype(\"Int64\")\n", ")\n", "outlets_sf[\"outlet - backend\"] = outlets_sf[\"outlet - backend\"].astype(\"str\")\n", "\n", "outlets_sf[\"To be included in shortfall report\"] = outlets_sf[\n", "    \"To be included in shortfall report\"\n", "].astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub_df = outlets_sf.dropna(subset=[\"outlet_id - backend\"])\n", "in_outlets = tuple(\n", "    outlets_sf[outlets_sf[\"To be included in shortfall report\"] == \"Yes\"][\n", "        \"outlet_id - frontend\"\n", "    ].values\n", ") + tuple(\n", "    sub_df[sub_df[\"To be included in shortfall report\"] == \"Yes\"][\n", "        \"outlet_id - backend\"\n", "    ].unique()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["red_con = pb.get_connection(\"redshift\")\n", "l_cats_df = pd.read_sql(l_cats, red_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# running queries\n", "retail_con = pb.get_connection(\"retail\")  # connection establised\n", "outlets_df = pd.read_sql_query(sql=run_outlets, con=retail_con)  # outlets information\n", "ciq_df = pd.read_sql_query(\n", "    sql=ciq.format(outlets=in_outlets), con=retail_con\n", ")  # current inventory info\n", "assort_df = pd.read_sql_query(sql=assort, con=retail_con)  # assortment status citywise\n", "po_df = pd.read_sql_query(sql=po_q, con=retail_con)  # details of the po raised\n", "\n", "\n", "forecast_df = pd.read_sql(forecast, red_con)  # forecasted sales plan for next 60 days\n", "item_df = pd.read_sql(item_info, red_con)  # item info , from inventory health"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_df = pd.read_sql_query(sql=sto_query, con=retail_con)  # sto detail"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_items = list(\n", "    l_cats_df.loc[l_cats_df[\"l0\"].isin([\"Vegetables & Fruits\", \"Fruits & Vegetables\"])][\n", "        \"item_id\"\n", "    ]\n", "    .astype(int)\n", "    .unique()\n", ")\n", "\n", "# list of items to be excluded"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_df = outlets_df.merge(ciq_df, how=\"inner\", left_on=\"outlet_id\", right_on=\"outlet_id\")\n", "# to get detail of item assortment on facility, outlet anf city level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ci_df_a = (\n", "    ci_df.groupby([\"city\", \"facility_id\", \"facility_name\", \"item_id\"])\n", "    .sum()[[\"current_inventory\", \"blocked_inventory\"]]\n", "    .reset_index()\n", ")\n", "\n", "# aggregate on city ,facility and item level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ia_df = ci_df_a.merge(\n", "    assort_df,\n", "    how=\"outer\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "\n", "# ia_df = ci_df_a.merge(assort_df,how = 'outer',left_on = ['city','item_id'],right_on = ['city','item_id'])\n", "##changes made for assortment level\n", "\n", "# status of assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ia_df[\"unblocked_inventory\"] = ia_df[\"current_inventory\"] - ia_df[\"blocked_inventory\"]\n", "# quantity for which shortfall is calculated"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_df = ia_df[\n", "    (ia_df[\"current_inventory\"] > 0) | (ia_df[\"assortment_status\"] == \"active\")\n", "]\n", "# where current inventory>0 or status is ctive"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_df = inv_df[inv_df[\"facility_name\"].notna()]\n", "# where facility name is not null (eg-test facilities)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# po_df[\"ID\"] = po_df[\"item_id\"].astype(str) + po_df[\"facility\"] + po_df[\"po_id\"].astype(str)\n", "# ids = po_df[\"ID\"]\n", "# po_df[ids.isin(ids[ids.duplicated()])].sort_values(by=\"ID\")\n", "# # .to_csv(\"dup.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_df = item_df.replace(r\"^\\s*$\", np.nan, regex=True)\n", "# data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["front_fac = list(\n", "    outlets_sf[outlets_sf[\"To be included in shortfall report\"] == \"Yes\"][\n", "        \"facility_id - frontend\"\n", "    ].unique()\n", ")\n", "back_fac = list(\n", "    outlets_sf[outlets_sf[\"To be included in shortfall report\"] == \"Yes\"][\n", "        \"facility_id - backend\"\n", "    ].unique()\n", ")\n", "\n", "# frontend and backend facility id where shortfall is being calculated"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["font_back = (\n", "    outlets_sf[outlets_sf[\"To be included in shortfall report\"] == \"Yes\"][\n", "        [\"facility - frontend\", \"facility - backend\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"facility - frontend\": \"facility_name\",\n", "            \"facility - backend\": \"feeder_name\",\n", "        }\n", "    )\n", ")\n", "\n", "# mapping of backend front end supply."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["font_back[\"facility_name\"] = font_back[\"facility_name\"].str.rstrip()\n", "font_back[\"feeder_name\"] = font_back[\"feeder_name\"].str.rstrip()\n", "\n", "# data clening"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Current inventory frontend\n", "inv_df[\"facility_id\"] = inv_df[\"facility_id\"].astype(\"float\").astype(\"Int64\")\n", "inv_df[\"facility_name\"] = inv_df[\"facility_name\"].str.rstrip()\n", "\n", "# data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["front_inv_df = inv_df.loc[\n", "    inv_df[\"facility_id\"].isin(front_fac)\n", "]  # frontend facilities from inv_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_current_inventory = front_inv_df[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"assortment_status\",\n", "        \"unblocked_inventory\",\n", "        \"facility_name\",\n", "    ]\n", "].merge(\n", "    item_df,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\", \"facility_name\"],\n", "    right_on=[\"facility_id\", \"item_id\", \"facility_name\"],\n", ")\n", "df_current_inventory = df_current_inventory.rename(\n", "    columns={\"unblocked_inventory\": \"current_inventory\"}\n", ")\n", "del df_current_inventory[\"facility_id\"]\n", "# basic details (bucket info, asp, vendor info etc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_current_inventory = df_current_inventory.replace(r\"^\\s*$\", np.nan, regex=True)\n", "df_current_inventory[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_current_inventory[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_current_inventory[\"is_gb\"].fillna(\"None\", inplace=True)\n", "\n", "# data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_current_inventory[\"asp\"].fillna(80.0, inplace=True)\n", "df_current_inventory[\"assortment_status\"].fillna(\"not-active\", inplace=True)\n", "\n", "# if no SP given ,its considered to be 80 (rare cases)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_current_inventory[\"feeder_inventory\"] = 0.0\n", "\n", "# feeder inventory column made"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_current_inventory = df_current_inventory.loc[\n", "    ~df_current_inventory[\"item_id\"].isin(ex_items)\n", "]\n", "\n", "# ex_items excluded"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ex_items\n", "df_current_inventory[\"current_inventory\"] = np.where(\n", "    df_current_inventory[\"current_inventory\"] < 0,\n", "    0,\n", "    df_current_inventory[\"current_inventory\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CI_cols = [\n", "    \"item_id\",\n", "    \"facility_name\",\n", "    \"item_name\",\n", "    \"assortment_status\",\n", "    \"variant_description\",\n", "    \"vendor_name\",\n", "    \"bucket_x\",\n", "    \"bucket_a_b\",\n", "    \"is_gb\",\n", "    \"asp\",\n", "    \"current_inventory\",\n", "    \"feeder_inventory\",\n", "]\n", "df_current_inventory = df_current_inventory[CI_cols]\n", "\n", "# columns arrangement sorted, removing unnecessary columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Current inventory backend\n", "back_inv_df = inv_df.loc[inv_df[\"facility_id\"].isin(back_fac)]\n", "back_inv_df = back_inv_df.rename(columns={\"facility_name\": \"feeder_name\"})\n", "\n", "# samne steps for backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["back_inv_df = back_inv_df.merge(\n", "    font_back, how=\"left\", left_on=[\"feeder_name\"], right_on=[\"feeder_name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del back_inv_df[\"facility_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory = back_inv_df[\n", "    [\n", "        \"item_id\",\n", "        \"feeder_name\",\n", "        \"facility_name\",\n", "        \"assortment_status\",\n", "        \"unblocked_inventory\",\n", "    ]\n", "].merge(\n", "    item_df,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"facility_name\"],\n", "    right_on=[\"item_id\", \"facility_name\"],\n", ")\n", "df_feeder_inventory = df_feeder_inventory.replace(r\"^\\s*$\", np.nan, regex=True)\n", "df_feeder_inventory = df_feeder_inventory.rename(\n", "    columns={\"unblocked_inventory\": \"current_inventory\"}\n", ")\n", "\n", "df_feeder_inventory[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_feeder_inventory[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_feeder_inventory[\"is_gb\"].fillna(\"None\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory[\"asp\"].fillna(80.0, inplace=True)\n", "df_feeder_inventory[\"assortment_status\"].fillna(\"not-active\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory[\"current_inventory\"] = np.where(\n", "    df_feeder_inventory[\"current_inventory\"] < 0,\n", "    0,\n", "    df_feeder_inventory[\"current_inventory\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory = df_feeder_inventory.loc[\n", "    ~df_feeder_inventory[\"item_id\"].isin(ex_items)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FCI_cols = [\n", "    \"item_id\",\n", "    \"facility_name\",\n", "    \"feeder_name\",\n", "    \"item_name\",\n", "    \"assortment_status\",\n", "    \"variant_description\",\n", "    \"vendor_name\",\n", "    \"bucket_x\",\n", "    \"bucket_a_b\",\n", "    \"is_gb\",\n", "    \"asp\",\n", "    \"current_inventory\",\n", "]\n", "df_feeder_inventory = df_feeder_inventory[FCI_cols]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_feeder_inventory[df_feeder_inventory['item_id'] == 10064134]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_open_po\n", "# df_cpd['Item IDFacility Name']\n", "# df_current_inventory['Item IDFacility Name']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_feeder_inventory\n", "\n", "\n", "# all above steps are same for backend as for frontend diif - (backend inventory - feeder inventory)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_df[\"facility_name\"] = po_df[\"facility_name\"].str.rstrip()  # data correction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["front_po_df = po_df.loc[po_df[\"facility_id\"].isin(front_fac)]\n", "\n", "# only frontend facilities taken"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po = front_po_df.merge(\n", "    item_df[\n", "        [\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"variant_description\",\n", "            \"bucket_x\",\n", "            \"bucket_a_b\",\n", "            \"is_gb\",\n", "            \"landing_price\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "del df_open_po[\"facility_id\"]\n", "# item_df merged and facility id deleted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po = df_open_po.loc[~df_open_po[\"item_id\"].isin(ex_items)]\n", "\n", "# fruit veg items deleted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Open P<PERSON> Feeder\n", "back_po_df = po_df.loc[po_df[\"facility_id\"].isin(back_fac)]\n", "back_po_df[\"schedule_date\"] = back_po_df[\"schedule_date\"] + timed<PERSON>ta(days=4)\n", "\n", "# schedule days added with 4 days because its an assumption that it will take 4 dys for item to move from feeder to frontend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sto_df_b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PO_cols = [\n", "    \"item_id\",\n", "    \"outlet_id\",\n", "    \"facility_name\",\n", "    \"variant_description\",\n", "    \"vendor_name\",\n", "    \"bucket_x\",\n", "    \"bucket_a_b\",\n", "    \"is_gb\",\n", "    \"po_id\",\n", "    \"schedule_date\",\n", "    \"expiry_date\",\n", "    \"ordered_quantity\",\n", "]\n", "df_open_po = df_open_po[PO_cols]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_open_po[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_open_po[\"is_gb\"].fillna(\"None\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = back_po_df.merge(\n", "    item_df[\n", "        [\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"variant_description\",\n", "            \"bucket_x\",\n", "            \"bucket_a_b\",\n", "            \"is_gb\",\n", "            \"landing_price\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "del df_open_po_feeder[\"facility_id\"]\n", "\n", "df_open_po_feeder[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_open_po_feeder[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_open_po_feeder[\"is_gb\"].fillna(\"None\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = df_open_po_feeder.rename(columns={\"facility_name\": \"feeder_name\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = df_open_po_feeder.merge(\n", "    font_back, how=\"left\", left_on=[\"feeder_name\"], right_on=[\"feeder_name\"]\n", ")\n", "del df_open_po_feeder[\"feeder_name\"]\n", "\n", "\n", "# same above as front end facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_sto = sto_df.merge(\n", "    item_df[\n", "        [\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"variant_description\",\n", "            \"bucket_x\",\n", "            \"bucket_a_b\",\n", "            \"is_gb\",\n", "            \"landing_price\",\n", "        ]\n", "    ],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "\n", "# sto informtions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_sto_a = df_sto.loc[df_sto[\"facility_id\"].isin(front_fac)]\n", "del df_sto_a[\"facility_id\"]\n", "\n", "df_sto_a[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_sto_a[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_sto_a[\"is_gb\"].fillna(\"None\", inplace=True)\n", "\n", "# frontend facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = pd.concat(\n", "    [df_open_po_feeder, df_sto_a[list(df_open_po_feeder.columns)]], ignore_index=True\n", ")\n", "# sto added to open po feeder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = df_open_po_feeder[PO_cols]\n", "# [df_open_po_feeder['schedule_date'].isnull()]\n", "# column orders set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder = df_open_po_feeder.loc[~df_open_po_feeder[\"item_id\"].isin(ex_items)]\n", "# items removed which are not required in shortfall"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fore_df = front_inv_df[[\"item_id\", \"facility_id\", \"facility_name\"]].merge(\n", "    forecast_df,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "fore_df[\"facility_name\"] = fore_df[\"facility_name\"].str.rstrip()\n", "\n", "# forecasted cpd taken"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cpd = fore_df.merge(\n", "    item_df[[\"item_id\", \"facility_id\", \"bucket_x\", \"bucket_a_b\", \"is_gb\", \"asp\"]],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "del df_cpd[\"facility_id\"]\n", "\n", "df_cpd[\"bucket_x\"].fillna(\"None\", inplace=True)\n", "df_cpd[\"bucket_a_b\"].fillna(\"None\", inplace=True)\n", "df_cpd[\"is_gb\"].fillna(\"None\", inplace=True)\n", "\n", "# item basic info joined, and data cleaned."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cpd.fillna(0.0, inplace=True)\n", "\n", "# Na<PERSON> replaced with 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cpd = df_cpd.loc[~df_cpd[\"item_id\"].isin(ex_items)]\n", "\n", "# items removed whose shortfall not required"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# excel_date(df_open_po_feeder.loc[1][-2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x_df = df_cpd.copy()\n", "# y_df = df_current_inventory.copy()\n", "\n", "# x = list(x_df['item_id'].astype(str) + x_df['facility_name'] + x_df['bucket_a_b'] + x_df['bucket_x'])\n", "# y = list(y_df['item_id'].astype(str) + y_df['facility_name'] + y_df['bucket_a_b'] + y_df['bucket_x'])\n", "\n", "# len(list(set(x) - set(y)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_df[(item_df[\"item_id\"]==10042687)&(item_df[\"facility_name\"]==\"SSC Bulk Mumbai M2 - Warehouse\")]\n", "# df_cpd[(df_cpd[\"item_id\"]==10042687)&(df_cpd[\"facility_name\"]==\"SSC Bulk Mumbai M2 - Warehouse\")]\n", "# df_current_inventory[(df_current_inventory[\"item_id\"]==10042687)&(df_current_inventory[\"facility_name\"]==\"SSC Bulk Mumbai M2 - Warehouse\")]\n", "# df_cpd\n", "# datetime(1899, 12, 30)\n", "# excel_date(\"2020-07-28\")\n", "# fore_df\n", "# df_cpd.loc[df_cpd['facility_name'].isnull()]\n", "# df_current_inventory.loc[df_current_inventory['facility_name'].isnull()]\n", "\n", "# 10042687SSC Bulk Mumbai M2 - WarehouseBNone\n", "# 10042687SSC Bulk Mumbai M2 - WarehouseBNone"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def excel_date(date1):\n", "    temp = date(1899, 12, 30)\n", "    delta = date1 - temp\n", "    return int(float(delta.days) + (float(delta.seconds) / 86400))\n", "\n", "\n", "# date time converted to epoc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def xldate_to_datetime(xldate):\n", "    temp = date(1899, 12, 30)\n", "    delta = timedelta(days=xldate)\n", "    timestampStr = (temp + delta).strftime(\"%d/%m/%Y\")\n", "    return timestampStr\n", "\n", "\n", "# epoc converted to date time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def item_fac_info(current_inventory):\n", "\n", "    df_item_fac = current_inventory[\n", "        [\n", "            \"item_id\",\n", "            \"facility_name\",\n", "            \"item_name\",\n", "            \"variant_description\",\n", "            \"vendor_name\",\n", "            \"bucket_x\",\n", "            \"bucket_a_b\",\n", "            \"is_gb\",\n", "            \"assortment_status\",\n", "            \"asp\",\n", "        ]\n", "    ]\n", "    df_item_fac[\"Item IDFacility Name\"] = (\n", "        df_item_fac[\"item_id\"].astype(str)\n", "        + df_item_fac[\"facility_name\"]\n", "        + df_item_fac[\"bucket_a_b\"]\n", "        + df_item_fac[\"bucket_x\"]\n", "    )\n", "    df_item_fac.set_index(\"Item IDFacility Name\", inplace=True, drop=True)\n", "\n", "    return df_item_fac\n", "\n", "\n", "# unique row created concatinating item id and facility name (key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def po_transform(open_po_df_in):\n", "\n", "    all_po_df = open_po_df_in.copy()\n", "\n", "    open_po_df_in[\"Item IDFacility Name\"] = (\n", "        open_po_df_in[\"item_id\"].astype(str)\n", "        + open_po_df_in[\"facility_name\"]\n", "        + open_po_df_in[\"bucket_a_b\"]\n", "        + open_po_df_in[\"bucket_x\"]\n", "    )\n", "    open_po_df_in = open_po_df_in[\n", "        [\"Item IDFacility Name\", \"schedule_date\", \"expiry_date\", \"ordered_quantity\"]\n", "    ]\n", "\n", "    open_po_df_in = open_po_df_in[open_po_df_in[\"Item IDFacility Name\"].notna()]\n", "\n", "    open_po_base = pd.DataFrame()\n", "\n", "    for i in range(0, len(open_po_df_in)):\n", "        df1 = pd.DataFrame()\n", "        open_po_qty = {}\n", "\n", "        open_po_qty[\"Item IDFacility Name\"] = open_po_df_in.iloc[i][0]\n", "\n", "        if pd.isna(open_po_df_in.iloc[i][1]) == False:\n", "            open_po_qty[excel_date(open_po_df_in.iloc[i][1])] = open_po_df_in.iloc[i][3]\n", "        #             open_po_qty[open_po_df_in.iloc[i][1]] = open_po_df_in.iloc[i][3]\n", "        #         else:\n", "        #             open_po_qty[excel_date(open_po_df_in.iloc[i][2])] = open_po_df_in.iloc[i][3]\n", "        #             open_po_qty[open_po_df_in.iloc[i][2]] = open_po_df_in.iloc[i][3]\n", "\n", "        df1 = pd.DataFrame(\n", "            open_po_qty,\n", "            index=[\n", "                i,\n", "            ],\n", "        )\n", "        open_po_base = pd.concat([open_po_base, df1], ignore_index=True)\n", "\n", "    all_po_df[\"Item IDFacility Name\"] = (\n", "        all_po_df[\"item_id\"].astype(str)\n", "        + all_po_df[\"facility_name\"]\n", "        + all_po_df[\"bucket_a_b\"]\n", "        + all_po_df[\"bucket_x\"]\n", "    )\n", "    all_po_df = (\n", "        all_po_df.groupby([\"Item IDFacility Name\"])\n", "        .sum()[[\"ordered_quantity\"]]\n", "        .rename(columns={\"ordered_quantity\": \"po_qty\"})\n", "    )\n", "\n", "    return (open_po_base, all_po_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_ci_w(df_current_inventory_in):\n", "    df_current_inventory_in[\"Item IDFacility Name\"] = (\n", "        df_current_inventory_in[\"item_id\"].astype(str)\n", "        + df_current_inventory_in[\"facility_name\"]\n", "        + df_current_inventory_in[\"bucket_a_b\"]\n", "        + df_current_inventory_in[\"bucket_x\"]\n", "    )\n", "    df_current_inventory_in = df_current_inventory_in[\n", "        [\"Item IDFacility Name\", \"current_inventory\", \"feeder_inventory\"]\n", "    ]\n", "    df_current_inventory_in.set_index(\"Item IDFacility Name\", inplace=True, drop=True)\n", "    df_current_inventory_in_dt = df_current_inventory_in.rename(\n", "        columns={df_current_inventory_in.columns[0]: excel_date(date.today())}\n", "    )\n", "\n", "    return (df_current_inventory_in, df_current_inventory_in_dt)\n", "\n", "\n", "# current inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def open_po_n_days_w(open_po_df_in, df_current_inventory_in, n):\n", "\n", "    # open_po_df_in\n", "    # excel_date(date.today())\n", "\n", "    up_cols = []\n", "    for vals in list(open_po_df_in.columns):\n", "        if (\n", "            (type(vals) == int)\n", "            and (excel_date(date.today()) <= vals)\n", "            and (excel_date(date.today()) + n > vals)\n", "        ):\n", "            #     if (type(vals) == float) and (excel_date(date.date.today()-1)<=vals):\n", "            up_cols.append(vals)\n", "    up_cols.sort()\n", "\n", "    open_po_df_in = open_po_df_in[[\"Item IDFacility Name\"] + up_cols]\n", "    open_po_df_in.set_index(\"Item IDFacility Name\", inplace=True, drop=True)\n", "    open_po_df_in = open_po_df_in.groupby(open_po_df_in.index).sum()\n", "\n", "    dts = []\n", "    for i in range(n):\n", "        dts.append(excel_date(date.today()) + i)\n", "\n", "    open_po_df_in_dt = list(open_po_df_in.columns)\n", "    l4 = [x for x in dts if x not in open_po_df_in_dt]\n", "    for i in l4:\n", "        open_po_df_in[i] = 0.0\n", "\n", "    open_po_base_ = df_current_inventory_in.join(open_po_df_in)\n", "    open_po_base_ = open_po_base_.replace(np.nan, 0.0, regex=True)\n", "    open_po_base_ = open_po_base_[dts]\n", "    #     open_po_base_.drop(columns = ['current_inventory'])\n", "\n", "    return open_po_base_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_current_inventory_w[0]\n", "# df_cpd_w\n", "# type(open_po_trans.columns[3])\n", "# df_open_po_30_w\n", "# open_po_trans"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cpd_w(df_cpd_in):\n", "\n", "    df_cpd_in[\"Item IDFacility Name\"] = (\n", "        df_cpd_in[\"item_id\"].astype(str)\n", "        + df_cpd_in[\"facility_name\"]\n", "        + df_cpd_in[\"bucket_a_b\"]\n", "        + df_cpd_in[\"bucket_x\"]\n", "    )\n", "    df_cpd_in = df_cpd_in.drop(\n", "        [\"facility_name\", \"item_id\", \"bucket_x\", \"bucket_a_b\", \"asp\", \"is_gb\"], axis=1\n", "    )\n", "    df_cpd_in.set_index(\"Item IDFacility Name\", inplace=True, drop=True)\n", "    df_cpd_in = df_cpd_in.replace(np.nan, \"0.0\", regex=True)\n", "\n", "    cpd_cols = {}\n", "    cpd_cols_lst = []\n", "    for i in range(len(df_cpd_in.columns)):\n", "        cpd_cols[df_cpd_in.columns[i]] = excel_date(date.today()) + i\n", "        cpd_cols_lst.append(excel_date(date.today()) + i)\n", "\n", "    df_cpd_in = df_cpd_in.rename(columns=cpd_cols)\n", "\n", "    return df_cpd_in"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def start_end_date(df_dicts):\n", "    dts = list(df_dicts.keys())\n", "    start_date = 0.0\n", "    for i in range(len(dts)):\n", "        if df_dicts[dts[i]] != 0:\n", "            start_date = dts[i]\n", "            break\n", "\n", "    end_date = start_date\n", "\n", "    if start_date == 0.0:\n", "        start_date = \"no_shortfall\"\n", "        end_date = \"no_shortfall\"\n", "\n", "    else:\n", "        for j in range(dts.index(start_date), len(dts)):\n", "            end_date = end_date + 1\n", "            if df_dicts[dts[j]] == 0.0:\n", "                end_date = dts[j]\n", "                break\n", "        end_date = end_date - 1\n", "    return [start_date, end_date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def shortfall_calc(item_fac, df_current_inventory_w_in, df_cpd_w_in, open_po_base_w_in):\n", "\n", "    col_c = list(df_cpd_w_in.columns)\n", "    #     col_i = list(df_current_inventory_w_in.columns)\n", "    col_i = [\n", "        p for p in list(df_current_inventory_w_in.columns) if not isinstance(p, str)\n", "    ]\n", "\n", "    # inventory_without_po\n", "    inven_without_po = pd.DataFrame\n", "    inven_without_po = df_cpd_w_in[list(df_cpd_w_in.columns)].astype(float)\n", "\n", "    inven_without_po[col_i[0]] = df_current_inventory_w_in[col_i[0]].astype(float)\n", "\n", "    for i in range(1, len(col_c)):\n", "        if col_c[i] == (excel_date(date.today()) + 4):\n", "            #         print(\"run\")\n", "            inven_without_po[col_c[i]] = (\n", "                inven_without_po[col_c[i - 1]].astype(float)\n", "                - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                + df_current_inventory_w_in[\"feeder_inventory\"].astype(float)\n", "            )\n", "        else:\n", "            inven_without_po[col_c[i]] = inven_without_po[col_c[i - 1]].astype(\n", "                float\n", "            ) - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "    # we add the inventory items from feeder to frontend after 4 days, and before that, the actual quantity from frontend is considered\n", "\n", "    #     for i in range(len(col_c)):\n", "    #         for j in range(len(col_i)):\n", "    #             if col_c[i] == col_i[j]:\n", "    #                 inven_without_po[col_c[i]] = df_current_inventory_w_in[col_i[j]].astype(float)\n", "    # #             if col_c[i] == (excel_date(date.today()) + 4):\n", "    # #                 inven_without_po[col_c[i]] = inven_without_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float) + df_current_inventory_w_in['feeder_inventory'].astype(float)\n", "    #             else:\n", "    #                 inven_without_po[col_c[i]] = inven_without_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float)\n", "\n", "    df_inv = inven_without_po\n", "    df_inv[df_inv < 0] = 0\n", "\n", "    # inventory_with_po\n", "    inven_with_po = pd.DataFrame\n", "    inven_with_po = df_cpd_w_in[list(df_cpd_w_in.columns)].astype(float)\n", "\n", "    for i in range(len(col_c)):\n", "        for j in range(len(col_i)):\n", "            if col_c[i] == col_i[j]:\n", "                inven_with_po[col_c[i]] = df_current_inventory_w_in[col_i[j]].astype(\n", "                    float\n", "                )\n", "            break\n", "\n", "    for i in range(col_c.index(col_i[0]) + 1, len(col_c)):\n", "\n", "        if col_c[i - 1] in list(open_po_base_w_in.columns):\n", "            if col_c[i] == (excel_date(date.today()) + 4):\n", "                inven_with_po[col_c[i]] = np.where(\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + open_po_base_w_in[col_c[i - 1]].astype(float)\n", "                        + df_current_inventory_w_in[\"feeder_inventory\"].astype(float)\n", "                    )\n", "                    > 0.0,\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + open_po_base_w_in[col_c[i - 1]].astype(float)\n", "                        + df_current_inventory_w_in[\"feeder_inventory\"].astype(float)\n", "                    ),\n", "                    0.0,\n", "                )\n", "            else:\n", "                inven_with_po[col_c[i]] = np.where(\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + open_po_base_w_in[col_c[i - 1]].astype(float)\n", "                    )\n", "                    > 0.0,\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + open_po_base_w_in[col_c[i - 1]].astype(float)\n", "                    ),\n", "                    0.0,\n", "                )\n", "        else:\n", "            if col_c[i] == (excel_date(date.today()) + 4):\n", "                inven_with_po[col_c[i]] = np.where(\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + df_current_inventory_w_in[\"feeder_inventory\"].astype(float)\n", "                    )\n", "                    > 0.0,\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                        + df_current_inventory_w_in[\"feeder_inventory\"].astype(float)\n", "                    ),\n", "                    0.0,\n", "                )\n", "            else:\n", "                inven_with_po[col_c[i]] = np.where(\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                    )\n", "                    > 0.0,\n", "                    (\n", "                        inven_with_po[col_c[i - 1]].astype(float)\n", "                        - df_cpd_w_in[col_c[i - 1]].astype(float)\n", "                    ),\n", "                    0.0,\n", "                )\n", "\n", "    #     for i in range (col_c.index(col_i[0])+1,len(col_c)):\n", "    #         if col_c[i-1] in list(open_po_base_w_in.columns):\n", "    #             inven_with_po[col_c[i]] = np.where((inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float) + open_po_base_w_in[col_c[i-1]].astype(float))>0.0,(inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float) + open_po_base_w_in[col_c[i-1]].astype(float)),0.0)\n", "    # #         if col_c[i-1] in list(open_po_base_w_in.columns) and :\n", "    # #             inven_with_po[col_c[i]] = np.where((inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float) + open_po_base_w_in[col_c[i-1]].astype(float))>0.0,(inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float) + open_po_base_w_in[col_c[i-1]].astype(float)),0.0)\n", "    #         else:\n", "    #             inven_with_po[col_c[i]] = np.where((inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float))>0.0,(inven_with_po[col_c[i-1]].astype(float) - df_cpd_w_in[col_c[i-1]].astype(float)),0.0)\n", "\n", "    # shortfall_without_po\n", "    short_without_po = pd.DataFrame\n", "    short_without_po = df_cpd_w_in[list(df_cpd_w_in.columns)].astype(float)\n", "\n", "    for i in range(col_c.index(col_i[0]), len(col_c)):\n", "        short_without_po[col_c[i]] = df_cpd_w_in[col_c[i]].astype(\n", "            float\n", "        ) - inven_without_po[col_c[i]].astype(float)\n", "\n", "    df_short_without_po = short_without_po\n", "    df_short_without_po[df_short_without_po < 0] = 0\n", "\n", "    df_sf_2 = item_fac.join(short_without_po)\n", "    df_sf_2 = df_sf_2[df_sf_2[\"assortment_status\"] == \"not-active\"]\n", "    na_index = list(df_sf_2.index)\n", "\n", "    short_without_po.loc[na_index] = 0.0\n", "\n", "    # shortfall_with_po\n", "    short_with_po = pd.DataFrame\n", "    short_with_po = df_cpd_w_in[list(df_cpd_w_in.columns)].astype(float)\n", "\n", "    for i in range(col_c.index(col_i[0]), len(col_c)):\n", "        if col_c[i] in list(open_po_base_w_in.columns):\n", "            short_with_po[col_c[i]] = (\n", "                df_cpd_w_in[col_c[i]].astype(float) - inven_with_po[col_c[i]]\n", "            )\n", "        else:\n", "            short_with_po[col_c[i]] = (\n", "                df_cpd_w_in[col_c[i]].astype(float) - inven_with_po[col_c[i]]\n", "            )\n", "\n", "    df_short_with_po = short_with_po\n", "    df_short_with_po[df_short_with_po < 0] = 0\n", "    df_short_with_po.loc[na_index] = 0.0\n", "\n", "    return (df_inv, inven_with_po, df_short_without_po, df_short_with_po)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# int(excel_date(date.today()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def summary_cal(\n", "    item_fac,\n", "    ci_work,\n", "    cpd_work,\n", "    o_po_work,\n", "    inv_without_po,\n", "    inv_with_po,\n", "    shortfall_without_po,\n", "    shortfall_with_po,\n", "    open_po_all_w,\n", "):\n", "\n", "    open_po_all_w = open_po_all_w.replace(np.nan, 0.0, regex=True)\n", "    sf_cols = [\"Open PO week 1\", \"Open PO week 2\", \"Open PO week 3\", \"Open PO week 4\"]\n", "    print(\"cp-1\")\n", "    # df_item_wise\n", "    df_item_wise = pd.DataFrame(columns=sf_cols, index=cpd_work.index)\n", "    print(\"checkpoint-1\")\n", "    # Expected Sale\n", "    df_es_w1 = cpd_work.iloc[:, :].join(\n", "        cpd_work.loc[:, dt_range_1[0] : dt_range_1[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 1 Expected Sale\")\n", "    )[[\"week 1 Expected Sale\"]]\n", "    df_es_w2 = cpd_work.iloc[:, :].join(\n", "        cpd_work.loc[:, dt_range_2[0] : dt_range_2[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 2 Expected Sale\")\n", "    )[[\"week 2 Expected Sale\"]]\n", "    df_es_w3 = cpd_work.iloc[:, :].join(\n", "        cpd_work.loc[:, dt_range_3[0] : dt_range_3[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 3 Expected Sale\")\n", "    )[[\"week 3 Expected Sale\"]]\n", "    df_es_w4 = cpd_work.iloc[:, :].join(\n", "        cpd_work.loc[:, dt_range_4[0] : dt_range_4[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 4 Expected Sale\")\n", "    )[[\"week 4 Expected Sale\"]]\n", "    df_es_w5 = cpd_work.iloc[:, :].join(\n", "        cpd_work.loc[:, dt_range_5[0] : dt_range_5[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 5 Expected Sale\")\n", "    )[[\"week 5 Expected Sale\"]]\n", "    print(\"cp-2\")\n", "    # Shortfall (without open po)\n", "    df_sf1_w1 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, dt_range_1[0] : dt_range_1[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 1 Shortfall\")\n", "    )[[\"week 1 Shortfall\"]]\n", "    df_sf1_w2 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, dt_range_2[0] : dt_range_2[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 2 Shortfall\")\n", "    )[[\"week 2 Shortfall\"]]\n", "    df_sf1_w3 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, dt_range_3[0] : dt_range_3[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 3 Shortfall\")\n", "    )[[\"week 3 Shortfall\"]]\n", "    df_sf1_w4 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, dt_range_4[0] : dt_range_4[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 4 Shortfall\")\n", "    )[[\"week 4 Shortfall\"]]\n", "    df_sf1_w5 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, dt_range_5[0] : dt_range_5[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 5 Shortfall\")\n", "    )[[\"week 5 Shortfall\"]]\n", "\n", "    # Shortfall2(with open po)\n", "    df_sf2_w1 = shortfall_with_po.iloc[:, :].join(\n", "        shortfall_with_po.loc[:, dt_range_1[0] : dt_range_1[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 1 Shortfall 2\")\n", "    )[[\"week 1 Shortfall 2\"]]\n", "    df_sf2_w2 = shortfall_with_po.iloc[:, :].join(\n", "        shortfall_with_po.loc[:, dt_range_2[0] : dt_range_2[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 2 Shortfall 2\")\n", "    )[[\"week 2 Shortfall 2\"]]\n", "    df_sf2_w3 = shortfall_with_po.iloc[:, :].join(\n", "        shortfall_with_po.loc[:, dt_range_3[0] : dt_range_3[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 3 Shortfall 2\")\n", "    )[[\"week 3 Shortfall 2\"]]\n", "    df_sf2_w4 = shortfall_with_po.iloc[:, :].join(\n", "        shortfall_with_po.loc[:, dt_range_4[0] : dt_range_4[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 4 Shortfall 2\")\n", "    )[[\"week 4 Shortfall 2\"]]\n", "    df_sf2_w5 = shortfall_with_po.iloc[:, :].join(\n", "        shortfall_with_po.loc[:, dt_range_5[0] : dt_range_5[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 5 Shortfall 2\")\n", "    )[[\"week 5 Shortfall 2\"]]\n", "\n", "    o_po_work.columns = o_po_work.columns.astype(float)\n", "    print(\"checkpoint-2\")\n", "    # open_po\n", "    df_open_po_w1 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_1[0] : dt_range_1[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 1 open po\")\n", "    )[[\"week 1 open po\"]]\n", "    df_open_po_w2 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_2[0] : dt_range_2[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 2 open po\")\n", "    )[[\"week 2 open po\"]]\n", "    df_open_po_w3 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_3[0] : dt_range_3[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 3 open po\")\n", "    )[[\"week 3 open po\"]]\n", "    df_open_po_w4 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_4[0] : dt_range_4[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 4 open po\")\n", "    )[[\"week 4 open po\"]]\n", "    df_open_po_w5 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_5[0] : dt_range_5[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"week 5 open po\")\n", "    )[[\"week 5 open po\"]]\n", "\n", "    # open_po before SF\n", "    df_sf3_w1 = pd.DataFrame()\n", "    df_sf3_w2 = pd.DataFrame()\n", "    df_sf3_w3 = pd.DataFrame()\n", "    df_sf3_w4 = pd.DataFrame()\n", "    df_sf3_w5 = pd.DataFrame()\n", "    print(\"checkpoint-3\")\n", "    df_sf3_w1 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_1[0] :]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"open_po before sf week 1\")\n", "    )[[\"open_po before sf week 1\"]]\n", "    df_sf3_w2 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_2[0] :]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"open_po before sf week 2\")\n", "    )[[\"open_po before sf week 2\"]]\n", "    df_sf3_w3 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_3[0] :]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"open_po before sf week 3\")\n", "    )[[\"open_po before sf week 3\"]]\n", "    df_sf3_w4 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_4[0] :]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"open_po before sf week 4\")\n", "    )[[\"open_po before sf week 4\"]]\n", "    df_sf3_w5 = o_po_work.iloc[:, :].join(\n", "        o_po_work.loc[:, dt_range_5[0] :]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"open_po before sf week 5\")\n", "    )[[\"open_po before sf week 5\"]]\n", "\n", "    # open_po after SF\n", "    df_sf4_w1 = pd.DataFrame()\n", "    df_sf4_w2 = pd.DataFrame()\n", "    df_sf4_w3 = pd.DataFrame()\n", "    df_sf4_w4 = pd.DataFrame()\n", "    df_sf4_w5 = pd.DataFrame()\n", "\n", "    df_sf4_w1[\"open_po after sf week 1\"] = np.maximum(\n", "        (\n", "            np.minimum(\n", "                df_sf1_w1[\"week 1 Shortfall\"] - df_sf3_w1[\"open_po before sf week 1\"],\n", "                df_open_po_w2[\"week 2 open po\"],\n", "            )\n", "        ),\n", "        0,\n", "    )\n", "    df_sf4_w2[\"open_po after sf week 2\"] = np.maximum(\n", "        (\n", "            np.minimum(\n", "                df_sf1_w2[\"week 2 Shortfall\"] - df_sf3_w2[\"open_po before sf week 2\"],\n", "                df_open_po_w3[\"week 3 open po\"],\n", "            )\n", "        ),\n", "        0,\n", "    )\n", "    df_sf4_w3[\"open_po after sf week 3\"] = np.maximum(\n", "        (\n", "            np.minimum(\n", "                df_sf1_w3[\"week 3 Shortfall\"] - df_sf3_w3[\"open_po before sf week 3\"],\n", "                df_open_po_w4[\"week 4 open po\"],\n", "            )\n", "        ),\n", "        0,\n", "    )\n", "    df_sf4_w4[\"open_po after sf week 4\"] = np.maximum(\n", "        (\n", "            np.minimum(\n", "                df_sf1_w4[\"week 4 Shortfall\"] - df_sf3_w4[\"open_po before sf week 4\"],\n", "                df_open_po_w5[\"week 5 open po\"],\n", "            )\n", "        ),\n", "        0,\n", "    )\n", "    df_sf4_w5[\"open_po after sf week 5\"] = np.maximum(\n", "        (\n", "            np.minimum(\n", "                df_sf1_w5[\"week 5 Shortfall\"] - df_sf3_w5[\"open_po before sf week 5\"],\n", "                df_open_po_w5[\"week 5 open po\"],\n", "            )\n", "        ),\n", "        0,\n", "    )\n", "\n", "    print(\"cp-3\")\n", "    # calculations\n", "    dict_sed = {}\n", "    for i in range(len(shortfall_without_po)):\n", "        dict_sed[shortfall_without_po.iloc[i].name] = start_end_date(\n", "            shortfall_without_po.iloc[i].to_dict()\n", "        )\n", "\n", "    open_po_sed = pd.DataFrame.from_dict(\n", "        dict_sed, orient=\"index\", columns=[\"start_date\", \"end_date\"]\n", "    )\n", "\n", "    dict_posf = {}\n", "    for i in range(len(open_po_sed)):\n", "        if type(open_po_sed.iloc[i][0]) == int:\n", "            (\n", "                open_pobsf_w1,\n", "                open_pobaf_w1,\n", "                open_pobsf_w2,\n", "                open_pobaf_w2,\n", "                open_pobsf_w3,\n", "                open_pobaf_w3,\n", "                open_pobsf_w4,\n", "                open_pobaf_w4,\n", "                open_pobsf_w5,\n", "                open_pobaf_w5,\n", "            ) = (0, 0, 0, 0, 0, 0, 0, 0, 0, 0)\n", "\n", "            if open_po_sed.iloc[i][0] in range(dt_range_1[0], dt_range_1[1] + 1):\n", "                open_pobsf_w1 = o_po_work.iloc[\n", "                    i, 0 : o_po_work.columns.get_loc(open_po_sed.iloc[i][0])\n", "                ].sum()\n", "                open_pobaf_w1 = o_po_work.iloc[\n", "                    i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]) :\n", "                ].sum()\n", "\n", "            if open_po_sed.iloc[i][0] in range(dt_range_2[0], dt_range_2[1] + 1):\n", "                open_pobsf_w2 = o_po_work.iloc[\n", "                    i, 0 : o_po_work.columns.get_loc(open_po_sed.iloc[i][0])\n", "                ].sum()\n", "                open_pobaf_w2 = o_po_work.iloc[\n", "                    i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]) :\n", "                ].sum()\n", "\n", "            if open_po_sed.iloc[i][0] in range(dt_range_3[0], dt_range_3[1] + 1):\n", "                open_pobsf_w3 = o_po_work.iloc[\n", "                    i, 0 : o_po_work.columns.get_loc(open_po_sed.iloc[i][0])\n", "                ].sum()\n", "                open_pobaf_w3 = o_po_work.iloc[\n", "                    i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]) :\n", "                ].sum()\n", "\n", "            if open_po_sed.iloc[i][0] in range(dt_range_4[0], dt_range_4[1] + 1):\n", "                open_pobsf_w4 = o_po_work.iloc[\n", "                    i, 0 : o_po_work.columns.get_loc(open_po_sed.iloc[i][0])\n", "                ].sum()\n", "                open_pobaf_w4 = o_po_work.iloc[\n", "                    i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]) :\n", "                ].sum()\n", "\n", "            if open_po_sed.iloc[i][0] in range(dt_range_5[0], dt_range_5[1] + 1):\n", "                open_pobsf_w5 = o_po_work.iloc[\n", "                    i, 0 : o_po_work.columns.get_loc(open_po_sed.iloc[i][0])\n", "                ].sum()\n", "                open_pobaf_w5 = o_po_work.iloc[\n", "                    i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]) :\n", "                ].sum()\n", "\n", "            # open_pobsf = o_po_work.iloc[i, 0:o_po_work.columns.get_loc(open_po_sed.iloc[i][0])].sum()\n", "            # open_pobaf = o_po_work.iloc[i, o_po_work.columns.get_loc(open_po_sed.iloc[i][0]):].sum()\n", "            dict_posf[o_po_work.iloc[i].name] = [\n", "                open_po_sed.iloc[i][0],\n", "                open_pobsf_w1,\n", "                open_pobaf_w1,\n", "                open_pobsf_w2,\n", "                open_pobaf_w2,\n", "                open_pobsf_w3,\n", "                open_pobaf_w3,\n", "                open_pobsf_w4,\n", "                open_pobaf_w4,\n", "                open_pobsf_w5,\n", "                open_pobaf_w5,\n", "            ]\n", "\n", "        else:\n", "            dict_posf[o_po_work.iloc[i].name] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "\n", "    open_po_sf = pd.DataFrame.from_dict(\n", "        dict_posf,\n", "        orient=\"index\",\n", "        columns=[\n", "            \"start_date\",\n", "            \"open_po_before_sf_week_1\",\n", "            \"open_po_after_sf_week_1\",\n", "            \"open_po_before_sf_week_2\",\n", "            \"open_po_after_sf_week_2\",\n", "            \"open_po_before_sf_week_3\",\n", "            \"open_po_after_sf_week_3\",\n", "            \"open_po_before_sf_week_4\",\n", "            \"open_po_after_sf_week_4\",\n", "            \"open_po_before_sf_week_5\",\n", "            \"open_po_after_sf_week_5\",\n", "        ],\n", "    )\n", "\n", "    open_po_sf.index.name = \"Item IDFacility Name\"\n", "\n", "    print(\"cp-4\")\n", "    # open_po before and after SF\n", "    df_opsf_w1 = open_po_sf.iloc[:, 1:3]\n", "    df_opsf_w2 = open_po_sf.iloc[:, 3:5]\n", "    df_opsf_w3 = open_po_sf.iloc[:, 5:7]\n", "    df_opsf_w4 = open_po_sf.iloc[:, 7:9]\n", "    df_opsf_w5 = open_po_sf.iloc[:, 9:11]\n", "\n", "    print(\"cp-5\")\n", "    # SF with no open-po\n", "    df_sf5_w1 = pd.DataFrame(index=inv_without_po.index)\n", "    df_sf5_w2 = pd.DataFrame(index=inv_without_po.index)\n", "    df_sf5_w3 = pd.DataFrame(index=inv_without_po.index)\n", "    df_sf5_w4 = pd.DataFrame(index=inv_without_po.index)\n", "    df_sf5_w5 = pd.DataFrame(index=inv_without_po.index)\n", "\n", "    #     df_sf5_w1['SF with no open-po week 1'] = np.where(df_sf3_w1['open_po before sf week 1'] == 0,df_sf2_w1['week 1 Shortfall 2'],0)\n", "    #     df_sf5_w2['SF with no open-po week 2'] = np.where(df_sf3_w2['open_po before sf week 2'] == 0,df_sf2_w2['week 2 Shortfall 2'],0)\n", "    #     df_sf5_w3['SF with no open-po week 3'] = np.where(df_sf3_w3['open_po before sf week 3'] == 0,df_sf2_w3['week 3 Shortfall 2'],0)\n", "    #     df_sf5_w4['SF with no open-po week 4'] = np.where(df_sf3_w4['open_po before sf week 4'] == 0,df_sf2_w4['week 4 Shortfall 2'],0)\n", "    #     df_sf5_w5['SF with no open-po week 5'] = np.where(df_sf3_w5['open_po before sf week 5'] == 0,df_sf2_w5['week 5 Shortfall 2'],0)\n", "\n", "    df_sf1_tw1 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, : dt_range_1[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"till week 1 Shortfall\")\n", "    )[[\"till week 1 Shortfall\"]]\n", "    df_sf1_tw2 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, : dt_range_2[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"till week 2 Shortfall\")\n", "    )[[\"till week 2 Shortfall\"]]\n", "    df_sf1_tw3 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, : dt_range_3[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"till week 3 Shortfall\")\n", "    )[[\"till week 3 Shortfall\"]]\n", "    df_sf1_tw4 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, : dt_range_4[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"till week 4 Shortfall\")\n", "    )[[\"till week 4 Shortfall\"]]\n", "    df_sf1_tw5 = shortfall_without_po.iloc[:, :].join(\n", "        shortfall_without_po.loc[:, : dt_range_5[1]]\n", "        .astype(float)\n", "        .sum(axis=1)\n", "        .rename(\"till week 5 Shortfall\")\n", "    )[[\"till week 5 Shortfall\"]]\n", "    print(\"cp-6\")\n", "    df_sf1_tw1 = df_sf1_tw1.join(open_po_all_w).replace(np.nan, 0.0, regex=True)\n", "    df_sf1_tw2 = df_sf1_tw2.join(open_po_all_w).replace(np.nan, 0.0, regex=True)\n", "    df_sf1_tw3 = df_sf1_tw3.join(open_po_all_w).replace(np.nan, 0.0, regex=True)\n", "    df_sf1_tw4 = df_sf1_tw4.join(open_po_all_w).replace(np.nan, 0.0, regex=True)\n", "    df_sf1_tw5 = df_sf1_tw5.join(open_po_all_w).replace(np.nan, 0.0, regex=True)\n", "\n", "    df_sf5_w1[\"SF with no open-po week 1\"] = (\n", "        df_sf1_tw1[\"till week 1 Shortfall\"] - df_sf1_tw1[\"po_qty\"]\n", "    )\n", "    df_sf5_w2[\"SF with no open-po week 2\"] = (\n", "        df_sf1_tw2[\"till week 2 Shortfall\"] - df_sf1_tw2[\"po_qty\"]\n", "    )\n", "    df_sf5_w3[\"SF with no open-po week 3\"] = (\n", "        df_sf1_tw3[\"till week 3 Shortfall\"] - df_sf1_tw3[\"po_qty\"]\n", "    )\n", "    df_sf5_w4[\"SF with no open-po week 4\"] = (\n", "        df_sf1_tw4[\"till week 4 Shortfall\"] - df_sf1_tw4[\"po_qty\"]\n", "    )\n", "    df_sf5_w5[\"SF with no open-po week 5\"] = (\n", "        df_sf1_tw5[\"till week 5 Shortfall\"] - df_sf1_tw5[\"po_qty\"]\n", "    )\n", "\n", "    df_sf5_w1[df_sf5_w1 < 0] = 0\n", "    df_sf5_w2[df_sf5_w2 < 0] = 0\n", "    df_sf5_w3[df_sf5_w3 < 0] = 0\n", "    df_sf5_w4[df_sf5_w4 < 0] = 0\n", "    df_sf5_w5[df_sf5_w5 < 0] = 0\n", "    print(\"cp-7\")\n", "    df_sf5_w1[\"SF with no open-po week 1\"] = (\n", "        df_sf5_w1.join(df_sf1_w1).replace(np.nan, 0.0, regex=True).min(axis=\"columns\")\n", "    )\n", "    print(\"a\")\n", "    df_sf5_w2[\"SF with no open-po week 2\"] = (\n", "        df_sf5_w2.join(df_sf1_w2).replace(np.nan, 0.0, regex=True).min(axis=\"columns\")\n", "    )\n", "    print(\"b\")\n", "    df_sf5_w3[\"SF with no open-po week 3\"] = (\n", "        df_sf5_w3.join(df_sf1_w3).replace(np.nan, 0.0, regex=True).min(axis=\"columns\")\n", "    )\n", "    print(\"c\")\n", "    df_sf5_w4[\"SF with no open-po week 4\"] = (\n", "        df_sf5_w4.join(df_sf1_w4).replace(np.nan, 0.0, regex=True).min(axis=\"columns\")\n", "    )\n", "    print(\"d\")\n", "    df_sf5_w5[\"SF with no open-po week 5\"] = (\n", "        df_sf5_w5.join(df_sf1_w5).replace(np.nan, 0.0, regex=True).min(axis=\"columns\")\n", "    )\n", "    print(\"e\")\n", "    print(\"cp-7.1\")\n", "    # Availability without po\n", "    df_a_w1 = pd.DataFrame(index=inv_without_po.index)\n", "    df_a_w2 = pd.DataFrame(index=inv_without_po.index)\n", "    df_a_w3 = pd.DataFrame(index=inv_without_po.index)\n", "    df_a_w4 = pd.DataFrame(index=inv_without_po.index)\n", "    df_a_w5 = pd.DataFrame(index=inv_without_po.index)\n", "    print(\"cp-7.2\")\n", "    df_a_w1[\"Availability without open po week 1\"] = np.where(\n", "        inv_without_po[dt_range_1[1]] > 0, 1, 0\n", "    )\n", "    df_a_w2[\"Availability without open po week 2\"] = np.where(\n", "        inv_without_po[dt_range_2[1]] > 0, 1, 0\n", "    )\n", "    df_a_w3[\"Availability without open po week 3\"] = np.where(\n", "        inv_without_po[dt_range_3[1]] > 0, 1, 0\n", "    )\n", "    df_a_w4[\"Availability without open po week 4\"] = np.where(\n", "        inv_without_po[dt_range_4[1]] > 0, 1, 0\n", "    )\n", "    df_a_w5[\"Availability without open po week 5\"] = np.where(\n", "        inv_without_po[dt_range_5[1]] > 0, 1, 0\n", "    )\n", "\n", "    print(\"cp-7.3\")\n", "    # Availability with po\n", "    df_ao_w1 = pd.DataFrame(index=inv_with_po.index)\n", "    df_ao_w2 = pd.DataFrame(index=inv_with_po.index)\n", "    df_ao_w3 = pd.DataFrame(index=inv_with_po.index)\n", "    df_ao_w4 = pd.DataFrame(index=inv_with_po.index)\n", "    df_ao_w5 = pd.DataFrame(index=inv_with_po.index)\n", "    print(\"cp-7.4\")\n", "    df_ao_w1[\"Availability with open po week 1\"] = np.where(\n", "        inv_with_po[dt_range_1[1]] > 0, 1, 0\n", "    )\n", "    df_ao_w2[\"Availability with open po week 2\"] = np.where(\n", "        inv_with_po[dt_range_2[1]] > 0, 1, 0\n", "    )\n", "    df_ao_w3[\"Availability with open po week 3\"] = np.where(\n", "        inv_with_po[dt_range_3[1]] > 0, 1, 0\n", "    )\n", "    df_ao_w4[\"Availability with open po week 4\"] = np.where(\n", "        inv_with_po[dt_range_4[1]] > 0, 1, 0\n", "    )\n", "    df_ao_w5[\"Availability with open po week 5\"] = np.where(\n", "        inv_with_po[dt_range_5[1]] > 0, 1, 0\n", "    )\n", "    print(\"cp-8\")\n", "    # opening inventory without po\n", "    df_oi_wo_w1 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_wo_w2 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_wo_w3 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_wo_w4 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_wo_w5 = pd.DataFrame(index=inv_with_po.index)\n", "\n", "    df_oi_wo_w1[\"opening inventory without open po week 1\"] = inv_without_po[\n", "        dt_range_1[0]\n", "    ]\n", "    df_oi_wo_w2[\"opening inventory without open po week 2\"] = inv_without_po[\n", "        dt_range_2[0]\n", "    ]\n", "    df_oi_wo_w3[\"opening inventory without open po week 3\"] = inv_without_po[\n", "        dt_range_3[0]\n", "    ]\n", "    df_oi_wo_w4[\"opening inventory without open po week 4\"] = inv_without_po[\n", "        dt_range_4[0]\n", "    ]\n", "    df_oi_wo_w5[\"opening inventory without open po week 5\"] = inv_without_po[\n", "        dt_range_5[0]\n", "    ]\n", "\n", "    # opening inventory with po\n", "    df_oi_o_w1 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_o_w2 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_o_w3 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_o_w4 = pd.DataFrame(index=inv_with_po.index)\n", "    df_oi_o_w5 = pd.DataFrame(index=inv_with_po.index)\n", "    print(\"cp-9\")\n", "    df_oi_o_w1[\"opening inventory with open po week 1\"] = inv_with_po[dt_range_1[0]]\n", "    df_oi_o_w2[\"opening inventory with open po week 2\"] = inv_with_po[dt_range_2[0]]\n", "    df_oi_o_w3[\"opening inventory with open po week 3\"] = inv_with_po[dt_range_3[0]]\n", "    df_oi_o_w4[\"opening inventory with open po week 4\"] = inv_with_po[dt_range_4[0]]\n", "    df_oi_o_w5[\"opening inventory with open po week 5\"] = inv_with_po[dt_range_5[0]]\n", "\n", "    # weekwise concatination\n", "    df_w1 = (\n", "        df_es_w1.join(df_open_po_w1)\n", "        .join(df_sf1_w1)\n", "        .join(df_sf2_w1)\n", "        .join(df_opsf_w1)\n", "        .join(df_sf5_w1)\n", "        .join(df_a_w1)\n", "        .join(df_ao_w1)\n", "        .join(df_oi_wo_w1)\n", "        .join(df_oi_o_w1)\n", "    )\n", "    df_w2 = (\n", "        df_es_w2.join(df_open_po_w2)\n", "        .join(df_sf1_w2)\n", "        .join(df_sf2_w2)\n", "        .join(df_opsf_w2)\n", "        .join(df_sf5_w2)\n", "        .join(df_a_w2)\n", "        .join(df_ao_w2)\n", "        .join(df_oi_wo_w2)\n", "        .join(df_oi_o_w2)\n", "    )\n", "    df_w3 = (\n", "        df_es_w3.join(df_open_po_w3)\n", "        .join(df_sf1_w3)\n", "        .join(df_sf2_w3)\n", "        .join(df_opsf_w3)\n", "        .join(df_sf5_w3)\n", "        .join(df_a_w3)\n", "        .join(df_ao_w3)\n", "        .join(df_oi_wo_w3)\n", "        .join(df_oi_o_w3)\n", "    )\n", "    df_w4 = (\n", "        df_es_w4.join(df_open_po_w4)\n", "        .join(df_sf1_w4)\n", "        .join(df_sf2_w4)\n", "        .join(df_opsf_w4)\n", "        .join(df_sf5_w4)\n", "        .join(df_a_w4)\n", "        .join(df_ao_w4)\n", "        .join(df_oi_wo_w4)\n", "        .join(df_oi_o_w4)\n", "    )\n", "    df_w5 = (\n", "        df_es_w5.join(df_open_po_w5)\n", "        .join(df_sf1_w5)\n", "        .join(df_sf2_w5)\n", "        .join(df_opsf_w5)\n", "        .join(df_sf5_w5)\n", "        .join(df_a_w5)\n", "        .join(df_ao_w5)\n", "        .join(df_oi_wo_w5)\n", "        .join(df_oi_o_w5)\n", "    )\n", "    print(\"cp-10\")\n", "\n", "    df_v1 = df_w1.join(df_w2).join(df_w3).join(df_w4).join(df_w5)\n", "    df_v2 = item_fac.join(df_v1)\n", "    df_v2_asp = df_v2\n", "\n", "    df_v2_asp_col = list(df_v2.columns)\n", "    print(\"cp-11\")\n", "    for i in range(10, len(df_v2_asp_col)):\n", "        if i in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 28, 29, 39, 40, 50, 51, 61, 62]:\n", "            continue\n", "        else:\n", "            df_v2_asp[df_v2_asp_col[i]] = (\n", "                df_v2_asp[df_v2_asp_col[i]] * df_v2_asp[df_v2_asp_col[9]]\n", "            )\n", "\n", "    df_v2 = df_v2_asp\n", "\n", "    ####\n", "    df_bucket_x = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"bucket_x\"])\n", "        .sum()[list(df_v1.columns)]\n", "    )\n", "    df_bucket_a_b = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"bucket_a_b\"])\n", "        .sum()[list(df_v1.columns)]\n", "    )\n", "    df_bucket_gb = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"is_gb\"])\n", "        .sum()[list(df_v1.columns)]\n", "    )\n", "    #     df_overall = df_v2[df_v2['assortment_status'] == 'active'].groupby(['facility_name']).sum()[list(df_v1.columns)]\n", "    df_overall = df_v2.groupby([\"facility_name\"]).sum()[list(df_v1.columns)]\n", "    df_assmt = df_v2.groupby([\"facility_name\", \"assortment_status\"]).sum()[\n", "        list(df_v1.columns)\n", "    ]\n", "\n", "    df_v3 = pd.concat(\n", "        [df_overall, df_bucket_x, df_bucket_a_b, df_bucket_gb, df_assmt], sort=False\n", "    )\n", "\n", "    df_v3[\"Facility\"] = \"\"\n", "    df_v3[\"Bucket\"] = \"\"\n", "    v3_index = df_v3.index\n", "    for i in range(len(v3_index)):\n", "\n", "        if type(v3_index[i]) == tuple:\n", "            df_v3[\"Facility\"][i] = v3_index[i][0]\n", "\n", "            if v3_index[i][1] == \"X\":\n", "                df_v3[\"Bucket\"][i] = \"X\"\n", "\n", "            if v3_index[i][1] in (\"A\", \"B\"):\n", "                df_v3[\"Bucket\"][i] = v3_index[i][1]\n", "\n", "            if v3_index[i][1] in (\"GB\"):\n", "                df_v3[\"Bucket\"][i] = v3_index[i][1]\n", "\n", "            if v3_index[i][1] in (\"active\", \"not-active\"):\n", "                df_v3[\"Bucket\"][i] = v3_index[i][1]\n", "\n", "        if type(v3_index[i]) == str:\n", "            df_v3[\"Facility\"][i] = v3_index[i]\n", "            df_v3[\"Bucket\"][i] = \"Overall\"\n", "\n", "    df_v4 = df_v3[[\"Facility\", \"Bucket\"] + list(df_v1.columns)]\n", "\n", "    df_v5 = df_v4.groupby([\"Bucket\"]).sum()[list(df_v1.columns)]\n", "\n", "    df_v5 = df_v5.drop(index=\"\")\n", "    df_v5[\"Bucket\"] = df_v5.index\n", "    df_v5[\"Facility\"] = \"Overall\"\n", "    df_v5 = df_v5[[\"Facility\", \"Bucket\"] + list(df_v1.columns)]\n", "    df_v6 = df_v4\n", "    df_v6[\"Bucket\"] = df_v4[\"Bucket\"].replace(\"\", np.nan)\n", "    df_v6 = df_v6.dropna(how=\"any\", axis=0)\n", "    df_v6 = df_v6.sort_values([\"Facility\", \"Bucket\"])\n", "\n", "    df_bucket_x_count = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"bucket_x\"])\n", "        .count()[list(df_v1.columns)]\n", "    )\n", "    df_bucket_a_b_count = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"bucket_a_b\"])\n", "        .count()[list(df_v1.columns)]\n", "    )\n", "    df_bucket_gb_count = (\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"]\n", "        .groupby([\"facility_name\", \"is_gb\"])\n", "        .count()[list(df_v1.columns)]\n", "    )\n", "    #     df_overall_count = df_v2[df_v2['assortment_status'] == 'active'].groupby(['facility_name']).count()[list(df_v1.columns)]\n", "    df_overall_count = df_v2.groupby([\"facility_name\"]).count()[list(df_v1.columns)]\n", "    df_assmt_count = df_v2.groupby([\"facility_name\", \"assortment_status\"]).count()[\n", "        list(df_v1.columns)\n", "    ]\n", "    df_v3_count = pd.concat(\n", "        [\n", "            df_overall_count,\n", "            df_bucket_x_count,\n", "            df_bucket_a_b_count,\n", "            df_bucket_gb_count,\n", "            df_assmt_count,\n", "        ],\n", "        sort=False,\n", "    )\n", "\n", "    df_v3_count[\"Facility\"] = \"\"\n", "    df_v3_count[\"Bucket\"] = \"\"\n", "    v3_count_index = df_v3_count.index\n", "    print(\"cp-13\")\n", "    for i in range(len(v3_count_index)):\n", "\n", "        if type(v3_count_index[i]) == tuple:\n", "            df_v3_count[\"Facility\"][i] = v3_count_index[i][0]\n", "\n", "            if v3_count_index[i][1] == \"X\":\n", "                df_v3_count[\"Bucket\"][i] = \"X\"\n", "\n", "            if v3_count_index[i][1] in (\"A\", \"B\"):\n", "                df_v3_count[\"Bucket\"][i] = v3_count_index[i][1]\n", "\n", "            if v3_count_index[i][1] in (\"GB\"):\n", "                df_v3_count[\"Bucket\"][i] = v3_count_index[i][1]\n", "\n", "            if v3_count_index[i][1] in (\"active\", \"not-active\"):\n", "                df_v3_count[\"Bucket\"][i] = v3_index[i][1]\n", "\n", "        if type(v3_count_index[i]) == str:\n", "            df_v3_count[\"Facility\"][i] = v3_count_index[i]\n", "            df_v3_count[\"Bucket\"][i] = \"Overall\"\n", "\n", "    df_v3_count = df_v3_count[\n", "        [\"Facility\", \"Bucket\", \"week 1 Expected Sale\"]\n", "    ].reset_index(drop=True)\n", "\n", "    df_v3_count = df_v3_count[df_v3_count.Bucket != \"\"]\n", "\n", "    lA = [\n", "        \"Overall\",\n", "        \"A\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"][\n", "            df_v2[\"bucket_a_b\"] == \"A\"\n", "        ].count()[\"bucket_a_b\"],\n", "    ]\n", "    lB = [\n", "        \"Overall\",\n", "        \"B\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"][\n", "            df_v2[\"bucket_a_b\"] == \"B\"\n", "        ].count()[\"bucket_a_b\"],\n", "    ]\n", "    lGB = [\n", "        \"Overall\",\n", "        \"GB\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"][df_v2[\"is_gb\"] == \"GB\"].count()[\n", "            \"is_gb\"\n", "        ],\n", "    ]\n", "    #     lOverall = ['Overall', 'Overall', df_v2[df_v2['assortment_status'] == 'active'].count()['item_id']]\n", "    lOverall = [\"Overall\", \"Overall\", df_v2.count()[\"item_id\"]]\n", "    lX = [\n", "        \"Overall\",\n", "        \"X\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"][df_v2[\"bucket_x\"] == \"X\"].count()[\n", "            \"bucket_x\"\n", "        ],\n", "    ]\n", "    lactive = [\n", "        \"Overall\",\n", "        \"active\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"active\"][\n", "            df_v2[\"assortment_status\"] == \"active\"\n", "        ].count()[\"assortment_status\"],\n", "    ]\n", "    lnot_active = [\n", "        \"Overall\",\n", "        \"not-active\",\n", "        df_v2[df_v2[\"assortment_status\"] == \"not-active\"].count()[\"assortment_status\"],\n", "    ]\n", "\n", "    df_v3_count_ova = pd.DataFrame.from_records(\n", "        [lA, lB, lGB, lOverall, lX, lactive, lnot_active],\n", "        columns=list(df_v3_count.columns),\n", "    )\n", "\n", "    df_v3_count_f = pd.concat([df_v3_count_ova, df_v3_count], axis=0)\n", "    df_v3_count_f = df_v3_count_f.rename(\n", "        columns={\"week 1 Expected Sale\": \"facility_wise total skus\"}\n", "    )\n", "    df_v3_count_f = df_v3_count_f.sort_values([\"Facility\", \"Bucket\"])\n", "\n", "    df_v7 = pd.concat([df_v5, df_v6], axis=0)\n", "    df_v7 = df_v7.merge(\n", "        df_v3_count_f,\n", "        how=\"left\",\n", "        left_on=[\"Facility\", \"Bucket\"],\n", "        right_on=[\"Facility\", \"Bucket\"],\n", "    )\n", "    print(\"cp-14\")\n", "    df_v7[\"Availability without open po week 1\"] = (\n", "        df_v7[\"Availability without open po week 1\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability without open po week 2\"] = (\n", "        df_v7[\"Availability without open po week 2\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability without open po week 3\"] = (\n", "        df_v7[\"Availability without open po week 3\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability without open po week 4\"] = (\n", "        df_v7[\"Availability without open po week 4\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability without open po week 5\"] = (\n", "        df_v7[\"Availability without open po week 5\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "\n", "    df_v7[\"Availability with open po week 1\"] = (\n", "        df_v7[\"Availability with open po week 1\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability with open po week 2\"] = (\n", "        df_v7[\"Availability with open po week 2\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability with open po week 3\"] = (\n", "        df_v7[\"Availability with open po week 3\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability with open po week 4\"] = (\n", "        df_v7[\"Availability with open po week 4\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    df_v7[\"Availability with open po week 5\"] = (\n", "        df_v7[\"Availability with open po week 5\"] / df_v7[\"facility_wise total skus\"]\n", "    )\n", "    print(\"cp-15\")\n", "    df_v7 = df_v7.drop(columns=[\"facility_wise total skus\"])\n", "\n", "    order = [\n", "        list(np.unique(list(df_v7[\"Facility\"]))),\n", "        [\"Overall\", \"X\", \"GB\", \"A\", \"B\", \"active\", \"not-active\"],\n", "    ]\n", "    df_v8 = df_v7\n", "    df_v8[\"Facility\"] = pd.Categorical(df_v7[\"Facility\"], order[0])\n", "    df_v8[\"Bucket\"] = pd.Categorical(df_v7[\"Bucket\"], order[1])\n", "    df_v8 = df_v8.sort_values(by=[\"Facility\", \"Bucket\"])\n", "\n", "    # date column renaming\n", "    new_cols = {}\n", "    for i in range(0, 61):\n", "        new_cols[excel_date(date.today()) + i] = str(date.today() + timedelta(days=i))\n", "\n", "    # updateing date columns\n", "    df_inv_u = item_fac.join(inv_without_po).rename(columns=new_cols)\n", "    inven_with_po_u = item_fac.join(inv_with_po).rename(columns=new_cols)\n", "    shortfall_without_po_u = item_fac.join(shortfall_without_po).rename(\n", "        columns=new_cols\n", "    )\n", "    df_short_with_po_u = item_fac.join(shortfall_with_po).rename(columns=new_cols)\n", "    df_v1_u = item_fac.join(df_v1)\n", "    open_po_base_u = item_fac.join(o_po_work)\n", "    cpd_work_u = item_fac.join(cpd_work).rename(columns=new_cols)\n", "    df_current_inventory_u = item_fac.join(ci_work).rename(columns=new_cols)\n", "\n", "    return (\n", "        df_current_inventory_u,\n", "        cpd_work_u,\n", "        open_po_base_u,\n", "        df_inv_u,\n", "        inven_with_po_u,\n", "        shortfall_without_po_u,\n", "        df_short_with_po_u,\n", "        df_v2,\n", "        df_v8,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dfs_tabs(df_list, sheet_list, file_name):\n", "    writer = pd.ExcelWriter(file_name, engine=\"xlsxwriter\")\n", "    for dataframe, sheet in zip(df_list, sheet_list):\n", "        dataframe.to_excel(\n", "            writer, sheet_name=sheet, startrow=0, startcol=0, index=False\n", "        )\n", "    writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_shortfall_calc(current_inventory, cpd, open_po):\n", "\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"creating working files...\")\n", "    item_fac = item_fac_info(current_inventory)\n", "    open_po_in_lst = po_transform(open_po)\n", "    open_po_trans = open_po_in_lst[0]\n", "    open_po_all = open_po_in_lst[1]\n", "    df_current_inventory_w = df_ci_w(current_inventory)\n", "    df_open_po_30_w = open_po_n_days_w(open_po_trans, df_current_inventory_w[0], 31)\n", "    df_open_po_60_w = open_po_n_days_w(open_po_trans, df_current_inventory_w[0], 61)\n", "    df_cpd_w = cpd_w(cpd)\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"working files created!\")\n", "\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"doing working calculations...\")\n", "    (\n", "        inv_without_po_r,\n", "        inv_with_po_r,\n", "        shortfall_without_po_r,\n", "        shortfall_with_po_r,\n", "    ) = shortfall_calc(item_fac, df_current_inventory_w[1], df_cpd_w, df_open_po_30_w)\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"working calculations done!\")\n", "\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"doing summary calculations...\")\n", "    (\n", "        current_inventory,\n", "        cpd,\n", "        open_po,\n", "        inven_without_po,\n", "        inven_with_po,\n", "        shortfall_without_po,\n", "        shortfall_with_po,\n", "        itemwise_summ,\n", "        final_summ,\n", "    ) = summary_cal(\n", "        item_fac,\n", "        df_current_inventory_w[1],\n", "        df_cpd_w,\n", "        df_open_po_60_w,\n", "        inv_without_po_r,\n", "        inv_with_po_r,\n", "        shortfall_without_po_r,\n", "        shortfall_with_po_r,\n", "        open_po_all,\n", "    )\n", "    print(datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"), \"summary calculations done!\")\n", "\n", "    return (\n", "        [inv_without_po_r, inv_with_po_r, shortfall_without_po_r, shortfall_with_po_r],\n", "        [\n", "            current_inventory,\n", "            cpd,\n", "            open_po,\n", "            inven_without_po,\n", "            inven_with_po,\n", "            shortfall_without_po,\n", "            shortfall_with_po,\n", "            itemwise_summ,\n", "            final_summ,\n", "        ],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dt_range_1 = [int(excel_date(datetime.today())),44047]\n", "\n", "\n", "sheet_id = \"1T4lK8JNRXQWMk5MrA4gw6_yPUVUqgFOoC5mgwd8gR9U\"\n", "sheet_name = \"Daterange sf\"\n", "daterange_df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "daterange_df[\"Start Date\"] = pd.to_datetime(daterange_df[\"Start Date\"])\n", "daterange_df[\"End Date\"] = pd.to_datetime(daterange_df[\"End Date\"])\n", "\n", "daterange_df[\"StartdateExcel\"] = daterange_df[\"StartdateExcel\"].astype(\"int\")\n", "daterange_df[\"EnddateExcel\"] = daterange_df[\"EnddateExcel\"].astype(\"int\")\n", "\n", "daterange_df = daterange_df[date.today() < daterange_df[\"End Date\"]]\n", "\n", "a = daterange_df.iloc[0, 1].date() - date.today()\n", "\n", "dt_range_1_0 = daterange_df.iloc[0, 3] - a.days + 1\n", "dt_range_1_1 = daterange_df.iloc[0, 3]\n", "dt_range_2_0 = daterange_df.iloc[1, 2]\n", "dt_range_2_1 = daterange_df.iloc[1, 3]\n", "dt_range_3_0 = daterange_df.iloc[2, 2]\n", "dt_range_3_1 = daterange_df.iloc[2, 3]\n", "dt_range_4_0 = daterange_df.iloc[3, 2]\n", "dt_range_4_1 = daterange_df.iloc[3, 3]\n", "dt_range_5_0 = daterange_df.iloc[4, 2]\n", "dt_range_5_1 = daterange_df.iloc[4, 3]\n", "\n", "start_end_df = daterange_df.iloc[0:5, 0:2]\n", "start_end_df.iloc[0, 0] = date.today() + <PERSON><PERSON>ta(days=1)\n", "\n", "Week_info = [\"Week-1\", \"Week-2\", \"Week-3\", \"Week-4\", \"Week-5\"]\n", "start_end_df[\"Week Range\"] = Week_info\n", "start_end_df = start_end_df[[\"Week Range\", \"Start Date\", \"End Date\"]]\n", "\n", "dt_range_1 = [dt_range_1_0, dt_range_1_1]\n", "dt_range_2 = [dt_range_2_0, dt_range_2_1]\n", "dt_range_3 = [dt_range_3_0, dt_range_3_1]\n", "dt_range_4 = [dt_range_4_0, dt_range_4_1]\n", "dt_range_5 = [dt_range_5_0, dt_range_5_1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = run_shortfall_calc(df_current_inventory, df_cpd, df_open_po)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x[1][3].to_csv(\"Inventory_without_Open_PO.csv\",index=False)\n", "x[1][-1].to_csv(\"final_summ.csv\", index=False)\n", "x[1][-2].to_csv(\"item_summ.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_open_po_feeder\n", "# df_feeder_inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shortfall_sum = x[0][2].sum(axis=1)\n", "shortfall_sum = shortfall_sum.to_frame().rename(columns={0: \"short_sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory_w = df_feeder_inventory.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df_current_inventory[\"feeder_inventory\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_feeder_inventory_w[\"Item IDFacility Name\"] = (\n", "    df_feeder_inventory_w[\"item_id\"].astype(str)\n", "    + df_feeder_inventory_w[\"facility_name\"]\n", "    + df_feeder_inventory_w[\"bucket_a_b\"]\n", "    + df_feeder_inventory_w[\"bucket_x\"]\n", ")\n", "df_feeder_inventory_w.set_index(\"Item IDFacility Name\", inplace=True, drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = df_feeder_inventory_w.join(shortfall_sum)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ys = (\n", "    y.groupby([\"item_id\", \"feeder_name\"])\n", "    .sum()[\"short_sum\"]\n", "    .to_frame()\n", "    .rename(columns={0: \"short_sum\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yys = y.merge(\n", "    ys,\n", "    left_on=[\"item_id\", \"feeder_name\"],\n", "    right_on=[\"item_id\", \"feeder_name\"],\n", "    how=\"left\",\n", ")\n", "yys[\"feeder_inventory\"] = yys[\"current_inventory\"] * (\n", "    yys[\"short_sum_x\"] / yys[\"short_sum_y\"]\n", ")\n", "yys = yys[[\"item_id\", \"facility_name\", \"bucket_x\", \"bucket_a_b\", \"feeder_inventory\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yyys = df_current_inventory.merge(\n", "    yys,\n", "    left_on=[\"item_id\", \"facility_name\", \"bucket_x\", \"bucket_a_b\"],\n", "    right_on=[\"item_id\", \"facility_name\", \"bucket_x\", \"bucket_a_b\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yyyys = yyys.copy()\n", "# list(set(list(df_current_inventory.columns)) - set(['current_inventory']))\n", "# yyys.groupby(['Item IDFacility Name']).sum()[['feeder_inventory','current_inventory']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yyyys = yyyys.reset_index()\n", "# yyyys['current_inventory'] = yyyys['current_inventory'] + yyyys['feeder_inventory']\n", "# yyyys = yyyys.drop(columns = ['feeder_inventory'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yyyys[\"feeder_inventory\"].fillna(0.0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# yyyys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_open_po_feeder_w = df_open_po_feeder.copy()\n", "df_open_po_feeder_w[\"Item IDFacility Name\"] = (\n", "    df_open_po_feeder_w[\"item_id\"].astype(str)\n", "    + df_open_po_feeder_w[\"facility_name\"]\n", "    + df_open_po_feeder_w[\"bucket_a_b\"]\n", "    + df_open_po_feeder_w[\"bucket_x\"]\n", ")\n", "df_open_po_feeder_w.set_index(\"Item IDFacility Name\", inplace=True, drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# z"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["z = df_open_po_feeder_w.join(shortfall_sum)\n", "zs = (\n", "    z.groupby([\"item_id\", \"outlet_id\"])\n", "    .sum()[\"short_sum\"]\n", "    .to_frame()\n", "    .rename(columns={0: \"short_sum\"})\n", ")\n", "zzs = z.merge(\n", "    zs, left_on=[\"item_id\", \"outlet_id\"], right_on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zzs[\"feeder_qty\"] = zzs[\"ordered_quantity\"] * (zzs[\"short_sum_x\"] / zzs[\"short_sum_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zzs = zzs[\n", "    [\n", "        \"item_id\",\n", "        \"outlet_id\",\n", "        \"facility_name\",\n", "        \"variant_description\",\n", "        \"vendor_name\",\n", "        \"bucket_x\",\n", "        \"bucket_a_b\",\n", "        \"is_gb\",\n", "        \"po_id\",\n", "        \"schedule_date\",\n", "        \"expiry_date\",\n", "        \"feeder_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zzs = zzs.rename(columns={\"feeder_qty\": \"ordered_quantity\"})\n", "# df_open_po = df_open_po.drop(columns = ['Item IDFacility Name'])\n", "zzzs = pd.concat([df_open_po, zzs], sort=False)\n", "zzzs = zzzs.drop(columns=[\"Item IDFacility Name\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yyyys = yyyys.sort_values(\n", "    [\"Item IDFacility Name\", \"feeder_inventory\"], ascending=[0, 0]\n", ")\n", "yyyys = yyyys.drop_duplicates(\n", "    [\n", "        \"item_id\",\n", "        \"facility_name\",\n", "        \"item_name\",\n", "        \"assortment_status\",\n", "        \"variant_description\",\n", "        \"vendor_name\",\n", "        \"bucket_x\",\n", "        \"bucket_a_b\",\n", "        \"is_gb\",\n", "        \"asp\",\n", "        \"current_inventory\",\n", "        \"Item IDFacility Name\",\n", "    ],\n", "    keep=\"first\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = run_shortfall_calc(yyyys, df_cpd, zzzs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tf_df = item_df[[\"facility_name\", \"item_id\", \"bucket_transfer_type\"]]\n", "po_qty_df = (\n", "    zzzs.groupby([\"item_id\", \"facility_name\"])\n", "    .sum()[[\"ordered_quantity\"]]\n", "    .reset_index()\n", "    .rename(columns={\"ordered_quantity\": \"po qty\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y[1][-2] = (\n", "    y[1][-2]\n", "    .merge(\n", "        tf_df,\n", "        how=\"left\",\n", "        left_on=[\"item_id\", \"facility_name\"],\n", "        right_on=[\"item_id\", \"facility_name\"],\n", "    )\n", "    .merge(\n", "        po_qty_df,\n", "        how=\"left\",\n", "        left_on=[\"item_id\", \"facility_name\"],\n", "        right_on=[\"item_id\", \"facility_name\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_df = l_cats_df[[\"item_id\", \"l0\"]]\n", "l0_df[\"item_id\"] = l0_df[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_l0 = item_df[[\"item_id\", \"facility_name\", \"po_cycle_name\"]].merge(\n", "    l0_df, how=\"left\", left_on=[\"item_id\"], right_on=[\"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y[1][-2] = y[1][-2].merge(\n", "    po_l0,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"facility_name\"],\n", "    right_on=[\"item_id\", \"facility_name\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y[1][-1].to_csv(\"final_summ_feeder.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y[1][-2].to_csv(\"item_summ_feeder.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install xlsxwriter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_name = \"shortfall_report_\" + str(date.today()) + \".xlsx\"\n", "dfs = [start_end_df, y[1][-2], y[1][-1]]\n", "sheets = [\"Week Date Range\", \"Item wise summary\", \"Summary\"]\n", "dfs_tabs(dfs, sheets, file_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## file to s3 path\n", "\n", "import boto3\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "\n", "\n", "def create_presigned_url(file_name, object_name, expiration=3600 * 24 * 30):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response\n", "\n", "\n", "# cloud_filepath = \"/\"+file_name\n", "cloud_filepath = \"pencilbox/shubham/\" + file_name\n", "pb.to_s3(file_name, bucket_name, cloud_filepath)\n", "File_Path_Link = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 24 * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ld = datetime.now().strftime(\"%Y-%m-%d\")\n", "from_email = \"<EMAIL>\"\n", "to_email = list(email_list[\"email\"])\n", "\n", "subject = \"Shortfall report \" + ld\n", "html_content = (\n", "    \"\"\"Hi Team,<br/><br/>\n", "                    <p> Please find the link to Shortfall report below. <br/>\n", "                         Kindly go through the same and let me know in case of any query.<br/>\n", "                             <br/> <br/><b> link: </b><br/></p>\"\"\"\n", "    + File_Path_Link\n", "    + \"\"\"\n", "                            <p style=\"color:red\"><br/><br/> Note: Above link will be expire in next 30 days<br/></p>\n", "                            <p><br/> <br/>\"\"\"\n", ")\n", "\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## starting automation from here"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alpha = y[1][-2][:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alpha[\"captured_at\"] = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alpha[\"po qty\"].fillna(0, inplace=True)\n", "alpha[\"po qty\"] = alpha[\"po qty\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# list(excess_w7.columns)\n", "column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"bigint\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"variant_description\", \"type\": \"varchar\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar\"},\n", "    {\"name\": \"bucket_a_b\", \"type\": \"varchar\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\"},\n", "    {\"name\": \"assortment_status\", \"type\": \"varchar\"},\n", "    {\"name\": \"asp\", \"type\": \"float\"},\n", "    {\"name\": \"week 1 Expected Sale\", \"type\": \"float\"},\n", "    {\"name\": \"week 1 open po\", \"type\": \"float\"},\n", "    {\"name\": \"week 1 Shortfall\", \"type\": \"float\"},\n", "    {\"name\": \"week 1 Shortfall 2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_before_sf_week_1\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_after_sf_week_1\", \"type\": \"float\"},\n", "    {\"name\": \"SF with no open-po week 1\", \"type\": \"float\"},\n", "    {\"name\": \"Availability without open po week 1\", \"type\": \"float\"},\n", "    {\"name\": \"Availability with open po week 1\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory without open po week 1\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory with open po week 1\", \"type\": \"float\"},\n", "    {\"name\": \"week 2 Expected Sale\", \"type\": \"float\"},\n", "    {\"name\": \"week 2 open po\", \"type\": \"float\"},\n", "    {\"name\": \"week 2 Shortfall\", \"type\": \"float\"},\n", "    {\"name\": \"week 2 Shortfall 2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_before_sf_week_2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_after_sf_week_2\", \"type\": \"float\"},\n", "    {\"name\": \"SF with no open-po week 2\", \"type\": \"float\"},\n", "    {\"name\": \"Availability without open po week 2\", \"type\": \"float\"},\n", "    {\"name\": \"Availability with open po week 2\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory without open po week 2\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory with open po week 2\", \"type\": \"float\"},\n", "    {\"name\": \"week 3 Expected Sale\", \"type\": \"float\"},\n", "    {\"name\": \"week 3 open po\", \"type\": \"float\"},\n", "    {\"name\": \"week 3 Shortfall\", \"type\": \"float\"},\n", "    {\"name\": \"week 3 Shortfall 2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_before_sf_week_3\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_after_sf_week_3\", \"type\": \"float\"},\n", "    {\"name\": \"SF with no open-po week 3\", \"type\": \"float\"},\n", "    {\"name\": \"Availability without open po week 3\", \"type\": \"float\"},\n", "    {\"name\": \"Availability with open po week 3\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory without open po week 3\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory with open po week 3\", \"type\": \"float\"},\n", "    {\"name\": \"week 4 Expected Sale\", \"type\": \"float\"},\n", "    {\"name\": \"week 4 open po\", \"type\": \"float\"},\n", "    {\"name\": \"week 4 Shortfall\", \"type\": \"float\"},\n", "    {\"name\": \"week 4 Shortfall 2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_before_sf_week_4\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_after_sf_week_4\", \"type\": \"float\"},\n", "    {\"name\": \"SF with no open-po week 4\", \"type\": \"float\"},\n", "    {\"name\": \"Availability without open po week 4\", \"type\": \"float\"},\n", "    {\"name\": \"Availability with open po week 4\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory without open po week 4\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory with open po week 4\", \"type\": \"float\"},\n", "    {\"name\": \"week 5 Expected Sale\", \"type\": \"float\"},\n", "    {\"name\": \"week 5 open po\", \"type\": \"float\"},\n", "    {\"name\": \"week 5 Shortfall\", \"type\": \"float\"},\n", "    {\"name\": \"week 5 Shortfall 2\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_before_sf_week_5\", \"type\": \"float\"},\n", "    {\"name\": \"open_po_after_sf_week_5\", \"type\": \"float\"},\n", "    {\"name\": \"SF with no open-po week 5\", \"type\": \"float\"},\n", "    {\"name\": \"Availability without open po week 5\", \"type\": \"float\"},\n", "    {\"name\": \"Availability with open po week 5\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory without open po week 5\", \"type\": \"float\"},\n", "    {\"name\": \"opening inventory with open po week 5\", \"type\": \"float\"},\n", "    {\"name\": \"bucket_transfer_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"po qty\", \"type\": \"integer\"},\n", "    {\"name\": \"po_cycle_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\"},\n", "    {\"name\": \"captured_at\", \"type\": \"date\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"Shortfall_report_dump\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [],\n", "    \"sortkey\": [],\n", "    \"incremental_key\": [],\n", "    \"load_type\": \"append\",\n", "}\n", "pb.to_redshift(alpha, **kwargs)\n", "\n", "logging.warning(\"\\n|| Data written to the table... ||\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id='1L3gPBe75f3A3rxhWwLJ8xBk84j-2WVF5JVQYDLfoyiM'\n", "# sheet_name='sf summary'\n", "\n", "# pb.to_sheets(y[1][-1], sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}