{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = (datetime.now() + <PERSON><PERSON><PERSON>(minutes=330)).date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = current_date - <PERSON><PERSON><PERSON>(days=30)\n", "end_date = current_date - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "\n", "select date_ts,\n", "       outlet_id,\n", "       outlet_name as \"Store Name\",\n", "       UPPER(case when city_name in ('Ghaziabad', 'Noida') then 'UP-NCR'\n", "            when city_name = 'Bangalore' then 'Bengaluru'\n", "            else city_name end) as \"City\",\n", "       count(distinct order_id) as \"Orders\",\n", "       count(distinct picker_id) as \"Pickers\",\n", "       round(count(distinct Case when instore_time <= 150 then order_id end)) as \"orders within 2.5 Min\",\n", "       round(count(distinct Case when delivery_time <= 900 then order_id end)) as \"orders delivered within 15 min\",\n", "       sum(picking_start_to_completed) as picking_time,\n", "       sum(total_items_quantity_ordered) as total_qty\n", "       \n", "from\n", "(\n", "SELECT date(order_checkout_ts_ist) as date_ts,\n", "        c.name as outlet_name,\n", "       o.outlet_id,\n", "       trim(location) as city_name,\n", "       o.suborder_id,\n", "       o.cart_id,\n", "       o.order_id,\n", "       --(z.order_id || z.order_item_id) as order_items,\n", "       o.picker_id,total_items_quantity_ordered,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_billing_completed_ts_ist) as instore_time,\n", "       DATEDIFF(seconds,order_picking_started_ts_ist,order_picking_completed_ts_ist) as picking_start_to_completed,\n", "       DATEDIFF(seconds,order_checkout_ts_ist,order_delivered_ts_ist) as delivery_time\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join dwh.fact_sales_order_item_details z on z.order_id = o.order_id\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id\n", "WHERE \n", "  date(o.order_checkout_ts_ist) BETWEEN CURRENT_DATE-30 AND CURRENT_DATE-1\n", "and is_rescheduled=false \n", "\n", "and order_picking_completed_ts_ist is not null and \n", "o.order_type='super_express'\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12\n", ")\n", "group by 1,2,3,4\n", "order by 1 desc\n", "\n", "\"\"\"\n", "\n", "data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_mp_q = \"\"\"\n", "select date(date) as date_ts,\n", "store_id as outlet_id,\n", "count(distinct case when (login is not null or attendance_punched='Yes') and (role not ilike '%%Store Manager%%' and role not ilike '%%Supervisor%%' and role not ilike '%%Team Leader%%')\n", "then employee_id end) as total_employees_present,\n", "count(distinct case when score>0 then employee_id end) as employees_present_activity,\n", "count(distinct case when score>500 then employee_id end) as employees_more_than_500_score\n", "from metrics.instore_manpower_performance\n", "where date(date) between '{start_date}' and '{end_date}'\n", "group by 1,2\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "\n", "total_mp_data = pd.read_sql(total_mp_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_mp_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"pick_time_per_item\"] = data[\"picking_time\"] / data[\"total_qty\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_temp = data.merge(total_mp_data, on=[\"date_ts\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_temp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_temp.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_temp = data_temp[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"orders delivered within 15 min\",\n", "        \"picking_time\",\n", "        \"total_qty\",\n", "        \"pick_time_per_item\",\n", "        \"employees_present_activity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fillrate_q = \"\"\"\n", "with attribution as (SELECT ScheduledDate,\n", "      outlet_id,\n", "      facilityname,\n", "      count(distinct order_id) as unfulfilled_orders,\n", "      count(item_id) as unfulfilled_items,\n", "      sum(CASE\n", "      WHEN reason = 'FNV' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Fnv,\n", "      sum(CASE\n", "      WHEN reason = 'Metering' or reason = 'Nayala' THEN unfulfilled\n", "      ELSE 0\n", "      END) as \"Nyala & Metering\",\n", "      sum(CASE\n", "      WHEN reason = 'Reschedule_Impact' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reschedule_Impact,\n", "    --   sum(CASE\n", "    --   WHEN  THEN unfulfilled\n", "    --   ELSE 0\n", "    --   END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'GRN pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as GRN_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'return pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as return_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'badstock marked' THEN unfulfilled\n", "      ELSE 0\n", "      END) as badstock_marked,\n", "      sum(CASE\n", "      WHEN reason = 'Tail_Assortment' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'Checkout' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Checkout,\n", "      sum(CASE\n", "      WHEN reason = 'Partial Inventory' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Partial_Inventory,\n", "      sum(CASE\n", "      WHEN reason = 'Reprocure' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reprocure,\n", "      sum(CASE WHEN reason = 'Others' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Others\n", "      from \n", "(\n", "SELECT slot_start::date AS ScheduledDate,\n", "             facility_id,\n", "             facilityname,\n", "             outlet_name,\n", "             outlet_id,\n", "             order_id,\n", "             a.item_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled,\n", "             (case \n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and ((upper_limit>0) or (fnv_upper_limit>0)) then 'Metering'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Created') then 'Nayala'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Reprocure') then 'Reschedule_Impact'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,inward_timestamp,picking_time) between 0 and 120) then 'GRN pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,return_timestamp,picking_time) between 0 and 120) then 'return pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (badstock_qty > ordered_quantity and datediff(min,badstock_timestamp,picking_time) between -120 and 120) then 'badstock marked'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (actual_quantity < 5) then 'Tail_Assortment'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.outlet_type=1) then 'FNV' \n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' then 'Checkout'\n", "                  when reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' then 'Partial Inventory'\n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' then 'Reprocure'\n", "                  else 'Others' end) as reason\n", "      FROM metrics.fillrateattribution_darkstores a\n", "      inner join lake_rpc.product_product pp on pp.item_id = a.item_id\n", "      WHERE (slot_start::date between '{start_date}' and '{end_date}') and reason_key <> ''\n", "      and pp.outlet_type=2\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               10\n", "      order by 1,2,3,4,5,6,7,8)\n", "-- where outlet_id in (995,1056)\n", "group by 1,2,3),\n", "\n", "delivered_orders as\n", "( select \n", "    date(order_delivery_slot_start_ts_ist) as scheduled_date,\n", "    outlet_id,\n", "    b.name as outlet_name,\n", "    count(distinct order_id) as total_orders,\n", "    sum(distinct_items_ordered) as total_items\n", "    from dwh.fact_supply_chain_order_details a \n", "    inner join lake_retail.console_outlet b on a.outlet_id = b.id\n", "    where is_order_delivered=true and \n", "    date(order_delivery_slot_start_ts_ist) between '{start_date}' and '{end_date}'\n", "    and business_type_id = 7\n", "    group by 1,2,3\n", ")\n", "\n", "\n", "select b.scheduled_date as date_ts,\n", "b.outlet_id,\n", "      b.outlet_name,\n", "      sum(b.total_orders) as total_orders,\n", "      ---b.total_orders-a.unfulfilled_orders as fulfilled_orders,\n", "      sum(a.unfulfilled_orders) as unfulfilled_orders,\n", "      sum(a.unfulfilled_orders)*1.0/sum(b.total_orders) as fill_rate\n", "    --   b.total_items as total_items,\n", "    --   b.total_items-a.unfulfilled_items as fulfilled_items\n", "      from delivered_orders b\n", "      inner join attribution a on a.outlet_id = b.outlet_id and a.ScheduledDate = b.scheduled_date\n", "        group by 1,2,3\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "\n", "\n", "fillrate_data = pd.read_sql(fillrate_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["complaints_q = \"\"\"\n", "select date(delivery_date) as date_ts,\n", "outlet_id,\n", "--trim(c.name) as city_name,\n", "       UPPER(case when c.name in ('Ghaziabad', 'Noida') then 'UP-NCR'\n", "            when c.name = 'Bangalore' then 'Bengaluru'\n", "            else c.name end) as city_name,\n", "       count(distinct order_id) as complaint_orders\n", "from metrics.complaints o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "inner join lake_retail.console_location c on c.id = m.tax_location_id\n", "where delivery_date between CURRENT_DATE - INTERVAL '30 DAYS' and current_date-1\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "complaints_data = pd.read_sql(complaints_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fillrate_data[\"fillrate\"] = 1 - fillrate_data[\"fill_rate\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wastage_q = \"\"\"\n", "select \n", "a.snapshot_date_ist::date as date_ts,\n", "a.outlet_id,\n", "co.name as outlet_name,\n", "(case when co.location = 'Gurgaon' then 'HR-NCR' when co.location = 'Noida' then 'UP-NCR' when co.location = 'Budhpur' then 'Delhi' else co.location end) as city,\n", "sum(crm_diff) crm,\n", "sum(crbs_diff) crbs,\n", "sum(loss_in_transit_diff) lit,\n", "sum(ifo_wo_iro_diff) ifo,\n", "sum(bad_stock_damage) bsd,\n", "sum(bad_stock_near_expiry) bsne,\n", "sum(bad_stock_expired) bse,\n", "sum(esto_transfer_loss_diff) estl,\n", "sum(esto_bad_stock_diff) estd,\n", "sum(manual_negative_update_diff) mun,\n", "sum(ifo_with_iro_diff) iro,\n", "sum(customer_return_wo_invoice_diff) cr,\n", "sum(manual_positive_update_diff) mpu,\n", "sum(reinventorization_diff) ri,\n", "sum(prn_diff) prn,\n", "sum(total_diff_loss_value) total,\n", "sum(total_diff_loss_value)- sum(esto_transfer_loss_diff) finalWastage,\n", "getdate() + interval '5.5 hours' as upd\n", "from dwh.view_agg_daily_category_diff_loss a\n", "left join lake_retail.console_outlet co on co.id = a.outlet_id and co.active = 1\n", "inner join lake_retail.console_location cl on cl.id = co.tax_location_id\n", "where a.snapshot_date_ist between '{start_date}' and '{end_date}'\n", "group by 1,2,3,4\n", "order by 1,2\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wastage_data = pd.read_sql(wastage_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_q = \"\"\"\n", "with base as(\n", "select\n", "    oi.cart_checkout_ts_ist::date as date_,\n", "    oi.outlet_id,\n", "    c.name as outlet_name,\n", "    oi.cart_id,\n", "    oi.product_id,\n", "    case when l0_category_id in (1487) then 'fnv'\n", "      when l0_category_id in (888,15,4,14,12) and l1_category_id in (279,1425,1361,1362,1363,1388,922,123,923,1200,953,1184) \n", "        and l2_category_id in (1956,1425,63,1367,1369,1730,127,1094,949,138,1091,1185,123,1778,950,1389,31,1097,198,1093) then 'perishable'\n", "      else 'packaged' end as category,\n", "    sum(oi.unit_selling_price*procured_quantity) gmv\n", "\n", "from dwh.fact_sales_order_item_details oi\n", "join dwh.fact_sales_order_details fs on fs.order_id = oi.order_id\n", "left join dwh.dim_product dp on dp.product_key = oi.dim_product_key and is_current\n", "inner join lake_retail.console_outlet c on c.id = oi.outlet_id and business_type_id = 7\n", "where date(fs.cart_checkout_ts_ist)  between '{start_date}' and '{end_date}'\n", "    and date(oi.cart_checkout_ts_ist) between '{start_date}' and '{end_date}'\n", "    and fs.order_current_status = 'DELIVERED'\n", "    and oi.is_internal_order = false\n", "    and procured_quantity > 0\n", "\n", "group by 1,2,3,4,5,6\n", ")\n", ",\n", "cat_gmv as(\n", "select\n", "date_,\n", "outlet_id,\n", "outlet_name,\n", "sum(case when category = 'fnv' then gmv end) as fnv_gmv,\n", "sum(case when category = 'perishable' then gmv end) as perishable_gmv,\n", "sum(case when category = 'packaged' then gmv end) as packaged_gmv,\n", "count(distinct case when category = 'fnv' then cart_id end) as fnv_cart,\n", "count(distinct case when category = 'perishable' then cart_id end) as perishable_cart,\n", "count(distinct case when category = 'packaged' then cart_id end) as packaged_cart\n", "\n", "from base\n", "group by 1,2,3\n", ")\n", ",\n", "og as(\n", "select\n", "date_,\n", "outlet_id,\n", "count(distinct cart_id) as orders\n", "\n", "from base\n", "group by 1,2\n", ")\n", "\n", "select\n", "a.date_ as date_ts,\n", "a.outlet_id,\n", "outlet_name,\n", "orders,\n", "fnv_gmv,\n", "perishable_gmv,\n", "packaged_gmv,\n", "(coalesce(fnv_gmv,0) + coalesce(perishable_gmv,0) + coalesce(packaged_gmv,0)) as gmv,\n", "fnv_cart,\n", "perishable_cart,\n", "packaged_cart,\n", "getdate() + interval '5.5 hours' as up\n", "\n", "\n", "from og a\n", "left join cat_gmv b on a.date_ = b.date_ and a.outlet_id = b.outlet_id\n", "order by 1,2\n", "\n", "\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "\n", "gmv_data = pd.read_sql(gmv_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1 = data_temp.merge(\n", "    fillrate_data[[\"date_ts\", \"outlet_id\", \"fillrate\"]],\n", "    on=[\"date_ts\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "data2 = data1.merge(\n", "    complaints_data[[\"date_ts\", \"outlet_id\", \"complaint_orders\"]],\n", "    on=[\"date_ts\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "data3 = data2.merge(\n", "    wastage_data[[\"date_ts\", \"outlet_id\", \"finalwastage\"]],\n", "    on=[\"date_ts\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "data4 = data3.merge(\n", "    gmv_data[[\"date_ts\", \"outlet_id\", \"gmv\"]],\n", "    on=[\"date_ts\", \"outlet_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data4 = data4[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_overall = data4[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_overall[\"city\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_overall_new = pd.concat([data4, data_city_overall])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_overall_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_overall = data_city_overall_new[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_store_overall['store id'] = 'Overall'\n", "data_store_overall[\"store name\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_overall_new = pd.concat([data_city_overall_new, data_store_overall])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = pb.from_sheets(\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\", \"Store info\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type[\"Outlet ID\"] = data_store_type[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type[[\"Outlet ID\", \"Operation Mode\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type.rename(\n", "    columns={\"Outlet ID\": \"outlet_id\", \"Operation Mode\": \"store type\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall = data_store_overall_new.merge(\n", "    data_store_type, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new = data_store_type_overall[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new[\"store type\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new = pd.concat(\n", "    [data_store_type_overall, data_store_type_overall_new]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new[\"week_start\"] = data_store_type_overall_new_new[\n", "    \"date_ts\"\n", "].apply(lambda x: x - timedelta(days=(x.weekday() + 1) % 7))\n", "\n", "data_store_type_overall_new_new[\"week_end\"] = data_store_type_overall_new_new[\n", "    \"week_start\"\n", "].apply(lambda x: x + <PERSON><PERSON>ta(days=6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new[\"week\"] = (\n", "    data_store_type_overall_new_new[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + data_store_type_overall_new_new[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_new = data_store_type_overall_new_new[\n", "    [\n", "        \"date_ts\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"store type\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_data = data_store_type_overall_new_new[\n", "    [\n", "        \"week\",\n", "        \"outlet_id\",\n", "        \"store name\",\n", "        \"city\",\n", "        \"orders\",\n", "        \"pickers\",\n", "        \"orders within 2.5 min\",\n", "        \"pick_time_per_item\",\n", "        \"fillrate\",\n", "        \"complaint_orders\",\n", "        \"store type\",\n", "        \"employees_present_activity\",\n", "        \"finalwastage\",\n", "        \"gmv\",\n", "        \"orders delivered within 15 min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall_new_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "with attribution as (SELECT \n", "      outlet_id,\n", "      ScheduledDate,\n", "      count(distinct order_id) as unfulfilled_orders,\n", "      count(item_id) as unfulfilled_items,\n", "      sum(CASE\n", "      WHEN reason = 'FNV' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Fnv,\n", "      sum(CASE\n", "      WHEN reason = 'Metering' or reason = 'Nayala' THEN unfulfilled\n", "      ELSE 0\n", "      END) as \"Nyala & Metering\",\n", "      sum(CASE\n", "      WHEN reason = 'Reschedule_Impact' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reschedule_Impact,\n", "    --   sum(CASE\n", "    --   WHEN  THEN unfulfilled\n", "    --   ELSE 0\n", "    --   END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'GRN pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as GRN_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'return pending putaway' THEN unfulfilled\n", "      ELSE 0\n", "      END) as return_pending_putaway,\n", "      sum(CASE\n", "      WHEN reason = 'badstock marked' THEN unfulfilled\n", "      ELSE 0\n", "      END) as badstock_marked,\n", "      sum(CASE\n", "      WHEN reason = 'Tail_Assortment' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN reason = 'Checkout' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Checkout,\n", "      sum(CASE\n", "      WHEN reason = 'Partial Inventory' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Partial_Inventory,\n", "      sum(CASE\n", "      WHEN reason = 'Reprocure' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Reprocure,\n", "      sum(CASE WHEN reason = 'Others' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Others\n", "      from \n", "(\n", "select * from\n", "(\n", "SELECT (slot_start)::date AS ScheduledDate,\n", "             outlet_id,\n", "             order_id,\n", "             a.item_id,\n", "             reason_key,\n", "             trim(location) as city_name,\n", "             max(unfulfilled_order) AS Unfulfilled,\n", "             (case \n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and ((upper_limit>0) or (fnv_upper_limit>0)) then 'Metering'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Created') then 'Nayala'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.description = 'Reprocure') then 'Reschedule_Impact'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,inward_timestamp,picking_time) between 0 and 120) then 'GRN pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (datediff(min,return_timestamp,picking_time) between 0 and 120) then 'return pending putaway'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (badstock_qty > ordered_quantity and datediff(min,badstock_timestamp,picking_time) between -120 and 120) then 'badstock marked'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (actual_quantity < 5) then 'Tail_Assortment'\n", "                  when (reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING') and (a.outlet_type=1) then 'FNV' \n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' then 'Checkout'\n", "                  when reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' then 'Partial Inventory'\n", "                  when reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' then 'Reprocure'\n", "                  else 'Others' end) as reason\n", "      FROM metrics.fillrateattribution_darkstores a\n", "      inner join lake_rpc.product_product pp on pp.item_id = a.item_id\n", "      inner join lake_retail.console_outlet c on c.id = a.outlet_id and business_type_id = 7\n", "      WHERE ((slot_start)::date \n", "      >= '{start_date}' and (slot_start)::date <= '{end_date}') \n", "      and reason_key <> ''\n", "      and a.outlet_id not in (3950,3658,1851,2574,3945,1473,3230)\n", "      and a.reason_key not in ('<PERSON><PERSON>','<PERSON>yal<PERSON>','Checkout')\n", "      and pp.outlet_type=2\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               8\n", "      order by 1,2,3,4,5,6,7,8\n", ")\n", "where reason not in ('<PERSON><PERSON>','<PERSON>yal<PERSON>','Checkout')\n", ")\n", "group by 1,2),\n", "\n", "delivered_orders as\n", "( select \n", "     date(order_delivery_slot_start_ts_ist) as scheduled_date,\n", "    -- outlet_id,\n", "    -- b.name as outlet_name,\n", "    b.id as outlet_id,\n", "    count(distinct order_id) as total_orders,\n", "    sum(distinct_items_ordered) as total_items\n", "    from dwh.fact_supply_chain_order_details a \n", "    inner join lake_retail.console_outlet b on a.outlet_id = b.id and business_type_id = 7\n", "    where is_order_delivered=true and \n", "    (date(order_delivery_slot_start_ts_ist)>= '{start_date}'\n", "    and date(order_delivery_slot_start_ts_ist)<= '{end_date}')\n", "    and business_type_id = 7\n", "    and outlet_id not in (3950,3658,1851,2574,3945,1473,3230)\n", "    group by 1,2\n", ")\n", "\n", "select a.outlet_id,\n", "       date(b.scheduled_date) as ScheduledDate,\n", "      sum(b.total_orders) as total_orders,\n", "      sum(a.unfulfilled_orders) as unfulfilled_orders,\n", "      (1- sum(a.unfulfilled_orders)*1.0/sum(b.total_orders)) as fill_rate\n", "      from delivered_orders b\n", "      inner join attribution a on a.outlet_id = b.outlet_id and b.scheduled_date = a.ScheduledDate\n", "        group by 1,2\n", " \"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "\n", "data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = pb.from_sheets(\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\", \"Store info\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type[\"Outlet ID\"] = data_store_type[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type[[\"Outlet ID\", \"Operation Mode\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type = data_store_type.rename(\n", "    columns={\"Outlet ID\": \"outlet_id\", \"Operation Mode\": \"store type\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall = data.merge(data_store_type, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall[\"week_start\"] = data_store_type_overall[\"scheduleddate\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "data_store_type_overall[\"week_end\"] = data_store_type_overall[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_store_type_overall[\"week\"] = (\n", "    data_store_type_overall[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + data_store_type_overall[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select x.outlet_id,\n", "        total_qty,\n", "        y.date_ts::date as date_ts,\n", "       -- AVG(total_mp) as avg_number_of_people,\n", "       (total_qty)/sum(total_mp) as IPP\n", "from\n", "(select outlet_id,\n", "        date_ts,\n", "       count(distinct case when total_working_time>=240 then employee_id end) as total_mp\n", "from\n", "(select outlet_id,\n", "        date_ts,\n", "       employee_id,\n", "       sum(datediff('minute',start_time,end_time)) as total_working_time\n", "from\n", "(\n", "(select \n", "outlet_id,\n", "b.name as outlet_name,\n", "trim(b.location) as city_name,\n", "a.employee_id,\n", "c.name as role,\n", "(case when type in (1,2) then 'Manual'\n", "when type=3 then 'Auto' end) as Logout_type,\n", "(start_timestamp + interval '5.5 hrs') as start_time,\n", "(end_timestamp + interval '5.5 hrs') as end_time,\n", "date(start_timestamp + interval '5.5 hrs') as date_ts\n", "from lake_warehouse_location.user_attendance a\n", "left join lake_retail.warehouse_user d on a.employee_id = d.emp_id\n", "left join lake_retail.warehouse_user_role c on d.role_id = c.id\n", "inner join lake_retail.console_outlet b on a.outlet_id = b.id and business_type_id = 7\n", "--inner join (select distinct store_id , ownership from metrics.instore_manpower_performance where date(date)>=current_date-1) mp on mp.store_id = a.outlet_id \n", "where c.name not in ('Team Leader','Store Manager')\n", "and (date(start_timestamp + interval '5.5 hrs') between date('{start_date}') and date('{end_date}'))\n", "--and mp.ownership not in ('PARTNER')\n", ")\n", "union all\n", "(select cast(site_id as int) as outlet_id,\n", "b.name as outlet_name,\n", "trim(b.location) as city_name,\n", " <PERSON>.employee_id,\n", " c.name as role,\n", " (case when shift_end_method='MANUAL' then 'Manual'\n", "when shift_end_method='AUTO' then 'Auto' end) as Logout_type,\n", "(shift_start_time + interval '5.5 hrs') as start_time,\n", "(shift_end_time + interval '5.5 hrs') as end_time,\n", " date(shift_start_time + interval '5.5 hrs') as date_ts\n", "from lake_ops_management.shift a\n", "left join lake_retail.warehouse_user d on a.employee_id = d.emp_id\n", "left join lake_retail.warehouse_user_role c on d.role_id = c.id\n", "inner join lake_retail.console_outlet b on cast(a.site_id as int) = b.id and business_type_id = 7\n", "--inner join (select distinct store_id , ownership from metrics.instore_manpower_performance where date(date)>=current_date-1) mp on mp.store_id = cast(a.site_id as int) \n", "where c.name not in ('Team Leader','Store Manager')\n", "and (date(shift_start_time + interval '5.5 hrs') between date('{start_date}') and date('{end_date}'))\n", "--and mp.ownership not in ('PARTNER')\n", "--and cast(site_id as int) not in (4107,4106,4094,3953,3950,3894,3812,3750,3703,3674,3673,3667,3662,3659,3658,3641,3640,3638,3592,3573,3532,3531,3530,3515,3507,3453,3448,3386,3385,3381,3373,3260,3242,3241,3240,3235,3233,3231,3230,3175,3170,3057,3016,3014,3011,3005,2992,2991,2986,2973,2972,2971,2948,2946,2942,2939,2938,2890,2889,2887,2886,2880,2871,2824,2823,2803,2799,2798,2796,2795,2793,2775,2764,2761,2741,2739,2737,2735,2734,2702,2692,2582,2553,2552,2548,2546,2545,2452,2450,2448,2444,2443,2415,2414,2413,2399,2397,2395,2393,2368,2366,2363,2360,2359,2343,2342,2341,2337,2330,2329,2328,2327,2315,2314,2308,2307,2288,2287,2283,2282,2281,2280,2264,2244,2243,2241,2240,2239,2238,2237,2236,2216,2214,2188,2187,2172,2170,2169,2168,2149,2143,2141,2139,2137,2094,2092,2078,2076,2074,2070,2064,2060,2058,1984,1980,1978,1976,1974,1972,1970,1953,1946,1944,1942,1912,1904,1902,1900,1893,1887,1879,1877,1872,1870,1868,1866,1851,1485,1477,1473,1461,1459,1457,1453,1431,1429,1411,1399,1393,1383,1381,1190,1122,1010,995,976)\n", ")\n", ")\n", "group by 1,2,3\n", ") \n", "group by 1,2\n", "having (count(distinct case when total_working_time>=240 then employee_id end))>0\n", ") x\n", "left join\n", "(SELECT \n", "       c.id as outlet_id,\n", "       date(o.order_checkout_ts_ist) as date_ts,\n", "       sum(o.total_items_quantity_ordered) as total_qty\n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet c on c.id = o.outlet_id and business_type_id = 7\n", "--inner join (select distinct store_id , ownership from metrics.instore_manpower_performance where date(date)>=current_date-1) mp on mp.store_id = o.outlet_id \n", "WHERE \n", "  (date(o.order_checkout_ts_ist + interval '5.5 hrs') between date('{start_date}') and date('{end_date}'))\n", "and is_rescheduled=false \n", "and order_picking_completed_ts_ist is not null and \n", "o.order_type='super_express'\n", "--and mp.ownership not in ('PARTNER')\n", "--and o.outlet_id not in (4107,4106,4094,3953,3950,3894,3812,3750,3703,3674,3673,3667,3662,3659,3658,3641,3640,3638,3592,3573,3532,3531,3530,3515,3507,3453,3448,3386,3385,3381,3373,3260,3242,3241,3240,3235,3233,3231,3230,3175,3170,3057,3016,3014,3011,3005,2992,2991,2986,2973,2972,2971,2948,2946,2942,2939,2938,2890,2889,2887,2886,2880,2871,2824,2823,2803,2799,2798,2796,2795,2793,2775,2764,2761,2741,2739,2737,2735,2734,2702,2692,2582,2553,2552,2548,2546,2545,2452,2450,2448,2444,2443,2415,2414,2413,2399,2397,2395,2393,2368,2366,2363,2360,2359,2343,2342,2341,2337,2330,2329,2328,2327,2315,2314,2308,2307,2288,2287,2283,2282,2281,2280,2264,2244,2243,2241,2240,2239,2238,2237,2236,2216,2214,2188,2187,2172,2170,2169,2168,2149,2143,2141,2139,2137,2094,2092,2078,2076,2074,2070,2064,2060,2058,1984,1980,1978,1976,1974,1972,1970,1953,1946,1944,1942,1912,1904,1902,1900,1893,1887,1879,1877,1872,1870,1868,1866,1851,1485,1477,1473,1461,1459,1457,1453,1431,1429,1411,1399,1393,1383,1381,1190,1122,1010,995,976)\n", "  group by 1,2\n", "  ) y on x.outlet_id = y.outlet_id and x.date_ts = y.date_ts\n", "  where y.date_ts::date is not null\n", "  group by 1,2,3\n", "  \"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipp = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store = pb.from_sheets(\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\", \"Store info\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store[\"Outlet ID\"] = store[\"Outlet ID\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store = store[[\"Outlet ID\", \"Operation Mode\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store = store.rename(columns={\"Outlet ID\": \"outlet_id\", \"Operation Mode\": \"store type\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_type_overall = ipp.merge(store, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_type_overall[\"week_start\"] = stores_type_overall[\"date_ts\"].apply(\n", "    lambda x: x - timedelta(days=(x.weekday() + 1) % 7)\n", ")\n", "\n", "stores_type_overall[\"week_end\"] = stores_type_overall[\"week_start\"].apply(\n", "    lambda x: x + <PERSON><PERSON><PERSON>(days=6)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_type_overall[\"week\"] = (\n", "    stores_type_overall[\"week_start\"].astype(str)\n", "    + \" to \"\n", "    + stores_type_overall[\"week_end\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_type_overall_new = stores_type_overall[\n", "    [\n", "        \"outlet_id\",\n", "        \"total_qty\",\n", "        \"date_ts\",\n", "        \"ipp\",\n", "        \"store type\",\n", "        \"week_start\",\n", "        \"week_end\",\n", "        \"week\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_type_overall_new[\"store type\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stores_overall_new_new = pd.concat([stores_type_overall, stores_type_overall_new])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    stores_overall_new_new,\n", "    \"1Dcru6sErH5Up4WbSMYYYLyW8dVc2Y7uVlO5b7VF3mzQ\",\n", "    \"ipp\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_store_type_overall,\n", "    \"1Dcru6sErH5Up4WbSMYYYLyW8dVc2Y7uVlO5b7VF3mzQ\",\n", "    \"fill_rate\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_store_type_overall_new_data,\n", "    \"1Dcru6sErH5Up4WbSMYYYLyW8dVc2Y7uVlO5b7VF3mzQ\",\n", "    \"raw_data\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_store_type_overall_new_new,\n", "    \"1jramBd9n6ggohujMi0-5wNYlYVVkd_Uby_8ObG0fyBQ\",\n", "    \"data_new\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}