{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "# import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.chdir('../')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1mXTqebNUBB1ZbVcFI782hWmOCzwXe2sYbOsVMJgp8Ik\"\n", "MetaData = pb.from_sheets(sheet_id, \"Stock Take\")\n", "\n", "outlets = list(MetaData[\"Outlet ID\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GLOBAL_BASE_DIR = \"StockTakeTesting/Stock_take_Path/Stock_take_Primary\"\n", "# OUTPUT_BASE_DIR = \"StockTakeTesting/Stock_take_Path/Stock_take_Secondary\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def read_df_Primary(name):\n", "#     return pd.read_csv(\n", "#         os.path.join(GLOBAL_BASE_DIR, \"ThisCycle\", name), encoding=\"utf8\"\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def read_df_Secondary(name):\n", "#     return pd.read_csv(\n", "#         os.path.join(OUTPUT_BASE_DIR, \"ThisCycle\", name), encoding=\"utf8\"\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PrimaryData = read_df_Primary(\n", "#             \"AuditsTodayPrimary\" + str(334) + \".csv\"\n", "#         )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PrimaryData = read_df_Secondary(\n", "#             \"AuditsTodayPrimary\" + str(current_outlet) + \".csv\"\n", "#         )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PrimaryQuery = \"\"\"Select * from metrics.cyclicauditprimary_suggested\n", "where outlet_id_x =%(outlet_id)s and auditdate=current_date \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SecondaryQuery = \"\"\"Select * from metrics.cyclicauditsecondary_suggested\n", "where outlet_id_x =%(outlet_id)s and auditdate=current_date \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for current_outlet in outlets:\n", "    SheetId = MetaData[MetaData[\"Outlet ID\"] == str(current_outlet)][\"Sheet ID\"]\n", "    SheetId.reset_index(drop=True, inplace=True)\n", "    SheetId = SheetId[0]\n", "    sheet_id = SheetId\n", "    try:\n", "        PrimaryData = pd.read_sql_query(\n", "            sql=PrimaryQuery, con=redshift, params={\"outlet_id\": int(current_outlet)}\n", "        )\n", "\n", "        SecondaryData = pd.read_sql_query(\n", "            sql=SecondaryQuery, con=redshift, params={\"outlet_id\": int(current_outlet)}\n", "        )\n", "        print(str(current_outlet), PrimaryData.shape, SecondaryData.shape)\n", "        PrimaryData[\"Audit Status (Yes/No)\"] = \"\"\n", "        PrimaryData[\"Discrepancy (Quantity Mismatch/BadStock)\"] = \"\"\n", "        PrimaryData[\"Remarks\"] = \"\"\n", "        PrimaryData.drop(\"index\", axis=1, inplace=True)\n", "        SecondaryData[\"Audit Status (Yes/No)\"] = \"\"\n", "        SecondaryData[\"Discrepancy (Quantity Mismatch/BadStock)\"] = \"\"\n", "        SecondaryData[\"Remarks\"] = \"\"\n", "\n", "        pb.to_sheets(df=PrimaryData, sheetid=sheet_id, sheetname=\"Primary Cyclic\")\n", "        pb.to_sheets(df=SecondaryData, sheetid=sheet_id, sheetname=\"Secondary Cyclic\")\n", "        print(\"Done for Outlet \" + str(current_outlet))\n", "    except:\n", "        message_text = \"failed for outlet_id - \" + str(current_outlet)\n", "        print(message_text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}