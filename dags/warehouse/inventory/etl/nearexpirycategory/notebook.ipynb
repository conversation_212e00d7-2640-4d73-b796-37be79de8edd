{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from gspread.exceptions import APIError"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1FuZaOgpraarFsd9RM2MbI3bf_qfIm66mZwODZjiry2A\"\n", "data = pb.from_sheets(sheet_id, \"Overall\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"facility_name\"] = \"\"\n", "data[\"consumption\"] = \"\"\n", "data[\"consumption_per_day\"] = \"\"\n", "data[\"cpd\"] = \"\"\n", "data[\"cpd_3d\"] = \"\"\n", "data[\"doi\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.outlet_id = data.outlet_id.astype(int)\n", "data.item_id = data.item_id.astype(int)\n", "data.mrp = data.mrp.astype(int)\n", "data.manufacturer_date = pd.to_datetime(data.manufacturer_date)\n", "data.nte_in_days = data.nte_in_days.astype(int)\n", "from locale import atof, setlocale, LC_NUMERIC\n", "\n", "setlocale(LC_NUMERIC, \"\")\n", "data.value = data.value.apply(atof)\n", "data.value = data.value.astype(float)\n", "data.quantity = data.quantity.apply(atof)\n", "data.quantity = data.quantity.astype(int)\n", "data.consumption = pd.to_numeric(data.consumption, errors=\"coerce\")\n", "data.cpd = pd.to_numeric(data.cpd, errors=\"coerce\")\n", "data.pred_cpd = pd.to_numeric(data.pred_cpd, errors=\"coerce\")\n", "data.expiry_date = pd.to_datetime(data.expiry_date)\n", "data.landing_price = data.landing_price.astype(float)\n", "data.doi = pd.to_numeric(data.doi, errors=\"coerce\")\n", "data.cpd_3d = pd.to_numeric(data.cpd_3d, errors=\"coerce\")\n", "data.doi_pred = pd.to_numeric(data.doi_pred, errors=\"coerce\")\n", "data.facility_id = data.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"data_update_date\": \"table & sheet update_date\",\n", "    \"facility_identifier\": \"short identifier for facility\",\n", "    \"manufacturer_date\": \"date of manufacturing for item getting near expired\",\n", "    \"description\": \"product description & details\",\n", "    \"landing_price\": \"landing price of the item\",\n", "    \"l0\": \"primary category\",\n", "    \"l1\": \"secondary category\",\n", "    \"l2\": \"teritiary catergory\",\n", "    \"brand_name\": \"brand name of product\",\n", "    \"perc_shelf_life_remaining\": \"shelf life remaining as of update date\",\n", "    \"product_shelf_life\": \"shelf life of product\",\n", "    \"nte_in_days\": \"days to near expiry\",\n", "    \"value\": \"quantity * landing price \",\n", "    \"cpd\": \"consumption per day last 30 days\",\n", "    \"cpd_3d\": \"consumption per day last 3 days\",\n", "    \"pred_cpd\": \"predicted consumption next 30 days\",\n", "    \"doi\": \"days of inventory\",\n", "    \"doi_pred\": \"days of inventory predicted\",\n", "    \"remarks\": \"remarks added in sheet, if any\",\n", "    \"done?\": \"flag for any action taken on item\",\n", "    \"key\": \"key (ignore)\",\n", "    \"facility_id\": \"facility_id at which the item is present\",\n", "    \"flag_rtv\": \"whether item can be returned to vendor\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MetaData = data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"select * from metrics.nearexpirycategorytracker limit 10\"\"\"\n", "data1 = pd.read_sql_query(sql=qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MetaData = MetaData[data1.columns]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MetaData[\"data_update_date\"] = pd.to_datetime(MetaData[\"data_update_date\"])\n", "### TranMetaDatasformations\n", "MetaData[\"key\"] = (\n", "    MetaData[\"key\"].astype(str) + \"|\" + MetaData[\"data_update_date\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(MetaData)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"nearexpirycategorytracker\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"data_update_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Data for items soon to be near expired and actions taken on them by category and ops teams\",\n", "}\n", "pb.to_redshift(MetaData, **kwargs)\n", "\n", "print(\"Done for Outlets\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}