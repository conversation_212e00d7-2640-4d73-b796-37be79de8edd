{"cells": [{"cell_type": "code", "execution_count": null, "id": "7f7c9ecc-9685-46c6-941d-928ab4e99f47", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "3b9e9ba7-265d-4e60-9cbc-62f1058ba598", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4979877-0a41-4dc3-a838-996a9ab26142", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    \"1X8rAc78KaU-zlvZsB1MHNB7wVD5rFbYueHEY6qy_yyw\", \"Item Procured at FE\"\n", ")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "46955249-d509-4815-8c6b-92a5f3db1a33", "metadata": {}, "outputs": [], "source": ["df.columns = [\"city_id\", \"item_id\"]\n", "df = df[(df[\"city_id\"] != \"\") & (df[\"item_id\"] != \"\")]\n", "df = df.astype(int)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "050bc9b7-f05e-4a1f-9a3b-afe0483a0a1d", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select distinct r.facility_id, r.tax_location_id city_id\n", "from lake_retail.console_outlet r\n", "join lake_crates.facility f on f.id = r.facility_id\n", "where r.business_type_id in (1,7,12,19,20)\n", "and device_id != 47\n", "\"\"\"\n", "f = pd.read_sql_query(sql, presto)\n", "f.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0fd8e3e-c01b-4b75-813a-d10e15beca4d", "metadata": {}, "outputs": [], "source": ["f_item = df.merge(f, on=\"city_id\")\n", "f_item = f_item[[\"facility_id\", \"item_id\"]].drop_duplicates()\n", "f_item.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d5544aff-837a-4b42-a74c-305a6bc11595", "metadata": {}, "outputs": [], "source": ["len(f_item)"]}, {"cell_type": "code", "execution_count": null, "id": "e70f46b7-23a3-414d-949f-471604b83547", "metadata": {}, "outputs": [], "source": ["assert not f_item[\"facility_id\"].isnull().values.any()\n", "assert not f_item[\"item_id\"].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "id": "f60d1748-ab9c-4a35-8ff1-7e2c01f37070", "metadata": {}, "outputs": [], "source": ["f_item[\"updated_at\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "id": "159a38e1-7718-4a34-b485-3dad3786357b", "metadata": {}, "outputs": [], "source": ["f_item.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f76fa50b-9da0-416f-b15f-b95ce8cd12cf", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dts_elg_inputs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row entry timestamp in utc\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"item_id\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"DTS eligible facility item pairs\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5126ff35-484a-42bd-9459-3494c26c1ea7", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(f_item, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3539a3f0-734d-4f69-bbbc-419581fb0da6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}