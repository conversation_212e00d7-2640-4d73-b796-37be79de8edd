{"cells": [{"cell_type": "code", "execution_count": null, "id": "2f01fd7c-92fb-4807-99a4-f5a96b099e5c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import calendar\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "8f78fb5f-712a-44cd-971b-0fb140f28122", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "82015305-2d3e-4431-a688-a46513344d2d", "metadata": {}, "outputs": [], "source": ["top = \"\"\"\n", "(select a.outlet_id,\n", "        a.outlet_name,\n", "        a.city_name,\n", "        b.total_qty as \"Inventory Qty(T-1)\",\n", "        a.liquidation_qty as \"Liquidation Qty(T-1)\",\n", "        a.reinventorisation_qty as \"RI Qty(T-1)\",\n", "        a.prn_qty as \"Prn Qty(T-1)\",\n", "        <PERSON>.bad_inventory_qty AS \"Net Bad Stock(T-1)\"\n", "        \n", "from\n", "(select date(date_ts),\n", "       a.outlet_id,\n", "       outlet_name as outlet_name,\n", "       location as city_name,\n", "       SUM(case when inventory_type = 'Bad Inventory' then a.quantity else 0 end) as bad_inventory_qty,\n", "       SUM(case when inventory_type = 'Secondary Sales (Liquidation)' then a.quantity else 0 end) as liquidation_qty,\n", "       SUM(case when inventory_type = 'Reinventorisation' then a.quantity else 0 end) as reinventorisation_qty,\n", "       SUM(case when inventory_type = 'PRN' then a.quantity else 0 end) as prn_qty\n", "from\n", "(select item_id,pp.name as item_name, a.variant_id,(pos_timestamp+interval '5.5 hours') as date_ts,outlet_id,\n", "co.name as outlet_name,location,\n", "case    when inventory_update_type_id in (52, 56, 69) then 'Secondary Sales (Liquidation)'\n", "when inventory_update_type_id in (80,121,116,122) then 'Reinventorisation'\n", "       when inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119) then 'Bad Inventory'\n", "       when inventory_update_type_id in (20,21,65) then 'PRN' end as inventory_type,\n", "sum(\"delta\") as quantity from lake_ims.ims_inventory_log a\n", "\n", "inner join (select distinct item_id,name,variant_id from lake_rpc.product_product where active = 1 \n", ") pp on pp.variant_id = a.variant_id\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id\n", "where business_type_id=7 and\n", "inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119,20,21,65,80,121,116,122,52, 56, 69) \n", "and co.active = 1\n", "and (date(pos_timestamp+interval '5.5 hours') = current_date-1)  \n", "group by 1,2,3,4,5,6,7,8) a\n", "group by 1,2,3,4\n", ") a\n", "inner join \n", "(select co.id,\n", "       date(a.created_at),\n", "       sum(a.quantity) as total_qty\n", "from lake_reports.reports_variant_details_snapshot a\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id and business_type_id=7 and active = 1\n", "where date(a.created_at) = current_date-1\n", "      group by 1,2) b on a.outlet_id = b.id\n", "      where a.outlet_id in (926,1010,976,1072,1122,995,1190,1477,1473,1485,1459,1431,1461,1953,1976,1970,1900,1972,1469,1984,1868,1877,1399,1866,1942,1978,1944,2236,2078,1879,1904,2395,2058,2394,1872,2337,2238,1887,1429,1851,2326,1381,1825,1383,2143,2188,2237,1974,2187,2066,1946,2281,2149,2070,1912,2139,2328,2545,2060,2074,2546,1902,1703,2307,1393,2359,1988,2582,1980,1961,2452,2241,2141,2283,2288,2412,2264,2448,2314,2308,2240,2548,2343,2282,1893,2342,2239,2327,2076,1411,2741,2565,1870,2552,2092,2444,2137,2064,2702,2775,2341,2948,2761,3005,2703,2329,2793,2168,2796,2737,2397,2946,2871,2287,3230,2443,2938,2399,3012,2361,2215,2214,2939,2368,2244,3014,2415,2216,3658,2363,2170,2094,3011,3373,2942,2169,2450,2794,3260,2799,3659,2739,2890,2413,2872,3980,2242,3641,3660,2243,3453,3170,2360,3812,2795,3515,2797,3175,3448,2366,2823,3662,3381,2280,2553,3242,2798,2734,2400,2972,2887,3233,3057,3982,3386,3592,3640,3950,2824,3674,3177,3530,3532,4094,2764,2330,2414,3638,3521,3241,3750,3749,2803,3953,2167,2393,4107,2986,2991,3586,2889,3235,3531,4106,2172,3231,2992,3240,3507,3894,3526,2315,3253,3673)\n", "      order by 5 desc\n", "      limit 11)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b62ca05b-f2e1-4683-9e0d-e1a1eb212c82", "metadata": {}, "outputs": [], "source": ["data_top = pd.read_sql(top, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "90049d26-20b8-46d3-9b80-17f6203fd6b1", "metadata": {}, "outputs": [], "source": ["index = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}"]}, {"cell_type": "code", "execution_count": null, "id": "96ca3fb7-86ee-4cb6-b582-a6a28c08d2ae", "metadata": {}, "outputs": [], "source": ["data_top_new = pd.DataFrame(data_top, index)"]}, {"cell_type": "code", "execution_count": null, "id": "ea49c29c-ee61-4e4f-95a6-89b2c42d6fcb", "metadata": {}, "outputs": [], "source": ["html1 = data_top_new.to_html()"]}, {"cell_type": "code", "execution_count": null, "id": "1f4a601b-3f42-4e5e-b3f5-0f0cfdb6610b", "metadata": {}, "outputs": [], "source": ["html1"]}, {"cell_type": "code", "execution_count": null, "id": "43b0d943-c49a-4983-80b2-09916ac60687", "metadata": {}, "outputs": [], "source": ["bottom = \"\"\"\n", "(select a.outlet_id,\n", "        a.outlet_name,\n", "        a.city_name,\n", "        b.total_qty as \"Inventory Qty(T-1)\",\n", "        a.liquidation_qty as \"Liquidation Qty(T-1)\",\n", "        a.reinventorisation_qty as \"RI Qty(T-1)\",\n", "        a.prn_qty as \"Prn Qty(T-1)\",\n", "        <PERSON>.bad_inventory_qty AS \"Net Bad Stock(T-1)\"\n", "from\n", "(select date(date_ts),\n", "       a.outlet_id,\n", "       outlet_name as outlet_name,\n", "       location as city_name,\n", "       SUM(case when inventory_type = 'Bad Inventory' then a.quantity else 0 end) as bad_inventory_qty,\n", "       SUM(case when inventory_type = 'Secondary Sales (Liquidation)' then a.quantity else 0 end) as liquidation_qty,\n", "       SUM(case when inventory_type = 'Reinventorisation' then a.quantity else 0 end) as reinventorisation_qty,\n", "       SUM(case when inventory_type = 'PRN' then a.quantity else 0 end) as prn_qty\n", "from\n", "(select item_id,pp.name as item_name, a.variant_id,(pos_timestamp+interval '5.5 hours') as date_ts,outlet_id,\n", "co.name as outlet_name,location,\n", "case    when inventory_update_type_id in (52, 56, 69) then 'Secondary Sales (Liquidation)'\n", "when inventory_update_type_id in (80,121,116,122) then 'Reinventorisation'\n", "       when inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119) then 'Bad Inventory'\n", "       when inventory_update_type_id in (20,21,65) then 'PRN' end as inventory_type,\n", "sum(\"delta\") as quantity from lake_ims.ims_inventory_log a\n", "\n", "inner join (select distinct item_id,name,variant_id from lake_rpc.product_product where active = 1 \n", ") pp on pp.variant_id = a.variant_id\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id\n", "where business_type_id=7 and\n", "inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119,20,21,65,80,121,116,122,52, 56, 69) \n", "and co.active = 1\n", "and (date(pos_timestamp+interval '5.5 hours') = current_date-1)  \n", "group by 1,2,3,4,5,6,7,8) a\n", "group by 1,2,3,4\n", ") a\n", "inner join \n", "(select co.id,\n", "       date(a.created_at),\n", "       sum(a.quantity) as total_qty\n", "from lake_reports.reports_variant_details_snapshot a\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id and business_type_id=7 and active = 1\n", "where date(a.created_at) = current_date-1\n", "      group by 1,2) b on a.outlet_id = b.id\n", "      where a.outlet_id in (926,1010,976,1072,1122,995,1190,1477,1473,1485,1459,1431,1461,1953,1976,1970,1900,1972,1469,1984,1868,1877,1399,1866,1942,1978,1944,2236,2078,1879,1904,2395,2058,2394,1872,2337,2238,1887,1429,1851,2326,1381,1825,1383,2143,2188,2237,1974,2187,2066,1946,2281,2149,2070,1912,2139,2328,2545,2060,2074,2546,1902,1703,2307,1393,2359,1988,2582,1980,1961,2452,2241,2141,2283,2288,2412,2264,2448,2314,2308,2240,2548,2343,2282,1893,2342,2239,2327,2076,1411,2741,2565,1870,2552,2092,2444,2137,2064,2702,2775,2341,2948,2761,3005,2703,2329,2793,2168,2796,2737,2397,2946,2871,2287,3230,2443,2938,2399,3012,2361,2215,2214,2939,2368,2244,3014,2415,2216,3658,2363,2170,2094,3011,3373,2942,2169,2450,2794,3260,2799,3659,2739,2890,2413,2872,3980,2242,3641,3660,2243,3453,3170,2360,3812,2795,3515,2797,3175,3448,2366,2823,3662,3381,2280,2553,3242,2798,2734,2400,2972,2887,3233,3057,3982,3386,3592,3640,3950,2824,3674,3177,3530,3532,4094,2764,2330,2414,3638,3521,3241,3750,3749,2803,3953,2167,2393,4107,2986,2991,3586,2889,3235,3531,4106,2172,3231,2992,3240,3507,3894,3526,2315,3253,3673)\n", "      order by 5 asc\n", "      limit 11)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ce8ceca8-0dcf-4e3c-b9f4-77cc5dd53239", "metadata": {}, "outputs": [], "source": ["data_bottom = pd.read_sql(bottom, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "9c983e6a-ccf5-4f1d-941f-831638fa4983", "metadata": {}, "outputs": [], "source": ["index = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}"]}, {"cell_type": "code", "execution_count": null, "id": "dbe351e8-d195-4ea4-8e20-b4a98bb48ca7", "metadata": {}, "outputs": [], "source": ["data_bottom_new = pd.DataFrame(data_bottom, index)"]}, {"cell_type": "code", "execution_count": null, "id": "a0f2ac84-79c3-4837-aa56-a828b73be2ca", "metadata": {}, "outputs": [], "source": ["data_bottom_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8d685e11-8c28-4af9-80cb-76dd6d1eee91", "metadata": {}, "outputs": [], "source": ["html2 = data_bottom_new.to_html()"]}, {"cell_type": "code", "execution_count": null, "id": "56f6d6eb-3fe6-401b-b4f2-1902c445eda6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "073702a9-4d4e-4285-91ae-5553e6b7c16e", "metadata": {}, "outputs": [], "source": ["all_stores = \"\"\"\n", "(select a.outlet_id,\n", "        a.outlet_name,\n", "        a.city_name,\n", "        b.total_qty as \"Inventory Qty(T-1)\",\n", "        a.liquidation_qty as \"Liquidation Qty(T-1)\",\n", "        a.reinventorisation_qty as \"RI Qty(T-1)\",\n", "        a.prn_qty as \"Prn Qty(T-1)\",\n", "        <PERSON>.bad_inventory_qty AS \"Net Bad Stock(T-1)\"\n", "from\n", "(select date(date_ts),\n", "       a.outlet_id,\n", "       outlet_name as outlet_name,\n", "       location as city_name,\n", "       SUM(case when inventory_type = 'Bad Inventory' then a.quantity else 0 end) as bad_inventory_qty,\n", "       SUM(case when inventory_type = 'Secondary Sales (Liquidation)' then a.quantity else 0 end) as liquidation_qty,\n", "       SUM(case when inventory_type = 'Reinventorisation' then a.quantity else 0 end) as reinventorisation_qty,\n", "       SUM(case when inventory_type = 'PRN' then a.quantity else 0 end) as prn_qty\n", "from\n", "(select item_id,pp.name as item_name, a.variant_id,(pos_timestamp+interval '5.5 hours') as date_ts,outlet_id,\n", "co.name as outlet_name,location,\n", "case    when inventory_update_type_id in (52, 56, 69) then 'Secondary Sales (Liquidation)'\n", "when inventory_update_type_id in (80,121,116,122) then 'Reinventorisation'\n", "       when inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119) then 'Bad Inventory'\n", "       when inventory_update_type_id in (20,21,65) then 'PRN' end as inventory_type,\n", "sum(\"delta\") as quantity from lake_ims.ims_inventory_log a\n", "\n", "inner join (select distinct item_id,name,variant_id from lake_rpc.product_product where active = 1 \n", ") pp on pp.variant_id = a.variant_id\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id\n", "where business_type_id=7 and\n", "inventory_update_type_id in (7,9,11,12,13,24,25,33,34,36,37,63,64,66,67,68,87,88,89,105,106,107,119,20,21,65,80,121,116,122,52, 56, 69) \n", "and co.active = 1\n", "and (date(pos_timestamp+interval '5.5 hours') = current_date-1)  \n", "group by 1,2,3,4,5,6,7,8) a\n", "group by 1,2,3,4\n", ") a\n", "inner join \n", "(select co.id,\n", "       date(a.created_at),\n", "       sum(a.quantity) as total_qty\n", "from lake_reports.reports_variant_details_snapshot a\n", "inner join lake_retail.console_outlet co on co.id = a.outlet_id and business_type_id=7 and active = 1\n", "where date(a.created_at) = current_date-1\n", "      group by 1,2) b on a.outlet_id = b.id\n", "      where a.outlet_id in (926,1010,976,1072,1122,995,1190,1477,1473,1485,1459,1431,1461,1953,1976,1970,1900,1972,1469,1984,1868,1877,1399,1866,1942,1978,1944,2236,2078,1879,1904,2395,2058,2394,1872,2337,2238,1887,1429,1851,2326,1381,1825,1383,2143,2188,2237,1974,2187,2066,1946,2281,2149,2070,1912,2139,2328,2545,2060,2074,2546,1902,1703,2307,1393,2359,1988,2582,1980,1961,2452,2241,2141,2283,2288,2412,2264,2448,2314,2308,2240,2548,2343,2282,1893,2342,2239,2327,2076,1411,2741,2565,1870,2552,2092,2444,2137,2064,2702,2775,2341,2948,2761,3005,2703,2329,2793,2168,2796,2737,2397,2946,2871,2287,3230,2443,2938,2399,3012,2361,2215,2214,2939,2368,2244,3014,2415,2216,3658,2363,2170,2094,3011,3373,2942,2169,2450,2794,3260,2799,3659,2739,2890,2413,2872,3980,2242,3641,3660,2243,3453,3170,2360,3812,2795,3515,2797,3175,3448,2366,2823,3662,3381,2280,2553,3242,2798,2734,2400,2972,2887,3233,3057,3982,3386,3592,3640,3950,2824,3674,3177,3530,3532,4094,2764,2330,2414,3638,3521,3241,3750,3749,2803,3953,2167,2393,4107,2986,2991,3586,2889,3235,3531,4106,2172,3231,2992,3240,3507,3894,3526,2315,3253,3673)\n", "      order by 5 desc\n", ")\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "eb919a0e-a14b-4d1e-8b28-dba5a7ad9d5b", "metadata": {}, "outputs": [], "source": ["all_stores_data = pd.read_sql(all_stores, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "7fc3bad1-7b66-4ed1-94cc-28356248b109", "metadata": {}, "outputs": [], "source": ["all_stores_data.to_csv(\"all_stores_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4c0bfae5-b031-4288-bcd3-5ee5ed6c2458", "metadata": {}, "outputs": [], "source": ["message1 = f\"\"\"<p>Hello &nbsp;Everyone,</p>\n", "<p>Please find below the details regarding net bad stock marked at top 10 and bottom 10 DS for yesterday.</p>\n", "<p><strong>TOP 10</strong><br><br></p>\n", "<p>{html1}</p>\n", "<p><strong>Bottom 10</strong></p>\n", "<p>{html2}</p>\n", "<p><PERSON><PERSON>,</p>\n", "<p>Blinkit Merchant Support</p>\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "08653c7e-8648-48b5-a182-618708e448a1", "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"jak<PERSON><PERSON>@gipltp.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"ayan.b<PERSON><PERSON><PERSON>@gipltp.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>.\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"Net Bad Stock Level at Merchant DS\"\n", "html_content = message1\n", "pb.send_email(\n", "    from_email, to_email, subject, html_content, files=[\"all_stores_data.csv\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0fa36082-5722-4c24-a5c9-77c8aa5d6fae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ebbe2615-9455-41aa-b355-51f40020e57b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5336da15-f0fb-4642-8d9b-4cb34384d93e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a20b4fc-636f-4b3e-8328-282b3fb2c803", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eb61d3c7-d21d-4a6a-9c07-f0169526f96c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "01b71694-0355-4274-b80c-1e683e8d33e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1393d54-0004-4de7-b914-ebbe299f0778", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c326bc38-dcf7-4f28-af50-72fe58badc51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc17b912-89e8-41a3-b95f-1d0c8fd5b653", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f533235-5294-4f15-8cf4-32cb4081cd82", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6806ade4-0b1d-4175-a527-fe1c07ea72f0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d97d169-1dcf-4ca1-8ce9-90417605b6d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48f174fa-1e5a-409c-ba4a-a2716ba0dffd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "90722a1d-c7ff-4690-9e9e-f57df16127b1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3785032-528e-4de7-84fb-792d67bc2e2c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "36cfb140-55ac-4384-94b4-b7ec44568bd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8dac11d8-be27-478f-8562-182181d003bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fdeb6ff-88c8-4687-9794-b8dad308ab8a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab491adc-ecef-41a4-913a-7f2d95701182", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf269638-2210-4923-8fd4-92bd92524747", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a342cf1-81f7-4dd1-a562-7b172f5a45a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}