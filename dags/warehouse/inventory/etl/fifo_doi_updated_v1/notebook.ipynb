{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "Redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "Retail = pb.get_connection(\"retail\")\n", "\n", "import sys\n", "import warnings\n", "\n", "if not sys.warnoptions:\n", "    warnings.simplefilter(\"ignore\")\n", "\n", "from datetime import timedelta, date\n", "\n", "\n", "def daterange(date1, date2):\n", "    for n in range(int((date2 - date1).days) + 1):\n", "        yield date1 + <PERSON><PERSON><PERSON>(n)\n", "\n", "\n", "import datetime\n", "\n", "datelist = []\n", "start_dt = date(2021, 4, 18)\n", "end_dt = date(2021, 4, 20)\n", "# Last_Date =  date(2020, 11, 2)\n", "Last_Date = datetime.datetime.today() - datetime.timedelta(days=1)\n", "for dt in daterange(Last_Date, Last_Date):\n", "    datelist.append(dt.strftime(\"%Y-%m-%d\"))\n", "\n", "\n", "datelist"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Total Expired/Near Expiry Items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for date in datelist:\n", "    Query = f\"\"\"With Expired_Base as (\n", "               SELECT DISTINCT \n", "                             iil.inventory_update_id, \n", "                             pp.item_id,\n", "                             pp.variant_id,\n", "                             pp.name as item_name,\n", "                             pp.variant_mrp variant_mrp,\n", "                             (iil.\"delta\"*pp.variant_mrp) as item_loss,\n", "                             date(convert_timezone('Asia/Kolkata', pos_timestamp)) inventory_update_date,\n", "                             date(convert_timezone('Asia/Kolkata', pos_timestamp)) inventory_update_ts,\n", "                             co.id as outlet_id,\n", "                             co.name  as outlet_name, \n", "                             co.facility_id, \n", "                             co.tax_location_id as city_id,\n", "                             iut.id as update_id,\n", "                             iut.name as update_name,\n", "                             iil.\"delta\",\n", "                             iut.is_bad_inventory_update,\n", "                             iut.group_id,\n", "                             iil.created_by,\n", "                             iil.transaction_lp,\n", "                             iil.weighted_lp,\n", "                             ibur.name as update_reason \n", "             FROM lake_ims.ims_inventory_log iil\n", "             INNER JOIN lake_retail.console_outlet co ON co.id = iil.outlet_id\n", "             INNER JOIN lake_rpc.product_product pp ON pp.variant_id = iil.variant_id\n", "             INNER JOIN lake_ims.ims_inventory_update_type iut ON iut.id = iil.inventory_update_type_id\n", "             AND (iut.is_bad_inventory_update = 1 OR iut.group_id in (6,12,1))\n", "             LEFT JOIN lake_ims.ims_bad_inventory_update_log ibil on iil.inventory_update_id=ibil.inventory_update_id\n", "             LEFT join lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", "             WHERE  date(convert_timezone('Asia/Kolkata', pos_timestamp))='{date}'\n", "               AND pp.active = 1\n", "               ),\n", "    Expired2 as \n", "    (Select item_id,\n", "           variant_id,\n", "           outlet_id,\n", "           outlet_name,\n", "           facility_id,\n", "           update_id,\n", "           update_name,\n", "           inventory_update_date::date,\n", "           sum(\"delta\") as Qty\n", "    From Expired_Base  where (update_name in ('bad_stock_near_expiry_product','bad_stock_expired_product')\n", "    or update_reason in ('STO Expiry','STO Near Expiry')\n", "    )\n", "    Group by 1,2,3,4,5,6,7,8\n", "    )\n", "    Select exp.*,pp.variant_mrp from Expired2 exp left join lake_rpc.product_product pp on pp.variant_id=exp.variant_id\"\"\"\n", "\n", "    Expired_Items = pd.read_sql_query(sql=Query, con=Redshift)\n", "\n", "    print(\"Input:\", Expired_Items.qty.sum())\n", "\n", "    ### Current Inventory\n", "    Query = f\"\"\"select outlet_id,\n", "           item_id,\n", "           snapshot_datetime::date as date,\n", "           sum(actual_quantity) - sum(blocked_quantity) as available_quantity\n", "    from lake_reports.reports_item_inventory_snapshot\n", "    where snapshot_datetime::date = '{date}'\n", "    group by 1,2,3\"\"\"\n", "\n", "    Current_Inventory = pd.read_sql_query(sql=Query, con=Redshift)\n", "\n", "    ## GRN Log\n", "    Query = \"\"\"With grn_base as (\n", "select grn.*, stk.manufacture_date, stk.expiry_date from lake_po.po_grn grn\n", "left join lake_ims.ims_inventory_stock_details\n", "stk on stk.inventory_update_id=grn.inventory_update_id\n", "where extract( month from grn.created_at::date) in (1,2,3,4,5,6,7,8,9,10,11,12) and extract( Year from grn.created_at::date) in ('2021','2020','2018','2019')\n", "),\n", "\n", "RPC_Shelf_Life as (\n", "select \n", "      item_id,\n", "      variant_id,\n", "      shelf_life,\n", "      outward_guidelines\n", "from lake_rpc.product_product\n", ")\n", "\n", "Select item_id,\n", "       outlet_id,\n", "       variant_id,\n", "       grn_id,\n", "       quantity,\n", "       po_id,\n", "       created_at::date as grn_date,\n", "       case when manufacture_date is null then created_at::date \n", "           else manufacture_date::date end as manufacture_date,\n", "       shelf_life,\n", "       New_shelf_Life,\n", "       outward_guidelines,\n", "       shelf_life - outward_guidelines-datediff('day',manufacture_date,created_at)::int as Remaining_days_till_NTE,\n", "       dateadd('day',(shelf_life - outward_guidelines)::int,manufacture_date)::date as NTE_Date\n", "From (       \n", "Select grn.item_id,\n", "       grn.variant_id,\n", "       grn.grn_id,\n", "       grn.outlet_id,\n", "       grn.quantity,\n", "       grn.po_id,\n", "       grn.created_at::date,\n", "       case when grn.manufacture_date::date is not null then grn.manufacture_date::date\n", "                 else dateadd('day',-rpc.shelf_life,expiry_date::date) end as manufacture_date,\n", "       rpc.shelf_life,\n", "       rpc.shelf_life-round(rpc.shelf_life*.12,0) as New_shelf_Life,\n", "       rpc.outward_guidelines\n", "from  grn_base grn left join  RPC_Shelf_Life rpc on rpc.item_id=grn.item_id and rpc.variant_id=grn.variant_id) \n", "\"\"\"\n", "\n", "    GRN_Log = pd.read_sql_query(sql=Query, con=Redshift)\n", "    GRN_Log.loc[\n", "        (GRN_Log.nte_date > pd.Timestamp.max.date()), \"nte_date\"\n", "    ] = pd.Timestamp.max.date()\n", "    GRN_Log[\"nte_date\"] = pd.to_datetime(GRN_Log[\"nte_date\"])\n", "\n", "    #     GRN_Log = pd.read_csv(\"GRN_Log.csv\")\n", "\n", "    ### ESTO Log\n", "\n", "    Query = \"\"\"With estobase as (select esto.*,\n", "       il.inventory_update_id,\n", "       stk.manufacture_date, \n", "       stk.expiry_date\n", "from metrics.esto_details esto\n", "left join (select ilg.*,pp.item_id from lake_ims.ims_inventory_log ilg inner join lake_rpc.product_product pp \n", "on pp.variant_id=ilg.variant_id and ilg.inventory_update_type_id in (1,28,76)\n", ") il on il.grofers_order_id = esto.sto_id and il.item_id=esto.item_id \n", "and il.outlet_id=esto.receiving_outlet_id\n", "left join lake_ims.ims_inventory_stock_details stk on stk.inventory_update_id=il.inventory_update_id \n", "where extract( month from grn_created_at::date) in (1,2,3,4,5,6,7,8,9,10,11,12) and extract( Year from grn_created_at::date) in ('2021','2020','2019','2018')\n", "),\n", "RPC_Shelf_Life as (\n", "select \n", "  item_id,\n", "  max(shelf_life) as shelf_life,\n", "  max(outward_guidelines) as outward_guidelines\n", "from lake_rpc.product_product group by 1\n", ")\n", "\n", "Select item_id,\n", "   outlet_id,\n", "   sto_id,\n", "   quantity,\n", "   created_at as grn_date,\n", "   case when manufacture_date is null then created_at::date \n", "   else manufacture_date::date end as manufacture_date,\n", "   shelf_life,\n", "   New_shelf_Life,\n", "   outward_guidelines,\n", "   case when manufacture_date is not null then (shelf_life - outward_guidelines-datediff('day',manufacture_date,created_at)::int)\n", "   else (New_shelf_Life - outward_guidelines) end as Remaining_days_till_NTE,\n", "   case when manufacture_date is not null then (dateadd('day',(shelf_life - outward_guidelines)::int,manufacture_date)::date)\n", "   else (dateadd('day',(New_shelf_Life - outward_guidelines)::int,created_at::date)::date) end as NTE_Date\n", "From (       \n", "Select sto.item_id,\n", "   sto.sto_id as sto_id,\n", "   sto.receiving_outlet_id as outlet_id,\n", "   sto.inward_quantity as quantity,\n", "   sto.grn_created_at::date as created_at,\n", "   case when sto.manufacture_date::date is not null then sto.manufacture_date::date\n", "                 else dateadd('day',-rpc.shelf_life,expiry_date::date) end as manufacture_date,\n", "   rpc.shelf_life,\n", "   rpc.shelf_life-round(rpc.shelf_life*.30,0) as New_shelf_Life,\n", "   rpc.outward_guidelines\n", "from  estobase sto left join  RPC_Shelf_Life rpc on rpc.item_id=sto.item_id ) \n", "    \"\"\"\n", "\n", "    STO_Log = pd.read_sql_query(sql=Query, con=Redshift)\n", "    STO_Log.loc[\n", "        (STO_Log.nte_date > pd.Timestamp.max.date()), \"nte_date\"\n", "    ] = pd.Timestamp.max.date()\n", "    STO_Log[\"nte_date\"] = pd.to_datetime(STO_Log[\"nte_date\"])\n", "\n", "    #     STO_Log=pd.read_csv(\"STO_Log.csv\")\n", "\n", "    ## GRN Operations\n", "\n", "    Current_Inventory[\"item_id\"] = Current_Inventory[\"item_id\"].astype(int)\n", "\n", "    Expired_Items_Merged = Expired_Items.merge(\n", "        Current_Inventory,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\", \"inventory_update_date\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"date\"],\n", "    )\n", "\n", "    Expired_Items_Merged.rename(columns={\"qty\": \"ExpiredQty\"}, inplace=True)\n", "\n", "    Expired_Items_Merged.fillna(0, inplace=True)\n", "\n", "    Expired_Items_Merged[\"TotalQty\"] = (\n", "        np.where(\n", "            Expired_Items_Merged[\"available_quantity\"] < 0,\n", "            0,\n", "            Expired_Items_Merged[\"available_quantity\"],\n", "        )\n", "        + Expired_Items_Merged[\"ExpiredQty\"]\n", "    )\n", "\n", "    Expired_Items_Merged2 = Expired_Items_Merged[\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"update_name\",\n", "            \"variant_id\",\n", "            \"inventory_update_date\",\n", "            \"ExpiredQty\",\n", "            \"TotalQty\",\n", "        ]\n", "    ]\n", "\n", "    warehouse_outlet_mapping = \"\"\"Select \n", "                                        distinct\n", "                                        cloud_store_id as outlet_id,\n", "                                        warehouse_id as HOT_Outlet_id\n", "                                        from lake_retail.warehouse_outlet_mapping\n", "    \"\"\"\n", "\n", "    Mapping = pd.read_sql_query(sql=warehouse_outlet_mapping, con=Redshift)\n", "\n", "    Mapping.dropna(inplace=True)\n", "\n", "    GRN_Log2 = GRN_Log.merge(\n", "        Mapping, left_on=\"outlet_id\", right_on=\"hot_outlet_id\", how=\"left\"\n", "    )\n", "\n", "    GRN_Log2.dropna(inplace=True)\n", "\n", "    Expired_Items_Merged3 = Expired_Items_Merged2.merge(\n", "        GRN_Log2,\n", "        left_on=[\"item_id\", \"outlet_id\", \"variant_id\"],\n", "        right_on=[\"item_id\", \"outlet_id_y\", \"variant_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    Expired_Items_Merged3_GRN = Expired_Items_Merged3[\n", "        ~Expired_Items_Merged3.grn_id.isnull()\n", "    ]\n", "\n", "    Expired_Items_Merged3_STO1 = Expired_Items_Merged3[\n", "        Expired_Items_Merged3.grn_id.isnull()\n", "    ][\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"update_name\",\n", "            \"variant_id\",\n", "            \"inventory_update_date\",\n", "            \"ExpiredQty\",\n", "            \"TotalQty\",\n", "        ]\n", "    ]\n", "    #### GRN Operations\n", "\n", "    Expired_Items_Merged4 = Expired_Items_Merged3_GRN.copy()\n", "\n", "    Expired_Items_Merged4.sort_values(\n", "        [\"outlet_id\", \"item_id\", \"update_name\", \"grn_date\"],\n", "        ascending=[True, True, True, False],\n", "        inplace=True,\n", "    )\n", "\n", "    Expired_Items_Merged4[\"Cumsum1\"] = Expired_Items_Merged4.groupby(\n", "        [\"item_id\", \"outlet_id\", \"update_name\", \"variant_id\", \"inventory_update_date\"],\n", "        as_index=False,\n", "    )[\"quantity\"].transform(pd.Series.cumsum)\n", "\n", "    Expired_Items_Merged4.fillna(0, inplace=True)\n", "\n", "    Expired_Items_Merged4.inventory_update_date.unique()\n", "\n", "    Expired_Items_Merged4.groupby(\n", "        [\"item_id\", \"outlet_id\", \"update_name\", \"variant_id\", \"inventory_update_date\"],\n", "        as_index=False,\n", "    )[\"ExpiredQty\"].max()[\"ExpiredQty\"].sum()\n", "\n", "    Expired_Items_Merged4.drop([\"outlet_id_x\", \"outlet_id_y\"], axis=1, inplace=True)\n", "\n", "    Expired_Items_Merged5 = Expired_Items_Merged4[\n", "        Expired_Items_Merged4[\"Cumsum1\"] >= Expired_Items_Merged4[\"TotalQty\"]\n", "    ]\n", "\n", "    Expired_Items_Merged6 = Expired_Items_Merged4.merge(\n", "        Expired_Items_Merged5.groupby(\n", "            [\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            as_index=False,\n", "        )[\"Cumsum1\"].min(),\n", "        on=[\n", "            \"item_id\",\n", "            \"outlet_id\",\n", "            \"update_name\",\n", "            \"variant_id\",\n", "            \"inventory_update_date\",\n", "        ],\n", "        how=\"left\",\n", "    )\n", "\n", "    Expired_Items_Merged6.rename(\n", "        columns={\"Cumsum1_x\": \"Cumsum1\", \"Cumsum1_y\": \"MinQty1\"}, inplace=True\n", "    )\n", "\n", "    ## To tackle items where total expired qty is more than total GRN Qty e.g. item_id=10061224 on November 3\n", "    Expired_Items_Merged6[\"ExceptionFlag\"] = np.where(\n", "        Expired_Items_Merged6[\"MinQty1\"].isnull(), 1, 0\n", "    )\n", "\n", "    # Expired_Items_Merged6['MinQty1'] = np.where(Expired_Items_Merged6['MinQty1'].isnull(),Expired_Items_Merged6['ExpiredQty'],Expired_Items_Merged6['MinQty1'])\n", "\n", "    Expired_Items_Merged6 = Expired_Items_Merged6[\n", "        (Expired_Items_Merged6[\"Cumsum1\"] <= Expired_Items_Merged6[\"MinQty1\"])\n", "        | (Expired_Items_Merged6[\"ExceptionFlag\"] == 1)\n", "    ]\n", "\n", "    Expired_Items_Merged6.sort_values(\n", "        [\"outlet_id\", \"item_id\", \"grn_date\"], ascending=[True, True, True], inplace=True\n", "    )\n", "\n", "    Expired_Items_Merged6[\"Cumsum2\"] = Expired_Items_Merged6.groupby(\n", "        [\"item_id\", \"outlet_id\", \"update_name\", \"variant_id\", \"inventory_update_date\"]\n", "    )[\"quantity\"].transform(pd.Series.cumsum)\n", "\n", "    Expired_Items_Merged7 = Expired_Items_Merged6[\n", "        (Expired_Items_Merged6[\"Cumsum2\"] >= Expired_Items_Merged6[\"ExpiredQty\"])\n", "        | (Expired_Items_Merged6[\"ExceptionFlag\"] == 1)\n", "    ]\n", "\n", "    Expired_Items_Merged8 = Expired_Items_Merged6.merge(\n", "        Expired_Items_Merged7.groupby(\n", "            [\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            as_index=False,\n", "        )[\"Cumsum2\"].min(),\n", "        on=[\n", "            \"item_id\",\n", "            \"outlet_id\",\n", "            \"update_name\",\n", "            \"variant_id\",\n", "            \"inventory_update_date\",\n", "        ],\n", "        how=\"left\",\n", "    )\n", "\n", "    Expired_Items_Merged8.rename(\n", "        columns={\"Cumsum2_x\": \"Cumsum2\", \"Cumsum2_y\": \"MinQty2\"}, inplace=True\n", "    )\n", "\n", "    Expired_Items_Merged8 = Expired_Items_Merged8[\n", "        (Expired_Items_Merged8[\"Cumsum2\"] <= Expired_Items_Merged8[\"MinQty2\"])\n", "        | (Expired_Items_Merged8[\"ExceptionFlag\"] == 1)\n", "    ]\n", "\n", "    Expired_Items_Merged8[\"ShiftQty\"] = Expired_Items_Merged8.groupby(\n", "        [\"outlet_id\", \"item_id\", \"update_name\", \"variant_id\", \"inventory_update_date\"]\n", "    )[\"Cumsum2\"].shift(1)\n", "\n", "    Expired_Items_Merged8.fillna(0, inplace=True)\n", "\n", "    Expired_Items_Merged8[\"Expired_GRN\"] = np.where(\n", "        Expired_Items_Merged8[\"ExpiredQty\"] >= Expired_Items_Merged8[\"Cumsum2\"],\n", "        Expired_Items_Merged8[\"quantity\"],\n", "        np.where(\n", "            Expired_Items_Merged8[\"ExceptionFlag\"] == 1,\n", "            Expired_Items_Merged8[\"ExpiredQty\"],\n", "            Expired_Items_Merged8[\"ExpiredQty\"] - Expired_Items_Merged8[\"ShiftQty\"],\n", "        ),\n", "    )\n", "\n", "    Expired_Items_Merged8[\"inventory_update_date\"] = Expired_Items_Merged8[\n", "        \"inventory_update_date\"\n", "    ].astype(\"datetime64[ns]\")\n", "\n", "    nte_date = pd.to_datetime(Expired_Items_Merged8.nte_date)\n", "\n", "    Expired_Items_Merged8[\"FIFO/DOI\"] = np.where(\n", "        Expired_Items_Merged8[\"inventory_update_date\"]\n", "        >= Expired_Items_Merged8[\"nte_date\"],\n", "        \"DOI\",\n", "        \"FIFO\",\n", "    )\n", "\n", "    Expired_Items_Merged8 = Expired_Items_Merged8[\n", "        Expired_Items_Merged8[\"Expired_GRN\"] > 0\n", "    ]\n", "\n", "    # Expired_Items_Merged8.drop('outlet_id_y', axis=1, inplace=True)\n", "\n", "    Expired_Items_Merged_GRN = Expired_Items_Merged8\n", "\n", "    #     print(Expired_Items_Merged_GRN.Expired_GRN.sum())\n", "\n", "    #     print(Expired_Items_Merged3_STO1.ExpiredQty.sum())\n", "\n", "    ## STO Data\n", "\n", "    Expired_Items_Merged3_STO = Expired_Items_Merged3_STO1.merge(\n", "        STO_Log,\n", "        left_on=[\"item_id\", \"outlet_id\"],\n", "        right_on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    Expired_Items_Merged3_None = Expired_Items_Merged3_STO[\n", "        Expired_Items_Merged3_STO.sto_id.isnull()\n", "    ][\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"update_name\",\n", "            \"variant_id\",\n", "            \"inventory_update_date\",\n", "            \"ExpiredQty\",\n", "            \"TotalQty\",\n", "            \"nte_date\",\n", "            \"manufacture_date\",\n", "            \"shelf_life\",\n", "            \"outward_guidelines\",\n", "        ]\n", "    ]\n", "\n", "    Expired_Items_Merged3_STO = Expired_Items_Merged3_STO[\n", "        ~Expired_Items_Merged3_STO.sto_id.isnull()\n", "    ]\n", "\n", "    #     print(Expired_Items_Merged3_None.ExpiredQty.sum())\n", "\n", "    try:\n", "\n", "        Expired_Items_Merged4 = Expired_Items_Merged3_STO.copy()\n", "\n", "        Expired_Items_Merged4.sort_values(\n", "            [\"outlet_id\", \"item_id\", \"update_name\", \"grn_date\"],\n", "            ascending=[True, True, True, False],\n", "            inplace=True,\n", "        )\n", "\n", "        Expired_Items_Merged4[\"Cumsum1\"] = Expired_Items_Merged4.groupby(\n", "            [\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            as_index=False,\n", "        )[\"quantity\"].transform(pd.Series.cumsum)\n", "\n", "        Expired_Items_Merged4.fillna(0, inplace=True)\n", "\n", "        Expired_Items_Merged4.inventory_update_date.unique()\n", "\n", "        Expired_Items_Merged4.groupby(\n", "            [\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            as_index=False,\n", "        )[\"ExpiredQty\"].max()[\"ExpiredQty\"].sum()\n", "\n", "        # # # Expired_Items_Merged4.drop(['outlet_id_x','outlet_id_y'], axis=1, inplace=True)\n", "\n", "        # ## Item_ID==10024670 has quantity< expired qty and hence getting removed also where quantity is 0 is getting removed\n", "\n", "        Expired_Items_Merged5 = Expired_Items_Merged4[\n", "            Expired_Items_Merged4[\"Cumsum1\"] >= Expired_Items_Merged4[\"TotalQty\"]\n", "        ]\n", "\n", "        Expired_Items_Merged6 = Expired_Items_Merged4.merge(\n", "            Expired_Items_Merged5.groupby(\n", "                [\n", "                    \"item_id\",\n", "                    \"outlet_id\",\n", "                    \"update_name\",\n", "                    \"variant_id\",\n", "                    \"inventory_update_date\",\n", "                ],\n", "                as_index=False,\n", "            )[\"Cumsum1\"].min(),\n", "            on=[\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            how=\"left\",\n", "        )\n", "\n", "        Expired_Items_Merged6.rename(\n", "            columns={\"Cumsum1_x\": \"Cumsum1\", \"Cumsum1_y\": \"MinQty1\"}, inplace=True\n", "        )\n", "\n", "        # ## To tackle items where total expired qty is more than total GRN Qty e.g. item_id=10061224 on November 3\n", "        Expired_Items_Merged6[\"ExceptionFlag\"] = np.where(\n", "            Expired_Items_Merged6[\"MinQty1\"].isnull(), 1, 0\n", "        )\n", "\n", "        Expired_Items_Merged6[\"MinQty1\"] = np.where(\n", "            Expired_Items_Merged6[\"MinQty1\"].isnull(),\n", "            Expired_Items_Merged6[\"ExpiredQty\"],\n", "            Expired_Items_Merged6[\"MinQty1\"],\n", "        )\n", "\n", "        Expired_Items_Merged6 = Expired_Items_Merged6[\n", "            (Expired_Items_Merged6[\"Cumsum1\"] <= Expired_Items_Merged6[\"MinQty1\"])\n", "            | (Expired_Items_Merged6[\"ExceptionFlag\"] == 1)\n", "        ]\n", "\n", "        Expired_Items_Merged6.sort_values(\n", "            [\"outlet_id\", \"item_id\", \"grn_date\"],\n", "            ascending=[True, True, True],\n", "            inplace=True,\n", "        )\n", "\n", "        Expired_Items_Merged6[\"Cumsum2\"] = Expired_Items_Merged6.groupby(\n", "            [\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ]\n", "        )[\"quantity\"].transform(pd.Series.cumsum)\n", "\n", "        Expired_Items_Merged7 = Expired_Items_Merged6[\n", "            (Expired_Items_Merged6[\"Cumsum2\"] >= Expired_Items_Merged6[\"ExpiredQty\"])\n", "            | (Expired_Items_Merged6[\"ExceptionFlag\"] == 1)\n", "        ]\n", "\n", "        Expired_Items_Merged8 = Expired_Items_Merged6.merge(\n", "            Expired_Items_Merged7.groupby(\n", "                [\n", "                    \"item_id\",\n", "                    \"outlet_id\",\n", "                    \"update_name\",\n", "                    \"variant_id\",\n", "                    \"inventory_update_date\",\n", "                ],\n", "                as_index=False,\n", "            )[\"Cumsum2\"].min(),\n", "            on=[\n", "                \"item_id\",\n", "                \"outlet_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ],\n", "            how=\"left\",\n", "        )\n", "\n", "        Expired_Items_Merged8.rename(\n", "            columns={\"Cumsum2_x\": \"Cumsum2\", \"Cumsum2_y\": \"MinQty2\"}, inplace=True\n", "        )\n", "\n", "        Expired_Items_Merged8 = Expired_Items_Merged8[\n", "            (Expired_Items_Merged8[\"Cumsum2\"] <= Expired_Items_Merged8[\"MinQty2\"])\n", "            | (Expired_Items_Merged8[\"ExceptionFlag\"] == 1)\n", "        ]\n", "\n", "        Expired_Items_Merged8[\"ShiftQty\"] = Expired_Items_Merged8.groupby(\n", "            [\n", "                \"outlet_id\",\n", "                \"item_id\",\n", "                \"update_name\",\n", "                \"variant_id\",\n", "                \"inventory_update_date\",\n", "            ]\n", "        )[\"Cumsum2\"].shift(1)\n", "\n", "        Expired_Items_Merged8.fillna(0, inplace=True)\n", "\n", "        Expired_Items_Merged8[\"Expired_GRN\"] = np.where(\n", "            Expired_Items_Merged8[\"ExpiredQty\"] >= Expired_Items_Merged8[\"Cumsum2\"],\n", "            Expired_Items_Merged8[\"quantity\"],\n", "            np.where(\n", "                Expired_Items_Merged8[\"ExceptionFlag\"] == 1,\n", "                Expired_Items_Merged8[\"ExpiredQty\"],\n", "                Expired_Items_Merged8[\"ExpiredQty\"] - Expired_Items_Merged8[\"ShiftQty\"],\n", "            ),\n", "        )\n", "\n", "        Expired_Items_Merged8[\"inventory_update_date\"] = Expired_Items_Merged8[\n", "            \"inventory_update_date\"\n", "        ].astype(\"datetime64[ns]\")\n", "\n", "        Expired_Items_Merged8.nte_date = pd.to_datetime(Expired_Items_Merged8.nte_date)\n", "        Expired_Items_Merged8.manufacture_date = pd.to_datetime(\n", "            Expired_Items_Merged8.manufacture_date\n", "        )\n", "\n", "        Expired_Items_Merged8[\"FIFO/DOI\"] = np.where(\n", "            Expired_Items_Merged8[\"inventory_update_date\"]\n", "            >= Expired_Items_Merged8[\"nte_date\"],\n", "            \"DOI\",\n", "            \"FIFO\",\n", "        )\n", "\n", "        Expired_Items_Merged8 = Expired_Items_Merged8[\n", "            Expired_Items_Merged8[\"Expired_GRN\"] > 0\n", "        ]\n", "\n", "        Expired_Items_Merged_STO = Expired_Items_Merged8\n", "\n", "        FinalTable_STO = Expired_Items_Merged_STO.groupby(\n", "            [\n", "                \"inventory_update_date\",\n", "                \"update_name\",\n", "                \"outlet_id\",\n", "                \"item_id\",\n", "                \"FIFO/DOI\",\n", "                \"variant_id\",\n", "                \"nte_date\",\n", "                \"manufacture_date\",\n", "                \"shelf_life\",\n", "                \"outward_guidelines\",\n", "            ],\n", "            as_index=False,\n", "        )[\"Expired_GRN\"].sum()\n", "\n", "        Expired_Items_Merged3_None[\"FIFO/DOI\"] = \"Inconclusive\"\n", "        Expired_Items_Merged3_None.rename(\n", "            columns={\"ExpiredQty\": \"Expired_GRN\"}, inplace=True\n", "        )\n", "        Expired_Items_Merged3_None = Expired_Items_Merged3_None[\n", "            [\n", "                \"inventory_update_date\",\n", "                \"update_name\",\n", "                \"outlet_id\",\n", "                \"item_id\",\n", "                \"FIFO/DOI\",\n", "                \"Expired_GRN\",\n", "                \"nte_date\",\n", "                \"manufacture_date\",\n", "                \"shelf_life\",\n", "                \"outward_guidelines\",\n", "            ]\n", "        ]\n", "\n", "        FinalTable_STO[\"Flag\"] = \"STO\"\n", "\n", "        FinalTable_GRN = Expired_Items_Merged_GRN.groupby(\n", "            [\n", "                \"inventory_update_date\",\n", "                \"update_name\",\n", "                \"outlet_id\",\n", "                \"item_id\",\n", "                \"FIFO/DOI\",\n", "                \"variant_id\",\n", "                \"nte_date\",\n", "                \"manufacture_date\",\n", "                \"shelf_life\",\n", "                \"outward_guidelines\",\n", "            ],\n", "            as_index=False,\n", "        )[\"Expired_GRN\"].sum()\n", "        FinalTable_GRN[\"Flag\"] = \"GRN\"\n", "\n", "    except:\n", "        print(\"No STO\")\n", "\n", "    try:\n", "        FinalTable = pd.concat(\n", "            [FinalTable_GRN, FinalTable_STO, Expired_Items_Merged3_None], axis=0\n", "        )\n", "    except:\n", "        FinalTable = FinalTable_GRN\n", "\n", "    FinalTable[\"inventory_update_date\"] = pd.to_datetime(\n", "        FinalTable[\"inventory_update_date\"]\n", "    )\n", "\n", "    FinalTable[\"Key\"] = (\n", "        FinalTable[\"outlet_id\"].astype(str)\n", "        + \"|\"\n", "        + FinalTable[\"item_id\"].astype(str)\n", "        + \"|\"\n", "        + FinalTable[\"variant_id\"].astype(str)\n", "        + \"|\"\n", "        + FinalTable[\"inventory_update_date\"].astype(str)\n", "        + \"|\"\n", "        + FinalTable[\"update_name\"].astype(str)\n", "    )\n", "\n", "    print(\"Output\", FinalTable.Expired_GRN.sum())\n", "\n", "    Description_Dict = {\n", "        \"expired_grn\": \"Quantity getting marked as bad-stock, expiry or near expiry\",\n", "        \"fifo/doi\": \"Attribution of whether items was marked due to FIFO or DOI\",\n", "        \"flag\": \"STO or GRN as source of inventory logs\",\n", "        \"inventory_update_date\": \"date of transaction\",\n", "        \"nte_date\": \"near to expiry date\",\n", "    }\n", "\n", "    def redshift_schema(df):\n", "        metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "        ref_dict = {\n", "            \"int64\": \"int\",\n", "            \"datetime64[ns]\": \"timestamp\",\n", "            \"object\": \"varchar(1000)\",\n", "            \"float64\": \"float\",\n", "            \"datetime64[ns, UTC]\": \"timestamp\",\n", "        }\n", "        return [\n", "            {\n", "                \"name\": key,\n", "                \"type\": ref_dict[value],\n", "                \"description\": Description_Dict.get(\n", "                    key, \"Column name sufficient for description\"\n", "                ),\n", "            }\n", "            for key, value in metadata.items()\n", "        ]\n", "\n", "    FinalTable[\"outlet_id_x\"] = FinalTable[\"outlet_id\"]\n", "\n", "    column_dtypes = redshift_schema(FinalTable)\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"expiry_fifo_doi_updated_v1\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"Key\"],\n", "        \"sortkey\": [\"outlet_id\"],\n", "        \"incremental_key\": \"inventory_update_date\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"For attribution of expiry and near expiry logs into FIFO and DOI\",\n", "    }\n", "\n", "    pb.to_redshift(FinalTable, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}