{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snapshot_datetime = datetime.utcnow() - timedelta(1)\n", "worth_date = str(snapshot_datetime.date())\n", "print(worth_date)\n", "snapshot_date = datetime.strptime(worth_date, \"%Y-%m-%d\")\n", "worth_timestamp = snapshot_date + timedelta(hours=18, minutes=29, seconds=59)\n", "print(worth_timestamp)\n", "snapshot_timestamp = worth_timestamp.isoformat()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_df = pd.read_sql_query(\n", "    f\"\"\"\n", "    SELECT distinct outlet_id,\n", "            r.name as outlet_name,\n", "            cct.name as company_name,\n", "            business_type_id,\n", "            r.facility_id,\n", "            cs.name as city_name\n", "                FROM (\n", "                (SELECT outlet_id\n", "                 FROM lake_view_reports.reports_inventory_snapshot\n", "                 WHERE active = 1\n", "                   AND quantity > 0\n", "                   AND insert_ds_ist = '{worth_date}'\n", "                   AND lake_active_record)\n", "              UNION ALL\n", "                (SELECT outlet_id\n", "                 FROM lake_reports.reports_good_inventory_snapshot\n", "                 WHERE active = 1\n", "                   AND quantity > 0\n", "                   AND insert_ds_ist = '{worth_date}'\n", "                   AND lake_active_record)) good_inventory\n", "    join lake_retail.console_outlet r on r.id = good_inventory.outlet_id and r.active = 1 and r.device_id != 47\n", "    join lake_retail.console_company_type cct ON r.company_type_id = cct.id\n", "    join lake_retail.console_location cs on cs.id = r.tax_location_id\n", "    where r.name not like '%%Infra%%'\n", "    and r.name not like '%%Liquidation%%'\n", "    and business_type_id in (1,7,12,19,20)\n", "    order by outlet_id\n", "\"\"\",\n", "    presto,\n", ")\n", "outlets_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_df = outlets_df[100:200]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import boto3\n", "# pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "# # s3_bucket = \"grofers-prod-retail-reports\"\n", "# s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "\n", "\n", "# def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 14):\n", "#     # Generate a presigned URL for the S3 object\n", "#     s3_client = boto3.client(\n", "#         \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "#     )\n", "#     try:\n", "#         response = s3_client.generate_presigned_url(\n", "#             \"get_object\",\n", "#             Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "#             ExpiresIn=expiration,\n", "#         )\n", "#     except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "#         return None\n", "#     # The response contains the presigned URL\n", "#     return response\n", "len(outlets_df)\n", "outlets_df.groupby(\"business_type_id\").outlet_id.count()\n", "rtv_df = pd.read_sql_query(\n", "    f\"\"\"select facility_id, item_id, 1 as rtv_flag\n", "        from metrics.rtv_elg_inputs\n", "        \"\"\",\n", "    redshift,\n", ")\n", "rtv_df.head()\n", "outlets = outlets_df.outlet_id.to_list()\n", "active_df = pd.read_sql_query(\n", "    \"\"\"SELECT DISTINCT \n", "       ma.facility_id,\n", "       ma.item_id AS \"item_id\",\n", "       1 as active\n", "    FROM lake_rpc.product_facility_master_assortment ma\n", "    LEFT JOIN lake_rpc.product_master_assortment_substate pmas ON ma.master_assortment_substate_id = pmas.id\n", "    LEFT JOIN lake_rpc.product_product rpp ON rpp.item_id = ma.item_id\n", "    INNER JOIN lake_retail.console_outlet co on co.facility_id = ma.facility_id and business_type_id in (1,7)\n", "    WHERE master_assortment_substate_id = 1 AND rpp.active = 1\"\"\",\n", "    redshift,\n", ")\n", "types_df = pb.from_sheets(\n", "    \"19eyyucAaKTOYOGINfXGLoD4T8QTxbn5aaa7-bTsQnE8\", \"Category mapping\"\n", ")\n", "prov_df = pb.from_sheets(\"19eyyucAaKTOYOGINfXGLoD4T8QTxbn5aaa7-bTsQnE8\", \"Provisions\")\n", "\n", "types_df[\"l0_id\"] = types_df[\"l0_id\"].astype(\"float\")\n", "prov_df\n", "states = pd.read_sql_query(\n", "    \"\"\"\n", "select cs.name as state_name,\n", "    r.id as outlet_id\n", "from lake_retail.console_outlet r\n", "join lake_retail.console_location cl on cl.id = r.tax_location_id\n", "join lake_retail.console_state cs on cs.id = cl.state_id\n", "\"\"\",\n", "    redshift,\n", ")\n", "states.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_counter = 0\n", "part = 1\n", "urls = \"\"\n", "ageing_df = pd.DataFrame()\n", "eod_df = pd.DataFrame()\n", "table_df = pd.DataFrame()\n", "for outlet_id in list(outlets_df.outlet_id.unique()):\n", "    print(outlet_id)\n", "    outlet_counter += 1\n", "    # for outlet_id in outlets:\n", "    outlet_level_df = pd.DataFrame()\n", "    active_variants_query = f\"\"\"SELECT variant_id,\n", "                                   upc_id,\n", "                                   outlet_id,\n", "                                   company_name,\n", "                                   sum(quantity) AS quantity,\n", "                                   sum(net_worth)/sum(quantity) AS landing_price\n", "                            FROM (\n", "                                    (SELECT upc_id,\n", "                                            variant_id,\n", "                                            quantity,\n", "                                            net_worth,\n", "                                            outlet_id,\n", "                                            cct.name as company_name\n", "                                     FROM lake_view_reports.reports_inventory_snapshot snap\n", "                                     JOIN lake_retail.console_outlet co ON co.id = snap.outlet_id\n", "                                     JOIN lake_retail.console_company_type cct ON co.company_type_id = cct.id\n", "                                     WHERE snap.active = 1\n", "                                       AND quantity > 0\n", "                                       AND outlet_id = {outlet_id}\n", "                                       AND insert_ds_ist = '{worth_date}'\n", "                                       AND snap.lake_active_record)\n", "                                  UNION ALL\n", "                                    (SELECT upc_id,\n", "                                            variant_id,\n", "                                            quantity,\n", "                                            net_worth,\n", "                                            outlet_id,\n", "                                            cct.name as company_name\n", "                                     FROM lake_view_reports.reports_good_inventory_snapshot good_snap\n", "                                     JOIN lake_retail.console_outlet co ON co.id = good_snap.outlet_id\n", "                                     JOIN lake_retail.console_company_type cct ON co.company_type_id = cct.id\n", "                                     WHERE good_snap.active = 1\n", "                                       AND quantity > 0\n", "                                       AND outlet_id = {outlet_id}\n", "                                       AND insert_ds_ist = '{worth_date}'\n", "                                       AND good_snap.lake_active_record))\n", "                            GROUP BY 1,\n", "                                     2,\n", "                                     3,\n", "                                     4\"\"\"\n", "    active_variants = pd.read_sql_query(\n", "        sql=active_variants_query, con=pb.get_connection(\"[Warehouse] Presto\")\n", "    )\n", "\n", "    ############################\n", "    eod_df = eod_df.append(active_variants)\n", "    ############################\n", "\n", "    # start_index = 0\n", "    # end_index = start_index + batch_size\n", "    rows_to_insert = []\n", "    # candidate_variants = active_variants\n", "\n", "    variant_list = active_variants.variant_id.to_list()\n", "    if len(variant_list) > 1:\n", "        # Fetch inventory stock details of variants\n", "\n", "        isd_query = f\"\"\"SELECT isd.outlet_id,\n", "                   co.facility_id,\n", "                   variant_id,\n", "                   CASE\n", "                       WHEN expiry_date > '2100-01-01' THEN '2100-01-01'\n", "                       WHEN expiry_date < '1970-01-01' THEN '1970-01-01'\n", "                       ELSE expiry_date\n", "                   END as expiry_date,\n", "                   manufacture_date,\n", "                   upc_id,\n", "                   date(isd.created_at + interval '5.5hrs') AS stock_in_date,\n", "                   --iii.entity_vendor_id,\n", "                   sum(\"delta\") AS stocked_in_quantity\n", "            FROM lake_ims.ims_inventory_stock_details isd\n", "            JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id = isd.grn_id\n", "            JOIN lake_retail.console_outlet co ON co.id = isd.outlet_id\n", "            WHERE isd.outlet_id = {outlet_id}\n", "            AND variant_id IN {*variant_list,}\n", "            --AND date(isd.created_at + interval '5.5hrs') <= '{worth_date}'\n", "            GROUP BY 1,2,3,4,5,6,7\n", "            --,10\n", "            \"\"\"\n", "        inventory_stock_details = pd.read_sql_query(\n", "            sql=isd_query, con=pb.get_connection(\"[Warehouse] Redshift\")\n", "        )\n", "\n", "        if inventory_stock_details.empty == False:\n", "            for variant_id in variant_list:\n", "                quantity = active_variants[\n", "                    active_variants[\"variant_id\"] == variant_id\n", "                ].quantity.values[0]\n", "                landing_price = active_variants[\n", "                    active_variants[\"variant_id\"] == variant_id\n", "                ].landing_price.values[0]\n", "                variant_inventory_stock_details = inventory_stock_details[\n", "                    inventory_stock_details[\"variant_id\"] == variant_id\n", "                ]\n", "                try:\n", "                    # self.compute_expiry_report_for_variant(quantity, variant_inventory_stock_details, rows_to_insert)\n", "                    total_quantity = abs(quantity)\n", "                    for detail in variant_inventory_stock_details.sort_values(\n", "                        by=[\"stock_in_date\", \"expiry_date\"], ascending=False\n", "                    ).to_dict(orient=\"records\"):\n", "                        # print(detail.get('expiry_date'))\n", "                        stocked_in_quantity = int(detail.get(\"stocked_in_quantity\"))\n", "                        if total_quantity <= stocked_in_quantity:\n", "                            row = (\n", "                                str(detail.get(\"upc_id\")),\n", "                                str(detail.get(\"variant_id\")),\n", "                                (total_quantity),\n", "                                (landing_price),\n", "                                str(detail.get(\"outlet_id\")),\n", "                                str(detail.get(\"facility_id\")),\n", "                                str(detail.get(\"manufacture_date\"))\n", "                                if detail.get(\"manufacture_date\")\n", "                                else None,\n", "                                str(detail.get(\"expiry_date\"))\n", "                                if detail.get(\"expiry_date\")\n", "                                else None,\n", "                                detail.get(\"stock_in_date\")\n", "                                # ,\n", "                                # detail.get('entity_vendor_id')\n", "                            )\n", "                            rows_to_insert.append(row)\n", "                            break\n", "\n", "                        elif total_quantity > stocked_in_quantity:\n", "                            row = (\n", "                                str(detail.get(\"upc_id\")),\n", "                                str(detail.get(\"variant_id\")),\n", "                                (stocked_in_quantity),\n", "                                (landing_price),\n", "                                str(detail.get(\"outlet_id\")),\n", "                                str(detail.get(\"facility_id\")),\n", "                                str(detail.get(\"manufacture_date\"))\n", "                                if detail.get(\"manufacture_date\")\n", "                                else None,\n", "                                str(detail.get(\"expiry_date\"))\n", "                                if detail.get(\"expiry_date\")\n", "                                else None,\n", "                                detail.get(\"stock_in_date\")\n", "                                # ,\n", "                                # detail.get('entity_vendor_id')\n", "                            )\n", "                            total_quantity = total_quantity - stocked_in_quantity\n", "                            rows_to_insert.append(row)\n", "\n", "                except:\n", "                    pass\n", "\n", "            df = pd.DataFrame(\n", "                rows_to_insert,\n", "                columns=[\n", "                    \"upc_id\",\n", "                    \"variant_id\",\n", "                    \"quantity\",\n", "                    \"landing_price\",\n", "                    \"outlet_id\",\n", "                    \"facility_id\",\n", "                    \"manufacture_date\",\n", "                    \"expiry_date\",\n", "                    \"stock_in_date\",\n", "                ],\n", "            )\n", "\n", "            ageing_df = ageing_df.append(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ageing_df[\"quantity\"] = ageing_df[\"quantity\"].astype(\"int\")\n", "ageing_df[\"outlet_id\"] = ageing_df[\"outlet_id\"].astype(\"int\")\n", "ageing_df[\"facility_id\"] = ageing_df[\"facility_id\"].astype(\"int\")\n", "ageing_df[\"manufacture_date\"] = pd.to_datetime(ageing_df[\"manufacture_date\"])\n", "ageing_df[\"expiry_date\"] = pd.to_datetime(ageing_df[\"expiry_date\"])\n", "ageing_df[\"stock_in_date\"] = pd.to_datetime(ageing_df[\"stock_in_date\"])\n", "ageing_df[\"etl_timestamp\"] = datetime.now(ist)\n", "\n", "del ageing_df[\"landing_price\"]\n", "\n", "ageing_df_column_dtypes = [\n", "    {\"name\": \"upc_id\", \"type\": \"varchar\", \"description\": \"upc_id\"},\n", "    {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "    {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"quantity\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\n", "        \"name\": \"manufacture_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"manufacture_date\",\n", "    },\n", "    {\"name\": \"expiry_date\", \"type\": \"datetime\", \"description\": \"expiry_date\"},\n", "    {\n", "        \"name\": \"stock_in_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"stock_in_date\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl_timestamp\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inventory_ageing\",\n", "    \"column_dtypes\": ageing_df_column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"variant_id\",\n", "        \"stock_in_date\",\n", "        \"expiry_date\",\n", "        \"etl_timestamp\",\n", "    ],\n", "    \"sortkey\": [\"outlet_id\", \"stock_in_date\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, upsert\n", "    \"table_description\": \"This tables contains the stock in date and expiry dates for all items at all outlets\",\n", "}\n", "\n", "pb.to_redshift(ageing_df, **kwargs)\n", "print(f\"done pushing\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}