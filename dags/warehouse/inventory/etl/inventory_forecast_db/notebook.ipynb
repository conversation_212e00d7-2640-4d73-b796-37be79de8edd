{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os as os\n", "import datetime as dt\n", "from calendar import monthrange\n", "from datetime import timedelta\n", "import pencilbox as pb\n", "\n", "retail = pb.get_connection(\"retail\")\n", "redshift_con = pb.get_connection(\"redshift\").connect()\n", "td = (pd.to_datetime(dt.datetime.now()) - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Carts data\n", "dq = \"\"\"\n", "select date( date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY('{date}')))))\n", "\"\"\".format(\n", "    date=td\n", ")\n", "sdate1 = pd.read_sql(dq, con=redshift_con)\n", "sdate1[\"date\"] = sdate1[\"date\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "sdate = sdate1.date[0]\n", "now = dt.datetime.strptime(td, \"%Y-%m-%d\")\n", "inv = monthrange(int(now.strftime(\"%Y\")), int(now.strftime(\"%m\")))[1]\n", "edate = (pd.to_datetime(sdate) + pd.DateOffset(days=(inv - 1))).strftime(\"%Y-%m-%d\")\n", "\n", "Q6 = \"\"\"\n", "with data as (\n", "select * from ( select *, row_number() over (partition by Merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts)\n", "where rnk =1 and date(f_date) between '{date}' and '{end_date}' )\n", "\n", "select \n", "city_name,facility,f_date as forecast_date,sum(forecast_carts) as forecast_carts\n", "from data\n", "group by 1,2,3\n", "order by 3,2\n", "\n", "\"\"\".format(\n", "    date=sdate, end_date=edate\n", ")\n", "#\n", "fcarts = pd.read_sql(Q6, con=redshift_con)\n", "upfcarts = fcarts.copy()\n", "date_r = fcarts.loc[:, [\"forecast_carts\", \"forecast_date\"]]\n", "date_r.drop_duplicates(subset=[\"forecast_date\"], keep=\"first\", inplace=True)\n", "date_r[\"forecast_date\"] = date_r[\"forecast_date\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d\")\n", ")\n", "date_r = date_r.sort_values([\"forecast_date\"], ascending=[True])\n", "now_m = list(date_r[\"forecast_date\"])\n", "faci_carts = fcarts.loc[\n", "    fcarts.forecast_date == td, [\"facility\", \"forecast_carts\"]\n", "]  # facility carts\n", "mfac = pd.DataFrame({\"forecast_date\": now_m[:10]})\n", "mfac[\"mfac\"] = 1.1\n", "upfcarts[\"forecast_date\"] = upfcarts[\"forecast_date\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d\")\n", ")\n", "upfcarts1 = pd.merge(\n", "    left=upfcarts,\n", "    right=mfac,\n", "    left_on=[\"forecast_date\"],\n", "    right_on=[\"forecast_date\"],\n", "    how=\"left\",\n", ")\n", "upfcarts1[\"mfac\"].fillna(1, inplace=True)\n", "upfcarts1[\"forecast_carts\"] = upfcarts1[\"forecast_carts\"] * upfcarts1[\"mfac\"]\n", "cupfcarts1 = upfcarts1.groupby([\"city_name\"])[\"forecast_carts\"].agg(\"sum\").reset_index()\n", "cupfcarts2 = cupfcarts1.rename(columns={\"forecast_carts\": \"total_carts\"})\n", "upfcarts2 = upfcarts1.loc[upfcarts1.forecast_date == td]\n", "cupfcarts3 = pd.merge(\n", "    left=upfcarts2,\n", "    right=cupfcarts2,\n", "    left_on=[\"city_name\"],\n", "    right_on=[\"city_name\"],\n", "    how=\"left\",\n", ")\n", "cupfcarts3[\"f_split\"] = (\n", "    cupfcarts3[\"forecast_carts\"] / cupfcarts3[\"total_carts\"]\n", ")  # spliter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1LkzcTCjt_J5ILplOZ-iK3wmCAklqbYlCV-0zad-syVM\"\n", "mappings = pb.from_sheets(sheet_id, \"Mappings\")\n", "mapping1 = mappings.loc[\n", "    mappings[\"4.FACILITY\"] != \"\", [\"4.FACILITY\", \"4.Location\", \"4.city_id\"]\n", "]\n", "mapping1 = mapping1.rename(\n", "    columns={\n", "        \"4.FACILITY\": \"facility\",\n", "        \"4.Location\": \"city_name\",\n", "        \"4.city_id\": \"city_id\",\n", "    }\n", ")\n", "q2 = \"\"\"select* from(select facility_id,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "from city_facility_split)\n", "where rnk=1\n", "    \"\"\"\n", "cityfc = pd.read_sql(q2, con=redshift_con)  # only facility id\n", "cityfc = pd.merge(\n", "    left=mapping1,\n", "    right=cityfc[{\"facility_id\", \"facility\"}],\n", "    left_on=\"facility\",\n", "    right_on=\"facility\",\n", "    how=\"left\",\n", ")\n", "faci_faci = mappings.loc[\n", "    mappings[\"5.correct_facility\"] != \"\",\n", "    [\"5.correct_fid\", \"5.correct_facility\", \"5.f_id\", \"5.facility\"],\n", "]\n", "faci_faci1 = faci_faci.rename(\n", "    columns={\n", "        \"5.correct_fid\": \"cfacility_id\",\n", "        \"5.correct_facility\": \"cfacility\",\n", "        \"5.f_id\": \"facility_id\",\n", "        \"5.facility\": \"facility\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Q1 = \"\"\"\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.GR_PRODUCT P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ) \n", "  \n", "SELECT item_id,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "FROM rpc_item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "\n", "\"\"\"\n", "iteem_l0 = pd.read_sql(Q1, con=redshift_con)  # 6280\n", "\n", "\n", "Q2 = \"\"\"\n", "SELECT \n", "     pp.item_id as item_id,\n", "     ipd.selling_price as sp,\n", "     outlet.name \"Outlet\",\n", "     outlet.facility_id as facility_id,\n", "     mm.facility,\n", "     count(distinct od.order_id) as Order_Count,\n", "     sum(ipd.quantity) as Sales_Quantity\n", "FROM consumer.view_pos_pos_invoice p\n", "JOIN ims_order_details od ON od.order_id = p.grofers_order_id\n", "JOIN pos_console_outlet outlet ON outlet.id = od.outlet and outlet.type_id = 2\n", "JOIN pos_pos_invoice_product_details ipd ON p.id = ipd.invoice_id\n", "JOIN pos_product_product pp ON pp.variant_id = ipd.variant_id\n", "left join (select* from(select city_id,facility_id, city,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "        from city_facility_split) where rnk=1) mm on mm.facility_id=outlet.facility_id\n", "WHERE date(p.pos_timestamp + interval '5.5 hours') BETWEEN date(Current_Date-30) AND date(Current_Date-1)\n", "AND p.invoice_type_id = '1' and mm.facility is not null\n", "GROUP BY 1,2,3,4,5\n", "\"\"\"\n", "sales_asp = pd.read_sql(Q2, con=redshift_con)\n", "\n", "Q3 = \"\"\"\n", "with data as (select item_id,\n", "    name,\n", "    is_pl,\n", "    (case when is_pl ='false' then 0\n", "    else 1 end )as pl,\n", "    max(updated_at)from rpc_product_product \n", "where active =1\n", "group by 1,2,3,4\n", " )\n", "select item_id,name,pl as is_pl from data\"\"\"\n", "item_code = pd.read_sql(Q3, con=redshift_con)\n", "Qcat = \"\"\"\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.GR_PRODUCT P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ) \n", "  \n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "FROM rpc_item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\"\"\"\n", "cat = pd.read_sql(Qcat, con=redshift_con)\n", "Buckets = pd.read_sql_query(\n", "    sql=\"\"\"\n", "select \n", "distinct\n", "a.item_id,\n", "case when tag_type_id=1 then tag_value end as bucket,\n", "case when tag_type_id=6 then tag_value end as bucket_X,\n", "pos.facility_id\n", "from rpc.item_outlet_tag_mapping as a\n", "inner join retail.console_outlet as pos\n", "on a.outlet_id=pos.id\n", "\"\"\",\n", "    con=retail,\n", ")\n", "Bucketab = Buckets.loc[\n", "    ((Buckets.bucket == \"A\") | (Buckets.bucket == \"B\")),\n", "    [\"item_id\", \"bucket\", \"facility_id\"],\n", "]\n", "Bucketx = Buckets.loc[(Buckets.bucket_X == \"Y\"), [\"item_id\", \"bucket_X\", \"facility_id\"]]\n", "Assortment_data = pd.read_sql_query(\n", "    sql=\"\"\"\n", "select item_id,city_id as city_id1 ,master_assortment_substate_id\n", "from rpc.product_master_assortment\n", "where \n", "master_assortment_substate_id=1\n", "\"\"\",\n", "    con=retail,\n", ")  # Assortment Tag\n", "cityfc[\"city_id\"] = cityfc[\"city_id\"].astype(int)\n", "Assortment_data = Assortment_data.rename(columns={\"city_id1\": \"city_id\"})\n", "Assortment_data1 = pd.merge(\n", "    left=Assortment_data,\n", "    right=cityfc,\n", "    left_on=\"city_id\",\n", "    right_on=\"city_id\",\n", "    how=\"left\",\n", ")\n", "Assortment_data1.dropna(subset=[\"facility_id\"], inplace=True)\n", "Assortment_data1.drop_duplicates(\n", "    subset=[\"item_id\", \"facility_id\"], keep=\"first\", inplace=True\n", ")\n", "Assortment_data1[\"Assortment_Tag\"] = \"active\"\n", "Assortment_data2 = Assortment_data1.drop(columns={\"master_assortment_substate_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def actual_sales_d(SD1, ED1):\n", "    Q_1 = \"\"\"\n", "    with\n", "\n", "    item_level_info as\n", "    (\n", "    select\n", "    item_id,\n", "    name as item_name,\n", "    brand,\n", "    brand_id,\n", "    manufacturer,\n", "    variant_description,\n", "    is_pl,\n", "    variant_mrp,\n", "    row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "    from\n", "    rpc_product_product\n", "    ),\n", "\n", "    it_pd as\n", "    (\n", "    select \n", "    distinct\n", "    item_id,\n", "    product_id,\n", "    multiplier,\n", "    updated_on,\n", "    max(updated_on) over (partition by product_id) as last_updated\n", "    from \n", "    it_pd_log\n", "    ),\n", "\n", "    mapping as\n", "    (\n", "    select \n", "    distinct\n", "    item_id,\n", "    product_id,\n", "    coalesce(multiplier,1) as multiplier\n", "    from it_pd\n", "    where last_updated = updated_on\n", "    ),\n", "\n", "    categories as\n", "    (\n", "    SELECT \n", "    P.ID AS PID,\n", "    P.NAME AS PRODUCT,\n", "    P.UNIT AS Unit,\n", "    C2.NAME AS L2,\n", "    (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "    C.NAME AS L0,\n", "    P.BRAND AS brand,\n", "    P.MANUFACTURER AS manf,\n", "    pt.name as product_type\n", "    FROM lake_cms.GR_PRODUCT P\n", "    INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "    AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "    INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "    inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "    ), \n", "\n", "    category_pre as\n", "    (\n", "    SELECT \n", "    item_id,\n", "    product_id,\n", "    cat.l0,\n", "    cat.l1,\n", "    cat.l2,\n", "    cat.product_type\n", "    FROM rpc_item_product_mapping rpc\n", "    INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "    ),\n", "    ptype_tbl as\n", "    (\n", "    select \n", "    item_id,\n", "    max(l0) as l0,\n", "    max(l1) as l1,\n", "    max(l2) as l2,\n", "    max(product_type) as ptype\n", "    from category_pre\n", "    group by 1\n", "    ),\n", "    tbl_treatment as\n", "    (\n", "    select\n", "    a.*,\n", "    date(a.install_ts + interval '5.5 Hours') as checkout_date,\n", "    om.city_name,\n", "    b.product_id,\n", "    c.item_id,\n", "    d.is_pl,\n", "    b.quantity*multiplier as quantity,\n", "    multiplier,\n", "    b.selling_price,\n", "    om.external_id as virtual_merchant_id,\n", "    d.variant_mrp,\n", "    sum(d.variant_mrp) over (partition by a.id, b.product_id) as total_mrp\n", "    from \n", "    lake_oms_bifrost.oms_order a\n", "    left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "    left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') between current_date - 220 AND current_date) b \n", "    on a.id = b.order_id\n", "    left join mapping c on b.product_id = c.product_id\n", "    left join (select * from item_level_info where row_rank = 1) d on c.item_id = d.item_id\n", "    left join ptype_tbl e on c.item_id = e.item_id\n", "    left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "    (a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "    where\n", "    date(a.install_ts + interval '5.5 Hours') between '{start_date}' and '{end_date}'\n", "    and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "    and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "    AND om.city_name not ilike '%%b2b%%'\n", "    --and current_status = 'CANCELLED'\n", "    and lq.pid is null\n", "    and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "    -- l0 not in ('Fruits & Vegetables', 'Vegetables & Fruits')\n", "    ),\n", "\n", "    merchant_and_store_join as\n", "    (\n", "    select \n", "    a.*,\n", "    selling_price*variant_mrp*1.000/(total_mrp*multiplier) as item_selling_price,\n", "    bb.facility_id\n", "    from tbl_treatment a\n", "    left join ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                    from Merchant_forecast_carts) bb on a.virtual_merchant_id = bb.merchant_id and date(a.checkout_date) = bb.f_date\n", "    where rnk =1\n", "    ),\n", "\n", "    final_sales as\n", "    (\n", "    select \n", "    a.facility_id,\n", "    a.item_id,\n", "    sum(a.quantity) as qty,\n", "    min(item_selling_price) as min_price\n", "    from merchant_and_store_join a\n", "    --where a.checkout_date '{start_date}' and '{end_date}'\n", "    group by 1,2\n", "    ),\n", "    price_history as (\n", "    select\n", "    date(gp.install_ts+interval '5.5 hours') as datee,\n", "    bb.facility,\n", "    bb.facility_id,\n", "    c.item_id,\n", "    gp.product_id\n", "    from gr_product_price_history gp\n", "    left join mapping c on gp.product_id = c.product_id\n", "    left join ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                    from Merchant_forecast_carts) bb on gp.merchant_id = bb.merchant_id and date(gp.install_ts+interval '5.5 hours') = bb.f_date\n", "    where rnk =1\n", "    and  date(gp.install_ts+interval '5.5 hours') BETWEEN '{start_date}' and '{end_date}'\n", "    group by 1,2,3,4,5\n", "    ),\n", "\n", "    final_live_days as\n", "    (\n", "    select item_id, facility, facility_id,\n", "    count(distinct case when (datee between '{start_date}' and '{end_date}' ) Then datee end ) as live_days\n", "    from price_history\n", "    group by 1,2,3\n", "    )\n", "\n", "    select \n", "    a.facility,\n", "    a.item_id,\n", "    sum(live_days) as live_days,\n", "    sum(case when qty is null then 0 else qty end) as sales_qty,\n", "    min(min_price) as min_price\n", "    from final_live_days a\n", "    left join final_sales b on a. item_id = b.item_id and a.facility_id = b.facility_id\n", "    where a.item_id is not null\n", "    group by 1,2\n", "    \"\"\".format(\n", "        start_date=SD1, end_date=ED1\n", "    )\n", "    sales1_data = pd.read_sql(Q_1, con=redshift_con)\n", "    return sales1_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c_art = \"\"\"\n", "with it_pd as\n", "(select distinct item_id, product_id,multiplier, updated_on, max(updated_on) over (partition by product_id) as last_updated from it_pd_log ),\n", "\n", "mapping as (select distinct item_id, product_id,coalesce(multiplier,1) as multiplier from it_pd\n", "    where last_updated = updated_on ),\n", "\n", "tbl_treatment as\n", "    (select a.*,date(a.install_ts + interval '5.5 Hours') as checkout_date,om.city_name,b.product_id,c.item_id,om.external_id as virtual_merchant_id\n", "    from lake_oms_bifrost.oms_order a\n", "    left join lake_oms_bifrost.view_oms_merchant om on a.merchant_id = om.id\n", "    left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') between current_date - 220 AND current_date) b on a.id = b.order_id\n", "    left join mapping c on b.product_id = c.product_id\n", "    left join rpc_item_details d on c.item_id = d.item_id\n", "    where (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "        and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "        and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)),\n", "\n", "merchant_and_store_join as (\n", "    select a.*,b.real_merchant_id,c.outlet_id,d.warehouse_id as hot_outlet_id,e.facility_id,f.name as facility_name from tbl_treatment a\n", "    left join (select * from lake_cms.gr_virtual_to_real_merchant_mapping where priority_order = 1 AND enabled_flag = true) b on a.virtual_merchant_id = b.virtual_merchant_id\n", "    left join (select * from rt_console_outlet_cms_store where active = true) c on b.real_merchant_id = c.cms_store\n", "    left join warehouse_outlet_mapping d on c.outlet_id = d.cloud_store_id\n", "    left join pos_console_outlet e on d.warehouse_id = e.id\n", "    left join crates_facility f on e.facility_id = f.id),\n", "\n", "actual1_cart as( \n", "select \n", "checkout_date as date_,\n", "virtual_merchant_id as merchant_id,\n", " count(distinct id) as orders\n", " from merchant_and_store_join\n", " where date(checkout_date)= '{start_date}'\n", "group by 1,2),\n", "\n", " data as (  select * from ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "            from Merchant_forecast_carts)\n", "            where rnk =1 and date(f_date)  = '{start_date}')\n", "           \n", "select mo.facility,\n", "sum(forecast_carts) as carts,\n", "sum(ac.orders) as actual_carts\n", "from data mo\n", "left join actual1_cart ac on mo.f_date=ac.date_ and mo.merchant_id = ac.merchant_id\n", "group by 1\n", "order by 1\n", "\"\"\".format(\n", "    start_date=td\n", ")\n", "cart_sales = pd.read_sql(c_art, con=redshift_con)\n", "sales_14d = actual_sales_d(td, td)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_14d1 = pd.merge(\n", "    left=sales_14d,\n", "    right=cart_sales,\n", "    left_on=\"facility\",\n", "    right_on=\"facility\",\n", "    how=\"left\",\n", ")\n", "sales_14d1[\"min_price\"].fillna(0, inplace=True)\n", "sales_14d1[\"carts\"].fillna(0, inplace=True)\n", "sales_14d1[\"actual_carts\"].fillna(0, inplace=True)\n", "sales_14d1[\"sales_qty\"].fillna(0, inplace=True)\n", "sales_14d1[\"date_\"] = td\n", "sales_data = sales_14d1.loc[\n", "    :,\n", "    [\n", "        \"date_\",\n", "        \"facility\",\n", "        \"item_id\",\n", "        \"carts\",\n", "        \"live_days\",\n", "        \"sales_qty\",\n", "        \"actual_carts\",\n", "        \"min_price\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fran_store = pd.read_sql_query(\n", "    sql=\"\"\"SELECT  date(convert_tz(p.pos_timestamp,'+00:00','+05:30')) AS date_,\n", "         p.invoice_id AS Invoice_ID,     \n", "         p.grofers_order_id as Order_ID,   \n", "\n", "         rp.item_id,\n", "         rp.name AS Product_Name,\n", "         rp.variant_uom_text as Variant_Description,\n", "         pi.quantity AS Qty,\n", "         pi.unit_rate as MRP,\n", "         pi.selling_price as Selling_Price,\n", "         ocp.facility_id as cfacility_id\n", "     FROM\n", "     pos.pos_invoice_product_details pi\n", "     INNER JOIN\n", "     pos.pos_invoice p FORCE INDEX (idx_outlet_id_pos_timestamp) ON p.id = pi.invoice_id\n", "     LEFT JOIN\n", "     pos.pos_invoice_payment_details pipd on p.id=pipd.invoice_id\n", "     LEFT JOIN\n", "     pos.pos_invoice_payment_type ppt on ppt.id=pipd.payment_type_id\n", "     INNER JOIN\n", "     retail.console_outlet r FORCE INDEX (PRIMARY) ON r.id = p.outlet_id\n", "     INNER JOIN\n", "     retail.console_location l1 ON l1.`id`=r.`tax_location_id`\n", "     left join \n", "    (select id,facility_id from retail.console_outlet\n", "    where facility_id is not null group by 1,2 ) ocp on p.outlet_id= ocp.id\n", "     INNER JOIN\n", "     retail.console_location l2 ON l2.`id`=p.customer_city\n", "     INNER JOIN\n", "     rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "     WHERE  p.invoice_type_id in (1,9)\n", "    and  r.business_type_id=8\n", "    and    date(convert_tz(p.pos_timestamp,'+00:00','+05:30')) = DATE_ADD(CURDATE(), INTERVAL - 1 DAY) \n", "    and pi.store_offer_id is null\n", "    \"\"\",\n", "    con=retail,\n", ")\n", "Fran_store[\"cfacility_id\"] = Fran_store[\"cfacility_id\"].astype(str)\n", "Fran__store = pd.merge(\n", "    left=Fran_store,\n", "    right=faci_faci1[{\"cfacility_id\", \"facility\"}],\n", "    left_on=[\"cfacility_id\"],\n", "    right_on=[\"cfacility_id\"],\n", "    how=\"left\",\n", ")\n", "Fran_store = Fran__store.drop(columns={\"cfacility_id\"})\n", "Fran_store[\"item_id\"] = Fran_store[\"item_id\"].astype(str)\n", "Fran_store[\"date_\"] = Fran_store[\"date_\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "forecast_c = (\n", "    fcarts.groupby([\"forecast_date\", \"facility\"])[\"forecast_carts\"]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ")\n", "forecast_c[\"forecast_date\"] = forecast_c[\"forecast_date\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d\")\n", ")\n", "forecast_c1 = forecast_c.rename(\n", "    columns={\"forecast_carts\": \"carts\", \"forecast_date\": \"date_\"}\n", ")  # forecast carts\n", "Fran_cart = Fran_store.copy()\n", "Fran_cart.drop_duplicates(\n", "    subset=[\"date_\", \"Invoice_ID\", \"facility\"], keep=\"first\", inplace=True\n", ")\n", "Fran_cart1 = (\n", "    Fran_cart.groupby([\"date_\", \"facility\"])[\"Invoice_ID\"].agg(\"count\").reset_index()\n", ")\n", "Fran_cart2 = Fran_cart1.rename(columns={\"Invoice_ID\": \"actual_carts\"})  # CARTS\n", "Fran_cart3 = pd.merge(\n", "    left=Fran_cart2,\n", "    right=forecast_c1,\n", "    left_on=[\"date_\", \"facility\"],\n", "    right_on=[\"date_\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "Fran_cart3[\"carts\"].fillna(0, inplace=True)  # final carts\n", "Fran_store1 = (\n", "    Fran_store.groupby([\"date_\", \"facility\", \"item_id\"])[\"Qty\", \"Selling_Price\"]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ")\n", "Fran_store2 = Fran_store1.rename(\n", "    columns={\"Qty\": \"sales_qty\", \"Selling_Price\": \"min_price\"}\n", ")  # qty\n", "Fran_store4 = pd.merge(\n", "    left=Fran_store2,\n", "    right=Fran_cart3,\n", "    left_on=[\"date_\", \"facility\"],\n", "    right_on=[\"date_\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "Fran_store4[\"live_days\"] = 1\n", "Fran_store5 = Fran_store4.loc[\n", "    :,\n", "    [\n", "        \"date_\",\n", "        \"facility\",\n", "        \"item_id\",\n", "        \"carts\",\n", "        \"live_days\",\n", "        \"sales_qty\",\n", "        \"actual_carts\",\n", "        \"min_price\",\n", "    ],\n", "]\n", "Fran_store5[\"carts\"] = Fran_store5[\"carts\"].astype(int)\n", "sales_data = Fran_store5.append(sales_data)\n", "sales_data.drop_duplicates(\n", "    subset=[\"date_\", \"facility\", \"item_id\"], keep=\"first\", inplace=True\n", ")\n", "sales_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Qlp = \"\"\"\n", "WITH \n", "\n", "item_city_state as \n", "  (\n", "    select b.item_id, ro.facility_id, ast.name as state\n", "        from product_master_assortment b\n", "        left join product_master_assortment_substate ast on ast.id = b.master_assortment_substate_id\n", "        inner join\n", "              (select item_id, city_id, max(updated_at) as max from product_master_assortment group by 1,2) a\n", "        on a.item_id = b.item_id and a.city_id = b.city_id and a.max = b.updated_at\n", "        left join view_pos_console_outlet ro on ro.tax_location_id = a.city_id\n", "       where b.master_assortment_substate_id in (1,5)  \n", "        group by 1,2,3\n", ") ,\n", "\n", " PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.GR_PRODUCT P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ),\n", "   \n", "   final_lo as\n", "   (\n", "  \n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "FROM rpc_item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "\n", "),\n", "\n", "\n", "lp_1 as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_Rank1\n", "from weighted_landing_price a\n", "left join (select * from warehouse_outlet_mapping where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from pos_console_outlet where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "final as\n", "(\n", "select a.item_id , a.facility_id ,l0, coalesce(e.landing_price, ee.landing_price) as landing_price\n", "from item_city_state a\n", "left join (select * from lp_1 where row_rank = 1) e on e.item_id = a.item_id and a.facility_id = e.facility_id\n", "left join (select * from lp_1 where row_rank1 = 1) ee on ee.item_id = a.item_id\n", "left join final_lo b on b.item_id = a.item_id\n", ")\n", "\n", "select a.*,m.facility,\n", "case \n", "when l0= 'Grocery & Staples' then (landing_price * 0.065) + landing_price\n", "when l0= 'Beverages' then (landing_price * 0.08) + landing_price\n", " when l0= 'Household Needs' then (landing_price * 0.1) + landing_price\n", " when l0=  'Personal Care' then (landing_price * 0.1) + landing_price\n", " when l0= 'Baby Care' then (landing_price * 0.065) + landing_price\n", " when l0= 'Noodles, Sauces & Instant Food' then (landing_price * 0.12) + landing_price\n", " when l0= 'Biscuits, Namkeen & Chocolates' then (landing_price * 0.13) + landing_price\n", " when l0=  'Breakfast & Dairy' then (landing_price * 0.12) + landing_price\n", " when l0=  'Furnishing & Home Needs' then (landing_price * 0.19) + landing_price\n", " when l0=  'Home & Kitchen' then (landing_price * 0.2) + landing_price\n", " when l0= 'Fresh & Frozen Food' then (landing_price * 0.19) + landing_price\n", " when l0= 'Pet Care' then (landing_price * 0.14) + landing_price\n", " when l0= 'Household Items' then (landing_price * 0.1) + landing_price\n", " when l0= 'Biscuits, Snacks & Chocolates' then (landing_price * 0.13) + landing_price\n", " end as for_asp\n", "from final a \n", "left join (select* from(select city_id,facility_id, city,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "    from city_facility_split)\n", "    where rnk=1) m on m.facility_id=a.facility_id\n", "where facility is not null\"\"\"\n", "asp_lp = pd.read_sql(Qlp, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Qm = \"\"\"\n", "with snorlax_tbl_treatment as(\n", "select\n", "item_id,\n", "outlet_id,\n", "date(date ) as date_of_prediction,\n", "consumption,\n", "row_number() over (partition by item_id,outlet_id, date(date) order by updated_at desc) as update_rank\n", "from\n", "snorlax_date_wise_consumption\n", "where date_of_prediction= '{date}' and outlet_id not in (430,415)    \n", "),\n", "\n", "data as(\n", "select \n", "b.outlet_id,b.item_id,facility_id as f_id,\n", "date_of_prediction,\n", "sum(consumption) as qty\n", "from snorlax_tbl_treatment b\n", "left join (select id as Outlet_id, facility_id, rnk from \n", "            (select m.*,row_number() over(partition by updated_at,id order by updated_at desc) as rnk \n", "            from consumer.pos_console_outlet m\n", "            where active ='True')\n", "            where rnk =1 ) po on po.Outlet_id= b.outlet_id\n", "---\n", "where  po.facility_id is not null  \n", "Group by 1,2,3,4)\n", "\n", "select item_id, f_id, mp.facility, date_of_prediction,\n", "sum(qty) as qty from data cc\n", "left join(select* from(select facility_id,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "            from city_facility_split)\n", "          where rnk=1) mp on mp.facility_id=cc.f_id\n", "where mp.facility is not null \n", "Group by 1,2,3,4\n", "\"\"\".format(\n", "    date=td\n", ")\n", "#\n", "fforecast_data = pd.read_sql(Qm, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Qc = \"\"\"\n", "select * from (\n", "select facility_id,\"item_id \"as item_id,\"category_ manager\"as category_manager, date(f_date) as Date_, qty as cat_input, date (update_at) as cat_pick_date,\n", "row_number()over (partition by facility_id, \"item_id \",f_date order by update_at desc) as rnk \n", "from category_inputs_db\n", "where date(f_date) = '{Date}')\n", "where rnk =1\n", "\"\"\".format(\n", "    Date=td\n", ")\n", "#\n", "cat_input = pd.read_sql(Qc, con=redshift_con)\n", "Qmq = \"\"\"\n", "\n", "select \n", "item_id,facility,\n", "avg(ds_cpd)as base_qty,\n", "avg(base_cpd)as cat_base_qty from ( select *,\n", "                            Row_number() over (partition by item_id,facility_id,forecast_date order by updated_at desc) as rnk\n", "                            from consumer.day_wise_monthly_sales_forecast\n", "                            where date(forecast_date) between '{start_date}'and '{end_date}')\n", "where rnk =1                             \n", "Group by 1,2\n", "\n", "\"\"\".format(\n", "    start_date=td, end_date=td\n", ")\n", "month_input1 = pd.read_sql(Qmq, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Qa = \"\"\"\n", "select \n", "item_id, \n", "facility_id, \n", "facility,\n", "avg(consumption) as Ad_qty\n", "from\n", "    (select \n", "    item_id, facility_id,facility,\n", "    consumption,\n", "    consumption_date,\n", "    rank() over (partition by item_id, facility_id, consumption_date order by updated_at desc) as rnk\n", "    from\n", "     consumer.day_wise_adjustment_db\n", "    where consumption_date = '{start_date}')\n", "where rnk = 1\n", "group by 1,2,3\n", "\n", "\n", "\"\"\".format(\n", "    start_date=td\n", ")\n", "#\n", "Adjust_input1 = pd.read_sql(Qa, con=redshift_con)\n", "Adjust_input1 = Adjust_input1.rename(columns={\"ad_qty\": \"Ad_qty\"})\n", "data_base = sales_data.loc[:, [\"item_id\", \"facility\"]].append(\n", "    Assortment_data2.loc[:, [\"item_id\", \"facility\"]]\n", ")\n", "data_base[\"item_id\"] = data_base[\"item_id\"].astype(int)\n", "data_base.drop_duplicates(subset=[\"item_id\", \"facility\"], keep=\"first\", inplace=True)\n", "q2 = \"\"\"select* from(select facility_id,facility, row_number() over(partition by facility order by update_at desc) rnk \n", "from city_facility_split)\n", "where rnk=1\n", "    \"\"\"\n", "cityfc = pd.read_sql(q2, con=redshift_con)  # only facility id\n", "data_base = pd.merge(\n", "    left=data_base, right=cityfc, left_on=\"facility\", right_on=\"facility\", how=\"left\"\n", ")\n", "data_base = data_base.sort_values([\"facility\"], ascending=[True])\n", "data_base[\"facility_id\"] = data_base[\"facility_id\"].astype(int)\n", "Assortment = Assortment_data2.loc[:, [\"item_id\", \"facility_id\"]]\n", "Assortment[\"assortment_flag\"] = 1\n", "Assortment.drop_duplicates(\n", "    subset=[\"item_id\", \"facility_id\"], keep=\"first\", inplace=True\n", ")\n", "data_base1 = pd.merge(\n", "    left=data_base,\n", "    right=Assortment,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "data_base1[\"assortment_flag\"].fillna(0, inplace=True)\n", "data_base1[\"assortment_flag\"] = data_base1[\"assortment_flag\"].astype(int)\n", "sales_data[\"item_id\"] = sales_data[\"item_id\"].astype(int)\n", "data_base2 = pd.merge(\n", "    left=data_base1,\n", "    right=sales_data.loc[\n", "        :, [\"item_id\", \"facility\", \"live_days\", \"sales_qty\", \"min_price\"]\n", "    ],\n", "    left_on=[\"item_id\", \"facility\"],\n", "    right_on=[\"item_id\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "data_base2[\"live_days\"].fillna(0, inplace=True)\n", "data_base2[\"sales_qty\"].fillna(0, inplace=True)\n", "data_base2[\"min_price\"].fillna(0, inplace=True)\n", "data_base2[\"live_days\"] = data_base2[\"live_days\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = data_base2[{\"item_id\", \"facility\"}]\n", "cpd[\"item_id\"] = cpd[\"item_id\"].astype(str)\n", "cpd[\"Union\"] = cpd[\"item_id\"].str.cat(cpd[\"facility\"].copy())\n", "Mapped_outlet = sales_asp.loc[:, [\"item_id\", \"sp\", \"facility\", \"sales_quantity\"]]\n", "new = Mapped_outlet[\"facility\"].copy()\n", "Mapped_outlet[\"item_id\"] = Mapped_outlet[\"item_id\"].astype(str)\n", "Mapped_outlet[\"Union\"] = Mapped_outlet[\"item_id\"].str.cat(new)  # display\n", "Mapped_outlet[\"GMV\"] = Mapped_outlet[\"sales_quantity\"] * Mapped_outlet[\"sp\"]\n", "temp = Mapped_outlet.groupby([\"item_id\"])\n", "res = temp[[\"GMV\", \"sales_quantity\"]].agg(\"sum\").reset_index()\n", "res = res.rename(columns={\"GMV\": \"SumOfgmv\", \"sales_quantity\": \"SumOfQty\"})\n", "res[\"asp_item\"] = res[\"SumOfgmv\"] / res[\"SumOfQty\"]  #\n", "temp1 = Mapped_outlet.groupby([\"Union\"])\n", "res1 = temp1[[\"GMV\", \"sales_quantity\"]].agg(\"sum\").reset_index()\n", "res1 = res1.rename(columns={\"GMV\": \"SumOfgmv\", \"sales_quantity\": \"SumOfQty\"})\n", "res1[\"asp_Union\"] = res1[\"SumOfgmv\"] / res1[\"SumOfQty\"]\n", "asp_cperd = cpd.loc[:, [\"Union\", \"item_id\"]]\n", "asp_cperd = asp_cperd.rename(columns={\"item_id\": \"Item ID\"})\n", "asp_cperd1 = pd.merge(\n", "    left=asp_cperd, right=res1, left_on=\"Union\", right_on=\"Union\", how=\"left\"\n", ")\n", "asp_cperd1[\"Item ID\"] = asp_cperd1[\"Item ID\"].astype(str)\n", "res[\"item_id\"] = res[\"item_id\"].astype(str)\n", "asp_cperd3 = pd.merge(\n", "    left=asp_cperd1.loc[:, [\"Union\", \"Item ID\", \"asp_Union\"]],\n", "    right=res.loc[:, [\"item_id\", \"asp_item\"]],\n", "    left_on=\"Item ID\",\n", "    right_on=\"item_id\",\n", "    how=\"left\",\n", ")\n", "asp_cperd4 = asp_cperd3.iloc[:, [0, 1, 2, 4]]\n", "asp_cperd4[\"final_asp1\"] = asp_cperd4[\"asp_Union\"].fillna(asp_cperd4[\"asp_item\"])\n", "asp_lp[\"item_id\"] = asp_lp[\"item_id\"].astype(str)\n", "asp_lp[\"Union\"] = asp_lp[\"item_id\"].str.cat(asp_lp[\"facility\"].copy())\n", "asp_cperd5 = pd.merge(\n", "    left=asp_cperd4,\n", "    right=asp_lp.loc[:, [\"Union\", \"for_asp\"]],\n", "    left_on=\"Union\",\n", "    right_on=\"Union\",\n", "    how=\"left\",\n", ")\n", "ASP_LP1 = asp_lp.rename(columns={\"item_id\": \"Item ID\", \"for_asp\": \"lp_asp_item\"})\n", "ASP_LP2 = ASP_LP1.loc[:, [\"Item ID\", \"lp_asp_item\"]]\n", "ASP_LP2.drop_duplicates(subset=[\"Item ID\"], keep=\"first\", inplace=True)\n", "asp_cperd6 = pd.merge(\n", "    left=asp_cperd5, right=ASP_LP2, left_on=\"Item ID\", right_on=\"Item ID\", how=\"left\"\n", ")\n", "asp_cperd6[\"final_asp1\"].fillna(asp_cperd6[\"for_asp\"])\n", "asp_cperd6[\"final_asp1\"].fillna(asp_cperd6[\"lp_asp_item\"])\n", "asp_cperd6[\"final_asp1\"] = np.where(\n", "    asp_cperd6[\"final_asp1\"] == 0, np.nan, asp_cperd6[\"final_asp1\"]\n", ")\n", "iteem_l0.drop_duplicates(subset=[\"item_id\"], keep=\"first\", inplace=True)\n", "item_1code = iteem_l0.loc[:, [\"item_id\", \"Variant_mrp\"]]\n", "item_1code[\"item_id\"] = item_1code[\"item_id\"].astype(str)\n", "asp_cperd7 = pd.merge(\n", "    left=asp_cperd6, right=item_1code, left_on=\"Item ID\", right_on=\"item_id\", how=\"left\"\n", ")\n", "asp_cperd7[\"final_asp\"] = asp_cperd7[\"final_asp1\"].fillna(\n", "    asp_cperd7[\"Variant_mrp\"] * 0.7\n", ")\n", "asp_cperd7[\"final_asp\"] = np.where(\n", "    asp_cperd7[\"final_asp\"] == 0, np.nan, asp_cperd7[\"final_asp\"]\n", ")\n", "asp_cperd7[\"final_asp\"].fillna(80, inplace=True)\n", "asp_cperd_final = asp_cperd7.loc[:, [\"Union\", \"final_asp\"]]\n", "ASP = pd.merge(\n", "    left=asp_cperd_final, right=cpd, left_on=\"Union\", right_on=\"Union\", how=\"left\"\n", ")\n", "ASP.drop_duplicates(subset=[\"Union\"], keep=\"first\", inplace=True)\n", "ASP = ASP.drop(columns={\"Union\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ASP[\"item_id\"] = ASP[\"item_id\"].astype(int)\n", "data_base3 = pd.merge(\n", "    left=data_base2,\n", "    right=ASP,\n", "    left_on=[\"item_id\", \"facility\"],\n", "    right_on=[\"item_id\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "item_code1 = item_code.sort_values([\"is_pl\"], ascending=[False])\n", "item_code1.drop_duplicates(subset=[\"item_id\"], keep=\"first\", inplace=True)\n", "data_base3 = pd.merge(\n", "    left=data_base3,\n", "    right=item_code1,\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "cat.drop_duplicates(subset=[\"item_id\"], keep=\"first\", inplace=True)\n", "cat1 = cat.rename(columns={\"name\": \"name_\"})\n", "data_base4 = pd.merge(\n", "    left=data_base3,\n", "    right=cat1.loc[\n", "        :, [\"item_id\", \"name_\", \"l0\", \"l1\", \"l2\", \"brand\", \"manf\", \"product_type\"]\n", "    ],\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "data_base4[\"is_pl\"].fillna(0, inplace=True)\n", "data_base4[\"is_pl\"] = data_base4[\"is_pl\"].astype(int)\n", "data_base4[\"name\"].fillna(data_base4[\"name_\"], inplace=True)\n", "data_base4[\"name\"].fillna(\"-\", inplace=True)\n", "data_base4[\"l0\"].fillna(\"-\", inplace=True)\n", "data_base4[\"l1\"].fillna(\"-\", inplace=True)\n", "data_base4[\"l2\"].fillna(\"-\", inplace=True)\n", "data_base4[\"brand\"].fillna(\"-\", inplace=True)\n", "data_base4[\"manf\"].fillna(\"-\", inplace=True)\n", "data_base4[\"product_type\"].fillna(\"-\", inplace=True)\n", "data_base4[\"Date_\"] = td\n", "Bucketx.drop_duplicates(subset=[\"item_id\", \"facility_id\"], keep=\"first\", inplace=True)\n", "Bucketab.drop_duplicates(subset=[\"item_id\", \"facility_id\"], keep=\"first\", inplace=True)\n", "data_base4 = pd.merge(\n", "    left=data_base4,\n", "    right=<PERSON>etx,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "data_base4 = pd.merge(\n", "    left=data_base4,\n", "    right=<PERSON><PERSON><PERSON>,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "data_base4[\"bucket\"].fillna(\"B\", inplace=True)\n", "data_base4[\"bucket_X\"].fillna(\"N\", inplace=True)\n", "data_base5 = data_base4.loc[\n", "    :,\n", "    [\n", "        \"Date_\",\n", "        \"item_id\",\n", "        \"facility\",\n", "        \"facility_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"bucket\",\n", "        \"bucket_X\",\n", "        \"is_pl\",\n", "        \"assortment_flag\",\n", "        \"min_price\",\n", "        \"final_asp\",\n", "        \"live_days\",\n", "        \"sales_qty\",\n", "    ],\n", "]\n", "data_base6 = pd.merge(\n", "    left=data_base5,\n", "    right=fforecast_data[{\"item_id\", \"facility\", \"qty\"}],\n", "    left_on=[\"item_id\", \"facility\"],\n", "    right_on=[\"item_id\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "data_base7 = pd.merge(\n", "    left=data_base6,\n", "    right=cat_input[\n", "        {\"facility_id\", \"item_id\", \"category_manager\", \"cat_input\", \"cat_pick_date\"}\n", "    ],\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "data_base8 = pd.merge(\n", "    left=data_base7,\n", "    right=Adjust_input1[{\"facility_id\", \"item_id\", \"Ad_qty\"}],\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "data_base9 = pd.merge(\n", "    left=data_base8,\n", "    right=month_input1.loc[:, [\"item_id\", \"facility\", \"base_qty\", \"cat_base_qty\"]],\n", "    left_on=[\"item_id\", \"facility\"],\n", "    right_on=[\"item_id\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "data_base9[\"l0_cap\"] = 1\n", "fcartdata = fcarts.loc[fcarts.forecast_date == td]\n", "temp = fcartdata.groupby([\"facility\"])\n", "resfc = temp[[\"forecast_carts\"]].agg(\"sum\").reset_index()\n", "data_base10 = pd.merge(\n", "    left=data_base9,\n", "    right=resfc,\n", "    left_on=[\"facility\"],\n", "    right_on=[\"facility\"],\n", "    how=\"left\",\n", ")\n", "actual_carts = sales_data.loc[:, [\"facility\", \"actual_carts\"]]\n", "actual_carts.drop_duplicates(subset=[\"facility\"], keep=\"first\", inplace=True)\n", "\n", "data_base11 = pd.merge(\n", "    left=data_base10,\n", "    right=actual_carts,\n", "    left_on=[\"facility\"],\n", "    right_on=[\"facility\"],\n", "    how=\"left\",\n", ")\n", "data_base11[\"actual_carts\"].fillna(0, inplace=True)\n", "data_base11 = data_base11.loc[\n", "    :,\n", "    [\n", "        \"Date_\",\n", "        \"item_id\",\n", "        \"facility\",\n", "        \"facility_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"bucket\",\n", "        \"bucket_X\",\n", "        \"is_pl\",\n", "        \"assortment_flag\",\n", "        \"min_price\",\n", "        \"final_asp\",\n", "        \"forecast_carts\",\n", "        \"actual_carts\",\n", "        \"live_days\",\n", "        \"sales_qty\",\n", "        \"base_qty\",\n", "        \"cat_base_qty\",\n", "        \"Ad_qty\",\n", "        \"l0_cap\",\n", "        \"cat_input\",\n", "        \"category_manager\",\n", "        \"cat_pick_date\",\n", "        \"qty\",\n", "    ],\n", "]\n", "data_base12 = data_base11.rename(\n", "    columns={\n", "        \"name\": \"item_name\",\n", "        \"final_asp\": \"asp\",\n", "        \"live_days\": \"live_flag\",\n", "        \"qty\": \"snorlax_qty\",\n", "        \"category_manager\": \"remarks\",\n", "        \"sales_qty\": \"actual_sales_qty\",\n", "        \"Date_\": \"date_\",\n", "        \"Ad_qty\": \"ad_qty\",\n", "    }\n", ")\n", "data_base12[\"updated_at\"] = dt.datetime.now().strftime(\"%Y-%m-%d %H:%M\")\n", "l2 = [\n", "    \"min_price\",\n", "    \"asp\",\n", "    \"forecast_carts\",\n", "    \"actual_carts\",\n", "    \"actual_sales_qty\",\n", "    \"base_qty\",\n", "    \"cat_base_qty\",\n", "    \"ad_qty\",\n", "    \"l0_cap\",\n", "    \"cat_input\",\n", "    \"snorlax_qty\",\n", "]\n", "for n in l2:\n", "    data_base12[n] = data_base12[n].astype(float)\n", "data_base12 = data_base12.rename(columns={\"live_falg\": \"live_flag\"})\n", "data_base13 = data_base12.loc[\n", "    (data_base12.l0 != \"Vegetables & Fruits\")\n", "    & (data_base12.l0 != \"Fashion and Lifestyle\")\n", "]\n", "fci1 = faci_faci1.loc[:, [\"cfacility\"]]\n", "fci = fci1.rename(columns={\"cfacility\": \"facility\"})\n", "data_base13 = pd.merge(\n", "    left=data_base13, right=fci, left_on=\"facility\", right_on=\"facility\", how=\"inner\"\n", ")\n", "data_base13.drop_duplicates(subset=[\"facility\", \"item_id\"], keep=\"first\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"inventory_forecast_db\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"varchar\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\"},\n", "        {\"name\": \"facility\", \"type\": \"varchar\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "        {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "        {\"name\": \"l0\", \"type\": \"varchar\"},\n", "        {\"name\": \"l1\", \"type\": \"varchar\"},\n", "        {\"name\": \"l2\", \"type\": \"varchar\"},\n", "        {\"name\": \"brand\", \"type\": \"varchar\"},\n", "        {\"name\": \"manf\", \"type\": \"varchar\"},\n", "        {\"name\": \"product_type\", \"type\": \"varchar\"},\n", "        {\"name\": \"bucket\", \"type\": \"varchar\"},\n", "        {\"name\": \"bucket_X\", \"type\": \"varchar\"},\n", "        {\"name\": \"is_pl\", \"type\": \"integer\"},\n", "        {\"name\": \"assortment_flag\", \"type\": \"integer\"},\n", "        {\"name\": \"min_price\", \"type\": \"float\"},\n", "        {\"name\": \"asp\", \"type\": \"float\"},\n", "        {\"name\": \"forecast_carts\", \"type\": \"float\"},\n", "        {\"name\": \"actual_carts\", \"type\": \"float\"},\n", "        {\"name\": \"live_flag\", \"type\": \"integer\"},\n", "        {\"name\": \"actual_sales_qty\", \"type\": \"float\"},\n", "        {\"name\": \"base_qty\", \"type\": \"float\"},\n", "        {\"name\": \"cat_base_qty\", \"type\": \"float\"},\n", "        {\"name\": \"ad_qty\", \"type\": \"float\"},\n", "        {\"name\": \"l0_cap\", \"type\": \"float\"},\n", "        {\"name\": \"cat_input\", \"type\": \"float\"},\n", "        {\"name\": \"remarks\", \"type\": \"varchar\"},\n", "        {\"name\": \"cat_pick_date\", \"type\": \"varchar(320)\"},\n", "        {\"name\": \"snorlax_qty\", \"type\": \"float\"},\n", "        {\"name\": \"updated_at\", \"type\": \"varchar\"},\n", "    ],\n", "    \"primary_key\": \"facility_id\",\n", "    \"sortkey\": \"Date_\",\n", "    # \"incremental_key\": incremental_key,\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "}\n", "pb.to_redshift(data_base13, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}