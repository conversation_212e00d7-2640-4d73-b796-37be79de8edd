{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "Redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = f\"\"\"\n", "select exp_attr_fin.*, prod_cat.l0,prod_cat.l1,\n", "(weighted_landing_price*total_doi_quantity) as \"expired_wlp_amount (doi)\"\n", "from (Select\n", " table1.*,\n", " Mrp*quantity as Total_mrp,\n", " weighted_landing_price*quantity as Total_landing_Price,\n", " cost_price*quantity as Total_cost_price,\n", " (weighted_landing_price*quantity) - (cost_price*quantity) as Tax,\n", "case when ((fifo_quantity+doi_quantity)>table1.quantity and doi_quantity>0) then\n", "(case when doi_quantity>fifo_quantity then quantity\n", "when doi_quantity=fifo_quantity then floor((quantity*1.0)/2.0)\n", "else 0 end)\n", "else doi_quantity end as total_doi_quantity,\n", "case when ((fifo_quantity+doi_quantity)>table1.quantity and fifo_quantity>0) then\n", "(case when fifo_quantity>doi_quantity then quantity\n", "when doi_quantity=fifo_quantity then CEIL((quantity*1.0)/2.0)\n", "else 0 end)\n", "else fifo_quantity end as total_fifo_quantity,\n", " total_doi_quantity*mrp as doi_mrp_amount,\n", " total_fifo_quantity*mrp as fifo_mrp_amount\n", "from\n", " (\n", " SELECT \n", "        fc.id as facility_id,\n", "        fc.name as facility_name,\n", "    i.outlet_id as outlet_id,\n", "    ro.name AS Outlet_name,\n", "     r.item_id as item,\n", "     i.variant_id as variant_id,\n", "     i.upc_id as upc,\n", "     r.name AS Product,\n", "(CASE WHEN r.outlet_type = 1 THEN 'F&V_type' WHEN r.outlet_type = 2 THEN 'Grocery_type' ELSE 'Not Assigned' END) AS Item_Type,\n", "     SUM(i.\"delta\") AS quantity,\n", "     r.variant_mrp AS Mrp,\n", "     r.variant_uom_text AS grammage,\n", "     convert_timezone('Asia/Kolkata',i.pos_timestamp)::date AS POS_Timestamp,\n", "     i2.name AS Update_type,\n", "     ibur.name,\n", "     r.is_pl,\n", "     i.weighted_lp as \"weighted_landing_price\",\n", "     coalesce(tax.cess,0) as cess,\n", "     coalesce(tax.cgst,0) as cgst,\n", "     coalesce(tax.sgst,0) as sgst,\n", "     i.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) as cost_price\n", " FROM\n", "    lake_ims.ims_inventory_log i\n", "         INNER JOIN\n", "     lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "         INNER JOIN\n", "     lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "     left join lake_crates.facility fc on ro.facility_id=fc.id\n", "         INNER JOIN\n", "     lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         LEFT JOIN\n", "     lake_rpc.product_tax tax ON r.item_id = tax.item_id and tax.location = 0\n", "       LEFT JOIN\n", "       lake_ims.ims_bad_inventory_update_log ibil on i.inventory_update_id=ibil.inventory_update_id\n", "       LEFT join \n", "       lake_ims.ims_bad_update_reason ibur on ibil.reason_id=ibur.id\n", " WHERE\n", "     i.inventory_update_type_id in (7,9,63, 11 , 12, 13, 64, 87, 88, 89) \n", "      AND convert_timezone('Asia/Kolkata',i.pos_timestamp)::date between current_date-40 AND current_date-1 \n", "      and (Update_type in ('bad_stock_near_expiry_product','bad_stock_expired_product')\n", "      or  ibur.name in ('STO Expiry','STO Near Expiry'))\n", "      --and ro.name not ilike '%%darks'\n", "    GROUP BY 1,2,3,4,5, 6,7, 8 , 9,11,12,13 ,14,15,16,17,18,19,20,21\n", "  ) table1\n", "  left join (\n", "--Attribution query\n", "WITH final as (\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\t\n", "              WHEN C1.NAME = C.name THEN C2.name\n", "              ELSE C1.name\n", "          END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "  FROM lake_cms.gr_product P\n", "  INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "  INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "  AND PCM.IS_PRIMARY=TRUE\n", "  INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "  INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "  INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id),\n", "product_mapping AS\n", "  ( SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "FROM lake_rpc.item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "WLP AS\n", "(Select item_id,\n", "        outlet_id,\n", "        landing_price\n", "From(        \n", "SELECT a.item_id,\n", "      outlet_id,\n", "      b.warehouse_id,\n", "      facility_id,\n", "      landing_price,\n", "      row_number() OVER (PARTITION BY facility_id,\n", "                                      a.item_id\n", "                         ORDER BY dt_ist DESC) AS row_rank,\n", "                        row_number() OVER (PARTITION BY a.item_id\n", "                                          ORDER BY dt_ist DESC) AS row_rank1\n", "FROM weighted_landing_price a\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_retail.warehouse_outlet_mapping\n", "  WHERE cloud_store_id IS NOT NULL) b ON a.outlet_id = b.cloud_store_id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_retail.view_console_outlet\n", "  WHERE active = TRUE) c ON b.warehouse_id = c.id\n", "WHERE landing_price IS NOT NULL\n", ") a where row_rank=1\n", ")\n", "\n", "SELECT inventory_update_date::date AS inventory_update_date,\n", "      outlet_id,\n", "      outlet_name,\n", "      item_id,\n", "      variant_id,\n", "      item_name,\n", "      item_type,\n", "      shelf_life,\n", "      l0,\n", "      l1,\n", "      l2,\n", "      update_name,\n", "      expired_grn AS Quantity,\n", "      variant_mrp,\n", "      landing_price,\n", "      expired_grn*variant_mrp AS Mrp_Amount,\n", "      \"fifo/doi\" AS \"fifo_doi\",\n", "      flag AS GRN_STO_Flag From\n", "(SELECT fd.*, pp.name AS item_name,\n", "          pp.variant_mrp,\n", "          pp.shelf_life AS shelf_life,\n", "          co.name AS outlet_name,\n", "          wlp.landing_price,\n", "          pm.l0,\n", "          pm.l1,\n", "          pm.l2,\n", "          CASE\n", "          WHEN pp.outlet_type=1 THEN 'FnV'\n", "          ELSE 'Grocery'\n", "          END AS Item_Type\n", "FROM metrics.expiry_fifo_doi_updated fd\n", "LEFT JOIN lake_rpc.product_product pp ON pp.variant_id=fd.variant_id\n", "LEFT JOIN lake_retail.console_outlet co ON co.id = fd.outlet_id\n", "LEFT JOIN product_mapping pm ON pm.item_id=fd.item_id\n", "LEFT JOIN WLP ON WLP.item_id=fd.item_id and WLP.outlet_id=fd.outlet_id\n", "WHERE \"fifo/doi\"<>'Inconclusive'\n", "AND inventory_update_date::date BETWEEN current_date-40 AND current_date-1 )\n", ")\n", "select inventory_update_date,outlet_id,item_id,outlet_name,\n", "      variant_id,\n", "      item_name,\n", "      item_type,\n", "      shelf_life,\n", "      l0,\n", "      l1,\n", "      l2,\n", "      update_name,\n", "      landing_price,variant_mrp,sum(quantity),\n", "      coalesce(sum(case when fifo_doi='FIFO' then quantity end),0) as fifo_quantity,\n", "      coalesce(sum(case when fifo_doi='DOI' then quantity end),0) as doi_quantity\n", "from final\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", ") as attr \n", "on attr.inventory_update_date=date(table1.POS_Timestamp)\n", "and attr.outlet_id=table1.outlet_id\n", "and attr.item_id=table1.item\n", "and attr.variant_id=table1.variant_id\n", "and attr.update_name=table1.update_type) as exp_attr_fin\n", "left join\n", "(SELECT distinct\n", "pp.variant_id,\n", "CAT.NAME AS l0,\n", "CAT1.NAME AS l1\n", "FROM\n", "lake_cms.GR_PRODUCT P\n", "INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1)as prod_cat\n", "on prod_cat.variant_id=exp_attr_fin.variant_id;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_summary = pd.read_sql_query(sql=Query, con=Redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"19EjtHBOqObT_3kvRV8eZ1_PB0k2fMWmfd8-6oW5YxlE\"\n", "input_sheet = pb.from_sheets(sheet_id, \"Facility-details\")\n", "input_sheet.drop([\"Zone\", \"Type\"], axis=\"columns\", inplace=True)\n", "input_sheet[\"Facility ID\"] = input_sheet[\"Facility ID\"].astype(int)\n", "input_sheet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_fin_summary = pd.merge(\n", "    doi_summary, input_sheet, left_on=\"facility_id\", right_on=\"Facility ID\", how=\"left\"\n", ")\n", "doi_fin_summary.drop(\"Facility ID\", axis=\"columns\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_fin_summary.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_fin_summary.pos_timestamp.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_fin_summary.pos_timestamp.min().month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"19EjtHBOqObT_3kvRV8eZ1_PB0k2fMWmfd8-6oW5YxlE\"\n", "sheet_name = \"Raw data\"\n", "pb.to_sheets(doi_fin_summary, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "today = date.today()\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "START_DATE = (datetime.now(tz) - timed<PERSON>ta(days=7)).date()\n", "END_DATE = (datetime.now(tz) - timed<PERSON>ta(days=1)).date()\n", "if (datetime.now(tz) - <PERSON><PERSON><PERSON>(days=1)).day > 7:\n", "    month = datetime.now(tz).month\n", "    res = doi_fin_summary[\n", "        pd.DatetimeIndex(doi_fin_summary[\"pos_timestamp\"]).month == month\n", "    ]\n", "else:\n", "    month = datetime.now(tz).month - 1\n", "    res = doi_fin_summary[\n", "        pd.DatetimeIndex(doi_fin_summary[\"pos_timestamp\"]).month == month\n", "    ]\n", "res_week = doi_fin_summary[\n", "    (doi_fin_summary[\"pos_timestamp\"] >= START_DATE)\n", "    & (doi_fin_summary[\"pos_timestamp\"] <= END_DATE)\n", "]\n", "print(START_DATE, END_DATE)\n", "print(month)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.pos_timestamp.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res_week.pos_timestamp.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a, b = res_week.pos_timestamp.min(), res_week.pos_timestamp.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c, d = res.pos_timestamp.min(), res.pos_timestamp.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(a, b, c, d)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH FC_MAPPING AS\n", "  (SELECT DISTINCT PCO.FACILITY_ID,\n", "                   CF.NAME AS FACILITY_NAME,\n", "                   BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,\n", "                   BOM.NAME AS BACKEND_MERCHANT_NAME,\n", "                   PCO.ID AS OUTLET_ID,\n", "                   PCO.NAME AS OUTLET_NAME\n", "   FROM lake_oms_bifrost.view_oms_merchant AS BOM\n", "   INNER JOIN lake_retail.console_outlet_cms_store AS CMS ON BOM.EXTERNAL_ID = CMS.CMS_STORE\n", "   AND CMS.ACTIVE = 1\n", "   AND CMS.CMS_UPDATE_ACTIVE = 1\n", "   AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "   INNER JOIN lake_retail.console_outlet AS PCO ON CMS.OUTLET_ID = PCO.ID\n", "   INNER JOIN lake_crates.facility AS CF ON CF.ID = PCO.FACILITY_ID\n", "   ),\n", "   \n", "   \n", "t1 as(\n", "Select distinct order_id from (\n", "Select \n", "suborder_id, order_id,\n", "json_extract_path_text(ose.extra,'to',true) as status\n", "from lake_oms_bifrost.oms_suborder_event ose\n", "Join lake_oms_bifrost.oms_suborder os ON os.id=ose.suborder_id\n", "where (convert_timezone('asia/kolkata',ose.update_ts))::date between %(start_date)s and %(end_date)s\n", "    and json_extract_path_text(ose.extra,'to',true) in ('BILLED')\n", "))   \n", "   \n", "Select distinct\n", "-- (o.update_ts + interval '5.5 HOUR')::date as deliverd_date,\n", "fc.FACILITY_ID as facility_id,\n", "fc.facility_name  as facility_name,\n", "--om.city_name,\n", "\n", "sum(o.total_cost) as gmv\n", "\n", "from  lake_oms_bifrost.oms_order o\n", "inner Join t1 on t1.order_id = o.id\n", "Left join \n", "  ( select aa.*, os.backend_merchant_id from \n", "  ( select oo.id as order_id ,  max(os.id) as suborder_id from \n", "  lake_oms_bifrost.oms_order oo \n", "  inner join lake_oms_bifrost.oms_suborder os ON os.order_id = oo.id\n", "   -- AND oo.CURRENT_STATUS = 'DELIVERED'\n", "    AND (oo.\"type\" is null or oo.\"type\" not ilike '%%internal%%')\n", "     and oo.station not ilike '%%b2b%%'\n", "     and (convert_timezone('asia/kolkata',oo.update_ts))::date between %(start_date)s and %(end_date)s\n", "     \n", "     group by 1\n", "     ) aa \n", "     inner join  lake_oms_bifrost.oms_suborder os ON os.id = aa.suborder_id) os  on os.order_id = o.id\n", "left join lake_oms_bifrost.view_oms_merchant om on o.merchant_id = om.id\n", "left join lake_oms_bifrost.oms_merchant bom ON bom.id = os.backend_merchant_id\n", "LEFT JOIN FC_MAPPING AS FC ON BOM.EXTERNAL_ID = FC.BACKEND_MERCHANT_ID\n", "where lower(om.city_name) not in ('not in service area')\n", "and lower(om.city_name) not like '%%b2b%%'\n", "and lower(om.city_name) not like '%%test%%'\n", "and fc.FACILITY_ID is not null\n", "group by 1,2\n", "order by 1,2\"\"\"\n", "billed_gmv_week = pd.read_sql_query(\n", "    sql=query, con=Redshift, params={\"start_date\": a, \"end_date\": b}\n", ")\n", "billed_gmv_mtd = pd.read_sql_query(\n", "    sql=query, con=Redshift, params={\"start_date\": c, \"end_date\": d}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.facility_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_gmv_week = billed_gmv_week.merge(\n", "    input_sheet, left_on=\"facility_id\", right_on=\"Facility ID\", how=\"inner\"\n", ")\n", "billed_gmv_mtd = billed_gmv_mtd.merge(\n", "    input_sheet, left_on=\"facility_id\", right_on=\"Facility ID\", how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_gmv_week = (\n", "    billed_gmv_week.groupby([\"Store-type\"]).agg({\"gmv\": sum}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_gmv_mtd = billed_gmv_mtd.groupby([\"Store-type\"]).agg({\"gmv\": sum}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_mtd = (\n", "    res[res[\"total_doi_quantity\"].notnull()]\n", "    .groupby([\"Store-type\"])[\"expired_wlp_amount (doi)\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_weekly = (\n", "    res_week[res_week[\"total_doi_quantity\"].notnull()]\n", "    .groupby([\"Store-type\"])[\"expired_wlp_amount (doi)\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_weekly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekly = billed_gmv_week.merge(doi_weekly, on=[\"Store-type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_mtd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd = billed_gmv_mtd.merge(doi_mtd, on=[\"Store-type\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tot = pd.DataFrame(\n", "    [\"Total\", mtd.gmv.sum(), mtd[\"expired_wlp_amount (doi)\"].sum()]\n", ").T.rename(columns={0: \"Store-type\", 1: \"gmv\", 2: \"expired_wlp_amount (doi)\"})\n", "tot1 = pd.DataFrame(\n", "    [\"Total\", weekly.gmv.sum(), weekly[\"expired_wlp_amount (doi)\"].sum()]\n", ").T.rename(columns={0: \"Store-type\", 1: \"gmv\", 2: \"expired_wlp_amount (doi)\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd = mtd.append(tot, \"sort=False\")\n", "weekly = weekly.append(tot1, \"sort=False\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd[\"expiry_percent\"] = mtd[\"expired_wlp_amount (doi)\"] / mtd[\"gmv\"] * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekly[\"expiry_percent\"] = weekly[\"expired_wlp_amount (doi)\"] / weekly[\"gmv\"] * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"19EjtHBOqObT_3kvRV8eZ1_PB0k2fMWmfd8-6oW5YxlE\"\n", "sheet_name = \"Weekly\"\n", "pb.to_sheets(weekly, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"19EjtHBOqObT_3kvRV8eZ1_PB0k2fMWmfd8-6oW5YxlE\"\n", "sheet_name = \"MTD\"\n", "pb.to_sheets(mtd, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}