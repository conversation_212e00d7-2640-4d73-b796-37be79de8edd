{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from gspread.exceptions import APIError"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1mXTqebNUBB1ZbVcFI782hWmOCzwXe2sYbOsVMJgp8Ik\"\n", "MetaData = pb.from_sheets(sheet_id, \"Stock Take\")\n", "\n", "outlets = list(MetaData[\"Outlet ID\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MetaData.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OutletId = 334"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SheetId = MetaData[MetaData['Outlet ID']==str(OutletId)]['Sheet ID']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SheetId.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SheetId=SheetId[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id = SheetId\n", "# PrimaryData = pb.from_sheets(sheet_id, \"Primary Cyclic\")\n", "# SecondaryData = pb.from_sheets(sheet_id,\"Secondary Cyclic\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PrimaryData['auditdate']= pd.to_datetime(PrimaryData['auditdate'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"auditdate\": \"Date on which Audit is Requested/Performed\",\n", "    \"facility\": \" \",\n", "    \"frequency\": \"An internal metric to measure importance location\",\n", "    \"location_id\": \"Unique Identity of Location\",\n", "    \"rankfinal\": \" Internal metric\",\n", "    \"weightedscore\": \"internal metric\",\n", "    \"key\": \"unique key for updating table\",\n", "    \"Audit Status (Yes/No)\": \" Whether audit was done or not\",\n", "    \"Discrepancy (Quantity Mismatch/BadStock)\": \" Discrepancy found during audit\",\n", "    \"Remarks\": \"Any Remarks given by auditor\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PrimaryData['Key'] = PrimaryData['auditdate'].astype(str)+'|'+PrimaryData['location_id']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_dtypes_primary = redshift_schema(PrimaryData)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"CyclicAuditPrimary\",\n", "#     \"column_dtypes\": column_dtypes_primary,\n", "#     \"primary_key\": [\"Key\"],\n", "#     \"sortkey\": [\"outlet_id_x\"],\n", "#     \"incremental_key\": \"auditdate\",\n", "#     \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NTE_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_redshift(PrimaryData, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for current_outlet in outlets:\n", "    SheetId = MetaData[MetaData[\"Outlet ID\"] == str(current_outlet)][\"Sheet ID\"]\n", "    SheetId.reset_index(drop=True, inplace=True)\n", "    SheetId = SheetId[0]\n", "    sheet_id = SheetId\n", "    try:\n", "        NTE_Data = pb.from_sheets(sheet_id, \"near_expiry\")\n", "\n", "        #### Transformations\n", "        NTE_Data[\"auditdate\"] = datetime.now()\n", "        NTE_Data[\"key\"] = NTE_Data[\"item_id\"] + \"|\" + NTE_Data[\"outlet_id\"]\n", "\n", "        NTE_Data = NTE_Data[\n", "            [\n", "                \"outlet_name\",\n", "                \"outlet_id\",\n", "                \"item_id\",\n", "                \"upc\",\n", "                \"variant_id\",\n", "                \"product_name\",\n", "                \"description\",\n", "                \"brand_name\",\n", "                \"manufacturer_date\",\n", "                \"nte_in_days\",\n", "                \"item_type\",\n", "                \"product_shelf_life\",\n", "                \"locations\",\n", "                \"loc_cnt\",\n", "                \"shelf_tot_qty\",\n", "                \"audit status (yes/no)\",\n", "                \"discrepancy (expiry/manufacturing/shelf life)\",\n", "                \"remarks\",\n", "                \"auditdate\",\n", "                \"key\",\n", "            ]\n", "        ]\n", "\n", "        #### Writing to Table\n", "        column_dtypes = redshift_schema(NTE_Data)\n", "\n", "        kwargs = {\n", "            \"schema_name\": \"metrics\",\n", "            \"table_name\": \"NTE_Audits\",\n", "            \"column_dtypes\": column_dtypes,\n", "            \"primary_key\": [\"Key\"],\n", "            \"sortkey\": [\"outlet_id\"],\n", "            \"incremental_key\": \"auditdate\",\n", "            \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "            \"table_description\": \"For storing near expiry item audit related data \",\n", "        }\n", "        print(NTE_Data.shape)\n", "        pb.to_redshift(NTE_Data, **kwargs)\n", "\n", "        print(\"Done for Outlet \" + str(current_outlet))\n", "    except:\n", "        message_text = \"failed for outlet_id - \" + str(current_outlet)\n", "        print(message_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}