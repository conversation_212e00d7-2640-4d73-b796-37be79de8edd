{"cells": [{"cell_type": "code", "execution_count": null, "id": "b76e0217-5d1a-40c0-aafa-6f50d35050ce", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime\n", "import time\n", "import uuid\n", "import sqlalchemy\n", "from sqlalchemy.engine.url import URL\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "2e6deae8-523d-4ace-b841-802d51459306", "metadata": {}, "outputs": [], "source": ["# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "5e22e604-7d5f-4c8a-9ce7-194fb6d13634", "metadata": {}, "outputs": [], "source": ["def get_retail_connection():\n", "    retail_conn_params = pb.get_secret(\"retail/reports/mysql/data_user\")\n", "    engine_url = URL(\n", "        drivername=\"mysql+mysqldb\",\n", "        username=retail_conn_params[\"user\"],\n", "        password=retail_conn_params[\"password\"],\n", "        host=\"retail-reports-cluster.cluster-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com\",\n", "        database=\"reports\",\n", "    )\n", "    engine = sqlalchemy.create_engine(\n", "        engine_url, pool_size=1, echo=False, pool_pre_ping=True\n", "    )\n", "    return engine.connect()"]}, {"cell_type": "code", "execution_count": null, "id": "6d5ceb8f-0d0b-48e4-b8c1-047d9964e80d", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1X8rAc78KaU-zlvZsB1MHNB7wVD5rFbYueHEY6qy_yyw\"\n", "sheet_name = \"RTV_input_tbl\"\n", "df = pb.from_sheets(sheet_id, sheet_name)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5b41fd98-c939-45e2-bb6c-316ae8c52455", "metadata": {}, "outputs": [], "source": ["inp1 = df[[\"mfg_id\"]]\n", "inp1 = inp1[inp1[\"mfg_id\"] != \"\"].astype(int)\n", "inp1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d6065f55-afb5-49c5-9bdd-8ec7a15f99b9", "metadata": {}, "outputs": [], "source": ["inp2 = df[[\"item_id\"]]\n", "inp2 = inp2[inp2[\"item_id\"] != \"\"].astype(int)\n", "inp2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2ff8dec5-59b0-402d-873b-66120371ab24", "metadata": {}, "outputs": [], "source": ["inp = df[[\"facility_id\", \"manufacturer_id\"]]\n", "inp = inp[(inp[\"facility_id\"] != \"\") & (inp[\"manufacturer_id\"] != \"\")]\n", "inp = inp.astype(int)\n", "inp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "71d90826-c273-44a3-a223-f32f4783b5bd", "metadata": {}, "outputs": [], "source": ["inp4 = df[[\"fid\", \"item\"]]\n", "inp4 = inp4[(inp4[\"fid\"] != \"\") & (inp4[\"item\"] != \"\")]\n", "inp4 = inp4.astype(int)\n", "inp4.columns = [\"facility_id\", \"item_id\"]\n", "inp4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "835d3d30-af41-4be2-aea0-20b6a9bc540e", "metadata": {}, "outputs": [], "source": ["city_item = df[[\"city_id\", \"item_id2\"]]\n", "city_item = city_item[(city_item[\"city_id\"] != \"\") & (city_item[\"item_id2\"] != \"\")]\n", "city_item = city_item.astype(int)\n", "city_item = city_item.rename(columns={\"item_id2\": \"item_id\"})\n", "city_item.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9d017224-296d-4902-8006-dfba8bbd4c31", "metadata": {}, "outputs": [], "source": ["city_mfg = df[[\"city_id2\", \"mfg_id2\"]]\n", "city_mfg = city_mfg[(city_mfg[\"city_id2\"] != \"\") & (city_mfg[\"mfg_id2\"] != \"\")]\n", "city_mfg = city_mfg.astype(int)\n", "city_mfg.columns = [\"city_id\", \"manufacturer_id\"]\n", "city_mfg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "32126f2f-2d22-460c-a697-101c37f03dad", "metadata": {}, "outputs": [], "source": ["len(inp1), len(inp2), len(inp), len(inp4), len(city_item), len(city_mfg)"]}, {"cell_type": "code", "execution_count": null, "id": "69b90b98-4c43-484f-b981-864dc92e63f9", "metadata": {}, "outputs": [], "source": ["f = pd.read_sql_query(\n", "    \"\"\"\n", "select distinct r.facility_id, r.tax_location_id city_id\n", "from lake_retail.console_outlet r\n", "join lake_crates.facility f on f.id = r.facility_id\n", "where r.business_type_id in (1,7,12,19,20)\n", "and device_id != 47\n", "\"\"\",\n", "    presto,\n", ")\n", "f.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "014be9a1-dde2-44b8-bb20-14b126d588c8", "metadata": {}, "outputs": [], "source": ["res1 = pd.read_sql_query(\n", "    f\"\"\"\n", "select distinct item_id\n", "from lake_rpc.product_product prod\n", "join lake_rpc.product_brand pb on pb.id = prod.brand_id\n", "where pb.manufacturer_id in {*inp1['mfg_id'],}\n", "\"\"\",\n", "    presto,\n", ")\n", "res1.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f601d859-f900-47a9-9fdc-a4b4a30d6869", "metadata": {}, "outputs": [], "source": ["res2 = pd.concat([res1, inp2], axis=0)\n", "res2.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7e95b434-e68d-4cb6-8a28-6e0b7bb59153", "metadata": {}, "outputs": [], "source": ["res2 = res2.merge(f[\"facility_id\"], how=\"cross\")"]}, {"cell_type": "code", "execution_count": null, "id": "80a56457-dc0d-4c6e-ac29-08c9e00ac907", "metadata": {}, "outputs": [], "source": ["f_mfg = city_mfg.merge(f, on=\"city_id\")\n", "f_mfg = f_mfg[[\"facility_id\", \"manufacturer_id\"]]\n", "f_mfg.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5a4cd59a-9041-4375-9fea-92f77b95b50c", "metadata": {}, "outputs": [], "source": ["inp = pd.concat([inp, f_mfg], axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "780bb737-d3c7-47a3-a2f4-99dc3a2384a1", "metadata": {}, "outputs": [], "source": ["res = pd.read_sql_query(\n", "    f\"\"\"\n", "select distinct manufacturer_id, item_id\n", "from lake_rpc.product_product prod\n", "join lake_rpc.product_brand pb on pb.id = prod.brand_id\n", "where pb.manufacturer_id in {*inp['manufacturer_id'].unique(),}\n", "\"\"\",\n", "    presto,\n", ")\n", "res.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "71d85eae-bf72-4f6d-b3fa-750b66735e11", "metadata": {}, "outputs": [], "source": ["res = inp.merge(res, on=\"manufacturer_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "f2003723-d300-4672-9781-cfc57faf3168", "metadata": {}, "outputs": [], "source": ["f_item = city_item.merge(f, on=\"city_id\")\n", "f_item = f_item[[\"facility_id\", \"item_id\"]]\n", "f_item.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b4089f39-f76f-411c-adb7-fc7781bfb403", "metadata": {}, "outputs": [], "source": ["rtv_df = pd.concat([res2, res, inp4, f_item], axis=0)\n", "rtv_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1009ac75-e1f3-467a-bb9f-64c8736cce6a", "metadata": {}, "outputs": [], "source": ["len(rtv_df)"]}, {"cell_type": "code", "execution_count": null, "id": "e50a1c84-b00b-4b4e-9d0f-18300ef7cb01", "metadata": {}, "outputs": [], "source": ["rtv_df = rtv_df[[\"facility_id\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "497d0ed1-bc92-440a-9af7-dc303791effe", "metadata": {}, "outputs": [], "source": ["len(rtv_df)"]}, {"cell_type": "code", "execution_count": null, "id": "080b2598-c842-4f04-a6fb-8253adf6ced1", "metadata": {}, "outputs": [], "source": ["rtv_df.insert(0, \"id\", [uuid.uuid4().hex for _ in range(len(rtv_df))])"]}, {"cell_type": "code", "execution_count": null, "id": "3e2f3945-37fd-46af-957c-1339b147d3c8", "metadata": {}, "outputs": [], "source": ["rtv_df[\"snapshot_datetime\"] = datetime.datetime.now()\n", "rtv_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1b606404-4170-40c2-ba06-41c297dc2b4b", "metadata": {}, "outputs": [], "source": ["assert not rtv_df[\"facility_id\"].isnull().values.any()\n", "assert not rtv_df[\"item_id\"].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "id": "32c8df8b-fbbe-4660-be3b-40085532891c", "metadata": {}, "outputs": [], "source": ["conn = get_retail_connection()"]}, {"cell_type": "code", "execution_count": null, "id": "f9c09a7c-6935-4a8a-9f86-ad65993c36a2", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "rtv_df.to_sql(\n", "    \"rtv_elg_inputs\",\n", "    con=conn,\n", "    if_exists=\"append\",\n", "    index=False,\n", "    chunksize=5000,\n", "    method=\"multi\",\n", ")\n", "print((time.time() - start) / 60, \"min\")"]}, {"cell_type": "code", "execution_count": null, "id": "62f0c809-9fea-45c9-b596-9a590f68b445", "metadata": {}, "outputs": [], "source": ["rtv_df[[\"id\", \"snapshot_datetime\"]].head(1).to_sql(\n", "    \"rtv_elg_inputs_run\", con=conn, if_exists=\"append\", index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8a251695-8027-4823-a9f8-a70245b5aebc", "metadata": {}, "outputs": [], "source": ["del rtv_df[\"id\"]\n", "rtv_df.rename(columns={\"snapshot_datetime\": \"updated_at\"}, inplace=True)\n", "rtv_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5e338c09-f9fd-4820-8a96-dc943f132849", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"rtv_elg_inputs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row entry timestamp in utc\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"item_id\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"RTV Eligible SKU's updated values\",\n", "}\n", "\n", "pb.to_redshift(rtv_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6dee7efc-b052-4ba5-850b-0dea4afef33b", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"rtv_elg_inputs_log\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row entry timestamp in utc\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"RTV Eligible SKU's log values\",\n", "}\n", "\n", "pb.to_redshift(rtv_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3539a3f0-734d-4f69-bbbc-419581fb0da6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}