{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Query = \"\"\"\n", "WITH table1_OB AS\n", " (SELECT *,\n", "         CASE\n", "              WHEN city IN ('Bengaluru',\n", "                                 'Chennai',\n", "                                 'Guwahati',\n", "                                 'Hyderabad',\n", "                                 'Kolkata') THEN 'South'\n", "              WHEN city IN ('Ahmedabad',\n", "                                 'Indore',\n", "                                 'Mumbai',\n", "                                 'Pune') THEN 'West'\n", "              ELSE 'North'\n", "          END AS ZONE\n", "   FROM metrics.received_synced_orders_test2 t1\n", "   WHERE t1.scheduled_date between current_date and current_date),\n", "table2_OB AS\n", " (SELECT  order_id as suborder_id,\n", "          outlet_id AS outlet_idx,\n", "          outlet_name AS outlet_namex,\n", "          city_id AS city_idx,\n", "          city AS cityx,\n", "          facility_id AS facility_idx,\n", "          facility_name,\n", "          date(scheduled_delivery_at) as scheduled_delivery_at,\n", "          outlet_pick_list_id,\n", "          pick_list_state_name,\n", "          hand_over_time,\n", "          pick_list_created_at,\n", "          picklist_created_at,\n", "          picklist_assigned_at,\n", "          picklist_in_progress,\n", "          picklist_completed_at,\n", "          picklist_billed_at,\n", "          picklist_on_hold_at,\n", "          picklist_resumed_at,\n", "          picklist_zero_procured_at,\n", "          picklist_discarded_at,\n", "          picklist_expired_at,\n", "          order_creation_timestamp,\n", "          order_billing_timestamp,\n", "          order_cancellation_timestamp,\n", "          order_discard_timestamp,\n", "          order_cancelled_nonbillable_timestamp,\n", "          order_cancelled_billed_timestamp,\n", "          order_cancelled_discarded_timestamp,\n", "          min(pick_list_item_quantity_updated_at) AS pick_list_item_quantity_updated_at,\n", "          count(item_id) AS item_cnt,\n", "          sum(required_quantity) AS required_quantity,\n", "          sum(picked_quantity) AS picked_quantity\n", "   FROM metrics.outbound t2\n", "   WHERE date(t2.scheduled_delivery_at)>=CURRENT_DATE\n", "     AND date(t2.scheduled_delivery_at)<=CURRENT_DATE\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12,\n", "            13,\n", "            14,\n", "            15,\n", "            16,\n", "            17,\n", "            18,\n", "            19,\n", "            20,\n", "            21,\n", "            22,\n", "            23,\n", "            24,\n", "            25,\n", "            26,\n", "            27,\n", "            28,\n", "            29),\n", "table3_OB AS\n", "(SELECT table1_OB.*,table2_OB.*\n", " FROM table1_OB\n", " LEFT JOIN table2_OB ON table1_OB.id=table2_OB.suborder_id\n", " LEFT JOIN consumer.retail_console_outlet rtco on rtco.id=table1_OB.outlet_id\n", " LEFT JOIN consumer.pos_console_company_type tp on rtco.company_type_id = tp.id where tp.id in (9,16)\n", "  and rtco.active = 1 and rtco.device_id <> 47)\n", "Select * from table3_OB \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data = pd.read_sql_query(sql=OB_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"scheduled_delivery_at\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data.facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data.columns\n", "OB_Data = OB_Data.filter(\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city\",\n", "        \"facility\",\n", "        \"backend_merchant_name\",\n", "        \"order_id\",\n", "        \"backend_merchant_id\",\n", "        \"id\",\n", "        \"current_status\",\n", "        \"type\",\n", "        \"scheduled_delivery_at\",\n", "        \"pick_list_state_name\",\n", "        \"hand_over_time\",\n", "        \"pick_list_created_at\",\n", "        \"picklist_created_at\",\n", "        \"picklist_assigned_at\",\n", "        \"picklist_in_progress\",\n", "        \"picklist_completed_at\",\n", "        \"picklist_billed_at\",\n", "        \"picklist_on_hold_at\",\n", "        \"picklist_resumed_at\",\n", "        \"picklist_zero_procured_at\",\n", "        \"picklist_discarded_at\",\n", "        \"picklist_expired_at\",\n", "        \"order_creation_timestamp\",\n", "        \"order_billing_timestamp\",\n", "        \"order_cancellation_timestamp\",\n", "        \"order_discard_timestamp\",\n", "        \"order_cancelled_nonbillable_timestamp\",\n", "        \"order_cancelled_billed_timestamp\",\n", "        \"order_cancelled_discarded_timestamp\",\n", "        \"pick_list_item_quantity_updated_at\",\n", "        \"item_cnt\",\n", "        \"required_quantity\",\n", "        \"procurement_amount\",\n", "        \"picked_quantity\",\n", "        \"zone\",\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Billed Orders "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BilledOrders=OB_Data.filter(['id', 'order_id','picklist_billed_at','facility_id', 'city_id', 'city','facility','scheduled_delivery_at','zone'])\n", "# BilledOrders['BilledFlag']=~pd.isna(BilledOrders.picklist_billed_at)\n", "# BilledOrdersGrouped = BilledOrders.groupby(['order_id','facility_id', 'city_id', 'city','facility','scheduled_delivery_at','zone'], as_index=False)\n", "# BilledFinal=BilledOrdersGrouped.aggregate(\n", "#     {\n", "#          'id':'nunique',\n", "#         'BilledFlag':sum\n", "#     }\n", "# )\n", "# BilledFinal.rename(columns={'id': 'Subordercount',\n", "#                             'BilledFlag':'BilledSuborders'\n", "#                          }, inplace=True)\n", "# BilledFinal['BilledOrderFlag'] =BilledFinal['Subordercount']==BilledFinal['BilledSuborders']\n", "# BilledFinal=BilledFinal.filter(['order_id','BilledOrderFlag'])\n", "# OB_Data = pd.merge(OB_Data,BilledFinal, on='order_id', how='left')\n", "# # OB_Data.columns\n", "# BilledFinal"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manipulatation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"BilledFlag\"] = ~pd.isna(OB_Data.picklist_billed_at)\n", "OB_Data[\"CompletedFlag\"] = ~pd.isna(OB_Data.picklist_completed_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"PickedCompleted\"] = OB_Data[\"CompletedFlag\"] * OB_Data[\"picked_quantity\"]\n", "OB_Data[\"RequiredCompleted\"] = OB_Data[\"CompletedFlag\"] * OB_Data[\"required_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"FullOrders\"] = OB_Data[\"required_quantity\"] == OB_Data[\"picked_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"IsGrocery\"] = ~OB_Data.outlet_name.str.contains(\"cold\", case=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"FullGrocery\"] = OB_Data[\"IsGrocery\"] & OB_Data[\"FullOrders\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"CompletedGrocery\"] = OB_Data[\"IsGrocery\"] & OB_Data[\"CompletedFlag\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DiffP2B = OB_Data[\"picklist_billed_at\"] - OB_Data[\"picklist_assigned_at\"]\n", "DiffP2H = OB_Data[\"hand_over_time\"] - OB_Data[\"picklist_assigned_at\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data[\"P2B\"] = list(map(lambda x: x.seconds / 3600, DiffP2B))\n", "OB_Data[\"P2H\"] = list(map(lambda x: x.seconds / 3600, DiffP2H))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data_Grouped = OB_Data.groupby(\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city\",\n", "        \"facility\",\n", "        \"scheduled_delivery_at\",\n", "        \"zone\",\n", "    ],\n", "    as_index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data_Grouped_Ancestor = OB_Data.groupby(\n", "    [\"facility_id\", \"city_id\", \"city\", \"facility\", \"scheduled_delivery_at\", \"zone\"],\n", "    as_index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def percentile(n):\n", "    def percentile_(x):\n", "        return np.percentile(x, n)\n", "\n", "    percentile_.__name__ = \"percentile_%s\" % n\n", "    return percentile_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = np.arange(1, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OB_Data.fillna(0, inplace= True)\n", "OB_Data[[\"P2B\", \"P2H\"]] = OB_Data[[\"P2B\", \"P2H\"]].fillna(value=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final = OB_Data_Grouped.agg(\n", "    {\n", "        \"BilledFlag\": sum,\n", "        \"CompletedFlag\": sum,\n", "        \"FullOrders\": sum,\n", "        \"CompletedGrocery\": sum,\n", "        \"FullGrocery\": sum,\n", "        \"PickedCompleted\": sum,\n", "        \"RequiredCompleted\": sum,\n", "        \"id\": \"nunique\",\n", "        \"IsGrocery\": sum,\n", "        \"P2B\": percentile(95),\n", "        \"P2H\": percentile(95),\n", "        \"procurement_amount\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final[\"IsOutletGrocery\"] = ~OB_final.outlet_name.str.contains(\"cold\", case=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final[\"P2B\"] = OB_final[\"IsOutletGrocery\"] * OB_final[\"P2B\"]\n", "OB_final[\"P2H\"] = OB_final[\"IsOutletGrocery\"] * OB_final[\"P2H\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Ancestor Part "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor = OB_Data_Grouped_Ancestor.agg({\"order_id\": \"nunique\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading capacity details from capacity system\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "    warehouse_external_id AS backend_merchant_id,\n", "    warehouse_name AS backend_merchant_name,\n", "    DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "    TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "    TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "    slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "    warehouse_planned AS given_capacity,\n", "    warehouse_actual AS capacity_utilised,\n", "        warehouse_available AS capacity_under_utilised,\n", "        min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "where warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9) a\n", "where\n", "delivery_date between current_date and current_date\n", "and given_capacity > 0\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_details = pd.read_sql_query(query, capacity_system)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct cms.outlet_id,o.name as outlet,cms.cms_store from\n", "consumer.oms_merchant m join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join consumer.pos_console_outlet o on cms.outlet_id= o.id \n", "where cms.active = 1 and cms.cms_update_active = 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cms_outlet_details = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"backend_merchant_id\",\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"planned_capacity\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "# warehouse_capacities = warehouse_capacities[warehouse_capacities.outlet_id == int(OUTLET_ID)]\n", "# warehouse_capacities[\"delivery_date\"] = pd.to_datetime(warehouse_capacities[\"delivery_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities_Grouped = warehouse_capacities.groupby(\n", "    [\"outlet_id\", \"delivery_date\"], as_index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacitiesfinal = warehouse_capacities_Grouped.agg({\"Capacity\": max})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacitiesfinal.delivery_date.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OTB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OTB_Query = \"\"\"\n", "  With table1_OTB AS\n", "  (SELECT t1.*,\n", "          CASE WHEN t1.billing_time=0 THEN 1\n", "              ELSE 0 END AS unbilled_orders,\n", "              t2.current_status\n", "  FROM metrics.otb t1\n", "  RIGHT JOIN metrics.received_synced_orders_test2 t2 ON t1.order_id=t2.id),\n", "  table2_OTB AS\n", "  (SELECT \n", "          date(scheduled_delivery_time) AS scheduled_delivery_date,\n", "          outlet_id,\n", "          outlet_name,\n", "          city_id,\n", "          city_name,\n", "          facility_id,\n", "          facility_name,\n", "          otb,\n", "          current_status,\n", "          count(distinct pick_list_id) AS COUNT,\n", "          sum(unbilled_orders) AS unbilled_orders\n", "  FROM table1_OTB\n", "  WHERE date(scheduled_delivery_time) between current_date and current_date\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9),\n", "                                  table3_OTB AS\n", "  (SELECT\n", "          scheduled_delivery_date,\n", "          outlet_id,\n", "          outlet_name,\n", "          city_id,\n", "          city_name,\n", "          facility_id,\n", "          facility_name,\n", "          sum(COUNT) AS Total_Orders_for_OTB,\n", "          sum(UnBilled_Orders) AS UnBilled_Orders,\n", "          sum(CASE\n", "                  WHEN otb='Delayed' THEN COUNT\n", "              END) AS Delayed_Orders,\n", "          sum(CASE\n", "                  WHEN otb='On_time' THEN COUNT\n", "              END) AS On_time_Orders\n", "  FROM table2_OTB where current_status not in ('CANCELLED','REPROCURED')\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7)\n", "Select * from table3_OTB\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OTB_Data = pd.read_sql_query(OTB_Query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OTB_Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OTB_Data = OTB_Data.filter(\n", "    [\n", "        \"scheduled_delivery_date\",\n", "        \"outlet_id\",\n", "        \"total_orders_for_otb\",\n", "        \"unbilled_orders\",\n", "        \"delayed_orders\",\n", "        \"on_time_orders\",\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON> "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Complaints_Query = \"\"\"\n", "with complaints as (\n", "SELECT outlet_id,\n", "       date(delivery_date)+2 AS DeliveryDate,\n", "       count(distinct order_id) AS TotalComplaints\n", "FROM metrics.complaints\n", "WHERE complaint_type='VALID' and general_category='Grocery'\n", "  AND delivery_date between CURRENT_DATE-2 and CURRENT_DATE-2\n", "  AND l1_category_id<>1487\n", "GROUP BY outlet_id,\n", "         DeliveryDate\n", "         ),\n", "ItemMissingData as (\n", "SELECT outlet_id,\n", "       date(delivery_date)+2 AS DeliveryDate,\n", "       count(distinct order_id) AS ItemMissing\n", "FROM metrics.complaints \n", "WHERE complaint_type='VALID' and general_category='Grocery'\n", "  AND complaint_category in ('Items missing - cart')\n", "  AND l1_category_id<>1487\n", "  AND delivery_date between CURRENT_DATE-2 and CURRENT_DATE-2\n", "GROUP BY outlet_id,\n", "         DeliveryDate\n", "         )\n", "         \n", "Select a.outlet_id,\n", "       a.<PERSON>,\n", "       TotalComplaints,\n", "       ItemMissing\n", "from complaints a left join ItemMissingData b on a.outlet_id = b.outlet_id\n", "and a.DeliveryDate=b.DeliveryDate\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ComplaintsData = pd.read_sql_query(sql=Complaints_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ComplaintsData.sort_values(\"deliverydate\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OB Comprehensive Final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final1 = pd.merge(\n", "    OB_final,\n", "    warehouse_capacitiesfinal,\n", "    left_on=[\"outlet_id\", \"scheduled_delivery_at\"],\n", "    right_on=[\"outlet_id\", \"delivery_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final2 = pd.merge(\n", "    OB_final1,\n", "    OTB_Data,\n", "    left_on=[\"outlet_id\", \"scheduled_delivery_at\"],\n", "    right_on=[\"outlet_id\", \"scheduled_delivery_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3 = pd.merge(\n", "    OB_final2,\n", "    Comp<PERSON>tsData,\n", "    left_on=[\"outlet_id\", \"scheduled_delivery_at\"],\n", "    right_on=[\"outlet_id\", \"deliverydate\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.facility.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ancestor Capacity Part"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3_Grouped = OB_final3.groupby(\n", "    [\"facility_id\", \"scheduled_delivery_at\"], as_index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3_Cap = OB_final3_Grouped.agg({\"Capacity\": sum})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor_Capacity = pd.merge(\n", "    O<PERSON>_<PERSON><PERSON><PERSON>, OB_final3_Cap, on=[\"facility_id\", \"scheduled_delivery_at\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor_Capacity.rename(columns={\"order_id\": \"AncestorOrders\",}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor_Capacity.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_Ancestor_Capacity[\"Key_id\"] = (\n", "    OB_Ancestor_Capacity.facility_id.astype(str)\n", "    + \"|\"\n", "    + OB_Ancestor_Capacity.scheduled_delivery_at.astype(str)\n", ")\n", "OB_Ancestor_Capacity[\"scheduled_delivery_at\"] = pd.to_datetime(\n", "    OB_Ancestor_Capacity[\"scheduled_delivery_at\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Suborder Part Redshift "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.rename(\n", "    columns={\n", "        \"BilledFlag\": \"BilledOrders\",\n", "        \"CompletedFlag\": \"CompletedOrders\",\n", "        \"FullOrders\": \"FullOrders\",\n", "        \"id\": \"SubOrders\",\n", "        \"IsGrocery\": \"GroceryOrders\",\n", "        \"PickedCompleted\": \"Picked_Quantity\",\n", "        \"RequiredCompleted\": \"Required_Quantity\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3[\"Key_id\"] = (\n", "    OB_final3.outlet_id.astype(str) + \"|\" + OB_final3.scheduled_delivery_at.astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3 = OB_final3.filter(\n", "    [\n", "        \"Key_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city\",\n", "        \"facility\",\n", "        \"scheduled_delivery_at\",\n", "        \"zone\",\n", "        \"BilledOrders\",\n", "        \"CompletedOrders\",\n", "        \"FullOrders\",\n", "        \"CompletedGrocery\",\n", "        \"FullGrocery\",\n", "        \"SubOrders\",\n", "        \"GroceryOrders\",\n", "        \"P2B\",\n", "        \"P2H\",\n", "        \"Picked_Quantity\",\n", "        \"Required_Quantity\",\n", "        \"total_orders_for_otb\",\n", "        \"unbilled_orders\",\n", "        \"delayed_orders\",\n", "        \"on_time_orders\",\n", "        \"totalcomplaints\",\n", "        \"itemmissing\",\n", "        \"procurement_amount\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.fillna(0, inplace=True)\n", "OB_final3[\"scheduled_delivery_at\"] = pd.to_datetime(OB_final3[\"scheduled_delivery_at\"])\n", "OB_final3[\"total_orders_for_otb\"] = OB_final3[\"total_orders_for_otb\"].astype(int)\n", "OB_final3[\"unbilled_orders\"] = OB_final3[\"unbilled_orders\"].astype(int)\n", "OB_final3[\"itemmissing\"] = OB_final3[\"itemmissing\"].astype(float)\n", "OB_final3[\"totalcomplaints\"] = OB_final3[\"totalcomplaints\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OB_final3.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(OB_final3)\n", "column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"OutboundExecutive2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"Key_id\"],\n", "    \"sortkey\": [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city\",\n", "        \"facility\",\n", "    ],\n", "    \"incremental_key\": \"scheduled_delivery_at\",\n", "    \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(OB_final3, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OB_Query = \"\"\"\n", "# Select * from metrics.outboundexecutive2\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data = pd.read_sql_query(sql=OB_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data.scheduled_delivery_at.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data=Data[Data['scheduled_delivery_at'] == '2020-03-31 00:00:00']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data.facility.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Capacity and Ancestors at Facility Level "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(OB_Ancestor_Capacity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"AncestorCapacity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"Key_id\"],\n", "    \"sortkey\": [\"facility_id\", \"city\", \"facility\"],\n", "    \"incremental_key\": \"scheduled_delivery_at\",\n", "    \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(OB_Ancestor_Capacity, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OB_Query = \"\"\"\n", "# Select * from metrics.AncestorCapacity\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data = pd.read_sql_query(sql=OB_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data.scheduled_delivery_at.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data[Data['scheduled_delivery_at']=='2020-04-14T00:00:00.000000000']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}