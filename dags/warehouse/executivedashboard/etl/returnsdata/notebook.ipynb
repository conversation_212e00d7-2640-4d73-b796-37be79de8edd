{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Query = \"\"\"\n", "SELECT     a.*,\n", "           CASE\n", "              WHEN d.id IN (1,\n", "                            18,\n", "                            22,\n", "                            45,\n", "                            48,\n", "                            51,\n", "                            56,\n", "                            68) THEN 'West'\n", "              WHEN d.id IN (3,\n", "                            14,\n", "                            24,\n", "                            32,\n", "                            34,\n", "                            42,\n", "                            43,\n", "                            57,\n", "                            63) THEN 'South'\n", "              ELSE 'North'\n", "          END AS ZONE,\n", "          c.retail_store_id,\n", "          d.id AS facility_id,\n", "          d.name\n", "   FROM metrics.returns_at_facility a \n", "   LEFT JOIN consumer.retail_console_outlet b ON b.id=a.outlet_id\n", "   LEFT JOIN consumer.warehouse_outlet_mapping c ON c.retail_store_id=a.outlet_id\n", "   LEFT JOIN consumer.retail_warehouse_facility d ON d.id= b.facility_id\n", "   where date(a.billed_at) >= current_date-30\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data = pd.read_sql_query(sql=Ret_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data[\"billed_at\"].max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data[\"billed_at\"] = Ret_Data[\"billed_at\"].dt.date\n", "Ret_Data[\"expectedamount\"] = Ret_Data[\"required_quantity\"] * Ret_Data[\"selling_price\"]\n", "Ret_Data[\"inwardamount\"] = Ret_Data[\"inward_quantity\"] * Ret_Data[\"selling_price\"]\n", "\n", "Ret_Data[\"goodamount\"] = Ret_Data[\"good_quantity\"] * Ret_Data[\"selling_price\"]\n", "Ret_Data[\"badamount\"] = Ret_Data[\"bad_quantity\"] * Ret_Data[\"selling_price\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data_Grouped = Ret_Data.groupby(\n", "    [\"zone\", \"facility_id\", \"name\", \"billed_at\"], as_index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data_final = Ret_Data_Grouped.agg(\n", "    {\n", "        \"required_quantity\": sum,\n", "        \"inward_quantity\": sum,\n", "        \"good_quantity\": sum,\n", "        \"bad_quantity\": sum,\n", "        \"expectedamount\": sum,\n", "        \"inwardamount\": sum,\n", "        \"goodamount\": sum,\n", "        \"badamount\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret2 = \"\"\"\n", "SELECT  a.*,\n", "           CASE\n", "              WHEN d.id IN (1,\n", "                            18,\n", "                            22,\n", "                            45,\n", "                            48,\n", "                            51,\n", "                            56,\n", "                            68) THEN 'West'\n", "              WHEN d.id IN (3,\n", "                            14,\n", "                            24,\n", "                            32,\n", "                            34,\n", "                            42,\n", "                            43,\n", "                            57,\n", "                            63) THEN 'South'\n", "              ELSE 'North'\n", "          END AS ZONE,\n", "          c.retail_store_id,\n", "          d.id AS facility_id,\n", "          d.name\n", "from metrics.grn_against_dummy_merchant a \n", "   LEFT JOIN consumer.retail_console_outlet b ON b.id=a.outlet_id\n", "   LEFT JOIN consumer.warehouse_outlet_mapping c ON c.retail_store_id=a.outlet_id\n", "   LEFT JOIN consumer.retail_warehouse_facility d ON d.id= b.facility_id\n", "   where date(a.grn_date) between current_date-30 and current_date\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice = pd.read_sql_query(sql=Ret2, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice[\"grn_date\"] = WithoutInvoice[\"grn_date\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice_Grouped = WithoutInvoice.groupby(\n", "    [\"facility_id\", \"grn_date\", \"zone\"], as_index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice_Final = WithoutInvoice_Grouped.agg(\n", "    {\"landing_price\": sum, \"total_quantity\": sum, \"cost_price\": sum}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithoutInvoice_Final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Reinventorize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query_Reinventorize = \"\"\"\n", "SELECT date(a.pos_timestamp ) as date,\n", "       CASE\n", "              WHEN d.id IN (1,\n", "                            18,\n", "                            22,\n", "                            45,\n", "                            48,\n", "                            51,\n", "                            56,\n", "                            68) THEN 'West'\n", "              WHEN d.id IN (3,\n", "                            14,\n", "                            24,\n", "                            32,\n", "                            34,\n", "                            42,\n", "                            43,\n", "                            57,\n", "                            63) THEN 'South'\n", "              ELSE 'North'\n", "          END AS ZONE,\n", "          d.id AS facility_id,\n", "          d.name,\n", "          sum(a.\"delta\") as Reinventorization\n", "  FROM consumer.ims_ims_inventory_log a\n", "   LEFT JOIN consumer.retail_console_outlet b ON b.id=a.outlet_id\n", "   LEFT JOIN consumer.warehouse_outlet_mapping c ON c.retail_store_id=a.outlet_id\n", "   LEFT JOIN consumer.retail_warehouse_facility d ON d.id= b.facility_id\n", "  WHERE inventory_update_type_id=80 AND a.pos_timestamp BETWEEN current_date -30 AND current_date group by 1,2,3,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Reinventorize_Data = pd.read_sql_query(sql=Query_Reinventorize, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Reinventorize_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Returns Comprehensive"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final1 = pd.merge(\n", "    Ret_Data_final,\n", "    WithoutInvoice_Final,\n", "    left_on=[\"facility_id\", \"billed_at\", \"zone\"],\n", "    right_on=[\"facility_id\", \"grn_date\", \"zone\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final2 = pd.merge(\n", "    Ret_Final1,\n", "    Reinventorize_Data,\n", "    left_on=[\"facility_id\", \"billed_at\", \"zone\"],\n", "    right_on=[\"facility_id\", \"date\", \"zone\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final2 = Ret_Final2.filter(\n", "    [\n", "        \"facility_id\",\n", "        \"billed_at\",\n", "        \"grn_date\",\n", "        \"date\",\n", "        \"zone\",\n", "        \"name_x\",\n", "        \"required_quantity\",\n", "        \"inward_quantity\",\n", "        \"good_quantity\",\n", "        \"bad_quantity\",\n", "        \"expectedamount\",\n", "        \"inwardamount\",\n", "        \"goodamount\",\n", "        \"badamount\",\n", "        \"landing_price\",\n", "        \"total_quantity\",\n", "        \"cost_price\",\n", "        \"reinventorization\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final2.rename(columns={\"name_x\": \"facility_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ret_Final2[\"key_id\"] = (\n", "    Ret_Final2.facility_id.astype(str) + \"|\" + Ret_Final2.billed_at.astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(Ret_Final2)\n", "column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"returnsexecutive2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key_id\"],\n", "    \"sortkey\": [\"facility_id\"],\n", "    \"incremental_key\": \"billed_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(Ret_Final2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ret_Final2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"select * from metrics.returnsexecutive2\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ret_Data = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ret_Data.billed_at.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ret_Data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}