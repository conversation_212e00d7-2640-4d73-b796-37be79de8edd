{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Query = \"\"\"\n", "Select case when city_name in ('Bengaluru','Chennai','Guwahati','Hyderabad','Kolkata') then 'South'\n", "          when city_name in ('Ahmedabad','Indore','Mumbai','Pune') then 'West'\n", "          else 'North' end as Zone,\n", "          updates_base.*, bad_stock_update_base.reason_id, bad_stock_update_base.reason\n", "from\n", "(\n", "\n", "SELECT \n", "l.id as city_id,\n", "l.name as city_name,\n", "f.id as facility_id,\n", "f.name as facility_name,\n", "inventory_base.outlet_id,\n", "inventory_base.outlet_name,\n", "inventory_base.inventory_update_id,\n", "inventory_base.inventory_update_date,\n", "inventory_base.inventory_update_ts,\n", "inventory_base.item_id,\n", "inventory_base.item_loss,\n", "inventory_base.update_id,\n", "inventory_base.update_name,\n", "inventory_base.\"delta\",\n", "inventory_base.is_bad_inventory_update,\n", "inventory_base.group_id,\n", "inventory_base.created_by,\n", "inventory_base.transaction_lp,\n", "inventory_base.weighted_lp\n", "\n", "FROM \n", "(SELECT DISTINCT         iil.inventory_update_id, \n", "                         pp.item_id,\n", "                         pp.variant_id,\n", "                         pp.name as item_name,\n", "                         pp.variant_mrp variant_mrp,\n", "                         (iil.\"delta\"*pp.variant_mrp) as item_loss,\n", "                         date(convert_timezone('Asia/Kolkata', pos_timestamp)) inventory_update_date,\n", "                         date(convert_timezone('Asia/Kolkata', pos_timestamp)) inventory_update_ts,\n", "                         co.id as outlet_id,\n", "                         co.name  as outlet_name, \n", "                         co.facility_id, \n", "                         co.tax_location_id as city_id,\n", "                         iut.id as update_id,\n", "                         iut.name as update_name,\n", "                         iil.\"delta\",\n", "                         iut.is_bad_inventory_update,\n", "                         iut.group_id,\n", "                         iil.created_by,\n", "                         iil.transaction_lp,\n", "                         iil.weighted_lp\n", "         FROM consumer.pos_ims_inventory_log iil\n", "         INNER JOIN consumer.pos_console_outlet co ON co.id = iil.outlet_id\n", "         INNER JOIN consumer.pos_product_product pp ON pp.variant_id = iil.variant_id\n", "         INNER JOIN consumer.pos_ims_inventory_update_type iut ON iut.id = iil.inventory_update_type_id\n", "         AND (iut.is_bad_inventory_update = 1 OR iut.group_id in (6,12))\n", "         WHERE  date(convert_timezone('Asia/Kolkata', pos_timestamp))=current_date-1\n", "           AND co.type_id = 2\n", "           AND pp.active = 'true'\n", " \n", " ) inventory_base \n", " inner join consumer.pos_console_location l on city_id = l.id\n", " inner join crates_facility f on facility_id = f.id\n", " \n", " ) updates_base \n", " \n", " left join \n", " \n", "(\n", "select\n", "\n", "date(convert_timezone('Asia/Kolkata',ibiul.created_at)) as bad_stock_mark_date, \n", "ibiul.inventory_update_id as bad_stock_update_base_inventory_update_id,\n", "ibiul.inventory_update_type_id as bad_stock_update_base_inventory_update_type_id,\n", "ibiul.reason_id,\n", "ibur.name as reason\n", "\n", "from \n", "consumer.ims_ims_bad_inventory_update_log ibiul\n", "INNER join consumer.ims_ims_bad_update_reason ibur on ibur.id=ibiul.reason_id\n", "INNER JOIN consumer.pos_ims_inventory_update_type u on u.id = ibiul.inventory_update_type_id\n", "where date(convert_timezone('Asia/Kolkata',ibiul.created_at))=current_date-1\n", "order by 1, 5, 4\n", ") bad_stock_update_base\n", "\n", "on updates_base.inventory_update_id::varchar = bad_stock_update_base.bad_stock_update_base_inventory_update_id::varchar\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data = pd.read_sql_query(sql=Inv_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Warehouse KPIs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data[\"WHDamageAmount\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_damage_product\"\n", ").astype(int) * Inv_Data[\"item_loss\"]\n", "\n", "Inv_Data[\"WHDamageQuantity\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_damage_product\"\n", ").astype(int) * Inv_Data[\"delta\"]\n", "\n", "Inv_Data[\"WHExpiredAmount\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_expired_product\"\n", ").astype(int) * Inv_Data[\"item_loss\"]\n", "\n", "Inv_Data[\"WHExpiredQuantity\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_expired_product\"\n", ").astype(int) * Inv_Data[\"delta\"]\n", "\n", "Inv_Data[\"WHNearExpiryAmount\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_near_expiry_product\"\n", ").astype(int) * Inv_Data[\"item_loss\"]\n", "\n", "Inv_Data[\"WHNearExpiryQuantity\"] = (\n", "    Inv_Data[\"update_name\"] == \"bad_stock_near_expiry_product\"\n", ").astype(int) * Inv_Data[\"delta\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data[\"WHNegUpdate\"] = (\n", "    Inv_Data[\"update_id\"].astype(str).isin([\"27\", \"42\", \"41\", \"15\", \"43\", \"39\", \"40\"])\n", ") * Inv_Data[\"item_loss\"]\n", "\n", "Inv_Data[\"WHPosUpdate\"] = (\n", "    Inv_Data[\"update_id\"].astype(str).isin([\"26\", \"47\", \"46\", \"14\", \"44\", \"48\", \"45\"])\n", ") * Inv_Data[\"item_loss\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data[\"WHNegUpdateQty\"] = (\n", "    Inv_Data[\"update_id\"].astype(str).isin([\"27\", \"42\", \"41\", \"15\", \"43\", \"39\", \"40\"])\n", ") * Inv_Data[\"delta\"]\n", "\n", "Inv_Data[\"WHPosUpdateQty\"] = (\n", "    Inv_Data[\"update_id\"].astype(str).isin([\"26\", \"47\", \"46\", \"14\", \"44\", \"48\", \"45\"])\n", ") * Inv_Data[\"delta\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Last Mile KPIs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data[\"LMDamageAmount\"] = (Inv_Data[\"update_id\"] == \"7\").astype(int) * Inv_Data[\n", "    \"item_loss\"\n", "]\n", "\n", "Inv_Data[\"LMDamageQuantity\"] = (Inv_Data[\"update_id\"] == \"7\").astype(int) * Inv_Data[\n", "    \"delta\"\n", "]\n", "\n", "Inv_Data[\"LMExpiredAmount\"] = (Inv_Data[\"update_id\"] == \"9\").astype(int) * Inv_Data[\n", "    \"item_loss\"\n", "]\n", "\n", "Inv_Data[\"LMExpiredQuantity\"] = (Inv_Data[\"update_id\"] == \"9\").astype(int) * Inv_Data[\n", "    \"delta\"\n", "]\n", "\n", "Inv_Data[\"LMNearExpiryAmount\"] = (Inv_Data[\"update_id\"] == \"63\").astype(int) * Inv_Data[\n", "    \"item_loss\"\n", "]\n", "\n", "Inv_Data[\"LMNearExpiryQuantity\"] = (Inv_Data[\"update_id\"] == \"63\").astype(\n", "    int\n", ") * Inv_Data[\"delta\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data_Grouped = Inv_Data.groupby(\n", "    [\n", "        \"zone\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"inventory_update_ts\",\n", "    ],\n", "    as_index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final = Inv_Data_Grouped.agg(\n", "    {\n", "        \"WHDamageAmount\": sum,\n", "        \"WHDamageQuantity\": sum,\n", "        \"WHExpiredAmount\": sum,\n", "        \"WHExpiredQuantity\": sum,\n", "        \"WHNearExpiryAmount\": sum,\n", "        \"WHNearExpiryQuantity\": sum,\n", "        \"WHNegUpdate\": sum,\n", "        \"WHPosUpdate\": sum,\n", "        \"WHNegUpdateQty\": sum,\n", "        \"WHPosUpdateQty\": sum,\n", "        \"LMDamageAmount\": sum,\n", "        \"LMDamageQuantity\": sum,\n", "        \"LMExpiredAmount\": sum,\n", "        \"LMExpiredQuantity\": sum,\n", "        \"LMNearExpiryAmount\": sum,\n", "        \"LMNearExpiryQuantity\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GMV_Query = \"\"\"\n", "select outlet_id,scheduled_date,sum(procurement_amount) as gmv from  \n", "metrics.received_synced_orders_test2 \n", "where \n", "scheduled_date=current_date-1\n", "and current_status='AT_DELIVERY_CENTER'  \n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GMV = pd.read_sql_query(sql=GMV_Query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GMV[\"scheduled_date\"] = pd.to_datetime(GMV[\"scheduled_date\"])\n", "GMV.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final[\"inventory_update_ts\"] = pd.to_datetime(Inv_Final[\"inventory_update_ts\"])\n", "Inv_Final = pd.merge(\n", "    Inv_Final,\n", "    GMV,\n", "    left_on=[\"outlet_id\", \"inventory_update_ts\"],\n", "    right_on=[\"outlet_id\", \"scheduled_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Final[\"key_id\"] = (\n", "    Inv_Final.outlet_id.astype(str) + \"|\" + Inv_Final.inventory_update_ts.astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(Inv_Final)\n", "column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"InventoryExecutive\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key_id\"],\n", "    \"sortkey\": [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"facility_name\",\n", "    ],\n", "    \"incremental_key\": \"inventory_update_ts\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(Inv_Final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_query = \"\"\"Select max(inventory_update_ts) from metrics.InventoryExecutive\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data = pd.read_sql_query(sql=Inv_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inv_Data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}