{"cells": [{"cell_type": "markdown", "id": "1594382b-567d-47c7-a410-a67947bec59e", "metadata": {}, "source": ["# Import Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "be53b378-18aa-48c9-b3d4-3d5271a4a4bb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "\n", "from datetime import datetime, timedelta"]}, {"cell_type": "markdown", "id": "f5f37df5-0591-4a71-becd-6bb1e46c12eb", "metadata": {}, "source": ["# Connect to database"]}, {"cell_type": "code", "execution_count": null, "id": "5fd7079d-d116-4a18-b317-098d1ee4d506", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "id": "aa221e38-efcc-4568-9947-26f02e92de0d", "metadata": {}, "source": ["# Write a function to calculate runtime while reading SQL query"]}, {"cell_type": "code", "execution_count": null, "id": "3f4fbbd7-81f9-4068-91d5-d31b62814e93", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "\n", "    if (end - start) > 60:\n", "        print(\"Time taken: \", (end - start) / 60, \"mins\")\n", "    else:\n", "        print(\"Time taken: \", (end - start), \"s\")\n", "\n", "    return df"]}, {"cell_type": "markdown", "id": "635f1698-dfa4-458c-9b68-70c697f4976d", "metadata": {}, "source": ["# Store result set from SQL queries in dataframes"]}, {"cell_type": "code", "execution_count": null, "id": "668a0131-a2b9-4ab9-b392-3d10c38d8022", "metadata": {}, "outputs": [], "source": ["items_picked_sql = f\"\"\"\n", "    SELECT \n", "        f.id AS facility_id,\n", "        f.name AS facility_name,\n", "        wpl.outlet_id,\n", "        co.name as outlet_name,\n", "        coalesce(wasl.zone_identifier, 'no_zone') as zone_identifier,\n", "        (wpl.created_at + interval '5.5 hours')::date as \"date\",\n", "        extract(hour from (wpl.created_at + interval '5.5 hours')) as hr,\n", "        sum(wpli.picked_quantity) AS item_picked_qty\n", "    FROM lake_warehouse_location.warehouse_pick_list wpl\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item wpli ON wpl.id = wpli.pick_list_id\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item_location wll ON wll.pick_list_item_id = wpli.id\n", "    JOIN lake_retail.console_outlet co ON co.id = wpl.outlet_id\n", "    JOIN lake_crates.facility f ON f.id = co.facility_id\n", "    JOIN lake_warehouse_location.warehouse_storage_location wasl ON wll.location_id = wasl.id\n", "    JOIN lake_warehouse_location.warehouse_outlet_mapping w ON w.outlet_id = co.id\n", "    AND w.warehouse_id = wasl.warehouse_id \n", "    WHERE wpl.created_at >= ('2022-07-25'::timestamp - interval '5.5 hours')\n", "    AND co.id in (512,287,337,714,481,1104,1566,1577,1624,1644,1725,2653,2904,4165,4170,4181)\n", "    GROUP BY 1,2,3,4,5,6,7\n", "\"\"\"\n", "\n", "items_picked = read_sql_query(items_picked_sql, redshift)\n", "items_picked.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "ffd2ea7f-65d7-4d37-bb67-cbd915839f63", "metadata": {}, "outputs": [], "source": ["sku_picked_sql = f\"\"\"\n", "    SELECT \n", "        f.id AS facility_id,\n", "        f.name AS facility_name,\n", "        wpl.outlet_id,\n", "        co.name as outlet_name,\n", "        coalesce(wasl.zone_identifier, 'no_zone') as zone_identifier,\n", "        (wpl.created_at + interval '5.5 hours')::date as \"date\",\n", "        extract(hour from (wpl.created_at + interval '5.5 hours')) as hr,\n", "        count(distinct wpli.item_id) AS unique_sku_picked\n", "    FROM lake_warehouse_location.warehouse_pick_list wpl\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item wpli ON wpl.id = wpli.pick_list_id\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item_location wll ON wll.pick_list_item_id = wpli.id\n", "    JOIN lake_retail.console_outlet co ON co.id = wpl.outlet_id\n", "    JOIN lake_crates.facility f ON f.id = co.facility_id\n", "    JOIN lake_warehouse_location.warehouse_storage_location wasl ON wll.location_id = wasl.id\n", "    JOIN lake_warehouse_location.warehouse_outlet_mapping w ON w.outlet_id = co.id\n", "    AND w.warehouse_id = wasl.warehouse_id \n", "    WHERE wpl.created_at >= ('2022-07-25'::timestamp - interval '5.5 hours')\n", "    AND co.id in (512,287,337,714,481,1104,1566,1577,1624,1644,1725,2653,2904,4165,4170,4181)\n", "    GROUP BY 1,2,3,4,5,6,7\n", "\"\"\"\n", "\n", "sku_picked = read_sql_query(sku_picked_sql, redshift)\n", "sku_picked.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "151addef-ca64-4785-84c9-7e01c234c8c7", "metadata": {}, "outputs": [], "source": ["pickers_sql = f\"\"\"\n", "    SELECT  \n", "        f.id as facility_id,\n", "        f.name AS facility_name,\n", "        ipl.outlet_id,\n", "        co.name as outlet_name,\n", "        count(distinct ipl.assigned_to_name) AS picker_id,\n", "        (ipl.created_at + interval '5.5 hours')::date as \"date\",\n", "        extract(hour from (ipl.created_at + interval '5.5 hours')) as hr,\n", "        coalesce(wasl.zone_identifier, 'no_zone') as zone_identifier\n", "    FROM lake_warehouse_location.warehouse_pick_list ipl\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item ipli ON ipl.id = ipli.pick_list_id\n", "    JOIN lake_warehouse_location.warehouse_pick_list_item_location wll ON wll.pick_list_item_id = ipli.id\n", "    JOIN lake_retail.console_outlet co ON co.id = ipl.outlet_id\n", "    JOIN lake_crates.facility f ON f.id = co.facility_id\n", "    JOIN lake_warehouse_location.warehouse_storage_location wasl ON wll.location_id = wasl.id\n", "    JOIN lake_warehouse_location.warehouse_outlet_mapping w ON w.outlet_id = co.id\n", "    AND w.warehouse_id = wasl.warehouse_id \n", "    WHERE ipl.outlet_id IN (512,287,337,714,481,1104,1566,1577,1624,1644,1725,2653,2904,4165,4170,4181)\n", "    AND ipl.state in (4,10) --picklist completed, billed\n", "    AND ipl.created_at >= ('2022-07-25'::timestamp - interval '5.5 hours')\n", "    GROUP BY 1,2,3,4,6,7,8    \n", "\"\"\"\n", "\n", "pickers = read_sql_query(pickers_sql, redshift)\n", "pickers.head(5)"]}, {"cell_type": "markdown", "id": "1fc6fab0-6d9c-4997-9e38-680214dedee2", "metadata": {}, "source": ["# Divide dispatch timings of facilities into slots"]}, {"cell_type": "code", "execution_count": null, "id": "2046f35b-936b-46f7-b2d3-6a42df47c8dd", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1lU8A4i90DH_58xMafJJKM739JtVf-DxKS1Ry1k40Ya8\"\n", "sheet_name = \"allocated_batches\""]}, {"cell_type": "code", "execution_count": null, "id": "961540a7-67f8-472a-9ad5-e451e1e2afdc", "metadata": {}, "outputs": [], "source": ["dispatch_slots = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "dispatch_slots.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7a4fb13d-3103-4d2e-b32c-e1702a73faef", "metadata": {}, "outputs": [], "source": ["dispatch_slots[\"slots\"] = dispatch_slots.groupby([\"backend_facility_id\"])[\n", "    \"dispatch_time\"\n", "].rank(method=\"dense\", ascending=True)\n", "dispatch_slots.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b101deac-b7dc-49ac-8cd9-d71f4b414c3e", "metadata": {}, "outputs": [], "source": ["dispatch_slots.drop(\n", "    [\"shift_flag\", \"shift_timing\", \"frontend_outlet_id\", \"active\"], axis=1, inplace=True\n", ")\n", "dispatch_slots.head(5)"]}, {"cell_type": "markdown", "id": "e4cfb2ff-6280-45e4-913b-aaa47bdcb0ed", "metadata": {}, "source": ["**convert dispatch_time from 1200 hrs to 2400 hrs**"]}, {"cell_type": "code", "execution_count": null, "id": "f169f936-aae7-47b3-95c4-174c73e40d27", "metadata": {}, "outputs": [], "source": ["dispatch_slots[\"dispatch_time\"] = pd.to_datetime(\n", "    dispatch_slots[\"dispatch_time\"], format=\"%I:%M %p\"\n", ").dt.strftime(\"%H:%M\")\n", "dispatch_slots.head(5)"]}, {"cell_type": "markdown", "id": "951b78da-183b-4012-a4d3-c37e43a3a706", "metadata": {}, "source": ["**extract hour from timestamp (dispatch time)**"]}, {"cell_type": "code", "execution_count": null, "id": "17cd867f-a925-44c0-9745-254d6ea600cf", "metadata": {}, "outputs": [], "source": ["dispatch_slots[\"hour\"] = dispatch_slots.dispatch_time.str.extract(\n", "    r\"(^[^:]*)\", expand=True\n", ")\n", "dispatch_slots.head(5)"]}, {"cell_type": "markdown", "id": "e247569e-877f-4d06-9c07-9cbf60280673", "metadata": {}, "source": ["**create a lag of 1 for hour column to get previous row's hour**"]}, {"cell_type": "code", "execution_count": null, "id": "18168645-1841-453a-9a70-61f19f610be0", "metadata": {}, "outputs": [], "source": ["dispatch_slots = dispatch_slots.drop_duplicates()\n", "dispatch_slots.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "22de9f7e-f2da-430e-b032-f2f28b2e7429", "metadata": {}, "outputs": [], "source": ["dispatch_slots[\"lag\"] = dispatch_slots.groupby([\"backend_facility_id\"])[\"hour\"].shift(1)\n", "dispatch_slots.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "624f7718-016d-40b6-8f36-456eb4de9542", "metadata": {}, "outputs": [], "source": ["dispatch_slots = dispatch_slots.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "d915d6e0-2cc3-45ad-af58-c64378c83bc1", "metadata": {}, "outputs": [], "source": ["dispatch_slots.head(5)"]}, {"cell_type": "markdown", "id": "1fa5f90a-16d2-4d86-89c1-728ef9d66100", "metadata": {}, "source": ["**get start and end hour**"]}, {"cell_type": "code", "execution_count": null, "id": "3896cd88-12d3-4d5e-a976-71ab505749c6", "metadata": {}, "outputs": [], "source": ["dispatch_slots.rename(columns={\"lag\": \"start_hour\", \"hour\": \"end_hour\"}, inplace=True)\n", "dispatch_slots.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "35b92d71-28b0-4e60-ac73-0fa53d5fe4dd", "metadata": {}, "outputs": [], "source": ["dispatch_slots[[\"backend_facility_id\", \"start_hour\", \"end_hour\"]] = dispatch_slots[\n", "    [\"backend_facility_id\", \"start_hour\", \"end_hour\"]\n", "].astype(int)\n", "dispatch_slots.dtypes"]}, {"cell_type": "markdown", "id": "547237fd-5f22-4e50-83e7-16a9fe0812a3", "metadata": {}, "source": ["# Merge dataframes from SQL queries"]}, {"cell_type": "code", "execution_count": null, "id": "62fff528-b276-4187-936e-de17245d8e36", "metadata": {}, "outputs": [], "source": ["base = pd.merge(\n", "    items_picked,\n", "    sku_picked,\n", "    on=[\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"zone_identifier\",\n", "        \"date\",\n", "        \"hr\",\n", "    ],\n", "    how=\"outer\",\n", ")\n", "base.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "29361299-b427-4321-9bda-b87cda7474e6", "metadata": {}, "outputs": [], "source": ["base = pd.merge(\n", "    base,\n", "    pickers,\n", "    on=[\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"zone_identifier\",\n", "        \"date\",\n", "        \"hr\",\n", "    ],\n", "    how=\"outer\",\n", ")\n", "base.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2e139818-e3ab-4453-b540-0005ac39aa39", "metadata": {}, "outputs": [], "source": ["base = base.fillna(0)"]}, {"cell_type": "markdown", "id": "fb6ab0db-507c-4a8c-9859-b632307a86c7", "metadata": {}, "source": ["# Map slots to base df"]}, {"cell_type": "code", "execution_count": null, "id": "2eb0951e-82e3-42d8-a74d-5f591646ab18", "metadata": {}, "outputs": [], "source": ["base_0 = pd.merge(\n", "    base,\n", "    dispatch_slots.rename(columns={\"backend_facility_id\": \"facility_id\"}),\n", "    on=\"facility_id\",\n", "    how=\"left\",\n", ")\n", "base_0.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "a0098145-83c3-4622-b5fd-5e474ebc7c61", "metadata": {}, "outputs": [], "source": ["base_0[\"slots\"] = np.where(\n", "    (base_0[\"hr\"] >= base_0[\"start_hour\"]) & (base_0[\"hr\"] < base_0[\"end_hour\"]),\n", "    base_0[\"slots\"],\n", "    np.nan,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f0344627-1838-49cc-9f6f-f8c6459190dc", "metadata": {}, "outputs": [], "source": ["base_0 = base_0[base_0.slots.isna() == False].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "df5657c4-ff30-43b6-b8af-7079c2dd3ee5", "metadata": {}, "outputs": [], "source": ["base_0.head(2)"]}, {"cell_type": "markdown", "id": "b843690b-65a7-4347-b6d1-5fbe36dc6849", "metadata": {}, "source": ["# Data Sanity Check"]}, {"cell_type": "code", "execution_count": null, "id": "0831e382-bea0-4ac5-850d-81aa29860875", "metadata": {}, "outputs": [], "source": ["base_0.groupby([\"facility_id\"]).agg({\"date\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "fce49974-b13e-48fb-bbde-f2a6a479cf00", "metadata": {}, "outputs": [], "source": ["base.groupby([\"facility_id\"]).agg({\"date\": \"count\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "441aa3b8-3982-4e60-9770-e29c94fcab64", "metadata": {}, "outputs": [], "source": ["base_1 = base.drop_duplicates()\n", "base_1.groupby([\"facility_id\"]).agg({\"date\": \"count\"}).reset_index()"]}, {"cell_type": "markdown", "id": "366f32cd-7eab-4a95-bfa5-d51a176e9a77", "metadata": {}, "source": ["# Data Cleaning"]}, {"cell_type": "code", "execution_count": null, "id": "894174c9-ba62-4081-b2fd-a33143c33b63", "metadata": {}, "outputs": [], "source": ["base_0.drop([\"dispatch_time\", \"end_hour\", \"start_hour\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "38a44d17-c2f4-4e27-8ed8-0fc45c1184f2", "metadata": {}, "outputs": [], "source": ["master_data = base_0"]}, {"cell_type": "code", "execution_count": null, "id": "548e028a-03fd-49f6-9629-8dd5d8fa9c70", "metadata": {}, "outputs": [], "source": ["master_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e07dfea8-0ebc-4135-b379-ecb50f076258", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    master_data,\n", "    \"13BCh3Uerk7LWjusdib4_kA6FND9CtrB-czVc8Grfegg\",\n", "    \"raw_data\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "11f1e6c2-bb7f-4ea7-8247-81e04bca9170", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}