{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import requests\n", "from requests.adapters import HTTPAdapter\n", "from requests.compat import urljoin\n", "from urllib3.util.retry import Retry\n", "import pandas\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DarsaAPIClient(object):\n", "    \"\"\"docstring for BaseClient\"\"\"\n", "\n", "    BASE_URL = \"\"\n", "\n", "    def __init__(self, token, url=None):\n", "        retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[500])\n", "\n", "        self.url = url and url or self.BASE_URL\n", "        self.session = requests.Session()\n", "        self.session.headers.update(\n", "            {\"Authorization\": f\"Token {token}\", \"content-type\": \"application/json\"}\n", "        )\n", "        self.session.mount(self.BASE_URL, HTTPAdapter(max_retries=retries))\n", "\n", "    def _build_url(self, path):\n", "        return urljoin(self.url, path)\n", "\n", "    def employee_logs(self, start_date, end_date):\n", "        url = self._build_url(\"api/log/employeesync/\")\n", "        return self.session.get(\n", "            url, params={\"start_date\": start_date.isoformat(), \"end_date\": end_date}\n", "        ).json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token = pb.get_secret(\"retail/tokens/darsa\")[\"token\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = DarsaAPIClient(token, url=\"https://grofers.darsa.ai/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_timestamp = list(\n", "    pandas.io.sql.execute(\n", "        \"\"\"\n", "select max(log_timestamp) from consumer.warehouse_employee_logs\n", "\"\"\",\n", "        con=con,\n", "    )\n", ")[0][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = max_timestamp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = []\n", "while start_date <= datetime.utcnow():\n", "    end_date = start_date + <PERSON><PERSON><PERSON>(minutes=30)\n", "    records = client.employee_logs(start_date, end_date)\n", "    rows = []\n", "    for record in records:\n", "        row = dict(record[\"employee\"])\n", "        row.update({k: v for k, v in record.items() if k != \"employee\"})\n", "        rows.append(row)\n", "\n", "    df = pandas.DataFrame(rows)\n", "    df = df.rename(columns={\"check_time\": \"log_timestamp\"})\n", "    print(f\"{start_date} -> {end_date} => {len(df)}\")\n", "    dfs.append(df)\n", "    start_date = end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"log_timestamp\",\n", "        \"type\": \"timestamp without time zone\",\n", "        \"description\": \"Log Timestamp\",\n", "    },\n", "    {\"name\": \"date_of_joining\", \"type\": \"date\", \"description\": \"Date of Joining\"},\n", "    {\n", "        \"name\": \"date_of_termination\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Date of Termination\",\n", "    },\n", "    {\"name\": \"department\", \"type\": \"varchar(500)\", \"description\": \"Department\"},\n", "    {\"name\": \"designation\", \"type\": \"varchar(500)\", \"description\": \"Designation\"},\n", "    {\"name\": \"direction\", \"type\": \"varchar(500)\", \"description\": \"Direction\"},\n", "    {\n", "        \"name\": \"employee_id\",\n", "        \"type\": \"varchar(500)\",\n", "        \"description\": \"Grofers Employee Id\",\n", "    },\n", "    {\"name\": \"employee_type\", \"type\": \"varchar(50)\", \"description\": \"Employee Type\"},\n", "    {\"name\": \"facility_code\", \"type\": \"integer\", \"description\": \"Facility Code\"},\n", "    {\"name\": \"gender\", \"type\": \"varchar(500)\", \"description\": \"Gender\"},\n", "    {\"name\": \"id\", \"type\": \"integer\", \"description\": \"Log Id\"},\n", "    {\"name\": \"image\", \"type\": \"varchar(500)\", \"description\": \"Image\"},\n", "    {\"name\": \"mode\", \"type\": \"varchar(500)\", \"description\": \"Mode\"},\n", "    {\"name\": \"name\", \"type\": \"varchar(500)\", \"description\": \"Employee Name\"},\n", "    {\"name\": \"role\", \"type\": \"varchar(500)\", \"description\": \"Role\"},\n", "    {\"name\": \"status\", \"type\": \"varchar(500)\", \"description\": \"Status\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"warehouse_employee_logs\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"id\"],\n", "    \"sortkey\": [\"id\"],\n", "    \"incremental_key\": \"log_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Warehouse Employee Logs\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    df = pandas.concat(dfs)\n", "except ValueError:\n", "    pass\n", "else:\n", "    df = df[[x[\"name\"] for x in column_dtypes]]\n", "    pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}