{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "\n", "\n", "retail_connection = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "SELECT sto.sto_id, sto.outlet_id as backend_outlet_id, sto.merchant_outlet_id as frontend_outlet_id, \n", "co.name as backend_outlet, \n", "date(convert_tz(sto.created_at,'+00:00','+05:30')) as sto_date,\n", "max(convert_tz(sto.created_at,'+00:00','+05:30')) as sto_issue_date_time,\n", "max(convert_tz(pi.created_at,'+00:00','+05:30')) as sto_billed_at\n", "-- max(convert_tz(sd.created_at,\"+00:00\",\"+05:30\")) as grn_details_created_at\n", "FROM ims.ims_sto_details sto\n", "left join pos.pos_invoice pi on pi.grofers_order_id = sto.sto_id and sto.outlet_id=pi.outlet_id\n", "INNER JOIN retail.console_outlet co on co.id = sto.outlet_id\n", "where sto.created_at >= current_timestamp-interval 3 hour\n", "and sto.outlet_id in (305,2679,2678,624,2656,1696,171,1334,632,1695,167,2647,1299,298,2643,1554,166,2645,1308,2669,1667,2644,1304,1734,1375,357,1671,169,2690,2675,1292,1301,696,376,2642,1672,575,510,1697,1278,1213,1293,2008,2646,2541,2790,1784,1306,472,1198,2729,1664,194,1298,2493,1666,2685,1048,1336,2537,1307,2687,360,2543,378,1335,1214,1555,398,609,1670,571,379,2689,1277,313,700,321,2492,473,2651,1050,168,695,2009,165,1281,401,1553,2472,2674,1310,1665,1668,2665,2730,2791,1203,1733,584,2684,1735,361,2668,1303,196,701,1276,2667,359,2532,1297,1309,2670,2728,1197,720,1300,2686,1302,193,1196,2676,2925,170,1202,703,1674,2671,2923,1305,1204,358,1049,2568,572,2677,2660,1212,2567,2471,2666,2789,2544,1785,1673,2673,1294,1669,2691,1311,1280,710,2672,2924,1193,702,2569,1279,1192,2007,2688)\n", "-- and ist.item_id not in (10005109, 10005493, 10020343, 10025805, 10025806, 10008629)\n", "and sto.sto_state not in (3) and pi.grofers_order_id is null\n", "group by 1,2,3,4,5\n", "order by 6 desc \n", "\n", "\"\"\"\n", "\n", "data = pd.read_sql_query(q, retail_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_outlet_list = list(data.frontend_outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data.shape[0] != 0:\n", "    qq = \"\"\"\n", "    select id as frontend_outlet_id, name as frontend_outet\n", "    from retail.console_outlet\n", "    where id in %(frontend_outlet_list)s\n", "    \"\"\"\n", "\n", "    frontend_outlet_data = pd.read_sql_query(\n", "        qq,\n", "        retail_connection,\n", "        params={\"frontend_outlet_list\": tuple(frontend_outlet_list)},\n", "    )\n", "\n", "    data_new = data.merge(frontend_outlet_data, on=\"frontend_outlet_id\", how=\"left\")\n", "\n", "    data_new[\"sto_billed_at_new\"] = data_new[\"sto_billed_at\"].apply(\n", "        lambda x: now if str(x) == \"None\" else x\n", "    )\n", "\n", "    data_new[\"delay_time\"] = data_new.apply(\n", "        lambda x: (x[\"sto_billed_at_new\"] - x[\"sto_issue_date_time\"]).total_seconds()\n", "        / 60,\n", "        axis=1,\n", "    )\n", "\n", "    data_new[\"delay\"] = data_new[\"delay_time\"].apply(\n", "        lambda x: 1 if np.int(x) > 60 else 0\n", "    )\n", "\n", "    data_new[\"expected_billing_time\"] = data_new[\"sto_issue_date_time\"].apply(\n", "        lambda x: x + <PERSON><PERSON><PERSON>(hours=1)\n", "    )\n", "    final_summary = data_new[data_new[\"delay\"] == 1][\n", "        [\n", "            \"sto_id\",\n", "            \"backend_outlet_id\",\n", "            \"frontend_outlet_id\",\n", "            \"frontend_outet\",\n", "            \"backend_outlet\",\n", "            \"sto_issue_date_time\",\n", "            \"expected_billing_time\",\n", "            \"delay_time\",\n", "        ]\n", "    ]\n", "\n", "    final_summary = final_summary.rename(\n", "        columns={\n", "            \"sto_id\": \"STO ID\",\n", "            \"backend_outlet_id\": \"Backend Outlet ID\",\n", "            \"frontend_outlet_id\": \"Frontend Outlet ID\",\n", "            \"frontend_outet\": \"Frontend Outlet\",\n", "            \"backend_outlet\": \"Backend Outlet\",\n", "            \"sto_issue_date_time\": \"STO Issue Time\",\n", "            \"expected_billing_time\": \"Expected Billing Time\",\n", "            \"delay_time\": \"Delay(mins)\",\n", "        }\n", "    )\n", "\n", "    final_summary = final_summary.round(0)\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.0,\n", "    row_height=0.625,\n", "    font_size=15,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    if final_summary.shape[0] != 0:\n", "        fig, ax = render_mpl_table(\n", "            final_summary.sort_values(by=\"Delay(mins)\", ascending=False),\n", "            header_columns=0,\n", "        )\n", "        fig.savefig(\"fnv_sto_delay.png\")\n", "    else:\n", "        pass\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install tabulate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from tabulate import tabulate\n", "# df = tabulate(final_summary[['STO ID','Backend Outlet ID']], tablefmt = \"fancygrid\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ascii_table = (\n", "#         \"```\"\n", "#         + tabulate(\n", "#             final_summary[[\"STO ID\",\"Backend Outlet\",\"Frontend Outlet\",\"STO Issue Time\",\"Expected Billing Time\",\"Delay(mins)\"]],\n", "#             tablefmt=\"fancygrid\",\n", "#             headers=\"keys\",\n", "#             showindex=False,\n", "#         )\n", "#         + \"```\"\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slack_channel = \"sto-delay-alerts\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    if final_summary.shape[0] != 0:\n", "        import os\n", "        from slack_sdk import WebClient\n", "        from slack_sdk.errors import SlackApiError\n", "\n", "        # filepath = './mytable.png'\n", "        token = \"******************************************************\"\n", "        client = WebClient(token=token)\n", "        file_check = \"./fnv_sto_delay.png\"\n", "        channel = slack_channel\n", "        text = (\n", "            \"STO Summary for CPC F&V Outlets. Please find below the STOs where it is not billed within 1 Hour from the time of its creation\"\n", "            #     +f\"\\n {final_summary}\"\n", "        )\n", "        try:\n", "            response = client.chat_postMessage(channel=channel, text=text)\n", "            #     client.chat_postMessage(channel=channel, text=ascii_table)\n", "            #     assert response[\"message\"][\"text\"] == text\n", "            filepath = file_check\n", "            response = client.files_upload(channels=channel, file=filepath)\n", "        #     assert response[\"file\"]  # the uploaded file\n", "        except SlackApiError as e:\n", "            # You will get a SlackApiError if \"ok\" is False\n", "            assert e.response[\"ok\"] is False\n", "            assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "            print(f\"Got an error: {e.response['error']}\")\n", "    else:\n", "        pass\n", "except:\n", "    pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}