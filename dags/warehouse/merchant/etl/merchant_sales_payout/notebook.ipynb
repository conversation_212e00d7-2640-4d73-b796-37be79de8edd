{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time, random, string\n", "import random, datetime\n", "import string, sys\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta, datetime\n", "\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")\n", "# presto_connection = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["date_filter = date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = pd.to_datetime(date_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_date.day in (27, 28, 29, 30, 31):\n", "    start_date = date(datetime.now().year, datetime.now().month, 26)\n", "    end_date = date(\n", "        (start_date + <PERSON><PERSON>ta(days=30)).year,\n", "        (start_date + timed<PERSON>ta(days=30)).month,\n", "        25,\n", "    )\n", "else:\n", "    start_date = date(datetime.now().year, datetime.now().month - 1, 26)\n", "    # datetime.today().replace(day=1).date()\n", "    end_date = date(\n", "        (start_date + <PERSON><PERSON>ta(days=30)).year,\n", "        (start_date + timed<PERSON>ta(days=30)).month,\n", "        25,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q = \"\"\"\n", "\n", "\n", "\n", "with lm_base as \n", "(select distinct date((sch_slot_start)) as sch_date_ist,city,outlet_id, outlet_name,\n", "backend_merchant_id,frontend_merchant_id,\n", "--frontend_merchant_name,\n", "order_id,\n", "current_status\n", "from \n", " (select distinct \n", "        m.city_name as city,\n", "        dwbm.backend_merchant_id,\n", "        m.external_id as frontend_merchant_id,\n", "        --m.name as frontend_merchant_name,\n", "        o.id as order_id,\n", "    \n", "        date(timestamp 'epoch' + o.slot_start * interval '1 second'+interval '5.5 hrs ') as sch_slot_start,\n", "        o.current_status,\n", "        dwbm.pos_outlet_id as outlet_id,\n", "        dwbm.pos_outlet_name as outlet_name\n", "      \n", "        from lake_oms_bifrost.oms_order \n", "            as o\n", "        inner join lake_oms_bifrost.view_oms_merchant \n", "            as m \n", "            on o.merchant_id = m.id \n", "        inner join dwh.dim_merchant_outlet_facility_mapping dwbm on m.external_id = dwbm.frontend_merchant_id\n", "        \n", "    \n", "        \n", "        where \n", "            o.type in ('RetailForwardOrder','InternalForwardOrder') and\n", "             valid_to_utc >= current_date\n", "            and dwbm.is_current = True\n", "            and dwbm.is_mapping_enabled = True and\n", "            merchant_business_type_id in (7,18)\n", "            and o.slot_start >= extract(epoch from ('{start_date}'::DATE - interval '5.5 hour')) \n", "            and o.slot_start <= extract(epoch from ('{end_date}'::DATE + interval '1 day'- interval '5.5 hour'))\n", "            AND o.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "            AND m.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "            \n", "            ) as a \n", "            ),\n", "            \n", "        \n", "\n", "total_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=1 then procurement_price*procurement_quantity end) as forward_sales,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as fnv_forward_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_price*procurement_quantity end) as non_fnv_forward_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=1 then procurement_quantity end) as fnv_forward_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=1 then procurement_quantity end) as non_fnv_forward_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        and pos.invoice_type_id in (1) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6) as a\n", "    group by 1),\n", " \n", "\n", "\n", "\n", "\n", "return_gmv as\n", "(\n", "select order_id, sum(case when fnv_flag='FnV' then return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when fnv_flag='Non-FnV' then return_gmv else 0 end) as non_fnv_return_gmv,\n", "sum(case when fnv_flag='FnV' then return_qty else 0 end) as fnv_return_qty,\n", "sum(case when fnv_flag='Non-FnV' then return_qty else 0 end) as non_fnv_return_qty\n", "from\n", "(select a.order_id::int, a.product_id,\n", "case when cat.parent_category_id in (9,1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-accepted_qty)*price) as return_gmv,\n", "sum((procured_quantity-accepted_qty)) as return_qty\n", "from lake_oms_bifrost.oms_order_item oi \n", "inner join \n", "(select distinct lsl.label_value as order_id, \n", "json_extract_path_text(lsi.product_metadata,'product_id',true) as product_id,\n", "(lsic.accepted_item_quantity) as accepted_qty,\n", "((lsi.value::int)/100) as price\n", "from \n", "lake_logistics.logistics_shipment_label lsl \n", "inner join lake_logistics.logistics_shipment_item lsi on lsl.shipment_id=lsi.shipment_id \n", "inner join lake_logistics.logistics_shipment_item_complaint lsic on lsi.id = lsic.shipment_item_id\n", "where label_type='ORDER_ID' and reason not in ('Item Missing') \n", "and date(lsl.install_ts)>'{start_date}'::date-interval '10 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '10 day'\n", "and date(lsi.install_ts)>'{start_date}'::date- interval '10 day') a\n", "on oi.product_id=a.product_id and oi.order_id = a.order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = a.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where product_title not ilike '%%Smart Bachat Club Membership%%' and (date(oi.install_ts)>'{start_date}'::date-interval '10 day')\n", "group by 1,2,3\n", "\n", "union \n", "\n", "\n", "select sub.order_id::int,o.product_id,\n", "case when cat.parent_category_id in (9,1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "sum((procured_quantity-delivered_quantity)*price_per_unit) as return_gmv,\n", "sum((procured_quantity-delivered_quantity)) as return_qty\n", "from lake_last_mile.order_item o\n", "inner join lake_oms_bifrost.oms_suborder sub on sub.id = o.sub_order_id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = o.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where missing_reason is not null\n", "    and missing_reason not in ('MISSIGN') and sub.current_status not in ('REPROCURED','CANCELLED') and\n", "    (date(sub.install_ts)> '{start_date}'::DATE - INTERVAL '10 day') and\n", "    (date(updated_at)> '{start_date}'::DATE - INTERVAL '10 day')\n", "    group by 1,2,3\n", ") group by 1\n", "),\n", "\n", "\n", "refund_gmv as\n", "(select order_id, \n", "\n", "sum(case when invoice_type_id=2 then procurement_price*procurement_quantity end) as refund_amount,\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as fnv_reverse_sales,\n", "\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_price*procurement_quantity end) as non_fnv_reverse_sales,\n", "\n", "\n", "sum(case when fnv_flag='FnV' and invoice_type_id=2 then procurement_quantity end) as fnv_reverse_item_count,\n", "sum(case when fnv_flag='Non-FnV' and invoice_type_id=2 then procurement_quantity end) as non_fnv_reverse_item_count\n", "\n", "\n", "from\n", "    (select \n", "    \n", "        os.order_id,\n", "       \n", "        pos.invoice_type_id,\n", "        pospd.variant_id,\n", "        \n", "        case when pp.outlet_type in (1) then 'FnV'\n", "        else 'Non-FnV' end as fnv_flag,\n", "        \n", "        (pospd.selling_price) as procurement_price,\n", "        (pospd.quantity) as procurement_quantity\n", "    from\n", "        lake_oms_bifrost.oms_suborder \n", "            as os \n", "    \n", "     inner join lm_base \n", "            as lm \n", "            on lm.order_id=os.order_id\n", "    inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = lm.order_id\n", "    inner join  \n", "        lake_pos.pos_invoice \n", "            as pos \n", "            on os.id = pos.grofers_order_id  \n", "    inner join lake_pos.pos_invoice_product_details as pospd on pospd.invoice_id = pos.id\n", "    inner join lake_rpc.product_product pp on pp.variant_id = pospd.variant_id \n", "\n", "    Where \n", "        os.current_status not in ('REPROCURED','CANCELLED') \n", "        and pp.active=1 and pospd.store_offer_id is null\n", "        AND os.install_ts >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        AND pos.pos_timestamp >= '{start_date}'::DATE - INTERVAL '10 day' \n", "        and pos.invoice_type_id in (2) and product_title not ilike '%%Smart Bachat Club Membership%%'\n", "    group by 1,2,3,4,5,6) as a\n", "    group by 1),\n", "\n", "\n", "\n", "\n", "\n", "rpu_gmv as\n", "( \n", "select order_id, sum(case when fnv_flag='FnV' then rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when fnv_flag='Non-FnV' then rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "sum(case when fnv_flag='FnV' then quantity else 0 end) as fnv_rpu_qty,\n", "sum(case when fnv_flag='Non-FnV' then quantity else 0 end) as non_fnv_rpu_qty\n", "from\n", "\n", "(select oo.original_order_id as order_id, oi.product_id,\n", "case when cat.parent_category_id in (9,1487) then 'FnV'\n", "else 'Non-FnV' end as fnv_flag,\n", "oi.selling_price*oi.quantity as rpu_gmv,\n", "oi.quantity\n", "from lake_oms_bifrost.oms_order o \n", "inner join lake_oms_bifrost.oms_internal_order_mapping oo on oo.internal_order_id = o.id\n", "inner join lm_base lm on lm.order_id = oo.original_order_id\n", "inner join lake_oms_bifrost.oms_order_item oi on oi.order_id = o.id\n", "inner join lake_cms.gr_product_category_mapping pcm on pcm.product_id = oi.product_id and is_primary='True'\n", "inner join lake_cms.gr_category cat on cat.id = pcm.category_id\n", "where o.type = 'InternalReverseOrder' and o.current_status = 'DELIVERED'\n", "and date(o.install_ts)>='{start_date}'::DATE - INTERVAL '10 day' \n", "and date(oo.install_ts)>='{start_date}'::DATE - INTERVAL '10 day' \n", "group by 1,2,3,4,5\n", ") as a\n", "group by 1\n", ")\n", "\n", "\n", "\n", "\n", "\n", "select sch_date_ist,\n", "a.outlet_id,\n", "a.outlet_name,\n", "city,\n", "backend_merchant_id, \n", "frontend_merchant_id,\n", "--a.order_id,\n", "--frontend_merchant_name,\n", "-- count(distinct a.order_id) as total_sch_orders,\n", "count(distinct case when current_status='DELIVERED' then a.order_id end) as delivered_order_count,\n", "-- count(distinct case when current_status='CANCELLED' then a.order_id end) as cancelled_order_count,\n", "sum(case when current_status='DELIVERED' then forward_sales else 0 end) as forward_sales,\n", "-- sum(case when current_status='DELIVERED' then fnv_forward_sales else 0 end) as fnv_forward_sales,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_forward_sales else 0 end) as non_fnv_forward_sales,\n", "-- sum(case when current_status='DELIVERED' then fnv_forward_item_count else 0 end) as fnv_forward_item_count,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_forward_item_count else 0 end) as non_fnv_forward_item_count,\n", "sum(case when current_status='DELIVERED' then fnv_return_gmv else 0 end) as fnv_return_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_return_gmv else 0 end) as non_fnv_return_gmv,\n", "-- sum(case when current_status='DELIVERED' then fnv_return_qty else 0 end) as fnv_return_qty,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_return_qty else 0 end) as non_fnv_return_qty,\n", "sum(case when current_status='DELIVERED' then fnv_rpu_gmv else 0 end) as fnv_rpu_gmv,\n", "sum(case when current_status='DELIVERED' then non_fnv_rpu_gmv else 0 end) as non_fnv_rpu_gmv,\n", "-- sum(case when current_status='DELIVERED' then fnv_rpu_qty else 0 end) as fnv_rpu_qty,\n", "-- sum(case when current_status='DELIVERED' then non_fnv_rpu_qty else 0 end) as non_fnv_rpu_qty,\n", "sum(case when current_status='DELIVERED' and (fnv_return_gmv is null or fnv_return_gmv=0) and \n", "(non_fnv_return_gmv is null or non_fnv_return_gmv=0) and \n", "(fnv_rpu_gmv is null or fnv_rpu_gmv=0) and \n", "(non_fnv_rpu_gmv is null or non_fnv_rpu_gmv=0)\n", "then refund_amount else 0 end) as refund_amount\n", "from lm_base a \n", "left join total_gmv b on a.order_id=b.order_id\n", "left join return_gmv c on a.order_id=c.order_id\n", "left join rpu_gmv d on a.order_id=d.order_id\n", "left join refund_gmv ref on a.order_id = ref.order_id\n", "group by 1,2,3,4,5,6\n", "        \n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")\n", "\n", "\n", "final_q_data_new_new = pd.read_sql_query(final_q, redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_new_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp = final_q_data_new_new[\n", "    (final_q_data_new_new[\"sch_date_ist\"] >= start_date)\n", "    & (final_q_data_new_new[\"sch_date_ist\"] <= end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp[\"return_sales\"] = (\n", "    final_q_data_temp[\"fnv_return_gmv\"] + final_q_data_temp[\"non_fnv_return_gmv\"]\n", ")\n", "final_q_data_temp[\"rpu_sales\"] = (\n", "    final_q_data_temp[\"fnv_rpu_gmv\"] + final_q_data_temp[\"non_fnv_rpu_gmv\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"16vNWXk0_jBkXTr2aBu9hbjyvhMpljRJWpM-hqu4-qis\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["night_data = pb.from_sheets(sheet_id, \"Internal\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["night_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["night_data[\"Night_start_date\"] = night_data[\"Night Ops start Date\"].apply(\n", "    lambda x: datetime.strptime(x, \"%m/%d/%Y\").date() if x != \"\" else 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["night_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["night_data[\"Outlet Id\"] = night_data[\"Outlet Id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp_night = final_q_data_temp.merge(\n", "    night_data[[\"Outlet Id\", \"Night_start_date\"]],\n", "    right_on=\"Outlet Id\",\n", "    left_on=\"outlet_id\",\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp_night.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_night_sales = final_q_data_temp_night[\n", "    final_q_data_temp_night[\"Night_start_date\"] != 0\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_night_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_night_sales[\"Night_start_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def night_classify(x):\n", "    if x[\"Night_start_date\"] != 0:\n", "        if x[\"sch_date_ist\"] >= x[\"Night_start_date\"]:\n", "            return \"Night\"\n", "        else:\n", "            return \"Normal\"\n", "    else:\n", "        return \"Normal\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp_night[\"night\"] = final_q_data_temp_night.apply(\n", "    lambda x: night_classify(x), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp_night[final_q_data_temp_night[\"outlet_id\"] == 1056].sort_values(\n", "    \"sch_date_ist\"\n", ").head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_q_data_temp_temp = final_q_data_temp_night.groupby(\n", "    [\"outlet_id\", \"outlet_name\", \"night\"], as_index=False\n", ").agg(\n", "    {\n", "        \"forward_sales\": \"sum\",\n", "        \"return_sales\": \"sum\",\n", "        \"rpu_sales\": \"sum\",\n", "        \"refund_amount\": \"sum\",\n", "        \"delivered_order_count\": \"mean\",\n", "    }\n", ")\n", "final_q_data_temp_temp[\"net_sales\"] = (\n", "    final_q_data_temp_temp[\"forward_sales\"]\n", "    - final_q_data_temp_temp[\"return_sales\"]\n", "    - final_q_data_temp_temp[\"rpu_sales\"]\n", "    - final_q_data_temp_temp[\"refund_amount\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_q_data_temp_temp.pivot_table(\n", "    index=[\"outlet_id\", \"outlet_name\"],\n", "    columns=[\"night\"],\n", "    values=[\"net_sales\", \"delivered_order_count\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.columns = [\" \".join(col).strip() for col in final_data.columns.values]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_data.fillna(0)\n", "final_data = final_data.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data[\"avg_delivered_order_count\"] = final_data.apply(\n", "    lambda x: (x[\"delivered_order_count Night\"] + x[\"delivered_order_count Normal\"])\n", "    // 2\n", "    if x[\"delivered_order_count Night\"] > 0\n", "    else int(x[\"delivered_order_count Normal\"]),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_data, sheet_id, \"Sales Data\", clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}