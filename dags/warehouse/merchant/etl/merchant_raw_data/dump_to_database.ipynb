{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["source_alias = None\n", "primary_key = None\n", "sql = None\n", "target_alias = None\n", "target_schema = None\n", "target_table = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["source_connection = pb.get_connection(source_alias)\n", "source_df = pd.read_sql_query(sql, source_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_engine = pb.get_connection(target_alias)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["where_clause = primary_key[0] + \" > CURRENT_DATE - INTERVAL '10 days'\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# where_clauses = []\n", "# for where_tuple in source_df[primary_key].to_dict(orient=\"records\"):\n", "#     where_clauses.append(\n", "#         \" AND \".join([f\"{column} = '{value}'\" for column, value in where_tuple.items()])\n", "#     )\n", "# where_clause = \") OR (\".join(where_clauses)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with target_engine.begin() as connection:\n", "    connection.execute(\n", "        f\"DELETE FROM {target_schema}.{target_table} WHERE ({where_clause})\"\n", "    )\n", "    source_df.to_sql(\n", "        name=f\"{target_table}\",\n", "        con=connection,\n", "        schema=target_schema,\n", "        if_exists=\"append\",\n", "        index=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}