{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pip install --upgrade pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.__version__"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import warnings\n", "import numpy as np\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=3):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            print(\"Read from the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=3):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            print(\"Pushed Data to the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Sheet_id = \"1NMayfbmxR-ehA1IytHja2hMwJ9tPKjYa_8-T5knVSzI\""]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Coins"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reading SKUs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins = from_sheets(Sheet_id, \"Coins\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins.item_id.astype(int).unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inbound View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "inbound = pd.read_sql_query(\n", "    f\"\"\"SELECT f.id as facility_id,\n", "           f.name as facility_name,\n", "           i.vendor_id,\n", "           coalesce(m.name,v.vendor_name) as vendor_name,\n", "           i.vendor_invoice_id as invoice_id,\n", "           (case when i.source_type = 1 then 'PO'\n", "                when i.source_type = 2 then 'STO'\n", "                when i.source_type = 3 then 'CRWI'\n", "            else null end) as grn_type,\n", "           coalesce(i.po_id,'with_po') as po_id, \n", "           pp.item_id,\n", "           id.name as item_name,\n", "           max(ii.created_at + interval '330' minute) as grn_at,\n", "           coalesce(sum(po.units_ordered),0) as po_qty,\n", "           sum(po.units_ordered*po.landing_rate) as po_value,\n", "           sum(ii.\"delta\") as grn_qty,\n", "           sum(ii.landing_price * ii.\"delta\") AS grn_value,\n", "           hh.name as disc_reason,\n", "           max(hh.created_at + interval '330' minute) as disc_at,\n", "           coalesce(sum(hh.quantity),0) as disc_qty,\n", "           coalesce(sum(hh.value),0) as disc_value\n", "    FROM (select x.* from lake_view_ims.ims_inward_invoice x\n", "          join lake_view_retail.console_outlet c on c.id = x.outlet_id\n", "          where insert_ds_ist between '2022-09-20' and cast(current_date as varchar)\n", "          and c.business_type_id in (1,12) and x.po_id is not null) i\n", "    JOIN (select * from lake_view_ims.ims_inventory_stock_details\n", "          where insert_ds_ist between '2022-09-20' and cast(current_date as varchar)) ii ON ii.grn_id = i.grn_id\n", "    JOIN lake_view_retail.console_outlet c on c.id = i.outlet_id and c.business_type_id in (1,12)\n", "    JOIN lake_view_crates.facility f on f.id = c.facility_id\n", "    JOIN lake_view_rpc.product_product pp on pp.variant_id = ii.variant_id and pp.active = 1 and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "    JOIN lake_view_rpc.item_details id on id.item_id = pp.item_id\n", "    LEFT JOIN lake_view_retail.console_merchant m on m.id = i.vendor_id\n", "    LEFT JOIN lake_view_vms.vms_vendor v on v.id = i.vendor_id\n", "    LEFT JOIN lake_view_po.purchase_order p on p.po_number = i.po_id\n", "    LEFT JOIN lake_view_po.purchase_order_items po on po.po_id = p.id and po.item_id = pp.item_id\n", "    LEFT JOIN (select d.vendor_invoice_id,di.upc_id, dr.name, max(created_at) as created_at,sum(di.quantity) as quantity, sum(di.landing_price*di.quantity) as value \n", "    from lake_view_pos.discrepancy_note d\n", "    LEFT JOIN lake_view_pos.discrepancy_note_product_detail di ON d.id = di.dn_id_id\n", "    LEFT JOIN lake_view_pos.discrepancy_reason dr on dr.id = di.reason_code group by 1,2,3 ) hh on hh.vendor_invoice_id = i.vendor_invoice_id and hh.upc_id = pp.upc\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,15\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inb = inbound[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"grn_type\",\n", "        \"invoice_id\",\n", "        \"po_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"grn_value\",\n", "        \"grn_at\",\n", "        \"disc_reason\",\n", "        \"disc_at\",\n", "        \"disc_qty\",\n", "        \"disc_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound[\"grn_date\"] = pd.to_datetime(inbound[\"grn_at\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inb_summ = (\n", "    inbound.groupby(\n", "        [\"facility_id\", \"facility_name\", \"grn_type\", \"grn_date\", \"item_id\", \"item_name\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"po_qty\": sum,\n", "            \"grn_qty\": sum,\n", "            \"grn_value\": sum,\n", "            \"disc_qty\": sum,\n", "            \"disc_value\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inb_summ[\"diff_qty\"] = np.where(\n", "    inb_summ[\"grn_type\"] == \"PO\",\n", "    inb_summ[\"po_qty\"] - inb_summ[\"grn_qty\"] - inb_summ[\"disc_qty\"].fillna(0),\n", "    \"NA\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inb = inb.sort_values(by=[\"grn_at\", \"facility_id\"], ascending=[False, True])\n", "inb_summ = inb_summ.sort_values(by=[\"grn_date\", \"facility_id\"], ascending=[False, True])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inb[inb[\"po_id\"] == \"1829610000005\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_sheets(inb, Sheet_id, \"IB(WH)\")\n", "to_sheets(inb_summ, Sheet_id, \"IB(WH) Summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Outbound View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "outbound = pd.read_sql_query(\n", "    f\"\"\"\n", "        select c.facility_id as sender_facility_id,\n", "               f.name as sender_facility_name,\n", "               c1.facility_id as ds_facility_id,\n", "               f1.name as ds_facility_name,\n", "               e.sto_id,\n", "               e.item_id,\n", "               e.item_name,\n", "               max(sto_created_at) as sto_created_at,\n", "               avg(reserved_quantity) as sto_qty,\n", "               max(picking_completed_at_ist) as picked_at,\n", "               sum(picked_quantity) as picked_qty,\n", "               max(sto_invoice_created_at) as billed_at,\n", "               sum(coalesce(billed_quantity,0)) as billed_qty,\n", "               max(grn_started_at) as grn_at,\n", "               sum(coalesce(inwarded_quantity,0)) as grn_qty,\n", "               max(discrepancy_created_at) as disc_at,\n", "               sum(coalesce(disc_qty,0)) as disc_qty,\n", "               sum(coalesce(short_qty,0)) as short_qty,\n", "               sum(coalesce(excess_qty,0)) as excess_qty,\n", "               sum(coalesce(damage_qty,0)) as damage_qty,\n", "               sum(coalesce(wrong_item_qty,0)) as wrong_item_qty,\n", "               sum(coalesce(b2b_return_qty,0)) as b2b_qty,\n", "               sum(coalesce(b2breturn_damaged,0)) as b2b_damaged_qty,\n", "               sum(coalesce(b2breturn_near_expiry,0)) as b2b_near_expiry_qty,\n", "               sum(coalesce(b2breturn_expired,0)) as b2b_expiry_qty\n", "        from metrics.esto_details e\n", "        join lake_retail.console_outlet c on c.id = e.sender_outlet_id and c.business_type_id in (1,12)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_outlet c1 on c1.id = e.receiving_outlet_id and c1.business_type_id in (7)\n", "        join lake_crates.facility f1 on f1.id = c1.facility_id\n", "        where item_id in {*coins.item_id.astype(int).unique(),}\n", "        and sto_created_at::date between '2022-09-20' and current_date\n", "        group by 1,2,3,4,5,6,7\n", "    \"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outbound[\"diff_qty\"] = (\n", "    outbound[\"billed_qty\"] - outbound[\"grn_qty\"] - outbound[\"b2b_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outbound[\"sto_date\"] = pd.to_datetime(outbound[\"sto_created_at\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outbound[\"disc_qty\"] = outbound[\"disc_qty\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out_summ = (\n", "    outbound.groupby(\n", "        [\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"ds_facility_name\",\n", "            \"sto_date\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_qty\": sum,\n", "            \"billed_qty\": sum,\n", "            \"grn_qty\": sum,\n", "            \"disc_qty\": sum,\n", "            \"short_qty\": sum,\n", "            \"excess_qty\": sum,\n", "            \"damage_qty\": sum,\n", "            \"wrong_item_qty\": sum,\n", "            \"b2b_qty\": sum,\n", "            \"b2b_damaged_qty\": sum,\n", "            \"b2b_near_expiry_qty\": sum,\n", "            \"b2b_expiry_qty\": sum,\n", "            \"diff_qty\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_sheets(outbound, Sheet_id, \"OB(WH)\")\n", "to_sheets(out_summ, Sheet_id, \"OB(WH) Summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inventory view"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv = pd.read_sql_query(\n", "    f\"\"\" \n", "            WITH ris AS\n", "  (SELECT r.variant_id,\n", "          outlet_id,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = '2022-09-20' THEN quantity\n", "                  ELSE 0\n", "              END) AS opening_quantity,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS closing_quantity\n", "   FROM lake_view_reports.reports_inventory_snapshot r\n", "   join lake_view_retail.console_outlet c on c.id = r.outlet_id and c.business_type_id in (1,12)\n", "   join lake_view_rpc.product_product p on p.variant_id = r.variant_id \n", "   WHERE insert_ds_ist IN ('2022-09-20',\n", "                           cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "     AND p.item_id in {*coins.item_id.astype(int).unique(),}\n", "     AND r.active = 1\n", "     AND r.lake_active_record = True\n", "   GROUP BY 1,\n", "            2),\n", "     rgis AS\n", "  (SELECT rg.variant_id,\n", "          outlet_id,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = '2022-09-20' THEN quantity\n", "                  ELSE 0\n", "              END) AS opening_quantity,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS closing_quantity\n", "   FROM lake_view_reports.reports_good_inventory_snapshot rg\n", "   join lake_view_retail.console_outlet c on c.id = rg.outlet_id and c.business_type_id in (1,12)\n", "   join lake_view_rpc.product_product p on p.variant_id = rg.variant_id \n", "   WHERE insert_ds_ist IN ('2022-09-20',\n", "                           cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "     AND p.item_id in {*coins.item_id.astype(int).unique(),}\n", "     AND rg.active = 1\n", "     AND rg.lake_active_record = True\n", "   GROUP BY 1,\n", "            2),\n", "     pp AS\n", "  (SELECT variant_id,\n", "          item_id\n", "   FROM lake_rpc.product_product \n", " ),\n", "     id AS\n", "  (SELECT item_id,\n", "          name,\n", "          brand_id\n", "   FROM lake_rpc.item_details),\n", "     pb AS\n", "  (SELECT id,\n", "          name,\n", "          manufacturer_id\n", "   FROM lake_rpc.product_brand),\n", "     pm AS\n", "  (SELECT id,\n", "          name\n", "   FROM lake_rpc.product_manufacturer),\n", "     co AS\n", "  (SELECT id,\n", "          name,\n", "          company_type_id,\n", "          tax_location_id\n", "   FROM lake_retail.console_outlet\n", "   WHERE active = 1\n", "     AND device_id <> 47),\n", "     cl AS\n", "  (SELECT id,\n", "          state_id\n", "   FROM lake_retail.console_location),\n", "     cs AS\n", "  (SELECT id,\n", "          name AS state_name\n", "   FROM lake_retail.console_state),\n", "     comp AS\n", "  (SELECT id\n", "   FROM lake_retail.console_company_type\n", "   WHERE entity_type <> 'THIRD_PARTY'),\n", "     ims_logs AS\n", "  ( SELECT iil.outlet_id,\n", "           pp.item_id,\n", "           pp.variant_id,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (1,28,76,90) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS grns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (22, 75,139) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS sales,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (23, 35, 54, 82) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS sale_returns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (2, 50) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS customer_sales,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (3, 4, 5, 6, 29, 30, 31, 32, 81) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS customer_returns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (11, 12, 13, 64) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS badstock,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122,141) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS stock_update_positive,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS stock_update_negative,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (16, 127) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS liquidation,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (17, 92, 91, 124, 125) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS esto,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (18) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS repackaging,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (19, 126) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS vendor_return\n", "   FROM\n", "     (SELECT inventory_update_id,\n", "             outlet_id,\n", "             variant_id,\n", "             inventory_update_type_id,\n", "             \"delta\"\n", "      FROM lake_view_ims.ims_inventory_log\n", "      WHERE insert_ds_ist BETWEEN '2022-09-20' and cast(cast(current_date AS date) AS varchar)\n", "        AND inventory_update_type_id IN (1,\n", "                                         28,\n", "                                         76,\n", "                                         90,\n", "                                         22,\n", "                                         75,\n", "                                         23,\n", "                                         35,\n", "                                         54,\n", "                                         82,\n", "                                         2,\n", "                                         50,\n", "                                         3,\n", "                                         4,\n", "                                         5,\n", "                                         6,\n", "                                         29,\n", "                                         30,\n", "                                         31,\n", "                                         32,\n", "                                         81,\n", "                                         11,\n", "                                         12,\n", "                                         13,\n", "                                         64,\n", "                                         14,\n", "                                         26,\n", "                                         44,\n", "                                         45,\n", "                                         46,\n", "                                         47,\n", "                                         48,\n", "                                         74,\n", "                                         118,\n", "                                         121,\n", "                                         122,\n", "                                         15,\n", "                                         27,\n", "                                         39,\n", "                                         40,\n", "                                         41,\n", "                                         42,\n", "                                         43,\n", "                                         73,\n", "                                         117,\n", "                                         129,\n", "                                         130,\n", "                                         131,\n", "                                         132,\n", "                                         16,\n", "                                         127,\n", "                                         17,\n", "                                         92,\n", "                                         91,\n", "                                         124,\n", "                                         125,\n", "                                         18,\n", "                                         19,\n", "                                         126,\n", "                                         139,\n", "                                         141)) iil\n", "   INNER JOIN\n", "     (SELECT variant_id,\n", "             item_id\n", "      FROM lake_rpc.product_product\n", "      where item_id in {*coins.item_id.astype(int).unique(),}\n", "      ) pp ON cast(pp.variant_id AS varchar) = cast(iil.variant_id AS varchar)\n", "   GROUP BY 1,\n", "            2,\n", "            3)\n", "SELECT ends.*,\n", "       coalesce(i.grns,0) as grns,\n", "       coalesce(i.sales,0) as sales,\n", "       coalesce(i.sale_returns,0) as sale_returns,\n", "       coalesce(i.customer_sales,0) as customer_sales,\n", "       coalesce(i.customer_returns,0) as customer_returns,\n", "       coalesce(i.badstock,0) as badstock,\n", "       coalesce(i.stock_update_positive,0) as stock_update_positive,\n", "       coalesce(i.stock_update_negative,0) as stock_update_negative,\n", "       coalesce(i.liquidation,0) as liquidation,\n", "       coalesce(i.esto,0) as esto,\n", "       coalesce(i.repackaging,0) as repackaging,\n", "       coalesce(i.vendor_return,0) as vendor_return,\n", "       (ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) AS calculated_closing_quantity,\n", "       abs((ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) - ends.closing_quantity) AS difference\n", "FROM\n", "  (SELECT coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) AS outlet_id,\n", "          coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name, '-') AS outlet_name,\n", "          coalesce(inv_snap.item_id, good_inv_snap.item_id) AS item_id,\n", "          coalesce(inv_snap.variant_id, good_inv_snap.variant_id) as variant_id,\n", "          coalesce(inv_snap.name, good_inv_snap.name, '-') AS name,\n", "          coalesce(inv_snap.brand, good_inv_snap.brand, '-') AS brand,\n", "          coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer, '-') AS manufacturer,\n", "          coalesce(inv_snap.state_id, good_inv_snap.state_id) AS state_id,\n", "          coalesce(inv_snap.state_name, good_inv_snap.state_name, '-') AS state_name,\n", "          coalesce(inv_snap.opening_quantity,0) + coalesce(good_inv_snap.opening_quantity,0) AS opening_quantity,\n", "          coalesce(inv_snap.closing_quantity,0) + coalesce(good_inv_snap.closing_quantity,0) AS closing_quantity\n", "   FROM\n", "     (SELECT DISTINCT ris.outlet_id,\n", "                      co.name AS outlet_name,\n", "                      pp.item_id,\n", "                      id.name AS name,\n", "                      pb.name AS brand,\n", "                      pm.name AS manufacturer,\n", "                      cl.state_id,\n", "                      ris.variant_id,\n", "                      cs.state_name,\n", "                      ris.opening_quantity,\n", "                      ris.closing_quantity\n", "      FROM ris\n", "      INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(ris.variant_id AS varchar)\n", "      LEFT JOIN id ON id.item_id = pp.item_id\n", "      JOIN pb ON pb.id = id.brand_id\n", "      JOIN pm ON pm.id = pb.manufacturer_id\n", "      INNER JOIN co ON co.id = ris.outlet_id\n", "      LEFT JOIN cl ON cl.id = co.tax_location_id\n", "      LEFT JOIN cs ON cs.id = cl.state_id\n", "      INNER JOIN comp ON comp.id = co.company_type_id\n", ") inv_snap\n", "   FULL OUTER JOIN\n", "     (SELECT DISTINCT rgis.outlet_id,\n", "                      co.name AS outlet_name,\n", "                      pp.item_id,\n", "                      id.name AS name,\n", "                      pb.name AS brand,\n", "                      pm.name AS manufacturer,\n", "                      cl.state_id,\n", "                      cs.state_name,\n", "                      rgis.variant_id,\n", "                      rgis.opening_quantity,\n", "                      rgis.closing_quantity\n", "      FROM rgis\n", "      INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(rgis.variant_id AS varchar)\n", "      LEFT JOIN id ON id.item_id = pp.item_id\n", "      JOIN pb ON pb.id = id.brand_id\n", "      JOIN pm ON pm.id = pb.manufacturer_id\n", "      INNER JOIN co ON co.id = rgis.outlet_id\n", "      LEFT JOIN cl ON cl.id = co.tax_location_id\n", "      LEFT JOIN cs ON cs.id = cl.state_id\n", "      INNER JOIN comp ON comp.id = co.company_type_id\n", ") good_inv_snap ON inv_snap.item_id = good_inv_snap.item_id\n", "   AND inv_snap.outlet_id = good_inv_snap.outlet_id) ends\n", "LEFT JOIN ims_logs i ON cast(ends.variant_id AS varchar) = cast(i.variant_id AS varchar)\n", "AND cast(ends.outlet_id AS int) = cast(i.outlet_id AS int) \"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# ds = pd.read_sql_query(\n", "#     f\"\"\"\n", "#             WITH ris AS\n", "#   (SELECT r.variant_id,\n", "#           outlet_id,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS opening_quantity,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS closing_quantity\n", "#    FROM lake_view_reports.reports_inventory_snapshot r\n", "#    join lake_view_retail.console_outlet c on c.id = r.outlet_id and c.business_type_id in (7)\n", "#    join lake_view_rpc.product_product p on p.variant_id = r.variant_id\n", "#    WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "#                            cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "#      AND p.item_id in {*coins.item_id.astype(int).unique(),}\n", "#      AND r.active = 1\n", "#      AND r.lake_active_record = True\n", "#    GROUP BY 1,\n", "#             2),\n", "#      rgis AS\n", "#   (SELECT rg.variant_id,\n", "#           outlet_id,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS opening_quantity,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS closing_quantity\n", "#    FROM lake_view_reports.reports_good_inventory_snapshot rg\n", "#    join lake_view_retail.console_outlet c on c.id = rg.outlet_id and c.business_type_id in (7)\n", "#    join lake_view_rpc.product_product p on p.variant_id = rg.variant_id\n", "#    WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "#                            cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "#      AND p.item_id in {*coins.item_id.astype(int).unique(),}\n", "#      AND rg.active = 1\n", "#      AND rg.lake_active_record = True\n", "#    GROUP BY 1,\n", "#             2),\n", "#      pp AS\n", "#   (SELECT variant_id,\n", "#           item_id\n", "#    FROM lake_rpc.product_product\n", "#  ),\n", "#      id AS\n", "#   (SELECT item_id,\n", "#           name,\n", "#           brand_id\n", "#    FROM lake_rpc.item_details),\n", "#      pb AS\n", "#   (SELECT id,\n", "#           name,\n", "#           manufacturer_id\n", "#    FROM lake_rpc.product_brand),\n", "#      pm AS\n", "#   (SELECT id,\n", "#           name\n", "#    FROM lake_rpc.product_manufacturer),\n", "#      co AS\n", "#   (SELECT id,\n", "#           name,\n", "#           company_type_id,\n", "#           tax_location_id\n", "#    FROM lake_retail.console_outlet\n", "#    WHERE active = 1\n", "#      AND device_id <> 47),\n", "#      cl AS\n", "#   (SELECT id,\n", "#           state_id\n", "#    FROM lake_retail.console_location),\n", "#      cs AS\n", "#   (SELECT id,\n", "#           name AS state_name\n", "#    FROM lake_retail.console_state),\n", "#      comp AS\n", "#   (SELECT id\n", "#    FROM lake_retail.console_company_type\n", "#    WHERE entity_type <> 'THIRD_PARTY'),\n", "#      ims_logs AS\n", "#   ( SELECT iil.outlet_id,\n", "#            pp.item_id,\n", "#            pp.variant_id,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (1,28,76,90) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS grns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (22, 75,139) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS sales,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (23, 35, 54, 82) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS sale_returns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (2, 50) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS customer_sales,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (3, 4, 5, 6, 29, 30, 31, 32, 81) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS customer_returns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (11, 12, 13, 64) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS badstock,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122,141) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS stock_update_positive,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS stock_update_negative,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (16, 127) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS liquidation,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (17, 92, 91, 124, 125) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS esto,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (18) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS repackaging,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (19, 126) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS vendor_return\n", "#    FROM\n", "#      (SELECT inventory_update_id,\n", "#              outlet_id,\n", "#              variant_id,\n", "#              inventory_update_type_id,\n", "#              \"delta\"\n", "#       FROM lake_view_ims.ims_inventory_log\n", "#       WHERE insert_ds_ist BETWEEN cast(cast(current_date - interval '30' DAY AS date) AS varchar) and cast(cast(current_date AS date) AS varchar)\n", "#         AND inventory_update_type_id IN (1,\n", "#                                          28,\n", "#                                          76,\n", "#                                          90,\n", "#                                          22,\n", "#                                          75,\n", "#                                          23,\n", "#                                          35,\n", "#                                          54,\n", "#                                          82,\n", "#                                          2,\n", "#                                          50,\n", "#                                          3,\n", "#                                          4,\n", "#                                          5,\n", "#                                          6,\n", "#                                          29,\n", "#                                          30,\n", "#                                          31,\n", "#                                          32,\n", "#                                          81,\n", "#                                          11,\n", "#                                          12,\n", "#                                          13,\n", "#                                          64,\n", "#                                          14,\n", "#                                          26,\n", "#                                          44,\n", "#                                          45,\n", "#                                          46,\n", "#                                          47,\n", "#                                          48,\n", "#                                          74,\n", "#                                          118,\n", "#                                          121,\n", "#                                          122,\n", "#                                          15,\n", "#                                          27,\n", "#                                          39,\n", "#                                          40,\n", "#                                          41,\n", "#                                          42,\n", "#                                          43,\n", "#                                          73,\n", "#                                          117,\n", "#                                          129,\n", "#                                          130,\n", "#                                          131,\n", "#                                          132,\n", "#                                          16,\n", "#                                          127,\n", "#                                          17,\n", "#                                          92,\n", "#                                          91,\n", "#                                          124,\n", "#                                          125,\n", "#                                          18,\n", "#                                          19,\n", "#                                          126,\n", "#                                          139,\n", "#                                          141)) iil\n", "#    INNER JOIN\n", "#      (SELECT variant_id,\n", "#              item_id\n", "#       FROM lake_rpc.product_product\n", "#       where item_id in {*coins.item_id.astype(int).unique(),}\n", "#       ) pp ON cast(pp.variant_id AS varchar) = cast(iil.variant_id AS varchar)\n", "#    GROUP BY 1,\n", "#             2,\n", "#             3)\n", "# SELECT ends.*,\n", "#        coalesce(i.grns,0) as grns,\n", "#        coalesce(i.sales,0) as sales,\n", "#        coalesce(i.sale_returns,0) as sale_returns,\n", "#        coalesce(i.customer_sales,0) as customer_sales,\n", "#        coalesce(i.customer_returns,0) as customer_returns,\n", "#        coalesce(i.badstock,0) as badstock,\n", "#        coalesce(i.stock_update_positive,0) as stock_update_positive,\n", "#        coalesce(i.stock_update_negative,0) as stock_update_negative,\n", "#        coalesce(i.liquidation,0) as liquidation,\n", "#        coalesce(i.esto,0) as esto,\n", "#        coalesce(i.repackaging,0) as repackaging,\n", "#        coalesce(i.vendor_return,0) as vendor_return,\n", "#        (ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) AS calculated_closing_quantity,\n", "#        abs((ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) - ends.closing_quantity) AS difference\n", "# FROM\n", "#   (SELECT coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) AS outlet_id,\n", "#           coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name, '-') AS outlet_name,\n", "#           coalesce(inv_snap.item_id, good_inv_snap.item_id) AS item_id,\n", "#           coalesce(inv_snap.variant_id, good_inv_snap.variant_id) as variant_id,\n", "#           coalesce(inv_snap.name, good_inv_snap.name, '-') AS name,\n", "#           coalesce(inv_snap.brand, good_inv_snap.brand, '-') AS brand,\n", "#           coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer, '-') AS manufacturer,\n", "#           coalesce(inv_snap.state_id, good_inv_snap.state_id) AS state_id,\n", "#           coalesce(inv_snap.state_name, good_inv_snap.state_name, '-') AS state_name,\n", "#           coalesce(inv_snap.opening_quantity,0) + coalesce(good_inv_snap.opening_quantity,0) AS opening_quantity,\n", "#           coalesce(inv_snap.closing_quantity,0) + coalesce(good_inv_snap.closing_quantity,0) AS closing_quantity\n", "#    FROM\n", "#      (SELECT DISTINCT ris.outlet_id,\n", "#                       co.name AS outlet_name,\n", "#                       pp.item_id,\n", "#                       id.name AS name,\n", "#                       pb.name AS brand,\n", "#                       pm.name AS manufacturer,\n", "#                       cl.state_id,\n", "#                       ris.variant_id,\n", "#                       cs.state_name,\n", "#                       ris.opening_quantity,\n", "#                       ris.closing_quantity\n", "#       FROM ris\n", "#       INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(ris.variant_id AS varchar)\n", "#       LEFT JOIN id ON id.item_id = pp.item_id\n", "#       JOIN pb ON pb.id = id.brand_id\n", "#       JOIN pm ON pm.id = pb.manufacturer_id\n", "#       INNER JOIN co ON co.id = ris.outlet_id\n", "#       LEFT JOIN cl ON cl.id = co.tax_location_id\n", "#       LEFT JOIN cs ON cs.id = cl.state_id\n", "#       INNER JOIN comp ON comp.id = co.company_type_id\n", "# ) inv_snap\n", "#    FULL OUTER JOIN\n", "#      (SELECT DISTINCT rgis.outlet_id,\n", "#                       co.name AS outlet_name,\n", "#                       pp.item_id,\n", "#                       id.name AS name,\n", "#                       pb.name AS brand,\n", "#                       pm.name AS manufacturer,\n", "#                       cl.state_id,\n", "#                       cs.state_name,\n", "#                       rgis.variant_id,\n", "#                       rgis.opening_quantity,\n", "#                       rgis.closing_quantity\n", "#       FROM rgis\n", "#       INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(rgis.variant_id AS varchar)\n", "#       LEFT JOIN id ON id.item_id = pp.item_id\n", "#       JOIN pb ON pb.id = id.brand_id\n", "#       JOIN pm ON pm.id = pb.manufacturer_id\n", "#       INNER JOIN co ON co.id = rgis.outlet_id\n", "#       LEFT JOIN cl ON cl.id = co.tax_location_id\n", "#       LEFT JOIN cs ON cs.id = cl.state_id\n", "#       INNER JOIN comp ON comp.id = co.company_type_id\n", "# ) good_inv_snap ON inv_snap.item_id = good_inv_snap.item_id\n", "#    AND inv_snap.outlet_id = good_inv_snap.outlet_id) ends\n", "# LEFT JOIN ims_logs i ON cast(ends.variant_id AS varchar) = cast(i.variant_id AS varchar)\n", "# AND cast(ends.outlet_id AS int) = cast(i.outlet_id AS int) \"\"\",\n", "#     presto,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_sheets(inv, Sheet_id, \"Inv(WH) Summary\")\n", "# to_sheets(ds, Sheet_id, \"Inv(DS) Summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Current inventory view"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins = pd.read_sql_query(\n", "    f\"\"\"\n", "        with inv as (select c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as quantity\n", "        from lake_view_ims.ims_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1\n", "        and c.business_type_id in (1,12,7)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5),\n", "        good_inv as (select c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as good_qty\n", "        from lake_view_ims.ims_good_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1 and c.business_type_id in (1,12,7)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5),\n", "        bad_inv as (select c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,\n", "        sum(i.quantity) as bad_qty,\n", "        sum(case when i.inventory_update_type_id in (11,87) then i.quantity else 0 end) as bad_damage_qty,\n", "        sum(case when i.inventory_update_type_id in (12,88) then i.quantity else 0 end) as bad_expiry_qty,\n", "        sum(case when i.inventory_update_type_id in (64,89) then i.quantity else 0 end) as bad_nearexpiry_qty\n", "        from lake_view_ims.ims_bad_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in {*coins.item_id.astype(int).unique(),}\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1\n", "         and c.business_type_id in (1,12,7)\n", "         join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5)\n", "        select \n", "            coalesce(inv.city, good_inv.city, bad_inv.city) as city,\n", "            coalesce(inv.facility_id, good_inv.facility_id, bad_inv.facility_id) as facility_id,\n", "            coalesce(inv.facility_name, good_inv.facility_name, bad_inv.facility_name) as facility_name,\n", "            coalesce(inv.item_id, good_inv.item_id, bad_inv.item_id) as item_id,\n", "            coalesce(inv.item_name, good_inv.item_name, bad_inv.item_name) as item_name,\n", "            coalesce(inv.quantity,0) as on_shelf_inventory,\n", "            coalesce(good_inv.good_qty,0) as good_inventory,\n", "            coalesce(bad_inv.bad_qty,0) as bad_inventory,\n", "            coalesce(bad_damage_qty,0) as bad_damage_qty,\n", "            coalesce(bad_expiry_qty,0) as bad_expiry_qty,            \n", "            coalesce(bad_nearexpiry_qty,0) as bad_nearexpiry_qty\n", "        from inv\n", "        full outer join good_inv on good_inv.facility_id = inv.facility_id and inv.item_id = good_inv.item_id\n", "        full outer join bad_inv on bad_inv.facility_id = inv.facility_id and inv.item_id = bad_inv.item_id\n", "        \"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_sheets(coins, Sheet_id, \"Current_Inventory\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "raw", "metadata": {"tags": []}, "source": ["# Diwali SKUs"]}, {"cell_type": "raw", "metadata": {}, "source": ["## Reading SKUs"]}, {"cell_type": "raw", "metadata": {}, "source": ["Sheet_id = \"1afkBga8LN8syjLJfeZt32_zqgxl5zbUNudcXB_CILTw\""]}, {"cell_type": "raw", "metadata": {}, "source": ["diw = from_sheets(Sheet_id, \"Diwali SKUs\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["diw.item_id.astype(int).nunique()"]}, {"cell_type": "raw", "metadata": {}, "source": ["## Inbound View"]}, {"cell_type": "raw", "metadata": {}, "source": ["import time\n", "st = time.time()\n", "inbound = pd.read_sql_query(\n", "    f\"\"\"SELECT f.id as facility_id,\n", "                       f.name as facility_name,\n", "                       i.vendor_id,\n", "                       coalesce(m.name,v.vendor_name) as vendor_name,\n", "                       i.vendor_invoice_id as invoice_id,\n", "                       (case when i.source_type = 1 then 'PO'\n", "                            when i.source_type = 2 then 'STO'\n", "                            when i.source_type = 3 then 'CRWI'\n", "                        else null end) as grn_type,\n", "                       i.po_id, \n", "                       pp.item_id,\n", "                       id.name as item_name,\n", "                       max(ii.created_at + interval '330' minute) as grn_at,\n", "                       sum(po.units_ordered*po.landing_rate) as po_value,\n", "                       sum(ii.\"delta\") as grn_qty,\n", "                       sum(ii.landing_price * ii.\"delta\") AS grn_value,\n", "                       dr.name as disc_reason,\n", "                       max(d.created_at + interval '330' minute) as disc_at,\n", "                       coalesce(sum(di.quantity),0) as disc_qty,\n", "                       coalesce(sum(di.landing_price*di.\"quantity\"),0) as disc_value\n", "    FROM (select x.* from lake_view_ims.ims_inward_invoice x\n", "          join lake_view_retail.console_outlet c on c.id = x.outlet_id\n", "          where insert_ds_ist between cast(current_date - interval '15' DAY as varchar) and cast(current_date as varchar)\n", "          and c.business_type_id in (1,12)) i\n", "    JOIN (select * from lake_view_ims.ims_inventory_stock_details\n", "          where insert_ds_ist between cast(current_date - interval '15' DAY as varchar) and cast(current_date as varchar)) ii ON ii.grn_id = i.grn_id\n", "    JOIN lake_view_retail.console_outlet c on c.id = i.outlet_id and c.business_type_id in (1,12)\n", "    JOIN lake_view_crates.facility f on f.id = c.facility_id\n", "    JOIN lake_view_rpc.product_product pp on pp.variant_id = ii.variant_id and pp.active = 1\n", "    and pp.item_id in {*diw.item_id.astype(int).unique(),}\n", "    JOIN lake_view_rpc.item_details id on id.item_id = pp.item_id\n", "    LEFT JOIN lake_view_retail.console_merchant m on m.id = i.vendor_id\n", "    LEFT JOIN lake_view_vms.vms_vendor v on v.id = i.vendor_id\n", "    LEFT JOIN lake_view_po.purchase_order p on p.po_number = i.po_id\n", "    LEFT JOIN lake_view_po.purchase_order_items po on po.po_id = p.id\n", "    LEFT JOIN lake_view_pos.discrepancy_note d ON d.vendor_invoice_id = i.vendor_invoice_id\n", "    LEFT JOIN lake_view_pos.discrepancy_note_product_detail di ON d.id = di.dn_id_id and di.upc_id = pp.upc\n", "    left join lake_view_pos.discrepancy_reason dr on dr.id = di.reason_code\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,14\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "raw", "metadata": {}, "source": ["inb = inbound[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"grn_type\",\n", "        \"invoice_id\",\n", "        \"po_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"grn_qty\",\n", "        \"grn_value\",\n", "        \"grn_at\",\n", "        \"disc_reason\",\n", "        \"disc_at\",\n", "        \"disc_qty\",\n", "        \"disc_value\",\n", "    ]\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["inbound[\"grn_date\"] = pd.to_datetime(inbound[\"grn_at\"]).dt.date"]}, {"cell_type": "raw", "metadata": {}, "source": ["inb_summ = (\n", "    inbound.groupby(\n", "        [\"facility_id\", \"facility_name\", \"grn_type\", \"grn_date\", \"item_id\", \"item_name\"]\n", "    )\n", "    .agg({\"grn_qty\": sum, \"grn_value\": sum, \"disc_qty\": sum, \"disc_value\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["# to_sheets(inb, Sheet_id,'IB(WH)')\n", "to_sheets(inb_summ, Sheet_id, \"IB(WH) Summary\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["## Outbound View"]}, {"cell_type": "raw", "metadata": {}, "source": ["import time\n", "\n", "st = time.time()\n", "outbound = pd.read_sql_query(\n", "    f\"\"\"\n", "                select c.facility_id as sender_facility_id,\n", "                       f.name as sender_facility_name,\n", "                       c1.facility_id as ds_facility_id,\n", "                       f1.name as ds_facility_name,\n", "                       e.sto_id,\n", "                       e.item_id,\n", "                       e.item_name,\n", "                       max(sto_created_at) as sto_created_at,\n", "                       avg(reserved_quantity) as sto_qty,\n", "                       max(picking_completed_at_ist) as picked_at,\n", "                       sum(picked_quantity) as picked_qty,\n", "                       max(sto_invoice_created_at) as billed_at,\n", "                       sum(coalesce(billed_quantity,0)) as billed_qty,\n", "                       max(grn_started_at) as grn_at,\n", "                       sum(coalesce(inwarded_quantity,0)) as grn_qty,\n", "                       max(discrepancy_created_at) as disc_at,\n", "                       sum(coalesce(disc_qty,0)) as disc_qty,\n", "                       sum(coalesce(b2b_return_qty,0)) as b2b_qty\n", "                from metrics.esto_details e\n", "                join lake_retail.console_outlet c on c.id = e.sender_outlet_id and c.business_type_id in (1,12)\n", "                join lake_crates.facility f on f.id = c.facility_id\n", "                join lake_retail.console_outlet c1 on c1.id = e.receiving_outlet_id and c1.business_type_id in (7)\n", "                join lake_crates.facility f1 on f1.id = c1.facility_id\n", "                where item_id in {*diw.item_id.astype(int).unique(),}\n", "                and sto_created_at::date between current_date-15 and current_date\n", "                group by 1,2,3,4,5,6,7\n", "            \"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "raw", "metadata": {}, "source": ["outbound[\"diff_qty\"] = (\n", "    outbound[\"billed_qty\"] - outbound[\"grn_qty\"] - outbound[\"b2b_qty\"]\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["outbound[\"sto_date\"] = pd.to_datetime(outbound[\"sto_created_at\"]).dt.date"]}, {"cell_type": "raw", "metadata": {}, "source": ["out_summ = (\n", "    outbound.groupby(\n", "        [\"sender_facility_name\", \"ds_facility_name\", \"sto_date\", \"item_name\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_qty\": sum,\n", "            \"billed_qty\": sum,\n", "            \"grn_qty\": sum,\n", "            \"disc_qty\": sum,\n", "            \"b2b_qty\": sum,\n", "            \"diff_qty\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["out_summ.shape"]}, {"cell_type": "raw", "metadata": {}, "source": ["out_summ = out_summ[out_summ[\"diff_qty\"] > 0]"]}, {"cell_type": "raw", "metadata": {}, "source": ["# to_sheets(outbound, Sheet_id,'OB(WH)')\n", "to_sheets(out_summ, Sheet_id, \"OB(WH) Summary\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["## Inventory view"]}, {"cell_type": "raw", "metadata": {}, "source": ["inv = pd.read_sql_query(\n", "    f\"\"\" \n", "            WITH ris AS\n", "  (SELECT r.variant_id,\n", "          outlet_id,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS opening_quantity,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS closing_quantity\n", "   FROM lake_view_reports.reports_inventory_snapshot r\n", "   join lake_view_retail.console_outlet c on c.id = r.outlet_id and c.business_type_id in (1,12)\n", "   join lake_view_rpc.product_product p on p.variant_id = r.variant_id \n", "   WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "                           cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "     AND p.item_id in {*diw.item_id.astype(int).unique(),}\n", "     AND r.active = 1\n", "     AND r.lake_active_record = True\n", "   GROUP BY 1,\n", "            2),\n", "     rgis AS\n", "  (SELECT rg.variant_id,\n", "          outlet_id,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS opening_quantity,\n", "          sum(CASE\n", "                  WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "                  ELSE 0\n", "              END) AS closing_quantity\n", "   FROM lake_view_reports.reports_good_inventory_snapshot rg\n", "   join lake_view_retail.console_outlet c on c.id = rg.outlet_id and c.business_type_id in (1,12)\n", "   join lake_view_rpc.product_product p on p.variant_id = rg.variant_id \n", "   WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "                           cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "     AND p.item_id in {*diw.item_id.astype(int).unique(),}\n", "     AND rg.active = 1\n", "     AND rg.lake_active_record = True\n", "   GROUP BY 1,\n", "            2),\n", "     pp AS\n", "  (SELECT variant_id,\n", "          item_id\n", "   FROM lake_rpc.product_product \n", " ),\n", "     id AS\n", "  (SELECT item_id,\n", "          name,\n", "          brand_id\n", "   FROM lake_rpc.item_details),\n", "     pb AS\n", "  (SELECT id,\n", "          name,\n", "          manufacturer_id\n", "   FROM lake_rpc.product_brand),\n", "     pm AS\n", "  (SELECT id,\n", "          name\n", "   FROM lake_rpc.product_manufacturer),\n", "     co AS\n", "  (SELECT id,\n", "          name,\n", "          company_type_id,\n", "          tax_location_id\n", "   FROM lake_retail.console_outlet\n", "   WHERE active = 1\n", "     AND device_id <> 47),\n", "     cl AS\n", "  (SELECT id,\n", "          state_id\n", "   FROM lake_retail.console_location),\n", "     cs AS\n", "  (SELECT id,\n", "          name AS state_name\n", "   FROM lake_retail.console_state),\n", "     comp AS\n", "  (SELECT id\n", "   FROM lake_retail.console_company_type\n", "   WHERE entity_type <> 'THIRD_PARTY'),\n", "     ims_logs AS\n", "  ( SELECT iil.outlet_id,\n", "           pp.item_id,\n", "           pp.variant_id,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (1,28,76,90) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS grns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (22, 75,139) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS sales,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (23, 35, 54, 82) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS sale_returns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (2, 50) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS customer_sales,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (3, 4, 5, 6, 29, 30, 31, 32, 81) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS customer_returns,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (11, 12, 13, 64) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS badstock,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122,141) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS stock_update_positive,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS stock_update_negative,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (16, 127) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS liquidation,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (17, 92, 91, 124, 125) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS esto,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (18) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS repackaging,\n", "           sum(CASE\n", "                   WHEN iil.inventory_update_type_id IN (19, 126) THEN \"delta\"\n", "                   ELSE 0\n", "               END) AS vendor_return\n", "   FROM\n", "     (SELECT inventory_update_id,\n", "             outlet_id,\n", "             variant_id,\n", "             inventory_update_type_id,\n", "             \"delta\"\n", "      FROM lake_view_ims.ims_inventory_log\n", "      WHERE insert_ds_ist BETWEEN cast(cast(current_date - interval '30' DAY AS date) AS varchar) and cast(cast(current_date AS date) AS varchar)\n", "        AND inventory_update_type_id IN (1,\n", "                                         28,\n", "                                         76,\n", "                                         90,\n", "                                         22,\n", "                                         75,\n", "                                         23,\n", "                                         35,\n", "                                         54,\n", "                                         82,\n", "                                         2,\n", "                                         50,\n", "                                         3,\n", "                                         4,\n", "                                         5,\n", "                                         6,\n", "                                         29,\n", "                                         30,\n", "                                         31,\n", "                                         32,\n", "                                         81,\n", "                                         11,\n", "                                         12,\n", "                                         13,\n", "                                         64,\n", "                                         14,\n", "                                         26,\n", "                                         44,\n", "                                         45,\n", "                                         46,\n", "                                         47,\n", "                                         48,\n", "                                         74,\n", "                                         118,\n", "                                         121,\n", "                                         122,\n", "                                         15,\n", "                                         27,\n", "                                         39,\n", "                                         40,\n", "                                         41,\n", "                                         42,\n", "                                         43,\n", "                                         73,\n", "                                         117,\n", "                                         129,\n", "                                         130,\n", "                                         131,\n", "                                         132,\n", "                                         16,\n", "                                         127,\n", "                                         17,\n", "                                         92,\n", "                                         91,\n", "                                         124,\n", "                                         125,\n", "                                         18,\n", "                                         19,\n", "                                         126,\n", "                                         139,\n", "                                         141)) iil\n", "   INNER JOIN\n", "     (SELECT variant_id,\n", "             item_id\n", "      FROM lake_rpc.product_product\n", "      where item_id in {*diw.item_id.astype(int).unique(),}\n", "      ) pp ON cast(pp.variant_id AS varchar) = cast(iil.variant_id AS varchar)\n", "   GROUP BY 1,\n", "            2,\n", "            3)\n", "SELECT ends.*,\n", "       coalesce(i.grns,0) as grns,\n", "       coalesce(i.sales,0) as sales,\n", "       coalesce(i.sale_returns,0) as sale_returns,\n", "       coalesce(i.customer_sales,0) as customer_sales,\n", "       coalesce(i.customer_returns,0) as customer_returns,\n", "       coalesce(i.badstock,0) as badstock,\n", "       coalesce(i.stock_update_positive,0) as stock_update_positive,\n", "       coalesce(i.stock_update_negative,0) as stock_update_negative,\n", "       coalesce(i.liquidation,0) as liquidation,\n", "       coalesce(i.esto,0) as esto,\n", "       coalesce(i.repackaging,0) as repackaging,\n", "       coalesce(i.vendor_return,0) as vendor_return,\n", "       (ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) AS calculated_closing_quantity,\n", "       abs((ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) - ends.closing_quantity) AS difference\n", "FROM\n", "  (SELECT coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) AS outlet_id,\n", "          coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name, '-') AS outlet_name,\n", "          coalesce(inv_snap.item_id, good_inv_snap.item_id) AS item_id,\n", "          coalesce(inv_snap.variant_id, good_inv_snap.variant_id) as variant_id,\n", "          coalesce(inv_snap.name, good_inv_snap.name, '-') AS name,\n", "          coalesce(inv_snap.brand, good_inv_snap.brand, '-') AS brand,\n", "          coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer, '-') AS manufacturer,\n", "          coalesce(inv_snap.state_id, good_inv_snap.state_id) AS state_id,\n", "          coalesce(inv_snap.state_name, good_inv_snap.state_name, '-') AS state_name,\n", "          coalesce(inv_snap.opening_quantity,0) + coalesce(good_inv_snap.opening_quantity,0) AS opening_quantity,\n", "          coalesce(inv_snap.closing_quantity,0) + coalesce(good_inv_snap.closing_quantity,0) AS closing_quantity\n", "   FROM\n", "     (SELECT DISTINCT ris.outlet_id,\n", "                      co.name AS outlet_name,\n", "                      pp.item_id,\n", "                      id.name AS name,\n", "                      pb.name AS brand,\n", "                      pm.name AS manufacturer,\n", "                      cl.state_id,\n", "                      ris.variant_id,\n", "                      cs.state_name,\n", "                      ris.opening_quantity,\n", "                      ris.closing_quantity\n", "      FROM ris\n", "      INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(ris.variant_id AS varchar)\n", "      LEFT JOIN id ON id.item_id = pp.item_id\n", "      JOIN pb ON pb.id = id.brand_id\n", "      JOIN pm ON pm.id = pb.manufacturer_id\n", "      INNER JOIN co ON co.id = ris.outlet_id\n", "      LEFT JOIN cl ON cl.id = co.tax_location_id\n", "      LEFT JOIN cs ON cs.id = cl.state_id\n", "      INNER JOIN comp ON comp.id = co.company_type_id\n", ") inv_snap\n", "   FULL OUTER JOIN\n", "     (SELECT DISTINCT rgis.outlet_id,\n", "                      co.name AS outlet_name,\n", "                      pp.item_id,\n", "                      id.name AS name,\n", "                      pb.name AS brand,\n", "                      pm.name AS manufacturer,\n", "                      cl.state_id,\n", "                      cs.state_name,\n", "                      rgis.variant_id,\n", "                      rgis.opening_quantity,\n", "                      rgis.closing_quantity\n", "      FROM rgis\n", "      INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(rgis.variant_id AS varchar)\n", "      LEFT JOIN id ON id.item_id = pp.item_id\n", "      JOIN pb ON pb.id = id.brand_id\n", "      JOIN pm ON pm.id = pb.manufacturer_id\n", "      INNER JOIN co ON co.id = rgis.outlet_id\n", "      LEFT JOIN cl ON cl.id = co.tax_location_id\n", "      LEFT JOIN cs ON cs.id = cl.state_id\n", "      INNER JOIN comp ON comp.id = co.company_type_id\n", ") good_inv_snap ON inv_snap.item_id = good_inv_snap.item_id\n", "   AND inv_snap.outlet_id = good_inv_snap.outlet_id) ends\n", "LEFT JOIN ims_logs i ON cast(ends.variant_id AS varchar) = cast(i.variant_id AS varchar)\n", "AND cast(ends.outlet_id AS int) = cast(i.outlet_id AS int) \"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["# ds = pd.read_sql_query(f\"\"\"\n", "#             WITH ris AS\n", "#   (SELECT r.variant_id,\n", "#           outlet_id,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS opening_quantity,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS closing_quantity\n", "#    FROM lake_view_reports.reports_inventory_snapshot r\n", "#    join lake_view_retail.console_outlet c on c.id = r.outlet_id and c.business_type_id in (7)\n", "#    join lake_view_rpc.product_product p on p.variant_id = r.variant_id\n", "#    WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "#                            cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "#      AND p.item_id in {*diw.item_id.astype(int).unique(),}\n", "#      AND r.active = 1\n", "#      AND r.lake_active_record = True\n", "#    GROUP BY 1,\n", "#             2),\n", "#      rgis AS\n", "#   (SELECT rg.variant_id,\n", "#           outlet_id,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '31' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS opening_quantity,\n", "#           sum(CASE\n", "#                   WHEN insert_ds_ist = cast(cast(current_date - interval '1' DAY AS date) AS varchar) THEN quantity\n", "#                   ELSE 0\n", "#               END) AS closing_quantity\n", "#    FROM lake_view_reports.reports_good_inventory_snapshot rg\n", "#    join lake_view_retail.console_outlet c on c.id = rg.outlet_id and c.business_type_id in (7)\n", "#    join lake_view_rpc.product_product p on p.variant_id = rg.variant_id\n", "#    WHERE insert_ds_ist IN (cast(cast(current_date - interval '31' DAY AS date) AS varchar),\n", "#                            cast(cast(current_date - interval '1' DAY AS date) AS varchar))\n", "#      AND p.item_id in {*diw.item_id.astype(int).unique(),}\n", "#      AND rg.active = 1\n", "#      AND rg.lake_active_record = True\n", "#    GROUP BY 1,\n", "#             2),\n", "#      pp AS\n", "#   (SELECT variant_id,\n", "#           item_id\n", "#    FROM lake_rpc.product_product\n", "#  ),\n", "#      id AS\n", "#   (SELECT item_id,\n", "#           name,\n", "#           brand_id\n", "#    FROM lake_rpc.item_details),\n", "#      pb AS\n", "#   (SELECT id,\n", "#           name,\n", "#           manufacturer_id\n", "#    FROM lake_rpc.product_brand),\n", "#      pm AS\n", "#   (SELECT id,\n", "#           name\n", "#    FROM lake_rpc.product_manufacturer),\n", "#      co AS\n", "#   (SELECT id,\n", "#           name,\n", "#           company_type_id,\n", "#           tax_location_id\n", "#    FROM lake_retail.console_outlet\n", "#    WHERE active = 1\n", "#      AND device_id <> 47),\n", "#      cl AS\n", "#   (SELECT id,\n", "#           state_id\n", "#    FROM lake_retail.console_location),\n", "#      cs AS\n", "#   (SELECT id,\n", "#           name AS state_name\n", "#    FROM lake_retail.console_state),\n", "#      comp AS\n", "#   (SELECT id\n", "#    FROM lake_retail.console_company_type\n", "#    WHERE entity_type <> 'THIRD_PARTY'),\n", "#      ims_logs AS\n", "#   ( SELECT iil.outlet_id,\n", "#            pp.item_id,\n", "#            pp.variant_id,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (1,28,76,90) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS grns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (22, 75,139) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS sales,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (23, 35, 54, 82) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS sale_returns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (2, 50) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS customer_sales,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (3, 4, 5, 6, 29, 30, 31, 32, 81) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS customer_returns,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (11, 12, 13, 64) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS badstock,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (14, 26, 44, 45, 46, 47, 48, 74, 118, 121, 122,141) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS stock_update_positive,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (15, 27, 39, 40, 41, 42, 43, 73, 117, 129, 130, 131, 132) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS stock_update_negative,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (16, 127) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS liquidation,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (17, 92, 91, 124, 125) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS esto,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (18) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS repackaging,\n", "#            sum(CASE\n", "#                    WHEN iil.inventory_update_type_id IN (19, 126) THEN \"delta\"\n", "#                    ELSE 0\n", "#                END) AS vendor_return\n", "#    FROM\n", "#      (SELECT inventory_update_id,\n", "#              outlet_id,\n", "#              variant_id,\n", "#              inventory_update_type_id,\n", "#              \"delta\"\n", "#       FROM lake_view_ims.ims_inventory_log\n", "#       WHERE insert_ds_ist BETWEEN cast(cast(current_date - interval '30' DAY AS date) AS varchar) and cast(cast(current_date AS date) AS varchar)\n", "#         AND inventory_update_type_id IN (1,\n", "#                                          28,\n", "#                                          76,\n", "#                                          90,\n", "#                                          22,\n", "#                                          75,\n", "#                                          23,\n", "#                                          35,\n", "#                                          54,\n", "#                                          82,\n", "#                                          2,\n", "#                                          50,\n", "#                                          3,\n", "#                                          4,\n", "#                                          5,\n", "#                                          6,\n", "#                                          29,\n", "#                                          30,\n", "#                                          31,\n", "#                                          32,\n", "#                                          81,\n", "#                                          11,\n", "#                                          12,\n", "#                                          13,\n", "#                                          64,\n", "#                                          14,\n", "#                                          26,\n", "#                                          44,\n", "#                                          45,\n", "#                                          46,\n", "#                                          47,\n", "#                                          48,\n", "#                                          74,\n", "#                                          118,\n", "#                                          121,\n", "#                                          122,\n", "#                                          15,\n", "#                                          27,\n", "#                                          39,\n", "#                                          40,\n", "#                                          41,\n", "#                                          42,\n", "#                                          43,\n", "#                                          73,\n", "#                                          117,\n", "#                                          129,\n", "#                                          130,\n", "#                                          131,\n", "#                                          132,\n", "#                                          16,\n", "#                                          127,\n", "#                                          17,\n", "#                                          92,\n", "#                                          91,\n", "#                                          124,\n", "#                                          125,\n", "#                                          18,\n", "#                                          19,\n", "#                                          126,\n", "#                                          139,\n", "#                                          141)) iil\n", "#    INNER JOIN\n", "#      (SELECT variant_id,\n", "#              item_id\n", "#       FROM lake_rpc.product_product\n", "#       where item_id in {*diw.item_id.astype(int).unique(),}\n", "#       ) pp ON cast(pp.variant_id AS varchar) = cast(iil.variant_id AS varchar)\n", "#    GROUP BY 1,\n", "#             2,\n", "#             3)\n", "# SELECT ends.*,\n", "#        coalesce(i.grns,0) as grns,\n", "#        coalesce(i.sales,0) as sales,\n", "#        coalesce(i.sale_returns,0) as sale_returns,\n", "#        coalesce(i.customer_sales,0) as customer_sales,\n", "#        coalesce(i.customer_returns,0) as customer_returns,\n", "#        coalesce(i.badstock,0) as badstock,\n", "#        coalesce(i.stock_update_positive,0) as stock_update_positive,\n", "#        coalesce(i.stock_update_negative,0) as stock_update_negative,\n", "#        coalesce(i.liquidation,0) as liquidation,\n", "#        coalesce(i.esto,0) as esto,\n", "#        coalesce(i.repackaging,0) as repackaging,\n", "#        coalesce(i.vendor_return,0) as vendor_return,\n", "#        (ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) AS calculated_closing_quantity,\n", "#        abs((ends.opening_quantity + coalesce(i.grns,0) - coalesce(i.sales,0) + coalesce(i.sale_returns,0) - coalesce(i.customer_sales,0) + coalesce(i.customer_returns,0) - coalesce(i.badstock,0) + coalesce(i.stock_update_positive,0) - coalesce(i.stock_update_negative,0) - coalesce(i.liquidation,0) - coalesce(i.esto,0) + coalesce(i.repackaging,0) - coalesce(i.vendor_return,0)) - ends.closing_quantity) AS difference\n", "# FROM\n", "#   (SELECT coalesce(inv_snap.outlet_id, good_inv_snap.outlet_id) AS outlet_id,\n", "#           coalesce(inv_snap.outlet_name, good_inv_snap.outlet_name, '-') AS outlet_name,\n", "#           coalesce(inv_snap.item_id, good_inv_snap.item_id) AS item_id,\n", "#           coalesce(inv_snap.variant_id, good_inv_snap.variant_id) as variant_id,\n", "#           coalesce(inv_snap.name, good_inv_snap.name, '-') AS name,\n", "#           coalesce(inv_snap.brand, good_inv_snap.brand, '-') AS brand,\n", "#           coalesce(inv_snap.manufacturer, good_inv_snap.manufacturer, '-') AS manufacturer,\n", "#           coalesce(inv_snap.state_id, good_inv_snap.state_id) AS state_id,\n", "#           coalesce(inv_snap.state_name, good_inv_snap.state_name, '-') AS state_name,\n", "#           coalesce(inv_snap.opening_quantity,0) + coalesce(good_inv_snap.opening_quantity,0) AS opening_quantity,\n", "#           coalesce(inv_snap.closing_quantity,0) + coalesce(good_inv_snap.closing_quantity,0) AS closing_quantity\n", "#    FROM\n", "#      (SELECT DISTINCT ris.outlet_id,\n", "#                       co.name AS outlet_name,\n", "#                       pp.item_id,\n", "#                       id.name AS name,\n", "#                       pb.name AS brand,\n", "#                       pm.name AS manufacturer,\n", "#                       cl.state_id,\n", "#                       ris.variant_id,\n", "#                       cs.state_name,\n", "#                       ris.opening_quantity,\n", "#                       ris.closing_quantity\n", "#       FROM ris\n", "#       INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(ris.variant_id AS varchar)\n", "#       LEFT JOIN id ON id.item_id = pp.item_id\n", "#       JOIN pb ON pb.id = id.brand_id\n", "#       JOIN pm ON pm.id = pb.manufacturer_id\n", "#       INNER JOIN co ON co.id = ris.outlet_id\n", "#       LEFT JOIN cl ON cl.id = co.tax_location_id\n", "#       LEFT JOIN cs ON cs.id = cl.state_id\n", "#       INNER JOIN comp ON comp.id = co.company_type_id\n", "# ) inv_snap\n", "#    FULL OUTER JOIN\n", "#      (SELECT DISTINCT rgis.outlet_id,\n", "#                       co.name AS outlet_name,\n", "#                       pp.item_id,\n", "#                       id.name AS name,\n", "#                       pb.name AS brand,\n", "#                       pm.name AS manufacturer,\n", "#                       cl.state_id,\n", "#                       cs.state_name,\n", "#                       rgis.variant_id,\n", "#                       rgis.opening_quantity,\n", "#                       rgis.closing_quantity\n", "#       FROM rgis\n", "#       INNER JOIN pp ON cast(pp.variant_id AS varchar) = cast(rgis.variant_id AS varchar)\n", "#       LEFT JOIN id ON id.item_id = pp.item_id\n", "#       JOIN pb ON pb.id = id.brand_id\n", "#       JOIN pm ON pm.id = pb.manufacturer_id\n", "#       INNER JOIN co ON co.id = rgis.outlet_id\n", "#       LEFT JOIN cl ON cl.id = co.tax_location_id\n", "#       LEFT JOIN cs ON cs.id = cl.state_id\n", "#       INNER JOIN comp ON comp.id = co.company_type_id\n", "# ) good_inv_snap ON inv_snap.item_id = good_inv_snap.item_id\n", "#    AND inv_snap.outlet_id = good_inv_snap.outlet_id) ends\n", "# LEFT JOIN ims_logs i ON cast(ends.variant_id AS varchar) = cast(i.variant_id AS varchar)\n", "# AND cast(ends.outlet_id AS int) = cast(i.outlet_id AS int) \"\"\", presto)"]}, {"cell_type": "raw", "metadata": {}, "source": ["inv.shape"]}, {"cell_type": "raw", "metadata": {}, "source": ["to_sheets(inv, Sheet_id, \"Inv(WH) Summary\")\n", "# to_sheets(ds, Sheet_id,'Inv(DS) Summary')"]}, {"cell_type": "raw", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}