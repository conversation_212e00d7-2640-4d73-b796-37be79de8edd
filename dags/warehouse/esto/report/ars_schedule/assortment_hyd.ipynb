{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "import numpy as np\n", "from datetime import datetime, date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Item ID\tItem Name\tL0\tL1\tPtype\tManufacturere\tvendor\n", "max at stores, curr-inv at stores, "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb = \"\"\"select a.facility_id, a.item_id,id.name as item_name, a.IMS_Qty-coalesce(b.blocked_quantity,0) as unblocked_qty from\n", "                (SELECT distinct\n", "                         o.facility_id as facility_id,\n", "                         p.item_id,\n", "                         sum(I.QUANTITY) AS IMS_Qty\n", "                  FROM lake_ims.ims_inventory I\n", "                  INNER JOIN lake_rpc.product_product p ON p.variant_id = I.variant_id\n", "                  INNER JOIN lake_retail.console_outlet o ON o.id = I.outlet_id and o.tax_location_id = 5\n", "                  WHERE I.QUANTITY>0\n", "                    AND I.ACTIVE = 1\n", "                    group by 1,2\n", "                ) a\n", "                left join (SELECT x.facility_id,item_id, SUM(Blocked_Quantity) as blocked_quantity\n", "                FROM (\n", "                select co.facility_id,outlet_id,item_id,date(scheduled_at + interval '5.5 hrs') as delivery_date, \n", "                quantity as Blocked_Quantity\n", "                FROM\n", "                (SELECT id, order_id, outlet as outlet_id, scheduled_at\n", "                FROM lake_ims.ims_order_details\n", "                where status_id  = 1) order_details\n", "                LEFT JOIN lake_ims.ims_order_items as order_items ON order_details.id = order_items.order_details_id\n", "                left join lake_retail.console_outlet co on co.id = outlet_id) x\n", "                GROUP BY 1,2) b on a.facility_id = b.facility_id and a.item_id = b.item_id\n", "                join lake_rpc.item_details id on id.item_id = a.item_id\"\"\"\n", "unb_inv = pd.read_sql_query(unb, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax = \"\"\"select tea.backend_facility_id,tea.backend_facility, tea.frontend_facility_id,tea.frontend_facility, c.item_id,\n", "        c.min_quantity as min_qty\n", "        from lake_ars.item_min_max_quantity c\n", "        left join (SELECT DISTINCT \n", "            a.item_id,\n", "            ro.facility_id AS frontend_facility_id,\n", "            f1.name as frontend_facility,\n", "            r.facility_id AS backend_facility_id,\n", "            f2.name as backend_facility\n", "        FROM lake_rpc.item_outlet_tag_mapping a\n", "        INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "        INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "        left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "        left join lake_crates.facility f2 on r.facility_id = f2.id\n", "        WHERE tag_type_id=8\n", "        AND a.active=1 AND ro.business_type_id in (7)\n", "        AND r.tax_location_id = 5\n", "        AND ro.name not like '%%old%%') tea on tea.frontend_facility_id = c.facility_id and tea.item_id = c.item_id\n", "        join lake_rpc.item_details id on id.item_id = c.item_id\"\"\"\n", "minmax_data = pd.read_sql_query(minmax, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax_data.groupby([\"backend_facility_id\"]).item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          brand,\n", "          manf\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "cat_data = pd.read_sql(query, pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_inv1 = unb_inv.merge(cat_data, on=[\"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_inv1.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_wh = (\n", "    unb_inv[unb_inv.facility_id.isin(minmax_data.backend_facility_id.unique())]\n", "    .reset_index(drop=True)\n", "    .rename(columns={\"unblocked_qty\": \"wh_qty\"})[[\"facility_id\", \"item_id\", \"wh_qty\"]]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_inv1 = unb_inv1[unb_inv1.item_id.isin(unb_wh.item_id.unique())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_wh.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax_data = unb_inv1.merge(\n", "    minmax_data,\n", "    right_on=[\"frontend_facility_id\", \"item_id\"],\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax_data1 = minmax_data.merge(\n", "    unb_wh,\n", "    left_on=[\"backend_facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# max_un = pd.pivot_table(minmax_data1, index=['facility_id','item_id','l0','l1','product_type','manf'],\n", "#                     columns=['rec_facility_id']).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["minmax_data1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un = minmax_data1[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"product_type\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"min_qty\",\n", "        \"unblocked_qty\",\n", "        \"wh_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un[\"unblocked_qty\"] = max_un[\"unblocked_qty\"].fillna(0)\n", "max_un[\"diff\"] = np.where(\n", "    max_un[\"min_qty\"] - max_un[\"unblocked_qty\"] > 0,\n", "    max_un[\"min_qty\"] - max_un[\"unblocked_qty\"],\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un.item_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# adding vendor data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct p.facility_id, p.item_id, p.vendor_id, v.vendor_name\n", "from lake_vms.vms_vendor_facility_alignment p\n", "join lake_vms.vms_vendor v on v.id = p.vendor_id\n", "join lake_retail.console_outlet co on co.facility_id = p.facility_id and co.tax_location_id = 5\n", "where p.active = 1\"\"\"\n", "data_v = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un1 = max_un.merge(\n", "    data_v,\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    left_on=[\"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un2 = max_un1[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"product_type\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"vendor_name\",\n", "        \"min_qty\",\n", "        \"unblocked_qty\",\n", "        \"diff\",\n", "        \"wh_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un2.item_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### adding PO_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct c.facility_id,p.id as po_id, outlet_id, c.name as outlet_name, vendor_id, vendor_name, pi.manufacturer_name,\n", "cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date)  as po_created_date, pi.item_id, pi.units_ordered, p.max_po_item_cycle\n", "from lake_po.purchase_order p\n", "join lake_retail.console_outlet c on c.id = p.outlet_id and c.tax_location_id = 5\n", "join lake_po.purchase_order_items pi on pi.po_id = p.id\n", "join (select c.facility_id, pi.item_id, max(p.created_at) as max_cr\n", "from lake_po.purchase_order p\n", "join lake_retail.console_outlet c on c.id = p.outlet_id and c.tax_location_id = 5\n", "join lake_po.purchase_order_items pi on pi.po_id = p.id\n", "group by 1,2\n", ") j on j.facility_id = c.facility_id and j.item_id = pi.item_id and p.created_at = j.max_cr\n", "where cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date) >= current_date - INTERVAL '21' DAY\n", "\"\"\"\n", "data = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data[\n", "    [\"facility_id\", \"item_id\", \"po_created_date\", \"max_po_item_cycle\", \"units_ordered\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select c.facility_id, p.item_id, max(cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date)) as po_grn_date\n", "from lake_po.po_grn p\n", "join lake_retail.console_outlet c on c.id = p.outlet_id and c.tax_location_id = 5\n", "where cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date) >= current_date - INTERVAL '21' DAY\n", "group by 1,2\n", "\"\"\"\n", "data_grn = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select c.facility_id, p.item_id, max(cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date)) as po_grn_date\n", "from lake_po.po_grn p\n", "join lake_retail.console_outlet c on c.id = p.outlet_id and c.tax_location_id = 5\n", "where cast(from_unixtime(p.created_at/pow(10,6), 'Asia/Kolkata') as date) >= current_date - INTERVAL '21' DAY\n", "group by 1,2\n", "\"\"\"\n", "data_grn = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_grn.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un3 = max_un2.merge(\n", "    data,\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    left_on=[\"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "max_un4 = max_un3.merge(\n", "    data_grn,\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    left_on=[\"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un5 = max_un4.drop(columns=[\"facility_id_x\", \"facility_id_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un5.item_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# active assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_qu = f\"\"\"SELECT DISTINCT f.name AS facility_name,\n", "                ma.facility_id,\n", "                co.business_type_id,\n", "                cl.name as city_name,\n", "                ma.item_id AS \"item_id\",\n", "                rpp.name as item_name,\n", "                rpp.weight_in_gm,\n", "                (CASE\n", "                    WHEN rpp.storage_type = 1 THEN 1\n", "                    WHEN rpp.storage_type = 5 THEN ROUND(rpp.weight_in_gm/1000, 1)\n", "                END) as \"storage_factor\",\n", "                (CASE\n", "                    WHEN rpp.storage_type = 1 THEN 'Regular'\n", "                    WHEN rpp.storage_type = 5 THEN 'Heavy'\n", "                END) as \"Storage Type\",\n", "                (CASE\n", "                    WHEN rpp.outlet_type = 1 THEN 'F&V type'\n", "                    WHEN rpp.outlet_type = 2 THEN 'Grocery type'\n", "                    ELSE 'Not Assigned'\n", "                END) AS \"Item Type\",\n", "                pmas.name AS master_assortment_substate,\n", "                (ma.created_at+interval '5.5 hrs')::date as active_date\n", "FROM lake_rpc.product_facility_master_assortment ma\n", "LEFT JOIN lake_rpc.product_master_assortment_substate pmas ON ma.master_assortment_substate_id = pmas.id\n", "LEFT JOIN lake_po.physical_facility pf ON pf.facility_id = ma.facility_id\n", "LEFT JOIN lake_rpc.product_product rpp ON rpp.item_id = ma.item_id\n", "INNER JOIN lake_retail.console_outlet co on co.facility_id = ma.facility_id and co.tax_location_id = 5\n", "LEFT JOIN lake_retail.console_location cl on cl.id = co.tax_location_id\n", "LEFT JOIN lake_crates.facility f on f.id = co.facility_id\n", "WHERE master_assortment_substate_id = 1 and ma.active = 1 \n", "and rpp.active = 1 and rpp.id IN\n", "       (SELECT max(id) AS id\n", "        FROM lake_rpc.product_product pp\n", "        WHERE pp.active = 1\n", "          AND pp.approved = 1\n", "        GROUP BY item_id)\n", "ORDER BY internal_facility_identifier ASC\n", "\"\"\"\n", "active_assortment_data = pd.read_sql_query(active_qu, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment = (\n", "    active_assortment_data.groupby([\"facility_id\", \"facility_name\"])\n", "    .item_id.nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_ds_assort = active_assortment[\n", "    active_assortment.facility_name.str.contains(\"ES\")\n", "].item_id.mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un5[\"Average ds assortment\"] = int(avg_ds_assort)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un5.item_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# adding tea tagging Ds count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT \n", "            a.item_id,\n", "            ro.facility_id AS frontend_facility_id,\n", "            f1.name as frontend_facility,\n", "            r.facility_id AS backend_facility_id,\n", "            f2.name as backend_facility\n", "        FROM lake_rpc.item_outlet_tag_mapping a\n", "        INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id and ro.device_id <> 47 and ro.active = 1\n", "        INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value and r.device_id <> 47 and r.active = 1\n", "        left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "        left join lake_crates.facility f2 on r.facility_id = f2.id\n", "        WHERE tag_type_id=8\n", "        AND a.active=1 AND ro.business_type_id in (7)\n", "        AND r.tax_location_id = 5 and ro.tax_location_id = 5\n", "        AND ro.name not like '%%old%%'\n", "        and ro.id in (select distinct outlet from lake_ims.ims_order_details i\n", "                     join lake_retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7\n", "                     and co.tax_location_id = 5\n", "                     where i.created_at::date = current_date-1)\n", "        \"\"\"\n", "hyd_tea = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_tags = (\n", "    hyd_tea.groupby([\"item_id\"])[\"frontend_facility_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"frontend_facility_id\": \"tea_tagged_frontends\"})\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# last 7 days cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["auery = \"\"\"select ii.item_id, round(sum(ii.reserved_quantity)/7.0,1) as consump\n", "            from lake_ims.ims_sto_item  ii\n", "            join lake_ims.ims_sto_details i on i.sto_id = ii.sto_id and i.sto_state not in (3)\n", "            join lake_retail.console_outlet co on co.id = i.outlet_id and co.facility_id in (43)\n", "            where i.created_at::date between current_date-7 and current_date-1\n", "            group by 1\"\"\"\n", "cpd = pd.read_sql(auery, redshift)\n", "cpd.head(2)\n", "\n", "max_un6 = max_un5.merge(cpd, on=[\"item_id\"], how=\"left\")\n", "\n", "max_un6[\"consump\"] = max_un6.consump.fillna(0)\n", "\n", "max_un7 = max_un6.merge(tea_tags, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un7.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(max_un7, \"1fo95ik8CCtj75YjdtcADD_Vjpie3N6rSswvIRfnEjgs\", \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_un7.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}