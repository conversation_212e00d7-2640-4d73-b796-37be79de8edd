{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select  name,\n", "        calendar,\n", "        1\n", "from lake_ars.job_schedule\n", "where is_active = 1\n", "and json_extract_scalar(simulation_config, '$.simulation_params.run') = 'ars_lite'\n", "\"\"\"\n", "data = pd.read_sql_query(q, con=presto)\n", "data1 = data.values.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(data1)):\n", "    print(i)\n", "    data1[i][1] = [i[\"facility_ids\"] for i in data.calendar.apply(json.loads).values[i]]\n", "    data1[i][2] = [\n", "        i[\"time_of_day_ist\"] for i in data.calendar.apply(json.loads).values[i]\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data2 = []\n", "for i in range(0, len(data1)):\n", "    for j in range(0, len(data1[i][2])):\n", "        for k in range(0, len(data1[i][1][j])):\n", "            print([data1[i][0], data1[i][1][j][k], data1[i][2][j]])\n", "            data2.append([data1[i][0], data1[i][1][j][k], data1[i][2][j]])\n", "data2\n", "df1 = pd.DataFrame(data2)\n", "df1.columns = [\"name\", \"backend_facility\", \"indent_time\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select backend_facility_id, slot_config from lake_ars.auto_sto_slot_config\"\"\"\n", "data = pd.read_sql_query(query, presto)\n", "\n", "data[\"slot_config\"] = data[\"slot_config\"].apply(lambda x: dict(eval(x)))\n", "\n", "df = data[\"slot_config\"].apply(pd.Series)\n", "\n", "mergedDf = data[[\"backend_facility_id\"]].merge(df, left_index=True, right_index=True)\n", "mergedDf = mergedDf.fillna(0)\n", "dat = pd.DataFrame(columns={\"backend_facility\", \"indent_time\", \"frontend_outlet\"})\n", "for i in mergedDf.index:\n", "    back = mergedDf[mergedDf.index == i].backend_facility_id\n", "    df = mergedDf[mergedDf.index == i]\n", "    for column in df.columns[1:]:\n", "        if df.loc[df.backend_facility_id == back, column][i] != 0:\n", "            for j in df.loc[df.backend_facility_id == back, column][i]:\n", "                dat1 = pd.DataFrame(\n", "                    {\n", "                        \"backend_facility\": [int(back[i])],\n", "                        \"indent_time\": [str(column)],\n", "                        \"frontend_outlet\": [int(j)],\n", "                    }\n", "                )\n", "                dat = pd.concat([dat, dat1], sort=True)\n", "final = dat.reset_index(drop=True)\n", "query = (\n", "    \"\"\"select id as frontend_outlet, name as ds_name from lake_retail.console_outlet\"\"\"\n", ")\n", "outlets = pd.read_sql_query(query, presto)\n", "query = \"\"\"select id as backend_facility, name as backend_facility_name from lake_crates.facility\"\"\"\n", "facility = pd.read_sql_query(query, presto)\n", "final = final.merge(outlets, on=[\"frontend_outlet\"])\n", "final = final.merge(facility, on=[\"backend_facility\"])\n", "final = final[\n", "    [\n", "        \"backend_facility\",\n", "        \"backend_facility_name\",\n", "        \"frontend_outlet\",\n", "        \"ds_name\",\n", "        \"indent_time\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final.merge(df1, on=[\"backend_facility\", \"indent_time\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final1.sort_values(\n", "    by=[\"backend_facility\", \"frontend_outlet\"], ascending=[True, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final1, \"1CInLRO0E1fBlyiTO3Jg0B0mUhn4PstzLs_vAHuedKtk\", \"ARS_TIMING\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}