{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import numpy as np\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All sto created"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT date(sto.created_at + interval '5.5 hour') AS sto_created_at,\n", "       date(sto.delivery_date + interval '5.5 hour') AS delivery_date,\n", "       case when (current_date + interval '5.5 hour')>date(sto.delivery_date + interval '5.5 hour') then 1\n", "       else 0 end as \"is_sto_expired\",\n", "       sto.sto_id,\n", "       f.id as facility_id,\n", "       f.name as facility_name,\n", "       sto.outlet_id AS sender_outlet_id,\n", "         o.name AS sender_outlet_name,\n", "       sto.merchant_outlet_id AS receiver_outlet_id,\n", "       o1.name AS receiver_outlet_name,\n", "       sto.sto_state AS sto_state_id,\n", "       CASE\n", "           WHEN sto.sto_state = 1 THEN 'Created'\n", "           WHEN sto.sto_state = 2 THEN 'Billed'\n", "           WHEN sto.sto_state = 5 THEN 'Partial-Billed'\n", "           WHEN sto.sto_state = 3 THEN 'Expired'\n", "           WHEN sto.sto_state = 4 THEN 'Inward'\n", "           ELSE 'New State'\n", "       END AS sto_state,\n", "       item_id,\n", "       i.expected_quantity  as sto_qty\n", "FROM lake_ims.ims_sto_details sto\n", "inner join lake_ims.ims_sto_item i on i.sto_id=sto.sto_id\n", "INNER JOIN lake_retail.console_outlet o ON sto.outlet_id = o.id\n", "INNER JOIN lake_retail.console_outlet o1 ON sto.merchant_outlet_id = o1.id\n", "inner join lake_crates.facility f on f.id=o.facility_id\n", "WHERE date(sto.created_at + interval '5.5 hour') between current_date - interval '30 days' and current_date\n", "\"\"\"\n", "\n", "sto = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### GRN Sto"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH sto_billed_grn_raw AS\n", "  (SELECT DISTINCT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "                   pi.grofers_order_id AS sto_id,\n", "                   sto.outlet_id,\n", "                   sto.merchant_outlet_id,\n", "                   pi.invoice_id,\n", "                   pi.transfer_state,\n", "                   r.item_id AS Item_ID,\n", "                   pipd.variant_id AS variant_id,\n", "                   r.name AS Product,\n", "                   (CASE\n", "                        WHEN r.outlet_type = 1 THEN 'F&V type'\n", "                        WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "                        ELSE 'Not Assigned'\n", "                    END) AS Item_Type,\n", "                   CASE\n", "                       WHEN i.inventory_update_type_id IN (1,\n", "                                                           28) THEN i.\"delta\"\n", "                       ELSE 0\n", "                   END AS GRN_Quantity,\n", "                   (i.pos_timestamp + interval '5.5 hour') grn_timestamp, \n", " (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", " pipd.quantity AS billed_qty,\n", " pipd.selling_price AS billing_price,\n", " lp.landing_price AS hot_landing_price\n", "   FROM lake_pos.view_pos_invoice pi\n", "   LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "   LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "   LEFT JOIN\n", "     ( SELECT *\n", "      FROM lake_ims.ims_inventory_log\n", "      WHERE (pos_timestamp) >'2020-08-10' \n", " ) i ON pi.invoice_id=i.merchant_invoice_id\n", "   AND pipd.variant_id::varchar=i.variant_id::varchar\n", "   LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id\n", "   AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "   LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "   WHERE pi.invoice_type_id IN (5,\n", "                                14,\n", "                                16)\n", "     AND (pi.pos_timestamp) >'2020-07-15'\n", "     AND date(sto.created_at + interval '5.5 hour') BETWEEN CURRENT_DATE - interval '30 days' AND CURRENT_DATE ),\n", "     sto_billed_grn AS\n", "  (SELECT DISTINCT sto_created_at,\n", "                   outlet_id,\n", "                   merchant_outlet_id,\n", "                   variant_id,\n", "                   sbg.invoice_id,\n", "                   sbg.transfer_state,\n", "                   sbg.product,\n", "                   sbg.item_id,\n", "                   sbg.sto_id,\n", "                   (sbg.billed_qty) billed_qty,\n", "                   max(sbg.invoice_creation) invoice_creation,\n", "                   sum(sbg.grn_quantity) grn_quantity,\n", "                   max(sbg.grn_timestamp) grn_timestamp,\n", "                   sum(sbg.billing_price*billed_qty) billing_price,\n", "                   (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "                   (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "                   sbg.billing_price AS billing_unit_price,\n", "                   sbg.hot_landing_price\n", "   FROM sto_billed_grn_raw sbg\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            17,\n", "            18),\n", "     return_b2b AS\n", "  (SELECT tab1.original_invoice_id,\n", "          tab1.item_id,\n", "          tab1.variant_id,\n", "          sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "          sum(tab2.b2b_customer_return) b2breturn_good,\n", "          sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "          sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "          sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "          sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "          sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "   FROM\n", "     (SELECT pi.invoice_id ret_invoice_id,\n", "             pi.original_invoice_id,\n", "             pp.item_id,\n", "             pipd.variant_id,\n", "             pipd.quantity b2b_return_qty\n", "      FROM lake_pos.view_pos_invoice pi\n", "      INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "      LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "      LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "      WHERE pi.invoice_type_id IN (8,\n", "                                   15)\n", "        AND pi2.invoice_type_id IN (5,\n", "                                    14,\n", "                                    16)\n", "        AND (pi.pos_timestamp) > '2020-08-15' )tab1\n", "   LEFT JOIN\n", "     (SELECT invoice_id AS ret_invoice_id,\n", "             item_id,\n", "             variant_id,\n", "             sum(CASE\n", "                     WHEN update_type_id=23 THEN ret_qty\n", "                 END) AS b2b_customer_return,\n", "             sum(CASE\n", "                     WHEN update_type_id=24 THEN ret_qty\n", "                 END) AS b2b_customer_return_expired,\n", "             sum(CASE\n", "                     WHEN update_type_id=25 THEN ret_qty\n", "                 END) AS b2b_customer_return_damaged,\n", "             sum(CASE\n", "                     WHEN update_type_id=66 THEN ret_qty\n", "                 END) AS b2b_customer_return_near_expiry,\n", "             sum(CASE\n", "                     WHEN update_type_id=119 THEN ret_qty\n", "                 END) AS lost_in_transit_positive,\n", "             sum(CASE\n", "                     WHEN update_type_id=120 THEN ret_qty\n", "                 END) AS lost_in_transit_negative,\n", "             sum(ret_qty) AS tot_ret_qty\n", "      FROM\n", "        (SELECT iil.invoice_id,\n", "                iil.merchant_invoice_id,\n", "                iil.\"delta\" AS ret_qty,\n", "                iut.id AS update_type_id,\n", "                iut.name AS update_type,\n", "                pp.item_id,\n", "                iil.variant_id\n", "         FROM lake_ims.ims_inventory_log iil\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "         LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "         WHERE iil.pos_timestamp>'2020-08-15' ) TEMP\n", "      GROUP BY 1,\n", "               2,\n", "               3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "   AND tab1.item_id=tab2.item_id\n", "   AND tab1.variant_id = tab2.variant_id\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     FINAL AS\n", "  ( SELECT sbg.sto_id,\n", "           sbg.outlet_id,\n", "           ozm1.outlet_name AS sender_outlet,\n", "           sbg.merchant_outlet_id,\n", "           ozm2.outlet_name AS rec_outlet,\n", "           sbg.sto_created_at,\n", "           sbg.item_id,\n", "           sbg.billing_unit_price,\n", "           hot_landing_price,\n", "           sbg.variant_id,\n", "           sbg.invoice_id,\n", "           sbg.transfer_state,\n", "           sbg.product,\n", "           sbg.billed_qty,\n", "           sbg.invoice_creation,\n", "           sbg.grn_quantity,\n", "           sbg.grn_timestamp,\n", "           sbg.billing_price,\n", "           sbg.diff_qty diff_qty_billed_grn,\n", "           sbg.diff_amt diff_amt_billed_grn,\n", "           COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", "           COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", "           COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", "           COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", "           COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", "           COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", "           COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "   FROM sto_billed_grn sbg\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "   LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "   LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id\n", "   AND b2b.item_id=sbg.item_id\n", "   AND b2b.variant_id = sbg.variant_id ) \n", "\n", "SELECT DISTINCT sto_id,\n", "                outlet_id as sender_outlet_id,\n", "                merchant_outlet_id AS receiver_outlet_id,\n", "                sto_created_at,\n", "                grn_timestamp,\n", "                item_id,\n", "                variant_id, \n", " invoice_id,\n", " invoice_creation,\n", " CASE\n", "     WHEN transfer_state = 1 THEN 'Raised'\n", "     WHEN transfer_state = 2 THEN 'GRN Complete'\n", "     WHEN transfer_state = 3 THEN 'GRN Partial'\n", "     WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "     ELSE 'NA'\n", " END AS Invoice_State,\n", " billed_qty,\n", " grn_quantity,\n", " tot_returned\n", " \n", "FROM FINAL f\n", "\n", "\"\"\"\n", "\n", "sto_billed = pd.read_sql(query, redshift)\n", "sto_billed.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_billed[\n", "    [\"sto_id\", \"sender_outlet_id\", \"receiver_outlet_id\", \"item_id\"]\n", "] = sto_billed[[\"sto_id\", \"sender_outlet_id\", \"receiver_outlet_id\", \"item_id\"]].astype(\n", "    \"float\"\n", ")\n", "\n", "sto_billed[\n", "    [\"sto_id\", \"sender_outlet_id\", \"receiver_outlet_id\", \"item_id\"]\n", "] = sto_billed[[\"sto_id\", \"sender_outlet_id\", \"receiver_outlet_id\", \"item_id\"]].astype(\n", "    \"int\"\n", ")\n", "stov_final = sto.merge(\n", "    sto_billed,\n", "    on=[\n", "        \"sto_created_at\",\n", "        \"sto_id\",\n", "        \"sender_outlet_id\",\n", "        \"receiver_outlet_id\",\n", "        \"item_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "stov_final[[\"billed_qty\", \"grn_quantity\", \"tot_returned\"]] = stov_final[\n", "    [\"billed_qty\", \"grn_quantity\", \"tot_returned\"]\n", "].fillna(0)\n", "stov_final = stov_final.fillna(\"1999-01-01 12:00:00\")\n", "stov_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_final = (\n", "    stov_final.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"sto_id\",\n", "            \"sto_state\",\n", "            \"sender_outlet_id\",\n", "            \"sender_outlet_name\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_outlet_name\",\n", "            \"sto_created_at\",\n", "            \"delivery_date\",\n", "            \"is_sto_expired\",\n", "            \"item_id\",\n", "            \"invoice_id\",\n", "            \"invoice_state\",\n", "            \"sto_qty\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"invoice_creation\": \"max\",\n", "            \"billed_qty\": \"sum\",\n", "            \"grn_quantity\": \"sum\",\n", "            \"tot_returned\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "sto_final[\"invoice_creation\"] = sto_final[\"invoice_creation\"].astype(\"str\")\n", "sto_final.replace(\"1999-01-01 12:00:00\", \"-\", inplace=True)\n", "sto_final[\"invoice_creation\"] = np.where(\n", "    sto_final[\"invoice_creation\"] == \"1999-01-01 12:00:00\",\n", "    \"-\",\n", "    sto_final[\"invoice_creation\"],\n", ")\n", "sto_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sto_open(\n", "    invoice_creation, is_sto_expired, sto_qty, billed_qty, grn_quantity, tot_returned\n", "):\n", "\n", "    if invoice_creation == \"-\":\n", "        if is_sto_expired == 1:\n", "            return 0\n", "        else:\n", "            return sto_qty\n", "\n", "    elif invoice_creation != \"-\":\n", "\n", "        invoice_creation = pd.to_datetime(invoice_creation)\n", "\n", "        if (now - invoice_creation).days > 15:\n", "            return 0\n", "        else:\n", "            return billed_qty - grn_quantity - tot_returned"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def open_sto(x):\n", "#     if x[\"invoice_creation\"] == \"-\":\n", "#         if x[\"is_sto_expired\"] == 1:\n", "#             return 0\n", "#         else:\n", "#             return x[\"sto_qty\"]\n", "\n", "#     elif x[\"invoice_creation\"] != \"-\":\n", "\n", "#         x[\"invoice_creation\"] = pd.to_datetime(x[\"invoice_creation\"])\n", "\n", "#         if (now - x[\"invoice_creation\"]).days > 15:\n", "#             return 0\n", "#         else:\n", "#             return x[\"billed_qty\"] - x[\"grn_quantity\"] - x[\"tot_returned\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st_open = np.vectorize(sto_open)\n", "sto_final[\"open_sto_qty\"] = st_open(\n", "    sto_final[\"invoice_creation\"],\n", "    sto_final[\"is_sto_expired\"],\n", "    sto_final[\"sto_qty\"],\n", "    sto_final[\"billed_qty\"],\n", "    sto_final[\"grn_quantity\"],\n", "    sto_final[\"tot_returned\"],\n", ")\n", "\n", "sto_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sto_final[\"open_sto_qty\"] = sto_final.apply(open_sto, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_final[\"open_sto_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_final2 = sto_final[sto_final[\"open_sto_qty\"] > 0]\n", "\n", "oo = (\n", "    sto_final2.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"sto_id\",\n", "            \"sender_outlet_id\",\n", "            \"sender_outlet_name\",\n", "            \"receiver_outlet_id\",\n", "            \"receiver_outlet_name\",\n", "            \"sto_created_at\",\n", "            \"item_id\",\n", "            \"invoice_id\",\n", "            \"invoice_creation\",\n", "            \"invoice_state\",\n", "        ]\n", "    )[\"sto_qty\", \"billed_qty\", \"grn_quantity\", \"tot_returned\", \"open_sto_qty\"]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "oo.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "time = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "subject_time = time.strftime(\"%m/%d/%Y %H:%M:%S\")\n", "filename_time = time.strftime(\"%m_%d_%Y_%H_%M_%S\")\n", "\n", "filename = \"open_sto_items_\" + filename_time + \".csv\"\n", "\n", "pb.to_csv(oo, filename)\n", "\n", "## Sending data to s3\n", "def tos3(file):\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    file_path = \"povms/stock_transfers/reports/{csv_file_path}\".format(\n", "        csv_file_path=file\n", "    )\n", "    bucket_name = \"grofers-retail-test\"\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    import boto3\n", "\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    bucket_obj.upload_file(\"/tmp/\" + file, file_path)\n", "    s3_client = session.client(\"s3\")\n", "    presigned_url = s3_client.generate_presigned_url(\n", "        \"get_object\",\n", "        Params={\"Bucket\": bucket_name, \"Key\": file_path},\n", "        HttpMethod=\"GET\",\n", "        ExpiresIn=86400,\n", "    )\n", "    return presigned_url\n", "\n", "\n", "download_link = tos3(filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Sending data on mail\n", "sheet_id = \"1ajlNzQih8Jw4pTB562TUxaFe5WsFqAzwbVDijnfu9f4\"\n", "from_email = \"<EMAIL>\"\n", "email_list = pb.from_sheets(sheet_id, \"Email\")\n", "to_email = list(email_list[\"Email\"])\n", "# to_email=['<EMAIL>']\n", "html_content = f\"<p>Hi, The items with open STO can be downloaded from this link:<a href={download_link}>open STOs</a><p>\"\n", "subject = \"Items with open STOs at \" + subject_time\n", "pb.send_email(from_email, to_email, subject, html_content=html_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}