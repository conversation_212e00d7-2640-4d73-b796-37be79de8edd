{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "import pandas as pd\n", "import pencilbox as pb\n", "import requests\n", "import re\n", "import time\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### API"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).replace(\n", "    hour=0, minute=0, second=0, microsecond=0\n", ")\n", "now = datetime.now()\n", "yesterday, now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = int(str(int(yesterday.timestamp())) + \"000\")\n", "end = int(str(int((now.timestamp()))) + \"000\")\n", "print(start, end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = f\"https://api.fleetx.io/api/v1/tp/grofers/milk-run?from={start}&to={end}\"\n", "payload = {}\n", "headers = {\n", "    \"accept\": \"*/*\",\n", "    \"Authorization\": \"Bearer ed21c248-15a7-43d9-a296-d0e7c77a6c35\",\n", "}\n", "response = requests.request(\"GET\", url, headers=headers, data=payload).json()\n", "response[-4:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols = set()\n", "for v in response:\n", "    for col in v:\n", "        cols.add(col)\n", "        if col == \"destinationList\":\n", "            if v[col]:\n", "                for l in v[col][0]:\n", "                    cols.add(l)\n", "cols"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# response = [{\n", "#     'vehicleNumber': 'DL01LW1569',\n", "#     'startDate': '05-Dec-2021 00:10:00',\n", "#     'source' : 'FC1(1)',\n", "#     'loadingCompletedAtSource': '05-Dec-2021 08:52:45',\n", "#     'dispatchFromSource': '05-Dec-2021 08:56:03',\n", "#     'destinationList': [{\n", "#         'destinationName': 'DS1(101)',\n", "#         'arrival': '05-Dec-2021 16:58:05',\n", "#         'unloadingStarted': '05-Dec-2021 17:00:00',\n", "#         'unloadingCompleted': '05-Dec-2021 18:00:00',\n", "#         'dispatch': '05-Dec-2021 17:00:34',\n", "#         }, {\n", "#         'destinationName': 'DS2(102)',\n", "#         'arrival': '05-Dec-2021 16:58:05',\n", "#         'unloadingStarted': '05-Dec-2021 17:00:00',\n", "#         'unloadingCompleted': '05-Dec-2021 18:00:00',\n", "#         'dispatch': '05-Dec-2021 17:00:34',\n", "#         }, {\n", "#         'destinationName': 'DS3(113)',\n", "#         'arrival': '05-Dec-2021 16:58:05',\n", "#         'unloadingStarted': '05-Dec-2021 17:00:00',\n", "#         'unloadingCompleted': '05-Dec-2021 18:00:00',\n", "#         'dispatch': '05-Dec-2021 17:00:34',\n", "#         }],\n", "#     }]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet_cols = [\n", "    \"destinationName1\",\n", "    \"destinationName2\",\n", "    \"destinationName3\",\n", "    \"source\",\n", "    \"vehicleNumber\",\n", "    \"lockName\",\n", "    \"source_facility_id\",\n", "    \"destinationName1_facility_id\",\n", "    \"destinationName2_facility_id\",\n", "    \"destinationName3_facility_id\",\n", "    \"startDate\",\n", "    \"loadingCompletedAtSource\",\n", "    \"dispatchFromSource\",\n", "    \"arrival1\",\n", "    \"unloadingStarted1\",\n", "    \"unloadingCompleted1\",\n", "    \"dispatch1\",\n", "    \"arrival2\",\n", "    \"unloadingStarted2\",\n", "    \"unloadingCompleted2\",\n", "    \"dispatch2\",\n", "    \"arrival3\",\n", "    \"unloadingStarted3\",\n", "    \"unloadingCompleted3\",\n", "    \"dispatch3\",\n", "    \"returnArrivalAtSource\",\n", "    \"lastUnlockAtSource\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet = pd.DataFrame(columns=fleet_cols)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# regex to match text in last paren\n", "pattern = r\"(?s:.*)\\((.*?)\\)\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(response)):\n", "    #     print(response[i])\n", "    for k in response[i]:\n", "        #         print(k)\n", "        if k == \"destinationList\":\n", "            #             print(response[i][k])\n", "            for num in range(len(response[i][k])):\n", "                #                 print(response[i][k][num])\n", "                destination = response[i][k][num]\n", "                for col in destination:\n", "                    text = destination[col]\n", "                    match = re.search(pattern, text)\n", "                    if match:\n", "                        facility_id = match.group(1)\n", "                        fleet.loc[i, col + str(num + 1) + \"_facility_id\"] = facility_id\n", "                    #                         print(text.split('(')[0].rstrip(), facility_id)\n", "                    fleet.loc[i, col + str(num + 1)] = text.split(\"(\")[0].rstrip()\n", "        else:\n", "            name_match = re.search(pattern, response[i][k])\n", "            if name_match:\n", "                facility_id = name_match.group(1)\n", "                fleet.loc[i, k + \"_facility_id\"] = facility_id\n", "                fleet.loc[i, k] = response[i][k].split(\"(\")[0].rstrip()\n", "            #                 print(response[i][k].split('(')[0].rstrip(), facility_id)\n", "            else:\n", "                #                 print(k, response[i][k])\n", "                fleet.loc[i, k] = response[i][k]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet[fleet.columns[6:10]] = fleet[fleet.columns[6:10]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet[fleet.columns[10:]] = fleet[fleet.columns[10:]].apply(\n", "    pd.to_datetime, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet = fleet[\n", "    [\n", "        \"vehicleNumber\",\n", "        \"lockName\",\n", "        \"startDate\",\n", "        \"source_facility_id\",\n", "        \"source\",\n", "        \"loadingCompletedAtSource\",\n", "        \"dispatchFromSource\",\n", "        \"destinationName1_facility_id\",\n", "        \"destinationName1\",\n", "        \"arrival1\",\n", "        \"unloadingStarted1\",\n", "        \"unloadingCompleted1\",\n", "        \"dispatch1\",\n", "        \"destinationName2_facility_id\",\n", "        \"destinationName2\",\n", "        \"arrival2\",\n", "        \"unloadingStarted2\",\n", "        \"unloadingCompleted2\",\n", "        \"dispatch2\",\n", "        \"destinationName3_facility_id\",\n", "        \"destinationName3\",\n", "        \"arrival3\",\n", "        \"unloadingStarted3\",\n", "        \"unloadingCompleted3\",\n", "        \"dispatch3\",\n", "        \"returnArrivalAtSource\",\n", "        \"lastUnlockAtSource\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fleet.source_facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet[\"etl_timestamp_utc\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_cols = [\n", "    \"vehicle_number\",\n", "    \"lockName\",\n", "    \"start_date\",\n", "    \"source_facility_id\",\n", "    \"source\",\n", "    \"loading_completed_at_source\",\n", "    \"dispatch_from_source\",\n", "    \"destination_name1_facility_id\",\n", "    \"destination_name1\",\n", "    \"arrival1\",\n", "    \"unloading_started1\",\n", "    \"unloading_completed1\",\n", "    \"dispatch1\",\n", "    \"destination_name2_facility_id\",\n", "    \"destination_name2\",\n", "    \"arrival2\",\n", "    \"unloading_started2\",\n", "    \"unloading_completed2\",\n", "    \"dispatch2\",\n", "    \"destination_name3_facility_id\",\n", "    \"destination_name3\",\n", "    \"arrival3\",\n", "    \"unloading_started3\",\n", "    \"unloading_completed3\",\n", "    \"dispatch3\",\n", "    \"return_arrival_at_source\",\n", "    \"last_unlock_at_source\",\n", "    \"etl_timestamp_utc\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet.columns = new_cols"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"vehicle_number\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"vehicle number\",\n", "    },\n", "    {\n", "        \"name\": \"lock<PERSON><PERSON>\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"lock number\",\n", "    },\n", "    {\n", "        \"name\": \"start_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"start date of the dispatch\",\n", "    },\n", "    {\n", "        \"name\": \"source_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"source facility id\",\n", "    },\n", "    {\n", "        \"name\": \"source\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"source facility name\",\n", "    },\n", "    {\n", "        \"name\": \"loading_completed_at_source\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"vehicle loading completed at the source timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch_from_source\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"vehicle dispatch at the source timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name1_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"first destination facility id\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name1\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"first destination name from the source\",\n", "    },\n", "    {\n", "        \"name\": \"arrival1\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"arrival time at the first destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_started1\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading started at first destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_completed1\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading completed at first destination\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch1\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"dispatch time from first destination\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name2_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"second destination facility id\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name2\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"second destination name from the source\",\n", "    },\n", "    {\n", "        \"name\": \"arrival2\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"arrival time at the second destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_started2\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading started at second destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_completed2\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading completed at second destination\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch2\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"dispatch time from second destination\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name3_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"third destination facility id\",\n", "    },\n", "    {\n", "        \"name\": \"destination_name3\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"third destination name from the source\",\n", "    },\n", "    {\n", "        \"name\": \"arrival3\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"arrival time at the third destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_started3\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading started at third destination\",\n", "    },\n", "    {\n", "        \"name\": \"unloading_completed3\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"unloading completed at third destination\",\n", "    },\n", "    {\n", "        \"name\": \"dispatch3\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"dispatch time from third destination\",\n", "    },\n", "    {\n", "        \"name\": \"return_arrival_at_source\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"return arrival time at source timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"last_unlock_at_source\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"last unlock at source timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp_utc\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"timestamp at which row entry is made\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fleet.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"fleet_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"vehicle_number\", \"start_date\"],\n", "    \"sortkey\": \"start_date\",\n", "    \"incremental_key\": \"etl_timestamp_utc\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores data from fleetx api\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(fleet, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "\n", "now = datetime.now(pytz.timezone(\"Asia/Calcutta\"))\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = date.today()\n", "yes = date.today() - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_budhpur = pd.DataFrame(columns=fleet.columns.str.lower())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_budhpur"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if now.hour == 23:\n", "    data_budhpur = pd.read_sql_query(\n", "        f\"\"\"select * from metrics.fleet_details\n", "where source_facility_id = 366 and start_date between '{str(date)} 10:00:00' and  '{str(date)} 23:00:00'\"\"\",\n", "        redshift,\n", "    )\n", "    a = str(date) + \" 10:00 AM\"\n", "    b = str(date) + \" 11:00 PM\"\n", "elif now.hour == 10:\n", "    data_budhpur = pd.read_sql_query(\n", "        f\"\"\"select * from metrics.fleet_details\n", "where source_facility_id = 366 and start_date between '{str(yes)} 23:00:00' and  '{str(date)} 10:00:00'\"\"\",\n", "        redshift,\n", "    )\n", "    a = str(yes) + \" 11:00 PM\"\n", "    b = str(date) + \" 10:00 AM\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_budhpur = data_budhpur[\n", "    [\n", "        \"vehicle_number\",\n", "        \"lockname\",\n", "        \"start_date\",\n", "        \"source_facility_id\",\n", "        \"source\",\n", "        \"loading_completed_at_source\",\n", "        \"dispatch_from_source\",\n", "        \"destination_name1_facility_id\",\n", "        \"destination_name1\",\n", "        \"arrival1\",\n", "        \"unloading_started1\",\n", "        \"unloading_completed1\",\n", "        \"dispatch1\",\n", "        \"destination_name2_facility_id\",\n", "        \"destination_name2\",\n", "        \"arrival2\",\n", "        \"unloading_started2\",\n", "        \"unloading_completed2\",\n", "        \"dispatch2\",\n", "        \"return_arrival_at_source\",\n", "        \"last_unlock_at_source\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data_budhpur.shape[0] != 0:\n", "    count_html = data_budhpur.to_html(index=False)\n", "    html3 = count_html.replace(\"<tr>\", '<tr align=\"center\">').replace(\n", "        '<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">'\n", "    )\n", "    data_budhpur.to_csv(\"fleetx_report_\" + str(date) + \".csv\", index=False)\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    html_content = f\"\"\"Hi, <br> <br> Please find below the Fleetx Milk Run for Budhpur CPC between {a} and {b} <br> <br>\n", "        {html3} \n", "        <br>\n", "        <PERSON><PERSON>,\n", "        <br>\n", "        Data Warehouse Team\"\"\"\n", "    subject = f\"Fleetx Milk Run Report For Budhpur\"\n", "    files = [\"fleetx_report_\" + str(date) + \".csv\"]\n", "    pb.send_email(from_email, to_email, subject, html_content, files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}