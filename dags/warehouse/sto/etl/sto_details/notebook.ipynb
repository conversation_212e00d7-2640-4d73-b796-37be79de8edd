{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_process_tracker_query = \"\"\" SELECT sto_base.*, \n", "sto_items.name as item_name,\n", "sto_items.mrp,\n", "sto_items.description\n", "\n", "       FROM\n", "(SELECT sto.*,\n", "       i.item_id,\n", "       i.expected_quantity,\n", "       i.reserved_quantity,\n", "       i.billed_quantity,\n", "       i.inward_quantity,\n", "       i.released_reserved_quantity,\n", "       i.released_billed_quantity,\n", "      convert_tz(i.updated_at,'+00:00','+05:30')  as sto_item_updated_at\n", "            \n", "FROM\n", "(Select sto1.*,\n", "                 max(case when log.sto_state=1 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Created_at,\n", "                 max(case when log.sto_state=2 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Billed_at,\n", "                    max(case when log.sto_state=3 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Expired_at,\n", "                    max(case when log.sto_state=4 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Inward_at\n", " FROM\n", "(Select sto.sto_id,\n", "      sto.outlet_id as sender_outlet_id,\n", "      o.name as sender_outlet_name,\n", "      sto.merchant_outlet_id as receiving_outlet_id,\n", "      o1.name as receiver_outlet_name,\n", "       convert_tz(sto.delivery_date,'+00:00','+05:30') as delivery_date,\n", "       convert_tz(sto.created_at,'+00:00','+05:30') as created_date,\n", "       convert_tz(sto.updated_at,'+00:00','+05:30') as sto_status_updated_at,\n", "      sto.sto_type as sto_type_id,\n", "\n", "      case when sto.sto_type = 1 then 'Manual'\n", "           when sto.sto_type = 2 then 'Automated' end as sto_type,\n", "\n", "      case when sto.sto_state = 1 then 'Created'\n", "           when sto.sto_state = 2 then 'Billed'\n", "            when sto.sto_state = 3 then 'Expired'\n", "            when sto.sto_state = 4 then 'Inward' end as sto_state,    \n", "            p_inv.invoice_id,\n", "\n", "            convert_tz(p_inv.pos_timestamp,'+00:00','+05:30') as STO_Invoice_created_at ,\n", "            p_inv.actual_sales,\n", "            \n", "            \n", "       (CASE\n", "            WHEN transfer_state = 1 THEN 'Raised'\n", "            WHEN transfer_state = 2 THEN 'GRN Complete'\n", "            WHEN transfer_state = 3 THEN 'GRN Partial'\n", "            WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "            ELSE 'NA'\n", "        END) AS \"Invoice_State\",\n", "\n", " \n", "   max(convert_tz(inward.created_at,'+00:00','+05:30')) as grn_created_at,\n", "   \n", "   sum(inward.net_amount) as inward_amount\n", "\n", "            \n", "           \n", "               \n", "\n", "from ims.ims_sto_details sto\n", "inner join retail.console_outlet o on sto.outlet_id = o.id\n", "inner join retail.console_outlet o1 on sto.merchant_outlet_id = o1.id\n", "\n", "left join pos.pos_invoice p_inv on sto.sto_id = p_inv.grofers_order_id and invoice_type_id = 5\n", "\n", "left join ims.ims_inward_invoice inward on p_inv.invoice_id = inward.vendor_invoice_id and inward.source_type = 2 \n", "\n", " where date(sto.delivery_date) >= adddate(CURDATE() , interval -10 day) \n", " -- where sto.created_at between '2019-07-01 18:30:00' and '2019-07-10 18:29:59'\n", "\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", "\n", ") sto1\n", "inner join ims.ims_sto_state_log log on sto1.sto_id = log.sto_id\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", ")sto\n", "inner join ims.ims_sto_item i on sto.sto_id = i.sto_id\n", ") sto_base\n", "inner join po.sto_items on sto_base.sto_id = sto_items.sto_id and sto_base.item_id = sto_items.item_id\n", "order by 1\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_process_tracker = pandas.read_sql_query(\n", "    sql=sto_process_tracker_query, con=retail_db\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_process_tracker.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_process_tracker[\"Max_updated_at\"] = sto_process_tracker.apply(\n", "    lambda x: max(\n", "        x[\"sto_status_updated_at\"],\n", "        x[\"sto_item_updated_at\"],\n", "        x[\"grn_created_at\"],\n", "        x[\"STO_Invoice_created_at\"],\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_process_tracker.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"sto_id\", \"type\": \"int\"},\n", "    {\"name\": \"sender_outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"sender_outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"receiving_outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"receiver_outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"delivery_date\", \"type\": \"datetime\"},\n", "    {\"name\": \"created_date\", \"type\": \"datetime\"},\n", "    {\"name\": \"sto_status_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"sto_type_id\", \"type\": \"int\"},\n", "    {\"name\": \"sto_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"sto_state\", \"type\": \"varchar\"},\n", "    {\"name\": \"invoice_id\", \"type\": \"varchar\"},\n", "    {\"name\": \"STO_Invoice_created_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"actual_sales\", \"type\": \"float\"},\n", "    {\"name\": \"Invoice_State\", \"type\": \"varchar\"},\n", "    {\"name\": \"grn_created_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"inward_amount\", \"type\": \"float\"},\n", "    {\"name\": \"sto_Created_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"sto_Billed_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"sto_Expired_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"sto_Inward_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"expected_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"reserved_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"billed_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"inward_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"released_reserved_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"released_billed_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"sto_item_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"mrp\", \"type\": \"float\"},\n", "    {\"name\": \"description\", \"type\": \"varchar\"},\n", "    {\"name\": \"max_updated_at\", \"type\": \"datetime\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"sto_details_ft\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"sto_id\", \"item_id\"],\n", "    \"sortkey\": [\"sto_id\", \"item_id\"],\n", "    \"incremental_key\": \"max_updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(sto_process_tracker, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}