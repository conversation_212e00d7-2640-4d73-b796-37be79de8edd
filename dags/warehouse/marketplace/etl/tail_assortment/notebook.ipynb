{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "from jinja2 import Template\n", "import os\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aur = pb.get_connection(\"[Prod] Aurora DB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=0)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=1)).date()\n", "today = start_date.strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet = [1024]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "select \n", "co.facility_id,\n", "wf.name as facility_name,\n", "co.name as outlet_name,\n", "co.id as outlet_id,\n", "co.business_type_id,\n", "iod.ancestor,\n", "iod.order_id,\n", "os.name as order_status,\n", "convert_tz(iod.slot_handover_time,'+00:00','+05:30') as 'handovertime',\n", "convert_tz(iod.scheduled_at,'+00:00','+05:30') as 'scheduled_delivery_timestamp',\n", "date(convert_tz(iod.scheduled_at,'+00:00','+05:30')) as 'scheduled_delivery_date',\n", "convert_tz(pi.pos_timestamp,'+00:00','+05:30') as 'order_billed_timestamp',\n", "extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) as hour, \n", "case when extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) <= 16 then 'SLOT A' ELSE 'SLOT B' end as Delivery_Slot\n", "from ims.ims_order_details iod inner join ims.ims_order_status os on iod.status_id = os.id inner join \n", "retail.console_outlet co on co.id=iod.outlet inner join\n", "retail.warehouse_facility wf on wf.id=co.facility_id left join\n", "pos.pos_invoice pi on pi.grofers_order_id=iod.order_id\n", "where  date(scheduled_at) >= current_date \n", "and iod.status_id in (1,2)\n", "-- and co.business_type_id = 13 \n", "-- and co.id = 1074\n", "\"\"\"\n", "\n", "b1 = pd.read_sql_query(sql=query, con=retail_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" select \n", "              order_id,\n", "              batch_id as order_batch_id,\n", "             (pos_created_at + interval '5.5 hours') as batch_id_assigned_at \n", "             from whl.batch_order_mapping where created_at > (CURRENT_DATE - interval '7 days')\"\"\"\n", "b2 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select \n", "             fhb.id as order_batch_id, \n", "             fhb.name as batch_name,\n", "             case when fhb.state = 1 then 'Created'\n", "             when fhb.state = 2 then 'Tagged'\n", "             when fhb.state = 3 then 'Activated'\n", "             when fhb.state = 4 then 'Handover_activated'\n", "             when fhb.state = 5 then 'Ready_for_Handover'\n", "             when fhb.state = 6 then 'Completed' end\n", "             as batch_current_state,\n", "             fhb.total_orders,\n", "             fhb.dropped_orders,\n", "             fhb.drop_zone_id ,\n", "             dz.name as drop_zone_name, \n", "             dz.state as drop_zone_state\n", "\n", "            from whl.forward_handover_batch fhb \n", "            left join whl.warehouse_drop_zone dz on fhb.drop_zone_id = dz.id\n", "            where date(fhb.created_at) >  (CURRENT_DATE - interval '20 days')::date\"\"\"\n", "\n", "b3 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"Select \n", "            batch_id as order_batch_id,\n", "            order_id, \n", "            crate_id,\n", "            crate_drop_timestamp,\n", "            max(crate_drop_timestamp) over (partition by order_id ) as last_crate_drop_timestamp \n", "          FROM \n", "              (Select \n", "                  batch_id, \n", "                  order_id,\n", "                  crate_id , \n", "                  (pos_created_at + interval '5.5 hours') as crate_drop_timestamp\n", "                FROM whl.batch_crate_mapping where created_at > (CURRENT_DATE - interval '7 days'))\n", "            T\"\"\"\n", "\n", "b4 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b5 = b1.merge(b2, how=\"left\", on=[\"order_id\"])\n", "b6 = b5.merge(b3, how=\"left\", on=[\"order_batch_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7 = b6.merge(b4, how=\"left\", on=[\"order_batch_id\", \"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.scheduled_delivery_date = pd.to_datetime(b7.scheduled_delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7[\"data_updated_at\"] = datetime.utcnow() - timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with \n", "FC_MAPPING AS \n", "    (\n", "        SELECT DISTINCT\n", "            PCO.FACILITY_ID,\n", "            CF.NAME AS FACILITY_NAME,\n", "            BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,\n", "            BOM.NAME AS BACKEND_MERCHANT_NAME,\n", "            PCO.ID AS OUTLET_ID,\n", "            PCO.NAME AS OUTLET_NAME\n", "        from lake_oms_bifrost.oms_merchant AS BOM\n", "        INNER join  lake_retail.console_outlet_cms_store  AS CMS\n", "            ON BOM.EXTERNAL_ID = CMS.CMS_STORE \n", "            AND CMS.ACTIVE = 1 \n", "            AND CMS.CMS_UPDATE_ACTIVE = 1\n", "            AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "        INNER join  lake_retail.console_outlet  AS PCO\n", "            ON CMS.OUTLET_ID = PCO.ID\n", "        INNER JOIN LAKE_CRATES.FACILITY AS CF \n", "            ON CF.ID = PCO.FACILITY_ID\n", ")\n", "\n", "select distinct os.id as suborder_id, o.route_id, s.name as station_name, json_extract_path_text(dp.meta,'unique_name',true) as dp_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant bom on os.backend_merchant_id = bom.id and bom.virtual_merchant_type <> 'superstore_merchant'\n", "left join lake_last_mile.order o on os.order_id = o.external_order_id\n", "left join lake_last_mile.distribution_point dp on dp.id = o.distribution_point_id\n", "left join fc_mapping fc on fc.backend_merchant_id = bom.external_id\n", "left join lake_last_mile.station s on o.station_id = s.id\n", "\n", "where (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date >= current_date -1\n", "\"\"\"\n", "\n", "b8 = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.order_id = b7.order_id.astype(int)\n", "\n", "b7 = b7.merge(b8, how=\"left\", left_on=[\"order_id\"], right_on=[\"suborder_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_details = b7[\n", "    [\n", "        \"facility_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"order_status\",\n", "        \"outlet_name\",\n", "        \"business_type_id\",\n", "        \"station_name\",\n", "        \"dp_id\",\n", "        \"handovertime\",\n", "        \"order_batch_id\",\n", "        \"batch_name\",\n", "        \"drop_zone_name\",\n", "        \"route_id\",\n", "        \"batch_current_state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ancestors = b7[b7.outlet_id.isin(outlet)][\"ancestor\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = order_details[order_details.ancestor.isin(ancestors)].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df.order_batch_id = df.order_batch_id.fillna(0)\n", "# df.route_id = df.route_id.fillna(0)\n", "# df.drop_zone_name = df.drop_zone_name.fillna(0)\n", "# df.batch_name = df.batch_name.fillna(0)\n", "# df.dp_id = df.dp_id.fillna(0)\n", "# df.ancestor = df.ancestor .astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed = df.sort_values(by=\"ancestor\").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1bNfBXCt9q0m5-oNsgQIe3WANyBEHUmF90uglQHCTjUk\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(detailed, sheet_id, \"Detailed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping = detailed[detailed[\"business_type_id\"].isin([7])][\n", "    [\"facility_name\", \"station_name\"]\n", "]\n", "mapping.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(mapping, sheet_id, \"Facility <> Station mapping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# detailed[detailed.business_type_id == 13][['handovertime', 'order_batch_id', 'batch_name', 'station_name', 'dp_id', 'route_id',]]\n", "\n", "# .groupby(['handovertime', 'order_batch_id', 'batch_name', 'station_name', 'dp_id', 'route_id', 'business_type_id'])['facility_name'].apply(lambda x: x if business_type_id in (1,2,3,4) else '' )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = detailed[\n", "    (detailed.business_type_id == 7)\n", "    #                   & (~detailed.order_batch_id.isna())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = summary[\n", "    [\n", "        \"handovertime\",\n", "        \"order_batch_id\",\n", "        \"batch_name\",\n", "        \"batch_current_state\",\n", "        \"station_name\",\n", "        \"dp_id\",\n", "        \"drop_zone_name\",\n", "    ]\n", "]\n", "summary.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.rename({\"drop_zone_name\": \"JIT dropzone\"}, inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed[\"Parent FC\"] = np.where(\n", "    detailed[\"business_type_id\"].isin([7]), detailed[\"facility_name\"], \"\"\n", ")\n", "parent = detailed[detailed.business_type_id.isin([7])][\n", "    [\"handovertime\", \"batch_name\", \"Parent FC\", \"drop_zone_name\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = summary.merge(parent, how=\"left\", on=[\"batch_name\", \"handovertime\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.rename({\"drop_zone_name\": \"Parent FC dropzone\"}, inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"state_id\"] = np.where(\n", "    summary.batch_current_state == \"Created\",\n", "    1,\n", "    np.where(\n", "        summary.batch_current_state == \"Tagged\",\n", "        2,\n", "        np.where(\n", "            summary.batch_current_state == \"Activated\",\n", "            3,\n", "            np.where(\n", "                summary.batch_current_state == \"Handover_activated\",\n", "                4,\n", "                np.where(\n", "                    summary.batch_current_state == \"Ready_for_Handover\",\n", "                    5,\n", "                    np.where(summary.batch_current_state == \"Completed\", 6, 0),\n", "                ),\n", "            ),\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = summary.sort_values(by=[\"handovertime\", \"state_id\"]).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.drop([\"index\", \"state_id\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t = datetime.datetime.today().strftime(\"%Y-%m-%d\")\n", "y = (datetime.datetime.today() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = final[\n", "    final[\"handovertime\"].apply(lambda x: pd.to_datetime(str(x)).strftime(\"%Y-%m-%d\"))\n", "    == t\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = final[\n", "    final[\"handovertime\"].apply(lambda x: pd.to_datetime(str(x)).strftime(\"%Y-%m-%d\"))\n", "    == y\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rem = final[\n", "    final[\"handovertime\"].apply(lambda x: pd.to_datetime(str(x)).strftime(\"%Y-%m-%d\"))\n", "    > t\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = today.append([yesterday, rem])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fin, sheet_id, \"Summary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Query took %s seconds to run\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}