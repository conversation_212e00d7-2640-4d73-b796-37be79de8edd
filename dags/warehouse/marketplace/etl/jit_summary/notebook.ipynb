{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "from jinja2 import Template\n", "import os\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aur = pb.get_connection(\"aurora\")\n", "retail_db = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=1)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=0)).date()\n", "yesterday = start_date.strftime(\"%d-%m-%Y\")\n", "today = end_date.strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date, end_date, today, yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1IWQlRMhYoikGaWr2fFj5Hm912maMPZWW1yIxLyHWZiw\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "select \n", "co.facility_id,\n", "wf.name as facility_name,\n", "co.name as outlet_name,\n", "co.id as outlet_id,\n", "co.business_type_id,\n", "iod.ancestor,\n", "iod.order_id,\n", "os.name as order_status,\n", "(case \n", "when HOUR(convert_tz(iod.scheduled_at,'+00:00','+05:30'))<=14 \n", "then 'slot-A'\n", "else 'slot-B'\n", "end) as slot,\n", "convert_tz(iod.slot_handover_time,'+00:00','+05:30') as 'handovertime',\n", "convert_tz(iod.scheduled_at,'+00:00','+05:30') as 'scheduled_delivery_timestamp',\n", "date(convert_tz(iod.scheduled_at,'+00:00','+05:30')) as 'scheduled_delivery_date',\n", "convert_tz(pi.pos_timestamp,'+00:00','+05:30') as 'order_billed_timestamp',\n", "extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) as hour, \n", "case when extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) <= 16 then 'SLOT A' ELSE 'SLOT B' end as Delivery_Slot\n", "from ims.ims_order_details iod inner join ims.ims_order_status os on iod.status_id = os.id inner join \n", "retail.console_outlet co on co.id=iod.outlet inner join\n", "retail.warehouse_facility wf on wf.id=co.facility_id left join\n", "pos.pos_invoice pi on pi.grofers_order_id=iod.order_id\n", "where  date(scheduled_at) >= current_date and date(scheduled_at) <= current_date+1\n", "and iod.status_id in (1,2)\n", "-- and co.business_type_id = 13 \n", "-- and co.id = 1074\n", "\"\"\"\n", "\n", "b1 = pd.read_sql_query(sql=query, con=retail_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" select \n", "              order_id,\n", "              batch_id as order_batch_id,\n", "             (pos_created_at + interval '5.5 hours') as batch_id_assigned_at \n", "             from whl.batch_order_mapping where created_at > (CURRENT_DATE - interval '7 days')\"\"\"\n", "b2 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select \n", "             fhb.id as order_batch_id, \n", "             fhb.name as batch_name,\n", "             case when fhb.state = 1 then 'Created'\n", "             when fhb.state = 2 then 'Tagged'\n", "             when fhb.state = 3 then 'Activated'\n", "             when fhb.state = 4 then 'Handover_activated'\n", "             when fhb.state = 5 then 'Ready_for_Handover'\n", "             when fhb.state = 6 then 'Completed' end\n", "             as batch_current_state,\n", "             fhb.total_orders,\n", "             fhb.dropped_orders,\n", "             fhb.drop_zone_id ,\n", "             dz.name as drop_zone_name, \n", "             dz.state as drop_zone_state\n", "\n", "            from whl.forward_handover_batch fhb \n", "            left join whl.warehouse_drop_zone dz on fhb.drop_zone_id = dz.id\n", "            where date(fhb.created_at) >  (CURRENT_DATE - interval '20 days')::date\"\"\"\n", "\n", "b3 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"Select \n", "            batch_id as order_batch_id,\n", "            order_id, \n", "            crate_id,\n", "            crate_drop_timestamp,\n", "            max(crate_drop_timestamp) over (partition by order_id ) as last_crate_drop_timestamp \n", "          FROM \n", "              (Select \n", "                  batch_id, \n", "                  order_id,\n", "                  crate_id , \n", "                  (pos_created_at + interval '5.5 hours') as crate_drop_timestamp\n", "                FROM whl.batch_crate_mapping where created_at > (CURRENT_DATE - interval '7 days'))\n", "            T\"\"\"\n", "\n", "b4 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b5 = b1.merge(b2, how=\"left\", on=[\"order_id\"])\n", "b6 = b5.merge(b3, how=\"left\", on=[\"order_batch_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7 = b6.merge(b4, how=\"left\", on=[\"order_batch_id\", \"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.scheduled_delivery_date = pd.to_datetime(b7.scheduled_delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7[\"data_updated_at\"] = datetime.utcnow() - timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with \n", "FC_MAPPING AS \n", "    (\n", "        SELECT DISTINCT\n", "            PCO.FACILITY_ID,\n", "            CF.NAME AS FACILITY_NAME,\n", "            BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,\n", "            BOM.NAME AS BACKEND_MERCHANT_NAME,\n", "            PCO.ID AS OUTLET_ID,\n", "            PCO.NAME AS OUTLET_NAME\n", "        from lake_oms_bifrost.oms_merchant AS BOM\n", "        INNER join  lake_retail.console_outlet_cms_store  AS CMS\n", "            ON BOM.EXTERNAL_ID = CMS.CMS_STORE \n", "            AND CMS.ACTIVE = 1 \n", "            AND CMS.CMS_UPDATE_ACTIVE = 1\n", "            AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "        INNER join  lake_retail.console_outlet  AS PCO\n", "            ON CMS.OUTLET_ID = PCO.ID\n", "        INNER JOIN LAKE_CRATES.FACILITY AS CF \n", "            ON CF.ID = PCO.FACILITY_ID\n", ")\n", "\n", "select distinct os.id as suborder_id, o.route_id, s.name as station_name, json_extract_path_text(dp.meta,'unique_name',true) as dp_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant bom on os.backend_merchant_id = bom.id and bom.virtual_merchant_type <> 'superstore_merchant'\n", "left join lake_last_mile.order o on os.order_id = o.external_order_id\n", "left join lake_last_mile.distribution_point dp on dp.id = o.distribution_point_id\n", "left join fc_mapping fc on fc.backend_merchant_id = bom.external_id\n", "left join lake_last_mile.station s on o.station_id = s.id\n", "\n", "where (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date >= current_date -1\n", "\"\"\"\n", "\n", "b8 = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.order_id = b7.order_id.astype(int)\n", "b7 = b7.merge(b8, how=\"left\", left_on=[\"order_id\"], right_on=[\"suborder_id\"])\n", "b7.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_details = b7[\n", "    [\n", "        \"facility_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"order_status\",\n", "        \"outlet_name\",\n", "        \"business_type_id\",\n", "        \"station_name\",\n", "        \"dp_id\",\n", "        \"handovertime\",\n", "        \"slot\",\n", "        \"scheduled_delivery_date\",\n", "        \"order_batch_id\",\n", "        \"batch_name\",\n", "        \"drop_zone_name\",\n", "        \"route_id\",\n", "        \"batch_current_state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ancestors = b7[b7.outlet_id == 997][\"ancestor\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = order_details[order_details.ancestor.isin(ancestors)].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed = df.sort_values(by=\"ancestor\").reset_index()\n", "detailed.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hour = int((datetime.now()+ timedelta(hours=5.5)).strftime(\"%H\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# detailed[\"slot\"] = np.where(int(detailed[\"handovertime\"] + timedelta(hours=5.5)).strftime(\"%H\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dividing slots logic\n", "# if hour>=18:\n", "#     #detailed_slot = detailed[(detailed[\"handovertime\"]>today+' 06:00:00') & (detailed[\"handovertime\"]<=today+' 18:00:00')]\n", "#     slot = today + \" Slot A\"\n", "# if hour>=6 and hour<18:\n", "#     #detailed_slot = detailed[(detailed[\"handovertime\"]>yesterday+' 18:00:00') & (detailed[\"handovertime\"]<=today+' 06:00:00')]\n", "#     slot = yesterday + \" Slot B\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed[\"handoverdate\"] = detailed[\"handovertime\"].dt.date\n", "# detailed[\"scheduledeliverydate\"] = detailed[]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if detailed.shape[0]:\n", "    g1_a = (\n", "        detailed.groupby(\n", "            [\"scheduled_delivery_date\", \"slot\", \"facility_name\", \"station_name\"]\n", "        )[\"order_status\"]\n", "        .value_counts()\n", "        .unstack()\n", "        .fillna(0)\n", "        .reset_index()\n", "    )\n", "    g2_a = (\n", "        detailed.groupby(\n", "            [\"scheduled_delivery_date\", \"slot\", \"facility_name\", \"station_name\"]\n", "        )[\"order_id\"]\n", "        .count()\n", "        .reset_index()\n", "    )\n", "    summary_a = pd.DataFrame()\n", "    summary_a = pd.merge(\n", "        g2_a,\n", "        g1_a,\n", "        on=[\"scheduled_delivery_date\", \"slot\", \"facility_name\", \"station_name\"],\n", "        how=\"left\",\n", "    ).rename(columns={\"order_id\": \"Orders\"})\n", "    # summary_a[\"Not Billed\"] = summary_a['Created']-summary_a['Billed']\n", "    # summary_a = summary_a.append(summary_a.sum(numeric_only=True), ignore_index=True)\n", "    # summary_a.facility_name.iloc[-1]=\"Total\"\n", "    # summary_a[\"Date & Slot\"] = slot\n", "    # cols = list(summary_a.columns)\n", "    # cols = [cols[-1]] + cols[:-1]\n", "    # summary_a = summary_a[cols]\n", "    summary_a.head()\n", "    pb.to_sheets(summary_a, sheet_id, \"Summary_Live\")\n", "else:\n", "    g1_a = pd.DataFrame()\n", "    g2_a = pd.DataFrame()\n", "    summary_a = pd.DataFrame()\n", "    print(\"No Data in outlet: 997\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# summary = pb.from_sheets(sheet_id,\"Summary_JIT\")\n", "# summary = pd.DataFrame()\n", "# summary = summary.append(summary_a)\n", "# summary = summary.append([slot])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}