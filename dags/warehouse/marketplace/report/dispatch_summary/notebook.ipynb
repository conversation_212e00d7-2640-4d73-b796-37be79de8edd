{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "from jinja2 import Template\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aur = pb.get_connection(\"aurora\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=0)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=1)).date()\n", "today = start_date.strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1dm6KDo1EcapR4stsPFclq6UD__kuhO6X4_Pu42o7-Qc\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet = pb.from_sheets(sheet_id, \"Emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet.outlet.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet = list(filter(None, sheet.outlet.values))\n", "outlet = [int(i) for i in outlet]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "select \n", "co.facility_id,\n", "wf.name as facility_name,\n", "co.name as outlet_name,\n", "co.id as outlet_id,\n", "co.business_type_id,\n", "iod.ancestor,\n", "iod.order_id,\n", "os.name as order_status,\n", "convert_tz(iod.slot_handover_time,'+00:00','+05:30') as 'handovertime',\n", "convert_tz(iod.scheduled_at,'+00:00','+05:30') as 'scheduled_delivery_timestamp',\n", "date(convert_tz(iod.scheduled_at,'+00:00','+05:30')) as 'scheduled_delivery_date',\n", "convert_tz(pi.pos_timestamp,'+00:00','+05:30') as 'order_billed_timestamp',\n", "extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) as hour, \n", "case when extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) <= 16 then 'SLOT A' ELSE 'SLOT B' end as Delivery_Slot\n", "from ims.ims_order_details iod inner join ims.ims_order_status os on iod.status_id = os.id inner join \n", "retail.console_outlet co on co.id=iod.outlet inner join\n", "retail.warehouse_facility wf on wf.id=co.facility_id left join\n", "pos.pos_invoice pi on pi.grofers_order_id=iod.order_id\n", "where date(scheduled_at) between current_date and current_date + interval 1 day \n", "\"\"\"\n", "\n", "b1 = pd.read_sql_query(sql=query, con=retail_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" select \n", "              order_id,\n", "              batch_id as order_batch_id,\n", "             (pos_created_at + interval '5.5 hours') as batch_id_assigned_at \n", "             from whl.batch_order_mapping where created_at > (CURRENT_DATE - interval '7 days')\"\"\"\n", "b2 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select \n", "             fhb.id as order_batch_id, \n", "             fhb.name as batch_name,\n", "             case when fhb.state = 1 then 'Created'\n", "             when fhb.state = 2 then 'Tagged'\n", "             when fhb.state = 3 then 'Activated'\n", "             when fhb.state = 4 then 'Handover_activated'\n", "             when fhb.state = 5 then 'Ready_for_Handover'\n", "             when fhb.state = 6 then 'Completed' end\n", "             as batch_current_state,\n", "             fhb.total_orders,\n", "             fhb.dropped_orders,\n", "             fhb.drop_zone_id ,\n", "             dz.name as drop_zone_name, \n", "             dz.state as drop_zone_state\n", "\n", "            from whl.forward_handover_batch fhb \n", "            inner join whl.warehouse_drop_zone dz on fhb.drop_zone_id = dz.id\n", "            where date(fhb.created_at) BETWEEN  (CURRENT_DATE - interval '20 days')::date AND (CURRENT_DATE + interval '1 days')::date\"\"\"\n", "\n", "b3 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"Select\n", "#             batch_id as order_batch_id,\n", "#             order_id,\n", "#             crate_id,\n", "#             crate_drop_timestamp,\n", "#             max(crate_drop_timestamp) over (partition by order_id ) as last_crate_drop_timestamp\n", "#           FROM\n", "#               (Select\n", "#                   batch_id,\n", "#                   order_id,\n", "#                   crate_id ,\n", "#                   (pos_created_at + interval '5.5 hours') as crate_drop_timestamp\n", "#                 FROM whl.batch_crate_mapping where created_at > (CURRENT_DATE - interval '7 days'))\n", "#             T\"\"\"\n", "\n", "query = \"\"\"Select \n", "            order_id, \n", "            crate_id,\n", "            crate_drop_timestamp,\n", "            max(crate_drop_timestamp) over (partition by order_id ) as last_crate_drop_timestamp \n", "            FROM \n", "              (Select \n", "                  order_id,\n", "                  crate_id , \n", "                  (created_at + interval '5.5 hours') as crate_drop_timestamp\n", "                FROM whl.warehouse_pick_list_order_crate_mapping\n", "                where created_at > (CURRENT_DATE - interval '1 days')) T\"\"\"\n", "\n", "b4 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with \n", "FC_MAPPING AS \n", "    (\n", "        SELECT DISTINCT\n", "            PCO.FACILITY_ID,\n", "            CF.NAME AS FACILITY_NAME,\n", "            BOM.EXTERNAL_ID AS BACKEND_MERCHANT_ID,\n", "            BOM.NAME AS BACKEND_MERCHANT_NAME,\n", "            PCO.ID AS OUTLET_ID,\n", "            PCO.NAME AS OUTLET_NAME\n", "        from lake_oms_bifrost.oms_merchant AS BOM\n", "        INNER join  lake_retail.console_outlet_cms_store  AS CMS\n", "            ON BOM.EXTERNAL_ID = CMS.CMS_STORE \n", "            AND CMS.ACTIVE = 1 \n", "            AND CMS.CMS_UPDATE_ACTIVE = 1\n", "            AND VIRTUAL_MERCHANT_TYPE <> 'superstore_merchant'\n", "        INNER join  lake_retail.console_outlet  AS PCO\n", "            ON CMS.OUTLET_ID = PCO.ID\n", "        INNER JOIN LAKE_CRATES.FACILITY AS CF \n", "            ON CF.ID = PCO.FACILITY_ID\n", ")\n", "\n", "select distinct os.id as suborder_id, o.route_id, s.name as station_name, json_extract_path_text(dp.meta,'unique_name',true) as dp_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant bom on os.backend_merchant_id = bom.id and bom.virtual_merchant_type <> 'superstore_merchant'\n", "left join lake_last_mile.order o on os.order_id = o.external_order_id\n", "left join lake_last_mile.distribution_point dp on dp.id = o.distribution_point_id\n", "left join fc_mapping fc on fc.backend_merchant_id = bom.external_id\n", "left join lake_last_mile.station s on o.station_id = s.id\n", "\n", "where (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date >= current_date -1\n", "\"\"\"\n", "\n", "b8 = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b5 = b1.merge(b2, how=\"left\", on=[\"order_id\"])\n", "\n", "b6 = b5.merge(b3, how=\"left\", on=[\"order_batch_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# b7 = b6.merge(b4, how=\"left\", on=[\"order_batch_id\", \"order_id\"])\n", "\n", "b7 = b6.merge(b4, how=\"left\", on=[\"order_id\", \"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.scheduled_delivery_date = pd.to_datetime(b7.scheduled_delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7[\"data_updated_at\"] = datetime.utcnow() - timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.order_id = b7.order_id.astype(int)\n", "\n", "b7 = b7.merge(b8, how=\"left\", left_on=[\"order_id\"], right_on=[\"suborder_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_details = b7[\n", "    [\n", "        \"Delivery_Slot\",\n", "        \"facility_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"order_status\",\n", "        \"outlet_name\",\n", "        \"business_type_id\",\n", "        \"station_name\",\n", "        \"dp_id\",\n", "        \"handovertime\",\n", "        \"order_batch_id\",\n", "        \"batch_name\",\n", "        \"drop_zone_name\",\n", "        \"route_id\",\n", "        \"crate_id\",\n", "        \"batch_current_state\",\n", "        \"scheduled_delivery_date\",\n", "        \"order_billed_timestamp\",\n", "        \"crate_drop_timestamp\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ancestors = b7[b7.outlet_id.isin(outlet)][\"ancestor\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = order_details[order_details.ancestor.isin(ancestors)].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"keep\"] = np.where(\n", "    (df.scheduled_delivery_date == start_date.strftime(\"%Y-%m-%d\"))\n", "    & (df.Delivery_Slot == \"SLOT B\"),\n", "    1,\n", "    np.where(\n", "        (df.scheduled_delivery_date == end_date.strftime(\"%Y-%m-%d\"))\n", "        & (df.Delivery_Slot == \"SLOT A\"),\n", "        1,\n", "        0,\n", "    ),\n", ")\n", "\n", "df = df[df.keep == 1].reset_index()\n", "df.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed = df.sort_values(by=\"ancestor\").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(detailed, sheet_id, \"Detailed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping = detailed[detailed[\"business_type_id\"].isin([1, 2, 3, 4])][\n", "    [\"facility_name\", \"station_name\"]\n", "]\n", "mapping.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(mapping, sheet_id, \"Facility <> Station mapping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping.station_name = mapping.station_name.fillna(\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = mapping.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = detailed[(detailed.business_type_id == 13)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed[\"parent_FC\"] = np.where(\n", "    detailed[\"business_type_id\"].isin([1, 2, 3, 4]), detailed[\"facility_name\"], \"\"\n", ")\n", "parent = detailed[detailed.business_type_id.isin([1, 2, 3, 4])][\n", "    [\"handovertime\", \"batch_name\", \"parent_FC\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7 = summary.merge(parent, how=\"left\", on=[\"batch_name\", \"handovertime\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = (\n", "    b7[b7[\"order_status\"] != \"Cancelled Nonbillable\"]\n", "    .groupby(\n", "        [\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "            \"outlet_name\",\n", "            \"parent_FC\",\n", "            \"station_name\",\n", "            \"scheduled_delivery_date\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Received\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y1 = (\n", "#     temp[\n", "#         (temp[\"pick_list_state\"] != 5)\n", "#         & (temp[\"pick_list_state\"] != 11)\n", "#         & (pd.isnull(temp[\"completion_time\"]) == False)\n", "#     ]\n", "#     .groupby([\"facility\", \"scheduled_delivery_date\", \"delivery_slot\", \"handovertime\"])[\n", "#         \"order_id\"\n", "#     ]\n", "#     .nunique()\n", "#     .reset_index()\n", "#     .rename(columns={\"order_id\": \"Picked\"})\n", "# )\n", "# y2 = y.merge(\n", "#     y1,\n", "#     how=\"left\",\n", "#     on=[\"facility\", \"handovertime\", \"scheduled_delivery_date\", \"delivery_slot\"],\n", "# )\n", "y3 = (\n", "    b7[\n", "        (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "        & (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    ]\n", "    .groupby(\n", "        [\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "            \"outlet_name\",\n", "            \"parent_FC\",\n", "            \"station_name\",\n", "            \"scheduled_delivery_date\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Billed\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y4 = y.merge(\n", "    y3,\n", "    how=\"left\",\n", "    on=[\n", "        \"Delivery_Slot\",\n", "        \"handovertime\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "    ],\n", ")\n", "y5 = (\n", "    b7[\n", "        (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "        & (pd.isnull(b7[\"crate_drop_timestamp\"]) == False)\n", "    ]\n", "    .groupby(\n", "        [\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "            \"outlet_name\",\n", "            \"parent_FC\",\n", "            \"station_name\",\n", "            \"scheduled_delivery_date\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Dropped\"})\n", ")\n", "y6 = y4.merge(\n", "    y5,\n", "    how=\"left\",\n", "    on=[\n", "        \"Delivery_Slot\",\n", "        \"handovertime\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "    ],\n", ")\n", "\n", "y6[[\"Billed\", \"Dropped\"]] = y6[[\"Billed\", \"Dropped\"]].fillna(0).astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y7 = (\n", "    b7.groupby(\n", "        [\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "            \"outlet_name\",\n", "            \"parent_FC\",\n", "            \"station_name\",\n", "            \"scheduled_delivery_date\",\n", "        ]\n", "    )[\"crate_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"crate_id\": \"crates\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y8 = y6.merge(\n", "    y7,\n", "    how=\"left\",\n", "    on=[\n", "        \"Delivery_Slot\",\n", "        \"handovertime\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y8.Billed = y8.Billed.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y8.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(y8, sheet_id, \"Order Flow Summary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_not_dropped = b7[\n", "    (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    & (pd.isnull(b7.crate_drop_timestamp) == True)\n", "][\n", "    [\n", "        \"Delivery_Slot\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"handovertime\",\n", "        \"batch_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"crate_id\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "    ]\n", "]\n", "orders_not_dropped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_dropped = b7[\n", "    (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    & (pd.isnull(b7.crate_drop_timestamp) == False)\n", "][\n", "    [\n", "        \"Delivery_Slot\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"handovertime\",\n", "        \"batch_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"crate_id\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "    ]\n", "]\n", "orders_dropped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_not_dropped,\n", "    \"1dm6KDo1EcapR4stsPFclq6UD__kuhO6X4_Pu42o7-Qc\",\n", "    \"Orders Not dropped\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_dropped, \"1dm6KDo1EcapR4stsPFclq6UD__kuhO6X4_Pu42o7-Qc\", \"Orders dropped\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7[\"status\"] = np.where(\n", "    (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "    & (pd.isnull(b7[\"order_billed_timestamp\"]) == False),\n", "    \"Billed\",\n", "    np.where(\n", "        (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "        & (pd.isnull(b7[\"crate_drop_timestamp\"]) == False),\n", "        \"Dropped\",\n", "        \"\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_file = b7[b7.status != \"\"][\n", "    [\n", "        \"Delivery_Slot\",\n", "        \"outlet_name\",\n", "        \"parent_FC\",\n", "        \"order_id\",\n", "        \"handovertime\",\n", "        \"batch_name\",\n", "        \"batch_current_state\",\n", "        \"station_name\",\n", "        \"scheduled_delivery_date\",\n", "        \"status\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["emails = pb.from_sheets(sheet_id, \"Emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Outlet_List = Outlets.OutletId.drop_duplicates().to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if int((datetime.today() + timedelta(hours=5, minutes=30)).strftime(\"%H\")) < 16:\n", "    slot = \"SLOT B\"\n", "    date = start_date\n", "else:\n", "    slot = \"SLOT A\"\n", "    date = end_date\n", "\n", "final = y8[y8.Delivery_Slot == slot]\n", "fin_csv = csv_file[csv_file.Delivery_Slot == slot]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = final.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_csv.to_csv(\"order_detail.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import os\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "# to_email = [\"<EMAIL>\",]\n", "\n", "to_email = emails.Email_Id.to_list()\n", "\n", "from_email = \"<EMAIL>\"\n", "\n", "subject = (\n", "    \"Dispatch summary for outlet \" + str(outlet) + \" - \" + str(date) + \" - \" + slot\n", ")\n", "\n", "# cwd = os.getcwd ()\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"dispatch_email_template.html\",\n", "    ),\n", "    \"r\",\n", ") as f:\n", "    t = Template(f.read())\n", "\n", "rendered = t.render(products=items, products1=items1)\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    bcc=[\n", "        \"<EMAIL>\",\n", "    ],\n", "    html_content=rendered,\n", "    files=[\"order_detail.csv\"],\n", ")\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import jinja2\n", "# import pencilbox as pb\n", "# from jinja2 import Template\n", "\n", "# for outlet in Outlet_List:\n", "\n", "#     Name = Outlets[Outlets[\"OutletId\"] == str(outlet)][\"Facility Name\"].unique()[0]\n", "\n", "#     Orders = y6[y6[\"facility_name\"] == Name]\n", "\n", "#     to_email = Outlets[Outlets[\"OutletId\"] == str(outlet)].Email_Id.to_list()\n", "\n", "#     Orders = Orders.to_dict(orient=\"rows\")\n", "\n", "#     from_email = \"<EMAIL>\"\n", "\n", "#     orders_not_dropped[orders_not_dropped[\"facility_name\"] == Name].to_csv(\n", "#         \"/tmp/OrdersNotDropped_\" + Name + \".csv\"\n", "#     )\n", "\n", "#     orders_dropped[orders_dropped[\"facility_name\"] == Name].to_csv(\n", "#         \"/tmp/OrdersDropped_\" + Name + \".csv\"\n", "#     )\n", "\n", "#     email_template = \"<PERSON>car<PERSON>_emails.html\"\n", "#     loader = jinja2.FileSystemLoader(\n", "#         searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/outbound/report/ninjacart/\"\n", "#     )\n", "#     tmpl_environ = jinja2.Environment(loader=loader)\n", "#     template = tmpl_environ.get_template(email_template)\n", "\n", "#     rendered = template.render(products1=Orders)\n", "\n", "#     subject = \"Orders Dropped and Not Dropped Data \" + Name + \" \" + str(today)\n", "\n", "#     pb.send_email(\n", "#         from_email,\n", "#         to_email,\n", "#         subject,\n", "#         html_content=rendered,\n", "#         files=[\n", "#             \"/tmp/OrdersNotDropped_\" + Name + \".csv\",\n", "#             \"/tmp/OrdersDropped_\" + Name + \".csv\",\n", "#         ],\n", "#     )\n", "\n", "#     print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}