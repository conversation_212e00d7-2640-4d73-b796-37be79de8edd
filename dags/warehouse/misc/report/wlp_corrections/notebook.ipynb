{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import numpy\n", "import jinja2\n", "import pandas as pd\n", "from datetime import datetime, date\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"WITH base AS\n", "  (SELECT outlet_id,\n", "          variant_id,\n", "          sum(quantity) AS qty\n", "   FROM lake_ims.ims_inventory\n", "   GROUP BY 1,\n", "            2\n", "   HAVING qty>0),\n", "it_pd AS\n", "  (SELECT x.product_id::int,\n", "          item_id::int\n", "   FROM consumer.it_pd_log it\n", "   JOIN\n", "     ( SELECT i.product_id,\n", "              max(updated_on) AS max_updated_at\n", "      FROM consumer.it_pd_log i\n", "      GROUP BY 1 ) x ON x.product_id=it.product_id\n", "   AND updated_on=x.max_updated_at and x.product_id is not null)\n", "SELECT lp.outlet_id,\n", "       co.name AS outlet_name,\n", "       bt.name AS outlet_type,\n", "       lp.variant_id,\n", "       pp.item_id,\n", "       max(pp.name) AS item_name,\n", "       max(lp.landing_price) AS Weighted_landing_price,\n", "       max(pp.variant_mrp) AS mrp\n", "FROM base\n", "INNER JOIN lake_ims.ims_inventory_landing_price lp ON lp.outlet_id = base.outlet_id\n", "AND lp.variant_id = base.variant_id\n", "LEFT JOIN lake_rpc.product_product pp ON lp.variant_id = pp.variant_id\n", "INNER JOIN lake_retail.console_outlet co ON co.id = lp.outlet_id\n", "LEFT JOIN lake_retail.console_business_type bt ON bt.id = co.business_type_id\n", "INNER JOIN dwh.dim_merchant_outlet_facility_mapping f ON f.pos_outlet_id = lp.outlet_id and f.is_current = True\n", "INNER JOIN it_pd ip ON ip.item_id = pp.item_id \n", "WHERE lp.active=1\n", "  AND pp.active=1\n", "  AND f.frontend_merchant_id IS NOT NULL\n", "  AND co.business_type_id NOT IN (9,\n", "                                  15,\n", "                                  14,\n", "                                  16)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5\n", "HAVING (Weighted_landing_price >= mrp) or (Weighted_landing_price < 1)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data = pd.read_sql_query(sql = query,con = pb.get_connection('[Warehouse] Redshift'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1 = pd.read_sql_query(sql=query1, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["numpy.sort(data1.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = list(data1.outlet_id.unique())\n", "variants = list(data1.variant_id.unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# wlp - attribution to latest update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = pd.read_sql_query(\n", "    f\"\"\"\n", "        with lat as\n", "        (select i.outlet_id, i.variant_id, max(i.pos_timestamp) as latest_ts\n", "        from lake_ims.ims_inventory_log i\n", "        join lake_retail.console_outlet c on c.id = i.outlet_id and c.business_type_id = 7 and c.active = 1\n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        where i.variant_id in {*variants,} and i.outlet_id in {*outlets,}\n", "           and ((i.weighted_lp >= pp.variant_mrp) or (i.weighted_lp < 1))\n", "        group by 1,2)\n", "        select  ii.inventory_update_id,\n", "                ii.outlet_id, \n", "                ii.variant_id, \n", "                i.name as update_type,\n", "                sum(ii.\"delta\") as update_quantity,\n", "                ii.pos_timestamp + interval '5.5 hrs' as update_timestamp,\n", "                ii.weighted_lp,\n", "                pp.variant_mrp\n", "            from lake_ims.ims_inventory_log ii\n", "            join lat on lat.outlet_id = ii.outlet_id and lat.variant_id = ii.variant_id and ii.pos_timestamp = lat.latest_ts\n", "            join lake_ims.ims_inventory_update_type i on i.id = ii.inventory_update_type_id\n", "            join lake_rpc.product_product pp on pp.variant_id = ii.variant_id and pp.active = 1\n", "            join lake_retail.console_outlet c on c.id = ii.outlet_id and c.active = 1 and c.device_id <> 47\n", "            group by 1,2,3,4,6,7,8\n", "       \"\"\",\n", "    pb.get_connection(\"[Warehouse] Redshift\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data2 = data1.merge(res, on=[\"outlet_id\", \"variant_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1.shape, data2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rawdata = data2[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"outlet_type\",\n", "        \"variant_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"inventory_update_id\",\n", "        \"update_type\",\n", "        \"update_quantity\",\n", "        \"update_timestamp\",\n", "        \"weighted_lp\",\n", "        \"variant_mrp\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rawdata.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"Weighted Landing Price Corrections\"\n", "to_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rawdata.to_csv(\"wlp_corrections_\" + str(date.today()) + \".csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = \"\"\"\n", "Hi,<br><br>\n", "\n", "Please find attached the weighted landing price corrections for today. <br>\n", "Along with the latest inventory update on that outlet-variant combination. <br>\n", "\"\"\"\n", "# html_content += \"<b>Outer case size min: </b>\" + str(outer_case_size_limit)\n", "# html_content += \"<br><br><b>Assortment last updated at: </b>\" + assortment_last_updated\n", "# html_content += \"<br><br><b>Unique ID: </b>\" + uid\n", "html_content += \"<br><br>Regards,<br>Data Warehouse\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[\"wlp_corrections_\" + str(date.today()) + \".csv\"],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}