{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m7UY7FQNhNGshlDiiNDsdlYuq-gVvC_W_tgMdtiN-F4\"\n", "outlets = pb.from_sheets(sheet_id, \"offline_stores\")\n", "outlets = outlets[[\"outlet_id\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"outlet_id\"], inplace=True)\n", "outlets = list(outlets.outlet_id.unique())\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "\n", "sheet_data = pb.from_sheets(sheet_id, \"offline_stores\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc_emails = sheet_data[\"cc_mail\"]\n", "email_list = sheet_data[[\"Group Mail Id\", \"outlet_id\", \"outlet_name\"]]\n", "email_list[\"Group Mail Id\"] = email_list[\"Group Mail Id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with items as\n", "(select item_id, item_name, variant_description from\n", "(select item_id,  name as item_name, variant_uom_text as variant_description, \n", "row_number() over (partition by item_id order by updated_at desc) as rank\n", "from \n", "lake_rpc.product_product)\n", "where rank = 1\n", ")\n", "\n", "select distinct (update_ts + interval '5.5 hours') as update_ts, gm.outlet_id, cl.frontend_id, cms_product_id, retail_item_id, l1_category,\n", "(items.item_name || ' ' || variant_description) as item_name, new_mrp, new_price as new_sticker, old_mrp, old_price as old_sticker \n", "from lake_pricing_v3.pricing_domain_productchangelog cl \n", "left join items on items.item_id = cl.retail_item_id\n", "left join consumer.grofers_market_stores_mapping gm on gm.frontend_id = cl.frontend_id\n", "left join dwh.dim_product dm on cl.cms_product_id = dm.product_id\n", "where cl.frontend_id in (select distinct frontend_id from consumer.grofers_market_stores_mapping)\n", "-- and new_price - old_price <> 0\n", "and new_price - old_price <> 0 \n", "and update_ts between (GETDATE() - interval '6 hour') and  GETDATE()\n", "order by update_ts desc\n", "\"\"\"\n", "data = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh_id = \"1Yn5tOUROILmcDducnvs33Q0iqtJ2XDsXduA0uQO0esg\"\n", "sheet_name = \"Sheet1\"\n", "partners = pb.from_sheets(sh_id, sheet_name)[[\"Outlet ID\", \"Supply\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["partners = partners[partners[\"Outlet ID\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["partners.tail(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in np.sort(data[\"outlet_id\"].unique()):\n", "    try:\n", "        NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "        final = data[data[\"outlet_id\"] == outlet_id][\n", "            [\n", "                \"outlet_id\",\n", "                \"frontend_id\",\n", "                \"cms_product_id\",\n", "                \"retail_item_id\",\n", "                \"l1_category\",\n", "                \"item_name\",\n", "                \"new_mrp\",\n", "                \"new_sticker\",\n", "                \"old_mrp\",\n", "                \"old_sticker\",\n", "            ]\n", "        ]\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        cc = [\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "        final.to_csv(\"price_stickers_%s.csv\" % outlet_id, index=False)\n", "        if (\n", "            partners.loc[\n", "                partners[\"Outlet ID\"].astype(int) == outlet_id, \"Supply\"\n", "            ].to_list()[0]\n", "            == \"SSC Tradecom\"\n", "        ):\n", "            from_email = \"<EMAIL>\"\n", "        elif (\n", "            partners.loc[\n", "                partners[\"Outlet ID\"].astype(int) == outlet_id, \"Supply\"\n", "            ].to_list()[0]\n", "            == \"Move On Delivery\"\n", "        ):\n", "            from_email = \"<EMAIL>\"\n", "        else:\n", "            print(\"email mapping not found\")\n", "        subject = \"Market Stores Price Stickers for %s\" % NOW_IST.date()\n", "        to_email = (\n", "            email_list[email_list[\"outlet_id\"] == str(outlet_id)][\"Group Mail Id\"]\n", "            .unique()\n", "            .tolist()\n", "        )\n", "        # to_email = [\"<EMAIL>\"]\n", "        print(outlet_id, to_email)\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject=subject,\n", "            html_content=\"\"\"\n", "                Hi,<br><br>\n", "                Please find the attached file for price stickers of the outlet: %s.<br>\n", "                <b></b><br>\n", "                <br><PERSON><PERSON>\n", "                \"\"\"\n", "            % outlet_id,\n", "            cc=cc,\n", "            files=[\"price_stickers_%s.csv\" % outlet_id],\n", "        )\n", "    except:\n", "        print(\"failed for: \", str(outlet_id))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}