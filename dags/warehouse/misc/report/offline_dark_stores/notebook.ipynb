{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pos = pb.get_connection(\"pos\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz)).strftime(\"%Y-%m-%d\")\n", "today\n", "\n", "start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=0)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=0)).date()\n", "end_date\n", "summary_date = start_date.strftime(\"%Y-%m-%d\")\n", "summary_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m7UY7FQNhNGshlDiiNDsdlYuq-gVvC_W_tgMdtiN-F4\"\n", "outlets = pb.from_sheets(sheet_id, \"offline_stores\")\n", "outlets = outlets[[\"outlet_id\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"outlet_id\"], inplace=True)\n", "outlets = list(outlets.outlet_id.unique())\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "\n", "sheet_data = pb.from_sheets(sheet_id, \"offline_stores\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc_emails = sheet_data[\"cc_mail\"]\n", "email_list = sheet_data[[\"Group Mail Id\", \"outlet_id\", \"outlet_name\"]]\n", "email_list[\"Group Mail Id\"] = email_list[\"Group Mail Id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc = cc_emails.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in outlets:\n", "    try:\n", "        outlet = int(outlet)\n", "        x.append(outlet)\n", "\n", "    except:\n", "        pass\n", "print(x)\n", "# outlets\n", "email_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales = \"\"\"select sales.* from\n", "(\n", "SELECT\n", "date(p.pos_timestamp + interval '5.5 hrs') AS Dated,\n", "p.outlet_id,\n", "p.invoice_id AS Invoice_ID,  \n", "CASE\n", " WHEN p.invoice_type_id in (9,10) THEN  p.offline_order_id else p.grofers_order_id  end as Order_ID, \n", "p.invoice_type_id ,\n", "CASE\n", " WHEN p.invoice_type_id = 1 THEN 'Online_sale'\n", " WHEN p.invoice_type_id = 2 THEN 'Online_return'\n", " WHEN p.invoice_type_id = 9 THEN 'Offline_sale'\n", " WHEN p.invoice_type_id = 10 THEN 'Offline_return'         \n", " else 'other' \n", " END AS Transaction_Type,\n", "p.outlet_name AS Store_Name,\n", "l1.name as Supply_City,\n", "l2.name as Customer_City,\n", "rp.item_id,\n", "rp.name AS Product_Name,\n", "pi.variant_id,\n", "(CASE WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "      WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "      ELSE 'Not Assigned'\n", "END) AS Item_Type,\n", "rp.variant_uom_text as Variant_Description,\n", "pi.unit_rate as MRP,\n", "(pi.unit_rate - pi.selling_price) as Discount,\n", "pi.selling_price as Selling_Price,\n", "CASE WHEN p.invoice_type_id in (1,9) THEN  sum(pi.selling_price*pi.quantity) ELSE 0 END AS Total_Gross_Bill_Amount,\n", "CASE WHEN p.invoice_type_id in (2,10) THEN sum(pi.selling_price*pi.quantity) ELSE 0 END AS Total_Return_Amount,\n", "sum(pi.quantity) as Qty\n", "FROM lake_pos.pos_invoice_product_details pi\n", "INNER JOIN lake_pos.pos_invoice p ON p.id = pi.invoice_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "INNER JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "WHERE p.invoice_type_id in (1,2,9,10) and p.outlet_id = %(outlet_id)s and  r.business_type_id = 8\n", "and date(p.pos_timestamp + interval '5.5 hrs') = %(start_date)s \n", "and pi.store_offer_id is null\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", ")sales\"\"\"\n", "\n", "iil = \"\"\" select iil.invoice_id,\n", "                 iil.grofers_order_id,\n", "                 iil.variant_id,\n", "                 avg(iil.transaction_lp) transaction_lp,\n", "                 avg(iil.weighted_lp) weighted_lp \n", "         from lake_ims.ims_inventory_log iil\n", "where date(iil.pos_timestamp + interval '5.5 hrs') = %(start_date)s\n", "and id>1225614316 and outlet_id=%(outlet_id)s\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in x:\n", "    sales_data = pd.read_sql_query(\n", "        sql=sales,\n", "        con=redshift,\n", "        params={\n", "            \"start_date\": start_date,\n", "            \"end_date\": end_date,\n", "            \"outlet_id\": outlet_id,\n", "        },\n", "    )\n", "\n", "    iil_data = pd.read_sql_query(\n", "        sql=iil,\n", "        con=redshift,\n", "        params={\n", "            \"start_date\": start_date,\n", "            \"end_date\": end_date,\n", "            \"outlet_id\": outlet_id,\n", "        },\n", "    )\n", "    final = pd.merge(\n", "        sales_data,\n", "        iil_data,\n", "        how=\"left\",\n", "        left_on=[\"invoice_id\", \"variant_id\", \"order_id\"],\n", "        right_on=[\"invoice_id\", \"variant_id\", \"grofers_order_id\"],\n", "        left_index=False,\n", "        right_index=False,\n", "        sort=True,\n", "    )\n", "    final.drop([\"grofers_order_id\"], axis=1, inplace=True)\n", "    # final.to_csv(\"data.csv\")\n", "    print(outlet_id, final.shape)\n", "    # if return the retrurn value else gross value\n", "\n", "    # 1,9\n", "    final.loc[\n", "        (final[\"invoice_type_id\"] == 1) | (final[\"invoice_type_id\"] == 9),\n", "        \"Total_Gross_Bill_Amount_TLP\",\n", "    ] = (\n", "        final[\"qty\"] * final[\"transaction_lp\"]\n", "    )\n", "    # 2,10\n", "\n", "    final.loc[\n", "        (final[\"invoice_type_id\"] == 2) | (final[\"invoice_type_id\"] == 10),\n", "        \"Total_Return_Amount_TLP\",\n", "    ] = (\n", "        final[\"qty\"] * final[\"transaction_lp\"] + 0\n", "    )\n", "    df = final\n", "    df[\"qty\"] = df[\"qty\"].astype(\"int\")\n", "    df1 = df.copy()\n", "    # print(df1[\"Store_Name\"]).unique()\n", "    df2 = df1.drop(\n", "        [\n", "            \"invoice_type_id\",\n", "            \"supply_city\",\n", "            \"customer_city\",\n", "            \"item_type\",\n", "            \"variant_description\",\n", "        ],\n", "        axis=1,\n", "    )\n", "    df2[\"Total_Gross_Bill_Amount_TLP\"] = df2[\"Total_Gross_Bill_Amount_TLP\"].fillna(0)\n", "    df2[\"Total_Return_Amount_TLP\"] = df2[\"Total_Return_Amount_TLP\"].fillna(0)\n", "    df2.drop([\"weighted_lp\", \"transaction_lp\"], axis=1, inplace=True)\n", "    # df2[\"margin_sales\"] = (\n", "    #     df2[\"Total_Gross_Bill_Amount\"] - df2[\"Total_Gross_Bill_Amount_TLP\"]\n", "    # )\n", "    # df2[\"margin_returns\"] = df2[\"Total_Return_Amount\"] - df2[\"Total_Return_Amount_TLP\"]\n", "    # df2[\"margin_items\"] = df2[\"margin_sales\"] + df2[\"margin_returns\"]\n", "    # df2[\"margin_items\"] = df2[\"margin_items\"].round(2)\n", "    df3 = df2.copy()\n", "    df3.drop(\n", "        [\n", "            \"order_id\",\n", "            \"product_name\",\n", "            \"store_name\",\n", "            \"discount\",\n", "            \"mrp\",\n", "        ],\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "\n", "    df4 = (\n", "        df3.groupby([\"outlet_id\", \"dated\", \"transaction_type\"])\n", "        .agg(\n", "            {\n", "                \"invoice_id\": \"nunique\",\n", "                \"total_gross_bill_amount\": \"sum\",\n", "                \"total_return_amount\": \"sum\",\n", "                \"Total_Gross_Bill_Amount_TLP\": \"sum\",\n", "                \"Total_Return_Amount_TLP\": \"sum\",\n", "                \"qty\": \"sum\",\n", "                \"item_id\": \"nunique\",\n", "            }\n", "        )\n", "        .rename(\n", "            columns={\n", "                \"invoice_id\": \"Total_orders\",\n", "                \"total_gross_bill_amount\": \"Sales_amt\",\n", "                \"total_return_amount\": \"Return_amt\",\n", "                \"Total_Gross_Bill_Amount_TLP\": \"Sales_amt_TLP\",\n", "                \"Total_Return_Amount_TLP\": \"Return_amt_TLP\",\n", "                \"qty\": \"Units\",\n", "                \"item_id\": \"SKUs\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    df4[\"IPO\"] = df4[\"Units\"] / df4[\"Total_orders\"].round(2)\n", "    df4[\"SPO\"] = df4[\"SKUs\"] / df4[\"Total_orders\"].round(2)\n", "    df4[\"IPO\"] = df4[\"IPO\"].round(2)\n", "    df4[\"SPO\"] = df4[\"SPO\"].round(2)\n", "    df4[\"Amount\"] = df4[\"Sales_amt\"] + df4[\"Return_amt\"]\n", "    # df4[\"Amount_TLP\"] = df4[\"Sales_amt_TLP\"] + df4[\"Return_amt_TLP\"]\n", "\n", "    df4[\"Amount\"] = df4[\"Amount\"].round(2)\n", "    # df4[\"Amount_TLP\"] = df4[\"Amount_TLP\"].round(2)\n", "    df4.drop(\n", "        [\"Sales_amt\", \"Return_amt\", \"Sales_amt_TLP\", \"Return_amt_TLP\"],\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "    df4.sort_values(\n", "        by=[\"dated\", \"transaction_type\"], ascending=[True, False], inplace=True\n", "    )\n", "    df4.transaction_type = df4.transaction_type.replace(\"Online_sale\", \"Online_sale *\")\n", "    df4.reset_index(drop=True, inplace=True)\n", "    # df4[\"margin\"] = df4[\"Amount\"] - df4[\"Amount_TLP\"]\n", "    # df4[\"margin\"] = df4[\"margin\"].round(2)\n", "    df4\n", "\n", "    df2.to_csv(\"summary_raw.csv\", index=False)\n", "    # Sending mailers\n", "    import jinja2\n", "    import pencilbox as pb\n", "    from jinja2 import Template\n", "\n", "    from_email = \"<EMAIL>\"\n", "    subject = \"Grofers Market Stores Sales Summary for %s\" % summary_date\n", "    #         to_email = email_list[email_list.outlet_id == outlet_id][\"Group Mail Id\"]\n", "    #         to_email=email_list[email_list.outlet_id==str(outlet_id)][\"Group Mail Id\"]\n", "    to_email = email_list[email_list[\"outlet_id\"] == str(outlet_id)][\n", "        \"Group Mail Id\"\n", "    ].tolist()\n", "\n", "    #         to_email=['<EMAIL>','<EMAIL>']\n", "\n", "    loader = jinja2.FileSystemLoader(\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/report/offline_dark_stores\"\n", "        # searchpath = \"\"\n", "        # searchpath=\"/home/<USER>/Development/airflow-dags/daggers/warehouse/misc/report/offline_dark_stores\"\n", "        #             searchpath=\"/home/<USER>/test/offline_dark_stores\"\n", "    )\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "    template = tmpl_environ.get_template(\"email_template.html\")\n", "    outlet_nam = df1[\"store_name\"].unique()\n", "\n", "    df12 = df4\n", "    if outlet_nam.shape[0] == 0:\n", "        continue\n", "    print(outlet_nam[0])\n", "    message_text = template.render(\n", "        products=df12.to_dict(orient=\"records\"),\n", "        today=today,\n", "        outlet=outlet_id,\n", "        outlet_name=outlet_nam[0],\n", "    )\n", "    to_email1 = [\"<EMAIL>\"]\n", "    to_email.extend(cc)\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject=subject,\n", "        html_content=message_text,\n", "        files=[\"summary_raw.csv\"],\n", "    )\n", "    print(to_email)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for outlet_id in x:\n", "\n", "#     try:\n", "\n", "#         df = pd.read_sql_query(\n", "#             sql=outlet_query,\n", "#             con=pos,\n", "#             params={\n", "#                 \"start_date\": start_date,\n", "#                 \"end_date\": end_date,\n", "#                 \"outlet_id\": outlet_id,\n", "#             },\n", "#         )\n", "#         df[\"Qty\"] = df[\"Qty\"].astype(\"int\")\n", "#         df1 = df.copy()\n", "#         df2 = df1.drop(\n", "#             [\n", "#                 \"invoice_type_id\",\n", "#                 \"Supply_City\",\n", "#                 \"Customer_City\",\n", "#                 \"Item_Type\",\n", "#                 \"Variant_Description\",\n", "#             ],\n", "#             axis=1,\n", "#         )\n", "#         df3 = df2.copy()\n", "#         df3.drop(\n", "#             [\n", "#                 \"Order_ID\",\n", "#                 \"Product_Name\",\n", "#                 \"Store_Name\",\n", "#                 \"Payment_Type\",\n", "#                 \"Discount\",\n", "#                 \"MRP\",\n", "#             ],\n", "#             axis=1,\n", "#             inplace=True,\n", "#         )\n", "\n", "#         df4 = (\n", "#             df3.groupby([\"outlet_id\", \"Dated\", \"Transaction_Type\"])\n", "#             .agg(\n", "#                 {\n", "#                     \"Invoice_ID\": \"nunique\",\n", "#                     \"Total_Gross_Bill_Amount\": \"sum\",\n", "#                     \"Total_Return_Amount\": \"sum\",\n", "#                     \"Qty\": \"sum\",\n", "#                     \"item_id\": \"nunique\",\n", "#                 }\n", "#             )\n", "#             .rename(\n", "#                 columns={\n", "#                     \"Invoice_ID\": \"Total_orders\",\n", "#                     \"Total_Gross_Bill_Amount\": \"Sales_amt\",\n", "#                     \"Total_Return_Amount\": \"Return_amt\",\n", "#                     \"Qty\": \"Units\",\n", "#                     \"item_id\": \"SKUs\",\n", "#                 }\n", "#             )\n", "#             .reset_index()\n", "#         )\n", "\n", "#         df4[\"IPO\"] = df4[\"Units\"] / df4[\"Total_orders\"].round(2)\n", "#         df4[\"SPO\"] = df4[\"SKUs\"] / df4[\"Total_orders\"].round(2)\n", "#         df4[\"IPO\"] = df4[\"IPO\"].round(2)\n", "#         df4[\"SPO\"] = df4[\"SPO\"].round(2)\n", "#         df4[\"Amount\"] = df4[\"Sales_amt\"] + df4[\"Return_amt\"]\n", "#         df4[\"Amount\"] = df4[\"Amount\"].round(2)\n", "#         df4.drop([\"Sales_amt\", \"Return_amt\"], axis=1, inplace=True)\n", "#         df4.sort_values(\n", "#             by=[\"Dated\", \"Transaction_Type\"], ascending=[True, False], inplace=True\n", "#         )\n", "#         df4.reset_index(drop=True, inplace=True)\n", "#         df4\n", "\n", "#         df2.to_csv(\"summary_raw.csv\", index=False)\n", "\n", "#         # Sending mailers\n", "#         import jinja2\n", "#         import pencilbox as pb\n", "#         from jinja2 import Template\n", "\n", "#         from_email = \"<EMAIL>\"\n", "#         subject = \"Offline Stores Sales Summary for %s\" % summary_date\n", "#         #         to_email = email_list[email_list.outlet_id == outlet_id][\"Group Mail Id\"]\n", "#         #         to_email=email_list[email_list.outlet_id==str(outlet_id)][\"Group Mail Id\"]\n", "#         to_email = email_list[email_list[\"outlet_id\"] == str(outlet_id)][\n", "#             \"Group Mail Id\"\n", "#         ].tolist()\n", "\n", "#         #         to_email=['<EMAIL>','<EMAIL>']\n", "\n", "#         loader = jinja2.FileSystemLoader(\n", "#             searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/report/offline_dark_stores\"\n", "#             #                          searchpath=\"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/misc/report/offline_dark_stores\"\n", "#             #             searchpath=\"/home/<USER>/test/offline_dark_stores\"\n", "#         )\n", "#         tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "#         template = tmpl_environ.get_template(\"email_template.html\")\n", "#         outlet_nam = df1[\"Store_Name\"].unique()\n", "#         cc = email_list[email_list[\"outlet_id\"] == str(1)][\"Group Mail Id\"].tolist()\n", "\n", "#         df12 = df4\n", "#         message_text = template.render(\n", "#             products=df12.to_dict(orient=\"records\"),\n", "#             today=today,\n", "#             outlet=outlet_id,\n", "#             outlet_name=outlet_nam[0],\n", "#         )\n", "\n", "#         pb.send_email(\n", "#             from_email,\n", "#             to_email,\n", "#             subject,\n", "#             bcc=[\"<EMAIL>\"],\n", "#             cc=cc,\n", "#             html_content=message_text,\n", "#             files=[\"summary_raw.csv\"],\n", "#         )\n", "#     #         print(\"sending for outlet: \" + str(outlet_id))\n", "\n", "#     except:\n", "#         message = \"failed for outlet_id - \" + str(outlet_id)\n", "#         print(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cc\n", "# End of file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}