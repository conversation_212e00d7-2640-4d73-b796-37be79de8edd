{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install haversine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import haversine as hs\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "with b2f as (\n", "WITH base AS \n", "(\n", "SELECT\n", "    fom.city_name AS city,\n", "    \n", "    CAST(bom.external_id AS TEXT) AS backend_merchant_id,\n", "    bom.name AS backend_merchant_name,\n", "    \n", "    CAST(fom.external_id AS TEXT) AS frontend_merchant_id,\n", "    fom.name AS frontend_merchant_name,\n", "    \n", "    oo.station,\n", "    oo.id AS order_id,\n", "    os.id AS suborder_id\n", "    \n", "FROM lake_oms_bifrost.oms_suborder AS os\n", "INNER JOIN lake_oms_bifrost.oms_order AS oo\n", "    ON os.order_id = oo.id\n", "    AND os.install_ts >= '2019-07-02 6:30:00'\n", "    AND os.type = 'RetailSuborder'\n", "    AND os.current_status <> 'CANCELLED'\n", "    AND oo.type = 'RetailForwardOrder'\n", "INNER JOIN lake_oms_bifrost.oms_merchant AS bom\n", "    ON os.backend_merchant_id = bom.id\n", "    AND bom.virtual_merchant_type <> 'superstore_merchant'\n", "INNER JOIN lake_oms_bifrost.oms_merchant AS fom\n", "    ON oo.merchant_id = fom.id\n", "    AND fom.virtual_merchant_type = 'superstore_merchant'\n", "GROUP BY 1,2,3,4,5,6,7,8\n", "ORDER BY 1,3,5,6,7,8\n", ")\n", "\n", "SELECT  \n", "    city,\n", "    backend_merchant_id,\n", "    backend_merchant_name,\n", "    frontend_merchant_id,\n", "    frontend_merchant_name,\n", "    upper(replace(station,'station','') ) as station,\n", "    cms.outlet_id,\n", "    rco.name as console_outlet_name,\n", "    rco.facility_id,\n", "    cf.name as facility\n", "    \n", "FROM base b\n", "inner join lake_retail.console_outlet_cms_store cms \n", "on cms.cms_store = b.backend_merchant_id \n", "join lake_retail.console_outlet rco on rco.id=cms.outlet_id\n", "join lake_crates.facility cf on cf.id=rco.facility_id\n", "where cms.cms_update_active=1\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 1,3,5,6\n", ")\n", "\n", "select facility_id,facility_name, avg(lat) as lat, avg(lon) as lon\n", "from \n", "((select distinct b2f.facility_id, b2f.facility as facility_name, avg(latitude) as lat, avg(longitude) as lon\n", "from lake_cms.view_gr_merchant l\n", "inner join b2f on b2f.backend_merchant_id = l.id\n", "group by 1,2)\n", "union\n", "(select distinct b2f.facility_id, b2f.facility as facility_name, avg(latitude) as lat, avg(longitude) as lon\n", "from lake_cms.view_gr_merchant l\n", "inner join b2f on b2f.frontend_merchant_id = l.id\n", "group by 1,2))\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\" select distinct co.facility_id as sender, cot.facility_id as receiver \n", "from \n", "metrics.esto_details \n", "join lake_retail.console_outlet co on co.id = sender_outlet_id\n", "join lake_retail.console_outlet cot on cot.id = receiving_outlet_id and cot.business_type_id = 7\n", "where created_date >= current_date-30\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["latlondata = pd.read_sql_query(\n", "    sql=query1, con=pb.get_connection(\"[Warehouse] Redshift\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sendrecdata = pd.read_sql_query(\n", "    sql=query2, con=pb.get_connection(\"[Warehouse] Redshift\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sendrecdata.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["latlondata.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sendrecdata.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = sendrecdata.merge(\n", "    la<PERSON><PERSON><PERSON>, left_on=[\"sender\"], right_on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = x.merge(la<PERSON><PERSON><PERSON>, left_on=[\"receiver\"], right_on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sendrecdata = y[(y[\"facility_id_y\"].notnull()) & (y[\"facility_id_x\"].notnull())][\n", "    [\"sender\", \"receiver\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sendrecdata.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def reciever_comb(arr):\n", "    comb = []\n", "    for i in range(0, len(arr)):\n", "        for j in range(0, i):\n", "            comb.append([arr[i], arr[j]])\n", "    return comb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i in sendrecdata[\"sender\"].unique():\n", "#     arr = sendrecdata[sendrecdata[\"sender\"]==i].receiver.unique()\n", "#     reciever_comb(arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame([], columns=[\"parent\", \"sender\", \"receiver\", \"distance\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["latlondata.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 0\n", "max_dist = 0\n", "for i in sendrecdata.index:\n", "    send = sendrecdata[\"sender\"][i]\n", "    send_lat = latlondata.loc[\n", "        latlondata[\"facility_id\"] == sendrecdata[\"sender\"][i], \"lat\"\n", "    ].iloc[0]\n", "    send_lon = latlondata.loc[\n", "        latlondata[\"facility_id\"] == sendrecdata[\"sender\"][i], \"lon\"\n", "    ].iloc[0]\n", "    rec = sendrecdata[\"receiver\"][i]\n", "    rec_lat = latlondata.loc[\n", "        latlondata[\"facility_id\"] == sendrecdata[\"receiver\"][i], \"lat\"\n", "    ].iloc[0]\n", "    rec_lon = latlondata.loc[\n", "        latlondata[\"facility_id\"] == sendrecdata[\"receiver\"][i], \"lon\"\n", "    ].iloc[0]\n", "    send_loc = (send_lat, send_lon)\n", "    rec_loc = (rec_lat, rec_lon)\n", "    dist = hs.haversine(send_loc, rec_loc)\n", "    df = pd.concat(\n", "        [\n", "            df,\n", "            pd.DataFrame(\n", "                {\n", "                    \"parent\": [send],\n", "                    \"sender\": [send],\n", "                    \"receiver\": [rec],\n", "                    \"distance\": [dist],\n", "                }\n", "            ),\n", "        ],\n", "        axis=0,\n", "    )\n", "#   print('Sender ID: ',send,'Sender Coordinates:',send_lat,send_lon, 'Receiver ID:', rec,'Receiver Coordinates',rec_lat, rec_lon, 'Distance:', dist,'KM')\n", "\n", "# r = requests.get(f\"http://router.project-osrm.org/route/v1/car/{send_lon},{send_lat};{rec_lon},{rec_lat}?overview=false\"\"\")\n", "# routes = json.loads(r.content)\n", "# route_1 = routes.get(\"routes\")[0]\n", "# print('Duration in minutes: ',route_1[\"duration\"]/60)\n", "#     count += 1\n", "#     if dist > max_dist:\n", "#         max_dist = dist\n", "# print(count)\n", "# print(max_dist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in sendrecdata[\"sender\"].unique():\n", "    arr = sendrecdata[sendrecdata[\"sender\"] == i].receiver.unique()\n", "    combs = reciever_comb(arr)\n", "    if len(combs) > 0:\n", "        for j in range(0, len(combs)):\n", "            send = combs[j][0]\n", "            send_lat = latlondata.loc[latlondata[\"facility_id\"] == send, \"lat\"].iloc[0]\n", "            send_lon = latlondata.loc[latlondata[\"facility_id\"] == send, \"lon\"].iloc[0]\n", "            rec = combs[j][1]\n", "            rec_lat = latlondata.loc[latlondata[\"facility_id\"] == rec, \"lat\"].iloc[0]\n", "            rec_lon = latlondata.loc[latlondata[\"facility_id\"] == rec, \"lon\"].iloc[0]\n", "            send_loc = (send_lat, send_lon)\n", "            rec_loc = (rec_lat, rec_lon)\n", "            dist = hs.haversine(send_loc, rec_loc)\n", "            df = pd.concat(\n", "                [\n", "                    df,\n", "                    pd.DataFrame(\n", "                        {\n", "                            \"parent\": [i],\n", "                            \"sender\": [send],\n", "                            \"receiver\": [rec],\n", "                            \"distance\": [dist],\n", "                        }\n", "                    ),\n", "                ],\n", "                axis=0,\n", "            )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = df.merge(\n", "    latlondata[[\"facility_id\", \"facility_name\"]],\n", "    left_on=[\"sender\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "df2 = df1.merge(\n", "    latlondata[[\"facility_id\", \"facility_name\"]],\n", "    left_on=[\"receiver\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = df2.rename(\n", "    columns={\"facility_name_x\": \"sender_name\", \"facility_name_y\": \"receiver_name\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = df2[[\"parent\", \"sender\", \"sender_name\", \"receiver\", \"receiver_name\", \"distance\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df2, \"164AOqDAwJRlL-xtD34pfvW0GcQKH9wp5o7o_ZwYd36U\", \"Distances\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}