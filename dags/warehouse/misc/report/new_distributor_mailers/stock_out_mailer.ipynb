{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import warnings\n", "from datetime import date, datetime, timedelta\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "mailer_filename = f\"stock_out_report_{yesterday}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_distributors = [\n", "    \"HOT\",\n", "    \"CLOUD\",\n", "    \"MODI\",\n", "    \"SSC\",\n", "    \"LT\",\n", "    \"FNV\",\n", "    \"LA\",\n", "    \"NM\",\n", "    \"CROF\",\n", "    \"CC\",\n", "]\n", "\n", "old = [\" \" + x + \" \" for x in old_distributors]\n", "old.extend([\"\\(\" + x + \"\\)\" for x in old_distributors])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new = [\n", "    \"Outlet ID\",\n", "    \"Source Entity Vendor Name\",\n", "    \"Source Entity Vendor ID\",\n", "    \"Destination Entity Vendor ID\",\n", "    \"Merchant Name\",\n", "    \"Invoice ID\",\n", "    \"Item ID\",\n", "    \"UPC\",\n", "    \"Variant ID\",\n", "    \"Product Name\",\n", "    \"Item Type\",\n", "    \"HSN Code\",\n", "    \"MRP\",\n", "    \"Grammage\",\n", "    \"Quantity\",\n", "    \"IGST(%)\",\n", "    \"CGST(%)\",\n", "    \"SGST(%)\",\n", "    \"CESS(%)\",\n", "    \"IGST Value\",\n", "    \"CGST Value\",\n", "    \"SGST Value\",\n", "    \"CESS Value\",\n", "    \"Total Basic Amount\",\n", "    \"POS Timestamp\",\n", "    \"Cloud Landing Price\",\n", "    \"Landing Price\",\n", "    \"Net Worth\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select *\n", "from metrics.finance_stock_out_report\n", "where cin = 'U74110DL2013PTC248660'\n", "and date(pos_timestamp) = current_date - 1\n", "\"\"\"\n", "\n", "esto = pd.read_sql_query(sql, redshift_con)\n", "esto.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not esto.empty:\n", "    assert len(esto[esto[\"source_entity_vendor_name\"].str.contains(\"|\".join(old))]) == 0\n", "    assert len(esto[esto[\"merchant_name\"].str.contains(\"|\".join(old))]) == 0\n", "\n", "    esto.drop(columns=[\"cin\", \"gst_tin\", \"legal_name\", \"etl_timestamp\"], inplace=True)\n", "    esto.columns = new\n", "    esto.to_csv(mailer_filename, index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    cc = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Stock Out Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> Please find the attached TAMS GLOBAL PRIVATE LIMITED Stock Out Report for {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(\n", "        from_email, to_email, subject, html_content, cc=cc, files=[mailer_filename]\n", "    )\n", "else:\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Stock Out Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> No TAMS GLOBAL PRIVATE LIMITED Stock Out happened on {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}