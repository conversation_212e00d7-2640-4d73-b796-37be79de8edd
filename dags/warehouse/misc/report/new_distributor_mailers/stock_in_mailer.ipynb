{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import warnings\n", "from datetime import date, datetime, timedelta\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "mailer_filename = f\"stock_in_report_{yesterday}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# old_distributors = [\n", "#     \"HOT\",\n", "#     \"CLOUD\",\n", "#     \"MODI\",\n", "#     \"SSC\",\n", "#     \"LT\",\n", "#     \"FNV\",\n", "#     \"LA\",\n", "#     \"NM\",\n", "#     \"CROF\",\n", "#     \"CC\",\n", "# ]\n", "\n", "# old = [\" \" + x + \" \" for x in old_distributors]\n", "# old.extend([\"\\(\" + x + \"\\)\" for x in old_distributors])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new = [\n", "    \"Stock Update ID\",\n", "    \"Purchase Number\",\n", "    \"PO Type\",\n", "    \"Vendor GST\",\n", "    \"Vendor Supply Location\",\n", "    \"Issue Date\",\n", "    \"Manufacture Date\",\n", "    \"Item ID\",\n", "    \"UPC ID\",\n", "    \"Variant ID\",\n", "    \"Product Name\",\n", "    \"Item Type\",\n", "    \"Manufacturer Name\",\n", "    \"PTR Flag\",\n", "    \"Quantity\",\n", "    \"Return Quantity\",\n", "    \"Actual GRN Quantity\",\n", "    \"MRP\",\n", "    \"Grammage\",\n", "    \"Outlet ID\",\n", "    \"Entity Vendor ID\",\n", "    \"En<PERSON><PERSON> Vendor Name\",\n", "    \"City Name\",\n", "    \"Stock In Date\",\n", "    \"Created At\",\n", "    \"Update Type\",\n", "    \"Expiry Date\",\n", "    \"Vendor ID\",\n", "    \"Merchant Name\",\n", "    \"Merchant Invoice ID\",\n", "    \"Created By\",\n", "    \"Cost Price\",\n", "    \"CGST(%)\",\n", "    \"SGST(%)\",\n", "    \"IGST(%)\",\n", "    \"CESS(%)\",\n", "    \"Landing Price\",\n", "    \"PO Landing Price\",\n", "    \"Purchase Order ID\",\n", "    \"GRN ID\",\n", "    \"GRN Amount\",\n", "    \"Net Cost Price\",\n", "    \"Net Landing Price\",\n", "    \"Net PO Landing Price\",\n", "    \"Total Cost Price\",\n", "    \"Tax\",\n", "    \"PL Flag\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "select *\n", "from metrics.finance_stock_in_report\n", "where cin = 'U74110DL2013PTC248660'\n", "and date(created_at) = current_date - 1\n", "\"\"\"\n", "\n", "stockin = pd.read_sql_query(sql, redshift_con)\n", "stockin.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not stockin.empty:\n", "    # assert len(stockin[stockin[\"merchant_name\"].str.contains(\"|\".join(old))]) == 0\n", "    # assert len(stockin[stockin[\"entity_vendor_name\"].str.contains(\"|\".join(old))]) == 0\n", "\n", "    stockin.drop(\n", "        columns=[\"cin\", \"gst_tin\", \"legal_name\", \"etl_timestamp\"], inplace=True\n", "    )\n", "    stockin.columns = new\n", "    stockin.to_csv(mailer_filename, index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    cc = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Stock In Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> Please find the attached TAMS GLOBAL PRIVATE LIMITED Stock In Report for {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(\n", "        from_email, to_email, subject, html_content, cc=cc, files=[mailer_filename]\n", "    )\n", "else:\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Stock In Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> No TAMS GLOBAL PRIVATE LIMITED Stock In happened on {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}