{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import warnings\n", "from datetime import date, datetime, timedelta\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "mailer_filename = f\"sales_report_{yesterday}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_distributors = [\n", "    \"HOT\",\n", "    \"CLOUD\",\n", "    \"MODI\",\n", "    \"SSC\",\n", "    \"LT\",\n", "    \"FNV\",\n", "    \"LA\",\n", "    \"NM\",\n", "    \"CROF\",\n", "    \"CC\",\n", "]\n", "\n", "old = [\" \" + x + \" \" for x in old_distributors]\n", "old.extend([\"\\(\" + x + \"\\)\" for x in old_distributors])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new = [\n", "    \"Invoice ID\",\n", "    \"Order ID\",\n", "    \"POS Timestamp\",\n", "    \"Outlet ID\",\n", "    \"En<PERSON><PERSON> Vendor Name\",\n", "    \"Source Entity Vendor ID\",\n", "    \"Seller Company\",\n", "    \"Customer Name\",\n", "    \"Supply City\",\n", "    \"Customer City\",\n", "    \"Item ID\",\n", "    \"Variant ID\",\n", "    \"Product Name\",\n", "    \"Item Type\",\n", "    \"HSN Code\",\n", "    \"PL Flag\",\n", "    \"Variant Description\",\n", "    \"VAT % Cal\",\n", "    \"CST % Cal\",\n", "    \"IGST(%)\",\n", "    \"CGST(%)\",\n", "    \"SGST(%)\",\n", "    \"CESS(%)\",\n", "    \"Quantity\",\n", "    \"MRP\",\n", "    \"Discount\",\n", "    \"Selling Price\",\n", "    \"VAT\",\n", "    \"CST\",\n", "    \"IGST Value\",\n", "    \"CGST Value\",\n", "    \"SGST Value\",\n", "    \"CESS Value\",\n", "    \"Total Tax\",\n", "    \"Total Gross Bill Amount\",\n", "    \"Total Return Amount\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "select *\n", "from metrics.finance_sales_report\n", "where cin = 'U74110DL2013PTC248660'\n", "and date(pos_timestamp) = current_date - 1\n", "\"\"\"\n", "\n", "sales = pd.read_sql_query(sql, redshift_con)\n", "sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not sales.empty:\n", "    assert len(sales[sales[\"entity_vendor_name\"].str.contains(\"|\".join(old))]) == 0\n", "\n", "    sales.drop(columns=[\"cin\", \"gst_tin\", \"id\", \"etl_timestamp\"], inplace=True)\n", "    sales.columns = new\n", "    sales.to_csv(mailer_filename, index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    cc = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Sales Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> Please find the attached TAMS GLOBAL PRIVATE LIMITED Sales Report for {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(\n", "        from_email, to_email, subject, html_content, cc=cc, files=[mailer_filename]\n", "    )\n", "\n", "else:\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"Sales Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> No TAMS GLOBAL PRIVATE LIMITED Sales happened on {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}