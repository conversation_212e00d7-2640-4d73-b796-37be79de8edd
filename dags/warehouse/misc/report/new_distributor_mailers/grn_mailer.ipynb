{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import warnings\n", "from datetime import date, datetime, timedelta\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "mailer_filename = f\"grn_report_{yesterday}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# old_distributors = [\n", "#     \"HOT\",\n", "#     \"CLOUD\",\n", "#     \"MODI\",\n", "#     \"SSC\",\n", "#     \"LT\",\n", "#     \"FNV\",\n", "#     \"LA\",\n", "#     \"NM\",\n", "#     \"CROF\",\n", "#     \"CC\",\n", "# ]\n", "\n", "# old = [\" \" + x + \" \" for x in old_distributors]\n", "# old.extend([\"\\(\" + x + \"\\)\" for x in old_distributors])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new = [\n", "    \"GRN ID\",\n", "    \"Vendor ID\",\n", "    \"Destination Entity Vendor ID\",\n", "    \"Net Amount\",\n", "    \"Invoice Discount\",\n", "    \"Freight Charges\",\n", "    \"PO Number\",\n", "    \"Merchant Invoice ID\",\n", "    \"Merchant Name\",\n", "    \"Valid PO\",\n", "    \"Source Type\",\n", "    \"Outlet ID\",\n", "    \"En<PERSON><PERSON> Vendor Name\",\n", "    \"Created At\",\n", "    \"TCS Amount\",\n", "    \"Total Invoice Amount\",\n", "    \"Total Vendor Invoice Value\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "select *\n", "from metrics.finance_grn_report\n", "where cin = 'U74110DL2013PTC248660'\n", "and date(created_at) = current_date - 1\n", "\"\"\"\n", "\n", "grn = pd.read_sql_query(sql, redshift_con)\n", "grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not grn.empty:\n", "    # assert len(grn[grn[\"merchant_name\"].str.contains(\"|\".join(old))]) == 0\n", "    # assert len(grn[grn[\"entity_vendor_name\"].str.contains(\"|\".join(old))]) == 0\n", "\n", "    grn.drop(columns=[\"cin\", \"gst_tin\", \"legal_name\", \"etl_timestamp\"], inplace=True)\n", "    grn.columns = new\n", "    grn.to_csv(mailer_filename, index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    cc = [\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"GRN Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> Please find the attached TAMS GLOBAL PRIVATE LIMITED GRN Report for {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(\n", "        from_email, to_email, subject, html_content, cc=cc, files=[mailer_filename]\n", "    )\n", "else:\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = f\"GRN Report for {yesterday}\"\n", "    html_content = f\"\"\"Hi, \n", "    <br><br> No TAMS GLOBAL PRIVATE LIMITED GRN happened on {yesterday}. \n", "    <br>\n", "    <br> Thanks,\n", "    <br> Data Warehouse\n", "    \"\"\"\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}