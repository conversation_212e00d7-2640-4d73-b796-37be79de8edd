{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos = pb.get_connection(\"[Replica] POS\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz)).strftime(\"%Y-%m-%d\")\n", "today\n", "\n", "start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=1)).date()\n", "summary_date = start_date.strftime(\"%Y-%m-%d\")\n", "summary_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1m7UY7FQNhNGshlDiiNDsdlYuq-gVvC_W_tgMdtiN-F4\"\n", "outlets = pb.from_sheets(sheet_id, \"offline_stores\")\n", "outlets = outlets[[\"outlet_id\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"outlet_id\"], inplace=True)\n", "outlets = list(outlets.outlet_id.unique())\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "\n", "sheet_data = pb.from_sheets(sheet_id, \"offline_stores\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc_emails = sheet_data[\"cc_mail\"]\n", "email_list = sheet_data[[\"Group Mail Id\", \"outlet_id\", \"outlet_name\"]]\n", "email_list[\"Group Mail Id\"] = email_list[\"Group Mail Id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc = cc_emails.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in outlets:\n", "    try:\n", "        outlet = int(outlet)\n", "        x.append(outlet)\n", "    except:\n", "        pass\n", "print(x)\n", "# outlets\n", "email_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales = \"\"\"\n", "(select abc.*,\n", "(CASE \n", "WHEN a.offer_type ILIKE '%%flat%%' and abc.transaction_type ilike '%%sale%%' THEN COALESCE((a.bf_margin * (abc.mrp - a.sp) * abc.quantity),0.0)\n", "WHEN a.offer_type ILIKE '%%flat%%' and abc.transaction_type ilike '%%return%%' THEN COALESCE(-(a.bf_margin * (abc.mrp - a.sp) * abc.quantity),0.0)\n", "WHEN a.offer_type NOT ILIKE '%%flat%%' and abc.transaction_type ilike '%%sale%%' THEN COALESCE((a.bf_margin * abc.mrp_at_pid), 0.0) \n", "WHEN a.offer_type NOT ILIKE '%%flat%%' and abc.transaction_type ilike '%%return%%' THEN COALESCE(-(a.bf_margin * abc.mrp_at_pid), 0.0) \n", "END) AS brand_fund\n", "from\n", "(with fs_gmv as (SELECT\n", "     date(p.pos_timestamp + interval '5.5 hours') AS dated,\n", "     p.outlet_id,\n", "     r.name,\n", "     (case \n", "        when l2.name  = '<PERSON><PERSON><PERSON>' then 'Lucknow'\n", "        when l2.name  = 'BIJNOR' then 'Math<PERSON>'\n", "        when l2.name  = '<PERSON><PERSON>' then 'Mumbai'\n", "        when l2.name  = 'Bahadurgarh' then 'HR-NCR'\n", "        when l2.name  = '<PERSON><PERSON>' then 'HR-NCR'\n", "        when l2.name  = 'Gurgaon' then 'HR-NCR'\n", "        when l2.name  = 'Zirakpur' then 'chandigarh'\n", "        when l2.name  = 'Gonda' then 'Lucknow'\n", "        WHEN l2.name  = 'Jamshedpur' then 'Kolkata'\n", "        when l2.name = 'Kota' then 'Jaipur'\n", "        when l2.name = '<PERSON><PERSON><PERSON><PERSON><PERSON>' then 'Lucknow'\n", "        when l2.name = 'Kurukshetra' then 'HR-NCR'\n", "        when l2.name = 'Aligarh' then 'UP-NCR'\n", "        when l2.name = 'Bhiwadi' then 'Jaipur'\n", "        when l2.name = '<PERSON><PERSON><PERSON><PERSON>' then 'Math<PERSON>'\n", "        when l2.name = 'Prakasam' then 'Hyderabad'\n", "        when l2.name = 'Markapuram' then 'Hyderabad'\n", "        else l2.name end) as Customer_City,\n", "     p.invoice_id AS invoice_id,\n", "     CASE WHEN p.invoice_type_id in (9,10) THEN  p.offline_order_id else p.grofers_order_id  end as order_id, \n", "     pi.offer_reference_id,\n", "     (CASE\n", "         WHEN p.invoice_type_id = 1 THEN 'Online_sale'\n", "         WHEN p.invoice_type_id = 2 THEN 'Online_return'\n", "         WHEN p.invoice_type_id = 9 THEN 'Offline_sale'\n", "         WHEN p.invoice_type_id = 10 THEN 'Offline_return'         \n", "         else 'other' \n", "         END) AS transaction_type ,\n", "         item_id,pi.variant_id,\n", "     rp.name AS product_name,\n", "     rp.variant_uom_text as variant_description,\n", "     pi.quantity,\n", "     pi.unit_rate as mrp,\n", "     pi.selling_price,\n", "    \n", "    (CASE\n", "         WHEN p.invoice_type_id in (1,9) THEN pi.selling_price*pi.quantity\n", "     ELSE 0\n", "         END) AS Total_Gross_Bill_Amount,\n", "    (CASE\n", "         WHEN p.invoice_type_id in (2,10) THEN pi.selling_price*pi.quantity\n", "     ELSE 0\n", "     END) AS Total_Return_Amount\n", " FROM\n", " lake_pos.pos_invoice_product_details pi\n", " INNER JOIN\n", " lake_pos.pos_invoice p ON p.id = pi.invoice_id\n", " INNER JOIN\n", " lake_retail.console_outlet r ON r.id = p.outlet_id\n", " INNER JOIN\n", " lake_retail.console_location l1 ON l1.id = r.tax_location_id\n", " INNER JOIN\n", " lake_retail.console_location l2 ON l2.id = p.customer_city\n", " INNER JOIN\n", " lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", " WHERE\n", "p.invoice_type_id in (1,2,9,10)\n", "and p.outlet_id in ({outlet_id})\n", "and  r.business_type_id=8\n", "and date(p.pos_timestamp + interval '5.5 hours') = %(summary_date)s\n", "and pi.store_offer_id is null),\n", "\n", "base_pid as (SELECT P.ID AS PID, item_id\n", "from lake_cms.gr_product P\n", "-- from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN lake_rpc.item_product_mapping  rpc ON rpc.product_id = p.id\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "\n", "\n", "pids as (with pids as (SELECT\n", "distinct       \n", "       so.offer_id,\n", "       (case when (so.is_offer_active = 'true' and r.product_id is not NULL) then 'active' else 'not active' end) as offer_status,\n", "       o.field_val AS item_id,\n", "       offer.offer_reference_id,\n", "       r.product_id as pid,\n", "       date(rh.created_at) as update_date,\n", "       rh.previous_product_id as lapsed_pid\n", "FROM lake_offer_master.store_offer so \n", "INNER JOIN lake_offer_master.offer_field o ON o.offer_id=so.offer_id\n", "INNER JOIN lake_offer_master.offer_type_field of ON of.id=o.offer_type_field_id\n", "inner join lake_offer_master.offer offer on offer.id=so.offer_id\n", "inner join lake_retail.console_outlet co on co.id = so.store_id\n", "left join lake_rpc.item_product_mapping r on r.offer_id=offer.offer_reference_id\n", "left join lake_rpc.item_product_mapping_history rh on rh.previous_offer_id=offer.offer_reference_id\n", "where of.field_type LIKE '%%item_id%%'\n", "  AND co.business_type_id = 8)\n", "  \n", "  select offer_id, offer_status, item_id, offer_reference_id,\n", "  case when pid is not null then pid else lapsed_pid end as pid\n", "  from pids),\n", " \n", "order_date as (select sub.id as sub_id, date(convert_timezone('Asia/Kolkata',oms.install_ts)) as Od_date, oms.id \n", "from lake_oms_bifrost.oms_order oms\n", "left join lake_oms_bifrost.oms_suborder sub on oms.id = sub.order_id),\n", "\n", "sales as (select distinct case when fs.transaction_type ilike '%%offline%%' then fs.dated else od_date end as order_date, fs.*,\n", "case when fs.offer_reference_id is null then cast(base_pid.PID as int) else cast(pids.pid as int) end as pro_id,\n", "fs.quantity*il.weighted_lp as weighted_lp_sum\n", "from fs_gmv fs\n", "left join base_pid on fs.item_id = base_pid.item_id\n", "left join pids on fs.offer_reference_id = pids.offer_reference_id and fs.item_id = pids.item_id\n", "left join order_date o on fs.order_id = o.sub_id\n", "left join (select iil.invoice_id,iil.grofers_order_id,iil.variant_id,avg(iil.transaction_lp) transaction_lp,avg(iil.weighted_lp) weighted_lp \n", "from lake_ims.ims_inventory_log iil\n", " where date(iil.pos_timestamp + interval '5.5 hours') = %(summary_date)s\n", " and id>1225614316 and outlet_id in ({outlet_id})\n", " group by 1,2,3)il on fs.invoice_id = il.invoice_id and fs.order_id = il.grofers_order_id and fs.variant_id = il.variant_id\n", "),\n", "\n", "it as (\n", "with it_pd as\n", "(select distinct\n", "  item_id,\n", "  product_id,\n", "  flag,\n", "  offer_type_identifier,\n", "  offer_type_tag,\n", "  updated_on,multiplier,\n", "  max(updated_on) over (partition by product_id) as last_updated,\n", "  min(updated_on) over (partition by item_id,product_id) as updated_at\n", "from it_pd_log\n", "where item_id is not null\n", ")\n", "select distinct\n", "  flag, item_id, multiplier,\n", "  offer_type_tag,\n", "  product_id, offer_type_identifier\n", "from it_pd i\n", "where last_updated = updated_on and offer_type_identifier not ilike '%%none%%'\n", "and flag not ilike '%%base%%'\n", ")\n", "select  \n", "order_date as order_placed_date,dated as billing_date,outlet_id,name as outlet_name,customer_city,sales.invoice_id,order_id,offer_reference_id,transaction_type,listagg(sales.item_id,' + ') as item_id,listagg(sales.product_name,' + ') as product_name,sum(quantity) as quantity,sum(mrp) as mrp,sum(selling_price) as selling_price,\n", "sum(Total_Gross_Bill_Amount) Total_Gross_Bill_Amount,sum(total_return_amount) total_return_amount,pro_id as product_id,\n", "(case \n", "  when it.offer_type_tag = 'combo' then 'COMBO'\n", "  when it.offer_type_tag = 'non_combo' then 'NON_COMBO'\n", "  else 'NONE' end) as offer_type_tag,\n", "(sum(sales.quantity)/sum(it.multiplier)) as actual_quantity,\n", "-- sales.mrp*sales.quantity as mrp_at_pid,\n", "sum(case when transaction_type ilike '%%sale%%' then sales.mrp*sales.quantity else 0-(sales.mrp*sales.quantity) end) as mrp_at_pid,sum(sales.weighted_lp_sum) as weighted_lp_sum \n", "from sales\n", "left join it on sales.pro_id = it.product_id and sales.item_id = it.item_id\n", "group by 1,2,3,4,5,6,7,8,9,17,18\n", ") as abc\n", "left join all_margins a ON lower(abc.customer_city) = lower(CASE\n", "                                                     WHEN a.city ILIKE 'bangalore' THEN 'bengaluru'                                                     ELSE a.city\n", "                                                 END) and abc.product_id = a.pid\n", "AND date(abc.order_placed_date) BETWEEN date(a.start_date) AND date(a.end_date)\n", "where offer_type_tag = 'COMBO')\n", "union all\n", "-- #######################################################################################################################################\n", "(\n", "select order_date as order_placed_date,dated as billing_date,outlet_id,name as outlet_name,customer_city,invoice_id,order_id,offer_reference_id,transaction_type,cast(item_id as varchar) as item_id,product_name,quantity,mrp,selling_price,\n", "Total_Gross_Bill_Amount,total_return_amount,product_id,offer_type_tag,act_qua as actual_quantity,mrp_at_pid,weight_lp_sum as weighted_lp,bf_margin as brand_fund\n", "from \n", "(with fs_gmv as (SELECT\n", "     date(p.pos_timestamp + interval '5.5 hours') AS dated,\n", "     p.outlet_id,\n", "     r.name,\n", "     (case \n", "        when l2.name  = '<PERSON><PERSON><PERSON>' then 'Lucknow'\n", "        when l2.name  = 'BIJNOR' then 'Math<PERSON>'\n", "        when l2.name  = '<PERSON><PERSON>' then 'Mumbai'\n", "        when l2.name  = 'Bahadurgarh' then 'HR-NCR'\n", "        when l2.name  = '<PERSON><PERSON>' then 'HR-NCR'\n", "        when l2.name  = 'Gurgaon' then 'HR-NCR'\n", "        when l2.name  = 'Zirakpur' then 'chandigarh'\n", "        when l2.name  = 'Gonda' then 'Lucknow'\n", "        WHEN l2.name  = 'Jamshedpur' then 'Kolkata'\n", "        when l2.name = 'Kota' then 'Jaipur'\n", "        when l2.name = '<PERSON><PERSON><PERSON><PERSON><PERSON>' then 'Lucknow'\n", "        when l2.name = 'Kurukshetra' then 'HR-NCR'\n", "        when l2.name = 'Aligarh' then 'UP-NCR'\n", "        when l2.name = 'Bhiwadi' then 'Jaipur'\n", "        when l2.name = '<PERSON><PERSON><PERSON><PERSON>' then 'Math<PERSON>'\n", "        when l2.name = 'Prakasam' then 'Hyderabad'\n", "        when l2.name = 'Markapuram' then 'Hyderabad'\n", "        else l2.name end) as Customer_City,\n", "     p.invoice_id AS invoice_id,\n", "     CASE WHEN p.invoice_type_id in (9,10) THEN  p.offline_order_id else p.grofers_order_id  end as order_id, \n", "     pi.offer_reference_id,\n", "\n", "     (CASE\n", "         WHEN p.invoice_type_id = 1 THEN 'Online_sale'\n", "         WHEN p.invoice_type_id = 2 THEN 'Online_return'\n", "         WHEN p.invoice_type_id = 9 THEN 'Offline_sale'\n", "         WHEN p.invoice_type_id = 10 THEN 'Offline_return'         \n", "         else 'other' \n", "         END) AS transaction_type ,\n", "         item_id,pi.variant_id,\n", "     rp.name AS product_name,\n", "     rp.variant_uom_text as variant_description,\n", "     pi.quantity,\n", "     pi.unit_rate as mrp,\n", "     pi.selling_price,\n", "    \n", "    (CASE\n", "         WHEN p.invoice_type_id in (1,9) THEN pi.selling_price*pi.quantity\n", "     ELSE 0\n", "         END) AS Total_Gross_Bill_Amount,\n", "    (CASE\n", "         WHEN p.invoice_type_id in (2,10) THEN pi.selling_price*pi.quantity\n", "     ELSE 0\n", "     END) AS Total_Return_Amount\n", " FROM\n", " lake_pos.pos_invoice_product_details pi\n", " INNER JOIN\n", " lake_pos.pos_invoice p ON p.id = pi.invoice_id\n", " INNER JOIN\n", " lake_retail.console_outlet r ON r.id = p.outlet_id\n", " INNER JOIN\n", " lake_retail.console_location l1 ON l1.id = r.tax_location_id\n", " INNER JOIN\n", " lake_retail.console_location l2 ON l2.id = p.customer_city\n", " INNER JOIN\n", " lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", " WHERE\n", " \n", " p.invoice_type_id in (1,2,9,10)\n", " \n", "and p.outlet_id in ({outlet_id})\n", "and  r.business_type_id=8\n", "and date(p.pos_timestamp + interval '5.5 hours') = %(summary_date)s\n", "and pi.store_offer_id is null),\n", "\n", "base_pid as (SELECT P.ID AS PID, item_id\n", "from lake_cms.gr_product P\n", "-- from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN lake_rpc.item_product_mapping  rpc ON rpc.product_id = p.id\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "\n", "\n", "pids as (with pids as (SELECT\n", "distinct       \n", "       so.offer_id,\n", "       (case when (so.is_offer_active = 'true' and r.product_id is not NULL) then 'active' else 'not active' end) as offer_status,\n", "       o.field_val AS item_id,\n", "       offer.offer_reference_id,\n", "       r.product_id as pid,\n", "       date(rh.created_at) as update_date,\n", "       rh.previous_product_id as lapsed_pid\n", "FROM lake_offer_master.store_offer so \n", "INNER JOIN lake_offer_master.offer_field o ON o.offer_id=so.offer_id\n", "INNER JOIN lake_offer_master.offer_type_field of ON of.id=o.offer_type_field_id\n", "inner join lake_offer_master.offer offer on offer.id=so.offer_id\n", "inner join lake_retail.console_outlet co on co.id = so.store_id\n", "left join lake_rpc.item_product_mapping r on r.offer_id=offer.offer_reference_id\n", "left join lake_rpc.item_product_mapping_history rh on rh.previous_offer_id=offer.offer_reference_id\n", "where of.field_type LIKE '%%item_id%%'\n", "  AND co.business_type_id = 8)\n", "  \n", "  select offer_id, offer_status, item_id, offer_reference_id,\n", "  case when pid is not null then pid else lapsed_pid end as pid\n", "  from pids),\n", "\n", "order_date as (select sub.id as sub_id, date(convert_timezone('Asia/Kolkata',oms.install_ts)) as Od_date, oms.id \n", "from lake_oms_bifrost.oms_order oms\n", "left join lake_oms_bifrost.oms_suborder sub on oms.id = sub.order_id),\n", "\n", "sales as (select distinct case when fs.transaction_type ilike '%%offline%%' then fs.dated else od_date end as order_date, fs.*,\n", "case when fs.offer_reference_id is null then cast(base_pid.PID as int) else cast(pids.pid as int) end as product_id,\n", "fs.quantity*il.weighted_lp as weighted_lp_sum\n", "from fs_gmv fs\n", "left join base_pid on fs.item_id = base_pid.item_id\n", "left join pids on fs.offer_reference_id = pids.offer_reference_id and fs.item_id = pids.item_id\n", "left join order_date o on fs.order_id = o.sub_id\n", "left join (select iil.invoice_id,iil.grofers_order_id,iil.variant_id,avg(iil.transaction_lp) transaction_lp,avg(iil.weighted_lp) weighted_lp \n", "from lake_ims.ims_inventory_log iil\n", " where date(iil.pos_timestamp + interval '5.5 hours') = %(summary_date)s\n", " and id>1225614316 and outlet_id in ({outlet_id})\n", " group by 1,2,3)il on fs.invoice_id = il.invoice_id and fs.order_id = il.grofers_order_id and fs.variant_id = il.variant_id\n", "),\n", "\n", "it as (\n", "with it_pd as\n", "(select distinct\n", "  item_id,\n", "  product_id,\n", "  flag,\n", "  offer_type_identifier,\n", "  offer_type_tag,\n", "  updated_on,multiplier,\n", "  max(updated_on) over (partition by product_id) as last_updated,\n", "  min(updated_on) over (partition by item_id,product_id) as updated_at\n", "from it_pd_log\n", "where item_id is not null\n", ")\n", "select distinct\n", "  flag, item_id, multiplier, offer_type_tag,product_id, offer_type_identifier\n", "from it_pd i\n", "where last_updated = updated_on \n", "-- and offer_type_identifier not ilike '%%none%%'\n", "and flag not ilike '%%base%%'\n", ")\n", "\n", "select distinct sales.*, it.multiplier,\n", "(case \n", "  when it.offer_type_tag = 'combo' then 'COMBO'\n", "  when it.offer_type_tag = 'non_combo' then 'NON_COMBO'\n", "  else 'NONE' end) as offer_type_tag,\n", "case when it.multiplier is not null then (sales.quantity/it.multiplier) else quantity end as act_qua,\n", "-- sales.mrp*sales.quantity as mrp_at_pid,\n", "case when transaction_type ilike '%%sale%%' then sales.mrp*sales.quantity else 0-(sales.mrp*sales.quantity) end as mrp_at_pid,\n", "sales.weighted_lp_sum as weight_lp_sum,\n", "CASE \n", "WHEN a.offer_type ILIKE '%%flat%%' and transaction_type ilike '%%sale%%' THEN COALESCE((a.bf_margin * (sales.mrp - a.sp) * sales.quantity),0.0)\n", "WHEN a.offer_type ILIKE '%%flat%%' and transaction_type ilike '%%return%%' THEN COALESCE(-(a.bf_margin * (sales.mrp - a.sp) * sales.quantity),0.0)\n", "WHEN a.offer_type NOT ILIKE '%%flat%%' and transaction_type ilike '%%sale%%' THEN COALESCE((a.bf_margin * sales.mrp *sales.quantity), 0.0) \n", "WHEN a.offer_type NOT ILIKE '%%flat%%' and transaction_type ilike '%%return%%' THEN COALESCE(-(a.bf_margin * sales.mrp *sales.quantity), 0.0) \n", "else 0\n", "END AS bf_margin\n", "from sales\n", "left join it on sales.product_id = it.product_id and sales.item_id = it.item_id\n", "left JOIN all_margins a ON lower(sales.customer_city) = lower(CASE\n", "                                                     WHEN a.city ILIKE 'bangalore' THEN 'bengaluru'\n", "                                                     ELSE a.city\n", "                                                 END) and sales.product_id = a.pid\n", "AND date(order_date) BETWEEN date(a.start_date) AND date(a.end_date)\n", ")xyz\n", "where offer_type_tag <> 'COMBO'\n", ")\n", "\"\"\".format(\n", "    outlet_id=\",\".join([str(y) for y in x])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_data = pd.read_sql_query(\n", "    sql=sales, con=redshift, params={\"summary_date\": summary_date}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.sort(sales_data.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.sort(np.array(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_data[\"brand_fund\"] = sales_data[\"brand_fund\"].abs()\n", "sales_data[\"brand_fund\"] = sales_data[\"brand_fund\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_data[\"billing_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_table = sales_data[\n", "    [\n", "        \"order_placed_date\",\n", "        \"billing_date\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"customer_city\",\n", "        \"invoice_id\",\n", "        \"order_id\",\n", "        \"offer_reference_id\",\n", "        \"transaction_type\",\n", "        \"item_id\",\n", "        \"product_name\",\n", "        \"quantity\",\n", "        \"mrp\",\n", "        \"selling_price\",\n", "        \"total_gross_bill_amount\",\n", "        \"total_return_amount\",\n", "        \"product_id\",\n", "        \"offer_type_tag\",\n", "        \"actual_quantity\",\n", "        \"mrp_at_pid\",\n", "        \"weighted_lp_sum\",\n", "        \"brand_fund\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_table[\n", "    (sales_table[\"invoice_id\"] == \"CAO1063T21010311\")\n", "    & (sales_table[\"item_id\"] == \"10001159\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display\n", "\n", "for outlet_id in x:\n", "    try:\n", "        final = sales_data[sales_data[\"outlet_id\"] == outlet_id]\n", "        df = final\n", "        df[\"quantity\"] = df[\"quantity\"].astype(\"int\")\n", "        df1 = df.copy()\n", "        # print(df1[\"Store_Name\"]).unique()\n", "        df2 = df1.drop(\n", "            [\"customer_city\"],\n", "            axis=1,\n", "        )\n", "        df2[\"total_amount\"] = (\n", "            df2[\"total_gross_bill_amount\"] + df2[\"total_return_amount\"]\n", "        )\n", "        df2[\"margins\"] = df2[\"total_amount\"] - df2[\"weighted_lp_sum\"]\n", "        df3 = df2.copy()\n", "        df3.drop(\n", "            [\n", "                \"order_id\",\n", "                \"outlet_name\",\n", "                \"mrp\",\n", "                \"product_name\",\n", "                \"order_placed_date\",\n", "                \"offer_reference_id\",\n", "                \"selling_price\",\n", "                \"product_id\",\n", "                \"offer_type_tag\",\n", "                \"actual_quantity\",\n", "                \"mrp_at_pid\",\n", "            ],\n", "            axis=1,\n", "            inplace=True,\n", "        )\n", "        df4 = (\n", "            df3.groupby([\"outlet_id\", \"billing_date\", \"transaction_type\"])\n", "            .agg(\n", "                {\n", "                    \"invoice_id\": \"nunique\",\n", "                    \"total_amount\": \"sum\",\n", "                    \"weighted_lp_sum\": \"sum\",\n", "                    \"quantity\": \"sum\",\n", "                    \"item_id\": \"nunique\",\n", "                    \"brand_fund\": \"sum\",\n", "                }\n", "            )\n", "            .rename(\n", "                columns={\n", "                    \"invoice_id\": \"Total_orders\",\n", "                    \"total_amount\": \"Amount\",\n", "                    \"weighted_lp_sum\": \"Amount_TLP\",\n", "                    \"quantity\": \"Units\",\n", "                    \"item_id\": \"SKUs\",\n", "                    \"brand_fund\": \"Brand_Fund_Margin\",\n", "                }\n", "            )\n", "            .reset_index()\n", "        )\n", "        df4[\"IPO\"] = df4[\"Units\"] / df4[\"Total_orders\"].round(2)\n", "        df4[\"SPO\"] = df4[\"SKUs\"] / df4[\"Total_orders\"].round(2)\n", "        df4[\"IPO\"] = df4[\"IPO\"].round(2)\n", "        df4[\"SPO\"] = df4[\"SPO\"].round(2)\n", "        df4[\"Amount\"] = df4[\"Amount\"].round(2)\n", "        df4[\"Amount_TLP\"] = df4[\"Amount_TLP\"].round(2)\n", "        df4.sort_values(\n", "            by=[\"billing_date\", \"transaction_type\"],\n", "            ascending=[True, False],\n", "            inplace=True,\n", "        )\n", "        df4.reset_index(drop=True, inplace=True)\n", "        df4[\"Margin\"] = df4[\"Amount\"] - df4[\"Amount_TLP\"]\n", "        df4[\"Margin\"] = df4[\"Margin\"].round(2)\n", "        df4[\"Brand_Fund_Margin\"] = df4[\"Brand_Fund_Margin\"].round(2)\n", "        df4[\"Total_Margin\"] = df4[\"Margin\"].astype(float) + df4[\n", "            \"Brand_Fund_Margin\"\n", "        ].astype(float)\n", "        df4[\"Total_Margin\"] = df4[\"Total_Margin\"].round(2)\n", "        df4 = df4[\n", "            [\n", "                \"outlet_id\",\n", "                \"billing_date\",\n", "                \"transaction_type\",\n", "                \"Total_orders\",\n", "                \"Amount\",\n", "                \"Amount_TLP\",\n", "                \"Units\",\n", "                \"SKUs\",\n", "                \"IPO\",\n", "                \"SPO\",\n", "                \"Brand_Fund_Margin\",\n", "                \"Margin\",\n", "                \"Total_Margin\",\n", "            ]\n", "        ]\n", "        df2.to_csv(\"summary_raw.csv\", index=False)\n", "        # Sending mailers\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        from_email = \"<EMAIL>\"\n", "        subject = (\n", "            \"Offline Stores Sales Summary Report with margins for %s\" % summary_date\n", "        )\n", "        #         to_email = email_list[email_list.outlet_id == outlet_id][\"Group Mail Id\"]\n", "        #         to_email=email_list[email_list.outlet_id==str(outlet_id)][\"Group Mail Id\"]\n", "        to_email = email_list[email_list[\"outlet_id\"] == str(outlet_id)][\n", "            \"Group Mail Id\"\n", "        ].tolist()\n", "        to_email.extend(cc)\n", "        # print(to_email)\n", "        to_email1 = [\"<EMAIL>\"]\n", "\n", "        loader = jinja2.FileSystemLoader(\n", "            # searchpath = \"\"\n", "            searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/report/offline_darkstores_margins\"\n", "            #                          searchpath=\"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/misc/report/offline_dark_stores\"\n", "            #             searchpath=\"/home/<USER>/test/offline_dark_stores\"\n", "        )\n", "        tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "        template = tmpl_environ.get_template(\"email_template.html\")\n", "        outlet_nam = df1[\"outlet_name\"].unique()\n", "        # cc = email_list[email_list[\"outlet_id\"] == str(1)][\"Group Mail Id\"].tolist()\n", "        df12 = df4\n", "        message_text = template.render(\n", "            products=df12.to_dict(orient=\"records\"),\n", "            today=today,\n", "            outlet=outlet_id,\n", "            outlet_name=outlet_nam[0],\n", "        )\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject,\n", "            html_content=message_text,\n", "            files=[\"summary_raw.csv\"],\n", "        )\n", "        print(\"sending for outlet: \" + str(outlet_id))\n", "    except:\n", "        print(\"failed for outlet: \" + str(outlet_id))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pushing data to table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_table.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_table[\"product_id\"] = sales_table[\"product_id\"].astype(\"Int64\")\n", "sales_table[\"mrp\"] = sales_table[\"mrp\"].round(decimals=2)\n", "sales_table[\"selling_price\"] = sales_table[\"selling_price\"].round(decimals=2)\n", "sales_table[\"quantity\"] = sales_table[\"quantity\"].astype(\"Int64\")\n", "sales_table[\"actual_quantity\"] = sales_table[\"actual_quantity\"].round(decimals=2)\n", "sales_table[\"mrp_at_pid\"] = sales_table[\"mrp_at_pid\"].round(decimals=2)\n", "sales_table[\"brand_fund\"] = sales_table[\"brand_fund\"].round(decimals=2)\n", "sales_table[\"total_gross_bill_amount\"] = sales_table[\"total_gross_bill_amount\"].round(\n", "    decimals=2\n", ")\n", "sales_table[\"total_return_amount\"] = sales_table[\"total_return_amount\"].round(\n", "    decimals=2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_table.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"order_placed_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"order date of online order\",\n", "    },\n", "    {\n", "        \"name\": \"billing_date\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"billing date of the order\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet id\"},\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"Name of the store\",\n", "    },\n", "    {\n", "        \"name\": \"customer_city\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"city of the store\",\n", "    },\n", "    {\n", "        \"name\": \"invoice_id\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"invoice bill number of the transaction\",\n", "    },\n", "    {\n", "        \"name\": \"order_id\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"order id (grofers suborder id)\",\n", "    },\n", "    {\n", "        \"name\": \"offer_reference_id\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"special offer id's\",\n", "    },\n", "    {\n", "        \"name\": \"transaction_type\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"type of transaction\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"varchar(1000)\", \"description\": \"item id\"},\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"character varying(1000)\",\n", "        \"description\": \"name of the product\",\n", "    },\n", "    {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"quantity\"},\n", "    {\"name\": \"mrp\", \"type\": \"float\", \"description\": \"MRP price\"},\n", "    {\"name\": \"selling_price\", \"type\": \"float\", \"description\": \"selling price\"},\n", "    {\n", "        \"name\": \"total_gross_bill_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total bill amount\",\n", "    },\n", "    {\n", "        \"name\": \"total_return_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"amount(monetary) of items returned\",\n", "    },\n", "    {\"name\": \"product_id\", \"type\": \"int\", \"description\": \"product_id of the item\"},\n", "    {\"name\": \"offer_type_tag\", \"type\": \"varchar(1000)\", \"description\": \"offer type\"},\n", "    {\n", "        \"name\": \"actual_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"quantity after the applied offer\",\n", "    },\n", "    {\"name\": \"mrp_at_pid\", \"type\": \"float\", \"description\": \"total mrp\"},\n", "    {\n", "        \"name\": \"weighted_lp_sum\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighed landing price\",\n", "    },\n", "    {\"name\": \"brand_fund\", \"type\": \"float\", \"description\": \"brand fund margin\"},\n", "]\n", "\n", "table_description = \"Contains brand fund details for Grofers Market Stores\"\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"brand_fund_offline_stores\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"billing_date\", \"outlet_id\", \"item_id\", \"invoice_id\", \"quantity\"],\n", "    \"sortkey\": [\"billing_date\"],\n", "    \"incremental_key\": \"order_placed_date\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"storing the brand fund margins of offline stores\",\n", "}\n", "pb.to_redshift(sales_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}