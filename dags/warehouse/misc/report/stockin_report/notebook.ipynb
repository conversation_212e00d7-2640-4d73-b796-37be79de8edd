{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            print(\"Read from the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            print(\"Pushed Data to the sheet\")\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "pos = pb.get_connection(\"pos\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "\n", "t_1 = (datetime.now() - <PERSON><PERSON>ta(days=1)).date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlets_query = (\n", "#     \"\"\"select id outlet_id,name as outlet_name  from  retail.console_outlet\"\"\"\n", "# )\n", "# outlet_ids = pd.read_sql_query(sql=outlets_query, con=retail)\n", "# outlet_ids.head()\n", "# outlet_list = outlet_ids[\"outlet_id\"]\n", "# type(outlet_list)\n", "# outletid_list = tuple(list(outlet_ids[\"outlet_id\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outletname_list = tuple(list(outlet_ids[\"outlet_name\"]))\n", "# print(outletname_list[0:10])\n", "# type(outletname_list)\n", "# # outletid_list=(100,410)\n", "# print(outletid_list[0:10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT \n", "i3.id as \"Stock Update ID\",\n", "pur_ord.po_number as \"Purchase Number\",\n", "(CASE\n", "  WHEN iii.source_type = 2 THEN 'ESTO Stock-in'\n", "  WHEN iii.source_type = 3 THEN 'Customer returened without invoice'\n", "  WHEN iii.source_type = 1 and iii.valid_po=1 THEN 'Valid PO'\n", "  ELSE 'In-Valid PO'\n", "END) AS \"PO Type\",\n", "pur_ord.vendor_gst as \"Vendor GST\",\n", "lo2.name as \"Vendor Supply Location\",\n", "-- Date(pur_ord.issue_date + interval '5.5 hrs') as \"Issue Date\",\n", "-- Date(i3.manufacture_date + interval '5.5 hrs') as \"Manufacture Date\",\n", "r.item_id AS \"Item ID\",\n", "i.upc_id AS \"UPC ID\",\n", "i.variant_id AS \"Variant ID\",\n", "r.name AS Product,\n", "(CASE\n", "  WHEN r.outlet_type = 1 THEN 'F&V type'\n", "WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "     ELSE 'Not Assigned'\n", "END) AS \"Item Type\",\n", "man.name AS \"Manufacturer Name\",\n", "--r.is_ptr AS \"PTR Flag\",\n", "i.\"delta\" AS Quantity,\n", "i3.return_quantity ,\n", "(i.\"delta\"-i3.return_quantity) as Final_quantity,\n", "r.variant_mrp AS MRP,\n", "r.variant_uom_text AS Grammage,\n", "ro.id as \"Outlet Id\",\n", "ro.name AS \"Outlet Name\",\n", "lo.name AS \"City Name\",\n", "Date(i.pos_timestamp+ interval '5.5 hrs') AS \"Stock In Date\",\n", "(i.created_at + interval '5.5 hrs') AS \"Created At\",\n", "i2.name AS \"Update Type\",\n", "i3.expiry_date AS \"Expiry Date\",\n", "rcm.name AS \"Merchant Name\",\n", "i.merchant_invoice_id AS \"Merchant Invoice ID\",\n", "i.created_by_name AS \"Created By\",\n", "i3.cost_price AS \"Cost Price\",\n", "cgst_tax.tax_percentage AS \"CGST(%%)\",\n", "sgst_tax.tax_percentage AS \"SGST(%%)\",\n", "igst_tax.tax_percentage AS \"IGST(%%)\",\n", "cess_tax.tax_percentage AS \"CESS(%%)\",\n", "i3.landing_price AS \"Landing Price\",\n", "i3.po_landing_price AS \"PO Landing Price\",\n", "i3.purchase_order_id as \"Purchase Order ID\",\n", "i3.grn_id as \"GRN ID\",\n", "(i3.cost_price*(i.\"delta\"-i3.return_quantity)) as \"Net Cost Price\",\n", "(i3.landing_price*(i.\"delta\" -i3.return_quantity)) as \"Net Landing Price\",\n", "(i3.po_landing_price*(i.\"delta\"-i3.return_quantity)) as \"Net PO landing price\",\n", "i3.cost_price * i.\"delta\" as \"Total Cost Price\",\n", "((i3.landing_price * i.\"delta\")-(i3.cost_price * i.\"delta\")) as \"Tax\"\n", "--r.is_pl\n", "FROM\n", "(select * from lake_ims.ims_inventory_log \n", "where \n", "    \"delta\" > 0 and \n", "    date(created_at + interval '5.5 hrs') = current_date-1 and\n", "    inventory_update_type_id IN (1,28,76,90,93)\n", ") i\n", "INNER JOIN (select item_id,name,variant_id,outlet_type,is_ptr,is_pl,variant_mrp,variant_uom_text,brand_id from lake_rpc.product_product) r ON r.variant_id = i.variant_id\n", "LEFT JOIN (select id,manufacturer_id from lake_rpc.product_brand) br ON br.id=r.brand_id\n", "LEFT JOIN (select id, name from lake_rpc.product_manufacturer) man ON man.id=br.manufacturer_id\n", "INNER JOIN (select id,name,tax_location_id from lake_retail.console_outlet) ro ON ro.id = i.outlet_id\n", "INNER JOIN (select id,name from lake_retail.console_location) lo ON ro.tax_location_id=lo.id\n", "INNER JOIN (select id,name from lake_ims.ims_inventory_update_type) i2 ON i2.id = i.inventory_update_type_id  \n", "INNER JOIN (select id,inventory_update_id, grn_id,purchase_order_id,cost_price,landing_price,return_quantity,po_landing_price,manufacture_date,expiry_date  from lake_ims.ims_inventory_stock_details) i3 ON i3.inventory_update_id = i.inventory_update_id\n", "INNER JOIN (select grn_id,source_type,valid_po from lake_ims.ims_inward_invoice) iii ON iii.grn_id=i3.grn_id\n", "INNER JOIN (select id,name from lake_retail.console_merchant) rcm ON rcm.id = i.merchant_id\n", "LEFT JOIN (select inventory_stock_id,tax_type_id,tax_percentage from lake_ims.ims_inventory_stock_tax) cgst_tax ON i3.id = cgst_tax.inventory_stock_id and cgst_tax.tax_type_id = 3\n", "LEFT JOIN (select inventory_stock_id,tax_type_id,tax_percentage from lake_ims.ims_inventory_stock_tax) sgst_tax ON i3.id = sgst_tax.inventory_stock_id and sgst_tax.tax_type_id = 4\n", "LEFT JOIN (select inventory_stock_id,tax_type_id,tax_percentage from lake_ims.ims_inventory_stock_tax) igst_tax ON i3.id = igst_tax.inventory_stock_id and igst_tax.tax_type_id = 5\n", "LEFT JOIN (select inventory_stock_id,tax_type_id,tax_percentage from lake_ims.ims_inventory_stock_tax) cess_tax ON i3.id = cess_tax.inventory_stock_id and cess_tax.tax_type_id = 6   \n", "LEFT JOIN (select po_number,vendor_shipping_city_id,issue_date,vendor_gst from lake_po.purchase_order) pur_ord ON pur_ord.po_number= i3.purchase_order_id \n", "LEFT JOIN (select id,name from lake_retail.console_location) lo2 on lo2.id=pur_ord.vendor_shipping_city_id\n", "\"\"\"\n", "raw_1day_final = pd.read_sql_query(sql=query, con=redshift)\n", "\n", "raw_1day_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_1day_final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1 = raw_1day_final[(raw_1day_final[\"stock in date\"] == t_1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name = \"GRN_raw_data_\" + str(t_1) + \".csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_1day_final.to_csv(name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "ld = datetime.datetime.now().strftime(\"%d-%m-%Y\")\n", "\n", "\n", "def create_presigned_url(bucket_name, object_name, expiration=60 * 60 * 24 * 7):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        logging.error(e)\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response\n", "\n", "\n", "# tmp for temp create file in folder\n", "# data.to_csv(ld+\"_InventoryHealth.csv\",index=False)\n", "# sheet_id = '1VBcmA-8XkeYpVNn6BlOttzWLklAJYuBE8tDzDP7sE8I'\n", "# email_id = pb.from_sheets(sheet_id,'email_ih')\n", "local_filename1 = name\n", "cloud_filepath = \"pencilbox/jyotesh/finance_grn/\" + local_filename1\n", "pb.to_s3(local_filename1, bucket_name, cloud_filepath)\n", "file_name_link = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 48\n", ")\n", "# from_email = \"<EMAIL>\"\n", "# to_email = list(email_id['email'])\n", "# pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id1 = \"1P07VmQMaw-uVuMjHXvweytirwf3rdMwhc28-G5ai3xg\"\n", "# sheet_id2 = \"1AVvr8FNguuyJ2WRagP0LF0kOMO0CkrALBhM-SBQEKR0\"\n", "# sheet_id3 = \"1O8B6JKE2QWzZ9xkBVbNwVjgCYWIZbMb_ZEXEhS9uvzM\"\n", "# sheet_id4 = \"15Lt4nQnT0yHP0SrB72i7RUOsNaCakmjQQ3kBbFvdj6M\"\n", "# sheet_id5 = \"1PeEb9cEkzRn-kWdesVtFtIW00PanSxK1Q6B6bf3TF4k\"\n", "# sheet_id6 = \"1FWsFVYq4VELiF-8TCDRevGEE6Xm3Hj22O2EZW_AlW_8\"\n", "# # time.sleep(60)\n", "# sheet_name = \"raw_data\"\n", "# to_sheets(data1, sheet_id1, sheet_name, service_account=\"service_account\")\n", "# to_sheets(data2, sheet_id2, sheet_name, service_account=\"service_account\")\n", "# to_sheets(data3, sheet_id3, sheet_name, service_account=\"service_account\")\n", "# to_sheets(data4, sheet_id4, sheet_name, service_account=\"service_account\")\n", "# to_sheets(data5, sheet_id5, sheet_name, service_account=\"service_account\")\n", "# to_sheets(data6, sheet_id6, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rec = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = final_rec\n", "# to_email = \"<EMAIL>\",\"vibhor.batra@grofers.com11\"\n", "# to_email = [x.strip() for x in list(email[\"emails\"]) if len(x.strip()) > 0]\n", "subject = \"GRN raw data for \" + str(t_1)\n", "to_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = \"\"\"\n", "Hi,<br><br>\n", "\n", "Please find the attached link for GRN raw data from yesterday's transaction.<br>\n", "Link: {links} <br>\n", "Link will expire in 7 days from today\n", "<b></b><br>\n", "\n", "\"\"\".format(\n", "    links=file_name_link\n", ")\n", "# html_content += \"<b>Outer case size min: </b>\" + str(outer_case_size_limit)\n", "# html_content += \"<br><br><b>Assortment last updated at: </b>\" + assortment_last_updated\n", "# html_content += \"<br><br><b>Unique ID: </b>\" + uid\n", "html_content += \"<br><br><PERSON><PERSON>,<br><PERSON><PERSON><PERSON>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "s = os.path.getsize(name) / (1024 * 1024)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "    )\n", "    print(\"mail sent\")\n", "except:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content_fail,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}