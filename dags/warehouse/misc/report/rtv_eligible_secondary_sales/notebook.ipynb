{"cells": [{"cell_type": "code", "execution_count": null, "id": "71bc95e2-1511-4d19-9a52-c298e3651b87", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import timedelta, date, datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "31405d10-b461-4354-be62-0dfab4e1ad55", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0021127-ffb2-4975-9686-babdc41cac30", "metadata": {}, "outputs": [], "source": ["liq = pd.read_sql_query(\n", "    \"\"\"\n", "with rtv as\n", "  (select facility_id, item_id\n", "   from metrics.rtv_elg_inputs)\n", "   \n", "select date(convert_timezone('Asia/Kolkata', pi.pos_timestamp)) pos_timestamp,\n", "r.facility_id,\n", "f.name facility_name,\n", "case when business_type_id != 7 then 1 else 0 end backend_flag,\n", "prod.item_id,\n", "id.name item_name,\n", "sum(case when invoice_type_id = 11 then pipd.quantity else 0 end) secondary_sale_quantity,\n", "sum(case when invoice_type_id = 12 then pipd.quantity else 0 end) bad_stock_sale_quantity,\n", "sum(case when invoice_type_id = 11 then pipd.quantity * selling_price else 0 end) secondary_sale_value,\n", "sum(case when invoice_type_id = 12 then pipd.quantity * selling_price else 0 end) bad_stock_sale_value,\n", "-- sum(pipd.quantity) quantity,\n", "-- sum(pipd.quantity * selling_price) invoice_value,\n", "sum(pipd.quantity * landing_price) estimated_prn_value,\n", "sum(pipd.quantity * landing_price) - sum(pipd.quantity * selling_price) \"loss_value\"\n", "from (select *\n", "      from lake_pos.pos_invoice\n", "      where pos_timestamp >= (current_date)::timestamp - interval '5.5 hrs' - interval '1 month'\n", "      and invoice_type_id in (11, \n", "                              12)) pi\n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id = pi.id \n", "and pipd.store_offer_id is null\n", "join lake_retail.console_outlet r on r.id = pi.outlet_id\n", "and r.name not like '%%Infra%%'\n", "and r.business_type_id != 8\n", "and r.name not like '%%ES Liquidation%%'\n", "join lake_crates.facility f on f.id = r.facility_id\n", "join lake_rpc.product_product prod on prod.variant_id = pipd.variant_id\n", "join rtv on rtv.item_id = prod.item_id and rtv.facility_id = f.id\n", "join lake_pos.pos_customer_tax_details p on p.invoice_id = pipd.invoice_id\n", "join lake_vms.vms_vendor v on v.id = p.vms_merchant_id and v.type_id = 3\n", "left join (select entity_vendor_id, variant_id, landing_price\n", "           from lake_ims.ims_entity_vendor_inventory_landing_price\n", "           where active = 1) lp on lp.entity_vendor_id = pi.source_entity_vendor_id\n", "and lp.variant_id = pipd.variant_id\n", "-- join lake_pos.pos_invoice_type pit on pit.id = pi.invoice_type_id\n", "-- join lake_retail.console_location loc1 on loc1.id = r.tax_location_id\n", "-- join lake_retail.console_location loc2 on loc2.id = pi.customer_city\n", "left join lake_rpc.item_details id on id.item_id = prod.item_id\n", "group by 1,2,3,4,5,6\n", "order by 1 desc,12 desc\n", "\"\"\",\n", "    redshift,\n", ")\n", "liq.head()"]}, {"cell_type": "code", "execution_count": null, "id": "24b65d92-0dd0-46d0-9732-f9e2943f5b6b", "metadata": {}, "outputs": [], "source": ["sku = (\n", "    liq[liq[\"secondary_sale_quantity\"] > 0]\n", "    .groupby(\"facility_id\")\n", "    .item_id.nunique()\n", "    .reset_index()\n", ")\n", "liq_sku = (\n", "    liq[liq[\"bad_stock_sale_quantity\"] > 0]\n", "    .groupby(\"facility_id\")\n", "    .item_id.nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c1400199-d543-4cb5-bd08-7797a16e3122", "metadata": {}, "outputs": [], "source": ["overall = (\n", "    liq.groupby([\"facility_id\", \"facility_name\", \"backend_flag\"])\n", "    .agg(\n", "        {\n", "            \"secondary_sale_quantity\": sum,\n", "            \"bad_stock_sale_quantity\": sum,\n", "            \"secondary_sale_value\": sum,\n", "            \"bad_stock_sale_value\": sum,\n", "            \"estimated_prn_value\": sum,\n", "            \"loss_value\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "# overall = overall.rename(columns={'item_id' : 'sku'})"]}, {"cell_type": "code", "execution_count": null, "id": "59b605e7-a239-427e-910b-f3c520c24dae", "metadata": {}, "outputs": [], "source": ["overall = overall.merge(sku, on=\"facility_id\", how=\"left\").rename(\n", "    columns={\"item_id\": \"secondary_sale_sku\"}\n", ")\n", "overall = overall.merge(liq_sku, on=\"facility_id\", how=\"left\").rename(\n", "    columns={\"item_id\": \"bad_stock_sale_sku\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d486641-14b9-43b5-a810-8d8b886b7341", "metadata": {}, "outputs": [], "source": ["overall = overall.sort_values(\"loss_value\", ascending=False)\n", "overall.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a812c8d0-1d70-4d2e-81e9-e43e0a773ee5", "metadata": {}, "outputs": [], "source": ["overall = overall.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "c4ee364d-f483-4920-9c10-777e2a364605", "metadata": {}, "outputs": [], "source": ["overall = overall[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"backend_flag\",\n", "        \"secondary_sale_sku\",\n", "        \"secondary_sale_quantity\",\n", "        \"secondary_sale_value\",\n", "        \"bad_stock_sale_sku\",\n", "        \"bad_stock_sale_quantity\",\n", "        \"bad_stock_sale_value\",\n", "        \"estimated_prn_value\",\n", "        \"loss_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "27646eae-3107-430f-87f9-78876d5118fb", "metadata": {}, "outputs": [], "source": ["# overall[overall['sku'] != (overall['secondary_sale_sku'] + overall['bad_stock_sale_sku'])]"]}, {"cell_type": "code", "execution_count": null, "id": "1c6c5a04-158d-4fdd-9630-21052cdb748f", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1h4_TRXpe8mU-_Kn60Gt0FPjWFY7z0YvTCDQGV5ZS00M\"\n", "sheet_name = \"WH Summary\"\n", "\n", "pb.to_sheets(\n", "    overall[overall[\"backend_flag\"] == 1].drop(columns=\"backend_flag\"),\n", "    sheet_id,\n", "    sheet_name,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09569dc1-fb55-4cd2-b6f8-6d182703d438", "metadata": {}, "outputs": [], "source": ["sheet_name = \"DS Summary\"\n", "\n", "pb.to_sheets(\n", "    overall[overall[\"backend_flag\"] == 0].drop(columns=\"backend_flag\"),\n", "    sheet_id,\n", "    sheet_name,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d9176ef9-580f-4548-822c-66847f41d7ce", "metadata": {}, "outputs": [], "source": ["sheet_name = \"Secondary Sales Numbers\"\n", "\n", "pb.to_sheets(liq.drop(columns=\"backend_flag\"), sheet_id, sheet_name)"]}, {"cell_type": "markdown", "id": "6b894123-440b-414a-97df-afc86144b823", "metadata": {}, "source": ["### Mailer"]}, {"cell_type": "code", "execution_count": null, "id": "92b6baa0-4bbe-467c-ae99-18742e68f7eb", "metadata": {}, "outputs": [], "source": ["now = datetime.now(ist)\n", "if now.weekday() == 0:\n", "    emails = pb.from_sheets(\n", "        \"1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I\", \"emails\"\n", "    ).emails.unique()\n", "    add = [\n", "        \"<EMAIL>\",\n", "        \"aditya.jais<PERSON>@handsontrades.com\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    emails = list(emails)\n", "    emails.extend(add)\n", "\n", "    prev_week = (now - timed<PERSON>ta(weeks=1)).date()\n", "    liq = liq[(liq[\"pos_timestamp\"] >= prev_week) & (liq[\"backend_flag\"] == 1)]\n", "\n", "    sku = (\n", "        liq[liq[\"secondary_sale_quantity\"] > 0]\n", "        .groupby(\"facility_id\")\n", "        .item_id.nunique()\n", "        .reset_index()\n", "    )\n", "    liq_sku = (\n", "        liq[liq[\"bad_stock_sale_quantity\"] > 0]\n", "        .groupby(\"facility_id\")\n", "        .item_id.nunique()\n", "        .reset_index()\n", "    )\n", "\n", "    overall = (\n", "        liq.groupby([\"facility_id\", \"facility_name\"])\n", "        .agg(\n", "            {\n", "                \"secondary_sale_quantity\": sum,\n", "                \"bad_stock_sale_quantity\": sum,\n", "                \"secondary_sale_value\": sum,\n", "                \"bad_stock_sale_value\": sum,\n", "                \"estimated_prn_value\": sum,\n", "                \"loss_value\": sum,\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    overall = overall.merge(sku, on=\"facility_id\", how=\"left\").rename(\n", "        columns={\"item_id\": \"secondary_sale_sku\"}\n", "    )\n", "    overall = overall.merge(liq_sku, on=\"facility_id\", how=\"left\").rename(\n", "        columns={\"item_id\": \"bad_stock_sale_sku\"}\n", "    )\n", "\n", "    overall = overall.sort_values(\"loss_value\", ascending=False)\n", "    overall = overall.fillna(0)\n", "    overall.head()\n", "\n", "    overall = overall[\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"secondary_sale_sku\",\n", "            \"secondary_sale_quantity\",\n", "            \"secondary_sale_value\",\n", "            \"bad_stock_sale_sku\",\n", "            \"bad_stock_sale_quantity\",\n", "            \"bad_stock_sale_value\",\n", "            \"estimated_prn_value\",\n", "            \"loss_value\",\n", "        ]\n", "    ]\n", "\n", "    overall = overall.astype(\n", "        {\n", "            \"secondary_sale_sku\": int,\n", "            \"secondary_sale_quantity\": int,\n", "            \"bad_stock_sale_sku\": int,\n", "            \"bad_stock_sale_quantity\": int,\n", "        }\n", "    )\n", "    overall = overall.round(2)\n", "\n", "    html_content = (\n", "        \"Hi, <br><br>\"\n", "        + \"Please find facility wise loss incurred due to secondary sales of RTV eligible items over the past week. <br>\"\n", "        + \"Here is link to secondary sales of RTV eligible items tracker: https://docs.google.com/spreadsheets/d/1h4_TRXpe8mU-_Kn60Gt0FPjWFY7z0YvTCDQGV5ZS00M/edit?usp=sharing. <br><br>\"\n", "        + overall.to_html(index=False)\n", "        .replace(\"<td>\", '<td align=\"center\" style=\"padding: 4px 4px\">')\n", "        .replace(\"<th>\", '<th align=\"center\" style=\"padding: 4px 4px\">')\n", "        .replace(\n", "            '<tr style=\"text-align: right;\">',\n", "            '<tr style=\"padding: 4px 4px text-align: center;\">',\n", "        )\n", "        .replace(\n", "            '<table border=\"1\" class=\"dataframe\">',\n", "            '<table style=\"border-collapse: collapse; text-align: center;\" border = \"1\" class=\"dataframe\">',\n", "        )\n", "        + \"<br>Thanks, <br>Data Warehouse\"\n", "    )\n", "\n", "    from_email = \"<EMAIL>\"\n", "    # from_email = '<EMAIL>'\n", "    to_email = emails\n", "    subject = \"Secondary Sales of RTV Eligible Items\"\n", "    pb.send_email(from_email, to_email, subject, html_content)\n", "    print(\"mail sent!\")"]}, {"cell_type": "code", "execution_count": null, "id": "c0d33875-ab28-43a1-9208-63ffcc6b4beb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}