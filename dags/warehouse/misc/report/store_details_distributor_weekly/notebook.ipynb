{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc426ac0-810e-42b7-ae3c-2e074bf68cb9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from datetime import datetime, date\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "99ccd26e-0658-442c-8ded-0a096ef4d512", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT * FROM \n", "(SELECT DISTINCT v.outlet_id,\n", "                o.name AS Outlet_Name,\n", "                o.facility_id,\n", "                f.name AS facility_name,\n", "                l.name AS city_name,\n", "                v.entity_vendor_id,\n", "                vm.vendor_name AS Entity_Vendor_Name,\n", "                cti.cin as cin_id,\n", "                CASE\n", "                    WHEN vm.type_id = 2 THEN 'Outlet_Vendor'\n", "                    WHEN vm.type_id = 5 THEN 'Distributor'\n", "                END AS Vendor_Type,\n", "                CASE\n", "                    WHEN cti.cin = 'U74140HR2015PTC056483' then '<EMAIL>'\n", "                    when cti.cin = 'U74110DL2013PTC248660' then '<EMAIL>'\n", "                    when cti.cin = 'ABA-6260' then '<EMAIL>'\n", "                    when cti.cin = 'U51900DL2022PTC393329'then '<EMAIL>'\n", "                    when cti.cin = 'AAZ-3294' then '<EMAIL>'\n", "                    ELSE null\n", "                END AS email_id,\n", "                cti.legal_name as d_name\n", "FROM lake_rpc.outlet_entity_vendor_item v\n", "LEFT JOIN lake_view_vms.vms_vendor vm ON v.entity_vendor_id=vm.id\n", "INNER JOIN lake_retail.console_outlet o ON v.outlet_id=o.id\n", "INNER JOIN lake_crates.facility f ON o.facility_id = f.id\n", "INNER JOIN lake_retail.console_location l ON l.id = o.tax_location_id\n", "LEFT JOIN lake_vms.vms_vendor_city_mapping cm ON cm.vendor_id = vm.id\n", "AND cm.active = 1\n", "LEFT JOIN lake_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = cm.id\n", "AND cti.active = 1\n", "WHERE vm.type_id = 5\n", "  AND v.active=1\n", "  AND cti.cin != 'U74140HR2015FTC055568'\n", "  AND cti.legal_name not like '%%BLINK%%'\n", "ORDER BY 1)\n", "WHERE email_id is not null\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "80bf8398-fb46-45cb-92e8-f13d0a7b669d", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, presto)\n", "df = data.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b98025a7-8398-42be-a14b-ca28a6644888", "metadata": {}, "outputs": [], "source": ["cin_mail = df[[\"cin_id\", \"email_id\", \"d_name\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "c17f7134-c6fd-4f92-9ed4-1047f05348f3", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "\n", "today = date.today()\n", "str(today)"]}, {"cell_type": "code", "execution_count": null, "id": "3d778360-8dad-4334-bae8-f97c99bfdea2", "metadata": {}, "outputs": [], "source": ["cin_mail"]}, {"cell_type": "code", "execution_count": null, "id": "05484226-c0b9-4e6c-84ec-fce4c1827f73", "metadata": {}, "outputs": [], "source": ["def send_email_1(temp_cin):\n", "    temp_df = df[df.cin_id == str(temp_cin)]\n", "    temp_df1 = temp_df[[\"outlet_id\", \"facility_name\", \"city_name\"]]\n", "    temp_df1 = temp_df1.rename(\n", "        columns={\n", "            \"outlet_id\": \"Outlet ID\",\n", "            \"facility_name\": \"Facility Name\",\n", "            \"city_name\": \"City\",\n", "        }\n", "    )\n", "    d_name = cin_mail[cin_mail.cin_id == temp_cin].d_name.unique()[0]\n", "    temp_df1.to_csv(\"Active Stores - \" + str(d_name) + \".csv\", index=False)\n", "    from_email = \"<EMAIL>\"\n", "    to_email = list(cin_mail[cin_mail.cin_id == temp_cin].email_id.unique())\n", "    cc = [\"<EMAIL>\", \"<EMAIL>\"]\n", "    # to_email = ['<EMAIL>']\n", "    # cc = []\n", "    subject = \"Active Store Details - \" + str(d_name)\n", "    html_content = (\n", "        \"\"\"Hi,<br><br>Please find attached the list of active stores under \"\"\"\n", "        + str(d_name)\n", "        # + \"\"\" as of \"\"\" + str(today)\n", "        + \"\"\"<br><br>\"\"\"\n", "        + \"\"\"Thanks\"\"\"\n", "    )\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        cc=cc,\n", "        files=[\"Active Stores - \" + str(d_name) + \".csv\"],\n", "    )\n", "    print(\"email sent to \" + str(d_name))"]}, {"cell_type": "code", "execution_count": null, "id": "0ad54d3f-b16d-4ccc-94f1-2a1be5cbd848", "metadata": {}, "outputs": [], "source": ["cin_unique = cin_mail.cin_id.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "004d3cef-0a8b-413b-8f62-774dd73b7f76", "metadata": {}, "outputs": [], "source": ["for temp_cin in cin_unique:\n", "    send_email_1(temp_cin)"]}, {"cell_type": "code", "execution_count": null, "id": "b8e3ee8a-2dbf-448c-872c-52f5e03acc49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c16a0622-8285-44fa-a6d3-9063a0b1ab61", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}