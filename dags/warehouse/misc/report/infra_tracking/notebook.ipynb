{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime as dt\n", "from datetime import datetime\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_sto_df = pb.from_sheets(\n", "    \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"STO Tracking\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_badstock_df = pb.from_sheets(\n", "    \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"DS Consumption\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_grn = pb.from_sheets(\n", "    \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"GRN Tracking\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = datetime.now()\n", "if today.day == 2:\n", "\n", "    email_df = pb.from_sheets(\"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"emails\")\n", "\n", "    emails = list(email_df.Emails.unique())\n", "\n", "    yesterday = datetime.now() - dt.<PERSON><PERSON><PERSON>(days=2)\n", "    month = yesterday.strftime(\"%B\")\n", "    print(f\"STO_Tracking_{month}\")\n", "    current_sto_df.to_csv(f\"Infra_STO_Tracking_{month}.csv\", index=False)\n", "    current_badstock_df.to_csv(f\"Infra_DS_consumption_{month}.csv\", index=False)\n", "    current_grn.to_csv(f\"Infra_GRN_Tracking_{month}.csv\", index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    to_email = emails\n", "\n", "    subject = f\"Infra Packing Materials Tracking for {month}\"\n", "    html_content = (\n", "        \"Hi,\"\n", "        + \"<br>\"\n", "        + f\"Please find attached the respective files for tracking the GRN, STOs and Consumption of packaging materials at Infra outlets for the month of {month}.\"\n", "        + \"<p>\"\n", "        + \"Thank you,\"\n", "        + \"<br>\"\n", "        + \"Data Warehouse Team\"\n", "    )\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[\n", "            f\"Infra_STO_Tracking_{month}.csv\",\n", "            f\"Infra_GRN_Tracking_{month}.csv\",\n", "            f\"Infra_DS_consumption_{month}.csv\",\n", "        ],\n", "    )\n", "\n", "    # resetting sheets for new month\n", "    current_sto_df = pd.DataFrame()\n", "    current_badstock_df = pd.DataFrame()\n", "    current_grn = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = pb.from_sheets(\"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"Item ID\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_ids = items[\"Item ID\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_ids = list(map(int, item_ids))\n", "# item_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT DISTINCT date(sto_created_at + interval '5.5 hrs') as Date,\n", "                sto_id as \"STO ID\",\n", "                sender_outlet_name as \"Sender Outlet\",\n", "                sender_outlet_id as \"Sender Outlet ID\",\n", "                item_id as \"Item ID\",\n", "                item_name as \"Item Name\",\n", "                receiver_outlet_name as \"Receiving Outlet Name\",\n", "                receiving_outlet_id as \"Receiving Outlet ID\",\n", "                expected_quantity as \"Expected Quantity\",\n", "                billed_quantity as \"Billed Quantity\",\n", "                inwarded_quantity as \"Inwarded Quantity\"\n", "FROM metrics.esto_details\n", "WHERE item_id IN {*item_ids,}\n", "  AND date(sto_created_at + interval '5.5 hrs') = CURRENT_DATE - 1\n", "  ORDER By 1,2,3\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_details = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_sto_df = current_sto_df.append(sto_details)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    current_sto_df, \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"STO Tracking\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT date(il.pos_timestamp + interval '5.5 hrs') AS \"date\",\n", "       rpp.item_id as \"item id\",\n", "       rpp.name as \"item name\",\n", "       rpp.variant_description as \"variant description\",\n", "       il.variant_id as \"variant id\",\n", "       co.name as \"outlet name\",\n", "       il.outlet_id as \"outlet id\",\n", "       sum(il.\"delta\") AS \"badstock qty\"\n", "FROM lake_ims.ims_inventory_log il\n", "JOIN lake_rpc.product_product rpp ON rpp.variant_id = il.variant_id -- JOIN lake_ims.ims_item_inventory_log iil ON iil.id = il.id\n", "INNER JOIN lake_retail.console_outlet co ON co.id = il.outlet_id -- AND co.name ILIKE '%%infra%%'\n", "\n", "WHERE date(il.pos_timestamp + interval '5.5 hrs') = CURRENT_DATE - 1 \n", "  AND item_id IN {*item_ids,}\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7\n", "ORDER BY 1,\n", "         2,\n", "         3,6\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["badstock = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["badstock.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_badstock_df = current_badstock_df.append(badstock)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    current_badstock_df,\n", "    \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\",\n", "    \"DS Consumption\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Po GRN data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT date(iil.pos_timestamp),\n", "       iil.outlet_id,\n", "       co.name,\n", "       pp.item_id,\n", "       pp.name,\n", "       sum(iil.\"delta\") AS grn_quantity\n", "FROM lake_ims.ims_inventory_log iil\n", "JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = iil.inventory_update_id\n", "JOIN lake_rpc.product_product pp ON pp.variant_id = iil.variant_id\n", "JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id = i3.grn_id\n", "JOIN lake_retail.console_outlet co ON co.id = iil.outlet_id\n", "WHERE date(iil.pos_timestamp) = CURRENT_DATE - 1\n", "  AND iil.inventory_update_type_id IN (1,\n", "                                       28,\n", "                                       76,\n", "                                       90)\n", "  AND item_id IN {*item_ids,}\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5\n", "ORDER BY 1,\n", "         2,\n", "         3\n", "\"\"\"\n", "grn_df = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_grn = current_grn.append(grn_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    current_grn, \"1iIXHX2Wh-lAlE8dh8sIbPJ9-duoMi7roocMHLKY2SO0\", \"GRN Tracking\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}