{"cells": [{"cell_type": "code", "execution_count": null, "id": "6dd9be0c-caf5-49b2-b66d-35229c2719b7", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "import numpy as np\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "df91529b-f1ca-452c-a6ca-8e69bd5e284f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "\n", "today = date.today()\n", "yesterday = today - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "0db4f2c8-2fda-4c68-833d-269ce895f89b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT base.\"Date\",\n", "       base.sender_outlet_id AS \"source outlet id\",\n", "       c.name AS \"source outlet name\",\n", "       SUM(base.total_eligible_invoices) AS \"total eligible invoices\",\n", "       SUM(base.\"pending invoices\") AS \"total pending invoices\",\n", "       round(\"total pending invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of pending\",\n", "       sum(base.\"success invoices\") AS \"total success invoices\",\n", "       round(\"total success invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of success\",\n", "       sum(base.\"other invoices\") AS \"total other invoices\",\n", "       round(\"total other invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of others\"\n", "FROM\n", "  (SELECT semi.sender_outlet_id,\n", "          semi.receiver_outlet_id,\n", "          semi.\"date\",\n", "          count(DISTINCT semi.invoice_id) AS total_eligible_invoices,\n", "          count(CASE\n", "                    WHEN semi.status = 1 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"pending invoices\",\n", "          count(CASE\n", "                    WHEN semi.status = 3 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"success invoices\",\n", "          count(CASE\n", "                    WHEN semi.status NOT IN (1,3) THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"other invoices\"\n", "   FROM\n", "     (SELECT final.sender_outlet_id,\n", "             final.receiver_outlet_id,\n", "             final.\"date\",\n", "             e.status,\n", "             e.invoice_id\n", "      FROM lake_pos.pos_ewaybill e\n", "      JOIN\n", "        (SELECT p.outlet_id AS sender_outlet_id,\n", "                s.merchant_outlet_id AS receiver_outlet_id,\n", "                (p.pos_timestamp + interval '5.5 hrs')::date AS \"date\"\n", "         FROM lake_pos.pos_invoice p\n", "         JOIN lake_ims.ims_sto_details s ON s.sto_id::int = p.grofers_order_id::int\n", "         JOIN lake_retail.console_outlet AS c ON c.id = p.outlet_id\n", "         WHERE p.invoice_type_id IN (5,\n", "                                     14,\n", "                                     16)\n", "           AND c.business_type_id IN (1,\n", "                                      12)\n", "           AND (p.pos_timestamp + interval '5.5 hrs')::date = CURRENT_DATE-1\n", "         GROUP BY 1,\n", "                  2,\n", "                  3\n", "         HAVING sum(p.actual_sales) >50000) FINAL ON final.sender_outlet_id = e.source_outlet_id\n", "      AND final.receiver_outlet_id = e.destination_outlet_id\n", "      AND (e.pos_timestamp + interval '5.5 hrs')::date = final.\"date\") AS semi\n", "   GROUP BY 1,\n", "            2,\n", "            3) base\n", "JOIN lake_retail.console_outlet c ON c.id = base.sender_outlet_id\n", "AND c.active = 1\n", "AND c.device_id <> 47\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 2,\n", "         1\n", "         \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "72baf373-67e1-4675-9a4a-a55a34374ccf", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "b7b657f5-a0fb-4249-b0e8-e59d1fd2c378", "metadata": {}, "outputs": [], "source": ["df = data.copy(deep=True)"]}, {"cell_type": "markdown", "id": "af9e881f-5420-4cdf-8e81-495df7818cae", "metadata": {}, "source": ["query2 = f\"\"\"SELECT base.\"Date\",\n", "       base.sender_outlet_id AS \"source outlet id\",\n", "       c.name AS \"source outlet name\",\n", "       base.receiver_outlet_id AS \"destination outlet id\",\n", "       c1.name AS \"destination outlet name\",\n", "       SUM(base.total_eligible_invoices) AS \"total eligible invoices\",\n", "       SUM(base.\"pending invoices\") AS \"total pending invoices\",\n", "       round(\"total pending invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of pending\",\n", "       sum(base.\"success invoices\") AS \"total success invoices\",\n", "       round(\"total success invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of success\",\n", "       sum(base.\"other invoices\") AS \"total other invoices\",\n", "       round(\"total other invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of others\"\n", "FROM\n", "  (SELECT semi.sender_outlet_id,\n", "          semi.receiver_outlet_id,\n", "          semi.\"date\",\n", "          count(DISTINCT semi.invoice_id) AS total_eligible_invoices,\n", "          count(CASE\n", "                    WHEN semi.status = 1 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"pending invoices\",\n", "          count(CASE\n", "                    WHEN semi.status = 3 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"success invoices\",\n", "          count(CASE\n", "                    WHEN semi.status NOT IN (1,3) THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"other invoices\"\n", "   FROM\n", "     (SELECT final.sender_outlet_id,\n", "             final.receiver_outlet_id,\n", "             final.\"date\",\n", "             e.status,\n", "             e.invoice_id\n", "      FROM lake_pos.pos_ewaybill e\n", "      JOIN\n", "        (SELECT p.outlet_id AS sender_outlet_id,\n", "                s.merchant_outlet_id AS receiver_outlet_id,\n", "                (p.pos_timestamp + interval '5.5 hrs')::date AS \"date\"\n", "         FROM lake_pos.pos_invoice p\n", "         JOIN lake_ims.ims_sto_details s ON s.sto_id::int = p.grofers_order_id::int\n", "         JOIN lake_retail.console_outlet AS c ON c.id = p.outlet_id\n", "         WHERE p.invoice_type_id IN (5,\n", "                                     14,\n", "                                     16)\n", "           AND c.business_type_id IN (1,\n", "                                      12)\n", "           AND (p.pos_timestamp + interval '5.5 hrs')::date = CURRENT_DATE-1\n", "         GROUP BY 1,\n", "                  2,\n", "                  3\n", "         HAVING sum(p.actual_sales) >50000) FINAL ON final.sender_outlet_id = e.source_outlet_id\n", "      AND final.receiver_outlet_id = e.destination_outlet_id\n", "      AND (e.pos_timestamp + interval '5.5 hrs')::date = final.\"date\") AS semi\n", "   GROUP BY 1,\n", "            2,\n", "            3) base\n", "JOIN lake_retail.console_outlet c ON c.id = base.sender_outlet_id\n", "AND c.active = 1\n", "AND c.device_id <> 47\n", "JOIN lake_retail.console_outlet c1 ON c1.id = base.receiver_outlet_id\n", "AND c1.active = 1\n", "AND c.device_id <> 47\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5\n", "ORDER BY 2,\n", "         1,\n", "         4\"\"\""]}, {"cell_type": "markdown", "id": "5a87f37c-b905-459c-b525-ba25687c10af", "metadata": {}, "source": ["data2 = pd.read_sql_query(query2, redshift)"]}, {"cell_type": "markdown", "id": "d9ad37fd-1ffc-44d7-a8e9-d0393889406f", "metadata": {}, "source": ["df2 = data2.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "19b22d2a-99d7-4003-ad31-847799471a71", "metadata": {}, "outputs": [], "source": ["query3 = f\"\"\"SELECT base.sender_outlet_id AS \"source outlet id\",\n", "       c.name AS \"source outlet name\",\n", "       total_eligible_invoices AS \"total eligible invoices\",\n", "       SUM(base.\"pending invoices\") AS \"total pending invoices\",\n", "       round(\"total pending invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of pending\",\n", "       sum(base.\"success invoices\") AS \"total success invoices\",\n", "       round(\"total success invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of success\",\n", "       sum(base.\"other invoices\") AS \"total other invoices\",\n", "       round(\"total other invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of others\"\n", "FROM\n", "  (SELECT semi.sender_outlet_id,\n", "          count(DISTINCT semi.invoice_id) AS total_eligible_invoices,\n", "          count(CASE\n", "                    WHEN semi.status = 1 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"pending invoices\",\n", "          count(CASE\n", "                    WHEN semi.status = 3 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"success invoices\",\n", "          count(CASE\n", "                    WHEN semi.status NOT IN (1,3) THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"other invoices\"\n", "   FROM\n", "     (SELECT i.invoice_id,\n", "             i.outlet_id AS sender_outlet_id,\n", "             s.merchant_outlet_id AS receiver_outlet_id,\n", "             (i.pos_timestamp + interval '5.5 hrs')::date AS \"date\",\n", "             e.status\n", "      FROM lake_pos.pos_invoice i\n", "      JOIN lake_ims.ims_sto_details s ON s.sto_id = i.grofers_order_id\n", "      JOIN lake_pos.pos_ewaybill e ON i.invoice_id = e.invoice_id\n", "      RIGHT JOIN\n", "        (SELECT p.outlet_id AS sender_outlet_id,\n", "                s.merchant_outlet_id AS receiver_outlet_id,\n", "                (p.pos_timestamp + interval '5.5 hrs')::date AS \"date\"\n", "         FROM lake_pos.pos_invoice p\n", "         JOIN lake_ims.ims_sto_details s ON s.sto_id = p.grofers_order_id\n", "         JOIN lake_retail.console_outlet AS c ON c.id = p.outlet_id\n", "         WHERE p.invoice_type_id IN (5,\n", "                                     14,\n", "                                     16)\n", "           AND c.business_type_id IN (1,\n", "                                      12)\n", "           AND (p.pos_timestamp + interval '5.5 hrs')::date BETWEEN CURRENT_DATE-2 AND CURRENT_DATE-1\n", "         GROUP BY 1,\n", "                  2,\n", "                  3\n", "         HAVING sum(p.actual_sales) >50000) FINAL ON final.sender_outlet_id = i.outlet_id\n", "      AND final.receiver_outlet_id = s.merchant_outlet_id\n", "      AND (i.pos_timestamp + interval '5.5 hrs')::date = final.\"date\") semi\n", "   GROUP BY 1) base\n", "JOIN lake_retail.console_outlet c ON c.id = base.sender_outlet_id\n", "AND c.active = 1\n", "AND c.device_id <> 47\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9301e431-9b7d-42eb-8705-cb5fcb497304", "metadata": {}, "outputs": [], "source": ["data3 = pd.read_sql_query(query3, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "d1187754-35e2-4aad-9976-2212376aacfd", "metadata": {}, "outputs": [], "source": ["df3 = data3.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f14cebcd-fa5e-4e82-b487-b9fbe06352bd", "metadata": {}, "outputs": [], "source": ["query4 = f\"\"\"SELECT base.sender_outlet_id AS \"source outlet id\",\n", "       c.name AS \"source outlet name\",\n", "       total_eligible_invoices AS \"total eligible invoices\",\n", "       SUM(base.\"pending invoices\") AS \"total pending invoices\",\n", "       round(\"total pending invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of pending\",\n", "       sum(base.\"success invoices\") AS \"total success invoices\",\n", "       round(\"total success invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of success\",\n", "       sum(base.\"other invoices\") AS \"total other invoices\",\n", "       round(\"total other invoices\"*100.0/\"total eligible invoices\",2) AS \"percentage of others\"\n", "FROM\n", "  (SELECT semi.sender_outlet_id,\n", "          count(DISTINCT semi.invoice_id) AS total_eligible_invoices,\n", "          count(CASE\n", "                    WHEN semi.status = 1 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"pending invoices\",\n", "          count(CASE\n", "                    WHEN semi.status = 3 THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"success invoices\",\n", "          count(CASE\n", "                    WHEN semi.status NOT IN (1,3) THEN semi.invoice_id\n", "                    ELSE NULL\n", "                END) AS \"other invoices\"\n", "   FROM\n", "     (SELECT i.invoice_id,\n", "             i.outlet_id AS sender_outlet_id,\n", "             s.merchant_outlet_id AS receiver_outlet_id,\n", "             (i.pos_timestamp + interval '5.5 hrs')::date AS \"date\",\n", "             e.status\n", "      FROM lake_pos.pos_invoice i\n", "      JOIN lake_ims.ims_sto_details s ON s.sto_id = i.grofers_order_id\n", "      JOIN lake_pos.pos_ewaybill e ON i.invoice_id = e.invoice_id\n", "      RIGHT JOIN\n", "        (SELECT p.outlet_id AS sender_outlet_id,\n", "                s.merchant_outlet_id AS receiver_outlet_id,\n", "                (p.pos_timestamp + interval '5.5 hrs')::date AS \"date\"\n", "         FROM lake_pos.pos_invoice p\n", "         JOIN lake_ims.ims_sto_details s ON s.sto_id = p.grofers_order_id\n", "         JOIN lake_retail.console_outlet AS c ON c.id = p.outlet_id\n", "         WHERE p.invoice_type_id IN (5,\n", "                                     14,\n", "                                     16)\n", "           AND c.business_type_id IN (1,\n", "                                      12)\n", "           AND (p.pos_timestamp + interval '5.5 hrs')::date BETWEEN CURRENT_DATE-7 AND CURRENT_DATE-1\n", "         GROUP BY 1,\n", "                  2,\n", "                  3\n", "         HAVING sum(p.actual_sales) >50000) FINAL ON final.sender_outlet_id = i.outlet_id\n", "      AND final.receiver_outlet_id = s.merchant_outlet_id\n", "      AND (i.pos_timestamp + interval '5.5 hrs')::date = final.\"date\") semi\n", "   GROUP BY 1) base\n", "JOIN lake_retail.console_outlet c ON c.id = base.sender_outlet_id\n", "AND c.active = 1\n", "AND c.device_id <> 47\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6a753e05-331c-4f97-a6c2-412a08e7c1ad", "metadata": {}, "outputs": [], "source": ["data4 = pd.read_sql_query(query4, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "c917ded4-f640-4bde-9b17-ff3f81e75900", "metadata": {}, "outputs": [], "source": ["df4 = data4.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6a17d578-6663-474e-a013-421501856c3f", "metadata": {}, "outputs": [], "source": ["main_df = df[[\"source outlet name\", \"total eligible invoices\", \"percentage of pending\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "ed8b07d5-a6c2-43ef-b649-10775bf897a1", "metadata": {}, "outputs": [], "source": ["main_df3 = df3[\n", "    [\"source outlet name\", \"total eligible invoices\", \"percentage of pending\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b8eb602e-e370-4137-9a7b-adb47b0889fc", "metadata": {}, "outputs": [], "source": ["main_df4 = df4[\n", "    [\"source outlet name\", \"total eligible invoices\", \"percentage of pending\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ee22e93f-914e-45ce-88ee-9baa048c4386", "metadata": {}, "outputs": [], "source": ["main_df = main_df.rename(\n", "    columns={\n", "        \"percentage of pending\": \"Invoices dispatched without ewaybill (%) yesterday\",\n", "        \"source outlet name\": \"FC Name\",\n", "        \"total eligible invoices\": \"Total eligible invoices for ewaybill yesterday\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dac74807-6216-4d3b-a890-bcfca585daf4", "metadata": {}, "outputs": [], "source": ["main_df3 = main_df3.rename(\n", "    columns={\n", "        \"percentage of pending\": \"Invoices dispatched without ewaybill (%) (past 2 days)\",\n", "        \"source outlet name\": \"FC Name\",\n", "        \"total eligible invoices\": \"Total eligible invoices for ewaybill (past 2 days)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42fb6889-1289-4ba7-9de9-b67df0b4cf82", "metadata": {}, "outputs": [], "source": ["main_df4 = main_df4.rename(\n", "    columns={\n", "        \"percentage of pending\": \"Invoices dispatched without ewaybill (%) (past week)\",\n", "        \"source outlet name\": \"FC Name\",\n", "        \"total eligible invoices\": \"Total eligible invoices for ewaybill (past week)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0788e495-7b00-4cbe-b611-6dcc00440ac8", "metadata": {}, "outputs": [], "source": ["main_main_df = pd.merge(main_df4, main_df3, how=\"left\", on=\"FC Name\")"]}, {"cell_type": "code", "execution_count": null, "id": "2bf5a8fb-0ba5-46a4-935b-1e8c93cefc28", "metadata": {}, "outputs": [], "source": ["main_main_df = pd.merge(main_main_df, main_df, how=\"left\", on=\"FC Name\")"]}, {"cell_type": "code", "execution_count": null, "id": "3c8ca850-c05c-441b-b0a0-a6b52e877a61", "metadata": {}, "outputs": [], "source": ["# df.to_csv(\"Ewaybill_adoption_at_source.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "165c5dfc-6f9a-47bc-b977-9e11f828e913", "metadata": {}, "outputs": [], "source": ["# df2.to_csv(\"Ewaybill_adoption_at_source_destination.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "bb9536ba-e6c7-40a2-a355-6fe1a5b62490", "metadata": {}, "outputs": [], "source": ["mail_summary = main_main_df[\n", "    [\n", "        \"FC Name\",\n", "        \"Total eligible invoices for ewaybill yesterday\",\n", "        \"Invoices dispatched without ewaybill (%) yesterday\",\n", "        \"Total eligible invoices for ewaybill (past 2 days)\",\n", "        \"Invoices dispatched without ewaybill (%) (past 2 days)\",\n", "        \"Total eligible invoices for ewaybill (past week)\",\n", "        \"Invoices dispatched without ewaybill (%) (past week)\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3e22e90a-a95b-4514-af14-6639d79064ed", "metadata": {}, "outputs": [], "source": ["mail_summary = mail_summary.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "28e37bb0-5983-43e1-b68f-bfdadb9ce7d5", "metadata": {}, "outputs": [], "source": ["tuples = [\n", "    (\"FC Name\", \"\"),\n", "    (\"Yesterday\", \"Total eligible invoices for ewaybill\"),\n", "    (\"Yesterday\", \"Invoices dispatched without ewaybill (%)\"),\n", "    (\"Past 2 days\", \"Total eligible invoices for ewaybill\"),\n", "    (\"Past 2 days\", \"Invoices dispatched without ewaybill (%)\"),\n", "    (\"Past Week\", \"Total eligible invoices for ewaybill\"),\n", "    (\"Past Week\", \"Invoices dispatched without ewaybill (%)\"),\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "429060f6-a8ff-4f17-84fb-5b5b541a65c7", "metadata": {}, "outputs": [], "source": ["mail_summary.columns = pd.MultiIndex.from_tuples(tuples)"]}, {"cell_type": "code", "execution_count": null, "id": "9837f834-4626-4ae0-aed2-c8fbc5c7583d", "metadata": {}, "outputs": [], "source": ["emails = pb.from_sheets(\"1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "id": "03461c7e-e877-46ca-8e8f-7205e4bf9569", "metadata": {}, "outputs": [], "source": ["list1 = list(emails[\"emails\"].unique())\n", "# list1 = ['<EMAIL>']"]}, {"cell_type": "code", "execution_count": null, "id": "9fabbc5a-9997-4fc3-af3e-419b2fd45c8a", "metadata": {}, "outputs": [], "source": ["mail_summary"]}, {"cell_type": "code", "execution_count": null, "id": "62c604f2-1d13-4d6b-b186-81f633dc1345", "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email=\"<EMAIL>\",\n", "    to_email=list1,\n", "    subject=\"Eway bill Adoption for \" + str(yesterday),\n", "    html_content=\"\"\"Hi,<br> <br> Please find attached the e-way bill adoption numbers for \"\"\"\n", "    + str(yesterday)\n", "    + \"\"\"<br> <br>\"\"\"\n", "    + mail_summary.to_html(index=False)\n", "    .replace(\"<td>\", '<td align=\"center\">')\n", "    .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "    + \"\"\"<br> Please use the folowing query for a detailed view with invoice ids \"\"\"\n", "    + \"\"\"<br> https://redash-queries.grofers.com/queries/224490/source\"\"\"\n", "    # + \"\"\"<br> You can find last one week's e-way bill adoption numbers here : https://docs.google.com/spreadsheets/d/1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I/edit?usp=sharing <br>\"\"\"\n", "    + \"\"\"<br> <br> Thanks,<br>Warehouse data team.\"\"\",\n", "    # files=[\n", "    #   \"Ewaybill_adoption_at_source.csv\",\n", "    #  \"Ewaybill_adoption_at_source_destination.csv\",\n", "    # ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "09b0144a-23f5-4e32-9d38-4ca07b1791fc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}