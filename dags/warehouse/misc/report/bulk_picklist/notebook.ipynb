{"cells": [{"cell_type": "code", "execution_count": null, "id": "0345ecd5-c981-458d-96a2-5b3e116e08d7", "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "import pandas as pd\n", "import pencilbox as pb\n", "import jinja2\n", "from datetime import timedelta\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "Presto = pb.get_connection(\"[Warehouse] Presto\")\n", "today = str(date.today())"]}, {"cell_type": "code", "execution_count": null, "id": "0c61da5d-2abf-45d1-aee7-1a8275a74adc", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT base.facility_id,\n", "       base.facility_name,\n", "       base.created_on,\n", "       base.sto_id,\n", "       base.pick_list_id,\n", "       base.updated_at,\n", "       wpli.item_id,\n", "       pp.name AS item_name,\n", "       sum(wpli.picked_quantity) AS \"Quantity\",\n", "       base.remarks\n", "FROM lake_view_warehouse_location.warehouse_pick_list_item wpli\n", "JOIN lake_view_rpc.item_details pp ON pp.item_id = wpli.item_id\n", "JOIN\n", "  (SELECT c.facility_id,\n", "          f.name AS facility_name,\n", "          cast((wpl.created_at + interval '330' MINUTE) AS date) AS created_on,\n", "          wpl.pick_list_id AS pick_list_id,\n", "          wpl.id,\n", "          CASE\n", "              WHEN wpl.state = 4 THEN 'Picking Completed but not Billed'\n", "              WHEN wpl.state = 3 THEN 'Picking in Progress'\n", "              WHEN wpl.state = 16 THEN 'Billing in Progress'\n", "              WHEN wpl.state IN (12,13) THEN 'Picklist On Hold'\n", "          END AS remarks,\n", "          w.entity_id AS sto_id,\n", "          wpl.updated_at,\n", "          i.invoice_id\n", "   FROM lake_view_warehouse_location.warehouse_pick_list wpl\n", "   JOIN lake_view_warehouse_location.warehouse_wave w ON w.id = wpl.wave_id\n", "   JOIN lake_view_retail.console_outlet c ON c.id = wpl.outlet_id\n", "   JOIN lake_view_crates.facility f ON f.id = c.facility_id\n", "   AND f.id IN (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\n", "   LEFT JOIN lake_view_pos.pos_invoice i ON i.grofers_order_id = cast(w.entity_id AS varchar)\n", "   AND i.invoice_type_id IN (5,14,16)\n", "   AND i.insert_ds_ist >= cast(CURRENT_DATE - interval '45' DAY AS varchar)\n", "   WHERE wpl.state IN (3,4,12,13,16)\n", "     AND wpl.insert_ds_ist BETWEEN cast(CURRENT_DATE - interval '45' DAY AS varchar) AND cast(CURRENT_DATE - interval '3' DAY AS varchar)\n", "     AND wpl.\"type\" = 5) base ON wpli.pick_list_id = base.id\n", "WHERE wpli.insert_ds_ist >= cast(CURRENT_DATE - interval '45' DAY AS varchar)\n", "  AND invoice_id IS NULL\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         6,\n", "         5,\n", "         7,\n", "         8,\n", "         10\n", "HAVING sum(wpli.picked_quantity) > 0\n", "ORDER BY 3,\n", "         1,\n", "         4,\n", "         5,\n", "         7\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "79e11911-f62c-4c13-829a-288f64e755e6", "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, Presto)"]}, {"cell_type": "code", "execution_count": null, "id": "37e95e03-aa38-4429-8101-8911ef39b8ab", "metadata": {}, "outputs": [], "source": ["df = data.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fb643082-750d-4986-987a-898c1c4d1d48", "metadata": {}, "outputs": [], "source": ["data2 = df[[\"facility_id\", \"facility_name\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "3b0e5dda-b335-4908-b670-e47ce4ca5154", "metadata": {}, "outputs": [], "source": ["df2 = data2.drop_duplicates(inplace=False)"]}, {"cell_type": "code", "execution_count": null, "id": "1193a3bb-6324-49bc-bda8-4f03e8d8309b", "metadata": {}, "outputs": [], "source": ["df3 = df2.facility_id.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "85cc9aea-1850-49f8-a0c5-b035657458b8", "metadata": {}, "outputs": [], "source": ["df = df.rename(\n", "    columns={\n", "        \"facility_id\": \"Facility ID\",\n", "        \"facility_name\": \"Facility Name\",\n", "        \"created_on\": \"Bulk Picklist created on\",\n", "        \"pick_list_id\": \"Bulk Picklist ID\",\n", "        \"remarks\": \"Remarks\",\n", "        \"sto_id\": \"STO ID\",\n", "        \"updated_at\": \"Bulk Picklist last updated at\",\n", "        \"item_id\": \"Item ID\",\n", "        \"item_name\": \"Item Name\",\n", "        \"quantity\": \"Quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79e76079-1fe2-423e-88b5-fb0acfbb0e04", "metadata": {}, "outputs": [], "source": ["email_df = pb.from_sheets(\"1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I\", \"Sheet6\")"]}, {"cell_type": "code", "execution_count": null, "id": "d94f5943-f845-4ba4-a130-81331148a192", "metadata": {}, "outputs": [], "source": ["today = date.today()\n", "yesterday = today - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "7327651f-b3b4-4ff2-b17e-12f8fd54a873", "metadata": {}, "outputs": [], "source": ["def send_email(f_id):\n", "    f_name = df2[df2.facility_id == f_id].facility_name.unique()[0]\n", "    temp_df = df[df[\"Facility ID\"] == f_id]\n", "    temp_df1 = temp_df[\n", "        [\n", "            \"Bulk Picklist ID\",\n", "            \"STO ID\",\n", "            \"Bulk Picklist created on\",\n", "            \"Bulk Picklist last updated at\",\n", "            \"Remarks\",\n", "        ]\n", "    ].drop_duplicates(inplace=False)\n", "    temp_df1.to_csv(\"Bulk picklists pending \" + str(f_name) + \".csv\", index=False)\n", "    temp_df2 = temp_df[\n", "        [\n", "            \"Bulk Picklist ID\",\n", "            \"STO ID\",\n", "            \"Item ID\",\n", "            \"Item Name\",\n", "            \"Quantity\",\n", "            \"Bulk Picklist created on\",\n", "            \"Bulk Picklist last updated at\",\n", "            \"Remarks\",\n", "        ]\n", "    ].drop_duplicates(inplace=False)\n", "    temp_df2.to_csv(\"Items in bulk picked state \" + str(f_name) + \".csv\", index=False)\n", "    f_number = temp_df1[\"Bulk Picklist ID\"].unique().shape[0]\n", "    from_email = \"<EMAIL>\"\n", "    to_email = list(email_df[email_df.facility_id == str(f_id)].email_ids.unique())\n", "    cc = list(email_df[email_df.facility_id == \"common\"].email_ids.unique())\n", "    subject = \"Bulk Picklists pending to be billed in \" + str(f_name)\n", "    html_content = (\n", "        \"\"\"Hi,<br>\n", "    <br>Please find attached .csv for:\n", "    <br>1. The list of bulk picklists pending to be billed (post 48 hours of creation time). \n", "    <br>2. The list of items and respective quantity in bulk picked state.<br>\n", "    <br>Bulk picklists pending to be billed (post 48 hours of creation time) : \"\"\"\n", "        + str(f_number)\n", "        + \"\"\"\n", "    <br><br>You are requested to close out on these bulk picklists to avoid hung inventory (leading to PNA and FR issues).\n", "    <br>Also, if you're planning to raise request for hung inventory - request you to check state of all pending Bulk Picklist and respective items and quantity before raising request.\n", "    <br> <br> Thanks,<br>Warehouse data team\"\"\"\n", "    )\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        cc=cc,\n", "        files=[\n", "            \"Bulk picklists pending \" + str(f_name) + \".csv\",\n", "            \"Items in bulk picked state \" + str(f_name) + \".csv\",\n", "        ],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "36c89e1f-3964-4b39-8112-664914559345", "metadata": {}, "outputs": [], "source": ["if len(df.index) == 0:\n", "    pb.send_email(\n", "        from_email=\"<EMAIL>\",\n", "        to_email=[\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ],\n", "        cc=[\"<EMAIL>\", \"<EMAIL>\"],\n", "        subject=\"No Bulk Picklists Pending\",\n", "        html_content=\"\"\"Hi,<br><br>Thanks,<br>Warehouse data team\"\"\",\n", "    )\n", "else:\n", "    for f_id in df3:\n", "        send_email(f_id)"]}, {"cell_type": "code", "execution_count": null, "id": "8b8e35e5-149e-45bb-bd57-0fadbd07e1ac", "metadata": {}, "outputs": [], "source": ["df[[\"Bulk Picklist ID\"]].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "17e82e3f-932d-4c96-b4e0-d4ad125ef8f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}