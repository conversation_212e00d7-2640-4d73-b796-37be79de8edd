{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import re\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now()\n", "current_date = now.date()\n", "current_day = current_date.weekday()\n", "\n", "\n", "uid = str(uuid.uuid1())\n", "uid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "select emp_id, picker_name, \n", "case when order_count in (4,5) then '4 or 5'\n", "when order_count<=7 then '6'\n", "when order_count>=8 then '8'\n", "end as order_threshold,\n", "'' as header,\n", "'' as body\n", "from\n", "(select \n", "pl.assigned_to_name as emp_id,\n", "        u.name as picker_name,\n", "        count(distinct pl.id) as order_count\n", "        from ims.ims_order_details i\n", "        inner join retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7 and tax_location_id=9\n", "        inner join warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "        left join warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id and pl.state in (4,10)\n", "        left join warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "        left join warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "        inner join retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "        where date(i.scheduled_at) = current_date and pllh.pick_list_id is null \n", "        and date_add(pl.picking_started_at,interval 5.5 hour) between concat(date(date_add(current_timestamp,interval 5.5 hour)),' ',extract(hour from date_add(current_timestamp,interval 5.5 hour))-1,':','30:00') and\n", "        concat(date(date_add(current_timestamp,interval 5.5 hour)),' ',extract(hour from date_add(current_timestamp,interval 5.5 hour)),':','30:00') \n", "        and date_add(pll.pos_timestamp, interval 5.5 hour) between concat(date(date_add(current_timestamp,interval 5.5 hour)),' ',extract(hour from date_add(current_timestamp,interval 5.5 hour))-1,':','30:00') and\n", "        concat(date(date_add(current_timestamp,interval 5.5 hour)),' ',extract(hour from date_add(current_timestamp,interval 5.5 hour)),':','30:00')\n", "        -- and i.status_id not in (3,5) \n", "        and i.outlet not in (1445) \n", "        group by 1,2) a where order_count>3 group by 1,2,3,4,5\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data, \"14HcRbvOrddJHxMfobof9f--wZ2WgEynyjTyGlWqGcbM\", \"Notif_data\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}