{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import time\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()\n", "\n", "\n", "uid = str(uuid.uuid1())\n", "uid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "       \n", "select emp_id, picker_name, \n", "count(distinct case when picking_time<=time_given then picklist_id end) as total_eligible_picklist,\n", "count(distinct case when picking_time<=time_given and hour_flag=1 then picklist_id end) as total_eligible_picklist_2_hours\n", "from\n", "(select emp_id, picker_name, \n", "picklist_id, \n", "case when (picking_started_at_ist between \n", "concat(date(date_add(current_timestamp,interval 330 minute)),' ',extract(hour from date_add(current_timestamp,interval 330 minute))-2,':','00:00') and\n", "        concat(date(date_add(current_timestamp,interval 330 minute)),' ',extract(hour from date_add(current_timestamp,interval 330 minute)),':','00:00')) then 1\n", "else 0 end as hour_flag,\n", "timestampdiff(second,picking_started_at_ist,picking_completed_at_ist) as picking_time,\n", "items*18 as time_given\n", "from\n", "(select \n", "pl.assigned_to_name as emp_id,\n", "        u.name as picker_name, date_add(pl.picking_started_at,interval 330 minute) AS picking_started_at_ist,\n", "    date_add(pll.pos_timestamp, interval 330 minute) AS picking_completed_at_ist,\n", "        pl.id as picklist_id,\n", "        count(distinct pli.item_id) as items\n", "   \n", "        from ims.ims_order_details i\n", "        inner join retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7 and tax_location_id=9\n", "        inner join warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "        left join warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id and pl.outlet_id = co.id\n", "        and pl.state in (4,10)\n", "        left join warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "        left join warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "        left join warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "        inner join retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "        where i.scheduled_at between concat(current_date-interval 1 day,' ','18:30:00') and current_timestamp and pllh.pick_list_id is null \n", "        and i.outlet not in (1445)\n", "        group by 1,2,3,4,5) a group by 1,2,3,4,5,6) as b group by 1,2 having total_eligible_picklist>0 order by 3 desc\n", "        \n", "        \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(q, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def payload_2hours_func(x):\n", "    return {\n", "        \"body\": \"<PERSON><PERSON><PERSON> 2 ghanto mein aapne ₹\"\n", "        + str(x[\"total_eligible_picklist_2_hours\"] * 5)\n", "        + \" incentive kamaya hai, aur aaj kul milake ₹\"\n", "        + str(x[\"total_eligible_picklist\"] * 5),\n", "        \"title\": \"Macha rahe ho \" + x[\"picker_name\"] + \"👏🏻👏🏻\",\n", "        \"emp_id\": x[\"emp_id\"],\n", "        \"sound_type\": \"1\",\n", "        \"topic\": \"generic_notification\",\n", "        \"device_role\": \"PICKER\",\n", "    }\n", "\n", "\n", "def payload_summary_func(x):\n", "    return {\n", "        \"body\": \"<PERSON><PERSON> a<PERSON>\"\n", "        + str(x[\"total_eligible_picklist\"] * 5)\n", "        + \" ka incentive kamaya hai.\",\n", "        \"title\": \"<PERSON><PERSON><PERSON> ho \" + x[\"picker_name\"] + \"👏🏻👏🏻\",\n", "        \"emp_id\": x[\"emp_id\"],\n", "        \"sound_type\": \"1\",\n", "        \"topic\": \"generic_notification\",\n", "        \"device_role\": \"PICKER\",\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"payload_2_hours\"] = data.apply(payload_2hours_func, axis=1)\n", "data[\"payload_summary\"] = data.apply(payload_summary_func, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def trigger_notif_2_hours(x):\n", "    r = requests.post(\n", "        \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/devices/push-notification/\",\n", "        json=x[\"payload_2_hours\"],\n", "    )\n", "    time.sleep(1)\n", "    return r.reason\n", "\n", "\n", "def trigger_notif_summary(x):\n", "    r = requests.post(\n", "        \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/devices/push-notification/\",\n", "        json=x[\"payload_summary\"],\n", "    )\n", "    time.sleep(1)\n", "    return r.reason"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_filter = data[data[\"total_eligible_picklist_2_hours\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if now.hour == 22:\n", "    response.append(data.apply(trigger_notif_summary, axis=1))\n", "else:\n", "    response.append(data_filter.apply(trigger_notif_2_hours, axis=1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def send_to_slack(\n", "    text,\n", "    channel=\"#picker-incentive-notif\",\n", "    token=\"*****************************************************\"\n", "    #     pb.get_secret(\"retail/slack_secrets/\")[\"wh_inv_slack\"],\n", "):\n", "    client = WebClient(token=token)\n", "    try:\n", "        response = client.chat_postMessage(channel=channel, text=text)\n", "        assert response[\"message\"][\"text\"] == text\n", "    except SlackApiError as e:\n", "        assert e.response[\"ok\"] is False\n", "        assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "        print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["send_to_slack(f\"\"\"{np.size(response)} Notif Sents : {response} \"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(\n", "        pd.<PERSON><PERSON><PERSON><PERSON>(response),\n", "        \"14HcRbvOrddJHxMfobof9f--wZ2WgEynyjTyGlWqGcbM\",\n", "        \"Sheet3\",\n", "        clear_cache=True,\n", "    )\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}