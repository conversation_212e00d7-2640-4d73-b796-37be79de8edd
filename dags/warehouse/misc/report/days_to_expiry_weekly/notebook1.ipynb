{"cells": [{"cell_type": "code", "execution_count": null, "id": "c4b2d3a7-db0a-42c8-b922-96cd3f00b338", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "from zoneinfo import ZoneInfo\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "2f235c9d-e92a-4af6-afb8-835c7d95647a", "metadata": {}, "outputs": [], "source": ["today = datetime.today()\n", "snapshot_datetime = today - timed<PERSON>ta(days=today.weekday() + 1)\n", "# snapshot_datetime = (datetime.today()) - timed<PERSON>ta(days=1)\n", "worth_date = str(snapshot_datetime.date())\n", "snapshot_datetime"]}, {"cell_type": "code", "execution_count": null, "id": "1a122f86-17ab-4b57-8570-d70c71ce88d5", "metadata": {}, "outputs": [], "source": ["worth_date"]}, {"cell_type": "code", "execution_count": null, "id": "04f04b40-de73-4f55-bef0-d93eadd0b0b0", "metadata": {}, "outputs": [], "source": ["snapshot_date = datetime.strptime(worth_date, \"%Y-%m-%d\")\n", "snapshot_date"]}, {"cell_type": "code", "execution_count": null, "id": "ec2d6b03-f148-4618-967f-500658923549", "metadata": {}, "outputs": [], "source": ["worth_timestamp = snapshot_date + timedelta(hours=18, minutes=29, seconds=59)\n", "worth_timestamp"]}, {"cell_type": "code", "execution_count": null, "id": "d8acf064-5193-4288-986f-f677c1cde887", "metadata": {}, "outputs": [], "source": ["snapshot_timestamp = worth_timestamp.isoformat()\n", "snapshot_timestamp"]}, {"cell_type": "code", "execution_count": null, "id": "05e308c1-a156-49a2-bf5e-1d6af78721d8", "metadata": {}, "outputs": [], "source": ["outlets_df = pd.read_sql_query(\n", "    f\"\"\"\n", "        SELECT DISTINCT outlet_id,\n", "                    entity_vendor_id,\n", "                    r.name AS outlet_name,\n", "                    cct.name AS company_name,\n", "                    business_type_id,\n", "                    vt.legal_name,\n", "                    r.facility_id,\n", "                    cs.name AS city_name\n", "    FROM (\n", "            (SELECT outlet_id,\n", "            entity_vendor_id\n", "             FROM lake_view_reports.reports_entity_inventory_snapshot\n", "             WHERE active = True\n", "               AND quantity > 0\n", "               AND insert_ds_ist = '{worth_date}'\n", "               AND lake_active_record)\n", "          UNION ALL\n", "            (SELECT outlet_id,\n", "            entity_vendor_id\n", "             FROM lake_view_reports.reports_entity_good_inventory_snapshot\n", "             WHERE active = True\n", "               AND quantity > 0\n", "               AND insert_ds_ist = '{worth_date}'\n", "               AND lake_active_record)) AS good_inventory\n", "    JOIN lake_view_retail.console_outlet r ON r.id = good_inventory.outlet_id AND r.active = 1 AND r.device_id != 47\n", "    AND r.id not in (451,683,760,969,4146)\n", "    JOIN lake_view_retail.console_company_type cct ON r.company_type_id = cct.id\n", "    JOIN lake_view_retail.console_location cs ON cs.id = r.tax_location_id\n", "    JOIN lake_view_vms.vms_vendor_city_mapping vc on vc.vendor_id = good_inventory.entity_vendor_id\n", "    JOIN lake_view_vms.vms_vendor_city_tax_info vt on vt.vendor_city_id = vc.id\n", "    WHERE r.name NOT LIKE '%%Infra%%'\n", "      AND r.name NOT LIKE '%%Liquidation%%'\n", "      AND business_type_id IN (1,\n", "                               7,\n", "                               12,\n", "                               19,\n", "                               20)\n", "      AND (vt.legal_name not like '%%BLINK%%')\n", "    ORDER BY outlet_id\n", "\"\"\",\n", "    presto,\n", ")\n", "outlets_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f0e6a9c8-2c5c-459d-8bef-bf44de487e6f", "metadata": {}, "outputs": [], "source": ["len(outlets_df)"]}, {"cell_type": "code", "execution_count": null, "id": "da9184af-6abd-4e47-bf62-d3dc7fc17c6c", "metadata": {}, "outputs": [], "source": ["outlets_df = outlets_df[0:100]"]}, {"cell_type": "code", "execution_count": null, "id": "857386f4-d508-461f-a009-95415565e7d2", "metadata": {}, "outputs": [], "source": ["outlets_df.groupby(\"business_type_id\").outlet_id.count()"]}, {"cell_type": "code", "execution_count": null, "id": "dfd87ae7-1a76-4462-9041-a84bedf47023", "metadata": {}, "outputs": [], "source": ["rtv_df = pd.read_sql_query(\n", "    f\"\"\"select facility_id, item_id, 1 as rtv_flag\n", "        from metrics.rtv_elg_inputs\n", "        \"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "39a82665-fc16-4fa6-ab6a-e40d5f8481ff", "metadata": {}, "outputs": [], "source": ["rtv_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b74a363f-4989-46fd-bb09-ca2549ee79f7", "metadata": {}, "outputs": [], "source": ["outlets = list(outlets_df.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "529fedf3-989c-4aa0-82b7-209ad8c373b3", "metadata": {}, "outputs": [], "source": ["active_df = pd.read_sql_query(\n", "    \"\"\"SELECT DISTINCT \n", "       ma.facility_id,\n", "       ma.item_id AS \"item_id\",\n", "       1 as active\n", "    FROM lake_rpc.product_facility_master_assortment ma\n", "    LEFT JOIN lake_rpc.product_master_assortment_substate pmas ON ma.master_assortment_substate_id = pmas.id\n", "    LEFT JOIN lake_rpc.product_product rpp ON rpp.item_id = ma.item_id\n", "    INNER JOIN lake_retail.console_outlet co on co.facility_id = ma.facility_id and business_type_id in (1,7,12,19,20)\n", "    WHERE master_assortment_substate_id = 1 AND rpp.active = 1\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "16b88cf6-58fc-4df5-ba26-5ba16f347a92", "metadata": {"tags": []}, "outputs": [], "source": ["types_df = pb.from_sheets(\n", "    \"19eyyucAaKTOYOGINfXGLoD4T8QTxbn5aaa7-bTsQnE8\", \"Category mapping\"\n", ")\n", "prov_df = pb.from_sheets(\"19eyyucAaKTOYOGINfXGLoD4T8QTxbn5aaa7-bTsQnE8\", \"Sheet2\")\n", "types_df[\"l0_id\"] = types_df[\"l0_id\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "afc4c32c-bb86-4c90-a858-793b15c8210c", "metadata": {}, "outputs": [], "source": ["prov_df"]}, {"cell_type": "code", "execution_count": null, "id": "8d9d2f70-4b9f-43e1-bdf5-0142a822db37", "metadata": {}, "outputs": [], "source": ["states = pd.read_sql_query(\n", "    \"\"\"\n", "select cs.name as state_name,\n", "    r.id as outlet_id\n", "from lake_retail.console_outlet r\n", "join lake_retail.console_location cl on cl.id = r.tax_location_id\n", "join lake_retail.console_state cs on cs.id = cl.state_id\n", "\"\"\",\n", "    redshift,\n", ")\n", "states.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cc07d209-b7ab-45f0-aa91-c3445da02ea1", "metadata": {}, "outputs": [], "source": ["outlets_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c009530a-2d54-433a-9710-879cbb420c70", "metadata": {"tags": []}, "outputs": [], "source": ["outlet_counter = 0\n", "# part = 1\n", "urls = \"\"\n", "eod_df = pd.DataFrame()\n", "table_df = pd.DataFrame()\n", "entity_df = pd.DataFrame()\n", "for outlet_id in outlets:\n", "    entities = outlets_df[outlets_df[\"outlet_id\"] == outlet_id][\n", "        \"entity_vendor_id\"\n", "    ].unique()\n", "    for entity in entities:\n", "        # for outlet_id in list(outlets_df.outlet_id.unique()):\n", "        # for i in range(0, len(outlets), 75):\n", "        # print(\"Starting Batch \", part)\n", "        # batch_outlets = outlets[i : i + 75]\n", "        # for outlet_id in batch_outlets:\n", "        print(\"Starting: \", outlet_id, entity)\n", "        outlet_counter += 1\n", "        # for outlet_id in outlets:\n", "        # outlet_level_df = pd.DataFrame()\n", "        active_variants_query = f\"\"\"SELECT variant_id,\n", "                                       upc_id,\n", "                                       outlet_id,\n", "                                       entity_vendor_id,\n", "                                       company_name,\n", "                                       sum(quantity) AS quantity,\n", "                                       sum(net_worth)/sum(quantity) AS landing_price\n", "                                FROM (\n", "                                        (SELECT upc_id,\n", "                                                variant_id,\n", "                                                quantity,\n", "                                                net_worth,\n", "                                                outlet_id,\n", "                                                entity_vendor_id,\n", "                                                cct.name as company_name\n", "                                         FROM lake_view_reports.reports_entity_inventory_snapshot snap\n", "                                         JOIN lake_retail.console_outlet co ON co.id = snap.outlet_id\n", "                                         JOIN lake_retail.console_company_type cct ON co.company_type_id = cct.id\n", "                                         WHERE snap.active = True\n", "                                           AND quantity > 0\n", "                                           AND outlet_id = {outlet_id}\n", "                                           AND insert_ds_ist = '{worth_date}'\n", "                                           AND entity_vendor_id = cast({entity} as integer)\n", "                                           AND snap.lake_active_record\n", "                                         group by 1,2,3,4,5,6,7\n", "                                           )\n", "                                      UNION ALL\n", "                                        (SELECT upc_id,\n", "                                                variant_id,\n", "                                                quantity,\n", "                                                net_worth,\n", "                                                outlet_id,\n", "                                                entity_vendor_id,\n", "                                                cct.name as company_name\n", "                                         FROM lake_view_reports.reports_entity_good_inventory_snapshot good_snap\n", "                                         JOIN lake_retail.console_outlet co ON co.id = good_snap.outlet_id\n", "                                         JOIN lake_retail.console_company_type cct ON co.company_type_id = cct.id\n", "                                         WHERE good_snap.active = True\n", "                                           AND quantity > 0\n", "                                           AND outlet_id = {outlet_id}\n", "                                           AND insert_ds_ist = '{worth_date}'\n", "                                           AND entity_vendor_id = cast({entity} as integer)\n", "                                           AND good_snap.lake_active_record\n", "                                         group by 1,2,3,4,5,6,7\n", "                                           ))\n", "                                GROUP BY 1,\n", "                                         2,\n", "                                         3,\n", "                                         4,\n", "                                         5\"\"\"\n", "        active_variants = pd.read_sql_query(\n", "            sql=active_variants_query, con=pb.get_connection(\"[Warehouse] Presto\")\n", "        )\n", "        eod_df = eod_df.append(active_variants)\n", "        print(\"step1: \", active_variants.entity_vendor_id.unique())\n", "        # start_index = 0\n", "        # end_index = start_index + batch_size\n", "        rows_to_insert = []\n", "        # candidate_variants = active_variants\n", "\n", "        variant_list = active_variants.variant_id.to_list()\n", "\n", "        # if len(variant_list) == 1:\n", "        #     print('Only 1 variant, skipping...')\n", "        #     break\n", "\n", "        if len(variant_list) > 1:\n", "            # Fetch inventory stock details of variants\n", "\n", "            isd_query = \"\"\"SELECT isd.outlet_id,\n", "                           co.facility_id,\n", "                           isd.variant_id,\n", "                           coalesce(iil.entity_vendor_id, (select entity_vendor_id from lake_retail.outlet_entity_vendor_mapping\n", "                            WHERE entity_vendor_type_id = 2 AND outlet_id = {outlet}))::int as entity_vendor_id,\n", "                           CASE\n", "                               WHEN expiry_date > '2100-01-01' THEN '2100-01-01'\n", "                               WHEN expiry_date < '1970-01-01' THEN '1970-01-01'\n", "                               ELSE expiry_date\n", "                           END AS expiry_date,\n", "                           manufacture_date,\n", "                           isd.upc_id,\n", "                           date(isd.created_at + interval '5.5hrs') AS stock_in_date,\n", "                           sum(isd.\"delta\") AS stocked_in_quantity\n", "                    FROM lake_ims.ims_inventory_stock_details isd\n", "                    JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id = isd.grn_id\n", "                    JOIN lake_ims.ims_inventory_log iil ON iil.inventory_update_id = isd.inventory_update_id\n", "                    JOIN lake_retail.console_outlet co ON co.id = isd.outlet_id\n", "                    WHERE isd.outlet_id = {outlet} \n", "                     AND isd.variant_id IN ({variants})\n", "                     AND date(isd.created_at + interval '5.5hrs') <= '{worth_date}'\n", "                      AND coalesce(iil.entity_vendor_id, (select entity_vendor_id from lake_retail.outlet_entity_vendor_mapping\n", "                WHERE entity_vendor_type_id = 2 AND outlet_id = {outlet}))::int = {entity_id}\n", "                    GROUP BY 1,\n", "                             2,\n", "                             3,\n", "                             4,\n", "                             5,\n", "                             6,\n", "                             7,\n", "                             8\n", "                    \"\"\".format(\n", "                variants=\"'\" + \"','\".join([str(x) for x in variant_list]) + \"'\",\n", "                outlet=outlet_id,\n", "                entity_id=entity,\n", "                worth_date=worth_date,\n", "            )\n", "\n", "            inventory_stock_details = pd.read_sql_query(\n", "                sql=isd_query, con=pb.get_connection(\"[Warehouse] Redshift\")\n", "            )\n", "            print(\"step2: \", inventory_stock_details.entity_vendor_id.unique())\n", "            if inventory_stock_details.empty == False:\n", "                for variant_id in variant_list:\n", "                    quantity = active_variants[\n", "                        active_variants[\"variant_id\"] == variant_id\n", "                    ].quantity.values[0]\n", "                    landing_price = active_variants[\n", "                        active_variants[\"variant_id\"] == variant_id\n", "                    ].landing_price.values[0]\n", "                    variant_inventory_stock_details = inventory_stock_details[\n", "                        inventory_stock_details[\"variant_id\"] == variant_id\n", "                    ]\n", "                    try:\n", "                        # self.compute_expiry_report_for_variant(quantity, variant_inventory_stock_details, rows_to_insert)\n", "                        total_quantity = abs(quantity)\n", "                        for detail in variant_inventory_stock_details.sort_values(\n", "                            by=[\"stock_in_date\", \"expiry_date\"], ascending=False\n", "                        ).to_dict(orient=\"records\"):\n", "                            # print(detail.get('expiry_date'))\n", "                            stocked_in_quantity = int(detail.get(\"stocked_in_quantity\"))\n", "                            if total_quantity <= stocked_in_quantity:\n", "                                row = (\n", "                                    str(detail.get(\"upc_id\")),\n", "                                    str(detail.get(\"variant_id\")),\n", "                                    (total_quantity),\n", "                                    (landing_price),\n", "                                    str(detail.get(\"outlet_id\")),\n", "                                    str(detail.get(\"entity_vendor_id\")),\n", "                                    str(detail.get(\"facility_id\")),\n", "                                    str(detail.get(\"manufacture_date\"))\n", "                                    if detail.get(\"manufacture_date\")\n", "                                    else None,\n", "                                    str(detail.get(\"expiry_date\"))\n", "                                    if detail.get(\"expiry_date\")\n", "                                    else None,\n", "                                    detail.get(\"stock_in_date\")\n", "                                    # ,\n", "                                    # detail.get('entity_vendor_id')\n", "                                )\n", "                                rows_to_insert.append(row)\n", "                                break\n", "\n", "                            elif total_quantity > stocked_in_quantity:\n", "                                row = (\n", "                                    str(detail.get(\"upc_id\")),\n", "                                    str(detail.get(\"variant_id\")),\n", "                                    (stocked_in_quantity),\n", "                                    (landing_price),\n", "                                    str(detail.get(\"outlet_id\")),\n", "                                    str(detail.get(\"entity_vendor_id\")),\n", "                                    str(detail.get(\"facility_id\")),\n", "                                    str(detail.get(\"manufacture_date\"))\n", "                                    if detail.get(\"manufacture_date\")\n", "                                    else None,\n", "                                    str(detail.get(\"expiry_date\"))\n", "                                    if detail.get(\"expiry_date\")\n", "                                    else None,\n", "                                    detail.get(\"stock_in_date\")\n", "                                    # ,\n", "                                    # detail.get('entity_vendor_id')\n", "                                )\n", "                                total_quantity = total_quantity - stocked_in_quantity\n", "                                rows_to_insert.append(row)\n", "\n", "                    except:\n", "                        pass\n", "\n", "                df = pd.DataFrame(\n", "                    rows_to_insert,\n", "                    columns=[\n", "                        \"upc_id\",\n", "                        \"variant_id\",\n", "                        \"quantity\",\n", "                        \"landing_price\",\n", "                        \"outlet_id\",\n", "                        \"entity_vendor_id\",\n", "                        \"facility_id\",\n", "                        \"manufacture_date\",\n", "                        \"expiry_date\",\n", "                        \"stock_in_date\",\n", "                    ],\n", "                )\n", "\n", "                # ageing_df = ageing_df.append(df)\n", "\n", "                # df = df.xgroupby(['upc_id', 'variant_id', 'outlet_id', 'facility_id', 'landing_price' 'expiry_date']).agg({'quantity':'sum','stock_in_date':'max'}).reset_index()\n", "                df[\"landing_price\"] = df.landing_price.astype(\"float\")\n", "\n", "                #                 wm = lambda x: np.average(x, weights=df.loc[x.index, \"quantity\"])*np.sum(df.loc[x.index, \"quantity\"])\n", "                df1 = (\n", "                    df.groupby(\n", "                        [\n", "                            \"upc_id\",\n", "                            \"variant_id\",\n", "                            \"outlet_id\",\n", "                            \"facility_id\",\n", "                            \"entity_vendor_id\",\n", "                            \"expiry_date\",\n", "                            \"landing_price\",\n", "                        ]\n", "                    )\n", "                    .agg({\"quantity\": \"sum\", \"stock_in_date\": \"max\"})\n", "                    .reset_index()\n", "                )\n", "\n", "                # 'entity_vendor_id']).quantity.sum().reset_index()\n", "                # outlet_level_df = pd.concat([outlet_level_df, df1], axis=0)\n", "\n", "                item_variant_df = pd.read_sql_query(\n", "                    \"\"\"SELECT pp.item_id,\n", "                           pp.variant_id,\n", "                           pp.variant_mrp,\n", "                           pp.shelf_life,\n", "                           pp.name as item_name,\n", "                           l0,\n", "                           l0_id,\n", "                           l1,\n", "                           l1_id,\n", "                           l2,\n", "                           l2_id\n", "                    FROM lake_rpc.product_product pp\n", "                    left JOIN lake_rpc.item_category_details icd ON icd.item_id = pp.item_id\"\"\",\n", "                    redshift,\n", "                )\n", "\n", "                temp_df = df1.merge(item_variant_df, on=[\"variant_id\"], how=\"left\")\n", "                temp_df[\"outlet_id\"] = temp_df[\"outlet_id\"].astype(\"int\")\n", "                # temp_df = temp_df.merge(outlets_df, how=\"left\", on=\"outlet_id\")\n", "                # days_exp = []\n", "                # days_exp_perc = []\n", "                # days_inv = []\n", "\n", "                for index, row in temp_df.iterrows():\n", "                    try:\n", "                        days_to_exp = (\n", "                            datetime.strptime(row[\"expiry_date\"], \"%Y-%m-%d\")\n", "                            - snapshot_datetime\n", "                        ).days\n", "\n", "                        # days_of_inventory = (abs(snapshot_date.date() - row[\"stock_in_date\"])).days\n", "\n", "                        percentage_days_left = (days_to_exp * 100) / row[\"shelf_life\"]\n", "                    except:\n", "                        days_to_exp = None\n", "                        percentage_days_left = None\n", "                        # days_of_inventory = None\n", "\n", "                    # days_exp.append(days_to_exp)\n", "                    # days_exp_perc.append(percentage_days_left)\n", "                    # days_inv.append(days_of_inventory)\n", "\n", "                temp_df = temp_df.fillna(0)\n", "            print(temp_df[temp_df.duplicated()].shape[0])\n", "            print(temp_df.entity_vendor_id.unique())\n", "            entity_df = pd.concat([entity_df, temp_df], axis=0)\n", "            entity_df[\"facility_id\"] = entity_df[\"facility_id\"].astype(\"int\")\n", "            entity_df[\"entity_vendor_id\"] = entity_df[\"entity_vendor_id\"].astype(\"int\")\n", "print(entity_df[entity_df.duplicated()].shape[0])\n", "if entity_df.empty == False:\n", "\n", "    # table_df = table_df.merge(ldp, on=[\"outlet_id\", \"variant_id\",\"stockin_date\"], how=\"left\")\n", "\n", "    entity_df = entity_df.merge(rtv_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "    entity_df[\"rtv_flag\"] = np.where(\n", "        entity_df[\"rtv_flag\"] == 1, \"eligible\", \"in-eligible\"\n", "    )\n", "\n", "    entity_df = pd.merge(\n", "        entity_df, active_df, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", "    ).<PERSON>na(0)\n", "    entity_df = pd.merge(\n", "        entity_df,\n", "        outlets_df[[\"outlet_id\", \"company_name\"]].drop_duplicates(),\n", "        on=[\"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    guidelines_df = pd.read_sql(\n", "        f\"\"\"\n", "        SELECT pp.variant_id, \n", "                pp.item_id,\n", "               pp.outward_guidelines\n", "        FROM lake_rpc.product_product pp\n", "    \"\"\",\n", "        redshift,\n", "    )\n", "\n", "    cpd_df = pd.read_sql(\n", "        f\"\"\"SELECT pp.variant_id, \n", "                    pp.item_id,\n", "                   pi.outlet_id,\n", "                   pi.source_entity_vendor_id as entity_vendor_id,\n", "                   sum(quantity)/30 AS cpd\n", "            FROM lake_pos.pos_invoice pi\n", "            LEFT JOIN lake_pos.pos_invoice_product_details pd ON pd.invoice_id = pi.id\n", "            LEFT JOIN lake_rpc.product_product pp ON pd.variant_id = pp.variant_id\n", "            LEFT JOIN lake_rpc.item_details id on id.item_id = pp.item_id\n", "            WHERE date(pi.pos_timestamp + interval '5.5hrs') BETWEEN (date('{worth_date}') - interval '29 days') \n", "            AND '{worth_date}'\n", "              AND pi.outlet_id in {*outlets,}\n", "              AND pi.invoice_type_id IN (5,\n", "                                         14,\n", "                                         16,\n", "                                         1)\n", "            GROUP BY 1,\n", "                     2,\n", "                     3,\n", "                     4\n", "            \"\"\",\n", "        redshift,\n", "    )\n", "\n", "    entity_df = pd.merge(\n", "        entity_df, guidelines_df, how=\"left\", on=[\"item_id\", \"variant_id\"]\n", "    )\n", "\n", "    entity_df = pd.merge(\n", "        entity_df,\n", "        cpd_df,\n", "        on=[\"item_id\", \"outlet_id\", \"variant_id\", \"entity_vendor_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    entity_df[\"cpd\"].fillna(0, inplace=True)\n", "\n", "    prov_table_df = pd.merge(\n", "        entity_df, types_df[[\"l0_id\", \"Type\"]], on=\"l0_id\", how=\"left\"\n", "    )\n", "    #     prov_table_df = prov_table_df.merge(prov_df, on=\"Type\", how=\"left\")\n", "\n", "    redshift_df = prov_table_df.drop_duplicates(keep=False).sort_values(\n", "        [\"outlet_id\", \"variant_id\"]\n", "    )\n", "\n", "    redshift_df[\"stock_in_date\"] = pd.to_datetime(redshift_df[\"stock_in_date\"])\n", "    redshift_df = redshift_df.merge(states, on=\"outlet_id\", how=\"left\")\n", "\n", "    redshift_df.rename(\n", "        columns={\"quantity\": \"total_qty\", \"total_quantity\": \"grn_qty\"}, inplace=True\n", "    )\n", "    redshift_df[\"outward_guidelines\"] = pd.to_timedelta(\n", "        redshift_df[\"outward_guidelines\"], \"d\"\n", "    )\n", "    redshift_df[\"etl_timestamp\"] = datetime.now(ist)\n", "    redshift_df[\"etl_timestamp\"] = pd.to_datetime(redshift_df[\"etl_timestamp\"])\n", "    redshift_df[\"expiry_date\"] = pd.to_datetime(redshift_df[\"expiry_date\"])\n", "\n", "    redshift_df[\"nte_date\"] = np.where(\n", "        redshift_df[\"Type\"].isin([\"Food\", \"Staples\"]),\n", "        redshift_df[\"expiry_date\"] - redshift_df[\"outward_guidelines\"],\n", "        redshift_df[\"expiry_date\"],\n", "    )\n", "\n", "    # dt_now = datetime.now(tz=ZoneInfo(\"Asia/Kolkata\")).replace(tzinfo=None)\n", "    redshift_df[\"days_to_nte\"] = (redshift_df[\"nte_date\"] - worth_timestamp).dt.days\n", "    redshift_df[\"days_to_nte\"] = np.where(\n", "        redshift_df[\"days_to_nte\"] < 0, 0, redshift_df[\"days_to_nte\"]\n", "    )\n", "    redshift_df[\"potential_sales\"] = redshift_df[\"cpd\"] * redshift_df[\"days_to_nte\"]\n", "\n", "    redshift_df[\"potential_expiry\"] = (\n", "        redshift_df[\"total_qty\"] - redshift_df[\"potential_sales\"]\n", "    )\n", "\n", "    redshift_df[\"provision_amount\"] = (\n", "        redshift_df[\"potential_expiry\"] * redshift_df[\"landing_price\"]\n", "    )\n", "\n", "    redshift_df[\"provision_amount\"] = np.where(\n", "        redshift_df[\"provision_amount\"] < 0, 0, redshift_df[\"provision_amount\"]\n", "    )\n", "\n", "    redshift_df[\"provision_amount\"] = np.where(\n", "        redshift_df[\"expiry_date\"] < \"2000-01-01\", 0, redshift_df[\"provision_amount\"]\n", "    )\n", "\n", "    # redshift_df.drop(['entity_vendor_id'], axis = 1)\n", "\n", "    # redshift_df = redshift_df.rename({'entity_vendor_id_x': 'entity_vendor_id'}, axis=1)\n", "    redshift_df[\"entity_vendor_id\"] = redshift_df[\"entity_vendor_id\"].astype(\"int\")\n", "\n", "    # only_provisions_df = redshift_df[~(redshift_df['expiry_date']<='1970-12-31')]\n", "\n", "    only_provisions_df = redshift_df.drop(\n", "        [\n", "            \"upc_id\",\n", "            \"expiry_date\",\n", "            \"stock_in_date\",\n", "            \"nte_date\",\n", "            \"company_name\",\n", "            \"days_to_nte\",\n", "        ],\n", "        axis=1,\n", "    )\n", "\n", "    only_provisions_df = (\n", "        only_provisions_df.groupby(\n", "            [\n", "                \"variant_id\",\n", "                \"outlet_id\",\n", "                \"entity_vendor_id\",\n", "                \"landing_price\",\n", "                \"item_id\",\n", "                \"variant_mrp\",\n", "                \"shelf_life\",\n", "                \"item_name\",\n", "                \"l0\",\n", "                \"l0_id\",\n", "                \"l1\",\n", "                \"l1_id\",\n", "                \"l2_id\",\n", "                # 'outlet_name',\n", "                # 'business_type_id',\n", "                \"facility_id\",\n", "                # 'city_name',\n", "                \"rtv_flag\",\n", "                \"active\",\n", "                \"outward_guidelines\",\n", "                \"cpd\",\n", "                # 'Type',\n", "                \"state_name\",\n", "                \"etl_timestamp\",\n", "            ]\n", "        )[[\"total_qty\", \"provision_amount\", \"potential_sales\", \"potential_expiry\"]]\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "\n", "    eod_df[\"landing_price\"] = eod_df[\"landing_price\"].astype(\"float\")\n", "\n", "    all_variants = only_provisions_df.merge(\n", "        eod_df, how=\"outer\", on=[\"variant_id\", \"outlet_id\", \"entity_vendor_id\"]\n", "    )\n", "    all_variants[\"total_qty\"] = all_variants[\"total_qty\"].fillna(0)\n", "\n", "    all_variants[\"non_grn_qty\"] = all_variants[\"quantity\"] - all_variants[\"total_qty\"]\n", "\n", "    all_variants.drop(\"landing_price_x\", axis=1, inplace=True)\n", "    all_variants.rename(columns={\"landing_price_y\": \"landing_price\"}, inplace=True)\n", "    all_variants[\"provision_amount\"] = all_variants[\"provision_amount\"].fillna(0)\n", "    all_variants.rename(columns={\"quantity\": \"eod_quantity\"}, inplace=True)\n", "    all_variants.drop(\"outward_guidelines\", axis=1, inplace=True)\n", "\n", "    # determining the push operation\n", "    if (\n", "        (\n", "            pd.read_sql_query(\n", "                \"\"\"\n", "            SELECT datediff(hour, max(etl_timestamp),getdate()::TIMESTAMP + interval '5.5 hrs')\n", "            FROM metrics.days_to_expiry_report_weekly\n", "            \"\"\",\n", "                redshift,\n", "            ).iat[0, 0]\n", "        )\n", "        < 4\n", "    ):\n", "        push = \"upsert\"\n", "    else:\n", "        push = \"rebuild\"\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"entity_vendor_id\", \"type\": \"int\", \"description\": \"entity_vendor_id\"},\n", "        {\"name\": \"item_id\", \"type\": \"float\", \"description\": \"item_id\"},\n", "        {\"name\": \"variant_mrp\", \"type\": \"float\", \"description\": \"variant_mrp\"},\n", "        {\"name\": \"shelf_life\", \"type\": \"float\", \"description\": \"shelf_life\"},\n", "        {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "        {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "        {\"name\": \"l0_id\", \"type\": \"float\", \"description\": \"l0_id\"},\n", "        {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "        {\"name\": \"l1_id\", \"type\": \"float\", \"description\": \"l1_id\"},\n", "        {\"name\": \"l2_id\", \"type\": \"float\", \"description\": \"l2_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"float\", \"description\": \"facility_id\"},\n", "        {\"name\": \"rtv_flag\", \"type\": \"varchar\", \"description\": \"rtv_flag\"},\n", "        {\"name\": \"active\", \"type\": \"float\", \"description\": \"active\"},\n", "        {\"name\": \"cpd\", \"type\": \"float\", \"description\": \"cpd\"},\n", "        {\"name\": \"state_name\", \"type\": \"varchar\", \"description\": \"state_name\"},\n", "        {\"name\": \"etl_timestamp\", \"type\": \"datetime\", \"description\": \"etl_timestamp\"},\n", "        {\"name\": \"total_qty\", \"type\": \"float\", \"description\": \"total_qty\"},\n", "        {\n", "            \"name\": \"provision_amount\",\n", "            \"type\": \"float\",\n", "            \"description\": \"provision_amount\",\n", "        },\n", "        {\"name\": \"potential_sales\", \"type\": \"float\", \"description\": \"potential_sales\"},\n", "        {\n", "            \"name\": \"potential_expiry\",\n", "            \"type\": \"float\",\n", "            \"description\": \"potential_expiry\",\n", "        },\n", "        {\"name\": \"upc_id\", \"type\": \"varchar\", \"description\": \"upc_id\"},\n", "        {\"name\": \"company_name\", \"type\": \"varchar\", \"description\": \"company_name\"},\n", "        {\n", "            \"name\": \"eod_quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"eod_snapshot_quantity\",\n", "        },\n", "        {\"name\": \"landing_price\", \"type\": \"float\", \"description\": \"landing_price\"},\n", "        {\"name\": \"non_grn_qty\", \"type\": \"float\", \"description\": \"non_grn_qty\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"days_to_expiry_report_weekly\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"outlet_id\", \"variant_id\", \"entity_vendor_id\"],\n", "        \"sortkey\": [\"outlet_id\", \"variant_id\"],\n", "        \"incremental_key\": \"etl_timestamp\",\n", "        \"load_type\": push,  # append, rebuild, upsert\n", "        \"table_description\": \"This tables contains the days left for an item to expire, in buckets of remaining shelf life as a percentage\",\n", "    }\n", "    pb.to_redshift(all_variants, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "eb4d8da1-1813-4537-930a-46cb4aabb8e1", "metadata": {}, "outputs": [], "source": ["print(\"done\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}