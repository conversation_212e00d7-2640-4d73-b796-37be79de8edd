# -*- coding: utf-8 -*-

import os
import json
import logging
from contextlib import closing
from datetime import datetime, timedelta

import requests

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.dummy_operator import DummyOperator
from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            node_selector={"nodetype": "spot"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image="442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable",
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1.5, "memory": "14G"},
                        requests={"cpu": 1, "memory": "12G"},
                    ),
                )
            ],
        )
    )
}


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter-service-data.prod-sgp-k8s.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "U03SCGRB152"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S03TEACQKDW> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2022-11-26T00:00:00", "%Y-%m-%dT%H:%M:%S"),
}

dag = DAG(
    dag_id="warehouse_misc_report_days_to_expiry_weekly_v4",
    default_args=args,
    schedule_interval="05 0 * * 1",
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "warehouse_misc_report_days_to_expiry_weekly_v4",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


notebook_task_bag = {}


notebook_task_1 = PapermillOperator(
    task_id="run_notebook1_1",
    input_nb="{cwd}/notebook1.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook1_1.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook1_1"] = notebook_task_1


notebook_task_2 = PapermillOperator(
    task_id="run_notebook2_2",
    input_nb="{cwd}/notebook2.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook2_2.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook2_2"] = notebook_task_2


notebook_task_3 = PapermillOperator(
    task_id="run_notebook3_3",
    input_nb="{cwd}/notebook3.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook3_3.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook3_3"] = notebook_task_3


notebook_task_4 = PapermillOperator(
    task_id="run_notebook4_4",
    input_nb="{cwd}/notebook4.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook4_4.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook4_4"] = notebook_task_4


notebook_task_5 = PapermillOperator(
    task_id="run_notebook5_5",
    input_nb="{cwd}/notebook5.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook5_5.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook5_5"] = notebook_task_5


notebook_task_6 = PapermillOperator(
    task_id="run_notebook6_6",
    input_nb="{cwd}/notebook6.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook6_6.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook6_6"] = notebook_task_6


notebook_task_7 = PapermillOperator(
    task_id="run_notebook7_7",
    input_nb="{cwd}/notebook7.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook7_7.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook7_7"] = notebook_task_7


notebook_task_8 = PapermillOperator(
    task_id="run_notebook8_8",
    input_nb="{cwd}/notebook8.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook8_8.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook8_8"] = notebook_task_8


notebook_task_9 = PapermillOperator(
    task_id="run_notebook9_9",
    input_nb="{cwd}/notebook9.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook9_9.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook9_9"] = notebook_task_9


notebook_task_10 = PapermillOperator(
    task_id="run_notebook10_10",
    input_nb="{cwd}/notebook10.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook10_10.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook10_10"] = notebook_task_10


notebook_task_11 = PapermillOperator(
    task_id="run_notebook11_11",
    input_nb="{cwd}/notebook11.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook11_11.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook11_11"] = notebook_task_11


notebook_task_12 = PapermillOperator(
    task_id="run_notebook12_12",
    input_nb="{cwd}/notebook12.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook12_12.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook12_12"] = notebook_task_12


notebook_task_13 = PapermillOperator(
    task_id="run_notebook13_13",
    input_nb="{cwd}/notebook13.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/warehouse_misc_report_days_to_expiry_weekly_v4/{{ run_id }}/run_notebook13_13.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/warehouse/misc/report/days_to_expiry_weekly"
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)
notebook_task_bag["run_notebook13_13"] = notebook_task_13


branch_out_1 = DummyOperator(
    task_id="branch_out_1", dag=dag, executor_config=k8s_executor_config
)
notebook_task_bag["branch_out_1"] = branch_out_1


join_1 = DummyOperator(task_id="join_1", dag=dag, executor_config=k8s_executor_config)
notebook_task_bag["join_1"] = join_1


branch_out_1 >> notebook_task_bag["run_notebook1_1"]
notebook_task_bag["run_notebook1_1"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook2_2"]
notebook_task_bag["run_notebook2_2"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook3_3"]
notebook_task_bag["run_notebook3_3"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook4_4"]
notebook_task_bag["run_notebook4_4"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook5_5"]
notebook_task_bag["run_notebook5_5"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook6_6"]
notebook_task_bag["run_notebook6_6"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook7_7"]
notebook_task_bag["run_notebook7_7"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook8_8"]
notebook_task_bag["run_notebook8_8"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook9_9"]
notebook_task_bag["run_notebook9_9"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook10_10"]
notebook_task_bag["run_notebook10_10"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook11_11"]
notebook_task_bag["run_notebook11_11"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook12_12"]
notebook_task_bag["run_notebook12_12"] >> join_1

branch_out_1 >> notebook_task_bag["run_notebook13_13"]
notebook_task_bag["run_notebook13_13"] >> join_1


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
