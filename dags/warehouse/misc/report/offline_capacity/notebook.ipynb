{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import os\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import requests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Google Sheet variable\n", "# External sheet\n", "SHEET_ID = \"1NFQ6hAbvzFadvWCy1wJ72JQSLIWgBeMaERGlzeZa_G4\"\n", "SHEET_NAME = \"offline_capacity\"\n", "# old sheet\n", "# data for weight rule\n", "# SHEET_ID = \"1yUeZhiF6fGbOFDlRO1Y94BpftCbLFaL89vrMlzy3nQM\"\n", "# SHEET_NAME='weights'\n", "weights = pb.from_sheets(SHEET_ID, SHEET_NAME)\n", "weights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Date Time Variable\n", "\n", "NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = str(NOW_IST.date())\n", "RUN_TIMESTAMP = str(\n", "    NOW_IST.replace(minute=0, second=0, microsecond=0) + <PERSON><PERSON><PERSON>(hours=1)\n", ")\n", "RUN_HOUR = str((NOW_IST + timedelta(hours=1)).hour)\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# File path\n", "\n", "BASE_PATH = \"data\"\n", "OUTPUT_FOLDERS = [\"wt_capacity_reallocation\", \"capacity_reallocation\"]\n", "\n", "MERCHANT_REBALANCED_CAPACITY_FILE_PATH = (\n", "    \"data/{}/wt_capacity_reallocation/offline_merchant_wt_capacity_reallocation_{}.csv\"\n", ").format(RUN_DATE, RUN_HOUR)\n", "# MERCHANT_REBALANCED_CAPACITY_FILE_PATH = ('data/{}/premium_capacity_reallocation/merchant_premium_capacity_reallocation_{}.csv').format(RUN_DATE,RUN_HOUR)\n", "MERCHANT_REBALANCED_CAPACITY_FILE_PATH"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creating Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def setup_output_data_folder(RUN_DATE):\n", "\n", "    for folder in OUTPUT_FOLDERS:\n", "        path = os.path.join(BASE_PATH, RUN_DATE, folder)\n", "        #         print(path)\n", "        os.makedirs(path, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rename(df):\n", "\n", "    df = df.rename(\n", "        columns={\n", "            \"start_slot\": \"slot_start\",\n", "            \"end_slot\": \"slot_end\",\n", "            \"checkout_merchant_id\": \"merchant_id\",\n", "            \"delivery_date\": \"date\",\n", "            \"merch_capacity_planned\": \"total_capacity\",\n", "            \"merch_capacity_actual\": \"reached_capacity\",\n", "            #                             f\"{type}_normal_capacity\": \"normal_capacity\",\n", "            #                             f\"{type}_reschedule_orders\" : \"reschedule_capacity\",\n", "            #                             f\"{type}_reschedule_capacity\": \"reschedule_capacity\",\n", "            #                             f\"{type}_premium_orders\": \"premium_capacity\",\n", "            #                             f\"{type}_premium_capacity\": \"premium_capacity\",\n", "            #                             f\"{type}_heavy_capacity\": \"heavy_capacity\",\n", "        }\n", "    )\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_function(df, message):\n", "    if df.shape[0] == 0:\n", "        print(\"--------------------------------------------------------------------\")\n", "        print(message)\n", "        print(\"--------------------------------------------------------------------\")\n", "        pan_india_capacity_file()\n", "\n", "\n", "#         exit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pan_india_capacity_file():\n", "\n", "    df = pd.read_sql(\n", "        query.format(a=a, b=b, c=c, d=d, wt_limit=wt_limit), redshift\n", "    ).reset_index(drop=True)\n", "    df = rename(df)\n", "    #     df = df[['delivery_date', 'checkout_merchant_id', 'start_slot', 'end_slot', 'merch_capacity_planned']]\n", "    #     df1=df[['date','merchant_id','slot_start','slot_end','total_capacity']]\n", "    #     df1['normal_capacity']=df_merchant_upload['total_capacity']\n", "    #     df1['reschedule_capacity']=0\n", "    #     df1['premium_capacity']=0\n", "    #     df1['heavy_capacity']=0\n", "\n", "    #     df1 = df1.drop_duplicates()\n", "    df.to_csv(\n", "        f\"data/pan_india_post_reallocation_merchant_capacity_file.csv\", index=False\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def api_hit(type, FILE_PATH):\n", "    url = \"http://supply-orchestrator-planning.prod-sgp-k8s.grofer.io/orchestrator/api/file_management/v1/sheets/\"\n", "    payload = {\"template_type\": f\"{type}CapacityTemplate\"}\n", "    files = {\"file\": open(FILE_PATH, \"rb\")}\n", "    headers = {}\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload, files=files)\n", "    response.status_code = 250\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    if response.status_code >= 200 and response.status_code < 300:\n", "        print(f\"{type} capacity upload - SUCCESS\")\n", "    else:\n", "        print(f\"{type} capacity upload - FAIL\")\n", "        print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["setup_output_data_folder(RUN_DATE)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reading Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "-------------------------------------------------------------------orders data---------------------------------------------------------\n", "\n", "WITH orderbase_raw AS\n", "  (SELECT \n", "            o.id lm_order_id,o.created_at lm_created,o.updated_at lm_updated,o.cart_id,o.checkout_merchant_id,o.checkout_merchant_name,\n", "            (o.delivery_slot_start_time + INTERVAL '5.5 HOUR')::date delivery_slot_start_time,o.external_order_id oms_id,o.fc_id,o.order_Status,\n", "            oi.product_name,oi.product_id,oi.total_weight,oi.quantity,oi.sub_order_id wh_suborder_id,\n", "             oo.install_ts,oo.slot_type,oo.current_status,oo.type,oo.direction,oo.merchant_id oms_merchant_id,oos.backend_merchant_id,\n", "             case   when checkout_merchant_id=28712 then '{a}'\n", "                    when checkout_merchant_id=28778 then '{b}'\n", "                    when checkout_merchant_id=28779 then '{c}'\n", "                    when checkout_merchant_id=28832 then '{d}' else 0 end as wt_allowed\n", " FROM lm_order o\n", "   INNER JOIN lm_order_item oi ON oi.order_id = o.id\n", "   INNER JOIN oms_order oo ON oo.id = o.external_order_id\n", "   left join oms_suborder oos on oo.id=oos.order_id\n", "  \n", "   WHERE CHECKOUT_MERCHANT_NAME LIKE '%%B2B%%'\n", "   AND (o.delivery_slot_start_time + INTERVAL '5.5 HOUR')::date >= CURRENT_DATE + interval '1 day'\n", "   AND oo.type = 'RetailForwardOrder'\n", "       and o.checkout_merchant_id in (28779, 28832, 28712, 28778)\n", "--   and  o.external_order_id=58018676\n", "--   GROUP BY 1,\n", "--             2,\n", "--             3,\n", "--             4\n", "            ),\n", "            \n", "            \n", "orderbase_summary as\n", "(\n", "select oms_id as order_id,checkout_merchant_id as checkout_merchant_id,checkout_merchant_name as merchant_name, delivery_slot_start_time as delivery_date,wt_allowed,\n", " count(distinct product_id) skus,sum(total_weight) weight, sum(quantity) qtys from orderbase_raw \n", "group by 1,2,3,4,5\n", "),\n", "\n", "table_merchant as (\n", "SELECT  checkout_merchant_id::int,merchant_name,delivery_date,wt_allowed, count(order_id) ord_cnt, sum(weight) weight_tot\n", "FROM orderbase_summary \n", "group by 1,2,3,4 \n", "),\n", "\n", "table_all as (\n", "select '10000'::int as checkout_merchant_id,'ALL' as merchant_name, delivery_date,\n", " sum(wt_allowed) as wt_allowed, sum(ord_cnt) ord_cnt, sum(weight_tot) weight_tot\n", "FROM table_merchant\n", "group by 1,2,3\n", "),\n", "\n", "table_alls as (\n", "select  checkout_merchant_id,  merchant_name, delivery_date,\n", "case when merchant_name='<PERSON><PERSON>' then {wt_limit} else wt_allowed end as  wt_allowed, ord_cnt, weight_tot \n", "FROM table_all\n", "),\n", "\n", "orders_data as\n", "(select * from table_alls\n", "union \n", "select * from table_merchant order by 1,3\n", "),\n", "\n", "\n", "\n", "-------------------------------------------------------------------capacity data---------------------------------------------------------\n", "\n", "\n", " capacity_raw as(\n", "SELECT\n", "    warehouse_external_id AS warehouse_id,\n", "    warehouse_name,\n", "    \n", "    merchant_external_id AS merchant_id,\n", "    merchant_name,\n", "    \n", "    cms_city_id,city_name,slot_type,\n", "    \n", "    -- station_external_name AS station_name,\n", " \n", "    DATE(slot_start + interval '5.5 hours') AS del_date,\n", "    \n", "    TO_CHAR(slot_start + interval '5.5 hours','HH24:MI') || ' - ' || \n", "    TO_CHAR(slot_end + interval '5.5 hours','HH24:MI') AS slot,\n", "    \n", "    TO_CHAR(slot_start + interval '5.5 hours','HH24:MI') AS start_slot,\n", "    TO_CHAR(slot_end + interval '5.5 hours','HH24:MI') AS end_slot,\n", "        COALESCE(SUM(warehouse_planned),0) AS wh_capacity_planned,\n", "    COALESCE(SUM(warehouse_actual),0) AS wh_capacity_actual,\n", "    COALESCE(SUM(warehouse_available),0) AS wh_capacity_available,\n", "    \n", "    \n", "    COALESCE(SUM(merchant_planned),0) AS merch_capacity_planned,\n", "    COALESCE(SUM(merchant_actual),0) AS merch_capacity_actual,\n", "    COALESCE(SUM(merchant_available),0) AS merch_capacity_available\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'normal' THEN merchant_planned END),0) AS merchant_normal_capacity,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'crm_reschedule' THEN merchant_planned END),0) AS merchant_reschedule_capacity,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'crm_reschedule' THEN merchant_actual END),0) AS merchant_reschedule_orders,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'crm_reschedule' THEN merchant_available END),0) AS merchant_reschedule_available_capacity,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'premium' THEN merchant_planned END),0) AS merchant_premium_capacity,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'premium' THEN merchant_actual END),0) AS merchant_premium_orders,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'premium' THEN merchant_available END),0) AS merchant_premium_available_capacity,\n", "    -- COALESCE(SUM(CASE WHEN slot_type = 'heavy' THEN merchant_planned END),0) AS merchant_heavy_capacity,\n", "    \n", "FROM sco_path_capacity AS spc\n", "INNER JOIN sco_network_path AS snp\n", "    ON spc.warehouse_id = snp.warehouse_id \n", "    AND spc.merchant_id = snp.merchant_id \n", "    AND spc.station_id = snp.station_id \n", "    AND snp.active = TRUE\n", "    AND date(spc.slot_start) >= CURRENT_DATE + interval '1 day'\n", "    -- BETWEEN (current_date::TIMESTAMP - INTERVAL '5.5 hours')\n", "    --     AND (current_date::TIMESTAMP + INTERVAL '1 DAY' - INTERVAL '5.5 hours')\n", "    and    merchant_name  like '%%B2B%%'\n", "    and     warehouse_name    not like '%%Cold%%'\n", "    and   merchant_planned !=0\n", "            and  spc.merchant_external_id in (28779, 28832, 28712, 28778)\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11),\n", "\n", "\n", "\n", "merchant_table as(\n", "select merchant_id,merchant_name, \n", "-- cms_city_id,city_name,\n", "slot_type,del_date,slot,start_slot,end_slot,\n", "sum(merch_capacity_planned) merch_capacity_planned,sum(merch_capacity_actual) merch_capacity_actual,sum(merch_capacity_available) merch_capacity_available,\n", "sum(wh_capacity_planned) wh_capacity_planned,sum(wh_capacity_actual) wh_capacity_actual,sum(wh_capacity_available) wh_capacity_avail\n", "from capacity_raw\n", "group by 1,2,3,4,5,6,7\n", "),\n", "\n", "all_table as\n", "(\n", "select \n", "10000::int as merchant_id,'ALL' as merchant_name, \n", "-- cms_city_id,city_name,\n", "slot_type,del_date, slot,start_slot,end_slot,\n", "sum(merch_capacity_planned) merch_capacity_planned,sum(merch_capacity_actual) merch_capacity_actual,sum(merch_capacity_available) merch_capacity_available,\n", "sum(wh_capacity_planned) wh_capacity_planned,sum(wh_capacity_actual) wh_capacity_actual,sum(wh_capacity_avail) wh_capacity_avail\n", "from merchant_table\n", "group by 1,2,3,4,5,6,7\n", "),\n", "\n", "capacity_data as\n", "(select * from merchant_table\n", "union\n", "\n", "select * from all_table\n", ")\n", "\n", "\n", "select od.*,ceil((weight_tot/wt_allowed)*100)::int as wt_used_perc, \n", "-- cd.cms_city_id,cd.city_name,\n", "cd.slot_type, cd.slot,cd.start_slot,cd.end_slot,\n", "cd.merch_capacity_planned,cd.merch_capacity_actual,cd.merch_capacity_available,wh_capacity_planned,wh_capacity_actual,wh_capacity_avail\n", "from orders_data od \n", "left join capacity_data cd on od.merchant_name=cd.merchant_name and od.delivery_date=cd.del_date \n", "-- where checkout_merchant_id=10000\n", "order by 1,3\n", "\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check if Weight condition is present or missing, if missing then terminate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dff=pd.DataFrame()\n", "\n", "# b=weights.loc[weights['merchant_id']=='28778']\n", "if weights.shape[0] == 0:\n", "\n", "    a, b, c, d = 1, 1, 1, 1\n", "    check_function(weights, \"No weight rule found, Exiting!!\")\n", "\n", "else:\n", "    a = weights.loc[weights.merchant_id == \"28712\", \"weight_allowed\"].values[0]\n", "    b = weights.loc[weights.merchant_id == \"28778\", \"weight_allowed\"].values[0]\n", "    c = weights.loc[weights.merchant_id == \"28779\", \"weight_allowed\"].values[0]\n", "    d = weights.loc[weights.merchant_id == \"28832\", \"weight_allowed\"].values[0]\n", "    wt_limit = int(\n", "        weights.loc[weights.merchant_id == \"10000\", \"weight_allowed\"].values[0]\n", "    )\n", "    #     wt_limit=15000\n", "    wt_threshold = int(\n", "        weights.loc[weights.merchant_id == \"10001\", \"weight_allowed\"].values[0]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql(\n", "    query.format(a=a, b=b, c=c, d=d, wt_limit=wt_limit), redshift\n", ").reset_index(drop=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = df[df[\"merchant_name\"] == \"ALL\"]\n", "x = x[x[\"wt_used_perc\"] > wt_threshold]\n", "x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates = x[x[\"merchant_name\"] == \"ALL\"][\"delivery_date\"].drop_duplicates()\n", "\n", "dates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# rslt_df = df[df['wt_used_perc'] > wt_threshold].reset_index(drop=True)\n", "rslt_df = df[df[\"delivery_date\"].isin(dates)].reset_index(drop=True)\n", "rslt_df = rslt_df[rslt_df[\"merchant_name\"] != \"ALL\"]\n", "rslt_df = rslt_df[rslt_df[\"merch_capacity_planned\"] > rslt_df[\"merch_capacity_actual\"]]\n", "rslt_df = rename(rslt_df)\n", "rslt_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check if there are any weight conditions that needs to be updated, if no update then exit "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_function(rslt_df, \"No merchant where capacity needs to be updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cols reqd\n", "# day,date,city,merchant_id,merchant_name,slot_start,slot_end,total_capacity,normal_capacity,reschedule_capacity,premium_capacity,heavy_capacity,reverse_pickup_capacity\n", "\n", "# pd.options.mode.chained_assignment = None\n", "# df_merchant_upload = pd.DataFrame(columns = ['date','merchant_id','slot_start','slot_end','total_capacity',\n", "#       'normal_capacity','reschedule_capacity','premium_orders','heavy_capacity'])\n", "\n", "df_merchant_upload = rslt_df[\n", "    [\n", "        \"date\",\n", "        \"merchant_id\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"total_capacity\",\n", "        \"reached_capacity\",\n", "    ]\n", "]\n", "df_merchant_upload[\"total_capacity\"] = df_merchant_upload[\"reached_capacity\"]\n", "df_merchant_upload.drop([\"reached_capacity\"], axis=1, inplace=True)\n", "# df_merchant_upload['total_capacity']=90\n", "df_merchant_upload[\"normal_capacity\"] = df_merchant_upload[\"total_capacity\"]\n", "df_merchant_upload[\"reschedule_capacity\"] = 0\n", "df_merchant_upload[\"premium_capacity\"] = 0\n", "df_merchant_upload[\"heavy_capacity\"] = 0\n", "df_merchant_upload[\"reverse_pickup_capacity\"] = 0\n", "\n", "\n", "df_merchant_upload.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merchant_upload.to_csv(MERCHANT_REBALANCED_CAPACITY_FILE_PATH, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x=df_merchant_upload[df_merchant_upload['merchant_id']==28712]\n", "\n", "# x['date']= datetime(2020, 4, 24).date()\n", "\n", "# x.to_csv(MERCHANT_REBALANCED_CAPACITY_FILE_PATH, index = False)\n", "# x['normal_capacity']=90\n", "# x['total_capacity']=90\n", "\n", "# x.to_csv(MERCHANT_REBALANCED_CAPACITY_FILE_PATH, index = False)\n", "# x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# API Hit to update the capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_merchant_upload.shape[0] != 0:\n", "    print(\"Capacity upload starting\")\n", "    api_hit(\"Merchant\", MERCHANT_REBALANCED_CAPACITY_FILE_PATH)\n", "\n", "else:\n", "    print(\"NoNo merchant where capacity needs to be updated present\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PAN India Capacity File"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_capacity_file()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}