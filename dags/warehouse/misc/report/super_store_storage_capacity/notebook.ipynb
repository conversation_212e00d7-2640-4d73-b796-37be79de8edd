{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta, time\n", "import jinja2\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tuples = [\n", "    (\"Outlet\", \"\"),\n", "    (\"Total Capacity\", \"Facility Id\"),\n", "    (\"Total Capacity\", \"Total Storage Cap\"),\n", "    (\"Total Capacity\", \"Total Inv Qty\"),\n", "    (\"Total Capacity\", \"Storage Cap In %\"),\n", "    (\"Regular\", \"Total Storage Cap\"),\n", "    (\"Regular\", \"Total Inv Qty\"),\n", "    (\"Regular\", \"Storage Cap In %\"),\n", "    (\"Regular\", \"Blocked Inv\"),\n", "    (\"Regular\", \"Blocked Inv in %\"),\n", "    (\"Heavy\", \"Total Storage Cap\"),\n", "    (\"Heavy\", \"Total Inv Qty\"),\n", "    (\"Heavy\", \"Storage Cap In %\"),\n", "    (\"Heavy\", \"Blocked Inv\"),\n", "    (\"Heavy\", \"Blocked Inv in %\"),\n", "    (\"Cold\", \"Total Storage Cap\"),\n", "    (\"Cold\", \"Total Inv Qty\"),\n", "    (\"Cold\", \"Storage Cap In %\"),\n", "    (\"Cold\", \"Blocked Inv\"),\n", "    (\"Cold\", \"Blocked Inv in %\"),\n", "    (\"Frozen\", \"Total Storage Cap\"),\n", "    (\"Frozen\", \"Total Inv Qty\"),\n", "    (\"Frozen\", \"Storage Cap In %\"),\n", "    (\"Frozen\", \"Blocked Inv\"),\n", "    (\"Frozen\", \"Blocked Inv in %\"),\n", "    (\"FNV\", \"Total Weight Cap\"),\n", "    (\"FNV\", \"Current Total Weight\"),\n", "    (\"FNV\", \"Weight %\"),\n", "    (\"Icecream\", \"Total Storage Cap\"),\n", "    (\"Icecream\", \"Total Inv Qty\"),\n", "    (\"Icecream\", \"Storage Cap In %\"),\n", "    (\"Icecream\", \"Blocked Inv\"),\n", "    (\"Icecream\", \"Blocked Inv in %\"),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data_to_redshift():\n", "    sheet_id = \"1rE-dh2LD-5WXa9xE8PaSrosDe2vL-TiA6-u5qfmTovg\"\n", "    sheet_name = \"Summary\"\n", "    df = pb.from_sheets(sheet_id, sheet_name)\n", "    df = df.iloc[1:]\n", "    df[\"Outlet\"] = df[\"Outlet\"].str.strip()\n", "    df = df[df[\"Outlet\"] != \"\"].reset_index(drop=True)\n", "    df.columns = [\n", "        (t[0] + \" \" + t[1]).replace(\" \", \"_\") if t[1] != \"\" else t[0] for t in tuples\n", "    ]\n", "    df.insert(25, \"etl_timestamp_ist\", datetime.now(tz))\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"outlet\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Outlet name\",\n", "        },\n", "        {\n", "            \"name\": \"total_capacity_facility_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Facility id\",\n", "        },\n", "        {\n", "            \"name\": \"total_capacity_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for all storage types\",\n", "        },\n", "        {\n", "            \"name\": \"total_capacity_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for all storage types\",\n", "        },\n", "        {\n", "            \"name\": \"total_capacity_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for all storage types\",\n", "        },\n", "        {\n", "            \"name\": \"regular_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for regular storage type\",\n", "        },\n", "        {\n", "            \"name\": \"regular_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for regular storage type\",\n", "        },\n", "        {\n", "            \"name\": \"regular_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for regular storage type\",\n", "        },\n", "        {\n", "            \"name\": \"regular_blocked_inv\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv for regular storage type\",\n", "        },\n", "        {\n", "            \"name\": \"regular_blocked_inv_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv in % for regular storage type\",\n", "        },\n", "        {\n", "            \"name\": \"heavy_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for heavy storage type\",\n", "        },\n", "        {\n", "            \"name\": \"heavy_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for heavy storage type\",\n", "        },\n", "        {\n", "            \"name\": \"heavy_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for heavy storage type\",\n", "        },\n", "        {\n", "            \"name\": \"heavy_blocked_inv\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv for heavy storage type\",\n", "        },\n", "        {\n", "            \"name\": \"heavy_blocked_inv_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv in % for heavy storage type\",\n", "        },\n", "        {\n", "            \"name\": \"cold_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for cold storage type\",\n", "        },\n", "        {\n", "            \"name\": \"cold_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for cold storage type\",\n", "        },\n", "        {\n", "            \"name\": \"cold_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for cold storage type\",\n", "        },\n", "        {\n", "            \"name\": \"cold_blocked_inv\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv for cold storage type\",\n", "        },\n", "        {\n", "            \"name\": \"cold_blocked_inv_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv in % for cold storage type\",\n", "        },\n", "        {\n", "            \"name\": \"frozen_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for frozen storage type\",\n", "        },\n", "        {\n", "            \"name\": \"frozen_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for frozen storage type\",\n", "        },\n", "        {\n", "            \"name\": \"frozen_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for frozen storage type\",\n", "        },\n", "        {\n", "            \"name\": \"frozen_blocked_inv\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv for frozen storage type\",\n", "        },\n", "        {\n", "            \"name\": \"frozen_blocked_inv_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv in % for frozen storage type\",\n", "        },\n", "        {\n", "            \"name\": \"etl_timestamp_ist\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"Timestamp at which row entry is made\",\n", "        },\n", "        {\n", "            \"name\": \"fnv_total_weight_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total weight cap for fnv storage type\",\n", "        },\n", "        {\n", "            \"name\": \"fnv_current_total_weight\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total current weight for fnv storage type\",\n", "        },\n", "        {\n", "            \"name\": \"fnv_weight_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Weight in % for fnv storage type\",\n", "        },\n", "        {\n", "            \"name\": \"icecream_total_storage_cap\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total storage cap for icecream storage type\",\n", "        },\n", "        {\n", "            \"name\": \"icecream_total_inv_qty\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total inv qty for icecream storage type\",\n", "        },\n", "        {\n", "            \"name\": \"icecream_storage_cap_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Storage cap in % for icecream storage type\",\n", "        },\n", "        {\n", "            \"name\": \"icecream_blocked_inv\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv for icecream storage type\",\n", "        },\n", "        {\n", "            \"name\": \"icecream_blocked_inv_in_%\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Blocked inv in % for icecream storage type\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"super_store_storage_capacity\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"outlet\", \"etl_timestamp_ist\"],\n", "        \"sortkey\": [\"etl_timestamp_ist\"],\n", "        \"incremental_key\": \"etl_timestamp_ist\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores super stores summarized storage capacity details\",\n", "    }\n", "\n", "    pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def send_email():\n", "    sql = \"\"\"\n", "    select *\n", "    from metrics.super_store_storage_capacity\n", "    where etl_timestamp_ist =\n", "        (select max(etl_timestamp_ist)\n", "         from metrics.super_store_storage_capacity\n", "         where date(etl_timestamp_ist) = current_date - 1)\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "\n", "    if not df.empty:\n", "        df = df.drop(\"etl_timestamp_ist\", axis=\"columns\")\n", "        df.columns = pd.MultiIndex.from_tuples(tuples)\n", "\n", "        today = str(datetime.now(tz).strftime(\"%Y-%m-%d\"))\n", "        from_email = \"<EMAIL>\"\n", "\n", "        to_email = [\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "\n", "        cc = [\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"mr.ab<PERSON><PERSON><PERSON>@grofers.com\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "\n", "        subject = subject = \"FC - Super Store Storage Capacity Report - %s\" % today\n", "        loader = jinja2.FileSystemLoader(\n", "            searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/report/super_store_storage_capacity\"\n", "            #         searchpath=\"/home/<USER>/airflow-dags/daggers/warehouse/misc/report/super_store_storage_capacity\"\n", "        )\n", "        tmpl_environ = jinja2.Environment(loader=loader)\n", "        template = tmpl_environ.get_template(\"email_template.html\")\n", "        html_content = template.render(report=df, today=today, tuples=tuples)\n", "\n", "        pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "now = datetime.now(tz)\n", "\n", "if now.hour == 9:\n", "    send_email()\n", "else:\n", "    load_data_to_redshift()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}