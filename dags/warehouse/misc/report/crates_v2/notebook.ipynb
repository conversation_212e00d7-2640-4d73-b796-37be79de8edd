{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\")\n", "\n", "pd.options.mode.chained_assignment = None\n", "pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)\n", "pd.set_option(\"precision\", 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "pos = pb.get_connection(\"pos\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "\n", "end_date = (datetime.now() - timed<PERSON>ta(days=4)).date()\n", "start_date = (datetime.now() - timed<PERSON>ta(days=8)).date()\n", "mail_date = (datetime.now() - timed<PERSON>ta(days=0)).date()\n", "end_date, start_date, mail_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_query = \"\"\"\n", "with batch_dispatch_activity AS \n", "\n", "(   \n", "    select \n", "        b.external_criteria as batch_id,\n", "        max(a.expected_end_time + interval '5.5 hour') as expected_dispatch_end_time, \n", "        max(end_time  + interval '5.5 hour') as actual_dispatch_time\n", "        \n", "    from lake_logex.activity a\n", "    inner join lake_logex.batch  b on a.batch_id = b.id\n", "    where a.activity_type = 'DISPATCH' \n", "    group by 1\n", ") ,\n", "batch_handover_activity AS \n", "(\n", "    select \n", "        b.external_criteria as batch_id,\n", "        max(expected_end_time + interval '5.5 hour') as expected_handover_end_time, \n", "        max(end_time + interval '5.5 hour') as actual_handover_end_time\n", "    \n", "    from lake_logex.activity  a\n", "    inner join lake_logex.batch  b on a.batch_id = b.id\n", "    where a.activity_type = 'HANDOVER' \n", "    -- and date(a.created_date) BETWEEN  (CURRENT_DATE - interval '5 days')::date AND CURRENT_DATE::date \n", "    group by 1\n", "),\n", "\n", "\n", "table_raw as \n", "(Select date(cl1.facility_timestamp + interval '5.5 hour') as Dispatch_Date  , cl1.new_facility_id, facility.name,\n", "cl1.crate_id, c.crate_id as id ,\n", "  cl1.new_status_id as first_status_id , cl1.new_crate_entity_id as first_entity_id, \n", "  cl2.new_status_id as second_status_id, cl2.new_crate_entity_id as second_entity_id, cl2.is_success as state2_success,\n", "  (cl2.facility_timestamp + interval '5.5 hour') as second_timestamp,\n", "  cl3.new_status_id as third_status_id, cl3.new_crate_entity_id as third_entity_id, cl3.is_success as state3_success,\n", "  (cl3.facility_timestamp + interval '5.5 hour') as third_timestamp,\n", "  cl4.new_status_id as fourth_status_id, cl4.new_crate_entity_id as fourth_entity_id, cl4.is_success as state4_success,\n", "  \n", "  cl5.new_status_id as fifth_status_id, cl5.new_crate_entity_id as fifth_entity_id, cl5.is_success as state5_success,\n", "  \n", "  \n", "  ce.entity_id,\n", "  o.id as order_id,\n", "  o.station as Station_id,\n", "--   (oa.created_at + interval '5.5 hour') fe_time,\n", "  \n", "  coalesce(dp.id,fe.id) as secondmile_dp_id,\n", "  coalesce(dp.name,fe.name) as secondmile_dp_name,\n", "    coalesce(fe.employee_id) as secondmile_fe_id,\n", "  coalesce(fe.name) as secondmile_fe_name,\n", "  coalesce(fe2.employee_id) as secondmile_fe_id_p,\n", "  coalesce(fe2.name) as secondmile_fe_name_p,\n", "  \n", "  o.current_status oms_status,\n", "  so.current_status sub_oms_status,\n", "\n", "--   lo.order_status lm_status,\n", "  ims_status.status_id ims_order_status,\n", "  (ims_status.scheduled_at + interval '5.5 hour') as sch_del_time,\n", "-- so.slot_start start_slot,\n", "(timestamp 'epoch' + so.slot_start * interval '1 second')+ interval '5.5 hour' AS slot_start,\n", "(timestamp 'epoch' + so.slot_end * interval '1 second')+ interval '5.5 hour' AS slot_end,\n", "concat(concat((extract(hour from (timestamp 'epoch' + so.slot_start * interval '1 second')+ interval '5.5 hour') :: char(2)) , ' -'), \n", "(extract(hour from (timestamp 'epoch' + so.slot_end * interval '1 second')+ interval '5.5 hour') :: char(2))) as  slot,\n", "-- so.slot_end end_slot,\n", "-- (pl.min_scheduled_time + interval '5.5 hour') min_scheduled_time,\n", "-- (pl.max_scheduled_time + interval '5.5 hour') max_scheduled_time,\n", "-- (lo.delivery_slot_start_time + interval '5.5 hour') delivery_slot_start_time,\n", "-- (lo.delivery_slot_end_time + interval '5.5 hour') delivery_slot_end_time,\n", "\n", "--   extract(hour from (ims_status.scheduled_at + interval '5.5 hour')) as del_hour,\n", "--   CASE WHEN extract(hour from (ims_status.scheduled_at + interval '5.5 hour')) >=6 AND extract(hour from (ims_status.scheduled_at + interval '5.5 hour')) <15\n", "-- THEN 'SLOT A' ELSE 'SLOT B' END AS slot, \n", "    \n", "     (o.update_ts + interval '5.5 hour') as   order_updated_ts,\n", "    so.id as suborder_id,\n", "    (so.update_ts + interval '5.5 hour') as so_updated_ts,\n", "    wpom.pick_list_id,\n", "    pl.id as picklist_id,\n", "    pl.outlet_id,\n", "    -- bom.batch_id as order_batch_id,\n", "    bda.batch_id,\n", "      bha.expected_handover_end_time,\n", "       bha.actual_handover_end_time,\n", "      bda.expected_dispatch_end_time,\n", "       bda.actual_dispatch_time\n", " \n", "from lake_crates.crate_log cl1 \n", " left join  \n", "lake_crates.crate_log cl2  \n", "  on cl1.crate_id = cl2.crate_id and  cl1.new_crate_entity_id = cl2.previous_crate_entity_id  and cl1.new_status_id =cl2.previous_status_id  and cl1.facility_timestamp=cl2.previous_facility_timestamp  and cl1.new_facility_id=cl2.previous_facility_id\n", " left join \n", "lake_crates.crate_log cl3 \n", "  on cl2.crate_id = cl3.crate_id and  cl2.new_crate_entity_id = cl3.previous_crate_entity_id  and cl2.new_status_id =cl3.previous_status_id and cl2.facility_timestamp=cl3.previous_facility_timestamp  and cl2.new_facility_id=cl3.previous_facility_id\n", " left join \n", "lake_crates.crate_log cl4 \n", "  on cl3.crate_id = cl4.crate_id and  cl3.new_crate_entity_id = cl4.previous_crate_entity_id  and cl3.new_status_id =cl4.previous_status_id and cl3.facility_timestamp=cl4.previous_facility_timestamp  and cl3.new_facility_id=cl4.previous_facility_id\n", "   \n", "   \n", "  left join \n", "lake_crates.crate_log cl5 \n", "  on cl4.crate_id = cl5.crate_id and  cl4.new_crate_entity_id = cl5.previous_crate_entity_id  and cl4.new_status_id =cl5.previous_status_id and cl4.facility_timestamp=cl5.previous_facility_timestamp  and cl4.new_facility_id=cl5.previous_facility_id\n", "   \n", " inner join  lake_crates.crate c  on cl1.crate_id = c.id\n", " inner join lake_crates.facility facility on cl1.new_facility_id = facility.id\n", " left join lake_crates.crate_entity ce on cl1.new_crate_entity_id = ce.id\n", " left join lake_oms_bifrost.oms_suborder so on ce.entity_id = so.id\n", " left join lake_oms_bifrost.oms_order o on so.order_id = o.id\n", " left join lake_last_mile.order lo on o.id = lo.external_order_id \n", " left join lake_last_mile.order_action oa on oa.order_id = lo.id  \n", " left join lake_last_mile.field_executive fe on oa.field_executive_id = fe.id  \n", " left join lake_last_mile.order_action oa2 on oa2.order_id = lo.id  \n", " left join lake_last_mile.field_executive fe2 on oa2.field_executive_id = fe2.id  \n", "\n", " left join lake_last_mile.view_distribution_point dp on fe.id = dp.primary_fe_id \n", " left join lake_ims.ims_order_details ims_status on  ce.entity_id=ims_status.order_id\n", " \n", " \n", " left join lake_warehouse_location.warehouse_pick_list_order_mapping wpom on wpom.order_id =so.id\n", " left join lake_warehouse_location.warehouse_pick_list pl on pl.id=wpom.pick_list_id\n", " left join lake_warehouse_location.batch_order_mapping bom on wpom.order_id = bom.order_id\n", " left join batch_dispatch_activity bda on bom.batch_id = bda.batch_id\n", " left join batch_handover_activity bha on bom.batch_id = bha.batch_id\n", "where \n", "date(cl1.facility_timestamp + interval '5.5 hour') between (CURRENT_DATE - interval '8 days') and (CURRENT_DATE - interval '4 days')\n", "-- '2020-07-01' and '2020-07-14'\n", "-- between (current_date-5) and (current_date-5) and\n", "and  oa.action_type = 'SECONDMILE_ASSIGNED'\n", "and  oa2.action_type = 'PICKED'\n", "-- and  oa.action_type = 'SECONDMILE_PICKED'\n", "\n", "\n", "and cl1.new_status_id = 11 and cl1.is_success =1\n", "-- and facility.id=1\n", "-- and cl1.new_facility_id =12\n", "-- and (dp.id=441 or fe.id=441) and  ims_status.status_id in (2)\n", "-- and cl2.new_status_id=5 and cl3.new_status_id is null\n", "-- and o.station='stationhr32'\n", "-- and cl1.new_facility_id = 3 and date(cl1.facility_timestamp + interval '5.5 hour')=current_date-10\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48\n", "),\n", "\n", "final as\n", "(\n", "select \n", "\n", "tr.*,\n", "-- case when second_status_id=5 and  third_status_id is null then 'expired' else 'got_back' end as crate_status,\n", "case when (second_status_id=1 or third_status_id=1) then  'got_back' else 'pending' end as crate_status_2,\n", "case when second_status_id=1 then second_timestamp  else Null end as unassigned_at,\n", "-- timestamp\n", "-- so_updated_ts-actual_handover_end_time dif1, \n", "-- so_updated_ts-actual_dispatch_time dif2 ,\n", "-- right-left\n", "datediff(hours,  expected_handover_end_time, expected_dispatch_end_time) disp_hand_expect,\n", "datediff(hours,  actual_handover_end_time, actual_dispatch_time) disp_hand_actual,\n", "datediff(hours,  actual_handover_end_time,so_updated_ts) as so_update_handover,\n", "datediff(hours,  actual_dispatch_time,so_updated_ts) as so_update_dispatch,\n", "datediff(hours,  actual_handover_end_time,order_updated_ts) as o_update_handover,\n", "datediff(hours,  actual_dispatch_time,order_updated_ts) as o_update_dispatch\n", "from table_raw tr\n", "-- where second_status_id=5 and  third_status_id is null\n", "-- and secondmile_fe_id=441 \n", "-- where order_id in (62888713,62827258)\n", "\n", ")\n", "\n", "select * from final\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# redshift.table_names(\"lake_warehouse_location\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.read_sql_query(sql=raw_query, con=redshift)\n", "\n", "raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data[\"dispatch_date\"] = raw_data[\"dispatch_date\"].astype(\"datetime64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data[\"dispatch_date\"] = raw_data[\"dispatch_date\"].dt.date\n", "# .date()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pending count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data[raw_data[\"crate_status_2\"] == \"pending\"].shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Reducing column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = raw_data[\n", "    [\n", "        \"dispatch_date\",\n", "        \"new_facility_id\",\n", "        \"name\",\n", "        \"crate_id\",\n", "        \"id\",\n", "        \"first_status_id\",\n", "        \"second_status_id\",\n", "        \"third_status_id\",\n", "        \"second_timestamp\",\n", "        \"third_status_id\",\n", "        \"order_id\",\n", "        \"station_id\",\n", "        \"secondmile_dp_name\",\n", "        \"secondmile_fe_name\",\n", "        \"secondmile_fe_name_p\",\n", "        \"sub_oms_status\",\n", "        \"sch_del_time\",\n", "        \"suborder_id\",\n", "        \"crate_status_2\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data.to_csv('final_data.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data_ahm=final_data[final_data['new_facility_id']==1]\n", "final_data_ahm = final_data.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_ahm.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Grouping data to get total crate count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_all = (\n", "    final_data_ahm.groupby([\"dispatch_date\", \"new_facility_id\", \"name\"])\n", "    .agg(\n", "        {\n", "            \"crate_id\": \"nunique\",\n", "            #                                                          \"shelf\":\"nunique\",\n", "            #                                                          \"item_id\":\"nunique\",\n", "            #                                                          \"item_quantity\":\"sum\"\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "summary_all.rename(columns={\"crate_id\": \"crate_tot\"}, inplace=True)\n", "summary_all.tail(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Grouping data to get total rec count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_got_back = (\n", "    final_data_ahm[final_data_ahm[\"crate_status_2\"] == \"got_back\"]\n", "    .groupby([\"dispatch_date\", \"new_facility_id\"])\n", "    .agg(\n", "        {\n", "            \"crate_id\": \"nunique\",\n", "            #                                                          \"shelf\":\"nunique\",\n", "            #                                                          \"item_id\":\"nunique\",\n", "            #                                                          \"item_quantity\":\"sum\"\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "summary_got_back.rename(columns={\"crate_id\": \"crate_rec\"}, inplace=True)\n", "summary_got_back.tail(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_final = pd.merge(\n", "    summary_all,\n", "    summary_got_back,\n", "    how=\"left\",\n", "    left_on=[\"dispatch_date\", \"new_facility_id\"],\n", "    right_on=[\"dispatch_date\", \"new_facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_final[\"pending\"] = summary_final[\"crate_tot\"] - summary_final[\"crate_rec\"]\n", "summary_final[\"pending%\"] = round(\n", "    (summary_final[\"pending\"] / summary_final[\"crate_tot\"]) * 100, 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_final.tail(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pivot to get day wise data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p1 = pd.pivot_table(\n", "    summary_final,\n", "    index=[\"new_facility_id\", \"name\"],\n", "    values=[\"pending\"],\n", "    columns=[\"dispatch_date\"],\n", "    aggfunc=[np.sum],\n", "    fill_value=0,\n", "    margins=True,\n", ")\n", "p1 = p1.droplevel([0, 1], axis=1).reset_index()\n", "del p1.columns.name\n", "# p1=p1.droplevel(0, axis=0).reset_index()\n", "# p1=p1.reset_index()\n", "# p1.rename_axis(None,1)\n", "# p1.rename(lambda x:x * 2, axis =\"index\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p2 = pd.pivot_table(\n", "    summary_final,\n", "    index=[\"new_facility_id\", \"name\"],\n", "    values=[\"pending%\"],\n", "    columns=[\"dispatch_date\"],\n", "    aggfunc=[np.mean],\n", "    fill_value=0,\n", "    margins=True,\n", ")\n", "p2 = p2.droplevel([0, 1], axis=1).reset_index()\n", "del p2.columns.name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# p1.reset_index(inplace=True)\n", "# p1 = p1.drop(columns=[\"dispatch_date\"], axis=1)\n", "# p1.reset_index(drop=True, inplace=True)\n", "# p1.rename_axis('dispatch_date', inplace=True)\n", "# p1.index.name = None\n", "p1.tail(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p1.replace([\"Super Store\", \"- Warehouse\"], \"\", regex=True, inplace=True)\n", "p2.replace([\"Super Store\", \"- Warehouse\"], \"\", regex=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sending Mail"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_table1 = p1.to_html(index=False)\n", "\n", "summary_table2 = p2.to_html(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# p1.to_csv('p1.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1SvCz7aMScfEi_yQgksSSLRr6tDBb2XxmCCJc18sNmDM\"\n", "rec = pb.from_sheets(sheet_id, \"mail_list\", service_account=\"service_account\")\n", "\n", "mail_list = rec[\"rec\"].reset_index(drop=True)\n", "len = mail_list.size\n", "mail_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rec = []\n", "for i in range(0, len):\n", "    x = mail_list[i]\n", "    final_rec.append(x)\n", "final_rec"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email = \"<EMAIL>\"\n", "from_email = \"<EMAIL>\"\n", "to_email = final_rec\n", "# to_email = ['<EMAIL>','<EMAIL>']\n", "subject = \"Pending Crate summary for \" + str(mail_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = (\n", "    \"\"\"\n", "    \n", "Hi,<br><br>\n", "\n", "Please find the summary for pending crates from \"\"\"\n", "    + str(start_date)\n", "    + \"\"\" to \"\"\"\n", "    + str(end_date)\n", "    + \"\"\".<br><br> Pending count:\"\"\"\n", "    + summary_table1\n", "    + \"\"\"<br><br> \"\"\"\n", "    + \"\"\"Detailed summary & raw data for pending crates can be found\n", "<a href='https://docs.google.com/spreadsheets/d/1SvCz7aMScfEi_yQgksSSLRr6tDBb2XxmCCJc18sNmDM/edit#gid=2136996697'>\n", "here.\n", "</a>\n", "<br><br>\n", "Best,<br>\n", "Data Team\n", "\n", "    \"\"\"\n", ")\n", "\n", "# html_content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to_email = final_rec\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content\n", "    #     , files=['raw_data.csv'],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Printing Sheets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_gsheet = final_data[final_data[\"crate_status_2\"] == \"pending\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_final.replace([\"Super Store\", \"- Warehouse\"], \"\", regex=True, inplace=True)\n", "\n", "sheet_id = \"1SvCz7aMScfEi_yQgksSSLRr6tDBb2XxmCCJc18sNmDM\"\n", "sheet_name = \"summary_all\"\n", "pb.to_sheets(summary_final, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"raw_data_pending\"\n", "pb.to_sheets(raw_gsheet, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"pending_count\"\n", "pb.to_sheets(p1, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"pending_share\"\n", "pb.to_sheets(p2, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### end of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}