{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta, timezone\n", "import os\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_TAT_query = f\"\"\"\n", "SELECT sender_facility_name,\n", "       receiver_facility_name,\n", "       sto_created_at,\n", "       indent,\n", "       stos_raised,\n", "       stos_qty,\n", "       STOs_not_putawayed,\n", "       not_putawayed_qty,\n", "       stos_low_fillrate\n", "FROM\n", "  (SELECT sender_facility_name,\n", "          receiver_facility_name,\n", "          sto_created_at::date,\n", "          indent,\n", "          count(DISTINCT sto_id) AS STOs_raised,\n", "          sum(created_quantity) AS STOs_qty, \n", "          count(CASE\n", "                  WHEN putaway_end_time IS NULL THEN sto_id\n", "                  ELSE NULL\n", "              END) AS STOs_not_putawayed,\n", "          sum(putawayed_quantity) AS total_putawayed_qty,\n", "          (CASE\n", "            WHEN STOs_qty - total_putawayed_qty < 0 THEN 0\n", "            ELSE STOs_qty - total_putawayed_qty\n", "            END) AS Not_Putawayed_qty,\n", "          count(CASE WHEN fillrate < 98 THEN sto_id ELSE NULL END) AS stos_low_fillrate\n", "   FROM\n", "     (SELECT sender_facility_name,\n", "             receiver_facility_name,\n", "             m.ars_mode,\n", "             indent,\n", "             sto_id,\n", "             created_q AS created_quantity,\n", "             billed_q AS billed_quantity,\n", "             sum(putaway_q) AS putawayed_quantity,\n", "             s.sto_created_at,\n", "             min(sto_billed) AS billed_time,\n", "             (putawayed_quantity/created_quantity)*100 AS fillrate,\n", "             min(putlist_creation) AS putlist_created_at,\n", "             max(putaway_complete) AS putaway_end_time,\n", "             datediff(<PERSON><PERSON><PERSON>,s.sto_created_at,billed_time) AS sto_created_to_billed_TAT_hours,\n", "             datediff(<PERSON><PERSON><PERSON>,s.sto_created_at,putlist_created_at) AS sto_created_to_putlist_created_TAT_hours,\n", "             datediff(<PERSON><PERSON><PERSON>,s.sto_created_at,putaway_end_time) AS sto_created_to_putaway_TAT_hours\n", "      FROM metrics.multirep_data m\n", "      INNER JOIN\n", "        (SELECT bulk_sto_id,\n", "                sto_id,\n", "                manual_run,\n", "                receiver_outlet_name,\n", "                sender_outlet_name,\n", "                receiving_outlet_id,\n", "                min(sto_created_at) AS sto_created_at,\n", "                min(putlist_creation_time) AS putlist_creation,\n", "                min(sto_invoice_created_at) AS sto_billed,\n", "                max(putaway_time) AS putaway_complete,\n", "                sum(expected_quantity) AS created_q,\n", "                sum(billed_quantity) AS billed_q,\n", "                sum(putaway_quantity) AS putaway_q\n", "         FROM metrics.esto_details\n", "         WHERE sto_created_at BETWEEN date_trunc('hour', getdate() - interval '12.5 hrs') AND date_trunc('hour', getdate() - interval '9.5 hrs' - interval '1 second')\n", "         GROUP BY 1,\n", "                  2,\n", "                  3,\n", "                  4,\n", "                  5,\n", "                  6) s ON s.bulk_sto_id = m.bulk_sto_id\n", "      AND ars_mode = 'normal'\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               9)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4\n", "   HAVING STOs_qty <> total_putawayed_qty)\n", "   ORDER BY 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tat = pd.read_sql(sto_TAT_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fcs = df_tat.sender_facility_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fc_level_df = (\n", "    df_tat.groupby(\n", "        [\n", "            \"sender_facility_name\",\n", "            \"sto_created_at\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"stos_raised\": \"sum\",\n", "            \"stos_qty\": \"sum\",\n", "            \"stos_not_putawayed\": \"sum\",\n", "            \"not_putawayed_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\n", "            \"Sender Facility\",\n", "            \"STO Created On\",\n", "            \"STOs Raised\",\n", "            \"Total Quantity Raised\",\n", "            \"STOs Pending Putaway\",\n", "            \"Quantity Pending Putaway\",\n", "        ],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t = datetime.now() - <PERSON><PERSON><PERSON>(hours=12.5)\n", "current_time = t.strftime(\"%I %p\")\n", "\n", "later = t + <PERSON><PERSON><PERSON>(hours=3)\n", "later_time = later.strftime(\"%I %p\")\n", "\n", "day = df_tat.sto_created_at.iloc[0].strftime(\"%Y/%m/%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "text = f\"Report for {day}, {current_time} to {later_time} for STOs where the processing is delayed:\"\n", "text2 = \"To view the raw data, use this query: https://redash-queries.grofers.com/queries/213254\"\n", "fig, ax = render_mpl_table(fc_level_df, header_columns=0)\n", "fig.savefig(\"STO_delayed_report.png\")\n", "token = \"******************************************************\"\n", "client = WebClient(token=token)\n", "file_check = \"./STO_delayed_report.png\"\n", "channel = \"grocery-supply-chain-alerts\"\n", "try:\n", "    response = client.chat_postMessage(channel=channel, text=text)\n", "    response = client.chat_postMessage(channel=channel, text=text2)\n", "    filepath = file_check\n", "    response = client.files_upload(channels=channel, file=filepath)\n", "except SlackApiError as e:\n", "    assert e.response[\"ok\"] is False\n", "    assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "    print(f\"Got an error: {e.response['error']}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}