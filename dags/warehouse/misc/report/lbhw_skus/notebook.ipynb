{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\")\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "pos = pb.get_connection(\"pos\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "\n", "end_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "start_date = (datetime.now() - timed<PERSON>ta(days=16)).date()\n", "end_date, start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_query = (\n", "    \"\"\"select id outlet_id,name as outlet_name  from  retail.console_outlet\"\"\"\n", ")\n", "outlet_ids = pd.read_sql_query(sql=outlets_query, con=retail)\n", "outlet_ids.head()\n", "outlet_list = outlet_ids[\"outlet_id\"]\n", "type(outlet_list)\n", "outletid_list = tuple(list(outlet_ids[\"outlet_id\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outletname_list = tuple(list(outlet_ids[\"outlet_name\"]))\n", "print(outletname_list[0:10])\n", "type(outletname_list)\n", "# outletid_list=(100,410)\n", "print(outletid_list[0:10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_10day_final = pd.DataFrame()\n", "\n", "for i in outletid_list[:]:\n", "    #     print(i)\n", "    query = \"\"\"SELECT \n", "   i3.id as \"Stock Update ID\",\n", "   pur_ord.po_number as \"Purchase Number\",\n", "   (CASE\n", "      WHEN iii.source_type = 2 THEN \"ESTO Stock-in\"\n", "      WHEN iii.source_type = 3 THEN \"Customer returened without invoice\"\n", "      WHEN iii.source_type = 1 and iii.valid_po=1 THEN \"Valid PO\"\n", "      ELSE \"In-Valid PO\"\n", "   END) AS \"PO Type\",\n", "   pur_ord.vendor_gst as \"Vendor GST\",\n", "   lo2.name as \"Vendor Supply Location\",\n", "   Date(convert_tz(pur_ord.issue_date,'+00:00','+05:30')) as \"Issue Date\",\n", "   Date(convert_tz(i3.manufacture_date,'+00:00','+05:30')) as \"Manufacture Date\",\n", "   r.item_id AS 'Item ID',\n", "   i.upc_id AS 'UPC ID',\n", "   i.variant_id AS 'Variant ID',\n", "   r.name AS Product,\n", "  (CASE\n", "      WHEN r.outlet_type = 1 THEN \"F&V type\"\n", "    WHEN r.outlet_type = 2 THEN \"Grocery type\"\n", "         ELSE 'Not Assigned'\n", "     END) AS \"Item Type\",\n", "   man.name AS 'Manufacturer Name',\n", "   r.is_ptr AS 'PTR Flag',\n", "   i.delta AS Quantity,\n", "   i3.return_quantity ,\n", "   (i.delta-i3.return_quantity),\n", "   r.variant_mrp AS MRP,\n", "   r.variant_uom_text AS Grammage,\n", "   ro.name AS 'Outlet Name',\n", "   lo.name AS 'City Name',\n", "   Date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) AS 'Stock In Date',\n", "   convert_tz(i.created_at,'+00:00','+05:30') AS 'Created At',\n", "   i2.name AS 'Update Type',\n", "   i3.expiry_date AS 'Expiry Date',\n", "   rcm.name AS 'Merchant Name',\n", "   i.merchant_invoice_id AS 'Merchant Invoice ID',\n", "   i.created_by_name AS 'Created By',\n", "   i3.cost_price AS 'Cost Price',\n", "   cgst_tax.tax_percentage AS 'CGST(%%)',\n", "   sgst_tax.tax_percentage AS 'SGST(%%)',\n", "   igst_tax.tax_percentage AS 'IGST(%%)',\n", "   cess_tax.tax_percentage AS 'CESS(%%)',\n", "   i3.landing_price AS 'Landing Price',\n", "   i3.po_landing_price AS 'PO Landing Price',\n", "   i3.purchase_order_id as 'Purchase Order ID',\n", "   i3.grn_id as 'GRN ID',\n", "   i3.landing_price * i.delta,\n", "   (i3.cost_price*(i.delta-i3.return_quantity)) as 'Net Cost Price',\n", "   (i3.landing_price*(i.delta-i3.return_quantity)) as 'Net Landing Price',\n", "   (i3.po_landing_price*(i.delta-i3.return_quantity)) as 'Net PO landing price',\n", "   i3.cost_price * i.delta as 'Total Cost Price',\n", "   ((i3.landing_price * i.delta)-(i3.cost_price * i.delta)) as 'Tax',\n", "   r.is_pl\n", "FROM\n", "   ims.ims_inventory_log i\n", "       INNER JOIN\n", "   rpc.product_product r ON r.variant_id = i.variant_id\n", "       LEFT JOIN\n", "   rpc.product_brand br ON br.id=r.brand_id\n", "   \t\tLEFT JOIN\n", "   rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "   \t\tINNER JOIN \n", "   retail.console_outlet ro ON ro.id = i.outlet_id\n", "       INNER JOIN\n", "   retail.console_location lo ON ro.tax_location_id=lo.id\n", "   \t\tINNER JOIN\n", "   ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id  \n", "       INNER JOIN\n", "   ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "       INNER JOIN\n", "   ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "       INNER JOIN\n", "   retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "       LEFT JOIN\n", "   ims.ims_inventory_stock_tax cgst_tax ON i3.id = cgst_tax.inventory_stock_id and cgst_tax.tax_type_id = 3\n", "      LEFT JOIN\n", "   ims.ims_inventory_stock_tax sgst_tax ON i3.id = sgst_tax.inventory_stock_id and sgst_tax.tax_type_id = 4\n", "        LEFT JOIN\n", "   ims.ims_inventory_stock_tax igst_tax ON i3.id = igst_tax.inventory_stock_id and igst_tax.tax_type_id = 5\n", "        LEFT JOIN\n", "   ims.ims_inventory_stock_tax cess_tax ON i3.id = cess_tax.inventory_stock_id and cess_tax.tax_type_id = 6   \n", "       LEFT JOIN\n", "   po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id \n", "   \t\tLEFT JOIN\n", "  \tpos.pos_invoice pi on pi.invoice_id=i.merchant_invoice_id\n", "   \t\tLEFT JOIN\n", "   \tretail.console_location lo2 on lo2.id=pur_ord.vendor_shipping_city_id\n", "\n", "WHERE\n", "   i.inventory_update_type_id IN (1,28,76,90,93)\n", "  and i.outlet_id  in (%(outlet_id)s) \n", " --  and i.outlet_id =410\n", "  and date(convert_tz(i.created_at,'+00:00','+05:30'))  between DATE_ADD(CURDATE(), INTERVAL - 10 day)  and DATE_ADD(CURDATE(), INTERVAL - 1 day) \n", "   and i.delta > 0\n", "       and  rcm.name not in (select distinct name as outlet_name  from  retail.console_outlet) \n", "    and  rcm.name not in ('Customer Return without Invoice')\n", "    and  rcm.name not in ('Customer Returns Without Invoice')\n", "    and  rcm.name not in ('Dummy stock in merchant','Dummy Merchant')\n", "    and  rcm.name not like '%%HOT Warehouse%%'\n", "    and  rcm.name not like '%%PN2 (HOT)%%' \n", "    and ro.name not like '%%Infra Outlet%%'\n", "\n", "\n", "--     and  iii.source_type = 1\n", "--     and  iii.valid_po=1   \n", "    \"\"\"\n", "    raw_2day_temp = pd.read_sql_query(\n", "        sql=query,\n", "        con=pos,\n", "        params={\"outlet_id\": i},\n", "    )\n", "    raw_10day_final = raw_10day_final.append(raw_2day_temp)\n", "\n", "print(\"raw_10day_grn captured\")\n", "\n", "raw_10day_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_10day_final[\"Item ID\"] = raw_10day_final[\"Item ID\"].astype(\"int\")\n", "raw_10day_final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_SKUs_list = raw_10day_final[[\"Item ID\", \"Outlet Name\", \"Stock In Date\", \"Product\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_SKUs_list.drop_duplicates(inplace=True, subset=[\"Item ID\"])\n", "grn_SKUs_list.reset_index(drop=True, inplace=True)\n", "print(grn_SKUs_list.shape)\n", "grn_SKUs_list.tail(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sending grn skus from last 10 days to sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1KSwW2m3o7x82pNheD_mdBLxrgWkgM_uA8L7DRho-jO8\"\n", "sheet_name = \"grn\"\n", "grn_SKUs_list.sort_values(\n", "    [\"Stock In Date\", \"Item ID\"], axis=0, ascending=True, inplace=True\n", ")\n", "\n", "\n", "pb.to_sheets(grn_SKUs_list, sheet_id, sheet_name, service_account=\"service_account\")\n", "print(\"sent to sheet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reading existing SKUs from Karan sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["existing_skus = pb.from_sheets(sheet_id, \"existing\", service_account=\"service_account\")\n", "existing_skus[\"Item ID\"] = existing_skus[\"Item ID\"].astype(\"int\")\n", "print(existing_skus.shape)\n", "existing_skus.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["existing_item_id = existing_skus[\"Item ID\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(existing_item_id)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finding new grn SKUs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_skus = grn_SKUs_list[\n", "    ~grn_SKUs_list[\"Item ID\"].isin(existing_skus[\"Item ID\"].unique())\n", "]\n", "sheet_name = \"new_sku\"\n", "new_skus.sort_values([\"Stock In Date\", \"Item ID\"], axis=0, inplace=True)\n", "pb.to_sheets(new_skus, sheet_id, sheet_name, service_account=\"service_account\")\n", "print(new_skus.shape)\n", "new_skus.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_SKUs_list[grn_SKUs_list['Item ID']==********]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ********\n", "# existing_skus[existing_skus['Item ID']==********]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_skus.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}