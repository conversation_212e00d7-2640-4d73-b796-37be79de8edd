{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = (datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)).date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_growth_buffer = 0.05  ### 1 means 100%\n", "buffer_for_window = 0.0  ## Its in mins\n", "billing_time_multiplier = 1  ## For Elimantining billing param (1 mean no 0 means yes)\n", "window_percentile = 0.75  ### 1 means 100 percentile\n", "order_percentile = 0.9  ### 1 means 100 percentile\n", "window_multiplier = 1  ### 1 means 100%\n", "window_lower = 0\n", "window_upper = 4\n", "growth_per = 0.10  ## 1 means 100%\n", "growth_dynamic = \"Yes\"\n", "picking_improvement = 0  ## For picking improvement\n", "weekly_off_buffer = 0.20\n", "general_buffer = 0.0\n", "fixed_window_size = 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# start_date = today -<PERSON><PERSON><PERSON>(days=7) ## 8\n", "# end_date = today - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "start_date = today + <PERSON><PERSON>ta(-today.weekday(), weeks=-1)\n", "end_date = today + <PERSON><PERSON><PERSON>(-today.weekday() - 1)\n", "\n", "# outlet_list = [1010,1072, 1445, 1139,1190]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_q = \"\"\"\n", "select distinct id from lake_retail.console_outlet where active=1 and business_type_id=7\n", "\"\"\"\n", "outlet_data = pd.read_sql(outlet_q, redshift)\n", "\n", "outlet_list = list(outlet_data.id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(outlet_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Manpower at Variable Window Size (Upto 4 min) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting the Window Size for each Outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_q = \"\"\"\n", "with lake_logistics_warehouse_data AS (\n", "        SELECT\n", "            date(wpl.assigned_at+interval '5.5 hours') as date,\n", "            extract(hour from ((wpl.assigned_at) + interval '330 minute')) as hour,\n", "            wpl.assigned_to_name as employee_id, outlet_id,\n", "            \n", "            wpl.id as picklist_id,\n", "            base.order_id,\n", "            base.id as suborder_id,\n", "            \n", "            wpl.picking_started_at AS picking_started_ts_utc,\n", "            max(pll.pos_timestamp) AS picking_completed_ts_utc,\n", "            MAX(\n", "                CASE\n", "                    WHEN wobl.action = 'BILLING_START'\n", "                    THEN wobl.pos_timestamp\n", "                END\n", "            ) AS billing_started_ts_utc,\n", "            MAX(\n", "                CASE\n", "                    WHEN wobl.action = 'BILLING_END'\n", "                    THEN wobl.pos_timestamp\n", "                END\n", "            ) AS billing_completed_ts_utc\n", "       \n", "        FROM\n", "             lake_oms_bifrost.oms_suborder \n", "                AS base\n", "        JOIN\n", "            lake_warehouse_location.warehouse_pick_list_order_mapping \n", "                AS wplom\n", "                ON (wplom.order_id) = (base.id)-- this is not a mistake\n", "        JOIN\n", "            lake_warehouse_location.warehouse_pick_list \n", "                AS wpl\n", "                ON wpl.id = wplom.pick_list_id\n", "        JOIN\n", "            lake_warehouse_location.pick_list_log \n", "                AS pll\n", "                ON pll.pick_list_id = wpl.id\n", "                    AND pll.picklist_state in (4) -- state 4 means picking completed\n", "        LEFT JOIN\n", "            lake_warehouse_location.warehouse_order_billing_log\n", "                AS wobl\n", "                ON base.id = wobl.order_id -- also not a mistake\n", "        \n", "        WHERE\n", "            date(wobl.pos_timestamp+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "            AND date(pll.pos_timestamp+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "            AND date(wpl.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "            AND date(wplom.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "            ----and outlet_id in {outlet_list}\n", "                                       \n", "        GROUP BY\n", "            1, 2, 3, 4,5,6,7,8\n", "    )\n", "    \n", "    \n", "select distinct outlet_id,    \n", "PERCENTILE_CONT({window_percentile}) WITHIN\n", "GROUP (ORDER BY picking_start_to_complete) OVER (PARTITION BY outlet_id) AS picking_start_to_complete_75_per,\n", "PERCENTILE_CONT({window_percentile}) WITHIN\n", "GROUP (ORDER BY picking_complete_to_billing_start) OVER (PARTITION BY outlet_id) AS picking_complete_to_billing_start_75_per,\n", "PERCENTILE_CONT({window_percentile}) WITHIN\n", "GROUP (ORDER BY billing_start_to_complete) OVER (PARTITION BY outlet_id) AS billing_start_to_complete_75_per\n", "from\n", "(SELECT\n", "            outlet_id,\n", "        suborder_id,\n", "    datediff(seconds,picking_started_ts_utc,picking_completed_ts_utc) as picking_start_to_complete,\n", "    datediff(seconds,picking_completed_ts_utc,billing_started_ts_utc) as picking_complete_to_billing_start,\n", "    datediff(seconds,billing_started_ts_utc,billing_completed_ts_utc) as billing_start_to_complete\n", "\n", "from lake_logistics_warehouse_data\n", "group by 1,2,3,4,5)\n", "\n", "\"\"\".format(\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    outlet_list=tuple(outlet_list),\n", "    window_percentile=window_percentile,\n", ")\n", "\n", "order_process_time_data = pd.read_sql(time_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data = order_process_time_data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data[\"time_taken_75_per\"] = (\n", "    order_process_time_data[\"picking_start_to_complete_75_per\"]\n", "    + order_process_time_data[\"picking_complete_to_billing_start_75_per\"]\n", "    + (\n", "        billing_time_multiplier\n", "        * order_process_time_data[\"billing_start_to_complete_75_per\"]\n", "    )\n", ")\n", "order_process_time_data[\"time_per_order\"] = (\n", "    order_process_time_data[\"time_taken_75_per\"] / 60\n", ")\n", "order_process_time_data[\"time_per_order\"] = order_process_time_data[\n", "    \"time_per_order\"\n", "] - (order_process_time_data[\"time_per_order\"] * picking_improvement)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data[\"time_per_order_with_buffer\"] = (\n", "    order_process_time_data[\"time_per_order\"] + buffer_for_window\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data[\"window_size\"] = order_process_time_data[\n", "    \"time_per_order_with_buffer\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data[\"final_window_size\"] = np.where(\n", "    order_process_time_data[\"window_size\"] < window_lower,\n", "    window_lower,\n", "    order_process_time_data[\"window_size\"],\n", ")\n", "order_process_time_data[\"final_window_size\"] = np.where(\n", "    order_process_time_data[\"final_window_size\"] > window_upper,\n", "    window_upper,\n", "    order_process_time_data[\"final_window_size\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data[\"final_window_size\"] = (\n", "    order_process_time_data[\"final_window_size\"] * window_multiplier\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_dict = dict(\n", "    zip(order_process_time_data.outlet_id, order_process_time_data.final_window_size)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Order with Rolling Window Logic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select \n", "--pl.assigned_to_name as emp_id,\n", "     --u.name as picker_name,\n", "        date(i.created_at+interval '5.5 hours') as order_checkout_date,\n", "        outlet,\n", "        co.name as outlet_name,\n", "        extract(hour from ((i.created_at) + interval '330 minute')) as hour,\n", "        extract(minute from ((i.created_at) + interval '330 minute')) as min_part,\n", "        ----TO_CHAR((pl.assigned_at) + interval '330 minute','YYYY-MM-DD HH24:MI') as timestamp,\n", "        count(distinct i.order_id) as total_orders\n", "        \n", "   \n", "        from lake_ims.ims_order_details i\n", "        inner join lake_retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7\n", "        inner join lake_warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "        left join lake_warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id\n", "        and pl.outlet_id = co.id\n", "        and pl.state in (4,10)\n", "        left join lake_warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "        left join lake_warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "        -- left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "         --inner join lake_retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "        where \n", "\n", "        date(i.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "        and pllh.pick_list_id is null \n", "\n", "        and i.outlet in {outlet_list}\n", "        group by 1,2,3,4,5\n", "        order by 2,1,4,5\n", "        \n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlet_list=tuple(outlet_list)\n", ")\n", "\n", "\n", "data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[(data[\"outlet\"] == 1505) & (data[\"hour\"] == 13)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"total_orders\"] = np.where(data[\"total_orders\"] > 4, 4, data[\"total_orders\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"total_orders\"].quantile(0.99)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min = data[\n", "    [\"order_checkout_date\", \"outlet\", \"outlet_name\", \"hour\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min[\"min_list\"] = data_hour_min[\"hour\"].apply(\n", "    lambda x: \"0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def splitDataFrameList(df, target_column, separator):\n", "    \"\"\"df = dataframe to split,\n", "    target_column = the column containing the values to split\n", "    separator = the symbol used to perform the split\n", "    returns: a dataframe with each entry for the target column separated, with each element moved into a new row.\n", "    The values in the other columns are duplicated across the newly divided rows.\n", "    \"\"\"\n", "\n", "    def splitListToRows(row, row_accumulator, target_column, separator):\n", "        split_row = row[target_column].split(separator)\n", "        for s in split_row:\n", "            new_row = row.to_dict()\n", "            new_row[target_column] = s\n", "            row_accumulator.append(new_row)\n", "\n", "    new_rows = []\n", "    df.apply(splitListToRows, axis=1, args=(new_rows, target_column, separator))\n", "    new_df = pd.DataFrame(new_rows)\n", "    return new_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new = splitDataFrameList(data_hour_min, \"min_list\", \",\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new = data_hour_min_new.rename(columns={\"min_list\": \"min_part\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new[\"min_part\"] = data_hour_min_new[\"min_part\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new = data_hour_min_new.merge(\n", "    data,\n", "    on=[\"order_checkout_date\", \"outlet\", \"outlet_name\", \"hour\", \"min_part\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new[\"order_checkout_date\"] = data_new[\"order_checkout_date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling = pd.DataFrame()\n", "\n", "for i in outlet_list:\n", "    try:\n", "        data_temp = (\n", "            data_new[data_new[\"outlet\"] == i]\n", "            .groupby([\"outlet\", \"outlet_name\", \"order_checkout_date\", \"hour\"])[\n", "                \"total_orders\"\n", "            ]\n", "            .rolling(int(window_dict[i]), min_periods=1)\n", "            .sum()\n", "            .reset_index()\n", "        )\n", "        #         print(data_temp.shape)\n", "        if data_temp.shape[0] != 0:\n", "            data_new_rolling = data_new_rolling.append(data_temp, ignore_index=True)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new[(data_new['outlet']==1010) & (data_new['hour']==12) & (data_new['order_checkout_date']=='2022-03-07')]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling[(data_new_rolling['outlet']==1010) & (data_new_rolling['hour']==12) & (data_new_rolling['order_checkout_date']=='2022-03-07')]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Order Runrate Trend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date2 = today - <PERSON><PERSON><PERSON>(days=15)\n", "end_date2 = today - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_q = \"\"\"\n", "select \n", "--pl.assigned_to_name as emp_id,\n", "     --u.name as picker_name,\n", "        date(i.created_at+interval '5.5 hours') as order_checkout_date,\n", "        date_part(w,date(i.created_at+interval '5.5 hours')) as week,\n", "        date_part(y,date(i.created_at+interval '5.5 hours')) as year,\n", "        outlet,\n", "        co.name as outlet_name,\n", "\n", "        ---extract(hour from ((i.created_at) + interval '330 minute')) as hour,\n", "        ---extract(minute from ((i.created_at) + interval '330 minute')) as min_part,\n", "        ----TO_CHAR((pl.assigned_at) + interval '330 minute','YYYY-MM-DD HH24:MI') as timestamp,\n", "        count(distinct i.order_id) as total_orders\n", "        \n", "   \n", "        from lake_ims.ims_order_details i\n", "        inner join lake_retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7\n", "        inner join lake_warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "        left join lake_warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id\n", "        and pl.outlet_id = co.id\n", "        and pl.state in (4,10)\n", "        left join lake_warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "        left join lake_warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "        -- left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "         inner join lake_retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "        where \n", "\n", "        date(i.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "        and pllh.pick_list_id is null \n", "\n", "        and i.outlet in {outlet_list}\n", "        group by 1,2,3,4,5\n", "        order by 4,3,2,1\n", "        \n", "\"\"\".format(\n", "    start_date=start_date2, end_date=end_date2, outlet_list=tuple(outlet_list)\n", ")\n", "\n", "\n", "order_run_rate = pd.read_sql(order_run_rate_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate['weekday_weekend'] = order_run_rate['order_checkout_date'].apply(lambda x: 'Weekend' if x.weekday() in (5,6) else 'Weekday')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # def previous_week_range(date):\n", "# import datetime\n", "\n", "# prev_start_date = today + datetime.timedelta(-today.weekday(), weeks=-1)\n", "# prev_end_date = today + datetime.timedelta(-today.weekday() - 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_new = order_run_rate[\n", "    (order_run_rate[\"order_checkout_date\"] >= start_date)\n", "    & (order_run_rate[\"order_checkout_date\"] <= end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def week_func(week):\n", "    return order_run_rate_group[order_run_rate_group[\"week\"] == week][\"year\"].min()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group = (\n", "    order_run_rate_new.groupby([\"outlet_name\", \"outlet\", \"year\", \"week\"])\n", "    .agg({\"total_orders\": [\"sum\", \"count\"]})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group.columns = [\n", "    \"\".join(col).strip() for col in order_run_rate_group.columns.values\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group[\"year_latest\"] = order_run_rate_group[\"week\"].apply(\n", "    lambda x: week_func(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_new = (\n", "    order_run_rate_group.groupby([\"outlet_name\", \"outlet\", \"year_latest\", \"week\"])\n", "    .agg({\"total_orderssum\": \"sum\", \"total_orderscount\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new =order_run_rate_group_new[order_run_rate_group_new['total_orderscount']==5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new[\"constant\"]=1\n", "# order_run_rate_group_new[\"rank\"] = order_run_rate_group_new.groupby(['outlet_name','outlet'])[\"constant\"].apply(lambda x: x.cumsum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections = pb.from_sheets(\n", "    \"1b7wOEzD82mGb8RnMIOLUMmKtE0vn6UomxLzF35JME_w\",\n", "    \"Current Growth (Marketing included)\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_list = list(set(order_projections.columns) - set({\"Outlet Id\", \"Store\"}))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new = pd.melt(\n", "    order_projections,\n", "    id_vars=[\"Outlet Id\", \"Store\"],\n", "    value_vars=date_list,\n", "    var_name=\"date\",\n", "    value_name=\"orders\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new[\"date\"] = pd.to_datetime(order_projections_new[\"date\"])\n", "order_projections_new[\"date\"] = order_projections_new[\"date\"].apply(lambda x: x.date())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new[\"orders\"] = order_projections_new[\"orders\"].apply(\n", "    lambda x: x.replace(\",\", \"\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new[order_projections_new[\"Outlet Id\"] == \"\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new[\"orders\"] = order_projections_new[\"orders\"].astype(int)\n", "order_projections_new[\"Outlet Id\"] = order_projections_new[\"Outlet Id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pendulum\n", "\n", "# today1 = pendulum.now()\n", "today1 = (pendulum.now() + timed<PERSON>ta(hours=5, minutes=30)).date()\n", "\n", "# start = today.start_of('week')\n", "current_start_date = pd.to_datetime(today1.start_of(\"week\").to_date_string()).date()\n", "# print(start.to_datetime_string())\n", "\n", "# end = today.end_of('week')\n", "current_end_date = pd.to_datetime(today1.end_of(\"week\").to_date_string()).date()\n", "# print(end.to_datetime_string())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pendulum\n", "\n", "# today1 = pendulum.now()\n", "today2 = (pendulum.now() + timedelta(days=7, hours=5, minutes=30)).date()\n", "\n", "# start = today.start_of('week')\n", "next_start_date = pd.to_datetime(today2.start_of(\"week\").to_date_string()).date()\n", "# print(start.to_datetime_string())\n", "\n", "# end = today.end_of('week')\n", "next_end_date = pd.to_datetime(today2.end_of(\"week\").to_date_string()).date()\n", "# print(end.to_datetime_string())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_current_week = order_projections_new[\n", "    (order_projections_new[\"date\"] >= current_start_date)\n", "    & (order_projections_new[\"date\"] <= current_end_date)\n", "]\n", "order_projections_next_week = order_projections_new[\n", "    (order_projections_new[\"date\"] >= next_start_date)\n", "    & (order_projections_new[\"date\"] <= next_end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_current_week.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_current_week_group = (\n", "    order_projections_current_week.groupby([\"Outlet Id\"])\n", "    .agg({\"orders\": \"sum\"})\n", "    .reset_index()\n", ")\n", "order_projections_current_week_group.rename(\n", "    columns={\"orders\": \"current_week_orders\"}, inplace=True\n", ")\n", "order_projections_next_week_group = (\n", "    order_projections_next_week.groupby([\"Outlet Id\"])\n", "    .agg({\"orders\": \"sum\"})\n", "    .reset_index()\n", ")\n", "order_projections_next_week_group.rename(\n", "    columns={\"orders\": \"next_week_orders\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_order_projections = order_projections_current_week_group.merge(\n", "    order_projections_next_week_group, on=\"Outlet Id\", how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_order_projections.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final = order_run_rate_group_new.merge(\n", "    final_order_projections, left_on=\"outlet\", right_on=\"Outlet Id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final[\"current_growth_per\"] = (\n", "    order_run_rate_final[\"current_week_orders\"]\n", "    / order_run_rate_final[\"total_orderssum\"]\n", ") - 1\n", "order_run_rate_final[\"next_growth_per\"] = (\n", "    order_run_rate_final[\"next_week_orders\"] / order_run_rate_final[\"total_orderssum\"]\n", ") - 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# datetime.strptime(order_projections_new['date'].iloc[0],'%m/%d/%y %H:%M:%S')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new[order_run_rate_group_new['outlet']==3660]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate[order_run_rate['outlet']==1397]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new['prev_week_order']=order_run_rate_group_new.groupby(['outlet_name','outlet'])['total_orderssum'].shift(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new = order_run_rate_group_new.dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_group_new['growth_per'] = (order_run_rate_group_new['total_orderssum']-order_run_rate_group_new['prev_week_order'])/order_run_rate_group_new['prev_week_order']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_week_count = order_run_rate_group_new.groupby(['outlet_name','outlet'])['week'].count().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_week_count = df_week_count.rename(columns={'week':'no_of_weeks'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final = order_run_rate_group_new.merge(df_week_count,on=['outlet','outlet_name'],how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final.to_csv('weekly_orders.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def weightage(x):\n", "#     if x['no_of_weeks']==1:\n", "#         return 1\n", "#     elif x['no_of_weeks']==2:\n", "#         if x['rank_new']==1:\n", "#             return 0.3\n", "#         elif x['rank_new']==2:\n", "#             return 0.7\n", "#     elif x['no_of_weeks']==3:\n", "#         if x['rank_new']==1:\n", "#             return 0.2\n", "#         elif x['rank_new']==2:\n", "#             return 0.3\n", "#         elif x['rank_new']==3:\n", "#             return 0.5\n", "#     elif x['no_of_weeks']==4:\n", "#         if x['rank_new']==1:\n", "#             return 0.1\n", "#         elif x['rank_new']==2:\n", "#             return 0.2\n", "#         elif x['rank_new']==3:\n", "#             return 0.3\n", "#         elif x['rank_new']==4:\n", "#             return 0.4\n", "#     else:\n", "#         return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final['rank_new'] = order_run_rate_final.groupby(['outlet_name','outlet'])[\"constant\"].apply(lambda x: x.cumsum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final[order_run_rate_final['outlet']==1397]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final['weightage'] = order_run_rate_final.apply(lambda x: weightage(x),axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final['growth_per_new'] = order_run_rate_final['growth_per']*order_run_rate_final['weightage']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final_new = order_run_rate_final.groupby(['outlet','outlet_name']).agg({'growth_per_new':'sum'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_final_new['order_growth_per'] = np.where(order_run_rate_final_new['growth_per_new']<order_growth_buffer,order_growth_buffer,order_run_rate_final_new['growth_per_new'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def q90(x):\n", "#     return x.quantile(0.9)\n", "# def q85(x):\n", "#     return x.quantile(0.85)\n", "# def q80(x):\n", "#     return x.quantile(0.8)\n", "# def q70(x):\n", "#     return x.quantile(0.7)\n", "\n", "\n", "def percentile(x):\n", "    return x.quantile(order_percentile)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth = order_run_rate_final.merge(\n", "    data_new_rolling, on=[\"outlet\", \"outlet_name\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# growth_buffer_data = pb.from_sheets('1ZXHqN9k7i8VTf9IUAE3q7xKJEOmCugWpPUTqYH--wz0','Sheet7',clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# growth_buffer_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# growth_buffer_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# growth_buffer_data['outlet'] = growth_buffer_data['outlet'].astype(int)\n", "# growth_buffer_data['per'] = growth_buffer_data['per'].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ### Need to change this code\n", "# if growth_dynamic == \"Yes\":\n", "#     print('Yes')\n", "#     data_new_rolling_growth['total_orders_final']=data_new_rolling_growth['total_orders']*(1+data_new_rolling_growth['order_growth_per'])\n", "# else:\n", "#     data_new_rolling_growth['total_orders_final']=data_new_rolling_growth['total_orders']*(1+growth_per)\n", "\n", "\n", "# data_new_rolling_growth = data_new_rolling_growth.merge(growth_buffer_data[['outlet','per']],on=['outlet'],how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth[\"total_orders_final_current_week\"] = data_new_rolling_growth[\n", "    \"total_orders\"\n", "] * (1 + data_new_rolling_growth[\"current_growth_per\"])\n", "data_new_rolling_growth[\"total_orders_final_next_week\"] = data_new_rolling_growth[\n", "    \"total_orders\"\n", "] * (1 + data_new_rolling_growth[\"next_growth_per\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth[\"total_orders_final_current_week\"] = data_new_rolling_growth[\n", "    \"total_orders_final_current_week\"\n", "] * (1 + general_buffer)\n", "data_new_rolling_growth[\"total_orders_final_next_week\"] = data_new_rolling_growth[\n", "    \"total_orders_final_next_week\"\n", "] * (1 + general_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling_growth = data_new_rolling_growth.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling_growth[(data_new_rolling_growth['outlet']==3660) & (data_new_rolling_growth['hour']==6)].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling_growth[(data_new_rolling_growth['outlet']==3660) & (data_new_rolling_growth['hour']==9)].dropna()['total_orders_final'].values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# np.quantile(data_new_rolling_growth[(data_new_rolling_growth['outlet']==3660) & (data_new_rolling_growth['hour']==9)].dropna()['total_orders_final'].values,0.8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling_growth[data_new_rolling_growth['outlet']==3660].to_csv('3660_outlet_data.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_new = (\n", "    data_new_rolling_growth.groupby([\"outlet\", \"outlet_name\", \"hour\"])\n", "    .agg(\n", "        {\n", "            \"total_orders_final_current_week\": percentile,\n", "            \"total_orders_final_next_week\": percentile,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_new_rolling_new[data_new_rolling_new['outlet']==1190]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final = data_new_rolling_new.merge(data_buffer_hour_min_rolling_new[['outlet','outlet_name','hour','per']],on=['outlet','outlet_name','hour'],how='left')\n", "data_final = data_new_rolling_new.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# exclude_outlet = [1346, 1851, 2137, 2340, 2847,2141]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final = data_final.dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final[data_final['total_orders_final_current_week'].isnull()].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final[data_final['picker_req'].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final['min_picker_req'] = data_final['total_orders_final'].apply(np.floor)\n", "# data_final['max_picker_req'] = data_final['total_orders_final'].apply(np.ceil)\n", "# data_final['picker_req'] = data_final['total_orders_final'].apply(np.ceil)\n", "# data_final = data_final[~data_final['outlet'].isin(exclude_outlet)]\n", "\n", "data_final[\"current_picker_req\"] = data_final[\"total_orders_final_current_week\"]\n", "data_final[\"current_picker_req\"] = data_final[\"current_picker_req\"].apply(np.ceil)\n", "data_final[\"current_picker_req\"] = data_final[\"current_picker_req\"].astype(int)\n", "\n", "# data_final['current_weekend_picker_req'] = data_final['total_orders_final_current_week']*(1+weekly_off_buffer)\n", "# data_final['current_weekend_picker_req'] = data_final['current_weekend_picker_req'].apply(np.ceil)\n", "# data_final['current_weekend_picker_req'] = data_final['current_weekend_picker_req'].astype(int)\n", "\n", "\n", "data_final[\"next_picker_req\"] = data_final[\"total_orders_final_next_week\"]\n", "data_final[\"next_picker_req\"] = data_final[\"next_picker_req\"].apply(np.ceil)\n", "data_final[\"next_picker_req\"] = data_final[\"next_picker_req\"].astype(int)\n", "\n", "# data_final['next_weekend_picker_req'] = data_final['total_orders_final_next_week']*(1+weekly_off_buffer)\n", "# data_final['next_weekend_picker_req'] = data_final['next_weekend_picker_req'].apply(np.ceil)\n", "# data_final['next_weekend_picker_req'] = data_final['next_weekend_picker_req'].astype(int)\n", "\n", "# data_final['current_weekend_picker_req'] = data_final['total_orders_final_current_week']*(1+weekly_off_buffer)\n", "# data_final['picker_req'] = data_final['picker_req'].apply(np.ceil)\n", "# data_final = data_final[~data_final['outlet'].isin(exclude_outlet)]\n", "# data_final['picker_req'] = data_final['picker_req'].astype(int)\n", "# data_final['picker_req'] = data_final['total_orders_final'].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final[\"hour\"] = data_final[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_shifts_final['shift_list'] = data_shifts_final.apply(lambda x: [x['shift_morning'],x['shift_evening'],'07:00-16:00','15:00-00:00'],axis=1)\n", "data_final[\"shift_list\"] = data_final.apply(\n", "    lambda x: [\"06:00-15:00\", \"15:00-00:00\"], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_shifts_dict = dict(zip(data_final[\"outlet\"], data_final[\"shift_list\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_shifts_final.to_pickle('shifts.pkl')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final[\"hour\"] = data_final[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final[\"hour_start\"] = data_final[\"hour\"].apply(\n", "    lambda x: ((datetime.now()).replace(hour=x, minute=0, second=0)).strftime(\n", "        \"%Y-%m-%d %H:%M:%S\"\n", "    )\n", ")\n", "data_final[data_final[\"outlet\"] == 1190]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(data_final.shape[0] + 1):\n", "    s = data_final.iloc[\n", "        [i],\n", "    ]\n", "    s = s.reindex(s.index.repeat(1))\n", "    s.loc[:, \"hour_start\"] = (\n", "        pd.to_datetime(s.hour_start.values[0]) + timed<PERSON>ta(hours=24)\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data_final = pd.concat([data_final, s])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final = data_final.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manpower Scheduling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install ortools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime, timedelta\n", "from random import randint, choice\n", "import numpy as np\n", "import pencilbox as pb\n", "from scheduler import manpower_scheduler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["((datetime.now() + <PERSON><PERSON>ta(days=1)).replace(hour=1, minute=0, second=0)).strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# manpower_rqt_start_time = \"2022-01-31 01:00:00\"\n", "# manpower_rqt_end_time = \"2022-02-01 00:00:00\"\n", "\n", "manpower_rqt_start_time = (\n", "    (datetime.now()).replace(hour=1, minute=0, second=0)\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "manpower_rqt_end_time = (\n", "    (datetime.now() + <PERSON><PERSON><PERSON>(days=1)).replace(hour=0, minute=0, second=0)\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "shift_length_time_set = (9,)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manpower_rqt_end_time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def hour_rounder(t):\n", "    return t.replace(second=0, microsecond=0, minute=0, hour=t.hour) + timedelta(\n", "        hours=t.minute // 30\n", "    )\n", "\n", "\n", "datetime_format = \"%Y-%m-%d %H:%M:%S\"\n", "# manpower_rqt_start_time = hour_rounder(datetime.strptime(manpower_rqt_start_time, datetime_format))\n", "# manpower_rqt_end_time = hour_rounder(datetime.strptime(manpower_rqt_end_time, datetime_format))\n", "\n", "hour_starts = pd.date_range(manpower_rqt_start_time, manpower_rqt_end_time, freq=\"1H\")\n", "number_of_hrs = len(hour_starts)\n", "\n", "# manpower_rqmt_df = pd.DataFrame({\"hour_start\":hour_starts, \"requirement\": [randint(5,10) for _ in range(number_of_hrs)]})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement = data_final.copy()\n", "# pd.read_csv(\"picker_hourly_requirement.csv\")\n", "# data_final.to_csv(\"picker_hourly_requirement.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement = hourly_requirement[\n", "    (hourly_requirement[\"hour_start\"] >= manpower_rqt_start_time)\n", "    & (hourly_requirement[\"hour_start\"] <= manpower_rqt_end_time)\n", "]\n", "hourly_requirement[\"hour_start\"] = hourly_requirement[\"hour_start\"].apply(\n", "    pd.to_datetime\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement.rename(columns={\"current_picker_req\": \"requirement\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_list = hourly_requirement[\"outlet\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current = pd.DataFrame()\n", "for merchant in merchant_list:\n", "    #     print(merchant)\n", "    manpower_rqmt_df = hourly_requirement[hourly_requirement[\"outlet\"] == merchant][\n", "        [\"hour_start\", \"requirement\"]\n", "    ].reset_index(drop=True)\n", "    #     print(manpower_rqmt_df)\n", "    merchant_name = hourly_requirement[hourly_requirement[\"outlet\"] == merchant][\n", "        \"outlet_name\"\n", "    ].values[0]\n", "    #     print(merchant_name)\n", "    #     print(hour_starts, shift_length_time_set, manpower_rqmt_df)\n", "    manpower_df = manpower_scheduler(\n", "        hour_starts, shift_length_time_set, manpower_rqmt_df, data_shifts_dict[merchant]\n", "    )\n", "    #     print(manpower_df)\n", "    manpower_df = manpower_df[manpower_df[\"optimal_manpower\"] > 0].reset_index(\n", "        drop=True\n", "    )\n", "    manpower_df[\"outlet\"] = merchant\n", "    manpower_df[\"outlet_name\"] = merchant_name\n", "    final_current = final_current.append(manpower_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current[\"shift\"] = final_current.apply(\n", "    lambda row: row[\"slot_start\"].strftime(\"%H\")\n", "    + \" - \"\n", "    + row[\"slot_end\"].strftime(\"%H\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current.rename(\n", "    columns={\"total_slot_time\": \"shift_length\", \"optimal_manpower\": \"picker_required\"},\n", "    inplace=True,\n", ")\n", "final_current = final_current[\n", "    [\"outlet\", \"outlet_name\", \"shift\", \"shift_length\", \"picker_required\"]\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group = (\n", "    final_current.groupby([\"outlet\", \"outlet_name\"])\n", "    .agg({\"picker_required\": \"sum\"})\n", "    .reset_index()\n", ")\n", "final_current_group = final_current_group.rename(\n", "    columns={\"picker_required\": \"current_weekday_picker_req\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group[\"current_weekend_picker_req\"] = final_current_group[\n", "    \"current_weekday_picker_req\"\n", "] * (1 + weekly_off_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group[\"current_weekend_picker_req\"] = final_current_group[\n", "    \"current_weekend_picker_req\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current[final_current[\"outlet\"] == 1022]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth[data_new_rolling_growth[\"outlet\"] == 1022]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Week Manpower Scheduling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next = data_final.copy()\n", "# pd.read_csv(\"picker_hourly_requirement.csv\")\n", "# data_final.to_csv(\"picker_hourly_requirement.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next = hourly_requirement_next[\n", "    (hourly_requirement_next[\"hour_start\"] >= manpower_rqt_start_time)\n", "    & (hourly_requirement_next[\"hour_start\"] <= manpower_rqt_end_time)\n", "]\n", "hourly_requirement_next[\"hour_start\"] = hourly_requirement_next[\"hour_start\"].apply(\n", "    pd.to_datetime\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next.rename(columns={\"next_picker_req\": \"requirement\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_list_next = hourly_requirement_next[\"outlet\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next = pd.DataFrame()\n", "for merchant in merchant_list_next:\n", "    #     print(merchant)\n", "    manpower_rqmt_df = hourly_requirement_next[\n", "        hourly_requirement_next[\"outlet\"] == merchant\n", "    ][[\"hour_start\", \"requirement\"]].reset_index(drop=True)\n", "    #     print(manpower_rqmt_df)\n", "    merchant_name = hourly_requirement_next[\n", "        hourly_requirement_next[\"outlet\"] == merchant\n", "    ][\"outlet_name\"].values[0]\n", "    #     print(merchant_name)\n", "    #     print(hour_starts, shift_length_time_set, manpower_rqmt_df)\n", "    manpower_df = manpower_scheduler(\n", "        hour_starts, shift_length_time_set, manpower_rqmt_df, data_shifts_dict[merchant]\n", "    )\n", "    #     print(manpower_df)\n", "    manpower_df = manpower_df[manpower_df[\"optimal_manpower\"] > 0].reset_index(\n", "        drop=True\n", "    )\n", "    manpower_df[\"outlet\"] = merchant\n", "    manpower_df[\"outlet_name\"] = merchant_name\n", "    final_next = final_next.append(manpower_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next[\"shift\"] = final_next.apply(\n", "    lambda row: row[\"slot_start\"].strftime(\"%H\")\n", "    + \" - \"\n", "    + row[\"slot_end\"].strftime(\"%H\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next.rename(\n", "    columns={\"total_slot_time\": \"shift_length\", \"optimal_manpower\": \"picker_required\"},\n", "    inplace=True,\n", ")\n", "final_next = final_next[\n", "    [\"outlet\", \"outlet_name\", \"shift\", \"shift_length\", \"picker_required\"]\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group = (\n", "    final_next.groupby([\"outlet\", \"outlet_name\"])\n", "    .agg({\"picker_required\": \"sum\"})\n", "    .reset_index()\n", ")\n", "final_next_group = final_next_group.rename(\n", "    columns={\"picker_required\": \"next_weekday_picker_req\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group[\"next_weekend_picker_req\"] = final_next_group[\n", "    \"next_weekday_picker_req\"\n", "] * (1 + weekly_off_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group[\"next_weekend_picker_req\"] = final_next_group[\n", "    \"next_weekend_picker_req\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group[final_next_group[\"outlet\"] == 1022]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_upto_4_min = final_current_group.merge(\n", "    final_next_group, on=[\"outlet\", \"outlet_name\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_upto_4_min.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_upto_4_min.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_upto_4_min = final_manpower_upto_4_min.rename(\n", "    columns={\n", "        \"current_weekday_picker_req\": \"current_weekday_picker_req_upto_4_min\",\n", "        \"current_weekend_picker_req\": \"current_weekend_picker_req_upto_4_min\",\n", "        \"next_weekday_picker_req\": \"next_weekday_picker_req_upto_4_min\",\n", "        \"next_weekend_picker_req\": \"next_weekend_picker_req_upto_4_min\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Manpower At Fixed Window Size (4 min)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data_new = outlet_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data_new = order_process_time_data_new.rename(\n", "    columns={\"id\": \"outlet_id\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data_new[\"final_window_size\"] = fixed_window_size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_process_time_data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_dict_new = dict(\n", "    zip(\n", "        order_process_time_data_new.outlet_id,\n", "        order_process_time_data_new.final_window_size,\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Order with Rolling Window Logic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# q=\"\"\"\n", "# select\n", "# --pl.assigned_to_name as emp_id,\n", "#      --u.name as picker_name,\n", "#         date(i.created_at+interval '5.5 hours') as order_checkout_date,\n", "#         outlet,\n", "#         co.name as outlet_name,\n", "#         extract(hour from ((i.created_at) + interval '330 minute')) as hour,\n", "#         extract(minute from ((i.created_at) + interval '330 minute')) as min_part,\n", "#         ----TO_CHAR((pl.assigned_at) + interval '330 minute','YYYY-MM-DD HH24:MI') as timestamp,\n", "#         count(distinct i.order_id) as total_orders\n", "\n", "\n", "#         from lake_ims.ims_order_details i\n", "#         inner join lake_retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7\n", "#         inner join lake_warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "#         left join lake_warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id\n", "#         and pl.outlet_id = co.id\n", "#         and pl.state in (4,10)\n", "#         left join lake_warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "#         left join lake_warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "#         -- left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "#          --inner join lake_retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "#         where\n", "\n", "#         date(i.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "#         and pllh.pick_list_id is null\n", "\n", "#         and i.outlet in {outlet_list}\n", "#         group by 1,2,3,4,5\n", "#         order by 2,1,4,5\n", "\n", "# \"\"\".format(start_date=start_date,end_date=end_date, outlet_list=tuple(outlet_list))\n", "\n", "\n", "# data_fix = pd.read_sql(q,redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fix = data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[(data[\"outlet\"] == 1505) & (data[\"hour\"] == 13)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fix[\"total_orders\"].quantile(0.99)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fix[\"total_orders\"] = np.where(\n", "    data_fix[\"total_orders\"] > 4, 4, data_fix[\"total_orders\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_fix = data_fix[\n", "    [\"order_checkout_date\", \"outlet\", \"outlet_name\", \"hour\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_fix[\"min_list\"] = data_hour_min_fix[\"hour\"].apply(\n", "    lambda x: \"0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def splitDataFrameList(df, target_column, separator):\n", "    \"\"\"df = dataframe to split,\n", "    target_column = the column containing the values to split\n", "    separator = the symbol used to perform the split\n", "    returns: a dataframe with each entry for the target column separated, with each element moved into a new row.\n", "    The values in the other columns are duplicated across the newly divided rows.\n", "    \"\"\"\n", "\n", "    def splitListToRows(row, row_accumulator, target_column, separator):\n", "        split_row = row[target_column].split(separator)\n", "        for s in split_row:\n", "            new_row = row.to_dict()\n", "            new_row[target_column] = s\n", "            row_accumulator.append(new_row)\n", "\n", "    new_rows = []\n", "    df.apply(splitListToRows, axis=1, args=(new_rows, target_column, separator))\n", "    new_df = pd.DataFrame(new_rows)\n", "    return new_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new_fix = splitDataFrameList(data_hour_min_fix, \"min_list\", \",\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new_fix = data_hour_min_new_fix.rename(columns={\"min_list\": \"min_part\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_hour_min_new_fix[\"min_part\"] = data_hour_min_new_fix[\"min_part\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_fix = data_hour_min_new_fix.merge(\n", "    data,\n", "    on=[\"order_checkout_date\", \"outlet\", \"outlet_name\", \"hour\", \"min_part\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_fix[\"order_checkout_date\"] = data_new_fix[\"order_checkout_date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_fix = pd.DataFrame()\n", "\n", "for i in outlet_list:\n", "    try:\n", "        data_temp = (\n", "            data_new_fix[data_new_fix[\"outlet\"] == i]\n", "            .groupby([\"outlet\", \"outlet_name\", \"order_checkout_date\", \"hour\"])[\n", "                \"total_orders\"\n", "            ]\n", "            .rolling(int(window_dict_new[i]), min_periods=1)\n", "            .sum()\n", "            .reset_index()\n", "        )\n", "        #         print(data_temp.shape)\n", "        if data_temp.shape[0] != 0:\n", "            data_new_rolling_fix = data_new_rolling_fix.append(\n", "                data_temp, ignore_index=True\n", "            )\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_fix.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Order Runrate Trend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date2 = today - <PERSON><PERSON><PERSON>(days=15)\n", "end_date2 = today - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_run_rate_q=\"\"\"\n", "# select\n", "# --pl.assigned_to_name as emp_id,\n", "#      --u.name as picker_name,\n", "#         date(i.created_at+interval '5.5 hours') as order_checkout_date,\n", "#         date_part(w,date(i.created_at+interval '5.5 hours')) as week,\n", "#         date_part(y,date(i.created_at+interval '5.5 hours')) as year,\n", "#         outlet,\n", "#         co.name as outlet_name,\n", "\n", "#         ---extract(hour from ((i.created_at) + interval '330 minute')) as hour,\n", "#         ---extract(minute from ((i.created_at) + interval '330 minute')) as min_part,\n", "#         ----TO_CHAR((pl.assigned_at) + interval '330 minute','YYYY-MM-DD HH24:MI') as timestamp,\n", "#         count(distinct i.order_id) as total_orders\n", "\n", "\n", "#         from lake_ims.ims_order_details i\n", "#         inner join lake_retail.console_outlet co on co.id = i.outlet and co.business_type_id = 7\n", "#         inner join lake_warehouse_location.warehouse_pick_list_order_mapping plo on plo.order_id = i.order_id\n", "#         left join lake_warehouse_location.warehouse_pick_list pl on pl.id = plo.pick_list_id\n", "#         and pl.outlet_id = co.id\n", "#         and pl.state in (4,10)\n", "#         left join lake_warehouse_location.pick_list_log pll on pl.id = pll.pick_list_id and pll.picklist_state = 4\n", "#         left join lake_warehouse_location.pick_list_log pllh on pl.id = pllh.pick_list_id and pllh.picklist_state = 12\n", "#         -- left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "#          inner join lake_retail.warehouse_user u on pl.assigned_to_name=u.emp_id\n", "#         where\n", "\n", "#         date(i.created_at+interval '5.5 hours') between '{start_date}' and '{end_date}'\n", "#         and pllh.pick_list_id is null\n", "\n", "#         and i.outlet in {outlet_list}\n", "#         group by 1,2,3,4,5\n", "#         order by 4,3,2,1\n", "\n", "# \"\"\".format(start_date=start_date2,end_date=end_date2,outlet_list=tuple(outlet_list))\n", "\n", "\n", "# order_run_rate_fix = pd.read_sql(order_run_rate_q,redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_fix = order_run_rate.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_new_fix = order_run_rate_fix[\n", "    (order_run_rate_fix[\"order_checkout_date\"] >= start_date)\n", "    & (order_run_rate_fix[\"order_checkout_date\"] <= end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def week_func_fix(week):\n", "    return order_run_rate_group_fix[order_run_rate_group_fix[\"week\"] == week][\n", "        \"year\"\n", "    ].min()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_fix = (\n", "    order_run_rate_new_fix.groupby([\"outlet_name\", \"outlet\", \"year\", \"week\"])\n", "    .agg({\"total_orders\": [\"sum\", \"count\"]})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_fix.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_fix.columns = [\n", "    \"\".join(col).strip() for col in order_run_rate_group_fix.columns.values\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_fix[\"year_latest\"] = order_run_rate_group_fix[\"week\"].apply(\n", "    lambda x: week_func_fix(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_new_fix = (\n", "    order_run_rate_group_fix.groupby([\"outlet_name\", \"outlet\", \"year_latest\", \"week\"])\n", "    .agg({\"total_orderssum\": \"sum\", \"total_orderscount\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_group_new_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_order_projections.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final_fix = order_run_rate_group_new_fix.merge(\n", "    final_order_projections, left_on=\"outlet\", right_on=\"Outlet Id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final_fix[\"current_growth_per\"] = (\n", "    order_run_rate_final_fix[\"current_week_orders\"]\n", "    / order_run_rate_final_fix[\"total_orderssum\"]\n", ") - 1\n", "order_run_rate_final_fix[\"next_growth_per\"] = (\n", "    order_run_rate_final_fix[\"next_week_orders\"]\n", "    / order_run_rate_final_fix[\"total_orderssum\"]\n", ") - 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_run_rate_final_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix = order_run_rate_final_fix.merge(\n", "    data_new_rolling_fix, on=[\"outlet\", \"outlet_name\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix[\n", "    \"total_orders_final_current_week\"\n", "] = data_new_rolling_growth_fix[\"total_orders\"] * (\n", "    1 + data_new_rolling_growth_fix[\"current_growth_per\"]\n", ")\n", "data_new_rolling_growth_fix[\n", "    \"total_orders_final_next_week\"\n", "] = data_new_rolling_growth_fix[\"total_orders\"] * (\n", "    1 + data_new_rolling_growth_fix[\"next_growth_per\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix[\n", "    \"total_orders_final_current_week\"\n", "] = data_new_rolling_growth_fix[\"total_orders_final_current_week\"] * (\n", "    1 + general_buffer\n", ")\n", "data_new_rolling_growth_fix[\n", "    \"total_orders_final_next_week\"\n", "] = data_new_rolling_growth_fix[\"total_orders_final_next_week\"] * (1 + general_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_fix.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_new_fix = (\n", "    data_new_rolling_growth_fix.groupby([\"outlet\", \"outlet_name\", \"hour\"])\n", "    .agg(\n", "        {\n", "            \"total_orders_final_current_week\": percentile,\n", "            \"total_orders_final_next_week\": percentile,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_new_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final = data_new_rolling_new.merge(data_buffer_hour_min_rolling_new[['outlet','outlet_name','hour','per']],on=['outlet','outlet_name','hour'],how='left')\n", "data_final_fix = data_new_rolling_new_fix.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix = data_final_fix.dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_final_fix['min_picker_req'] = data_final_fix['total_orders_final'].apply(np.floor)\n", "# data_final_fix['max_picker_req'] = data_final_fix['total_orders_final'].apply(np.ceil)\n", "# data_final_fix['picker_req'] = data_final_fix['total_orders_final'].apply(np.ceil)\n", "# data_final_fix = data_final_fix[~data_final_fix['outlet'].isin(exclude_outlet)]\n", "\n", "data_final_fix[\"current_picker_req\"] = data_final_fix[\"total_orders_final_current_week\"]\n", "data_final_fix[\"current_picker_req\"] = data_final_fix[\"current_picker_req\"].apply(\n", "    np.ceil\n", ")\n", "data_final_fix[\"current_picker_req\"] = data_final_fix[\"current_picker_req\"].astype(int)\n", "\n", "# data_final_fix['current_weekend_picker_req'] = data_final_fix['total_orders_final_current_week']*(1+weekly_off_buffer)\n", "# data_final_fix['current_weekend_picker_req'] = data_final_fix['current_weekend_picker_req'].apply(np.ceil)\n", "# data_final_fix['current_weekend_picker_req'] = data_final_fix['current_weekend_picker_req'].astype(int)\n", "\n", "\n", "data_final_fix[\"next_picker_req\"] = data_final_fix[\"total_orders_final_next_week\"]\n", "data_final_fix[\"next_picker_req\"] = data_final_fix[\"next_picker_req\"].apply(np.ceil)\n", "data_final_fix[\"next_picker_req\"] = data_final_fix[\"next_picker_req\"].astype(int)\n", "\n", "# data_final_fix['next_weekend_picker_req'] = data_final_fix['total_orders_final_next_week']*(1+weekly_off_buffer)\n", "# data_final_fix['next_weekend_picker_req'] = data_final_fix['next_weekend_picker_req'].apply(np.ceil)\n", "# data_final_fix['next_weekend_picker_req'] = data_final_fix['next_weekend_picker_req'].astype(int)\n", "\n", "# data_final_fix['current_weekend_picker_req'] = data_final_fix['total_orders_final_current_week']*(1+weekly_off_buffer)\n", "# data_final_fix['picker_req'] = data_final_fix['picker_req'].apply(np.ceil)\n", "# data_final_fix = data_final_fix[~data_final_fix['outlet'].isin(exclude_outlet)]\n", "# data_final_fix['picker_req'] = data_final_fix['picker_req'].astype(int)\n", "# data_final_fix['picker_req'] = data_final_fix['total_orders_final'].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix[\"hour\"] = data_final_fix[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_shifts_final['shift_list'] = data_shifts_final.apply(lambda x: [x['shift_morning'],x['shift_evening'],'07:00-16:00','15:00-00:00'],axis=1)\n", "data_final_fix[\"shift_list\"] = data_final_fix.apply(\n", "    lambda x: [\"06:00-15:00\", \"15:00-00:00\"], axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_shifts_dict_fix = dict(zip(data_final_fix[\"outlet\"], data_final_fix[\"shift_list\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix[\"hour\"] = data_final_fix[\"hour\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix[\"hour_start\"] = data_final_fix[\"hour\"].apply(\n", "    lambda x: ((datetime.now()).replace(hour=x, minute=0, second=0)).strftime(\n", "        \"%Y-%m-%d %H:%M:%S\"\n", "    )\n", ")\n", "data_final_fix[data_final_fix[\"outlet\"] == 1190]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(data_final_fix.shape[0] + 1):\n", "    s = data_final_fix.iloc[\n", "        [i],\n", "    ]\n", "    s = s.reindex(s.index.repeat(1))\n", "    s.loc[:, \"hour_start\"] = (\n", "        pd.to_datetime(s.hour_start.values[0]) + timed<PERSON>ta(hours=24)\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    data_final_fix = pd.concat([data_final_fix, s])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix = data_final_fix.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final_fix.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manpower Scheduling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install ortools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime, timedelta\n", "from random import randint, choice\n", "import numpy as np\n", "import pencilbox as pb\n", "from scheduler import manpower_scheduler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# manpower_rqt_start_time = \"2022-01-31 01:00:00\"\n", "# manpower_rqt_end_time = \"2022-02-01 00:00:00\"\n", "\n", "manpower_rqt_start_time = (\n", "    (datetime.now()).replace(hour=1, minute=0, second=0)\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "manpower_rqt_end_time = (\n", "    (datetime.now() + <PERSON><PERSON><PERSON>(days=1)).replace(hour=0, minute=0, second=0)\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "shift_length_time_set = (9,)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manpower_rqt_end_time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def hour_rounder(t):\n", "    return t.replace(second=0, microsecond=0, minute=0, hour=t.hour) + timedelta(\n", "        hours=t.minute // 30\n", "    )\n", "\n", "\n", "datetime_format = \"%Y-%m-%d %H:%M:%S\"\n", "# manpower_rqt_start_time = hour_rounder(datetime.strptime(manpower_rqt_start_time, datetime_format))\n", "# manpower_rqt_end_time = hour_rounder(datetime.strptime(manpower_rqt_end_time, datetime_format))\n", "\n", "hour_starts = pd.date_range(manpower_rqt_start_time, manpower_rqt_end_time, freq=\"1H\")\n", "number_of_hrs = len(hour_starts)\n", "\n", "# manpower_rqmt_df = pd.DataFrame({\"hour_start\":hour_starts, \"requirement\": [randint(5,10) for _ in range(number_of_hrs)]})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix = data_final_fix.copy()\n", "# pd.read_csv(\"picker_hourly_requirement.csv\")\n", "# data_final.to_csv(\"picker_hourly_requirement.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix = hourly_requirement_fix[\n", "    (hourly_requirement_fix[\"hour_start\"] >= manpower_rqt_start_time)\n", "    & (hourly_requirement_fix[\"hour_start\"] <= manpower_rqt_end_time)\n", "]\n", "hourly_requirement_fix[\"hour_start\"] = hourly_requirement_fix[\"hour_start\"].apply(\n", "    pd.to_datetime\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix.rename(\n", "    columns={\"current_picker_req\": \"requirement\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_list = hourly_requirement_fix[\"outlet\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_fix = pd.DataFrame()\n", "for merchant in merchant_list:\n", "    #     print(merchant)\n", "    manpower_rqmt_df = hourly_requirement_fix[\n", "        hourly_requirement_fix[\"outlet\"] == merchant\n", "    ][[\"hour_start\", \"requirement\"]].reset_index(drop=True)\n", "    #     print(manpower_rqmt_df)\n", "    merchant_name = hourly_requirement_fix[\n", "        hourly_requirement_fix[\"outlet\"] == merchant\n", "    ][\"outlet_name\"].values[0]\n", "    #     print(merchant_name)\n", "    #     print(hour_starts, shift_length_time_set, manpower_rqmt_df)\n", "    manpower_df = manpower_scheduler(\n", "        hour_starts,\n", "        shift_length_time_set,\n", "        manpower_rqmt_df,\n", "        data_shifts_dict_fix[merchant],\n", "    )\n", "    #     print(manpower_df)\n", "    manpower_df = manpower_df[manpower_df[\"optimal_manpower\"] > 0].reset_index(\n", "        drop=True\n", "    )\n", "    manpower_df[\"outlet\"] = merchant\n", "    manpower_df[\"outlet_name\"] = merchant_name\n", "    final_current_fix = final_current_fix.append(manpower_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_fix[\"shift\"] = final_current_fix.apply(\n", "    lambda row: row[\"slot_start\"].strftime(\"%H\")\n", "    + \" - \"\n", "    + row[\"slot_end\"].strftime(\"%H\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_fix.rename(\n", "    columns={\"total_slot_time\": \"shift_length\", \"optimal_manpower\": \"picker_required\"},\n", "    inplace=True,\n", ")\n", "final_current_fix = final_current_fix[\n", "    [\"outlet\", \"outlet_name\", \"shift\", \"shift_length\", \"picker_required\"]\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_fix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group_fix = (\n", "    final_current_fix.groupby([\"outlet\", \"outlet_name\"])\n", "    .agg({\"picker_required\": \"sum\"})\n", "    .reset_index()\n", ")\n", "final_current_group_fix = final_current_group_fix.rename(\n", "    columns={\"picker_required\": \"current_weekday_picker_req\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group_fix[\"current_weekend_picker_req\"] = final_current_group_fix[\n", "    \"current_weekday_picker_req\"\n", "] * (1 + weekly_off_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group_fix[\"current_weekend_picker_req\"] = final_current_group_fix[\n", "    \"current_weekend_picker_req\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group_fix[final_current_group_fix[\"outlet\"] == 1022]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_current_group_fix.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Week Manpower Scheduling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next_fix = data_final_fix.copy()\n", "# pd.read_csv(\"picker_hourly_requirement.csv\")\n", "# data_final.to_csv(\"picker_hourly_requirement.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next_fix = hourly_requirement_next_fix[\n", "    (hourly_requirement_next_fix[\"hour_start\"] >= manpower_rqt_start_time)\n", "    & (hourly_requirement_next_fix[\"hour_start\"] <= manpower_rqt_end_time)\n", "]\n", "hourly_requirement_next_fix[\"hour_start\"] = hourly_requirement_next_fix[\n", "    \"hour_start\"\n", "].apply(pd.to_datetime)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next_fix.rename(\n", "    columns={\"next_picker_req\": \"requirement\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hourly_requirement_next_fix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_list_next = hourly_requirement_next_fix[\"outlet\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_fix = pd.DataFrame()\n", "for merchant in merchant_list_next:\n", "    #     print(merchant)\n", "    manpower_rqmt_df = hourly_requirement_next_fix[\n", "        hourly_requirement_next_fix[\"outlet\"] == merchant\n", "    ][[\"hour_start\", \"requirement\"]].reset_index(drop=True)\n", "    #     print(manpower_rqmt_df)\n", "    merchant_name = hourly_requirement_next_fix[\n", "        hourly_requirement_next_fix[\"outlet\"] == merchant\n", "    ][\"outlet_name\"].values[0]\n", "    #     print(merchant_name)\n", "    #     print(hour_starts, shift_length_time_set, manpower_rqmt_df)\n", "    manpower_df = manpower_scheduler(\n", "        hour_starts,\n", "        shift_length_time_set,\n", "        manpower_rqmt_df,\n", "        data_shifts_dict_fix[merchant],\n", "    )\n", "    #     print(manpower_df)\n", "    manpower_df = manpower_df[manpower_df[\"optimal_manpower\"] > 0].reset_index(\n", "        drop=True\n", "    )\n", "    manpower_df[\"outlet\"] = merchant\n", "    manpower_df[\"outlet_name\"] = merchant_name\n", "    final_next_fix = final_next_fix.append(manpower_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_fix.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_fix[\"shift\"] = final_next_fix.apply(\n", "    lambda row: row[\"slot_start\"].strftime(\"%H\")\n", "    + \" - \"\n", "    + row[\"slot_end\"].strftime(\"%H\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_fix.rename(\n", "    columns={\"total_slot_time\": \"shift_length\", \"optimal_manpower\": \"picker_required\"},\n", "    inplace=True,\n", ")\n", "final_next_fix = final_next_fix[\n", "    [\"outlet\", \"outlet_name\", \"shift\", \"shift_length\", \"picker_required\"]\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_fix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group_fix = (\n", "    final_next_fix.groupby([\"outlet\", \"outlet_name\"])\n", "    .agg({\"picker_required\": \"sum\"})\n", "    .reset_index()\n", ")\n", "final_next_group_fix = final_next_group_fix.rename(\n", "    columns={\"picker_required\": \"next_weekday_picker_req\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group_fix[\"next_weekend_picker_req\"] = final_next_group_fix[\n", "    \"next_weekday_picker_req\"\n", "] * (1 + weekly_off_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group_fix[\"next_weekend_picker_req\"] = final_next_group_fix[\n", "    \"next_weekend_picker_req\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_next_group_fix[final_next_group_fix[\"outlet\"] == 1022]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_fix_4_min = final_current_group_fix.merge(\n", "    final_next_group_fix, on=[\"outlet\", \"outlet_name\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_fix_4_min.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_fix_4_min.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_fix_4_min = final_manpower_fix_4_min.rename(\n", "    columns={\n", "        \"current_weekday_picker_req\": \"current_weekday_picker_req_fix_4_min\",\n", "        \"current_weekend_picker_req\": \"current_weekend_picker_req_fix_4_min\",\n", "        \"next_weekday_picker_req\": \"next_weekday_picker_req_fix_4_min\",\n", "        \"next_weekend_picker_req\": \"next_weekend_picker_req_fix_4_min\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower_fix_4_min.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Final Manpower Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower = final_manpower_upto_4_min.merge(\n", "    final_manpower_fix_4_min, on=[\"outlet\", \"outlet_name\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Order Bucket"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Last Week Order Bucket"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select date(order_checkout_ts_ist) as date_ts,\n", "outlet_id, replace(trim(location),'Bangalore','Bengaluru') as city_name,\n", "count(distinct order_id) as order_count\n", "from dwh.fact_supply_chain_order_details a\n", "inner join lake_retail.console_outlet c on c.id = a.outlet_id\n", "where date(order_checkout_ts_ist) between '{start_date}' and '{end_date}'\n", "and is_order_cancelled=false\n", "and outlet_id in {outlet_list} and business_type_id=7\n", "group by 1,2,3\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date, outlet_list=tuple(outlet_list)\n", ")\n", "\n", "avg_order_data = pd.read_sql(q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_order_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_order_data[avg_order_data[\"outlet_id\"] == 1914]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lw_avg_order_data = (\n", "    avg_order_data.groupby([\"outlet_id\", \"city_name\"])\n", "    .agg({\"order_count\": \"mean\", \"date_ts\": \"nunique\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lw_avg_order_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lw_avg_order_data = lw_avg_order_data[lw_avg_order_data[\"date_ts\"] == 7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lw_avg_order_data = lw_avg_order_data.rename(columns={\"order_count\": \"lw_orders\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lw_avg_order_data = lw_avg_order_data[[\"outlet_id\", \"city_name\", \"lw_orders\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_projections_current_week.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cw_avg_order_data = (\n", "    order_projections_current_week.groupby(\"Outlet Id\")\n", "    .agg({\"orders\": \"mean\"})\n", "    .reset_index()\n", ")\n", "cw_avg_order_data = cw_avg_order_data.rename(columns={\"orders\": \"cw_orders\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nw_avg_order_data = (\n", "    order_projections_next_week.groupby(\"Outlet Id\")\n", "    .agg({\"orders\": \"mean\"})\n", "    .reset_index()\n", ")\n", "nw_avg_order_data = nw_avg_order_data.rename(columns={\"orders\": \"nw_orders\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower1 = final_manpower.merge(\n", "    lw_avg_order_data, left_on=\"outlet\", right_on=\"outlet_id\", how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nw_avg_order_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower2 = final_manpower1.merge(\n", "    cw_avg_order_data, left_on=\"outlet\", right_on=\"Outlet Id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower3 = final_manpower2.merge(\n", "    nw_avg_order_data, left_on=\"outlet\", right_on=\"Outlet Id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_mapping = pb.from_sheets(\n", "    \"1V1sIwT5MnAiZOev81vLIyUJFZANyfDCy0OViHV85HbA\", \"Store Type\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["store_mapping[\"outlet_id\"] = store_mapping[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4 = final_manpower3.merge(store_mapping, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def order_bucketing(x):\n", "    if x <= 100:\n", "        return \"0-100\"\n", "    elif x <= 200:\n", "        return \"100-200\"\n", "    elif x <= 300:\n", "        return \"200-300\"\n", "    elif x <= 400:\n", "        return \"300-400\"\n", "    elif x <= 500:\n", "        return \"400-500\"\n", "    elif x <= 600:\n", "        return \"500-600\"\n", "    elif x <= 700:\n", "        return \"600-700\"\n", "    elif x <= 800:\n", "        return \"700-800\"\n", "    elif x <= 900:\n", "        return \"800-900\"\n", "    elif x <= 1000:\n", "        return \"900-1000\"\n", "    elif x <= 1100:\n", "        return \"1000-1100\"\n", "    elif x <= 1200:\n", "        return \"1100-1200\"\n", "    elif x <= 1300:\n", "        return \"1200-1300\"\n", "    elif x <= 1400:\n", "        return \"1300-1400\"\n", "    elif x <= 1500:\n", "        return \"1400-1500\"\n", "    elif x > 1500:\n", "        return \"1500+\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4[\"lw_order_bucket\"] = final_manpower4[\"lw_orders\"].apply(\n", "    lambda x: order_bucketing(x)\n", ")\n", "final_manpower4[\"cw_order_bucket\"] = final_manpower4[\"cw_orders\"].apply(\n", "    lambda x: order_bucketing(x)\n", ")\n", "final_manpower4[\"nw_order_bucket\"] = final_manpower4[\"nw_orders\"].apply(\n", "    lambda x: order_bucketing(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual_manpower_q = \"\"\"with all_data as\n", "(SELECT date(put_list.created_at+interval '5.5 hours') as date_ts,\n", "put_list.outlet_id,\n", " put_list.assigned_to_name as employee_id\n", "\n", "            \n", "FROM lake_warehouse_location.warehouse_put_list put_list\n", "INNER JOIN lake_retail.console_outlet outlet ON outlet.id=put_list.outlet_id\n", "where put_list.updated_at is not null and business_type_id=7 and\n", "outlet_id in {outlet_list} and\n", "date(put_list.created_at+interval '5.5 hours') between current_date-1 and current_date-1\n", "group by 1,2,3\n", "\n", "\n", "union\n", "\n", "SELECT date(order_checkout_ts_ist) as date_ts,outlet_id,\n", "       \n", "       \n", "    picker_id as employee_id\n", "      \n", "      \n", "FROM dwh.fact_supply_chain_order_details o\n", "inner join lake_retail.console_outlet m on m.id = o.outlet_id\n", "WHERE (date(order_checkout_ts_ist) BETWEEN CURRENT_DATE - 1 AND CURRENT_DATE-1)\n", "and order_picking_completed_ts_ist is not null and business_type_id=7\n", "and outlet_id in {outlet_list}\n", "  group by 1,2,3) \n", "  \n", "  select outlet_id, count(distinct employee_id) as actual_manpower from all_data group by 1\n", "\"\"\".format(\n", "    outlet_list=tuple(outlet_list)\n", ")\n", "\n", "actual_manpower_data = pd.read_sql(actual_manpower_q, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual_manpower_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower5 = final_manpower4.merge(\n", "    actual_manpower_data, left_on=\"outlet\", right_on=\"outlet_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_manpower5.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_final_manpower = final_manpower5[\n", "    [\n", "        \"outlet\",\n", "        \"outlet_name\",\n", "        \"city_name\",\n", "        \"type\",\n", "        \"lw_order_bucket\",\n", "        \"cw_order_bucket\",\n", "        \"current_weekday_picker_req_upto_4_min\",\n", "        \"current_weekday_picker_req_fix_4_min\",\n", "        \"current_weekend_picker_req_upto_4_min\",\n", "        \"current_weekend_picker_req_fix_4_min\",\n", "        \"actual_manpower\",\n", "        \"nw_order_bucket\",\n", "        \"next_weekday_picker_req_upto_4_min\",\n", "        \"next_weekday_picker_req_fix_4_min\",\n", "        \"next_weekend_picker_req_upto_4_min\",\n", "        \"next_weekend_picker_req_fix_4_min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_final_manpower.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_final_manpower_new = (\n", "    final_final_manpower.groupby([\"cw_order_bucket\"])\n", "    .agg(\n", "        {\n", "            \"current_weekday_picker_req_fix_4_min\": \"median\",\n", "            \"current_weekend_picker_req_fix_4_min\": \"median\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_final_manpower_new.head()\n", "final_final_manpower_new = final_final_manpower_new.rename(\n", "    columns={\n", "        \"current_weekday_picker_req_fix_4_min\": \"current_weekday_picker_req_fix_4_min_median\",\n", "        \"current_weekend_picker_req_fix_4_min\": \"current_weekend_picker_req_fix_4_min_median\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_final_manpower_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_final_final_manpower = final_final_manpower.merge(\n", "    final_final_manpower_new, on=[\"cw_order_bucket\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_final_final_manpower.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_final_final_manpower = last_final_final_manpower[\n", "    [\n", "        \"outlet\",\n", "        \"outlet_name\",\n", "        \"city_name\",\n", "        \"type\",\n", "        \"lw_order_bucket\",\n", "        \"cw_order_bucket\",\n", "        \"current_weekday_picker_req_upto_4_min\",\n", "        \"current_weekday_picker_req_fix_4_min\",\n", "        \"current_weekday_picker_req_fix_4_min_median\",\n", "        \"current_weekend_picker_req_upto_4_min\",\n", "        \"current_weekend_picker_req_fix_4_min\",\n", "        \"current_weekend_picker_req_fix_4_min_median\",\n", "        \"actual_manpower\",\n", "        \"nw_order_bucket\",\n", "        \"next_weekday_picker_req_upto_4_min\",\n", "        \"next_weekday_picker_req_fix_4_min\",\n", "        \"next_weekend_picker_req_upto_4_min\",\n", "        \"next_weekend_picker_req_fix_4_min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_final_final_manpower = last_final_final_manpower[\n", "    ~last_final_final_manpower[\"actual_manpower\"].isnull()\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    last_final_final_manpower,\n", "    \"1V1sIwT5MnAiZOev81vLIyUJFZANyfDCy0OViHV85HbA\",\n", "    \"Manpower Projection\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_variable = data_new_rolling_growth[\n", "    [\n", "        \"outlet_name\",\n", "        \"outlet\",\n", "        \"year_latest\",\n", "        \"week\",\n", "        \"total_orderssum\",\n", "        \"current_week_orders\",\n", "        \"next_week_orders\",\n", "        \"current_growth_per\",\n", "        \"next_growth_per\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_variable.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_variable = data_new_rolling_growth_variable.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_variable = data_new_rolling_growth_variable.rename(\n", "    columns={\"total_orderssum\": \"prev_week_orders\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_per_fix = data_new_rolling_growth_fix[\n", "    [\n", "        \"outlet_name\",\n", "        \"outlet\",\n", "        \"year_latest\",\n", "        \"week\",\n", "        \"total_orderssum\",\n", "        \"current_week_orders\",\n", "        \"next_week_orders\",\n", "        \"current_growth_per\",\n", "        \"next_growth_per\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_per_fix = data_new_rolling_growth_per_fix.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_rolling_growth_per_fix = data_new_rolling_growth_per_fix.rename(\n", "    columns={\"total_orderssum\": \"prev_week_orders\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_new_rolling_growth_variable,\n", "    \"1V1sIwT5MnAiZOev81vLIyUJFZANyfDCy0OViHV85HbA\",\n", "    \"Growth Per (Upto 4 min)\",\n", "    clear_cache=True,\n", ")\n", "pb.to_sheets(\n", "    data_new_rolling_growth_per_fix,\n", "    \"1V1sIwT5MnAiZOev81vLIyUJFZANyfDCy0OViHV85HbA\",\n", "    \"Growth Per (Fix 4 min)\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_dict = {\n", "    \"prev_week_start\": start_date,\n", "    \"prev_week_end\": end_date,\n", "    \"current_week_start\": current_start_date,\n", "    \"current_week_end\": current_end_date,\n", "    \"next_week_start\": next_start_date,\n", "    \"next_week_end\": next_end_date,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_df = pd.DataFrame(date_dict, index=[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    date_df,\n", "    \"1V1sIwT5MnAiZOev81vLIyUJFZANyfDCy0OViHV85HbA\",\n", "    \"Week Dates\",\n", "    clear_cache=True,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}