{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "import boto3\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"eod_inventory_snapshot.csv\"\n", "s3_bucket = \"grofers-prod-retail-reports\"\n", "s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "report_type = \"eod_inventory_snapshot\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 7):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _date in range(1, 2, 1):\n", "    date_filter = date.today() - <PERSON><PERSON><PERSON>(days=_date)\n", "    print(date_filter)\n", "    sql_query = \"\"\"\n", "    SELECT outlet.id AS outlet_id,\n", "           outlet.name AS outlet_name,\n", "           comp.name AS company_name,\n", "           prod.item_id,\n", "           prod.upc,\n", "           prod.variant_id,\n", "           (CASE\n", "                WHEN cast(prod.storage_type AS integer) = 1 THEN 'Regular'\n", "                WHEN cast(prod.storage_type AS integer) = 2 THEN 'Fridge'\n", "                WHEN cast(prod.storage_type AS integer) = 3 THEN 'Freezer'\n", "                WHEN cast(prod.storage_type AS integer) = 4 THEN 'Large'\n", "                WHEN cast(prod.storage_type AS integer) = 5 THEN 'Heavy'\n", "                WHEN cast(prod.storage_type AS integer) = 6 THEN 'Non_Veg_Cold'\n", "                WHEN cast(prod.storage_type AS integer) = 7 THEN 'Non_Veg_Frozen'\n", "                WHEN cast(prod.storage_type AS integer) = 8 THEN 'Fast_Moving'\n", "                ELSE 'Not Assigned'\n", "            END) AS \"storage_type\",\n", "           brand.name AS brand_name,\n", "           pm.name AS manufacturer,\n", "           prod.name AS product_name,\n", "           (CASE\n", "                WHEN prod.outlet_type = 1 THEN 'F&V type'\n", "                WHEN prod.outlet_type = 2 THEN 'Grocery type'\n", "                ELSE 'Not Assigned'\n", "            END) AS \"Item_Type\",\n", "           prod.variant_uom_text AS description,\n", "           prod.active,\n", "           (coalesce(EOD.inv_snapshot_datetime,EOD.good_snapshot_datetime) + interval '5' hour + interval '30' MINUTE) AS \"snapshot_datetime\",\n", "           EOD.on_shelf_quantity,\n", "           EOD.on_shelf_networth AS on_shelf_worth,\n", "           EOD.on_shelf_quantity * prod.variant_mrp AS \"on_shelf_MRPworth\",\n", "           EOD.off_shelf_quantity,\n", "           EOD.off_shelf_networth AS \"off_shelf_worth\",\n", "           (EOD.on_shelf_quantity + EOD.off_shelf_quantity) AS \"Total_Quantity\",\n", "           (EOD.on_shelf_networth + EOD.off_shelf_networth) AS \"Total_Networth\",\n", "           prod.is_pl\n", "    FROM\n", "      (SELECT coalesce(good_inv.variant_id, inv.variant_id) AS variant_id,\n", "              coalesce(good_inv.outlet_id, inv.outlet_id) AS outlet_id,\n", "              good_inv.snapshot_datetime AS \"good_snapshot_datetime\",\n", "              inv.snapshot_datetime AS \"inv_snapshot_datetime\",\n", "              coalesce(good_inv.quantity, 0) AS \"off_shelf_quantity\",\n", "              coalesce(good_inv.networth, 0) AS \"off_shelf_networth\",\n", "              coalesce(inv.quantity, 0) AS \"on_shelf_quantity\",\n", "              coalesce(inv.net_worth, 0) AS \"on_shelf_networth\"\n", "       FROM\n", "         (SELECT snapshot_datetime,\n", "                 outlet_id,\n", "                 variant_id,\n", "                 sum(quantity) AS quantity,\n", "                 sum(net_worth) AS networth\n", "          FROM lake_view_reports.reports_good_inventory_snapshot snap\n", "          INNER JOIN lake_retail.console_outlet r ON r.id = snap.outlet_id\n", "          WHERE insert_ds_ist = '{snapdate}'\n", "            AND snap.active = 1\n", "            AND r.active = 1\n", "            AND r.device_id <> 47\n", "          GROUP BY outlet_id,\n", "                   variant_id,\n", "                   snapshot_datetime) good_inv\n", "       FULL OUTER JOIN\n", "         (SELECT snapshot_datetime,\n", "                 outlet_id,\n", "                 variant_id,\n", "                 quantity,\n", "                 net_worth\n", "          FROM lake_view_reports.reports_inventory_snapshot snap\n", "          INNER JOIN lake_retail.console_outlet r ON r.id = snap.outlet_id\n", "          WHERE insert_ds_ist = '{snapdate}'\n", "            AND snap.active = 1\n", "            AND r.active = 1\n", "            AND r.device_id <> 47) inv ON inv.outlet_id = good_inv.outlet_id\n", "       AND inv.variant_id = good_inv.variant_id) EOD\n", "    INNER JOIN lake_rpc.product_product prod ON prod.variant_id = EOD.variant_id\n", "    LEFT JOIN lake_rpc.product_brand brand ON brand.id = prod.brand_id\n", "    INNER JOIN lake_rpc.product_manufacturer pm ON pm.id = brand.manufacturer_id\n", "    INNER JOIN lake_retail.console_outlet outlet ON outlet.id = EOD.outlet_id\n", "    INNER JOIN lake_retail.console_company_type comp ON comp.id=outlet.company_type_id\n", "    AND comp.entity_type <> 'THIRD_PARTY'\n", "    WHERE outlet.active = 1\n", "      AND (EOD.on_shelf_quantity > 0\n", "           OR EOD.off_shelf_quantity > 0)\n", "    ORDER BY outlet.id\n", "    \"\"\".format(\n", "        snapdate=date_filter\n", "    )\n", "\n", "    df = pd.read_sql_query(sql_query, presto)\n", "    print(df.shape)\n", "\n", "    assert len(df) > 0\n", "\n", "    df[\"snapshot_datetime\"] = pd.to_datetime(df[\"snapshot_datetime\"])\n", "    df[[\"on_shelf_worth\", \"off_shelf_worth\", \"Total_Networth\"]] = df[\n", "        [\"on_shelf_worth\", \"off_shelf_worth\", \"Total_Networth\"]\n", "    ].astype(float)\n", "\n", "    urls = \"\"\n", "    companies = df.company_name.unique()\n", "\n", "    for company_name in companies:\n", "        snap_company = df[df.company_name == company_name]\n", "        mailer_filename = f\"eod_inventory_snapshot{date_filter}_{company_name}.csv\"\n", "        snap_company.to_csv(mailer_filename, index=False)\n", "\n", "        s3_mailer_key = f\"pencilbox/dag_output/eod_inventory_snapshot/{mailer_filename}\"\n", "        pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "        url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "        urls = urls + \"\\n\" + company_name + \" - \" + url + \"\\n\"\n", "\n", "        for outlet_id in df[df.company_name == company_name].outlet_id.unique():\n", "            snap = df[df.outlet_id == outlet_id].drop(columns=[\"company_name\"])\n", "            s3_key = f\"entity_reports/{company_name}/{report_type}/{outlet_id}/{date_filter.strftime('%Y/%m/%d')}/{filename}\"\n", "            print(outlet_id, snap.shape, s3_key)\n", "            snap.to_csv(filename)\n", "            try:\n", "                pb.to_s3(filename, s3_bucket, s3_key)\n", "            except:\n", "                print(\"trying again 1 ...\")\n", "                try:\n", "                    pb.to_s3(filename, s3_bucket, s3_key)\n", "                except:\n", "                    print(\"trying again 2 ...\")\n", "                    try:\n", "                        pb.to_s3(filename, s3_bucket, s3_key)\n", "                    except:\n", "                        print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"EOD Inventory Snapshot Report - {date_filter.strftime('%Y/%m/%d')}\"\n", "\n", "html_content = f\"\"\"Hi, \n", "<br><br> Please find entity wise EOD Inventory Snapshot Reports for {date_filter.strftime('%Y/%m/%d')}. \n", "<br> The links will expire in 7 days. Please download it for reference.\n", "<br> {urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod = (\n", "    df.groupby([\"snapshot_datetime\", \"outlet_id\", \"outlet_name\", \"company_name\"])\n", "    .agg(\n", "        {\n", "            \"on_shelf_quantity\": sum,\n", "            \"on_shelf_worth\": sum,\n", "            \"off_shelf_quantity\": sum,\n", "            \"off_shelf_worth\": sum,\n", "            \"Total_Quantity\": sum,\n", "            \"Total_Networth\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "eod.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(eod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailer_filename = f\"eod_inventory_snapshot_summary{date_filter}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod.to_csv(mailer_filename, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"EOD Inventory Snapshot Summary - {date_filter.strftime('%Y/%m/%d')}\"\n", "\n", "html_content = f\"\"\"Hi, \n", "<br><br> Please find outlet wise EOD Inventory Snapshot Summary for {date_filter.strftime('%Y/%m/%d')} attached. \n", "<br>\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, files=[mailer_filename])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}