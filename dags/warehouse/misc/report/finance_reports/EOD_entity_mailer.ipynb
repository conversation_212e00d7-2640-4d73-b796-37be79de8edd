{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "import boto3\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"eod_report.csv\"\n", "# s3_bucket = \"grofers-prod-retail-reports\"\n", "s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "report_type = \"eod_report\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 10):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = (date.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT outlet_id AS \"Outlet ID\",\n", "       outlet_name AS \"Outlet Name\",\n", "       vendor_name AS \"Vendor Name\",\n", "       entity_vendor_id AS \"Entity Vendor ID\",\n", "       eod.cin AS \"CIN\",\n", "       eod.gst_tin AS \"GST IN\",\n", "       eod.legal_name AS \"Legal Name\",\n", "       item_id AS \"Item ID\",\n", "       upc AS \"UPC\",\n", "       variant_id AS \"Variant ID\",\n", "       Storage_Type AS \"Storage Type\",\n", "       brand_name AS \"Brand Name\",\n", "       Manufacturer_name AS \"Manufacturer Name\",\n", "       product_name AS \"Product Name\",\n", "       Item_Type AS \"Item Type\",\n", "       Description AS \"Description\",\n", "       eod.active AS \"Active\",\n", "       snapshot_datetime AS \"Snapshot Datetime\",\n", "       on_shelf_numbers AS \"On Shelf Quantity\",\n", "       on_shelf_worth AS \"On Shelf Worth\",\n", "       On_Shelf_MRPWorth AS \"On Shelf MRP Worth\",\n", "       off_shelf_quantity AS \"Off Shelf Quantity\",\n", "       off_shelf_worth AS \"Off Shelf Worth\",\n", "       total_quantity AS \"Total Quantity\",\n", "       total_networth AS \"Total Networth\",\n", "       is_pl AS \"Is PL\"\n", "FROM\n", "  (SELECT *,\n", "          dense_rank() OVER (PARTITION BY entity_vendor_id, variant_id\n", "                             ORDER BY snapshot_datetime, etl_timestamp DESC) AS row_rank\n", "   FROM metrics.finance_eod_inventory_report\n", "   WHERE date(snapshot_datetime) = '{yesterday}'\n", "    ) eod\n", "join lake_retail.console_outlet r on r.id = eod.outlet_id\n", "  and lower(r.name) NOT like '%%infra%%'\n", "  and lower(r.name) NOT like '%%liquidation%%'\n", "  and lower(r.name) NOT like '%%kundli%%'\n", "  and r.business_type_id NOT in (8,13,14,15,17)\n", "  and r.id != 4146\n", "join lake_retail.console_company_type c on c.id = r.company_type_id \n", "  and c.entity_type != 'THIRD_PARTY'\n", "where \"row_rank\" = 1\n", "\"\"\"\n", "\n", "eod_data = pd.DataFrame()\n", "for chunk in pd.read_sql_query(query, redshift, chunksize=50000):\n", "    eod_data = pd.concat([eod_data, chunk], axis=0)\n", "\n", "eod_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert len(eod_data) > 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod_data[\"legal name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod_data.cin.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main_entities = [\n", "    \"90MINUTES RETAIL PRIVATE LIMITED\",\n", "    \"COMMONCITY RETAIL PRIVATE LIMITED\",\n", "    \"CROFCLOUD ONSALE LANDING PRIVATE LIMITED\",\n", "    \"FNV RETAIL PRIVATE LIMITED\",\n", "    \"HANDS ON TRADES PRIVATE LIMITED\",\n", "    \"KEMEXEL ECOMMERCE PRIVATE LIMITED\",\n", "    \"LA SUPER RETAIL PRIVATE LIMITED\",\n", "    \"LOVEEY TRADING INDIA PRIVATE LIMITED\",\n", "    \"MOVEON DELIVERY INTIME PRIVATE LIMITED\",\n", "    \"SSC TRADECOM PRIVATE LIMITED\",\n", "    \"STOREASY LLP\",\n", "    \"SUPERWELL COMTRADE PRIVATE LIMITED\",\n", "    \"TAMS GLOBAL PRIVATE LIMITED\",\n", "    \"MOONSTONE VENTURES LLP\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deloitte_entities = [\n", "    \"TAMS GLOBAL PRIVATE LIMITED\",\n", "    \"KEMEXEL ECOMMERCE PRIVATE LIMITED\",\n", "    \"SUPERWELL COMTRADE PRIVATE LIMITED\",\n", "    \"STOREASY LLP\",\n", "    \"MOONSTONE VENTURES LLP\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# urls = \"\"\n", "# for cin in eod_data[\"cin\"].unique():\n", "#     snap_cin = eod_data[eod_data[\"cin\"] == cin]\n", "#     mailer_filename = f'eod_report{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_{cin}.csv'\n", "#     print(mailer_filename)\n", "#     snap_cin.to_csv(mailer_filename, index=False)\n", "\n", "#     s3_mailer_key = f\"pencilbox/dag_output/eod_report/{mailer_filename}\"\n", "#     pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "#     url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "#     urls = urls + \"\\n\" + cin + \" - \" + url + \"\\n\"\n", "\n", "# print(urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = \"\"\n", "deloitte_urls = \"\"\n", "for entity_name in eod_data[\"legal name\"].unique():\n", "    if entity_name in main_entities:\n", "        snap_entity_name = eod_data[eod_data[\"legal name\"] == entity_name]\n", "        mailer_filename = f'eod_entity_report_{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_{entity_name}.csv'\n", "        print(mailer_filename)\n", "        snap_entity_name.to_csv(mailer_filename, index=False)\n", "\n", "        s3_mailer_key = f\"pencilbox/dag_output/eod_data/{mailer_filename}\"\n", "        pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "        url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "        urls = urls + \"\\n\" + entity_name + \" - \" + url + \"\\n\"\n", "\n", "        if entity_name in deloitte_entities:\n", "            deloitte_urls = deloitte_urls + \"\\n\" + entity_name + \" - \" + url + \"\\n\"\n", "\n", "others = eod_data[~(eod_data[\"legal name\"].isin(main_entities))]\n", "mailer_filename = f'eod_entity_report_{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_Others.csv'\n", "print(mailer_filename)\n", "others.to_csv(mailer_filename, index=False)\n", "\n", "s3_mailer_key = f\"pencilbox/dag_output/eod_report/{mailer_filename}\"\n", "pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "urls = urls + \"\\n Others - \" + url + \"\\n\"\n", "\n", "print(urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = urls.replace(\"\\n\", \"<br>\")\n", "deloitte_urls = deloitte_urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise EOD Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise EOD report for {yesterday}. \n", "<br> The links will expire in 10 days. Please download it for reference.\n", "<br> {urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise EOD Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise EOD report for {yesterday}. \n", "<br> The links will expire in 10 days. Please download it for reference.\n", "<br> {deloitte_urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod = (\n", "    eod_data.groupby([\"snapshot datetime\", \"outlet id\", \"outlet name\", \"cin\"])\n", "    .agg(\n", "        {\n", "            \"on shelf quantity\": sum,\n", "            \"on shelf worth\": sum,\n", "            \"off shelf quantity\": sum,\n", "            \"off shelf worth\": sum,\n", "            \"total quantity\": sum,\n", "            \"total networth\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "eod.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(eod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailer_filename = f\"entity_eod_summary{yesterday}.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eod.to_csv(mailer_filename, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise EOD Report Summary - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi, \n", "<br><br> Please find entity wise EOD Inventory Snapshot Summary for {yesterday} attached. \n", "<br>\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, files=[mailer_filename])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}