{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "import boto3\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"sales_item_report.csv\"\n", "s3_bucket = \"grofers-prod-retail-reports\"\n", "s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "report_type = \"sales_item_report\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 10):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = (date.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def push_to_s3(sales):\n", "    for company_name in sales[\"company_name\"].unique():\n", "        for outlet_id in sales[sales[\"company_name\"] == company_name][\n", "            \"outlet_id\"\n", "        ].unique():\n", "            sales_outlet = sales[sales[\"outlet_id\"] == outlet_id].drop(\n", "                columns=\"company_name\"\n", "            )\n", "            s3_key = f\"entity_reports/{company_name}/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "            print(outlet_id, sales_outlet.shape, s3_key)\n", "            sales_outlet.to_csv(filename, index=False)\n", "            try:\n", "                pb.to_s3(filename, s3_bucket, s3_key)\n", "            except:\n", "                print(\"trying again 1 ...\")\n", "                try:\n", "                    pb.to_s3(filename, s3_bucket, s3_key)\n", "                except:\n", "                    print(\"trying again 2 ...\")\n", "                    try:\n", "                        pb.to_s3(filename, s3_bucket, s3_key)\n", "                    except:\n", "                        print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT p.insert_ds_ist AS date,\n", "       rp.item_id,\n", "       pd.quantity,\n", "       p.outlet_legal_name AS entity,\n", "       p.outlet_name,\n", "       p.outlet_city,\n", "       CASE\n", "           WHEN p.invoice_type_id NOT IN (1,\n", "                                          2) THEN p.customer_name\n", "           ELSE ''\n", "       END AS buyer_company,\n", "       cm.name AS merchant_name,\n", "       cl.name AS customer_city_name,\n", "       CASE\n", "           WHEN p.invoice_type_id IN (1) THEN 'B2C Sales'\n", "           WHEN p.invoice_type_id IN (2) THEN 'B2C Sales Return'\n", "           WHEN p.invoice_type_id IN (5,\n", "                                      14,\n", "                                      16) THEN 'Excess Stock Transfer'\n", "       END AS invoice_type,\n", " Coalesce(sum(pd.selling_price* pd.quantity),0) AS total_sale_value,\n", " Coalesce(sum(pd.cess_value),0) AS total_cess_value,\n", " Coalesce(sum(pd.cgst_value),0) AS total_cgst_value,\n", " Coalesce(sum(pd.igst_value),0) AS total_igst_value,\n", " Coalesce(sum(pd.sgst_value),0) AS total_sgst_value,\n", " Coalesce(sum(pd.additional_cess_value),0) AS total_additional_cess,\n", " sum((coalesce(pd.cgst_value,0)+coalesce(pd.sgst_value,0)+coalesce(pd.igst_value,0)+coalesce(pd.cess_value,0) + coalesce(pd.additional_cess_value,0))) AS total_tax\n", "FROM\n", "  (SELECT *\n", "   FROM lake_view_pos.pos_invoice\n", "   WHERE insert_ds_ist = '{yesterday}'\n", "     AND invoice_type_id IN (1,\n", "                             2,\n", "                             5,\n", "                             14,\n", "                             16)\n", "     AND lake_active_record) p\n", "INNER JOIN\n", "  (SELECT *\n", "   FROM lake_pos.pos_invoice_product_details\n", "   WHERE insert_ds_ist = '{yesterday}'\n", "     AND store_offer_id IS NULL\n", "     AND lake_active_record) pd ON pd.invoice_id = p.id\n", "LEFT JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "LEFT JOIN lake_rpc.product_product rp ON rp.variant_id = pd.variant_id\n", "JOIN lake_retail.console_location cl ON cl.id = p.customer_city\n", "LEFT JOIN lake_vms.vms_vendor vv ON vv.id = p.source_entity_vendor_id\n", "LEFT JOIN lake_retail.console_merchant cm ON cm.id = p.merchant_id\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10\n", "                \n", "\"\"\"\n", "\n", "sales = pd.read_sql_query(query, presto)\n", "sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = \"\"\n", "for entity in sales[\"entity\"].unique():\n", "    # if company_name in retailers:\n", "    snap_entity = sales[sales[\"entity\"] == entity]\n", "    mailer_filename = f'sales_item_report{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_{entity}.csv'\n", "    print(mailer_filename)\n", "    snap_entity.to_csv(mailer_filename, index=False)\n", "\n", "    s3_mailer_key = f\"pencilbox/dag_output/sales_item_report/{mailer_filename}\"\n", "    pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "    url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "    urls = urls + \"\\n\" + entity + \" - \" + url + \"\\n\"\n", "print(urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    # \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"dev.b<PERSON><EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    #     \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\"\n", "    #     \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise Item Sales Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise sales item reports for {yesterday}. \n", "<br> The links will expire in 10 days. Please download it for reference.\n", "<br> {urls}\n", "<br> Thanks\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}