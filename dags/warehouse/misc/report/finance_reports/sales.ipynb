{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "import boto3\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"sales_report.csv\"\n", "# s3_bucket = \"grofers-prod-retail-reports\"\n", "s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "report_type = \"sales_report\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 7):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = (date.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def push_to_s3(sales):\n", "#     for company_name in sales[\"company_name\"].unique():\n", "#         for outlet_id in sales[sales[\"company_name\"] == company_name][\n", "#             \"outlet_id\"\n", "#         ].unique():\n", "#             sales_outlet = sales[sales[\"outlet_id\"] == outlet_id].drop(\n", "#                 columns=\"company_name\"\n", "#             )\n", "#             s3_key = f\"entity_reports/{company_name}/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "#             print(outlet_id, sales_outlet.shape, s3_key)\n", "#             sales_outlet.to_csv(filename, index=False)\n", "#             try:\n", "#                 pb.to_s3(filename, s3_bucket, s3_key)\n", "#             except:\n", "#                 print(\"trying again 1 ...\")\n", "#                 try:\n", "#                     pb.to_s3(filename, s3_bucket, s3_key)\n", "#                 except:\n", "#                     print(\"trying again 2 ...\")\n", "#                     try:\n", "#                         pb.to_s3(filename, s3_bucket, s3_key)\n", "#                     except:\n", "#                         print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = f\"\"\"\n", "# SELECT p.invoice_id AS \"Invoice ID\",\n", "#        p.original_invoice_id AS \"Original Invoice ID\",\n", "#        convert_timezone('Asia/Kolkata', ret_inv.pos_timestamp) AS \"Original Invoice Timestamp\",\n", "#        p.grofers_order_id AS \"Grofers Order ID\",\n", "#        convert_timezone('Asia/Kolkata', p.pos_timestamp) AS \"POS Timestamp\",\n", "#        p.outlet_name AS \"Store Name\",\n", "#        l1.name AS \"Supply City\",\n", "#        l2.name AS \"Customer City\",\n", "#        rp.item_id,\n", "#        rp.variant_description,\n", "#        rp.hsn_code,\n", "#        rp.name AS \"Product Name\",\n", "#        (CASE\n", "#             WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "#             WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "#             ELSE 'Not Assigned'\n", "#         END) AS \"Item Type\",\n", "#        COALESCE(pi.cgst_per,0) AS cgst_per,\n", "#        COALESCE(pi.sgst_per,0) AS sgst_per,\n", "#        COALESCE(pi.igst_per,0) AS igst_per,\n", "#        COALESCE(pi.cess_per,0) AS cess_per,\n", "#        sum(pi.quantity) AS \"Qty\",\n", "#        pi.unit_rate AS \"MRP\",\n", "#        (pi.unit_rate - pi.selling_price) AS Discount,\n", "#        pi.selling_price AS \"Selling Price\",\n", "#        sum(COALESCE(pi.cess_value,0)) AS cess_value,\n", "#        sum(COALESCE(pi.cgst_value,0)) AS cgst_value,\n", "#        sum(COALESCE(pi.sgst_value,0)) AS sgst_value,\n", "#        sum(COALESCE(pi.igst_value,0)) AS igst_value,\n", "#        sum(coalesce(pi.vat,0)) AS vat,\n", "#        sum(coalesce(pi.cst,0)) AS cst,\n", "#        sum(coalesce(pi.surcharge,0)) AS surcharge,\n", "#        sum(coalesce(pi.vat,0) + coalesce(pi.cst,0) + coalesce(pi.surcharge,0)) AS \"Total Tax\",\n", "#        sum(COALESCE(pi.cess_value,0)+COALESCE(pi.cgst_value,0)+COALESCE(pi.sgst_value,0)+COALESCE(pi.igst_value,0)) AS \"Total GST\",\n", "#        CASE\n", "#            WHEN p.invoice_type_id = 7 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "#            ELSE 0\n", "#        END AS \"Total Gross Bill Amount\",\n", "#        CASE\n", "#            WHEN p.invoice_type_id = 8 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "#            ELSE 0\n", "#        END AS \"Total Return Amount\",\n", "#        p.customer_name,\n", "#        p.customer_address,\n", "#        p.outlet_id, -- added\n", "#        comp.name as company_name -- added\n", "# FROM\n", "#   (SELECT *\n", "#    FROM lake_pos.pos_invoice\n", "#    WHERE date(convert_timezone('Asia/Kolkata', pos_timestamp)) = '{yesterday}'\n", "#      -- AND outlet_name ~* '(HOT|SSC|MODI)'\n", "#      -- AND outlet_name IN ({0})\n", "#      AND invoice_type_id IN (7,\n", "#                              8)) p\n", "# INNER JOIN lake_pos.pos_invoice_product_details pi ON p.id = pi.invoice_id\n", "# INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "# inner join lake_retail.console_company_type comp on comp.id = r.company_type_id\n", "# and comp.entity_type <> 'THIRD_PARTY'\n", "# and comp.id in (4,\n", "#                 9,\n", "#                 16)\n", "# INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "# INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "# LEFT JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "# LEFT JOIN lake_pos.pos_invoice ret_inv ON ret_inv.invoice_id = p.original_invoice_id\n", "# WHERE pi.store_offer_id IS NULL\n", "# GROUP BY 1,\n", "#          2,\n", "#          3,\n", "#          4,\n", "#          5,\n", "#          6,\n", "#          7,\n", "#          8,\n", "#          9,\n", "#          10,\n", "#          11,\n", "#          12,\n", "#          13,\n", "#          14,\n", "#          15,\n", "#          16,\n", "#          17,\n", "#          19,\n", "#          20,\n", "#          21,\n", "#          33,\n", "#          34,\n", "#          35,\n", "#          36,\n", "#          p.invoice_type_id,\n", "#          p.pos_timestamp\n", "# ORDER BY p.pos_timestamp;\n", "# \"\"\"\n", "\n", "# sales = pd.read_sql_query(query, redshift)\n", "# sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sales.company_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sales[\"pos timestamp\"].dt.date.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# hot_sales = sales[sales[\"company_name\"] == \"HOT\"]\n", "# cloud_sales = sales[sales[\"company_name\"].isin([\"CLOUD\", \"MODI\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# push_to_s3(hot_sales)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# push_to_s3(cloud_sales)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT invoice_id AS \"Invoice ID\",\n", "       order_id AS \"Order ID\",\n", "       pos_timestamp AS \"POS Timestamp\",\n", "       outlet_id AS \"Outlet ID\",\n", "       cin as \"CIN\",\n", "       entity_vendor_name AS \"Entity Vendor Name\",\n", "       source_entity_vendor_id AS \"Source Entity Vendor ID\",\n", "       legal_name AS \"Seller Company\",\n", "       customer_name AS \"Customer Name\",\n", "       supply_city AS \"Supply City\",\n", "       customer_city AS \"Customer City\",\n", "       item_id AS \"Item ID\",\n", "       variant_id AS \"Variant ID\",\n", "       product_name AS \"Product Name\",\n", "       item_type AS \"Item Type\",\n", "       hsn_code AS \"HSN Code\",\n", "       is_pl AS \"PL Flag\",\n", "       variant_description AS \"Variant Description\",\n", "       \"vat %% cal\" AS \"VAT %% Cal\",\n", "       \"cst %% cal\" AS \"CST %% Cal\",\n", "       igst_per AS \"IGST(%%)\",\n", "       cgst_per AS \"CGST(%%)\",\n", "       sgst_per AS \"SGST(%%)\",\n", "       cess_per AS \"CESS(%%)\",\n", "       quantity AS \"Quantity\",\n", "       mrp AS \"MRP\",\n", "       discount AS \"Discount\",\n", "       selling_price AS \"Selling Price\",\n", "       vat AS \"VAT\",\n", "       cst AS \"CST\",\n", "       igst_value AS \"IGST Value\",\n", "       cgst_value AS \"CGST Value\",\n", "       sgst_value AS \"SGST Value\",\n", "       cess_value AS \"CESS Value\",\n", "       additional_cess_value AS \"Additional Cess Value\",\n", "       total_tax AS \"Total Tax\",\n", "       total_gross_bill_amount AS \"Total Gross Bill Amount\",\n", "       total_return_amount AS \"Total Return Amount\"\n", "FROM\n", "  (SELECT *,\n", "          dense_rank() OVER (PARTITION BY id\n", "                             ORDER BY etl_timestamp DESC) AS row_rank\n", "   FROM metrics.finance_sales_report\n", "   WHERE date(pos_timestamp) = '{yesterday}' )\n", "WHERE \"row_rank\" = 1\n", "\"\"\"\n", "\n", "sales_data = pd.DataFrame()\n", "for chunk in pd.read_sql_query(query, redshift, chunksize=50000):\n", "    sales_data = pd.concat([sales_data, chunk], axis=0)\n", "\n", "sales_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deloitte_entities = [\n", "    \"TAMS GLOBAL PRIVATE LIMITED\",\n", "    \"KEMEXEL ECOMMERCE PRIVATE LIMITED\",\n", "    \"SUPERWELL COMTRADE PRIVATE LIMITED\",\n", "    \"STOREASY LLP\",\n", "    \"MOONSTONE VENTURES LLP\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = \"\"\n", "deloitte_urls = \"\"\n", "for seller_company in sales_data[\"seller company\"].unique():\n", "    # if company_name in retailers:\n", "    snap_seller_company = sales_data[sales_data[\"seller company\"] == seller_company]\n", "    mailer_filename = f'sales_report{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_{seller_company}.csv'\n", "    print(mailer_filename)\n", "    snap_seller_company.to_csv(mailer_filename, index=False)\n", "\n", "    s3_mailer_key = f\"pencilbox/dag_output/sales_report/{mailer_filename}\"\n", "    pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "    url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "    urls = urls + \"\\n\" + seller_company + \" - \" + url + \"\\n\"\n", "\n", "    if seller_company in deloitte_entities:\n", "        deloitte_urls = deloitte_urls + \"\\n\" + seller_company + \" - \" + url + \"\\n\"\n", "print(urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = urls.replace(\"\\n\", \"<br>\")\n", "deloitte_urls = deloitte_urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise Sales Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise sales reports for {yesterday}. \n", "<br> The links will expire in 7 days. Please download it for reference.\n", "<br> {urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise Sales Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise sales reports for {yesterday}. \n", "<br> The links will expire in 7 days. Please download it for reference.\n", "<br> {deloitte_urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# same variant in an invoice billed twice\n", "# customer_sales = customer_sales.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales.company_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales[\"pos timestamp\"].dt.date.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# retailers = [\"LT\", \"FNV\", \"LA\", \"NM\", \"CROF\", \"CC\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# urls = \"\"\n", "# for company_name in customer_sales[\"company_name\"].unique():\n", "#     if company_name in retailers:\n", "#         snap_company = customer_sales[customer_sales.company_name == company_name]\n", "#         mailer_filename = f'sales_report{datetime.strptime(yesterday, \"%Y/%m/%d\").strftime(\"%Y-%m-%d\")}_{company_name}.csv'\n", "#         snap_company.to_csv(mailer_filename, index=False)\n", "\n", "#         s3_mailer_key = f\"pencilbox/dag_output/sales_report/{mailer_filename}\"\n", "#         pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "#         url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "#         urls = urls + \"\\n\" + company_name + \" - \" + url + \"\\n\"\n", "\n", "#         for outlet_id in customer_sales[customer_sales[\"company_name\"] == company_name][\n", "#             \"outlet_id\"\n", "#         ].unique():\n", "#             customer_sales_outlet = customer_sales[\n", "#                 customer_sales[\"outlet_id\"] == outlet_id\n", "#             ].drop(columns=\"company_name\")\n", "#             s3_key = f\"entity_reports/{company_name}/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "#             print(outlet_id, customer_sales_outlet.shape, s3_key)\n", "#             customer_sales_outlet.to_csv(filename, index=False)\n", "#             try:\n", "#                 pb.to_s3(filename, s3_bucket, s3_key)\n", "#             except:\n", "#                 print(\"trying again 1 ...\")\n", "#                 try:\n", "#                     pb.to_s3(filename, s3_bucket, s3_key)\n", "#                 except:\n", "#                     print(\"trying again 2 ...\")\n", "#                     try:\n", "#                         pb.to_s3(filename, s3_bucket, s3_key)\n", "#                     except:\n", "#                         print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# others = customer_sales[~customer_sales[\"company_name\"].isin(retailers)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mailer_filename = f'sales_report{datetime.strptime(yesterday, \"%Y/%m/%d\").strftime(\"%Y-%m-%d\")}_others.csv'\n", "# others.to_csv(mailer_filename, index=False)\n", "\n", "# s3_mailer_key = f\"pencilbox/dag_output/sales_report/{mailer_filename}\"\n", "# pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "# url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "# urls = urls + \"\\n\" + \"OTHERS\" + \" - \" + url + \"\\n\"\n", "\n", "# # for company_name in others[\"company_name\"].unique():\n", "# # for outlet_id in others[others[\"company_name\"] == company_name][\"outlet_id\"].unique():\n", "# for outlet_id in others[\"outlet_id\"].unique():\n", "#     others_outlet = others[others[\"outlet_id\"] == outlet_id]\n", "#     s3_key = f\"entity_reports/others/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "#     print(outlet_id, others_outlet.shape, s3_key)\n", "#     customer_sales_outlet.to_csv(filename, index=False)\n", "#     try:\n", "#         pb.to_s3(filename, s3_bucket, s3_key)\n", "#     except:\n", "#         print(\"trying again 1 ...\")\n", "#         try:\n", "#             pb.to_s3(filename, s3_bucket, s3_key)\n", "#         except:\n", "#             print(\"trying again 2 ...\")\n", "#             try:\n", "#                 pb.to_s3(filename, s3_bucket, s3_key)\n", "#             except:\n", "#                 print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# urls = urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email = \"<EMAIL>\"\n", "\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# cc = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# subject = f\"Sales Report - {yesterday}\"\n", "\n", "# html_content = f\"\"\"Hi,\n", "# <br><br> Please find entity wise sales reports for {yesterday}.\n", "# <br> The links will expire in 7 days. Please download it for reference.\n", "# <br> {urls}\n", "# <br> Thanks\n", "# <br> Data Warehouse\n", "# \"\"\"\n", "# pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}