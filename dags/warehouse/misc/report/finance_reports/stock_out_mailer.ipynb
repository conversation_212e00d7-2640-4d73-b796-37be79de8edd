{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "import boto3\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"stock_out_report.csv\"\n", "# s3_bucket = \"grofers-prod-retail-reports\"\n", "s3_mailer_bucket = \"grofers-prod-dse-sgp\"\n", "report_type = \"stock_out_report\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_presigned_url(s3_bucket, s3_key, expiration=60 * 60 * 24 * 7):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": s3_bucket, \"Key\": s3_key},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = (date.today() - <PERSON><PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select outlet_id as \"Outlet ID\",\n", "               source_entity_vendor_name as \"Source Entity Vendor Name\",\n", "               source_entity_vendor_id as \"Source Entity Vendor ID\",\n", "               legal_name as \"Legal Name\",\n", "               source_state \"Source State\",\n", "               destination_entity_vendor_id as \"Destination Entity Vendor ID\",\n", "               merchant_name as \"Merchant Name\",\n", "               destination_legal_name as \"Destination Legal Name\",\n", "               destination_state \"Destination State\",\n", "               invoice_id as \"Invoice ID\",\n", "               item_id as \"Item ID\",\n", "               upc as \"UPC\",\n", "               variant_id as \"Variant ID\",\n", "               product_name as \"Product Name\",\n", "               item_type as \"Item Type\",\n", "               hsn_code as \"HSN Code\",\n", "               mrp as \"MRP\",\n", "               grammage as \"Grammage\",\n", "               quantity as \"Quantity\",\n", "               igst_per as \"IGST(%%)\",\n", "               cgst_per as \"CGST(%%)\",\n", "               sgst_per as \"SGST(%%)\",\n", "               cess_per as \"CESS(%%)\",\n", "               igst_value as \"IGST Value\",\n", "               cgst_value as \"CGST Value\",\n", "               sgst_value as \"SGST Value\",\n", "               cess_value as \"CESS Value\",\n", "               additional_cess_value as \"Additional CESS Value\",\n", "               total_basic_amount as \"Total Basic Amount\",\n", "               pos_timestamp as \"POS Timestamp\",\n", "               cloud_landing_price as \"Cloud Landing Price\",\n", "               landing_price as \"Landing Price\",\n", "               net_worth as \"Net Worth\"\n", "        from (\n", "        select *,\n", "               dense_rank() over (partition by invoice_id, variant_id\n", "                                      order by etl_timestamp desc) as row_rank\n", "        from metrics.finance_stock_out_report\n", "        where date(pos_timestamp) = '{yesterday}'\n", "        )\n", "        where \"row_rank\" = 1\n", "\"\"\"\n", "\n", "stock_out_data = pd.DataFrame()\n", "for chunk in pd.read_sql_query(query, redshift, chunksize=50000):\n", "    stock_out_data = pd.concat([stock_out_data, chunk], axis=0)\n", "\n", "stock_out_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_data[\"legal name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deloitte_entities = [\n", "    \"TAMS GLOBAL PRIVATE LIMITED\",\n", "    \"KEMEXEL ECOMMERCE PRIVATE LIMITED\",\n", "    \"SUPERWELL COMTRADE PRIVATE LIMITED\",\n", "    \"STOREASY LLP\",\n", "    \"MOONSTONE VENTURES LLP\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = \"\"\n", "deloitte_urls = \"\"\n", "for legal_name in stock_out_data[\"legal name\"].unique():\n", "    # if company_name in retailers:\n", "    snap_legal_name = stock_out_data[stock_out_data[\"legal name\"] == legal_name]\n", "    mailer_filename = f'stock_out_report{datetime.strptime(yesterday, \"%Y-%m-%d\").strftime(\"%Y-%m-%d\")}_{legal_name}.csv'\n", "    print(mailer_filename)\n", "    snap_legal_name.to_csv(mailer_filename, index=False)\n", "\n", "    s3_mailer_key = f\"pencilbox/dag_output/stock_out_report/{mailer_filename}\"\n", "    pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "    url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "    urls = urls + \"\\n\" + legal_name + \" - \" + url + \"\\n\"\n", "\n", "    if legal_name in deloitte_entities:\n", "        deloitte_urls = deloitte_urls + \"\\n\" + legal_name + \" - \" + url + \"\\n\"\n", "print(urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = urls.replace(\"\\n\", \"<br>\")\n", "deloitte_urls = deloitte_urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise Excess stock transfer (Stock Out) Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise Excess stock transfer (Stock Out) reports for {yesterday}. \n", "<br> The links will expire in 7 days. Please download it for reference.\n", "<br> {urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = f\"Entity Wise Excess stock transfer (Stock Out) Report - {yesterday}\"\n", "\n", "html_content = f\"\"\"Hi,\n", "<br><br> Please find entity wise Excess stock transfer (Stock Out) reports for {yesterday}. \n", "<br> The links will expire in 7 days. Please download it for reference.\n", "<br> {deloitte_urls}\n", "<br> Thanks,\n", "<br> Data Warehouse\n", "\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# same variant in an invoice billed twice\n", "# customer_sales = customer_sales.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales.company_name.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# customer_sales[\"pos timestamp\"].dt.date.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# retailers = [\"LT\", \"FNV\", \"LA\", \"NM\", \"CROF\", \"CC\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# urls = \"\"\n", "# for company_name in customer_sales[\"company_name\"].unique():\n", "#     if company_name in retailers:\n", "#         snap_company = customer_sales[customer_sales.company_name == company_name]\n", "#         mailer_filename = f'sales_report{datetime.strptime(yesterday, \"%Y/%m/%d\").strftime(\"%Y-%m-%d\")}_{company_name}.csv'\n", "#         snap_company.to_csv(mailer_filename, index=False)\n", "\n", "#         s3_mailer_key = f\"pencilbox/dag_output/sales_report/{mailer_filename}\"\n", "#         pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "#         url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "#         urls = urls + \"\\n\" + company_name + \" - \" + url + \"\\n\"\n", "\n", "#         for outlet_id in customer_sales[customer_sales[\"company_name\"] == company_name][\n", "#             \"outlet_id\"\n", "#         ].unique():\n", "#             customer_sales_outlet = customer_sales[\n", "#                 customer_sales[\"outlet_id\"] == outlet_id\n", "#             ].drop(columns=\"company_name\")\n", "#             s3_key = f\"entity_reports/{company_name}/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "#             print(outlet_id, customer_sales_outlet.shape, s3_key)\n", "#             customer_sales_outlet.to_csv(filename, index=False)\n", "#             try:\n", "#                 pb.to_s3(filename, s3_bucket, s3_key)\n", "#             except:\n", "#                 print(\"trying again 1 ...\")\n", "#                 try:\n", "#                     pb.to_s3(filename, s3_bucket, s3_key)\n", "#                 except:\n", "#                     print(\"trying again 2 ...\")\n", "#                     try:\n", "#                         pb.to_s3(filename, s3_bucket, s3_key)\n", "#                     except:\n", "#                         print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# others = customer_sales[~customer_sales[\"company_name\"].isin(retailers)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mailer_filename = f'sales_report{datetime.strptime(yesterday, \"%Y/%m/%d\").strftime(\"%Y-%m-%d\")}_others.csv'\n", "# others.to_csv(mailer_filename, index=False)\n", "\n", "# s3_mailer_key = f\"pencilbox/dag_output/sales_report/{mailer_filename}\"\n", "# pb.to_s3(mailer_filename, s3_mailer_bucket, s3_mailer_key)\n", "# url = create_presigned_url(s3_mailer_bucket, s3_mailer_key)\n", "# urls = urls + \"\\n\" + \"OTHERS\" + \" - \" + url + \"\\n\"\n", "\n", "# # for company_name in others[\"company_name\"].unique():\n", "# # for outlet_id in others[others[\"company_name\"] == company_name][\"outlet_id\"].unique():\n", "# for outlet_id in others[\"outlet_id\"].unique():\n", "#     others_outlet = others[others[\"outlet_id\"] == outlet_id]\n", "#     s3_key = f\"entity_reports/others/{report_type}/{outlet_id}/{yesterday}/{filename}\"\n", "#     print(outlet_id, others_outlet.shape, s3_key)\n", "#     customer_sales_outlet.to_csv(filename, index=False)\n", "#     try:\n", "#         pb.to_s3(filename, s3_bucket, s3_key)\n", "#     except:\n", "#         print(\"trying again 1 ...\")\n", "#         try:\n", "#             pb.to_s3(filename, s3_bucket, s3_key)\n", "#         except:\n", "#             print(\"trying again 2 ...\")\n", "#             try:\n", "#                 pb.to_s3(filename, s3_bucket, s3_key)\n", "#             except:\n", "#                 print(\"Failed :/ \", s3_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# urls = urls.replace(\"\\n\", \"<br>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email = \"<EMAIL>\"\n", "\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# cc = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# subject = f\"Sales Report - {yesterday}\"\n", "\n", "# html_content = f\"\"\"Hi,\n", "# <br><br> Please find entity wise sales reports for {yesterday}.\n", "# <br> The links will expire in 7 days. Please download it for reference.\n", "# <br> {urls}\n", "# <br> Thanks\n", "# <br> Data Warehouse\n", "# \"\"\"\n", "# pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}