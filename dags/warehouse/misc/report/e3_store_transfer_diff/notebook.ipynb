{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with <PERSON><PERSON> as (\n", "SELECT sto.sto_id,\n", "      sto.outlet_id,\n", "      sto.merchant_outlet_id,\n", "      (CASE\n", " \t\tWHEN sto.sto_type = 1 THEN 'MANUAL'\n", "        WHEN sto.sto_type = 2 THEN 'AUTO'\n", "        WHEN sto.sto_type = 3 THEN 'MANUAL_NO_INVENTORY_CHECK'\n", "        WHEN sto.sto_type = 4 THEN 'B2B_STO'\n", "        WHEN sto.sto_type = 5 THEN 'B2B_STO_NO_INVENTORY_CHECK'\n", "        ELSE 'Not Assigned'\n", "     END) AS STO_Type,\n", "      (CASE\n", " \t\tWHEN sto.sto_state = 1 THEN 'CREATED'\n", "        WHEN sto.sto_state = 2 THEN 'BILLED'\n", "        WHEN sto.sto_state = 3 THEN 'EXPIRED'\n", "        WHEN sto.sto_state = 4 THEN 'INWARD'\n", "        WHEN sto.sto_state = 5 THEN 'FORCE_BILLED'\n", "        ELSE 'Not Assigned'\n", "     END) AS STO_State,\n", "        date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "        date(sto.delivery_date + interval '5.5 hour') sto_delivery_date,\n", "      sto_item.item_id,\n", "      sto_item.expected_quantity AS sto_raised_quantity\n", "from   lake_ims.ims_sto_details  sto\n", "LEFT join lake_ims.ims_sto_item sto_item ON sto.sto_id=sto_item.sto_id\n", "WHERE sto.merchant_outlet_id in (846, 926, 971,978,995,993,976,1010,1016,1022,1024)\n", "and sto_delivery_date =current_date-1\n", "-- and sto.sto_id = 22385\n", "),\n", "\n", "sto_billed_grn_raw as\n", "(\n", "SELECT\n", "      pi.grofers_order_id as sto_id,\n", "      pi.invoice_id,\n", "      pi.transfer_state,\n", "  r.item_id AS Item_ID,\n", "  i.upc_id AS UPC_ID,\n", "  i.variant_id AS variant_id,\n", "  r.name AS Product,\n", "  (CASE\n", "      WHEN r.outlet_type = 1 THEN 'F&V type'\n", "    WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "         ELSE 'Not Assigned'\n", "     END) AS Item_Type,\n", "  \n", "  case when i.inventory_update_type_id in (1,28) then  i.\"delta\" else 0 end AS GRN_Quantity,\n", "  (i.pos_timestamp + interval '5.5 hour') grn_timestamp,\n", "  i.merchant_invoice_id AS Merchant_Invoice_ID,\n", "  (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "  pipd.quantity as billed_qty,\n", "  pipd.selling_price as billing_price\n", "from \n", "            lake_pos.view_pos_invoice  pi\n", "  left join lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id \n", "  left join lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "  LEFT JOIN (\n", "  select  * from lake_ims.ims_inventory_log where (pos_timestamp)  >'2020-08-10' -- and inventory_update_type_id in (1,28)\n", "  ) i  on pi.invoice_id=i.merchant_invoice_id and pipd.variant_id::varchar=i.variant_id::varchar  \n", "\n", "  WHERE \n", "  pi.invoice_type_id in (5,14)\n", "--   and pi.grofers_order_id= 20918\n", "--   and r.item_id =10005825\n", "-- and pi.invoice_id ='BSC969T200000041'\n", "  and  (pi.pos_timestamp) >'2020-07-15'\n", "  and  (i.pos_timestamp)  >'2020-07-15'\n", "--   and pi.grofers_order_id in (select distinct sto_id from Sto)\n", "--   i.inventory_update_type_id IN (1,28,76,90,93)\n", "  \n", "--   and pi.outlet_id in (845,846,847)\n", "--   and i.\"delta\" > 0\n", "--   and iii.source_type = 2\n", "     \n", "),\n", "\n", "sto_billed_grn as\n", "(\n", "select \n", "sbg.invoice_id,sbg.transfer_state,sbg.product,sbg.item_id,sbg.sto_id,\n", "(sbg.billed_qty) billed_qty,max(sbg.invoice_creation) invoice_creation,sum(sbg.grn_quantity) grn_quantity,\n", "max(sbg.grn_timestamp) grn_timestamp,sum(sbg.billing_price*billed_qty) billing_price, (sbg.billed_qty) -sum(sbg.grn_quantity) as diff_qty,\n", "(sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) as diff_amt,\n", "sbg.billing_price as unit_price\n", "from sto_billed_grn_raw sbg\n", "group by 1,2,3,4,5,6,13\n", "),\n", "\n", "discrepancy as\n", "(\n", "select created_at,vendor_invoice_id,net_amount,outlet_id,\n", "upc_id,name,cost_price,landing_price,item_id,sum(quantity) as disc_qty\n", "from(\n", "select dn.id,(dn.created_at + interval '5.5 hour') created_at,dn.vendor_name,dn.vendor_invoice_id,dn.net_amount,dn.outlet_id,\n", "dnpd.upc_id,dnpd.name,dnpd.quantity,dnpd.cost_price,dnpd.landing_price,dnpd.remark,\n", "pp.item_id\n", "from  lake_pos.discrepancy_note   dn \n", "left join lake_pos.discrepancy_note_product_detail dnpd on dn.id=dnpd.dn_id_id\n", "left join (select distinct item_id,upc from  lake_rpc.product_product ) pp on  dnpd.upc_id=pp.upc\n", "where (created_at)>'2020-08-15'\n", "-- and vendor_invoice_id='BGC100T200000622'\n", ") temp\n", "group by 1,2,3,4,5,6,7,8,9),\n", "\n", "return_b2b as\n", "(\n", "select tab1.ret_invoice_id,tab1.original_invoice_id,tab1.item_id,sum(tab1.b2b_return_qty) as b2b_return_qty,\n", "sum(tab2.b2b_customer_return) b2breturn_good,\n", "sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "sum(tab2.lost_in_transit_positive)  lost_in_transit_positive,\n", "sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "\n", "from\n", "(\n", "select pi.invoice_id ret_invoice_id, pi.original_invoice_id,pp.item_id,pipd.variant_id,pipd.quantity  b2b_return_qty from \n", "lake_pos.view_pos_invoice  pi \n", "inner join lake_pos.view_pos_invoice pi2  on pi2.invoice_id=pi.original_invoice_id\n", "left join lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id\n", "left join lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "where pi.invoice_type_id=8\n", "and   pi2.invoice_type_id in (5,14)\n", "and  (pi.pos_timestamp) > '2020-08-15' \n", "\n", "\n", ")tab1\n", "left join \n", "(\n", "select invoice_id as ret_invoice_id, item_id,\n", "sum(case  when update_type_id=23 then ret_qty end) as b2b_customer_return,\n", "sum(case  when update_type_id=24 then ret_qty end) as b2b_customer_return_expired,\n", "sum(case  when update_type_id=25 then ret_qty end) as b2b_customer_return_damaged,\n", "sum(case  when update_type_id=66 then ret_qty end) as b2b_customer_return_near_expiry,\n", "sum(case  when update_type_id=119 then ret_qty end) as lost_in_transit_positive,\n", "sum(case  when update_type_id=120 then ret_qty end) as lost_in_transit_negative,\n", "sum(ret_qty) as tot_ret_qty from\n", "(select iil.invoice_id,iil.merchant_invoice_id, iil.\"delta\" as ret_qty,iut.id as update_type_id,iut.name as update_type,pp.item_id\n", "from\n", "lake_ims.ims_inventory_log iil \n", "left join lake_ims.ims_inventory_update_type iut on iil.inventory_update_type_id=iut.id\n", "left join lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "where iil.pos_timestamp>'2020-08-15' \n", "\n", "-- and invoice_id='BZC714T200000318'\n", ") temp\n", "group by 1,2\n", ")tab2\n", "on tab1.ret_invoice_id=tab2.ret_invoice_id and tab1.item_id=tab2.item_id\n", "\n", "group by 1,2,3),\n", "\n", "final as\n", "(\n", " select sto.sto_id,sto.STO_State,sto.sto_type,sto.outlet_id,ozm1.outlet_name as sender_outlet,sto.merchant_outlet_id,ozm2.outlet_name as rec_outlet,\n", " sto.sto_created_at,sto.sto_Delivery_Date,sto.item_id,unit_price,\n", " sbg.invoice_id,sbg.transfer_state,sbg.product,sto_raised_quantity,sbg.billed_qty,sbg.invoice_creation,sbg.grn_quantity,sbg.grn_timestamp,sbg.billing_price,\n", " sbg.diff_qty diff_qty_billed_grn, sbg.diff_amt diff_amt_billed_grn,COALESCE(dis.disc_qty,0) as discrepancy_qty,\n", "--  COALESCE(b2b.b2b_return_qty,0) as b2b_return_qty\n", "COALESCE(b2b.b2b_return_qty,0)  as tot_returned,\n", "COALESCE(b2b.b2breturn_good,0)  as b2breturn_good,\n", "COALESCE(b2b.b2breturn_expired,0)  as b2breturn_expired,\n", "COALESCE(b2b.b2breturn_damaged,0)  as b2breturn_damaged,\n", "COALESCE(b2b.b2breturn_near_expiry,0) as b2breturn_near_expiry,\n", "COALESCE(b2b.lost_in_transit_positive,0) as lost_in_transit_positive,\n", "COALESCE(b2b.lost_in_transit_negative,0) as lost_in_transit_negative\n", "\n", " from sto \n", " left join sto_billed_grn sbg on sto.item_id=sbg.item_id and sto.sto_id=sbg.sto_id\n", " left join metrics.outlet_zone_mapping ozm1 on ozm1.outlet_id=sto.outlet_id\n", " left join metrics.outlet_zone_mapping ozm2 on ozm2.outlet_id=sto.merchant_outlet_id\n", " left join discrepancy dis on dis.vendor_invoice_id= sbg.invoice_id and dis.item_id=sto.item_id\n", " left join return_b2b b2b on b2b.original_invoice_id= sbg.invoice_id and b2b.item_id=sto.item_id\n", "\n", ")\n", "\n", "select \n", "sto_id, outlet_id, sender_outlet,\n", "        merchant_outlet_id, rec_outlet, sto_created_at,grn_timestamp,\n", "        sto_delivery_date, item_id,product, invoice_id,invoice_creation,unit_price,\n", "        billing_price,sto_raised_quantity,billed_qty,grn_quantity,discrepancy_qty::int,\n", "        tot_returned,b2breturn_good,b2breturn_expired,b2breturn_damaged,b2breturn_near_expiry,lost_in_transit_negative ,diff_qty_billed_grn,\n", "        billed_qty-grn_quantity-tot_returned diff_qty,diff_amt_billed_grn,( billed_qty-grn_quantity-tot_returned)*unit_price as diff_amt\n", " from final  where billed_qty>0\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.read_sql_query(sql=query, con=redshift)\n", "#             params={\n", "#                 \"start_date\": start_date,\n", "#                 \"end_date\": end_date,\n", "#                 \"outlet_id\": outlet_id,\n", "#             },"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.billing_price = raw_data.billing_price.round()\n", "raw_data.diff_amt = raw_data.diff_amt.round()\n", "raw_data.diff_amt.astype(\"int\")\n", "raw_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.to_csv(\"summary_raw.csv\")\n", "raw_data.columns, raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# raw_data.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_sto = (\n", "    raw_data.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"sender_outlet\",\n", "            \"merchant_outlet_id\",\n", "            \"rec_outlet\",\n", "            \"sto_delivery_date\",\n", "            \"sto_id\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"sto_raised_quantity\": \"sum\",\n", "            \"billed_qty\": \"sum\",\n", "            \"grn_quantity\": \"sum\",\n", "            \"billing_price\": \"sum\",\n", "            \"diff_qty\": \"sum\",\n", "            \"diff_amt\": \"sum\",\n", "        }\n", "    )\n", "    .rename(\n", "        columns={\n", "            \"outlet_id\": \"Sender_OutletID\",\n", "            \"sender_outlet\": \"Sender_Outlet\",\n", "            \"merchant_outlet_id\": \"Rec_OutletID\",\n", "            \"rec_outlet\": \"Rec_Outlet\",\n", "            \"item_id\": \"SKU_Count\",\n", "            \"sto_raised_quantity\": \"Sto_Raised_Quantity\",\n", "            \"billed_qty\": \"Billed_Quantity\",\n", "            \"grn_quantity\": \"GRN_Quantity\",\n", "            \"billing_price\": \"Billed_Amount\",\n", "            \"diff_qty\": \"Diff_Qty\",\n", "            \"diff_amt\": \"Diff_Amt\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_sto.rename(\n", "    columns={\n", "        \"outlet_id\": \"Sender_OutletID\",\n", "        \"sender_outlet\": \"Sender_Outlet\",\n", "        \"merchant_outlet_id\": \"Rec_OutletID\",\n", "        \"rec_outlet\": \"Rec_Outlet\",\n", "        \"item_id\": \"SKU_Count\",\n", "        \"sto_raised_quantity\": \"Sto_Raised_Quantity\",\n", "        \"billed_qty\": \"Billed_Quantity\",\n", "        \"grn_quantity\": \"GRN_Quantity\",\n", "        \"billing_price\": \"Billed_Amount\",\n", "        \"diff_qty\": \"Diff_Qty\",\n", "        \"diff_amt\": \"Diff_Amt\",\n", "    },\n", "    inplace=True,\n", ")\n", "summary_sto.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_sto.sort_values(\"Diff_Qty\", axis=0, ascending=False, inplace=True)\n", "\n", "summary_sto.reset_index(inplace=True, drop=True)\n", "\n", "summary_sto.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_sto[summary_sto[\"Diff_Qty\"] > 0].shape, summary_sto.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_sto.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_outlet = (\n", "    raw_data.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"sender_outlet\",\n", "            \"merchant_outlet_id\",\n", "            \"rec_outlet\",\n", "            \"sto_delivery_date\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_id\": \"nunique\",\n", "            \"item_id\": \"nunique\",\n", "            \"sto_raised_quantity\": \"sum\",\n", "            \"discrepancy_qty\": \"sum\",\n", "            \"billed_qty\": \"sum\",\n", "            \"grn_quantity\": \"sum\",\n", "            \"billing_price\": \"sum\",\n", "            \"diff_qty\": \"sum\",\n", "            \"diff_amt\": \"sum\",\n", "        }\n", "    )\n", "    .rename(\n", "        columns={\n", "            \"sto_id\": \"STO_Count\",\n", "            \"item_id\": \"SKU_Count\",\n", "            \"sto_raised_quantity\": \"Sto_Raised_Quantity\",\n", "            \"billed_qty\": \"Billed_Quantity\",\n", "            \"grn_quantity\": \"GRN_Quantity\",\n", "            \"discrepancy_qty\": \"Discrepancy_Qty\",\n", "            \"billing_price\": \"Billed_Amount\",\n", "            \"diff_qty\": \"Diff_Qty\",\n", "            \"diff_amt\": \"Diff_Amt\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_outlet.replace([\"Super Store\", \"- Warehouse\"], \"\", regex=True, inplace=True)\n", "summary_outlet.rename(\n", "    columns={\n", "        \"outlet_id\": \"Sender_OutletID\",\n", "        \"sender_outlet\": \"Sender_Outlet\",\n", "        \"merchant_outlet_id\": \"Rec_OutletID\",\n", "        \"rec_outlet\": \"Rec_Outlet\",\n", "        \"sto_delivery_date\": \"Del_Date\",\n", "        \"item_id\": \"SKU_Count\",\n", "        \"sto_raised_quantity\": \"Sto_Raised_Quantity\",\n", "        \"billed_qty\": \"Billed_Quantity\",\n", "        \"grn_quantity\": \"GRN_Quantity\",\n", "        \"billing_price\": \"Billed_Amount\",\n", "        \"diff_qty\": \"Diff_Qty\",\n", "        \"diff_amt\": \"Diff_Amt\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "summary_outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_with_diff = summary_sto[summary_sto[\"Diff_Qty\"] > 0]\n", "sto_with_diff.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_outlet_sto_diff = (\n", "    sto_with_diff.groupby(\n", "        [\n", "            \"Sender_OutletID\",\n", "            \"Sender_Outlet\",\n", "            \"Rec_OutletID\",\n", "            \"Rec_Outlet\",\n", "            \"sto_delivery_date\",\n", "        ]\n", "    )\n", "    .agg({\"sto_id\": \"nunique\"})\n", "    .rename(\n", "        columns={\n", "            \"sto_id\": \"STO_With_Diff\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "summary_outlet_sto_diff.rename(columns={\"sto_delivery_date\": \"Del_Date\"}, inplace=True)\n", "\n", "summary_outlet_sto_diff.drop([\"Sender_Outlet\", \"Rec_Outlet\"], axis=1, inplace=True)\n", "summary_outlet_sto_diff.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pd.merge(\n", "    summary_outlet,\n", "    summary_outlet_sto_diff,\n", "    how=\"left\",\n", "    left_on=[\"Sender_OutletID\", \"Rec_OutletID\", \"Del_Date\"],\n", "    right_on=[\"Sender_OutletID\", \"Rec_OutletID\", \"Del_Date\"],\n", ")\n", "\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"Sender_OutletID\",\n", "    \"Sender_Outlet\",\n", "    \"Rec_OutletID\",\n", "    \"Rec_Outlet\",\n", "    \"Del_Date\",\n", "    \"STO_Count\",\n", "    \"STO_With_Diff\",\n", "    \"SKU_Count\",\n", "    \"Sto_Raised_Quantity\",\n", "    \"Billed_Quantity\",\n", "    \"GRN_Quantity\",\n", "    \"Discrepancy_Qty\",\n", "    \"Diff_Qty\",\n", "    \"Billed_Amount\",\n", "    \"Diff_Amt\",\n", "]\n", "result = final.reindex(columns=column_names)\n", "result[\"Diff_Amt_Perc\"] = (result[\"Diff_Amt\"] / result[\"Billed_Amount\"]) * 100\n", "result[\"Billed_fill_rate\"] = (\n", "    result[\"Billed_Quantity\"] / result[\"Sto_Raised_Quantity\"]\n", ") * 100\n", "result[\"GRN_fill_rate\"] = (result[\"GRN_Quantity\"] / result[\"Billed_Quantity\"]) * 100\n", "\n", "result.Diff_Amt_Perc = result.Diff_Amt_Perc.round(2)\n", "result.Billed_fill_rate = result.Billed_fill_rate.round(2)\n", "result.GRN_fill_rate = result.GRN_fill_rate.round(2)\n", "result.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.shape, result.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "mail_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"E3 Stores ESTO Diff Summary for Delivery Dated: \" + str(mail_date)\n", "\n", "sheet_id = \"1twqLD46IOdSnIhL6gR3riti6wdlZaG1Ojju5iVG2MYg\"\n", "sheet_name = \"mailers\"\n", "mail_list = pb.from_sheets(sheet_id, sheet_name)\n", "to_email = mail_list[\"mail_id\"].to_list()\n", "\n", "# to_email=['<EMAIL>','<EMAIL>']\n", "\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/report/e3_store_transfer_diff\"\n", "    #                 searchpath=\"/home/<USER>/Development/grofers/airflow-dags/daggers/warehouse/misc/report/e3_store_transfer_diff\"\n", ")\n", "\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "\n", "template = tmpl_environ.get_template(\"email_template.html\")\n", "\n", "message_text = template.render(\n", "    products=result.to_dict(orient=\"records\"),\n", ")\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    bcc=[\n", "        \"<EMAIL>\",\n", "    ],\n", "    #     cc=cc,\n", "    html_content=message_text,\n", "    files=[\"summary_raw.csv\"],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_email"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}