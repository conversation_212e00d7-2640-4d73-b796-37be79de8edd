{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta, date\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "\n", "# pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for a in range(1, 39, 1):\n", "for a in range(0, 1, 1):\n", "    yesterday = date.today() - <PERSON><PERSON><PERSON>(days=a + 1)\n", "    print(yesterday)\n", "\n", "    # query = f\"\"\"SELECT\n", "    #           iil.entity_vendor_id,\n", "    #           v.vendor_name,\n", "    #           cti.cin,\n", "    #           cti.gst_tin,\n", "    #           cti.legal_name,\n", "    #           vcm.city_id,\n", "    #           iil.outlet_id AS \"Outlet_id\",\n", "    #            o.name AS \"Store_Name\",\n", "    #            p.item_id AS \"Item_Id\",\n", "    #            iil.upc_id AS \"UPC_Id\",\n", "    #            p.name AS \"Product_Name\",\n", "    #            p.variant_mrp AS \"Variant_MRP\",\n", "    #            p.variant_uom_text AS \"Grammage\",\n", "    #            convert_timezone('Asia/Kolkata',iil.pos_timestamp) AS \"POS_Timestamp\",\n", "    #            iiut.inventory_operation AS \"Operation\",\n", "    #            iil.\"delta\" AS \"Change_Quantity\",\n", "    #            iil.quantity AS \"Final_Quantity\",\n", "    #            iiut.name AS \"Inventory_Update_Type\",\n", "    #            iil.id as inventory_update_id,\n", "    #            iil.created_by_name AS \"Created_By_Name\",\n", "    #            iil.created_by_phone AS \"Created_By_Phone\",\n", "    #            au.email AS \"Store_Manager_Email_id\",\n", "    #            iil.transaction_lp AS \"HOT_Landing_Price\"\n", "    #     FROM lake_ims.ims_inventory_log iil\n", "    #     INNER JOIN lake_rpc.product_product p ON iil.variant_id=p.variant_id\n", "    #     INNER JOIN lake_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "    #     INNER JOIN lake_retail.console_outlet o ON o.id=iil.outlet_id\n", "    #     INNER JOIN lake_retail.auth_user au ON iil.created_by=au.id\n", "    #     LEFT JOIN lake_vms.vms_vendor v ON v.id = iil.entity_vendor_id\n", "    #     LEFT JOIN lake_vms.vms_vendor_city_mapping vcm ON vcm.vendor_id = iil.entity_vendor_id AND vcm.active = 1\n", "    #     LEFT JOIN lake_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = vcm.id AND cti.active = 1\n", "    #     WHERE iil.inventory_update_type_id IN (14,\n", "    #                                            15,\n", "    #                                            39,\n", "    #                                            40,\n", "    #                                            41,\n", "    #                                            42,\n", "    #                                            43,\n", "    #                                            44,\n", "    #                                            45,\n", "    #                                            46,\n", "    #                                            47,\n", "    #                                            48)\n", "    #       AND date(iil.pos_timestamp + interval '5.5 hrs') in ('{yesterday}')\n", "    #     ORDER BY iil.pos_timestamp DESC;\"\"\"\n", "\n", "    query = f\"\"\"SELECT iil.entity_vendor_id,\n", "               v.vendor_name,\n", "               cti.cin,\n", "               cti.gst_tin,\n", "               cti.legal_name,\n", "               vcm.city_id,\n", "               iil.outlet_id AS \"Outlet_id\",\n", "               o.name AS \"Store_Name\",\n", "               p.item_id AS \"Item_Id\",\n", "               iil.upc_id AS \"UPC_Id\",\n", "               p.name AS \"Product_Name\",\n", "               p.variant_mrp AS \"Variant_MRP\",\n", "               p.variant_uom_text AS \"Grammage\",\n", "               iil.pos_timestamp + INTERVAL '5' HOUR + INTERVAL '30' MINUTE AS pos_timestamp,\n", "               iiut.inventory_operation AS \"Operation\",\n", "               iil.\"delta\" AS \"Change_Quantity\",\n", "               iil.quantity AS \"Final_Quantity\",\n", "               iiut.name AS \"Inventory_Update_Type\",\n", "               iil.id AS inventory_update_id,\n", "               iil.created_by_name AS \"Created_By_Name\",\n", "               iil.created_by_phone AS \"Created_By_Phone\",\n", "               au.email AS \"Store_Manager_Email_id\",\n", "               iil.transaction_lp AS \"HOT_Landing_Price\"\n", "        FROM lake_view_ims.ims_inventory_log iil\n", "        INNER JOIN lake_rpc.product_product p ON iil.variant_id=p.variant_id\n", "        INNER JOIN lake_view_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "        INNER JOIN lake_view_retail.console_outlet o ON o.id=iil.outlet_id\n", "        INNER JOIN lake_view_retail.auth_user au ON iil.created_by=au.id\n", "        LEFT JOIN lake_view_vms.vms_vendor v ON v.id = iil.entity_vendor_id\n", "        LEFT JOIN lake_view_vms.vms_vendor_city_mapping vcm ON vcm.vendor_id = iil.entity_vendor_id\n", "        AND vcm.active = 1\n", "        LEFT JOIN lake_view_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = vcm.id\n", "        AND cti.active = 1\n", "        WHERE iil.inventory_update_type_id IN (14,\n", "                                               15,\n", "                                               39,\n", "                                               40,\n", "                                               41,\n", "                                               42,\n", "                                               43,\n", "                                               44,\n", "                                               45,\n", "                                               46,\n", "                                               47,\n", "                                               48)\n", "          --AND cast(cast(iil.pos_timestamp AS TIMESTAMP) + interval '5' HOUR + interval '30' MINUTE AS date) = cast('{yesterday}' AS date)\n", "        AND iil.insert_ds_ist = '{yesterday}'\n", "        ORDER BY iil.pos_timestamp DESC\"\"\"\n", "\n", "    manual_update_report = pd.read_sql(query, presto)\n", "    manual_update_report[\"etl_timestamp\"] = datetime.now(ist)\n", "\n", "    manual_update_report[\"pos_timestamp\"] = pd.to_datetime(\n", "        manual_update_report[\"pos_timestamp\"]\n", "    )\n", "\n", "    assert len(manual_update_report[[\"inventory_update_id\"]].drop_duplicates()) == len(\n", "        manual_update_report\n", "    )\n", "    assert not manual_update_report[\"entity_vendor_id\"].isnull().values.any()\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"entity_vendor_id\", \"type\": \"int\", \"description\": \"entity_vendor_id\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"vendor_name\"},\n", "        {\"name\": \"cin\", \"type\": \"varchar\", \"description\": \"cin\"},\n", "        {\"name\": \"gst_tin\", \"type\": \"varchar\", \"description\": \"gst_tin\"},\n", "        {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"legal_name\"},\n", "        {\"name\": \"city_id\", \"type\": \"int\", \"description\": \"city_id\"},\n", "        {\"name\": \"Outlet_id\", \"type\": \"int\", \"description\": \"Outlet_id\"},\n", "        {\"name\": \"Store_Name\", \"type\": \"varchar\", \"description\": \"Store_Name\"},\n", "        {\"name\": \"Item_Id\", \"type\": \"int\", \"description\": \"Item_Id\"},\n", "        {\"name\": \"UPC_Id\", \"type\": \"varchar\", \"description\": \"UPC_Id\"},\n", "        {\"name\": \"Product_Name\", \"type\": \"varchar\", \"description\": \"Product_Name\"},\n", "        {\"name\": \"Variant_MRP\", \"type\": \"float\", \"description\": \"Variant_MRP\"},\n", "        {\"name\": \"Grammage\", \"type\": \"varchar\", \"description\": \"Grammage\"},\n", "        {\"name\": \"pos_timestamp\", \"type\": \"datetime\", \"description\": \"pos_timestamp\"},\n", "        {\"name\": \"Operation\", \"type\": \"varchar\", \"description\": \"Operation\"},\n", "        {\"name\": \"Change_Quantity\", \"type\": \"int\", \"description\": \"Change_Quantity\"},\n", "        {\"name\": \"Final_Quantity\", \"type\": \"int\", \"description\": \"Final_Quantity\"},\n", "        {\n", "            \"name\": \"Inventory_Update_Type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Inventory_Update_Type\",\n", "        },\n", "        {\n", "            \"name\": \"inventory_update_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"inventory_update_id\",\n", "        },\n", "        {\n", "            \"name\": \"Created_By_Name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Created_By_Name\",\n", "        },\n", "        {\n", "            \"name\": \"Created_By_Phone\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Created_By_Phone\",\n", "        },\n", "        {\n", "            \"name\": \"Store_Manager_Email_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Store_Manager_Email_id\",\n", "        },\n", "        {\n", "            \"name\": \"HOT_Landing_Price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"HOT_Landing_Price\",\n", "        },\n", "        {\"name\": \"etl_timestamp\", \"type\": \"datetime\", \"description\": \"etl_timestamp\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"finance_manual_update_report\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"inventory_update_id\", \"etl_timestamp\"],\n", "        \"sortkey\": \"pos_timestamp\",\n", "        \"incremental_key\": \"etl_timestamp\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Manual update report for the multi entity construct\",  # Description of the table being sent to redshift\n", "    }\n", "    pb.to_redshift(manual_update_report, **kwargs)\n", "    print(f\"done for {yesterday}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}