{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta, date\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for a in range(1, 30, 1):\n", "for a in range(0, 1, 1):\n", "    yesterday = date.today() - <PERSON><PERSON><PERSON>(days=a + 1)\n", "    print(yesterday)\n", "\n", "    query = f\"\"\"\n", "    SELECT i3.id AS \"Stock_Update_ID\",\n", "           pur_ord.po_number AS \"Purchase_Number\",\n", "           (CASE\n", "                WHEN iii.source_type = 2 THEN 'ESTO Stock-in'\n", "                WHEN iii.source_type = 3 THEN 'Customer returened without invoice'\n", "                WHEN iii.source_type = 1\n", "                     AND iii.valid_po=1 THEN 'Valid PO'\n", "                ELSE 'In-Valid PO'\n", "            END) AS \"PO_Type\",\n", "           pur_ord.vendor_gst AS \"Vendor_GST\",\n", "           lo2.name AS \"Vendor_Supply_Location\",\n", "           date(pur_ord.issue_date + interval '5.5 hrs') AS \"Issue_Date\",\n", "           date(i3.manufacture_date + interval '5.5 hrs') AS \"Manufacture_Date\",\n", "           r.item_id AS \"Item_ID\",\n", "           i.upc_id AS \"UPC_ID\",\n", "           i.variant_id AS \"Variant_ID\",\n", "           r.name AS \"Product_Name\",\n", "           (CASE\n", "                WHEN r.outlet_type = 1 THEN 'F&V type'\n", "                WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "                ELSE 'Not Assigned'\n", "            END) AS \"Item_Type\",\n", "           man.name AS \"Manufacturer_Name\",\n", "           r.is_ptr AS \"PTR_Flag\",\n", "           i.\"delta\" AS Quantity,\n", "           i3.return_quantity,\n", "           (i.\"delta\"-i3.return_quantity) AS \"Actual_GRN_Quantity\",\n", "           r.variant_mrp AS MRP,\n", "           r.variant_uom_text AS Grammage,\n", "           ro.id as outlet_id,\n", "           i.entity_vendor_id,\n", "           cti.cin,\n", "           cti.gst_tin,\n", "           cti.legal_name,\n", "           -- ro.name AS \"Outlet_Name\",\n", "           v.vendor_name AS entity_vendor_name,\n", "           lo.name AS \"City_Name\",\n", "           date(i.pos_timestamp + interval '5.5 hrs') AS \"Stock_In_Date\",\n", "           i.created_at + interval '5.5 hrs' AS \"created_at\",\n", "           i2.name AS \"Update_Type\",\n", "           i3.expiry_date AS \"Expiry_Date\",\n", "           iii.vendor_id,\n", "           -- rcm.name AS \"Merchant_Name\",\n", "           (CASE\n", "                 WHEN iii.source_type = 1 THEN rcm.name\n", "                 ELSE src_vendor.vendor_name\n", "            END) AS \"Merchant_Name\",\n", "           i.merchant_invoice_id AS \"Merchant_Invoice_ID\",\n", "           i.created_by_name AS \"Created_By\",\n", "           i3.cost_price AS \"Cost_Price\",\n", "           cgst_tax.tax_percentage AS \"CGST(%%)\",\n", "           sgst_tax.tax_percentage AS \"SGST(%%)\",\n", "           igst_tax.tax_percentage AS \"IGST(%%)\",\n", "           cess_tax.tax_percentage AS \"CESS(%%)\",\n", "           add_cess.tax_percentage AS \"additional_cess(%%)\",\n", "           i3.landing_price AS \"Landing_Price\",\n", "           i3.po_landing_price AS \"PO_Landing_Price\",\n", "           i3.purchase_order_id AS \"Purchase_Order_ID\",\n", "           i3.grn_id AS \"GRN_ID\",\n", "           i3.landing_price * i.\"delta\" AS \"GRN_Amount\",\n", "           (i3.cost_price*(i.\"delta\"-i3.return_quantity)) AS \"Net_Cost_Price\",\n", "           (i3.landing_price*(i.\"delta\"-i3.return_quantity)) AS \"Net_Landing_Price\",\n", "           (i3.po_landing_price*(i.\"delta\"-i3.return_quantity)) AS \"Net_PO_landing_price\",\n", "           i3.cost_price * i.\"delta\" AS \"Total_Cost_Price\",\n", "           ((i3.landing_price * i.\"delta\")-(i3.cost_price * i.\"delta\")) AS \"Tax\",\n", "           r.is_pl\n", "    FROM lake_ims.ims_inventory_log i\n", "    INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "    LEFT JOIN lake_rpc.product_brand br ON br.id=r.brand_id\n", "    LEFT JOIN lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "    INNER JOIN lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "    INNER JOIN lake_retail.console_location lo ON ro.tax_location_id=lo.id\n", "    INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "    INNER JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "    INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "    INNER JOIN lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "    LEFT JOIN lake_ims.ims_inventory_stock_tax cgst_tax ON i3.id = cgst_tax.inventory_stock_id\n", "    AND cgst_tax.tax_type_id = 3\n", "    LEFT JOIN lake_ims.ims_inventory_stock_tax sgst_tax ON i3.id = sgst_tax.inventory_stock_id\n", "    AND sgst_tax.tax_type_id = 4\n", "    LEFT JOIN lake_ims.ims_inventory_stock_tax igst_tax ON i3.id = igst_tax.inventory_stock_id\n", "    AND igst_tax.tax_type_id = 5\n", "    LEFT JOIN lake_ims.ims_inventory_stock_tax cess_tax ON i3.id = cess_tax.inventory_stock_id\n", "    AND cess_tax.tax_type_id = 6\n", "    LEFT JOIN lake_ims.ims_inventory_stock_tax add_cess ON i3.id = add_cess.inventory_stock_id\n", "    AND add_cess.tax_type_id = 7\n", "    LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "    LEFT JOIN lake_pos.pos_invoice pi ON pi.invoice_id=i.merchant_invoice_id\n", "    LEFT JOIN lake_retail.console_location lo2 ON lo2.id=pur_ord.vendor_shipping_city_id\n", "    LEFT JOIN lake_vms.vms_vendor_city_mapping vcm ON vcm.vendor_id = i.entity_vendor_id\n", "    AND vcm.active = 1\n", "    LEFT JOIN lake_vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = vcm.id\n", "    AND cti.active = 1\n", "    LEFT JOIN lake_vms.vms_vendor v ON v.id = i.entity_vendor_id\n", "    left join lake_vms.vms_vendor src_vendor on src_vendor.id = iii.vendor_id\n", "    WHERE i.inventory_update_type_id IN (1,\n", "                                         28,\n", "                                         76,\n", "                                         90,\n", "                                         93)\n", "      AND date(convert_timezone('Asia/Kolkata', i.pos_timestamp)) = '{yesterday}'\n", "      AND i.\"delta\" > 0;\n", "    \"\"\"\n", "\n", "    stock_in_report = pd.read_sql(query, redshift)\n", "    stock_in_report[\"etl_timestamp\"] = datetime.now(ist)\n", "\n", "    stock_in_report = stock_in_report[\n", "        [\n", "            \"stock_update_id\",\n", "            \"purchase_number\",\n", "            \"po_type\",\n", "            \"vendor_gst\",\n", "            \"vendor_supply_location\",\n", "            \"issue_date\",\n", "            \"manufacture_date\",\n", "            \"item_id\",\n", "            \"upc_id\",\n", "            \"variant_id\",\n", "            \"product_name\",\n", "            \"item_type\",\n", "            \"manufacturer_name\",\n", "            \"ptr_flag\",\n", "            \"quantity\",\n", "            \"return_quantity\",\n", "            \"actual_grn_quantity\",\n", "            \"mrp\",\n", "            \"grammage\",\n", "            \"outlet_id\",\n", "            \"entity_vendor_id\",\n", "            \"cin\",\n", "            \"gst_tin\",\n", "            \"legal_name\",\n", "            \"entity_vendor_name\",\n", "            \"city_name\",\n", "            \"stock_in_date\",\n", "            \"created_at\",\n", "            \"update_type\",\n", "            \"expiry_date\",\n", "            \"vendor_id\",\n", "            \"merchant_name\",\n", "            \"merchant_invoice_id\",\n", "            \"created_by\",\n", "            \"cost_price\",\n", "            \"cgst(%)\",\n", "            \"sgst(%)\",\n", "            \"igst(%)\",\n", "            \"cess(%)\",\n", "            \"landing_price\",\n", "            \"po_landing_price\",\n", "            \"purchase_order_id\",\n", "            \"grn_id\",\n", "            \"grn_amount\",\n", "            \"net_cost_price\",\n", "            \"net_landing_price\",\n", "            \"net_po_landing_price\",\n", "            \"total_cost_price\",\n", "            \"tax\",\n", "            \"is_pl\",\n", "            \"etl_timestamp\",\n", "            \"additional_cess(%)\",\n", "        ]\n", "    ]\n", "\n", "    assert len(stock_in_report[[\"stock_update_id\"]].drop_duplicates()) == len(\n", "        stock_in_report\n", "    )\n", "    assert not stock_in_report[\"vendor_id\"].isnull().values.any()\n", "    assert not stock_in_report[\"entity_vendor_id\"].isnull().values.any()\n", "    print(f\"done for {yesterday}\")\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"stock_update_id\", \"type\": \"int\", \"description\": \"stock_update_id\"},\n", "        {\n", "            \"name\": \"purchase_number\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"purchase_number\",\n", "        },\n", "        {\"name\": \"po_type\", \"type\": \"varchar\", \"description\": \"po_type\"},\n", "        {\"name\": \"vendor_gst\", \"type\": \"varchar\", \"description\": \"vendor_gst\"},\n", "        {\n", "            \"name\": \"vendor_supply_location\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"vendor_supply_location\",\n", "        },\n", "        {\"name\": \"issue_date\", \"type\": \"varchar\", \"description\": \"issue_date\"},\n", "        {\n", "            \"name\": \"manufacture_date\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"manufacture_date\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "        {\"name\": \"upc_id\", \"type\": \"varchar\", \"description\": \"upc_id\"},\n", "        {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "        {\"name\": \"product_name\", \"type\": \"varchar\", \"description\": \"product_name\"},\n", "        {\"name\": \"item_type\", \"type\": \"varchar\", \"description\": \"item_type\"},\n", "        {\n", "            \"name\": \"manufacturer_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"manufacturer_name\",\n", "        },\n", "        {\"name\": \"ptr_flag\", \"type\": \"int\", \"description\": \"ptr_flag\"},\n", "        {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"quantity\"},\n", "        {\"name\": \"return_quantity\", \"type\": \"int\", \"description\": \"return_quantity\"},\n", "        {\n", "            \"name\": \"actual_grn_quantity\",\n", "            \"type\": \"int\",\n", "            \"description\": \"actual_grn_quantity\",\n", "        },\n", "        {\"name\": \"mrp\", \"type\": \"float\", \"description\": \"mrp\"},\n", "        {\"name\": \"grammage\", \"type\": \"varchar\", \"description\": \"grammage\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"entity_vendor_id\", \"type\": \"int\", \"description\": \"entity_vendor_id\"},\n", "        {\"name\": \"cin\", \"type\": \"varchar\", \"description\": \"cin\"},\n", "        {\"name\": \"gst_tin\", \"type\": \"varchar\", \"description\": \"gst_tin\"},\n", "        {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"legal_name\"},\n", "        {\n", "            \"name\": \"entity_vendor_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"entity_vendor_name\",\n", "        },\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "        {\"name\": \"stock_in_date\", \"type\": \"varchar\", \"description\": \"stock_in_date\"},\n", "        {\"name\": \"created_at\", \"type\": \"datetime\", \"description\": \"created_at\"},\n", "        {\"name\": \"update_type\", \"type\": \"varchar\", \"description\": \"update_type\"},\n", "        {\"name\": \"expiry_date\", \"type\": \"varchar\", \"description\": \"expiry_date\"},\n", "        {\"name\": \"vendor_id\", \"type\": \"int\", \"description\": \"vendor_id\"},\n", "        {\"name\": \"merchant_name\", \"type\": \"varchar\", \"description\": \"merchant_name\"},\n", "        {\n", "            \"name\": \"merchant_invoice_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant_invoice_id\",\n", "        },\n", "        {\"name\": \"created_by\", \"type\": \"varchar\", \"description\": \"created_by\"},\n", "        {\"name\": \"cost_price\", \"type\": \"float\", \"description\": \"cost_price\"},\n", "        {\"name\": \"cgst(%)\", \"type\": \"float\", \"description\": \"cgst(%)\"},\n", "        {\"name\": \"sgst(%)\", \"type\": \"float\", \"description\": \"sgst(%)\"},\n", "        {\"name\": \"igst(%)\", \"type\": \"float\", \"description\": \"igst(%)\"},\n", "        {\"name\": \"cess(%)\", \"type\": \"float\", \"description\": \"cess(%)\"},\n", "        {\"name\": \"landing_price\", \"type\": \"float\", \"description\": \"landing_price\"},\n", "        {\n", "            \"name\": \"po_landing_price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"po_landing_price\",\n", "        },\n", "        {\n", "            \"name\": \"purchase_order_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"purchase_order_id\",\n", "        },\n", "        {\"name\": \"grn_id\", \"type\": \"varchar\", \"description\": \"grn_id\"},\n", "        {\"name\": \"grn_amount\", \"type\": \"float\", \"description\": \"grn_amount\"},\n", "        {\"name\": \"net_cost_price\", \"type\": \"float\", \"description\": \"net_cost_price\"},\n", "        {\n", "            \"name\": \"net_landing_price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"net_landing_price\",\n", "        },\n", "        {\n", "            \"name\": \"net_po_landing_price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"net_po_landing_price\",\n", "        },\n", "        {\n", "            \"name\": \"total_cost_price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total_cost_price\",\n", "        },\n", "        {\"name\": \"tax\", \"type\": \"float\", \"description\": \"tax\"},\n", "        {\"name\": \"is_pl\", \"type\": \"int\", \"description\": \"is_pl\"},\n", "        {\"name\": \"etl_timestamp\", \"type\": \"datetime\", \"description\": \"etl_timestamp\"},\n", "        {\n", "            \"name\": \"additional_cess(%)\",\n", "            \"type\": \"float\",\n", "            \"description\": \"additional_cess(%)\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"finance_stock_in_report\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"stock_update_id\", \"etl_timestamp\"],\n", "        \"sortkey\": \"created_at\",\n", "        \"incremental_key\": \"etl_timestamp\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stock in report for the multi entity construct\",  # Description of the table being sent to redshift\n", "    }\n", "    pb.to_redshift(stock_in_report, **kwargs)\n", "    print(f\"done for {yesterday}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}