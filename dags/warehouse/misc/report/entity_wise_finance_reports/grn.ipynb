{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn = pd.read_sql_query(\n", "    \"\"\"\n", "with inward as\n", "  (select *\n", "   from lake_view_ims.ims_inward_invoice\n", "   where insert_ds_ist = cast(current_date - interval '1' day as varchar))\n", "\n", "SELECT t1.*,\n", "       t2.tax_collected_at_source AS \"tcs_amount\",\n", "       t2.sum_amount AS \"total_invoice_amount\",\n", "       t2.total_invoice_value AS \"total_vendor_invoice_value\"\n", "FROM\n", "  (SELECT inward.grn_id,\n", "          -- inward.vendor_id,\n", "          -- pi.source_entity_vendor_id,\n", "          (CASE\n", "               WHEN inward.source_type in (2,4) THEN pi.source_entity_vendor_id\n", "               ELSE inward.vendor_id\n", "           END) as vendor_id,\n", "          inward.entity_vendor_id,\n", "          cti.cin,\n", "          cti.gst_tin,\n", "          cti.legal_name,\n", "          inward.net_amount,\n", "          inward.invoice_discount,\n", "          inward.freight_charges,\n", "          inward.po_id AS \"po_number\",\n", "          inward.vendor_invoice_id AS \"merchant_invoice_id\",\n", "          -- rcm.name AS \"Merchant_Name\",\n", "          (CASE\n", "               WHEN inward.source_type in (2,4) THEN src_vendor.vendor_name\n", "               ELSE rcm.name\n", "           END) AS \"merchant_name\",\n", "          (CASE\n", "               WHEN inward.valid_po = 1 THEN 'True'\n", "               ELSE 'False'\n", "           END) AS \"valid_po\",\n", "          (CASE\n", "               WHEN inward.source_type = 1 THEN 'External Vendor'\n", "               WHEN inward.source_type = 2 THEN 'ESTO'\n", "               WHEN inward.source_type = 3 THEN 'Dummy'\n", "               WHEN inward.source_type = 4 THEN 'RTS'\n", "           END) AS \"source_type\",\n", "          ro.id AS \"outlet_id\",\n", "          -- ro.name AS \"Outlet_Name\",\n", "          v.vendor_name as entity_vendor_name,\n", "          inward.created_at + interval '5' hour + interval '30' minute as created_at\n", "   FROM inward\n", "   INNER JOIN lake_retail.console_outlet ro ON ro.id = inward.outlet_id\n", "   INNER JOIN lake_retail.console_merchant rcm ON rcm.id = inward.vendor_id\n", "   left join lake_vms.vms_vendor_city_mapping cm on cm.vendor_id = inward.entity_vendor_id and cm.active = 1\n", "   left join lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = cm.id and cti.active = 1\n", "   left join lake_vms.vms_vendor v on v.id = inward.entity_vendor_id\n", "   left join lake_pos.pos_invoice pi on pi.invoice_id = inward.vendor_invoice_id\n", "   left join lake_vms.vms_vendor src_vendor on src_vendor.id =  pi.source_entity_vendor_id) t1\n", "INNER JOIN\n", "  (SELECT grn_id,\n", "          vendor_invoice_id,\n", "          tax_collected_at_source,\n", "          sum(net_amount) \"SUM_AMOUNT\",\n", "          total_invoice_value\n", "   FROM inward\n", "   GROUP BY grn_id,\n", "            vendor_invoice_id,\n", "            tax_collected_at_source,\n", "            total_invoice_value) t2 ON t1.merchant_invoice_id = t2.vendor_invoice_id \n", "and t1.grn_id = t2.grn_id\n", "\"\"\",\n", "    presto,\n", ")\n", "\n", "grn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn[\"etl_timestamp\"] = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn['entity_vendor_id'] = grn['entity_vendor_id'].astype('Int64')\n", "# grn['vendor_id'] = grn['vendor_id'].astype('Int64')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols = [\n", "    \"net_amount\",\n", "    \"invoice_discount\",\n", "    \"freight_charges\",\n", "    \"tcs_amount\",\n", "    \"total_invoice_amount\",\n", "    \"total_vendor_invoice_value\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn[cols] = grn[cols].astype(float)\n", "grn[\"created_at\"] = pd.to_datetime(grn[\"created_at\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert len(grn[[\"grn_id\"]].drop_duplicates()) == len(grn)\n", "assert not grn[[\"entity_vendor_id\", \"vendor_id\"]].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn[grn['cin'] == 'U74110DL2013PTC248660'].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"grn_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"grn id\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"vendor id from vms vendor\",\n", "    },\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"corporate identification number\",\n", "    },\n", "    {\n", "        \"name\": \"gst_tin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"gst tin\",\n", "    },\n", "    {\n", "        \"name\": \"legal_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"legal name\",\n", "    },\n", "    {\n", "        \"name\": \"net_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net amount\",\n", "    },\n", "    {\n", "        \"name\": \"invoice_discount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"invoice discount\",\n", "    },\n", "    {\n", "        \"name\": \"freight_charges\",\n", "        \"type\": \"float\",\n", "        \"description\": \"freight charges\",\n", "    },\n", "    {\n", "        \"name\": \"po_number\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"po id\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_invoice_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"vendor invoice id\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"merchant name\",\n", "    },\n", "    {\n", "        \"name\": \"valid_po\",\n", "        \"type\": \"bool\",\n", "        \"description\": \"valid po\",\n", "    },\n", "    {\n", "        \"name\": \"source_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"source type\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"entity vendor name\",\n", "    },\n", "    {\n", "        \"name\": \"created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"inward invoice created at timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"tcs_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"tax collected at source\",\n", "    },\n", "    {\n", "        \"name\": \"total_invoice_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total invoice amount\",\n", "    },\n", "    {\n", "        \"name\": \"total_vendor_invoice_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total vendor invoice value\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"row entry timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"finance_grn_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"grn_id\", \"etl_timestamp\"],\n", "    \"sortkey\": [\"created_at\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores grn related data for finance\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(grn, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}