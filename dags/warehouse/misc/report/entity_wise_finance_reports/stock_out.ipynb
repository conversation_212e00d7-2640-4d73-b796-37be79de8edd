{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime, date, timedelta\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yesterday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_presto = f\"\"\"SELECT pi.outlet_id,\n", "       v.vendor_name as source_entity_vendor_name,\n", "       pi.source_entity_vendor_id,\n", "       cti.cin,\n", "       cti.gst_tin,\n", "       cti.legal_name,\n", "       cs.name source_state,\n", "       sto.destination_entity_vendor_id,\n", "       pi.customer_name AS \"merchant_name\",\n", "       cti2.legal_name destination_legal_name,\n", "       cs2.name destination_state,\n", "       pi.invoice_id,\n", "       pp.item_id,\n", "       pp.upc,\n", "       pp.variant_id,\n", "       pp.name AS \"product_name\",\n", "       (CASE\n", "            WHEN pp.outlet_type = 1 THEN 'F&V type'\n", "            WHEN pp.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"item_type\",\n", "       pp.hsn_code,\n", "       pp.variant_mrp AS \"mrp\",\n", "       pp.variant_uom_text AS \"grammage\",\n", "       pd.quantity AS \"quantity\",\n", "       coalesce(pd.igst_per,0) AS \"igst_per\",\n", "       coalesce(pd.cgst_per,0) AS \"cgst_per\",\n", "       coalesce(pd.sgst_per,0) AS \"sgst_per\",\n", "       coalesce(pd.cess_per,0) AS \"cess_per\",\n", "       coalesce(pd.igst_value,0) AS \"igst_value\",\n", "       coalesce(pd.cgst_value,0) AS \"cgst_value\",\n", "       coalesce(pd.sgst_value,0) AS \"sgst_value\",\n", "       coalesce(pd.cess_value,0) AS \"cess_value\",\n", "       pd.quantity * coalesce(pd.additional_cess_value,0) AS \"additional_cess_value\",\n", "       round(pd.quantity*pd.selling_price,2)-(coalesce(pd.igst_value,0)+coalesce(pd.cgst_value,0)+coalesce(pd.sgst_value,0)+coalesce(pd.cess_value,0) + (pd.quantity * coalesce(pd.additional_cess_value,0))) AS \"total_basic_amount\",\n", "       (pi.pos_timestamp + INTERVAL '5' HOUR + INTERVAL '30' MINUTE) AS \"pos_timestamp\",\n", "       pd.selling_price AS \"cloud_landing_price\",\n", "       lp.landing_price,\n", "       round(pd.quantity*pd.selling_price,2) AS \"net_worth\"\n", "FROM (select * from lake_view_pos.pos_invoice where invoice_type_id IN (5,14,16) and insert_ds_ist = '{yesterday}') pi\n", "left join lake_view_vms.vms_vendor_city_mapping vcm on vcm.vendor_id = pi.source_entity_vendor_id and vcm.active = 1\n", "left join lake_view_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id and cti.active = 1\n", "left join lake_view_vms.vms_vendor v on v.id = pi.source_entity_vendor_id\n", "left join (select distinct id, source_entity_vendor_id, destination_entity_vendor_id from lake_view_po.sto) sto on cast(sto.id as int) = cast(pi.grofers_order_id as int)\n", "inner join (select * from lake_view_pos.pos_invoice_product_details where insert_ds_ist = '{yesterday}') pd ON pi.id = pd.invoice_id\n", "inner join lake_view_rpc.product_product pp ON pp.variant_id = pd.variant_id\n", "inner join lake_view_retail.console_outlet o ON o.id = pi.outlet_id\n", "inner join lake_view_retail.console_merchant cm ON cm.id = pi.merchant_id\n", "inner join lake_view_retail.console_outlet merchant_outlet ON cm.outlet_id = merchant_outlet.id\n", "-- inner join lake_view_retail.console_company_type com_type ON com_type.id = merchant_outlet.company_type_id AND com_type.entity_type != 'THIRD_PARTY'\n", "left join lake_view_pos.pos_invoice_product_lp lp on lp.invoice_id = pi.id and lp.variant_id = pd.variant_id\n", "inner join lake_retail.console_location cl on cl.id = o.tax_location_id\n", "inner join lake_retail.console_location cl2 on cl2.id = merchant_outlet.tax_location_id\n", "inner join lake_retail.console_state cs on cs.id = cl.state_id\n", "inner join lake_retail.console_state cs2 on cs2.id = cl2.state_id\n", "left join lake_view_vms.vms_vendor_city_mapping vcm2 on vcm2.vendor_id = sto.destination_entity_vendor_id and vcm2.active = 1\n", "left join lake_view_vms.vms_vendor_city_tax_info cti2 on cti2.vendor_city_id = vcm2.id and cti2.active = 1\n", "\"\"\"\n", "esto_presto = pd.read_sql_query(sql_presto, presto)\n", "esto_presto.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["esto_presto.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["esto_presto[\"quantity\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["esto_presto[\"etl_timestamp\"] = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert len(esto_presto[[\"invoice_id\", \"variant_id\"]].drop_duplicates()) == len(\n", "    esto_presto\n", ")\n", "assert (\n", "    not esto_presto[[\"source_entity_vendor_id\", \"destination_entity_vendor_id\"]]\n", "    .isnull()\n", "    .values.any()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["esto_presto = esto_presto[\n", "    [\n", "        \"outlet_id\",\n", "        \"source_entity_vendor_name\",\n", "        \"source_entity_vendor_id\",\n", "        \"cin\",\n", "        \"gst_tin\",\n", "        \"legal_name\",\n", "        \"destination_entity_vendor_id\",\n", "        \"merchant_name\",\n", "        \"invoice_id\",\n", "        \"item_id\",\n", "        \"upc\",\n", "        \"variant_id\",\n", "        \"product_name\",\n", "        \"item_type\",\n", "        \"hsn_code\",\n", "        \"mrp\",\n", "        \"grammage\",\n", "        \"quantity\",\n", "        \"igst_per\",\n", "        \"cgst_per\",\n", "        \"sgst_per\",\n", "        \"cess_per\",\n", "        \"igst_value\",\n", "        \"cgst_value\",\n", "        \"sgst_value\",\n", "        \"cess_value\",\n", "        \"total_basic_amount\",\n", "        \"pos_timestamp\",\n", "        \"cloud_landing_price\",\n", "        \"landing_price\",\n", "        \"net_worth\",\n", "        \"etl_timestamp\",\n", "        \"additional_cess_value\",\n", "        \"source_state\",\n", "        \"destination_legal_name\",\n", "        \"destination_state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"source entity vendor name\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"source entity vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"cin\",\n", "    },\n", "    {\n", "        \"name\": \"gst_tin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"gst tin\",\n", "    },\n", "    {\n", "        \"name\": \"legal_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"legal name\",\n", "    },\n", "    {\n", "        \"name\": \"destination_entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"destination entity vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"merchant name\",\n", "    },\n", "    {\n", "        \"name\": \"invoice_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"invoice id\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item id\",\n", "    },\n", "    {\n", "        \"name\": \"upc\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"upc\",\n", "    },\n", "    {\n", "        \"name\": \"variant_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"variant id\",\n", "    },\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"product name\",\n", "    },\n", "    {\n", "        \"name\": \"item_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item type\",\n", "    },\n", "    {\n", "        \"name\": \"hsn_code\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"hsn code\",\n", "    },\n", "    {\n", "        \"name\": \"mrp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"mrp\",\n", "    },\n", "    {\n", "        \"name\": \"grammage\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"grammage\",\n", "    },\n", "    {\n", "        \"name\": \"quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"quantity\",\n", "    },\n", "    {\n", "        \"name\": \"igst_per\",\n", "        \"type\": \"float\",\n", "        \"description\": \"igst(%)\",\n", "    },\n", "    {\n", "        \"name\": \"cgst_per\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cgst(%)\",\n", "    },\n", "    {\n", "        \"name\": \"sgst_per\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sgst(%)\",\n", "    },\n", "    {\n", "        \"name\": \"cess_per\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cess(%)\",\n", "    },\n", "    {\n", "        \"name\": \"igst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"igst value\",\n", "    },\n", "    {\n", "        \"name\": \"cgst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cgst value\",\n", "    },\n", "    {\n", "        \"name\": \"sgst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sgst value\",\n", "    },\n", "    {\n", "        \"name\": \"cess_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cess value\",\n", "    },\n", "    {\n", "        \"name\": \"total_basic_amount\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total basic amount\",\n", "    },\n", "    {\n", "        \"name\": \"pos_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"pos timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"cloud_landing_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cloud landing price\",\n", "    },\n", "    {\n", "        \"name\": \"landing_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"landing price\",\n", "    },\n", "    {\n", "        \"name\": \"net_worth\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net worth\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"row entry timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"additional_cess_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"additional cess value\",\n", "    },\n", "    {\n", "        \"name\": \"source_state\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"seller state\",\n", "    },\n", "    {\n", "        \"name\": \"destination_legal_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"buyer legal name\",\n", "    },\n", "    {\n", "        \"name\": \"destination_state\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"buyer state\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"finance_stock_out_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"invoice_id\", \"variant_id\", \"etl_timestamp\"],\n", "    \"sortkey\": [\"pos_timestamp\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores Excess Stock Transfer related data for finance\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(esto_presto, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}