{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for a in range(0, 1, 1):\n", "    yesterday = date.today() - <PERSON><PERSON><PERSON>(days=a + 1)\n", "    print(yesterday)\n", "\n", "    query = f\"\"\"\n", "       SELECT p.invoice_id,\n", "       p.grofers_order_id AS order_id,\n", "       p.pos_timestamp + interval '5' hour + interval '30' minute as pos_timestamp,\n", "       p.outlet_id,\n", "       v.vendor_name as entity_vendor_name,\n", "       p.source_entity_vendor_id,\n", "       cti.cin,\n", "       cti.gst_tin,\n", "       cti.legal_name,\n", "       p.customer_name,\n", "       l1.name AS supply_city,\n", "       l2.name AS customer_city,\n", "       rp.item_id,\n", "       pi.variant_id,\n", "       pi.id,\n", "       rp.name AS product_name,\n", "       (CASE\n", "            WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "            WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS item_type,\n", "       rp.hsn_code,\n", "       rp.is_pl,\n", "       rp.variant_uom_text AS variant_description,\n", "       (CASE\n", "           WHEN pi.selling_price-(coalesce(pi.\"vat\",0)/pi.quantity) != 0 THEN Round((100*(coalesce(pi.\"vat\",0)/pi.quantity))/(pi.selling_price-(coalesce(pi.\"vat\",0)/pi.quantity)),2)\n", "           ELSE null\n", "       END) AS \"vat %% cal\",\n", "       (CASE\n", "           WHEN pi.selling_price-(coalesce(pi.\"cst\",0)/pi.quantity) != 0 THEN Round((100*(coalesce(pi.\"cst\",0)/pi.quantity))/(pi.selling_price-(coalesce(pi.\"cst\",0)/pi.quantity)),2)\n", "           ELSE null\n", "       END) AS \"cst %% cal\",\n", "       pi.igst_per,\n", "       pi.cgst_per,\n", "       pi.sgst_per,\n", "       pi.cess_per,\n", "       pi.quantity,\n", "       pi.unit_rate AS mrp,\n", "       (pi.unit_rate - pi.selling_price) AS discount,\n", "       pi.selling_price,\n", "       coalesce(pi.\"vat\",0) as vat,\n", "       coalesce(pi.\"cst\",0) as cst,\n", "       pi.igst_value,\n", "       pi.cgst_value,\n", "       pi.sgst_value,\n", "       pi.cess_value,\n", "       (coalesce(pi.cgst_value,0)+coalesce(pi.sgst_value,0)+coalesce(pi.igst_value,0)+coalesce(pi.cess_value,0) + (pi.quantity * coalesce(pi.additional_cess_value,0))) AS total_tax,\n", "         CASE\n", "             WHEN p.invoice_type_id = 1 THEN pi.selling_price*pi.quantity\n", "             ELSE 0\n", "         END AS total_gross_bill_amount,\n", "         CASE\n", "             WHEN p.invoice_type_id = 2 THEN pi.selling_price*pi.quantity\n", "             ELSE 0\n", "         END AS total_return_amount,\n", "         pi.quantity * pi.additional_cess_value additional_cess_value\n", "        from (select *\n", "              from lake_view_pos.pos_invoice\n", "              where insert_ds_ist = '{yesterday}'\n", "              and invoice_type_id IN (1,\n", "                                      2)) p\n", "        inner join (select *\n", "                    from lake_pos.pos_invoice_product_details\n", "                    where insert_ds_ist = '{yesterday}'\n", "                    and store_offer_id is NULL) pi on pi.invoice_id = p.id\n", "        left join lake_vms.vms_vendor_city_mapping cm on cm.vendor_id = p.source_entity_vendor_id and cm.active = 1\n", "        left join lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = cm.id and cti.active = 1\n", "        left join lake_vms.vms_vendor v on v.id = p.source_entity_vendor_id\n", "        INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "        INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "        INNER JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "        INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "    \"\"\"\n", "\n", "    sales = pd.DataFrame()\n", "    for chunk in pd.read_sql_query(query, presto, chunksize=50000):\n", "        sales = pd.concat([sales, chunk], axis=0)\n", "\n", "    sales[\"etl_timestamp\"] = datetime.now(ist)\n", "    sales[\"pos_timestamp\"] = pd.to_datetime(sales[\"pos_timestamp\"])\n", "    sales = sales[\n", "        [\n", "            \"invoice_id\",\n", "            \"order_id\",\n", "            \"pos_timestamp\",\n", "            \"outlet_id\",\n", "            \"entity_vendor_name\",\n", "            \"source_entity_vendor_id\",\n", "            \"cin\",\n", "            \"gst_tin\",\n", "            \"legal_name\",\n", "            \"customer_name\",\n", "            \"supply_city\",\n", "            \"customer_city\",\n", "            \"item_id\",\n", "            \"variant_id\",\n", "            \"id\",\n", "            \"product_name\",\n", "            \"item_type\",\n", "            \"hsn_code\",\n", "            \"is_pl\",\n", "            \"variant_description\",\n", "            \"vat % cal\",\n", "            \"cst % cal\",\n", "            \"igst_per\",\n", "            \"cgst_per\",\n", "            \"sgst_per\",\n", "            \"cess_per\",\n", "            \"quantity\",\n", "            \"mrp\",\n", "            \"discount\",\n", "            \"selling_price\",\n", "            \"vat\",\n", "            \"cst\",\n", "            \"igst_value\",\n", "            \"cgst_value\",\n", "            \"sgst_value\",\n", "            \"cess_value\",\n", "            \"total_tax\",\n", "            \"total_gross_bill_amount\",\n", "            \"total_return_amount\",\n", "            \"etl_timestamp\",\n", "            \"additional_cess_value\",\n", "        ]\n", "    ]\n", "\n", "    sales = sales.astype(\n", "        {\n", "            \"igst_per\": float,\n", "            \"cgst_per\": float,\n", "            \"sgst_per\": float,\n", "            \"cess_per\": float,\n", "            \"vat\": float,\n", "            \"cst\": float,\n", "            \"igst_value\": float,\n", "            \"cgst_value\": float,\n", "            \"sgst_value\": float,\n", "            \"cess_value\": float,\n", "            \"additional_cess_value\": float,\n", "            \"total_tax\": float,\n", "        }\n", "    )\n", "\n", "    assert len(sales[[\"id\"]].drop_duplicates()) == len(sales)\n", "    assert not sales[\"source_entity_vendor_id\"].isnull().values.any()\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"invoice_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"invoice id\",\n", "        },\n", "        {\n", "            \"name\": \"order_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"grofers order id\",\n", "        },\n", "        {\n", "            \"name\": \"pos_timestamp\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"pos timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"outlet_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"outlet id\",\n", "        },\n", "        {\n", "            \"name\": \"entity_vendor_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"entity vendor name\",\n", "        },\n", "        {\n", "            \"name\": \"source_entity_vendor_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"source entity vendor id\",\n", "        },\n", "        {\n", "            \"name\": \"cin\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cin\",\n", "        },\n", "        {\n", "            \"name\": \"gst_tin\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gst tin\",\n", "        },\n", "        {\n", "            \"name\": \"legal_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"legal name\",\n", "        },\n", "        {\n", "            \"name\": \"customer_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"customer name\",\n", "        },\n", "        {\n", "            \"name\": \"supply_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"supply city\",\n", "        },\n", "        {\n", "            \"name\": \"customer_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"customer city\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"item id\",\n", "        },\n", "        {\n", "            \"name\": \"variant_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"variant id\",\n", "        },\n", "        {\n", "            \"name\": \"id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"pipd id\",\n", "        },\n", "        {\n", "            \"name\": \"product_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"product name\",\n", "        },\n", "        {\n", "            \"name\": \"item_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"item type\",\n", "        },\n", "        {\n", "            \"name\": \"hsn_code\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"product hsn code\",\n", "        },\n", "        {\n", "            \"name\": \"is_pl\",\n", "            \"type\": \"bool\",\n", "            \"description\": \"pl flag\",\n", "        },\n", "        {\n", "            \"name\": \"variant_description\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"variant description\",\n", "        },\n", "        {\n", "            \"name\": \"vat % cal\",\n", "            \"type\": \"float\",\n", "            \"description\": \"vat % cal\",\n", "        },\n", "        {\n", "            \"name\": \"cst % cal\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cst % cal\",\n", "        },\n", "        {\n", "            \"name\": \"igst_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"igst(%)\",\n", "        },\n", "        {\n", "            \"name\": \"cgst_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cgst(%)\",\n", "        },\n", "        {\n", "            \"name\": \"sgst_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sgst(%)\",\n", "        },\n", "        {\n", "            \"name\": \"cess_per\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cess(%)\",\n", "        },\n", "        {\n", "            \"name\": \"quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"quantity\",\n", "        },\n", "        {\n", "            \"name\": \"mrp\",\n", "            \"type\": \"float\",\n", "            \"description\": \"mrp\",\n", "        },\n", "        {\n", "            \"name\": \"discount\",\n", "            \"type\": \"float\",\n", "            \"description\": \"discount\",\n", "        },\n", "        {\n", "            \"name\": \"selling_price\",\n", "            \"type\": \"float\",\n", "            \"description\": \"selling price\",\n", "        },\n", "        {\n", "            \"name\": \"vat\",\n", "            \"type\": \"float\",\n", "            \"description\": \"vat\",\n", "        },\n", "        {\n", "            \"name\": \"cst\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cst\",\n", "        },\n", "        {\n", "            \"name\": \"igst_value\",\n", "            \"type\": \"float\",\n", "            \"description\": \"igst value\",\n", "        },\n", "        {\n", "            \"name\": \"cgst_value\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cgst value\",\n", "        },\n", "        {\n", "            \"name\": \"sgst_value\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sgst value\",\n", "        },\n", "        {\n", "            \"name\": \"cess_value\",\n", "            \"type\": \"float\",\n", "            \"description\": \"cess value\",\n", "        },\n", "        {\n", "            \"name\": \"total_tax\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total tax\",\n", "        },\n", "        {\n", "            \"name\": \"total_gross_bill_amount\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total gross bill amount\",\n", "        },\n", "        {\n", "            \"name\": \"total_return_amount\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total return amount\",\n", "        },\n", "        {\n", "            \"name\": \"etl_timestamp\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"row entry timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"additional_cess_value\",\n", "            \"type\": \"float\",\n", "            \"description\": \"additional_cess_value\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"finance_sales_report\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"id\", \"etl_timestamp\"],\n", "        \"sortkey\": [\"pos_timestamp\"],\n", "        \"incremental_key\": \"etl_timestamp\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores sales by item id related data for finance\",  # Description of the table being sent to redshift\n", "    }\n", "\n", "    pb.to_redshift(sales, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales.head(2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}