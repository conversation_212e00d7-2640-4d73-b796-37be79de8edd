{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oevm = pd.read_sql_query(\n", "    \"\"\"\n", "select distinct entity_vendor_id, outlet_id\n", "from lake_retail.outlet_entity_vendor_mapping where active = 1 and lake_active_record = True\n", "\"\"\",\n", "    presto,\n", ")\n", "oevm.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["DL vendors can be mapped to multiple stores and will hold and bill medicinal handling type items"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["for a in range(0, 1, 1):\n", "    yesterday = date.today() - <PERSON><PERSON><PERSON>(days=a + 1)\n", "    print(yesterday)\n", "    yesterday = str(yesterday)\n", "    query = f\"\"\"SELECT outlet.id as outlet_id,\n", "           outlet.name as outlet_name,\n", "           ven.vendor_name,\n", "           EOD.entity_vendor_id,\n", "           cti.cin,\n", "           cti.gst_tin,\n", "           cti.legal_name,\n", "           prod.item_id,\n", "           prod.upc,\n", "           prod.variant_id,\n", "           (CASE\n", "                WHEN cast(prod.storage_type as integer) = 1 THEN 'Regular'\n", "                WHEN cast(prod.storage_type as integer) = 2 THEN 'Fridge'\n", "                WHEN cast(prod.storage_type as integer) = 3 THEN 'Freezer'\n", "                WHEN cast(prod.storage_type as integer) = 4 THEN 'Large'\n", "                WHEN cast(prod.storage_type as integer) = 5 THEN 'Heavy'\n", "                WHEN cast(prod.storage_type as integer) = 6 THEN 'Non_Veg_Cold'\n", "                WHEN cast(prod.storage_type as integer) = 7 THEN 'Non_Veg_Frozen'\n", "                WHEN cast(prod.storage_type as integer) = 8 THEN 'Fast_Moving'\n", "                ELSE 'Not Assigned'\n", "            END) AS \"Storage_Type\",\n", "           brand.name AS brand_name,\n", "           pm.name AS Manufacturer_name,\n", "           prod.name AS product_name,\n", "           (CASE\n", "                WHEN prod.outlet_type = 1 THEN 'F&V type'\n", "                WHEN prod.outlet_type = 2 THEN 'Grocery type'\n", "                ELSE 'Not Assigned'\n", "            END) AS \"Item_Type\",\n", "           prod.variant_uom_text AS \"Description\",\n", "           prod.active,\n", "           (EOD.snapshot_datetime + interval '5' hour + interval '30' minute) AS \"snapshot_datetime\",\n", "           EOD.on_shelf_quantity AS \"on_shelf_numbers\",\n", "           EOD.on_shelf_networh AS \"on_shelf_worth\",\n", "           EOD.on_shelf_quantity*prod.variant_mrp AS \"On_Shelf_MRPWorth\",\n", "           EOD.off_shelf_quantity AS \"off_shelf_quantity\",\n", "           EOD.off_shelf_networth AS \"off_shelf_worth\",\n", "           (EOD.on_shelf_quantity + EOD.off_shelf_quantity) AS \"total_quantity\",\n", "           (EOD.on_shelf_networh + EOD.off_shelf_networth) AS \"total_networth\",\n", "           prod.is_pl\n", "    FROM\n", "      (SELECT coalesce(good_inv.variant_id, snap_inv.variant_id) AS \"Variant_id\",\n", "              coalesce(good_inv.outlet_id, snap_inv.outlet_id) AS \"Outlet_id\",\n", "              coalesce(good_inv.entity_vendor_id, snap_inv.entity_vendor_id) AS \"entity_vendor_id\",\n", "              coalesce(good_inv.snapshot_datetime, snap_inv.snapshot_datetime) AS \"snapshot_datetime\",\n", "              coalesce(good_inv.quantity,0) AS \"off_shelf_quantity\",\n", "              coalesce(good_inv.networth,0) AS \"off_shelf_networth\",\n", "              coalesce(snap_inv.quantity,0) AS \"on_shelf_quantity\",\n", "              coalesce(snap_inv.net_worth,0) AS \"on_shelf_networh\"\n", "       FROM\n", "         (SELECT outlet_id,\n", "                 entity_vendor_id,\n", "                 variant_id,\n", "                 sum(quantity) AS quantity,\n", "                 sum(net_worth) AS networth,\n", "                 snapshot_datetime\n", "          FROM lake_view_reports.reports_entity_good_inventory_snapshot snap\n", "          INNER JOIN lake_retail.console_outlet co ON snap.outlet_id=co.id\n", "          WHERE insert_ds_ist = '{yesterday}'\n", "            AND co.active = 1\n", "            AND co.device_id <> 47\n", "            AND snap.active = True\n", "            AND snap.lake_active_record = true\n", "          GROUP BY outlet_id,\n", "                   entity_vendor_id,\n", "                   variant_id,\n", "                   snapshot_datetime) good_inv\n", "       FULL OUTER JOIN\n", "         (SELECT variant_id,\n", "                 quantity,\n", "                 net_worth,\n", "                 outlet_id,\n", "                 entity_vendor_id,\n", "                 snapshot_datetime\n", "          FROM lake_view_reports.reports_entity_inventory_snapshot inv_snap\n", "          INNER JOIN lake_retail.console_outlet co ON inv_snap.outlet_id=co.id\n", "          WHERE insert_ds_ist = '{yesterday}'\n", "            AND co.active = 1\n", "            AND co.device_id <> 47\n", "            AND inv_snap.active = True\n", "            AND inv_snap.lake_active_record = True) snap_inv ON good_inv.variant_id=snap_inv.variant_id\n", "                                                 AND good_inv.outlet_id=snap_inv.outlet_id\n", "                                                 AND good_inv.entity_vendor_id=snap_inv.entity_vendor_id) EOD\n", "     left join lake_vms.vms_vendor_city_mapping vcm on vcm.vendor_id = EOD.entity_vendor_id AND vcm.active = 1\n", "     left join lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = vcm.id AND cti.active = 1\n", "    INNER JOIN lake_rpc.product_product prod ON prod.variant_id=EOD.Variant_id\n", "    LEFT JOIN lake_rpc.product_brand brand ON brand.id=prod.brand_id\n", "    INNER JOIN lake_rpc.product_manufacturer pm ON pm.id=brand.manufacturer_id\n", "    INNER JOIN lake_retail.console_outlet outlet ON outlet.id=EOD.outlet_id\n", "    LEFT JOIN lake_vms.vms_vendor ven ON ven.id = EOD.entity_vendor_id\n", "    WHERE outlet.active = 1\n", "      AND (EOD.on_shelf_quantity > 0\n", "           OR EOD.off_shelf_quantity > 0)\n", "            --('U74140HR2015PTC056483') KEM\n", "            --('U74110DL2013PTC248660') TAMS\n", "    ORDER BY outlet.id\n", "    \"\"\"\n", "\n", "    # eod_report = pd.read_sql_query(query, presto)\n", "\n", "    chunks = list()\n", "    for chunk in pd.read_sql_query(query, presto, chunksize=50000):\n", "        chunks.append(chunk)\n", "\n", "    eod_report = pd.concat(chunks).reset_index(drop=True)\n", "    eod_report.head()\n", "\n", "    eod_report[\"etl_timestamp\"] = datetime.now(ist)\n", "    eod_report[\"is_pl\"] = eod_report[\"is_pl\"].astype(bool)\n", "    eod_report[\"total_networth\"] = eod_report[\"total_networth\"].astype(float)\n", "    eod_report[\"on_shelf_worth\"] = eod_report[\"on_shelf_worth\"].astype(float)\n", "    eod_report[\"off_shelf_worth\"] = eod_report[\"off_shelf_worth\"].astype(float)\n", "    # eod_report['snapshot_datetime'].astype(datetime)\n", "    eod_report[\"snapshot_datetime\"] = pd.to_datetime(eod_report[\"snapshot_datetime\"])\n", "    eod_report[\"active\"].astype(bool)\n", "\n", "    # eod_report.head()\n", "\n", "    # eod_report = eod_report[\n", "    #     ~((eod_report[\"entity_vendor_id\"] == 16907) & (eod_report[\"outlet_id\"] == 1885))\n", "    # ]\n", "\n", "    eod_report = eod_report.merge(oevm, on=[\"entity_vendor_id\", \"outlet_id\"])\n", "\n", "    assert len(\n", "        eod_report[\n", "            [\"entity_vendor_id\", \"outlet_id\", \"variant_id\", \"etl_timestamp\"]\n", "        ].drop_duplicates()\n", "    ) == len(eod_report)\n", "    assert (\n", "        not eod_report[\n", "            [\n", "                \"outlet_id\",\n", "                \"entity_vendor_id\",\n", "                \"variant_id\",\n", "            ]\n", "        ]\n", "        .isnull()\n", "        .values.any()\n", "    )\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"vendor_name\"},\n", "        {\"name\": \"entity_vendor_id\", \"type\": \"int\", \"description\": \"entity_vendor_id\"},\n", "        {\"name\": \"cin\", \"type\": \"varchar\", \"description\": \"cin\"},\n", "        {\"name\": \"gst_tin\", \"type\": \"varchar\", \"description\": \"gst_tin\"},\n", "        {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"legal_name\"},\n", "        {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "        {\"name\": \"upc\", \"type\": \"varchar\", \"description\": \"upc\"},\n", "        {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "        {\"name\": \"Storage_Type\", \"type\": \"varchar\", \"description\": \"Storage_Type\"},\n", "        {\"name\": \"brand_name\", \"type\": \"varchar\", \"description\": \"brand_name\"},\n", "        {\n", "            \"name\": \"Manufacturer_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Manufacturer_name\",\n", "        },\n", "        {\"name\": \"product_name\", \"type\": \"varchar\", \"description\": \"product_name\"},\n", "        {\"name\": \"Item_Type\", \"type\": \"varchar\", \"description\": \"Item_Type\"},\n", "        {\"name\": \"Description\", \"type\": \"varchar\", \"description\": \"Description\"},\n", "        {\"name\": \"active\", \"type\": \"bool\", \"description\": \"active\"},\n", "        {\n", "            \"name\": \"snapshot_datetime\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"snapshot_datetime\",\n", "        },\n", "        {\"name\": \"on_shelf_numbers\", \"type\": \"int\", \"description\": \"on_shelf_numbers\"},\n", "        {\"name\": \"on_shelf_worth\", \"type\": \"float\", \"description\": \"on_shelf_worth\"},\n", "        {\n", "            \"name\": \"<PERSON>_<PERSON><PERSON>_MRPWorth\",\n", "            \"type\": \"float\",\n", "            \"description\": \"On_Shelf_MRPWorth\",\n", "        },\n", "        {\n", "            \"name\": \"off_shelf_quantity\",\n", "            \"type\": \"int\",\n", "            \"description\": \"off_shelf_quantity\",\n", "        },\n", "        {\"name\": \"off_shelf_worth\", \"type\": \"float\", \"description\": \"off_shelf_worth\"},\n", "        {\"name\": \"total_quantity\", \"type\": \"int\", \"description\": \"total_quantity\"},\n", "        {\"name\": \"total_networth\", \"type\": \"float\", \"description\": \"total_networth\"},\n", "        {\"name\": \"is_pl\", \"type\": \"bool\", \"description\": \"is_pl\"},\n", "        {\"name\": \"etl_timestamp\", \"type\": \"datetime\", \"description\": \"etl_timestamp\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"finance_eod_inventory_report\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"outlet_id\", \"entity_vendor_id\", \"variant_id\", \"etl_timestamp\"],\n", "        \"sortkey\": \"snapshot_datetime\",\n", "        \"incremental_key\": \"etl_timestamp\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores the EOD inventory report data for finance\",  # Description of the table being sent to redshift\n", "    }\n", "    print(eod_report.shape)\n", "    pb.to_redshift(eod_report, **kwargs)\n", "    print(f\"Done for {yesterday}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # yesterday = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "# yesterday = '2022-01-31'\n", "# # mailer_filename = f\"sales_report_{yesterday}.csv\"\n", "# mailer_filename = 'eod_jan_31.csv'\n", "\n", "\n", "# # eod_new.drop(columns=[\"cin\", \"gst_tin\", \"etl_timestamp\"], inplace=True)\n", "# # # eod_new.columns = new\n", "# # eod_new.to_csv(mailer_filename, index=False)\n", "\n", "\n", "# # from_email = \"<EMAIL>\"\n", "# # to_email = [\n", "# #     \"dev.b<PERSON><EMAIL>\",\n", "# # ]\n", "# # cc = [\n", "# #     \"dev.b<PERSON><EMAIL>\",\n", "# # ]\n", "\n", "# from_email = \"<EMAIL>\"\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "# ]\n", "# cc = [\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# subject = f\"EOD Report for {yesterday}\"\n", "# html_content = f\"\"\"Hi,\n", "# <br><br> Please find the attached TAMS GLOBAL PRIVATE LIMITED EOD Report for 2022-01-31.\n", "# <br>\n", "# <br> Thanks,\n", "# <br> Data Warehouse\n", "# \"\"\"\n", "\n", "# pb.send_email(\n", "#     from_email, to_email, subject, html_content, cc=cc, files=[mailer_filename]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# for i,j in zip(eod_report.columns, eod_report.dtypes):\n", "#     if j=='object':\n", "#         j = 'varchar'\n", "#     if j=='int64':\n", "#         j='int'\n", "#     if j=='float64':\n", "#         j='float'\n", "#     print(f\"\"\"{{\n", "#     \\\"name\\\":\\\"{i}\\\",\n", "#     \\\"type\\\":\\\"{j}\\\",\n", "#     \\\"description\\\":\\\"{i}\\\"\n", "# }},\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}