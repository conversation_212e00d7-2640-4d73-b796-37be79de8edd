{"cells": [{"cell_type": "code", "execution_count": null, "id": "bd99ff81-d376-42a1-8dba-a7844bd6a064", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "80517865-14f1-4b10-a377-8456b6318880", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "5b76aeb6-227e-44c7-a693-eab519c7e5c6", "metadata": {}, "outputs": [], "source": ["crbs = pd.read_sql_query(\n", "    \"\"\"\n", "SELECT i.grofers_order_id,\n", "       iod.scheduled_at + interval '5' hour + interval '30' minute as scheduled_at,\n", "       r.item_id,\n", "       i.variant_id,\n", "       i.upc_id,\n", "       r.name AS product_name,\n", "       (CASE\n", "            WHEN r.outlet_type = 1 THEN 'F&V type'\n", "            WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"item_type\",\n", "       SUM(i.\"delta\") AS quantity,\n", "       r.variant_mrp AS mrp,\n", "       sum(i.\"delta\")*r.variant_mrp AS \"total_mrp\",\n", "       r.variant_uom_text AS grammage,\n", "       i.outlet_id,\n", "       ro.name AS outlet_name,\n", "       i.entity_vendor_id,\n", "       v.vendor_name as entity_vendor_name,\n", "       cti.cin,\n", "       cti.gst_tin,\n", "       cti.legal_name,\n", "       loc2.name AS outlet_location,\n", "       loc1.name AS customer_location,\n", "       i.pos_timestamp + interval '5' hour + interval '30' minute as pos_timestamp,\n", "       i2.name AS update_type,\n", "       i.created_by_name,\n", "       i.transaction_lp as weighted_lp,\n", "       i.inventory_update_id\n", "FROM (select *\n", "      from lake_view_ims.ims_inventory_log\n", "      where insert_ds_ist = cast(current_date - interval '1' day as varchar)\n", "      and inventory_update_type_id in (7,\n", "                                       9,\n", "                                       33,\n", "                                       34,\n", "                                       63,\n", "                                       67)) i\n", "INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "left JOIN lake_view_ims.ims_order_details iod ON i.grofers_order_id=iod.order_id\n", "left JOIN lake_retail.console_location loc1 ON loc1.id=iod.retail_city_id\n", "left JOIN lake_retail.console_location loc2 ON loc2.id=ro.tax_location_id\n", "left join lake_vms.vms_vendor_city_mapping cm on cm.vendor_id = i.entity_vendor_id and cm.active = 1\n", "left join lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = cm.id and cti.active = 1\n", "left join lake_vms.vms_vendor v on v.id = i.entity_vendor_id\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         9,\n", "         11,\n", "         12,\n", "         13,\n", "         14,\n", "         15,\n", "         16,\n", "         17,\n", "         18,\n", "         19,\n", "         20,\n", "         21,\n", "         22,\n", "         23,\n", "         24,\n", "         25\n", "\"\"\",\n", "    presto,\n", ")\n", "\n", "crbs.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "340b95e8-5405-4c47-80de-4b0a6cd911fb", "metadata": {}, "outputs": [], "source": ["crbs[\"etl_timestamp\"] = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "id": "327ae63a-e8dc-4077-9dec-cb652aecc967", "metadata": {}, "outputs": [], "source": ["crbs[\"scheduled_at\"] = pd.to_datetime(crbs[\"scheduled_at\"])\n", "crbs[\"pos_timestamp\"] = pd.to_datetime(crbs[\"pos_timestamp\"])\n", "crbs[\"weighted_lp\"] = crbs[\"weighted_lp\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6ddc9e3c-d936-42dc-a089-4173cf0dd4d3", "metadata": {}, "outputs": [], "source": ["assert len(crbs[[\"inventory_update_id\"]].drop_duplicates()) == len(crbs)\n", "assert not crbs[\"entity_vendor_id\"].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "id": "3c26922e-a8e3-4941-b3a9-8572c0b50d28", "metadata": {}, "outputs": [], "source": ["crbs.info()"]}, {"cell_type": "code", "execution_count": null, "id": "9a41f747-90a1-460a-9d96-f97d4c5e1cd0", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"grofers_order_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"grofers order id\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"scheduled at\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item id\",\n", "    },\n", "    {\n", "        \"name\": \"variant_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"variant id\",\n", "    },\n", "    {\n", "        \"name\": \"upc_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"upc id\",\n", "    },\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"product name\",\n", "    },\n", "    {\n", "        \"name\": \"item_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item type\",\n", "    },\n", "    {\n", "        \"name\": \"quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity\",\n", "    },\n", "    {\n", "        \"name\": \"mrp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"mrp\",\n", "    },\n", "    {\n", "        \"name\": \"total_mrp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total mrp\",\n", "    },\n", "    {\n", "        \"name\": \"grammage\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"grammage\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet name\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"vendor id from vms vendor\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"entity vendor name\",\n", "    },\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"cin\",\n", "    },\n", "    {\n", "        \"name\": \"gst_tin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"gst tin\",\n", "    },\n", "    {\n", "        \"name\": \"legal_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"legal name\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_location\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet location\",\n", "    },\n", "    {\n", "        \"name\": \"customer_location\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"customer location\",\n", "    },\n", "    {\n", "        \"name\": \"pos_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"pos timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"update_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"update type\",\n", "    },\n", "    {\n", "        \"name\": \"created_by_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"created by name\",\n", "    },\n", "    {\n", "        \"name\": \"weighted_lp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted lp\",\n", "    },\n", "    {\n", "        \"name\": \"inventory_update_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"inventory update id\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"row entry timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "36b30f87-0841-43c7-bf77-545547ed2a42", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"finance_crbs_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"inventory_update_id\", \"etl_timestamp\"],\n", "    \"sortkey\": [\"pos_timestamp\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores customer returned bad stock transaction related data for finance\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ce86f3a2-ae70-4ff3-95da-587e535bf029", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(crbs, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7497ff27-a936-4ebb-9c54-d579d8e77fbb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}