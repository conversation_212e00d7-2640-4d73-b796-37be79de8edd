{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, date, timedelta\n", "import pytz\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "pd.set_option(\"display.max_columns\", 40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select  discrepancy_table.id,\n", "        discrepancy_table.outlet_id,\n", "        discrepancy_table.outlet_name, \n", "        discrepancy_table.discrepancy_id, \n", "        convert_timezone('Asia/Kolkata',discrepancy_table.created_at) as created_at, \n", "        discrepancy_table.vendor_name, \n", "        discrepancy_table.vendor_id, \n", "        discrepancy_table.vendor_invoice_id, \n", "        discrepancy_table.seller_company, \n", "        discrepancy_table.buyer_company, \n", "        discrepancy_table.product_name, \n", "        discrepancy_table.upc_id, \n", "        discrepancy_table.entity_vendor_id,\n", "        discrepancy_table.cin,\n", "        pp.item_id, \n", "        discrepancy_table.quantity as discrepancy_quantity, \n", "        discrepancy_table.po_number, \n", "        pi.invoice_id as billing_invoice, \n", "        discrepancy_table.cgst_value, \n", "        discrepancy_table.sgst_value, \n", "        discrepancy_table.igst_value, \n", "        discrepancy_table.cess_value, \n", "        discrepancy_table.additional_cess_value,\n", "        (discrepancy_table.landing_price*discrepancy_table.quantity) as discrepancy_price, \n", "        sum(pipd.quantity) as invoice_quantity, \n", "        case when pi.invoice_id is NULL then 0 else 1 end as internal_billing_flag, \n", "        discrepancy_table.reason_name, \n", "        discrepancy_table.remark \n", "from \n", "    (\n", "         select d_note.outlet_id,\n", "                co.name as outlet_name, \n", "                d_note_details.remark, \n", "                d_note.id as discrepancy_id, \n", "                d_note.created_at, \n", "                d_note.vendor_id, \n", "                d_note.vendor_name, \n", "                d_note.po_number, \n", "                d_note.vendor_invoice_id, \n", "                co.legal_name as buyer_company, \n", "                mo.legal_name as seller_company, \n", "                d_note_details.id,\n", "                d_note_details.upc_id, \n", "                d_note_details.name as product_name, \n", "                d_note_details.landing_price, \n", "                d_note_details.quantity, \n", "                d_note_details.cgst_value, \n", "                d_note_details.sgst_value, \n", "                d_note_details.igst_value, \n", "                d_note_details.cess_value, \n", "                d_note_details.additional_cess_value,\n", "                d_reason.name as reason_name, \n", "                cm.outlet_id as vendor_outlet_id, \n", "                v.id as entity_vendor_id,\n", "                vt.cin\n", "        from lake_pos.discrepancy_note d_note \n", "        inner join lake_pos.discrepancy_note_product_detail d_note_details on d_note_details.dn_id_id=d_note.id \n", "        inner join lake_retail.console_outlet co on co.id=d_note.outlet_id \n", "        inner join lake_retail.outlet_entity_vendor_mapping ev on d_note.entity_vendor_id = ev.entity_vendor_id and co.active = 1 and ev.active = 1\n", "        inner join lake_vms.vms_vendor v on v.id = ev.entity_vendor_id\n", "        inner join lake_vms.vms_vendor_city_mapping vc on vc.vendor_id = v.id\n", "        inner join lake_vms.vms_vendor_city_tax_info vt on vt.vendor_city_id = vc.id\n", "        inner join lake_pos.discrepancy_reason d_reason on d_reason.id=d_note_details.reason_code \n", "        inner join lake_retail.console_merchant cm on cm.id=d_note.vendor_id \n", "        left join lake_retail.console_outlet mo on mo.id=cm.outlet_id \n", "        where date(d_note.created_at + interval '5.5 hrs') between current_date-7 and current_date-1\n", "    ) discrepancy_table \n", "left join lake_pos.pos_invoice pi on pi.invoice_id=discrepancy_table.vendor_invoice_id and pi.outlet_id=discrepancy_table.vendor_outlet_id \n", "left join lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id and pipd.upc_id = discrepancy_table.upc_id \n", "left join lake_rpc.product_product pp on pp.upc = discrepancy_table.upc_id \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disc = pd.read_sql_query(q, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disc[disc[[\"id\"]].duplicated()].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disc.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disc[\"etl_timestamp\"] = pd.to_datetime(datetime.now(ist))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disc.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"primary key\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet name\",\n", "    },\n", "    {\n", "        \"name\": \"discrepancy_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"discrepancy id\",\n", "    },\n", "    {\n", "        \"name\": \"created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"created at timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"vendor name\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_invoice_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"vendor invoice id\",\n", "    },\n", "    {\n", "        \"name\": \"seller_company\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"seller company\",\n", "    },\n", "    {\n", "        \"name\": \"buyer_company\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"buyer company\",\n", "    },\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"product name\",\n", "    },\n", "    {\n", "        \"name\": \"upc_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"upc id\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"entity vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Corporate Identification Number\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"item id\",\n", "    },\n", "    {\n", "        \"name\": \"discrepancy_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"discrepancy quantity\",\n", "    },\n", "    {\n", "        \"name\": \"po_number\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"PO Number\",\n", "    },\n", "    {\n", "        \"name\": \"billing_invoice\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"billing invoice\",\n", "    },\n", "    {\n", "        \"name\": \"cgst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cgst value\",\n", "    },\n", "    {\n", "        \"name\": \"sgst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sgst value\",\n", "    },\n", "    {\n", "        \"name\": \"igst_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"igst value\",\n", "    },\n", "    {\n", "        \"name\": \"cess_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cess value\",\n", "    },\n", "    {\n", "        \"name\": \"additional_cess_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"additional cess value\",\n", "    },\n", "    {\n", "        \"name\": \"discrepancy_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"discrepancy price\",\n", "    },\n", "    {\n", "        \"name\": \"invoice_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"invoice quantity\",\n", "    },\n", "    {\n", "        \"name\": \"internal_billing_flag\",\n", "        \"type\": \"int\",\n", "        \"description\": \"internal billing flag\",\n", "    },\n", "    {\n", "        \"name\": \"reason_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"reason for discrepancy\",\n", "    },\n", "    {\n", "        \"name\": \"remark\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"remark\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"row entry timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"finance_discrepancy_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"id\", \"etl_timestamp\"],\n", "    \"sortkey\": \"created_at\",\n", "    \"incremental_key\": \"created_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores discrepancy related data for finance\",\n", "}\n", "pb.to_redshift(disc, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}