{"cells": [{"cell_type": "code", "execution_count": null, "id": "bd99ff81-d376-42a1-8dba-a7844bd6a064", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "80517865-14f1-4b10-a377-8456b6318880", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "5b76aeb6-227e-44c7-a693-eab519c7e5c6", "metadata": {}, "outputs": [], "source": ["prn = pd.read_sql_query(\n", "    \"\"\"\n", "SELECT p.outlet_id,\n", "       p.outlet_name,\n", "       p.source_entity_vendor_id,\n", "       v.vendor_name as entity_vendor_name,\n", "       cti.cin,\n", "       cti.gst_tin,\n", "       cti.legal_name,\n", "       p.merchant_id,\n", "       c1.name AS merchant_name,\n", "       p.invoice_id,\n", "       p.pos_timestamp + interval '5' hour + interval '30' minute as pos_timestamp,\n", "       p1.id,\n", "       p2.item_id,\n", "       p1.upc_id,\n", "       p2.name AS product_name,\n", "       (CASE\n", "            WHEN p2.outlet_type = 1 THEN 'F&V type'\n", "            WHEN p2.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"item_type\",\n", "       p1.variant_id,\n", "       p2.variant_uom_value AS uom,\n", "       p2.variant_mrp AS mrp,\n", "       p1.quantity,\n", "       p1.selling_price AS landing_price,\n", "       COALESCE(p1.cgst_value,0)+COALESCE(p1.sgst_value,0)+COALESCE(p1.igst_value,0)+COALESCE(p1.cess_value,0)+(p1.quantity * COALESCE(p1.additional_cess_value,0)) AS \"total_tax\",\n", "       Round((p1.selling_price * p1.quantity)-(COALESCE(p1.cgst_value,0)+COALESCE(p1.sgst_value,0)+COALESCE(p1.igst_value,0)+COALESCE(p1.cess_value,0)+(p1.quantity * COALESCE(p1.additional_cess_value,0))),2) AS \"total_cost_price\",\n", "       p1.selling_price * p1.quantity AS \"total_landing_price\",\n", "       p.grofers_order_id AS merchant_invoice_id\n", "FROM lake_retail.console_merchant c1\n", "INNER JOIN (select *\n", "            from lake_view_pos.pos_invoice\n", "            where insert_ds_ist = cast(current_date - interval '1' day as varchar)\n", "            and invoice_type_id = 6) p ON p.merchant_id=c1.id\n", "INNER JOIN (select *\n", "            from lake_view_pos.pos_invoice_product_details\n", "            where insert_ds_ist = cast(current_date - interval '1' day as varchar)) p1 ON p.id = p1.invoice_id\n", "INNER JOIN lake_rpc.product_product p2 ON p2.variant_id = p1.variant_id\n", "left join lake_vms.vms_vendor_city_mapping cm on cm.vendor_id = p.source_entity_vendor_id and cm.active = 1\n", "left join lake_vms.vms_vendor_city_tax_info cti on cti.vendor_city_id = cm.id and cti.active = 1\n", "left join lake_vms.vms_vendor v on v.id = p.source_entity_vendor_id\n", "\"\"\",\n", "    presto,\n", ")\n", "prn.head()"]}, {"cell_type": "code", "execution_count": null, "id": "340b95e8-5405-4c47-80de-4b0a6cd911fb", "metadata": {}, "outputs": [], "source": ["prn[\"etl_timestamp\"] = datetime.now(ist)"]}, {"cell_type": "code", "execution_count": null, "id": "327ae63a-e8dc-4077-9dec-cb652aecc967", "metadata": {}, "outputs": [], "source": ["prn[\"pos_timestamp\"] = pd.to_datetime(prn[\"pos_timestamp\"])\n", "prn[\"total_tax\"] = prn[\"total_tax\"].astype(float)\n", "prn[\"quantity\"] = prn[\"quantity\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6ddc9e3c-d936-42dc-a089-4173cf0dd4d3", "metadata": {}, "outputs": [], "source": ["assert not prn.duplicated(\"id\").any()\n", "assert not prn[\"source_entity_vendor_id\"].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "id": "3c26922e-a8e3-4941-b3a9-8572c0b50d28", "metadata": {}, "outputs": [], "source": ["prn.info()"]}, {"cell_type": "code", "execution_count": null, "id": "9a41f747-90a1-460a-9d96-f97d4c5e1cd0", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet name\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"source entity vendor id\",\n", "    },\n", "    {\n", "        \"name\": \"entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"entity vendor name\",\n", "    },\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"cin\",\n", "    },\n", "    {\n", "        \"name\": \"gst_tin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"gstin\",\n", "    },\n", "    {\n", "        \"name\": \"legal_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"legal name\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"merchant id\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"merchant name\",\n", "    },\n", "    {\n", "        \"name\": \"invoice_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"prn invoice id\",\n", "    },\n", "    {\n", "        \"name\": \"pos_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"pos timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"id\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"item id\",\n", "    },\n", "    {\n", "        \"name\": \"upc_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"upc id\",\n", "    },\n", "    {\n", "        \"name\": \"product_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"product name\",\n", "    },\n", "    {\n", "        \"name\": \"item_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"item type\",\n", "    },\n", "    {\n", "        \"name\": \"variant_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"variant id\",\n", "    },\n", "    {\n", "        \"name\": \"uom\",\n", "        \"type\": \"float\",\n", "        \"description\": \"uom\",\n", "    },\n", "    {\n", "        \"name\": \"mrp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"mrp\",\n", "    },\n", "    {\n", "        \"name\": \"quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity\",\n", "    },\n", "    {\n", "        \"name\": \"landing_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"landing price\",\n", "    },\n", "    {\n", "        \"name\": \"total_tax\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total tax\",\n", "    },\n", "    {\n", "        \"name\": \"total_cost_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total cost price\",\n", "    },\n", "    {\n", "        \"name\": \"total_landing_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total landing price\",\n", "    },\n", "    {\n", "        \"name\": \"merchant_invoice_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"merchant invoice id\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"row entry timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "36b30f87-0841-43c7-bf77-545547ed2a42", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"finance_prn_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"id\", \"etl_timestamp\"],\n", "    \"sortkey\": [\"pos_timestamp\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores purchase return related data for finance\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ce86f3a2-ae70-4ff3-95da-587e535bf029", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(prn, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7497ff27-a936-4ebb-9c54-d579d8e77fbb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}