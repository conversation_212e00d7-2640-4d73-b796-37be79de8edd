{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Live PNA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=10):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60)\n", "\n", "\n", "def to_sheets(\n", "    df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=10\n", "):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = from_sheets(\"1ZQADLwdjWFcS8gwkrP6uIyXfkEa-S2TJz4VNZI9aLfA\", \"Sheet1\").iloc[\n", "    :, 0:3\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with itpd as (select\n", "x.product_id::int, max(item_id::int) as item_id\n", "from\n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from\n", "consumer.it_pd_log i\n", "--join order_product_details opd on opd.product_id=i.product_id\n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at group by 1\n", ")\n", "\n", ",base_table as (SELECT DISTINCT convert_timezone('asia/kolkata',(TIMESTAMP 'epoch' + oo.slot_start * interval '1 second')) AS slot_start,\n", "                convert_timezone('asia/kolkata',(TIMESTAMP 'epoch' + oo.slot_end * interval '1 second')) AS slot_end,\n", "                oo.id AS order_id,\n", "                CASE\n", "                    WHEN soi.suborder_id IS NULL THEN so2.id\n", "                    ELSE soi.suborder_id\n", "                END AS suborder_id,\n", "                ooi.product_id AS product_id,\n", "                ooi.product_title as product_name,\n", "                ooi.id AS order_item_id,\n", "                ooi.quantity AS ordered_quantity,\n", "                ooi.procured_quantity AS procured_quantity\n", "FROM\n", "  (SELECT DISTINCT order_id\n", "   FROM lake_oms_bifrost.oms_suborder os\n", "   LEFT JOIN lake_oms_bifrost.oms_merchant m ON os.backend_merchant_id = m.id\n", "   LEFT JOIN lake_retail.console_outlet_cms_store cms ON m.external_id = cms.cms_store\n", "   AND active = 1\n", "   AND cms_update_active = 1\n", "   LEFT JOIN lake_retail.console_outlet o ON cms.outlet_id= o.id\n", "   WHERE (slot_start BETWEEN extract(epoch\n", "                                     FROM (CURRENT_DATE::TIMESTAMP - interval '5.5 hour')) AND extract(epoch\n", "                                                                                                       FROM CURRENT_DATE::TIMESTAMP + interval '1 day' - interval '5.5 hour'))\n", "     AND TYPE = 'RetailSuborder'\n", "     AND o.business_type_id = 7\n", "     AND current_status IN ('AT_DELIVERY_CENTER',\n", "                            'BILLED') ) AS os\n", "INNER JOIN lake_oms_bifrost.oms_order AS oo ON os.order_id = oo.id\n", "AND oo.type IN ('RetailForwardOrder',\n", "                'InternalForwardOrder')\n", "INNER JOIN lake_oms_bifrost.oms_order_item AS ooi ON oo.id = ooi.order_id\n", "INNER JOIN lake_oms_bifrost.oms_merchant AS om ON oo.merchant_id = om.id\n", "LEFT JOIN lake_oms_bifrost.oms_suborder_item soi ON soi.order_item_id = ooi.id\n", "LEFT JOIN lake_oms_bifrost.oms_suborder so ON so.id=soi.suborder_id\n", "LEFT JOIN\n", "  (SELECT order_id,\n", "          max(id) AS id\n", "   FROM lake_oms_bifrost.oms_suborder\n", "   WHERE (slot_start BETWEEN extract(epoch\n", "                                     FROM (CURRENT_DATE::TIMESTAMP - interval '5.5 hour')) AND extract(epoch\n", "                                                                                                       FROM (CURRENT_DATE::TIMESTAMP + interval '1 day' - interval '5.5 hour')))\n", "     AND TYPE = 'RetailSuborder' \n", "     AND current_status IN ('AT_DELIVERY_CENTER',\n", "                            'BILLED')\n", "   GROUP BY 1) so2 ON oo.id=so2.order_id\n", "WHERE ooi.quantity> ooi.procured_quantity\n", "),\n", "\n", "ReasonKey as (Select distinct bt.*,\n", "                 oic.reason_key\n", "from base_table bt\n", "left join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.order_item_id = oic.order_item_id\n", "),\n", "Final as (\n", "Select ReasonKey.*,o.id as Outlet_id,o.name as Outlet_name,o.facility_id as Facility_id,f.name as facilityname, om.city_name as city\n", "from ReasonKey \n", "left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "left join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", ")\n", "Select distinct\n", "      f.outlet_id,\n", "      f.outlet_name,\n", "      f.city,\n", "      'Marked PNA' as reason,\n", "      f.order_id,\n", "      f.product_id,\n", "      pp.item_id,\n", "      f.product_name,\n", "      case when pp.outlet_type=1 then 'FNV' else 'Grocery/Cold' end as Product_Type,\n", "      f.slot_start,\n", "      f.slot_end,\n", "      f.slot_end::date as scheduled_date,\n", "      f.ordered_quantity,\n", "      f.procured_quantity as picked_qty\n", "from Final f left join itpd on itpd.product_id=f.product_id\n", "left join lake_rpc.product_product pp on pp.item_id = itpd.item_id \n", "where reason_key= 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING'\n", "\"\"\"\n", "x = pd.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = x[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"city\",\n", "        \"reason\",\n", "        \"order_id\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"product_type\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"scheduled_date\",\n", "        \"ordered_quantity\",\n", "        \"picked_qty\",\n", "    ]\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Multiple PNA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["z = df.groupby([\"outlet_id\"]).product_id.value_counts().reset_index(name=\"count\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = z[z[\"count\"] >= 3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["it_pd_log_query = \"\"\"select  \n", "x.product_id::int, item_id::int  \n", "from \n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.it_pd_log i \n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\n", "\"\"\"\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift)\n", "\n", "it_pd_log_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = y.merge(it_pd_log_df, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m[\"item_id\"] = m[\"item_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if m.shape[0] > 0:\n", "    m[\"item_id\"] = m.groupby([\"outlet_id\", \"product_id\", \"count\"]).item_id.transform(\n", "        lambda x: \" \".join(x)\n", "    )\n", "    m = m.drop_duplicates()\n", "\n", "    out = df[[\"outlet_id\", \"outlet_name\"]].drop_duplicates()\n", "\n", "    prod = df[[\"product_id\", \"product_name\"]].drop_duplicates()\n", "\n", "    m = m.merge(out, on=[\"outlet_id\"], how=\"inner\")\n", "\n", "    m = m.merge(prod, on=[\"product_id\"], how=\"inner\")\n", "\n", "    final = m[\n", "        [\"outlet_id\", \"outlet_name\", \"product_id\", \"item_id\", \"product_name\", \"count\"]\n", "    ]\n", "\n", "    final = final.sort_values(by=[\"count\"], ascending=False)\n", "\n", "    final_fnv = final[final.product_name.str.contains(\"Fresh\", na=False)]\n", "\n", "    final.shape, final_fnv.shape\n", "\n", "    final.head()\n", "\n", "    final_fnv.head()\n", "\n", "    to_sheets(final, \"1Fbe9ThTPudbqIU5znq5DHNaboX2c4kS94TRoZdFYAg4\", \"Overall\")\n", "    to_sheets(final_fnv, \"1Fbe9ThTPudbqIU5znq5DHNaboX2c4kS94TRoZdFYAg4\", \"FNV\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import traceback, time\n", "\n", "for i in outlets[\"Outlet ID\"].unique():\n", "    live = data[data.outlet_id == int(i)]\n", "    print(i, live.shape)\n", "    sh_id = outlets[outlets[\"Outlet ID\"] == i][\"SheetId\"].to_list()[0]\n", "    print(sh_id)\n", "    to_sheets(live, sh_id, \"Live Data\")\n", "    time.sleep(20)\n", "    if m.shape[0] > 0:\n", "        mul_pna = final[final.outlet_id == int(i)]\n", "        print(i, mul_pna.shape)\n", "        to_sheets(mul_pna, sh_id, \"Multiple PNA\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def get_short_name(outlet_id):\n", "#     sql_outlet_names = \"\"\"\n", "#     SELECT DISTINCT co.id AS outlet_id, short_code\n", "#     FROM console_outlet co\n", "#     JOIN console_device cd ON co.device_id = cd.id\n", "#     WHERE co.id = %(outlet_id)s\n", "#     \"\"\"\n", "#     df = pd.read_sql_query(sql_outlet_names, retail ,params ={\"outlet_id\": outlet_id})\n", "#     return df[\"short_code\"].to_list()[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def update_stock(outlet_id, upc, variant_id, quantity):\n", "#     short_code = get_short_name(outlet_id)\n", "#     transaction_id = str(uuid.uuid4())\n", "#     update_ts = str(datetime.now())\n", "#     url = \"https://{}-phoenix-retail.grofers.com/api/v2/manual-stock-update/\".format(\n", "#         short_code\n", "#     )\n", "#     headers = {\n", "#         \"cache-control\": \"no-cache\",\n", "#         \"content-type\": \"application/json;charset=UTF-8\",\n", "#         \"api_client\": \"3\",\n", "#         \"imei\": \"100000000000086\",\n", "#         \"outlet-id\": outlet_id,\n", "#         \"username\": \"<EMAIL>\",\n", "#         \"password\": \"warehouse123\",\n", "#         \"service-auth\": \"Ip8Ok8ooEu6al6Di\",\n", "#         \"AUTH_TOKEN\": \"D95BA511C3898BEE6FF96EE3EAF7A\",\n", "#         \"service-type\": \"supply_marketplace\",\n", "#         \"TRANSACTION-ID\": transaction_id,\n", "#     }\n", "#     payload = {\n", "#         \"pos_id\": 1,\n", "#         \"pos_timestamp\": update_ts,\n", "#         \"outlet_id\": outlet_id,\n", "#         \"product_details\": [\n", "#             {\n", "#                 \"upc_id\": upc,\n", "#                 \"variant_id\": variant_id,\n", "#                 \"quantity\": quantity,\n", "#                 \"update_type_id\": 2,\n", "#             }\n", "#         ],\n", "#     }\n", "#     response = requests.request(\"POST\", url, headers=headers, data=json.dumps(payload))\n", "#     if response.status_code == 200 or (\n", "#         response.status_code == 400\n", "#         and \"Updated quantity cannot be same as current saleable quantity for UPC - \"\n", "#         in response.text\n", "#     ):\n", "#         return \"Success\"\n", "#     else:\n", "#         return \"Failure\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import uuid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# str(uuid.uuid4())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# variant_details_query = \"\"\"select  from ims.ims_inventory_log\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x[x[\"item_id\"]==10005578]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}