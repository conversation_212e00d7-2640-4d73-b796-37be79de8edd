{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import pytz\n", "import time\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "!pip install pandasql\n", "import pandasql as ps\n", "from pandasql import sqldf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims = pb.get_connection(\"[Replica] RDS IMS\")\n", "whl = pb.get_connection(\"[Replica] RDS Warehouse Location\")\n", "pos = pb.get_connection(\"[Replica] RDS POS\")\n", "po = pb.get_connection(\"[Replica] RDS PO\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = str(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0))\n", "end_time = str(datetime.now().replace(hour=23, minute=59, second=59, microsecond=0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time, end_time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = pd.read_sql_query(\n", "    \"\"\"select distinct id from lake_retail.console_outlet \n", "                               where business_type_id in (1,12) and company_type_id in (9,16) \n", "                               and facility_id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873)\"\"\",\n", "    redshift,\n", ")\n", "outlets = list(outlets.id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bcpl_entities = pd.read_sql_query(\n", "    f\"\"\"select distinct entity_vendor_id from \n", "                               lake_retail.outlet_entity_vendor_mapping o\n", "                               join lake_vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                               where o.outlet_id in {*outlets,}\n", "                               and v.vendor_name ilike '%%BCPL%%'\n", "                               \"\"\",\n", "    redshift,\n", ")\n", "bcpl_entities = bcpl_entities.entity_vendor_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Indent details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "indents = pd.read_sql_query(\n", "    f\"\"\"select s.outlet_id, \n", "                (case when HOUR(s.created_at) between 0 and 11 then 'Morning' else 'Evening' end) as shift,\n", "                FROM_UNIXTIME(((UNIX_TIMESTAMP(s.created_at + INTERVAL '330' MINUTE)) DIV 600) * 600) AS created_at,\n", "                (dispatch_time + INTERVAL '330' MINUTE) as Dispatch_time, \n", "                s.merchant_id as <PERSON><PERSON><PERSON>,\n", "                s.id as sto_id, \n", "                count(distinct si.item_id) as skus, \n", "                sum(si.reserved_quantity) as created_qty\n", "     from po.sto s\n", "     join po.sto_items si on si.sto_id = s.id\n", "     where s.created_at between '{start_time}' and '{end_time}'\n", "     and s.outlet_id in {*outlets,}\n", "     and s.source_entity_vendor_id not in {*bcpl_entities,}\n", "     group by 1,2,3,4,5,6\n", "     \"\"\",\n", "    po,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents[\"Dispatch_time\"] = indents[\"Dispatch_time\"].fillna(\"Manual\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st = time.time()\n", "ims_ = pd.read_sql_query(\n", "    f\"\"\"select \n", "            s.outlet_id, \n", "            s.sto_id,\n", "            sum(si.billed_quantity) as billed_qty\n", "    from ims.ims_sto_details s\n", "    join ims.ims_sto_item si on si.sto_id = s.sto_id\n", "    where s.created_at between '{start_time}' and '{end_time}'\n", "    and s.outlet_id in {*outlets,}\n", "    and s.merchant_id in {*indents.Darkstores.unique(),}\n", "    and s.sto_id in {*indents.sto_id.unique(),}\n", "    group by 1,2\n", "    \"\"\",\n", "    ims,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dispatch = pd.read_sql_query(\n", "    f\"\"\"select sto_id, sum(dispatched_box_count) as dispatched_box_count, sum(box_count) as box_count\n", "        from warehouse_location.dispatch_indent \n", "        where created_at between '{start_time}' and '{end_time}' and sto_id in {*indents.sto_id.unique(),}\n", "        group by 1\"\"\",\n", "    whl,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = indents.merge(ims_, on=[\"sto_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(dispatch, on=[\"sto_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = (\n", "    final.groupby([\"outlet_id\", \"shift\", \"created_at\", \"Dispatch_time\"])\n", "    .agg(\n", "        {\n", "            \"Darkstores\": \"nunique\",\n", "            \"sto_id\": \"nunique\",\n", "            \"skus\": sum,\n", "            \"created_qty\": sum,\n", "            \"billed_qty\": sum,\n", "            \"dispatched_box_count\": sum,\n", "            \"box_count\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final1, \"1veeWnrCpvSZvEvZf2IwXiEC8RNWBU0KYYqvzC3-9-Lk\", \"Indents\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Picking Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wave = pd.read_sql_query(\n", "    f\"\"\"select id, entity_id from warehouse_location.warehouse_wave \n", "            where entity_id in {*indents.sto_id.unique(),}\"\"\",\n", "    whl,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["picklists = pd.read_sql_query(\n", "    f\"\"\"select wave_id, \n", "                                         id, \n", "                                         pick_list_id, \n", "                                         state,\n", "                                         assigned_to_name,\n", "                                         outlet_id,\n", "                                         created_at + interval '330' MINUTE as created_at,\n", "                                         assigned_at + interval '330' MINUTE as assigned_at, \n", "                                         picking_started_at + interval '330' MINUTE as picking_started_at,\n", "                                         (case when HOUR(created_at) between 0 and 11 \n", "                                         then 'Morning' else 'Evening' end) as shift\n", "                                         from warehouse_location.warehouse_pick_list \n", "                                         where id IS NOT NULL and wave_id in {*wave.id.unique(),}\"\"\",\n", "    whl,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_items = pd.read_sql_query(\n", "    f\"\"\"select * from warehouse_location.warehouse_pick_list_item \n", "                                  where pick_list_id in {*picklists.id.unique(),}\"\"\",\n", "    whl,\n", ")\n", "picklist_logs = pd.read_sql_query(\n", "    f\"\"\"select pick_list_id, \n", "                                  pos_timestamp + interval '330' minute as picking_completed_at\n", "                                  from warehouse_location.pick_list_log\n", "                                  where pick_list_id in {*picklists.id.unique(),}\n", "                                  and picklist_state = 4\"\"\",\n", "    whl,\n", ")\n", "disp = pd.read_sql_query(\n", "    f\"\"\"select sto_id, sum(dispatched_box_count) as dis_box, sum(box_count) as box_c \n", "                             from warehouse_location.dispatch_indent \n", "                             where sto_id in {*indents.sto_id.unique(),}\n", "                             group by 1\"\"\",\n", "    whl,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["picklist_data = sqldf(\n", "    \"\"\"select \n", "          pl.outlet_id,\n", "          pl.id AS picklist_id,\n", "          pl.pick_list_id as on_ground_picklist_id,\n", "          pl.assigned_to_name as picker_id,\n", "          (case \n", "              when pl.state = 1 then 'Created'\n", "              when pl.state = 2 then 'Assigned'\n", "              when pl.state = 3 then 'In_progress'\n", "              when pl.state = 4 then 'Completed'\n", "              when pl.state = 10 then 'Billed'\n", "          else null end) as state,\n", "          w.entity_id as sto_id,\n", "          pl.shift,\n", "          (pl.assigned_at) AS picklist_assigned_at,\n", "          (pl.picking_started_at) AS picking_started_at_ist,\n", "          (pll.picking_completed_at) AS picking_completed_at_ist,\n", "          max(pli.updated_at) as max_updated_at,\n", "          count(distinct pli.item_id) as SKUs,\n", "          count(distinct case when pli.picked_quantity > 0 then pli.item_id else null end) as SKUs_picked,\n", "          sum(pli.required_quantity) as quantity_required,\n", "          sum(pli.picked_quantity) as quantity_picked,\n", "          sum(dis_box) as dispatched_boxes,\n", "          sum(box_c) as total_boxes\n", "          FROM wave w\n", "          JOIN picklists pl on w.id = pl.wave_id\n", "          JOIN picklist_items pli on pli.pick_list_id = pl.id\n", "          LEFT JOIN picklist_logs pll on pll.pick_list_id = pl.id\n", "          LEFT JOIN disp d on d.sto_id = w.entity_id\n", "          GROUP BY 1,2,3,4,5,6,7,8,9\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_data[\"picklist_assigned_at\"] = pd.to_datetime(\n", "    picklist_data[\"picklist_assigned_at\"]\n", ")\n", "picklist_data[\"picking_started_at_ist\"] = pd.to_datetime(\n", "    picklist_data[\"picking_started_at_ist\"]\n", ")\n", "picklist_data[\"picking_completed_at_ist\"] = pd.to_datetime(\n", "    picklist_data[\"picking_completed_at_ist\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["# st = time.time()\n", "# picklist_data = pd.DataFrame()\n", "# for outlet_id in outlets:\n", "#     dtemp = pd.read_sql_query(\n", "#         f\"\"\"\n", "#     SELECT\n", "#       pl.outlet_id,\n", "#       pl.id AS picklist_id,\n", "#       pl.pick_list_id as on_ground_picklist_id,\n", "#       pl.assigned_to_name as picker_id,\n", "#       (case\n", "#           when pl.state = 1 then 'Created'\n", "#           when pl.state = 2 then 'Assigned'\n", "#           when pl.state = 3 then 'In_progress'\n", "#           when pl.state = 4 then 'Completed'\n", "#           when pl.state = 10 then 'Billed'\n", "#       else null end) as state,\n", "#       w.entity_id as sto_id,\n", "#       (case when HOUR(pl.created_at) between 0 and 11 then 'Morning' else 'Evening' end) as shift,\n", "#       (pl.assigned_at + INTERVAL '330' MINUTE) AS picklist_assigned_at,\n", "#       (pl.picking_started_at + INTERVAL '330' MINUTE) AS picking_started_at_ist,\n", "#       (pll.pos_timestamp + INTERVAL '330' MINUTE) AS picking_completed_at_ist,\n", "#       TIMESTAMPDIFF(minute,pl.assigned_at,pl.picking_started_at) as picking_assigned_to_start_mins,\n", "#       TIMESTAMPDIFF(minute,pl.picking_started_at,pll.pos_timestamp) as picking_mins,\n", "#       max(pli.updated_at + INTERVAL '330' MINUTE) as max_updated_at,\n", "#       count(distinct pli.item_id) as SKUs,\n", "#       count(distinct case when pli.picked_quantity > 0 then pli.item_id else null end) as SKUs_picked,\n", "#       sum(pli.required_quantity) as quantity_required,\n", "#       sum(pli.picked_quantity) as quantity_picked,\n", "#       sum(dis_box) as dispatched_boxes,\n", "#       sum(box_c) as total_boxes\n", "#       FROM (select * from warehouse_location.warehouse_pick_list where id IS NOT NULL and outlet_id = {outlet_id}\n", "#             and created_at between '{start_time}' and '{end_time}') pl\n", "#       INNER JOIN (select * from warehouse_location.warehouse_wave where created_at between '{start_time}' and '{end_time}') w on w.id = pl.wave_id\n", "#       and w.entity_id in {*indents.sto_id.unique(),}\n", "#       LEFT JOIN (select sto_id, sum(dispatched_box_count) as dis_box, sum(box_count) as box_c\n", "#                  from warehouse_location.dispatch_indent where created_at between '{start_time}' and '{end_time}'\n", "#                  group by 1) d on d.sto_id = w.entity_id\n", "#       INNER JOIN (select * from warehouse_location.warehouse_pick_list_item\n", "#                   where created_at between '{start_time}' and '{end_time}') pli on pli.pick_list_id = pl.id\n", "#       LEFT JOIN (select * from warehouse_location.pick_list_log\n", "#                  where pos_timestamp between '{start_time}' and '{end_time}' and picklist_state = 4) pll ON pl.id = pll.pick_list_id\n", "#       GROUP BY 1,2,3,4,5,6,7,8,9,10,11\"\"\",\n", "#         whl,\n", "#     )\n", "#     picklist_data = pd.concat([picklist_data, dtemp], axis=0)\n", "#     print(\"Run time: \", time.time() - st)\n", "# print(picklist_data.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picks = picklist_data.picker_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pickers = pd.read_sql_query(\n", "    f\"\"\"select emp_id as picker_id, name as picker_name \n", "                                from lake_retail.warehouse_user where emp_id in {*picks,}\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet = pd.read_sql_query(\n", "    f\"\"\"select id as outlet_id, name as outlet_name \n", "                                from lake_retail.console_outlet where id in {*outlets,}\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_data = picklist_data.merge(pickers, on=[\"picker_id\"], how=\"left\")\n", "picklist_data = picklist_data.merge(outlet, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_data[\"picking_mins\"] = round(\n", "    (\n", "        picklist_data[\"picking_completed_at_ist\"]\n", "        - picklist_data[\"picking_started_at_ist\"]\n", "    ).dt.seconds\n", "    / 60,\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist_data.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Picker ID\t\n", "Picker Name\t\n", "Assigned Picklists\t\n", "In Progress picklists\t\n", "Line Items\t\n", "Line Items Picked\tAssigned Quantity\tPicked Quantity\tLocations Visited\tWorking mins"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pickers = sqldf(\n", "    \"\"\"select \n", "        outlet_id,\n", "        picker_id, \n", "        picker_name,\n", "        shift,\n", "        count(distinct picklist_id) as picklists_assigned,\n", "        count(distinct case when state = 'In_progress' then picklist_id else null end) as picklists_in_progress,\n", "        min(picklist_assigned_at) as first_pick_list_assigned_at,\n", "        max(max_updated_at) as latest_update_at,\n", "        sum(picking_mins) as picking_mins,\n", "        sum(SKUs) as line_items,\n", "        sum(SKUs_picked) as line_items_picked,\n", "        sum(quantity_required) as required_quantity,\n", "        sum(quantity_picked) as picked_quantity\n", "    from picklist_data\n", "    where picker_id != ''\n", "    group by 1,2,3,4\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def minute_to(td):\n", "    return td.seconds // 60"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pickers[\"working_minutes\"] = (\n", "    pd.to_datetime(pickers[\"latest_update_at\"])\n", "    - pd.to_datetime(pickers[\"first_pick_list_assigned_at\"])\n", ").apply(lambda x: minute_to(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pickers = pickers[\n", "    [\n", "        \"outlet_id\",\n", "        \"picker_id\",\n", "        \"picker_name\",\n", "        \"shift\",\n", "        \"picklists_assigned\",\n", "        \"picklists_in_progress\",\n", "        \"line_items\",\n", "        \"line_items_picked\",\n", "        \"required_quantity\",\n", "        \"picked_quantity\",\n", "        \"first_pick_list_assigned_at\",\n", "        \"latest_update_at\",\n", "        \"picking_mins\",\n", "        \"working_minutes\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(pickers, \"1veeWnrCpvSZvEvZf2IwXiEC8RNWBU0KYYqvzC3-9-Lk\", \"pickers\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pickers.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hourly Picking Trends "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time, end_time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# picklist_data['zone_identifier'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# picklist_data[picklist_data['zone_identifier'].fillna(0).str.startswith(\"SPEC\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# picking = pd.read_sql_query(f\"\"\"\n", "#       SELECT\n", "#           pl.outlet_id,\n", "#           pl.assigned_to_name as picker_id,\n", "#           HOUR(pli.updated_at + INTERVAL '330' MINUTE) as HOUR,\n", "#           sum(pli.picked_quantity) as picked_qty\n", "#       FROM (select * from warehouse_location.warehouse_pick_list where id IS NOT NULL\n", "#             and created_at between '{start_time}' and '{end_time}') pl\n", "#       INNER JOIN (select * from warehouse_location.warehouse_wave where created_at between '{start_time}' and '{end_time}') w on w.id = pl.wave_id\n", "#       and w.entity_id in {*indents.sto_id.unique(),}\n", "#       INNER JOIN (select * from warehouse_location.warehouse_pick_list_item\n", "#                   where created_at between '{start_time}' and '{end_time}') pli on pli.pick_list_id = pl.id\"\"\", whl)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}