{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# NTE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_raw_data = \"\"\"WITH product_expiry_details AS \n", "(SELECT \n", "outlet.name as \"outlet_name\",\n", "outlet.id as \"outlet_id\",\n", "p.item_id as \"item_id\",\n", "r.upc_id as \"UPC\",\n", "r.variant_id as \"variant_id\",\n", "p.name as \"product_name\",\n", "p.variant_uom_text as \"description\",\n", "p.variant_mrp as \"mrp\",\n", "lp.landing_price as \"landing_price\",\n", "pb.name as \"brand_name\",\n", "sum(r.quantity) as \"quantity\",\n", "r.expiry_date as \"expiry_date\",\n", "r.manufacture_date as \"manufacturer_date\",\n", "    (CASE\n", "        WHEN r.manufacture_date is not null THEN datediff(day,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(day,-p.outward_guidelines,dateadd(day,p.shelf_life,r.manufacture_date)))\n", "        ELSE datediff(day,date(convert_timezone('Asia/Kolkata',GETDATE())),dateadd(day,-p.outward_guidelines,r.expiry_date))\n", "    END) AS \"NTE_in_days\",\n", "datediff(day,CURRENT_DATE,r.expiry_date) as \"days_to_expire\",\n", "case when p.outlet_type=1 then 'FnV' when  p.outlet_type=2 then 'Groc.' else 'other' end as item_type,\n", "    p.shelf_life as product_shelf_life,\n", "    (CASE\n", "        WHEN r.manufacture_date is not null THEN 100-((datediff(day,r.manufacture_date,DATE(convert_timezone('Asia/Kolkata',r.created_at)))::float/p.shelf_life)*100)\n", "        ELSE (datediff(day,DATE(convert_timezone('Asia/Kolkata',r.created_at)),r.expiry_date)::float/p.shelf_life)*100\n", "    END) as \"perc_shelf_life_remaining\"\n", "FROM lake_reports.reports_variant_details_snapshot r\n", "INNER JOIN\n", "lake_rpc.product_product p ON p.variant_id=r.variant_id\n", "INNER JOIN\n", "lake_retail.console_outlet outlet on outlet.id=r.outlet_id\n", "INNER JOIN\n", "lake_ims.ims_inventory_landing_price lp ON lp.variant_id=r.variant_id AND lp.outlet_id=r.outlet_id\n", "INNER JOIN\n", "lake_rpc.product_brand pb on pb.id=p.brand_id\n", "WHERE \n", "--r.outlet_id=100 and\n", "DATE(r.created_at)=DATE(CURRENT_DATE) AND outlet.business_type_id <> 9\n", "and r.expiry_date < (dateadd(day,120,current_date)) and p.active=1 and p.skip_expiry=0\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18\n", "ORDER BY outlet.name,datediff(day,r.expiry_date,CURRENT_DATE) asc),\n", "product_category as (\n", "SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "CategoryFinal as (\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL   \n", "),\n", "item_location as\n", "(select item_id,variant_id,outlet_id,batch, sum(quantity) as quantity,count(location_name) as location_cnt,\n", "listagg(location_name,', ')\n", "within group (order by location_id,location_name) as locations\n", "from \n", "(\n", "select wil.item_id,wil.variant_id,wil.location_id,wil.quantity,wil.capacity,wil.batch::date,\n", "wsl.location_name,wsl.warehouse_id,wsl.shelf_id,om.outlet_id\n", "from lake_warehouse_location.warehouse_item_location wil\n", "left join lake_warehouse_location.warehouse_storage_location wsl on wil.location_id=wsl.id\n", "left join lake_warehouse_location.warehouse_outlet_mapping om on om.warehouse_id=wsl.warehouse_id\n", "where wil.quantity>0 \n", ")\n", "group by 1,2,3,4\n", "),\n", "snorlax as \n", "(\n", "select distinct wom.cloud_store_id as ds_outlet,\n", "--dwc.date as delivery_date,\n", "dwc.item_id,\n", "sum(dwc.consumption)/30.0 as final_cpd\n", "from\n", "(select outlet_id,item_id,\"date\",consumption from lake_snorlax.date_wise_consumption where\n", "\"date\" between current_date-30\n", "and current_date-1) dwc\n", "left join lake_retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join lake_retail.console_outlet ro on ro.id=dwc.outlet_id\n", "group by 1,2\n", "),\n", "snorlax_3d as \n", "(\n", "select distinct wom.cloud_store_id as ds_outlet,\n", "--dwc.date as delivery_date,\n", "dwc.item_id,\n", "sum(dwc.consumption)/3.0 as final_cpd\n", "from\n", "(select outlet_id,item_id,\"date\",consumption from lake_snorlax.date_wise_consumption where\n", "\"date\" between current_date-3\n", "and current_date-1) dwc\n", "left join lake_retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join lake_retail.console_outlet ro on ro.id=dwc.outlet_id\n", "group by 1,2\n", "),\n", "snorlax_pred as \n", "(\n", "select distinct wom.cloud_store_id as ds_outlet,\n", "--dwc.date as delivery_date,\n", "dwc.item_id,\n", "sum(dwc.consumption)/30.0 as final_cpd\n", "from\n", "(select outlet_id,item_id,\"date\",consumption from lake_snorlax.date_wise_consumption where\n", "\"date\" between current_date\n", "and current_date+29) dwc\n", "left join lake_retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join lake_retail.console_outlet ro on ro.id=dwc.outlet_id\n", "group by 1,2\n", ")\n", "SELECT\n", "distinct\n", "current_date as data_update_date,\n", "ped.outlet_id,\n", "ped.outlet_name,\n", "fc.id as facility_id,\n", "fc.name as facility,\n", "ped.item_id,\n", "cf.product_id,\n", "ped.variant_id,\n", "ped.product_name,\n", "ped.description,\n", "ped.mrp,\n", "round(ped.landing_price,2) as landing_price,\n", "cf.L0,\n", "cf.L1,\n", "cf.L2,\n", "ped.brand_name,\n", "ped.manufacturer_date,\n", "ped.expiry_date,\n", "round(ped.perc_shelf_life_remaining,1) as perc_shelf_life_remaining,\n", "ped.product_shelf_life,\n", "ped.nte_in_days,\n", "round(ped.quantity*ped.landing_price,2) as value,\n", "ped.quantity,\n", "round(cpd.final_cpd,2) as cpd,\n", "round(cpd_3d.final_cpd,2) as cpd_3d,\n", "round(cpd_pred.final_cpd,2) as pred_cpd,\n", "null as  \"Remarks\",\n", "null as \"Done?\"\n", "from  \n", "product_expiry_details ped left join CategoryFinal cf on cf.item_id=ped.item_id \n", "left join snorlax cpd ON cpd.item_id=ped.item_id and cpd.ds_outlet=ped.outlet_id\n", "left join snorlax_pred cpd_pred ON cpd_pred.item_id=ped.item_id and cpd_pred.ds_outlet=ped.outlet_id\n", "left join snorlax_3d cpd_3d ON cpd_3d.item_id=ped.item_id and cpd_3d.ds_outlet=ped.outlet_id\n", "left join lake_retail.console_outlet co on co.id=ped.outlet_id\n", "left join lake_crates.facility fc on fc.id=co.facility_id \n", "where item_type!='FnV'\n", "and ped.outlet_id not in (451,452,969,1044)\n", "-- and outlet_name not like '%%ES%%'\n", "and nte_in_days < 7 and ped.perc_shelf_life_remaining >= 10 and (ped.product_shelf_life) >= abs(nte_in_days)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_data = pd.read_sql_query(sql=query_raw_data, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_data1 = nte_data[\n", "    [\n", "        \"data_update_date\",\n", "        \"facility_id\",\n", "        \"facility\",\n", "        \"item_id\",\n", "        \"variant_id\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"description\",\n", "        \"mrp\",\n", "        \"landing_price\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand_name\",\n", "        \"expiry_date\",\n", "        \"perc_shelf_life_remaining\",\n", "        \"product_shelf_life\",\n", "        \"nte_in_days\",\n", "        \"quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_data1[\"Liquidation Realisation Price\"] = nte_data1[\"landing_price\"] * 0.25"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_data1 = nte_data1.sort_values(by=[\"nte_in_days\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_data1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_summ = (\n", "    nte_data1.groupby([\"facility_id\", \"facility\"])\n", "    .agg({\"item_id\": \"nunique\", \"quantity\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_summ = nte_summ.rename(columns={\"item_id\": \"Skus_count\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_summ = nte_summ.sort_values(by=[\"quantity\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nte_summ.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(nte_data1, \"1OAYBCBZJ_qvP430IZiujUQfVCUZzk6qTlJHwEEdNEls\", \"NTE\")\n", "except:\n", "    print(\"failed_updating_sheet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    pb.to_sheets(nte_summ, \"1OAYBCBZJ_qvP430IZiujUQfVCUZzk6qTlJHwEEdNEls\", \"NTE_Summ\")\n", "except:\n", "    print(\"failed_updating_summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Excess inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_qu = \"\"\"\n", "with snorlax_pred as \n", "(\n", "(\n", "select fc.id as facility_id,dwc.item_id,sum(dwc.consumption) as consump from lake_snorlax.date_wise_consumption dwc\n", "left join lake_retail.console_outlet co on co.id = outlet_id\n", "left join lake_crates.facility fc on fc.id = co.facility_id\n", "where \"date\" between current_date and current_date+29 and co.business_type_id not in (7,8)\n", "group by 1,2\n", ")\n", "union\n", "(\n", "select fc.id as facility_id,dwc.item_id,sum(dwc.consumption) as consump from lake_snorlax.date_wise_consumption dwc\n", "left join lake_retail.console_outlet co on co.id = outlet_id\n", "left join lake_crates.facility fc on fc.id = co.facility_id\n", "where \"date\" between current_date and current_date+14 and co.business_type_id in (7,8)\n", "group by 1,2\n", ")\n", "),\n", "product_category as (\n", "SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "),\n", "TEA_tagging as \n", "(SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'),\n", "CategoryFinal as (\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL   \n", ")\n", "select fc.id as facility_id,fc.name as facility,ii.outlet_id,cast(pp.item_id as int) as item_id,cf.product_id,ii.variant_id,cf.L0,cf.L1,cf.L2,pp.name as product_name,ii.quantity,sp.consump as pred_consumption,pp.variant_mrp as mrp,lp.landing_price\n", "from lake_ims.ims_inventory ii \n", "left join lake_rpc.product_product pp on pp.variant_id = ii.variant_id\n", "left join lake_retail.console_outlet co on co.id = ii.outlet_id\n", "left join lake_crates.facility fc on fc.id = co.facility_id\n", "left join snorlax_pred sp on sp.facility_id  = fc.id and sp.item_id = pp.item_id\n", "left join CategoryFinal cf on cf.item_id = pp.item_id\n", "left join TEA_tagging tea on tea.item_id = pp.item_id and tea.frontend_facility_id = fc.id\n", "left join lake_ims.ims_inventory_landing_price lp ON lp.variant_id=ii.variant_id AND lp.outlet_id=ii.outlet_id\n", "where ii.active = 1 and ii.quantity > 0 and pp.outlet_type != 1 and fc.id != 39\n", "and ((lp.landing_price <= 50) or (pp.item_id in (10062321,10062320,10073557,10063442,10073558,10063250,10063253,10064665,10063254)))\n", "and fc.id in (1,3,12,15,22,24,26,29,30,34,42,43,48,49,63,92,140,149,268,299,\n", "50,56,116,212,213,236,264,269,142,\n", "136,137,152,178,179,184,177,194,202,214,225,232,233,240,242,259,241,262,263,272,274,275,281,301,315,316,318,323,\n", "327,\n", "328,\n", "338,\n", "360,\n", "369,\n", "374,\n", "389,\n", "228,201,161,135,319,197,285,219,237,227,223,271,238,246,209,233,232,178,272,281,136,137,225,275,152,202,177,184,214,259,262,263,242,241,179,301,240)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = pd.read_sql_query(sql=inv_qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["block_quantity = \"\"\"SELECT outlet_id,item_id, SUM(Blocked_Quantity) as Blocked_Quantity\n", "FROM (\n", "select outlet_id,item_id,date(scheduled_at + interval '5.5 Hours') as delivery_date, quantity as Blocked_Quantity\n", "FROM\n", "(SELECT id, order_id, outlet as outlet_id, scheduled_at\n", "FROM lake_ims.ims_order_details\n", "where status_id  = 1) order_details\n", "LEFT JOIN\n", "lake_ims.ims_order_items as order_items\n", "ON order_details.id = order_items.order_details_id)\n", "GROUP BY 1,2\"\"\"\n", "block_quantity_data = pd.read_sql_query(sql=block_quantity, con=redshift)\n", "\n", "block_quantity_data = block_quantity_data[\n", "    (block_quantity_data[\"item_id\"] > 0) & (block_quantity_data[\"blocked_quantity\"] > 0)\n", "]\n", "\n", "block_quantity_data[\"item_id\"] = block_quantity_data[\"item_id\"].astype(int)\n", "block_quantity_data[\"blocked_quantity\"] = block_quantity_data[\n", "    \"blocked_quantity\"\n", "].astype(int)\n", "\n", "inv_data = inv_data.merge(block_quantity_data, how=\"left\", on=[\"outlet_id\", \"item_id\"])\n", "\n", "inv_data[\"blocked_quantity\"] = inv_data[\"blocked_quantity\"].fillna(0).astype(int)\n", "\n", "inv_data[\"quantity\"] = inv_data[\"quantity\"] - inv_data[\"blocked_quantity\"]\n", "\n", "inv_data = inv_data[inv_data[\"quantity\"] > 0]\n", "\n", "inv_data.shape\n", "\n", "inv_data = inv_data.drop(columns=[\"blocked_quantity\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data[\n", "    inv_data[\"item_id\"].isin(\n", "        (\n", "            10062321,\n", "            10062320,\n", "            10073557,\n", "            10063442,\n", "            10073558,\n", "            10063250,\n", "            10063253,\n", "            10064665,\n", "            10063254,\n", "        )\n", "    )\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data[inv_data.pred_consumption.notnull()].shape, inv_data[\n", "    inv_data.pred_consumption.isnull()\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nocpd = inv_data[inv_data.pred_consumption.isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data[(inv_data[\"facility_id\"] == 264) & (inv_data[\"item_id\"] == 10042687)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv = inv_data[(inv_data[\"pred_consumption\"].notnull()) & (inv_data[\"pred_consumption\"]<inv_data[\"quantity\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea = pd.read_sql_query(sql=qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea[(tea[\"backend_facility_id\"] == 264) & (tea[\"item_id\"] == 10042687)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = pd.merge(\n", "    nocpd,\n", "    tea,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"backend_facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a[(a[\"facility_id\"] == 264) & (a[\"item_id\"] == 10042687)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = pd.merge(\n", "    a,\n", "    inv_data[[\"facility_id\", \"item_id\", \"pred_consumption\"]],\n", "    how=\"left\",\n", "    left_on=[\"frontend_facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b[(b[\"facility_id_x\"] == 264) & (b[\"item_id\"] == 10042687)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c = (\n", "    b.groupby([\"facility_id_x\", \"item_id\"])\n", "    .agg({\"pred_consumption_y\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = c[c[\"pred_consumption_y\"] == 0]\n", "e = c[c[\"pred_consumption_y\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f = pd.merge(\n", "    inv_data,\n", "    e,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id_x\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data_quan[(data_quan[\"item_id\"]==10011645)&(data_quan[\"facility_id\"]==1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f[(f[\"facility_id\"] == 1) & (f[\"item_id\"] == 10011645)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = f[(f[\"pred_consumption\"].notnull()) | (f[\"pred_consumption_y\"].notnull())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g[\"pred_consumption\"] = g[[\"pred_consumption\", \"pred_consumption_y\"]].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g[g[\"pred_consumption\"].isnull()].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = g.drop(columns=[\"facility_id_x\", \"pred_consumption_y\", \"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g[g[\"pred_consumption\"] > 0].shape, g[g[\"pred_consumption\"] == 0].shape, g[\n", "    g[\"pred_consumption\"].isnull()\n", "].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g[\"excess\"] = g[\"quantity\"] - g[\"pred_consumption\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g[g[\"excess\"] >= 10].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["h = g[g[\"excess\"] >= 10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["h.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(h, \"1OAYBCBZJ_qvP430IZiujUQfVCUZzk6qTlJHwEEdNEls\", \"Excess\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = (\n", "    h.groupby([\"facility_id\", \"facility\"])\n", "    .agg({\"item_id\": \"nunique\", \"excess\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i[\"excess\"] = round(i[\"excess\"], 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = i.rename(columns={\"item_id\": \"skus_count\", \"excess\": \"excess_qty\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = i.sort_values(by=[\"excess_qty\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(i, \"1OAYBCBZJ_qvP430IZiujUQfVCUZzk6qTlJHwEEdNEls\", \"Excess_Summ\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# April Freebies tracker"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reading combos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo = pb.from_sheets(\"1ffr25_gqckulPx0fIBU9jJTGkKF12H7ml7b8ycBM_N0\", \"Combos-Excess\")\n", "combo_common = pb.from_sheets(\n", "    \"1ffr25_gqckulPx0fIBU9jJTGkKF12H7ml7b8ycBM_N0\", \"Key April Combos\"\n", ")\n", "combo_G4 = pb.from_sheets(\n", "    \"1ffr25_gqckulPx0fIBU9jJTGkKF12H7ml7b8ycBM_N0\", \"Combos G4&G4-B\"\n", ")\n", "combo_Dairy_G = pb.from_sheets(\n", "    \"1ffr25_gqckulPx0fIBU9jJTGkKF12H7ml7b8ycBM_N0\", \"Combos - Dairy Gurgaon\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_excess = h[[\"facility_id\", \"facility\", \"item_id\", \"excess\"]]\n", "data_quan = g[[\"facility_id\", \"facility\", \"item_id\", \"quantity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_excess = (\n", "    data_excess.groupby([\"facility_id\", \"facility\", \"item_id\"])\n", "    .agg({\"excess\": sum})\n", "    .reset_index()\n", ")\n", "data_quan = (\n", "    data_quan.groupby([\"facility_id\", \"facility\", \"item_id\"])\n", "    .agg({\"quantity\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cart = \"\"\"\n", "with data as (\n", "select * from ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "               from Merchant_forecast_carts)\n", "where rnk =1 and date(f_date) between '2021-04-01' and '2021-04-30')\n", "select\n", "facility_id,facility,\n", "round(sum(forecast_carts)/30.0,0) as forecast_carts\n", "from data\n", "group by 1,2\n", "order by 1,2\n", "\"\"\"\n", "cart_data = pd.read_sql_query(sql=cart, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = cart_data[cart_data[\"facility_id\"].isin(data_excess.facility_id.unique())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# combo excess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo.columns = combo.iloc[0]\n", "combo = combo.drop(combo.index[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo = combo[~(combo[\"item_id\"] == \"\")]\n", "combo[\"item_id\"] = combo[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "combo = combo.iloc[0:1827, :]\n", "combo.loc[combo[\"Offer pid\"] == \"\", \"Offer pid\"] = np.NaN\n", "combo[\"Offer pid\"] = combo[\"Offer pid\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo = combo[[\"Combo Sr no\", \"Offer pid\", \"item_id\"]]\n", "combo.loc[combo[\"Combo Sr no\"] == \"\", \"Combo Sr no\"] = np.NaN\n", "combo[\"Combo Sr no\"] = combo[\"Combo Sr no\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facilities = [\n", "    1,\n", "    3,\n", "    12,\n", "    15,\n", "    22,\n", "    24,\n", "    26,\n", "    29,\n", "    30,\n", "    34,\n", "    42,\n", "    43,\n", "    48,\n", "    49,\n", "    63,\n", "    92,\n", "    140,\n", "    149,\n", "    268,\n", "    299,\n", "    50,\n", "    56,\n", "    116,\n", "    212,\n", "    213,\n", "    236,\n", "    264,\n", "    269,\n", "    142,\n", "    136,\n", "    137,\n", "    152,\n", "    178,\n", "    179,\n", "    184,\n", "    177,\n", "    194,\n", "    202,\n", "    214,\n", "    225,\n", "    232,\n", "    233,\n", "    240,\n", "    242,\n", "    259,\n", "    241,\n", "    262,\n", "    263,\n", "    272,\n", "    274,\n", "    275,\n", "    281,\n", "    301,\n", "    315,\n", "    316,\n", "    318,\n", "    323,\n", "    327,\n", "    328,\n", "    338,\n", "    360,\n", "    369,\n", "    374,\n", "    389,\n", "    228,\n", "    201,\n", "    161,\n", "    135,\n", "    319,\n", "    197,\n", "    285,\n", "    219,\n", "    237,\n", "    227,\n", "    223,\n", "    271,\n", "    238,\n", "    246,\n", "    209,\n", "    233,\n", "    232,\n", "    178,\n", "    272,\n", "    281,\n", "    136,\n", "    137,\n", "    225,\n", "    275,\n", "    152,\n", "    202,\n", "    177,\n", "    184,\n", "    214,\n", "    259,\n", "    262,\n", "    263,\n", "    242,\n", "    241,\n", "    179,\n", "    301,\n", "    240,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facilities = np.unique(facilities)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac = pd.DataFrame({\"facility_id\": facilities, \"tmp\": 1})\n", "x = (\n", "    combo[[\"Combo Sr no\", \"Offer pid\"]]\n", "    .drop_duplicates()\n", "    .assign(tmp=1)\n", "    .merge(fac, on=\"tmp\")\n", "    .drop(\"tmp\", 1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_ = x.merge(combo[[\"Offer pid\", \"item_id\"]], on=[\"Offer pid\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da = combo_.merge(\n", "    data_quan[[\"facility_id\", \"item_id\", \"quantity\"]],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "dat = da.merge(\n", "    data_excess[[\"facility_id\", \"item_id\", \"excess\"]],\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat = dat.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g = (\n", "    dat.groupby([\"facility_id\", \"Offer pid\"])\n", "    .agg({\"quantity\": \"min\", \"excess\": \"min\"})\n", "    .reset_index()\n", ")\n", "dat_g = dat_g[(dat_g[\"excess\"] > 0) | (dat_g[\"excess\"] > 0)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# key april combos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_common.columns = combo_common.iloc[0]\n", "combo_common = combo_common.drop(combo_common.index[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_common = combo_common[[\"Item id\", \"Offer pid\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "combo_common.loc[combo_common[\"Offer pid\"] == \"\", \"Offer pid\"] = np.NaN\n", "combo_common[\"Offer pid\"] = combo_common[\"Offer pid\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_common[\"Item id\"] = combo_common[\"Item id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = (\n", "    combo_common[[\"Offer pid\"]]\n", "    .drop_duplicates()\n", "    .assign(tmp=1)\n", "    .merge(fac, on=\"tmp\")\n", "    .drop(\"tmp\", 1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo__ = y.merge(combo_common[[\"Offer pid\", \"Item id\"]], on=[\"Offer pid\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da1 = combo__.merge(\n", "    data_quan,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"Item id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "dat1 = da1.merge(\n", "    data_excess,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat1 = dat1.fillna(0)[[\"facility_id\", \"Offer pid\", \"Item id\", \"quantity\", \"excess\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g1 = (\n", "    dat1.groupby([\"facility_id\", \"Offer pid\"])\n", "    .agg({\"quantity\": \"min\", \"excess\": \"min\"})\n", "    .reset_index()\n", ")\n", "dat_g1 = dat_g1[(dat_g1[\"excess\"] > 0) | (dat_g1[\"quantity\"] > 0)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Combos G4/G4-B"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_G4.columns = combo_G4.iloc[0]\n", "combo_G4 = combo_G4.drop(combo_G4.index[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_g = fac.loc[\n", "    fac[\"facility_id\"].isin(\n", "        [29, 49, 197, 247, 223, 271, 237, 204, 227, 319, 209, 238, 285, 219, 246, 370]\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "combo_G4.loc[combo_G4[\"Offer pid\"] == \"\", \"Offer pid\"] = np.NaN\n", "combo_G4[\"Offer pid\"] = combo_G4[\"Offer pid\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_G4[\"item_id\"] = combo_G4[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_G4 = combo_G4[[\"Offer pid\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["z = (\n", "    combo_G4[[\"Offer pid\"]]\n", "    .drop_duplicates()\n", "    .assign(tmp=1)\n", "    .merge(fac_g, on=\"tmp\")\n", "    .drop(\"tmp\", 1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_g_ = z.merge(combo_G4[[\"Offer pid\", \"item_id\"]], on=[\"Offer pid\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da2 = combo_g_.merge(\n", "    data_quan,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "dat2 = da2.merge(\n", "    data_excess,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat2 = dat2[[\"facility_id\", \"Offer pid\", \"item_id\", \"quantity\", \"excess\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat2 = dat2.fillna(0)\n", "dat_g2 = (\n", "    dat2.groupby([\"facility_id\", \"Offer pid\"])\n", "    .agg({\"quantity\": \"min\", \"excess\": \"min\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g2 = dat_g2[(dat_g2[\"excess\"] > 0) | (dat_g2[\"quantity\"] > 0)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g2.facility_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Combos Dairy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_Dairy_G = combo_Dairy_G.rename(columns={\"\": \"combo_name\"})\n", "combo_Dairy_G = combo_Dairy_G.iloc[:, 0:15]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_Dairy_G.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "combo_Dairy_G.loc[combo_Dairy_G[\"Offer pid\"] == \"\", \"Offer pid\"] = np.NaN\n", "combo_Dairy_G[\"Offer pid\"] = combo_Dairy_G[\"Offer pid\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_Dairy_G = combo_Dairy_G[[\"Offer pid\", \"Item_Id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_dairy = [135, 197, 209, 219, 223, 227, 237, 246, 271, 285, 319, 238]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_dairy = pd.DataFrame({\"facility_id\": fac_dairy, \"tmp\": 1})\n", "z = (\n", "    combo_Dairy_G[[\"Offer pid\"]]\n", "    .drop_duplicates()\n", "    .assign(tmp=1)\n", "    .merge(fac_dairy, on=\"tmp\")\n", "    .drop(\"tmp\", 1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_Dairy_G = z.merge(combo_Dairy_G[[\"Offer pid\", \"Item_Id\"]], on=[\"Offer pid\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combo_Dairy_G[\"Item_Id\"] = combo_Dairy_G[\"Item_Id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da3 = combo_Dairy_G.merge(\n", "    data_quan,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"Item_Id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "dat3 = da3.merge(\n", "    data_excess,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat3 = dat3.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat3 = dat3[[\"facility_id\", \"Offer pid\", \"Item_Id\", \"quantity\", \"excess\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g3 = (\n", "    dat3.groupby([\"facility_id\", \"Offer pid\"])\n", "    .agg({\"quantity\": \"min\", \"excess\": \"min\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g3 = dat_g3[(dat_g3[\"excess\"] > 0) | (dat_g3[\"quantity\"] > 0)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Combos Ugadi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combos_ugadi = pb.from_sheets(\n", "    \"1ffr25_gqckulPx0fIBU9jJTGkKF12H7ml7b8ycBM_N0\", \"Combos Ugadi\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "combos_ugadi.loc[combos_ugadi[\"Offer pid\"] == \"\", \"Offer pid\"] = np.NaN\n", "combos_ugadi[\"Offer pid\"] = combos_ugadi[\"Offer pid\"].fillna(method=\"ffill\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combos_ugadi = combos_ugadi[[\"Offer pid\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combos_ugadi[\"item_id\"] = combos_ugadi[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["z = (\n", "    combos_ugadi[[\"Offer pid\"]]\n", "    .drop_duplicates()\n", "    .assign(tmp=1)\n", "    .merge(fac, on=\"tmp\")\n", "    .drop(\"tmp\", 1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combos_ugadi = z.merge(combos_ugadi[[\"Offer pid\", \"item_id\"]], on=[\"Offer pid\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da4 = combos_ugadi.merge(\n", "    data_quan,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")\n", "dat4 = da4.merge(\n", "    data_excess,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat4 = dat4.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat4 = dat4[[\"facility_id\", \"Offer pid\", \"item_id\", \"quantity\", \"excess\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g4 = (\n", "    dat4.groupby([\"facility_id\", \"Offer pid\"])\n", "    .agg({\"quantity\": \"min\", \"excess\": \"min\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g4 = dat_g4[(dat_g4[\"excess\"] > 0) | (dat_g4[\"quantity\"] > 0)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Combining dataframes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_g.shape, dat_g1.shape, dat_g2.shape, dat_g3.shape, dat_g4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_gs = (\n", "    dat_g.groupby([\"facility_id\"])\n", "    .agg({\"excess\": \"sum\", \"quantity\": \"sum\", \"Offer pid\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_gs1 = (\n", "    dat_g1.groupby([\"facility_id\"])\n", "    .agg({\"excess\": \"sum\", \"quantity\": \"sum\", \"Offer pid\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_gs2 = (\n", "    dat_g2.groupby([\"facility_id\"])\n", "    .agg({\"excess\": \"sum\", \"quantity\": \"sum\", \"Offer pid\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_gs3 = (\n", "    dat_g3.groupby([\"facility_id\"])\n", "    .agg({\"excess\": \"sum\", \"quantity\": \"sum\", \"Offer pid\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat_gs4 = (\n", "    dat_g4.groupby([\"facility_id\"])\n", "    .agg({\"excess\": \"sum\", \"quantity\": \"sum\", \"Offer pid\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"select id as facility_id, name as facility_name from lake_crates.facility where id in ({fac})\"\"\".format(\n", "    fac=\",\".join([str(x) for x in np.sort(facilities)])\n", ")\n", "final = pd.read_sql_query(sql=qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.sort_values(by=[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"facility_id\"] = final[\"facility_id\"].astype(int)\n", "fin[\"facility_id\"] = fin[\"facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["off = pb.from_sheets(\"13A4iIKXeu6rdFWuoIgo1PzYhclHOLpeIl-wjGi7gmvc\", \"april_offline\")\n", "off.columns = off.iloc[0]\n", "off = off.drop(off.index[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["off = off[[\"Facility id\", \"per day carts\"]]\n", "off = off.rename(\n", "    columns={\"Facility id\": \"facility_id\", \"per day carts\": \"forecast_carts\"}\n", ")\n", "off = off[(off[\"facility_id\"].notnull()) & (off[\"facility_id\"] != \"\")]\n", "off[\"facility_id\"] = off[\"facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(\n", "    fin[[\"facility_id\", \"forecast_carts\"]], how=\"left\", on=[\"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(off, how=\"left\", on=[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.replace(r\"^\\s*$\", np.nan, regex=True)\n", "final[\"forecast_carts_x\"] = final[\"forecast_carts_x\"].fillna(0)\n", "final[\"forecast_carts_y\"] = final[\"forecast_carts_y\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"forecast_carts\"] = final[\"forecast_carts_x\"] + final[\"forecast_carts_y\"].astype(\n", "    float\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.drop([\"forecast_carts_x\", \"forecast_carts_y\"], 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Freebies reqt for 1 day\"] = final[\"forecast_carts\"] * 0.6 * 3\n", "final[\"Freebies reqt for 5 day\"] = final[\"forecast_carts\"] * 0.6 * 3 * 5\n", "final[\"Freebies reqt for April\"] = final[\"forecast_carts\"] * 0.6 * 3 * 30"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[final[\"forecast_carts\"] == 0].facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(\n", "    dat_gs, how=\"left\", left_on=[\"facility_id\"], right_on=[\"facility_id\"]\n", ")\n", "final = final.merge(dat_gs1, how=\"left\", on=[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.rename(\n", "    columns={\n", "        \"excess_x\": \"excess(combo)\",\n", "        \"quantity_x\": \"quantity(combo)\",\n", "        \"excess_y\": \"excess(key_combo)\",\n", "        \"quantity_y\": \"quantity(key_combo)\",\n", "        \"Offer pid_x\": \"offers(combo)\",\n", "        \"Offer pid_y\": \"offers(key_combo)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(dat_gs2, how=\"left\", on=[\"facility_id\"])\n", "final = final.merge(dat_gs3, how=\"left\", on=[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.rename(\n", "    columns={\n", "        \"excess_x\": \"excess(G4/G4B)\",\n", "        \"quantity_x\": \"quantity(G4/G4B)\",\n", "        \"excess_y\": \"excess(dairy_combo)\",\n", "        \"quantity_y\": \"quantity(dairy_combo)\",\n", "        \"Offer pid_x\": \"offers(G4/G4B)\",\n", "        \"Offer pid_y\": \"offers(dairy_combo)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.merge(dat_gs4, how=\"left\", on=[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.rename(\n", "    columns={\n", "        \"excess\": \"excess(ugadi)\",\n", "        \"quantity\": \"quantity(ugadi)\",\n", "        \"Offer pid\": \"offers(ugadi)\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Current Inventory\"] = (\n", "    final[\"quantity(combo)\"]\n", "    + final[\"quantity(key_combo)\"]\n", "    + final[\"quantity(G4/G4B)\"]\n", "    + final[\"quantity(dairy_combo)\"]\n", "    + final[\"quantity(ugadi)\"]\n", ")\n", "final[\"Excess Inventory\"] = (\n", "    final[\"excess(combo)\"]\n", "    + final[\"excess(key_combo)\"]\n", "    + final[\"excess(G4/G4B)\"]\n", "    + final[\"excess(dairy_combo)\"]\n", "    + final[\"excess(ugadi)\"]\n", ")\n", "final[\"Total Combos available\"] = (\n", "    final[\"offers(combo)\"]\n", "    + final[\"offers(key_combo)\"]\n", "    + final[\"offers(G4/G4B)\"]\n", "    + final[\"offers(dairy_combo)\"]\n", "    + final[\"offers(ugadi)\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\n", "    [\n", "        \"excess(combo)\",\n", "        \"quantity(combo)\",\n", "        \"excess(key_combo)\",\n", "        \"quantity(key_combo)\",\n", "        \"excess(G4/G4B)\",\n", "        \"quantity(G4/G4B)\",\n", "        \"excess(dairy_combo)\",\n", "        \"quantity(dairy_combo)\",\n", "        \"excess(ugadi)\",\n", "        \"quantity(ugadi)\",\n", "    ]\n", "] = (\n", "    final[\n", "        [\n", "            \"excess(combo)\",\n", "            \"quantity(combo)\",\n", "            \"excess(key_combo)\",\n", "            \"quantity(key_combo)\",\n", "            \"excess(G4/G4B)\",\n", "            \"quantity(G4/G4B)\",\n", "            \"excess(dairy_combo)\",\n", "            \"quantity(dairy_combo)\",\n", "            \"excess(ugadi)\",\n", "            \"quantity(ugadi)\",\n", "        ]\n", "    ]\n", "    * 3\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Current Inventory\"] = final[\"Current Inventory\"] * 3\n", "final[\"Excess Inventory\"] = final[\"Excess Inventory\"] * 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Days of survival (Excess)\"] = final[\"Excess Inventory\"] / (\n", "    final[\"Freebies reqt for 1 day\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Days of survival (Total inv)\"] = final[\"Current Inventory\"] / (\n", "    final[\"Freebies reqt for 1 day\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Excess Inventory\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Current Inventory\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final.to_excel(\"final.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final, \"1W4LwXCdnI9qRnGIEsM1AG8jTKEdfLZ_syksOUWAMgLQ\", \"Summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Raw Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat = dat[[\"facility_id\", \"Offer pid\", \"item_id\", \"quantity\", \"excess\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat1 = dat1.rename(columns={\"Item id\": \"item_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat3 = dat3.rename(columns={\"Item_Id\": \"item_id\", \"combo_name\": \"Offer pid\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat.shape, dat1.shape, dat2.shape, dat3.shape, dat.shape[0] + dat1.shape[\n", "    0\n", "] + dat2.shape[0] + dat3.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = pd.concat([dat, dat1, dat2, dat3, dat4], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"select id as facility_id, name as facility_name from lake_crates.facility where id in ({fac})\"\"\".format(\n", "    fac=\",\".join([str(x) for x in np.sort(facilities)])\n", ")\n", "fa = pd.read_sql_query(sql=qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = raw_data.merge(fa, on=[\"facility_id\"])[\n", "    [\"facility_id\", \"facility_name\", \"Offer pid\", \"item_id\", \"quantity\", \"excess\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# raw_data.to_excel(\"raw_data.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(raw_data, \"1W4LwXCdnI9qRnGIEsM1AG8jTKEdfLZ_syksOUWAMgLQ\", \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}