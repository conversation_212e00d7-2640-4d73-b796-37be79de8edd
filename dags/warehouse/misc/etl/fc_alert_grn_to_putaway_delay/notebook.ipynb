{"cells": [{"cell_type": "code", "execution_count": null, "id": "46c3e814-9487-4424-85cc-b33796670665", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import math\n", "import matplotlib.pyplot as plt\n", "import pytz\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "sql = \"\"\"with gr_info as (select co.facility_id,pg.item_id as item_id,max(grn_id) as latest_grn_id,sum(pg.quantity) as TotalGrnQty,\n", "max(pg.created_at+interval '5' hour + interval '30' minute) as GrnTime from lake_view_po.purchase_order po\n", "join lake_view_po.po_grn pg on pg.po_id=po.id\n", "join lake_view_retail.console_outlet co on co.id=po.outlet_id and co.facility_id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\n", "\n", "where  pg.created_at>=current_date - interval '1' day \n", "group by 1,2),\n", "\n", "putaway as (\n", " SELECT  co.facility_id,\n", "              item_id,\n", "              max(wpli.created_at+interval '5' hour + interval '30' minute) as Putlist_Creation_time,\n", "              max(wpli.updated_at+interval '5' hour + interval '30' minute) AS Putaway_time,\n", "              sum(quantity) as Putaway_Qty\n", "    FROM lake_view_warehouse_location.warehouse_put_list_item wpli\n", "     join lake_view_retail.console_outlet co on co.id = wpli.outlet_id and co.facility_id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\n", "    WHERE date(wpli.created_at)>=current_date- interval '1' day\n", "    AND item_state_id=4 \n", "    GROUP BY 1,2\n", "    ORDER BY 1,2\n", "    \n", ")\n", "\t\t\n", "select gr_info.facility_id,\n", "gr_info.item_id,\n", "gr_info.latest_grn_id,\n", "gr_info.GrnTime as last_grn_time,gr_info.TotalGrnQty,\n", "putaway.Putaway_time as putaway_time,\n", "putaway.Putaway_Qty \n", "\n", "from gr_info\n", "left join putaway on gr_info.facility_id=putaway.facility_id and gr_info.item_id=putaway.item_id and  putaway.Putaway_time>=gr_info.GrnTime\n", "\n", "where Putaway_Qty is null \"\"\"\n", "\n", "df = pd.read_sql_query(sql=sql, con=presto)\n", "# df=df.fillna(\"Put Away Not Done\")\n", "df[\"flag\"] = df.apply(\n", "    lambda df: \"Yes\"\n", "    if pd.isnull(df[\"putaway_qty\"])\n", "    and (\n", "        (\n", "            (datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30))\n", "            - pd.to_datetime(df[\"last_grn_time\"])\n", "        ).total_seconds()\n", "        / 60\n", "    )\n", "    > 480\n", "    else \"No\",\n", "    axis=1,\n", ")\n", "df = df[df[\"flag\"] == \"Yes\"]\n", "\n", "fac_df = pd.read_sql_query(\n", "    sql=\"\"\"select f.id as facility_id,name as facility_name from lake_crates.facility f\"\"\",\n", "    con=presto,\n", ")\n", "items_df = pd.read_sql_query(\n", "    sql=\"\"\"select item_id,name  from lake_view_rpc.item_details\"\"\", con=presto\n", ")\n", "df = df.merge(fac_df, on=[\"facility_id\"], how=\"inner\").merge(\n", "    items_df, on=[\"item_id\"], how=\"inner\"\n", ")\n", "df = df[\n", "    [\n", "        \"facility_name\",\n", "        \"facility_id\",\n", "        \"name\",\n", "        \"item_id\",\n", "        \"latest_grn_id\",\n", "        \"last_grn_time\",\n", "        \"totalgrnqty\",\n", "    ]\n", "]\n", "df[\"PutAway Status\"] = \"Pending\"\n", "df = df.rename(\n", "    columns={\n", "        \"facility_name\": \"Facility Name\",\n", "        \"facility_id\": \"Facility ID\",\n", "        \"item_id\": \"Item ID\",\n", "        \"latest_grn_id\": \"Last Grn ID\",\n", "        \"last_grn_time\": \"Last Grn Time\",\n", "        \"totalgrnqty\": \"Total Grn Qty\",\n", "        \"name\": \"<PERSON><PERSON>\",\n", "    }\n", ")\n", "\n", "\n", "pb.to_sheets(\n", "    df,\n", "    \"1CwHPwZPcL523lLvmi5oH_NPbDTOI7NxV5ldQv8b18Xs\",\n", "    \"Raw-Data\",\n", "    service_account=\"service_account\",\n", ")\n", "\n", "cluster_sql = \"\"\"select f.id as facility_id,\n", "\n", "case when id in (264,1320,555) then 'C1'\n", "when id in (92,554,24,517) then 'C2'\n", "when id in (43,1873,1876) then 'C3'\n", "when id in (1,513,603,1206,1872) then 'C4' ELSE 'NOT KNOWN' END AS cluster\n", "from lake_crates.facility f\n", "where id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\"\"\"\n", "\n", "\n", "cluster_df = pd.read_sql_query(sql=cluster_sql, con=presto)\n", "cluster_df = cluster_df.rename(columns={\"facility_id\": \"Facility ID\"})\n", "\n", "df = df.merge(cluster_df, on=[\"Facility ID\"], how=\"inner\")\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\"Facility Name\", \"Facility ID\", \"Total Grn Qty\", \"No of Items\"],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "def slack_df(temp_df, cluster):\n", "    slack_df = (\n", "        temp_df.groupby([\"Facility Name\", \"Facility ID\"])\n", "        .agg({\"Total Grn Qty\": \"sum\", \"Item ID\": \"count\"})\n", "        .reset_index()\n", "    )\n", "    slack_df = slack_df.rename(columns={\"Item ID\": \"No of Items\"})\n", "\n", "    if cluster == \"C1\":\n", "        channel = \"blinkit-fc-c1-alerts\"\n", "    elif cluster == \"C2\":\n", "        channel = \"blinkit-fc-c2-alerts\"\n", "    elif cluster == \"C3\":\n", "        channel = \"blinkit-fc-c3-alerts\"\n", "    elif cluster == \"C4\":\n", "        channel = \"blinkit-fc-c4-alerts\"\n", "\n", "    # channel='test-channel-category'\n", "\n", "    if len(slack_df) > 0:\n", "        fig, ax = render_mpl_table(slack_df, header_columns=0)\n", "        fig.savefig(\"Grn_To_PutAway_Processing_Delay.png\")\n", "\n", "    if len(slack_df) > 0:\n", "        now = (datetime.now() + <PERSON><PERSON>ta(hours=5, minutes=30)).strftime(\n", "            \"%d %B, %Y %H:%M:%S\"\n", "        )\n", "        text = f\"\"\"*GRN To PutAway Processing Delay Alert  - {now}* \\n This <https://docs.google.com/spreadsheets/d/1CwHPwZPcL523lLvmi5oH_NPbDTOI7NxV5ldQv8b18Xs/edit#gid=0|link> has pending and delayed raw data..\"\"\"\n", "        pb.send_slack_message(\n", "            channel, text, files=[\"Grn_To_PutAway_Processing_Delay.png\"]\n", "        )\n", "\n", "\n", "for cluster in df[\"cluster\"].unique().tolist():\n", "    temp_df = df[df[\"cluster\"] == cluster]\n", "    slack_df(temp_df, cluster)"]}, {"cell_type": "code", "execution_count": null, "id": "f36639ee-5ac9-4021-abc4-8c7a8e829988", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f75692e0-3d19-42cb-abe7-bedb1699e88d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d5f39a06-d6cf-4513-bab8-d9348c4af4e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}