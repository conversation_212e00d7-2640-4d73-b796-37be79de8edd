{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pickle\n", "from collections import defaultdict\n", "import json\n", "from ast import literal_eval\n", "import numpy as np\n", "import math\n", "from catboost import CatBoostRegressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "utc_now = datetime.utcnow()\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")\n", "start_date = utc_now - <PERSON><PERSON><PERSON>(minutes=120)\n", "start_date = start_date.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "end_date = utc_now.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from time import strftime\n", "\n", "start_date = utc_now - <PERSON><PERSON><PERSON>(minutes=120)\n", "start_date = start_date.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "end_date = utc_now.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "dag_id = \"warehouse_misc_etl_picking_effort_estimation_v6\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename_iod = \"/tmp/picking_time_ideal_v3.csv\"\n", "local_filename_model = \"/tmp/cat_boostmodel.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_filename_iod = \"/eep/warehouse/picking/data/picking_time_ideal_cat_v3.csv\"\n", "cloud_filename_model = \"/eep/warehouse/picking/prod/model/catboost_model_v3.1.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "pb.from_s3(bucket_name, cloud_filename_iod, local_filename_iod)\n", "pb.from_s3(bucket_name, cloud_filename_model, local_filename_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_v3 = pd.read_csv(local_filename_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import CatBoost\n", "\n", "model = CatBoostRegressor()\n", "model.load_model(local_filename_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oids = list(picking_time_ideal_v3.outlet_id.unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Snippet to get the minimum of start_time\n", "\n", "- The code snippet queries `metrics.picking_effort_estimation_project_v2` to get the last updated time and chooses the minumum time from the last updated time and the current time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_last_updated():\n", "    peep_query = \"\"\"\n", "        SELECT order_id,updated_at from metrics.picking_effort_estimation_project_v2 \n", "        where dag_id = 'warehouse_misc_etl_picking_effort_estimation_v6' \n", "        order by updated_at desc limit 1\n", "        \"\"\"\n", "    peep_predictions = pd.read_sql(peep_query, pb.get_connection(\"redshift\"))\n", "    peep_predictions.updated_at = pd.to_datetime(peep_predictions.updated_at)\n", "    return peep_predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def return_daterange(start, end):\n", "    date_ranges = []\n", "    date_diff = end - start\n", "    if date_diff.days > 1:\n", "        dates = [\n", "            str(x.date())\n", "            for x in pd.date_range(start=start, end=end, periods=date_diff.days + 1)\n", "        ]\n", "\n", "        for days in range(0, len(dates) - 1):\n", "            date_ranges.append((dates[days], dates[days + 1]))\n", "        print(date_ranges)\n", "    else:\n", "        date_ranges.append(\n", "            (start.strftime(\"%Y-%m-%d %H:%M:%S\"), end.strftime(\"%Y-%m-%d %H:%M:%S\"))\n", "        )\n", "\n", "    return date_ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def change_start(start_time=utc_now - timedelta(minutes=120)):\n", "    peep_predictions = get_last_updated().copy()\n", "    s = start_time < peep_predictions.updated_at\n", "    if s.values[0] == True:\n", "        return [\n", "            start_time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            utc_now.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "        ]\n", "    else:\n", "        return return_daterange(\n", "            pd.Timestamp(peep_predictions.updated_at.values[0]), utc_now\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_ranges = change_start()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame()\n", "for dates in date_ranges:\n", "    query = \"\"\"\n", "    SELECT\n", "            osq.order_id,\n", "            osq.outlet_id,\n", "            ios.name AS order_status,\n", "            osq.updated_at,\n", "            osq.slot_handover_time,\n", "            osq.ancestor,\n", "            count(distinct osq.item_id) AS SPO,\n", "            sum(quantity) IPO,\n", "           count(CASE\n", "                     WHEN handling_type=1 THEN item_id\n", "                 END) AS Handling_Regular,\n", "           count(CASE\n", "                     WHEN handling_type=2 THEN item_id\n", "                 END) AS Handling_Fragile,\n", "           count(CASE\n", "                     WHEN handling_type=4 THEN item_id\n", "                 END) AS Handling_Large,\n", "           count(CASE\n", "                     WHEN storage_type = 1 THEN item_id\n", "                 END) AS Storage_Regular,\n", "           count(CASE\n", "                     WHEN storage_type = 5 THEN item_id\n", "                 END) AS Storage_Heavy,\n", "                 sum(weight_in_gm)/1000 weight_in_kgs\n", "        FROM (\n", "            SELECT\n", "                iod.outlet AS outlet_id,\n", "                iod.order_id,\n", "                iod.status_id,\n", "                iot.item_id AS item_id,\n", "                iot.quantity AS quantity,\n", "                iod.updated_at,\n", "                iod.slot_handover_time,\n", "                iod.ancestor,\n", "                weight_in_gm*quantity weight_in_gm,\n", "              handling_type,\n", "              storage_type\n", "            FROM ims.ims_order_details AS iod\n", "            LEFT JOIN ims.ims_order_items AS iot\n", "            ON iod.id = iot.order_details_id\n", "            LEFT JOIN \n", "\n", "            (SELECT item_id, weight_in_gm,handling_type,storage_type,active FROM rpc.product_product pp2 \n", "            WHERE pp2.id in (\n", "            SELECT max(id) as id FROM rpc.product_product pp \n", "            WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "            ) product ON product.item_id = iot.item_id AND product.active=1\n", "\n", "            WHERE iod.updated_at BETWEEN '{start_date}' and '{end_date}'\n", "\n", "            GROUP BY 1, 2, 3, 4,5,6, 7, 8,9,10,11\n", "        ) osq\n", "        LEFT JOIN ims.ims_order_status AS ios\n", "        ON osq.status_id = ios.id \n", "        group by 1,2,3,4,5,6\n", "        ORDER BY osq.updated_at DESC\n", "        \"\"\".format(\n", "        start_date=dates[0], end_date=dates[1]\n", "    )\n", "    df = df.append(pd.read_sql_query(query, con=pb.get_connection(\"ims\")))\n", "    print(\"Done for :\", dates[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df[df.outlet_id.isin(oids)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.slot_handover_time = np.where(\n", "    df[\"slot_handover_time\"].isna(), df[\"updated_at\"], df[\"slot_handover_time\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.slot_handover_time = pd.to_datetime(df.slot_handover_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Handling_Regular_Large\"] = (data.Handling_Large > 0) & (data.Handling_Regular > 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Storage_Regular_Heavy\"] = (data.Storage_Regular > 0) & (data.Storage_Heavy > 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"month\"] = data.slot_handover_time.dt.month\n", "data[\"day\"] = data.slot_handover_time.dt.day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.feature_names_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_predicted = model.predict(\n", "    data.drop(\n", "        [\"order_id\", \"order_status\", \"updated_at\", \"slot_handover_time\", \"ancestor\"],\n", "        axis=1,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"picking_time_predicted\"] = picking_time_predicted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(\n", "    df,\n", "    data[[\"order_id\", \"outlet_id\", \"picking_time_predicted\"]],\n", "    on=[\"order_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rename_cols = dict()\n", "for cols in picking_time_ideal_v3.columns.values:\n", "    if cols != \"outlet_id\" and cols != \"picking_time_ideal\":\n", "        rename_cols[cols] = cols + \"_ideal\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rename_cols"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_v3.rename(rename_cols, axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(df, picking_time_ideal_v3, how=\"left\", on=[\"outlet_id\"])\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"picking_effort\"] = df[\"picking_time_predicted\"] / df[\"picking_time_ideal\"]\n", "df.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.dropna(subset=[\"IPO\", \"SPO\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.IPO = df.IPO.astype(int)\n", "df.SPO = df.SPO.astype(int)\n", "df.SPO_ideal = df.SPO_ideal.astype(int)\n", "df.IPO_ideal = df.IPO_ideal.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"dag_id\"] = \"warehouse_misc_etl_picking_effort_estimation_v6\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dtypes_dict = {\n", "    \"Handling_Fragile\": \"float8\",\n", "    \"Handling_Fragile_ideal\": \"float8\",\n", "    \"Handling_Large\": \"float8\",\n", "    \"Handling_Large_ideal\": \"float8\",\n", "    \"Handling_Regular\": \"float8\",\n", "    \"Handling_Regular_Large_ideal\": \"boolean\",\n", "    \"Handling_Regular_ideal\": \"float8\",\n", "    \"IPO\": \"integer\",\n", "    \"IPO_ideal\": \"integer\",\n", "    \"SPO\": \"integer\",\n", "    \"SPO_ideal\": \"integer\",\n", "    \"Storage_Heavy\": \"float8\",\n", "    \"Storage_Heavy_ideal\": \"float8\",\n", "    \"Storage_Regular\": \"float8\",\n", "    \"Storage_Regular_Heavy_ideal\": \"boolean\",\n", "    \"Storage_Regular_ideal\": \"float8\",\n", "    \"ancestor\": \"bigint\",\n", "    \"dag_id\": \"varchar\",\n", "    \"day_ideal\": \"integer\",\n", "    \"month_ideal\": \"integer\",\n", "    \"order_id\": \"bigint\",\n", "    \"order_status\": \"varchar\",\n", "    \"outlet_id\": \"integer\",\n", "    \"picking_effort\": \"float8\",\n", "    \"picking_time_ideal\": \"float8\",\n", "    \"picking_time_predicted\": \"float8\",\n", "    \"slot_handover_time\": \"timestamp\",\n", "    \"updated_at\": \"timestamp\",\n", "    \"weight_in_kgs\": \"float8\",\n", "    \"weight_in_kgs_ideal\": \"float8\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [{\"name\": key, \"type\": dtypes_dict[key]} for key in df.columns.values]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"picking_effort_estimation_project_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"order_id\", \"outlet_id\"],\n", "    \"sortkey\": [\"order_id\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",\n", "}\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}