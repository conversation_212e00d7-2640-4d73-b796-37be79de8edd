{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Auto-training of PEEP Model\n", "\n", "### To DOs\n", "\n", "- Query the new data for `t-1`.\n", "- Append the new data frame to the existing training data and commit to S3.\n", "- Clean / Feature Engineer for the new data. \n", "- Train the new model with validation data as `t-1` with baseline as pre-trained model.\n", "- Push the results and the newly trained model to S3."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python3 -m pip install -q catboost\n", "!python3 -m pip install -q joblib\n", "!python3 -m pip install sklearn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pickle\n", "from collections import defaultdict\n", "import json\n", "from ast import literal_eval\n", "import numpy as np\n", "import re\n", "import math\n", "import matplotlib.pyplot as plt\n", "from catboost import CatBoostRegressor\n", "from sklearn.ensemble import RandomForestRegressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename_cat_iod = \"/tmp/picking_time_ideal_cat_v3.csv\"\n", "local_filename_lr_iod = \"/tmp/picking_time_ideal_lr_v3.csv\"\n", "local_filename_cat_model = \"/tmp/cat_boostmodel.sav\"\n", "local_filename_lr_model = \"/tmp/linear_regression_model.sav\"\n", "local_filename_plogs = \"/tmp/performance_logs.csv\"\n", "local_filename_elogs = \"/tmp/execution_logs.csv\"\n", "local_filename_training_data = \"/tmp/training_data.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_filename_cat_iod = \"/eep/warehouse/picking/data/picking_time_ideal_cat_v3.csv\"\n", "cloud_filename_lr_iod = \"/eep/warehouse/picking/data/picking_time_ideal_lr_v3.csv\"\n", "cloud_filename_cat_model = \"/eep/warehouse/picking/prod/model/catboost_model_v3.1.sav\"\n", "cloud_filename_lr_model = \"/eep/warehouse/picking/prod/model/linear_regression_v3.1.sav\"\n", "cloud_filename_elogs = \"/eep/warehouse/picking/logs/execution_logs.csv\"\n", "cloud_filename_plogs = \"/eep/warehouse/picking/logs/v3/performance_logs\"\n", "cloud_filename_training_data = \"/eep/warehouse/picking/data/training_data.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "pb.from_s3(bucket_name, cloud_filename_cat_iod, local_filename_cat_iod)\n", "pb.from_s3(bucket_name, cloud_filename_lr_iod, local_filename_lr_iod)\n", "\n", "pb.from_s3(bucket_name, cloud_filename_cat_model, local_filename_cat_model)\n", "pb.from_s3(bucket_name, cloud_filename_lr_model, local_filename_lr_model)\n", "\n", "pb.from_s3(bucket_name, cloud_filename_elogs, local_filename_elogs)\n", "pb.from_s3(bucket_name, cloud_filename_plogs, local_filename_plogs)\n", "pb.from_s3(bucket_name, cloud_filename_training_data, local_filename_training_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = pd.read_csv(local_filename_cat_iod)\n", "picking_time_ideal_lr_v3 = pd.read_csv(local_filename_lr_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_logs = pd.read_csv(local_filename_elogs)\n", "performance_logs = pd.read_csv(local_filename_plogs)\n", "training_data = pd.read_csv(local_filename_training_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import CatBoost\n", "\n", "model = CatBoostRegressor()\n", "model.load_model(local_filename_cat_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["regressor = joblib.load(local_filename_lr_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.picking_started_at = pd.to_datetime(training_data.picking_started_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.picking_started_at.dt.date.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = training_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data[\"date\"] = training_data.picking_started_at.dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.date.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.drop(\"date\", axis=1, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building a Recency Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta\n", "import pytz\n", "\n", "utc_now = datetime.datetime.utcnow()\n", "today = str(utc_now.date())\n", "week = str(utc_now.date() - datetime.timedelta(days=2))\n", "print(today, week)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min(\n", "    training_data.picking_started_at.dt.date.max(),\n", "    utc_now.date() - datetime.timedelta(days=2),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def return_daterange(start, end):\n", "    date_ranges = []\n", "    date_diff = end - start\n", "    if date_diff.days > 7:\n", "\n", "        dates = [str(x.date()) for x in pd.date_range(start=start, end=end, periods=8)]\n", "\n", "        for days in range(0, len(dates) - 1):\n", "            date_ranges.append((dates[days], dates[days + 1]))\n", "    else:\n", "        date_ranges.append((start, end))\n", "\n", "    return date_ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_ranges = return_daterange(\n", "    min(\n", "        training_data.picking_started_at.dt.date.max(),\n", "        utc_now.date() - datetime.timedelta(days=2),\n", "    ),\n", "    utc_now.date(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data = pd.DataFrame()\n", "for dates in date_ranges:\n", "    query = \"\"\"\n", "        SELECT b.outlet_id,\n", "           pick_list_id,\n", "           TYPE as picklist_type,\n", "           picking_started_at,\n", "           picking_completed_at,\n", "           TIME_TO_SEC(TIMEDIFF( picking_completed_at, picking_started_at)) time_diff_in_seconds,\n", "           count(DISTINCT item_id) SPO,\n", "           sum(IPO) AS IPO,\n", "           count(CASE\n", "                     WHEN handling_type=1 THEN item_id\n", "                 END) AS Handling_Regular,\n", "           count(CASE\n", "                     WHEN handling_type=2 THEN item_id\n", "                 END) AS Handling_Fragile,\n", "           count(CASE\n", "                     WHEN handling_type=4 THEN item_id\n", "                 END) AS Handling_Large,\n", "           count(CASE\n", "                     WHEN storage_type = 1 THEN item_id\n", "                 END) AS Storage_Regular,\n", "           count(CASE\n", "                     WHEN storage_type = 5 THEN item_id\n", "                 END) AS Storage_Heavy,\n", "            sum(weight_in_kgs) weight_in_kgs\n", "    FROM\n", "      (SELECT wpl.outlet_id,\n", "              wpl.pick_list_id,\n", "              wpl.type,\n", "              pli.item_id,\n", "              pli.required_quantity IPO,\n", "              wpl.picking_started_at,\n", "              pll.pos_timestamp AS picking_completed_at,\n", "              (weight_in_gm*required_quantity)/1000 AS weight_in_kgs,\n", "              handling_type,\n", "              storage_type\n", "       FROM warehouse_location.warehouse_pick_list wpl\n", "       INNER JOIN warehouse_location.pick_list_log pll ON pll.pick_list_id=wpl.id\n", "       INNER JOIN warehouse_location.warehouse_pick_list_item pli ON pli.pick_list_id=wpl.id\n", "       JOIN rpc.product_product rpp ON rpp.item_id=pli.item_id\n", "       AND rpp.active=1\n", "       WHERE wpl.created_at BETWEEN '{}' AND '{}'\n", "         AND pll.picklist_state=4\n", "       GROUP BY 1,\n", "                2,\n", "                3,\n", "                4,\n", "                5,\n", "                6,\n", "                7,\n", "                8,\n", "                9,\n", "                10) b\n", "    GROUP BY 1,\n", "             2,\n", "             3,\n", "             4,5,6\"\"\".format(\n", "        dates[0], dates[1]\n", "    )\n", "\n", "    con = pb.get_connection(\"retail\")\n", "    recency_data = recency_data.append(pd.read_sql_query(query, con=con))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data = recency_data.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Engineering Training and Testing-Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Training Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["week_train_data = train_data[\n", "    train_data.picking_started_at >= str(utc_now.date() - datetime.timedelta(days=31))\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def feature_engineer(training_data):\n", "    training_data.picking_started_at = pd.to_datetime(training_data.picking_started_at)\n", "    training_data.drop(\n", "        training_data[(training_data.picking_started_at >= week)].index, inplace=True\n", "    )\n", "    training_data[\"month\"] = training_data.picking_started_at.dt.month\n", "    training_data[\"day\"] = training_data.picking_started_at.dt.day\n", "    training_data[\"Handling_Regular_Large\"] = (training_data.Handling_Large > 0) & (\n", "        training_data.Handling_Regular > 0\n", "    )\n", "    training_data[\"Storage_Regular_Heavy\"] = (training_data.Storage_Regular > 0) & (\n", "        training_data.Storage_Heavy > 0\n", "    )\n", "    training_data.drop(\n", "        training_data[training_data.time_diff_in_seconds > 3000].index, inplace=True\n", "    )\n", "    training_data = training_data[\n", "        training_data.outlet_id.isin(picking_time_ideal_cat_v3.outlet_id.unique())\n", "    ]\n", "    training_data.drop(\n", "        [\"picking_started_at\", \"pick_list_id\", \"picking_completed_at\"],\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "\n", "    return training_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data = feature_engineer(training_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_data.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["week_training_data = feature_engineer(week_train_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["week_training_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["week_train_data[[\"month\", \"day\"]].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.outlet_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.picking_started_at = pd.to_datetime(recency_data.picking_started_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data[\"Handling_Regular_Large\"] = (recency_data.Handling_Large > 0) & (\n", "    recency_data.Handling_Regular > 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data[\"Storage_Regular_Heavy\"] = (recency_data.Storage_Regular > 0) & (\n", "    recency_data.Storage_Heavy > 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data[\"month\"] = recency_data.picking_started_at.dt.month\n", "recency_data[\"day\"] = recency_data.picking_started_at.dt.day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.dropna(subset=[\"time_diff_in_seconds\"], inplace=True)\n", "recency_data.drop(\n", "    [\"picking_started_at\", \"picking_completed_at\", \"pick_list_id\"], axis=1, inplace=True\n", ")\n", "recency_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    recency_data[recency_data.time_diff_in_seconds > 3000].shape[0]\n", "    / recency_data.shape[0]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data = recency_data[recency_data.picklist_type == 1]\n", "recency_data.drop([\"picklist_type\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.drop(\n", "    recency_data[recency_data.time_diff_in_seconds > 3000].index, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data = recency_data[\n", "    recency_data.outlet_id.isin(picking_time_ideal_cat_v3.outlet_id.unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.groupby(\"outlet_id\").size().plot.bar(figsize=[16, 6])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.month.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training Linear Regression Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lr_features = [\"SPO\", \"IPO\", \"weight_in_kgs\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression\n", "\n", "regressor = LinearRegression()\n", "regressor.fit(\n", "    week_training_data[lr_features], week_training_data[\"time_diff_in_seconds\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred = regressor.predict(recency_data[lr_features])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import r2_score\n", "from sklearn.metrics import mean_absolute_error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lr_r2 = r2_score(recency_data[\"time_diff_in_seconds\"], y_pred)\n", "lr_r2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lr_mae = mean_absolute_error(recency_data[\"time_diff_in_seconds\"], y_pred)\n", "lr_mae"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training the CAT Boost Model "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import Pool, CatBoostRegressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_pool = Pool(\n", "    data=training_data.drop(\"time_diff_in_seconds\", axis=1),\n", "    label=training_data.time_diff_in_seconds,\n", ")\n", "\n", "validation_pool = Pool(\n", "    data=recency_data.drop(\"time_diff_in_seconds\", axis=1),\n", "    label=recency_data.time_diff_in_seconds,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = CatBoostRegressor(\n", "    iterations=1000, learning_rate=0.07, loss_function=\"MAE\", train_dir=\"/tmp/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.set_params(logging_level=None, verbose=None, verbose_eval=None, silent=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.fit(train_pool, eval_set=validation_pool)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rcat_predictions = model.predict(recency_data.drop(\"time_diff_in_seconds\", axis=1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import r2_score\n", "from sklearn.metrics import mean_absolute_error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rcat_r2 = r2_score(recency_data[\"time_diff_in_seconds\"], rcat_predictions)\n", "rcat_r2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rcat_mae = mean_absolute_error(recency_data[\"time_diff_in_seconds\"], rcat_predictions)\n", "rcat_mae"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model.get_best_score())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.get_best_score()[\"learn\"][\"MAE\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model.get_best_iteration())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rf_feat_importance(m, df):\n", "    return pd.DataFrame(\n", "        {\"cols\": df.columns, \"imp\": m.feature_importances_}\n", "    ).sort_values(\"imp\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fi = rf_feat_importance(model, training_data.drop(\"time_diff_in_seconds\", axis=1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fi.plot.bar(\"cols\", \"imp\", figsize=[16, 6])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Predictions from Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df = recency_data.copy()\n", "prediction_df[\"predictions\"] = rcat_predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df.tail(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df[\"error\"] = abs(\n", "    prediction_df.time_diff_in_seconds - prediction_df.predictions\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df.groupby(\"outlet_id\").error.median().plot.bar(figsize=[16, 6])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df.groupby(\"outlet_id\").error.mean().plot.bar(figsize=[16, 6])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df = prediction_df.reset_index().drop(\"index\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_df[[\"time_diff_in_seconds\", \"predictions\"]].head(200).plot(figsize=[16, 6])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Recalculating the ideal time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_iod(df, m, model_name):\n", "    features_dict = {\n", "        \"catboost\": list(set(df.columns.values) - set([\"picking_time_ideal\"])),\n", "        \"linear_regression\": lr_features,\n", "        \"random_forest\": rf_features,\n", "    }\n", "    df[\"month\"] = asia_now.month\n", "    df[\"day\"] = asia_now.day\n", "    df.drop([\"picking_time_ideal\"], axis=1, inplace=True)\n", "    df[\"picking_time_ideal\"] = m.predict(df[features_dict[model_name]])\n", "    return df[set(features_dict[model_name]).union([\"picking_time_ideal\", \"outlet_id\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = calculate_iod(\n", "    picking_time_ideal_cat_v3.copy(), model, \"catboost\"\n", ").copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_lr_v3 = calculate_iod(\n", "    picking_time_ideal_cat_v3.copy(), regressor, \"linear_regression\"\n", ").copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_lr_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.to_csv(local_filename_cat_iod, index=None)\n", "picking_time_ideal_lr_v3.to_csv(local_filename_lr_iod, index=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Updating the Training Data "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data[\"Handling_Regular_Large\"] = (new_data.Handling_Large > 0) & (\n", "    new_data.Handling_Regular > 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data[\"Storage_Regular_Heavy\"] = (new_data.Storage_Regular > 0) & (\n", "    new_data.Storage_Heavy > 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data = new_data[new_data.picklist_type == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_data.drop(\"picklist_type\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = train_data.append(new_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.picking_started_at = pd.to_datetime(train_data.picking_started_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data[train_data.time_diff_in_seconds.isna()].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.dropna(subset=[\"picking_started_at\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.picking_started_at.dt.month.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.drop_duplicates(inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Uploading the new training data to S3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data.to_csv(local_filename_training_data, index=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename = local_filename_training_data\n", "op_cloud_filepath = cloud_filename_training_data\n", "pb.to_s3(local_filename, bucket_name, op_cloud_filepath)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving Recency Models Locally"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.save_model(local_filename_cat_model)\n", "joblib.dump(regressor, local_filename_lr_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving CatBoost Models to S3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_filename_cat_model = \"/eep/warehouse/picking/model_dumps/catboost_model_v3.1_{}.sav\".format(\n", "    asia_now.date()\n", ")\n", "cloud_filename_regressor = \"/eep/warehouse/picking/model_dumps/linear_regression_v3.1_{}.sav\".format(\n", "    asia_now.date()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_filename_model = \"/eep/warehouse/picking/prod/model/catboost_model_v3.1.sav\"\n", "prod_filename_regressor = \"/eep/warehouse/picking/prod/model/linear_regression_v3.1.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_paths = dict(\n", "    zip(\n", "        [local_filename_cat_model, local_filename_lr_model],\n", "        [prod_filename_model, prod_filename_regressor],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_paths = dict(\n", "    zip(\n", "        [local_filename_cat_model, local_filename_lr_model],\n", "        [cloud_filename_cat_model, cloud_filename_regressor],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "for local_filename_model, cloud_filename_model in prod_paths.items():\n", "    local_filename = local_filename_model\n", "    op_cloud_filepath = cloud_filename_model\n", "    pb.to_s3(local_filename, bucket_name, op_cloud_filepath)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "for local_filename_model, cloud_filename_model in cloud_paths.items():\n", "    local_filename = local_filename_model\n", "    op_cloud_filepath = cloud_filename_model\n", "    pb.to_s3(local_filename, bucket_name, op_cloud_filepath)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving the Ideal Order Definition to S3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iod_paths = dict(\n", "    zip(\n", "        [local_filename_cat_iod, local_filename_lr_iod],\n", "        [cloud_filename_cat_iod, cloud_filename_lr_iod],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iod_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"grofers-prod-dse-sgp\"\n", "for local_filename_iod, cloud_filename_iod in iod_paths.items():\n", "    local_filename = local_filename_iod\n", "    op_cloud_filepath = cloud_filename_iod\n", "    pb.to_s3(local_filename, bucket_name, op_cloud_filepath)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging Run Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_logs = execution_logs.append(\n", "    [\n", "        {\n", "            \"Execution_Date\": asia_now.date(),\n", "            \"Execution_Time\": time,\n", "            \"File_name\": cloud_filename_cat_model,\n", "            \"File_Type\": \"CatBoost Model\",\n", "        },\n", "        {\n", "            \"Execution_Date\": asia_now.date(),\n", "            \"Execution_Time\": time,\n", "            \"File_name\": cloud_filename_regressor,\n", "            \"File_Type\": \"Linear Regression Model\",\n", "        },\n", "    ],\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["performance_logs = performance_logs.append(\n", "    [\n", "        {\n", "            \"Execution_Date\": asia_now.date(),\n", "            \"Execution_Time\": time,\n", "            \"Model_Name\": \"CatBoost Model\",\n", "            \"R2_Accuracy\": rcat_r2,\n", "            \"MAE_Accuracy\": model.get_best_score()[\"learn\"][\"MAE\"],\n", "            \"Best Iteration\": model.get_best_iteration(),\n", "            \"Validation MAE\": model.get_best_score()[\"validation\"][\"MAE\"],\n", "        },\n", "        {\n", "            \"Execution_Date\": asia_now.date(),\n", "            \"Execution_Time\": time,\n", "            \"Model_Name\": \"Linear Regression Model\",\n", "            \"R2_Accuracy\": lr_r2,\n", "            \"MAE_Accuracy\": np.nan,\n", "            \"Best Iteration\": np.nan,\n", "            \"Validation MAE\": lr_mae,\n", "        },\n", "    ],\n", "    ignore_index=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["performance_logs.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_logs.tail(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pushing Lo<PERSON> to S3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["performance_logs.to_csv(local_filename_plogs, index=None)\n", "execution_logs.to_csv(local_filename_elogs, index=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename = local_filename_elogs\n", "op_cloud_filepath = cloud_filename_elogs\n", "pb.to_s3(local_filename, bucket_name, op_cloud_filepath)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename = local_filename_plogs\n", "cloud_filename_plogs = \"/eep/warehouse/picking/logs/v3/performance_logs\"\n", "pb.to_s3(local_filename, bucket_name, cloud_filename_plogs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}