{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["!pip install ipython"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime as dt\n", "from datetime import timedelta\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "from IPython.display import HTML"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (int(dt.today().strftime(\"%w\"))) == 6:  # sunday = 0\n", "    #     run the ranks loop\n", "    all_ranks_df = pd.DataFrame()\n", "    facility_id = [264, 92, 42, 3, 22, 43, 268, 1320, 15, 1, 1206]\n", "    outlet_id = [1104, 714, 475, 116, 292, 481, 1112, 2904, 334, 337, 2653]\n", "    percent_df = pb.from_sheets(\n", "        \"1q76_n0RSOEHo5bEG_FMGRN1YEE0dnutMsIAY_Wb8eok\", \"ds_percentage\"\n", "    )\n", "    for fc, outlet in zip(facility_id, outlet_id):\n", "        percentage = int(\n", "            percent_df[percent_df[\"FC_ID\"] == str(fc)].percentage.unique()[0]\n", "        )\n", "        #         print(percentage)\n", "        q1 = f\"\"\"select facility_id,\n", "                 rec_outlet,\n", "                 rec_outlet_id,\n", "                 CASE \n", "                     WHEN (diff_rank*1.0/cnt) <= ({percentage}*1.0/100) THEN 'inactive' \n", "                     ELSE 'active'\n", "               END as qc_status\n", "               from (SELECT facility_id,\n", "               rec_outlet,\n", "               rec_outlet_id,\n", "               count(*) over (partition by facility_id) as cnt,\n", "               abs(sum(diff_qty)) AS total_diff_qty,\n", "               abs(sum(diff_amt)) AS total_diff_amount,\n", "               Row_number() OVER (PARTITION BY facility_id\n", "                                ORDER BY total_diff_qty ASC) diff_rank\n", "           FROM\n", "             (WITH sto_billed_grn_raw AS\n", "                            (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "                                    pi.grofers_order_id AS sto_id,\n", "                                    sto.outlet_id,\n", "                                    sto.merchant_outlet_id,\n", "                                    pi.invoice_id,\n", "                                    pi.transfer_state,\n", "                                    r.item_id AS Item_ID,\n", "                                    id.name AS Item_name,\n", "                                    (CASE\n", "                                         WHEN id.outlet_type = 1 THEN 'F&V type'\n", "                                         WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "                                         ELSE 'Not Assigned'\n", "                                     END) AS Item_Type,\n", "                                    sum(pipd.quantity) AS billed_qty,\n", "                                    coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "                                    max(i.post + interval '5.5 hour') grn_timestamp,\n", "                                    (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "                                    avg(pipd.selling_price) AS billing_price,\n", "                                    avg(lp.landing_price) AS hot_landing_price\n", "                             FROM lake_pos.pos_invoice pi\n", "                             LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "                             LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "                             LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "                             LEFT JOIN\n", "                               (SELECT merchant_invoice_id,\n", "                                       variant_id,\n", "                                       sum(\"delta\") AS grn,\n", "                                       max(pos_timestamp) AS post\n", "                                FROM lake_ims.ims_inventory_log\n", "                                WHERE (pos_timestamp) >'2020-01-10'\n", "                                  AND inventory_update_type_id IN (1,\n", "                                                                   28,\n", "                                                                   76,\n", "                                                                   90,\n", "                                                                   93)\n", "                                GROUP BY 1,\n", "                                         2) i ON pi.invoice_id=i.merchant_invoice_id\n", "                             AND pipd.variant_id::varchar=i.variant_id::varchar\n", "                             LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id\n", "                             AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "                             LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "                             WHERE pi.invoice_type_id IN (5,\n", "                                                          14,\n", "                                                          16)\n", "                               AND (pi.pos_timestamp) >'2020-01-15'\n", "                               AND date(sto.created_at + interval '5.5 hour') BETWEEN CURRENT_DATE-30 AND CURRENT_DATE-2\n", "                             GROUP BY 1,\n", "                                      2,\n", "                                      3,\n", "                                      4,\n", "                                      5,\n", "                                      6,\n", "                                      7,\n", "                                      8,\n", "                                      9,\n", "                                      13),\n", "                               sto_billed_grn AS\n", "                            (SELECT sto_created_at,\n", "                                    outlet_id,\n", "                                    merchant_outlet_id,\n", "                                    sbg.invoice_id,\n", "                                    sbg.transfer_state,\n", "                                    sbg.item_name,\n", "                                    sbg.item_id,\n", "                                    sbg.sto_id,\n", "                                    (sbg.billed_qty) billed_qty,\n", "                                    max(sbg.invoice_creation) invoice_creation,\n", "                                    sum(sbg.grn_quantity) grn_quantity,\n", "                                    max(sbg.grn_timestamp) grn_timestamp,\n", "                                    sum(sbg.billing_price*billed_qty) billing_price,\n", "                                    (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "                                    (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "                                    sbg.billing_price AS billing_unit_price,\n", "                                    sbg.hot_landing_price\n", "                             FROM sto_billed_grn_raw sbg\n", "                             GROUP BY 1,\n", "                                      2,\n", "                                      3,\n", "                                      4,\n", "                                      5,\n", "                                      6,\n", "                                      7,\n", "                                      8,\n", "                                      9,\n", "                                      16,\n", "                                      17),\n", "                               discrepancy AS\n", "                            (SELECT vendor_invoice_id,\n", "                                    item_id,\n", "                                    max((temp.created_at + interval '5.5 hour')) AS disc_created_at,\n", "                                    sum(disc_qty) AS disc_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('UPC is invalid','Wrong item') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS wrong_item_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('MRP Mismatch') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS variant_mismatch_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('UPC not scannable','UPC not printed on SKU') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS UPC_not_scannable_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)', 'STO Short Quantity (Transport)','STO Short Quantity (Source)') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS short_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS quality_issue_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Shelf Life guidelines breached') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS expiry_nte_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Freebies missing') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS freebies_missing_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Rcpt Qty > Invoice Qty') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS excess_qty,\n", "                                    sum(CASE\n", "                                            WHEN disc_remark IN ('Damaged','Packaging issue','Labelling issue') THEN disc_qty\n", "                                            ELSE 0\n", "                                        END) AS damage_qty\n", "                             FROM\n", "                               (SELECT dn.id,\n", "                                       (dn.created_at + interval '5.5 hour') created_at,\n", "                                       dn.vendor_name,\n", "                                       dn.vendor_invoice_id,\n", "                                       dn.net_amount,\n", "                                       dn.outlet_id,\n", "                                       dnpd.upc_id,\n", "                                       dnpd.name,\n", "                                       dnpd.quantity AS disc_qty,\n", "                                       dnpd.cost_price,\n", "                                       dnpd.landing_price,\n", "                                       dnpd.remark,\n", "                                       pp.item_id,\n", "                                       dnr.name AS disc_remark\n", "                                FROM\n", "                                  (SELECT d.*\n", "                                   FROM lake_pos.discrepancy_note d\n", "                                   INNER JOIN\n", "                                     (SELECT vendor_invoice_id,\n", "                                             max(created_at) AS created_at\n", "                                      FROM lake_pos.discrepancy_note\n", "                                      WHERE date(created_at)>= CURRENT_DATE-30\n", "                                      GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "                                   AND d.created_at = d1.created_at) dn\n", "                                LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "                                LEFT JOIN lake_pos.discrepancy_reason dnr ON dnr.id = dnpd.reason_code\n", "                                LEFT JOIN\n", "                                  (SELECT DISTINCT item_id,\n", "                                                   upc\n", "                                   FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", "                    ) TEMP\n", "                             GROUP BY 1,\n", "                                      2),\n", "                               return_b2b AS\n", "                            (SELECT tab1.original_invoice_id,\n", "                                    tab1.item_id,\n", "                                    max(return_timestamp) AS return_timestamp,\n", "                                    sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "                                    sum(tab2.b2b_customer_return) b2breturn_good,\n", "                                    sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "                                    sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "                                    sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "                                    sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "                                    sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "                             FROM\n", "                               (SELECT pi.invoice_id ret_invoice_id,\n", "                                       pi.original_invoice_id,\n", "                                       pp.item_id,\n", "                                       pipd.variant_id,\n", "                                       max(pi.pos_timestamp+interval'5.5 hrs') AS return_timestamp,\n", "                                       sum(pipd.quantity) AS b2b_return_qty\n", "                                FROM lake_pos.view_pos_invoice pi\n", "                                INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "                                LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "                                LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "                                WHERE pi.invoice_type_id IN (8,\n", "                                                             15)\n", "                                  AND pi2.invoice_type_id IN (5,\n", "                                                              14,\n", "                                                              16)\n", "                                  AND (pi.pos_timestamp) > '2021-01-15'\n", "                                GROUP BY 1,\n", "                                         2,\n", "                                         3,\n", "                                         4)tab1\n", "                             LEFT JOIN\n", "                               (SELECT invoice_id AS ret_invoice_id,\n", "                                       item_id,\n", "                                       variant_id,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id IN (23,35) THEN ret_qty\n", "                                           END) AS b2b_customer_return,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id IN (24,36) THEN ret_qty\n", "                                           END) AS b2b_customer_return_expired,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id IN (25,37) THEN ret_qty\n", "                                           END) AS b2b_customer_return_damaged,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id IN (66,68) THEN ret_qty\n", "                                           END) AS b2b_customer_return_near_expiry,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id=119 THEN ret_qty\n", "                                           END) AS lost_in_transit_positive,\n", "                                       sum(CASE\n", "                                               WHEN update_type_id=120 THEN ret_qty\n", "                                           END) AS lost_in_transit_negative,\n", "                                       sum(ret_qty) AS tot_ret_qty\n", "                                FROM\n", "                                  (SELECT iil.invoice_id,\n", "                                          iil.merchant_invoice_id,\n", "                                          iil.\"delta\" AS ret_qty,\n", "                                          iut.id AS update_type_id,\n", "                                          iut.name AS update_type,\n", "                                          pp.item_id,\n", "                                          iil.variant_id\n", "                                   FROM lake_ims.ims_inventory_log iil\n", "                                   LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "                                   LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "                                   WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "                                GROUP BY 1,\n", "                                         2,\n", "                                         3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "                             AND tab1.item_id=tab2.item_id\n", "                             AND tab1.variant_id = tab2.variant_id\n", "                             GROUP BY 1,\n", "                                      2),\n", "                               FINAL AS\n", "                            (SELECT sbg.sto_id,\n", "                                    sbg.outlet_id,\n", "                                    ozm1.outlet_name AS sender_outlet,\n", "                                    sbg.merchant_outlet_id,\n", "                                    ozm2.outlet_name AS rec_outlet,\n", "                                    ozm2.outlet_id AS rec_outlet_id,\n", "                                    sbg.sto_created_at,\n", "                                    sbg.item_id,\n", "                                    sbg.billing_unit_price,\n", "                                    hot_landing_price,\n", "                                    sbg.invoice_id,\n", "                                    sbg.transfer_state,\n", "                                    sbg.Item_name,\n", "                                    sbg.billed_qty,\n", "                                    sbg.invoice_creation,\n", "                                    sbg.grn_quantity,\n", "                                    sbg.grn_timestamp,\n", "                                    sbg.billing_price,\n", "                                    sbg.diff_qty diff_qty_billed_grn,\n", "                                    sbg.diff_amt diff_amt_billed_grn,\n", "                                    COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "                                    disc_created_at,\n", "                                    wrong_item_qty,\n", "                                    variant_mismatch_qty,\n", "                                    UPC_not_scannable_qty,\n", "                                    short_qty,\n", "                                    quality_issue_qty,\n", "                                    expiry_nte_qty,\n", "                                    freebies_missing_qty,\n", "                                    excess_qty,\n", "                                    damage_qty,\n", "                                    b2b.return_timestamp,\n", "                                    COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", "                                    COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", "                                    COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", "                                    COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", "                                    COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", "                                    COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", "                                    COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "                             FROM sto_billed_grn sbg\n", "                             LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "                             LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "                             AND ozm2.business_type_id = 7\n", "                             LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id\n", "                             AND dis.item_id = sbg.item_id\n", "                             LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id\n", "                             AND b2b.item_id=sbg.item_id) SELECT fc.id AS facility_id,\n", "                                                                 fc.name AS facility_name,\n", "                                                                 sto_id,\n", "                                                                 outlet_id,\n", "                                                                 sender_outlet,\n", "                                                                 merchant_outlet_id,\n", "                                                                 rec_outlet,\n", "                                                                 rec_outlet_id,\n", "                                                                 sto_created_at,\n", "                                                                 (billed_qty-grn_quantity-tot_returned) AS diff_qty,\n", "                                                                 (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt\n", "                          FROM FINAL f\n", "                          LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id\n", "                          INNER JOIN (select distinct merchant_outlet_id as act_outlet from lake_ims.ims_sto_details where date(created_at) > CURRENT_DATE-7 AND outlet_id = {outlet}) active ON active.act_outlet = rec_outlet_id\n", "                          LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "                          WHERE billed_qty>0\n", "                            --AND diff_amt !=0\n", "                            AND sender_outlet NOT LIKE '%%Infra%%'\n", "                            AND rec_outlet NOT LIKE '%%Infra%%'\n", "                            AND co.active = 1\n", "                            AND device_id <> 47\n", "                AND facility_id = {fc} )\n", "\n", "           GROUP BY 1,\n", "                    2,3) \n", "                    \"\"\"\n", "\n", "        ranks_df = pd.read_sql_query(q1, con=redshift)\n", "        all_ranks_df = all_ranks_df.append(ranks_df)\n", "    pb.to_sheets(\n", "        all_ranks_df, \"1q76_n0RSOEHo5bEG_FMGRN1YEE0dnutMsIAY_Wb8eok\", \"DS Ranks\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# all_ranks_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sending the list of the DS (inactive + active) to various FC heads\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["if (int(dt.today().strftime(\"%w\"))) == 6:  # sunday = 0\n", "    mails = pb.from_sheets(\"1UNkNysRfAIzcLi4998uAlS59_VNAQDJj37JU4jBMYv4\", \"emails\")\n", "    facility_id = [264, 92, 42, 3, 22, 43, 268, 1320, 15, 1, 1206]\n", "    facility_name = [\n", "        \"Farukhnagar - SR Feeder Warehouse\",\n", "        \"Super Store Dasna 2 - Warehouse\",\n", "        \"Super Store Chennai C2 - Warehouse\",\n", "        \"Super Store Bengaluru - Warehouse\",\n", "        \"Super Store Pune - Warehouse\",\n", "        \"Super Store Hyderabad H2 - Warehouse\",\n", "        \"Super Store Dhaturi - Warehouse\",\n", "        \"Ludhiana - Feeder Warehouse\",\n", "        \"Super Store Jaipur - Warehouse\",\n", "        \"Super Store Ahmedabad - Warehouse\",\n", "        \"Super Store Lucknow L4 - Warehouse\",\n", "    ]\n", "\n", "    for fc, name in zip(facility_id, facility_name):\n", "        mails_list = mails[mails[\"facility_id\"] == str(fc)].email_ids.unique().tolist()\n", "        print(name, mails_list)\n", "\n", "        ds_list = all_ranks_df[all_ranks_df[\"facility_id\"] == fc]\n", "        ds_list = ds_list.rename(\n", "            columns={\n", "                \"facility_id\": \"FC ID\",\n", "                \"rec_outlet\": \"Dark Store\",\n", "                \"rec_outlet_id\": \"Dark Store Outlet ID\",\n", "                \"qc_status\": \"QC Status\",\n", "            }\n", "        )\n", "        html_df = ds_list.to_html()\n", "        html2 = html_df.replace(\"<tr>\", '<tr align=\"center\">').replace(\n", "            '<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">'\n", "        )\n", "\n", "        count_ds = ds_list[\"QC Status\"].value_counts().reset_index(name=\"counts\")\n", "        count_ds = count_ds.rename(\n", "            columns={\"index\": \"QC Status\", \"counts\": \"Number of DS\"}\n", "        )\n", "        count_html = count_ds.to_html()\n", "        html3 = count_html.replace(\"<tr>\", '<tr align=\"center\">').replace(\n", "            '<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">'\n", "        )\n", "\n", "        from_email = \"<EMAIL>\"\n", "        to_email = mails_list\n", "        cc = [\n", "            \"dev.b<PERSON><EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "\n", "        subject = f\"Outbound QC Status Changes\"\n", "        html_content = (\n", "            \"Hi,\"\n", "            + \"<br>\"\n", "            + \"Please find below the list of Dark Stores along with the QC status to implement\"\n", "            + \"<p>\"\n", "            + name\n", "            + \"<p>\"\n", "            + html2\n", "            + \"<p>\"\n", "            + html3\n", "            + \"Regards,\"\n", "            + \"<br>\"\n", "            + \"Data Warehouse Team\"\n", "        )\n", "\n", "        pb.send_email(from_email, to_email, subject, html_content, cc=cc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Dates need to be handled\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_diff_df = pd.DataFrame()\n", "\n", "fc_facility_ids = [264, 92, 42, 3, 22, 43, 268, 1320, 15, 1, 1206]\n", "fc_outlet_ids = [1104, 714, 475, 116, 292, 481, 1112, 2904, 334, 337, 2653]\n", "\n", "for fc, outlet in zip(fc_facility_ids, fc_outlet_ids):\n", "    query = f\"\"\"SELECT facility_id,\n", "                   facility_name,\n", "                   rec_outlet,\n", "                   rec_outlet_id,\n", "                   Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-2 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-2_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-3 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-3_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-4 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-4_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-5 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-5_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-6 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-6_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-7 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-7_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-8 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-8_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-9 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-9_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-10 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-10_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-11 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-11_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-12 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-12_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-13 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-13_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-14 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-14_qty\",      \n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-15 THEN abs(total_diff_qty)\n", "                           ELSE 0\n", "                       END) AS \"t-15_qty\",\n", "                       Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-16 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-16_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-17 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-17_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-18 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-18_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-19 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-19_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-20 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-20_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-21 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-21_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-22 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-22_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-23 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-23_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-24 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-24_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-25 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-25_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-26 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-26_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-27 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-27_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-28 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-28_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-29 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-29_qty\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-30 THEN abs(total_diff_qty)\n", "                            ELSE 0\n", "                        END) AS \"t-30_qty\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-2 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-2_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-3 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-3_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-4 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-4_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-5 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-5_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-6 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-6_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-7 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-7_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-8 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-8_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-9 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-9_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-10 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-10_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-11 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-11_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-12 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-12_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-13 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-13_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-14 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-14_amt\",\n", "                       Sum(CASE\n", "                           WHEN sto_created_at = CURRENT_DATE-15 THEN abs(total_diff_amount)\n", "                           ELSE 0\n", "                       END) AS \"t-15_amt\",\n", "                       Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-16 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-16_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-17 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-17_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-18 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-18_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-19 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-19_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-20 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-20_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-21 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-21_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-22 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-22_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-23 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-23_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-24 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-24_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-25 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-25_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-26 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-26_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-27 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-27_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-28 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-28_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-29 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-29_amt\",\n", "                        Sum(CASE\n", "                            WHEN sto_created_at = CURRENT_DATE-30 THEN abs(total_diff_amount)\n", "                            ELSE 0\n", "                        END) AS \"t-30_amt\"\n", "            FROM\n", "              (SELECT sto_created_at,\n", "                      facility_id,\n", "                      facility_name,\n", "                      sender_outlet,\n", "                      rec_outlet,\n", "                      rec_outlet_id,\n", "                      sum(diff_qty) AS total_diff_qty,\n", "                      sum(diff_amt) AS total_diff_amount\n", "               FROM\n", "                 (WITH sto_billed_grn_raw AS\n", "                    (SELECT date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "                            pi.grofers_order_id AS sto_id,\n", "                            sto.outlet_id,\n", "                            sto.merchant_outlet_id,\n", "                            pi.invoice_id,\n", "                            pi.transfer_state,\n", "                            r.item_id AS Item_ID,\n", "                            id.name AS Item_name,\n", "                            (CASE\n", "                                 WHEN id.outlet_type = 1 THEN 'F&V type'\n", "                                 WHEN id.outlet_type = 2 THEN 'Grocery type'\n", "                                 ELSE 'Not Assigned'\n", "                             END) AS Item_Type,\n", "                            sum(pipd.quantity) AS billed_qty,\n", "                            coalesce(sum(i.grn),0) AS GRN_Quantity,\n", "                            max(i.post + interval '5.5 hour') grn_timestamp,\n", "                            (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "                            avg(pipd.selling_price) AS billing_price,\n", "                            avg(lp.landing_price) AS hot_landing_price\n", "                     FROM lake_pos.pos_invoice pi\n", "                     LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "                     LEFT JOIN lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "                     LEFT JOIN lake_rpc.item_details id ON id.item_id = r.item_id\n", "                     LEFT JOIN\n", "                       (SELECT merchant_invoice_id,\n", "                               variant_id,\n", "                               sum(\"delta\") AS grn,\n", "                               max(pos_timestamp) AS post\n", "                        FROM lake_ims.ims_inventory_log\n", "                        WHERE (pos_timestamp) >'2020-01-10'\n", "                          AND inventory_update_type_id IN (1,\n", "                                                           28,\n", "                                                           76,\n", "                                                           90,\n", "                                                           93)\n", "                        GROUP BY 1,\n", "                                 2) i ON pi.invoice_id=i.merchant_invoice_id\n", "                     AND pipd.variant_id::varchar=i.variant_id::varchar\n", "                     LEFT JOIN lake_pos.pos_invoice_product_lp lp ON lp.invoice_id=pi.id\n", "                     AND lp.variant_id::varchar=pipd.variant_id::varchar\n", "                     LEFT JOIN lake_ims.ims_sto_details sto ON sto.sto_id = pi.grofers_order_id\n", "                     WHERE pi.invoice_type_id IN (5,\n", "                                                  14,\n", "                                                  16)\n", "                       AND (pi.pos_timestamp) >'2020-01-15'\n", "                       AND date(sto.created_at + interval '5.5 hour') BETWEEN CURRENT_DATE-30 AND CURRENT_DATE-2\n", "                     GROUP BY 1,\n", "                              2,\n", "                              3,\n", "                              4,\n", "                              5,\n", "                              6,\n", "                              7,\n", "                              8,\n", "                              9,\n", "                              13),\n", "                       sto_billed_grn AS\n", "                    (SELECT sto_created_at,\n", "                            outlet_id,\n", "                            merchant_outlet_id,\n", "                            sbg.invoice_id,\n", "                            sbg.transfer_state,\n", "                            sbg.item_name,\n", "                            sbg.item_id,\n", "                            sbg.sto_id,\n", "                            (sbg.billed_qty) billed_qty,\n", "                            max(sbg.invoice_creation) invoice_creation,\n", "                            sum(sbg.grn_quantity) grn_quantity,\n", "                            max(sbg.grn_timestamp) grn_timestamp,\n", "                            sum(sbg.billing_price*billed_qty) billing_price,\n", "                            (sbg.billed_qty) -sum(sbg.grn_quantity) AS diff_qty,\n", "                            (sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) AS diff_amt,\n", "                            sbg.billing_price AS billing_unit_price,\n", "                            sbg.hot_landing_price\n", "                     FROM sto_billed_grn_raw sbg\n", "                     GROUP BY 1,\n", "                              2,\n", "                              3,\n", "                              4,\n", "                              5,\n", "                              6,\n", "                              7,\n", "                              8,\n", "                              9,\n", "                              16,\n", "                              17),\n", "                       discrepancy AS\n", "                    (SELECT vendor_invoice_id,\n", "                            item_id,\n", "                            max((temp.created_at + interval '5.5 hour')) AS disc_created_at,\n", "                            sum(disc_qty) AS disc_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('UPC is invalid','Wrong item') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS wrong_item_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('MRP Mismatch') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS variant_mismatch_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('UPC not scannable','UPC not printed on SKU') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS UPC_not_scannable_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Rcpt Qty < Invoice Qty','Item not in ESTO','STO Missing (Transport)','STO Missing (Source)', 'STO Short Quantity (Transport)','STO Short Quantity (Source)') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS short_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Stale items- Mould/Fungus/Rotten','F&V Foul smell') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS quality_issue_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Shelf Life guidelines breached') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS expiry_nte_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Freebies missing') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS freebies_missing_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Rcpt Qty > Invoice Qty') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS excess_qty,\n", "                            sum(CASE\n", "                                    WHEN disc_remark IN ('Damaged','Packaging issue','Labelling issue') THEN disc_qty\n", "                                    ELSE 0\n", "                                END) AS damage_qty\n", "                     FROM\n", "                       (SELECT dn.id,\n", "                               (dn.created_at + interval '5.5 hour') created_at,\n", "                               dn.vendor_name,\n", "                               dn.vendor_invoice_id,\n", "                               dn.net_amount,\n", "                               dn.outlet_id,\n", "                               dnpd.upc_id,\n", "                               dnpd.name,\n", "                               dnpd.quantity AS disc_qty,\n", "                               dnpd.cost_price,\n", "                               dnpd.landing_price,\n", "                               dnpd.remark,\n", "                               pp.item_id,\n", "                               dnr.name AS disc_remark\n", "                        FROM\n", "                          (SELECT d.*\n", "                           FROM lake_pos.discrepancy_note d\n", "                           INNER JOIN\n", "                             (SELECT vendor_invoice_id,\n", "                                     max(created_at) AS created_at\n", "                              FROM lake_pos.discrepancy_note\n", "                              WHERE date(created_at)>= CURRENT_DATE-30\n", "                              GROUP BY 1) d1 ON d.vendor_invoice_id = d1.vendor_invoice_id\n", "                           AND d.created_at = d1.created_at) dn\n", "                        LEFT JOIN lake_pos.discrepancy_note_product_detail dnpd ON dn.id=dnpd.dn_id_id\n", "                        LEFT JOIN lake_pos.discrepancy_reason dnr ON dnr.id = dnpd.reason_code\n", "                        LEFT JOIN\n", "                          (SELECT DISTINCT item_id,\n", "                                           upc\n", "                           FROM lake_rpc.product_product) pp ON dnpd.upc_id=pp.upc -- where vendor_invoice_id='BPM292T210001751' and upc = '8901063338623'\n", "            ) TEMP\n", "                     GROUP BY 1,\n", "                              2),\n", "                       return_b2b AS\n", "                    (SELECT tab1.original_invoice_id,\n", "                            tab1.item_id,\n", "                            max(return_timestamp) AS return_timestamp,\n", "                            sum(tab1.b2b_return_qty) AS b2b_return_qty,\n", "                            sum(tab2.b2b_customer_return) b2breturn_good,\n", "                            sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "                            sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "                            sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "                            sum(tab2.lost_in_transit_positive) lost_in_transit_positive,\n", "                            sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "                     FROM\n", "                       (SELECT pi.invoice_id ret_invoice_id,\n", "                               pi.original_invoice_id,\n", "                               pp.item_id,\n", "                               pipd.variant_id,\n", "                               max(pi.pos_timestamp+interval'5.5 hrs') AS return_timestamp,\n", "                               sum(pipd.quantity) AS b2b_return_qty\n", "                        FROM lake_pos.view_pos_invoice pi\n", "                        INNER JOIN lake_pos.view_pos_invoice pi2 ON pi2.invoice_id=pi.original_invoice_id\n", "                        LEFT JOIN lake_pos.pos_invoice_product_details pipd ON pi.id=pipd.invoice_id\n", "                        LEFT JOIN lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "                        WHERE pi.invoice_type_id IN (8,\n", "                                                     15)\n", "                          AND pi2.invoice_type_id IN (5,\n", "                                                      14,\n", "                                                      16)\n", "                          AND (pi.pos_timestamp) > '2021-01-15'\n", "                        GROUP BY 1,\n", "                                 2,\n", "                                 3,\n", "                                 4)tab1\n", "                     LEFT JOIN\n", "                       (SELECT invoice_id AS ret_invoice_id,\n", "                               item_id,\n", "                               variant_id,\n", "                               sum(CASE\n", "                                       WHEN update_type_id IN (23,35) THEN ret_qty\n", "                                   END) AS b2b_customer_return,\n", "                               sum(CASE\n", "                                       WHEN update_type_id IN (24,36) THEN ret_qty\n", "                                   END) AS b2b_customer_return_expired,\n", "                               sum(CASE\n", "                                       WHEN update_type_id IN (25,37) THEN ret_qty\n", "                                   END) AS b2b_customer_return_damaged,\n", "                               sum(CASE\n", "                                       WHEN update_type_id IN (66,68) THEN ret_qty\n", "                                   END) AS b2b_customer_return_near_expiry,\n", "                               sum(CASE\n", "                                       WHEN update_type_id=119 THEN ret_qty\n", "                                   END) AS lost_in_transit_positive,\n", "                               sum(CASE\n", "                                       WHEN update_type_id=120 THEN ret_qty\n", "                                   END) AS lost_in_transit_negative,\n", "                               sum(ret_qty) AS tot_ret_qty\n", "                        FROM\n", "                          (SELECT iil.invoice_id,\n", "                                  iil.merchant_invoice_id,\n", "                                  iil.\"delta\" AS ret_qty,\n", "                                  iut.id AS update_type_id,\n", "                                  iut.name AS update_type,\n", "                                  pp.item_id,\n", "                                  iil.variant_id\n", "                           FROM lake_ims.ims_inventory_log iil\n", "                           LEFT JOIN lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "                           LEFT JOIN lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "                           WHERE iil.pos_timestamp>'2021-01-15' ) TEMP\n", "                        GROUP BY 1,\n", "                                 2,\n", "                                 3)tab2 ON tab1.ret_invoice_id=tab2.ret_invoice_id\n", "                     AND tab1.item_id=tab2.item_id\n", "                     AND tab1.variant_id = tab2.variant_id\n", "                     GROUP BY 1,\n", "                              2),\n", "                       FINAL AS\n", "                    (SELECT sbg.sto_id,\n", "                            sbg.outlet_id,\n", "                            ozm1.outlet_name AS sender_outlet,\n", "                            sbg.merchant_outlet_id,\n", "                            ozm2.outlet_name AS rec_outlet,\n", "                            ozm2.outlet_id AS rec_outlet_id,\n", "                            sbg.sto_created_at,\n", "                            sbg.item_id,\n", "                            sbg.billing_unit_price,\n", "                            hot_landing_price,\n", "                            sbg.invoice_id,\n", "                            sbg.transfer_state,\n", "                            sbg.Item_name,\n", "                            sbg.billed_qty,\n", "                            sbg.invoice_creation,\n", "                            sbg.grn_quantity,\n", "                            sbg.grn_timestamp,\n", "                            sbg.billing_price,\n", "                            sbg.diff_qty diff_qty_billed_grn,\n", "                            sbg.diff_amt diff_amt_billed_grn,\n", "                            COALESCE(dis.disc_qty,0) AS discrepancy_qty,\n", "                            disc_created_at,\n", "                            wrong_item_qty,\n", "                            variant_mismatch_qty,\n", "                            UPC_not_scannable_qty,\n", "                            short_qty,\n", "                            quality_issue_qty,\n", "                            expiry_nte_qty,\n", "                            freebies_missing_qty,\n", "                            excess_qty,\n", "                            damage_qty,\n", "                            b2b.return_timestamp,\n", "                            COALESCE(b2b.b2b_return_qty,0) AS tot_returned,\n", "                            COALESCE(b2b.b2breturn_good,0) AS b2breturn_good,\n", "                            COALESCE(b2b.b2breturn_expired,0) AS b2breturn_expired,\n", "                            COALESCE(b2b.b2breturn_damaged,0) AS b2breturn_damaged,\n", "                            COALESCE(b2b.b2breturn_near_expiry,0) AS b2breturn_near_expiry,\n", "                            COALESCE(b2b.lost_in_transit_positive,0) AS lost_in_transit_positive,\n", "                            COALESCE(b2b.lost_in_transit_negative,0) AS lost_in_transit_negative\n", "                     FROM sto_billed_grn sbg\n", "                     LEFT JOIN metrics.outlet_zone_mapping ozm1 ON ozm1.outlet_id=sbg.outlet_id\n", "                     LEFT JOIN metrics.outlet_zone_mapping ozm2 ON ozm2.outlet_id=sbg.merchant_outlet_id\n", "                     AND ozm2.business_type_id = 7\n", "                     LEFT JOIN discrepancy dis ON dis.vendor_invoice_id= sbg.invoice_id\n", "                     AND dis.item_id = sbg.item_id\n", "                     LEFT JOIN return_b2b b2b ON b2b.original_invoice_id= sbg.invoice_id\n", "                     AND b2b.item_id=sbg.item_id) SELECT fc.id AS facility_id,\n", "                                                         fc.name AS facility_name,\n", "                                                         sto_id,\n", "                                                         outlet_id,\n", "                                                         sender_outlet,\n", "                                                         merchant_outlet_id,\n", "                                                         rec_outlet,\n", "                                                         rec_outlet_id,\n", "                                                         sto_created_at,\n", "                                                         (billed_qty-grn_quantity-tot_returned) AS diff_qty,\n", "                                                         (billed_qty-grn_quantity-tot_returned)*hot_landing_price AS diff_amt\n", "                  FROM FINAL f\n", "                  LEFT JOIN lake_retail.console_outlet co ON co.id=f.outlet_id \n", "                  INNER JOIN (select distinct merchant_outlet_id as act_outlet from lake_ims.ims_sto_details where date(created_at) > CURRENT_DATE-7 AND outlet_id = {outlet}) active ON active.act_outlet = rec_outlet_id \n", "                  LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "                  WHERE billed_qty>0\n", "                    --AND diff_amt !=0\n", "                    AND sender_outlet NOT LIKE '%%Infra%%'\n", "                    AND rec_outlet NOT LIKE '%%Infra%%'\n", "                    AND facility_id IN ({fc})\n", "                    AND co.active = 1\n", "                    AND device_id <> 47)\n", "               GROUP BY 1,\n", "                        2,\n", "                        3,\n", "                        4,\n", "                        5,\n", "                        6)\n", "            GROUP BY 1,\n", "                     2,\n", "                     3,\n", "                     4\"\"\"\n", "    fc_df = pd.read_sql_query(query, con=redshift)\n", "    all_diff_df = all_diff_df.append(fc_df, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_diff_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_ranks_df = pb.from_sheets(\n", "    \"1q76_n0RSOEHo5bEG_FMGRN1YEE0dnutMsIAY_Wb8eok\", \"DS Ranks\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_diff_df = all_diff_df[\n", "    all_diff_df[\"rec_outlet_id\"].isin(current_ranks_df.rec_outlet_id.unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(all_diff_df, \"1q76_n0RSOEHo5bEG_FMGRN1YEE0dnutMsIAY_Wb8eok\", \"Raw_diff\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}