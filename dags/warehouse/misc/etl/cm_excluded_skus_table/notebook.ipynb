{"cells": [{"cell_type": "code", "execution_count": null, "id": "590160e2-9799-4616-a679-a2e95d73677b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "025bb699-f426-4533-8c7a-270686f6186f", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2341a0b-0ba3-493c-9fc5-c6bd11fb653f", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"12gZ9APn40mCvhGRAqTX14-puLGwLJH0oCxQllFR9BMo\", \"excluded SKUs\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f8512206-c4bb-4e53-9663-3b497aba4111", "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv('CM plan SKUs pan India - excluded SKUs (1).csv')\n", "# df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb20d891-3ae6-4563-8488-bb811758712d", "metadata": {}, "outputs": [], "source": ["# df['excluded on'] = pd.to_datetime(df['excluded on'])"]}, {"cell_type": "code", "execution_count": null, "id": "d6187549-7c52-4246-a92a-d35ca05d68da", "metadata": {}, "outputs": [], "source": ["df = df[df[\"excluded at\"] != \"all FCs\"]\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e72668bd-f01c-4fbc-9172-1a1290d2c35c", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select id facility_id, name \"excluded at\"\n", "from lake_crates.facility\n", "\"\"\"\n", "f = pd.read_sql_query(sql, trino)\n", "f.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "52115cba-e101-4804-b796-5c4ca006eb1b", "metadata": {}, "outputs": [], "source": ["df = df.merge(f, on=\"excluded at\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "e8bae54d-1232-4ddd-8fc8-4d94fdfac901", "metadata": {}, "outputs": [], "source": ["# df.to_csv('excluded_26_apr.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b8acd1b7-4a12-4e7b-bd19-31d4c64add3b", "metadata": {}, "outputs": [], "source": ["df = df[[\"facility_id\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "3735a5c0-4d3f-482b-afd5-da38ef65d170", "metadata": {}, "outputs": [], "source": ["df[\"item_id\"] = df[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "8247b86f-1e84-4f22-9ab9-5ab49b1df57a", "metadata": {}, "outputs": [], "source": ["len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "f05a2047-05c8-4227-a945-f7f7e474c3e1", "metadata": {}, "outputs": [], "source": ["# df"]}, {"cell_type": "code", "execution_count": null, "id": "d6514e9b-11d9-4ccd-abdf-d022d2f3d09e", "metadata": {}, "outputs": [], "source": ["# assert not df['facility_id'].isnull().values.any()\n", "# assert not df['item_id'].isnull().values.any()"]}, {"cell_type": "code", "execution_count": null, "id": "6e484119-2f51-46a0-a323-7f5fe43e0d42", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"warehouse_etls\",\n", "    \"table_name\": \"cm_excluded_items\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"Case movement excluded facility item pairs\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "94377feb-83ce-48ed-8f46-67299260a26b", "metadata": {}, "outputs": [], "source": ["pb.to_trino(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9e0fb26f-3ba5-4ab2-aef0-02439f8b38d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}