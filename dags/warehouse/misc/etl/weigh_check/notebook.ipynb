{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime as datetime\n", "import numpy as np\n", "import time\n", "import pytz\n", "\n", "retail = pb.get_connection(\"retail\")\n", "import sys\n", "import warnings\n", "\n", "if not sys.warnoptions:\n", "    warnings.simplefilter(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ES11\n", "outlet_id1 = 1072\n", "sheet_id1 = \"1rL9WUEyFIytppSQQjXTpR9GMizIdD8iYvZJBBu8fWdU\"\n", "# ES15\n", "outlet_id2 = 1139\n", "sheet_id2 = \"1yKIoSwrxAt-SWpC4wJS13Z_p4uG9qXRaYEsDuxq0gWg\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pushing_data(outlet_id, sheet_id):\n", "    outlet = outlet_id\n", "    from datetime import date, datetime, timedelta\n", "\n", "    tz = pytz.timezone(\"Asia/Kolkata\")\n", "    start = (datetime.now(tz) - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "    end = (datetime.now(tz)).strftime(\"%Y-%m-%d\")\n", "    print(start, end)\n", "    query2 = \"\"\"select isd.outlet as outlet_id, \n", "            isd.order_id,\n", "            ia.product_id,\n", "            ia.item_id,\n", "            ia.quantity\n", "    from ims.ims_order_details isd\n", "    inner join ims.ims_order_actuals ia on isd.id = ia.order_details_id\n", "    where date(convert_tz(isd.scheduled_at,'+00:00','+05:30')) = %(start_date)s and isd.outlet = %(outlet_num)s\n", "    order by 1,2\"\"\"\n", "    orders_ims = pd.read_sql_query(\n", "        sql=query2,\n", "        con=pb.get_connection(\"[Replica] IMS\"),\n", "        params={\"start_date\": start, \"end_date\": end, \"outlet_num\": outlet},\n", "    )\n", "    it_pd_log_query = \"\"\"select  \n", "    x.product_id::int, item_id::int\n", "    from consumer.it_pd_log it join\n", "    (select  i.product_id, max(updated_on) as max_updated_at from \n", "    consumer.it_pd_log i \n", "    group by 1)x on x.product_id=it.product_id and updated_on=x.max_updated_at\"\"\"\n", "    it_pd_log_df = pd.read_sql_query(\n", "        sql=it_pd_log_query, con=pb.get_connection(\"[Warehouse] Redshift\")\n", "    )\n", "    orders_ims.order_id = orders_ims.order_id.astype(int)\n", "    y = orders_ims.merge(it_pd_log_df, on=[\"product_id\"], how=\"left\")\n", "    z = y.drop(columns=[\"product_id\", \"item_id_x\"]).rename(\n", "        columns={\"item_id_y\": \"item_id\"}\n", "    )\n", "    final = z.groupby([\"outlet_id\", \"order_id\", \"item_id\"]).quantity.sum().reset_index()\n", "    weight_qu = \"\"\"SELECT pp.item_id, \n", "            (case when max(outlet_type) = 1 then 1 else 0 end) as item_type,\n", "            (case when ((max(outlet_type) = 2) and (max(handling_type) in (1,2,3)) and (max(storage_type) in (1,8))) then 1 else 0 end) as grocery_type,\n", "           max(weight_in_gm) AS weight_in_gm\n", "           FROM rpc.product_product pp\n", "           WHERE pp.active=1\n", "           GROUP BY 1\"\"\"\n", "    weights = pd.read_sql_query(sql=weight_qu, con=retail)\n", "    final = final.merge(weights, on=[\"item_id\"], how=\"left\")\n", "    final_ = final\n", "\n", "    x = (\n", "        final.groupby([\"outlet_id\", \"order_id\"])\n", "        .agg({\"weight_in_gm\": \"min\"})\n", "        .reset_index()\n", "    )\n", "    x = x.rename(columns={\"weight_in_gm\": \"minimum weight of item\"})\n", "    final_[\"weight_in_gm\"] = final[\"quantity\"] * final[\"weight_in_gm\"]\n", "    final_df = (\n", "        final_.groupby([\"outlet_id\", \"order_id\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"count\",\n", "                \"quantity\": \"sum\",\n", "                \"weight_in_gm\": \"sum\",\n", "                \"item_type\": \"min\",\n", "                \"grocery_type\": \"min\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    final_df1 = final_df.merge(x, on=[\"outlet_id\", \"order_id\"], how=\"left\")\n", "    final_df1 = final_df1.sort_values(by=[\"order_id\"], ascending=True)\n", "    final_df2 = final_df1[\n", "        [\n", "            \"outlet_id\",\n", "            \"order_id\",\n", "            \"item_id\",\n", "            \"quantity\",\n", "            \"minimum weight of item\",\n", "            \"weight_in_gm\",\n", "            \"item_type\",\n", "            \"grocery_type\",\n", "        ]\n", "    ]\n", "    query = \"\"\"SELECT bl.order_id AS order_id, 1 as skip_scanned\n", "       FROM warehouse_location.warehouse_pick_list_order_mapping om\n", "       INNER JOIN warehouse_location.warehouse_pick_list pl ON om.pick_list_id=pl.id\n", "       INNER JOIN retail.console_outlet co ON co.id=pl.outlet_id AND co.id = %(outlet)s\n", "       INNER JOIN warehouse_location.warehouse_order_billing_log bl ON om.order_id=bl.order_id AND bl.action ='SKIP_SCAN'\n", "       WHERE date(pl.min_scheduled_time) = %(start_date)s\"\"\"\n", "    data2 = pd.read_sql_query(\n", "        sql=query, con=retail, params={\"start_date\": start, \"outlet\": outlet}\n", "    )\n", "    data2.shape[0]\n", "    for i in range(0, 999 - final_df2.shape[0]):\n", "        final_df2 = final_df2.append(pd.Series(), ignore_index=True)\n", "    print(final_df2.shape)\n", "    pb.to_sheets(final_df2, sheet_id, \"system_data\")\n", "    pb.to_sheets(data2, sheet_id, \"skip_scan\")\n", "    print(\"data_pushed_to_sheets\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def store_data(outlet_id, sheet_id):\n", "    from datetime import date, datetime, timedelta\n", "\n", "    data = pb.from_sheets(sheet_id, \"main_sheet\")\n", "    data1 = data[data[\"suborder\"] != \"\"]\n", "    data1 = data1.replace(r\"^\\s*$\", np.nan, regex=True)\n", "    data1.outlet = data1.outlet.astype(int)\n", "    data1.suborder = data1.suborder.astype(int)\n", "    data1.system_weight = data1.system_weight.astype(float)\n", "    data1.weight_of_minimum_item = data1.weight_of_minimum_item.astype(float)\n", "    data1.count_of_polybag = data1.count_of_polybag.astype(float)\n", "    data1.weight_of_polybag_1 = data1.weight_of_polybag_1.astype(float)\n", "    data1.weight_of_polybag_2 = data1.weight_of_polybag_2.astype(float)\n", "    data1.weight_of_polybag_3 = data1.weight_of_polybag_3.astype(float)\n", "    data1.weight_of_polybag_4 = data1.weight_of_polybag_4.astype(float)\n", "    data1.weight_of_polybag_5 = data1.weight_of_polybag_5.astype(float)\n", "    data1.weight_of_polybag_6 = data1.weight_of_polybag_6.astype(float)\n", "    data1.weight_of_polybag_7 = data1.weight_of_polybag_7.astype(float)\n", "    data1.weight_of_polybag_8 = data1.weight_of_polybag_8.astype(float)\n", "    data1.weight_of_polybag_9 = data1.weight_of_polybag_9.astype(float)\n", "    data1.weight_of_polybag_10 = data1.weight_of_polybag_10.astype(float)\n", "    data1.weight_of_polybag_11 = data1.weight_of_polybag_11.astype(float)\n", "    data1.weight_of_polybag_12 = data1.weight_of_polybag_12.astype(float)\n", "    data1.suggestion = data1.suggestion.astype(str)\n", "    data1.total_weight = data1.total_weight.astype(float)\n", "    data1.abs_deviation = data1.abs_deviation.astype(float)\n", "    data1.skipscan_used = data1.skipscan_used.astype(float)\n", "    data1.fnv_available = data1.fnv_available.astype(float)\n", "    data1.grocery_type = data1.grocery_type.astype(float)\n", "    data1.remark = data1.remark.astype(str)\n", "    data1[\"run_date\"] = datetime.now(pytz.timezone(\"Asia/Kolkata\")).date()\n", "    Description_Dict = {\n", "        \"outlet\": \"outlet_id of the experiment\",\n", "        \"suborder\": \"suborder_id\",\n", "        \"system_weight\": \"total order weight as per the system\",\n", "        \"weight_of_minimum_item\": \"min weight of the item in the order\",\n", "        \"count_of_polybag\": \"polybag count for the order\",\n", "        \"suggestion\": \"suggestion of the action to be taken\",\n", "        \"total_weight\": \"total weight as per sum of actual polybag weights entered\",\n", "        \"abs_deviation\": \"deviation in the weight\",\n", "        \"skipscan_used\": \"if skipscan feature is used for the order\",\n", "        \"fnv_available\": \"fnv available in the order\",\n", "        \"grocery_type\": \"If all the items of the order are of grocery type\",\n", "        \"remark\": \"remarks added by the team\",\n", "        \"run_date\": \"rundate of the experiment\",\n", "        \"key\": \"key (ignore)\",\n", "    }\n", "    data1[\"key\"] = data1[\"outlet\"].astype(str) + \"_\" + data1[\"suborder\"].astype(str)\n", "\n", "    def redshift_schema(df):\n", "        metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "        ref_dict = {\n", "            \"int64\": \"int\",\n", "            \"datetime64[ns]\": \"timestamp\",\n", "            \"object\": \"varchar(1000)\",\n", "            \"float64\": \"float\",\n", "            \"datetime64[ns, UTC]\": \"timestamp\",\n", "        }\n", "        return [\n", "            {\n", "                \"name\": key,\n", "                \"type\": ref_dict[value],\n", "                \"description\": Description_Dict.get(\n", "                    key, \"Column name sufficient for description\"\n", "                ),\n", "            }\n", "            for key, value in metadata.items()\n", "        ]\n", "\n", "    def metadata_dict(df):\n", "        return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "    column_dtypes = redshift_schema(data1)\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"weigh_check_darkstores\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"key\"],\n", "        \"sortkey\": [\"outlet\"],\n", "        \"incremental_key\": \"run_date\",\n", "        \"table_description\": \"to store weigh & check data experiment for darkstores\",\n", "        \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "    }\n", "    pb.to_redshift(data1, **kwargs)\n", "    print(\"data_stored_to_table\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "print(datetime.now(tz).hour)\n", "if datetime.now(tz).hour >= 21:\n", "    store_data(outlet_id1, sheet_id1)\n", "    store_data(outlet_id2, sheet_id2)\n", "# time.sleep(60)\n", "pushing_data(outlet_id1, sheet_id1)\n", "pushing_data(outlet_id2, sheet_id2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}