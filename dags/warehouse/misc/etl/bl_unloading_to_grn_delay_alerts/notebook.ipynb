{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ab01526-ede3-43f3-a7c5-ce942afe9d02", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import math\n", "import matplotlib.pyplot as plt\n", "\n", "whl = pb.get_connection(\"[Replica] RDS Warehouse Location\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "po = pb.get_connection(\"[Replica] RDS PO\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "\n", "query = \"\"\"SELECT it.facility_id,\n", "             it.outlet_id,\n", "             it.id AS task_id,\n", "             iut.unloading_id AS unloading_id,\n", "             it.reference_number as po_number,\n", "             (iut.pos_created_at + interval '5' hour + interval '30' minute) unloading_task_created_at,\n", "             iui.unloading_done,\n", "             iui.item_id,\n", "             iui.Un<PERSON><PERSON>\n", "           \n", "      FROM inbound_task it\n", "      INNER JOIN inbound_unloading_task iut ON it.id=iut.inbound_task_id and iut.state =7 and it.type=1 and iut.pos_created_at>= current_date - interval '1' day\n", "      and it.facility_id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\n", "      INNER JOIN\n", "        (SELECT unloading_task_id,\n", "                item_id,\n", "                sum(quantity) as UnloadedQty,\n", "                max(pos_created_at + interval '5' hour + interval '30' minute) unloading_done\n", "         FROM inbound_unloading_item where pos_created_at>=current_date - interval '1' day\n", "         GROUP BY 1,2) iui ON iui.unloading_task_id = iut.unloading_id \"\"\"\n", "\n", "unloading_df = pd.read_sql_query(sql=query, con=whl)\n", "unloading_df = unloading_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"unloading_id\",\n", "        \"po_number\",\n", "        \"unloading_task_created_at\",\n", "        \"unloading_done\",\n", "        \"item_id\",\n", "        \"UnloadedQty\",\n", "    ]\n", "]\n", "\n", "outlet_df = pd.read_sql_query(\n", "    \"\"\"select r.id outlet_id, \n", "       f.name as facility_name\n", "from lake_retail.console_outlet r\n", "join lake_crates.facility f on f.id = r.facility_id\n", "\"\"\",\n", "    redshift,\n", ")\n", "\n", "unloading_outlet_df = unloading_df.merge(outlet_df, on=[\"outlet_id\"], how=\"inner\")\n", "\n", "unloading_outlet_df\n", "po_sql = \"\"\"select po.id as po_id,po.po_number as po_number,\n", "pg.item_id as item_id,\n", "sum(pg.quantity) as Grn<PERSON><PERSON>,\n", "max((pg.created_at+ interval '5' hour + interval '30' minute)) AS grn_at from purchase_order po\n", "join po_grn pg on pg.po_id=po.id\n", "where  pg.created_at >= current_date - interval '1' day\n", "\n", "group by 1,2,3 \"\"\"\n", "po_df = pd.read_sql_query(sql=po_sql, con=po)\n", "final_df = unloading_outlet_df.merge(po_df, on=[\"po_number\", \"item_id\"], how=\"left\")\n", "final_df = final_df.sort_values(by=[\"facility_id\", \"unloading_id\"], ascending=True)\n", "# final_df['unl_to_grn_time']=final_df.apply(lambda df: (df['grn_at']-df['unloading_done']).total_seconds()/60,axis=1)\n", "final_df[\"flag\"] = final_df.apply(\n", "    lambda df: \"Yes\"\n", "    if pd.isnull(df[\"grn_at\"])\n", "    and (\n", "        (\n", "            (datetime.now() + timed<PERSON>ta(hours=5, minutes=30)) - df[\"unloading_done\"]\n", "        ).total_seconds()\n", "        / 60\n", "    )\n", "    > 120\n", "    else \"No\",\n", "    axis=1,\n", ")\n", "final_df = final_df[final_df[\"flag\"] == \"Yes\"]\n", "items_df = pd.read_sql_query(\n", "    sql=\"\"\"select item_id,name  from lake_view_rpc.item_details\"\"\", con=presto\n", ")\n", "final_df = final_df.merge(items_df, on=[\"item_id\"], how=\"inner\")\n", "\n", "final_df = final_df[\n", "    [\"facility_name\", \"facility_id\", \"item_id\", \"name\", \"UnloadedQty\", \"unloading_done\"]\n", "]\n", "# final_df['GrnQty']=final_df['GrnQty'].fillna('No GRN Done')\n", "# final_df['grn_at']=final_df['grn_at'].fillna('No GRN Done')\n", "final_df[\"Grn Status\"] = \"Pending\"\n", "final_df = final_df.rename(\n", "    columns={\n", "        \"facility_name\": \"Facility Name\",\n", "        \"facility_id\": \"Facility ID\",\n", "        \"name\": \"<PERSON><PERSON>\",\n", "        \"item_id\": \"Item ID\",\n", "        \"unloading_done\": \"Unloading Done\",\n", "    }\n", ")\n", "\n", "\n", "pb.to_sheets(\n", "    final_df,\n", "    \"1RZpLCP3xqtsHqBMTTClHfvXGIUwke5BeRoCyaCKFpGU\",\n", "    \"Raw-Data\",\n", "    service_account=\"service_account\",\n", ")\n", "\n", "cluster_sql = \"\"\"select f.id as facility_id,\n", "\n", "case when id in (264,1320,555) then 'C1'\n", "when id in (92,554,24,517) then 'C2'\n", "when id in (43,1873,1876) then 'C3'\n", "when id in (1,513,603,1206,1872) then 'C4' ELSE 'NOT KNOWN' END AS cluster\n", "from lake_crates.facility f\n", "where id in (1,24,43,92,264,513,517,554,555,603,1206,1320,1872,1873,1876)\"\"\"\n", "\n", "\n", "cluster_df = pd.read_sql_query(sql=cluster_sql, con=presto)\n", "cluster_df = cluster_df.rename(columns={\"facility_id\": \"Facility ID\"})\n", "\n", "final_df = final_df.merge(cluster_df, on=[\"Facility ID\"], how=\"inner\")\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\"Facility Name\", \"Facility ID\", \"Total Qty\", \"No of Items\"],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "def slack_df(temp_df, cluster):\n", "    slack_df = (\n", "        temp_df.groupby([\"Facility Name\", \"Facility ID\"])\n", "        .agg({\"UnloadedQty\": \"sum\", \"Item ID\": \"count\"})\n", "        .reset_index()\n", "    )\n", "    slack_df = slack_df.rename(\n", "        columns={\n", "            \"facility_name\": \"Facility Name\",\n", "            \"facility_id\": \"Facility ID\",\n", "            \"UnloadedQty\": \"Total Qty\",\n", "            \"item_id\": \"No of Items\",\n", "        }\n", "    )\n", "\n", "    if cluster == \"C1\":\n", "        channel = \"blinkit-fc-c1-alerts\"\n", "    elif cluster == \"C2\":\n", "        channel = \"blinkit-fc-c2-alerts\"\n", "    elif cluster == \"C3\":\n", "        channel = \"blinkit-fc-c3-alerts\"\n", "    elif cluster == \"C4\":\n", "        channel = \"blinkit-fc-c4-alerts\"\n", "\n", "    # channel='test-channel-category'\n", "\n", "    if len(slack_df) > 0:\n", "        fig, ax = render_mpl_table(slack_df, header_columns=0)\n", "        fig.savefig(\"Unloading_To_Grn_Processing_Delay.png\")\n", "\n", "    if len(slack_df) > 0:\n", "        now = (datetime.now() + <PERSON><PERSON>ta(hours=5, minutes=30)).strftime(\n", "            \"%d %B, %Y %H:%M:%S\"\n", "        )\n", "        text = f\"\"\"*Unloading To GRN Processing Delay Alert  - {now}* \\n This <https://docs.google.com/spreadsheets/d/1RZpLCP3xqtsHqBMTTClHfvXGIUwke5BeRoCyaCKFpGU/edit#gid=0|link> has pending and delayed raw data..\"\"\"\n", "        pb.send_slack_message(\n", "            channel, text, files=[\"Unloading_To_Grn_Processing_Delay.png\"]\n", "        )\n", "\n", "\n", "for cluster in final_df[\"cluster\"].unique().tolist():\n", "    temp_df = final_df[final_df[\"cluster\"] == cluster]\n", "    slack_df(temp_df, cluster)"]}, {"cell_type": "code", "execution_count": null, "id": "9d6cbaf6-9a55-4726-a29c-f09e57b0192b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}