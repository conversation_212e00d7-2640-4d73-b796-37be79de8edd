{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "import pandas as pd\n", "import pandasql as ps\n", "import pencilbox as pb\n", "import jinja2\n", "from datetime import timedelta\n", "import numpy as np\n", "\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "today = str(date.today())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = \"\"\"WITH CATS AS\n", "  (WITH product_category AS\n", "     (SELECT P.ID AS PID,\n", "             P.NAME AS PRODUCT,\n", "             P.UNIT AS Unit,\n", "             C2.NAME AS L2,\n", "             (CASE\n", "                  WHEN C1.NAME = C.name THEN C2.name\n", "                  ELSE C1.name\n", "              END) AS L1,\n", "             C.NAME AS L0,\n", "             (CASE\n", "                  WHEN C1.id = C.id THEN C2.id\n", "                  ELSE C1.id\n", "              END) AS L1_id,\n", "             C.id AS l0_id,\n", "             P.BRAND AS brand,\n", "             P.MANUFACTURER AS manf,\n", "             pt.name AS product_type\n", "      FROM lake_cms.gr_product P\n", "      INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "      INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "      AND PCM.IS_PRIMARY=TRUE\n", "      INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "      INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "      INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id) SELECT DISTINCT rpc.item_id,\n", "                                                                                        cat.l0,\n", "                                                                                        cat.l1,\n", "                                                                                        cat.l2,\n", "                                                                                        cat.l0_id,\n", "                                                                                        cat.l1_id\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   WHERE rpc.product_id IS NOT NULL\n", "   ORDER BY 1,\n", "            2,\n", "            3),\n", "     LOCATION AS\n", "  (SELECT item_level.facility_id,\n", "       item_level.outlet_id,\n", "       item_level.outlet_name,\n", "       (CASE\n", "            WHEN item_level.location_type = 1 THEN 'Entire Warehouse'\n", "            ELSE 'Secondary'\n", "        END) AS zone_construct,\n", "       zz.zone_type,\n", "       cats.l0_id,\n", "       cats.l1_id,\n", "       item_level.item_id,\n", "       items.name AS item_name,\n", "       item_level.location_quantity\n", "FROM\n", "  (SELECT outlet.facility_id,\n", "          outlet.id AS outlet_id,\n", "          outlet.name AS outlet_name,\n", "          wz.location_type,\n", "          pt.item_id,\n", "          sum(pt.quantity) AS location_quantity\n", "   FROM lake_warehouse_location.warehouse_item_location pt\n", "   INNER JOIN lake_warehouse_location.warehouse_storage_location wz ON wz.id = pt.location_id\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = pt.warehouse_id\n", "   INNER JOIN lake_retail.console_outlet outlet ON outlet.id=wom.outlet_id\n", "   AND outlet.id IN (714)\n", "   AND wz.is_active = 1\n", "   AND wz.location_type = (SELECT (CASE\n", "                  WHEN json_extract_path_text(pos_config,'auto_sto_wave_creation_enabled') = 'true' THEN 1\n", "                  ELSE 2\n", "              END)\n", "      FROM lake_retail.console_outlet co\n", "      WHERE co.id = outlet.id)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5) item_level\n", "LEFT JOIN cats ON cats.item_id = item_level.item_id\n", "LEFT JOIN lake_rpc.item_details items ON items.item_id=item_level.item_id\n", "LEFT JOIN\n", "  (SELECT handling_type,\n", "          storage_type,\n", "          zone_type\n", "   FROM lake_warehouse_location.item_zone_type) zz ON zz.handling_type = coalesce(cast(items.handling_type AS INT),0)\n", "AND zz.storage_type = coalesce(cast(items.storage_type AS INT),0)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10\n", "HAVING location_quantity > 0),\n", "            \n", "            \n", "                                  actual_zones AS\n", "  (SELECT wom.outlet_id,\n", "          a.zone_identifier,\n", "          b.zone_type,\n", "          coalesce(l0_id,0) AS l0_id,\n", "          coalesce(l1_id,0) AS l1_id\n", "   FROM lake_warehouse_location.item_category_zone_type_zone_id b\n", "   INNER JOIN lake_warehouse_location.warehouse_zone_id_mapping a ON a.id = b.zone_id\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = a.warehouse_id\n", "   WHERE outlet_id IN (714)\n", "   AND a.enabled = 1 AND b.enabled = 1)\n", "      \n", "SELECT facility_id,\n", "       a.outlet_id,\n", "       outlet_name,\n", "       zone_construct,\n", "       coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) AS actual_zone,\n", "       a.zone_type,\n", "       l0,\n", "       l1,\n", "       a.item_id,\n", "       a.item_name,\n", "       location_quantity AS total_quantity\n", "    --   coalesce(sum(outbound1),0) AS total_outbound1,\n", "    --   coalesce(sum(outbound3),0) AS total_outbound3,\n", "    --   coalesce(sum(outbound7),0) AS total_outbound7\n", "FROM LOCATION a\n", "JOIN cats ON cats.item_id = a.item_id\n", "LEFT JOIN actual_zones z2 ON z2.zone_type = a.zone_type\n", "AND z2.l1_id = a.l1_id\n", "AND z2.l0_id = a.l0_id\n", "AND z2.outlet_id = a.outlet_id\n", "LEFT JOIN actual_zones z1 ON z1.zone_type = a.zone_type\n", "AND z1.l0_id = a.l0_id\n", "AND z1.l1_id = 0\n", "AND z1.l0_id != 0\n", "AND z1.outlet_id = a.outlet_id\n", "LEFT JOIN actual_zones z ON z.zone_type = a.zone_type\n", "AND z.l0_id = 0\n", "AND z.outlet_id = a.outlet_id\n", "-- LEFT JOIN out1 ON out1.item_id = a.item_id AND out1.outlet_id = a.outlet_id\n", "-- LEFT JOIN out3 ON out3.item_id = a.item_id AND out3.outlet_id = a.outlet_id\n", "-- LEFT JOIN out7 ON out7.item_id = a.item_id AND out7.outlet_id = a.outlet_id\n", "WHERE a.outlet_id = 714\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11\n", "ORDER BY a.item_id, actual_zone\n", "         \"\"\"\n", "df = pd.read_sql(data, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_df = df[[\"item_id\", \"actual_zone\"]].drop_duplicates().reset_index()\n", "# temp_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spr_items_df = pb.from_sheets(\n", "    \"12LmRyYk8biimSqc_qm88b2F9lFUuWFUfghDqu6WBXos\", \"Dasna_input\"\n", ").sort_values(\n", "    [\"item_id\", \"L0\", \"L1\", \"Zone Identifier\"], ascending=[True, True, True, True]\n", ")\n", "spr_list = spr_items_df.item_id.unique().tolist()\n", "spr_list = [int(i) for i in spr_list]\n", "# spr_items_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_zone_df = pd.DataFrame(columns=[\"item_id\", \"actual_zone\"])\n", "for i, row in temp_df.iterrows():\n", "    item_id = temp_df.loc[i, \"item_id\"]\n", "    try:\n", "        if item_id == temp_df.loc[i + 1, \"item_id\"]:\n", "            if item_id in spr_list:\n", "                #                 print('Spr')\n", "                correct_zone = (\n", "                    spr_items_df[spr_items_df[\"item_id\"].astype(int) == item_id][\n", "                        \"Actual_zone_id\"\n", "                    ]\n", "                    .values[0]\n", "                    .upper()\n", "                )\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "            #                 print('Item id', item_id)\n", "            #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "            else:\n", "                #                 print('Non Spr')\n", "                #                 print(df.loc[i,'actual_zone'][-3:])\n", "                correct_zone = temp_df.loc[i, \"actual_zone\"]\n", "                if (\n", "                    (temp_df.loc[i, \"actual_zone\"] == \"REGULAR_08\")\n", "                    or (temp_df.loc[i, \"actual_zone\"] == \"REGULAR_06\")\n", "                    or (temp_df.loc[i, \"actual_zone\"] == \"REGULAR_07\")\n", "                    or (temp_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\")\n", "                ):\n", "                    #         or (df.loc[i,'actual_zone'] == 'REGULAR_05'\n", "\n", "                    if temp_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\":\n", "                        correct_zone = \"LARGE_HEAVY_01\"\n", "                    else:\n", "                        correct_zone = \"REGULAR_03\"\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "    #                 print('Item id', item_id)\n", "    #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "    except:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df = (\n", "    df.drop([\"actual_zone\"], axis=1)\n", "    .drop_duplicates()\n", "    .merge(correct_zone_df, on=\"item_id\", how=\"left\")\n", ")\n", "result_df = result_df.merge(temp_df, on=\"item_id\", how=\"left\")\n", "result_df[\"actual_zone\"] = np.where(\n", "    result_df[\"actual_zone_x\"].isna(),\n", "    result_df[\"actual_zone_y\"],\n", "    result_df[\"actual_zone_x\"],\n", ")\n", "result_df = result_df.drop(\n", "    [\"index\", \"actual_zone_x\", \"actual_zone_y\"], axis=1\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out1_df = pd.read_sql(\n", "    \"\"\"\n", "SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS total_outbound1\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "   and co.id = 714\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') = CURRENT_DATE-1\n", "   GROUP BY 1,\n", "            2\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out3_df = pd.read_sql(\n", "    \"\"\"\n", "SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS total_outbound3\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "   and co.id = 714\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') BETWEEN CURRENT_DATE-3 AND CURRENT_DATE-1\n", "   GROUP BY 1,\n", "            2\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out7_df = pd.read_sql(\n", "    \"\"\"\n", "SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS total_outbound7\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "   and co.id = 714\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') BETWEEN CURRENT_DATE-7 AND CURRENT_DATE-1\n", "   GROUP BY 1,\n", "            2\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = (\n", "    result_df.merge(out1_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "    .merge(out3_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "    .merge(out7_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summ_df = ps.sqldf(\n", "    \"\"\"SELECT FACILITY_ID,\n", "    OUTLET_ID,\n", "    OUTLET_NAME,\n", "    ZONE_CONSTRUCT,\n", "    ACTUAL_ZONE,\n", "    count(distinct item_id) as Total_SKUs,\n", "    SUM (total_quantity) as total_quantity,\n", "    sum(total_outbound1) as total_outbound1,\n", "    sum(total_outbound3) as total_outbound3,\n", "    sum(total_outbound7) as total_outbound7\n", "    from df1\n", "    GROUP BY 1,2,3,4,5\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summ_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_df = pb.from_sheets(\"1V8tVfEFmPxBS_v8hmaB22NuQBDN1o5Qv_V9XnA4iI_Y\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["onshelf_inventory_df = pd.read_sql(\n", "    \"\"\"WITH cats AS\n", "  (WITH product_category AS\n", "     (SELECT P.ID AS PID,\n", "             P.NAME AS PRODUCT,\n", "             P.UNIT AS Unit,\n", "             C2.NAME AS L2,\n", "             (CASE\n", "                  WHEN C1.NAME = C.name THEN C2.name\n", "                  ELSE C1.name\n", "              END) AS L1,\n", "             C.NAME AS L0,\n", "             C.id as l0_id,\n", "             (CASE\n", "               WHEN C1.id = C.id THEN C2.id\n", "               ELSE C1.id\n", "             END) AS l1_id,\n", "             P.BRAND AS brand,\n", "             P.MANUFACTURER AS manf,\n", "             pt.name AS product_type\n", "      FROM lake_cms.gr_product P\n", "      INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "      INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "      AND PCM.IS_PRIMARY=TRUE\n", "      INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "      INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "      INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id) \n", "      SELECT DISTINCT rpc.item_id,\n", "                    cat.l0,\n", "                    cat.l1,\n", "                    cat.l2,\n", "                    cat.l0_id,cat.l1_id\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   WHERE rpc.product_id IS NOT NULL\n", "   ORDER BY 1,\n", "            2,\n", "            3),\n", "location_data as (SELECT item_level.facility_id,\n", "       item_level.outlet_id,\n", "       item_level.outlet_name,\n", "       (CASE\n", "            WHEN item_level.location_type = 1 THEN 'Entire Warehouse'\n", "            ELSE 'Secondary'\n", "        END) AS zone_construct,\n", "       item_level.current_zone,\n", "       zz.zone_type,\n", "       cats.l0_id,\n", "       cats.l1_id,\n", "       item_level.item_id,\n", "       items.name AS item_name,\n", "       item_level.location_quantity\n", "FROM\n", "  (SELECT outlet.facility_id,\n", "          outlet.id AS outlet_id,\n", "          outlet.name AS outlet_name,\n", "          wz.location_type,\n", "          wz.zone_identifier as current_zone,\n", "          pt.item_id,\n", "          sum(pt.quantity) AS location_quantity\n", "   FROM lake_warehouse_location.warehouse_item_location pt\n", "   INNER JOIN lake_warehouse_location.warehouse_storage_location wz ON wz.id = pt.location_id\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = pt.warehouse_id\n", "   INNER JOIN lake_retail.console_outlet outlet ON outlet.id=wom.outlet_id\n", "   AND outlet.id IN (714)\n", "   AND wz.is_active = 1\n", "   AND wz.location_type = (SELECT (CASE WHEN json_extract_path_text(pos_config,'auto_sto_wave_creation_enabled') = 'true' THEN 1 ELSE 2 END)\n", "      FROM lake_retail.console_outlet co\n", "      WHERE co.id = outlet.id)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,6) item_level\n", "LEFT JOIN cats ON cats.item_id = item_level.item_id\n", "LEFT JOIN lake_rpc.item_details items ON items.item_id=item_level.item_id\n", "LEFT JOIN\n", "  (SELECT handling_type,\n", "          storage_type,\n", "          zone_type\n", "   FROM lake_warehouse_location.item_zone_type) zz ON zz.handling_type = coalesce(cast(items.handling_type AS INT),0)\n", "AND zz.storage_type = coalesce(cast(items.storage_type AS INT),0)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11\n", "HAVING location_quantity > 0),\n", "actual_zones as (select wom.outlet_id,a.zone_identifier, b.zone_type, coalesce(l0_id,0) as l0_id,coalesce(l1_id,0) as l1_id\n", "from lake_warehouse_location.item_category_zone_type_zone_id b\n", "inner join lake_warehouse_location.warehouse_zone_id_mapping a on a.id = b.zone_id \n", "inner join lake_warehouse_location.warehouse_outlet_mapping wom on wom.warehouse_id = a.warehouse_id\n", "where outlet_id = 714\n", "AND a.enabled = 1 AND b.enabled = 1)\n", "\n", "select distinct a.facility_id, a.outlet_id,a.outlet_name,a.zone_construct,a.item_id,a.item_name,a.location_quantity,a.current_zone,coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) as actual_zone,\n", "(case when a.current_zone != actual_zone then 'YES' else 'NO' end) as Different_Zones\n", "from location_data a\n", "left join actual_zones z2 on z2.zone_type = a.zone_type and z2.l1_id = a.l1_id and z2.l0_id = a.l0_id and z2.outlet_id  = a.outlet_id\n", "left join actual_zones z1 on z1.zone_type = a.zone_type and z1.l0_id = a.l0_id and z1.l1_id = 0 and z1.l0_id != 0 and z1.outlet_id  = a.outlet_id\n", "left join actual_zones z on z.zone_type = a.zone_type and z.l0_id = 0 and z.outlet_id  = a.outlet_id\n", "where a.outlet_id = 714\n", "order by item_id, actual_zone\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_onshelf_inventory_df = (\n", "    onshelf_inventory_df[[\"item_id\", \"actual_zone\"]].drop_duplicates().reset_index()\n", ")\n", "# a = temp_onshelf_inventory_df.combine_first(onshelf_inventory_df)\n", "# temp1_onshelf_inventory_df = a[['facility_id','outlet_id','outlet_name','item_id','item_name','location_quantity','zone_construct','current_zone','actual_zone']]\n", "# temp1_onshelf_inventory_df = temp1_onshelf_inventory_df.astype({'item_id':'int','location_quantity':'int','facility_id':'int','outlet_id':'int'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_zone_df = pd.DataFrame(columns=[\"item_id\", \"actual_zone\"])\n", "for i, row in temp_onshelf_inventory_df.iterrows():\n", "    item_id = temp_onshelf_inventory_df.loc[i, \"item_id\"]\n", "    try:\n", "        if item_id == temp_onshelf_inventory_df.loc[i + 1, \"item_id\"]:\n", "            if item_id in spr_list:\n", "                #                 print('Spr')\n", "                correct_zone = (\n", "                    spr_items_df[spr_items_df[\"item_id\"].astype(int) == item_id][\n", "                        \"Actual_zone_id\"\n", "                    ]\n", "                    .values[0]\n", "                    .upper()\n", "                )\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "            #                 print('Item id', item_id)\n", "            #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "            else:\n", "                #                 print('Non Spr')\n", "                correct_zone = temp_onshelf_inventory_df.loc[i, \"actual_zone\"]\n", "                if (\n", "                    (temp_onshelf_inventory_df.loc[i, \"actual_zone\"] == \"REGULAR_08\")\n", "                    or (temp_onshelf_inventory_df.loc[i, \"actual_zone\"] == \"REGULAR_06\")\n", "                    or (temp_onshelf_inventory_df.loc[i, \"actual_zone\"] == \"REGULAR_07\")\n", "                    or (\n", "                        temp_onshelf_inventory_df.loc[i, \"actual_zone\"]\n", "                        == \"LARGE_HEAVY_02\"\n", "                    )\n", "                ):\n", "                    #                 or (temp_onshelf_inventory_df.loc[i,'actual_zone'] == 'REGULAR_05'\n", "                    if (\n", "                        temp_onshelf_inventory_df.loc[i, \"actual_zone\"]\n", "                        == \"LARGE_HEAVY_02\"\n", "                    ):\n", "                        correct_zone = \"LARGE_HEAVY_01\"\n", "                    else:\n", "                        correct_zone = \"REGULAR_03\"\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "    #                 print('Item id', item_id)\n", "    #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "    except:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_onshelf_df = (\n", "    onshelf_inventory_df.drop([\"actual_zone\", \"different_zones\"], axis=1)\n", "    .drop_duplicates()\n", "    .merge(correct_zone_df, on=\"item_id\", how=\"left\")\n", ")\n", "result_onshelf_df = result_onshelf_df.merge(\n", "    temp_onshelf_inventory_df, on=\"item_id\", how=\"left\"\n", ")\n", "result_onshelf_df[\"actual_zone\"] = np.where(\n", "    result_onshelf_df[\"actual_zone_x\"].isna(),\n", "    result_onshelf_df[\"actual_zone_y\"],\n", "    result_onshelf_df[\"actual_zone_x\"],\n", ")\n", "result_onshelf_df = result_onshelf_df.drop(\n", "    [\"index\", \"actual_zone_x\", \"actual_zone_y\"], axis=1\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["same_zone_list = []\n", "for i, row in result_onshelf_df.iterrows():\n", "    if row[\"current_zone\"] == row[\"actual_zone\"]:\n", "        same_zone_list.append(\"YES\")\n", "    else:\n", "        same_zone_list.append(\"NO\")\n", "result_onshelf_df[\"same_zone\"] = same_zone_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["putaway_df = pd.read_sql(\n", "    \"\"\"SELECT facility_id,\n", "       outlet_id,\n", "       outlet_name,\n", "       zone_construct,\n", "    --   putlist_id,\n", "       putaway_date,\n", "    --   putter_id,\n", "       item_id,\n", "       item_name,\n", "       zone_type,\n", "       l0_id,\n", "       l1_id,\n", "       putaway_zone,\n", "       actual_zone,\n", "       sum(placed_quantity) as placed_quantity\n", "FROM\n", "  (WITH cats AS\n", "     (WITH product_category AS\n", "        (SELECT P.ID AS PID,\n", "                P.NAME AS PRODUCT,\n", "                P.UNIT AS Unit,\n", "                C2.NAME AS L2,\n", "                (CASE\n", "                     WHEN C1.NAME = C.name THEN C2.name\n", "                     ELSE C1.name\n", "                 END) AS L1,\n", "                C.NAME AS L0,\n", "                C.id AS l0_id,\n", "                (CASE\n", "                     WHEN C1.id = C.id THEN C2.id\n", "                     ELSE C1.id\n", "                 END) AS l1_id,\n", "                P.BRAND AS brand,\n", "                P.MANUFACTURER AS manf,\n", "                pt.name AS product_type\n", "         FROM lake_cms.gr_product P\n", "         INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "         INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "         AND PCM.IS_PRIMARY=TRUE\n", "         INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "         INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "         INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id) SELECT DISTINCT rpc.item_id,\n", "                                                                                           cat.l0,\n", "                                                                                           cat.l1,\n", "                                                                                           cat.l2,\n", "                                                                                           cat.l0_id,\n", "                                                                                           cat.l1_id\n", "      FROM lake_rpc.item_product_mapping rpc\n", "      INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "      WHERE rpc.product_id IS NOT NULL\n", "      ORDER BY 1,\n", "               2,\n", "               3),\n", "        putaway_data AS\n", "     (SELECT outlet.facility_id,\n", "             outlet.id AS outlet_id,\n", "             outlet.name AS outlet_name,\n", "             (CASE\n", "                  WHEN wz.location_type = 1 THEN 'Entire Warehouse'\n", "                  ELSE 'Secondary'\n", "              END) AS zone_construct,\n", "             pt.id AS putlist_id,\n", "             (pti.updated_at+interval '5.5 hrs')::date AS putaway_date,\n", "             pt.assigned_to_name AS putter_id,\n", "             pti.item_id,\n", "             items.name AS item_name,\n", "             wz.zone_identifier AS putaway_zone,\n", "             zz.zone_type,\n", "             cats.l0_id,\n", "             cats.l1_id,\n", "             pti.placed_quantity\n", "      FROM lake_warehouse_location.warehouse_put_list pt\n", "      INNER JOIN lake_warehouse_location.warehouse_put_list_item pti ON pti.putlist_id = pt.id\n", "      INNER JOIN lake_warehouse_location.warehouse_put_list_item_location ptil ON pti.id = ptil.putlist_item_id\n", "      INNER JOIN lake_retail.console_outlet outlet ON outlet.id=pt.outlet_id\n", "      INNER JOIN lake_warehouse_location.warehouse_storage_location wz ON wz.id = ptil.location_id\n", "      AND is_active = 1\n", "      AND wz.location_type =(SELECT (CASE\n", "                     WHEN json_extract_path_text(pos_config,'auto_sto_wave_creation_enabled') = 'true' THEN 1\n", "                     ELSE 2\n", "                 END)\n", "         FROM lake_retail.console_outlet co\n", "         WHERE co.id = outlet.id)\n", "      INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = wz.warehouse_id\n", "      LEFT JOIN cats ON cats.item_id = pti.item_id\n", "      LEFT JOIN lake_rpc.item_details items ON items.item_id=pti.item_id\n", "      LEFT JOIN\n", "        (SELECT handling_type,\n", "                storage_type,\n", "                zone_type\n", "         FROM lake_warehouse_location.item_zone_type) zz ON zz.handling_type = coalesce(cast(items.handling_type AS INT),0)\n", "      AND zz.storage_type = coalesce(cast(items.storage_type AS INT),0)\n", "      WHERE outlet.id IN (714)\n", "          \n", "        AND (pti.updated_at+interval '5.5 hrs')::date = CURRENT_DATE-1 \n", "),\n", "        actual_zones AS\n", "     (SELECT wom.outlet_id,\n", "             a.zone_identifier,\n", "             b.zone_type,\n", "             coalesce(l0_id,0) AS l0_id,\n", "             coalesce(l1_id,0) AS l1_id\n", "      FROM lake_warehouse_location.item_category_zone_type_zone_id b\n", "      INNER JOIN lake_warehouse_location.warehouse_zone_id_mapping a ON a.id = b.zone_id\n", "      INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = a.warehouse_id\n", "      WHERE outlet_id = 714\n", "      AND a.enabled = 1 AND b.enabled = 1) SELECT DISTINCT a.facility_id,\n", "                                                                                                  a.outlet_id,\n", "                                                                                                  a.outlet_name,\n", "                                                                                                  a.zone_construct,\n", "                                                                                                  a.putlist_id,\n", "                                                                                                  a.putaway_date,\n", "                                                                                                  a.putter_id,\n", "                                                                                                  a.item_id,\n", "                                                                                                  a.item_name,\n", "                                                                                                  a.zone_type,\n", "                                                                                                  a.l0_id,\n", "                                                                                                  a.l1_id,\n", "                                                                                                  a.placed_quantity,\n", "                                                                                                  a.putaway_zone,\n", "                                                                                                  coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) AS actual_zone,\n", "                                                                                                  (CASE\n", "                                                                                                       WHEN a.putaway_zone != actual_zone THEN 'YES'\n", "                                                                                                       ELSE 'NO'\n", "                                                                                                   END) AS Different_Zones\n", "   FROM putaway_data a\n", "   LEFT JOIN actual_zones z2 ON z2.zone_type = a.zone_type\n", "   AND z2.l1_id = a.l1_id\n", "   AND z2.l0_id = a.l0_id\n", "   AND z2.outlet_id = a.outlet_id\n", "   LEFT JOIN actual_zones z1 ON z1.zone_type = a.zone_type\n", "   AND z1.l0_id = a.l0_id\n", "   AND z1.l1_id = 0\n", "   AND z1.l0_id != 0\n", "   AND z1.outlet_id = a.outlet_id\n", "   LEFT JOIN actual_zones z ON z.zone_type = a.zone_type\n", "   AND z.l0_id = 0\n", "   AND z.outlet_id = a.outlet_id\n", "   WHERE a.outlet_id = 714\n", "   ORDER BY a.item_id)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11,\n", "         12\n", "ORDER BY item_id,\n", "         actual_zone\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_putaway_df = putaway_df[[\"item_id\", \"actual_zone\"]].drop_duplicates().reset_index()\n", "# a = temp_putaway_df.combine_first(putaway_df)\n", "# temp1_putaway_df = a[['facility_id','outlet_id','outlet_name','putaway_date','item_id','item_name','placed_quantity','zone_construct','putaway_zone','actual_zone']]\n", "# temp1_putaway_df = temp1_putaway_df.astype({'item_id':'int','placed_quantity':'int','facility_id':'int','outlet_id':'int'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_zone_df = pd.DataFrame(columns=[\"item_id\", \"actual_zone\"])\n", "for i, row in temp_putaway_df.iterrows():\n", "    item_id = temp_putaway_df.loc[i, \"item_id\"]\n", "    try:\n", "        if item_id == temp_putaway_df.loc[i + 1, \"item_id\"]:\n", "            if item_id in spr_list:\n", "                #                 print('Spr')\n", "                correct_zone = (\n", "                    spr_items_df[spr_items_df[\"item_id\"].astype(int) == item_id][\n", "                        \"Actual_zone_id\"\n", "                    ]\n", "                    .values[0]\n", "                    .upper()\n", "                )\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "            #                 print('Item id', item_id)\n", "            #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "            else:\n", "                #                 print('Non Spr')\n", "                correct_zone = temp_putaway_df.loc[i, \"actual_zone\"]\n", "                if (\n", "                    (temp_putaway_df.loc[i, \"actual_zone\"] == \"REGULAR_08\")\n", "                    or (temp_putaway_df.loc[i, \"actual_zone\"] == \"REGULAR_06\")\n", "                    or (temp_putaway_df.loc[i, \"actual_zone\"] == \"REGULAR_07\")\n", "                    or (temp_putaway_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\")\n", "                ):\n", "                    #                     (temp_putaway_df.loc[i,'actual_zone'] == 'REGULAR_05'\n", "                    if temp_putaway_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\":\n", "                        correct_zone = \"LARGE_HEAVY_01\"\n", "                    else:\n", "                        correct_zone = \"REGULAR_03\"\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "    #                 print('Item id', item_id)\n", "    #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "    except:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_putaway_df = (\n", "    putaway_df.drop([\"actual_zone\"], axis=1)\n", "    .drop_duplicates()\n", "    .merge(correct_zone_df, on=\"item_id\", how=\"left\")\n", ")\n", "result_putaway_df = result_putaway_df.merge(temp_putaway_df, on=\"item_id\", how=\"left\")\n", "result_putaway_df[\"actual_zone\"] = np.where(\n", "    result_putaway_df[\"actual_zone_x\"].isna(),\n", "    result_putaway_df[\"actual_zone_y\"],\n", "    result_putaway_df[\"actual_zone_x\"],\n", ")\n", "result_putaway_df = result_putaway_df.drop(\n", "    [\"index\", \"actual_zone_x\", \"actual_zone_y\"], axis=1\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["same_zone_list = []\n", "for i, row in result_putaway_df.iterrows():\n", "    if row[\"putaway_zone\"] == row[\"actual_zone\"]:\n", "        same_zone_list.append(\"YES\")\n", "    else:\n", "        same_zone_list.append(\"NO\")\n", "result_putaway_df[\"same_zone\"] = same_zone_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["migration_df = pd.read_sql(\n", "    \"\"\"WITH cats AS\n", "  (WITH product_category AS\n", "     (SELECT P.ID AS PID,\n", "             P.NAME AS PRODUCT,\n", "             P.UNIT AS Unit,\n", "             C2.NAME AS L2,\n", "             (CASE\n", "                  WHEN C1.NAME = C.name THEN C2.name\n", "                  ELSE C1.name\n", "              END) AS L1,\n", "             C.NAME AS L0,\n", "             C.id as l0_id,\n", "             (CASE\n", "               WHEN C1.id = C.id THEN C2.id\n", "               ELSE C1.id\n", "             END) AS l1_id,\n", "             P.BRAND AS brand,\n", "             P.MANUFACTURER AS manf,\n", "             pt.name AS product_type\n", "      FROM lake_cms.gr_product P\n", "      INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "      INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "      AND PCM.IS_PRIMARY=TRUE\n", "      INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "      INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "      INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id) \n", "      SELECT DISTINCT rpc.item_id,\n", "                    cat.l0,\n", "                    cat.l1,\n", "                    cat.l2,\n", "                    cat.l0_id,cat.l1_id\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   WHERE rpc.product_id IS NOT NULL\n", "   ORDER BY 1,\n", "            2,\n", "            3),\n", "location_data as (\n", "SELECT outlet.facility_id,\n", "outlet.id AS outlet_id,\n", "       outlet.name as outlet_name,\n", "       (case when wz.location_type = 1 then 'Entire Warehouse' else 'Secondary' end) as zone_construct,\n", "       wz.zone_identifier as current_zone,\n", "       zz.zone_type,\n", "       cats.l0_id,cats.l1_id,\n", "       pt.item_id,\n", "       items.name AS item_name,\n", "       sum(pt.quantity) as location_quantity\n", "   FROM lake_warehouse_location.warehouse_item_location pt\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom on wom.warehouse_id = pt.warehouse_id \n", "   INNER JOIN lake_retail.console_outlet outlet ON outlet.id=wom.outlet_id\n", "   INNER JOIN lake_warehouse_location.warehouse_storage_location wz on wz.id = pt.location_id and wz.is_active = 1 and\n", "   wz.location_type = (select (case when json_extract_path_text(pos_config,'auto_sto_wave_creation_enabled') = 'true' then 1 else 2 end) from \n", "   lake_retail.console_outlet co where co.id = outlet.id)\n", "   LEFT JOIN cats ON cats.item_id = pt.item_id \n", "   LEFT JOIN lake_rpc.item_details items ON items.item_id=pt.item_id\n", "   LEFT join (select handling_type,storage_type,zone_type from lake_warehouse_location.item_zone_type) zz on zz.handling_type = coalesce(cast(items.handling_type AS INT),0) and zz.storage_type = coalesce(cast(items.storage_type AS INT),0)\n", "   WHERE outlet.id IN (714)\n", "   group by 1,2,3,4,5,6,7,8,9,10\n", "   having location_quantity > 0\n", "),\n", "actual_zones as (select wom.outlet_id,a.zone_identifier, b.zone_type, coalesce(l0_id,0) as l0_id,coalesce(l1_id,0) as l1_id\n", "from lake_warehouse_location.item_category_zone_type_zone_id b\n", "inner join lake_warehouse_location.warehouse_zone_id_mapping a on a.id = b.zone_id \n", "inner join lake_warehouse_location.warehouse_outlet_mapping wom on wom.warehouse_id = a.warehouse_id\n", "where outlet_id = 714\n", "AND a.enabled = 1 AND b.enabled = 1)\n", "\n", "select distinct a.facility_id,a.outlet_id,a.outlet_name,a.zone_construct,a.item_id,a.item_name,a.location_quantity,a.current_zone,coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) as actual_zone,\n", "(case when a.current_zone != actual_zone then 'YES' else 'NO' end) as Different_Zones\n", "from location_data a\n", "left join actual_zones z2 on z2.zone_type = a.zone_type and z2.l1_id = a.l1_id and z2.l0_id = a.l0_id and z2.outlet_id  = a.outlet_id\n", "left join actual_zones z1 on z1.zone_type = a.zone_type and z1.l0_id = a.l0_id and z1.l1_id = 0 and z1.l0_id != 0 and z1.outlet_id  = a.outlet_id\n", "left join actual_zones z on z.zone_type = a.zone_type and z.l0_id = 0 and z.outlet_id  = a.outlet_id\n", "where a.outlet_id = 714\n", "order by item_id, actual_zone\n", "\"\"\",\n", "    redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_migration_df = (\n", "    migration_df[[\"item_id\", \"actual_zone\"]].drop_duplicates().reset_index()\n", ")\n", "# a = temp_migration_df.combine_first(migration_df)\n", "# temp1_migration_df = a[['facility_id','outlet_id','outlet_name','item_id','item_name','location_quantity','zone_construct','current_zone','actual_zone']]\n", "# temp1_migration_df = temp1_migration_df.astype({'item_id':'int','location_quantity':'int','facility_id':'int','outlet_id':'int'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_zone_df = pd.DataFrame(columns=[\"item_id\", \"actual_zone\"])\n", "for i, row in temp_migration_df.iterrows():\n", "    item_id = temp_migration_df.loc[i, \"item_id\"]\n", "    try:\n", "        if item_id == temp_migration_df.loc[i + 1, \"item_id\"]:\n", "            if item_id in spr_list:\n", "                #                 print('Spr')\n", "                correct_zone = (\n", "                    spr_items_df[spr_items_df[\"item_id\"].astype(int) == item_id][\n", "                        \"Actual_zone_id\"\n", "                    ]\n", "                    .values[0]\n", "                    .upper()\n", "                )\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "            #                 print('Item id', item_id)\n", "            #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "            else:\n", "                #                 print('Non Spr')\n", "                correct_zone = temp_migration_df.loc[i, \"actual_zone\"]\n", "                if temp_migration_df.loc[i + 1, \"item_id\"] == \"REGULAR_05\":\n", "                    correct_zone = \"REGULAR_05\"\n", "                if (\n", "                    (temp_migration_df.loc[i, \"actual_zone\"] == \"REGULAR_08\")\n", "                    or (temp_migration_df.loc[i, \"actual_zone\"] == \"REGULAR_06\")\n", "                    or (temp_migration_df.loc[i, \"actual_zone\"] == \"REGULAR_07\")\n", "                    or (temp_migration_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\")\n", "                ):\n", "                    #                     or (temp_migration_df.loc[i,'actual_zone'] == 'REGULAR_05')\n", "                    if temp_migration_df.loc[i, \"actual_zone\"] == \"LARGE_HEAVY_02\":\n", "                        correct_zone = \"LARGE_HEAVY_01\"\n", "                    else:\n", "                        correct_zone = \"REGULAR_03\"\n", "                correct_zone_df = correct_zone_df.append(\n", "                    {\"item_id\": item_id, \"actual_zone\": correct_zone}, ignore_index=True\n", "                )\n", "    #                 print('Item id', item_id)\n", "    #                 print('Correct zone: ',correct_zone,'\\n')\n", "\n", "    except:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_migration_df = (\n", "    migration_df.drop([\"actual_zone\", \"different_zones\"], axis=1)\n", "    .drop_duplicates()\n", "    .merge(correct_zone_df, on=\"item_id\", how=\"left\")\n", ")\n", "result_migration_df = result_migration_df.merge(\n", "    temp_migration_df, on=\"item_id\", how=\"left\"\n", ")\n", "result_migration_df[\"actual_zone\"] = np.where(\n", "    result_migration_df[\"actual_zone_x\"].isna(),\n", "    result_migration_df[\"actual_zone_y\"],\n", "    result_migration_df[\"actual_zone_x\"],\n", ")\n", "result_migration_df = result_migration_df.drop(\n", "    [\"index\", \"actual_zone_x\", \"actual_zone_y\"], axis=1\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_migration_df = result_migration_df[\n", "    ~result_migration_df[\"item_id\"].isin([10001228, 10003581, 10044801, 10046676])\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["same_zone_list = []\n", "for i, row in result_migration_df.iterrows():\n", "    if row[\"current_zone\"] == row[\"actual_zone\"]:\n", "        same_zone_list.append(\"YES\")\n", "    else:\n", "        same_zone_list.append(\"NO\")\n", "result_migration_df[\"same_zone\"] = same_zone_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_onshelf_summ = ps.sqldf(\n", "    \"\"\"select facility_id, \n", "    outlet_id, \n", "    outlet_name, \n", "    zone_construct, \n", "    count (distinct item_id) as total_skus,\n", "    sum(location_quantity) as total_quantity,\n", "    count(case when same_zone = 'YES' then same_zone END) as skus_in_correct_zone,\n", "    sum(case when same_zone = 'YES' then location_quantity END) as quantity_in_correct_zone,\n", "    count(case when same_zone = 'YES' then same_zone END)*100.0/(count (distinct item_id)) as total_correct_skus_percentage,\n", "    (sum(case when same_zone = 'YES' then location_quantity END))*100.0/(sum(location_quantity)) as total_correct_quantity_percentage\n", "    from result_onshelf_df\n", "    group by 1,2,3,4   \n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_onshelf_summ"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_onshelf_df = result_onshelf_summ[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"zone_construct\",\n", "        \"total_skus\",\n", "        \"total_quantity\",\n", "        \"skus_in_correct_zone\",\n", "        \"quantity_in_correct_zone\",\n", "        \"total_correct_skus_percentage\",\n", "        \"total_correct_quantity_percentage\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_onshelf_df = sheet_onshelf_df.rename(\n", "    {\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"zone_construct\": \"Zone Construct\",\n", "        \"total_skus\": \"Total SKUs\",\n", "        \"total_quantity\": \"Total Quantity\",\n", "        \"skus_in_correct_zone\": \"SKUs Placed in Correct Zone\",\n", "        \"quantity_in_correct_zone\": \"Qty Placed in Correct Zone\",\n", "        \"total_correct_skus_percentage\": \"Zone SKU Adherenece\",\n", "        \"total_correct_quantity_percentage\": \"Zone Qty Adherence\",\n", "    },\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sheet_onshelf_df,\n", "    \"1V8tVfEFmPxBS_v8hmaB22NuQBDN1o5Qv_V9XnA4iI_Y\",\n", "    \"Dasna_onshelf_zone_adherence\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_putaway_summ = ps.sqldf(\n", "    \"\"\"select facility_id, \n", "    outlet_id, \n", "    outlet_name, \n", "    zone_construct, \n", "    putaway_date,\n", "    count(distinct item_id) as putaway_skus, \n", "    sum(placed_quantity) as putaway_quantity,\n", "    count(case when same_zone = 'YES' then same_zone END) as putaway_skus_in_zone,\n", "    sum(case when same_zone = 'YES' then placed_quantity END) as putaway_qty_in_zone,\n", "    count(case when same_zone = 'YES' then same_zone END)*100.0/count(distinct item_id) as putaway_skus_zone_adhereance,\n", "    sum(case when same_zone = 'YES' then placed_quantity END)*100.0/sum(placed_quantity) as putaway_qty_zone_adhereance\n", "    from result_putaway_df\n", "    group by 1,2,3,4,5 \n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_putaway_summ"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_putaway_df = result_putaway_summ[\n", "    [\n", "        \"zone_construct\",\n", "        \"putaway_date\",\n", "        \"putaway_skus\",\n", "        \"putaway_quantity\",\n", "        \"putaway_skus_in_zone\",\n", "        \"putaway_qty_in_zone\",\n", "        \"putaway_skus_zone_adhereance\",\n", "        \"putaway_qty_zone_adhereance\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_putaway_df = sheet_putaway_df.rename(\n", "    {\n", "        \"zone_construct\": \"Zone Construct\",\n", "        \"putaway_date\": \"Putaway Date\",\n", "        \"putaway_skus\": \"SKUs Putawayed\",\n", "        \"putaway_quantity\": \"Quantity Putawayed\",\n", "        \"putaway_skus_in_zone\": \"SKUs Placed in Correct Zone\",\n", "        \"putaway_qty_in_zone\": \"Qty Placed in Correct Zone\",\n", "        \"putaway_skus_zone_adhereance\": \"Zone SKU Adherenece\",\n", "        \"putaway_qty_zone_adhereance\": \"Zone Qty Adherence\",\n", "    },\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    sheet_putaway_df,\n", "    \"1V8tVfEFmPxBS_v8hmaB22NuQBDN1o5Qv_V9XnA4iI_Y\",\n", "    \"Dasna_putaway_adherence\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def send_email(facility_id):\n", "    df[df[\"facility_id\"] == facility_id].to_csv(\"Raw_Data.csv\", index=False)\n", "    result_migration_df[\n", "        (result_migration_df[\"facility_id\"] == facility_id)\n", "        & (result_migration_df[\"same_zone\"] == \"NO\")\n", "    ].to_csv(\"Zone_Mismatch.csv\", index=False)\n", "    today = date.today()\n", "    yesterday = today - <PERSON><PERSON><PERSON>(days=1)\n", "    print(str(yesterday))\n", "    from_email = \"<EMAIL>\"\n", "    to_email = list(\n", "        email_df[email_df.facility_id == str(facility_id)].email_ids.unique()\n", "    )\n", "    cc = list(email_df[email_df.facility_id == \"common\"].email_ids.unique())\n", "\n", "    #     to_email = [\"<EMAIL>\"]\n", "    #     cc = []\n", "    loader = jinja2.FileSystemLoader(\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/etl/zone_construct_mailer\"\n", "        #         searchpath=\"/home/<USER>/ZoneConstruct/Dasna\"\n", "    )\n", "\n", "    outlets_onshelf = list(\n", "        onshelf_inventory_df[\n", "            onshelf_inventory_df[\"facility_id\"] == facility_id\n", "        ].outlet_id.unique()\n", "    )\n", "\n", "    outlets_put = list(\n", "        putaway_df[putaway_df[\"facility_id\"] == facility_id].outlet_id.unique()\n", "    )\n", "\n", "    print(\"-------\")\n", "\n", "    onshelf_inv_sku_adherence = []\n", "    onshelf_inv_qty_adherence = []\n", "    putaway_sku_adherence = []\n", "    putaway_qty_adherence = []\n", "    outlets_onshelf_names = []\n", "    outlets_put_names = []\n", "\n", "    for outlet in outlets_onshelf:\n", "        outlets_onshelf_names.append(\n", "            summ_df[summ_df[\"outlet_id\"] == outlet].outlet_name.unique()[0]\n", "        )\n", "    #     print(outlets_onshelf_names)\n", "\n", "    for outlet in outlets_put:\n", "        outlets_put_names.append(\n", "            summ_df[summ_df[\"outlet_id\"] == outlet].outlet_name.unique()[0]\n", "        )\n", "    #     print(outlets_put_names)\n", "\n", "    subject = f\"Zone construct report at Dasna - Outlet 714 for {yesterday}\"\n", "\n", "    for outlet_id in outlets_onshelf:\n", "        onshelf_inv_sku_adherence.append(\n", "            result_onshelf_summ[result_onshelf_summ[\"outlet_id\"] == outlet_id]\n", "            .reset_index()\n", "            .iloc[:, 9][0]\n", "            .round(2)\n", "        )\n", "        onshelf_inv_qty_adherence.append(\n", "            result_onshelf_summ[result_onshelf_summ[\"outlet_id\"] == outlet_id]\n", "            .reset_index()\n", "            .iloc[:, 10][0]\n", "            .round(2)\n", "        )\n", "    #     print(onshelf_inv_sku_adherence)\n", "    #     print(onshelf_inv_qty_adherence)\n", "\n", "    for outlet_id in outlets_put:\n", "        putaway_sku_adherence.append(\n", "            result_putaway_summ[result_putaway_summ[\"outlet_id\"] == outlet_id]\n", "            .reset_index()\n", "            .iloc[:, 10][0]\n", "            .round(2)\n", "        )\n", "        #         print(putaway_sku_adherence)\n", "        putaway_qty_adherence.append(\n", "            result_putaway_summ[result_putaway_summ[\"outlet_id\"] == outlet_id]\n", "            .reset_index()\n", "            .iloc[:, 11][0]\n", "            .round(2)\n", "        )\n", "    #         print(putaway_qty_adherence)\n", "\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "    template = tmpl_environ.get_template(\"email_template.html\")\n", "\n", "    html_content = template.render(\n", "        reports=summ_df[summ_df.facility_id == facility_id].to_dict(orient=\"records\"),\n", "        today=yesterday,\n", "        onshelf_inv_sku_adherence=onshelf_inv_sku_adherence,\n", "        onshelf_inv_qty_adherence=onshelf_inv_qty_adherence,\n", "        putaway_sku_adherence=putaway_sku_adherence,\n", "        putaway_qty_adherence=putaway_qty_adherence,\n", "        outlets_onshelf=outlets_onshelf,\n", "        outlets_put=outlets_put,\n", "        outlets_onshelf_names=outlets_onshelf_names,\n", "        outlets_put_names=outlets_put_names,\n", "    )\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        cc=cc,\n", "        files=[\"Raw_Data.csv\", \"Zone_Mismatch.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["send_email(92)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}