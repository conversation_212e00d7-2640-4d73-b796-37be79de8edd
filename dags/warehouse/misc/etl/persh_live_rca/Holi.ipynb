{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import requests\n", "import time\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, time\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")\n", "now = datetime.now(tz)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Search Indexing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def indexing(index_input):\n", "    df_i = pd.DataFrame()\n", "    for productID in index_input:\n", "        if productID != 0:\n", "            url = f\"http://172.31.94.54:5001/product-indexing/all-merchants/{int(productID)}\"\n", "            try:\n", "                response = requests.request(\"GET\", url)\n", "                # time.sleep(1)\n", "                print(productID, response.json()[\"Missing\"])\n", "            except:\n", "                continue\n", "            if not (\n", "                response.json()[\"Missing\"] == \"No mapping found for product ID\"\n", "                or len(response.json()[\"Missing\"]) == 0\n", "            ):\n", "                frontends = pd.read_sql_query(\n", "                    \"\"\"select merchant_id from lake_cms.gr_merchant_product_mapping \n", "                                          where id in ({missing})\"\"\".format(\n", "                        missing=\",\".join([str(x) for x in response.json()[\"Missing\"]])\n", "                    ),\n", "                    redshift,\n", "                ).merchant_id.unique()\n", "                for i in frontends:\n", "                    df = pd.DataFrame({\"product_id\": [productID], \"frontend_id\": [i]})\n", "                    df_i = pd.concat([df_i, df], axis=0)\n", "\n", "    return df_i"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Alerts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def alerts(a, b, c):\n", "    import requests\n", "    from slack_sdk import WebClient\n", "    from slack_sdk.errors import SlackApiError\n", "\n", "    text = f\"Search Indexing Report for Valentine's Day Products (Report generated at {now}) with positive inventory and NO CMS issues:\"\n", "    text1 = f\"Unique Frontend Merchants not indexed: {a}, Unique Product ID's not indexed: {b}, Unique Mappings not indexed: {c}\"\n", "    text2 = \"To check the raw data, use this sheet: https://docs.google.com/spreadsheets/d/1-v_AMKltNY447GWneaM_x_3cNkmNKF5IEIUAiZvK9lc\"\n", "    # fig, ax = render_mpl_table(fc_level_df, header_columns=0)\n", "    # fig.savefig(\"STO_delayed_report.png\")\n", "    token = \"******************************************************\"\n", "    client = WebClient(token=token)\n", "    file_check = \"./indexing_issues.csv\"\n", "    channel = \"indexing-misses\"\n", "    try:\n", "        response = client.chat_postMessage(channel=channel, text=text)\n", "        response = client.chat_postMessage(channel=channel, text=text1)\n", "        response = client.chat_postMessage(channel=channel, text=text2)\n", "        filepath = file_check\n", "        response = client.files_upload(channels=channel, file=filepath)\n", "    except SlackApiError as e:\n", "        assert e.response[\"ok\"] is False\n", "        assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "        print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def tracker(p_ids, f_ids, sheet_id, sheet_name):\n", "    persh_query = f\"\"\"\n", "        SELECT merchant_id as frontend_id, m.name as frontend_name, m.gr_id as frontend_gr_id,\n", "         product_id, p.name as product_name,p.enabled_flag as product_enabled_flag,\n", "        fmpm.inventory_limit,fmpm.master_inventory_limit, fmpm.enabled_flag, priority_order,\n", "        pp.price, pp.mrp from lake_cms.gr_merchant_product_mapping fmpm \n", "        left join lake_cms.view_gr_merchant m on m.id = fmpm.merchant_id \n", "        LEFT JOIN lake_cms.gr_product p on p.id = fmpm.product_id\n", "        LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = fmpm.merchant_id\n", "                                                AND pp.cms_product_id = fmpm.product_id\n", "        WHERE fmpm.merchant_id IN {*f_ids,}\n", "          AND fmpm.product_id IN {*p_ids,}\"\"\"\n", "    persh_pids = pd.read_sql_query(persh_query, con=redshift)\n", "\n", "    print(\"stage1 complete\")\n", "\n", "    cats = pd.read_sql_query(\n", "        \"\"\"\n", "       select distinct P.ID AS PID,\n", "              P.NAME AS PRODUCT,\n", "              P.UNIT AS Unit,\n", "              C2.NAME AS L2,\n", "              (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "              C.NAME AS L0,\n", "              C.ID as L0_id, \n", "              (case when C1.NAME = C.name then C2.ID else C1.ID end) AS L1_id,\n", "              P.BRAND AS brand,\n", "              P.MANUFACTURER AS manf,\n", "              pt.name as product_type\n", "       from lake_cms.gr_product P\n", "       INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "       INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID AND PCM.IS_PRIMARY=TRUE\n", "       INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "       INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "       INNER JOIN lake_cms.gr_product_type pt on p.type_id=pt.id\"\"\",\n", "        redshift,\n", "    )\n", "    print(\"stage2 complete\")\n", "\n", "    persh_pids = persh_pids.merge(\n", "        cats[[\"pid\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "        left_on=[\"product_id\"],\n", "        right_on=[\"pid\"],\n", "        how=\"left\",\n", "    )\n", "    print(\"stage3 complete\")\n", "\n", "    prodct_cat = pd.read_sql_query(\n", "        \"\"\"select product_id, category_id from\n", "                                  lake_cms.gr_product_category_mapping \n", "                                  where is_primary = 1\"\"\",\n", "        redshift,\n", "    )\n", "    print(\"stage4 complete\")\n", "\n", "    merchant_cat = pd.read_sql_query(\n", "        \"\"\"select merchant_id, category_id from\n", "                                  lake_cms.gr_merchant_category_mapping\"\"\",\n", "        redshift,\n", "    )\n", "    print(\"stage5 complete\")\n", "\n", "    d1 = df1.merge(\n", "        persh_pids,\n", "        left_on=[\"f_ids\", \"pids\"],\n", "        right_on=[\"frontend_id\", \"product_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    print(\"stage6 complete\")\n", "    d2 = d1.merge(prodct_cat, on=[\"product_id\"], how=\"left\")\n", "\n", "    d3 = d2.merge(\n", "        merchant_cat,\n", "        left_on=[\"frontend_id\", \"category_id\"],\n", "        right_on=[\"merchant_id\", \"category_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    print(\"stage7 complete\")\n", "\n", "    d3[\"product_category_missing_in_merchant\"] = np.where(\n", "        d3[\"merchant_id\"].isna(), 1, 0\n", "    )\n", "    d3[\"product_category_missing\"] = np.where(d3[\"category_id\"].isna(), 1, 0)\n", "    d3[\"mrp_missing\"] = np.where(d3[\"mrp\"].isna(), 1, 0)\n", "    d3[\"price_missing\"] = np.where(d3[\"price\"].isna(), 1, 0)\n", "    d3[\"fe_enabled_flag_issue\"] = np.where(d3[\"enabled_flag\"] == 0, 1, 0)\n", "    d3[\"inventory_limit_fe_issue\"] = np.where(d3[\"inventory_limit\"] == 0, 1, 0)\n", "\n", "    merchant_mapping = pd.read_sql_query(\n", "        f\"\"\"select vrmm.virtual_merchant_id as f_merchant_id, vrmm.real_merchant_id as b_merchant_id, m.gr_id as backend_gr_id,\n", "        dm.chain_id, dm.chain_name\n", "        from lake_cms.gr_virtual_to_real_merchant_mapping vrmm\n", "        join dwh.dim_merchant dm on dm.merchant_id = vrmm.virtual_merchant_id and is_current = 1\n", "        join lake_cms.view_gr_merchant m on m.id = vrmm.real_merchant_id\n", "        where vrmm.enabled_flag = true and vrmm.virtual_merchant_id in {*f_ids,}\"\"\",\n", "        redshift,\n", "    )\n", "    print(\"stage8 complete\")\n", "\n", "    d4 = d3.merge(\n", "        merchant_mapping,\n", "        left_on=[\"frontend_id\"],\n", "        right_on=[\"f_merchant_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    print(\"stage9 complete\")\n", "\n", "    d4[\"be_fe_merchant_mappping_missing\"] = np.where(d4[\"b_merchant_id\"].isna(), 1, 0)\n", "\n", "    b_merchants = d4.b_merchant_id.fillna(0).unique()\n", "    b_merchant_product = pd.DataFrame()\n", "    #     print(len(b_merchants))\n", "    n = len(b_merchants)\n", "    inc = int(math.sqrt(len(b_merchants)))\n", "\n", "    for j in range(0, n, inc):\n", "        if j + inc < n:\n", "            b_mercs = b_merchants[j : j + inc]\n", "        else:\n", "            b_mercs = b_merchants[j:n]\n", "\n", "        if len(b_mercs) == 1:\n", "            b_mercs = np.append(b_mercs, 0)\n", "        #         print(b_mercs)\n", "        b_temp = pd.read_sql_query(\n", "            f\"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "                                            enabled_flag, priority_order, pp.price, pp.mrp\n", "                                            from lake_cms.gr_merchant_product_mapping mp\n", "                                            LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "                                            AND pp.cms_product_id = mp.product_id\n", "                                            where merchant_id in {*b_mercs,}\"\"\",\n", "            redshift,\n", "        )\n", "        b_merchant_product = pd.concat([b_merchant_product, b_temp], axis=0)\n", "\n", "    print(\"stage10 complete\")\n", "\n", "    d5 = d4.merge(\n", "        b_merchant_product,\n", "        left_on=[\"b_merchant_id\", \"product_id\"],\n", "        right_on=[\"merchant_id\", \"product_id\"],\n", "        how=\"left\",\n", "        suffixes=(\"_fe\", \"_be\"),\n", "    )\n", "\n", "    print(\"stage11 complete\")\n", "\n", "    d5[\"be_merchant_product_missing (store mapping)\"] = np.where(\n", "        d5[\"merchant_id_be\"].isna(), 1, 0\n", "    )\n", "    d5[\"inventory_limit_be_issue\"] = np.where(d5[\"inventory_limit_be\"] == 0, 1, 0)\n", "    d5[\"inventory_limit_be_fe_mismatch_issue\"] = np.where(\n", "        d5[\"inventory_limit_be\"] != d5[\"inventory_limit_fe\"], 1, 0\n", "    )\n", "\n", "    prod_map = pd.read_sql_query(\n", "        \"\"\"select item_id, product_id, offer_id\n", "                                from lake_view_rpc.item_product_mapping\n", "                                where active = 1\"\"\",\n", "        presto,\n", "    )\n", "\n", "    d6 = d5.merge(prod_map, left_on=[\"product_id\"], right_on=[\"product_id\"], how=\"left\")\n", "\n", "    print(\"stage12 complete\")\n", "\n", "    d6[\"item_product_mapping_missing\"] = np.where(d6[\"item_id\"].isna(), 1, 0)\n", "\n", "    backend_outlet = pd.read_sql_query(\n", "        \"\"\"select  c.outlet_id, c.cms_store, m.name as be_name\n", "       from lake_retail.console_outlet_cms_store c\n", "       join lake_cms.view_gr_merchant m on m.id = c.cms_store \n", "       join dwh.dim_merchant dm on dm.merchant_id = m.id and is_current = 1 and dm.chain_id = 177\n", "       where c.active = 1 and c.cms_update_active = 1 and c.outlet_id in (select id \n", "       from lake_retail.console_outlet where business_type_id = 7)\"\"\",\n", "        redshift,\n", "    )\n", "\n", "    print(\"stage13 complete\")\n", "\n", "    d7 = d6.merge(\n", "        backend_outlet, left_on=[\"b_merchant_id\"], right_on=[\"cms_store\"], how=\"left\"\n", "    )\n", "\n", "    print(\"stage14 complete\")\n", "\n", "    d7[\"outlet_be_merchant_missing\"] = np.where(d7[\"outlet_id\"].isna(), 1, 0)\n", "\n", "    item_inv = pd.read_sql_query(\n", "        \"\"\"select outlet_id, co.name as outlet_name, i.item_id, id.name as item_name, quantity, threshold_quantity,\n", "    (case when co.active = 1 and co.device_id <> 47 then 1 else 0 end) as outlet_flag\n", "    from lake_view_ims.ims_item_inventory i\n", "    join lake_rpc.item_details id on id.item_id = i.item_id and perishable = 1\n", "    join lake_retail.console_outlet co on co.id =  i.outlet_id and business_type_id = 7\n", "    where i.active = 1 and quantity > 0\"\"\",\n", "        presto,\n", "    )\n", "\n", "    print(\"stage15 complete\")\n", "\n", "    d8 = d7.merge(\n", "        item_inv,\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    print(\"stage16 complete\")\n", "\n", "    d8[\"positive_inventory\"] = np.where(d8[\"quantity\"].isna(), 0, 1)\n", "    d8[\"inventory_limit_be_issue\"] = np.where(\n", "        d8[\"positive_inventory\"] == 0, 0, d8[\"inventory_limit_be_issue\"]\n", "    )\n", "    d8[\"inventory_limit_fe_issue\"] = np.where(\n", "        d8[\"positive_inventory\"] == 0, 0, d8[\"inventory_limit_fe_issue\"]\n", "    )\n", "    d9 = d8[\n", "        [\n", "            \"frontend_id\",\n", "            \"frontend_name\",\n", "            \"frontend_gr_id\",\n", "            \"b_merchant_id\",\n", "            \"backend_gr_id\",\n", "            \"product_id\",\n", "            \"product_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "            \"product_enabled_flag\",\n", "            \"product_category_missing_in_merchant\",\n", "            \"product_category_missing\",\n", "            \"mrp_missing\",\n", "            \"price_missing\",\n", "            \"fe_enabled_flag_issue\",\n", "            \"inventory_limit_fe_issue\",\n", "            \"be_fe_merchant_mappping_missing\",\n", "            \"be_merchant_product_missing (store mapping)\",\n", "            \"inventory_limit_be_issue\",\n", "            \"inventory_limit_be_fe_mismatch_issue\",\n", "            \"item_product_mapping_missing\",\n", "            \"outlet_be_merchant_missing\",\n", "            \"positive_inventory\",\n", "        ]\n", "    ].rename(columns={\"f_ids\": \"frontend_merchant_id\", \"pids\": \"product_id\"})\n", "\n", "    d9[\"total_flags\"] = d9[\n", "        [\n", "            \"product_category_missing_in_merchant\",\n", "            \"product_category_missing\",\n", "            \"mrp_missing\",\n", "            \"price_missing\",\n", "            \"fe_enabled_flag_issue\",\n", "            \"inventory_limit_fe_issue\",\n", "            \"be_fe_merchant_mappping_missing\",\n", "            \"be_merchant_product_missing (store mapping)\",\n", "            \"inventory_limit_be_issue\",\n", "            \"inventory_limit_be_fe_mismatch_issue\",\n", "            \"item_product_mapping_missing\",\n", "            \"outlet_be_merchant_missing\",\n", "        ]\n", "    ].sum(axis=1)\n", "\n", "    index_input = d9.product_id.fillna(0).unique()\n", "    df_i = pd.DataFrame()\n", "    df_i = indexing(index_input)\n", "    if \"product_id\" in df_i:\n", "        df_i[\"indexed\"] = 0\n", "        d10 = d9.merge(df_i, on=[\"product_id\", \"frontend_id\"], how=\"left\")\n", "        d10[\"not_indexed_flag\"] = np.where(d10[\"indexed\"] == 0, 1, 0)\n", "        d10 = d10.drop(columns=[\"indexed\"])\n", "    else:\n", "        d10 = d9.copy()\n", "        d10[\"not_indexed_flag\"] = \"N/A\"\n", "\n", "    d10[\"light_house_link\"] = (\n", "        \"https://lighthouse.grofers.com/dashboard/merchants/\"\n", "        + d10[\"frontend_id\"].fillna(0).astype(int).astype(str)\n", "        + \"/products/\"\n", "        + d10[\"product_id\"].fillna(0).astype(int).astype(str)\n", "    )\n", "    d10 = d10[(~d10[\"frontend_id\"].isna()) & (~d10[\"product_id\"].isna())]\n", "    d10.drop(\n", "        d10[d10.total_flags == 0].index, inplace=True\n", "    )  # dropping all rows where sum of all flags is 0\n", "\n", "    d11 = d10[\n", "        (d10[\"total_flags\"] == 0)\n", "        & (d10[\"positive_inventory\"] == 1)\n", "        & (d10[\"not_indexed_flag\"] == 1)\n", "    ]\n", "\n", "    a = d11.frontend_id.nunique()\n", "    b = d11.product_id.nunique()\n", "    c = d11[[\"frontend_id\", \"product_id\"]].drop_duplicates().shape[0]\n", "\n", "    d11[[\"frontend_id\", \"product_id\"]].to_csv(\"indexing_issues.csv\")\n", "\n", "    alerts(a, b, c)\n", "\n", "    pb.to_sheets(d10, sheet_id, sheet_name)\n", "\n", "    print(\"completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pb.from_sheets(\"1-_NIDKOisxS4V18csVJ-TNBhNbF8bHWF9IbOxdDgpoo\", \"Inputs Sheet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# taking input from each row of input sheet\n", "length = len(data[\"Pids comma separated\"])\n", "for i in range(0, length):\n", "    if len(data.iloc[i][\"Pids comma separated\"]) > 0:\n", "        p_ids = set(x.strip() for x in data.iloc[i][\"Pids comma separated\"].split(\",\"))\n", "        p_ids = list(p_ids)\n", "    else:\n", "        p_ids = []\n", "    if len(data.iloc[i][\"Fids comma separated (Leave blank for all fids )\"]) > 0:\n", "        f_ids = set(\n", "            x.strip()\n", "            for x in data.iloc[i][\n", "                \"Fids comma separated (Leave blank for all fids )\"\n", "            ].split(\",\")\n", "        )\n", "        f_ids = list(f_ids)\n", "    else:\n", "        f_ids = []\n", "    sheet_id = data.iloc[i][\"Output  spreadsheet Id\"]\n", "    sheet_name = data.iloc[i][\"Sheet Name\"]\n", "\n", "    query = \"\"\"SELECT vm.id as frontend\n", "    FROM lake_cms.gr_virtual_to_real_merchant_mapping vrm\n", "    JOIN lake_cms.view_gr_merchant vm ON vrm.virtual_merchant_id = vm.id\n", "    JOIN lake_cms.view_gr_merchant rm ON vrm.real_merchant_id = rm.id\n", "    WHERE vrm.enabled_flag = TRUE\n", "    AND vm.enabled_flag = TRUE\n", "    AND vm.virtual_type='superstore_merchant'\n", "    AND vm.current_state!='content'\n", "    AND rm.virtual_type = 'not_applicable'\"\"\"\n", "    fronts = pd.read_sql_query(query, redshift)\n", "\n", "    if len(f_ids) > 0:\n", "        for j in range(len(f_ids) - 1, -1, -1):\n", "            if f_ids[j].isdigit():\n", "                continue\n", "            else:\n", "                f_ids.pop(j)\n", "    else:\n", "        f_ids = (\n", "            fronts.frontend.unique()\n", "        )  # if input cell is blank..take input from the query\n", "\n", "    n = len(p_ids)\n", "    m = len(f_ids)\n", "    for j in range(n - 1, -1, -1):\n", "        if p_ids[j].isdigit():\n", "            continue\n", "        else:\n", "            p_ids.pop(j)\n", "\n", "    p_ids = list([int(i) for i in p_ids])\n", "    f_ids = list([int(i) for i in f_ids])\n", "\n", "    excepts = [\n", "        31151,\n", "        31141,\n", "        31142,\n", "        31143,\n", "        31144,\n", "        31145,\n", "        31146,\n", "        31147,\n", "        31148,\n", "        31150,\n", "        31180,\n", "        31161,\n", "        31162,\n", "        31163,\n", "        31164,\n", "        31165,\n", "        31168,\n", "        31169,\n", "        31170,\n", "        31171,\n", "        28663,\n", "        28800,\n", "        28614,\n", "        26511,\n", "        26782,\n", "        26255,\n", "        26942,\n", "        30978,\n", "        29701,\n", "        26943,\n", "        26253,\n", "        29981,\n", "        26389,\n", "        27364,\n", "        26528,\n", "        29704,\n", "        28864,\n", "        28696,\n", "        28755,\n", "        28798,\n", "        26780,\n", "        26171,\n", "        26049,\n", "        30963,\n", "        26383,\n", "        25568,\n", "        26379,\n", "        29749,\n", "        28822,\n", "        29664,\n", "        28809,\n", "        28734,\n", "        28707,\n", "        29705,\n", "        28683,\n", "        28706,\n", "        30634,\n", "        31594,\n", "        29832,\n", "        26197,\n", "        29877,\n", "        30021,\n", "        28472,\n", "        29846,\n", "    ]\n", "    f_ids = list(set(f_ids) - set(excepts))\n", "\n", "    if len(p_ids) == 0:\n", "        p_ids.append(0)\n", "        p_ids.append(0)\n", "    if len(p_ids) == 1:\n", "        p_ids.append(0)\n", "    if len(f_ids) == 1:\n", "        f_ids.append(0)\n", "\n", "    #     creating a df of p_ids and f_ids\n", "    from itertools import product\n", "\n", "    df1 = pd.DataFrame(list(product(f_ids, p_ids)), columns=[\"f_ids\", \"pids\"])\n", "\n", "    tracker(p_ids, f_ids, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d11[[\"frontend_id\", \"product_id\"]].to_csv(\"indexing_issues.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(d10, \"1-v_AMKltNY447GWneaM_x_3cNkmNKF5IEIUAiZvK9lc\", \"Data (with flags)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}