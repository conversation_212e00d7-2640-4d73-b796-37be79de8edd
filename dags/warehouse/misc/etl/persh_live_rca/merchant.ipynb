{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Backward"]}, {"cell_type": "markdown", "metadata": {}, "source": ["f_ids = f_merchants"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# p_ids = [394525,396899,396899,477000,427121,427121,477797]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# from itertools import product\n", "# df1 = pd.DataFrame(list(product(f_ids, p_ids)), columns=['f_ids', 'pids'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# df1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["persh_query = f\"\"\"WITH perishables AS (SELECT p.name,\n", "       CAST(p.id AS text) as product_id,\n", "       CAST(pcm.category_id AS text),\n", "       CAST(p.type_id AS text) -- COUNT(p.id)\n", "FROM lake_cms.gr_product p\n", "LEFT JOIN lake_cms.gr_product_category_mapping pcm ON pcm.product_id = p.id\n", "AND pcm.is_primary = TRUE\n", "LEFT JOIN lake_cms.gr_product_type pt ON pt.id = p.type_id\n", "WHERE (pcm.category_id = 1094\n", "       AND p.type_id = 1810)\n", "  OR (pcm.category_id = 1090\n", "      AND p.type_id = 3768)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 6858)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 2456)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 6844)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 1899)\n", "  OR (pcm.category_id = 1093\n", "      AND p.type_id = 8041)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 7747)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 9070)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 9230)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 9070)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 893)\n", "  OR (pcm.category_id = 950\n", "      AND p.type_id = 557)\n", "  OR (pcm.category_id = 34\n", "      AND p.type_id = 10317)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 5905)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3915)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 3715)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 9730)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2210)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2024)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 9603)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 5748)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 5868)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4346)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 9116)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4923)\n", "  OR (pcm.category_id = 1093\n", "      AND p.type_id = 5909)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 10555)\n", "  OR (pcm.category_id = 1956\n", "      AND p.type_id = 6844)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 9434)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5920)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3512)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2061)\n", "  OR (pcm.category_id = 1425\n", "      AND p.type_id = 5018)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 4205)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 479)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 9722)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 142)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2355)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 5113)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 7584)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4611)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5917)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3297)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3398)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 12051)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 9669)\n", "  OR (pcm.category_id = 1185\n", "      AND p.type_id = 623)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 4115)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 6263)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 6620)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4666)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5203)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 9632)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 8836)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2437)\n", "  OR (pcm.category_id = 950\n", "      AND p.type_id = 9485)\n", "  OR (pcm.category_id = 949\n", "      AND p.type_id = 5905)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3619)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 6844)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 1141)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 4611)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 5642)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 11247)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 9676)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3413)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 3249)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5748)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 10522)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 893)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 1141)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 11514)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 10345)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 3303)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 6467)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 10345)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2456)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 2828)\n", "  OR (pcm.category_id = 950\n", "      AND p.type_id = 9430)\n", "  OR (pcm.category_id = 1094\n", "      AND p.type_id = 8642)\n", "  OR (pcm.category_id = 1093\n", "      AND p.type_id = 2717)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 830)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4521)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 5784)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 8638)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5368)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3345)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 802)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 460)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 10427)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 9457)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 10427)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 10758)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 1778)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 11968)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 11035)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3447)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 4662)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 10522)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 3322)\n", "  OR (pcm.category_id = 197\n", "      AND p.type_id = 3447)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 77)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 102)\n", "  OR (pcm.category_id = 1960\n", "      AND p.type_id = 10302)\n", "  OR (pcm.category_id = 1097\n", "      AND p.type_id = 5526)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 10317)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 3549)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 7208)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 4699)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 9176)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 7342)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 92)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 4565)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 891)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 4263)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 1081)\n", "  OR (pcm.category_id = 1368\n", "      AND p.type_id = 3549)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 5506)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 5948)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 5207)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 3989)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 6792)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 1276)\n", "  OR (pcm.category_id = 67\n", "      AND p.type_id = 5207)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 4699)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 1855)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 10317)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 10317)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 6009)\n", "  OR (pcm.category_id = 67\n", "      AND p.type_id = 92)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 7250)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 5409)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 4826)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 11116)\n", "  OR (pcm.category_id = 1368\n", "      AND p.type_id = 4699)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 3337)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 9332)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 1425)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 11523)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 7208)\n", "  OR (pcm.category_id = 1730\n", "      AND p.type_id = 11555)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 1758)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 3613)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 3831)\n", "  OR (pcm.category_id = 67\n", "      AND p.type_id = 3989)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 4487)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 3831)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 11944)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 3719)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 1276)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 1855)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 9430)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 2259)\n", "  OR (pcm.category_id = 1368\n", "      AND p.type_id = 6792)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 9348)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 1425)\n", "  OR (pcm.category_id = 1730\n", "      AND p.type_id = 8567)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 1352)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 9667)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 7523)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 9933)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 11944)\n", "  OR (pcm.category_id = 1730\n", "      AND p.type_id = 1625)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 10509)\n", "  OR (pcm.category_id = 1391\n", "      AND p.type_id = 2611)\n", "  OR (pcm.category_id = 1369\n", "      AND p.type_id = 10941)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 4263)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 8309)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 3507)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 5409)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 1758)\n", "  OR (pcm.category_id = 67\n", "      AND p.type_id = 1276)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 11527)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 8048)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 11944)\n", "  OR (pcm.category_id = 1389\n", "      AND p.type_id = 3831)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 1741)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 3491)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 2611)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 2737)\n", "  OR (pcm.category_id = 1395\n", "      AND p.type_id = 11524)\n", "  OR (pcm.category_id = 1730\n", "      AND p.type_id = 2973)\n", "  OR (pcm.category_id = 67\n", "      AND p.type_id = 3613)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 330)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 4755)\n", "  OR (pcm.category_id = 63\n", "      AND p.type_id = 3193)\n", "  OR (pcm.category_id = 65\n", "      AND p.type_id = 3549)\n", "  OR (pcm.category_id = 1730\n", "      AND p.type_id = 5047)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 5948)\n", "  OR (pcm.category_id = 1367\n", "      AND p.type_id = 736)\n", "  OR (pcm.category_id = 1778\n", "      AND p.type_id = 6009)\n", "  OR (pcm.category_id = 20\n", "      AND p.type_id = 893)\n", "  OR (pcm.category_id = 173\n", "      AND p.type_id = 4822)\n", "  OR (pcm.category_id = 676\n", "      AND p.type_id = 4822)\n", "  OR (pcm.category_id = 20\n", "      AND p.type_id = 19)\n", "  OR (pcm.category_id = 20\n", "      AND p.type_id = 4822)\n", "  OR (pcm.category_id = 677\n", "      AND p.type_id = 4822)\n", "  OR (pcm.category_id = 20\n", "      AND p.type_id = 2442)\n", "  OR (pcm.category_id = 173\n", "      AND p.type_id = 2173)\n", "      )\n", "SELECT merchant_id as frontend_id, m.name as frontend_name, m.gr_id as frontend_gr_id,\n", " product_id, p.name as product_name,p.enabled_flag as product_enabled_flag,\n", "fmpm.inventory_limit,fmpm.master_inventory_limit, fmpm.enabled_flag, priority_order,\n", "pp.price, pp.mrp\n", "from lake_cms.gr_merchant_product_mapping fmpm \n", "left join lake_cms.view_gr_merchant m on m.id = fmpm.merchant_id \n", "LEFT JOIN lake_cms.gr_product p on p.id = fmpm.product_id\n", "LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = fmpm.merchant_id\n", "                                        AND pp.cms_product_id = fmpm.product_id\n", "WHERE fmpm.merchant_id IN {*f_ids,}\n", "  AND fmpm.product_id IN\n", "    (SELECT product_id\n", "     FROM perishables)\"\"\"\n", "persh_pids = pd.read_sql_query(persh_query, con=redshift)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["persh_pids.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# express_query = \"\"\"express_frontends AS\n", "#         (SELECT DISTINCT vrm.virtual_merchant_id AS \"merchant_id\"\n", "#    FROM lake_cms.gr_virtual_to_real_merchant_mapping vrm\n", "#    JOIN lake_cms.view_gr_merchant rm ON vrm.real_merchant_id = rm.id\n", "#    JOIN lake_cms.view_gr_merchant vm ON vrm.virtual_merchant_id = vm.id\n", "#    WHERE vrm.enabled_flag = TRUE\n", "#      AND vm.enabled_flag = TRUE\n", "#      AND vm.virtual_type='superstore_merchant'\n", "#      AND vm.current_state!='content'\n", "#      AND rm.virtual_type = 'not_applicable'\n", "#      AND json_extract_path_text(rm.meta,'merchant_type')='Express'\n", "#      AND vm.id in {*f_ids,})\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# for i in range(0, len(f_ids), int(math.sqrt(len(f_ids)))):\n", "#     print(f_ids[i : i + int(math.sqrt(len(f_ids)))])\n", "#     fmp_temp = pd.read_sql_query(\n", "#         f\"\"\"select merchant_id, product_id, inventory_limit,master_inventory_limit, enabled_flag, priority_order, pp.price, pp.mrp\n", "#                                      from lake_cms.gr_merchant_product_mapping mp\n", "#                                      LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "#                                         AND pp.cms_product_id = mp.product_id\n", "#                                      where merchant_id in {*f_ids[i:i+int(math.sqrt(len(f_ids)))],}\"\"\",\n", "#         redshift,\n", "#     )\n", "#     f_merchant_product = pd.concat([f_merchant_product, fmp_temp], axis=0)\n", "# print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["import time\n", "\n", "st = time.time()\n", "prodct_cat = pd.read_sql_query(\n", "    \"\"\"select product_id, category_id from\n", "                                  lake_cms.gr_product_category_mapping \n", "                                  where is_primary = 1\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["st = time.time()\n", "merchant_cat = pd.read_sql_query(\n", "    \"\"\"select merchant_id, category_id from\n", "                                  lake_cms.gr_merchant_category_mapping\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["persh_pids.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# d1 = df1.merge(persh_pids, left_on = ['f_ids','pids'], right_on = ['f_id','product_id'], how = 'left')\n", "d1 = persh_pids"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d2 = d1.merge(prodct_cat, on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["merchant_cat.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3 = d2.merge(\n", "    merchant_cat,\n", "    left_on=[\"frontend_id\", \"category_id\"],\n", "    right_on=[\"merchant_id\", \"category_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# outlet_be_merchant_missing\n", "# item_product_mapping_missing\n", "# be_merchant_product_missing (store mapping)\n", "# be_fe_merchant_mappping_missing\n", "# fe_merchant_product_missing (dashboarding)\n", "# fe_enabled_flag_issue\n", "# inventory_limit_be_issue\n", "# inventory_limit_fe_issue\n", "# inventory_limit_be_fe_mismatch_issue\n", "# price_missing\n", "# mrp_missing\n", "# product_category_missing\n", "# product_category_missing_in_merchant"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3[\"product_category_missing_in_merchant\"] = np.where(d3[\"merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3[\"product_category_missing\"] = np.where(d3[\"category_id\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3[\"mrp_missing\"] = np.where(d3[\"mrp\"].isna(), 1, 0)\n", "d3[\"price_missing\"] = np.where(d3[\"price\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# d3['fe_merchant_product_missing (dashboarding)'] = np.where(d3['product_id'].isna(),1,0)\n", "d3[\"fe_enabled_flag_issue\"] = np.where(d3[\"enabled_flag\"] == 0, 1, 0)\n", "d3[\"inventory_limit_fe_issue\"] = np.where(d3[\"inventory_limit\"] == 0, 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["st = time.time()\n", "merchant_mapping = pd.read_sql_query(\n", "    f\"\"\"select vrmm.virtual_merchant_id as f_merchant_id, vrmm.real_merchant_id as b_merchant_id, chain_id, chain_name\n", "        from lake_cms.gr_virtual_to_real_merchant_mapping vrmm\n", "        join dwh.dim_merchant dm on dm.merchant_id = vrmm.virtual_merchant_id and is_current = 1\n", "        where vrmm.enabled_flag = true and vrmm.virtual_merchant_id in {*f_ids,}\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["merchant_mapping.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d4 = d3.merge(\n", "    merchant_mapping, left_on=[\"frontend_id\"], right_on=[\"f_merchant_id\"], how=\"left\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d4[\"be_fe_merchant_mappping_missing\"] = np.where(d4[\"b_merchant_id\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d4.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["b_merchants = d4.b_merchant_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["st = time.time()\n", "b_merchant_product = pd.DataFrame()\n", "for i in range(0, len(b_merchants), int(math.sqrt(len(b_merchants)))):\n", "    print(b_merchants[i : i + int(math.sqrt(len(b_merchants)))])\n", "    b_temp = pd.read_sql_query(\n", "        f\"\"\"select merchant_id, product_id, inventory_limit, master_inventory_limit, \n", "                                        enabled_flag, priority_order, pp.price, pp.mrp\n", "                                        from lake_cms.gr_merchant_product_mapping mp\n", "                                        LEFT JOIN lake_pricing_v3.pricing_domain_prices pp ON pp.frontend_id = mp.merchant_id\n", "                                        AND pp.cms_product_id = mp.product_id\n", "                                        where merchant_id in {*b_merchants[i:i+int(math.sqrt(len(b_merchants)))],}\"\"\",\n", "        redshift,\n", "    )\n", "    b_merchant_product = pd.concat([b_merchant_product, b_temp], axis=0)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["b_merchant_product.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d5 = d4.merge(\n", "    b_merchant_product,\n", "    left_on=[\"b_merchant_id\", \"product_id\"],\n", "    right_on=[\"merchant_id\", \"product_id\"],\n", "    how=\"left\",\n", "    suffixes=(\"_fe\", \"_be\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d5[\"be_merchant_product_missing (store mapping)\"] = np.where(\n", "    d5[\"merchant_id_be\"].isna(), 1, 0\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d5[\"inventory_limit_be_issue\"] = np.where(d5[\"inventory_limit_be\"] == 0, 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d5[\"inventory_limit_be_fe_mismatch_issue\"] = np.where(\n", "    d5[\"inventory_limit_be\"] != d5[\"inventory_limit_fe\"], 1, 0\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["st = time.time()\n", "prod_map = pd.read_sql_query(\n", "    \"\"\"select item_id, product_id, offer_id\n", "                                from lake_view_rpc.item_product_mapping\n", "                                where active = 1\"\"\",\n", "    presto,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d6 = d5.merge(prod_map, left_on=[\"product_id\"], right_on=[\"product_id\"], how=\"left\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d6[\"item_product_mapping_missing\"] = np.where(d6[\"item_id\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["st = time.time()\n", "backend_outlet = pd.read_sql_query(\n", "    \"\"\"select  c.outlet_id, c.cms_store, m.name as be_name\n", "                                from lake_retail.console_outlet_cms_store c\n", "                                join lake_cms.view_gr_merchant m on m.id = c.cms_store \n", "                                join dwh.dim_merchant dm on dm.merchant_id = m.id and is_current = 1 and dm.chain_id = 177\n", "                                where c.active = 1 and c.cms_update_active = 1 and c.outlet_id in (select id \n", "                                from lake_retail.console_outlet where business_type_id = 7)\"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d7 = d6.merge(\n", "    backend_outlet, left_on=[\"b_merchant_id\"], right_on=[\"cms_store\"], how=\"left\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d7[\"outlet_be_merchant_missing\"] = np.where(d7[\"outlet_id\"].isna(), 1, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d7.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d8 = d7[\n", "    [\n", "        \"frontend_id\",\n", "        \"frontend_name\",\n", "        \"frontend_gr_id\",\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"product_enabled_flag\",\n", "        \"product_category_missing_in_merchant\",\n", "        \"product_category_missing\",\n", "        \"mrp_missing\",\n", "        \"price_missing\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"item_product_mapping_missing\",\n", "        \"outlet_be_merchant_missing\",\n", "    ]\n", "].rename(columns={\"f_ids\": \"frontend_merchant_id\", \"pids\": \"product_id\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d8[\"total_flags\"] = d8[\n", "    [\n", "        \"product_category_missing_in_merchant\",\n", "        \"product_category_missing\",\n", "        \"mrp_missing\",\n", "        \"price_missing\",\n", "        \"fe_enabled_flag_issue\",\n", "        \"inventory_limit_fe_issue\",\n", "        \"be_fe_merchant_mappping_missing\",\n", "        \"be_merchant_product_missing (store mapping)\",\n", "        \"inventory_limit_be_issue\",\n", "        \"inventory_limit_be_fe_mismatch_issue\",\n", "        \"item_product_mapping_missing\",\n", "        \"outlet_be_merchant_missing\",\n", "    ]\n", "].sum(axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d8.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d9 = d8[d8[\"total_flags\"] > 0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d9.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["d9.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["try:\n", "    pb.to_sheets(\n", "        d9, \"1SVyua2igFJXXMtekeAe2YQnhRWg5sqR-xpGShUvjiKA\", \"Data (with flags)\"\n", "    )\n", "except:\n", "    print(\"Failed!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}