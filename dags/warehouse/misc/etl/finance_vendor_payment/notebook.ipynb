{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account=\"service_account\", max_tries=5):\n", "    _retry_for = [429, 500, 503]\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60 * attempt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# pos = pb.get_connection(\"[Replica] POS\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "# RUN_DATE = NOW_IST.date()\n", "\n", "end_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "start_date = (datetime.now() - timed<PERSON>ta(days=1)).date().replace(day=1)\n", "end_date, start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlets_query = (\n", "#     \"\"\"select id outlet_id,name as outlet_name  from  lake_retail.console_outlet\"\"\"\n", "# )\n", "# outlet_ids = pd.read_sql_query(sql=outlets_query, con=redshift)\n", "# outlet_ids.head()\n", "# outlet_list = outlet_ids[\"outlet_id\"]\n", "# type(outlet_list)\n", "# outletid_list = tuple(list(outlet_ids[\"outlet_id\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outletname_list = tuple(list(outlet_ids[\"outlet_name\"]))\n", "# print(outletname_list[0:10])\n", "# type(outletname_list)\n", "# # outletid_list=(100,410)\n", "# print(outletid_list[0:10])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Weekly grn part"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekly_final = pd.DataFrame()\n", "query = \"\"\"select \n", "distinct  GRN_ID,\n", "Purchase_Number,PO_Type,Vendor_GST,\n", "Vendor_Supply_Location,Issue_Date,Item_Type,\n", "Outlet_ID,Outlet_Name,City_Name,Stock_In_Date,Created_At,Update_Type,\n", "merchant_id,Merchant_Name,Merchant_Invoice_ID,Purchase_Order_ID,Stock_In_type ,invoice_rank,invoice_link,invoice_upload,File_Uploaded_at,max(Manufacturer_Name) Manufacturer_Name,\n", "sum(GRN_amount) amount \n", "from(\n", "select \n", "inventory_update_id,inventory_update_type_id,\n", "Stock_Update_ID,Purchase_Number,PO_Type,Vendor_GST,\n", "Vendor_Supply_Location,Issue_Date,Item_Type, Manufacturer_Name,\n", "Outlet_ID,Outlet_Name,City_Name,Stock_In_Date,date(Created_At) Created_At,Update_Type,\n", "merchant_id,Merchant_Name,Merchant_Invoice_ID,Purchase_Order_ID,\n", "GRN_ID,GRN_amount,Stock_In_type,invoice_rank,invoice_link,invoice_upload,File_Uploaded_at\n", "from \n", "(\n", "SELECT \n", "i3.id as \"Stock_Update_ID\",\n", "i.inventory_update_id,\n", "pur_ord.po_number as Purchase_Number,\n", "(CASE \n", "WHEN iii.source_type = 2 THEN 'ESTO Stock-in'\n", "WHEN iii.source_type = 3 THEN 'Customer returened without invoice'\n", "WHEN (iii.source_type = 1 and iii.valid_po=1) THEN 'Valid PO'\n", "ELSE 'In-Valid PO'\n", "END) AS \"PO_Type\",\n", "pur_ord.vendor_gst as \"Vendor_GST\",\n", "lo2.name as \"Vendor_Supply_Location\",\n", "Date(pur_ord.issue_date+interval'5.5 hrs') as \"Issue_Date\",\n", "Date(i3.manufacture_date+interval'5.5 hrs') as \"Manufacture_Date\",\n", "r.item_id AS Item_ID,\n", "i.upc_id AS UPC_ID,\n", "i.variant_id AS Variant_ID,\n", "r.name AS Product,\n", "(CASE\n", "WHEN r.outlet_type = 1 THEN 'F&V type'\n", "WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "ELSE 'Not Assigned'\n", "END) AS \"Item_Type\",\n", "man.name AS Manufacturer_Name,\n", "r.is_ptr AS PTR_Flag,\n", "i.\"delta\" AS Quantity,\n", "i3.return_quantity ,\n", "(i.\"delta\"-i3.return_quantity),\n", "r.variant_mrp AS MRP,\n", "r.variant_uom_text AS Grammage,\n", "ro.id as outlet_id,\n", "ro.name AS Outlet_Name,\n", "lo.name AS City_Name,\n", "Date(i.pos_timestamp+interval'5.5 hrs') AS Stock_In_Date,\n", "(i.created_at+interval'5.5 hrs') AS Created_At,\n", "i2.id inventory_update_type_id,\n", "i2.name AS Update_Type,\n", "i3.expiry_date AS Expiry_Date,\n", "rcm.id as merchant_id,\n", "rcm.name AS Merchant_Name,\n", "i.merchant_invoice_id AS Merchant_Invoice_ID,\n", "i.created_by_name AS Created_By,\n", "i3.cost_price AS Cost_Price,\n", "i3.landing_price AS Landing_Price,\n", "i3.purchase_order_id as Purchase_Order_ID,\n", "i3.grn_id as GRN_ID,\n", "i3.landing_price * i.\"delta\" GRN_amount,\n", "(i3.cost_price*(i.\"delta\"-i3.return_quantity)) as Net_Cost_Price,\n", "(i3.landing_price*(i.\"delta\"-i3.return_quantity)) as Net_Landing_Price,\n", "i3.cost_price * i.\"delta\" as Total_Cost_Price,\n", "((i3.landing_price * i.\"delta\")-(i3.cost_price * i.\"delta\")) as Tax,\n", "(CASE\n", "WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "ELSE 'Normal Stock-In'\n", "END) AS \"Stock_In_type\",\n", "r.is_pl,\n", "inv_file.uuid,\n", "(rank() over (partition by inv_file.outlet_id,inv_file.merchant_id,inv_file.invoice_id order by inv_file.file_uploaded_at)) as invoice_rank,\n", "(case when inv_file.file_uploaded_at is null then 'Not Uploaded' else 'Uploaded' end) as invoice_upload,\n", "(inv_file.file_uploaded_at+interval'5.5 hrs') AS File_Uploaded_at,\n", "concat('https://retail.grofers.com/pos/v1/vendor-invoice-file/',concat((inv_file.uuid)::text,'/download/')) as invoice_link\n", "FROM lake_ims.ims_inventory_log i\n", "INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "LEFT JOIN lake_rpc.product_brand br ON br.id=r.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "INNER JOIN  lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "INNER JOIN lake_retail.console_location lo ON ro.tax_location_id=lo.id\n", "INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id  \n", "INNER JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "INNER JOIN lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id \n", "LEFT JOIN lake_pos.pos_invoice pi on pi.invoice_id=i.merchant_invoice_id\n", "LEFT JOIN lake_retail.console_location lo2 on lo2.id=pur_ord.vendor_shipping_city_id\n", "left join lake_pos.pos_vendor_invoice_file  inv_file  on inv_file.outlet_id=i.outlet_id and  inv_file.merchant_id=i.merchant_id and inv_file.invoice_id=i.merchant_invoice_id\n", "and date(inv_file.created_at+interval'5.5 hrs') >= current_date - 40\n", "WHERE\n", "i.inventory_update_type_id IN (1,28,76,90)\n", "and date(i.created_at+interval'5.5 hrs') between current_date - 40 and current_date - 1\n", "and i.\"delta\" > 0\n", "and  rcm.name not in (select distinct name as outlet_name  from  lake_retail.console_outlet) \n", "\n", "and  rcm.name not in ('Customer Return without Invoice')\n", "and  rcm.name not in ('Customer Returns Without Invoice')\n", "and  rcm.name not in ('Dummy stock in merchant','Dummy Merchant')\n", "and  rcm.name not like '%%HOT Warehouse%%'\n", "and  rcm.name not like '%%PN2 (HOT)%%' \n", "and ro.name not like '%%Infra Outlet%%'\n", "and ro.name not like '%%Liquidation%%'\n", "--  and  iii.source_type = 1\n", "--  and  iii.valid_po=1   \n", ")temp\n", ")temp_group\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22\n", "\n", "\"\"\"\n", "weekly_final = pd.read_sql_query(\n", "    sql=query,\n", "    con=redshift,\n", ")\n", "# weekly_final = weekly_final.append(weekly_temp)\n", "print(\"weekly grn captured\")\n", "weekly_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekly_final[weekly_final[\"invoice_rank\"] > 1].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"city_name\",\n", "    \"merchant_name\",\n", "    \"vendor_supply_location\",\n", "    \"vendor_gst\",\n", "    \"stock_in_date\",\n", "    \"grn_id\",\n", "    \"invoice_link\",\n", "    \"invoice_upload\",\n", "    \"file_uploaded_at\",\n", "    \"purchase_number\",\n", "    \"merchant_invoice_id\",\n", "    \"amount\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"merchant_id\",\n", "    \"po_type\",\n", "    \"manufacturer_name\",\n", "    \"item_type\",\n", "]\n", "weekly_final1 = weekly_final.reindex(columns=column_names)\n", "\n", "print(weekly_final1.shape)\n", "weekly_final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grocery = weekly_final1[weekly_final1[\"item_type\"] == \"Grocery type\"]\n", "fnv = weekly_final1[weekly_final1[\"item_type\"] == \"F&V type\"]\n", "\n", "# fnv_weekly = fnv[\n", "#     (fnv[\"stock_in_date\"] >= start_date) & (fnv[\"stock_in_date\"] <= end_date)\n", "# ]\n", "# grocery_weekly = grocery[\n", "#     (grocery[\"stock_in_date\"] >= start_date) & (grocery[\"stock_in_date\"] <= end_date)\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grocery.sort_values(by=[\"stock_in_date\"], inplace=True)\n", "fnv.sort_values(by=[\"stock_in_date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grocery.shape, fnv.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id='1ZxaTO3jKGFjoABArZ0NDvD7zzUANGjUrxeZjmjTymh4'\n", "sheet_id = \"1v4bcv2pyhSFwT3CIQKHEORQGQGr9K0-eo61yViCg0Yw\"\n", "time.sleep(10)\n", "sheet_name = \"warehouse(grocery)\"\n", "to_sheets(grocery, sheet_id, sheet_name, service_account=\"service_account\")\n", "print(\"sent to grocery sheet\")\n", "\n", "time.sleep(10)\n", "sheet_name = \"cpc(fnv)\"\n", "to_sheets(fnv, sheet_id, sheet_name, service_account=\"service_account\")\n", "print(\"sent to fnv sheet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prn = pd.read_sql_query(\n", "    \"\"\"\n", "select p.outlet_id,\n", "       p.outlet_name,\n", "       p.merchant_id,\n", "       c1.name AS merchant_name,\n", "       p.invoice_id,\n", "       p.grofers_order_id AS merchant_invoice_id,\n", "       date(convert_timezone('Asia/Kolkata', p.pos_timestamp)) as prn_date,\n", "       (CASE\n", "            WHEN p2.outlet_type = 1 THEN 'F&V type'\n", "            WHEN p2.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"item_type\",\n", "       (case when pif.file_uploaded_at is null then 'Not Uploaded' else 'Uploaded' end) as invoice_upload,\n", "       convert_timezone('Asia/Kolkata', pif.file_uploaded_at) AS file_uploaded_at,\n", "       sum(p1.quantity) as quantity,\n", "       sum(p1.selling_price * p1.quantity) as amount\n", "from lake_retail.console_merchant c1\n", "join lake_pos.pos_invoice p ON p.merchant_id=c1.id\n", "join lake_pos.pos_invoice_product_details p1 ON p.id = p1.invoice_id\n", "join lake_rpc.product_product p2 ON p2.variant_id = p1.variant_id\n", "left join lake_pos.pos_invoice_file pif on pif.invoice_id = p.invoice_id\n", "join lake_retail.console_outlet r on r.id = p.outlet_id and r.active = 1 and r.device_id != 47\n", "where p.invoice_type_id = 6\n", "and p.pos_timestamp >= (current_date - 40)::timestamp - interval '5.5 hrs'\n", "and p.pos_timestamp < current_date::timestamp - interval '5.5 hrs'\n", "and r.name not like '%%Infra%%'\n", "and c1.name not like '%%Dummy%%'\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "order by 7\n", "\"\"\",\n", "    redshift,\n", ")\n", "prn.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# prn = prn[(prn['prn_date'] >= start_date) & (prn['prn_date'] <= end_date)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1v4bcv2pyhSFwT3CIQKHEORQGQGr9K0-eo61yViCg0Yw\"\n", "sheet_name = \"PRN(grocery)\"\n", "to_sheets(\n", "    prn[prn[\"item_type\"] == \"Grocery type\"],\n", "    sheet_id,\n", "    sheet_name,\n", "    service_account=\"service_account\",\n", ")\n", "\n", "sheet_id = \"1v4bcv2pyhSFwT3CIQKHEORQGQGr9K0-eo61yViCg0Yw\"\n", "sheet_name = \"PRN(fnv)\"\n", "to_sheets(\n", "    prn[prn[\"item_type\"] == \"F&V type\"],\n", "    sheet_id,\n", "    sheet_name,\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FC Name | No of Invoices GRN Done YD | No of Inovoices Uploaded YD | % Adherence |No of Invoices GRN Done MTD |\n", "# No of Invoices Uploaded MTD|% Adherence | Total GN Done Invoices Pending Till Date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"select \n", "distinct  City_Name, \n", "          Outlet_ID,\n", "          Outlet_Name, \n", "          count(distinct case when Stock_In_Date = current_date-1 then Merchant_Invoice_ID else null end) as Invoices_Created_yesterday,\n", "          count(distinct case when (Stock_In_Date = current_date-1) and (File_Uploaded_at is not null) then Merchant_Invoice_ID else null end) as Invoices_Uploaded_yesterday,\n", "          round((case when Invoices_Created_yesterday = 0 then null else Invoices_Uploaded_yesterday*100.0/Invoices_Created_yesterday end),2) as Upload_Adherence_yd,\n", "          count(distinct case when Stock_In_Date >= date_trunc('month', current_date) then Merchant_Invoice_ID else null end) as Invoices_Created_MTD,\n", "          count(distinct case when (Stock_In_Date>= date_trunc('month', current_date)) and (File_Uploaded_at is not null) then Merchant_Invoice_ID else null end) as Invoices_Uploaded_mtd,\n", "          round((case when Invoices_Created_MTD = 0 then null else Invoices_Uploaded_mtd*100.0/Invoices_Created_MTD  end),2) as Upload_Adherence_mtd\n", "from(\n", "select \n", "inventory_update_id,inventory_update_type_id,\n", "Stock_Update_ID,Purchase_Number,PO_Type,Vendor_GST,\n", "Vendor_Supply_Location,Issue_Date,Item_Type, Manufacturer_Name,\n", "Outlet_ID,Outlet_Name,City_Name,Stock_In_Date,date(Created_At) Created_At,Update_Type,\n", "merchant_id,Merchant_Name,Merchant_Invoice_ID,Purchase_Order_ID,\n", "GRN_ID,GRN_amount,Stock_In_type,invoice_rank,invoice_link,invoice_upload,File_Uploaded_at\n", "from \n", "(\n", "SELECT \n", "i3.id as \"Stock_Update_ID\",\n", "i.inventory_update_id,\n", "pur_ord.po_number as Purchase_Number,\n", "(CASE \n", "WHEN iii.source_type = 2 THEN 'ESTO Stock-in'\n", "WHEN iii.source_type = 3 THEN 'Customer returened without invoice'\n", "WHEN (iii.source_type = 1 and iii.valid_po=1) THEN 'Valid PO'\n", "ELSE 'In-Valid PO'\n", "END) AS \"PO_Type\",\n", "pur_ord.vendor_gst as \"Vendor_GST\",\n", "lo2.name as \"Vendor_Supply_Location\",\n", "Date(pur_ord.issue_date+interval'5.5 hrs') as \"Issue_Date\",\n", "Date(i3.manufacture_date+interval'5.5 hrs') as \"Manufacture_Date\",\n", "r.item_id AS Item_ID,\n", "i.upc_id AS UPC_ID,\n", "i.variant_id AS Variant_ID,\n", "r.name AS Product,\n", "(CASE\n", "WHEN r.outlet_type = 1 THEN 'F&V type'\n", "WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "ELSE 'Not Assigned'\n", "END) AS \"Item_Type\",\n", "man.name AS Manufacturer_Name,\n", "r.is_ptr AS PTR_Flag,\n", "i.\"delta\" AS Quantity,\n", "i3.return_quantity ,\n", "(i.\"delta\"-i3.return_quantity),\n", "r.variant_mrp AS MRP,\n", "r.variant_uom_text AS Grammage,\n", "ro.id as outlet_id,\n", "ro.name AS Outlet_Name,\n", "lo.name AS City_Name,\n", "Date(i.pos_timestamp+interval'5.5 hrs') AS Stock_In_Date,\n", "(i.created_at+interval'5.5 hrs') AS Created_At,\n", "i2.id inventory_update_type_id,\n", "i2.name AS Update_Type,\n", "i3.expiry_date AS Expiry_Date,\n", "rcm.id as merchant_id,\n", "rcm.name AS Merchant_Name,\n", "i.merchant_invoice_id AS Merchant_Invoice_ID,\n", "i.created_by_name AS Created_By,\n", "i3.cost_price AS Cost_Price,\n", "i3.landing_price AS Landing_Price,\n", "i3.purchase_order_id as Purchase_Order_ID,\n", "i3.grn_id as GRN_ID,\n", "i3.landing_price * i.\"delta\" GRN_amount,\n", "(i3.cost_price*(i.\"delta\"-i3.return_quantity)) as Net_Cost_Price,\n", "(i3.landing_price*(i.\"delta\"-i3.return_quantity)) as Net_Landing_Price,\n", "i3.cost_price * i.\"delta\" as Total_Cost_Price,\n", "((i3.landing_price * i.\"delta\")-(i3.cost_price * i.\"delta\")) as Tax,\n", "(CASE\n", "WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "ELSE 'Normal Stock-In'\n", "END) AS \"Stock_In_type\",\n", "r.is_pl,\n", "inv_file.uuid,\n", "(rank() over (partition by inv_file.outlet_id,inv_file.merchant_id,inv_file.invoice_id order by inv_file.file_uploaded_at)) as invoice_rank,\n", "(case when inv_file.file_uploaded_at is null then 'Not Uploaded' else 'Uploaded' end) as invoice_upload,\n", "(inv_file.file_uploaded_at+interval'5.5 hrs') AS File_Uploaded_at,\n", "concat('https://retail.grofers.com/pos/v1/vendor-invoice-file/',concat((inv_file.uuid)::text,'/download/')) as invoice_link\n", "FROM lake_ims.ims_inventory_log i\n", "INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "LEFT JOIN lake_rpc.product_brand br ON br.id=r.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "INNER JOIN  lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "INNER JOIN lake_retail.console_location lo ON ro.tax_location_id=lo.id\n", "INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id  \n", "INNER JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "INNER JOIN lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id \n", "LEFT JOIN lake_pos.pos_invoice pi on pi.invoice_id=i.merchant_invoice_id\n", "LEFT JOIN lake_retail.console_location lo2 on lo2.id=pur_ord.vendor_shipping_city_id\n", "left join lake_pos.pos_vendor_invoice_file  inv_file  on inv_file.outlet_id=i.outlet_id and  inv_file.merchant_id=i.merchant_id and inv_file.invoice_id=i.merchant_invoice_id\n", "and date(inv_file.created_at+interval'5.5 hrs') >= current_date - 40\n", "WHERE\n", "i.inventory_update_type_id IN (1,28,76,90)\n", "and date(i.created_at+interval'5.5 hrs') between current_date - 40 and current_date - 1\n", "and i.\"delta\" > 0\n", "and  rcm.name not in (select distinct name as outlet_name  from  lake_retail.console_outlet) \n", "\n", "and  rcm.name not in ('Customer Return without Invoice')\n", "and  rcm.name not in ('Customer Returns Without Invoice')\n", "and  rcm.name not in ('Dummy stock in merchant','Dummy Merchant')\n", "and  rcm.name not like '%%HOT Warehouse%%'\n", "and  rcm.name not like '%%PN2 (HOT)%%' \n", "and ro.name not like '%%Infra Outlet%%'\n", "and ro.name not like '%%Liquidation%%'\n", "--  and  iii.source_type = 1\n", "--  and  iii.valid_po=1   \n", ")temp\n", ")temp_group\n", "group by 1,2,3\n", "order by 7 desc\"\"\"\n", "summ = pd.read_sql_query(query1, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summ.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_sheets(summ, sheet_id, \"Summary(GRN)\", service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prn_adh = pd.read_sql_query(\n", "    f\"\"\"\n", "select outlet_id,\n", "       outlet_name,\n", "       count(distinct case when prn_date = {end_date} then invoice_id else null end) as invoices_created_yesterday,\n", "       count(distinct case when prn_date = {end_date} and file_uploaded_at is not null then invoice_id else null end) as invoices_uploaded_yesterday,\n", "       round((case when invoices_created_yesterday = 0 then null else invoices_uploaded_yesterday*100.0/invoices_created_yesterday end), 2) as upload_adh_yd,\n", "       count(distinct case when prn_date >= date_trunc('month', current_date) then invoice_id else null end) as Invoices_Created_MTD,\n", "       count(distinct case when (prn_date >= date_trunc('month', current_date)) and (file_uploaded_at is not null) then invoice_id else null end) as invoices_uploaded_mtd,\n", "       round((case when Invoices_Created_MTD = 0 then null else invoices_uploaded_mtd*100.0/Invoices_Created_MTD  end),2) as upload_adh_mtd\n", "from (select p.outlet_id,\n", "       p.outlet_name,\n", "       p.merchant_id,\n", "       c1.name AS merchant_name,\n", "       p.invoice_id,\n", "       date(convert_timezone('Asia/Kolkata', p.pos_timestamp)) as prn_date,\n", "       p.grofers_order_id AS merchant_invoice_id,\n", "       (case when pif.file_uploaded_at is null then 'Not Uploaded' else 'Uploaded' end) as invoice_upload,\n", "       convert_timezone('Asia/Kolkata', pif.file_uploaded_at) AS file_uploaded_at,\n", "       sum(p1.quantity) as quantity,\n", "       sum(p1.selling_price * p1.quantity) as amount\n", "from lake_retail.console_merchant c1\n", "join lake_pos.pos_invoice p ON p.merchant_id=c1.id\n", "join lake_pos.pos_invoice_product_details p1 ON p.id = p1.invoice_id\n", "join lake_rpc.product_product p2 ON p2.variant_id = p1.variant_id\n", "left join lake_pos.pos_invoice_file pif on pif.invoice_id = p.invoice_id\n", "join lake_retail.console_outlet r on r.id = p.outlet_id and r.active = 1 and r.device_id != 47\n", "where p.invoice_type_id = 6\n", "and p.pos_timestamp >= current_date - 40\n", "and p.pos_timestamp < current_date::timestamp - interval '5.5 hrs'\n", "and r.name not like '%%Infra%%'\n", "and c1.name not like '%%Dummy%%'\n", "group by 1,2,3,4,5,6,7,8,9)\n", "group by 1,2\n", "order by 7 desc\n", "\"\"\",\n", "    redshift,\n", ")\n", "prn_adh.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"Summary(PRN)\"\n", "to_sheets(prn_adh, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON> Tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final = pd.<PERSON><PERSON><PERSON><PERSON>()\n", "# for i in outletid_list:\n", "#     #     print(i)\n", "#     query = \"\"\"select\n", "# distinct  GRN_ID,\n", "\n", "# Purchase_Number,PO_Type,Vendor_GST,\n", "# Vendor_Supply_Location,Issue_Date,Item_Type,\n", "# Outlet_ID,Outlet_Name,City_Name,Stock_In_Date,Created_At,Update_Type,\n", "# merchant_id,Merchant_Name,Merchant_Invoice_ID,Purchase_Order_ID,Stock_In_type ,max(Manufacturer_Name) Manufacturer_Name,\n", "# sum(GRN_amount) amount\n", "\n", "# from(\n", "# select\n", "# inventory_update_id,inventory_update_type_id,\n", "# Stock_Update_ID,Purchase_Number,PO_Type,Vendor_GST,\n", "# Vendor_Supply_Location,Issue_Date,Item_Type, Manufacturer_Name,\n", "# Outlet_ID,Outlet_Name,City_Name,Stock_In_Date,date(Created_At) Created_At,Update_Type,\n", "# merchant_id,Merchant_Name,Merchant_Invoice_ID,Purchase_Order_ID,\n", "# GRN_ID,GRN_amount,Stock_In_type\n", "# from\n", "#  (\n", "#  SELECT\n", "#    i3.id as \"Stock_Update_ID\",\n", "#    i.inventory_update_id,\n", "#    pur_ord.po_number as \"Purchase_Number\",\n", "#    (CASE\n", "#       WHEN iii.source_type = 2 THEN \"ESTO Stock-in\"\n", "#       WHEN iii.source_type = 3 THEN \"Customer returened without invoice\"\n", "#       WHEN iii.source_type = 1 and iii.valid_po=1 THEN \"Valid PO\"\n", "#       ELSE \"In-Valid PO\"\n", "#    END) AS \"PO_Type\",\n", "#    pur_ord.vendor_gst as \"Vendor_GST\",\n", "#    lo2.name as \"Vendor_Supply_Location\",\n", "#    Date(convert_tz(pur_ord.issue_date,'+00:00','+05:30')) as \"Issue_Date\",\n", "#    Date(convert_tz(i3.manufacture_date,'+00:00','+05:30')) as \"Manufacture_Date\",\n", "#    r.item_id AS 'Item_ID',\n", "#    i.upc_id AS 'UPC_ID',\n", "#    i.variant_id AS 'Variant_ID',\n", "#    r.name AS Product,\n", "# \t(CASE\n", "\n", "#     \tWHEN r.outlet_type = 1 THEN \"F&V type\"\n", "# \t\tWHEN r.outlet_type = 2 THEN \"Grocery type\"\n", "#          ELSE 'Not Assigned'\n", "\n", "#      END) AS \"Item_Type\",\n", "#    man.name AS 'Manufacturer_Name',\n", "#    r.is_ptr AS 'PTR_Flag',\n", "#    i.delta AS Quantity,\n", "#    i3.return_quantity ,\n", "#    (i.delta-i3.return_quantity),\n", "#    r.variant_mrp AS MRP,\n", "#    r.variant_uom_text AS Grammage,\n", "#    ro.id as outlet_id,\n", "#    ro.name AS 'Outlet_Name',\n", "#    lo.name AS 'City_Name',\n", "#    Date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) AS 'Stock_In_Date',\n", "#    convert_tz(i.created_at,'+00:00','+05:30') AS 'Created_At',\n", "#    i2.id inventory_update_type_id,\n", "#    i2.name AS 'Update_Type',\n", "#    i3.expiry_date AS 'Expiry_Date',\n", "#    rcm.id as merchant_id,\n", "#    rcm.name AS 'Merchant_Name',\n", "#    i.merchant_invoice_id AS 'Merchant_Invoice_ID',\n", "#    i.created_by_name AS 'Created_By',\n", "#    i3.cost_price AS 'Cost_Price',\n", "#    i3.landing_price AS 'Landing_Price',\n", "#    i3.purchase_order_id as 'Purchase_Order_ID',\n", "#    i3.grn_id as 'GRN_ID',\n", "#    i3.landing_price * i.delta GRN_amount,\n", "#    (i3.cost_price*(i.delta-i3.return_quantity)) as 'Net_Cost_Price',\n", "#    (i3.landing_price*(i.delta-i3.return_quantity)) as 'Net_Landing_Price',\n", "#    i3.cost_price * i.delta as 'Total_Cost_Price',\n", "#    ((i3.landing_price * i.delta)-(i3.cost_price * i.delta)) as 'Tax',\n", "#    (CASE\n", "\n", "#          WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "#          ELSE 'Normal Stock-In'\n", "\n", "#      END) AS \"Stock_In_type\",\n", "#     r.is_pl\n", "\n", "# FROM\n", "#    ims.ims_inventory_log i\n", "#        INNER JOIN\n", "#    rpc.product_product r ON r.variant_id = i.variant_id\n", "#        LEFT JOIN\n", "#    rpc.product_brand br ON br.id=r.brand_id\n", "#    \t\tLEFT JOIN\n", "#    rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "#    \t\tINNER JOIN\n", "#    retail.console_outlet ro ON ro.id = i.outlet_id\n", "#        INNER JOIN\n", "#    retail.console_location lo ON ro.tax_location_id=lo.id\n", "#    \t\tINNER JOIN\n", "#    ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "#        INNER JOIN\n", "#    ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "#        INNER JOIN\n", "#    ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "#        INNER JOIN\n", "#    retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "#        LEFT JOIN\n", "#    po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "#    \t\tLEFT JOIN\n", "#   \tpos.pos_invoice pi on pi.invoice_id=i.merchant_invoice_id\n", "#    \t\tLEFT JOIN\n", "#    \tretail.console_location lo2 on lo2.id=pur_ord.vendor_shipping_city_id\n", "# WHERE\n", "#    i.inventory_update_type_id IN (1,28,76,90)\n", "#   and i.outlet_id  in (%(outlet_id)s)\n", "#   and date(convert_tz(i.created_at,'+00:00','+05:30')) between '2020-01-01' and '2020-07-30'\n", "#    and i.delta > 0\n", "#    and  rcm.name not in (select distinct name as outlet_name  from  retail.console_outlet)\n", "#     and  rcm.name not in ('Customer Return without Invoice')\n", "#     and  rcm.name not in ('Customer Returns Without Invoice')\n", "#         and  rcm.name not in ('Dummy stock in merchant','Dummy Merchant')\n", "#         and  rcm.name not like '%%HOT Warehouse%%'\n", "#          and  rcm.name not like '%%PN2 (HOT)%%'\n", "#         and ro.name not like '%%Infra Outlet%%'\n", "#     --  and  iii.source_type = 1\n", "#     --  and  iii.valid_po=1\n", "#    )temp\n", "\n", "#   )temp_group\n", "#   group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18\n", "\n", "#    \"\"\"\n", "#     temp = pd.read_sql_query(\n", "#         sql=query,\n", "#         con=retail,\n", "#         params={\"outlet_id\": i},\n", "#     )\n", "#     final = final.append(temp)\n", "\n", "# print(\"stock in  captured\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data = final.copy(deep=True)\n", "# final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # final.to_csv('aa.csv',index=False)\n", "# grn_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data.columns\n", "# column_names = [\n", "#     \"\" \"outlet_id\",\n", "#     \"Outlet_Name\",\n", "#     \"City_Name\",\n", "#     \"merchant_id\",\n", "#     \"Merchant_Name\",\n", "#     \"Vendor_Supply_Location\",\n", "#     \"Stock_In_Date\",\n", "#     \"Created_At\",\n", "#     \"Merchant_Invoice_ID\",\n", "#     \"Purchase_Order_ID\",\n", "#     \"GRN_ID\",\n", "#     \"Purchase_Number\",\n", "#     \"PO_Type\",\n", "#     \"Issue_Date\",\n", "#     \"amount\",\n", "#     \"Manufacturer_Name\",\n", "#     \"Vendor_GST\",\n", "#     \"Item_Type\",\n", "# ]\n", "# grn_data = grn_data.reindex(columns=column_names)\n", "# print(grn_data.shape)\n", "# grn_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_names = [\n", "#     \"City_Name\",\n", "#     \"Merchant_Name\",\n", "#     \"Vendor_Supply_Location\",\n", "#     \"Vendor_GST\",\n", "#     \"Stock_In_Date\",\n", "#     \"GRN_ID\",\n", "#     \"Purchase_Number\",\n", "#     \"Merchant_Invoice_ID\",\n", "#     \"amount\",\n", "#     \"outlet_id\",\n", "#     \"Outlet_Name\",\n", "#     \"merchant_id\",\n", "#     \"PO_Type\",\n", "#     \"Manufacturer_Name\",\n", "#     \"Item_Type\",\n", "#     #     'invoice_link'\n", "# ]\n", "# grn_data = grn_data.reindex(columns=column_names)\n", "# grn_data[\"key\"] = (\n", "#     grn_data[\"City_Name\"] + grn_data[\"Merchant_Name\"] + grn_data[\"Manufacturer_Name\"]\n", "# )\n", "# print(grn_data.shape)\n", "# grn_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id = \"1ZxaTO3jKGFjoABArZ0NDvD7zzUANGjUrxeZjmjTymh4\"\n", "# sheet_name = \"Sheet1\"\n", "# # pb.to_sheets(grn_data, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grocery = grn_data[grn_data[\"Item_Type\"] == \"Grocery type\"]\n", "# fnv = grn_data[grn_data[\"Item_Type\"] == \"F&V type\"]\n", "\n", "# fnv_weekly = fnv[\n", "#     (fnv[\"Stock_In_Date\"] >= start_date) & (fnv[\"Stock_In_Date\"] <= end_date)\n", "# ]\n", "# grocery_weekly = grocery[\n", "#     (grocery[\"Stock_In_Date\"] >= start_date) & (grocery[\"Stock_In_Date\"] <= end_date)\n", "# ]\n", "\n", "# fnv_weekly.sort_values(by=[\"Stock_In_Date\"], inplace=True)\n", "# grocery_weekly.sort_values(by=[\"Stock_In_Date\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data[\"GRN_ID\"].count(), grn_data[\"GRN_ID\"].nunique(), grn_data[\n", "#     \"GRN_ID\"\n", "# ].count() - grn_data[\"GRN_ID\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # sheet_id='1ZxaTO3jKGFjoABArZ0NDvD7zzUANGjUrxeZjmjTymh4'\n", "# sheet_id = \"1v4bcv2pyhSFwT3CIQKHEORQGQGr9K0-eo61yViCg0Yw\"\n", "# time.sleep(60)\n", "# sheet_name = \"grocery\"\n", "# pb.to_sheets(grocery_weekly, sheet_id, sheet_name, service_account=\"service_account\")\n", "# print('sent to grocery sheet')\n", "\n", "# time.sleep(60)\n", "# sheet_name = \"fnv\"\n", "# pb.to_sheets(fnv_weekly, sheet_id, sheet_name, service_account=\"service_account\")\n", "# print('sent to fnv sheet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data.drop(['invoice_link'], axis=1, inplace = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fnv=fnv[(fnv['Stock_In_Date']>=start_date) & (fnv['Stock_In_Date']<=end_date)].shape\n", "# (fnv['Stock_In_Date'].unique())\n", "# grocery_weekly.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# External sheet for vendor city\n", "sheet_id = \"1SWaL5s0TXSusvGhFgKc2ZojXr1jRHglwnipf4V5Fz2w\"\n", "sheet_name = \"vendor_city\"\n", "cities = from_sheets(sheet_id, sheet_name)\n", "city_list = tuple(list(cities[\"City\"]))\n", "print(city_list[0:10])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# final = pd.<PERSON><PERSON><PERSON><PERSON>()\n", "tracker_data = pd.DataFrame()\n", "for i in city_list:\n", "    print(i)\n", "\n", "    #     sheet_id = \"11N3Twzh7mIo_XGMXgvfAz7AUcESVjEd5HPzuaW0zPpc\"\n", "    sheet_id = \"1BFcfALY4yeWiKJPOadFT7UGE9gRBJHMvCzxm7njdfWY\"\n", "    sheet_name = i\n", "    df = from_sheets(sheet_id, sheet_name)\n", "    new_header = df.iloc[1]\n", "    df.columns = new_header\n", "    df = df[2:]\n", "    dfx = df.iloc[:, :]\n", "    #     final = final.append(df,sort=False,ignore_index=True)\n", "    #     final1=pd.concat([final1, dfx] ,axis=0)\n", "    tracker_data = pd.concat([tracker_data, dfx], ignore_index=True, axis=0, sort=False)\n", "    #     print(dfx.shape)\n", "    print(tracker_data.shape)\n", "#     print(dfx.columns==final1.columns)\n", "\n", "# s.copy(deep=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["city_list = [\"Gurgaon\", \"Jhajjar\", \"Sonepat\"]\n", "sheet_id = \"1gkAVOValnmkrZBA6zRI5pZ5GIIwjgjLNHafclkhV_X8\"\n", "\n", "for i in city_list:\n", "    print(i)\n", "    sheet_name = i\n", "    df = from_sheets(sheet_id, sheet_name)\n", "    new_header = df.iloc[1]\n", "    df.columns = new_header\n", "    df = df[2:]\n", "    dfx = df.iloc[:, :]\n", "    tracker_data = pd.concat([tracker_data, dfx], ignore_index=True, axis=0, sort=False)\n", "    print(tracker_data.shape)\n", "# df = from_sheets(sheet_id, \"Gurgaon\")\n", "# df1 = from_sheets(sheet_id, \"Jhajjar\")\n", "# df2 = from_sheets(sheet_id, \"Sonepat\")\n", "# new_header = df.iloc[1]\n", "# df.columns = new_header\n", "# df = df[2:]\n", "# dfx = df.iloc[:, :]\n", "# df1 = df1[2:]\n", "# df2 = df2[2:]\n", "# print(df.shape,df1.shape,df2.shape)\n", "# df = pd.concat([df, df1, df2], axis=0)\n", "# new_header = pd.concat([df, df1, df2], axis=0).iloc[1]\n", "# df.columns = new_header\n", "# dfx = df.iloc[:, :]\n", "# #     final = final.append(df,sort=False,ignore_index=True)\n", "# tracker_data = pd.concat([tracker_data, dfx], ignore_index=True, axis=0, sort=False)\n", "#     final1=pd.concat([final1, dfx] ,axis=0)\n", "# print(dfx.shape)\n", "# print(tracker_data.shape)\n", "# print(dfx.columns==final1.columns)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["indexNames = tracker_data[\n", "    (tracker_data[\"City Name\"] == \"\")\n", "    & (tracker_data[\"Outlet Name\"] == \"\")\n", "    & (tracker_data[\"GRN NO\"] == \"\")\n", "    & (tracker_data[\"GRN NO\"] == \"\")\n", "    & (tracker_data[\"GRN NO\"] == \"\")\n", "].index\n", "\n", "tracker_data.drop(indexNames, inplace=True)\n", "print(tracker_data.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tracker_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tracker_data[\"City Name\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tracker_data_1 = tracker_data[\n", "    tracker_data[\"City Name\"].isin(\n", "        [\n", "            \"Ahmedabad\",\n", "            \"Bengaluru\",\n", "            \"Chennai\",\n", "            \"Kolkata\",\n", "            \"Lucknow\",\n", "            \"Mumbai\",\n", "            \"Hyderabad\",\n", "            \"Pune\",\n", "            \"Zirakpur\",\n", "        ]\n", "    )\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tracker_data_2 = tracker_data[\n", "    ~tracker_data[\"City Name\"].isin(\n", "        [\n", "            \"Ahmedabad\",\n", "            \"Bengaluru\",\n", "            \"Chennai\",\n", "            \"Kolkata\",\n", "            \"Lucknow\",\n", "            \"Mumbai\",\n", "            \"Hyderabad\",\n", "            \"Pune\",\n", "            \"Zirakpur\",\n", "        ]\n", "    )\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["print(tracker_data_1.size, tracker_data_2.size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["import time\n", "\n", "sheet_id_1 = \"1_q4JmNFVxYc_Yh1GSns7bCfP9aeSUaIyqdoNPHVlMVM\"\n", "sheet_id_2 = \"1MyjcG8Qq-XIVs_9DJZ9XyNRC3YMU2A8zeWZn5grWQNg\"\n", "sheet_name = \"merge_tracker\"\n", "print(\"pushing merge tracker data - 1\")\n", "to_sheets(tracker_data_1, sheet_id_1, sheet_name)\n", "print(\"pushing merge tracker data - 2\")\n", "to_sheets(tracker_data_2, sheet_id_2, sheet_name)\n", "print(\"merge_tracker_data_pushed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# sheet_id = \"1ZxaTO3jKGFjoABArZ0NDvD7zzUANGjUrxeZjmjTymh4\"\n", "# sheet_name = \"Sheet2\"\n", "# # pb.to_sheets(tracker_data, sheet_id, sheet_name, service_account=\"service_account\")\n", "# # tracker_data.to_csv('tracker_data.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# dd=final1.dropna(axis=0,subset=['Outlet Name'])\n", "# dd=final1.dropna(how='any',axis=0, subset=['City Name'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tracker_data[\"City Name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # tracker_data.iloc[:,10:20].describe()\n", "# print(\"duplicate GRNS in tracker\")\n", "# tracker_data[\"GRN NO\"].count(), tracker_data[\"GRN NO\"].nunique(), tracker_data[\n", "#     \"GRN NO\"\n", "# ].count() - tracker_data[\"GRN NO\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grn_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # grn_data.merge(tracker_data, left_on='lkey', right_on='rkey',\n", "# #           suffixes=('_left', '_right'))\n", "\n", "# final = pd.merge(\n", "#     grn_data, tracker_data, how=\"left\", left_on=\"GRN_ID\", right_on=\"GRN NO\"\n", "# )\n", "# #                         ).sort_values(by=['skus','qtys'],ascending=[False,True])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final.to_csv('final.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final = final.loc[:, ~final.columns.duplicated()]\n", "\n", "# column_names = [\n", "#     \"City_Name\",\n", "#     \"Merchant_Name\",\n", "#     \"Vendor_Supply_Location\",\n", "#     \"Stock_In_Date\",\n", "#     \"GRN_ID\",\n", "#     \"Purchase_Number\",\n", "#     \"Merchant_Invoice_ID\",\n", "#     \"amount\",\n", "#     \"outlet_id\",\n", "#     \"Outlet_Name\",\n", "#     \"merchant_id\",\n", "#     \"Manufacturer_Name\",\n", "#     \"Item_Type\",\n", "#     \"GRN NO\",\n", "#     \"Invoice no as per Po<PERSON>\",\n", "#     \" Total GRN Value \",\n", "#     \"Credit Day's\",\n", "#     \"Payment Due date\",\n", "#     \"Phy Invoice No\",\n", "#     \"Phy Invoice Date\",\n", "#     #                 ' Invoice Value ',\n", "#     #                 ' Discrepancy Value ','TCS Amount', ' Discount Value ', ' Freight Charges ',\n", "#     \"Approved Payment\",\n", "#     \" GRN Diff \",\n", "#     \"Invoice Check Status\",\n", "#     \"Invoice OK date\",\n", "#     \"Payment Mode\",\n", "#     \"SPAN\",\n", "#     \" Purchase Return \",\n", "#     \" Incentive on sale \",\n", "#     \" Bargain/Rate Diff \",\n", "#     \" Advance Paid \",\n", "#     \" Paid Value \",\n", "#     \"Payment Status\",\n", "#     \"Payment Date\",\n", "#     \"Remarks\",\n", "# ]\n", "# final = final.reindex(columns=column_names)\n", "\n", "# print(final.shape)\n", "# final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final[final['GRN NO']>'1'].head(20)\n", "# (final['Approved Payment']).describe()\n", "# final.describe()\n", "# final.<PERSON><PERSON>[30:32,:46]\n", "\n", "# final['amount']\n", "\n", "# final['Approved Payment']=final['Approved Payment'].fillna(0)\n", "# final['Approved Payment']\n", "# db_table.replace('#N/A', '',inplace=True)\n", "# final['Approved Payment'] = final['Approved Payment'].astype('float')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# type(tracker_data[\" Paid Value \"][1])\n", "# tracker_data.iloc[0:2,10:46]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final['Approved Payment'] = final['Approved Payment'].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tracker_data[' Paid Value '] = tracker_data[' Paid Value '].astype('float')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id = \"1ZxaTO3jKGFjoABArZ0NDvD7zzUANGjUrxeZjmjTymh4\"\n", "# sheet_name = \"Sheet3\"\n", "# # pb.to_sheets(final, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import openpyxl\n", "# final.to_excel('final.xlsx', index = False, header=True)\n", "# final.to_csv('final.csv')\n", "# tracker_data.to_csv('tracker_data.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # pd.set_option('display.width', None)\n", "# # pd.set_option('display.max_colwidth', -1)\n", "# print(final.shape)\n", "# final.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From here\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # live_tracker['del_date']=live_tracker['del_date'].astype('datetime64[ns]')\n", "\n", "# run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "# # run_id =run_id.date()\n", "# # final[\"run\"] = run_id\n", "# final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # final.dtypes\n", "# db_table = final.copy(deep=True)\n", "# db_table[\"run\"] = run_id\n", "# print(db_table.shape)\n", "# db_table.columns = db_table.columns.str.strip()\n", "# # db_table.columns\n", "# db_table.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table[db_table[\"GRN_ID\"] == \"GRN/HTAHM04/VS/435/2021/18042020/000000186\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_names = [\n", "#     \"run\",\n", "#     \"City_Name\",\n", "#     \"Merchant_Name\",\n", "#     \"Vendor_Supply_Location\",\n", "#     \"Stock_In_Date\",\n", "#     \"GRN_ID\",\n", "#     \"Purchase_Number\",\n", "#     \"Merchant_Invoice_ID\",\n", "#     \"amount\",\n", "#     \"outlet_id\",\n", "#     \"Outlet_Name\",\n", "#     \"merchant_id\",\n", "#     \"Manufacturer_Name\",\n", "#     \"Item_Type\",\n", "#     \"GRN NO\",\n", "#     \"Invoice no as per Po<PERSON>\",\n", "#     #                 'Total GRN Value',\n", "#     \"Credit Day's\",\n", "#     \"Payment Due date\",\n", "#     \"Phy Invoice No\",\n", "#     \"Phy Invoice Date\",\n", "#     #                'Approved Payment',\n", "#     #                 'GRN Diff',\n", "#     \"Invoice Check Status\",\n", "#     #                'Invoice OK date',\n", "#     \"Payment Mode\",\n", "#     #                 'SPAN',\n", "#     \"Purchase Return\",\n", "#     \"Incentive on sale\",\n", "#     \"Bargain/Rate Diff\",\n", "#     \"Advance Paid\",\n", "#     \"Paid Value\",\n", "#     \"Payment Status\",\n", "#     \"Payment Date\"\n", "#     #                 'Remarks'\n", "# ]\n", "# db_table = db_table.reindex(columns=column_names)\n", "\n", "# db_table.replace(\"#N/A\", \"\", inplace=True)\n", "# db_table[\"Phy Invoice Date\"].replace({\"0\": \"\"}, inplace=True)\n", "# db_table.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\"name\": \"run\", \"type\": \"datetime\", \"description\": \"run\"},\n", "#     {\"name\": \"City_Name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "#     {\"name\": \"Merchant_Name\", \"type\": \"varchar\", \"description\": \"merchant_name\"},\n", "#     {\n", "#         \"name\": \"Vendor_Supply_Location\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"vendor location\",\n", "#     },\n", "#     {\"name\": \"Stock_In_Date\", \"type\": \"datetime\", \"description\": \"stockin date\"},\n", "#     {\"name\": \"GRN_ID\", \"type\": \"varchar\", \"description\": \"Grn id\"},\n", "#     {\"name\": \"Purchase_Number\", \"type\": \"varchar\", \"description\": \"purchase number\"},\n", "#     {\"name\": \"Merchant_Invoice_ID\", \"type\": \"varchar\", \"description\": \"invoice id\"},\n", "#     {\"name\": \"amount\", \"type\": \"float\", \"description\": \"amount\"},\n", "#     {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "#     {\"name\": \"Outlet_Name\", \"type\": \"varchar\", \"description\": \"outlet name\"},\n", "#     {\"name\": \"merchant_id\", \"type\": \"int\", \"description\": \"merchant id\"},\n", "#     {\n", "#         \"name\": \"Manufacturer_Name\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"manufacturer name\",\n", "#     },\n", "#     {\"name\": \"Item_Type\", \"type\": \"varchar\", \"description\": \"item type\"},\n", "#     {\"name\": \"GRN NO\", \"type\": \"varchar\", \"description\": \"GRN No\"},\n", "#     {\n", "#         \"name\": \"Invoice no as per Po<PERSON>\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"Invoice number in POS\",\n", "#     },\n", "#     #     {\"name\": 'Total GRN Value', \"type\": \"varchar\"},\n", "#     {\"name\": \"Credit Day's\", \"type\": \"int\", \"description\": \"credit days\"},\n", "#     {\"name\": \"Payment Due date\", \"type\": \"varchar\", \"description\": \"payment duedate\"},\n", "#     {\n", "#         \"name\": \"Phy Invoice No\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"physical invoice number\",\n", "#     },\n", "#     {\n", "#         \"name\": \"Phy Invoice Date\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"physical invoice date\",\n", "#     },\n", "#     #     {\"name\": 'Invoice Value', \"type\": \"float\"},\n", "#     #     {\"name\": 'Approved Payment', \"type\": \"varchar\"},\n", "#     #     {\"name\": '<PERSON>R<PERSON> Diff', \"type\": \"varchar\"},\n", "#     {\n", "#         \"name\": \"Invoice Check Status\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"invoice check status\",\n", "#     },\n", "#     #     {\"name\": 'Invoice OK date', \"type\": \"datetime\"},\n", "#     {\"name\": \"Payment Mode\", \"type\": \"varchar\", \"description\": \"payment mode\"},\n", "#     {\"name\": \"Purchase Return\", \"type\": \"varchar\", \"description\": \"purchase return\"},\n", "#     {\n", "#         \"name\": \"Incentive on sale\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"incentive on sale\",\n", "#     },\n", "#     {\"name\": \"Bargain/Rate Diff\", \"type\": \"varchar\", \"description\": \"bargain diff\"},\n", "#     #     {\"name\": 'SP<PERSON>', \"type\": \"varchar\"}\n", "#     {\"name\": \"Advance Paid\", \"type\": \"varchar\", \"description\": \"advance payment\"},\n", "#     {\"name\": \"Paid Value\", \"type\": \"varchar\", \"description\": \"paid value\"},\n", "#     {\"name\": \"Payment Status\", \"type\": \"varchar\", \"description\": \"payment status\"},\n", "#     {\"name\": \"Payment Date\", \"type\": \"varchar\", \"description\": \"payment date\"}\n", "#     #     {\"name\": 'Remarks', \"type\": \"varchar\"},\n", "#     #     {\"name\": 'run', \"type\": \"datetime\"},\n", "# ]\n", "# len(column_dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table[db_table[\"Purchase_Number\"] == \"1716372\"]\n", "# # 1716372\n", "# # 1652290\n", "# # db_table[db_table['Paid Value']>'1000'].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# drop_grn_list = [\n", "#     \"GRN/HTGGN4/VS/410/2021/03042020/000000046\",\n", "#     \"GRN/HTGGN4/VS/410/2021/20042020/000000431\",\n", "#     \"GRN/HTGGN4/VS/410/2021/23032020/000011335\",\n", "#     \"GRN/HTBAN4/VS/413/2021/03032020/000010670\",\n", "#     \"GRN//VS/432/1920/14012020/000010373\",\n", "#     \"GRN//VS/432/2021/22042020/000000574\",\n", "#     \"GRN/HTGGN11/VS/436/2021/21042020/000000427\",\n", "#     \"GRN/HTGGN12/VS/444/2021/16042020/000000317\",\n", "#     \"GRN/HTGGN12/VS/444/2021/20042020/000000428\",\n", "#     \"GRN/HTGGN12/VS/521/2021/22042020/000000433\",\n", "#     \"GRN/HTGGN12/VS/521/2021/27042020/000000645\",\n", "#     \"GRN/HTMUM9_3/VS/581/2021/25042020/000000054\",\n", "#     \"GRN/HTGGN12_1/VS/595/2021/21032020/000000249\",\n", "#     \"GRN/Das201/VS/713/2021/02042020/000000018\",\n", "#     \"GRN/HTUP2/VS/408/1920/01022020/000007271\",\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # db_table1 = db_table[~db_table['GRN_ID'].isin(a)]\n", "# db_table1 = db_table[~db_table[\"GRN_ID\"].isin(drop_grn_list)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(db_table1.shape)\n", "# db_table1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table1[(db_table1[\"GRN_ID\"] == \"GRN/HTBAN5/VS/442/2021/26062020/000002632\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"finance_vendor_payment_tracker\",\n", "#     \"column_dtypes\": column_dtypes,\n", "#     \"primary_key\": [\"Stock_In_Date\", \"GRN_ID\"],\n", "#     \"sortkey\": [\"Stock_In_Date\"],\n", "#     \"incremental_key\": \"run\",\n", "#     \"load_type\": \"rebuild\",\n", "#     \"table_description\": \"storing the finance payment details\",  # append, rebuild, truncate or upsert\n", "# }\n", "\n", "# print(len(column_dtypes))\n", "# try:\n", "#     pb.to_redshift(db_table1.iloc[:, :], **kwargs)\n", "#     print(\"table published\")\n", "# except:\n", "#     print(\"Error publishing \")\n", "# # kwargs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # db_table1=db_table1.iloc[:,:]\n", "# # tt['Paid Value']=tt['Paid Value'].astype('float64')\n", "# # tt.head()\n", "\n", "\n", "# def columns_update(column):\n", "#     column = column.str.replace(\",\", \"\")\n", "#     column = column.str.replace(\"-\", \"\")\n", "#     column = pd.to_numeric(column, errors=\"coerce\")\n", "#     return column\n", "\n", "\n", "# db_table1[\"Purchase Return\"] = columns_update(db_table1[\"Purchase Return\"])\n", "# db_table1[\"Incentive on sale\"] = columns_update(db_table1[\"Incentive on sale\"])\n", "# db_table1[\"Bargain/Rate Diff\"] = columns_update(db_table1[\"Bargain/Rate Diff\"])\n", "# db_table1[\"Advance Paid\"] = columns_update(db_table1[\"Advance Paid\"])\n", "# db_table1[\"Paid Value\"] = columns_update(db_table1[\"Paid Value\"])\n", "# db_table1[\"Adjustments\"] = (\n", "#     db_table1[\"Purchase Return\"]\n", "#     + db_table1[\"Incentive on sale\"]\n", "#     + db_table1[\"Bargain/Rate Diff\"]\n", "#     + db_table1[\"Advance Paid\"]\n", "# )\n", "# db_table1[\"amount_net_payable\"] = db_table[\"amount\"] - db_table1[\"Adjustments\"]\n", "\n", "# # df.apply(lambda x: func(x['col1'],x['col2']),axis=1)\n", "# # db_table1['Incentive on sale'] = df.apply(lambda x: columns_update(db_table1['Incentive on sale']),axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\"name\": \"run\", \"type\": \"datetime\", \"description\": \"run\"},\n", "#     {\"name\": \"City_Name\", \"type\": \"varchar\", \"description\": \"city\"},\n", "#     {\"name\": \"Merchant_Name\", \"type\": \"varchar\", \"description\": \"merchant_name\"},\n", "#     {\n", "#         \"name\": \"Vendor_Supply_Location\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"vendor_locatoin\",\n", "#     },\n", "#     {\"name\": \"Stock_In_Date\", \"type\": \"datetime\", \"description\": \"stockin date\"},\n", "#     {\"name\": \"GRN_ID\", \"type\": \"varchar\", \"description\": \"grn id\"},\n", "#     {\n", "#         \"name\": \"Merchant_Invoice_ID\",\n", "#         \"type\": \"varchar\",\n", "#         \"description\": \"merchant invoice id\",\n", "#     },\n", "#     {\"name\": \"amount\", \"type\": \"float\", \"description\": \"amount\"},\n", "#     {\"name\": \"Purchase Return\", \"type\": \"float\", \"description\": \"purchase return\"},\n", "#     {\"name\": \"Incentive on sale\", \"type\": \"float\", \"description\": \"incentive on sale\"},\n", "#     {\"name\": \"Bargain/Rate Diff\", \"type\": \"float\", \"description\": \"bargain diff\"},\n", "#     {\"name\": \"Advance Paid\", \"type\": \"float\", \"description\": \"advance paid\"},\n", "#     {\"name\": \"Paid Value\", \"type\": \"float\", \"description\": \"paid value\"},\n", "#     {\"name\": \"Payment Status\", \"type\": \"varchar\", \"description\": \"payment status\"},\n", "#     {\"name\": \"amount_net_payable\", \"type\": \"float\", \"description\": \"amount payable\"},\n", "#     {\"name\": \"adjustments\", \"type\": \"float\", \"description\": \"adjustments of amount\"},\n", "#     #     {\"name\": \"Payment Date\", \"type\": \"datetime\"},\n", "# ]\n", "\n", "# column_names = [\n", "#     \"run\",\n", "#     \"City_Name\",\n", "#     \"Merchant_Name\",\n", "#     \"Vendor_Supply_Location\",\n", "#     \"Stock_In_Date\",\n", "#     \"GRN_ID\",\n", "#     \"Merchant_Invoice_ID\",\n", "#     \"amount\",\n", "#     \"Purchase Return\",\n", "#     \"Incentive on sale\",\n", "#     \"Bargain/Rate Diff\",\n", "#     \"Advance Paid\",\n", "#     \"Paid Value\",\n", "#     \"Payment Status\",\n", "#     #     'Incentive on sales',\n", "#     \"amount_net_payable\",\n", "#     \"adjustments\"\n", "#     #     \"Payment Date\",\n", "# ]\n", "\n", "# db_table1 = db_table1.reindex(columns=column_names)\n", "\n", "# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"finance_vendor_payment_tracker_test\",\n", "#     \"column_dtypes\": column_dtypes,\n", "#     \"primary_key\": [\"Stock_In_Date\", \"GRN_ID\"],\n", "#     \"sortkey\": [\"Stock_In_Date\"],\n", "#     \"incremental_key\": \"run\",\n", "#     \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "# }\n", "\n", "# len(column_dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table1[\"adjustments\"] = (\n", "#     db_table1[\"Purchase Return\"].fillna(0)\n", "#     + db_table1[\"Incentive on sale\"].fillna(0)\n", "#     + db_table1[\"Bargain/Rate Diff\"].fillna(0)\n", "#     + db_table1[\"Advance Paid\"].fillna(0)\n", "# )\n", "# db_table1[\"amount_net_payable\"] = db_table[\"amount\"] - db_table1[\"adjustments\"].fillna(\n", "#     0\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"finance_vendor_payment_tracker_test\",\n", "#     \"column_dtypes\": column_dtypes,\n", "#     \"primary_key\": [\"Stock_In_Date\", \"GRN_ID\"],\n", "#     \"sortkey\": [\"Stock_In_Date\"],\n", "#     \"incremental_key\": \"run\",\n", "#     \"load_type\": \"rebuild\",\n", "#     \"table_description\": \"finace vendor payment data \",  # append, rebuild, truncate or upsert\n", "# }\n", "\n", "# print(len(column_dtypes))\n", "\n", "# pb.to_redshift(db_table1.iloc[1:, :], **kwargs)\n", "\n", "# print(\"table published\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ((datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)) - NOW_IST) / 60"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# db_table.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}