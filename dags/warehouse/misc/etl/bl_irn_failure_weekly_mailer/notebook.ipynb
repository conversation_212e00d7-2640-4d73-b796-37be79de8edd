{"cells": [{"cell_type": "code", "execution_count": null, "id": "cefbe94e-e398-4c8e-8ee6-d97fcbe74597", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "from tabulate import tabulate\n", "\n", "pos = pb.get_connection(\"[Replica] RDS POS\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "\n", "invoice_type_sql = \"\"\"\n", "select pit.name as invoice_type_name,invoice_type, count(distinct invoice_id) no_of_invoices_failed from irn_invoice\n", "join pos_invoice_type pit on pit.id=irn_invoice.invoice_type\n", "where date(created_at)>='2022-10-01' and date(created_at)<current_date - interval '3' day and date(created_at)>=current_date - interval '10' day and  irn_generation_state in (4,6,7)\n", "group by 1,2\n", "order by 2 desc\"\"\"\n", "\n", "invoice_type_df = pd.read_sql_query(sql=invoice_type_sql, con=pos)\n", "invoice_type_df = invoice_type_df[\n", "    [\"invoice_type_name\", \"invoice_type\", \"no_of_invoices_failed\"]\n", "].rename(\n", "    columns={\n", "        \"invoice_type_name\": \"Invoice Type\",\n", "        \"invoice_type\": \"Invoice Type ID\",\n", "        \"no_of_invoices_failed\": \"No of Invoices Failed\",\n", "    }\n", ")\n", "\n", "\n", "outlet_df = pd.read_sql_query(\n", "    \"\"\"select r.id outlet_id, \n", "       facility_id,f.name as facility_name\n", "from lake_retail.console_outlet r\n", "join lake_crates.facility f on f.id = r.facility_id\n", "\"\"\",\n", "    redshift,\n", ")\n", "\n", "# outlet_level_df=outlet_level_df.merge(outlet_df,on=['outlet_id'],how='inner')\n", "# outlet_level_df=outlet_level_df[['facility_name','outlet_id','no_of_invoices_failed']].rename(columns={'facility_name':\"Facility Name\",'outlet_id':'Outlet ID','no_of_invoices_failed':\"No of Invoices Failed\"})\n", "\n", "invoice_type_df\n", "\n", "invoice_level_info_sql = \"\"\"select date(created_at) as date,outlet_id,pit.name as invoice_type_name,invoice_id,failed_code,comment,retry_count  from irn_invoice\n", "join pos_invoice_type pit on pit.id=irn_invoice.invoice_type\n", "where date(created_at)>='2022-10-01' and date(created_at)<current_date - interval '3' day and date(created_at)>=current_date - interval '10' day and  irn_generation_state in (4,6,7)\"\"\"\n", "\n", "invoice_level_df = pd.read_sql_query(sql=invoice_level_info_sql, con=pos)\n", "invoice_level_df = invoice_level_df.sort_values(by=[\"date\"], ascending=True)\n", "invoice_level_df = invoice_level_df.merge(\n", "    outlet_df[[\"outlet_id\", \"facility_name\"]], on=[\"outlet_id\"], how=\"inner\"\n", ")\n", "\n", "invoice_level_df = invoice_level_df[\n", "    [\n", "        \"date\",\n", "        \"facility_name\",\n", "        \"invoice_type_name\",\n", "        \"invoice_id\",\n", "        \"failed_code\",\n", "        \"comment\",\n", "        \"retry_count\",\n", "    ]\n", "].rename(\n", "    columns={\n", "        \"date\": \"Date\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"invoice_id\": \"Invoice ID\",\n", "        \"comment\": \"Comment\",\n", "        \"failed_code\": \"Failed Code\",\n", "        \"facility_name\": \"Facility Name\",\n", "        \"retry_count\": \"Retry Count\",\n", "        \"invoice_type_name\": \"Invoice Type\",\n", "    }\n", ")\n", "invoice_level_df"]}, {"cell_type": "code", "execution_count": null, "id": "c86b3c71-999b-4dd9-972c-24bc1a9f9066", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    invoice_level_df,\n", "    \"1Urzf8DZ_KvSQ94Y6U1v1QDk6W-IlIsMTAvLTfcPwosA\",\n", "    \"Raw-Data\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d9ec0901-1f32-4e38-87ec-67ef2a6b1e43", "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"IRNs Failure Report [Weekly].\"\n", "html_content = \"\"\"<html>\n", "<head>\n", "<style> \n", "table, th, td {{ border: 1px solid black }}\n", "th, td {{ padding: 5px; }}\n", "</style>\n", "</head>\n", "<body><p>Hello team,</p>\n", "<p> PFB details of Einvoicing failures. This is a <a href=\"https://docs.google.com/spreadsheets/d/1Urzf8DZ_KvSQ94Y6U1v1QDk6W-IlIsMTAvLTfcPwosA/edit#gid=0\">link</a> for raw data.\n", "<p><b>Invoice Type Summary:<b></p>\n", "{table1}\n", "<p><PERSON><PERSON>,</p>\n", "<p><PERSON><PERSON><PERSON></p>\n", "</body></html>\"\"\"\n", "html_content = html_content.format(\n", "    table1=tabulate(\n", "        invoice_type_df.reset_index(drop=True),\n", "        showindex=False,\n", "        tablefmt=\"html\",\n", "        headers=\"keys\",\n", "    ),\n", ")\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "id": "a32f25f0-36e6-4ddf-a365-0a40924d69ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ca1a863-8872-4059-8af8-afaf9b095e87", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}