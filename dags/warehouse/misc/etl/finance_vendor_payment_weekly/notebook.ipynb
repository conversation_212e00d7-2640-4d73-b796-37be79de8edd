{"cells": [{"cell_type": "code", "execution_count": null, "id": "e7bffbdc-d930-4732-b40b-a74fcb360bda", "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "id": "bf528484-ce8b-409b-858c-b198b266679b", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "id": "60433a00-9271-438c-a06a-302aee4d337b", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "c477878e-3ca6-4d14-9e00-46a6faf2715b", "metadata": {}, "outputs": [], "source": ["end_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "start_date = (datetime.now() - timed<PERSON>ta(days=1)).date().replace(day=1)\n", "end_date, start_date"]}, {"cell_type": "markdown", "id": "6117e30d-9972-40a3-831b-ea423d6a4ccf", "metadata": {"tags": []}, "source": ["# Summary mailer"]}, {"cell_type": "code", "execution_count": null, "id": "f2ef9c4c-6f49-4f31-8f82-6495ac615f1b", "metadata": {}, "outputs": [], "source": ["# City Name | Outlet id | Outlet Name | No of Invoices GRN Done YD | No of Inovoices Uploaded YD | % Adherence"]}, {"cell_type": "code", "execution_count": null, "id": "03052afe-2e0c-43c6-b064-9d84e0327198", "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "SELECT DISTINCT City_Name,\n", "                Outlet_ID,\n", "                Outlet_Name,\n", "                business_type_id,\n", "                count(DISTINCT Merchant_Invoice_ID) AS Invoices_Created,\n", "                count(DISTINCT CASE\n", "                                   WHEN (File_Uploaded_at IS NOT NULL) THEN Merchant_Invoice_ID\n", "                                   ELSE NULL\n", "                               END) AS Invoices_Uploaded,\n", "                round((CASE\n", "                           WHEN Invoices_Created = 0 THEN NULL\n", "                           ELSE Invoices_Uploaded*100.0/Invoices_Created\n", "                       END),2) AS Upload_Adherence\n", "FROM\n", "  (SELECT inventory_update_id,\n", "          inventory_update_type_id,\n", "          Stock_Update_ID,\n", "          PO_Type,\n", "          Vendor_GST,\n", "          Vendor_Supply_Location,\n", "          Issue_Date,\n", "          Item_Type,\n", "          Manufacturer_Name,\n", "          Outlet_ID,\n", "          Outlet_Name,\n", "          business_type_id,\n", "          City_Name,\n", "          Stock_In_Date,\n", "          date(Created_At) Created_At,\n", "          Update_Type,\n", "          merchant_id,\n", "          Merchant_Name,\n", "          Merchant_Invoice_ID,\n", "          Purchase_Order_ID,\n", "          GRN_ID,\n", "          GRN_amount,\n", "          Stock_In_type,\n", "          invoice_rank,\n", "          invoice_link,\n", "          invoice_upload,\n", "          File_Uploaded_at\n", "   FROM\n", "     (SELECT i3.id AS \"Stock_Update_ID\",\n", "             i.inventory_update_id,\n", "             pur_ord.po_number AS Purchase_Number,\n", "             (CASE\n", "                  WHEN iii.source_type = 2 THEN 'ESTO Stock-in'\n", "                  WHEN iii.source_type = 3 THEN 'Customer returened without invoice'\n", "                  WHEN (iii.source_type = 1\n", "                        AND iii.valid_po=1) THEN 'Valid PO'\n", "                  ELSE 'In-Valid PO'\n", "              END) AS \"PO_Type\",\n", "             pur_ord.vendor_gst AS \"Vendor_GST\",\n", "             lo2.name AS \"Vendor_Supply_Location\",\n", "             Date(pur_ord.issue_date+interval'5.5 hrs') AS \"Issue_Date\",\n", "             Date(i3.manufacture_date+interval'5.5 hrs') AS \"Manufacture_Date\",\n", "             r.item_id AS Item_ID,\n", "             i.upc_id AS UPC_ID,\n", "             i.variant_id AS Variant_ID,\n", "             r.name AS Product,\n", "             (CASE\n", "                  WHEN r.outlet_type = 1 THEN 'F&V type'\n", "                  WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "                  ELSE 'Not Assigned'\n", "              END) AS \"Item_Type\",\n", "             man.name AS Manufacturer_Name,\n", "             r.is_ptr AS PTR_Flag,\n", "             i.\"delta\" AS Quantity,\n", "             i3.return_quantity,\n", "             (i.\"delta\"-i3.return_quantity) AS diff_quantity,\n", "             r.variant_mrp AS MRP,\n", "             r.variant_uom_text AS Grammage,\n", "             ro.id AS outlet_id,\n", "             ro.name AS Outlet_Name,\n", "             ro.business_type_id,\n", "             lo.name AS City_Name,\n", "             Date(i.pos_timestamp+interval'5.5 hrs') AS Stock_In_Date,\n", "             (i.created_at+interval'5.5 hrs') AS Created_At,\n", "             i2.id inventory_update_type_id,\n", "             i2.name AS Update_Type,\n", "             i3.expiry_date AS Expiry_Date,\n", "             rcm.id AS merchant_id,\n", "             rcm.name AS Merchant_Name,\n", "             i.merchant_invoice_id AS Merchant_Invoice_ID,\n", "             i.created_by_name AS Created_By,\n", "             i3.cost_price AS Cost_Price,\n", "             i3.landing_price AS Landing_Price,\n", "             i3.purchase_order_id AS Purchase_Order_ID,\n", "             i3.grn_id AS GRN_ID,\n", "             i3.landing_price * i.\"delta\" GRN_amount,\n", "             (i3.cost_price*(i.\"delta\"-i3.return_quantity)) AS Net_Cost_Price,\n", "             (i3.landing_price*(i.\"delta\"-i3.return_quantity)) AS Net_Landing_Price,\n", "             i3.cost_price * i.\"delta\" AS Total_Cost_Price,\n", "             ((i3.landing_price * i.\"delta\")-(i3.cost_price * i.\"delta\")) AS Tax,\n", "             (CASE\n", "                  WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "                  ELSE 'Normal Stock-In'\n", "              END) AS \"Stock_In_type\",\n", "             r.is_pl,\n", "             inv_file.uuid,\n", "             (rank() OVER (PARTITION BY inv_file.outlet_id,\n", "                                        inv_file.merchant_id,\n", "                                        inv_file.invoice_id\n", "                           ORDER BY inv_file.file_uploaded_at)) AS invoice_rank,\n", "             (CASE\n", "                  WHEN inv_file.file_uploaded_at IS NULL THEN 'Not Uploaded'\n", "                  ELSE 'Uploaded'\n", "              END) AS invoice_upload,\n", "             (inv_file.file_uploaded_at+interval'5.5 hrs') AS File_Uploaded_at,\n", "             concat('https://retail.grofers.com/pos/v1/vendor-invoice-file/',concat((inv_file.uuid)::text,'/download/')) AS invoice_link\n", "      FROM lake_ims.ims_inventory_log i\n", "      INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "      LEFT JOIN lake_rpc.product_brand br ON br.id=r.brand_id\n", "      LEFT JOIN lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "      INNER JOIN lake_retail.console_location lo ON ro.tax_location_id=lo.id\n", "      INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "      INNER JOIN lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "      INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "      INNER JOIN lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "      LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "      LEFT JOIN lake_pos.pos_invoice pi ON pi.invoice_id=i.merchant_invoice_id\n", "      LEFT JOIN lake_retail.console_location lo2 ON lo2.id=pur_ord.vendor_shipping_city_id\n", "      LEFT JOIN lake_pos.pos_vendor_invoice_file inv_file ON inv_file.outlet_id=i.outlet_id\n", "      AND inv_file.merchant_id=i.merchant_id\n", "      AND inv_file.invoice_id=i.merchant_invoice_id\n", "      AND date(inv_file.created_at+interval'5.5 hrs') >= CURRENT_DATE - 60\n", "      WHERE i.inventory_update_type_id IN (1,\n", "                                           28,\n", "                                           76,\n", "                                           90)\n", "        AND date(i.created_at+interval'5.5 hrs') BETWEEN CURRENT_DATE - 60 AND CURRENT_DATE - 1\n", "        AND i.\"delta\" > 0\n", "        AND rcm.name NOT IN\n", "          (SELECT DISTINCT name AS outlet_name\n", "           FROM lake_retail.console_outlet)\n", "        AND rcm.name NOT IN ('Customer Return without Invoice')\n", "        AND rcm.name NOT IN ('Customer Returns Without Invoice')\n", "        AND rcm.name NOT IN ('Dummy stock in merchant',\n", "                             'Dummy <PERSON>')\n", "        AND rcm.name NOT LIKE '%%HOT Warehouse%%'\n", "        AND rcm.name NOT LIKE '%%PN2 (HOT)%%'\n", "        AND ro.name NOT LIKE '%%Infra Outlet%%'\n", "        AND ro.name NOT LIKE '%%Liquidation%%' )TEMP)temp_group\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4\n", "ORDER BY 5 DESC\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "d60fc4d7-c6f6-4cf2-86df-8fced6fe3f76", "metadata": {}, "outputs": [], "source": ["summ_grn = pd.read_sql_query(query1, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "61066e94-24f6-4636-ab11-5b0241bdc56e", "metadata": {}, "outputs": [], "source": ["summ_grn[\"upload_adherence1\"] = summ_grn[\"upload_adherence\"].astype(str) + \" %\""]}, {"cell_type": "code", "execution_count": null, "id": "ebfb0e55-6331-4255-8c61-52c8d37e0107", "metadata": {}, "outputs": [], "source": ["summ_grn = summ_grn.rename(\n", "    columns={\n", "        \"city_name\": \"City Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"invoices_created\": \"Number of Invoices Created\",\n", "        \"invoices_uploaded\": \"Number of Invoices Uploaded\",\n", "        \"upload_adherence1\": \"Upload Adherence\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "799be232-ae69-455a-b180-3ba416c19dc3", "metadata": {}, "outputs": [], "source": ["summ_grn_fin = summ_grn[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet ID\",\n", "        \"Outlet Name\",\n", "        \"Number of Invoices Created\",\n", "        \"Number of Invoices Uploaded\",\n", "        \"Upload Adherence\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "57c17b73-7956-4521-bca4-e02eb4ac007c", "metadata": {}, "outputs": [], "source": ["mail_summ_1 = summ_grn[[\"Outlet ID\", \"business_type_id\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "83a24264-ad53-4dfc-8426-fc77f60c8777", "metadata": {}, "outputs": [], "source": ["mail_summ_1 = mail_summ_1[\n", "    (mail_summ_1.business_type_id == 1) | (mail_summ_1.business_type_id == 12)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "91c8d5be-3433-4520-afba-4926f396dba1", "metadata": {}, "outputs": [], "source": ["mail_summ_1 = mail_summ_1[[\"Outlet ID\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "5baef714-1880-4a32-a4ed-d26b5d8a74f0", "metadata": {}, "outputs": [], "source": ["outlets_1 = mail_summ_1[\"Outlet ID\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "e167aedf-9a08-4e36-92f3-600fab836d23", "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "\n", "SELECT city_name,\n", "       outlet_id,\n", "       outlet_name,\n", "       business_type_id,\n", "       count(DISTINCT invoice_id) AS invoices_created,\n", "       count(DISTINCT CASE\n", "                          WHEN file_uploaded_at IS NOT NULL THEN invoice_id\n", "                          ELSE NULL\n", "                      END) AS invoices_uploaded,\n", "       round((CASE\n", "                  WHEN invoices_created = 0 THEN NULL\n", "                  ELSE invoices_uploaded*100.0/invoices_created\n", "              END), 2) AS upload_adherence\n", "FROM\n", "  (SELECT lo.name AS city_name,\n", "          p.outlet_id,\n", "          r.name as outlet_name,\n", "          r.business_type_id,\n", "          p.merchant_id,\n", "          c1.name AS merchant_name,\n", "          p.invoice_id,\n", "          date(convert_timezone('Asia/Kolkata', p.pos_timestamp)) AS prn_date,\n", "          p.grofers_order_id AS merchant_invoice_id,\n", "          (CASE\n", "               WHEN pif.file_uploaded_at IS NULL THEN 'Not Uploaded'\n", "               ELSE 'Uploaded'\n", "           END) AS invoice_upload,\n", "          convert_timezone('Asia/Kolkata', pif.file_uploaded_at) AS file_uploaded_at,\n", "          sum(p1.quantity) AS quantity,\n", "          sum(p1.selling_price * p1.quantity) AS amount\n", "   FROM lake_retail.console_merchant c1\n", "   JOIN lake_pos.pos_invoice p ON p.merchant_id=c1.id\n", "   JOIN lake_pos.pos_invoice_product_details p1 ON p.id = p1.invoice_id\n", "   JOIN lake_rpc.product_product p2 ON p2.variant_id = p1.variant_id\n", "   LEFT JOIN lake_pos.pos_invoice_file pif ON pif.invoice_id = p.invoice_id\n", "   JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "   INNER JOIN lake_retail.console_location lo ON r.tax_location_id=lo.id\n", "   AND r.active = 1\n", "   AND r.device_id != 47\n", "   WHERE p.invoice_type_id = 6\n", "     AND p.pos_timestamp >= CURRENT_DATE - 60\n", "     AND p.pos_timestamp < CURRENT_DATE::TIMESTAMP - interval '5.5 hrs'\n", "     AND r.name NOT LIKE '%%Infra%%'\n", "     AND c1.name NOT LIKE '%%Dummy%%'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4\n", "ORDER BY 5 DESC\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7d2f665c-5257-4684-9a0b-08e92a2ee5de", "metadata": {}, "outputs": [], "source": ["summ_prn = pd.read_sql_query(query2, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "224d4b26-fcb5-4f23-97a7-f8d5196cfcfd", "metadata": {}, "outputs": [], "source": ["summ_prn[\"upload_adherence1\"] = summ_prn[\"upload_adherence\"].astype(str) + \" %\""]}, {"cell_type": "code", "execution_count": null, "id": "a7b66683-7921-4afc-aa57-c507aa12e933", "metadata": {}, "outputs": [], "source": ["summ_prn = summ_prn.rename(\n", "    columns={\n", "        \"city_name\": \"City Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"invoices_created\": \"Number of Invoices Created\",\n", "        \"invoices_uploaded\": \"Number of Invoices Uploaded\",\n", "        \"upload_adherence1\": \"Upload Adherence\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d396db42-fbe0-4e44-ac91-fc46ef352939", "metadata": {}, "outputs": [], "source": ["summ_prn_fin = summ_prn[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet ID\",\n", "        \"Outlet Name\",\n", "        \"Number of Invoices Created\",\n", "        \"Number of Invoices Uploaded\",\n", "        \"Upload Adherence\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "214b28d9-4a7a-45de-b5c4-8a1b36deaa3e", "metadata": {}, "outputs": [], "source": ["mail_summ_2 = summ_prn[[\"Outlet ID\", \"business_type_id\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c96ef92f-41e7-4397-b277-e8b16c022282", "metadata": {}, "outputs": [], "source": ["mail_summ_2 = mail_summ_2[\n", "    (mail_summ_2.business_type_id == 1) | (mail_summ_2.business_type_id == 12)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "02028c8a-f6c8-4a8e-9914-2f7b70c06d07", "metadata": {}, "outputs": [], "source": ["mail_summ_2 = mail_summ_2[[\"Outlet ID\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "fb5826a5-fe24-4839-a23a-b8065ee305e3", "metadata": {}, "outputs": [], "source": ["outlets_2 = mail_summ_2[\"Outlet ID\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "ae6831d8-6380-4d8b-bb0c-d90f559632f9", "metadata": {}, "outputs": [], "source": ["outlets = np.hstack((outlets_1, outlets_2))\n", "outlets = np.unique(outlets)"]}, {"cell_type": "code", "execution_count": null, "id": "10082e6c-dc87-4487-bad2-e2206e614637", "metadata": {}, "outputs": [], "source": ["query3 = f\"\"\"\n", "select  f.id, f.name, co.id as outlet_id, co.name as outlet_name\n", "from lake_retail.console_outlet co \n", "join lake_crates.facility f on f.id = co.facility_id \n", "where co.id in {*outlets,}\n", "and f.id not in (15,19,22,24,42,48,1766,906)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "880c8c65-4be0-4404-91f0-fa2725746fe2", "metadata": {}, "outputs": [], "source": ["data3 = pd.read_sql_query(query3, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "3afca831-4351-4fa8-8547-1c405a18b46c", "metadata": {}, "outputs": [], "source": ["data3 = data3.rename(\n", "    columns={\n", "        \"id\": \"Facility ID\",\n", "        \"name\": \"Facility Name\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ce0ee89d-f0de-4319-8b4e-323c8fae9696", "metadata": {}, "outputs": [], "source": ["mail_summ = data3.merge(\n", "    summ_grn[\n", "        [\n", "            \"Outlet ID\",\n", "            \"Number of Invoices Created\",\n", "            \"Number of Invoices Uploaded\",\n", "            \"Upload Adherence\",\n", "        ]\n", "    ],\n", "    on=\"Outlet ID\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a267dba1-c309-4fab-990a-78c85f9a6ded", "metadata": {}, "outputs": [], "source": ["mail_summ = mail_summ.merge(\n", "    summ_prn[\n", "        [\n", "            \"Outlet ID\",\n", "            \"Number of Invoices Created\",\n", "            \"Number of Invoices Uploaded\",\n", "            \"Upload Adherence\",\n", "        ]\n", "    ],\n", "    on=\"Outlet ID\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b351f06d-9818-4c9f-a699-daca3a287520", "metadata": {}, "outputs": [], "source": ["mail_summ = mail_summ.sort_values(by=\"Facility ID\")"]}, {"cell_type": "code", "execution_count": null, "id": "3d4387bf-4da6-4e4a-bfa9-27a37fb2ac85", "metadata": {}, "outputs": [], "source": ["mail_summ = mail_summ.fillna(-1)"]}, {"cell_type": "code", "execution_count": null, "id": "02b7aac7-0ea7-4967-bc9e-1bb6036582e1", "metadata": {}, "outputs": [], "source": ["mail_summ[\n", "    [\n", "        \"Number of Invoices Created_x\",\n", "        \"Number of Invoices Uploaded_x\",\n", "        \"Number of Invoices Created_y\",\n", "        \"Number of Invoices Uploaded_y\",\n", "    ]\n", "] = mail_summ[\n", "    [\n", "        \"Number of Invoices Created_x\",\n", "        \"Number of Invoices Uploaded_x\",\n", "        \"Number of Invoices Created_y\",\n", "        \"Number of Invoices Uploaded_y\",\n", "    ]\n", "].astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4331361c-78c8-4416-8788-c8d2d138fbbb", "metadata": {}, "outputs": [], "source": ["mail_summ.replace(to_replace=-1, value=\"-\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8046c524-3070-47b7-803a-9b56b90f1c0d", "metadata": {}, "outputs": [], "source": ["mail_summ"]}, {"cell_type": "code", "execution_count": null, "id": "2ffeb34c-dadc-42bd-a7b6-de188be5e6b3", "metadata": {}, "outputs": [], "source": ["tuples = [\n", "    (\"Facility ID\", \"\"),\n", "    (\"Facility Name\", \"\"),\n", "    (\"Outlet ID\", \"\"),\n", "    (\"Outlet Name\", \"\"),\n", "    (\"GRN\", \"Number of Invoices Created\"),\n", "    (\"GRN\", \"Number of Invoices Uploaded\"),\n", "    (\"GRN\", \"Upload Adherence\"),\n", "    (\"PRN\", \"Number of Invoices Created\"),\n", "    (\"PRN\", \"Number of Invoices Uploaded\"),\n", "    (\"PRN\", \"Upload Adherence\"),\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e0064e55-2291-434c-971e-c25263df25d7", "metadata": {}, "outputs": [], "source": ["mail_summ.columns = pd.MultiIndex.from_tuples(tuples)"]}, {"cell_type": "code", "execution_count": null, "id": "cf5bf610-8852-4afc-804a-05580177f2c5", "metadata": {}, "outputs": [], "source": ["summ_grn_fin.to_csv(\"GRN Upload Pendency\" + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a85ede57-0e55-4c6b-88aa-7dcea81f28a5", "metadata": {}, "outputs": [], "source": ["summ_prn_fin.to_csv(\"PRN Upload Pendency\" + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e17a503e-9237-4dc2-8918-6c62e430bf2d", "metadata": {}, "outputs": [], "source": ["emails = pb.from_sheets(\"1vF3kmEfI_8WNqAgjQmK48tlCKaQ7shHJhzROSAwj-8I\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b607aa4-3edb-462f-9fcc-fc51145922df", "metadata": {}, "outputs": [], "source": ["list1 = list(emails[\"emails\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "e0bf1a1f-22c7-4d69-98d9-ec2858cb62aa", "metadata": {}, "outputs": [], "source": ["list2 = [\"<EMAIL>\", \"<EMAIL>\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6699275f-6f96-4d7f-9a20-df8e70f8b1fb", "metadata": {}, "outputs": [], "source": ["list1.extend(list2)"]}, {"cell_type": "code", "execution_count": null, "id": "f7558e9b-1dd0-49b1-86ae-4f06b9a85ccc", "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email=\"<EMAIL>\",\n", "    to_email=list1,\n", "    subject=\"GRN & PRN Upload Pendency for last 60 days\",\n", "    html_content=\"\"\"Hi,<br> <br>\"\"\"\n", "    + \"\"\"Please find attached Vendor Invoice Upload status and RTV pickup confirmation for last 60 days\"\"\"\n", "    + \"\"\"<br><br>\"\"\"\n", "    + mail_summ.to_html(index=False)\n", "    .replace(\"<td>\", '<td align=\"center\">')\n", "    .replace('<tr style=\"text-align: right;\">', '<tr style=\"text-align: center;\">')\n", "    + \"\"\"<br><br>Outlet level data can be found in the csv files\"\"\"\n", "    + \"\"\"<br><br> PRN query : https://redash-queries.grofers.com/queries/239259/source\"\"\"\n", "    + \"\"\"<br><br> GRN query : https://redash-queries.grofers.com/queries/238585/source\"\"\"\n", "    + \"\"\"<br> <br> Thanks,<br>Warehouse data team\"\"\",\n", "    files=[\n", "        \"GRN Upload Pendency.csv\",\n", "        \"PRN Upload Pendency.csv\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29a5065a-9ae0-4cda-aef8-f257aef4369f", "metadata": {}, "outputs": [], "source": ["print(\"End of Code\")"]}, {"cell_type": "code", "execution_count": null, "id": "33a79eb7-4197-4204-bb97-f6005ac6fd06", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}