{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ims = pb.get_connection(\"ims\")\n", "# retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "-- select  outlet_id,count(*) from(\n", "select \n", "t1.id as Outlet_id,\n", "t1.name as Outlet_name,\n", "t1.location as Outlet_location,\n", "t1.phone_no,\n", "t1.email,\n", "t1.tin,\n", "t1.store_code,\n", "t1.address as Outlet_address,\n", "t1.active as Outlet_Active,\n", "t1.created_at,\n", "t1.created_by,\n", "t1.updated_at,\n", "t1.updated_by,\n", "t1.tax_location_id as City_id,\n", "t1.cin,\n", "t1.pan,\n", "t1.pos_config,\n", "t1.facility_id,\n", "t1.business_type_id,\n", "t2.name as Facility_name,\n", "t2.created_at as Fac_log_created,\n", "t2.updated_at as Fac_log_updated,\n", "t2.identifier as Facility_identifier,\n", "t2.enabled,\n", "t2.location_id as Facility_location_id,\n", "t3.name as city_name,\n", "t3.state_id as state_id,\n", "t4.name as state_name,\n", "t5.warehouse_id as warehouse_id,\n", "t6.name as warehouse_name,\n", "case  \n", "    when t3.state_id='1' then 'South'\n", "    when t3.state_id='2' then 'West'\n", "    when t3.state_id='3' then 'South'\n", "    when t3.state_id='4' then 'West'\n", "    when t3.state_id='5' then 'North'\n", "    when t3.state_id='6' then 'North'\n", "    when t3.state_id='7' then 'South'\n", "    when t3.state_id='8' then 'East'\n", "    when t3.state_id='9' then 'East'\n", "    when t3.state_id='10' then 'East'\n", "    when t3.state_id='11' then 'East'\n", "    when t3.state_id='12' then 'West'\n", "    when t3.state_id='13' then 'North'\n", "    when t3.state_id='14' then 'North'\n", "    when t3.state_id='15' then 'East'\n", "    when t3.state_id='16' then 'South'\n", "    when t3.state_id='17' then 'West'\n", "    when t3.state_id='18' then 'East'\n", "    when t3.state_id='19' then 'East'\n", "    when t3.state_id='20' then 'East'\n", "    when t3.state_id='21' then 'East'\n", "    when t3.state_id='22' then 'East'\n", "    when t3.state_id='23' then 'North'\n", "    when t3.state_id='24' then 'North'\n", "    when t3.state_id='25' then 'East'\n", "    when t3.state_id='26' then 'East'\n", "    when t3.state_id='27' then 'North'\n", "    when t3.state_id='28' then 'North'\n", "    when t3.state_id='29' then 'East'\n", "    when t3.state_id='30' then 'South'\n", "    when t3.state_id='31' then 'North'\n", "    when t3.state_id='32' then 'West'\n", "    when t3.state_id='33' then 'West'\n", "    when t3.state_id='34' then 'South'\n", "    when t3.state_id='35' then 'South'\n", "    when t3.state_id='36' then 'South'\n", "    when t3.state_id='37' then 'NA'\n", "else 'Others'\n", "end as Zone\n", "from \n", "lake_retail.console_outlet t1 \n", "left join lake_retail.warehouse_facility t2 on t2.id= t1.facility_id \n", "left join lake_retail.console_location t3 on t1.tax_location_id=t3.id \n", "left join lake_retail.console_state t4 on t4.id=t3.state_id \n", "left join lake_warehouse_location.warehouse_outlet_mapping t5 on t1.id=t5.outlet_id \n", "left join lake_warehouse_location.warehouse t6 on t6.id=t5.warehouse_id\n", "where  t1.created_at is not null and t1.device_id <> 47 and t1.active = 1\n", "-- and t1.id = 100\n", "-- )tt group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone = pd.read_sql_query(query, redshift)\n", "\n", "# inbound[\"for_date\"] = pd.to_datetime(\n", "#     inbound[\"for_date\"]\n", "# )\n", "# inbound.length\n", "# inbound.to_csv(\"outbound.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlet_zone['infra_facility']=outlet_zone['facility_id']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["outlet_zone[\"run_id\"] = run_id\n", "# outlet_zone=outlet_zone.fillna(0)\n", "# outlet_zone['Outlet_address']=''\n", "# outlet_zone['pos_config']=''\n", "outlet_zone = outlet_zone.fillna(0)\n", "outlet_zone.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone = outlet_zone[\n", "    [\n", "        \"run_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"outlet_location\",\n", "        \"phone_no\",\n", "        \"email\",\n", "        \"tin\",\n", "        \"store_code\",\n", "        \"outlet_address\",\n", "        \"outlet_active\",\n", "        \"created_at\",\n", "        \"created_by\",\n", "        \"updated_at\",\n", "        \"updated_by\",\n", "        \"city_id\",\n", "        \"cin\",\n", "        \"pan\",\n", "        \"facility_id\",\n", "        \"business_type_id\",\n", "        \"facility_name\",\n", "        \"facility_identifier\",\n", "        \"enabled\",\n", "        \"facility_location_id\",\n", "        \"city_name\",\n", "        \"state_id\",\n", "        \"state_name\",\n", "        \"warehouse_id\",\n", "        \"warehouse_name\",\n", "        \"zone\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone[\"infra_facility\"] = np.where(\n", "    (\n", "        (outlet_zone[\"business_type_id\"] == 5)\n", "        | (outlet_zone[\"outlet_name\"].str.contains(\"infra\"))\n", "    ),\n", "    1,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone[\"run_id\"] = outlet_zone[\"run_id\"].astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"run timestamp\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Outlet_name\"},\n", "    {\n", "        \"name\": \"outlet_location\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Outlet_location_details\",\n", "    },\n", "    {\"name\": \"phone_no\", \"type\": \"varchar\", \"description\": \"phone_no of store\"},\n", "    {\"name\": \"email\", \"type\": \"varchar\", \"description\": \"email_id of store\"},\n", "    {\"name\": \"tin\", \"type\": \"varchar\", \"description\": \"tax identification number\"},\n", "    {\"name\": \"store_code\", \"type\": \"varchar\", \"description\": \"store code\"},\n", "    {\n", "        \"name\": \"outlet_address\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"address of the outlet_id\",\n", "    },\n", "    {\"name\": \"outlet_active\", \"type\": \"int\", \"description\": \"flag for active status\"},\n", "    {\"name\": \"created_at\", \"type\": \"datetime\", \"description\": \"created_at\"},\n", "    {\"name\": \"created_by\", \"type\": \"varchar\", \"description\": \"created_by\"},\n", "    {\"name\": \"updated_at\", \"type\": \"datetime\", \"description\": \"updated_at\"},\n", "    {\"name\": \"updated_by\", \"type\": \"varchar\", \"description\": \"updated_by\"},\n", "    {\"name\": \"city_id\", \"type\": \"int\", \"description\": \"city identification\"},\n", "    {\n", "        \"name\": \"cin\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Corporate Identification Number\",\n", "    },\n", "    {\"name\": \"pan\", \"type\": \"varchar\", \"description\": \"PAN number\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"business_type_id\", \"type\": \"float\", \"description\": \"business_type_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"facility_identifier\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"facility_identification\",\n", "    },\n", "    {\"name\": \"enabled\", \"type\": \"int\", \"description\": \"enabled flag\"},\n", "    {\n", "        \"name\": \"facility_location_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"location id of facility\",\n", "    },\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "    {\"name\": \"state_id\", \"type\": \"int\", \"description\": \"state id of outlet\"},\n", "    {\"name\": \"state_name\", \"type\": \"varchar\", \"description\": \"state name\"},\n", "    {\n", "        \"name\": \"warehouse_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"warehouse identification number\",\n", "    },\n", "    {\"name\": \"warehouse_name\", \"type\": \"varchar\", \"description\": \"name of warehouse\"},\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"zone of outlet\"},\n", "    {\n", "        \"name\": \"infra_facility\",\n", "        \"type\": \"int\",\n", "        \"description\": \"flag for infra facility or not\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"outlet_zone_mapping\",\n", "    \"table_description\": \"storing the outlet to zone mapping\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"run_id\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_zone.zone.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Flat table (indent_details) creation\n", "pb.to_redshift(outlet_zone, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}