{"cells": [{"cell_type": "code", "execution_count": null, "id": "de69d8fb-b67b-4494-91b3-0158c6bbc207", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import math\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "! pip install tabulate"]}, {"cell_type": "code", "execution_count": null, "id": "98600fb2-d243-4ba1-89c4-0cf4022bdeb1", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "3469ca68-3b30-4fda-8020-7b67b03a61c1", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"select distinct icd.item_id,icd.name,l0,l0_id,l1,l1_id, \n", "case when id.handling_type=1 then 'Regular'\n", "when id.handling_type=2 then 'Fragile' \n", "when id.handling_type=3 then 'Hazardous' \n", "when id.handling_type=4 then 'Large' \n", "when id.handling_type=5 then 'Refrigerated' \n", "when id.handling_type=6 then 'Medicinal' \n", "when id.handling_type=7 then 'Special' \n", "when id.handling_type=8 then 'Packaging' \n", "when id.handling_type=9 then 'Gold' else 'Not Known' end as handling_type, \n", "\n", "case when id.storage_type =1 then 'Regular' \n", "when id.storage_type =2 then 'Fridge' \n", "when id.storage_type =3 then 'Freezer' \n", "when id.storage_type =4 then 'Large'\n", "when id.storage_type =5 then 'Heavy' \n", "when id.storage_type =6 then 'Non_Veg_Cold' \n", "when id.storage_type =7 then 'Non_Veg_Frozen' \n", "when id.storage_type =8 then 'Fast_Moving' \n", "when id.storage_type =9 then 'Secure_Asset' else 'Not Known' end as storage_type\n", "\n", "\n", "from lake_rpc.item_category_details icd\n", "join lake_rpc.item_details id on id.item_id=icd.item_id\n", "\"\"\"\n", "\n", "live_df = pd.read_sql_query(sql=sql, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "27649760-fd9e-4836-ae6f-7ffd3db193d4", "metadata": {}, "outputs": [], "source": ["live_df"]}, {"cell_type": "code", "execution_count": null, "id": "6688fe47-76f2-4efb-a35d-553397b2a10a", "metadata": {}, "outputs": [], "source": ["# stored_df=pd.DataFrame({'item_id':[10087016],'name':['Maggi Desi Cheesy Masala Noodles 60.5 g'],'l0':['Instant & Frozen Food'],'l0_id':[15],'l1':['Noodles_1'],'l1_id':[962]})\n", "stored_sql = \"\"\"select * from metrics.item_category_history\"\"\"\n", "\n", "stored_df = pd.read_sql_query(sql=stored_sql, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "1f276cb7-f4d2-4afa-9b60-117a30ff7ccb", "metadata": {}, "outputs": [], "source": ["stored_df = stored_df[\n", "    [\"item_id\", \"l0\", \"l0_id\", \"l1\", \"l1_id\", \"handling_type\", \"storage_type\"]\n", "]\n", "stored_df = stored_df.rename(\n", "    columns={\n", "        \"l0\": \"l0_old\",\n", "        \"l0_id\": \"Old L0\",\n", "        \"l1\": \"l1_old\",\n", "        \"l1_id\": \"Old L1\",\n", "        \"handling_type\": \"Old Handling Type\",\n", "        \"storage_type\": \"Old Storage Type\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e15c4b7a-246e-4852-af81-ddeb469e8f43", "metadata": {}, "outputs": [], "source": ["stored_df"]}, {"cell_type": "code", "execution_count": null, "id": "8f8f5d37-1ecb-4b72-bb45-a81ee87a2088", "metadata": {}, "outputs": [], "source": ["check_df = live_df.merge(stored_df, on=[\"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e6e3c2a-45f6-4762-93a0-480f20ae6feb", "metadata": {}, "outputs": [], "source": ["check_df = check_df.rename(\n", "    columns={\n", "        \"l0\": \"l0_new\",\n", "        \"l0_id\": \"New L0\",\n", "        \"l1\": \"l1_new\",\n", "        \"l1_id\": \"New L1\",\n", "        \"handling_type\": \"New Handling Type\",\n", "        \"storage_type\": \"New Storage Type\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1b098061-4cf1-4e87-8bf1-10a59f225977", "metadata": {}, "outputs": [], "source": ["check_df"]}, {"cell_type": "code", "execution_count": null, "id": "e22acb35-8723-4c77-974a-d31a8d533543", "metadata": {}, "outputs": [], "source": ["slack_df = check_df[\n", "    (check_df[\"New L0\"] != check_df[\"Old L0\"])\n", "    | (check_df[\"New L1\"] != check_df[\"Old L1\"])\n", "    | (check_df[\"New Handling Type\"] != check_df[\"Old Handling Type\"])\n", "    | (check_df[\"New Storage Type\"] != check_df[\"Old Storage Type\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ab92ce2-122e-418d-8eda-7ed361adcec8", "metadata": {}, "outputs": [], "source": ["slack_df"]}, {"cell_type": "code", "execution_count": null, "id": "582845d7-298a-4707-b2c3-cd0f2e7141ff", "metadata": {}, "outputs": [], "source": ["slack_df = slack_df[\n", "    [\n", "        \"item_id\",\n", "        \"l0_old\",\n", "        \"l0_new\",\n", "        \"l1_old\",\n", "        \"l1_new\",\n", "        \"Old Handling Type\",\n", "        \"New Handling Type\",\n", "        \"Old Storage Type\",\n", "        \"New Storage Type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9cec75b3-cbb2-493a-90e1-7bba61de51cf", "metadata": {}, "outputs": [], "source": ["# slack_df['slack_channel'] = 'test-channel-category'\n", "# slack_df\n", "channel = \"bl-fc-alerts\""]}, {"cell_type": "code", "execution_count": null, "id": "921d0f66-d6a5-49cb-83b7-5efd0481372c", "metadata": {}, "outputs": [], "source": ["slack_df[\"item_id\"] = slack_df[\"item_id\"].astype(str)\n", "slack_df = slack_df.rename(\n", "    columns={\n", "        \"item_id\": \"ITEM ID\",\n", "        \"l0_old\": \"Old L0 Category\",\n", "        \"l0_new\": \"New L0 Category\",\n", "        \"l1_old\": \"Old L1 Category\",\n", "        \"l1_new\": \"New L1 Category\",\n", "    }\n", ")\n", "slack_df"]}, {"cell_type": "code", "execution_count": null, "id": "7ceeea6c-c32f-48e4-bb21-b5db855c9050", "metadata": {}, "outputs": [], "source": ["slack_df"]}, {"cell_type": "code", "execution_count": null, "id": "6e0e3fc5-6fa4-46e8-ae4c-533433b9fef7", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    slack_df,\n", "    \"1duT2YXHBcua9PdcvwxSApS3u1-yfsebl0bUOMrbKngQ\",\n", "    \"Raw-Data\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "16145c94-4e84-4897-b7fb-f955e9d23905", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\n", "            \"ITEM ID\",\n", "            \"Old L0 Category\",\n", "            \"New L0 Category\",\n", "            \"Old L1 Category\",\n", "            \"New L1 Category\",\n", "            \"Old Handling Type\",\n", "            \"New Handling Type\",\n", "            \"Old Storage Type\",\n", "            \"New Storage Type\",\n", "        ],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "8039e773-b5a0-460c-832e-a78a22e7b0f2", "metadata": {}, "outputs": [], "source": ["if len(slack_df) > 0:\n", "    fig, ax = render_mpl_table(slack_df, header_columns=0)\n", "    fig.savefig(\"List_of_items.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "850930b8-c9d5-424a-bd95-6cc5bc5fd699", "metadata": {}, "outputs": [], "source": ["if len(slack_df) > 0:\n", "    now = datetime.now().date().strftime(\"%d %B, %Y\")\n", "    text = f\"\"\"*List of items with changes in category mapping/handling type/storage type - {now}* \\n refer to this <https://docs.google.com/spreadsheets/d/1duT2YXHBcua9PdcvwxSApS3u1-yfsebl0bUOMrbKngQ/edit#gid=0|link> for raw data..\"\"\"\n", "    pb.send_slack_message(channel, text, files=[\"List_of_items.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "293e049b-9099-4a22-af3e-caa07bc0d5d0", "metadata": {}, "outputs": [], "source": ["# live_df=pd.DataFrame({'item_id':[10087016],'l0':['Instant & Frozen Food'],'l0_id':[15],'l1':['Noodles_112'],'l1_id':[968],'handling_type':'Fragile','storage_type':'Fast_Moving'})\n", "live_df = live_df[\n", "    [\"item_id\", \"l0\", \"l0_id\", \"l1\", \"l1_id\", \"handling_type\", \"storage_type\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ca8b5c3a-0d17-4138-a439-9d3e1adf400a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"item_category_history\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "        {\n", "            \"name\": \"l0\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l0 category\",\n", "        },\n", "        {\n", "            \"name\": \"l0_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"l0 id\",\n", "        },\n", "        {\n", "            \"name\": \"l1\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l1 category\",\n", "        },\n", "        {\n", "            \"name\": \"l1_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"l1 id\",\n", "        },\n", "        {\n", "            \"name\": \"handling_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"handling type\",\n", "        },\n", "        {\n", "            \"name\": \"storage_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"storage type\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"item_id\"],\n", "    \"sortkey\": [\"item_id\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Item Category Mapping history\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "e791bf19-aa20-4e59-9cac-374db7ab91f3", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(live_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "adacbff5-6a61-426a-a5c7-b55c788b005e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "86971976-82a6-4c2b-a605-e8641bbb978a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}