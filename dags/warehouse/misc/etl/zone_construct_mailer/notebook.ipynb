{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import jinja2\n", "from datetime import timedelta\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "today = str(date.today())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_df = pb.from_sheets(\"1V8tVfEFmPxBS_v8hmaB22NuQBDN1o5Qv_V9XnA4iI_Y\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_df[\"facility_id\"] = pd.to_numeric(email_df[\"facility_id\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["data = \"\"\"With LOCATION AS\n", "  (SELECT item_level.facility_id,\n", "       item_level.outlet_id,\n", "       item_level.outlet_name,\n", "       (CASE\n", "            WHEN item_level.location_type = 1 THEN 'Entire Warehouse'\n", "            ELSE 'None'\n", "        END) AS zone_construct,\n", "       zz.zone_type,\n", "       cats.l0_id,\n", "       cats.l0,\n", "       cats.l1_id,\n", "       cats.l1,\n", "       items.handling_type,\n", "       items.storage_type,\n", "       item_level.zone_identifier as current_zone,\n", "       item_level.item_id,\n", "       items.name AS item_name,\n", "       item_level.location_quantity\n", "FROM\n", "  (SELECT outlet.facility_id,\n", "          outlet.id AS outlet_id,\n", "          outlet.name AS outlet_name,\n", "          wz.location_type,\n", "          wz.zone_identifier,\n", "          pt.item_id,\n", "          sum(pt.quantity) AS location_quantity\n", "   FROM lake_warehouse_location.warehouse_item_location pt\n", "   INNER JOIN lake_warehouse_location.warehouse_storage_location wz ON wz.id = pt.location_id\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = pt.warehouse_id\n", "   INNER JOIN lake_retail.console_outlet outlet ON outlet.id=wom.outlet_id\n", "   AND outlet_id = %(o_id)s\n", "   AND wz.is_active = 1\n", "   AND wz.location_type =\n", "     (SELECT (CASE\n", "                  WHEN json_extract_path_text(pos_config,'auto_sto_wave_creation_enabled') = 'true' THEN 1\n", "                  ELSE 2\n", "              END)\n", "      FROM lake_retail.console_outlet co\n", "      WHERE co.id = outlet.id)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6) item_level\n", "LEFT JOIN lake_rpc.item_category_details cats ON cats.item_id = item_level.item_id\n", "LEFT JOIN lake_rpc.item_details items ON items.item_id=item_level.item_id\n", "LEFT JOIN\n", "  (SELECT handling_type,\n", "          storage_type,\n", "          zone_type\n", "   FROM lake_warehouse_location.item_zone_type) zz ON zz.handling_type = coalesce(cast(items.handling_type AS INT),0)\n", "AND zz.storage_type = coalesce(cast(items.storage_type AS INT),0)\n", "WHERE item_level.location_quantity > 0), \n", "actual_zones AS\n", "  (SELECT wom.outlet_id,\n", "             a.zone_identifier,\n", "             b.zone_type,\n", "             (case when coalesce(a.fast_moving_zone,0) = 1 then 'FAST' \n", "                  when coalesce(a.selective_rack_zone,0) =  1 then 'SPR' else 'NONE' end) as extra_flag,\n", "             coalesce(l0_id,0) AS l0_id,\n", "             coalesce(l1_id,0) AS l1_id\n", "      FROM lake_warehouse_location.item_category_zone_type_zone_id b\n", "      INNER JOIN lake_warehouse_location.warehouse_zone_id_mapping a ON a.id = b.zone_id\n", "      INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = a.warehouse_id\n", "      INNER JOIN lake_retail.console_outlet co ON co.id = wom.outlet_id\n", "      WHERE co.id = %(o_id)s\n", "      AND a.enabled = 1 AND b.enabled = 1),\n", "out1 AS\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS outbound1\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "  AND outlet_id = %(o_id)s\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') = CURRENT_DATE-1\n", "   GROUP BY 1,\n", "            2),\n", "                                  out3 AS\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS outbound3\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "   AND outlet_id = %(o_id)s\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') BETWEEN CURRENT_DATE - 3 AND CURRENT_DATE - 1\n", "   GROUP BY 1,\n", "            2),\n", "                                  out7 AS\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          sum(billed_quantity) AS outbound7\n", "   FROM lake_ims.ims_sto_details i\n", "   JOIN lake_retail.console_outlet co ON co.id = i.outlet_id\n", "   AND outlet_id = %(o_id)s\n", "   JOIN lake_ims.ims_sto_item ii ON ii.sto_id = i.sto_id\n", "   WHERE date(i.created_at + interval '5.5 hrs') BETWEEN CURRENT_DATE -7 AND CURRENT_DATE-1\n", "   GROUP BY 1,\n", "            2)\n", "SELECT distinct a.facility_id,\n", "       a.outlet_id,\n", "       outlet_name,\n", "       zone_construct,\n", "       handling_type,\n", "       storage_type,\n", "       current_zone,\n", "       coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) AS actual_zone,\n", "       a.zone_type,\n", "       l0,a.l0_id,\n", "       l1,a.l1_id,\n", "       a.item_id,\n", "       a.item_name,\n", "       coalesce(cc.flag,'NONE') as extraflag,\n", "       location_quantity AS total_quantity,\n", "       coalesce(outbound1,0) AS total_outbound1,\n", "       coalesce(outbound3,0) AS total_outbound3,\n", "       coalesce(outbound7,0) AS total_outbound7\n", "FROM LOCATION a\n", "LEFT JOIN (select outlet_id, item_id, (case when config_type_id = 1 then 'SPR' when config_type_id = 2 then 'FAST' else 'NONE'end) as flag\n", "from lake_warehouse_location.item_outlet_configs\n", "where value = 'TRUE' and outlet_id = %(o_id)s and active = True) cc on cc.item_id = a.item_id\n", "LEFT JOIN actual_zones z2 ON z2.zone_type = a.zone_type AND z2.l1_id = a.l1_id AND z2.l0_id = a.l0_id AND z2.outlet_id = a.outlet_id\n", "and z2.extra_flag = coalesce(cc.flag,'NONE')\n", "LEFT JOIN actual_zones z1 ON z1.zone_type = a.zone_type AND z1.l0_id = a.l0_id AND z1.l1_id = 0 AND z1.l0_id != 0 AND z1.outlet_id = a.outlet_id\n", "and z1.extra_flag = coalesce(cc.flag,'NONE')\n", "LEFT JOIN actual_zones z ON z.zone_type = a.zone_type AND z.l0_id = 0 AND z.outlet_id = a.outlet_id\n", "and z.extra_flag = coalesce(cc.flag,'NONE')\n", "LEFT JOIN out1 ON out1.item_id = a.item_id AND out1.outlet_id = a.outlet_id\n", "LEFT JOIN out3 ON out3.item_id = a.item_id AND out3.outlet_id = a.outlet_id\n", "LEFT JOIN out7 ON out7.item_id = a.item_id AND out7.outlet_id = a.outlet_id\n", "WHERE a.outlet_id = %(o_id)s\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_df = pd.read_sql_query(\n", "    sql=\"\"\"SELECT DISTINCT outlet.id AS outlet_id, facility_id\n", "                    FROM lake_retail.console_outlet outlet\n", "                    INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.outlet_id = outlet.id\n", "                    AND wom.is_active =1\n", "                    WHERE json_extract_path_text(pos_config,'sto_split_criteria') IN (4,\n", "                                                                                      5,6)\n", "                      AND device_id NOT IN (47)\n", "                      AND outlet.id in (select distinct outlet_id \n", "                                from lake_ims.ims_sto_details \n", "                                where created_at::date = current_date-1)\n", "                      AND active = 1\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", ")\n", "\n", "# outlets_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["email_df = email_df.merge(outlets_df, on=\"facility_id\", how=\"left\")\n", "email_df[\"facility_id\"] = email_df[\"facility_id\"].fillna(\"common\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = outlets_df.outlet_id.unique().tolist()\n", "for o_id in outlets:\n", "    df = pd.read_sql_query(\n", "        sql=data,\n", "        con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "        params={\"o_id\": o_id},\n", "    )\n", "\n", "    summ_df = (\n", "        df.groupby(\n", "            [\n", "                \"facility_id\",\n", "                \"outlet_id\",\n", "                \"outlet_name\",\n", "                \"zone_construct\",\n", "                \"actual_zone\",\n", "            ]\n", "        )\n", "        .agg(\n", "            {\n", "                \"item_id\": \"count\",\n", "                \"total_quantity\": \"sum\",\n", "                \"total_outbound1\": \"sum\",\n", "                \"total_outbound3\": \"sum\",\n", "                \"total_outbound7\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(columns={\"item_id\": \"Total_SKUs\"})\n", "    )\n", "    onshelf_inventory_df = (\n", "        df.groupby([\"facility_id\", \"outlet_id\", \"outlet_name\", \"zone_construct\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"nunique\",\n", "                \"total_quantity\": sum,\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    df[\"different_zones\"] = np.where(df[\"current_zone\"] == df[\"actual_zone\"], 0, 1)\n", "    df1 = df[df[\"different_zones\"] == 0]\n", "    df2 = (\n", "        df1.groupby([\"facility_id\", \"outlet_id\", \"outlet_name\", \"zone_construct\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"nunique\",\n", "                \"total_quantity\": sum,\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    onshelf_inventory_df = onshelf_inventory_df.merge(\n", "        df2, on=[\"facility_id\", \"outlet_id\", \"outlet_name\", \"zone_construct\"]\n", "    )\n", "    onshelf_inventory_df.rename(\n", "        columns={\n", "            \"item_id_x\": \"Total SKUs\",\n", "            \"total_quantity_x\": \"Total Quantity\",\n", "            \"item_id_y\": \"SKUs in correct zone\",\n", "            \"total_quantity_y\": \"Qty in correct zone\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    onshelf_inventory_df[\"sku_percentage\"] = (\n", "        onshelf_inventory_df[\"SKUs in correct zone\"]\n", "        * 100.0\n", "        / onshelf_inventory_df[\"Total SKUs\"]\n", "    )\n", "    onshelf_inventory_df[\"qty_percentage\"] = (\n", "        onshelf_inventory_df[\"Qty in correct zone\"]\n", "        * 100.0\n", "        / onshelf_inventory_df[\"Total Quantity\"]\n", "    )\n", "    migration_df = df[df[\"different_zones\"] == 1]\n", "    df[df[\"outlet_id\"] == o_id].to_csv(\"Raw_Data.csv\", index=False)\n", "    migration_df.to_csv(\"Zone_Mismatch.csv\", index=False)\n", "    today = date.today()\n", "    yesterday = today - <PERSON><PERSON><PERSON>(days=1)\n", "    from_email = \"<EMAIL>\"\n", "    to_email = list(email_df[email_df.outlet_id == o_id].email_ids.unique())\n", "    cc = list(email_df[email_df.facility_id == \"common\"].email_ids.unique())\n", "    # to_email = ['<EMAIL>']\n", "    # cc = []\n", "    subject = \"Zone construct report for %s\" % yesterday\n", "    loader = jinja2.FileSystemLoader(\n", "        # searchpath=\"/home/<USER>/Development/airflow-dags/daggers/warehouse/misc/etl/zone_construct_mailer\"\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/misc/etl/zone_construct_mailer\"\n", "    )\n", "    # list of outlets at that facility for onshelf\n", "    outlets_onshelf = list(\n", "        onshelf_inventory_df[\n", "            onshelf_inventory_df[\"outlet_id\"] == o_id\n", "        ].outlet_id.unique()\n", "    )\n", "    print(outlets_onshelf)\n", "    print(\"-------\")\n", "    onshelf_inv_sku_adherence = []\n", "    onshelf_inv_qty_adherence = []\n", "    outlets_onshelf_names = []\n", "    for outlet in outlets_onshelf:\n", "        outlets_onshelf_names.append(\n", "            summ_df[summ_df[\"outlet_id\"] == o_id].outlet_name.unique()[0]\n", "        )\n", "    for outlet_id in outlets_onshelf:\n", "        onshelf_inv_sku_adherence.append(\n", "            onshelf_inventory_df[onshelf_inventory_df[\"outlet_id\"] == o_id]\n", "            .reset_index()\n", "            .iloc[:, 9][0]\n", "        )\n", "        onshelf_inv_qty_adherence.append(\n", "            onshelf_inventory_df[onshelf_inventory_df[\"outlet_id\"] == o_id]\n", "            .reset_index()\n", "            .iloc[:, 10][0]\n", "        )\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "    template = tmpl_environ.get_template(\"email_template.html\")\n", "    html_content = template.render(\n", "        reports=summ_df[summ_df.outlet_id == o_id].to_dict(orient=\"records\"),\n", "        today=yesterday,\n", "        onshelf_inv_sku_adherence=onshelf_inv_sku_adherence,\n", "        onshelf_inv_qty_adherence=onshelf_inv_qty_adherence,\n", "        outlets_onshelf=outlets_onshelf,\n", "        outlets_onshelf_names=outlets_onshelf_names,\n", "    )\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        cc=cc,\n", "        files=[\"Raw_Data.csv\", \"Zone_Mismatch.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}