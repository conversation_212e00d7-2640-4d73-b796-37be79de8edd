{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## WH Slot Redefinition\n", "\n", "### Objective\n", "- LM Capacity gets defined in 4 slots <Heavy, Normal, Premium and Reschedules> whereas warehouse is agnostic of this capacity. In order to make sure that the warehouse assigns representative capacities to the slots, we need to redefine the capacity at different slots. \n", "\n", "### To DOs\n", "\n", "- Pull capacity numbers\n", "- Pull the IoD numbers\n", "- Redefine IoD\n", "- Solve the linear Equation for redefinition"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python3 -m pip install -q catboost\n", "!python3 -m pip install -q joblib\n", "!python3 -m pip install -q sklearn\n", "!python3 -m pip install -q seaborn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pickle\n", "from collections import defaultdict\n", "import json\n", "from ast import literal_eval\n", "import numpy as np\n", "import re\n", "import math\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from catboost import CatBoostRegressor\n", "from sklearn.ensemble import RandomForestRegressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_colwidth\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename_cat_iod = \"/tmp/picking_time_ideal_cat_v3.csv\"\n", "local_filename_rm_iod = \"/tmp/picking_time_ideal_rm_v3.csv\"\n", "local_filename_lr_iod = \"/tmp/picking_time_ideal_lr_v3.csv\"\n", "local_filename_cat_model = \"/tmp/cat_boostmodel.sav\"\n", "local_filename_rm_model = \"/tmp/random_forest_model.sav\"\n", "local_filename_lr_model = \"/tmp/linear_regression_model.sav\"\n", "local_filename_plogs = \"/tmp/performance_logs.csv\"\n", "local_filename_elogs = \"/tmp/execution_logs.csv\"\n", "local_file_name_hist = \"/tmp/peep_history_stats.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_filename_cat_iod = \"/eep/warehouse/picking/data/picking_time_ideal_cat_v3.csv\"\n", "cloud_filename_rm_iod = \"/eep/warehouse/picking/data/picking_time_ideal_rm_v3.csv\"\n", "cloud_filename_lr_iod = \"/eep/warehouse/picking/data/picking_time_ideal_lr_v3.csv\"\n", "cloud_filename_cat_model = \"/eep/warehouse/picking/prod/model/catboost_model_v3.1.sav\"\n", "cloud_filename_rm_model = \"/eep/warehouse/picking/prod/model/random_forest_v3.1.sav\"\n", "cloud_filename_lr_model = \"/eep/warehouse/picking/prod/model/linear_regression_v3.1.sav\"\n", "cloud_filename_elogs = \"/eep/warehouse/picking/logs/execution_logs.csv\"\n", "cloud_filename_plogs = \"/eep/warehouse/picking/logs/v3/performance_logs\"\n", "cloud_filename_hist = \"/eep/warehouse/picking/data/peep_history_stats.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "pb.from_s3(bucket_name, cloud_filename_cat_iod, local_filename_cat_iod)\n", "pb.from_s3(bucket_name, cloud_filename_lr_iod, local_filename_lr_iod)\n", "\n", "pb.from_s3(bucket_name, cloud_filename_cat_model, local_filename_cat_model)\n", "pb.from_s3(bucket_name, cloud_filename_lr_model, local_filename_lr_model)\n", "\n", "pb.from_s3(bucket_name, cloud_filename_elogs, local_filename_elogs)\n", "pb.from_s3(bucket_name, cloud_filename_plogs, local_filename_plogs)\n", "pb.from_s3(bucket_name, cloud_filename_hist, local_file_name_hist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = pd.read_csv(local_filename_cat_iod)\n", "picking_time_ideal_lr_v3 = pd.read_csv(local_filename_lr_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = pd.read_csv(local_filename_cat_iod)\n", "picking_time_ideal_lr_v3 = pd.read_csv(local_filename_lr_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_logs = pd.read_csv(local_filename_elogs)\n", "performance_logs = pd.read_csv(local_filename_plogs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats = pd.read_csv(local_file_name_hist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import CatBoost\n", "\n", "model = CatBoostRegressor()\n", "model.load_model(local_filename_cat_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reading the capacity numbers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_query = \"\"\"\n", "with ancestor_capacity as\n", "(\n", "select \n", "\n", "wcd.outlet_id, \n", "outlet as Outlet_Name, \n", "ozm.facility_id as fac_id, ozm.facility_name fac_name,zone,\n", "date(delivery_date) as delivery_date, \n", "delivery_slot_type,\n", "sum(capacity) as Capacity\n", "\n", "from          metrics.warehouse_capacity_details  wcd\n", "left join     metrics.outlet_zone_mapping ozm on wcd.outlet_id=ozm.outlet_id\n", "where \n", "date(delivery_date) between current_date - INTERVAL '1 DAYS'  and  current_date + INTERVAL '5 DAYS'\n", "\n", "group by 1,2,3,4,5,6,7 order by 1,2\n", ")\n", "\n", "select \n", "date(delivery_date) as delivery_date, \n", "outlet_id, \n", "outlet_name as outlet_Name, \n", "delivery_slot_type,\n", "sum(Capacity) as Capacity\n", "\n", "from ancestor_capacity \n", "group by 1,2,3,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_data = pd.read_sql(capacity_query, con=pb.get_connection(\"redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_capacity = (\n", "    capacity_data.groupby([\"delivery_date\", \"outlet_id\", \"outlet_name\"])[\"capacity\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_capacity.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_iod(df, m, model_name):\n", "    features_dict = {\n", "        \"catboost\": list(set(df.columns.values) - set([\"picking_time_ideal\"]))\n", "    }\n", "    df[\"month\"] = asia_now.month\n", "    df[\"day\"] = asia_now.day\n", "    df.drop([\"picking_time_ideal\"], axis=1, inplace=True)\n", "    df[\"picking_time_ideal\"] = m.predict(df[features_dict[model_name]])\n", "    return df[set(features_dict[model_name]).union([\"picking_time_ideal\", \"outlet_id\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = calculate_iod(\n", "    picking_time_ideal_cat_v3.copy(), model, \"catboost\"\n", ").copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_capacity = daily_capacity.merge(\n", "    picking_time_ideal_cat_v3[[\"outlet_id\", \"picking_time_ideal\"]],\n", "    on=\"outlet_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_capacity.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_capacity[daily_capacity.outlet_id == 100].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Redefining Ideal Orders for Heavy and Normal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.columns.values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats = history_stats[\n", "    history_stats.outlet_id.isin(picking_time_ideal_cat_v3.outlet_id.unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[history_stats.is_heavy == \"normal\"].sort_values(\"SPO\").head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[\"month\"] = asia_now.month\n", "history_stats[\"day\"] = asia_now.day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[\"picking_time_ideal\"] = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.feature_names_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"retail\")\n", "\n", "Query = \"\"\"SELECT a.id,\n", "       b.name as BusinessType\n", "FROM retail.console_outlet a \n", "left join retail.console_business_type b on b.id = a.business_type_id\n", "\"\"\"\n", "\n", "Outlet_Business_Type = pd.read_sql_query(Query, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats = history_stats.merge(\n", "    Outlet_Business_Type, how=\"left\", left_on=\"outlet_id\", right_on=\"id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[\"BusinessType_Grocery\"] = 1\n", "history_stats[\"BusinessType_B2B_Bulk\"] = 0\n", "history_stats[\"BusinessType_Cold\"] = 0\n", "history_stats[\"BusinessType_Dark Store - External\"] = 0\n", "history_stats[\"BusinessType_Dark Store - Grofers\"] = 0\n", "history_stats[\"BusinessType_Dropshipment\"] = 0\n", "history_stats[\"BusinessType_Large\"] = 0\n", "history_stats[\"BusinessType_Marketplace\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats = history_stats.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_features = [\n", "    \"index\",\n", "    \"outlet_id\",\n", "    \"SPO\",\n", "    \"IPO\",\n", "    \"Handling_Regular\",\n", "    \"Handling_Fragile\",\n", "    \"Handling_Large\",\n", "    \"Storage_Regular\",\n", "    \"Storage_Heavy\",\n", "    \"weight_in_kgs\",\n", "    \"Handling_Regular_Large\",\n", "    \"Storage_Regular_Heavy\",\n", "    \"BusinessType_B2B_Bulk\",\n", "    \"BusinessType_Cold\",\n", "    \"BusinessType_Dark Store - External\",\n", "    \"BusinessType_Dark Store - Grofers\",\n", "    \"BusinessType_Dropshipment\",\n", "    \"BusinessType_Grocery\",\n", "    \"BusinessType_Large\",\n", "    \"BusinessType_Marketplace\",\n", "    \"month\",\n", "    \"day\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[\"picking_time_ideal\"] = model.predict(history_stats[cat_features])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["history_stats[history_stats.outlet_id == 100]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Solving the System of Equations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_capacities = (\n", "    history_stats.pivot(\"outlet_id\", \"is_heavy\", \"picking_time_ideal\")\n", "    .reset_index()\n", "    .merge(daily_capacity, on=\"outlet_id\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_capacities.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_capacities[\"new_heavy_capacity\"] = (\n", "    new_capacities.heavy\n", "    * 0.13\n", "    * new_capacities.capacity\n", "    / new_capacities.picking_time_ideal\n", ").apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_capacities.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rounding_factors = new_capacities[\n", "    [\"outlet_id\", \"delivery_date\", \"heavy\", \"normal\", \"capacity\", \"picking_time_ideal\"]\n", "].copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rounding_factors[\"heavy_factor\"] = (\n", "    rounding_factors.picking_time_ideal / rounding_factors.heavy\n", ")\n", "rounding_factors[\"normal_factor\"] = (1 - 0.13 * rounding_factors.heavy_factor) / (\n", "    1 - 0.13\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rounding_factors.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oid_bid_map_query = \"\"\"\n", "SELECT DISTINCT\n", "    pco.id AS outlet_id,\n", "    pco.name AS outlet_name,\n", "    bom.external_id AS warehouse_id\n", "        \n", "FROM lake_oms_bifrost.oms_merchant AS bom\n", "INNER JOIN lake_retail.console_outlet_cms_store AS cms\n", "    ON bom.external_id = cms.cms_store \n", "    AND cms.active = 1 \n", "    AND cms.cms_update_active = 1\n", "    AND virtual_merchant_type <> 'superstore_merchant'\n", "INNER JOIN lake_retail.console_outlet AS pco\n", "    ON cms.outlet_id = pco.id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oid_bid_map = pd.read_sql_query(oid_bid_map_query, con=pb.get_connection(\"redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oid_bid_map.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oid_bid_map[oid_bid_map.warehouse_id == 29148]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rounding_factors2 = rounding_factors.merge(oid_bid_map, on=\"outlet_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rounding_factors2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_file_name = \"/tmp/capacity_recaliberation.csv\"\n", "rounding_factors2.to_csv(local_file_name, index=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_s3(\n", "    local_file_name,\n", "    bucket_name,\n", "    \"/eep/warehouse/picking/data/capacity_recaliberation.csv\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}