{"cells": [{"cell_type": "code", "execution_count": null, "id": "4fab4b5a-027f-4055-a194-b2481357df6d", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "import numpy as np\n", "import math\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "sql = \"\"\"select sto.id as sto_id,sto.outlet_id,sto.outlet_name,sto.created_at+ interval '5' hour + interval '30' minute as sto_created_at,\n", "sto.source_entity_vendor_id as source_entity_vendor_id,sto.destination_entity_vendor_id as destination_entity_vendor_id,\n", "\n", "vt1.legal_name as source_vendor, vt2.legal_name as destination_vendor,\n", "case when vt1.legal_name !=vt2.legal_name then 'Wrong Sto Created' else 'Ok' end as flag\n", "from lake_view_po.sto sto\n", "join lake_view_vms.vms_vendor_city_mapping v1 on v1.vendor_id=sto.source_entity_vendor_id\n", "join lake_vms.vms_vendor_city_tax_info vt1 on vt1.vendor_city_id=v1.id\n", "join lake_view_vms.vms_vendor_city_mapping v2 on v2.vendor_id=sto.destination_entity_vendor_id\n", "join lake_vms.vms_vendor_city_tax_info vt2 on vt2.vendor_city_id=v2.id\n", "where sto.created_at>=now() - interval '12' hour\n", "and (vt1.legal_name ='BLINK COMMERCE PRIVATE LIMITED' or  vt2.legal_name='BLINK COMMERCE PRIVATE LIMITED')\"\"\"\n", "\n", "df = pd.read_sql_query(sql=sql, con=presto)\n", "\n", "df = df[df[\"flag\"] == \"Wrong Sto Created\"]\n", "\n", "raw_data_df = df\n", "raw_data_df = raw_data_df.rename(\n", "    columns={\n", "        \"sto_id\": \"STO ID\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"sto_created_at\": \"STO Created At\",\n", "        \"source_vendor\": \"Source Vendor\",\n", "        \"destination_vendor\": \"Destination Vendor\",\n", "        \"flag\": \"Flag\",\n", "    }\n", ")\n", "\n", "pb.to_sheets(\n", "    raw_data_df,\n", "    \"1pp_u0rwoZzhXKbmKJrxBvZsuvOlmJuKnzQcrnk_u4uk\",\n", "    \"Raw-Data\",\n", "    service_account=\"service_account\",\n", ")\n", "\n", "\n", "slack_df = (\n", "    df.groupby([\"outlet_name\", \"source_vendor\", \"destination_vendor\"])\n", "    .agg({\"sto_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "\n", "slack_df = slack_df.rename(\n", "    columns={\n", "        \"sto_id\": \"Number of Wrong STO Created\",\n", "        \"outlet_name\": \"Outlet Name\",\n", "        \"source_vendor\": \"Source Entity\",\n", "        \"destination_vendor\": \"Receiver Entity\",\n", "    }\n", ")\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=20,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\n", "            \"Outlet Name\",\n", "            \"Source Entity\",\n", "            \"Receiver Entity\",\n", "            \"Number of Wrong STO Created\",\n", "        ],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "channel = \"bl-bcpl-alerts\"\n", "\n", "if len(slack_df) > 0:\n", "    fig, ax = render_mpl_table(slack_df, header_columns=0)\n", "    fig.savefig(\"BCPL_STOs_B2B.png\")\n", "\n", "if len(slack_df) > 0:\n", "    now = (datetime.now() + <PERSON><PERSON>ta(hours=5, minutes=30)).strftime(\n", "        \"%d %B, %Y %H:%M:%S\"\n", "    )\n", "    text = f\"\"\":red_circle::red_circle::red_circle: *BCPL Sale Alert B2B for - {now}* \\n This <https://docs.google.com/spreadsheets/d/1pp_u0rwoZzhXKbmKJrxBvZsuvOlmJuKnzQcrnk_u4uk/edit#gid=0|link> has raw data.. <@{'U03S2MANL7P'}><@{'U03RBH0P63Y'}> \"\"\"\n", "    pb.send_slack_message(channel, text, files=[\"BCPL_STOs_B2B.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2ebd4fa1-2ff5-4d48-a89d-368496ccd11c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f11b0e89-aa9c-40cd-8736-97fc0f625800", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}