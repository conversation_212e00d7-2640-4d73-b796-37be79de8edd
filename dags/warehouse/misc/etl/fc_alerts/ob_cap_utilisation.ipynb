{"cells": [{"cell_type": "code", "execution_count": null, "id": "db5c1cae-32de-45f3-8b48-213de8def9e8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import timedelta, date, datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "0308d212-4e71-4afd-9d9d-5e3a66219865", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "2393a716-a10a-454f-a378-89d8c6e79f9e", "metadata": {}, "outputs": [], "source": ["now = datetime.now(ist).hour\n", "mi = datetime.now(ist).minute"]}, {"cell_type": "code", "execution_count": null, "id": "5f919285-35d3-4b5c-9c46-73a3242476a7", "metadata": {}, "outputs": [], "source": ["now, mi"]}, {"cell_type": "code", "execution_count": null, "id": "e35a4be3-58c8-414e-8bc9-5ba512f1ad1b", "metadata": {}, "outputs": [], "source": ["# now = 12\n", "# mi = 40"]}, {"cell_type": "code", "execution_count": null, "id": "1507f6d5-bda9-4ca0-9613-7960<PERSON>ba6a4", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.0,\n", "    row_height=0.825,\n", "    font_size=20,\n", "    header_color=\"#f7b059\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=data.columns,\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "986bd7cc-a76f-4a50-ae4a-b4240fddc624", "metadata": {}, "outputs": [], "source": ["if now in (8, 12, 21, 0):\n", "    if mi >= 30 and mi <= 40:\n", "        if now >= 7 and now < 20:\n", "            day = datetime.now(ist).date()\n", "            shift_num = 1\n", "        elif now >= 20 and now < 24:\n", "            day = datetime.now(ist).date()\n", "            shift_num = 2\n", "        else:\n", "            day = datetime.now(ist).date() - <PERSON><PERSON><PERSON>(days=1)\n", "            shift_num = 2\n", "\n", "        # now, day, shift_num\n", "\n", "        shift = pb.from_sheets(\n", "            \"11jcKXsivy_UlNO2N_nLvGx_RSTY7XU1sOzQ0soFeWSo\", \"shift_details\"\n", "        )\n", "        shift[\"facility_id\"] = shift[\"facility_id\"].astype(int)\n", "        # shift.head()\n", "\n", "        shift[\"shift\"] = shift_num\n", "        if shift_num == 2:\n", "            shift[\"start_date\"] = str(day)\n", "            shift[\"end_date\"] = str(day + timedelta(days=1))\n", "            shift[\"per_load\"] = shift[\"evening_per_days_load\"]\n", "            shift[\"start_datetime\"] = pd.to_datetime(\n", "                shift[\"start_date\"] + shift[\"shift_end_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "            )\n", "            shift[\"end_datetime\"] = pd.to_datetime(\n", "                shift[\"end_date\"] + shift[\"shift_start_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "            )\n", "        else:\n", "            shift[\"start_date\"] = shift[\"end_date\"] = str(day)\n", "            shift[\"per_load\"] = shift[\"morning_per_days_load\"]\n", "            shift[\"start_datetime\"] = pd.to_datetime(\n", "                shift[\"start_date\"] + shift[\"shift_start_time\"],\n", "                format=\"%Y-%m-%d%I:%M %p\",\n", "            )\n", "            shift[\"end_datetime\"] = pd.to_datetime(\n", "                shift[\"end_date\"] + shift[\"shift_end_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "            )\n", "\n", "        shift = shift[\n", "            [\n", "                \"facility_id\",\n", "                \"slack_channel\",\n", "                \"shift\",\n", "                \"per_load\",\n", "                \"start_datetime\",\n", "                \"end_datetime\",\n", "            ]\n", "        ]\n", "\n", "        attr = pd.read_sql_query(\n", "            f\"\"\"\n", "        select backend_facility_id facility_id, max(picking_capacity_quantity) planned_capacity_quantity\n", "        FROM lake_view_ars.backend_facility_transfer_attributes\n", "        WHERE from_date <= cast('{day}' AS DATE)\n", "        AND to_date >= cast('{day}' AS DATE)\n", "        AND lake_active_record = TRUE\n", "        AND id != 671\n", "        group by 1\n", "        \"\"\",\n", "            presto,\n", "        )\n", "        # attr.head()\n", "        shift = shift.merge(attr, on=\"facility_id\", how=\"left\")\n", "        # shift.head()\n", "        a = pd.read_sql_query(\n", "            f\"\"\"\n", "        select f.id facility_id,\n", "               REPLACE(f.name, 'Super Store ', '') facility_name,\n", "               isd.sto_id,\n", "               isd.created_at + interval '5' HOUR + interval '30' MINUTE sto_created_at,\n", "               sum(reserved_quantity) reserved_quantity\n", "        from lake_view_ims.ims_sto_details isd\n", "        join lake_view_po.sto sto on sto.id = isd.sto_id \n", "        join lake_view_vms.vms_vendor_city_mapping v on v.vendor_id = sto.source_entity_vendor_id \n", "        and v.active = 1\n", "        join lake_view_vms.vms_vendor_city_tax_info vt on vt.vendor_city_id = v.id and vt.active = 1\n", "        join lake_view_vms.vms_vendor vms on vms.id = sto.source_entity_vendor_id\n", "        join lake_view_ims.ims_sto_item si on si.sto_id = isd.sto_id\n", "        join lake_view_retail.console_outlet c on c.id = isd.outlet_id \n", "        and c.facility_id in {*shift['facility_id'],}\n", "        join lake_view_retail.console_outlet c1 on c1.id = isd.merchant_outlet_id \n", "        and c1.business_type_id = 7\n", "        join lake_view_crates.facility f on f.id  = c.facility_id\n", "        where isd.created_at >= cast('{day}' as timestamp) - interval '5' hour - interval '30' minute\n", "          and isd.created_at < cast('{day}' as timestamp) + interval '1' day\n", "          and vt.legal_name not like '%%blink%%'\n", "          and vt.legal_name not like '%%Blink%%'\n", "          and vms.vendor_name not like '%%BCPL%%'\n", "        group by 1,2,3,4\n", "        \"\"\",\n", "            presto,\n", "        )\n", "        # a.head()\n", "\n", "        a = a.merge(shift, on=\"facility_id\")\n", "\n", "        a[\"sto_created_at\"] = a[\"sto_created_at\"].apply(pd.to_datetime, errors=\"coerce\")\n", "        a[\"per_load\"] = a[\"per_load\"].astype(float)\n", "        a[\"planned_capacity_quantity\"] = round(\n", "            a[\"planned_capacity_quantity\"] * a[\"per_load\"]\n", "        )\n", "        # a.head()\n", "\n", "        final = a[\n", "            (a[\"sto_created_at\"] >= a[\"start_datetime\"])\n", "            & (a[\"sto_created_at\"] <= a[\"end_datetime\"])\n", "        ]\n", "\n", "        final = (\n", "            final.groupby(\n", "                [\"facility_name\", \"slack_channel\", \"planned_capacity_quantity\"]\n", "            )[\"reserved_quantity\"]\n", "            .sum()\n", "            .reset_index()\n", "        )\n", "        final[\"util_per\"] = round(\n", "            (final[\"reserved_quantity\"] / final[\"planned_capacity_quantity\"]) * 100\n", "        )\n", "        final2 = final[final[\"util_per\"] < 85]\n", "\n", "        final2 = final2.rename(\n", "            columns={\n", "                \"facility_name\": \"Facility Name\",\n", "                \"planned_capacity_quantity\": \"Planned Cap\",\n", "                \"util_per\": \"% Util\",\n", "            }\n", "        )\n", "\n", "        final2 = final2.sort_values(\"% Util\")\n", "\n", "        # final2.head()\n", "\n", "        # final2['slack_channel'] = 'bl-wh-test-ch'\n", "\n", "        for channel in set(final2[\"slack_channel\"]):\n", "            print(channel)\n", "            df = final2[final2[\"slack_channel\"] == channel]\n", "            if channel != \"\" and len(df) > 0:\n", "                text = f\"\"\"*Capacity Utilisation Alert (Planned Vs Actual) for {day} Shift-{shift_num}*\n", "                \\nThe following facilites have capacity utilisation under 85% -```{df.drop(columns=['slack_channel', 'reserved_quantity']).to_markdown(index=False)}```\"\"\"\n", "                pb.send_slack_message(channel, text)\n", "        print(\"Alerted!\")"]}, {"cell_type": "code", "execution_count": null, "id": "97511893-cc28-4e2c-aa75-900fc61efae9", "metadata": {}, "outputs": [], "source": ["# now = datetime.now(ist).hour\n", "# if now >= 7 and now < 20:\n", "#     day = datetime.now(ist).date()\n", "#     shift_num = 1\n", "# elif now >= 20 and now < 0:\n", "#     day = datetime.now(ist).date()\n", "#     shift_num = 2\n", "# else:\n", "#     day = datetime.now(ist).date() - <PERSON><PERSON><PERSON>(days=1)\n", "#     shift_num = 2"]}, {"cell_type": "code", "execution_count": null, "id": "15ccc172-74ca-4707-abb6-fbd590cfc672", "metadata": {}, "outputs": [], "source": ["# now, day, shift_num"]}, {"cell_type": "code", "execution_count": null, "id": "419715d7-e0c7-4e18-8df6-0ce75dd8b200", "metadata": {}, "outputs": [], "source": ["# shift = pb.from_sheets('11jcKXsivy_UlNO2N_nLvGx_RSTY7XU1sOzQ0soFeWSo', 'shift_details')\n", "# shift['facility_id'] = shift['facility_id'].astype(int)\n", "# shift.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0cb4d2b-3dbf-422c-acd3-05cd9531d57d", "metadata": {}, "outputs": [], "source": ["# shift['shift'] = shift_num\n", "# if shift_num == 2:\n", "#     shift['start_date'] = str(day)\n", "#     shift['end_date'] = str(day + timedelta(days=1))\n", "#     shift['per_load'] = shift['evening_per_days_load']\n", "#     shift['start_datetime'] = pd.to_datetime(\n", "#     shift['start_date'] + shift['shift_end_time'], format=\"%Y-%m-%d%I:%M %p\"\n", "#     )\n", "#     shift['end_datetime'] = pd.to_datetime(\n", "#         shift['end_date'] + shift['shift_start_time'], format=\"%Y-%m-%d%I:%M %p\"\n", "#     )\n", "# else:\n", "#     shift['start_date'] = shift['end_date'] = str(day)\n", "#     shift['per_load'] = shift['morning_per_days_load']\n", "#     shift['start_datetime'] = pd.to_datetime(\n", "#     shift['start_date'] + shift['shift_start_time'], format=\"%Y-%m-%d%I:%M %p\"\n", "#     )\n", "#     shift['end_datetime'] = pd.to_datetime(\n", "#         shift['end_date'] + shift['shift_end_time'], format=\"%Y-%m-%d%I:%M %p\"\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "de363a20-3f0d-444c-a9de-97d3070b51cb", "metadata": {}, "outputs": [], "source": ["# shift = shift[\n", "#     [\n", "#         'facility_id',\n", "#         'slack_channel',\n", "#         'shift',\n", "#         'per_load',\n", "#         'start_datetime',\n", "#         'end_datetime',\n", "#     ]\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "id": "f146c771-5d12-4df8-915e-466c9a53b0f1", "metadata": {}, "outputs": [], "source": ["# attr = pd.read_sql_query(f\"\"\"\n", "# select backend_facility_id facility_id, max(picking_capacity_quantity) planned_capacity_quantity\n", "# FROM lake_view_ars.backend_facility_transfer_attributes\n", "# WHERE from_date <= cast('{day}' AS DATE)\n", "# AND to_date >= cast('{day}' AS DATE)\n", "# AND lake_active_record = TRUE\n", "# AND id != 671\n", "# group by 1\n", "# \"\"\", presto)\n", "# attr.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ccc43f5a-b389-4e64-ab5b-60f65c3a4c5d", "metadata": {}, "outputs": [], "source": ["# shift = shift.merge(attr, on='facility_id', how='left')"]}, {"cell_type": "code", "execution_count": null, "id": "fcca7fda-482c-4baa-a088-503036b0053e", "metadata": {}, "outputs": [], "source": ["# shift.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa749206-cd7c-4373-87bd-88b25fc40a30", "metadata": {}, "outputs": [], "source": ["# a = pd.read_sql_query(f\"\"\"\n", "# select f.id facility_id,\n", "#        REPLACE(f.name, 'Super Store ', '') facility_name,\n", "#        isd.sto_id,\n", "#        isd.created_at + interval '5' HOUR + interval '30' MINUTE sto_created_at,\n", "#        sum(reserved_quantity) reserved_quantity\n", "# from lake_view_ims.ims_sto_details isd\n", "# join lake_view_po.sto sto on sto.id = isd.sto_id\n", "# join lake_view_vms.vms_vendor_city_mapping v on v.vendor_id = sto.source_entity_vendor_id and v.active = 1\n", "# join lake_view_vms.vms_vendor_city_tax_info vt on vt.vendor_city_id = v.id and vt.active = 1\n", "# join lake_view_vms.vms_vendor vms on vms.id = sto.source_entity_vendor_id\n", "# join lake_view_ims.ims_sto_item si on si.sto_id = isd.sto_id\n", "# join lake_view_retail.console_outlet c on c.id = isd.outlet_id\n", "# and c.facility_id in {*shift['facility_id'],}\n", "# join lake_view_retail.console_outlet c1 on c1.id = isd.merchant_outlet_id\n", "# and c1.business_type_id = 7\n", "# join lake_view_crates.facility f on f.id  = c.facility_id\n", "# where isd.created_at >= cast('{day}' as timestamp) - interval '5' hour - interval '30' minute\n", "#   and isd.created_at < cast('{day}' as timestamp) + interval '1' day\n", "#   and vt.legal_name not like '%%blink%%'\n", "#   and vt.legal_name not like '%%Blink%%'\n", "#   and vms.vendor_name not like '%%BCPL%%'\n", "# group by 1,2,3,4\n", "# \"\"\", presto)\n", "# a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9d02553c-2809-4633-b025-3f4588fdd732", "metadata": {}, "outputs": [], "source": ["# a = a.merge(shift, on = 'facility_id')"]}, {"cell_type": "code", "execution_count": null, "id": "7ca36e5b-d102-480a-8b2f-ff77433d869d", "metadata": {}, "outputs": [], "source": ["# a['sto_created_at'] = a['sto_created_at'].apply(pd.to_datetime, errors='coerce')\n", "# a['per_load'] = a['per_load'].astype(float)\n", "# a['planned_capacity_quantity'] = round(a['planned_capacity_quantity'] * a['per_load'])\n", "# a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9aa06356-990c-4576-8327-70f160f66e1b", "metadata": {}, "outputs": [], "source": ["# final = a[(a['sto_created_at'] >= a['start_datetime'])\n", "#           & (a['sto_created_at'] <= a['end_datetime'])]"]}, {"cell_type": "code", "execution_count": null, "id": "713b25bd-15d3-475c-831d-51ce6103c214", "metadata": {}, "outputs": [], "source": ["# final = final.groupby(['facility_name',\n", "#                        'slack_channel',\n", "#                        'planned_capacity_quantity'])['reserved_quantity'].sum().reset_index()\n", "# final['util_per'] = round((final['reserved_quantity']/final['planned_capacity_quantity'])*100)\n", "# final2 = final[final['util_per'] < 85]"]}, {"cell_type": "code", "execution_count": null, "id": "61df00e8-4b21-49c2-aa4a-39c278d85be1", "metadata": {}, "outputs": [], "source": ["# final2 = final2.rename(columns={'facility_name' : 'Facility Name',\n", "#                        'planned_capacity_quantity' : 'Planned Cap',\n", "#                        'util_per' : '% Util'})"]}, {"cell_type": "code", "execution_count": null, "id": "8e343a71-b6fc-4a83-b477-c3bcf9cd6d29", "metadata": {}, "outputs": [], "source": ["# final2 = final2.sort_values('% Util')"]}, {"cell_type": "code", "execution_count": null, "id": "3fc99857-fd36-4674-a68b-e3386bf947a4", "metadata": {}, "outputs": [], "source": ["# final2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e656bc3f-d4f9-4b01-8ebb-581e74c4b123", "metadata": {}, "outputs": [], "source": ["# final2['slack_channel'] = 'bl-wh-test-ch'"]}, {"cell_type": "code", "execution_count": null, "id": "6764402d-c349-4c3e-b621-61994814e0ee", "metadata": {}, "outputs": [], "source": ["# for channel in set(final2['slack_channel']):\n", "#     print(channel)\n", "#     df = final2[final2['slack_channel'] == channel]\n", "#     if(channel != '' and len(df) > 0):\n", "#         text = f\"\"\"*Capacity Utilisation Alert (Planned Vs Actual) for*\\n*{day} Shift-{shift_num}*\n", "#         \\nThe following facilites have capacity utilisation under 85% -\n", "#         ```{df.drop(columns=['slack_channel', 'reserved_quantity']).to_markdown(index=False)}```\"\"\"\n", "#         pb.send_slack_message(channel, text)"]}, {"cell_type": "code", "execution_count": null, "id": "2f36ebea-e40b-40e1-8a2e-ad9a831aaa3b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}