{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = 1\n", "today = date.today()\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "START_DATE = (datetime.now(tz) - timedelta(days=i)).strftime(\"%Y-%m-%d\")\n", "END_DATE = (datetime.now(tz) - timedelta(days=i)).strftime(\"%Y-%m-%d\")\n", "\n", "START_DATE1 = (datetime.now(tz) - timedelta(days=i + 2)).strftime(\"%Y-%m-%d\")\n", "END_DATE1 = (datetime.now(tz) - timed<PERSON>ta(days=i - 1)).strftime(\"%Y-%m-%d\")\n", "\n", "print(START_DATE, END_DATE, START_DATE1, END_DATE1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"slot_start\": \"slot start time\",\n", "    \"slot_end\": \"slot end time\",\n", "    \"order_item_id\": \"OMS order item id\",\n", "    \"ordered_quantity\": \"ordered quantity of the item\",\n", "    \"procured_quantity\": \"procured quantity of the item\",\n", "    \"reason_key\": \"reason for unfulfillment\",\n", "    \"unfulfilled_order\": \"unfulfilled order flag\",\n", "    \"multiplier\": \"item multiplier\",\n", "    \"ancestor\": \"ancestor order id\",\n", "    \"description\": \"order log description\",\n", "    \"fnv_upper_limit\": \"fnv upper limit for the item\",\n", "    \"upper_limit\": \"upper limit for the item\",\n", "    \"outlet_type\": \"from product_product, to determine fnv or grocery type\",\n", "    \"id\": \"picklist id\",\n", "    \"picking_time\": \"picking timestamp of order\",\n", "    \"picking_hour\": \"picking hour of timestamp\",\n", "    \"inward_timestamp\": \"last inward timestamp of the item\",\n", "    \"inward_hour\": \"inward hour of timestamp\",\n", "    \"grn_inward_quan\": \"grn inwarded quantity of item\",\n", "    \"badstock_timestamp\": \"last bad stock marked timestamp\",\n", "    \"badstock_qty\": \"bad stock marked quantity\",\n", "    \"hour\": \"inventory snapshot close of picking time\",\n", "    \"actual_quantity\": \"inventory snapshot qty at hour\",\n", "    \"return_timestamp\": \"return timestamp before picking\",\n", "    \"return_quan\": \"return quantity of item\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "\n", "# PNA Marked Items at order level\n", "\n", "query1 = \"\"\"\n", "with base_table as\n", "(\n", "select distinct\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_start * interval '1 second')) as slot_start,\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_end * interval '1 second')) as slot_end,\n", "    oo.id as order_id,\n", "    case when soi.suborder_id is null then so2.id else soi.suborder_id end as suborder_id,\n", "    ooi.product_id as product_id,\n", "    ooi.id as order_item_id,\n", "    ooi.quantity as ordered_quantity,\n", "    ooi.procured_quantity as procured_quantity  \n", "from \n", "(\n", "select distinct order_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder' and o.business_type_id = 7\n", "  and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", ") as os\n", "inner join lake_oms_bifrost.oms_order as oo on os.order_id = oo.id\n", "and oo.type in ('RetailForwardOrder','InternalForwardOrder')\n", "and oo.current_status = 'DELIVERED'\n", "inner join lake_oms_bifrost.oms_order_item as ooi on oo.id = ooi.order_id\n", "inner join lake_oms_bifrost.oms_merchant as om on oo.merchant_id = om.id \n", "--and  om.virtual_merchant_type = 'superstore_merchant'\n", "left join lake_oms_bifrost.oms_suborder_item soi on soi.order_item_id = ooi.id\n", "left join lake_oms_bifrost.oms_suborder so on so.id=soi.suborder_id\n", "left join (select order_id, max(id) as id from lake_oms_bifrost.oms_suborder \n", "where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "  and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", "group by 1 ) so2 on oo.id=so2.order_id\n", "),\n", "ReasonKey as (Select distinct bt.*,\n", "                 oic.reason_key\n", "from base_table bt\n", "inner join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.order_item_id = oic.order_item_id\n", "),\n", "Final as (\n", "Select ReasonKey.*,o.id as Outlet_id,o.name as Outlet_name,o.facility_id as Facility_id,f.name as facilityname, om.city_name as city\n", "from ReasonKey \n", "left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "left join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", ")\n", "Select * from Final \n", "\"\"\"\n", "df = pd.read_sql_query(\n", "    sql=query1, con=redshift, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", ")\n", "\n", "df.head()\n", "\n", "item_category_query = \"\"\"\n", "SELECT distinct\n", "pp.item_id,\n", "pp.variant_id,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID\n", "/*\n", "ipm.product_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID,\n", "CAT2.NAME AS l2,\n", "CAT2.ID AS l2_ID,\n", "pb.name as brand,\n", "pb.id as brand_id\n", "P.ENABLED_FLAG*/\n", "FROM\n", "lake_cms.GR_PRODUCT P\n", "INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1 \n", "\"\"\"\n", "item_category_df = pd.read_sql_query(sql=item_category_query, con=redshift)\n", "item_category_df.sample(2)\n", "\n", "df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()\n", "\n", "round(\n", "    (\n", "        df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()\n", "        / df.order_id.nunique()\n", "    )\n", "    * 100,\n", "    2,\n", ")\n", "\n", "X = df[\"procured_quantity\"] < df[\"ordered_quantity\"]\n", "df[\"unfulfilled_order\"] = X.apply(lambda x: int(x))\n", "\n", "Order_df = df.groupby([\"city\", \"order_id\"], as_index=False).agg(\n", "    {\"unfulfilled_order\": max}\n", ")\n", "\n", "Order_df = Order_df.groupby(\"city\", as_index=False).agg(\n", "    {\"order_id\": \"nunique\", \"unfulfilled_order\": sum}\n", ")\n", "Order_df[\"Fill Rate\"] = round(\n", "    (Order_df[\"unfulfilled_order\"] / Order_df[\"order_id\"]) * 100, 2\n", ")\n", "Order_df\n", "\n", "it_pd_log_query = \"\"\"select  \n", "x.product_id::int, item_id::int, multiplier  \n", "from \n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.it_pd_log i \n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\n", "\"\"\"\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift)\n", "\n", "it_pd_log_df.head()\n", "\n", "Final = pd.merge(\n", "    left=df,\n", "    right=it_pd_log_df,\n", "    how=\"inner\",\n", "    left_on=[\"product_id\"],\n", "    right_on=[\"product_id\"],\n", ")\n", "\n", "Final.shape\n", "\n", "# Nyala lag bucket\n", "\n", "query_order_log = \"\"\"SELECT T2.* , o.name as outlet_name, l.id as city_id, l.name as city_name FROM\n", "(\n", "SELECT T1.*, ul.fnv_upper_limit, ul.upper_limit , ul.feeder_live\n", "\n", "FROM\n", "(\n", "SELECT T.*, il.item_id , il.actual_quantity , il.blocked_quantity  FROM\n", "\n", "(\n", "SELECT od.outlet as outlet_id , od.order_id , od.ancestor, \n", "date(od.scheduled_at+interval '5.5 hours') as scheduled_at, \n", "od.status_id as order_status_id, \n", "ol.id,\n", "date(ol.created_at+interval '5.5 hours') as created_at,\n", "\n", "ol.description, \n", "ol.status_id as log_status_id, \n", "date(ol.old_scheduled_at+interval '5.5 hours') as old_scheduled_at\n", "\n", "FROM lake_ims.ims_order_details od\n", "inner join lake_ims.ims_order_log ol on od.order_id = ol.order_id\n", "\n", "where date(od.scheduled_at)  between %(start_date)s and %(end_date)s and ol.description in ('Reprocure','Created') \n", "\n", ")T\n", "\n", "inner join lake_ims.ims_item_inventory_log il on T.id = il.order_log_id\n", "where il.actual_quantity < il.blocked_quantity\n", ") T1\n", "\n", "inner join lake_ims.ims_item_inventory ul on T1.item_id = ul.item_id and T1.outlet_id = ul.outlet_id \n", ") T2 \n", "\n", "inner join lake_retail.console_outlet o on T2.outlet_id = o.id \n", "inner join lake_retail.console_location l on o.tax_location_id = l.id\n", "\"\"\"\n", "Order_log = pd.read_sql(\n", "    sql=query_order_log,\n", "    con=redshift,\n", "    params={\"start_date\": START_DATE1, \"end_date\": END_DATE1},\n", ")\n", "\n", "Order_log[\"key\"] = Order_log.ancestor.astype(str) + \"|\" + Order_log.item_id.astype(str)\n", "\n", "len(Order_log[\"key\"].unique())\n", "\n", "Order_log_Fil = Order_log.groupby([\"ancestor\", \"item_id\"], as_index=False).agg(\n", "    {\"description\": max, \"fnv_upper_limit\": max, \"upper_limit\": max}\n", ")\n", "\n", "Order_log_Fil.ancestor = Order_log_Fil.ancestor.astype(int)\n", "\n", "Order_log_Fil.head(2)\n", "\n", "Final1 = pd.merge(\n", "    Final,\n", "    Order_log_Fil,\n", "    how=\"left\",\n", "    left_on=[\"order_id\", \"item_id\"],\n", "    right_on=[\"ancestor\", \"item_id\"],\n", ")\n", "Final1.shape\n", "\n", "Final1[Final1[\"unfulfilled_order\"] == 1].shape\n", "\n", "Final1[(Final1[\"description\"] == \"Created\") & (Final[\"unfulfilled_order\"] == 1)].shape\n", "\n", "# FnV Data\n", "\n", "QueryFnv = \"\"\"Select distinct item_id,max(outlet_type) as outlet_type from lake_rpc.product_product where outlet_type=1 group by 1\n", "\"\"\"\n", "FnV_Data = pd.read_sql_query(sql=QueryFnv, con=redshift)\n", "Final2 = Final1.merge(FnV_Data, how=\"left\", on=\"item_id\")\n", "Final2.shape\n", "\n", "Final2.head(2)\n", "\n", "query = \"\"\"\n", "With Base as (\n", "Select id from lake_oms_bifrost.oms_suborder \n", "where slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour'))\n", ")\n", "\n", "SELECT Base.id,max(plog.pos_timestamp + interval '5.5 hour') as Picking_time, extract(hour from max(plog.pos_timestamp + interval '5.5 hour')) as picking_hour\n", "From Base\n", "JOIN lake_warehouse_location.warehouse_pick_list_order_mapping plo on Base.id = plo.order_id\n", "JOIN lake_warehouse_location.pick_list_log plog on plog.pick_list_id=plo.pick_list_id\n", "where plog.picklist_state = 3\n", "group by 1\n", "\"\"\"\n", "Picking_Time = pd.read_sql_query(\n", "    sql=query, con=redshift, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", ")\n", "\n", "Final3 = Final2.merge(Picking_Time, how=\"left\", left_on=\"suborder_id\", right_on=\"id\")\n", "\n", "Final3.head(2)\n", "\n", "Final3[Final3.id.isnull()].shape\n", "\n", "# GRN Logs\n", "\n", "# # latest grn time and quantity grned at outlet & item level\n", "# grn_query = \"\"\"\n", "# select po.*\n", "# from lake_po.po_grn po\n", "# inner join lake_retail.console_outlet o on o.id = po.outlet_id\n", "# where date(po.created_at + interval '5.5 hour') = %(start_date)s and o.business_type_id = 7\"\"\"\n", "# grn_logs = pd.read_sql_query(sql=grn_query, con=redshift, params = {\"start_date\": START_DATE})\n", "# grn_logs = grn_logs[[\"item_id\",\"quantity\",\"created_at\",\"outlet_id\"]]\n", "\n", "# grn_logs.shape\n", "\n", "sto_query = \"\"\"\n", "SELECT \n", "ii.outlet_id,pp.item_id,(ii.created_at + interval '5.5 hour') as inward_timestamp,extract(hour from (ii.created_at + interval '5.5 hour')) as inward_hour,sum(isd.\"delta\") as grn_inward_quan\n", "from   lake_ims.ims_inward_invoice ii\n", "left join lake_ims.ims_inventory_stock_details isd on isd.grn_id = ii.grn_id\n", "left join lake_retail.console_outlet co on co.id = ii.outlet_id\n", "left join lake_rpc.product_product pp on pp.variant_id = isd.variant_id\n", "WHERE co.business_type_id = 7\n", "and date(ii.created_at + interval '5.5 hour') = %(start_date)s\n", "group by 1,2,3\n", "\"\"\"\n", "sto_data = pd.read_sql_query(\n", "    sql=sto_query, con=redshift, params={\"start_date\": START_DATE}\n", ")\n", "\n", "# Final3[(Final3.picking_hour.isnull()) & (Final3[\"reason_key\"]=='ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING')].shape\n", "\n", "Final4 = pd.merge(\n", "    Final3,\n", "    sto_data,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", ")\n", "\n", "Final4_1 = Final4[Final4[\"inward_hour\"].isnull()]\n", "Final4_2 = Final4[~Final4[\"inward_hour\"].isnull()]\n", "\n", "# Final4[~(Final4[\"picking_hour\"].isnull() | Final4[\"inward_hour\"].isnull())].shape\n", "\n", "Final4_2.columns\n", "\n", "Final4_temp = Final4_2.groupby(\n", "    [\"order_id\", \"suborder_id\", \"product_id\", \"order_item_id\", \"item_id\"],\n", "    as_index=False,\n", ").agg({\"inward_hour\": max})\n", "\n", "Final4_2 = Final4_2.merge(\n", "    Final4_temp,\n", "    how=\"inner\",\n", "    on=[\n", "        \"order_id\",\n", "        \"suborder_id\",\n", "        \"product_id\",\n", "        \"order_item_id\",\n", "        \"item_id\",\n", "        \"inward_hour\",\n", "    ],\n", ")\n", "\n", "Final4 = pd.concat([Final4_2, Final4_1], axis=0)\n", "\n", "# Final4[(Final4[\"suborder_id\"]==116053132) & (Final4[\"item_id\"]==10000778)]\n", "\n", "# Final4.groupby(['order_id', 'suborder_id', 'product_id',\n", "#        'order_item_id','item_id'],as_index = False).count()\n", "\n", "# Final4[(Final4['inward_hour']<=Final4['picking_hour']) | (Final4['picking_hour'].isnull()) | (Final4['inward_hour'].isnull())].shape\n", "\n", "Final4.shape\n", "\n", "# Final4.groupby(['slot_start', 'slot_end', 'order_id', 'suborder_id', 'product_id',\n", "#        'order_item_id', 'ordered_quantity', 'procured_quantity', 'reason_key',\n", "#        'outlet_id', 'outlet_name', 'facility_id', 'facilityname', 'city',\n", "#        'unfulfilled_order', 'item_id', 'multiplier', 'ancestor', 'description',\n", "#        'fnv_upper_limit', 'upper_limit', 'outlet_type', 'id', 'picking_time',\n", "#        'picking_hour']).agg('count')\n", "\n", "# badstock\n", "\n", "badstock_query = \"\"\"\n", "with temp as (select\n", "        ul.outlet_id,\n", "        rpc.item_id,\n", "        (ul.created_at + interval '5.5 hour') as badStock_timestamp,\n", "        sum(\"delta\") as badstock_qty\n", "from  lake_ims.ims_bad_inventory_update_log ul \n", "left join lake_rpc.product_product rpc on ul.variant_id=rpc.variant_id\n", "left join lake_retail.console_outlet co on co.id = ul.outlet_id\n", "where date(ul.created_at + interval '5.5 hour') = %(start_date)s and co.business_type_id = 7\n", "group by 1,2,3),\n", "temp_1 as (select outlet_id,item_id,max(badStock_timestamp) as badStock_timestamp\n", "from temp\n", "group by 1,2)\n", "select temp.*\n", "from temp\n", "inner join temp_1 on temp.outlet_id = temp_1.outlet_id and temp.item_id = temp_1.item_id and temp.badStock_timestamp = temp_1.badStock_timestamp\n", "\"\"\"\n", "badstock_data = pd.read_sql_query(\n", "    sql=badstock_query, con=redshift, params={\"start_date\": START_DATE}\n", ")\n", "\n", "badstock_data.head()\n", "\n", "Final5 = pd.merge(\n", "    Final4,\n", "    badstock_data,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", ")\n", "\n", "Final5.shape\n", "\n", "facility_list = list(\n", "    Final5[Final5.reason_key.notnull()].facility_id.astype(str).unique()\n", ")\n", "item_id_list = list(Final5[Final5.reason_key.notnull()].item_id.astype(str).unique())\n", "\n", "# Inventory snapshot\n", "\n", "# inventory_snapshot_query = \"\"\"\n", "# select outlet_id,item_id,actual_quantity,snapshot_datetime + interval '5.5 hour' from\n", "# lake_reports.reports_item_inventory_snapshot s\n", "# left join lake_retail.console_outlet co on co.id = s.outlet_id\n", "# where date(s.snapshot_datetime) = %(start_date)s and co.business_type_id = 7\n", "# order by 1,2\n", "# \"\"\"\n", "# inventory_data = pd.read_sql_query(sql = inventory_snapshot_query, con = redshift, params={\"start_date\": START_DATE})\n", "\n", "# inventory_snapshot_query = \"\"\"\n", "# select co.id as outlet_id,rda.item_id,extract(hour from order_date) as hour,actual_quantity\n", "# from consumer.rpc_daily_availability  rda\n", "# left join lake_retail.console_outlet co on co.facility_id = rda.facility_id\n", "# where date(order_date) = %(start_date)s and co.business_type_id = 7 and availability = 'live' and active_flag = 'active'\n", "# \"\"\"\n", "# inventory_data = pd.read_sql_query(\n", "#     sql=inventory_snapshot_query, con=redshift, params={\"start_date\": START_DATE}\n", "# )\n", "\n", "inventory_snapshot_query = \"\"\"\n", "select co.id as outlet_id,rda.item_id,extract(hour from order_date) as hour,actual_quantity \n", "from consumer.rpc_daily_availability rda\n", "inner join lake_retail.console_outlet co on co.facility_id = rda.facility_id\n", "where date(order_date) = %(start_date)s and co.business_type_id = 7 and availability = 'live' and active_flag = 'active'\n", "and rda.facility_id in %(facility_list)s\n", "and rda.item_id in %(item_list)s\n", "group by 1,2,3,4\n", "\"\"\"\n", "inventory_data = pd.read_sql(\n", "    sql=inventory_snapshot_query,\n", "    con=redshift,\n", "    params={\n", "        \"start_date\": START_DATE,\n", "        \"facility_list\": tuple(facility_list),\n", "        \"item_list\": tuple(item_id_list),\n", "    },\n", ")\n", "\n", "inventory_data = inventory_data.drop_duplicates()\n", "\n", "inventory_data[\"item_id\"] = inventory_data[\"item_id\"].astype(int)\n", "\n", "inventory_data.shape\n", "\n", "Final6 = pd.merge(\n", "    Final5,\n", "    inventory_data,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", ")\n", "\n", "Final6.columns\n", "\n", "Final6.shape\n", "\n", "Final6_1 = Final6[Final6[\"hour\"].isnull()]\n", "Final6_2 = Final6[Final6[\"picking_hour\"].isnull()]\n", "Final6_3 = Final6[\n", "    (\n", "        (Final6[\"hour\"].notnull() | Final6[\"picking_hour\"].notnull())\n", "        & (Final6[\"hour\"] <= Final6[\"picking_hour\"])\n", "    )\n", "]\n", "\n", "print(Final6_1.shape[0], Final6_2.shape[0], Final6_3.shape[0])\n", "\n", "Final6_temp = Final6_2.groupby(\n", "    [\"order_id\", \"suborder_id\", \"product_id\", \"order_item_id\", \"item_id\"],\n", "    as_index=False,\n", ").agg({\"hour\": max})\n", "Final6_2 = Final6_2.merge(\n", "    Final6_temp,\n", "    how=\"inner\",\n", "    on=[\"order_id\", \"suborder_id\", \"product_id\", \"order_item_id\", \"item_id\", \"hour\"],\n", ")\n", "\n", "Final6_temp_1 = Final6_3.groupby(\n", "    [\"order_id\", \"suborder_id\", \"product_id\", \"order_item_id\", \"item_id\"],\n", "    as_index=False,\n", ").agg({\"hour\": max})\n", "Final6_3.hour = Final6_3.hour.astype(float)\n", "Final6_3 = Final6_3.merge(\n", "    Final6_temp_1,\n", "    how=\"inner\",\n", "    on=[\"order_id\", \"suborder_id\", \"product_id\", \"order_item_id\", \"item_id\", \"hour\"],\n", ")\n", "\n", "Final6 = pd.concat([Final6_1, Final6_2, Final6_3], axis=0, sort=False)\n", "\n", "Final6.shape\n", "\n", "# Returns\n", "\n", "# returns with timestamp\n", "returns_query = \"\"\"\n", "with temp as (select pi.outlet_id,rpc.item_id,pi.pos_timestamp + interval '5.5 hour' as return_timestamp,sum(pid.quantity) as return_quan\n", "from lake_pos.pos_invoice pi\n", "left join lake_retail.console_outlet co on co.id = pi.outlet_id\n", "left join lake_pos.pos_invoice_product_details pid on pid.invoice_id = pi.id\n", "left join lake_rpc.product_product rpc on pid.variant_id = rpc.variant_id\n", "where pi.invoice_type_id = 2 and co.business_type_id = 7 and date(pi.pos_timestamp + interval '5.5 hour') = %(start_date)s\n", "group by 1,2,3),\n", "temp_1 as (\n", "select outlet_id,item_id,max(return_timestamp) as return_timestamp\n", "from temp\n", "group by 1,2\n", ")\n", "select temp.*\n", "from temp\n", "inner join temp_1 on temp.outlet_id = temp_1.outlet_id and temp.item_id = temp_1.item_id and temp.return_timestamp = temp_1.return_timestamp\n", "\"\"\"\n", "returns_data = pd.read_sql_query(\n", "    sql=returns_query, con=redshift, params={\"start_date\": START_DATE}\n", ")\n", "\n", "returns_data.head()\n", "\n", "Final7 = pd.merge(\n", "    Final6,\n", "    returns_data,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", ")\n", "\n", "Final7.shape\n", "\n", "# pushing data to table\n", "\n", "\n", "def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "\n", "Final7[\"key\"] = (\n", "    Final7[\"order_item_id\"].astype(str) + \"|\" + Final7[\"item_id\"].astype(str)\n", ")\n", "\n", "Final7.key.nunique()\n", "\n", "Final7.head(2)\n", "\n", "Final7.reason_key.value_counts()\n", "\n", "# Final7[Final7[\"unfulfilled_order\"]==1].groupby([\"outlet_id\"]).order_id.nunique()\n", "\n", "Final7.groupby([\"outlet_id\"]).order_id.nunique()\n", "\n", "Final7[\"procured_quantity\"] = Final7[\"procured_quantity\"].fillna(0)\n", "Final7[\"procured_quantity\"] = Final7[\"procured_quantity\"].astype(int)\n", "\n", "Final7.dtypes\n", "\n", "Final7[\"outlet_id\"] = Final7[\"outlet_id\"].astype(int)\n", "Final7[\"facility_id\"] = Final7[\"facility_id\"].astype(int)\n", "Final7[\"product_id\"] = Final7[\"product_id\"].astype(int)\n", "\n", "Final7[abs(Final7[\"hour\"] - Final7[\"picking_hour\"]) < 2].shape\n", "\n", "column_dtypes = redshift_schema(Final7)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"FillRateAttribution_darkstores\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"slot_end\",\n", "    \"table_description\": \"to store fillrateattribution data for darkstores\",\n", "    \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "}\n", "pb.to_redshift(Final7, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}