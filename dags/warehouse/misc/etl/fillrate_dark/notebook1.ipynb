{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# To Do\n", "### 1. item product mapping exact"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for day in range(1, 2):\n", "    today = date.today()\n", "    tz = pytz.timezone(\"Asia/Kolkata\")\n", "    START_DATE = (datetime.now(tz) - timedelta(days=day)).strftime(\"%Y-%m-%d\")\n", "    END_DATE = (datetime.now(tz) - timedelta(days=day)).strftime(\"%Y-%m-%d\")\n", "\n", "    START_DATE1 = (datetime.now(tz) - timedelta(days=day + 2)).strftime(\"%Y-%m-%d\")\n", "    END_DATE1 = (datetime.now(tz) - timedelta(days=day - 1)).strftime(\"%Y-%m-%d\")\n", "    print(START_DATE, END_DATE, START_DATE1, END_DATE1)\n", "    redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    # PNA Marked Items at order level\n", "    query1 = \"\"\"\n", "    with base_table as \n", "    (\n", "    select distinct\n", "        convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_start * interval '1 second')) as slot_start,\n", "        convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_end * interval '1 second')) as slot_end,\n", "        oo.id as order_id,\n", "        so2.id as suborder_id,\n", "        ooi.product_id as product_id,\n", "        ooi.id as order_item_id,\n", "        ooi.quantity as ordered_quantity,\n", "        ooi.procured_quantity as procured_quantity  \n", "    from \n", "    (\n", "    select distinct order_id\n", "    from lake_oms_bifrost.oms_suborder os\n", "    left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "    left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "    left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "    where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "            and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "        and type = 'RetailSuborder' and o.business_type_id = 7\n", "      and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", "    ) as os\n", "    inner join lake_oms_bifrost.oms_order as oo on os.order_id = oo.id\n", "    and oo.type in ('RetailForwardOrder','InternalForwardOrder')\n", "    and oo.current_status = 'DELIVERED'\n", "    inner join lake_oms_bifrost.oms_order_item as ooi on oo.id = ooi.order_id\n", "    inner join lake_oms_bifrost.oms_merchant as om on oo.merchant_id = om.id \n", "    -- and  om.virtual_merchant_type = 'superstore_merchant'\n", "    left join lake_oms_bifrost.oms_suborder_item soi on soi.order_item_id = ooi.id\n", "    left join lake_oms_bifrost.oms_suborder so on so.id=soi.suborder_id\n", "    left join (select order_id, max(id) as id from lake_oms_bifrost.oms_suborder \n", "    where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "            and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "        and type = 'RetailSuborder'\n", "      and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", "    group by 1 ) so2 on oo.id=so2.order_id\n", "    ),\n", "    ReasonKey as (Select distinct bt.*,\n", "                     oic.reason_key\n", "    from base_table bt\n", "    left join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.order_item_id = oic.order_item_id\n", "    ),\n", "    Final as (\n", "    Select ReasonKey.*,o.id as Outlet_id,o.name as Outlet_name,o.facility_id as Facility_id,f.name as facilityname, om.city_name as city\n", "    from ReasonKey \n", "    left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "    left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "    left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "    left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "    left join lake_retail.console_location l on o.tax_location_id = l.id\n", "    left join lake_crates.facility f on o.facility_id = f.id\n", "    left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "    left join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", "    )\n", "    Select * from Final \n", "    \"\"\"\n", "    df = pd.read_sql_query(\n", "        sql=query1,\n", "        con=redshift,\n", "        params={\"start_date\": START_DATE, \"end_date\": END_DATE},\n", "    )\n", "    df.head()\n", "\n", "    item_category_query = \"\"\"\n", "    SELECT distinct\n", "    pp.item_id,\n", "    pp.variant_id,\n", "    CAT.NAME AS l0,\n", "    CAT.ID AS l0_ID,\n", "    CAT1.NAME AS l1,\n", "    CAT1.ID AS l1_ID\n", "    /*\n", "    ipm.product_id,\n", "    p.type as product_type,\n", "    p.type_id as p_type,\n", "    CAT.NAME AS l0,\n", "    CAT.ID AS l0_ID,\n", "    CAT1.NAME AS l1,\n", "    CAT1.ID AS l1_ID,\n", "    CAT2.NAME AS l2,\n", "    CAT2.ID AS l2_ID,\n", "    pb.name as brand,\n", "    pb.id as brand_id\n", "    P.ENABLED_FLAG*/\n", "    FROM\n", "    lake_cms.GR_PRODUCT P\n", "    INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "    INNER JOIN lake_cms.GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "    inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "    left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "    left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "    where pp.active =1 and pp.approved=1 \n", "    \"\"\"\n", "    item_category_df = pd.read_sql_query(sql=item_category_query, con=redshift)\n", "    item_category_df.sample(2)\n", "\n", "    df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()\n", "    round(\n", "        (\n", "            df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()\n", "            / df.order_id.nunique()\n", "        )\n", "        * 100,\n", "        2,\n", "    )\n", "\n", "    X = df[\"procured_quantity\"] < df[\"ordered_quantity\"]\n", "    df[\"unfulfilled_order\"] = X.apply(lambda x: int(x))\n", "\n", "    Order_df = df.groupby([\"city\", \"order_id\"], as_index=False).agg(\n", "        {\"unfulfilled_order\": max}\n", "    )\n", "    Order_df = Order_df.groupby(\"city\", as_index=False).agg(\n", "        {\"order_id\": \"nunique\", \"unfulfilled_order\": sum}\n", "    )\n", "    Order_df[\"Fill Rate\"] = round(\n", "        (Order_df[\"unfulfilled_order\"] / Order_df[\"order_id\"]) * 100, 2\n", "    )\n", "    Order_df\n", "\n", "    it_pd_log_query = \"\"\"select  \n", "    x.product_id::int, item_id::int, multiplier  \n", "    from \n", "    consumer.it_pd_log it join\n", "    (\n", "    select  i.product_id, max(updated_on) as max_updated_at from \n", "    consumer.it_pd_log i \n", "    group by 1\n", "    )\n", "    x\n", "    on x.product_id=it.product_id\n", "    and updated_on=x.max_updated_at\n", "    \"\"\"\n", "    it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift)\n", "\n", "    it_pd_log_df.head()\n", "\n", "    Final = pd.merge(\n", "        left=df,\n", "        right=it_pd_log_df,\n", "        how=\"inner\",\n", "        left_on=[\"product_id\"],\n", "        right_on=[\"product_id\"],\n", "    )\n", "\n", "    Final.shape\n", "\n", "    QueryFnv = \"\"\"Select distinct item_id,max(outlet_type) as outlet_type from lake_rpc.product_product group by 1\n", "    \"\"\"\n", "    FnV_Data = pd.read_sql_query(sql=QueryFnv, con=redshift)\n", "\n", "    Final = Final.merge(FnV_Data, on=[\"item_id\"], how=\"left\")\n", "\n", "    fulfilled = Final[Final.reason_key.isnull()]\n", "\n", "    Final = Final[Final.reason_key.notnull()]\n", "\n", "    Final.shape, fulfilled.shape\n", "\n", "    # Picking time\n", "    # START_DATE2 = (datetime.now(tz) - timedelta(days=i+2)).strftime(\"%Y-%m-%d\")\n", "    query = \"\"\"\n", "    SELECT od.order_id as suborder_id, od.status_id, max(plog.pos_timestamp + interval '5.5 hour') as picking_time, extract(hour from max(plog.pos_timestamp + interval '5.5 hour')) as picking_hour\n", "    From (select order_id,status_id from lake_ims.ims_order_details where scheduled_at >= %(start_date1)s ) od\n", "    LEFT JOIN lake_warehouse_location.warehouse_pick_list_order_mapping plo on od.order_id = plo.order_id\n", "    LEFT JOIN lake_warehouse_location.pick_list_log plog on plog.pick_list_id=plo.pick_list_id and plog.picklist_state = 3 \n", "    group by 1,2\n", "    \"\"\"\n", "    Picking_Time = pd.read_sql_query(\n", "        sql=query,\n", "        con=redshift,\n", "        params={\n", "            \"start_date\": START_DATE,\n", "            \"end_date\": END_DATE,\n", "            \"start_date1\": START_DATE1,\n", "        },\n", "    )\n", "    Picking_Time.suborder_id = Picking_Time.suborder_id.astype(int)\n", "\n", "    Final1 = Final.merge(\n", "        Picking_Time, how=\"left\", left_on=\"suborder_id\", right_on=\"suborder_id\"\n", "    )\n", "\n", "    print(\n", "        Final1.shape,\n", "        Final1[Final1.picking_time.notnull()].shape,\n", "        Final1[Final1.picking_time.isnull()].shape,\n", "    )\n", "\n", "    Final1.head(2)\n", "\n", "    Final1.shape\n", "\n", "    unf_orders = Final1.order_id.unique()\n", "\n", "    # Nyala, metering & resch\n", "    query_order_log_updated = \"\"\"SELECT T2.* , o.name as outlet_name, l.id as city_id, l.name as city_name FROM\n", "    (\n", "    SELECT T1.*,ul.fnv_upper_limit as fnv_upper_limit_live,ul.upper_limit as upper_limit_live,\n", "    (ulh.created_at + interval '5.5 hrs') as upper_limit_timestamp, ulh.fnv_upper_limit, ulh.upper_limit\n", "    FROM\n", "    (\n", "    SELECT T.*, il.item_id , il.actual_quantity , il.blocked_quantity,\n", "    (case when il.actual_quantity < il.blocked_quantity then 'NO' else 'YES' end) as is_inv_present_ordering\n", "    FROM\n", "    (\n", "    SELECT od.outlet as outlet_id , od.order_id , od.ancestor, \n", "    (od.scheduled_at + interval '5.5 hrs') as scheduled_at, \n", "    od.status_id as order_status_id, \n", "    ol.id,\n", "    (ol.created_at + interval '5.5 hrs') as order_created_at,\n", "    ol.description, \n", "    ol.status_id as log_status_id, \n", "    date(ol.old_scheduled_at + interval '5.5 hrs') as old_scheduled_at\n", "    FROM lake_ims.ims_order_details od\n", "    inner join lake_ims.ims_order_log ol on od.order_id = ol.order_id\n", "    where date(od.scheduled_at)  between %(start_date)s and %(end_date)s and ol.description in ('Reprocure','Created') \n", "    and od.ancestor in ({orders})\n", "    )T\n", "    left join lake_ims.ims_item_inventory_log il on T.id = il.order_log_id\n", "    -- where il.actual_quantity < il.blocked_quantity\n", "    ) T1\n", "    left join lake_ims.ims_item_inventory ul on T1.item_id = ul.item_id and T1.outlet_id = ul.outlet_id \n", "    left join lake_ims.ims_item_upper_limit_history ulh on ul.id = ulh.item_inventory_id and ulh.created_at > %(start_date)s\n", "    ) T2 \n", "\n", "    inner join lake_retail.console_outlet o on T2.outlet_id = o.id \n", "    inner join lake_retail.console_location l on o.tax_location_id = l.id\"\"\".format(\n", "        orders=\",\".join([str(x) for x in unf_orders])\n", "    )\n", "\n", "    Order_log = pd.read_sql_query(\n", "        sql=query_order_log_updated,\n", "        con=redshift,\n", "        params={\"start_date\": START_DATE1, \"end_date\": END_DATE1},\n", "    )\n", "\n", "    Order_log[Order_log[\"actual_quantity\"] >= Order_log[\"blocked_quantity\"]].shape\n", "\n", "    Order_log[Order_log[\"actual_quantity\"] < Order_log[\"blocked_quantity\"]].shape\n", "\n", "    Order_log[\"key\"] = (\n", "        Order_log.ancestor.astype(str) + \"|\" + Order_log.item_id.astype(str)\n", "    )\n", "\n", "    Order_log.columns\n", "\n", "    Order_log1 = Order_log[\n", "        [\n", "            \"order_id\",\n", "            \"ancestor\",\n", "            \"description\",\n", "            \"is_inv_present_ordering\",\n", "            \"item_id\",\n", "            \"fnv_upper_limit_live\",\n", "            \"upper_limit_live\",\n", "            \"upper_limit_timestamp\",\n", "            \"fnv_upper_limit\",\n", "            \"upper_limit\",\n", "        ]\n", "    ]\n", "\n", "    Order_log1.ancestor = Order_log1.ancestor.astype(int)\n", "    Order_log1.order_id = Order_log1.order_id.astype(int)\n", "    Order_log1 = Order_log1.rename(\n", "        columns={\"order_id\": \"suborder_id\", \"ancestor\": \"order_id\"}\n", "    )\n", "    Order_log1.head(2)\n", "\n", "    Final2 = Final1.merge(\n", "        Order_log1,\n", "        how=\"left\",\n", "        left_on=[\"order_id\", \"suborder_id\", \"item_id\"],\n", "        right_on=[\"order_id\", \"suborder_id\", \"item_id\"],\n", "    )\n", "    Final2.shape\n", "\n", "    Final2.head(2)\n", "\n", "    Final2[\"final_upper_limit_fnv\"] = Final2[\"fnv_upper_limit_live\"]\n", "    Final2[\"final_upper_limit\"] = Final2[\"upper_limit_live\"]\n", "\n", "    Final2_1 = Final2[Final2[\"upper_limit_timestamp\"].isnull()]\n", "    Final2_2 = Final2[Final2[\"upper_limit_timestamp\"].notnull()]\n", "\n", "    Final2_2 = Final2_2[(Final2_2[\"picking_time\"] >= Final2_2[\"upper_limit_timestamp\"])]\n", "\n", "    if Final2_2.shape[0] > 0:\n", "\n", "        Final2_2.head(2)\n", "\n", "        temp = (\n", "            Final2_2.groupby(\n", "                [\n", "                    \"slot_start\",\n", "                    \"slot_end\",\n", "                    \"order_id\",\n", "                    \"suborder_id\",\n", "                    \"product_id\",\n", "                    \"order_item_id\",\n", "                    \"ordered_quantity\",\n", "                    \"procured_quantity\",\n", "                    \"reason_key\",\n", "                    \"outlet_id\",\n", "                    \"outlet_name\",\n", "                    \"facility_id\",\n", "                    \"facilityname\",\n", "                    \"city\",\n", "                    \"unfulfilled_order\",\n", "                    \"item_id\",\n", "                    \"multiplier\",\n", "                    \"status_id\",\n", "                    \"picking_time\",\n", "                    \"picking_hour\",\n", "                ]\n", "            )\n", "            .upper_limit_timestamp.max()\n", "            .reset_index()\n", "        )\n", "        Final2_2x = Final2_2.merge(\n", "            temp,\n", "            on=[\n", "                \"slot_start\",\n", "                \"slot_end\",\n", "                \"order_id\",\n", "                \"suborder_id\",\n", "                \"product_id\",\n", "                \"order_item_id\",\n", "                \"ordered_quantity\",\n", "                \"procured_quantity\",\n", "                \"reason_key\",\n", "                \"outlet_id\",\n", "                \"outlet_name\",\n", "                \"facility_id\",\n", "                \"facilityname\",\n", "                \"city\",\n", "                \"unfulfilled_order\",\n", "                \"item_id\",\n", "                \"multiplier\",\n", "                \"status_id\",\n", "                \"picking_time\",\n", "                \"picking_hour\",\n", "                \"upper_limit_timestamp\",\n", "            ],\n", "            how=\"inner\",\n", "        )\n", "\n", "        Final2_2x[\"final_upper_limit_fnv\"] = Final2_2x[\"fnv_upper_limit_live\"]\n", "        Final2_2x[\"final_upper_limit\"] = Final2_2x[\"upper_limit_live\"]\n", "\n", "        Final2_2x.shape, Final2_2.shape, Final2_1.shape\n", "\n", "        Final3 = pd.concat([Final2_1, Final2_2x], axis=0, sort=False)\n", "    else:\n", "        Final3 = Final2.copy()\n", "\n", "    Final1.shape, Final3.shape\n", "\n", "    temp = pd.concat([Final1, Final3.iloc[:, 0:20]], axis=0, sort=False)\n", "\n", "    temp.drop_duplicates(keep=False).head()\n", "\n", "    Final2_3 = pd.DataFrame()\n", "    for j, k in zip(\n", "        tuple(temp.drop_duplicates(keep=False).order_id),\n", "        tuple(temp.drop_duplicates(keep=False).item_id),\n", "    ):\n", "        Final2_3 = pd.concat(\n", "            [Final2_3, Final2[(Final2[\"order_id\"] == j) & (Final2[\"item_id\"] == k)]],\n", "            axis=0,\n", "        )\n", "\n", "    Final3 = pd.concat([Final3, Final2_3], axis=0, sort=False)\n", "\n", "    Final3 = Final3.drop(\n", "        columns=[\n", "            \"fnv_upper_limit_live\",\n", "            \"upper_limit_live\",\n", "            \"upper_limit_timestamp\",\n", "            \"fnv_upper_limit\",\n", "            \"upper_limit\",\n", "        ]\n", "    )\n", "\n", "    Final1.shape, Final3.shape\n", "\n", "    # FnV Data\n", "\n", "    Final4 = Final3\n", "    Final4.shape\n", "\n", "    Final4.head(2)\n", "\n", "    Final4.shape\n", "\n", "    # ims, ils actual quantity\n", "\n", "    ims_query = \"\"\"\n", "    select pom.order_id as suborder_id, pom.pick_list_id,pli.item_id,pli.ims_quantity as ims_qty\n", "    from lake_warehouse_location.warehouse_pick_list_item_non_ils_error_log pli \n", "    left join lake_warehouse_location.warehouse_pick_list_order_mapping pom on pli.pick_list_id = pom.pick_list_id\n", "    where date(pli.created_at+interval '5.5 hours') >= %(start_date)s\n", "    \"\"\"\n", "    ims_quantity = pd.read_sql_query(\n", "        sql=ims_query,\n", "        con=redshift,\n", "        params={\"start_date\": START_DATE1, \"end_date\": END_DATE},\n", "    )\n", "\n", "    query = \"\"\"\n", "    With Base as (\n", "    Select id from lake_oms_bifrost.oms_suborder \n", "    where slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "    and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour'))\n", "    )\n", "    select pom.order_id as suborder_id, pom.pick_list_id,ple.item_id,ple.location_quantity,ple.quantity_picked,ple.quantity_suggested, (ple.created_at + interval '5.5hrs')\n", "    from Base\n", "    left join lake_warehouse_location.warehouse_pick_list_order_mapping pom on pom.order_id = Base.id\n", "    -- left join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pom.pick_list_id\n", "    inner join lake_warehouse_location.warehouse_pick_list_item_location_error_log ple on ple.pick_list_id = pom.pick_list_id and ple.error_type = 5\n", "    \"\"\"\n", "    ils_quantity = pd.read_sql_query(\n", "        sql=query, con=redshift, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", "    )\n", "\n", "    ils_quantity.suborder_id = ils_quantity.suborder_id.astype(int)\n", "    ils_quantity.item_id = ils_quantity.item_id.astype(int)\n", "\n", "    ims_quantity.suborder_id = ims_quantity.suborder_id.astype(int)\n", "    ims_quantity.item_id = ims_quantity.item_id.astype(int)\n", "\n", "    Final4 = Final4.merge(\n", "        ils_quantity[\n", "            [\n", "                \"suborder_id\",\n", "                \"pick_list_id\",\n", "                \"item_id\",\n", "                \"location_quantity\",\n", "                \"quantity_picked\",\n", "                \"quantity_suggested\",\n", "            ]\n", "        ],\n", "        left_on=[\"suborder_id\", \"item_id\"],\n", "        right_on=[\"suborder_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    Final4 = Final4.merge(\n", "        ims_quantity[[\"suborder_id\", \"pick_list_id\", \"item_id\", \"ims_qty\"]],\n", "        left_on=[\"suborder_id\", \"item_id\"],\n", "        right_on=[\"suborder_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    Final4.columns\n", "\n", "    Final4.reason_key.value_counts()\n", "\n", "    Final5 = Final4\n", "    Final5[\"is_inv_present_picking\"] = np.where(\n", "        (Final5[\"ims_qty\"] > 0) | (Final5[\"location_quantity\"] > 0), \"YES\", \"NO\"\n", "    )\n", "\n", "    Final5[\n", "        (Final5[\"is_inv_present_picking\"] == \"YES\")\n", "        & (Final5[\"is_inv_present_ordering\"] == \"YES\")\n", "    ].shape\n", "\n", "    Final5[\n", "        (Final5[\"is_inv_present_picking\"] == \"YES\")\n", "        & (Final5[\"is_inv_present_ordering\"] == \"NO\")\n", "    ].shape\n", "\n", "    Final5[\n", "        (Final5[\"is_inv_present_picking\"] == \"NO\")\n", "        & (Final5[\"is_inv_present_ordering\"] == \"YES\")\n", "    ].shape\n", "\n", "    Final5[\n", "        (Final5[\"is_inv_present_picking\"] == \"NO\")\n", "        & (Final5[\"is_inv_present_ordering\"] == \"NO\")\n", "    ].shape\n", "\n", "    Final5[\"upper_limit\"] = Final5[\"final_upper_limit_fnv\"].fillna(0) + Final5[\n", "        \"final_upper_limit\"\n", "    ].fillna(0)\n", "\n", "    Final5[Final5.upper_limit > 0].shape\n", "\n", "    Final5.description.value_counts()\n", "\n", "    Final5.shape\n", "\n", "    # edit cart\n", "    edit_query = \"\"\"\n", "    select distinct order_id,max(cost_comparison) as cost_comparison,\n", "    max(new_items_check) as new_items_check\n", "    from\n", "    (select *,\n", "    case when (cast(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(extra ,'order'),'new'),'total_cost') as float)::int) = (cast(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(extra ,'order'),'old'),'total_cost') as float)::int) then 'same_cost'\n", "        else 'not_same' end as cost_comparison,\n", "        CASE WHEN IS_VALID_JSON(extra) = True AND extra::TEXT LIKE '%%edited_items%%' and JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(extra ,'edited_items'),'new_items')::text ilike '%%Guaranteed Gifts%%' \n", "        then 'gift_pack' else 'not_gift_pack' end as new_items_check\n", "        from lake_oms_bifrost.oms_order_event where install_ts::date > '2021-01-01'\n", "        and event_type_key = 'order_item_edit_v2' and order_id in ({orders})\n", "    )\n", "    group by 1 \n", "    \"\"\".format(\n", "        orders=\",\".join([str(x) for x in unf_orders])\n", "    )\n", "    edit_cart = pd.read_sql_query(sql=edit_query, con=redshift)\n", "\n", "    resch_qu = \"\"\"select distinct order_id, max(event_type_key)  as event_type_key from lake_oms_bifrost.oms_order_event ooe \n", "        where ooe.event_type_key in ('order_rescheduling','station_assignment') \n", "        and ooe.order_id in ({orders})\n", "        group by 1\"\"\".format(\n", "        orders=\",\".join([str(x) for x in unf_orders])\n", "    )\n", "    resch = pd.read_sql_query(sql=resch_qu, con=redshift)\n", "\n", "    Final5 = Final5.merge(edit_cart, on=[\"order_id\"], how=\"left\")\n", "    Final5 = Final5.merge(resch, on=[\"order_id\"], how=\"left\")\n", "\n", "    Final5.shape\n", "\n", "    # manual update\n", "    manual_update_qu = \"\"\"\n", "    SELECT\n", "        iil.outlet_id as outlet_id,\n", "        p.item_id as Item_Id,\n", "        convert_timezone('Asia/Kolkata',iil.pos_timestamp) AS POS_Timestamp,\n", "        iiut.inventory_operation as Operation,\n", "        iil.\"delta\" as Change_Quantity,\n", "        iil.quantity as Final_Quantity,\n", "        iiut.name as Inventory_Update_Type\n", "    FROM \n", "        lake_ims.ims_inventory_log iil\n", "    INNER JOIN\n", "        lake_rpc.product_product p ON iil.variant_id=p.variant_id\n", "    INNER JOIN\n", "        lake_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "    left JOIN lake_retail.console_outlet o ON o.id=iil.outlet_id\n", "    left join lake_crates.facility fc on o.facility_id=fc.id\n", "    WHERE iil.inventory_update_type_id IN (14,15,39,40,41,42,43,44,45,46,47,48) and o.business_type_id = 7\n", "    AND date(convert_timezone('Asia/Kolkata',iil.pos_timestamp)) between %(start_date)s and %(end_date)s\n", "    \"\"\"\n", "    manual_update = pd.read_sql_query(\n", "        sql=manual_update_qu,\n", "        con=redshift,\n", "        params={\"start_date\": START_DATE, \"end_date\": END_DATE},\n", "    )\n", "\n", "    manual_update = manual_update[manual_update[\"operation\"] == \"-\"]\n", "\n", "    manual_update.head(2)\n", "\n", "    Final6 = pd.merge(\n", "        Final5,\n", "        manual_update,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", "\n", "    # GRN Logs\n", "    # # latest grn time and quantity grned at outlet & item level\n", "    # grn_query = \"\"\"\n", "    # select po.*\n", "    # from lake_po.po_grn po\n", "    # inner join lake_retail.console_outlet o on o.id = po.outlet_id\n", "    # where date(po.created_at + interval '5.5 hour') = %(start_date)s and o.business_type_id = 7\"\"\"\n", "    # grn_logs = pd.read_sql_query(sql=grn_query, con=redshift, params = {\"start_date\": START_DATE})\n", "    # grn_logs = grn_logs[[\"item_id\",\"quantity\",\"created_at\",\"outlet_id\"]]\n", "    # grn_logs.shape\n", "\n", "    sto_query = \"\"\"\n", "    SELECT \n", "    ii.outlet_id,pp.item_id,(ii.created_at + interval '5.5 hour') as inward_timestamp,extract(hour from (ii.created_at + interval '5.5 hour')) as inward_hour,sum(isd.\"delta\") as grn_inward_quan\n", "    from   lake_ims.ims_inward_invoice ii\n", "    left join lake_ims.ims_inventory_stock_details isd on isd.grn_id = ii.grn_id\n", "    left join lake_retail.console_outlet co on co.id = ii.outlet_id\n", "    left join lake_rpc.product_product pp on pp.variant_id = isd.variant_id\n", "    WHERE co.business_type_id = 7\n", "    and date(ii.created_at + interval '5.5 hour') = %(start_date)s\n", "    group by 1,2,3\n", "    \"\"\"\n", "    sto_data = pd.read_sql_query(\n", "        sql=sto_query, con=redshift, params={\"start_date\": START_DATE}\n", "    )\n", "\n", "    # Final3[(Final3.picking_hour.isnull()) & (Final3[\"reason_key\"]=='ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING')].shape\n", "\n", "    Final6 = pd.merge(\n", "        Final6,\n", "        sto_data,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", "\n", "    badstock_query = \"\"\"\n", "    with temp as (select\n", "            ul.outlet_id,\n", "            rpc.item_id,\n", "            (ul.created_at + interval '5.5 hour') as badStock_timestamp,\n", "            sum(\"delta\") as badstock_qty\n", "    from  lake_ims.ims_bad_inventory_update_log ul \n", "    left join lake_rpc.product_product rpc on ul.variant_id=rpc.variant_id\n", "    left join lake_retail.console_outlet co on co.id = ul.outlet_id\n", "    where date(ul.created_at + interval '5.5 hour') = %(start_date)s and co.business_type_id = 7\n", "    group by 1,2,3),\n", "    temp_1 as (select outlet_id,item_id,max(badStock_timestamp) as badStock_timestamp\n", "    from temp\n", "    group by 1,2)\n", "    select temp.*\n", "    from temp\n", "    inner join temp_1 on temp.outlet_id = temp_1.outlet_id and temp.item_id = temp_1.item_id and temp.badStock_timestamp = temp_1.badStock_timestamp\n", "    \"\"\"\n", "    badstock_data = pd.read_sql_query(\n", "        sql=badstock_query, con=redshift, params={\"start_date\": START_DATE}\n", "    )\n", "\n", "    badstock_data.head()\n", "\n", "    Final7 = pd.merge(\n", "        Final6,\n", "        badstock_data,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", "\n", "    Final7.shape\n", "\n", "    # returns with timestamp\n", "    returns_query = \"\"\"\n", "    with temp as (select pi.outlet_id,rpc.item_id,pi.pos_timestamp + interval '5.5 hour' as return_timestamp,sum(pid.quantity) as return_quan\n", "    from lake_pos.pos_invoice pi\n", "    left join lake_retail.console_outlet co on co.id = pi.outlet_id\n", "    left join lake_pos.pos_invoice_product_details pid on pid.invoice_id = pi.id\n", "    left join lake_rpc.product_product rpc on pid.variant_id = rpc.variant_id\n", "    where pi.invoice_type_id = 2 and co.business_type_id = 7 and date(pi.pos_timestamp + interval '5.5 hour') = %(start_date)s\n", "    group by 1,2,3),\n", "    temp_1 as (\n", "    select outlet_id,item_id,max(return_timestamp) as return_timestamp\n", "    from temp\n", "    group by 1,2\n", "    )\n", "    select temp.*\n", "    from temp\n", "    inner join temp_1 on temp.outlet_id = temp_1.outlet_id and temp.item_id = temp_1.item_id and temp.return_timestamp = temp_1.return_timestamp\n", "    \"\"\"\n", "    returns_data = pd.read_sql_query(\n", "        sql=returns_query, con=redshift, params={\"start_date\": START_DATE}\n", "    )\n", "    returns_data.head()\n", "\n", "    Final8 = pd.merge(\n", "        Final7,\n", "        returns_data,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", "\n", "    Final8.shape\n", "\n", "    facility_list = list(\n", "        Final8[Final8.reason_key.notnull()].facility_id.astype(str).unique()\n", "    )\n", "    item_id_list = list(\n", "        Final8[Final8.reason_key.notnull()].item_id.astype(str).unique()\n", "    )\n", "\n", "    #     inventory_snapshot_query = \"\"\"\n", "    #     select co.id as outlet_id,rda.item_id,(order_date + interval '5.5 hrs') as snapshot_time,actual_quantity\n", "    #     from consumer.rpc_daily_availability  rda\n", "    #     left join lake_retail.console_outlet co on co.facility_id = rda.facility_id\n", "    #     where date(order_date + interval '5.5 hrs') = %(start_date)s and co.business_type_id = 7 and availability = 'live' and active_flag = 'active'\n", "    #     \"\"\"\n", "    #     inventory_data = pd.read_sql_query(\n", "    #         sql=inventory_snapshot_query, con=redshift, params={\"start_date\": START_DATE}\n", "    #     )\n", "    inventory_snapshot_query = \"\"\"\n", "    select co.id as outlet_id,rda.item_id,extract(hour from order_date) as hour,actual_quantity \n", "    from consumer.rpc_daily_availability rda\n", "    inner join lake_retail.console_outlet co on co.facility_id = rda.facility_id\n", "    where date(order_date) = %(start_date)s and co.business_type_id = 7 and availability = 'live' and active_flag = 'active'\n", "    and rda.facility_id in %(facility_list)s\n", "    and rda.item_id in %(item_list)s\n", "    group by 1,2,3,4\n", "    \"\"\"\n", "    inventory_data = pd.read_sql(\n", "        sql=inventory_snapshot_query,\n", "        con=redshift,\n", "        params={\n", "            \"start_date\": START_DATE,\n", "            \"facility_list\": tuple(facility_list),\n", "            \"item_list\": tuple(item_id_list),\n", "        },\n", "    )\n", "    inventory_data = inventory_data.drop_duplicates()\n", "\n", "    inventory_data[\"item_id\"] = inventory_data[\"item_id\"].astype(int)\n", "\n", "    inventory_data.shape\n", "\n", "    Final9 = pd.merge(\n", "        Final8,\n", "        inventory_data,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", "\n", "    Final9.shape\n", "\n", "    # pushing data to table\n", "    def redshift_schema(df):\n", "        metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "        ref_dict = {\n", "            \"int64\": \"int\",\n", "            \"datetime64[ns]\": \"timestamp\",\n", "            \"object\": \"varchar(1000)\",\n", "            \"float64\": \"float\",\n", "            \"datetime64[ns, UTC]\": \"timestamp\",\n", "        }\n", "        return [\n", "            {\"name\": key, \"type\": ref_dict[value], \"description\": key}\n", "            for key, value in metadata.items()\n", "        ]\n", "\n", "    def metadata_dict(df):\n", "        return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "    Final9.head(2)\n", "\n", "    Final9 = Final9.rename(columns={\"order_id_x\": \"order_id\"})\n", "\n", "    columns = list(set(Final9.columns).difference(fulfilled.columns))\n", "\n", "    for k in columns:\n", "        fulfilled[k] = np.nan\n", "\n", "    Final9.shape\n", "\n", "    fulfilled.shape\n", "\n", "    total = pd.concat([fulfilled, Final9], axis=0, sort=False)\n", "\n", "    total[\"key\"] = (\n", "        total[\"suborder_id\"].astype(str)\n", "        + \"|\"\n", "        + total[\"order_item_id\"].astype(str)\n", "        + \"|\"\n", "        + total[\"item_id\"].astype(str)\n", "    )\n", "\n", "    total.final_upper_limit = total.final_upper_limit.fillna(0)\n", "    total.final_upper_limit_fnv = total.final_upper_limit_fnv.fillna(0)\n", "\n", "    total.snapshot_time = pd.to_datetime(total.snapshot_time)\n", "    total.return_timestamp = pd.to_datetime(total.return_timestamp)\n", "    total.picking_time = pd.to_datetime(total.picking_time)\n", "    total.inward_timestamp = pd.to_datetime(total.inward_timestamp)\n", "    total.badstock_timestamp = pd.to_datetime(total.badstock_timestamp)\n", "    total.reason_key = total.reason_key.astype(str)\n", "    total.pos_timestamp = pd.to_datetime(total.pos_timestamp)\n", "\n", "    total[total.reason_key == \"None\"].shape\n", "\n", "    total.groupby([\"outlet_id\"]).order_id.nunique()\n", "\n", "    total[\"procured_quantity\"] = total[\"procured_quantity\"].fillna(0)\n", "    total[\"procured_quantity\"] = total[\"procured_quantity\"].astype(int)\n", "\n", "    total[\"outlet_id\"] = total[\"outlet_id\"].astype(int)\n", "    total[\"facility_id\"] = total[\"facility_id\"].astype(int)\n", "    total[\"product_id\"] = total[\"product_id\"].astype(int)\n", "\n", "    total = total[\n", "        [\n", "            \"slot_start\",\n", "            \"slot_end\",\n", "            \"order_id\",\n", "            \"suborder_id\",\n", "            \"product_id\",\n", "            \"order_item_id\",\n", "            \"ordered_quantity\",\n", "            \"procured_quantity\",\n", "            \"reason_key\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"facility_id\",\n", "            \"facilityname\",\n", "            \"city\",\n", "            \"unfulfilled_order\",\n", "            \"item_id\",\n", "            \"multiplier\",\n", "            \"outlet_type\",\n", "            \"pick_list_id_x\",\n", "            \"quantity_picked\",\n", "            \"quantity_suggested\",\n", "            \"badstock_timestamp\",\n", "            \"final_upper_limit\",\n", "            \"return_timestamp\",\n", "            \"is_inv_present_ordering\",\n", "            \"return_quan\",\n", "            \"upper_limit\",\n", "            \"grn_inward_quan\",\n", "            \"pos_timestamp\",\n", "            \"new_items_check\",\n", "            \"event_type_key\",\n", "            \"picking_time\",\n", "            \"location_quantity\",\n", "            \"badstock_qty\",\n", "            \"cost_comparison\",\n", "            \"change_quantity\",\n", "            \"pick_list_id_y\",\n", "            \"final_quantity\",\n", "            \"inventory_update_type\",\n", "            \"inward_hour\",\n", "            \"status_id\",\n", "            \"inward_timestamp\",\n", "            \"snapshot_time\",\n", "            \"operation\",\n", "            \"picking_hour\",\n", "            \"description\",\n", "            \"actual_quantity\",\n", "            \"ims_qty\",\n", "            \"is_inv_present_picking\",\n", "            \"final_upper_limit_fnv\",\n", "            \"key\",\n", "        ]\n", "    ]\n", "\n", "    column_dtypes = redshift_schema(total)\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"FillRateAttribution_darkstores_v2\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"key\"],\n", "        \"sortkey\": [\"outlet_id\"],\n", "        \"incremental_key\": \"slot_end\",\n", "        \"table_description\": \"to store fillrateattribution data for darkstores\",\n", "        \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "    }\n", "    pb.to_redshift(total, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(\n", "    sql=\"\"\"select distinct slot_end::date FROM metrics.fillrateattribution_darkstores_v2 where slot_end::date > current_date-15 order by 1\"\"\",\n", "    con=pb.get_connection(\"redshift\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}