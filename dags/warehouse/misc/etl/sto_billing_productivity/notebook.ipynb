{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import time\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install pandasql\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Mwb3Tj5jfFNGL2mLBdF2OE_l3sXF9CDZxlMqYJEUt7o\"\n", "shift_base = pb.from_sheets(sheet_id, \"Sheet1\", clear_cache=True)\n", "# shift_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shift_base[\"shift_start_time\"] = pd.to_datetime(\n", "    shift_base[\"shift_start_time\"], format=\"%H:%M\"\n", ").dt.time\n", "shift_base[\"shift_end_time\"] = pd.to_datetime(\n", "    shift_base[\"shift_end_time\"], format=\"%H:%M\"\n", ").dt.time\n", "shift_base[\"delta\"] = np.where(\n", "    shift_base[\"shift_start_time\"] > shift_base[\"shift_end_time\"], 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def daterange(date1, date2):\n", "    for n in range(int((date2 - date1).days) + 1):\n", "        yield date1 + <PERSON><PERSON><PERSON>(n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(date.today())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["for a in range(1, 0, -1):\n", "    datelist = []\n", "    # start_dt = date(2021, 6, 9)\n", "    # end_dt = date(2021, 6, 13)\n", "    # Last_Date =  date(2020, 11, 2)\n", "    Start_Date = date.today() - <PERSON><PERSON><PERSON>(days=a + 1)\n", "    Last_Date = date.today() - <PERSON><PERSON><PERSON>(days=a)\n", "    print(Start_Date)\n", "    print(Last_Date)\n", "    for dt in daterange(Start_Date, Last_Date):\n", "        datelist.append(dt.strftime(\"%Y-%m-%d\"))\n", "\n", "    for _date in datelist:\n", "        cur_date = pd.to_datetime(_date, format=\"%Y-%m-%d\").date()\n", "        cur_date_cpy = cur_date.strftime(\"%Y-%m-%d\")\n", "        nxd = (cur_date + timedelta(days=1)).strftime(\"%Y-%m-%d\")  # next day\n", "        prd = (cur_date + timedelta(days=-1)).strftime(\"%Y-%m-%d\")  # previous day\n", "        # print(cur_date, cur_date_cpy, nxd, prd)\n", "        new_shift = shift_base[0:0]\n", "        new_shift[\"operation_date\"] = \"\"\n", "\n", "        # calculation of operation date. delta > 1: the date has changed overnight\n", "        for index, row in shift_base.iterrows():\n", "            copy_row = row\n", "            if row[\"delta\"] == 0:\n", "                row[\"shift_start_time\"] = pd.to_datetime(\n", "                    cur_date_cpy + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                row[\"shift_end_time\"] = pd.to_datetime(\n", "                    cur_date_cpy + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                row[\"operation_date\"] = cur_date\n", "                new_shift = new_shift.append(row, ignore_index=True)\n", "            else:\n", "                copy_row[\"shift_start_time\"] = pd.to_datetime(\n", "                    prd + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                copy_row[\"shift_end_time\"] = pd.to_datetime(\n", "                    cur_date_cpy + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                copy_row[\"operation_date\"] = cur_date + timedelta(days=-1)\n", "                new_shift = new_shift.append(copy_row, ignore_index=True)\n", "                row[\"shift_start_time\"] = pd.to_datetime(\n", "                    cur_date_cpy + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                row[\"shift_end_time\"] = pd.to_datetime(\n", "                    nxd + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "                )\n", "                row[\"operation_date\"] = cur_date\n", "                new_shift = new_shift.append(row, ignore_index=True)\n", "\n", "        new_shift[\"facility_id\"] = new_shift[\"facility_id\"].astype(str).astype(int)\n", "\n", "        query = f\"\"\"\n", "                SELECT co.name AS \"outlet_name\",\n", "                       log.outlet_id,\n", "                       co.facility_id,\n", "                       co1.name AS ds_name,\n", "                       Convert_timezone('Asia/Kolkata',log.pos_timestamp) AS \"billing_time\",\n", "                       log.created_by_name AS \"biller_id\",\n", "                       grofers_order_id AS \"Order_ID_STO_ID\",\n", "                       \"delta\" AS quantity,\n", "                       rpp.item_id AS \"item_id\",\n", "                       wpl.assigned_to_device_id,\n", "                       inventory_update_id\n", "                FROM lake_ims.ims_inventory_log log\n", "                INNER JOIN lake_retail.console_outlet co ON co.id=log.outlet_id\n", "                JOIN lake_rpc.product_product rpp ON rpp.variant_id = log.variant_id\n", "                AND rpp.active = 1\n", "                JOIN lake_warehouse_location.warehouse_wave ww ON ww.entity_id = grofers_order_id\n", "                JOIN lake_warehouse_location.warehouse_pick_list wpl ON wpl.wave_id = ww.id\n", "                JOIN lake_ims.ims_sto_details ims ON ims.sto_id = grofers_order_id\n", "                INNER JOIN lake_retail.console_outlet co1 ON co1.id=ims.merchant_outlet_id\n", "                WHERE date(pos_timestamp + interval '5.5 hrs') IN ('{cur_date}','{nxd}')\n", "                  AND inventory_update_type_id=124\n", "                ORDER BY order_id_sto_id\n", "            \"\"\"\n", "        base = pd.read_sql(query, redshift)\n", "        merged = base.merge(\n", "            new_shift[\n", "                [\n", "                    \"facility_id\",\n", "                    \"shift_num\",\n", "                    \"shift_start_time\",\n", "                    \"shift_end_time\",\n", "                    \"operation_date\",\n", "                ]\n", "            ],\n", "            how=\"inner\",\n", "            on=\"facility_id\",\n", "        )\n", "        merged[\"valid\"] = np.where(\n", "            (merged[\"shift_start_time\"] <= merged[\"billing_time\"])\n", "            & (merged[\"billing_time\"] < merged[\"shift_end_time\"]),\n", "            1,\n", "            0,\n", "        )\n", "\n", "        merged_final = merged[merged[\"valid\"] == 1]\n", "\n", "        merged_final = merged_final.drop(columns={\"valid\"})\n", "        merged_final[\"etl_timestamp_utc\"] = datetime.now()\n", "        merged_final[\"operation_date\"] = pd.to_datetime(\n", "            merged_final[\"operation_date\"], format=\"%Y-%m-%d\"\n", "        )\n", "        #         merged_final[\"shift_end_time\"] = merged_final[\"shift_end_time\"].dt.strftime('%Y-%m-%d %H:%M:%S')\n", "        #         merged_final[\"shift_start_time\"] = merged_final[\"shift_start_time\"].dt.strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "        merged_final[[\"facility_id\", \"outlet_id\", \"quantity\"]] = merged_final[\n", "            [\"facility_id\", \"outlet_id\", \"quantity\"]\n", "        ].astype(\"int\")\n", "\n", "    loop = (\n", "        merged_final[\n", "            [\"facility_id\", \"shift_start_time\", \"shift_end_time\", \"operation_date\"]\n", "        ].drop_duplicates()\n", "        #     .reset_index()\n", "    )\n", "\n", "    #     loop = loop[loop['facility_id'] == 26]\n", "\n", "    operational_data = loop[0:0].rename(\n", "        columns={\n", "            \"shift_start_time\": \"operational_start_time\",\n", "            \"shift_end_time\": \"operational_end_time\",\n", "        }\n", "    )\n", "\n", "    for _facility_id in loop.facility_id.unique():\n", "        row = loop[loop[\"facility_id\"] == _facility_id]\n", "        for operation_date in row.operation_date.unique():\n", "            if pd.to_datetime(operation_date).strftime(\n", "                \"%Y-%m-%d\"\n", "            ) == Start_Date.strftime(\"%Y-%m-%d\"):\n", "                min_ = pd.to_datetime(\n", "                    Start_Date.strftime(\"%Y-%m-%d\")\n", "                    + \" \"\n", "                    + row[row[\"operation_date\"] == Last_Date.strftime(\"%Y-%m-%d\")]\n", "                    .shift_start_time.min()\n", "                    .strftime(\"%H:%M:%S\")\n", "                )\n", "                max_ = row[row[\"operation_date\"] == operation_date].shift_end_time.max()\n", "            else:\n", "                min_ = row[\n", "                    row[\"operation_date\"] == operation_date\n", "                ].shift_start_time.min()\n", "                max_ = row[row[\"operation_date\"] == operation_date].shift_end_time.max()\n", "            operational_data = operational_data.append(\n", "                pd.DataFrame(\n", "                    {\n", "                        \"facility_id\": [_facility_id],\n", "                        \"operation_date\": [\n", "                            pd.to_datetime(operation_date).strftime(\"%Y-%m-%d\")\n", "                        ],\n", "                        \"operational_start_time\": [min_],\n", "                        \"operational_end_time\": [max_],\n", "                    }\n", "                ),\n", "                ignore_index=True,\n", "            )\n", "\n", "    summ_final = pd.DataFrame()\n", "    for index, row in loop.iterrows():\n", "        summ = pd.DataFrame()\n", "        f = row[\"facility_id\"]\n", "        start_time = pd.to_datetime(row[\"shift_start_time\"])\n", "        end_time = pd.to_datetime(row[\"shift_end_time\"])\n", "        operation_date = row[\"operation_date\"].strftime(\"%Y-%m-%d\")\n", "        op_start_time = pd.to_datetime(\n", "            operational_data[\n", "                (operational_data[\"facility_id\"] == f)\n", "                & (operational_data[\"operation_date\"] == operation_date)\n", "            ][\"operational_start_time\"].to_list()[0]\n", "        )\n", "        op_end_time = pd.to_datetime(\n", "            operational_data[\n", "                (operational_data[\"facility_id\"] == f)\n", "                & (operational_data[\"operation_date\"] == operation_date)\n", "            ][\"operational_end_time\"].to_list()[0]\n", "        )\n", "        # contains log in & log out (device) timestamps for all timestamps that fall within the shift (start-time till end-time)\n", "        # note: here we don't filter for > 100 billed quantities and take all billers. The filtering happens at the query end\n", "        query1 = \"\"\"\n", "            SELECT distinct co.facility_id,\n", "                   co.name AS facility_name,\n", "                   dl.<PERSON><PERSON><PERSON>,\n", "                   log.created_by_name AS \"emp_id\",\n", "                   wu.name AS user_name,\n", "                   wpl.assigned_to_device_id as \"device_id\",\n", "                   dl.action,\n", "                   (log.pos_timestamp + interval '5.5 hrs') AS pos_timestamp\n", "            FROM lake_ims.ims_inventory_log log\n", "            INNER JOIN lake_retail.console_outlet co ON co.id=log.outlet_id\n", "            JOIN lake_warehouse_location.warehouse_wave ww ON ww.entity_id = grofers_order_id\n", "            JOIN lake_warehouse_location.warehouse_pick_list wpl ON wpl.wave_id = ww.id\n", "            JOIN lake_ims.ims_sto_details ims ON ims.sto_id = grofers_order_id\n", "            INNER JOIN lake_retail.console_outlet co1 ON co1.id=ims.merchant_outlet_id\n", "            LEFT JOIN lake_warehouse_location.warehouse_device_user_log dl ON log.created_by_name = dl.user_name \n", "            LEFT JOIN lake_retail.warehouse_user wu ON wu.emp_id = dl.user_name\n", "            WHERE (log.pos_timestamp + interval '5.5 hrs') BETWEEN %(start_time)s AND %(end_time)s\n", "              AND co.facility_id = %(facility)s\n", "              AND inventory_update_type_id=124\n", "              AND action IN ('LOGI<PERSON>','LOGOUT','FORCE_LOGOUT')\n", "              AND ROLE IN ('BILLER') \n", "             \"\"\"\n", "        data1 = pd.read_sql_query(\n", "            sql=query1,\n", "            con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "            params={\"start_time\": start_time, \"end_time\": end_time, \"facility\": f},\n", "        )\n", "\n", "        # Returns a list of employees along with their INs and OUTs (facial attendance). Note, if the employee leaves after the shift gets over, his OUT was not returned\n", "        query2 = \"\"\"\n", "            select *\n", "            FROM\n", "              (SELECT name,\n", "                      employee_id,\n", "                      direction, \n", "                      time\n", "               FROM \n", "                 (SELECT name,\n", "                         employee_id,\n", "                         (log_timestamp + interval '5.5 hrs') AS \"time\",\n", "                         department,\n", "                         direction,\n", "                         facility_code,\n", "                         id\n", "                  FROM consumer.warehouse_employee_logs\n", "                  WHERE \n", "                        direction IN ('IN',\n", "                                      'OUT') \n", "                  AND (log_timestamp + interval '5.5 hrs') BETWEEN %(start_time)s AND %(end_time)s\n", "                  ))\n", "                  ORDER BY time\n", "        \"\"\"\n", "\n", "        data3 = pd.read_sql_query(\n", "            sql=query2,\n", "            con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "            params={\"start_time\": op_start_time, \"end_time\": op_end_time},\n", "        )\n", "\n", "        # data_final is at the item level for each biller\n", "        data_final = merged_final[\n", "            (merged_final[\"facility_id\"] == f)\n", "            & (merged_final[\"shift_start_time\"] == str(start_time))\n", "            & (merged_final[\"shift_end_time\"] == str(end_time))\n", "        ]\n", "\n", "        # now at a biller level, for all items\n", "        billers = data_final.biller_id.unique()\n", "        data2 = data1[data1[\"emp_id\"].isin(billers)]\n", "        data4 = data3[data3[\"employee_id\"].isin(billers)]\n", "\n", "        import pandasql as ps\n", "\n", "        enter_to_purchase_time = ps.sqldf(\n", "            \"\"\"\n", "            SELECT DISTINCT p.facility_id,\n", "                            p.role,\n", "                            p.emp_id,\n", "                            p.user_name,\n", "                            p.device_id,\n", "                            max(p.pos_timestamp) OVER (PARTITION BY p.emp_id,\n", "                                                                    p.device_id,\n", "                                                                    p.pos_timestamp\n", "                                                       ORDER BY p.pos_timestamp DESC) AS 'start', \n", "                            min(e.pos_timestamp) OVER (PARTITION BY p.emp_id,\n", "                                                           p.device_id,\n", "                                                           p.pos_timestamp\n", "                                                       ORDER BY p.pos_timestamp DESC) AS 'end'\n", "            FROM data2 p\n", "            INNER JOIN data2 e ON e.emp_id = p.emp_id\n", "            AND e.device_id = p.device_id\n", "            WHERE p.action IN ('LOGIN')\n", "              AND e.action IN ('LOGOUT',\n", "                               'FORCE_LOGOUT')\n", "              AND e.pos_timestamp > p.pos_timestamp\n", "            ORDER BY e.pos_timestamp\"\"\",\n", "            locals(),\n", "        )\n", "\n", "        login_to_logout_time = ps.sqldf(\n", "            \"\"\"\n", "            SELECT p.employee_id,\n", "                   p.time AS 'start', \n", "                   e.time AS 'end'\n", "            FROM (select employee_id,max(time) as time from data4 where direction IN ('IN') group by 1)  p\n", "            INNER JOIN (select employee_id,min(time) as time from data4 where direction IN ('OUT') group by 1) e ON e.employee_id = p.employee_id\n", "              AND e.time > p.time\n", "            ORDER BY e.time\"\"\",\n", "            locals(),\n", "        )\n", "\n", "        enter_to_purchase_time[\"diff\"] = pd.to_datetime(\n", "            enter_to_purchase_time[\"end\"]\n", "        ) - pd.to_datetime(enter_to_purchase_time[\"start\"])\n", "\n", "        login_to_logout_time[\"diff\"] = pd.to_datetime(\n", "            login_to_logout_time[\"end\"]\n", "        ) - pd.to_datetime(login_to_logout_time[\"start\"])\n", "\n", "        #         device time\n", "        final_time = pd.DataFrame()\n", "\n", "        for m in billers:\n", "            x = data1[data1[\"emp_id\"] == m]\n", "            devices = x.device_id.dropna().unique()\n", "            for w in devices:\n", "                delta1 = np.nan\n", "                delta2 = np.nan\n", "                today = start_time\n", "                yesterday = end_time\n", "                y = enter_to_purchase_time[\n", "                    (enter_to_purchase_time[\"emp_id\"] == m)\n", "                    & ((enter_to_purchase_time[\"device_id\"] == w))\n", "                ][\"diff\"].sum()\n", "                if x.shape[0] > 0:\n", "                    if x[x[\"device_id\"] == w].iloc[0, :][\"action\"] in (\n", "                        \"LOGOUT\",\n", "                        \"FORCE_LOGOUT\",\n", "                    ):\n", "                        action = x[x[\"device_id\"] == w].iloc[0, :][\"action\"]\n", "                        min_logout = x[(x[\"device_id\"] == w) & (x[\"action\"] == action)][\n", "                            \"pos_timestamp\"\n", "                        ].min()\n", "                        delta1 = pd.to_datetime(min_logout) - pd.to_datetime(today)\n", "                    elif x[x[\"device_id\"] == w].iloc[-1, :][\"action\"] == \"LOGIN\":\n", "                        max_login = x[\n", "                            (x[\"device_id\"] == w) & (x[\"action\"].isin([\"LOGIN\"]))\n", "                        ][\"pos_timestamp\"].max()\n", "                        delta2 = pd.to_datetime(yesterday) - pd.to_datetime(max_login)\n", "                final = pd.DataFrame([y, delta1, delta2]).sum()\n", "                z = pd.DataFrame({\"emp_id\": m, \"device_id\": w, \"final_time\": final})\n", "                final_time = final_time.append(z)\n", "            #         try:\n", "            summ = pd.merge(\n", "                data_final,\n", "                final_time,\n", "                how=\"left\",\n", "                left_on=[\"biller_id\", \"assigned_to_device_id\"],\n", "                right_on=[\"emp_id\", \"device_id\"],\n", "            ).drop([\"emp_id\", \"assigned_to_device_id\"], axis=1)\n", "        #         except:\n", "        #             print('Error')\n", "\n", "        # calculating the logged hours\n", "        final_time = pd.DataFrame()\n", "\n", "        for m in billers:\n", "            x = data4[data4[\"employee_id\"] == m]\n", "            delta1 = np.nan\n", "            delta2 = np.nan\n", "            # shifts\n", "            shift_start = op_start_time\n", "            shift_end = op_end_time\n", "            # summing all individual shifts within the day\n", "            y = login_to_logout_time[(login_to_logout_time[\"employee_id\"] == m)][\n", "                \"diff\"\n", "            ].sum()\n", "            # if multiple entries\n", "            if x.shape[0] > 0:\n", "                if x.iloc[0, :][\"direction\"] in (\"OUT\"):\n", "                    min_out = x[x[\"direction\"] == \"OUT\"][\"time\"].min()\n", "                    delta1 = pd.to_datetime(min_out) - pd.to_datetime(shift_start)\n", "                # hours outside usual shift\n", "                elif x.iloc[-1, :][\"direction\"] == \"IN\":\n", "                    max_in = x[x[\"direction\"].isin([\"IN\"])][\"time\"].max()\n", "                    delta2 = pd.to_datetime(shift_end) - pd.to_datetime(max_in)\n", "            final = pd.DataFrame([y, delta1, delta2]).sum()\n", "            z = pd.DataFrame({\"biller_id\": m, \"logged_time\": final})\n", "            final_time = final_time.append(z)\n", "        try:\n", "            summ = pd.merge(\n", "                summ,\n", "                final_time,\n", "                how=\"left\",\n", "                left_on=[\"biller_id\"],\n", "                right_on=[\"biller_id\"],\n", "            )\n", "        except:\n", "            print(\"Error2\")\n", "\n", "        summ[\"active_hours\"] = summ[\"final_time\"] / pd.Timedelta(hours=1)\n", "        summ[\"logged_hours\"] = summ[\"logged_time\"] / pd.Timedelta(hours=1)\n", "\n", "        summ_final = pd.concat([summ_final, summ], axis=0, sort=False)\n", "\n", "        print(\"Done for facility: \", f, \" Iteration \", a)\n", "\n", "    summ_final1 = summ_final[\n", "        [\n", "            \"outlet_name\",\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"operation_date\",\n", "            \"shift_num\",\n", "            \"shift_start_time\",\n", "            \"shift_end_time\",\n", "            \"biller_id\",\n", "            \"order_id_sto_id\",\n", "            \"item_id\",\n", "            \"quantity\",\n", "            \"billing_time\",\n", "            \"ds_name\",\n", "            \"etl_timestamp_utc\",\n", "            \"device_id\",\n", "            \"inventory_update_id\",\n", "            \"active_hours\",\n", "            \"logged_hours\",\n", "        ]\n", "    ].rename(columns={\"ds_name\": \"receiver_outlet_name\"})\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"sender_outlet_name\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"sender_outlet_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"sender_facility_id\"},\n", "        {\n", "            \"name\": \"operation_date\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"billing_completed_at_ist\",\n", "        },\n", "        {\"name\": \"shift_num\", \"type\": \"varchar\", \"description\": \"Biller_Name\"},\n", "        {\n", "            \"name\": \"shift_start_time\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"billing_completed_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"shift_end_time\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"billing_completed_at_ist\",\n", "        },\n", "        {\"name\": \"biller_id\", \"type\": \"varchar\", \"description\": \"biller_Id\"},\n", "        {\n", "            \"name\": \"order_id_sto_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"grofers_order_id\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"varchar\", \"description\": \"item_id\"},\n", "        {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"billed quantity\"},\n", "        {\n", "            \"name\": \"billing_time\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"billing timestamp\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"receiver_outlet_name\",\n", "        },\n", "        {\n", "            \"name\": \"etl_timestamp_utc\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"etl_timestamp_utc\",\n", "        },\n", "        {\"name\": \"device_id\", \"type\": \"float\", \"description\": \"device_id\"},\n", "        {\n", "            \"name\": \"inventory_update_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"inventory_update_id\",\n", "        },\n", "        {\"name\": \"active_hours\", \"type\": \"float\", \"description\": \"active hours\"},\n", "        {\n", "            \"name\": \"logged_hours\",\n", "            \"type\": \"float\",\n", "            \"description\": \"logged_hours based on FRS\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"sto_billing_productivity\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"inventory_update_id\"],\n", "        \"sortkey\": [\"billing_time\", \"outlet_id\"],\n", "        \"incremental_key\": \"etl_timestamp_utc\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, upsert\n", "        \"table_description\": \"This tables maps every inventory update corresponding to B2B sto billing to the biller and the billing shift\",\n", "    }\n", "\n", "    pb.to_redshift(summ_final1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summ_final1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}