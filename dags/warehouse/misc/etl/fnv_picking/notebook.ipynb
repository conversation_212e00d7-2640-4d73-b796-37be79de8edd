{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datetime.now(tz).hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prepacked = pb.from_sheets(\"1CWRqw3454i5Q6x5PJlJkz5ZU6uJlPcj9sZTA5nqn4FM\", \"Prepacked\")\n", "dump = pb.from_sheets(\"1CWRqw3454i5Q6x5PJlJkz5ZU6uJlPcj9sZTA5nqn4FM\", \"Dump\")\n", "dump = dump[dump[\"Remarks\"] == \"Need to Dump\"]\n", "outlets = pb.from_sheets(\"1CWRqw3454i5Q6x5PJlJkz5ZU6uJlPcj9sZTA5nqn4FM\", \"Input\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "    SELECT \n", "           co.id as outlet_id,\n", "           co.name AS outlet_name,\n", "           imi.item_id,\n", "           concat(id.name,'-',pp.uom) AS item_name,\n", "           sum(case when im.scheduled_at between ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                    WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                            ELSE 30-minute(now())\n", "                                                END) MINUTE),  INTERVAL -second(now()) SECOND)) - INTERVAL 29 MINUTE - INTERVAL 59 SECOND) and \n", "                                                ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                   WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                   ELSE 30-minute(now())\n", "                                               END) MINUTE), INTERVAL - second(now()) SECOND)) )\n", "                                                then\n", "                                                imi.quantity else 0 end) AS current_slot,\n", "           sum(case when im.scheduled_at between ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                    WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                            ELSE 30-minute(now())\n", "                                                END) MINUTE),  INTERVAL -second(now()) SECOND)) + INTERVAL 1 SECOND) and \n", "                                                ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                   WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                   ELSE 30-minute(now())\n", "                                               END) MINUTE), INTERVAL - second(now()) SECOND)) + INTERVAL 30 MINUTE)\n", "                                                then\n", "                                                imi.quantity else 0 end) AS next_slot,\n", "            sum(case when im.scheduled_at between ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                   WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                   ELSE 30-minute(now())\n", "                                               END) MINUTE), INTERVAL - second(now()) SECOND)) + INTERVAL 30 MINUTE + INTERVAL 1 SECOND) and \n", "                                                ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                   WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                   ELSE 30-minute(now())\n", "                                               END) MINUTE), INTERVAL - second(now()) SECOND)) + INTERVAL 60 MINUTE)\n", "                                                then\n", "                                                imi.quantity else 0 end) AS next_2_next_slot\n", "    FROM ims.ims_order_details im\n", "    INNER JOIN ims.ims_order_items imi ON imi.order_details_id = im.id\n", "    INNER JOIN retail.console_outlet co ON co.id = im.outlet\n", "    AND co.business_type_id = 7\n", "    INNER JOIN\n", "      (SELECT DISTINCT item_id, max(variant_uom_text) as uom\n", "       FROM rpc.product_product\n", "       WHERE outlet_type = 1\n", "       group by 1) pp ON pp.item_id = imi.item_id\n", "    INNER JOIN rpc.item_details id ON id.item_id = imi.item_id\n", "    WHERE (im.scheduled_at) BETWEEN ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                    WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                            ELSE 30-minute(now())\n", "                                                END) MINUTE),  INTERVAL -second(now()) SECOND)) - INTERVAL 29 MINUTE - INTERVAL 59 SECOND) AND \n", "                                    ((DATE_ADD(DATE_ADD(now(), INTERVAL (CASE\n", "                                                   WHEN minute(now())>= 30 THEN 60-minute(now())\n", "                                                   ELSE 30-minute(now())\n", "                                               END) MINUTE), INTERVAL - second(now()) SECOND)) + INTERVAL 60 MINUTE)\n", "      AND im.outlet in ({outlet_ids})\n", "    GROUP BY 1,2,3,4\"\"\".format(\n", "    outlet_ids=\",\".join([str(x) for x in outlets.outlet_id.unique()])\n", ")\n", "main = pd.read_sql_query(sql=query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet in outlets[\"outlet_id\"].to_list():\n", "    print(outlet, datetime.now(tz))\n", "    to_sheet_id = outlets[outlets[\"outlet_id\"] == outlet][\"Sheet_id\"].unique()[0]\n", "    if datetime.now(tz).hour < 7:\n", "        sales_query = \"\"\"select \n", "            imi.item_id,\n", "            concat(id.name,'-',pp.uom) AS item_name,\n", "            sum(imi.quantity)/(case when DAYOFWEEK(date(now())) in (1,7) then 4.0 else 10.0 end) as qty\n", "            FROM ims.ims_order_details im\n", "        INNER JOIN ims.ims_order_items imi ON imi.order_details_id = im.id\n", "        INNER JOIN retail.console_outlet co ON co.id = im.outlet\n", "        AND co.business_type_id = 7\n", "        INNER JOIN\n", "          (SELECT DISTINCT item_id, max(variant_uom_text) as uom\n", "        FROM rpc.product_product\n", "        WHERE outlet_type = 1\n", "        group by 1) pp ON pp.item_id = imi.item_id\n", "        INNER JOIN rpc.item_details id ON id.item_id = imi.item_id\n", "        WHERE date(convert_tz(im.scheduled_at,\"+00:00\",\"+05:30\")) between current_date-14 and current_date-1\n", "        and (HOUR(convert_tz(im.created_at,\"+00:00\",\"+05:30\")) > 22 or HOUR(convert_tz(im.created_at,\"+00:00\",\"+05:30\")) < 8)\n", "        and im.outlet = %(outlet_id)s and im.status_id = 2\n", "        and (case when DAYOFWEEK(date(now())) in (1,7) then 1 else 0 end) = (case when DAYOFWEEK(date(convert_tz(im.scheduled_at,\"+00:00\",\"+05:30\"))) in (1,7) then 1 else 0 end)\n", "        group by 1,2\n", "        \"\"\"\n", "        data_prev = pd.read_sql_query(\n", "            sql=sales_query, con=retail, params={\"outlet_id\": outlet}\n", "        )\n", "        data_prev.qty = round(data_prev.qty)\n", "        data_prev = data_prev[data_prev[\"qty\"] > 0]\n", "        data_prev = data_prev.sort_values(by=[\"qty\"], ascending=False)\n", "        data_prev = data_prev[\n", "            (data_prev[\"qty\"] > 3)\n", "            & (\n", "                ~data_prev.item_id.isin(\n", "                    prepacked[prepacked[\"Remarks\"] == \"Prepacked\"][\"Item ID\"].to_list()\n", "                )\n", "            )\n", "        ]\n", "        default_pack = prepacked[~prepacked[\"Remarks\"].isin([\"Prepacked\", \"NA\"])][\n", "            [\"Item ID\", \"Item Name\"]\n", "        ].rename(columns={\"Item ID\": \"item_id\", \"Item Name\": \"item_name\"})\n", "        default_pack[\"qty\"] = 3\n", "        data_prev1 = pd.concat([data_prev, default_pack], axis=0, sort=False)\n", "    data = main[main[\"outlet_id\"] == int(outlet)].drop(columns={\"outlet_id\"})\n", "    data = data[\n", "        ~data.item_id.isin(\n", "            prepacked[prepacked[\"Remarks\"] == \"Prepacked\"][\"Item ID\"].to_list()\n", "        )\n", "    ]\n", "    data[\"outlet_name\"] = data[\"outlet_name\"].astype(str)\n", "    data[\"outlet_name\"] = data[\"outlet_name\"].map(\n", "        lambda x: x.lstrip(\"Super Store\")\n", "        .rstrip(\"(NM)\")\n", "        .rstrip(\"(CC)\")\n", "        .rstrip(\"(SSC)\")\n", "        .rstrip(\"(FNV)\")\n", "        .rstrip(\"(LA)\")\n", "    )\n", "    data[\"outlet_name\"] = data[\"outlet_name\"].map(lambda x: x.lstrip(\" \").rstrip(\" \"))\n", "    data1 = data[[\"item_name\", \"current_slot\", \"next_slot\", \"next_2_next_slot\"]]\n", "\n", "    # data.scheduled_at.min(),data.scheduled_at.max()\n", "\n", "    # data = data.sort_values(by = [\"scheduled_at\"], ascending = False)\n", "\n", "    # print(data.shape)\n", "    # for i in range(0, 999 - data.shape[0]):\n", "    #     data = data.append(pd.Series(), ignore_index=True)\n", "    # print(data.shape)\n", "\n", "    j = pd.date_range(\"00:00\", periods=48, freq=\"30min\", tz=\"Asia/Kolkata\")\n", "    x = pd.DataFrame(\n", "        [str(datetime.now(tz).strftime(\"%H:%M:%S\"))], columns=[\"item_name\"]\n", "    )\n", "    data2 = pd.concat([x, data1], sort=False).reset_index(drop=True)\n", "    y = pd.DataFrame(\n", "        [str(datetime.now(tz).strftime(\"%H:%M:%S\"))], columns=[\"outlet_name\"]\n", "    )\n", "    data = pd.concat([y, data], sort=False).reset_index(drop=True)\n", "\n", "    for i in range(0, len(j)):\n", "        if j[i] > datetime.now(tz):\n", "            # print(j[i - 1].strftime(\"%H:%M:%S\"))\n", "            data2.iloc[0, 1] = (\n", "                str(j[i - 1].strftime(\"%H:%M\")) + \"-\" + str(j[i].strftime(\"%H:%M\"))\n", "            )\n", "            data.iloc[0, 3] = (\n", "                str(j[i - 1].strftime(\"%H:%M\")) + \"-\" + str(j[i].strftime(\"%H:%M\"))\n", "            )\n", "            # print(j[i].strftime(\"%H:%M:%S\"))\n", "            data2.iloc[0, 2] = (\n", "                str(j[i].strftime(\"%H:%M\")) + \"-\" + str(j[i + 1].strftime(\"%H:%M\"))\n", "            )\n", "            data.iloc[0, 4] = (\n", "                str(j[i].strftime(\"%H:%M\")) + \"-\" + str(j[i + 1].strftime(\"%H:%M\"))\n", "            )\n", "            # print(j[i + 1].strftime(\"%H:%M:%S\"))\n", "            data2.iloc[0, 3] = (\n", "                str(j[i + 1].strftime(\"%H:%M\")) + \"-\" + str(j[i + 2].strftime(\"%H:%M\"))\n", "            )\n", "            data.iloc[0, 5] = (\n", "                str(j[i + 1].strftime(\"%H:%M\")) + \"-\" + str(j[i + 2].strftime(\"%H:%M\"))\n", "            )\n", "            break\n", "    import time\n", "\n", "    try:\n", "        pb.to_sheets(data2, to_sheet_id, \"Fnv_Data\")\n", "        pb.to_sheets(data, to_sheet_id, \"Backup\")\n", "        if datetime.now(tz).hour < 7:\n", "            pb.to_sheets(dump, to_sheet_id, \"dump\")\n", "            pb.to_sheets(data_prev1, to_sheet_id, \"morning_prepack\")\n", "    except:\n", "        print(\"failed...trying again 1\")\n", "        try:\n", "            time.sleep(15)\n", "            pb.to_sheets(data2, to_sheet_id, \"Fnv_Data\")\n", "            pb.to_sheets(data, to_sheet_id, \"Backup\")\n", "            if datetime.now(tz).hour < 7:\n", "                pb.to_sheets(dump, to_sheet_id, \"dump\")\n", "                pb.to_sheets(data_prev1, to_sheet_id, \"morning_prepack\")\n", "        except:\n", "            print(\"failed...trying again 2\")\n", "            try:\n", "                time.sleep(15)\n", "                pb.to_sheets(data2, to_sheet_id, \"Fnv_Data\")\n", "                pb.to_sheets(data, to_sheet_id, \"Backup\")\n", "                if datetime.now(tz).hour < 7:\n", "                    pb.to_sheets(dump, to_sheet_id, \"dump\")\n", "                    pb.to_sheets(data_prev1, to_sheet_id, \"morning_prepack\")\n", "            except:\n", "                print(\"Giving up !\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}