{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# FC_DIFF"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = (datetime.now(tz) - timed<PERSON>ta(days=11)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=5)).strftime(\"%Y-%m-%d\")\n", "print(\"Run on Mondays or change <PERSON><PERSON>ta\")\n", "print(start, end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query1 = \"\"\"\n", "WITH TEMP AS\n", "  (SELECT table1.*,\n", "          Mrp*quantity AS Total_mrp,\n", "          weighted_landing_price*quantity AS Total_landing_Price,\n", "          cost_price*quantity AS Total_cost_price,\n", "          (weighted_landing_price*quantity) - (cost_price*quantity) AS Tax\n", "   FROM\n", "     ( SELECT r.item_id AS item,\n", "              i.variant_id AS variant_id,\n", "              i.upc_id AS upc,\n", "              r.name AS Product,\n", "              (CASE\n", "                   WHEN r.outlet_type = 1 THEN 'F&V_type'\n", "                   WHEN r.outlet_type = 2 THEN 'Grocery_type'\n", "                   ELSE 'Not Assigned'\n", "               END) AS Item_Type,\n", "              SUM(i.\"delta\") AS quantity,\n", "              r.variant_mrp AS Mrp,\n", "              r.variant_uom_text AS grammage,\n", "              i.outlet_id AS outlet,\n", "              ro.name AS Outlet_name,\n", "              convert_timezone('Asia/Kolkata',i.pos_timestamp) AS POS_Timestamp,\n", "              i2.name AS Update_type,\n", "              ibur.name,\n", "              i.created_by_name AS created,\n", "              r.is_pl,\n", "              i.weighted_lp AS \"weighted_landing_price\",\n", "              coalesce(tax.cess,0) AS cess,\n", "              coalesce(tax.cgst,0) AS cgst,\n", "              coalesce(tax.sgst,0) AS sgst,\n", "              i.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) AS cost_price\n", "      FROM lake_ims.ims_inventory_log i\n", "      INNER JOIN lake_rpc.product_product r ON r.variant_id = i.variant_id\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "      INNER JOIN lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "      LEFT JOIN lake_rpc.product_tax tax ON r.item_id = tax.item_id\n", "      AND ro.tax_location_id = tax.location\n", "      LEFT JOIN lake_ims.ims_bad_inventory_update_log ibil ON i.inventory_update_id=ibil.inventory_update_id\n", "      LEFT JOIN lake_ims.ims_bad_update_reason ibur ON ibil.reason_id=ibur.id\n", "      WHERE i.inventory_update_type_id IN (11,\n", "                                           12,\n", "                                           13,\n", "                                           64,\n", "                                           87,\n", "                                           88,\n", "                                           89)\n", "        AND i.pos_timestamp::date BETWEEN %(start)s and %(end)s\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               7,\n", "               8,\n", "               9,\n", "               10,\n", "               11,\n", "               12,\n", "               13,\n", "               14,\n", "               15,\n", "               16,\n", "               17,\n", "               18,\n", "               19 ) table1 )\n", "SELECT co.facility_id,\n", "       fc.name AS facility_name,\n", "       update_type,\n", "       sum(quantity) AS quantity,\n", "       sum(Total_cost_price) AS cost_price_amount\n", "FROM TEMP t\n", "LEFT JOIN lake_retail.console_outlet co ON co.id=t.outlet\n", "LEFT JOIN lake_crates.facility fc ON co.facility_id=fc.id\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\"\n", "data_bs = pd.read_sql_query(\n", "    sql=Query1, con=redshift, params={\"start\": start, \"end\": end}\n", ")\n", "data_bs.sort_values(by=[\"cost_price_amount\"], ascending=False).head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff_bs_sum = data_bs.sum(axis=0)[\"cost_price_amount\"]\n", "diff_bs_sum = np.round(diff_bs_sum, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# New query\n", "Query2_new = \"\"\"\n", "with \n", "sto_billed_grn_raw as\n", "(\n", "SELECT distinct date(sto.created_at + interval '5.5 hour') sto_created_at,\n", "      pi.grofers_order_id as sto_id,\n", "      sto.outlet_id,\n", "      sto.merchant_outlet_id,\n", "      pi.invoice_id,\n", "      pi.transfer_state,\n", "  r.item_id AS Item_ID,\n", "  pipd.variant_id AS variant_id,\n", "  r.name AS Product,\n", "  (CASE\n", "      WHEN r.outlet_type = 1 THEN 'F&V type'\n", "    WHEN r.outlet_type = 2 THEN 'Grocery type'\n", "         ELSE 'Not Assigned'\n", "     END) AS Item_Type,\n", "  \n", "  case when i.inventory_update_type_id in (1,28) then  i.\"delta\" else 0 end AS GRN_Quantity,\n", "  (i.pos_timestamp + interval '5.5 hour') grn_timestamp,\n", "--   i.merchant_invoice_id AS Merchant_Invoice_ID,\n", "  (pi.pos_timestamp + interval '5.5 hour') invoice_creation,\n", "  pipd.quantity as billed_qty,\n", "  pipd.selling_price as billing_price,\n", "  lp.landing_price as hot_landing_price\n", "from \n", "            lake_pos.view_pos_invoice  pi\n", "  left join lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id \n", "  left join lake_rpc.product_product r ON r.variant_id = pipd.variant_id\n", "  LEFT JOIN (\n", "  select  * from lake_ims.ims_inventory_log where (pos_timestamp) >'2020-08-10' -- and inventory_update_type_id in (1,28)\n", "  ) i  on pi.invoice_id=i.merchant_invoice_id and pipd.variant_id::varchar=i.variant_id::varchar  \n", "  left join\n", "\tlake_pos.pos_invoice_product_lp lp on lp.invoice_id=pi.id and lp.variant_id::varchar=pipd.variant_id::varchar\n", "  left join lake_ims.ims_sto_details  sto on sto.sto_id = pi.grofers_order_id\n", "\n", "  WHERE \n", "  pi.invoice_type_id in (5,14,16)\n", "--   and pi.grofers_order_id= 20918\n", "--   and r.item_id =10005825\n", "-- and pi.invoice_id = 'BZC714T200001585'\n", "  and  (pi.pos_timestamp) >'2020-07-15'\n", "--   and  (i.pos_timestamp)  >'2020-07-15'\n", "  and date(sto.created_at + interval '5.5 hour') between %(start)s and %(end)s\n", "--   and pi.grofers_order_id in (select distinct sto_id from Sto)\n", "--   i.inventory_update_type_id IN (1,28,76,90,93)\n", "  \n", "--   and pi.outlet_id in (845,846,847)\n", "--   and i.\"delta\" > 0\n", "--   and iii.source_type = 2\n", "     \n", "),\n", "\n", "sto_billed_grn as\n", "(\n", "select distinct sto_created_at, outlet_id , merchant_outlet_id,variant_id,\n", "sbg.invoice_id,sbg.transfer_state,sbg.product,sbg.item_id,sbg.sto_id,\n", "(sbg.billed_qty) billed_qty,max(sbg.invoice_creation) invoice_creation,sum(sbg.grn_quantity) grn_quantity,\n", "max(sbg.grn_timestamp) grn_timestamp,sum(sbg.billing_price*billed_qty) billing_price, (sbg.billed_qty) -sum(sbg.grn_quantity) as diff_qty,\n", "(sbg.billing_price*billed_qty)-sum(sbg.billing_price*grn_quantity) as diff_amt,\n", "sbg.billing_price as billing_unit_price, sbg.hot_landing_price\n", "from sto_billed_grn_raw sbg\n", "group by 1,2,3,4,5,6,7,8,9,10,17,18\n", "),\n", "\n", "-- discrepancy as\n", "-- (\n", "-- select created_at,vendor_invoice_id,net_amount,outlet_id,\n", "-- upc_id,name,cost_price,landing_price,item_id,variant_id, sum(quantity) as disc_qty\n", "-- from(\n", "-- select dn.id,(dn.created_at + interval '5.5 hour') created_at,dn.vendor_name,dn.vendor_invoice_id,dn.net_amount,dn.outlet_id,\n", "-- dnpd.upc_id,dnpd.name,dnpd.quantity,dnpd.cost_price,dnpd.landing_price,dnpd.remark,\n", "-- pp.item_id , pp.variant_id\n", "-- from  lake_pos.discrepancy_note   dn \n", "-- left join lake_pos.discrepancy_note_product_detail dnpd on dn.id=dnpd.dn_id_id\n", "-- left join (select distinct item_id,upc,variant_id from  lake_rpc.product_product ) pp on  dnpd.upc_id=pp.upc\n", "-- where (created_at)>'2020-08-15'\n", "-- -- and vendor_invoice_id='BGC100T200000622'\n", "-- ) temp\n", "-- group by 1,2,3,4,5,6,7,8,9,10),\n", "\n", "return_b2b as\n", "(\n", "select tab1.original_invoice_id,tab1.item_id,tab1.variant_id,\n", "sum(tab1.b2b_return_qty) as b2b_return_qty,\n", "sum(tab2.b2b_customer_return) b2breturn_good,\n", "sum(tab2.b2b_customer_return_expired) b2breturn_expired,\n", "sum(tab2.b2b_customer_return_damaged) b2breturn_damaged,\n", "sum(tab2.b2b_customer_return_near_expiry) b2breturn_near_expiry,\n", "sum(tab2.lost_in_transit_positive)  lost_in_transit_positive,\n", "sum(tab2.lost_in_transit_negative) lost_in_transit_negative\n", "\n", "from\n", "(\n", "select pi.invoice_id ret_invoice_id, pi.original_invoice_id,pp.item_id,pipd.variant_id,pipd.quantity  b2b_return_qty from \n", "lake_pos.view_pos_invoice  pi \n", "inner join lake_pos.view_pos_invoice pi2  on pi2.invoice_id=pi.original_invoice_id\n", "left join lake_pos.pos_invoice_product_details pipd on pi.id=pipd.invoice_id\n", "left join lake_rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "where pi.invoice_type_id in (8,15)\n", "and   pi2.invoice_type_id in (5,14,16)\n", "and  (pi.pos_timestamp) > '2020-08-15' \n", "-- and pi.invoice_id='BGC340U200000134'\n", "\n", ")tab1\n", "left join \n", "(\n", "select invoice_id as ret_invoice_id, item_id, variant_id,\n", "sum(case  when update_type_id=23 then ret_qty end) as b2b_customer_return,\n", "sum(case  when update_type_id=24 then ret_qty end) as b2b_customer_return_expired,\n", "sum(case  when update_type_id=25 then ret_qty end) as b2b_customer_return_damaged,\n", "sum(case  when update_type_id=66 then ret_qty end) as b2b_customer_return_near_expiry,\n", "sum(case  when update_type_id=119 then ret_qty end) as lost_in_transit_positive,\n", "sum(case  when update_type_id=120 then ret_qty end) as lost_in_transit_negative,\n", "sum(ret_qty) as tot_ret_qty from\n", "(select iil.invoice_id,iil.merchant_invoice_id, iil.\"delta\" as ret_qty,iut.id as update_type_id,iut.name as update_type,pp.item_id , iil.variant_id\n", "from\n", "lake_ims.ims_inventory_log iil \n", "left join lake_ims.ims_inventory_update_type iut on iil.inventory_update_type_id=iut.id\n", "left join lake_rpc.product_product pp ON iil.variant_id = pp.variant_id\n", "where iil.pos_timestamp>'2020-08-15' \n", "\n", "-- and invoice_id='BGC340U200000134'\n", ") temp\n", "group by 1,2,3\n", ")tab2\n", "on tab1.ret_invoice_id=tab2.ret_invoice_id and tab1.item_id=tab2.item_id and tab1.variant_id = tab2.variant_id\n", "-- where  tab1.ret_invoice_id='BGC340U200000134'\n", "group by 1,2,3),\n", "\n", "final as\n", "(\n", " select sbg.sto_id,sbg.outlet_id,ozm1.outlet_name as sender_outlet,sbg.merchant_outlet_id,ozm2.outlet_name as rec_outlet,\n", " sbg.sto_created_at,sbg.item_id,sbg.billing_unit_price,hot_landing_price, sbg.variant_id,\n", " sbg.invoice_id,sbg.transfer_state,sbg.product,sbg.billed_qty,sbg.invoice_creation,sbg.grn_quantity,sbg.grn_timestamp,sbg.billing_price,\n", " sbg.diff_qty diff_qty_billed_grn, sbg.diff_amt diff_amt_billed_grn,--COALESCE(dis.disc_qty,0) as discrepancy_qty,\n", "--  COALESCE(b2b.b2b_return_qty,0) as b2b_return_qty\n", "COALESCE(b2b.b2b_return_qty,0)  as tot_returned,\n", "COALESCE(b2b.b2breturn_good,0)  as b2breturn_good,\n", "COALESCE(b2b.b2breturn_expired,0)  as b2breturn_expired,\n", "COALESCE(b2b.b2breturn_damaged,0)  as b2breturn_damaged,\n", "COALESCE(b2b.b2breturn_near_expiry,0) as b2breturn_near_expiry,\n", "COALESCE(b2b.lost_in_transit_positive,0) as lost_in_transit_positive,\n", "COALESCE(b2b.lost_in_transit_negative,0) as lost_in_transit_negative\n", "\n", "from  sto_billed_grn sbg \n", " left join metrics.outlet_zone_mapping ozm1 on ozm1.outlet_id=sbg.outlet_id\n", " left join metrics.outlet_zone_mapping ozm2 on ozm2.outlet_id=sbg.merchant_outlet_id\n", "--  left join discrepancy dis on dis.vendor_invoice_id= sbg.invoice_id and dis.item_id=sbg.item_id and dis.variant_id = sbg.variant_id\n", " left join return_b2b b2b on b2b.original_invoice_id= sbg.invoice_id and b2b.item_id=sbg.item_id and b2b.variant_id = sbg.variant_id\n", "\n", ")\n", "\n", "-- select sum(esto_diff) as diff, sum(b2b) as b2b from (\n", "select sto_created_at,\n", "        outlet_id,\n", "        sender_outlet,\n", "        merchant_outlet_id,\n", "        rec_outlet,\n", "        Invoice_State,\n", "        sum(hot_landing_price*tot_returned) as b2b,\n", "        sum(diff_qty) as esto_qnty_diff,\n", "        sum(diff_amt) as esto_diff\n", "        from (\n", "select distinct\n", "sto_id, outlet_id, sender_outlet,\n", "        merchant_outlet_id, rec_outlet, sto_created_at,grn_timestamp,\n", "       item_id, variant_id, product, invoice_id,invoice_creation,billing_unit_price, hot_landing_price,\n", "               CASE\n", "            WHEN transfer_state = 1 THEN 'Raised'\n", "            WHEN transfer_state = 2 THEN 'GRN Complete'\n", "            WHEN transfer_state = 3 THEN 'GRN Partial'\n", "            WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "            ELSE 'NA'\n", "        END AS Invoice_State,\n", "        billing_price,billed_qty,grn_quantity,--discrepancy_qty::int,\n", "        tot_returned,b2breturn_good,b2breturn_expired,b2breturn_damaged,b2breturn_near_expiry,lost_in_transit_negative ,diff_qty_billed_grn,\n", "        case when (billed_qty-grn_quantity-tot_returned) < 0 then 0 else (billed_qty-grn_quantity-tot_returned) end diff_qty,\n", "        diff_amt_billed_grn,\n", "        case when ( billed_qty-grn_quantity-tot_returned)*hot_landing_price < 0 then 0 else ( billed_qty-grn_quantity-tot_returned)*hot_landing_price end as diff_amt\n", " from final  where billed_qty>0\n", "-- and invoice_id = 'BBM467T200000406'\n", "-- and sto_id = 69525\n", "-- and item_id = 10044053\n", ") a\n", "where Invoice_State not in ('Raised','NA')\n", "group by 1,2,3,4,5,6\n", "-- )      \n", "\"\"\"\n", "data_esto_summ = pd.read_sql_query(\n", "    sql=Query2_new, con=redshift, params={\"start\": start, \"end\": end}\n", ")\n", "data_esto_summ.sort_values(by=[\"esto_diff\"], ascending=False).head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_esto_summ_sum = data_esto_summ.sum(axis=0)[\"esto_diff\"]\n", "data_esto_summ_sum = np.round(data_esto_summ_sum, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query3 = \"\"\"\n", "With Temp as (\n", "SELECT\n", "\tiil.outlet_id as outletid,\n", "\to.name as Store<PERSON>ame,\n", "\tp.item_id as Item_Id,\n", "\tiil.upc_id as UPC_Id,\n", "\tp.name as Product_Name,\n", "\t(CASE\n", "    WHEN p.outlet_type = 1 THEN 'F&V type'\n", "\tWHEN p.outlet_type = 2 THEN 'Grocery type'\n", "    ELSE 'Not Assigned'\n", "    END) AS Item_Type,\n", "\tp.variant_mrp as <PERSON><PERSON><PERSON>_MR<PERSON>,\n", "\tp.variant_uom_text as <PERSON><PERSON>,\n", "\tconvert_timezone('Asia/Kolkata',iil.pos_timestamp) AS POS_Timestamp,\n", "\tiiut.inventory_operation as Operation,\n", "\tiil.\"delta\" as Change_Quantity,\n", "\tiil.quantity as Final_Quantity,\n", "\tiiut.name as Inventory_Update_Type,\n", "\tiil.created_by_name as Created_By_Name,\n", "\tiil.created_by_phone as Created_By_Phone,\n", "\tau.email as Store_Manager_Email_id,\n", "\tiil.transaction_lp as hot_landing_price,\n", "\tiil.weighted_lp/(1+((COALESCE(tax.cgst,0)+COALESCE(tax.sgst,0)+COALESCE(tax.cess,0))/100)) as cost_price\n", "FROM \n", "\tlake_ims.ims_inventory_log iil\n", "INNER JOIN\n", "\tlake_rpc.product_product p ON iil.variant_id=p.variant_id\n", "INNER JOIN\n", "\tlake_ims.ims_inventory_update_type iiut ON iiut.id=iil.inventory_update_type_id\n", "INNER JOIN\n", "\tlake_retail.console_outlet o ON o.id=iil.outlet_id\n", "INNER JOIN\n", "\tlake_finance.auth_user au ON iil.created_by=au.id\n", "\tleft join lake_rpc.product_tax tax ON p.item_id = tax.item_id and o.tax_location_id = tax.location\n", "WHERE iil.inventory_update_type_id IN (14,15,39,40,41,42,43,44,45,46,47,48)\n", "AND iil.pos_timestamp::date between %(start)s and %(end)s\n", ") \n", "\n", "<PERSON> t.outletid,\n", "       co.name as outlet_name,\n", "       co.facility_id,\n", "       fc.name as facility_name,\n", "       operation,\n", "       inventory_update_type,\n", "       sum(change_quantity) as Quantity,\n", "       sum(change_quantity*cost_price) as Amount\n", "from Temp t left join lake_retail.console_outlet co on co.id=t.outletid\n", "left join lake_crates.facility fc on co.facility_id=fc.id\n", "where outletid not in \n", "                    (773, 947, 1015, 616, 945, 920, 670, 949, 951, 942, 712, 943, 1028, 758, 709, 950, 1008, 598, 983, 929, 748, 1007, 939, 934, 933, 932, 931, 930, 938, 999, 1027, \n", "                    902, 967, 1012, 946, 954, 977, 850, 955, 919, 849, 952, 936, 924, 984, 1019, 998, 990, 922, 1098, 975, 1077, 1014, 1066, 1067, 1065, 1064, 1068, 1069,\n", "                    326, 388, 588, 599, 600, 668, 693, 719, 776, 782, 783, 785, 966, 970, 997, 1021)\n", "group by 1,2,3,4,5,6\n", "        \"\"\"\n", "data_man = pd.read_sql_query(\n", "    sql=Query3, con=redshift, params={\"start\": start, \"end\": end}\n", ")\n", "data_man.sort_values(by=[\"amount\"], ascending=False).inventory_update_type.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_man_breq_pos = data_man[\n", "    data_man[\"inventory_update_type\"] == \"business_request_positive\"\n", "]\n", "data_man_breq_neg = data_man[\n", "    data_man[\"inventory_update_type\"] == \"business_request_negative\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_man_breq = (\n", "    data_man_breq_pos.sum(axis=0)[\"amount\"] - data_man_breq_neg.sum(axis=0)[\"amount\"]\n", ")\n", "data_man_sum_pos = data_man[data_man[\"operation\"] == \"+\"].sum(axis=0)[\"amount\"]\n", "data_man_sum_neg = data_man[data_man[\"operation\"] == \"-\"].sum(axis=0)[\"amount\"]\n", "man_diff = data_man_sum_pos - data_man_sum_neg - data_man_breq\n", "man_diff = np.round(man_diff, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"BAD STOCK DIFF:\", diff_bs_sum)\n", "print(\"ESTO DIFF:\", data_esto_summ_sum)\n", "print(\"Manual Diff:\", man_diff)\n", "print(\"FC_DIFF:\", round(diff_bs_sum + data_esto_summ_sum + man_diff, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Adoption and Time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = (datetime.now(tz) - timed<PERSON>ta(days=11)).strftime(\"%Y-%m-%d\")\n", "start1 = (datetime.now(tz) - timedelta(days=16)).strftime(\"%Y-%m-%d\")\n", "end1 = (datetime.now(tz) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=5)).strftime(\"%Y-%m-%d\")\n", "start, end, start1, end1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_period_start = start\n", "run_period_end = end\n", "redshift_con = pb.get_connection(\"redshift\")\n", "\n", "ReturnsBase = \"\"\"\n", "SELECT sub_order_id,\n", "       l1_reason,\n", "       max(inward_date) as inward_date,\n", "       sum(inwardedvalue) as inwardedvalue,\n", "       sum(expectedvalue) as expectedvalue\n", "from metrics.expectedreturnsuniverse\n", "where scheduleddate between %(start)s and %(end)s \n", "group by 1,2\n", "\"\"\"\n", "Returns = pd.read_sql_query(\n", "    sql=ReturnsBase,\n", "    con=redshift_con,\n", "    params={\"start\": run_period_start, \"end\": run_period_end},\n", ")\n", "Returns.shape\n", "WithOrderId_Returns = \"\"\"\n", "With base as (\n", "select 'with_oid' as  type, \n", "rtoi.order_id, \n", "max(rtsl.created_at) as taskcreatedtime,\n", "listagg(distinct rtsl.return_task_id,',') as return_tasks, \n", "avg(rtoi.quantity) as expected_quantity, \n", "avg(rtoi.map_quantity) map_quantity\n", "from lake_warehouse_location.return_task_state_log rtsl\n", "join lake_warehouse_location.return_task rt on rt.id=rtsl.return_task_id\n", "join lake_warehouse_location.return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  lake_ims.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join  lake_pos.view_pos_invoice pos on pos.grofers_order_id= rtoi.order_id and invoice_type_id=1\n", "where \n", "rtsl.state=1\n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=%(start)s and convert_timezone('Asia/Kolkata',rtsl.created_at)<=%(end)s\n", "and len(trim(rtoi.order_id))>5 and rtoi.order_id is not null and rtoi.order_id<>'' and rtoi.order_id <> 'None' and rtoi.order_id<>' '\n", "group by 1,2\n", "),\n", "Received as (\n", "Select rtoi.order_id as order1, \n", "max(rtsl.created_at) as taskreceivedtime\n", "from lake_warehouse_location.return_task_state_log rtsl\n", "join lake_warehouse_location.return_task rt on rt.id=rtsl.return_task_id\n", "join lake_warehouse_location.return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "where \n", "rtsl.state=5\n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=%(start)s and  convert_timezone('Asia/Kolkata',rtsl.created_at)<=%(end)s\n", "and len(trim(rtoi.order_id))>5 and rtoi.order_id is not null and rtoi.order_id<>'' and rtoi.order_id <> 'None' and rtoi.order_id<>' '\n", "group by 1\n", "),\n", "Completed as (\n", "Select \n", "rtoi.order_id as order2, \n", "max(rtsl.created_at) as taskcompletedtime\n", "from lake_warehouse_location.return_task_state_log rtsl\n", "join lake_warehouse_location.return_task rt on rt.id=rtsl.return_task_id\n", "join lake_warehouse_location.return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "where rtsl.state=7\n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=%(start)s and  convert_timezone('Asia/Kolkata',rtsl.created_at)<=%(end)s \n", "and len(trim(rtoi.order_id))>5 and rtoi.order_id is not null and rtoi.order_id<>'' and rtoi.order_id <> 'None' and rtoi.order_id<>' '\n", "group by 1\n", ")\n", "Select * from base left join Received on base.order_id=Received.order1\n", "left join Completed on base.order_id=Completed.order2\n", "\"\"\"\n", "WithOrder = pd.read_sql_query(\n", "    sql=WithOrderId_Returns, con=redshift_con, params={\"start\": start1, \"end\": end1}\n", ")\n", "WithOrder.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithOrder1 = WithOrder[\n", "    [\"order_id\", \"taskcreatedtime\", \"taskreceivedtime\", \"taskcompletedtime\"]\n", "]\n", "FinalReturns = pd.merge(\n", "    Returns, WithOrder1, how=\"left\", left_on=\"sub_order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Adoption = len(FinalReturns.order_id.unique()) / len(FinalReturns.sub_order_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FinalReturns[\"TimeDiff_CreatedtoDock\"] = (\n", "    (FinalReturns[\"taskreceivedtime\"] - FinalReturns[\"taskcreatedtime\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)\n", "FinalReturns[\"TimeDiff_DocktoInward\"] = (\n", "    (FinalReturns[\"taskcompletedtime\"] - FinalReturns[\"taskreceivedtime\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)\n", "FinalReturns[\"TimeDiff_CreatedtoComplete\"] = (\n", "    (FinalReturns[\"taskcompletedtime\"] - FinalReturns[\"taskcreatedtime\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    \"TimeDiff_CreatedtoDock:\",\n", "    np.nanpercentile(FinalReturns[\"TimeDiff_CreatedtoDock\"], 90),\n", ")\n", "print(\n", "    \"TimeDiff_DocktoInward\", np.nanpercentile(FinalReturns[\"TimeDiff_DocktoInward\"], 90)\n", ")\n", "print(\n", "    \"TimeDiff_CreatedtoComplete\",\n", "    np.nanpercentile(FinalReturns[\"TimeDiff_CreatedtoComplete\"], 90),\n", ")\n", "print(\"Adoption\", round(Adoption * 100, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# FC fillrate attribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "WITH facilitylevel AS\n", "  (SELECT facility_id,\n", "          facilityname,\n", "          outlet_name,\n", "          outlet_id,\n", "          business_type,\n", "          count(DISTINCT order_id) AS Delivered_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1 THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1\n", "                                  AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders_WH,\n", "          (1-(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1 THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/count(DISTINCT order_id)::float))*100 AS Overall_FillRate,\n", "          (1-(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1\n", "                                      AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/count(DISTINCT order_id)::float))*100 AS WH_FillRate\n", "   FROM\n", "     (SELECT \n", "            mf.slot_end::date AS ScheduledDate,\n", "            mf.facility_id facility_id,\n", "            mf.facilityname facilityname,\n", "            mf.outlet_name outlet_name,\n", "            mf.outlet_id outlet_id,\n", "            co.business_type_id business_type,\n", "            mf.order_id order_id,\n", "            mf.item_id item_id,\n", "            mf.reason_key reason_key,\n", "            max(mf.unfulfilled_order) AS Unfulfilled\n", "      FROM metrics.fillrateattribution mf\n", "      left join lake_retail.console_outlet co on (co.id = mf.outlet_id)\n", "      WHERE slot_end::date BETWEEN %(start)s AND %(end)s \n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9)\n", "   WHERE facilityname IS NOT NULL\n", "     AND facilityname!=''\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5),\n", "L1_Reason AS\n", "  (SELECT facility_id,\n", "          outlet_id,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Reprocure_Count,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Reprocure,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Checkout_Count,      \n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Checkout,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Warehouse,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Warehouse_Count\n", "  FROM\n", "     (SELECT fra.slot_end::date AS ScheduledDate,\n", "             facility_id,\n", "             facilityname,\n", "             outlet_id,\n", "             outlet_name,\n", "             order_id,\n", "            --  suborder_id,\n", "            --  item_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "      FROM metrics.fillrateattribution fra\n", "      LEFT join lake_oms_bifrost.oms_order oo ON oo.id = fra.order_id\n", "      INNER join lake_oms_bifrost.oms_merchant om ON oo.merchant_id=om.id\n", "      WHERE fra.slot_end::date BETWEEN %(start)s AND %(end)s\n", "        AND reason_key IS NOT NULL\n", "        AND reason_key!=''\n", "      GROUP BY 1,\n", "              2,\n", "              3,\n", "              4,\n", "              5,\n", "              6,\n", "              7)\n", "  GROUP BY 1,2),\n", "   \n", "L2_Reason as \n", "(\n", "SELECT facility_id,\n", "      outlet_id,\n", "      sum(CASE\n", "      WHEN Reason = 'FNV' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Fnv,\n", "      sum(CASE\n", "      WHEN Reason = 'Metering' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN Reason = 'Nayala' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE\n", "      WHEN Reason = 'Reschedule' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Resche<PERSON>le,\n", "      sum(CASE\n", "      WHEN Reason = 'Bad Stock' THEN unfulfilled\n", "      ELSE 0\n", "      END) as BadStock,\n", "      sum(CASE\n", "      WHEN Reason = 'Edit Cart' THEN unfulfilled\n", "      ELSE 0\n", "      END) as <PERSON><PERSON><PERSON>,\n", "      sum(CASE WHEN Reason = 'Others' THEN unfulfilled\n", "      ELSE 0\n", "      END) as Others\n", "    --  sum(CASE\n", "    -- WHEN Reason = 'FNV' THEN unfulfilled\n", "    --   ELSE 0\n", "    --   END)::float/sum(unfulfilled)::float*100 AS FNV,\n", "    --       sum(CASE\n", "    --               WHEN Reason = 'Metering' THEN unfulfilled\n", "    --               ELSE 0\n", "    --           END)::float/sum(unfulfilled)::float*100 AS Metering,\n", "    --       sum(CASE\n", "    --               WHEN Reason = 'Nayala' THEN unfulfilled\n", "    --               ELSE 0\n", "    --           END)::float/sum(unfulfilled)::float*100 AS Nayala,\n", "    --       sum(CASE\n", "    --               WHEN Reason = 'Reschedule' THEN unfulfilled\n", "    --               ELSE 0\n", "    --           END)::float/sum(unfulfilled)::float*100 AS Reschedule,\n", "    --       sum(CASE\n", "    --               WHEN Reason = 'Others' THEN unfulfilled\n", "    --               ELSE 0\n", "    --           END)::float/sum(unfulfilled)::float*100 AS Others\n", "  From (\n", "  SELECT fra.slot_end::date AS ScheduledDate,\n", "             facility_id,\n", "             facilityname,\n", "             outlet_id,\n", "             order_id,\n", "             outlet_name,\n", "             suborder_id,\n", "             item_id,\n", "             reason_key,\n", "             case when outlet_type=1 then 'FNV' \n", "                  when upper_limit>0 then 'Metering'\n", "                  when description = 'Reprocure' then 'Reschedule'\n", "                  when description = 'Created' then 'Nayala'\n", "                  when edittime>picking_time then 'Edit Cart'\n", "                  when sum>0 then 'Bad Stock'\n", "                 -- when available_quantity>0 then 'Inventory Sanity'\n", "                  else 'Others' end as Reason,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "      FROM metrics.fillrateattribution fra\n", "      LEFT join lake_oms_bifrost.oms_order oo ON oo.id = fra.order_id\n", "      INNER join lake_oms_bifrost.oms_merchant om ON oo.merchant_id=om.id\n", "      WHERE fra.slot_end::date BETWEEN %(start)s AND %(end)s\n", "        AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING'\n", "      GROUP BY 1,\n", "              2,\n", "              3,\n", "              4,\n", "              5,\n", "              6,\n", "              7,\n", "              8,\n", "              9,\n", "              10) Group by 1,2\n", "        )       \n", "SELECT distinct fl.*,\n", "      reprocure,\n", "      checkout,\n", "      warehouse,\n", "      reprocure_count,\n", "      checkout_count,\n", "      warehouse_count,\n", "      FNV,\n", "      Metering,\n", "      <PERSON><PERSON><PERSON>,\n", "      Reschedule,\n", "      BadStock,\n", "      Edit<PERSON><PERSON>,\n", "      Others\n", "FROM facilitylevel fl\n", "LEFT JOIN L1_Reason l1 ON fl.facility_id=l1.facility_id and fl.outlet_id=l1.outlet_id\n", "LEFT JOIN L2_Reason l2 on fl.facility_id = l2.facility_id and  fl.outlet_id=l2.outlet_id\n", "\"\"\"\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = (datetime.now(tz) - timed<PERSON>ta(days=11)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=5)).strftime(\"%Y-%m-%d\")\n", "start_1 = (datetime.now(tz) - timed<PERSON>ta(days=4)).strftime(\"%Y-%m-%d\")\n", "end_1 = (datetime.now(tz) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "print(\"Run on Mondays or change <PERSON><PERSON>ta\")\n", "print(start, end)\n", "print(start_1, end_1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(sql=Query, con=redshift, params={\"start\": start, \"end\": end})\n", "data_1 = pd.read_sql_query(\n", "    sql=Query, con=redshift, params={\"start\": start_1, \"end\": end_1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fillrate_metric(data):\n", "    delivered_orders = data.sum(axis=0)[\"delivered_orders\"]\n", "    total_unfulfilled = data.sum(axis=0)[\"unfulfilled_orders\"]\n", "    data_filter = data[data[\"business_type\"].isin([1, 2, 3, 4])]\n", "    data_filter_dark = data[data[\"business_type\"].isin([7, 8])]\n", "    data_filter_other = data[data[\"business_type\"].isin([5, 6, 9, 10, 11, 12, 13, 14])]\n", "    total_delivered_dark = data_filter_dark.sum(axis=0)[\"delivered_orders\"]\n", "    due_to_DS_FL_3PL = data_filter_dark.sum(axis=0)[\"unfulfilled_orders\"]\n", "    due_to_WH = data_filter.sum(axis=0)[\"unfulfilled_orders_wh\"]\n", "    due_Checkout = data_filter.sum(axis=0)[\"checkout_count\"]\n", "    due_Reprocure = data_filter.sum(axis=0)[\"reprocure_count\"]\n", "    # print(due_to_DS_FL_3PL,due_to_WH,due_Checkout,due_Reprocure,total_delivered_dark)\n", "    FC_unful = total_unfulfilled / delivered_orders\n", "    Checkout = due_Checkout / delivered_orders\n", "    warehouse = due_to_WH / delivered_orders\n", "    DSFS = due_to_DS_FL_3PL / delivered_orders\n", "    abs_DSFS = due_to_DS_FL_3PL / total_delivered_dark\n", "    reshc = due_Reprocure / delivered_orders\n", "    print(\n", "        \"FC (warehouse) unfulfilment:\",\n", "        round(FC_unful * 100, 2),\n", "        \"\\nMissed at Checkout:\",\n", "        round(Checkout * 100, 2),\n", "        \"\\nMissed at warehouse:\",\n", "        round(warehouse * 100, 2),\n", "        \"\\nMissed at DS/FS:\",\n", "        round(DSFS * 100, 2),\n", "        \"\\nAbs fill rate DS/FS:\",\n", "        round(abs_DSFS * 100, 2),\n", "        \"\\nMiss due to rescheduling:\",\n", "        round(reshc * 100, 2),\n", "    )\n", "    return \"------\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["<b>Business type mapping:</b>  \n", "1\t-> Grocery  \n", "2\t-> Cold  \n", "3\t-> Large  \n", "4\t-> F&V  \n", "5\t-> Infra  \n", "6\t-> Liquidation  \n", "7\t-> Dark Store - Grofers  \n", "8\t-> Dark Store - External  \n", "9\t-> Dropshipment  \n", "10\t-> B2B Offline  \n", "11\t-> B2B Franchise  \n", "12\t-> B2B_Bulk  \n", "13\t-> Marketplace  \n", "14\t-> Test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(fillrate_metric(data))\n", "print(fillrate_metric(data_1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# O2P, P2P & P2D"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_o2p = \"\"\"\n", "WITH BASE_TABLE AS\n", "(\n", "SELECT DISTINCT\n", "    OM.NAME AS MERCHANT,\n", "    OS.ID AS suborder_id,\n", "    OS.checkout_time\n", "\n", "FROM \n", "(\n", "SELECT DISTINCT id , ORDER_ID, convert_timezone('Asia/Kolkata', INSTALL_TS) as checkout_time \n", "\n", "from lake_oms_bifrost.oms_suborder WHERE DATE(convert_timezone('Asia/Kolkata', INSTALL_TS))  between %(start_date)s and %(end_date)s AND TYPE = 'RetailSuborder'\n", "      AND CURRENT_STATUS IN ('AT_DELIVERY_CENTER','BILLED')\n", "    \n", ") AS OS\n", "\n", "INNER join lake_oms_bifrost.oms_order AS OO ON OS.ORDER_ID = OO.ID AND OO.TYPE IN ('RetailForwardOrder','InternalForwardOrder') AND OO.CURRENT_STATUS = 'DELIVERED'\n", "\n", "INNER join lake_oms_bifrost.oms_merchant AS OM ON OO.MERCHANT_ID = OM.ID AND  OM.VIRTUAL_MERCHANT_TYPE = 'superstore_merchant'\n", "),\n", "\n", "\n", "picker_assignment as ( select BASE_TABLE.*, max(install_ts + interval '5.5 hours') as picker_assignment_time\n", "         \n", "from BASE_TABLE\n", "left join (Select * from lake_oms_bifrost.oms_suborder_event where DATE(convert_timezone('Asia/Kolkata', INSTALL_TS)) between date(%(start_date)s::date + interval '-7 days') \n", "and date(%(end_date)s::date + interval '14 days')\n", ")soe\n", "on BASE_TABLE.suborder_id = soe.suborder_id and event_type_key = 'retail_picker_assignment'\n", "where install_ts is not null\n", "group by 1,2,3\n", "\n", "),\n", "\n", "-- select *, datediff(mins, checkout_time, picker_assignment_time) from picker_assignment\n", "\n", "-- select * from lake_oms_bifrost.oms_suborder_event where suborder_id = 93639410\n", "-- select '{{start_date}}'::date + interval '-7 days' , '{{end_date}}'::date + interval '7 days'\n", "\n", "abc as (\n", " select   MERCHANT,suborder_id,\n", "datediff(mins, checkout_time, picker_assignment_time)::float as \"O2A\"\n", "from picker_assignment\n", "),\n", "\n", "\n", "percentile_O2A_l as ( select \n", "                  PERCENTILE_DISC ( 0.90 )\n", "                  WITHIN GROUP (ORDER BY O2A asc) \n", "                --   OVER ( PARTITION BY MERCHANT ) as percentile\n", "                 OVER (  ) as percentile\n", "                  from abc\n", "                   \n", "\n", "),\n", "\n", "\n", "\n", "percentile_O2A_total as ( select 'Total' as \"Outlet\",\n", "                                percentile/60 as \"Percentile\"\n", "                             from percentile_O2A_l\n", "                                                group by 1,2\n", "\n", "\n", ")\n", "\n", "select * from percentile_O2A_total\n", "\"\"\"\n", "o2p = pd.read_sql_query(\n", "    sql=query_o2p, con=redshift, params={\"start_date\": start, \"end_date\": end}\n", ")\n", "o2p_1 = pd.read_sql_query(\n", "    sql=query_o2p, con=redshift, params={\"start_date\": start_1, \"end_date\": end_1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_p2p_p2d = \"\"\"\n", "With logs_l as  \n", "(select  \n", "\t\t\to.name, \n", "\t\t\tbom.batch_id as order_batch_id,\n", "\t\t\tpom.order_id, \n", "\t\t\tp.pick_list_id,\n", "\t\t\tp.id,\n", "\t\t    case when pl.picklist_state = 2 then (pl.pos_timestamp ) end as Assignment_time,\n", "\t        case when pl.picklist_state = 4 then (pl.pos_timestamp ) end as Complete_Time\n", "\t\t\n", "from \t\tlake_warehouse_location.warehouse_pick_list p \n", "inner join \tlake_warehouse_location.pick_list_log pl on p.id = pl.pick_list_id\n", "inner join \tlake_warehouse_location.warehouse_pick_list_order_mapping pom on p.id = pom.pick_list_id\n", "inner join \tlake_retail.console_outlet o on p.outlet_id = o.id\n", "left join lake_warehouse_location.batch_order_mapping bom on pom.order_id = bom.order_id\n", "where \t\tp.type = 1\n", "and \t\tp.state in (10,4)\n", "and \t\tdate(p.min_scheduled_time + interval '5.5 hours') between %(start_date)s and %(end_date)s\n", "\n", "),\n", "\n", "log_fin as ( select \tname,\n", "\t\t\t            order_id,\n", "\t\t                pick_list_id,\n", "\t\t\t            id,\n", "\t\t\t            order_batch_id,\n", "\t\t\t           sum(date_part(epoch,Assignment_time)) as Assignment_time,\n", "\t\t\t          sum(date_part(epoch,Complete_Time)) as Complete_Time\n", "\t\t\tfrom logs_l\n", "\t\t\tgroup by 1,2,3,4,5\n", "\n", "),\n", "\n", "\n", "\n", "P2B_l as (select \tname,\n", "\t         \torder_id,\n", "\t     \tpick_list_id,\n", "       \t \tAssignment_time,\n", "\t    \t(timestamp 'epoch' +  Complete_Time * interval '1 second') as Complete_Time_v,\n", "\t      \tid ,\n", "\t      \torder_batch_id,\n", "       \t   datediff(mins,timestamp 'epoch' + Assignment_time * interval '1 second',timestamp 'epoch' +  Complete_Time * interval '1 second')  as \"A2C\"\n", "\t   from \n", "            log_fin \n", "            \n", "\t   ),\n", "\n", "-- dispatch_expected as (\n", "--   select  distinct suborder_id,\n", "          \n", "--           (TIMESTAMP 'epoch' + dispatch_time::bigint* INTERVAL '1 second' ) as dispatch_time \n", "           \n", "--           from  (\n", "--                  select  suborder_id ,\n", "--                  max(json_extract_path_text(json_extract_path_text(extra,'to_details',TRUE), 'dispatch_time',TRUE)) as expected_dispatch_time\n", "--                  from lake_oms_bifrost.oms_suborder_event --                  where install_ts::date BETWEEN '2020-03-01' and '2020-05-21' and event_type_key ='sort_id_update' \n", "--                  group by 1\n", "                 \n", "--                  ) a\n", "-- ),\t   \n", "\n", "\n", "\n", "batch_dispatch_activity AS \n", "\n", "(   \n", "    select \n", "        b.external_criteria as batch_id,\n", "        max(a.expected_end_time + interval '5.5 hour') as expected_dispatch_end_time, \n", "        max(end_time) as dispatch_time\n", "        \n", "    from lake_logex.activity a\n", "    inner join lake_logex.batch  b on a.batch_id = b.id\n", "    where a.activity_type = 'DISPATCH' \n", "    group by 1\n", ")  ,\n", "\n", "\n", "\n", "\n", "abc as ( select  P2B_l.name,\n", "          P2B_l.order_id,\n", "\t\tP2B_l.pick_list_id,\n", "\t\tP2B_l.Assignment_time,\n", "\t\tP2B_l.Complete_Time_v,\n", "\t\tP2B_l.id ,\n", "        P2B_l.A2C,\n", "        P2B_l.order_batch_id,\n", "        d.dispatch_time\n", "\n", "          \n", "\n", "from P2B_l \n", "left join batch_dispatch_activity d on P2B_l.order_batch_id = d.batch_id\n", "),\n", "\n", "\n", "abc1 as ( select abc.* ,\n", "              (datediff(mins,Complete_Time_v, dispatch_time)::float) as complete_dispatch_time\n", "from abc \n", "where dispatch_time is not null \n", "\n", "\n", "\n", "), \n", "\n", "\n", "percentile_total_dispatch_l as ( select name ,\n", "                  PERCENTILE_DISC ( 0.90 )\n", "                  WITHIN GROUP (ORDER BY complete_dispatch_time asc) \n", "                --   OVER ( PARTITION BY name ) as percentile\n", "                 OVER (  ) as percentile\n", "                  from abc1\n", "                   \n", "\n", "),\n", "\n", "percentile_dispatch_total as ( select 'Total' as \"Outlet\",\n", "                                percentile as \"Percentile\"\n", "                             from percentile_total_dispatch_l\n", "                                                group by 1,2\n", "\n", "\n", "),\n", "\n", "final_dispatch as (  select percentile_dispatch_total.Outlet,\n", "                Percentile/60::float as \"90th Percentile\"\n", "                from  percentile_dispatch_total\n", "\n", "),\n", "\n", "\n", "percentile_total_l as ( select  \n", "                  PERCENTILE_DISC ( 0.90 )\n", "                  WITHIN GROUP (ORDER BY A2C asc) \n", "                  OVER ( ) as percentile\n", "                  from P2B_l\n", "                   \n", "\n", "),\n", "\n", "percentile_total as ( select 'Total' as \"Outlet\",\n", "                                percentile as \"Percentile\"\n", "                             from percentile_total_l\n", "                                                group by 1,2\n", "\n", "\n", "),\n", "\n", "final as (  select percentile_total.Outlet,\n", "                Percentile/60::float as \"90th Percentile\"\n", "                from  percentile_total\n", "\n", ")\n", "\n", "\t\n", "-- select distinct name, percentile  from percentile_total_dispatch_l\n", "\n", "-- order by 1\n", "-- limit 5000\n", "\n", "select * from final \n", "union  \n", "select * from final_dispatch \n", "\n", "\n", "\n", "\"\"\"\n", "p2p = pd.read_sql_query(\n", "    sql=query_p2p_p2d, con=redshift, params={\"start_date\": start, \"end_date\": end}\n", ")\n", "p2p_1 = pd.read_sql_query(\n", "    sql=query_p2p_p2d, con=redshift, params={\"start_date\": start_1, \"end_date\": end_1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"o2p\", round(o2p[\"percentile\"][0], 2))\n", "print(\"p2p\", round(p2p[\"90th percentile\"][0], 2))\n", "print(\"p2d\", round(p2p[\"90th percentile\"][1], 2))\n", "print(\"----------\")\n", "print(\"o2p\", round(o2p_1[\"percentile\"][0], 2))\n", "print(\"p2p\", round(p2p_1[\"90th percentile\"][0], 2))\n", "print(\"p2d\", round(p2p_1[\"90th percentile\"][1], 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Returns query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_ret = \"\"\"\n", "WITH expected AS\n", "  (\n", "SELECT \n", "       variant_id,\n", "       sub_order_id,\n", "       avg(expected_quantity) AS required_quantity,\n", "       avg(good_quantity)+avg(bad_quantity) AS inward_quantity\n", "   FROM  metrics.returns_good_bad_inv\n", "   WHERE scheduleddate::date >= %(start)s\n", "     AND scheduleddate::date <=%(end)s\n", "GROUP BY 1,\n", "         2 \n", "),\n", " with_order_id_return_total AS\n", "  (SELECT iil.grofers_order_id,\n", "          iil.variant_id,\n", "          coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "   JOIN expected e ON e.sub_order_id=iil.grofers_order_id\n", "   AND e.variant_id=iil.variant_id\n", "   WHERE iut.group_name='return'\n", "AND iil.grofers_order_id IS NOT NULL\n", "     AND iil.grofers_order_id !=''\n", "   GROUP BY 1,\n", "            2),     \n", " with_order_id_return_good AS\n", "  (SELECT iil.grofers_order_id,\n", "          iil.variant_id,\n", "          coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id\n", "   JOIN expected e ON e.sub_order_id=iil.grofers_order_id\n", "   AND e.variant_id=iil.variant_id\n", "   WHERE \n", " iut.group_name='return'\n", "     AND ((iut.is_good_inventory_update=1\n", "           AND iut.good_inventory_operation = '+')\n", "          OR (iut.is_inventory_update =1\n", "              AND iut.inventory_operation = '+'))\n", "     AND iil.grofers_order_id IS NOT NULL\n", "     AND iil.grofers_order_id !=''\n", "   GROUP BY 1,\n", "            2),         \n", " with_order_id_return_bad AS\n", "  (SELECT iil.grofers_order_id,\n", "          iil.variant_id,\n", "          coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id\n", "   JOIN expected e ON e.sub_order_id=iil.grofers_order_id\n", "   AND e.variant_id=iil.variant_id\n", "   WHERE iut.group_name='return'\n", "     AND iut.is_bad_inventory_update=1\n", "     AND iut.bad_inventory_operation='+'\n", "   GROUP BY 1,\n", "            2),\n", " with_order_id_return_bad_perishable AS\n", "  (SELECT iil.grofers_order_id,\n", "          iil.variant_id,\n", "          coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id\n", "   JOIN expected e ON e.sub_order_id=iil.grofers_order_id\n", "   AND e.variant_id=iil.variant_id\n", "   join lake_rpc.product_product pp on pp.variant_id=iil.variant_id and (pp.storage_type IN (2,\n", "                                                         6,\n", "                                                         3,\n", "                                                         7) or pp.outlet_type = 1)\n", "   WHERE iut.group_name='return'\n", "     AND iut.is_bad_inventory_update=1\n", "     AND iut.bad_inventory_operation='+'\n", "   GROUP BY 1,\n", "            2),            \n", " without_order_id_good AS\n", "  (SELECT sum(iisd.\"delta\")::int AS quantity\n", "   from  lake_ims.view_ims_inward_invoice  iii\n", "   join lake_ims.ims_inventory_stock_details iisd ON iisd.grn_id=iii.grn_id\n", "   join lake_rpc.product_product rpp ON rpp.variant_id=iisd.variant_id\n", "   join lake_ims.ims_inventory_log piil ON piil.inventory_update_id::varchar=iisd.inventory_update_id::varchar\n", "   WHERE date(iii.created_at) BETWEEN %(start)s AND %(end)s\n", "     and piil.inventory_update_type_id not in (87,88,89)\n", "     AND iii.source_type=3 ),\n", " without_order_id_bad AS\n", "  (SELECT sum(iisd.\"delta\")::int AS quantity\n", "   from  lake_ims.view_ims_inward_invoice  iii\n", "   join lake_ims.ims_inventory_stock_details iisd ON iisd.grn_id=iii.grn_id\n", "   join lake_rpc.product_product rpp ON rpp.variant_id=iisd.variant_id\n", "   join lake_ims.ims_inventory_log piil ON piil.inventory_update_id::varchar=iisd.inventory_update_id::varchar\n", "   WHERE date(iii.created_at) BETWEEN %(start)s AND %(end)s\n", "     AND iii.source_type=3\n", "      and piil.inventory_update_type_id in (87,88,89)\n", "      ),\n", "without_order_id_bad_perishable AS\n", "  (SELECT sum(iisd.\"delta\")::int AS quantity\n", "   from  lake_ims.view_ims_inward_invoice  iii\n", "   join lake_ims.ims_inventory_stock_details iisd ON iisd.grn_id=iii.grn_id\n", "   join lake_rpc.product_product rpp ON rpp.variant_id=iisd.variant_id\n", "   join lake_ims.ims_inventory_log piil ON piil.inventory_update_id::varchar=iisd.inventory_update_id::varchar\n", "   WHERE date(iii.created_at) BETWEEN %(start)s AND %(end)s\n", "     AND iii.source_type=3\n", "     and (rpp.storage_type IN (2,\n", "                                                         6,\n", "                                                         3,\n", "                                                         7) or rpp.outlet_type = 1)\n", "      and piil.inventory_update_type_id in (87,88,89)\n", "     ),                           \n", " with_order_id_total_return_marked_overall AS\n", "  (SELECT -- iil.grofers_order_id,\n", "-- iil.variant_id,\n", "coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id --join expected e on e.sub_order_id=iil.grofers_order_id and e.variant_id=iil.variant_id\n", "\n", "   WHERE date(iil.pos_timestamp) BETWEEN %(start)s AND %(end)s\n", "     AND iut.group_name in ('return')\n", "AND iil.grofers_order_id IS NOT NULL\n", "     AND iil.grofers_order_id !='' \n", "),\n", " with_order_id_good_return_marked_overall AS\n", "  (SELECT -- iil.grofers_order_id,\n", "-- iil.variant_id,\n", "coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id --join expected e on e.sub_order_id=iil.grofers_order_id and e.variant_id=iil.variant_id\n", "\n", "   WHERE date(iil.pos_timestamp) BETWEEN %(start)s AND %(end)s\n", "     AND iut.group_name in ('return')\n", "     AND ((iut.is_good_inventory_update=1\n", "           AND iut.good_inventory_operation = '+')\n", "          OR (iut.is_inventory_update =1\n", "              AND iut.inventory_operation = '+'))\n", "     AND iil.grofers_order_id IS NOT NULL\n", "     AND iil.grofers_order_id !='' ),\n", "     \n", "     \n", " with_order_id_bad_return_marked_overall AS\n", "  (SELECT -- iil.grofers_order_id,\n", "-- iil.variant_id,\n", "coalesce(sum(iil.\"delta\"),0) AS quantity\n", "   from  lake_ims.ims_inventory_log  iil\n", "   INNER join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id --join expected e on e.sub_order_id=iil.grofers_order_id and e.variant_id=iil.variant_id\n", "\n", "   WHERE date(iil.pos_timestamp) BETWEEN %(start)s AND %(end)s\n", "     AND iut.group_name='return'\n", "     AND iut.is_bad_inventory_update=1\n", "     AND iut.bad_inventory_operation='+' ),\n", "reinventorization as      \n", "(\n", "SELECT \n", "          sum(iil.\"delta\") as qty\n", "  from  lake_ims.ims_inventory_log  iil\n", "  WHERE inventory_update_type_id in (80,121,122)\n", "     AND outlet_id not in (922,903,588)\n", "     AND iil.pos_timestamp   BETWEEN %(start)s AND %(end)s )\n", "     \n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'total_returns_from_expected',\n", "       sum(quantity) quantity\n", "FROM with_order_id_return_total -- union\n", "-- select 'total_inwarded_in_mine', sum(inward_quantity) quantity from expected\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'total_expected_from_billed',\n", "       sum(required_quantity) quantity\n", "FROM expected\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_good_returns_from_expected',\n", "       sum(quantity) quantity\n", "FROM with_order_id_return_good\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_bad_returns_from_expected',\n", "       sum(quantity) quantity\n", "FROM with_order_id_return_bad\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'without_order_id_good_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM without_order_id_good\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'without_order_id_bad_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM without_order_id_bad\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_total_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM with_order_id_total_return_marked_overall\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_good_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM with_order_id_good_return_marked_overall\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_bad_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM with_order_id_bad_return_marked_overall\n", "\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'without_order_id_perishable_bad_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM without_order_id_bad_perishable\n", "\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'with_order_id_perishable_bad_returns_marked_in_date_range',\n", "       sum(quantity) quantity\n", "FROM with_order_id_return_bad_perishable\n", "UNION\n", "SELECT %(start)s ||' to ' || %(end)s AS daterange,\n", "       'total_reinventorization',\n", "       sum(qty) quantity\n", "FROM reinventorization\n", "\"\"\"\n", "returns_data = pd.read_sql_query(\n", "    sql=query_ret, con=redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["returns_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = int(\n", "    returns_data[returns_data[\"?column?\"] == \"total_expected_from_billed\"][\n", "        \"quantity\"\n", "    ].to_list()[0]\n", ")\n", "b = int(\n", "    returns_data[returns_data[\"?column?\"] == \"total_returns_from_expected\"][\n", "        \"quantity\"\n", "    ].to_list()[0]\n", ")\n", "c = round(b / a * 100, 2)\n", "d = int(\n", "    returns_data[\n", "        returns_data[\"?column?\"] == \"with_order_id_good_returns_from_expected\"\n", "    ][\"quantity\"].to_list()[0]\n", ")\n", "e = round(d / b * 100, 2)\n", "f = int(\n", "    returns_data[returns_data[\"?column?\"] == \"with_order_id_bad_returns_from_expected\"][\n", "        \"quantity\"\n", "    ].to_list()[0]\n", ")\n", "g = round(f / b * 100, 2)\n", "h = int(\n", "    returns_data[\n", "        returns_data[\"?column?\"]\n", "        == \"with_order_id_perishable_bad_returns_marked_in_date_range\"\n", "    ][\"quantity\"].to_list()[0]\n", ")\n", "i = round(h / f * 100, 2)\n", "j = int(\n", "    returns_data[\n", "        returns_data[\"?column?\"] == \"without_order_id_bad_returns_marked_in_date_range\"\n", "    ][\"quantity\"].to_list()[0]\n", ")\n", "k = int(\n", "    returns_data[\n", "        returns_data[\"?column?\"] == \"without_order_id_good_returns_marked_in_date_range\"\n", "    ][\"quantity\"].to_list()[0]\n", ")\n", "l = j + k\n", "m = round(j / l * 100, 2)\n", "n = round(k / l * 100, 2)\n", "o = int(\n", "    returns_data[\n", "        returns_data[\"?column?\"]\n", "        == \"without_order_id_perishable_bad_returns_marked_in_date_range\"\n", "    ][\"quantity\"].to_list()[0]\n", ")\n", "p = round(o / j * 100, 2)\n", "q = int(\n", "    returns_data[returns_data[\"?column?\"] == \"total_reinventorization\"][\n", "        \"quantity\"\n", "    ].to_list()[0]\n", ")\n", "r = round((d + q + k) / (l + b) * 100, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    a, \"\\n\", c, \"\\n\", e, \"\\n\", g, \"\\n\", i, \"\\n\", l, \"\\n\", n, \"\\n\", m, \"\\n\", p, \"\\n\", r\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}