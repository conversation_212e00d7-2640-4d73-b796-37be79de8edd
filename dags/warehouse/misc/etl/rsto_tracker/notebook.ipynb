{"cells": [{"cell_type": "code", "execution_count": null, "id": "31cee633-4402-44a7-90c1-d0702e9477d8", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "6c779c08-eb1b-4c53-921d-db63995dafe9", "metadata": {}, "outputs": [], "source": ["pd.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "fb9a25a0-6b4c-4c8e-8759-03a7f386fb45", "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import warnings\n", "import numpy as np\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "ed49e98c-39c6-403d-abcf-3daddfa70b23", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "0f939893-91a9-4414-979c-b89c69b374ee", "metadata": {}, "outputs": [], "source": ["Sheet_id = \"1Mp0XqyyTjNMxSNEvDDR79cuJtipLiWP7F6Q7m3vD1RY\""]}, {"cell_type": "markdown", "id": "9d532107-bdf4-43c5-a219-3d55a776517c", "metadata": {}, "source": ["OUtbound "]}, {"cell_type": "code", "execution_count": null, "id": "c9a76f00-4b0b-4925-b670-100d4fbd25c9", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "outbound = pd.read_sql_query(\n", "    \"\"\"select  c.facility_id as sender_facility_id,\n", "               f.name as sender_facility_name,\n", "               c1.facility_id as facility_id,\n", "               f1.name as facility_name,\n", "               e.sto_id,\n", "               e.item_id,\n", "               e.item_name,\n", "               max(sto_created_at) as sto_created_at,\n", "               avg(reserved_quantity) as sto_qty,\n", "               max(picking_completed_at_ist) as picked_at,\n", "               sum(picked_quantity) as picked_qty,\n", "               max(sto_invoice_created_at) as billed_at,\n", "               sum(coalesce(billed_quantity,0)) as billed_qty,\n", "               max(grn_started_at) as grn_at,\n", "               sum(coalesce(inwarded_quantity,0)) as grn_qty,\n", "               max(discrepancy_created_at) as disc_at,\n", "               sum(coalesce(disc_qty,0)) as disc_qty,\n", "               sum(coalesce(short_qty,0)) as short_qty,\n", "               sum(coalesce(excess_qty,0)) as excess_qty,\n", "               sum(coalesce(damage_qty,0)) as damage_qty,\n", "               sum(coalesce(wrong_item_qty,0)) as wrong_item_qty,\n", "               sum(coalesce(b2b_return_qty,0)) as b2b_qty,\n", "               sum(coalesce(b2breturn_damaged,0)) as b2b_damaged_qty,\n", "               sum(coalesce(b2breturn_near_expiry,0)) as b2b_near_expiry_qty,\n", "               sum(coalesce(b2breturn_expired,0)) as b2b_expiry_qty\n", "from metrics.esto_details e\n", "\n", "join lake_retail.console_outlet c on c.id = e.sender_outlet_id and c.business_type_id in (7)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_outlet c1 on c1.id = e.receiving_outlet_id and c1.business_type_id in (1,12)\n", "        join lake_crates.facility f1 on f1.id = c1.facility_id\n", "        join lake_po.sto  s1 on s1.id=e.sto_id\n", "        where item_id in (10106019,10106020,10106544,10106612,10106612,10106652,10106809,10106810,10106905,10111652,10116155,10116154,10116161,10116156,10117983,10117982,10117984,10106648)\n", "        and sto_created_at::date between '2022-11-01' and current_date\n", "        and s1.sto_state_id<>3\n", "        group by 1,2,3,4,5,6,7\n", "        \n", "        \n", "        \"\"\",\n", "    redshift,\n", ")\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "id": "4d1ada73-0c49-495c-ad4e-329f9960a8d9", "metadata": {}, "outputs": [], "source": ["outbound[\"diff_qty\"] = (\n", "    outbound[\"billed_qty\"] - outbound[\"grn_qty\"] - outbound[\"b2b_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "07e6fbf3-60eb-434e-b589-3e7cac4cbfa1", "metadata": {}, "outputs": [], "source": ["outbound[\"sto_date\"] = pd.to_datetime(outbound[\"sto_created_at\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "65f662b2-2abe-4484-be41-8236695db247", "metadata": {}, "outputs": [], "source": ["outbound[\"disc_qty\"] = outbound[\"disc_qty\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "93928a41-5906-467b-8a4c-4dc84894a697", "metadata": {}, "outputs": [], "source": ["out_summ = (\n", "    outbound.groupby(\n", "        [\n", "            \"sender_facility_id\",\n", "            \"sender_facility_name\",\n", "            \"facility_name\",\n", "            \"facility_id\",\n", "            \"sto_date\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"sto_qty\": sum,\n", "            \"billed_qty\": sum,\n", "            \"grn_qty\": sum,\n", "            \"disc_qty\": sum,\n", "            \"short_qty\": sum,\n", "            \"excess_qty\": sum,\n", "            \"damage_qty\": sum,\n", "            \"wrong_item_qty\": sum,\n", "            \"b2b_qty\": sum,\n", "            \"b2b_damaged_qty\": sum,\n", "            \"b2b_near_expiry_qty\": sum,\n", "            \"b2b_expiry_qty\": sum,\n", "            \"diff_qty\": sum,\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b1ce1fe-9ddb-482d-87fa-5e0ec32520e0", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(outbound, \"1Mp0XqyyTjNMxSNEvDDR79cuJtipLiWP7F6Q7m3vD1RY\", \"IB(WH)\")\n", "pb.to_sheets(out_summ, \"1Mp0XqyyTjNMxSNEvDDR79cuJtipLiWP7F6Q7m3vD1RY\", \"OB(WH) Summary\")"]}, {"cell_type": "markdown", "id": "78f933d8-fdb6-48a1-97d4-261e5d9fd624", "metadata": {}, "source": ["COINs Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "e57221ee-0c3c-4876-9312-89f5be0a51f9", "metadata": {}, "outputs": [], "source": ["coins = pd.read_sql_query(\n", "    f\"\"\"\n", "        with inv as (select i.outlet_id,c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as quantity\n", "        from lake_view_ims.ims_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in (10106019,10106020,10106544,10106612,10106612,10106652,10106809,10106810,10106905,10111652,10116155,10116154,10116161,10116156,10117983,10117982,10117984,10106648)\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1\n", "        and c.business_type_id in (1,12,7)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5,6),\n", "        good_inv as (select c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,sum(i.quantity) as good_qty\n", "        from lake_view_ims.ims_good_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in (10106019,10106020,10106544,10106612,10106612,10106652,10106809,10106810,10106905,10111652,10116155,10116154,10116161,10116156,10117983,10117982,10117984,10106648)\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1 and c.business_type_id in (1,12,7)\n", "        join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5),\n", "        bad_inv as (select c.facility_id, f.name as facility_name, pp.item_id, id.name as item_name,l.name as city,\n", "        sum(i.quantity) as bad_qty,\n", "        sum(case when i.inventory_update_type_id in (11,87) then i.quantity else 0 end) as bad_damage_qty,\n", "        sum(case when i.inventory_update_type_id in (12,88) then i.quantity else 0 end) as bad_expiry_qty,\n", "        sum(case when i.inventory_update_type_id in (64,89) then i.quantity else 0 end) as bad_nearexpiry_qty\n", "        from lake_view_ims.ims_bad_inventory i \n", "        join lake_rpc.product_product pp on pp.variant_id = i.variant_id and pp.active = 1\n", "        and pp.item_id in (10106019,10106020,10106544,10106612,10106612,10106652,10106809,10106810,10106905,10111652,10116155,10116154,10116161,10116156,10117983,10117982,10117984,10106648)\n", "        join lake_rpc.item_details id on id.item_id = pp.item_id \n", "        Join lake_retail.console_outlet c on c.id = i.outlet_id and c.active = 1\n", "         and c.business_type_id in (1,12,7)\n", "         join lake_crates.facility f on f.id = c.facility_id\n", "        join lake_retail.console_location l on l.id = c.tax_location_id\n", "        and quantity > 0 \n", "        where i.active = 1\n", "        group by 1,2,3,4,5),\n", "         ds_warhouse_map as (SELECT bfom.outlet_id as outlet_id,cf1.name as DS, bfom.facility_id as backend_facility_id, cf.name as backend_facility_name\n", "FROM lake_po.bulk_facility_outlet_mapping bfom\n", "INNER JOIN lake_po.physical_facility_outlet_mapping pfom ON bfom.outlet_id=pfom.outlet_id\n", "and bfom.facility_id in (1,555,554,24,43,92,264,513,517,603,1206,1320,1872,1873,1876)\n", "LEFT JOIN lake_crates.facility cf on cf.id = bfom.facility_id\n", "LEFT JOIN lake_crates.facility cf1 on cf1.id = pfom.facility_id\n", "WHERE bfom.active = True\n", "AND pfom.active = 1\n", "AND pfom.ars_active = 1)\n", "\n", "        select \n", "             inv.outlet_id as outlet_id, --cf.name as backend_facility_name,\n", "             case when (ds_warhouse_map.backend_facility_id is null)then  coalesce(inv.facility_id, good_inv.facility_id, bad_inv.facility_id) else ds_warhouse_map.backend_facility_id end as backend_facility_id,\n", "             \n", "            coalesce(inv.city, good_inv.city, bad_inv.city) as city,\n", "            coalesce(inv.facility_id, good_inv.facility_id, bad_inv.facility_id) as facility_id,\n", "            coalesce(inv.facility_name, good_inv.facility_name, bad_inv.facility_name) as facility_name,\n", "            coalesce(inv.item_id, good_inv.item_id, bad_inv.item_id) as item_id,\n", "            coalesce(inv.item_name, good_inv.item_name, bad_inv.item_name) as item_name,\n", "            coalesce(inv.quantity,0) as on_shelf_inventory,\n", "            coalesce(good_inv.good_qty,0) as good_inventory,\n", "            coalesce(bad_inv.bad_qty,0) as bad_inventory,\n", "            coalesce(bad_damage_qty,0) as bad_damage_qty,\n", "            coalesce(bad_expiry_qty,0) as bad_expiry_qty,            \n", "            coalesce(bad_nearexpiry_qty,0) as bad_nearexpiry_qty\n", "        from inv\n", "         left join ds_warhouse_map on ds_warhouse_map.outlet_id=inv.outlet_id \n", "        full outer join good_inv on good_inv.facility_id = inv.facility_id and inv.item_id = good_inv.item_id\n", "        full outer join bad_inv on bad_inv.facility_id = inv.facility_id and inv.item_id = bad_inv.item_id\n", "       \n", "        \n", "\n", "        \"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7d4e79bf-68c0-4d2e-ba8a-e3276d6209be", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(coins, \"1Mp0XqyyTjNMxSNEvDDR79cuJtipLiWP7F6Q7m3vD1RY\", \"Current_Inventory\")"]}, {"cell_type": "code", "execution_count": null, "id": "a6c7470d-082c-4a11-b9cb-e17a5cd51289", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab17e67a-aa5e-4b03-be3b-9a2a408ad44f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}