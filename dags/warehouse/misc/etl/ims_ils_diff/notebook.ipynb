{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "import numpy as np\n", "from datetime import datetime, date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos = pb.get_connection(\"[Replica] POS\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datetime.now(tz).hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_query = \"\"\"SELECT o.id as outlet_id,\n", "          o.name ,\n", "          p.item_id,\n", "          I.UPC_ID AS UPC,\n", "          I.variant_id,\n", "          p.name AS Item_Name,\n", "          p.VARIANT_DESCRIPTION,\n", "          coalesce(I.QUANTITY,0) AS IMS_Qty\n", "   FROM ims.ims_inventory I\n", "   INNER JOIN rpc.product_product p ON p.variant_id = I.variant_id and p.active = 1\n", "   INNER JOIN retail.console_outlet o ON o.id = I.outlet_id and I.outlet_id in (116,1104,714,1624,1644,1577,1112,481,292,369,287,337,334)\n", "   where I.QUANTITY>0 and I.active = 1\"\"\"\n", "ims = pd.read_sql_query(ims_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ils_query = \"\"\"SELECT a.item_id,\n", "             a.variant_id,\n", "             b.outlet_id,\n", "             SUM(a.quantity) AS location_quantity\n", "      FROM  warehouse_location.warehouse_item_location a\n", "      INNER JOIN warehouse_location.warehouse_outlet_mapping b ON a.warehouse_id = b.warehouse_id\n", "      and b.outlet_id in (116,1104,714,1624,1644,1577,1112,481,292,369,287,337,334)\n", "      GROUP BY a.item_id,\n", "               a.variant_id,\n", "               b.outlet_id\"\"\"\n", "ils = pd.read_sql_query(ils_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_s = \"\"\"select outlet_id,outlet_name, \n", "count(distinct case when location_type = 1 then x.item_id else null end) as primary_skus,\n", "sum(case when location_type = 1 then quantity else 0 end) as primary_qty,\n", "count(distinct case when location_type = 2 then x.item_id else null end) as secondary_skus,\n", "sum(case when location_type = 2 then quantity else 0 end) as secondary_qty\n", "from\n", "(SELECT \n", "    outlet.id as outlet_id,\n", "\toutlet.name as outlet_name,\n", "\titem_loc.item_id,\n", "\tbatch,\n", "\tlocation_id,\n", "\tproduct.upc,\n", "\titem_loc.variant_id,\n", "\tproduct.name,\n", "\t(CASE\n", "      WHEN product.outlet_type = 1 THEN \"F&V type\"\n", "      WHEN product.outlet_type = 2 THEN \"Grocery type\"\n", "     ELSE 'Not Assigned'\n", "\tEND) AS \"Item Type\",\n", "\tproduct.variant_mrp,\n", "\tproduct.variant_description,\n", "\tstorage_loc.location_name,\n", "\titem_loc.quantity,\n", "\tstorage_loc.location_type\n", "FROM \n", "    warehouse_location.warehouse_item_location  item_loc\n", "\tINNER JOIN warehouse_location.warehouse_storage_location storage_loc ON storage_loc.id=item_loc.location_id\n", "\tINNER JOIN warehouse_location.warehouse_outlet_mapping out_map ON out_map.warehouse_id=storage_loc.warehouse_id\n", "\tINNER JOIN retail.console_outlet outlet ON outlet.id=out_map.outlet_id\n", "\tLEFT JOIN rpc.product_product product on product.variant_id=item_loc.variant_id and product.active = 1\n", "WHERE out_map.outlet_id in (116,1104,714,1624,1644,1577,1112,481,292,369,287,337,334) \n", "AND item_loc.active=1 and item_loc.quantity > 0) x\n", "group by 1,2\n", "\"\"\"\n", "snapshot = pd.read_sql_query(sql=query_s, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = ims.merge(ils, on=[\"outlet_id\", \"variant_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total[\"dif\"] = abs(total[\"IMS_Qty\"] - total[\"location_quantity\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = ps.sqldf(\n", "    \"\"\"\n", "            SELECT \n", "            outlet_id,\n", "               count(distinct (case when IMS_Qty > 0 then item_id else null end)) as ims_items,\n", "               sum(IMS_Qty) as ims_qty,\n", "               count(distinct (case when location_quantity > 0 then item_id else null end)) as ils_items,\n", "               sum(location_quantity) as ils_qty,\n", "               count(distinct (case when dif > 0 then item_id else null end)) as diff_items,\n", "               sum(dif) as diff_qty\n", "            FROM total p\n", "            group by 1\n", "            \"\"\",\n", "    locals(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"etl_timestamp\"] = datetime.now(tz)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snapshot.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final.merge(snapshot, on=[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final1[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"ims_items\",\n", "        \"ims_qty\",\n", "        \"ils_items\",\n", "        \"ils_qty\",\n", "        \"primary_skus\",\n", "        \"primary_qty\",\n", "        \"secondary_skus\",\n", "        \"secondary_qty\",\n", "        \"diff_items\",\n", "        \"diff_qty\",\n", "        \"etl_timestamp\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1[\"etl_timestamp\"] = pd.to_datetime(final1[\"etl_timestamp\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1[\"outlet_id\"] = final1[\"outlet_id\"].astype(int)\n", "final1[\"ims_items\"] = final1[\"ims_items\"].astype(int)\n", "final1[\"ims_qty\"] = final1[\"ims_qty\"].astype(int)\n", "final1[\"ils_items\"] = final1[\"ils_items\"].astype(int)\n", "final1[\"ils_qty\"] = final1[\"ils_qty\"].astype(int)\n", "final1[\"primary_skus\"] = final1[\"primary_skus\"].astype(int)\n", "final1[\"primary_qty\"] = final1[\"primary_qty\"].astype(int)\n", "final1[\"secondary_skus\"] = final1[\"secondary_skus\"].astype(int)\n", "final1[\"secondary_qty\"] = final1[\"secondary_qty\"].astype(int)\n", "final1[\"diff_items\"] = final1[\"diff_items\"].astype(int)\n", "final1[\"diff_qty\"] = final1[\"diff_qty\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"outlet_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"outlet name\",\n", "    },\n", "    {\n", "        \"name\": \"ims_items\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ims items\",\n", "    },\n", "    {\n", "        \"name\": \"ims_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ims qty\",\n", "    },\n", "    {\n", "        \"name\": \"ils_items\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ils items\",\n", "    },\n", "    {\n", "        \"name\": \"ils_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ils qty\",\n", "    },\n", "    {\n", "        \"name\": \"primary_skus\",\n", "        \"type\": \"int\",\n", "        \"description\": \"primary_skus\",\n", "    },\n", "    {\n", "        \"name\": \"primary_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"primary_qty\",\n", "    },\n", "    {\n", "        \"name\": \"secondary_skus\",\n", "        \"type\": \"int\",\n", "        \"description\": \"secondary_skus\",\n", "    },\n", "    {\n", "        \"name\": \"secondary_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"secondary_qty\",\n", "    },\n", "    {\n", "        \"name\": \"diff_items\",\n", "        \"type\": \"int\",\n", "        \"description\": \"diff_items\",\n", "    },\n", "    {\n", "        \"name\": \"diff_qty\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ims ils diff quantity\",\n", "    },\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ims_ils_diff_snapshot\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"etl_timestamp\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    # \"incremental_key\": [\"outlet_id\",\"etl_timestamp\"],\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Table for Storage IMS ILS Inventory snapshots for Backend Warehouses\",\n", "}\n", "pb.to_redshift(final1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}