{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "pd.set_option(\"display.max_columns\", 200)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import time\n", "\n", "start_time = time.time()\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "oms_bifrost = pb.get_connection(\"oms_bifrost\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "select scheduleddate, city,NULL as a,front_end,delivered_orders,unfulfilled_orders,overall_fillrate,wh_fillrate,l1_reason_wh,warehouse_count,warehouse,l1_reason_reprocure,reprocure_count,reprocure,l1_reason_checkout,checkout_count,checkout,l1_reason_partial_inventory,partial_inventory_count,partial_inventory\n", "from\n", "(\n", "with base_table as\n", "(\n", "select distinct\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_start * interval '1 second')) as slot_start,\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_end * interval '1 second')) as slot_end,\n", "    oo.id as order_id,\n", "    case when soi.suborder_id is null then so2.id else soi.suborder_id end as suborder_id,\n", "    ooi.product_id as product_id,\n", "    ooi.id as item_id,\n", "    ooi.quantity as ordered_quantity,\n", "    coalesce(ooi.procured_quantity, 0) as procured_quantity,\n", "    (case when ooi.quantity > coalesce(ooi.procured_quantity, 0) then 1 else 0 end) as Unfulfilled_order\n", "    from \n", "(\n", "select distinct\n", "    order_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where   (slot_start between extract(epoch from ((current_date-2)::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from ((current_date)::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", ") as os\n", "inner join lake_oms_bifrost.oms_order as oo on os.order_id = oo.id\n", "and oo.type in ('RetailForwardOrder','InternalForwardOrder')\n", "and oo.current_status = 'DELIVERED'\n", "inner join lake_oms_bifrost.oms_order_item as ooi on oo.id = ooi.order_id\n", "inner join lake_oms_bifrost.oms_merchant as om on oo.merchant_id = om.id \n", "--and  om.virtual_merchant_type = 'superstore_merchant'\n", "left join lake_oms_bifrost.oms_suborder_item soi on soi.order_item_id = ooi.id\n", "left join lake_oms_bifrost.oms_suborder so on so.id=soi.suborder_id\n", "left join (select order_id, max(id) as id from lake_oms_bifrost.oms_suborder \n", "where   (slot_start between extract(epoch from ((current_date-2)::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from ((current_date)::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", "group by 1 ) so2 on oo.id=so2.order_id\n", "),\n", "<PERSON><PERSON>ey as (Select bt.*,\n", "                 oic.reason_key\n", "from base_table bt\n", "left join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.item_id = oic.order_item_id\n", "),\n", "Final as (\n", "Select distinct ReasonKey.*,l.name as c_name,om.name as front_end, om.city_name as city\n", "from ReasonKey \n", "left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "inner join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", "),\n", "facilitylevel AS (\n", "SELECT ScheduledDate,city as city,\n", "          front_end,\n", "          count(DISTINCT order_id) AS Delivered_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1 THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1\n", "                                  AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders_WH,\n", "          (1-(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1 THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/count(DISTINCT order_id)::float))*100 AS Overall_FillRate,\n", "          (count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1\n", "                                      AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/count(DISTINCT order_id)::float)*100 AS WH_FillRate,\n", "           count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1\n", "                                  AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                             ELSE NULL\n", "                        END)::float/count(DISTINCT order_id)::float*100 AS L1_reason_WH\n", "   FROM (\n", "   SELECT distinct slot_end::date AS ScheduledDate,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             item_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "             from Final\n", "             GROUP BY 1,2,3,4,5,6)\n", "   WHERE city IS NOT NULL\n", "     AND city!=''\n", "   GROUP BY 1,2,3),\n", "L1_Reason AS\n", "  (SELECT ScheduledDate,city as city,\n", "        front_end,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Reprocure_Count,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Reprocure,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Checkout_Count,      \n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Checkout,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Partial_inventory_count,      \n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Partial_inventory,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/sum(unfulfilled)::float*100 AS Warehouse,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Warehouse_Count\n", "  FROM\n", "     (SELECT distinct fra.slot_end::date AS ScheduledDate,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "      FROM (\n", "   SELECT slot_end::date AS slot_end,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             item_id,\n", "             reason_key,\n", "             unfulfilled_order\n", "             from Final\n", "             GROUP BY 1,2,3,4,5,6,7) fra\n", "      LEFT join lake_oms_bifrost.oms_order oo ON oo.id = fra.order_id\n", "      INNER join lake_oms_bifrost.oms_merchant om ON oo.merchant_id=om.id\n", "      WHERE reason_key IS NOT NULL\n", "        AND reason_key!=''\n", "      GROUP BY 1,2,3,4,5)\n", "  GROUP BY 1,2,3)\n", "SELECT distinct fl.*,l1.Warehouse_Count,l1.Warehouse,l1.Reprocure_Count,l1.Reprocure,l1.Checkout_Count,l1.Checkout,l1.Partial_inventory_count,l1.Partial_inventory,l1.reprocure_count/(1.0*fl.delivered_orders)*100.0 as l1_reason_reprocure,l1.checkout_count/(1.0*fl.delivered_orders)*100 as l1_reason_checkout,l1.Partial_inventory_count/(1.0*fl.delivered_orders)*100 as l1_reason_partial_inventory\n", "FROM facilitylevel fl\n", "LEFT JOIN L1_Reason l1 ON fl.ScheduledDate = l1.ScheduledDate and fl.city=l1.city and fl.front_end=l1.front_end\n", "order by 1,2)\n", "where scheduleddate between current_date-2 and current_date\n", "\"\"\"\n", "data = pd.read_sql_query(sql=query1, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "select scheduleddate, city,NULL as a,front_end,delivered_orders,unfulfilled_orders,overall_fillrate,wh_fillrate,l1_reason_wh,warehouse_count,warehouse,l1_reason_reprocure,reprocure_count,reprocure,l1_reason_checkout,checkout_count,checkout,l1_reason_partial_inventory,partial_inventory_count,partial_inventory\n", "from\n", "(\n", "with base_table as\n", "(\n", "select distinct\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_start * interval '1 second')) as slot_start,\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_end * interval '1 second')) as slot_end,\n", "    oo.id as order_id,\n", "    case when soi.suborder_id is null then so2.id else soi.suborder_id end as suborder_id,\n", "    ooi.product_id as product_id,\n", "    ooi.id as item_id,\n", "    ooi.quantity as ordered_quantity,\n", "    coalesce(ooi.procured_quantity, 0) as procured_quantity,\n", "    (case when ooi.quantity > coalesce(ooi.procured_quantity, 0) then 1 else 0 end) as Unfulfilled_order\n", "    from \n", "(\n", "select distinct\n", "    order_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where   (slot_start between extract(epoch from ((current_date+1)::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from ((current_date+4)::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "and current_status <> 'CANCELLED'\n", ") as os\n", "inner join lake_oms_bifrost.oms_order as oo on os.order_id = oo.id\n", "and oo.type in ('RetailForwardOrder','InternalForwardOrder')\n", "and oo.current_status <> 'CANCELLED'\n", "inner join lake_oms_bifrost.oms_order_item as ooi on oo.id = ooi.order_id\n", "inner join lake_oms_bifrost.oms_merchant as om on oo.merchant_id = om.id \n", "--and  om.virtual_merchant_type = 'superstore_merchant'\n", "left join lake_oms_bifrost.oms_suborder_item soi on soi.order_item_id = ooi.id\n", "left join lake_oms_bifrost.oms_suborder so on so.id=soi.suborder_id\n", "left join (select order_id, max(id) as id from lake_oms_bifrost.oms_suborder \n", "where   (slot_start between extract(epoch from ((current_date+1)::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from ((current_date+4)::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "and current_status <> 'CANCELLED'\n", "group by 1 ) so2 on oo.id=so2.order_id\n", "),\n", "<PERSON><PERSON>ey as (Select bt.*,\n", "                 oic.reason_key\n", "from base_table bt\n", "left join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.item_id = oic.order_item_id\n", "),\n", "Final as (\n", "Select distinct ReasonKey.*,l.name as c_name,om.name as front_end, om.city_name as city\n", "from ReasonKey \n", "left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "inner join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", "),\n", "facilitylevel AS (\n", "SELECT ScheduledDate,city as city,\n", "          front_end,\n", "          count(DISTINCT order_id) AS Delivered_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1 THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders,\n", "          count(DISTINCT CASE\n", "                             WHEN Unfulfilled=1\n", "                                  AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                             ELSE NULL\n", "                         END) AS Unfulfilled_Orders_WH,\n", "        COALESCE(1-(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1 THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/ NULLIF(count(DISTINCT order_id)::float,0)), 0)*100 AS Overall_FillRate,\n", "          COALESCE(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1\n", "                                      AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/ NULLIF(count(DISTINCT order_id)::float,0), 0)*100 AS WH_FillRate,\n", "           COALESCE(count(DISTINCT CASE\n", "                                 WHEN Unfulfilled=1\n", "                                      AND reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN order_id\n", "                                 ELSE NULL\n", "                             END)::float/ NULLIF(count(DISTINCT order_id)::float,0), 0)*100 AS L1_reason_WH\n", "   FROM (\n", "   SELECT distinct slot_end::date AS ScheduledDate,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             item_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "             from Final\n", "             GROUP BY 1,2,3,4,5,6)\n", "   WHERE city IS NOT NULL\n", "     AND city!=''\n", "   GROUP BY 1,2,3),\n", "L1_Reason AS\n", "  (SELECT ScheduledDate,city as city,\n", "        front_end,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Reprocure_Count,\n", "          COALESCE(sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/NULLIF(sum(unfulfilled)::float,0),0)*100 AS Reprocure,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Checkout_Count,      \n", "          COALESCE(sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/NULLIF(sum(unfulfilled)::float,0),0)*100 AS Checkout,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Partial_inventory_count,      \n", "          COALESCE(sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PARTIAL_INVENTORY_AVAILABLE' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/NULLIF(sum(unfulfilled)::float,0),0)*100 AS Partial_inventory,\n", "          COALESCE(sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END)::float/NULLIF(sum(unfulfilled)::float,0),0)*100 AS Warehouse,\n", "          sum(CASE\n", "                  WHEN reason_key = 'ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING' THEN unfulfilled\n", "                  ELSE 0\n", "              END) as Warehouse_Count\n", "  FROM\n", "     (SELECT distinct fra.slot_end::date AS ScheduledDate,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             reason_key,\n", "             max(unfulfilled_order) AS Unfulfilled\n", "      FROM (\n", "   SELECT slot_end::date AS slot_end,\n", "             city,\n", "             front_end,\n", "             order_id,\n", "             item_id,\n", "             reason_key,\n", "             unfulfilled_order\n", "             from Final\n", "             GROUP BY 1,2,3,4,5,6,7) fra\n", "      LEFT join lake_oms_bifrost.oms_order oo ON oo.id = fra.order_id\n", "      INNER join lake_oms_bifrost.oms_merchant om ON oo.merchant_id=om.id\n", "      WHERE reason_key IS NOT NULL\n", "        AND reason_key!=''\n", "      GROUP BY 1,2,3,4,5)\n", "  GROUP BY 1,2,3)\n", "SELECT distinct fl.*,l1.Warehouse_Count,l1.Warehouse,l1.Reprocure_Count,l1.Reprocure,l1.Checkout_Count,l1.Checkout,l1.Partial_inventory_count,l1.Partial_inventory,l1.reprocure_count/NULLIF((1.0*fl.delivered_orders),0)*100.0 as l1_reason_reprocure,l1.checkout_count/NULLIF((1.0*fl.delivered_orders),0)*100 as l1_reason_checkout,l1.Partial_inventory_count/NULLIF((1.0*fl.delivered_orders),0)*100 as l1_reason_partial_inventory\n", "FROM facilitylevel fl\n", "LEFT JOIN L1_Reason l1 ON fl.ScheduledDate = l1.ScheduledDate and fl.city=l1.city and fl.front_end=l1.front_end\n", "order by 1,2)\n", "where scheduleddate between current_date+1 and current_date+4\n", "\"\"\"\n", "data_fut = pd.read_sql_query(sql=query2, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fut[\"scheduleddate\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"scheduleddate\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.columns, data_fut.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_f = pd.concat([data, data_fut], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data_f"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.drop_duplicates(inplace=True)\n", "data = data[~data[\"city\"].isin([\"B2B Delhi\", \"B2B Gurugram\", \"Not in service area\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.city.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.groupby([\"scheduleddate\"]).delivered_orders.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh_id = \"1I3I9zaY9Xek_02IhcYyMYzIVFar05ZoPp5v6jos9BFc\"\n", "mapp = pb.from_sheets(sh_id, \"Sheet6\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_front = data.merge(mapp, how=\"left\", left_on=\"front_end\", right_on=\"FrontEnd\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_front = data_front.drop(columns=[\"a\", \"FrontEnd\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_front.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_front = data_front[\n", "    [\n", "        \"scheduleddate\",\n", "        \"city\",\n", "        \"Facility\",\n", "        \"front_end\",\n", "        \"delivered_orders\",\n", "        \"unfulfilled_orders\",\n", "        \"overall_fillrate\",\n", "        \"wh_fillrate\",\n", "        \"l1_reason_wh\",\n", "        \"warehouse_count\",\n", "        \"warehouse\",\n", "        \"l1_reason_reprocure\",\n", "        \"reprocure_count\",\n", "        \"reprocure\",\n", "        \"l1_reason_checkout\",\n", "        \"checkout_count\",\n", "        \"checkout\",\n", "        \"l1_reason_partial_inventory\",\n", "        \"partial_inventory_count\",\n", "        \"partial_inventory\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_front_sheet = data_front[\n", "    [\n", "        \"scheduleddate\",\n", "        \"city\",\n", "        \"Facility\",\n", "        \"front_end\",\n", "        \"delivered_orders\",\n", "        \"overall_fillrate\",\n", "        \"wh_fillrate\",\n", "        \"l1_reason_reprocure\",\n", "        \"l1_reason_checkout\",\n", "        \"l1_reason_partial_inventory\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh_id = \"1I3I9zaY9Xek_02IhcYyMYzIVFar05ZoPp5v6jos9BFc\"\n", "pb.to_sheets(data_front_sheet, sh_id, \"Frontend_Level_View\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fac = data_front.groupby([\"scheduleddate\", \"Facility\"], as_index=False).agg(\n", "    {\n", "        \"delivered_orders\": sum,\n", "        \"unfulfilled_orders\": sum,\n", "        \"warehouse_count\": sum,\n", "        \"reprocure_count\": sum,\n", "        \"checkout_count\": sum,\n", "        \"partial_inventory_count\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fac[\"overall_fillrate %\"] = (\n", "    1 - data_fac[\"unfulfilled_orders\"] / data_fac[\"delivered_orders\"]\n", ") * 100\n", "data_fac[\"wh_fillrate %\"] = (\n", "    data_fac[\"warehouse_count\"] / data_fac[\"delivered_orders\"]\n", ") * 100\n", "data_fac[\"l1_reason_wh %\"] = (\n", "    data_fac[\"warehouse_count\"] / data_fac[\"delivered_orders\"]\n", ") * 100\n", "data_fac[\"l1_reason_reprocure %\"] = (\n", "    data_fac[\"reprocure_count\"] / data_fac[\"delivered_orders\"]\n", ") * 100\n", "data_fac[\"l1_reason_checkout %\"] = (\n", "    data_fac[\"checkout_count\"] / data_fac[\"delivered_orders\"]\n", ") * 100\n", "data_fac[\"l1_reason_partial_inventory %\"] = (\n", "    data_fac[\"partial_inventory_count\"] / data_fac[\"delivered_orders\"]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_fac_level = data_fac[\n", "    [\n", "        \"scheduleddate\",\n", "        \"Facility\",\n", "        \"delivered_orders\",\n", "        \"overall_fillrate %\",\n", "        \"wh_fillrate %\",\n", "        \"l1_reason_reprocure %\",\n", "        \"l1_reason_checkout %\",\n", "        \"l1_reason_partial_inventory %\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data_fac_level, sh_id, \"Facility_Level_View\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_grp = data.groupby([\"scheduleddate\", \"city\"], as_index=False).agg(\n", "    {\n", "        \"delivered_orders\": sum,\n", "        \"unfulfilled_orders\": sum,\n", "        \"warehouse_count\": sum,\n", "        \"reprocure_count\": sum,\n", "        \"checkout_count\": sum,\n", "        \"partial_inventory_count\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pan = data_grp.groupby([\"scheduleddate\"], as_index=False).agg(\n", "    {\n", "        \"delivered_orders\": sum,\n", "        \"unfulfilled_orders\": sum,\n", "        \"warehouse_count\": sum,\n", "        \"reprocure_count\": sum,\n", "        \"checkout_count\": sum,\n", "        \"partial_inventory_count\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pan[\"overall_fillrate %\"] = (\n", "    1 - data_pan[\"unfulfilled_orders\"] / data_pan[\"delivered_orders\"]\n", ") * 100\n", "data_pan[\"wh_fillrate %\"] = (\n", "    data_pan[\"warehouse_count\"] / data_pan[\"delivered_orders\"]\n", ") * 100\n", "data_pan[\"l1_reason_wh %\"] = (\n", "    data_pan[\"warehouse_count\"] / data_pan[\"delivered_orders\"]\n", ") * 100\n", "data_pan[\"l1_reason_reprocure %\"] = (\n", "    data_pan[\"reprocure_count\"] / data_pan[\"delivered_orders\"]\n", ") * 100\n", "data_pan[\"l1_reason_checkout %\"] = (\n", "    data_pan[\"checkout_count\"] / data_pan[\"delivered_orders\"]\n", ") * 100\n", "data_pan[\"l1_reason_partial_inventory %\"] = (\n", "    data_pan[\"partial_inventory_count\"] / data_pan[\"delivered_orders\"]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pan.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pan = data_pan[\n", "    [\n", "        \"scheduleddate\",\n", "        \"delivered_orders\",\n", "        \"overall_fillrate %\",\n", "        \"wh_fillrate %\",\n", "        \"l1_reason_reprocure %\",\n", "        \"l1_reason_checkout %\",\n", "        \"l1_reason_partial_inventory %\",\n", "    ]\n", "]\n", "pb.to_sheets(data_pan, sh_id, \"PanIndia_Level_View\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_grp[\"overall_fillrate %\"] = (\n", "    1 - data_grp[\"unfulfilled_orders\"] / data_grp[\"delivered_orders\"]\n", ") * 100\n", "data_grp[\"wh_fillrate %\"] = (\n", "    data_grp[\"warehouse_count\"] / data_grp[\"delivered_orders\"]\n", ") * 100\n", "data_grp[\"l1_reason_wh %\"] = (\n", "    data_grp[\"warehouse_count\"] / data_grp[\"delivered_orders\"]\n", ") * 100\n", "data_grp[\"l1_reason_reprocure %\"] = (\n", "    data_grp[\"reprocure_count\"] / data_grp[\"delivered_orders\"]\n", ") * 100\n", "data_grp[\"l1_reason_checkout %\"] = (\n", "    data_grp[\"checkout_count\"] / data_grp[\"delivered_orders\"]\n", ") * 100\n", "data_grp[\"l1_reason_partial_inventory %\"] = (\n", "    data_grp[\"partial_inventory_count\"] / data_grp[\"delivered_orders\"]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_grp = np.round(data_grp, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_level = data_grp[\n", "    [\n", "        \"scheduleddate\",\n", "        \"city\",\n", "        \"delivered_orders\",\n", "        \"overall_fillrate %\",\n", "        \"wh_fillrate %\",\n", "        \"l1_reason_reprocure %\",\n", "        \"l1_reason_checkout %\",\n", "        \"l1_reason_partial_inventory %\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_city_level.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data_city_level, sh_id, \"City_Level_View\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_f[(data_f[\"scheduleddate\"].astype(str) == \"2021-01-18\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}