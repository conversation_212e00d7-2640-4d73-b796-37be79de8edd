{"cells": [{"cell_type": "code", "execution_count": null, "id": "25836861-a7e1-4945-9196-579c4d2efa95", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "5f8e9e26-3488-4181-9ea4-6e642f792944", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "7d12f204-95fa-425e-97bc-249765e3ada9", "metadata": {}, "outputs": [], "source": ["OID_ = [1104, 4170]"]}, {"cell_type": "code", "execution_count": null, "id": "62818816-c65f-4bc0-adc6-080600da93e1", "metadata": {}, "outputs": [], "source": ["inv = pd.read_sql_query(\n", "    f\"\"\"\n", "select outlet_id, \n", "       item_id, \n", "       case when value = 'TRUE' and active = True then 1 else 0 end fast_flag\n", "from lake_view_warehouse_location.item_outlet_configs\n", "where config_type_id = 2  \n", "-- and active = True\n", "and outlet_id in {*OID_,}\n", "\"\"\",\n", "    presto,\n", ")\n", "inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e57c5689-fa2f-4f6d-8c6c-9f591d7d063d", "metadata": {}, "outputs": [], "source": ["mig = pb.from_sheets(\"1Ard2Cq6nKtAmv1y4yVRja-I_0BYf4RuqgHt0bMhmsK8\", \"To be moved\")\n", "mig.head()"]}, {"cell_type": "code", "execution_count": null, "id": "553b12dd-f285-42fd-a075-b8f8286ea2d3", "metadata": {}, "outputs": [], "source": ["mig[[\"outlet_id\", \"item_id\"]] = mig[[\"outlet_id\", \"item_id\"]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "3098fb55-8352-44e5-b9d2-02d1a82b2193", "metadata": {}, "outputs": [], "source": ["inv = inv.merge(\n", "    mig[[\"outlet_id\", \"item_id\"]].drop_duplicates(), on=[\"outlet_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "896345e4-c3a1-4d30-b0cb-b7b65f43d657", "metadata": {}, "outputs": [], "source": ["inv = inv[inv[\"fast_flag\"] == 1]"]}, {"cell_type": "markdown", "id": "ab771cbf-5937-4687-b566-157cc78cc12f", "metadata": {}, "source": ["### STO Info"]}, {"cell_type": "code", "execution_count": null, "id": "71b683c9-654f-4376-8898-91802e6551c4", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(\n", "    f\"\"\"\n", "select f.id facility_id, \n", "       s.outlet_id,\n", "       s.id sto_id, \n", "       item_id \n", "from lake_po.sto s\n", "join lake_po.sto_items si on si.sto_id = s.id\n", "join lake_retail.console_outlet o on o.id = s.outlet_id\n", "join lake_crates.facility f on f.id = o.facility_id\n", "WHERE s.outlet_id in {*OID_,}\n", "and si.item_id in {*set(inv['item_id']),}\n", "and s.created_at >= '2022-09-13'::TIMESTAMP - interval '5.5 hrs'\n", "and s.created_at < CURRENT_DATE::TIMESTAMP - interval '5.5 hrs'\n", "and s.zone_identifier ilike '%%regular%%'\n", "group by 1,2,3,4\n", "\"\"\",\n", "    redshift,\n", ")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "39799b23-b653-4e73-82c3-2d60f78c1f8f", "metadata": {}, "outputs": [], "source": ["df = df.merge(inv, on=[\"outlet_id\", \"item_id\"])\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "55cdc855-d26c-44f9-b5ea-dd90ffb49290", "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(\n", "    f\"\"\"\n", "select f.id facility_id, \n", "       f.name facility_name, \n", "       DATE(convert_timezone('Asia/Kolkata', s.created_at)) \"CREATED_ON\", \n", "       s.id sto_id, \n", "       REPLACE(s.zone_identifier, 'FAST_', '') zone_identifier,\n", "       COUNT(DISTINCT item_id) num_sku, \n", "       SUM(expected_quantity) ordered_quantity\n", "from lake_po.sto s\n", "join lake_po.sto_items si on si.sto_id = s.id\n", "join lake_retail.console_outlet o on o.id = s.outlet_id\n", "join lake_crates.facility f on f.id = o.facility_id\n", "WHERE s.outlet_id in {*OID_,}\n", "and s.id in {*set(df['sto_id']),}\n", "and s.created_at >= '2022-09-13'::TIMESTAMP - interval '5.5 hrs'\n", "and s.created_at < CURRENT_DATE::TIMESTAMP - interval '5.5 hrs'\n", "and s.zone_identifier ilike '%%regular%%'\n", "group by 1,2,3,4,5\n", "\"\"\",\n", "    redshift,\n", ")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9dab053e-358e-452b-a877-76898f46f40a", "metadata": {}, "outputs": [], "source": ["df[df.duplicated([\"sto_id\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "78a943a1-f2c0-4411-81b5-5898b03fe105", "metadata": {}, "outputs": [], "source": ["len(df)"]}, {"cell_type": "markdown", "id": "05a015d4-a521-4716-9eec-4fad6bdaabb2", "metadata": {}, "source": ["### Floor Info"]}, {"cell_type": "code", "execution_count": null, "id": "e12c66cc-57ca-4e8b-8332-18c71f8534ac", "metadata": {}, "outputs": [], "source": ["f = pd.read_sql_query(\n", "    f\"\"\"\n", "select DISTINCT o.facility_id, \n", "                REPLACE(sl.zone_identifier, 'FAST_', '') zone_identifier,\n", "                f.level floor\n", "from lake_warehouse_location.warehouse_outlet_mapping om\n", "join lake_warehouse_location.warehouse_storage_location sl on sl.warehouse_id = om.warehouse_id\n", "and om.outlet_id in {*OID_,} \n", "and sl.zone_identifier ilike '%%regular%%'\n", "join lake_warehouse_location.warehouse_shelf s on s.id = sl.shelf_id\n", "join lake_warehouse_location.warehouse_rack r on r.id = s.rack_id\n", "join lake_warehouse_location.warehouse_aisle a on a.id = r.aisle_id\n", "join lake_warehouse_location.warehouse_zone z on z.id = a.zone_id\n", "join lake_warehouse_location.warehouse_floor f on f.id = z.floor_id\n", "join lake_retail.console_outlet o on o.id = om.outlet_id\n", "\"\"\",\n", "    redshift,\n", ")\n", "f.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3a42afb-089f-4b95-847b-a807ccaeae30", "metadata": {}, "outputs": [], "source": ["f[f.duplicated([\"facility_id\", \"zone_identifier\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "f3cc326f-b6f4-4470-9567-1bb93bd788f0", "metadata": {}, "outputs": [], "source": ["df = df.merge(f, on=[\"facility_id\", \"zone_identifier\"])"]}, {"cell_type": "code", "execution_count": null, "id": "89a7d9a9-d670-4b14-b099-49965599d054", "metadata": {}, "outputs": [], "source": ["len(df)"]}, {"cell_type": "markdown", "id": "6344d377-651c-43bb-a27a-92ec06e81643", "metadata": {}, "source": ["### Picking Info"]}, {"cell_type": "code", "execution_count": null, "id": "bd1590d0-01ae-4221-a92a-d857fd858465", "metadata": {}, "outputs": [], "source": ["pl_df = pd.read_sql_query(\n", "    f\"\"\"\n", "select f2.id facility_id, \n", "       f2.name facility_name,\n", "       DATE(convert_timezone('Asia/Kolkata', pl.created_at)) pl_created_on,\n", "       w.entity_id sto_id,\n", "       -- pli.item_id,\n", "       -- pli.item_name,\n", "       pl.id pl_id,\n", "       pl.assigned_to_name picker_id,\n", "       f.level floor,\n", "       COUNT(DISTINCT pli.item_id) num_sku,\n", "       sum(plil.quantity) picked_quantity,\n", "       min(pl.picking_started_at + interval '5.5 hrs') picking_started_at,\n", "       max(pll.pos_timestamp + interval '5.5 hrs') picklist_completed_at,\n", "       max(pll2.pos_timestamp + interval '5.5 hrs') billed_at,\n", "       datediff(minute, min(pl.picking_started_at), max(pll.pos_timestamp)) picking_time,\n", "       datediff(minute, min(pl.picking_started_at), max(pll2.pos_timestamp)) p2b,\n", "       datediff(minute, max(plil.updated_at), max(pll2.pos_timestamp)) l2b\n", "       -- plil.updated_at + interval '5.5 hrs' item_picked_at\n", "from (select *\n", "      from lake_warehouse_location.warehouse_wave\n", "      WHERE entity_id in {*set(df['sto_id']),}) w\n", "join lake_warehouse_location.warehouse_pick_list pl on pl.wave_id = w.id and w.wave_type = 4\n", "join lake_warehouse_location.warehouse_pick_list_item pli on pli.pick_list_id = pl.id\n", "join lake_warehouse_location.pick_list_log pll on pll.pick_list_id = pl.id and pll.picklist_state = 4\n", "join lake_warehouse_location.pick_list_log pll2 on pll2.pick_list_id = pl.id and pll2.picklist_state = 10\n", "join lake_warehouse_location.warehouse_pick_list_item_location plil on plil.pick_list_item_id = pli.id\n", "join lake_warehouse_location.warehouse_outlet_mapping om on om.outlet_id = pl.outlet_id\n", "join lake_warehouse_location.warehouse_storage_location sl on sl.id = plil.location_id \n", "join lake_warehouse_location.warehouse_shelf s on s.id = sl.shelf_id\n", "join lake_warehouse_location.warehouse_rack r on r.id = s.rack_id\n", "join lake_warehouse_location.warehouse_aisle a on a.id = r.aisle_id\n", "join lake_warehouse_location.warehouse_zone z on z.id = a.zone_id\n", "join lake_warehouse_location.warehouse_floor f on f.id = z.floor_id\n", "join lake_retail.console_outlet o on o.id = pl.outlet_id\n", "join lake_crates.facility f2 on f2.id = o.facility_id\n", "and om.warehouse_id = sl.warehouse_id\n", "WHERE sl.zone_identifier ilike '%%regular%%'\n", "group by 1,2,3,4,5,6,7\n", "\"\"\",\n", "    redshift,\n", ")\n", "pl_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a582846d-2715-4c02-b1c3-e7538f943189", "metadata": {}, "outputs": [], "source": ["len(pl_df)"]}, {"cell_type": "code", "execution_count": null, "id": "0358c4a7-dce1-459e-98c1-3ed2b1412354", "metadata": {}, "outputs": [], "source": ["pl_df[\"sto_id\"].nunique(), len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "588eae60-421a-4a04-a4ee-e6b3f9836e07", "metadata": {}, "outputs": [], "source": ["pl_df[pl_df.duplicated([\"pl_id\", \"floor\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "fc4b876e-63af-42cc-a0dd-e7f7e57172bd", "metadata": {}, "outputs": [], "source": ["len(pl_df[pl_df.duplicated([\"pl_id\"])])"]}, {"cell_type": "code", "execution_count": null, "id": "26d6051d-95b0-47b8-94a0-a9170e34f04b", "metadata": {}, "outputs": [], "source": ["pl_df = pl_df[~pl_df[\"pl_id\"].isin(pl_df[pl_df.duplicated([\"pl_id\"])][\"pl_id\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "3beaadb1-f38d-4b05-9758-1b3d76269bf6", "metadata": {}, "outputs": [], "source": ["# fls = pl_df[['sto_id', 'floor']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "a3c7fe71-53e6-4b73-a555-7f449a5aaf4e", "metadata": {}, "outputs": [], "source": ["# fls[fls.duplicated(['sto_id'])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "7bf237a4-dbf6-4aaa-b7d3-66d3ced3c33c", "metadata": {}, "outputs": [], "source": ["# df2 = df.merge(fls, on = 'sto_id', how = 'left')\n", "# df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2235f207-229f-4151-8d65-38198c6b1ff8", "metadata": {}, "outputs": [], "source": ["# len(df2)"]}, {"cell_type": "code", "execution_count": null, "id": "01cb5f24-6ef9-4c83-a84c-c4d0a59e0304", "metadata": {}, "outputs": [], "source": ["# df2[(~df2['floor_y'].isna()) & (df2['floor_x'] != df2['floor_y'])]"]}, {"cell_type": "code", "execution_count": null, "id": "6c243dd0-d6f2-4319-a9b9-6aa0082859db", "metadata": {}, "outputs": [], "source": ["df[\"floor\"] = np.where(df[\"floor\"] == 0, \"G\", \"G+\")"]}, {"cell_type": "code", "execution_count": null, "id": "1d3949f2-990f-4793-a0fe-2fe794e43213", "metadata": {}, "outputs": [], "source": ["df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c04fd4d5-97a4-40d6-93be-44c248767552", "metadata": {}, "outputs": [], "source": ["df = (\n", "    df.groupby([\"facility_id\", \"facility_name\", \"created_on\", \"floor\"])\n", "    .agg(\n", "        num_stos=(\"sto_id\", \"nunique\"),\n", "        ordered_quantity=(\"ordered_quantity\", \"sum\"),\n", "        avg_num_sku=(\"num_sku\", \"mean\"),\n", "        num_sku=(\"num_sku\", \"sum\"),\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b877f5c0-3006-4db0-a43e-dd43d5ac67f0", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "691ffa40-2cca-4d5d-886b-b93de1f379fc", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df, \"1RM_2wH3DgU33z_Swmhuqd2sGW_Ay_xZefDNVU-iFj8s\", \"RAW1\")"]}, {"cell_type": "code", "execution_count": null, "id": "44fd594c-89db-45b5-9ab9-4add6298573b", "metadata": {}, "outputs": [], "source": ["# df.groupby(['facility_id',\n", "#             'created_on',\n", "#             'floor']).agg({'num_sku' : 'mean',\n", "#                            'sto_id' : 'nunique',\n", "#                            'ordered_quantity' : 'sum'})"]}, {"cell_type": "code", "execution_count": null, "id": "3999672e-7801-48e5-bae2-75b7d0f6512e", "metadata": {}, "outputs": [], "source": ["# pl_df.groupby('facility_id')[['picking_time', 'p2b']].quantile(0.9)"]}, {"cell_type": "code", "execution_count": null, "id": "fa2aed93-de86-4b05-87ac-e81ebe6af74b", "metadata": {}, "outputs": [], "source": ["# pl_df[(pl_df['facility_id'] == 264)\n", "#       & (pl_df['picking_time'] <= 155)].groupby(['pl_created_on', 'floor']).agg({'picking_time' : 'mean'})"]}, {"cell_type": "code", "execution_count": null, "id": "ea3e6c2c-bf6b-4e73-8417-5ac1c5f0b57f", "metadata": {}, "outputs": [], "source": ["# pl_df[(pl_df['facility_id'] == 264)\n", "#       & (pl_df['p2b'] <= 165)].groupby(['pl_created_on', 'floor']).agg({'p2b' : 'mean'})"]}, {"cell_type": "code", "execution_count": null, "id": "277f302e-5897-4963-8b07-c5a796a7781c", "metadata": {}, "outputs": [], "source": ["pl_df[\"floor\"] = np.where(pl_df[\"floor\"] == 0, \"G\", \"G+\")"]}, {"cell_type": "code", "execution_count": null, "id": "37c4d8ef-d9dd-4502-9cf5-e7d7164134b5", "metadata": {}, "outputs": [], "source": ["pldf = pd.DataFrame()\n", "for fid in pl_df[\"facility_id\"].unique():\n", "    pl_df2 = pl_df[pl_df[\"facility_id\"] == fid]\n", "    pl_df2 = pl_df2[pl_df2[\"picking_time\"] <= pl_df2[\"picking_time\"].quantile(0.9)]\n", "    pldf1 = (\n", "        pl_df2.groupby([\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"])\n", "        .agg(\n", "            picking_time_excl=(\"picking_time\", \"sum\"),\n", "            num_pl_picking_time=(\"pl_id\", \"nunique\"),\n", "            num_sku_picking_time=(\"num_sku\", \"sum\"),\n", "            picking_time_picked_quantity=(\"picked_quantity\", \"sum\"),\n", "            avg_picking_time=(\"picking_time\", \"mean\"),\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    pl_df2 = pl_df[pl_df[\"facility_id\"] == fid]\n", "    pl_df2 = pl_df2[pl_df2[\"p2b\"] <= pl_df2[\"p2b\"].quantile(0.9)]\n", "    pldf2 = (\n", "        pl_df2.groupby([\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"])\n", "        .agg(\n", "            p2b_excl=(\"p2b\", \"sum\"),\n", "            num_pl_p2b=(\"pl_id\", \"nunique\"),\n", "            num_sku_p2b=(\"num_sku\", \"sum\"),\n", "            p2b_picked_quantity=(\"picked_quantity\", \"sum\"),\n", "            avg_p2b=(\"p2b\", \"mean\"),\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    pl_df4 = pl_df[pl_df[\"facility_id\"] == fid]\n", "    pl_df4 = pl_df4[pl_df4[\"l2b\"] <= pl_df4[\"l2b\"].quantile(0.9)]\n", "    pldf4 = (\n", "        pl_df4.groupby([\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"])\n", "        .agg(\n", "            l2b_excl=(\"l2b\", \"sum\"),\n", "            num_pl_l2b=(\"pl_id\", \"nunique\"),\n", "            num_sku_l2b=(\"num_sku\", \"sum\"),\n", "            l2b_picked_quantity=(\"picked_quantity\", \"sum\"),\n", "            avg_l2b=(\"l2b\", \"mean\"),\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    pldf2 = pldf1.merge(\n", "        pldf2, on=[\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"]\n", "    )\n", "    pldf2 = pldf2.merge(\n", "        pldf4, on=[\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"]\n", "    )\n", "    pldf = pldf.append(pldf2)"]}, {"cell_type": "code", "execution_count": null, "id": "3696f607-cbe8-4223-8a2a-a8f88f90b35e", "metadata": {}, "outputs": [], "source": ["pldf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2aea6a14-6165-4dad-919b-4abc29a62cce", "metadata": {}, "outputs": [], "source": ["pl_df = (\n", "    pl_df.groupby([\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"])\n", "    .agg(\n", "        num_pickers=(\"picker_id\", \"nunique\"), picked_quantity=(\"picked_quantity\", \"sum\")\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "40eb7d89-e5f2-4acf-8760-b020466f9816", "metadata": {}, "outputs": [], "source": ["pldf = pl_df.merge(pldf, on=[\"facility_id\", \"facility_name\", \"pl_created_on\", \"floor\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a424942a-7e72-4ee4-a3a3-519fdfed7aa3", "metadata": {}, "outputs": [], "source": ["pldf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4cd3f0a1-8621-4463-8aba-d613a37a06da", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(pldf, \"1RM_2wH3DgU33z_Swmhuqd2sGW_Ay_xZefDNVU-iFj8s\", \"RAW\")"]}, {"cell_type": "code", "execution_count": null, "id": "568bd6f5-7075-4a7e-aea4-954b5d2abf72", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}