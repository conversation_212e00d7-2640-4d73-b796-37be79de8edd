{"cells": [{"cell_type": "code", "execution_count": null, "id": "1ac33b72-5d2c-4212-ae1d-99217948c951", "metadata": {}, "outputs": [], "source": ["!pip install -q pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "25836861-a7e1-4945-9196-579c4d2efa95", "metadata": {}, "outputs": [], "source": ["import pandasql\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "5f8e9e26-3488-4181-9ea4-6e642f792944", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "7d12f204-95fa-425e-97bc-249765e3ada9", "metadata": {}, "outputs": [], "source": ["OID_ = [1104, 4170, 2653]"]}, {"cell_type": "code", "execution_count": null, "id": "58c13839-ed56-4a0d-856d-4ba29de5167b", "metadata": {}, "outputs": [], "source": ["inv = pd.read_sql_query(\n", "    f\"\"\"\n", "select updated_on, outlet_id,\n", "       o.name outlet_name,\n", "       m.item_id, \n", "       id.name item_name,\n", "       l0_id,\n", "       l0,\n", "       l1_id,\n", "       l1,\n", "       iz.zone_type,\n", "       coalesce(fast_flag, 0) fast_flag,\n", "       m.active\n", "from (select DATE(updated_at + interval '5' hour + interval '30' minute) updated_on, \n", "       outlet_id, \n", "       item_id, \n", "       case when value = 'TRUE' and active = True then 1 else 0 end fast_flag,\n", "       active\n", "from lake_view_warehouse_location.item_outlet_configs\n", "where config_type_id = 2  \n", "-- and active = True\n", "and outlet_id in {*OID_,}) m\n", "join lake_retail.console_outlet o on o.id = m.outlet_id\n", "left join lake_rpc.item_details id on id.item_id = m.item_id\n", "left join lake_warehouse_location.item_zone_type iz on iz.handling_type = cast(id.handling_type as int)\n", "and iz.storage_type = cast(id.storage_type as int)\n", "left join lake_rpc.item_category_details icd on icd.item_id = m.item_id\n", "\"\"\",\n", "    presto,\n", ")\n", "inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "48d04a49-6174-4770-a704-625ff68e8a12", "metadata": {}, "outputs": [], "source": ["# inv[inv.duplicated(['outlet_id', 'item_id'])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "5e72c5bb-3b20-4d26-9ed7-1430a3a9aa10", "metadata": {}, "outputs": [], "source": ["mig = pb.from_sheets(\"1Ard2Cq6nKtAmv1y4yVRja-I_0BYf4RuqgHt0bMhmsK8\", \"To be moved\")\n", "mig.head()"]}, {"cell_type": "code", "execution_count": null, "id": "28ffde56-b737-4593-854a-e244b23c7923", "metadata": {}, "outputs": [], "source": ["mig[[\"outlet_id\", \"item_id\"]] = mig[[\"outlet_id\", \"item_id\"]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "08488275-ec54-4aca-a10c-3296087184a2", "metadata": {}, "outputs": [], "source": ["inv = inv.merge(\n", "    mig[[\"outlet_id\", \"item_id\"]].drop_duplicates(), on=[\"outlet_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "da5017c8-e901-42f9-be48-63f1db1ff6f1", "metadata": {}, "outputs": [], "source": ["inv.groupby([\"outlet_id\", \"fast_flag\"]).item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "e259f21f-f505-486c-8549-8f5d24b3915f", "metadata": {}, "outputs": [], "source": ["# mig.groupby('outlet_id').item_id.nunique()"]}, {"cell_type": "markdown", "id": "71e9dcce-f7c8-499b-806d-561f0e013375", "metadata": {}, "source": ["### Ideal Zone Info"]}, {"cell_type": "code", "execution_count": null, "id": "1299c807-25d7-42ed-b25e-9ecfb44d252c", "metadata": {}, "outputs": [], "source": ["ainv = pd.read_sql_query(\n", "    f\"\"\"\n", "WITH D as\n", "  (select outlet_id,\n", "       o.name outlet_name,\n", "       m.item_id, \n", "       id.name item_name,\n", "       l0_id,\n", "       l0,\n", "       l1_id,\n", "       l1,\n", "       iz.zone_type,\n", "       coalesce(fast_flag, 0) fast_flag\n", "from (select DATE(updated_at + interval '5' hour + interval '30' minute) updated_on, \n", "       outlet_id, \n", "       item_id, \n", "       case when value = 'TRUE' then 1 else 0 end fast_flag\n", "from lake_view_warehouse_location.item_outlet_configs\n", "where config_type_id = 2 \n", "-- and value = 'TRUE' \n", "-- and active = True\n", "and outlet_id in {*OID_,}) m\n", "join lake_retail.console_outlet o on o.id = m.outlet_id\n", "join lake_rpc.item_details id on id.item_id = m.item_id\n", "join lake_warehouse_location.item_zone_type iz on iz.handling_type = cast(id.handling_type as int)\n", "and iz.storage_type = cast(id.storage_type as int)\n", "join lake_rpc.item_category_details icd on icd.item_id = m.item_id),\n", "\n", "    actual_zones as\n", "  (SELECT wom.outlet_id,\n", "          a.zone_identifier,\n", "          b.zone_type,\n", "          coalesce(a.fast_moving_zone, 0) fast_moving_zone,\n", "          coalesce(l0_id,0) AS l0_id,\n", "          coalesce(l1_id,0) AS l1_id\n", "   FROM lake_warehouse_location.item_category_zone_type_zone_id b\n", "   INNER JOIN lake_warehouse_location.warehouse_zone_id_mapping a ON a.id = b.zone_id\n", "   INNER JOIN lake_warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id = a.warehouse_id\n", "   WHERE outlet_id in {*OID_,}\n", "     AND a.enabled = 1\n", "     AND b.enabled = 1)\n", "\n", "SELECT a.outlet_id,\n", "       -- a.outlet_name,\n", "       a.item_id, \n", "       -- a.item_name,\n", "       -- a.l0_id,\n", "       -- a.l0,\n", "       -- a.l1_id,\n", "       -- a.l1,\n", "       -- a.zone_type,\n", "       -- a.fast_flag,\n", "                   coalesce(z2.zone_identifier,coalesce(z1.zone_identifier,z.zone_identifier)) AS actual_zone\n", "                   -- a.quantity\n", "                   -- (CASE\n", "                   --      WHEN a.zone_identifier != actual_zone THEN 'YES'\n", "                   --      ELSE 'NO'\n", "                   --  END) AS different_zones\n", "   FROM D a\n", "   LEFT JOIN actual_zones z2 ON z2.zone_type = a.zone_type\n", "   AND z2.l1_id = a.l1_id\n", "   AND z2.l0_id = a.l0_id\n", "   AND z2.outlet_id = a.outlet_id\n", "   and a.fast_flag = z2.fast_moving_zone\n", "   LEFT JOIN actual_zones z1 ON z1.zone_type = a.zone_type\n", "   AND z1.l0_id = a.l0_id\n", "   AND z1.l1_id = 0\n", "   AND z1.l0_id != 0\n", "   AND z1.outlet_id = a.outlet_id\n", "   and a.fast_flag = z1.fast_moving_zone\n", "   LEFT JOIN actual_zones z ON z.zone_type = a.zone_type\n", "   AND z.l0_id = 0\n", "   AND z.outlet_id = a.outlet_id\n", "   and a.fast_flag = z.fast_moving_zone\n", "\"\"\",\n", "    presto,\n", ")\n", "ainv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0e738b29-64c9-4e02-9019-c10ad00daeec", "metadata": {}, "outputs": [], "source": ["ainv[\"key\"] = ainv[\"outlet_id\"].astype(str) + \" \" + ainv[\"item_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "878a5980-a29d-48a1-8d6b-1240c4bce2cf", "metadata": {}, "outputs": [], "source": ["ainv[ainv.duplicated([\"key\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "54aea0a3-19a1-4e22-bdab-c8201f0e990b", "metadata": {}, "outputs": [], "source": ["# ainv[(ainv['key'] == '4170 10045197')]"]}, {"cell_type": "code", "execution_count": null, "id": "6eb85ae5-7809-4b60-98b8-c6b518826e32", "metadata": {}, "outputs": [], "source": ["ainv2 = ainv.merge(mig, on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3a3d4028-6db5-4190-a765-e0409ba79808", "metadata": {}, "outputs": [], "source": ["ainv2[ainv2.duplicated([\"key\"])]"]}, {"cell_type": "code", "execution_count": null, "id": "2c609437-0fd4-4066-bdf7-d2628a1bbc02", "metadata": {}, "outputs": [], "source": ["linv = pd.read_sql_query(\n", "    f\"\"\"\n", "select wom.outlet_id, \n", "       wil.item_id,\n", "       f.level floor,\n", "       wsl.zone_identifier,\n", "       sum(wil.quantity) quantity\n", "from lake_warehouse_location.warehouse_item_location wil\n", "join lake_warehouse_location.warehouse_storage_location wsl on wsl.id = wil.location_id\n", "join lake_warehouse_location.warehouse_outlet_mapping wom on wom.warehouse_id = wil.warehouse_id\n", "join lake_view_warehouse_location.warehouse_shelf s on s.id = wsl.shelf_id\n", "join lake_view_warehouse_location.warehouse_rack r on r.id = s.rack_id\n", "join lake_view_warehouse_location.warehouse_aisle a on a.id = r.aisle_id\n", "join lake_view_warehouse_location.warehouse_zone z on z.id = a.zone_id\n", "join lake_view_warehouse_location.warehouse_floor f on f.id = z.floor_id\n", "where wil.active = 1 \n", "and wil.quantity > 0\n", "and wom.outlet_id in {*OID_,}\n", "and zone_identifier like '%%REGULAR%%'\n", "group by 1,2,3,4\n", "\"\"\",\n", "    presto,\n", ")\n", "linv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "40522f8c-89ae-491b-a305-f4c03db162ef", "metadata": {}, "outputs": [], "source": ["linv[linv.duplicated([\"outlet_id\", \"item_id\", \"zone_identifier\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "68efa256-3c64-4220-9abe-fba7e9b3b6c6", "metadata": {}, "outputs": [], "source": ["m = inv.merge(\n", "    ainv[~ainv[\"key\"].isin(ainv[ainv[\"key\"].duplicated()][\"key\"])],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "m.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aee1dcd6-ee2a-4871-ac1e-51136b310a96", "metadata": {}, "outputs": [], "source": ["m.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "76864876-c2d4-4b7d-9d50-18c5443980e6", "metadata": {}, "outputs": [], "source": ["len(inv), len(m)"]}, {"cell_type": "code", "execution_count": null, "id": "d4577fec-b79d-4f1a-b5a3-34cd5c2862ea", "metadata": {}, "outputs": [], "source": ["final = m.merge(linv, on=[\"outlet_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "754678f1-40c2-4902-bd10-ba7830b85afb", "metadata": {}, "outputs": [], "source": ["len(final)"]}, {"cell_type": "code", "execution_count": null, "id": "ba976689-1328-4041-907a-ee0bf9b4941a", "metadata": {}, "outputs": [], "source": ["final.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "887edf03-76ed-4bf5-93c5-d18bd6afa9b3", "metadata": {}, "outputs": [], "source": ["# final = final.merge(my, on = ['outlet_id', 'item_id'], how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "b99f6bb9-b7bc-4776-adcf-e1db751ab60f", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0644750-57cf-4fe5-8b91-9750560f3d25", "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"updated_on\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0_id\",\n", "        \"l0\",\n", "        \"l1_id\",\n", "        \"l1\",\n", "        \"zone_type\",\n", "        \"fast_flag\",\n", "        \"actual_zone\",\n", "        \"floor\",\n", "        \"zone_identifier\",\n", "        \"quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "051ffe90-5944-493f-a671-4812521d7b60", "metadata": {}, "outputs": [], "source": ["final[final.duplicated([\"outlet_id\", \"item_id\", \"zone_identifier\"])].head()"]}, {"cell_type": "code", "execution_count": null, "id": "47a738d3-e84c-48dc-9186-ed2532b20d2a", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final, \"1Ard2Cq6nKtAmv1y4yVRja-I_0BYf4RuqgHt0bMhmsK8\", \"Migration raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "97c1104b-81f6-4725-8c3c-daa6af6b6375", "metadata": {}, "outputs": [], "source": ["D = pandasql.sqldf(\n", "    \"\"\"\n", "select D.outlet_id 'Outlet ID', \n", "       D.outlet_name 'Outlet',\n", "       -- D.floor,\n", "       -- <PERSON><PERSON>updated_on 'Tagged On',\n", "       <PERSON>.fast_flag 'fast flag',\n", "       o.'SKU Count (As per ops)',\n", "       -- COUNT(DISTINCT CASE \n", "       --                    WHEN D.zone_identifier like '%%FAST%%' \n", "       --                    THEN D.zone_identifier ELSE NULL \n", "       --                END) 'No. fast zones',\n", "       COUNT(DISTINCT D.item_id) 'SKU Count (To be moved)',\n", "       SUM(D.quantity) 'SKUs Quantity (To be moved)',\n", "       COUNT(DISTINCT CASE \n", "                          WHEN D.zone_identifier NOT LIKE '%%FAST%%'\n", "                          THEN D.item_id ELSE NULL \n", "                      END) 'SKU Count (Moved)',\n", "       SUM(CASE \n", "               WHEN D.zone_identifier NOT LIKE '%%FAST%%'\n", "               THEN D.quantity ELSE 0 \n", "           END) 'SKUs Quantity (Moved)'\n", "from (select *\n", "      from final where quantity > 0\n", "      and fast_flag = 1) D\n", "join (select outlet_id, fast_flag, COUNT(DISTINCT item_id) 'SKU Count (As per ops)'\n", "      from inv\n", "      group by 1,2) o on o.outlet_id = D.outlet_id and o.fast_flag = D.fast_flag\n", "group by 1,2,3,4\n", "\"\"\"\n", ")\n", "D.head()"]}, {"cell_type": "code", "execution_count": null, "id": "525046c6-6a8c-4cd2-8c06-e2ccf26232d1", "metadata": {}, "outputs": [], "source": ["D[\"SKU Count (Moved)\"] = D[\"SKU Count (To be moved)\"] - D[\"SKU Count (Moved)\"]\n", "D[\"SKUs Quantity (Moved)\"] = (\n", "    D[\"SKUs Quantity (To be moved)\"] - D[\"SKUs Quantity (Moved)\"]\n", ")\n", "D"]}, {"cell_type": "code", "execution_count": null, "id": "e43d829b-ae46-4da4-9159-81e5635a6f9a", "metadata": {}, "outputs": [], "source": ["D2 = pandasql.sqldf(\n", "    \"\"\"\n", "select D.outlet_id 'Outlet ID', \n", "       D.outlet_name 'Outlet',\n", "       -- D.floor,\n", "       -- <PERSON><PERSON>updated_on 'Tagged On',\n", "       <PERSON>.fast_flag 'fast flag',\n", "       o.'SKU Count (As per ops)',\n", "       -- COUNT(DISTINCT CASE \n", "       --                    WHEN D.zone_identifier like '%%FAST%%' \n", "       --                    THEN D.zone_identifier ELSE NULL \n", "       --                END) 'No. fast zones',\n", "       COUNT(DISTINCT D.item_id) 'SKU Count (To be moved)',\n", "       SUM(D.quantity) 'SKUs Quantity (To be moved)',\n", "       COUNT(DISTINCT CASE \n", "                          WHEN D.zone_identifier LIKE '%%FAST%%'\n", "                          THEN D.item_id ELSE NULL \n", "                      END) 'SKU Count (Moved)',\n", "       SUM(CASE \n", "               WHEN D.zone_identifier LIKE '%%FAST%%'\n", "               THEN D.quantity ELSE 0 \n", "           END) 'SKUs Quantity (Moved)'\n", "from (select *\n", "      from final where quantity > 0\n", "      and fast_flag = 0) D\n", "join (select outlet_id, fast_flag, COUNT(DISTINCT item_id) 'SKU Count (As per ops)'\n", "      from inv\n", "      group by 1,2) o on o.outlet_id = D.outlet_id and o.fast_flag = D.fast_flag\n", "group by 1,2,3,4\n", "\"\"\"\n", ")\n", "D2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f0e54b56-2903-4791-9bc7-42f44cb260cb", "metadata": {}, "outputs": [], "source": ["D2[\"SKU Count (Moved)\"] = D2[\"SKU Count (To be moved)\"] - D2[\"SKU Count (Moved)\"]\n", "D2[\"SKUs Quantity (Moved)\"] = (\n", "    D2[\"SKUs Quantity (To be moved)\"] - D2[\"SKUs Quantity (Moved)\"]\n", ")\n", "D2"]}, {"cell_type": "code", "execution_count": null, "id": "22d58e6e-78d2-4372-bc2d-e5835aa4e497", "metadata": {}, "outputs": [], "source": ["D = D.append(D2)"]}, {"cell_type": "code", "execution_count": null, "id": "db03c340-f04a-4dcc-8096-422547b860d9", "metadata": {}, "outputs": [], "source": ["D[\"Pending SKU Count\"] = D[\"SKU Count (To be moved)\"] - D[\"SKU Count (Moved)\"]\n", "D[\"Pending SKUs Quantity\"] = (\n", "    D[\"SKUs Quantity (To be moved)\"] - D[\"SKUs Quantity (Moved)\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8f5a16d-b0fc-4d8f-a37e-5ae0450d8b0f", "metadata": {}, "outputs": [], "source": ["D"]}, {"cell_type": "code", "execution_count": null, "id": "31d54b9c-497d-4aa2-896a-9c7b600075d4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(D, \"1Ard2Cq6nKtAmv1y4yVRja-I_0BYf4RuqgHt0bMhmsK8\", \"Migration Summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c3b29ac-0045-4ccb-a50b-8e2da1c0585f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}