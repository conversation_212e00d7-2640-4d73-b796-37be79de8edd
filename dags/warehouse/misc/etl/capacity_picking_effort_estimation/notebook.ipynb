{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -q catboost\n", "!pip install -q joblib\n", "!pip install -q scikit-learn==0.22.2.post1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pickle\n", "from collections import defaultdict\n", "import json\n", "from ast import literal_eval\n", "import numpy as np\n", "import math\n", "from sklearn.linear_model import LinearRegression\n", "from catboost import CatBoostRegressor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "utc_now = datetime.utcnow()\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["from time import strftime\n", "\n", "start_date = utc_now - timed<PERSON><PERSON>(minutes=1400)\n", "start_date = start_date.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "end_date = utc_now.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "dag_id = \"misc_picking_effort_estimation_etl_picking_effort_estimation_v5\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.width\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"ims_suborder_status\": \"order status in ims\",\n", "    \"updated_at\": \"timestamp of update\",\n", "    \"slot_handover_time\": \"handover time of slot\",\n", "    \"SPO\": \"SKUs per order\",\n", "    \"IPO\": \"Items per order\",\n", "    \"Handling_Regular\": \"Handling type regular\",\n", "    \"Handling_Fragile\": \"Handling type fragile\",\n", "    \"Handling_Large\": \"Large handling type\",\n", "    \"Storage_Regular\": \"regular storage type\",\n", "    \"Storage_Heavy\": \"Heavy storage type\",\n", "    \"weight_in_kgs\": \"weight of order in kgs\",\n", "    \"picking_time_predicted\": \"predicted picking time from outbound model\",\n", "    \"day_ideal\": \"day of picking\",\n", "    \"picking_time_ideal\": \"picking time of outlet based on ideal order definition\",\n", "    \"weight_in_kgs_ideal\": \"ideal weight of order at outlet\",\n", "    \"SPO_ideal\": \"ideal skus per order\",\n", "    \"IPO_ideal\": \"ideal items per order\",\n", "    \"picking_effort\": \"ratio of picking time predicted to ideal picking time\",\n", "    \"is_ingested\": \"flag of model\",\n", "    \"model_name\": \"model used for prediction\",\n", "    \"station\": \"station of outlet\",\n", "    \"order_status\": \"status of order in OMS\",\n", "    \"suborder_status\": \"status of sub-order in OMS\",\n", "    \"slot_start\": \"slot start time\",\n", "    \"slot_end\": \"slot end time\",\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading Input Data\n", "\n", "- Loading Random Forest , CatBoost and Linear Regression Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename_cat_iod = \"/tmp/picking_time_ideal_cat_v3.csv\"\n", "local_filename_lr_iod = \"/tmp/picking_time_ideal_lr_v3.csv\"\n", "local_filename_cat_model = \"/tmp/cat_boostmodel.sav\"\n", "local_filename_lr_model = \"/tmp/linear_regression_model.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cloud_filename_cat_iod = \"/eep/warehouse/picking/data/picking_time_ideal_cat_v3.csv\"\n", "cloud_filename_lr_iod = \"/eep/warehouse/picking/data/picking_time_ideal_lr_v3.csv\"\n", "cloud_filename_cat_model = \"/eep/warehouse/picking/prod/model/catboost_model_v3.1.sav\"\n", "cloud_filename_lr_model = \"/eep/warehouse/picking/prod/model/linear_regression_v3.1.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "pb.from_s3(bucket_name, cloud_filename_cat_iod, local_filename_cat_iod)\n", "pb.from_s3(bucket_name, cloud_filename_cat_model, local_filename_cat_model)\n", "\n", "pb.from_s3(bucket_name, cloud_filename_lr_iod, local_filename_lr_iod)\n", "pb.from_s3(bucket_name, cloud_filename_lr_model, local_filename_lr_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3 = pd.read_csv(local_filename_cat_iod)\n", "picking_time_ideal_lr_v3 = pd.read_csv(local_filename_lr_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from catboost import CatBoost\n", "\n", "model = CatBoostRegressor()\n", "model.load_model(local_filename_cat_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["regressor = joblib.load(local_filename_lr_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3.Handling_Regular = (\n", "    picking_time_ideal_cat_v3.Handling_Regular.astype(float)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_lr_v3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oids = list(picking_time_ideal_cat_v3.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_query = \"\"\"\n", "SELECT DISTINCT\n", "    bom.external_id AS backend_merchant_id,\n", "    fom.external_id AS frontend_merchant_id,\n", "    oo.station,\n", "    oo.slot_start,\n", "    oo.slot_end,\n", "    oo.slot_type,\n", "    oo.type AS order_type,\n", "    os.type AS suborder_type,\n", "    oo.current_status AS order_status,\n", "    os.current_status AS suborder_status,\n", "    oo.id AS order_id,\n", "    os.id AS suborder_id\n", "FROM oms_suborder AS os\n", "INNER JOIN oms_order AS oo \n", "    ON os.order_id = oo.id\n", "    AND os.update_ts > now() - interval '120 mins'\n", "INNER JOIN oms_merchant AS bom\n", "    ON os.backend_merchant_id = bom.id\n", "INNER JOIN oms_merchant AS fom\n", "    ON oo.merchant_id = fom.id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_data = pd.read_sql(oms_query, con=pb.get_connection(\"[Replica] OMS Bifrost\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_data.suborder_status.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_data.suborder_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prediction_flow(oms_data):\n", "    query = \"\"\"\n", "    SELECT\n", "            osq.order_id as suborder_id,\n", "            osq.outlet_id,\n", "            ios.name AS order_status,\n", "            osq.updated_at,\n", "            osq.slot_handover_time,\n", "            osq.ancestor as order_id,\n", "            count(distinct osq.item_id) AS SPO,\n", "            sum(quantity) IPO,\n", "           count(CASE\n", "                     WHEN handling_type=1 THEN item_id\n", "                 END) AS Handling_Regular,\n", "           count(CASE\n", "                     WHEN handling_type=2 THEN item_id\n", "                 END) AS Handling_Fragile,\n", "           count(CASE\n", "                     WHEN handling_type=4 THEN item_id\n", "                 END) AS Handling_Large,\n", "           count(CASE\n", "                     WHEN storage_type = 1 THEN item_id\n", "                 END) AS Storage_Regular,\n", "           count(CASE\n", "                     WHEN storage_type = 5 THEN item_id\n", "                 END) AS Storage_Heavy,\n", "                 sum(weight_in_gm)/1000 weight_in_kgs\n", "        FROM (\n", "            SELECT\n", "                iod.outlet AS outlet_id,\n", "                iod.order_id,\n", "                iod.status_id,\n", "                iot.item_id AS item_id,\n", "                iot.quantity AS quantity,\n", "                iod.updated_at,\n", "                iod.slot_handover_time,\n", "                iod.ancestor,\n", "                weight_in_gm*quantity weight_in_gm,\n", "              handling_type,\n", "              storage_type\n", "            FROM ims.ims_order_details AS iod\n", "            LEFT JOIN ims.ims_order_items AS iot\n", "            ON iod.id = iot.order_details_id\n", "            LEFT JOIN \n", "\n", "            (SELECT item_id, weight_in_gm,handling_type,storage_type,active FROM rpc.product_product pp2 \n", "            WHERE pp2.id in (\n", "            SELECT max(id) as id FROM rpc.product_product pp \n", "            WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "            ) product ON product.item_id = iot.item_id AND product.active=1\n", "\n", "            WHERE iod.order_id IN ({suborder_id})\n", "\n", "            GROUP BY 1, 2, 3, 4,5,6, 7, 8,9,10,11\n", "        ) osq\n", "        LEFT JOIN ims.ims_order_status AS ios\n", "        ON osq.status_id = ios.id \n", "        group by 1,2,3,4,5,6\n", "        ORDER BY osq.updated_at DESC\n", "        \"\"\".format(\n", "        suborder_id=\",\".join([str(x) for x in oms_data.suborder_id.unique()])\n", "    )\n", "\n", "    df = pd.read_sql_query(query, con=pb.get_connection(\"[Replica] IMS\"))\n", "    df.rename(\n", "        {\n", "            \"spo\": \"SPO\",\n", "            \"ipo\": \"IPO\",\n", "            \"handling_regular\": \"Handling_Regular\",\n", "            \"handling_fragile\": \"Handling_Fragile\",\n", "            \"handling_large\": \"Handling_Large\",\n", "            \"storage_regular\": \"Storage_Regular\",\n", "            \"storage_heavy\": \"Storage_Heavy\",\n", "        },\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "\n", "    print(df.shape)\n", "\n", "    df = df[df.outlet_id.isin(oids)]\n", "\n", "    df.slot_handover_time = np.where(\n", "        df[\"slot_handover_time\"].isna(), df[\"updated_at\"], df[\"slot_handover_time\"]\n", "    )\n", "\n", "    df.slot_handover_time = pd.to_datetime(df.slot_handover_time)\n", "\n", "    data = df.copy()\n", "\n", "    data[\"index\"] = data.index\n", "\n", "    data[\"Handling_Regular_Large\"] = (data.Handling_Large > 0) & (\n", "        data.Handling_Regular > 0\n", "    )\n", "\n", "    data[\"Storage_Regular_Heavy\"] = (data.Storage_Regular > 0) & (\n", "        data.Storage_Heavy > 0\n", "    )\n", "\n", "    data[\"month\"] = data.slot_handover_time.dt.month\n", "    data[\"day\"] = data.slot_handover_time.dt.day\n", "\n", "    model.feature_names_\n", "\n", "    data.columns.values\n", "\n", "    con = pb.get_connection(\"retail\")\n", "    Query = \"\"\"SELECT a.id,\n", "           b.name as BusinessType\n", "    FROM retail.console_outlet a\n", "    left join retail.console_business_type b on b.id = a.business_type_id\n", "    \"\"\"\n", "    Outlet_Business_Type = pd.read_sql_query(Query, con)\n", "\n", "    data = data.merge(\n", "        Outlet_Business_Type, how=\"left\", left_on=\"outlet_id\", right_on=\"id\"\n", "    )\n", "    data.drop(\"id\", axis=1, inplace=True)\n", "\n", "    data.head()\n", "\n", "    z = pd.get_dummies(data.BusinessType, prefix=\"BusinessType\", drop_first=True)\n", "    if z.shape[1] != 0:\n", "        data = pd.merge(data.reset_index(), z.reset_index(), on=\"index\", how=\"left\")\n", "        data.drop([\"BusinessType\", \"level_0\"], axis=1, inplace=True)\n", "        data.head()\n", "    else:\n", "        data[\"BusinessType_Grocery\"] = 1\n", "        data.drop([\"BusinessType\"], axis=1, inplace=True)\n", "        data.head()\n", "\n", "    data.head()\n", "\n", "    data[\"BusinessType_B2B_Bulk\"] = 0\n", "    data[\"BusinessType_Cold\"] = 0\n", "    data[\"BusinessType_Dark Store - External\"] = 0\n", "    data[\"BusinessType_Dark Store - Grofers\"] = 0\n", "    data[\"BusinessType_Dropshipment\"] = 0\n", "    data[\"BusinessType_Large\"] = 0\n", "    data[\"BusinessType_Marketplace\"] = 0\n", "\n", "    data = data[\n", "        [\n", "            \"index\",\n", "            \"outlet_id\",\n", "            \"SPO\",\n", "            \"IPO\",\n", "            \"Handling_Regular\",\n", "            \"Handling_Fragile\",\n", "            \"Handling_Large\",\n", "            \"Storage_Regular\",\n", "            \"Storage_Heavy\",\n", "            \"weight_in_kgs\",\n", "            \"Handling_Regular_Large\",\n", "            \"Storage_Regular_Heavy\",\n", "            \"BusinessType_B2B_Bulk\",\n", "            \"BusinessType_Cold\",\n", "            \"BusinessType_Dark Store - External\",\n", "            \"BusinessType_Dark Store - Grofers\",\n", "            \"BusinessType_Dropshipment\",\n", "            \"BusinessType_Grocery\",\n", "            \"BusinessType_Large\",\n", "            \"BusinessType_Marketplace\",\n", "            \"month\",\n", "            \"day\",\n", "            \"suborder_id\",\n", "            \"order_status\",\n", "            \"updated_at\",\n", "            \"slot_handover_time\",\n", "            \"order_id\",\n", "        ]\n", "    ]\n", "\n", "    # data.drop([\"suborder_id\",\"order_status\",\"updated_at\",\"slot_handover_time\",\"order_id\"],axis=1,inplace = True)\n", "\n", "    def rename_cols(picking_time_ideal_cat_v3):\n", "        rename_cols = dict()\n", "        for cols in picking_time_ideal_cat_v3.columns.values:\n", "            if cols != \"outlet_id\" and cols != \"picking_time_ideal\":\n", "                rename_cols[cols] = cols + \"_ideal\"\n", "\n", "        picking_time_ideal_cat_v3.rename(rename_cols, axis=1, inplace=True)\n", "        return picking_time_ideal_cat_v3\n", "\n", "    # ideal_order_definition = picking_time_ideal_cat_v3.copy()\n", "    # data[\"picking_time_predicted\"] = model.predict(data)\n", "\n", "    # data = pd.merge(\n", "    #     data, rename_cols(ideal_order_definition), how=\"left\", on=[\"outlet_id\"]\n", "    # )\n", "    # data[\"picking_effort\"] = (\n", "    #     data[\"picking_time_predicted\"] / data[\"picking_time_ideal\"]\n", "    # )\n", "\n", "    def predict_effort(data, ideal_order_definition, model, model_name):\n", "\n", "        if model_name == \"catboost_model\":\n", "            try:\n", "                data[\"picking_time_predicted\"] = model.predict(\n", "                    data.drop(\n", "                        [\n", "                            \"suborder_id\",\n", "                            \"order_status\",\n", "                            \"updated_at\",\n", "                            \"slot_handover_time\",\n", "                            \"order_id\",\n", "                        ],\n", "                        axis=1,\n", "                    )\n", "                )\n", "                data = pd.merge(\n", "                    data,\n", "                    rename_cols(ideal_order_definition),\n", "                    how=\"left\",\n", "                    on=[\"outlet_id\"],\n", "                )\n", "                data[\"picking_effort\"] = (\n", "                    data[\"picking_time_predicted\"] / data[\"picking_time_ideal\"]\n", "                )\n", "\n", "                data[\"is_ingested\"] = 1\n", "            except:\n", "                data[\"picking_time_predicted\"] = np.nan\n", "                data[\"is_ingested\"] = 0\n", "\n", "            data[\"model_name\"] = model_name\n", "        return data\n", "\n", "    predictions = predict_effort(\n", "        data.copy(), picking_time_ideal_cat_v3.copy(), model, \"catboost_model\"\n", "    )\n", "\n", "    predictions.columns.values\n", "\n", "    # predictions = predictions.append(\n", "    #     predict_effort(\n", "    #         data.copy(), picking_time_ideal_lr_v3, regressor, \"linear_regression_model\"\n", "    #     ),\n", "    #     sort=False,\n", "    # )\n", "\n", "    predictions.head()\n", "\n", "    predictions.model_name.unique()\n", "\n", "    predictions.groupby(\"model_name\")[\"suborder_id\"].nunique()\n", "\n", "    # predictions[predictions.model_name == \"linear_regression_model\"].tail()\n", "\n", "    predictions.groupby(\"outlet_id\")[\"suborder_id\"].nunique()\n", "\n", "    p0_oids = predictions[\n", "        (predictions.model_name == \"catboost_model\") & (predictions.is_ingested == 1)\n", "    ].suborder_id.unique()\n", "\n", "    predictions.is_ingested.value_counts()\n", "\n", "    # p1_oids = set(p0_oids).union(\n", "    #     predictions[\n", "    #         (predictions.model_name == \"linear_regression_model\")\n", "    #         & (predictions.is_ingested == 1)\n", "    #     ].suborder_id.unique()\n", "    # )\n", "\n", "    # predictions[\"is_ingested\"] = np.where(\n", "    #     (predictions.model_name == \"linear_regression_model\")\n", "    #     & (predictions.suborder_id.isin(p0_oids)),\n", "    #     0,\n", "    #     predictions.is_ingested,\n", "    # )\n", "\n", "    df = predictions.dropna(subset=[\"IPO\", \"SPO\"]).copy()\n", "\n", "    df.groupby(\"outlet_id\")[\"suborder_id\"].nunique()\n", "\n", "    df.columns.values\n", "\n", "    df.IPO = df.IPO.astype(int)\n", "    df.SPO = df.SPO.astype(int)\n", "    df.SPO_ideal = df.SPO_ideal.astype(float)\n", "    df.IPO_ideal = df.IPO_ideal.astype(float)\n", "\n", "    df.order_id = df.order_id.astype(int)\n", "    df.suborder_id = df.suborder_id.astype(int)\n", "    oms_data.order_id = oms_data.order_id.astype(int)\n", "    oms_data.suborder_id = oms_data.suborder_id.astype(int)\n", "\n", "    df.rename({\"order_status\": \"ims_suborder_status\"}, axis=1, inplace=True)\n", "\n", "    finaldf = df.merge(oms_data, on=[\"order_id\", \"suborder_id\"], how=\"left\")\n", "\n", "    finaldf.head()\n", "\n", "    finaldf[\"dag_id\"] = dag_id\n", "\n", "    finaldf.dtypes\n", "\n", "    finaldf.suborder_status.unique()\n", "\n", "    finaldf.columns\n", "\n", "    finaldf1 = finaldf[\n", "        [\n", "            \"suborder_id\",\n", "            \"outlet_id\",\n", "            \"ims_suborder_status\",\n", "            \"updated_at\",\n", "            \"slot_handover_time\",\n", "            \"order_id\",\n", "            \"SPO\",\n", "            \"IPO\",\n", "            \"Handling_Regular\",\n", "            \"Handling_Fragile\",\n", "            \"Handling_Large\",\n", "            \"Storage_Regular\",\n", "            \"Storage_Heavy\",\n", "            \"weight_in_kgs\",\n", "            \"Handling_Regular_Large\",\n", "            \"Storage_Regular_Heavy\",\n", "            \"month\",\n", "            \"day\",\n", "            \"picking_time_predicted\",\n", "            \"Handling_Regular_ideal\",\n", "            \"picking_time_ideal\",\n", "            \"day_ideal\",\n", "            \"Storage_Regular_Heavy_ideal\",\n", "            \"Handling_Regular_Large_ideal\",\n", "            \"weight_in_kgs_ideal\",\n", "            \"Storage_Heavy_ideal\",\n", "            \"Handling_Large_ideal\",\n", "            \"Storage_Regular_ideal\",\n", "            \"SPO_ideal\",\n", "            \"Handling_Fragile_ideal\",\n", "            \"IPO_ideal\",\n", "            \"month_ideal\",\n", "            \"picking_effort\",\n", "            \"is_ingested\",\n", "            \"model_name\",\n", "            \"backend_merchant_id\",\n", "            \"frontend_merchant_id\",\n", "            \"station\",\n", "            \"slot_start\",\n", "            \"slot_end\",\n", "            \"slot_type\",\n", "            \"order_type\",\n", "            \"suborder_type\",\n", "            \"order_status\",\n", "            \"suborder_status\",\n", "            \"dag_id\",\n", "        ]\n", "    ]\n", "    return finaldf1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finaldf1 = prediction_flow(oms_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finaldf1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dtypes_dict = {\n", "    \"suborder_id\": \"bigint\",\n", "    \"outlet_id\": \"integer\",\n", "    \"ims_suborder_status\": \"varchar\",\n", "    \"updated_at\": \"timestamp\",\n", "    \"slot_handover_time\": \"timestamp\",\n", "    \"order_id\": \"bigint\",\n", "    \"SPO\": \"integer\",\n", "    \"IPO\": \"integer\",\n", "    \"Handling_Regular\": \"float8\",\n", "    \"Handling_Fragile\": \"float8\",\n", "    \"Handling_Large\": \"float8\",\n", "    \"Storage_Regular\": \"float8\",\n", "    \"Storage_Heavy\": \"float8\",\n", "    \"weight_in_kgs\": \"float8\",\n", "    \"Handling_Regular_Large\": \"bool\",\n", "    \"Storage_Regular_Heavy\": \"bool\",\n", "    \"month\": \"integer\",\n", "    \"day\": \"integer\",\n", "    \"picking_time_predicted\": \"float8\",\n", "    \"day_ideal\": \"float8\",\n", "    \"Handling_Regular_ideal\": \"float8\",\n", "    \"picking_time_ideal\": \"float8\",\n", "    \"Storage_Regular_Heavy_ideal\": \"boolean\",\n", "    \"Handling_Regular_Large_ideal\": \"boolean\",\n", "    \"weight_in_kgs_ideal\": \"float8\",\n", "    \"Storage_Heavy_ideal\": \"float8\",\n", "    \"Handling_Large_ideal\": \"float8\",\n", "    \"Storage_Regular_ideal\": \"float8\",\n", "    \"SPO_ideal\": \"float8\",\n", "    \"Handling_Fragile_ideal\": \"float8\",\n", "    \"IPO_ideal\": \"float8\",\n", "    \"month_ideal\": \"float8\",\n", "    \"picking_effort\": \"float8\",\n", "    \"is_ingested\": \"integer\",\n", "    \"model_name\": \"varchar\",\n", "    \"backend_merchant_id\": \"integer\",\n", "    \"frontend_merchant_id\": \"integer\",\n", "    \"station\": \"varchar\",\n", "    \"slot_start\": \"integer\",\n", "    \"slot_end\": \"integer\",\n", "    \"slot_type\": \"varchar\",\n", "    \"order_type\": \"varchar\",\n", "    \"suborder_type\": \"varchar\",\n", "    \"order_status\": \"varchar\",\n", "    \"suborder_status\": \"varchar\",\n", "    \"dag_id\": \"varchar\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": key,\n", "        \"type\": dtypes_dict[key],\n", "        \"description\": Description_Dict.get(\n", "            key, \"Column name sufficient for description\"\n", "        ),\n", "    }\n", "    for key in finaldf1.columns.values\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"new_picking_effort_estimation_project\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"order_id\", \"suborder_id\", \"outlet_id\"],\n", "    \"sortkey\": [\"order_id\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"effort_model_predictions\",\n", "}\n", "pb.to_redshift(finaldf1, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Appendix: Recalculating IOD from Ideal SPO/IPO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3[[\"outlet_id\", \"SPO\", \"IPO\", \"picking_time_ideal\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old = picking_time_ideal_cat_v3[[\"outlet_id\", \"IPO\", \"SPO\"]].sort_values(\n", "    by=[\"outlet_id\"]\n", ")\n", "old[\"SPO\"] = old[\"SPO\"].astype(float)\n", "old[\"IPO\"] = old[\"IPO\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ideal = pb.from_sheets(\"1OZCegNgTBUVJqGhAooC3a48iyKIjA6nAsvhUrhLexds\", \"PEEP\")\n", "ideal[\"outlet_id\"] = ideal[\"outlet_id\"].astype(int)\n", "ideal[\"SPO\"] = ideal[\"SPO\"].astype(float)\n", "ideal[\"IPO\"] = ideal[\"IPO\"].astype(float)\n", "ideal = ideal.sort_values(by=[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "asia_now = datetime.now(tz)\n", "time = (\n", "    str(asia_now.time().hour)\n", "    + \":\"\n", "    + str(asia_now.time().minute)\n", "    + \":\"\n", "    + str(asia_now.time().second)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = datetime.today().date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old.shape, ideal.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ideal.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def diff_columns(ideal, old):\n", "    j = []\n", "    for i in ideal[\"outlet_id\"]:\n", "        if (\n", "            ideal[ideal[\"outlet_id\"] == i][\"SPO\"].to_list()[0]\n", "            != old[old[\"outlet_id\"] == i][\"SPO\"].to_list()[0]\n", "        ):\n", "            j.append(i)\n", "            continue\n", "        if (\n", "            ideal[ideal[\"outlet_id\"] == i][\"IPO\"].to_list()[0]\n", "            != old[old[\"outlet_id\"] == i][\"IPO\"].to_list()[0]\n", "        ):\n", "            j.append(i)\n", "    return j\n", "\n", "\n", "j = diff_columns(ideal, old)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["j"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_iod(df, m, model_name):\n", "    features_dict = {\n", "        \"catboost\": list(set(df.columns.values) - set([\"picking_time_ideal\"]))\n", "    }\n", "    df[\"month\"] = asia_now.month\n", "    df[\"day\"] = asia_now.day\n", "    df.drop([\"picking_time_ideal\"], axis=1, inplace=True)\n", "    df[\"picking_time_ideal\"] = m.predict(df[features_dict[model_name]])\n", "    return df[set(features_dict[model_name]).union([\"picking_time_ideal\", \"outlet_id\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(j) > 0:\n", "    pb.send_email(\n", "        from_email=\"<EMAIL>\",\n", "        to_email=[\"<EMAIL>\"],\n", "        subject=\"Ideal IPO/SPO changed\",\n", "        html_content=\"\"\" Hi,<br><br> This is a test mail. please ignore. <br> Thanks, <br> <PERSON><PERSON>h\"\"\",\n", "    )\n", "    for Outlet_id in j:\n", "        a = ideal[ideal[\"outlet_id\"] == Outlet_id]\n", "        if a[\"IPO\"].to_list()[0] or a[\"SPO\"].to_list()[0]:\n", "            a[\"SPO\"] = a[\"SPO\"].astype(float)\n", "            a[\"IPO\"] = a[\"IPO\"].astype(float)\n", "            picking_time_ideal_cat_v3.SPO[\n", "                picking_time_ideal_cat_v3[\"outlet_id\"] == Outlet_id\n", "            ] = a[\"SPO\"].to_list()[0]\n", "            picking_time_ideal_cat_v3.IPO[\n", "                picking_time_ideal_cat_v3[\"outlet_id\"] == Outlet_id\n", "            ] = a[\"IPO\"].to_list()[0]\n", "    # upsert_data = pd.read_sql_query(sql=upsert, con=pb.get_connection(\"redshift\"))\n", "    picking_time_ideal_cat_v3.Storage_Heavy = (\n", "        picking_time_ideal_cat_v3.Storage_Heavy.astype(float)\n", "    )\n", "    picking_time_ideal_cat_v3 = calculate_iod(\n", "        picking_time_ideal_cat_v3.copy(), model, \"catboost\"\n", "    ).copy()\n", "    picking_time_ideal_cat_v3.to_csv(local_filename_cat_iod, index=None)\n", "    bucket_name = \"prod-dse-projects\"\n", "    pb.to_s3(local_filename_cat_iod, bucket_name, cloud_filename_cat_iod)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picking_time_ideal_cat_v3[[\"outlet_id\", \"IPO\", \"SPO\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Upsert T+1 predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(j) > 0:\n", "    print(j)\n", "    ### upserting T+1 predictions\n", "    upsert = \"\"\"select * from metrics.new_picking_effort_estimation_project\n", "            where date(slot_handover_time) > current_date and outlet_id in ({outlet})\n", "             \"\"\".format(\n", "        outlet=\",\".join([str(x) for x in j])\n", "    )\n", "    upsert_data = pd.read_sql_query(\n", "        sql=upsert, con=pb.get_connection(\"[Warehouse] Redshift\")\n", "    )\n", "    print(upsert_data.shape)\n", "    suborders = upsert_data[\n", "        [\n", "            \"backend_merchant_id\",\n", "            \"frontend_merchant_id\",\n", "            \"station\",\n", "            \"slot_start\",\n", "            \"slot_end\",\n", "            \"slot_type\",\n", "            \"order_type\",\n", "            \"suborder_type\",\n", "            \"order_status\",\n", "            \"suborder_status\",\n", "            \"order_id\",\n", "            \"suborder_id\",\n", "        ]\n", "    ]\n", "    final_upsert = prediction_flow(suborders)\n", "    pb.to_redshift(finaldf1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}