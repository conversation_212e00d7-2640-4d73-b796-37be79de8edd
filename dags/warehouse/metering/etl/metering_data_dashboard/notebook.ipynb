{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from sklearn.externals import joblib\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTLET_ID_LIST = (\n", "    85,\n", "    100,\n", "    116,\n", "    238,\n", "    240,\n", "    246,\n", "    279,\n", "    280,\n", "    287,\n", "    289,\n", "    292,\n", "    309,\n", "    314,\n", "    316,\n", "    334,\n", "    337,\n", "    340,\n", "    343,\n", "    346,\n", "    352,\n", "    354,\n", "    363,\n", "    364,\n", "    367,\n", "    369,\n", "    370,\n", "    396,\n", "    399,\n", "    451,\n", "    452,\n", "    459,\n", "    461,\n", "    475,\n", "    478,\n", "    481,\n", "    482,\n", "    491,\n", "    493,\n", "    503,\n", "    505,\n", "    512,\n", "    514,\n", "    520,\n", "    522,\n", "    528,\n", "    534,\n", "    537,\n", "    539,\n", "    603,\n", "    612,\n", "    618,\n", "    626,\n", ")\n", "\n", "BACKEND_MERCHANT_ID_LIST = (\n", "    26512,\n", "    25677,\n", "    25811,\n", "    28497,\n", "    28498,\n", "    28517,\n", "    28604,\n", "    28603,\n", "    28621,\n", "    28624,\n", "    28626,\n", "    28627,\n", "    28648,\n", "    28649,\n", "    28650,\n", "    28689,\n", "    28686,\n", "    28691,\n", "    28690,\n", "    28699,\n", "    28693,\n", "    28694,\n", "    28692,\n", "    28721,\n", "    28722,\n", "    28708,\n", "    28704,\n", "    28705,\n", "    28709,\n", "    28711,\n", "    28726,\n", "    28725,\n", "    28731,\n", "    28732,\n", "    28741,\n", "    28744,\n", "    28742,\n", "    28743,\n", "    28746,\n", "    28747,\n", "    28754,\n", "    28753,\n", "    28745,\n", "    28748,\n", "    28750,\n", "    28752,\n", "    28737,\n", "    28739,\n", "    28749,\n", "    28751,\n", "    28799,\n", "    28796,\n", "    28816,\n", "    28817,\n", ")\n", "ENV = \"prod\"\n", "run_id = \"Manual\"\n", "OUTPUT_BASE_DIR = \"output_metering\"\n", "start_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=1)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=15)).date()\n", "todays_date = (datetime.today() + timedelta(hours=5.5)).date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup database connections\n", "if ENV == \"stage\":\n", "    ims = pb.get_connection(\"ims-stage\")\n", "    retail = pb.get_connection(\"retail-stage\")\n", "\n", "if ENV == \"prod\":\n", "    ims = pb.get_connection(\"ims\")\n", "    retail = pb.get_connection(\"retail\")\n", "    redshift = pb.get_connection(\"redshift\")\n", "    capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "    promise_tm = pb.get_connection(\"promise_time\")\n", "\n", "\n", "ims = pb.get_connection(\"ims\")\n", "retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "promise_tm = pb.get_connection(\"promise_time\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1c7PFZr_jx2ZvhBp-ymDu3ZPRji0M-jFEsYqrzjjIZCw\"\n", "predicted_carts = pb.from_sheets(sheet_id, \"predicted_carts\")\n", "predicted_carts[\"ssc_outlet_id\"] = predicted_carts[\"ssc_outlet_id\"].astype(int)\n", "predicted_carts[\"carts\"] = predicted_carts[\"carts\"].astype(int)\n", "predicted_carts[\"date\"] = pd.to_datetime(predicted_carts[\"date\"])\n", "considered_days = pb.from_sheets(sheet_id, \"considered_days\")\n", "considered_days[\"days\"] = considered_days[\"days\"].astype(int)\n", "custom_days = considered_days[\"days\"].max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predicted_carts.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_list = pb.from_sheets(sheet_id, \"outlet_list\")\n", "outlet_list[\"ssc_outlet_id\"] = outlet_list[\"ssc_outlet_id\"].astype(int)\n", "\n", "OUTLET_ID_LIST = outlet_list.values.tolist()\n", "\n", "backend_merchant = pb.from_sheets(sheet_id, \"backend_merchant\")\n", "\n", "for (columnName, columnData) in backend_merchant.iteritems():\n", "    BACKEND_MERCHANT_ID_LIST = tuple(columnData.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_query(name, query, con, params=None):\n", "    df = pd.read_sql_query(query, con=con, params=params)\n", "    return df\n", "\n", "\n", "def extrapolated_qty_cal(x):\n", "    if x[\"Capacity\"] > x[\"total_orders\"]:\n", "        if x[\"carts\"] == 0 or pd.isnull(x[\"carts\"]) == True:\n", "            return x[\"total_item_quantity\"] + math.ceil(\n", "                x[\"rolling_cpd\"] * ((x[\"Capacity\"] - x[\"total_orders\"]) / x[\"Capacity\"])\n", "            )\n", "        else:\n", "            return x[\"total_item_quantity\"] + math.ceil(\n", "                x[\"rolling_cpd\"] * ((x[\"Capacity\"] - x[\"total_orders\"]) / x[\"carts\"])\n", "            )\n", "    else:\n", "        return x[\"total_item_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Active Assortment\n", "active_assortment_query = \"\"\"\n", "SELECT DISTINCT\n", "   ma.item_id,\n", "  -- o.outlet_id as HOT_outlet,\n", "   coalesce(wom.cloud_store_id,o.outlet_id) as outlet_id \n", "FROM rpc.product_facility_master_assortment ma\n", "       INNER JOIN po.physical_facility_outlet_mapping o ON ma.facility_id = o.facility_id AND o.is_primary=1\n", "       LEFT JOIN retail.warehouse_outlet_mapping wom ON wom.warehouse_id = o.outlet_id\n", "       INNER JOIN rpc.product_product pp ON pp.item_id = ma.item_id\n", "WHERE--\n", "   (\n", "       # no cold outlet at all in the city\n", "       o.facility_id != ALL(\n", "           SELECT o1.facility_id from po.physical_facility_outlet_mapping o1 where o1.outlet_name like '%%Cold%%'\n", "       )\n", "       OR\n", "       (\n", "           o.outlet_name like '%%Cold%%' XOR\n", "           pp.storage_type not in (2,3,6,7)\n", "       )\n", "   ) AND\n", "\n", "\n", "  (wom.cloud_store_id in %(outlet_id_list)s OR o.outlet_id in %(outlet_id_list)s) AND\n", "   pp.active AND\n", "   ma.master_assortment_substate_id = 1;\n", "\"\"\"\n", "\n", "active_assortment_0 = fetch_query(\n", "    \"active_assortment\",\n", "    active_assortment_query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")\n", "\n", "# active_assortment.head()\n", "\n", "# Availability\n", "\n", "item_wise_actual_blocked_qty_query = \"\"\"SELECT DISTINCT\n", "     iii.outlet_id,\n", "     iii.item_id,\n", "     COALESCE(iii.upper_limit,iii.fnv_upper_limit) as upper_limit_qty,\n", "     iii.num_order_days as upper_limit_days,\n", "     iii.quantity as actual_inv,\n", "      COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0) as blocked_inv,\n", "     (iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0)) as Net_avai_Quantity\n", "FROM\n", "ims.ims_item_inventory iii\n", "     LEFT JOIN\n", "ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "  --   INNER JOIN\n", "-- retail.warehouse_outlet_mapping wom on iii.outlet_id = wom.cloud_store_id\n", "     INNER JOIN\n", "retail.console_outlet co ON co.id=iii.outlet_id\n", "     INNER JOIN\n", "retail.console_location cl ON cl.id = co.tax_location_id\n", "     INNER JOIN\n", "(SELECT item_id, name FROM rpc.product_product pp2 \n", "WHERE pp2.id in (\n", "SELECT max(id) as id FROM rpc.product_product pp \n", "WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", ") product ON product.item_id = iii.item_id\n", "WHERE iii.outlet_id in %(outlet_id_list)s AND iii.active = 1 \n", "GROUP BY 1,2,3,4,5;\"\"\"\n", "\n", "item_wise_actual_blocked_qty = fetch_query(\n", "    \"item_wise_actual_blocked_qty\",\n", "    item_wise_actual_blocked_qty_query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_zero_actual_inv = item_wise_actual_blocked_qty[\n", "    item_wise_actual_blocked_qty[\"actual_inv\"] > 0\n", "][[\"outlet_id\", \"item_id\"]]\n", "\n", "active_assortment_1 = (\n", "    active_assortment_0.merge(\n", "        non_zero_actual_inv,\n", "        how=\"outer\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_assortment_query = \"\"\"\n", "SELECT DISTINCT\n", "   ma.item_id,\n", "  -- o.outlet_id as HOT_outlet,\n", "   coalesce(wom.cloud_store_id,o.outlet_id) as outlet_id \n", "FROM rpc.product_facility_master_assortment ma\n", "       INNER JOIN po.physical_facility_outlet_mapping o ON ma.facility_id = o.facility_id AND o.is_primary=1\n", "       LEFT JOIN retail.warehouse_outlet_mapping wom ON wom.warehouse_id = o.outlet_id\n", "       INNER JOIN rpc.product_product pp ON pp.item_id = ma.item_id\n", "       \n", "WHERE\n", "   (\n", "       # no cold outlet at all in the city\n", "       o.facility_id != ALL(\n", "           SELECT o1.facility_id from po.physical_facility_outlet_mapping o1 where o1.outlet_name like '%%Cold%%'\n", "       )\n", "       OR\n", "       (\n", "           o.outlet_name like '%%Cold%%' XOR\n", "           pp.storage_type not in (2,3,6,7)\n", "       )\n", "   ) AND\n", "\n", "\n", "  (wom.cloud_store_id in %(outlet_id_list)s OR o.outlet_id in %(outlet_id_list)s) AND\n", "   pp.active AND\n", "   ma.master_assortment_substate_id != 1;\n", "\"\"\"\n", "\n", "inactive_assortment = fetch_query(\n", "    \"inactive_assortment\",\n", "    inactive_assortment_query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_wise_actual_blocked_qty.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_wise_actual_blocked_qty_on_upper_limit = item_wise_actual_blocked_qty[\n", "    pd.isnull(item_wise_actual_blocked_qty.upper_limit_qty) == False\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_assortment_on_upper_limit = (\n", "    inactive_assortment.merge(\n", "        item_wise_actual_blocked_qty_on_upper_limit,\n", "        how=\"inner\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[[\"outlet_id\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_assortment_on_upper_limit.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment = (\n", "    active_assortment_1.merge(\n", "        inactive_assortment_on_upper_limit,\n", "        how=\"outer\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Promise Time\n", "promise_time_query = \"\"\" select distinct esd.backend_merchant_id, \n", "        esd.backend_merchant_name,\n", "        concat(esd.earliest_available_slot_date,' ', split_part(earliest_available_slot,'-',1)) as\n", "        earliest_promise_timestamp,\n", "        esd.earliest_available_slot_date,\n", "        esd.earliest_available_slot,\n", "        T.promise_time_hours\n", "from \n", "\n", "(select *\n", "        from earliest_slot_details \n", "        where backend_merchant_id in %(backend_merchant_id)s\n", ") esd\n", "\n", "inner join \n", "\n", "(\n", "select \n", "\n", "    backend_merchant_id, \n", "    backend_merchant_name, \n", "    min(promise_time_hours) as promise_time_hours\n", "\n", "from (select \n", "\n", "    backend_merchant_id, \n", "    backend_merchant_name, \n", "    promise_time_hours\n", "from earliest_slot_details \n", "where promise_time_hours > 0\n", ") p\n", "\n", "where backend_merchant_id in %(backend_merchant_id)s \n", "\n", "group by 1,2\n", ") T\n", "\n", "on esd.promise_time_hours = T.promise_time_hours   AND esd.backend_merchant_id = T.backend_merchant_id\n", "\n", "\"\"\"\n", "\n", "promise_time_bk_end = fetch_query(\n", "    \"promise_time\",\n", "    promise_time_query,\n", "    promise_tm,\n", "    params={\"backend_merchant_id\": BACKEND_MERCHANT_ID_LIST},\n", ")\n", "\n", "bk_end_merchant_to_outlet_map_query = \"\"\"select distinct cms.outlet_id, o.name as outlet, cms.cms_store\n", "from lake_oms_bifrost.oms_merchant m\n", "inner join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "inner join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where cms.active = 1 and cms.cms_update_active = 1\"\"\"\n", "\n", "cms_outlet_details = fetch_query(\n", "    \"cms_outlet_details\", bk_end_merchant_to_outlet_map_query, redshift\n", ")\n", "\n", "promise_time = (\n", "    promise_time_bk_end.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[[\"outlet_id\", \"earliest_promise_timestamp\"]]\n", "    .rename(columns={\"earliest_promise_timestamp\": \"earliest_promised_slot\"})\n", "    .drop_duplicates()\n", ")\n", "\n", "promise_time[\"earliest_promised_slot\"] = pd.to_datetime(\n", "    promise_time[\"earliest_promised_slot\"]\n", ")\n", "promise_time = promise_time.groupby([\"outlet_id\"], as_index=False).agg(\n", "    {\"earliest_promised_slot\": \"min\"}\n", ")\n", "\n", "promise_time[\"days_from_today\"] = promise_time.apply(\n", "    lambda x: (x[\"earliest_promised_slot\"].date() - datetime.today().date()).days,\n", "    axis=1,\n", ")\n", "promise_time[\"days\"] = promise_time.apply(\n", "    lambda x: max(x[\"days_from_today\"], custom_days), axis=1\n", ")\n", "\n", "\n", "# promise_time = (\n", "#     promise_time_bk_end.merge(\n", "#         cms_outlet_details,\n", "#         how=\"inner\",\n", "#         left_on=[\"backend_merchant_id\"],\n", "#         right_on=[\"cms_store\"],\n", "#     )[[\"outlet_id\",\"earliest_promise_timestamp\"]]\n", "#     .rename(columns={\"earliest_promise_timestamp\": \"earliest_promised_slot_ist\"})\n", "#     .drop_duplicates()\n", "# )\n", "\n", "# promise_time['earliest_promised_slot_ist'] = pd.to_datetime(promise_time['earliest_promised_slot_ist'])\n", "# promise_time[\"earliest_promised_slot\"] = promise_time.apply(lambda x: (x['earliest_promised_slot_ist'] - timedelta(hours = 5.5)), axis=1)\n", "# promise_time['earliest_promised_slot'] = pd.to_datetime(promise_time['earliest_promised_slot'])\n", "# promise_time = promise_time.groupby(['outlet_id'],as_index = False).agg({'earliest_promised_slot':'min'})\n", "\n", "# promise_time['days_from_today'] = promise_time.apply(lambda x: (x['earliest_promised_slot'].date() - datetime.today().date()).days, axis=1)\n", "# promise_time[\"days\"] = promise_time.apply(lambda x: max(x[\"days_from_today\"],custom_days), axis =1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["promise_time.earliest_promised_slot.min()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Incoming Inventory Information\n", "\n", "all_open_POs_query = \"\"\"SELECT\n", "   poi.item_id,\n", "   wom.cloud_store_id as outlet_id,\n", "   po.id,\n", "   pos.po_state_id,\n", "   post.name,\n", "   date(convert_tz(po.issue_date,\"+00:00\",\"+05:30\")) as issue_date,\n", "   date(convert_tz(po.expiry_date,\"+00:00\",\"+05:30\")) as  expiry_date,\n", "   date(convert_tz(ps.schedule_date_time,\"+00:00\",\"+05:30\")) as  schedule_date_time,   \n", "   poi.units_ordered\n", "FROM po.purchase_order po\n", "INNER JOIN retail.warehouse_outlet_mapping wom ON wom.warehouse_id = po.outlet_id\n", "INNER JOIN po.purchase_order_items poi ON po.id = poi.po_id\n", "INNER JOIN po.purchase_order_status pos ON po.id = pos.po_id\n", "INNER JOIN po.purchase_order_state post ON pos.po_state_id = post.id \n", "LEFT JOIN po.po_schedule ps ON ps.po_id_id = po.id\n", "WHERE wom.cloud_store_id in %(outlet_id_list)s  AND post.name IN ('Created', 'Scheduled', 'Unscheduled', 'Rescheduled')  \n", "\"\"\"\n", "\n", "all_open_POs = fetch_query(\n", "    \"open_POs\", all_open_POs_query, retail, params={\"outlet_id_list\": OUTLET_ID_LIST}\n", ")\n", "\n", "all_open_POs[\"schedule_date_time\"] = pd.to_datetime(all_open_POs[\"schedule_date_time\"])\n", "all_open_POs[\"expiry_date\"] = pd.to_datetime(all_open_POs[\"expiry_date\"])\n", "# all_open_POs[\"PO_delivery_date\"] = all_open_POs.schedule_date_time.combine_first(\n", "#     all_open_POs.expiry_date\n", "# )\n", "\n", "all_open_POs[\"PO_delivery_date\"] = all_open_POs[\"schedule_date_time\"]\n", "\n", "all_open_POs[\"PO_delivery_date\"] = pd.to_datetime(all_open_POs[\"PO_delivery_date\"])\n", "\n", "g = all_open_POs.groupby([\"outlet_id\", \"item_id\"])\n", "all_open_POs[\"RN\"] = g[\"PO_delivery_date\"].rank(method=\"min\", na_option=\"bottom\")\n", "\n", "# g1 = all_open_POs.groupby(['outlet_id','item_id','RN'])\n", "# all_open_POs[\"RN\"] += g1['expiry_date'].rank() - 1\n", "\n", "all_open_POs[\"RN\"] = all_open_POs[\"RN\"].astype(\"int\")\n", "\n", "item_open_POs_all = all_open_POs[all_open_POs.RN == 1][\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"schedule_date_time\",\n", "        \"expiry_date\",\n", "        \"units_ordered\",\n", "        \"PO_delivery_date\",\n", "    ]\n", "]\n", "\n", "item_open_POs_all = item_open_POs_all.rename(\n", "    columns={\n", "        \"schedule_date_time\": \"po_schedule_date_time\",\n", "        \"expiry_date\": \"po_expiry_date\",\n", "        \"units_ordered\": \"po_qty\",\n", "    }\n", ")\n", "\n", "\n", "open_STOs_query = \"\"\" SELECT s.merchant_outlet_id as outlet_id,\n", "   isi.item_id,\n", "   date(convert_tz(s.delivery_date,\"+00:00\",\"+05:30\")) as sto_delivery_date,\n", "   sum(isi.reserved_quantity) as sto_ordered_qty ,\n", "   sum(isi.billed_quantity) as sto_billed_quantity\n", "   FROM  ims.ims_sto_details s \n", "   INNER JOIN ims.ims_sto_item isi ON isi.sto_id = s.sto_id\n", "   WHERE s.sto_state IN (1,2,5) AND s.merchant_outlet_id in %(outlet_id_list)s\n", "   group by 1,2,3\n", "   order by 1,2 \n", "\"\"\"\n", "\n", "open_STOs = fetch_query(\n", "    \"open_STOs\", open_STOs_query, retail, params={\"outlet_id_list\": OUTLET_ID_LIST}\n", ")\n", "g2 = open_STOs.groupby([\"outlet_id\", \"item_id\"])\n", "open_STOs[\"sto_delivery_date\"] = pd.to_datetime(open_STOs[\"sto_delivery_date\"])\n", "open_STOs[\"RN\"] = g2[\"sto_delivery_date\"].rank()\n", "item_open_STOs = open_STOs[open_STOs.RN == 1][\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"sto_delivery_date\",\n", "        \"sto_ordered_qty\",\n", "        \"sto_billed_quantity\",\n", "    ]\n", "]\n", "item_open_STOs[\"sto_quantity\"] = np.where(\n", "    item_open_STOs[\"sto_billed_quantity\"] == 0,\n", "    item_open_STOs[\"sto_ordered_qty\"],\n", "    item_open_STOs[\"sto_billed_quantity\"],\n", ")\n", "\n", "item_pending_putaway_query = \"\"\" SELECT \n", "good_inv.outlet_id,\n", "product.item_id,\n", "sum(case when good_inv.inventory_update_type_id = 28 then good_inv.quantity end ) as 'Pending_Putaway_qty'\n", "FROM ims.ims_good_inventory good_inv \n", "INNER JOIN\n", "rpc.product_product product on product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1 AND good_inv.outlet_id in %(outlet_id_list)s\n", "group by 1,2\n", "\"\"\"\n", "item_pending_putaway = fetch_query(\n", "    \"item_pending_putaway\",\n", "    item_pending_putaway_query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")\n", "\n", "# add delivery date only when Pending_Putaway_qty is non zero\n", "item_pending_putaway[\"pending_putaway_delivery_date\"] = np.where(\n", "    item_pending_putaway[\"Pending_Putaway_qty\"] > 0,\n", "    (datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)).date(),\n", "    None,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_open_POs = item_open_POs_all.groupby(\n", "    [\"outlet_id\", \"item_id\", \"PO_delivery_date\"], as_index=False\n", ").agg({\"po_qty\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# External data issues (Exception handling)\n", "# if (len(predicted_carts[predicted_carts.ssc_outlet_id == OUTLET_ID]) > 0) & (len(predicted_carts[\"carts\"]) > 0):\n", "\n", "# Warehouse Capacity\n", "\n", "capacity_query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "    warehouse_external_id AS backend_merchant_id,\n", "    warehouse_name AS backend_merchant_name,\n", "\n", "  --  DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", " --   TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "--    TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\n", "    DATE(slot_start) AS delivery_date,\n", "    TO_CHAR(slot_start ,'HH24:MI:SS') || ' - ' || \n", "    TO_CHAR(slot_end ,'HH24:MI:SS') AS delivery_slot,\n", "  \n", "    slot_type,\n", "\n", "    warehouse_asked AS asked_capcity,\n", "    warehouse_planned AS given_capacity,\n", "    warehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999 \n", "and warehouse_external_id in %(backend_merchant_id)s \n", "GROUP BY 1,2,3,4,5,6,7,8,9\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date + interval '1 days') and date(current_date + interval '15 days')\n", "and given_capacity > 0\n", "group by 1,2,3\n", "order by 2,3;\n", "\"\"\"\n", "capacity_details = fetch_query(\n", "    \"capacity_details\",\n", "    capacity_query,\n", "    capacity_system,\n", "    params={\"backend_merchant_id\": BACKEND_MERCHANT_ID_LIST},\n", ")\n", "\n", "\n", "warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[[\"outlet_id\", \"delivery_date\", \"planned_capacity\"]]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "# warehouse_capacities = warehouse_capacities[warehouse_capacities.outlet_id == int(OUTLET_ID)]\n", "warehouse_capacities[\"outlet_id\"] = warehouse_capacities[\"outlet_id\"].astype(\"int\")\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(\n", "    warehouse_capacities[\"delivery_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CPD\n", "query = \"\"\"SELECT DISTINCT wom.cloud_store_id as outlet_id,\n", "                dwc.date AS delivery_date,\n", "                dwc.item_id,\n", "                dwc.consumption AS inv_cpd\n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id, \n", "          date, \n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE - interval 1 week AND CURRENT_DATE + interval 2 week\n", "   ) dwc\n", "JOIN retail.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "WHERE wom.cloud_store_id in %(outlet_id_list)s\n", "\n", "\"\"\"\n", "\n", "inventory_cpd = fetch_query(\n", "    \"inventory_cpd\",\n", "    query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")\n", "\n", "inventory_cpd[\"delivery_date\"] = pd.to_datetime(inventory_cpd[\"delivery_date\"])\n", "inventory_cpd[\"rolling_cpd\"] = inventory_cpd.groupby(\"item_id\")[\"inv_cpd\"].transform(\n", "    lambda x: x.rolling(5).mean()\n", ")\n", "inventory_cpd[\"inv_cpd\"] = np.ceil(inventory_cpd[\"inv_cpd\"])\n", "inventory_cpd[\"rolling_cpd\"] = np.ceil(inventory_cpd[\"rolling_cpd\"])\n", "# inventory_cpd[\"outlet_id\"] = OUTLET_ID\n", "\n", "# avg_inventory_cpd = inventory_cpd.groupby('item_id', as_index = False).agg({'inv_cpd':'mean'}).rename(columns={\"inv_cpd\": \"avg_inv_cpd\"})\n", "\n", "# avg_inventory_cpd[\"avg_inv_cpd\"] = np.ceil(avg_inventory_cpd[\"avg_inv_cpd\"])\n", "# avg_inventory_cpd[\"avg_inv_cpd\"] = avg_inventory_cpd[\"avg_inv_cpd\"].astype(\"int\")\n", "# avg_inventory_cpd[\"outlet_id\"] = OUTLET_ID"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_cpd_query = \"\"\"select distinct wom.cloud_store_id as outlet_id, dwc.item_id, avg(dwc.consumption) as avg_inv_cpd \n", "from \n", "(select outlet_id, item_id, date, consumption from snorlax.date_wise_consumption where \n", "date between current_date \n", "and current_date + interval 1 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "where wom.cloud_store_id in %(outlet_id_list)s\n", "group by 1,2\n", "\"\"\"\n", "avg_inventory_cpd = fetch_query(\n", "    \"avg_inventory_cpd\",\n", "    avg_cpd_query,\n", "    retail,\n", "    params={\"outlet_id_list\": OUTLET_ID_LIST},\n", ")\n", "\n", "avg_inventory_cpd[\"avg_inv_cpd\"] = np.ceil(avg_inventory_cpd[\"avg_inv_cpd\"])\n", "avg_inventory_cpd[\"avg_inv_cpd\"] = avg_inventory_cpd[\"avg_inv_cpd\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date_minus_1 = start_date + <PERSON><PERSON>ta(days=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date_minus_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date_plus_1 = end_date + <PERSON><PERSON>ta(days=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date_plus_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Picked orders and Qty\n", "\n", "# Picked orders and Item <PERSON>ty for T+1 , T+2 ... delivery\n", "picked_orders_query = \"\"\" SELECT o.outlet AS outlet_id,\n", "             date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,              \n", "           count(DISTINCT CASE WHEN o.status_id NOT IN (3,5) THEN o.order_id END) AS total_orders\n", "           FROM ims.ims_order_details o\n", "  --  WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) BETWEEN %(start_date)s AND %(end_date)s\n", "    WHERE scheduled_at BETWEEN %(start_date)s AND %(end_date)s\n", "      AND o.outlet in %(outlet_id_list)s\n", "    GROUP BY 1,\n", "             2\n", "    ORDER BY 1,\n", "             2 \"\"\"\n", "\n", "picked_orders = fetch_query(\n", "    \"picked_orders\",\n", "    picked_orders_query,\n", "    retail,\n", "    params={\n", "        \"outlet_id_list\": OUTLET_ID_LIST,\n", "        \"start_date\": start_date_minus_1,\n", "        \"end_date\": end_date_plus_1,\n", "    },\n", ")\n", "\n", "picked_orders[\"delivery_date\"] = pd.to_datetime(picked_orders[\"delivery_date\"])\n", "\n", "\n", "picked_item_qty_query = \"\"\" SELECT od.outlet AS outlet_id,\n", "          date(convert_tz(od.scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "           oi.item_id,\n", "           sum(CASE WHEN od.status_id NOT IN (3,5) THEN quantity END) AS total_item_quantity\n", "    FROM ims.ims_order_items oi\n", "    INNER JOIN\n", "      (SELECT DISTINCT outlet,\n", "                       id,\n", "                       order_id,\n", "                       scheduled_at,\n", "                       status_id\n", "       FROM ims.ims_order_details od\n", "    --   WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) BETWEEN %(start_date)s AND %(end_date)s\n", "    WHERE scheduled_at BETWEEN %(start_date)s AND %(end_date)s\n", "         AND outlet in %(outlet_id_list)s \n", "         AND od.status_id != 3 AND od.status_id != 5) od ON oi.order_details_id = od.id\n", "    GROUP BY 1,\n", "             2,\n", "             3\n", "\"\"\"\n", "\n", "picked_item_qty = fetch_query(\n", "    \"picked_item_qty\",\n", "    picked_item_qty_query,\n", "    retail,\n", "    params={\n", "        \"outlet_id_list\": OUTLET_ID_LIST,\n", "        \"start_date\": start_date_minus_1,\n", "        \"end_date\": end_date_plus_1,\n", "    },\n", ")\n", "\n", "picked_item_qty[\"delivery_date\"] = pd.to_datetime(picked_item_qty[\"delivery_date\"])\n", "\n", "\n", "picked_orders_item_qty = picked_item_qty.merge(\n", "    picked_orders,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"delivery_date\"],\n", "    right_on=[\"outlet_id\", \"delivery_date\"],\n", ")[[\"outlet_id\", \"delivery_date\", \"total_orders\", \"item_id\", \"total_item_quantity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data Manipulation\n", "warehouse_capacities_picked_orders_item_qty = warehouse_capacities.merge(\n", "    picked_orders_item_qty,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"delivery_date\"],\n", "    right_on=[\"outlet_id\", \"delivery_date\"],\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"delivery_date\",\n", "        \"Capacity\",\n", "        \"total_orders\",\n", "        \"item_id\",\n", "        \"total_item_quantity\",\n", "    ]\n", "]\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"total_orders\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"total_orders\"].fillna(0)\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"item_id\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"item_id\"].fillna(0)\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"total_item_quantity\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"total_item_quantity\"].fillna(0)\n", "\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"total_orders\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"total_orders\"].astype(\"int\")\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"item_id\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"item_id\"].astype(\"int\")\n", "warehouse_capacities_picked_orders_item_qty[\n", "    \"total_item_quantity\"\n", "] = warehouse_capacities_picked_orders_item_qty[\"total_item_quantity\"].astype(\"int\")\n", "\n", "###############################################\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd = (\n", "    warehouse_capacities_picked_orders_item_qty.merge(\n", "        inventory_cpd,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"delivery_date\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"delivery_date\", \"item_id\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"delivery_date\",\n", "            \"Capacity\",\n", "            \"total_orders\",\n", "            \"item_id\",\n", "            \"total_item_quantity\",\n", "            \"inv_cpd\",\n", "            \"rolling_cpd\",\n", "        ]\n", "    ]\n", ")\n", "\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd[\n", "    \"inv_cpd\"\n", "] = warehouse_capacities_picked_orders_item_qty_inventory_cpd[\"inv_cpd\"].fillna(0)\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd[\n", "    \"rolling_cpd\"\n", "] = warehouse_capacities_picked_orders_item_qty_inventory_cpd[\"rolling_cpd\"].fillna(0)\n", "\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd[\n", "    \"inv_cpd\"\n", "] = warehouse_capacities_picked_orders_item_qty_inventory_cpd[\"inv_cpd\"].astype(\"int\")\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd[\n", "    \"rolling_cpd\"\n", "] = warehouse_capacities_picked_orders_item_qty_inventory_cpd[\"rolling_cpd\"].astype(\n", "    \"int\"\n", ")\n", "\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd_predicted_carts = (\n", "    warehouse_capacities_picked_orders_item_qty_inventory_cpd.merge(\n", "        predicted_carts,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"delivery_date\"],\n", "        right_on=[\"ssc_outlet_id\", \"date\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"delivery_date\",\n", "        \"Capacity\",\n", "        \"total_orders\",\n", "        \"carts\",\n", "        \"item_id\",\n", "        \"total_item_quantity\",\n", "        \"inv_cpd\",\n", "        \"rolling_cpd\",\n", "    ]\n", "]\n", "\n", "\n", "# Extrapolated Qty\n", "warehouse_capacities_picked_orders_item_qty_inventory_cpd_predicted_carts[\n", "    \"extrapolated_qty\"\n", "] = warehouse_capacities_picked_orders_item_qty_inventory_cpd_predicted_carts.apply(\n", "    extrapolated_qty_cal, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify risk & OOS items\n", "\n", "item_wise_actual_blocked_qty[\"availability\"] = np.where(\n", "    item_wise_actual_blocked_qty[\"Net_avai_Quantity\"] > 0, 1, 0\n", ")\n", "\n", "active_assort_oos_or_risk_items_check = (\n", "    active_assortment.merge(\n", "        item_wise_actual_blocked_qty,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "    ]\n", "]\n", "\n", "active_assort_oos_or_risk_items_check[\n", "    \"actual_inv\"\n", "] = active_assort_oos_or_risk_items_check[\"actual_inv\"].fillna(0)\n", "active_assort_oos_or_risk_items_check[\n", "    \"blocked_inv\"\n", "] = active_assort_oos_or_risk_items_check[\"blocked_inv\"].fillna(0)\n", "active_assort_oos_or_risk_items_check[\n", "    \"availability\"\n", "] = active_assort_oos_or_risk_items_check[\"availability\"].fillna(0)\n", "\n", "active_assort_oos_or_risk_items_check[\n", "    \"actual_inv\"\n", "] = active_assort_oos_or_risk_items_check[\"actual_inv\"].astype(\"int\")\n", "active_assort_oos_or_risk_items_check[\n", "    \"blocked_inv\"\n", "] = active_assort_oos_or_risk_items_check[\"blocked_inv\"].astype(\"int\")\n", "active_assort_oos_or_risk_items_check[\n", "    \"availability\"\n", "] = active_assort_oos_or_risk_items_check[\"availability\"].astype(\"int\")\n", "\n", "\n", "active_assort_oos_or_risk_items_check_pt = (\n", "    active_assort_oos_or_risk_items_check.merge(\n", "        promise_time,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\"],\n", "        right_on=[\"outlet_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "    ]\n", "]\n", "\n", "\n", "ft = (\n", "    active_assort_oos_or_risk_items_check_pt.merge(\n", "        item_open_POs,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "    ]\n", "]\n", "ft1 = (\n", "    ft.merge(\n", "        item_open_STOs,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "    ]\n", "]\n", "\n", "ft2 = (\n", "    ft1.merge(\n", "        item_pending_putaway,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "    ]\n", "]\n", "\n", "ft2[\"po_qty\"] = ft2[\"po_qty\"].fillna(0)\n", "ft2[\"sto_quantity\"] = ft2[\"sto_quantity\"].fillna(0)\n", "ft2[\"Pending_Putaway_qty\"] = ft2[\"Pending_Putaway_qty\"].fillna(0)\n", "\n", "ft2[\"po_qty\"] = ft2[\"po_qty\"].astype(\"int\")\n", "ft2[\"sto_quantity\"] = ft2[\"sto_quantity\"].astype(\"int\")\n", "ft2[\"Pending_Putaway_qty\"] = ft2[\"Pending_Putaway_qty\"].astype(\"int\")\n", "\n", "ft2[\"sto_delivery_date\"] = pd.to_datetime(ft2[\"sto_delivery_date\"])\n", "ft2[\"PO_delivery_date\"] = pd.to_datetime(ft2[\"PO_delivery_date\"])\n", "ft2[\"pending_putaway_delivery_date\"] = pd.to_datetime(\n", "    ft2[\"pending_putaway_delivery_date\"]\n", ")\n", "\n", "ft2[\"future_stock_in_date\"] = ft2[\n", "    [\"PO_delivery_date\", \"sto_delivery_date\", \"pending_putaway_delivery_date\"]\n", "].min(axis=1)\n", "\n", "ft3 = (\n", "    ft2.merge(\n", "        avg_inventory_cpd,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"future_stock_in_date\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "        \"avg_inv_cpd\",\n", "    ]\n", "]\n", "\n", "ft3[\"avg_inv_cpd\"] = ft3[\"avg_inv_cpd\"].fillna(0)\n", "ft3[\"avg_inv_cpd\"] = ft3[\"avg_inv_cpd\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def daterange(start_date, end_date):\n", "    for n in range(int((end_date - start_date).days + 1)):\n", "        yield start_date + timedelta(n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivery_date = []\n", "for d in daterange(start_date, end_date):\n", "    delivery_date.append(d)\n", "df_dates = pd.DataFrame()\n", "df_dates[\"delivery_date\"] = delivery_date\n", "df_dates[\"cross_join_key\"] = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_date_wise = active_assortment.copy(deep=True)\n", "active_assortment_date_wise[\"cross_join_key\"] = 1\n", "active_assortment_date_wise = active_assortment_date_wise.merge(\n", "    df_dates, on=\"cross_join_key\"\n", ")\n", "active_assortment_date_wise = active_assortment_date_wise[\n", "    [\"item_id\", \"outlet_id\", \"delivery_date\"]\n", "]\n", "active_assortment_date_wise[\"delivery_date\"] = pd.to_datetime(\n", "    active_assortment_date_wise[\"delivery_date\"]\n", ")\n", "active_assortment_date_wise.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f = (\n", "    active_assortment_date_wise.merge(\n", "        warehouse_capacities_picked_orders_item_qty_inventory_cpd_predicted_carts,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\", \"delivery_date\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"delivery_date\"],\n", "    )\n", ")[[\"outlet_id\", \"delivery_date\", \"item_id\", \"extrapolated_qty\"]].rename(\n", "    columns={\"extrapolated_qty\": \"extrapolated_qty_order\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ff = (\n", "    f.merge(\n", "        inventory_cpd,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\", \"delivery_date\"],\n", "        right_on=[\"outlet_id\", \"item_id\", \"delivery_date\"],\n", "    )\n", ")[\n", "    [\"outlet_id\", \"delivery_date\", \"item_id\", \"extrapolated_qty_order\", \"inv_cpd\"]\n", "].rename(\n", "    columns={\"inv_cpd\": \"extrapolated_qty_cpd\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ff[\"extrapolated_qty\"] = ff.extrapolated_qty_order.combine_first(\n", "    ff.extrapolated_qty_cpd\n", ")\n", "ff[\"extrapolated_qty\"] = ff[\"extrapolated_qty\"].fillna(0)\n", "ff[\"extrapolated_qty\"] = ff[\"extrapolated_qty\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = promise_time[\"days\"].max()\n", "df_filtered = ff[\n", "    (ff.delivery_date >= pd.to_datetime((todays_date + timedelta(days=1))))\n", "    & (ff.delivery_date <= pd.to_datetime((todays_date + timedelta(days=int(y)))))\n", "]\n", "# df_agg = df_filtered.groupby('item_id', as_index = False).agg({'extrapolated_qty':'sum'})\n", "# df_agg[\"outlet_id\"] = OUTLET_ID\n", "df_agg = df_filtered.groupby([\"outlet_id\", \"item_id\"], as_index=False).agg(\n", "    {\"extrapolated_qty\": \"sum\"}\n", ")\n", "df_agg = df_agg.rename(columns={\"extrapolated_qty\": \"extrapolated_qty_n_days\"})\n", "# df_agg.head(2)\n", "\n", "ft6 = (\n", "    ft3.merge(\n", "        df_agg,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"future_stock_in_date\",\n", "        \"extrapolated_qty_n_days\",\n", "        \"days\",\n", "        \"earliest_promised_slot\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "        \"avg_inv_cpd\",\n", "    ]\n", "]\n", "\n", "ft6[\"Risk\"] = np.where(\n", "    (ft6[\"availability\"] == 1) & (ft6[\"actual_inv\"] <= ft6[\"extrapolated_qty_n_days\"]),\n", "    1,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ff.columns = [\n", "    \"outlet_id\",\n", "    \"delivery_date\",\n", "    \"item_id\",\n", "    \"extrapolated_qty_order\",\n", "    \"extrapolated_qty_cpd\",\n", "    \"extrapolated_qty\",\n", "]\n", "df = ff.copy(deep=True)\n", "df = df.sort_values(\n", "    by=[\"outlet_id\", \"item_id\", \"delivery_date\"], ascending=[True, True, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_grp = (\n", "    df.groupby([\"outlet_id\", \"item_id\", \"delivery_date\"])[\"extrapolated_qty\"]\n", "    .sum()\n", "    .groupby(level=[1])\n", "    .cumsum(axis=0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_grp = df_grp.reset_index()\n", "df_grp.rename(columns={\"extrapolated_qty\": \"consumption_cumsum\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fff = df_grp.merge(\n", "    ft6, how=\"left\", left_on=[\"outlet_id\", \"item_id\"], right_on=[\"outlet_id\", \"item_id\"]\n", ")\n", "fff = fff[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"delivery_date\",\n", "        \"consumption_cumsum\",\n", "        \"actual_inv\",\n", "        \"availability\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fff.loc[\n", "    (fff[\"consumption_cumsum\"] >= fff[\"actual_inv\"])\n", "    & (fff[\"actual_inv\"] > 0)\n", "    & (fff[\"availability\"] != 0),\n", "    \"future_stockout_date\",\n", "] = fff[\"delivery_date\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fff[pd.isnull(fff.future_stockout_date) == False].tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fff.loc[\n", "    (fff[\"actual_inv\"] <= 0) | (fff[\"availability\"] == 0), \"future_stockout_date\"\n", "] = todays_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fff.future_stockout_date = pd.to_datetime(fff.future_stockout_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_agg2 = fff.groupby([\"outlet_id\", \"item_id\"], as_index=False).agg(\n", "    {\"future_stockout_date\": \"min\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft7 = (\n", "    ft6.merge(\n", "        df_agg2,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"Risk\",\n", "        \"future_stockout_date\",\n", "        \"future_stock_in_date\",\n", "        \"earliest_promised_slot\",\n", "        \"extrapolated_qty_n_days\",\n", "        \"days\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "        \"avg_inv_cpd\",\n", "    ]\n", "]\n", "\n", "ft7[\"promise_time_date\"] = ft7[\"earliest_promised_slot\"].dt.date\n", "ft7[\"promise_time_date\"] = pd.to_datetime(ft7[\"promise_time_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def metering_eligibility_check(x):\n", "    if (x[\"availability\"] == 0) and (\n", "        x[\"promise_time_date\"] > x[\"future_stock_in_date\"]\n", "    ):\n", "        return 1\n", "    elif x[\"Risk\"] == 1 and (x[\"promise_time_date\"] > x[\"future_stock_in_date\"]):\n", "        return 1\n", "    elif (\n", "        x[\"Risk\"] == 1\n", "        and (x[\"promise_time_date\"] <= x[\"future_stock_in_date\"])\n", "        and (x[\"future_stock_in_date\"] < x[\"future_stockout_date\"])\n", "    ):\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "ft7[\"metering_eligibility\"] = ft7.apply(metering_eligibility_check, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft7 = ft7[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"Risk\",\n", "        \"future_stockout_date\",\n", "        \"future_stock_in_date\",\n", "        \"earliest_promised_slot\",\n", "        \"metering_eligibility\",\n", "        \"extrapolated_qty_n_days\",\n", "        \"days\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "        \"avg_inv_cpd\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_bucket_query = \"\"\"SELECT a.item_id,\n", "       b.cloud_store_id AS outlet_id, \n", "       (CASE\n", "         WHEN a.tag_value='A'\n", "         AND a1.tag_value ='Y' THEN 'X'\n", "            WHEN a.tag_value='A'\n", "           AND a1.tag_value IS NULL THEN 'A'\n", "            ELSE 'B'\n", "          END) AS item_bucket\n", "FROM rpc.item_outlet_tag_mapping a\n", "INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id = b.warehouse_id\n", "LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id = a.item_id\n", "AND a1.outlet_id = a.outlet_id\n", "AND a1.tag_type_id = 6\n", "AND a1.tag_value ='Y'\n", "WHERE  b.cloud_store_id in %(outlet_id_list)s AND a.tag_type_id IN (1)\n", "\"\"\"\n", "item_bucket = fetch_query(\n", "    \"item_bucket\", item_bucket_query, retail, params={\"outlet_id_list\": OUTLET_ID_LIST}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft8 = (\n", "    ft7.merge(\n", "        item_bucket,\n", "        how=\"left\",\n", "        left_on=[\"outlet_id\", \"item_id\"],\n", "        right_on=[\"outlet_id\", \"item_id\"],\n", "    )\n", ")[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"availability\",\n", "        \"actual_inv\",\n", "        \"blocked_inv\",\n", "        \"upper_limit_qty\",\n", "        \"upper_limit_days\",\n", "        \"Risk\",\n", "        \"future_stockout_date\",\n", "        \"future_stock_in_date\",\n", "        \"earliest_promised_slot\",\n", "        \"metering_eligibility\",\n", "        \"item_bucket\",\n", "        \"extrapolated_qty_n_days\",\n", "        \"days\",\n", "        \"po_qty\",\n", "        \"PO_delivery_date\",\n", "        \"sto_quantity\",\n", "        \"sto_delivery_date\",\n", "        \"Pending_Putaway_qty\",\n", "        \"pending_putaway_delivery_date\",\n", "        \"avg_inv_cpd\",\n", "    ]\n", "]\n", "ft8[\"run_id\"] = datetime.now()\n", "ft8[\"days\"] = ft8[\"days\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft8[\"earliest_promised_slot\"] = ft8.apply(\n", "    lambda x: (x[\"earliest_promised_slot\"] - <PERSON><PERSON><PERSON>(hours=5.5)), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft8[\"future_stockout_date\"] = pd.to_datetime(ft8[\"future_stockout_date\"], utc=True)\n", "ft8[\"future_stock_in_date\"] = pd.to_datetime(ft8[\"future_stock_in_date\"], utc=True)\n", "ft8[\"earliest_promised_slot\"] = pd.to_datetime(ft8[\"earliest_promised_slot\"], utc=True)\n", "ft8[\"PO_delivery_date\"] = pd.to_datetime(ft8[\"PO_delivery_date\"], utc=True)\n", "ft8[\"sto_delivery_date\"] = pd.to_datetime(ft8[\"sto_delivery_date\"], utc=True)\n", "ft8[\"pending_putaway_delivery_date\"] = pd.to_datetime(\n", "    ft8[\"pending_putaway_delivery_date\"], utc=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#### GPOS Integration #####\n", "WH_GPOS_URL = \"http://retail-ims-retail.prod-sgp-k8s.grofer.io/v1/metering/bulk-create/\"\n", "# WH_GPOS_URL = \"https://retail-test.grofer.io/ims/v1/metering/bulk-create/\"\n", "\n", "ft8.rename(\n", "    columns={\n", "        \"availability\": \"cms_availability\",\n", "        \"actual_inv\": \"actual_inventory\",\n", "        \"blocked_inv\": \"blocked_inventory\",\n", "        \"actual_inv\": \"actual_inventory\",\n", "        \"upper_limit_qty\": \"upper_limit_quantity\",\n", "        \"Risk\": \"risk\",\n", "        \"future_stockout_date\": \"future_stock_out_date\",\n", "        \"future_stockin_date\": \"future_stock_in_date\",\n", "        \"po_qty\": \"po_quantity\",\n", "        \"metering_eligibility\": \"metering_eligible\",\n", "        \"PO_delivery_date\": \"po_date\",\n", "        \"sto_delivery_date\": \"sto_date\",\n", "        \"Pending_Putaway_qty\": \"pending_putaway_quantity\",\n", "        \"pending_putaway_delivery_date\": \"pending_putaway_date\",\n", "        \"days\": \"days_of_inventory\",\n", "        \"avg_inv_cpd\": \"avg_cpd\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "\n", "ft8 = ft8.sort_values([\"outlet_id\", \"item_id\"])\n", "\n", "ft8[\"earliest_promised_slot\"] = ft8[\"earliest_promised_slot\"].astype(str)\n", "ft8[\"future_stock_out_date\"] = ft8[\"future_stock_out_date\"].astype(str)\n", "ft8[\"future_stock_in_date\"] = ft8[\"future_stock_in_date\"].astype(str)\n", "ft8[\"po_date\"] = ft8[\"po_date\"].astype(str)\n", "ft8[\"sto_date\"] = ft8[\"sto_date\"].astype(str)\n", "ft8[\"pending_putaway_date\"] = ft8[\"pending_putaway_date\"].astype(str)\n", "ft8[\"run_id\"] = ft8[\"run_id\"].astype(str)\n", "\n", "ft8 = ft8.where((pd.notnull(ft8)), None)\n", "ft8.earliest_promised_slot = ft8.earliest_promised_slot.apply(\n", "    lambda x: None if x == \"NaT\" or x == \"None\" else x\n", ")\n", "ft8.future_stock_out_date = ft8.future_stock_out_date.apply(\n", "    lambda x: None if x == \"NaT\" or x == \"None\" else x\n", ")\n", "ft8.future_stock_in_date = ft8.future_stock_in_date.apply(\n", "    lambda x: None if x == \"NaT\" or x == \"None\" else x\n", ")\n", "ft8.po_date = ft8.po_date.apply(lambda x: None if x == \"NaT\" or x == \"None\" else x)\n", "ft8.sto_date = ft8.sto_date.apply(lambda x: None if x == \"NaT\" or x == \"None\" else x)\n", "ft8.pending_putaway_date = ft8.pending_putaway_date.apply(\n", "    lambda x: None if x == \"NaT\" or x == \"None\" else x\n", ")\n", "\n", "\n", "items_rows = ft8[\n", "    [\n", "        \"run_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"cms_availability\",\n", "        \"actual_inventory\",\n", "        \"blocked_inventory\",\n", "        \"upper_limit_quantity\",\n", "        \"upper_limit_days\",\n", "        \"risk\",\n", "        \"earliest_promised_slot\",\n", "        \"future_stock_out_date\",\n", "        \"future_stock_in_date\",\n", "        \"metering_eligible\",\n", "        \"item_bucket\",\n", "        \"po_quantity\",\n", "        \"po_date\",\n", "        \"sto_quantity\",\n", "        \"sto_date\",\n", "        \"pending_putaway_quantity\",\n", "        \"pending_putaway_date\",\n", "        \"days_of_inventory\",\n", "        \"avg_cpd\",\n", "    ]\n", "].to_dict(orient=\"records\")\n", "\n", "n = 200\n", "for i in range(0, len(items_rows), n):\n", "\n", "    payload = {\"item_list\": items_rows[i : i + n]}\n", "    response = requests.post(WH_GPOS_URL, json=payload)\n", "    #     print(i)\n", "    if response.status_code >= 400:\n", "        print(response.status_code)\n", "#         from_email = \"<EMAIL>\"\n", "#         subject = (\n", "#             \"Error status code in data for metering dashboard %s\" % response.status_code\n", "#         )\n", "#         html_content = f\"Status code: {response.status_code}\"\n", "#         to_email = [\"<EMAIL>\"]\n", "\n", "#         pb.send_email(from_email, to_email, subject, html_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}