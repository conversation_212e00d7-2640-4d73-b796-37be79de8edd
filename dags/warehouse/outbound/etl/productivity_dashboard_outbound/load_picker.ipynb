{"cells": [{"cell_type": "code", "execution_count": null, "id": "f069d83d-de79-4af8-8656-7ec913fdbb49", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "38785e96-2fbe-4f8c-ace1-4d45c15d869a", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "5cc9e2a8-cf5b-4380-a582-83509d5ff7d9", "metadata": {}, "outputs": [], "source": ["shift = pd.read_sql_query(\n", "    \"\"\"\n", "select *\n", "from metrics.productivity_dashboard_shift_details\n", "where shift_date >= (current_date - 2) and shift_date <= current_date\n", "\"\"\",\n", "    redshift,\n", ")\n", "shift.head()"]}, {"cell_type": "code", "execution_count": null, "id": "52519654-3b6c-4bd3-948d-8611830100ba", "metadata": {}, "outputs": [], "source": ["picker_raw = pd.read_sql_query(\n", "    f\"\"\"\n", "SELECT c.facility_id,\n", "       f.name AS facility_name,\n", "       -- cast(isto.created_at + interval '5' hour + interval '30' MINUTE AS date) AS sto_created_date,\n", "       isto.sto_id,\n", "       coalesce(wpl.pick_list_id,ipl.item_pick_list_id) as pick_list_id,\n", "       coalesce(wpl.assigned_to_name,ipl.assigned_to_name) AS picker_id,\n", "       coalesce(wu.name,wu1.name) AS picker_name,\n", "       count(DISTINCT (case when wpli.picked_quantity > 0 then  wpli.item_id else null end)) + count(DISTINCT (case when ipli.picked_quantity > 0 then  ipl.item_id else null end)) AS sku,\n", "       coalesce(sum(wpli.picked_quantity),sum(ipli.picked_quantity)) AS picked_quantity,\n", "       date_diff('minute',coalesce(wpl.picking_started_at,ipl.assigned_at),coalesce(wpll.pos_timestamp,ipll.created_at)) AS picking_time,\n", "       (isto.created_at + interval '5' hour + interval '30' MINUTE) AS sto_created_at\n", "FROM lake_view_ims.ims_sto_details isto\n", "join lake_view_po.sto sto on sto.id = isto.sto_id\n", "join lake_view_vms.vms_vendor vms on vms.id = sto.source_entity_vendor_id\n", "LEFT JOIN lake_view_warehouse_location.warehouse_wave w ON w.entity_id = isto.sto_id\n", "AND w.wave_type = 4\n", "LEFT JOIN lake_view_warehouse_location.warehouse_pick_list wpl ON w.id = wpl.wave_id\n", "AND cast(wpl.insert_ds_ist AS date) >= current_date - interval '2' day\n", "LEFT JOIN lake_view_warehouse_location.warehouse_pick_list_item wpli ON wpl.id = wpli.pick_list_id\n", "AND cast(wpli.insert_ds_ist AS date) >= current_date - interval '2' day\n", "left JOIN lake_view_warehouse_location.pick_list_log wpll ON wpll.pick_list_id = wpl.id\n", "AND wpll.picklist_state = 4\n", "left join lake_view_warehouse_location.warehouse_item_pick_list_line_item ipli on isto.sto_id = cast(ipli.entity_id as int)\n", "and cast(wpli.insert_ds_ist AS date) >= current_date - interval '2' day\n", "left join lake_view_warehouse_location.warehouse_item_pick_list ipl on ipl.id = ipli.item_pick_list_id \n", "left join lake_view_warehouse_location.warehouse_item_pick_list_log ipll on ipll.item_pick_list_id = ipl.id \n", "and cast(ipll.insert_ds_ist AS date) >= current_date - interval '2' day\n", "JOIN lake_view_retail.console_outlet c ON c.id = wpl.outlet_id\n", "AND c.active = 1\n", "AND c.device_id <> 47 \n", "AND c.facility_id IN {*shift['facility_id'].unique(),}\n", "JOIN lake_crates.facility f ON f.id = c.facility_id\n", "left JOIN lake_retail.warehouse_user wu ON lower(wu.emp_id) = lower(wpl.assigned_to_name)\n", "and wu.emp_id is not null and wu.emp_id != ''\n", "left JOIN lake_retail.warehouse_user wu1 ON lower(wu1.emp_id) = lower(ipl.assigned_to_name)\n", "and wu1.emp_id is not null and wu1.emp_id != ''\n", "WHERE isto.created_at >= cast(current_date as timestamp) - interval '5' hour - interval '30' minute - interval '2' day\n", "AND isto.created_at < cast(cast(current_date as varchar) as timestamp) + interval '1' day - interval '30' minute - interval '5' hour\n", "and vms.vendor_name not like '%%BCPL%%' \n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         9,\n", "         10\n", "\"\"\",\n", "    presto,\n", ")\n", "\n", "picker_raw.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eba9470d-0c04-4a9a-93bb-020531f11f3b", "metadata": {}, "outputs": [], "source": ["picker_raw[\"sto_created_at\"] = pd.to_datetime(picker_raw[\"sto_created_at\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2e3f4ee5-5c5f-41a9-92df-e098874b342d", "metadata": {}, "outputs": [], "source": ["picker_raw = pd.merge(picker_raw, shift, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ee69c95-957c-426e-91eb-b31b46a954a4", "metadata": {}, "outputs": [], "source": ["picker_df = picker_raw[\n", "    (picker_raw[\"sto_created_at\"] >= picker_raw[\"start_datetime\"])\n", "    & (picker_raw[\"sto_created_at\"] <= picker_raw[\"end_datetime\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "660c1972-c1e8-4983-a61b-8684c162a16b", "metadata": {}, "outputs": [], "source": ["picker_df = picker_df[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"shift_date\",\n", "        \"shift\",\n", "        \"sto_id\",\n", "        \"pick_list_id\",\n", "        \"picker_id\",\n", "        \"picker_name\",\n", "        \"sku\",\n", "        \"picked_quantity\",\n", "        \"picking_time\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "21000951-534a-4689-a043-80cc5dd9087e", "metadata": {}, "outputs": [], "source": ["len(picker_df)"]}, {"cell_type": "code", "execution_count": null, "id": "8c7540df-b4dd-4297-ad49-29df3033733a", "metadata": {}, "outputs": [], "source": ["len(picker_df[[\"pick_list_id\", \"facility_id\"]].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "149e1af0-49fa-46be-be59-b0d2b51081fc", "metadata": {}, "outputs": [], "source": ["lst = list(\n", "    picker_df[picker_df[[\"pick_list_id\", \"facility_id\"]].duplicated()].index.values\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "40539b82-e0fe-4446-829b-4798e63a3dd8", "metadata": {}, "outputs": [], "source": ["picker_df.drop(lst, axis=0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0b81d145-99de-48f3-bd51-c2a5e605e4be", "metadata": {}, "outputs": [], "source": ["len(picker_df)"]}, {"cell_type": "code", "execution_count": null, "id": "9ff5781a-fc53-42f4-890a-631392e2e9de", "metadata": {}, "outputs": [], "source": ["len(picker_df[[\"pick_list_id\", \"facility_id\"]].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "6c2e9dd3-c1de-4f7d-885d-0023148e3ea2", "metadata": {}, "outputs": [], "source": ["assert len(picker_df[[\"facility_id\", \"pick_list_id\"]].drop_duplicates()) == len(\n", "    picker_df\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a1bca195-e21e-4e3c-ac9f-ebbf4be361ea", "metadata": {}, "outputs": [], "source": ["picker_df[picker_df[[\"pick_list_id\", \"facility_id\"]].duplicated()]"]}, {"cell_type": "code", "execution_count": null, "id": "3696a5b2-6f97-4bc1-aefc-92047d366152", "metadata": {}, "outputs": [], "source": ["picker_df[\"picked_quantity\"] = picker_df[\"picked_quantity\"].astype(\"Int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6ed7412-a3aa-4fed-b06b-1947e0856686", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"facility id sending the sto\",\n", "    },\n", "    {\n", "        \"name\": \"facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"name of facility sending the sto\",\n", "    },\n", "    {\n", "        \"name\": \"shift_date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"date on which the sto is created\",\n", "    },\n", "    {\n", "        \"name\": \"shift\",\n", "        \"type\": \"int\",\n", "        \"description\": \"shift\",\n", "    },\n", "    {\n", "        \"name\": \"sto_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifer for the stock transfer order\",\n", "    },\n", "    {\n", "        \"name\": \"pick_list_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifer for the pick list\",\n", "    },\n", "    {\n", "        \"name\": \"picker_id\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"unique identifier of picker\",\n", "    },\n", "    {\n", "        \"name\": \"picker_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"name of the picker\",\n", "    },\n", "    {\n", "        \"name\": \"sku\",\n", "        \"type\": \"int\",\n", "        \"description\": \"number of skus picked\",\n", "    },\n", "    {\n", "        \"name\": \"picked_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"picked quantity\",\n", "    },\n", "    {\n", "        \"name\": \"picking_time\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picking time in minutes\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e78bee88-9050-4857-80a3-25bb8e881e96", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"productivity_dashboard_picker_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"pick_list_id\", \"sto_id\"],\n", "    \"sortkey\": [\"shift_date\"],\n", "    \"incremental_key\": \"shift_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Stores picker data at a shift, pick list level \",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "26272336-4a4e-461e-b2e1-3dcc324f3d92", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(picker_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "42b64083-631e-419c-99e4-63a6daa0a3c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ee5cc8a-5264-4230-a9ec-e22dedad6be5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}