{"cells": [{"cell_type": "code", "execution_count": null, "id": "f69642b2-2886-4d81-a404-8a541c938387", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "import pytz\n", "from datetime import timedelta, date, datetime\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 200)\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "whl = pb.get_connection(\"[Replica] RDS Warehouse Location\")\n", "ims = pb.get_connection(\"[Replica] RDS IMS\")\n", "po = pb.get_connection(\"[Replica] RDS PO\")\n", "pos = pb.get_connection(\"[Replica] RDS POS\")"]}, {"cell_type": "code", "execution_count": null, "id": "3678f5ad-2f95-40cc-81ab-43ef1c7e369b", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "e9bf405f-2aa5-4550-82ae-dd504753edfd", "metadata": {}, "outputs": [], "source": ["ist = pytz.timezone(\"Asia/Kolkata\")\n", "now = datetime.now(ist)\n", "hour = now.hour\n", "now = now.strftime(\"%Y-%m-%d %H:%M\")\n", "if hour >= 7 and hour < 24:\n", "    day = datetime.now(ist).date()\n", "else:\n", "    day = datetime.now(ist).date() - <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "code", "execution_count": null, "id": "83ff7ba7-a72c-4029-872e-9724bdd9bb31", "metadata": {}, "outputs": [], "source": ["day, now, hour,"]}, {"cell_type": "code", "execution_count": null, "id": "4b10041d-0804-4fb8-918b-71ff2115d609", "metadata": {}, "outputs": [], "source": ["s_outlets = [714, 1104, 1566, 1577, 1624, 1644, 2653]"]}, {"cell_type": "code", "execution_count": null, "id": "5209b05e-a9ce-47d4-920e-fa8ff7a24a21", "metadata": {}, "outputs": [], "source": ["d_outlets = [\n", "    1024,\n", "    1974,\n", "    2280,\n", "    2552,\n", "    2692,\n", "    2886,\n", "    2890,\n", "    3232,\n", "    3297,\n", "    3574,\n", "    3590,\n", "    1431,\n", "    2938,\n", "    2939,\n", "    2946,\n", "    3231,\n", "    3244,\n", "    3260,\n", "    3662,\n", "    1716,\n", "    1868,\n", "    2062,\n", "    2398,\n", "    2792,\n", "    2793,\n", "    2799,\n", "    2801,\n", "    2803,\n", "    3175,\n", "    2551,\n", "    2912,\n", "    3448,\n", "    3661,\n", "    2366,\n", "    2367,\n", "    2548,\n", "    2553,\n", "    2704,\n", "    3531,\n", "    3673,\n", "    3894,\n", "    2096,\n", "    2837,\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5d94428d-fa77-4747-99b4-4adf50c8c9d5", "metadata": {}, "outputs": [], "source": ["all_outlets = s_outlets + d_outlets"]}, {"cell_type": "code", "execution_count": null, "id": "65a09516-4613-4091-98c1-e7d1fceee129", "metadata": {}, "outputs": [], "source": ["query0 = f\"\"\"\n", "SELECT id as o_id, \n", "       name as outlet_name \n", "FROM lake_view_retail.console_outlet co \n", "WHERE id in {*all_outlets,}\n", "ORDER BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2175a79c-6e71-418e-99fa-279bcaf507d7", "metadata": {}, "outputs": [], "source": ["df0 = pd.read_sql_query(query0, presto)"]}, {"cell_type": "code", "execution_count": null, "id": "299a2dca-4f15-486a-938e-6b99783aa6dc", "metadata": {}, "outputs": [], "source": ["query1 = f\"\"\"\n", "SELECT DISTINCT s.outlet_id,\n", "                s.id AS sto_id,\n", "                si.run_id AS ars_run_id,\n", "                (min(si.created_at) + interval '330' MINUTE) as ars_creation_ts\n", "FROM po.sto s\n", "JOIN po.sto_items si ON si.sto_id = s.id\n", "WHERE s.created_at >= '{day}' - interval '1' day\n", "  AND si.run_id != 0\n", "  AND si.run_id IS NOT NULL\n", "  AND si.run_id != \"\"\n", "  AND s.outlet_id IN {*s_outlets,}\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6ee67b6e-422f-44f8-bddf-f6ece708ecde", "metadata": {}, "outputs": [], "source": ["df1 = pd.read_sql_query(query1, po)"]}, {"cell_type": "code", "execution_count": null, "id": "720f7ea2-a552-41f7-9238-2179a1de8500", "metadata": {}, "outputs": [], "source": ["query2 = f\"\"\"\n", "SELECT DISTINCT s.outlet_id,\n", "                s.merchant_outlet_id,\n", "                s.sto_id,\n", "                sum(expected_quantity) as expected_quantity,\n", "                sum(billed_quantity) as billed_quantity,\n", "                sum(inward_quantity) as inward_quantity\n", "FROM ims.ims_sto_details s\n", "JOIN ims.ims_sto_item si on si.sto_id = s.sto_id\n", "WHERE s.created_at >= '{day}' - interval '1' day \n", "  AND s.outlet_id IN {*s_outlets,}\n", "  AND s.merchant_outlet_id IN {*d_outlets,}\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "648574d1-9fb3-49f2-8d24-7aea4db03751", "metadata": {}, "outputs": [], "source": ["df2 = pd.read_sql_query(query2, ims)"]}, {"cell_type": "code", "execution_count": null, "id": "baea0baf-9fe2-40b8-8a2a-6a27cae8f01e", "metadata": {}, "outputs": [], "source": ["df1 = df1.merge(df2, how=\"inner\", on=[\"outlet_id\", \"sto_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "1cb6dc7c-27f7-4846-875c-a5ad2ecd215b", "metadata": {}, "outputs": [], "source": ["query3 = f\"\"\"\n", "SELECT DISTINCT di.id AS invoice_id,\n", "                di.sto_id,\n", "                CASE\n", "                    WHEN di.state = 3 THEN di.updated_at + interval '330' MINUTE\n", "                    ELSE NULL\n", "                END AS sto_dispatched_at,\n", "                di.created_at + interval '330' MINUTE as sto_billed_at\n", "FROM warehouse_location.dispatch_indent di\n", "WHERE di.created_at >= '{day}' - interval '1' DAY\n", "  AND di.source_outlet_id IN {*s_outlets,}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "02e0cf53-7240-412c-8a3c-2b0939deb228", "metadata": {}, "outputs": [], "source": ["df3 = pd.read_sql_query(query3, whl)"]}, {"cell_type": "code", "execution_count": null, "id": "afd660ea-c0f7-4619-9da6-4d0b6e8d7541", "metadata": {}, "outputs": [], "source": ["query4 = f\"\"\"\n", "SELECT DISTINCT i.vendor_invoice_id AS invoice_id,\n", "                max(s.created_at) + INTERVAL '330' MINUTE AS grn_completed_ts\n", "FROM ims.ims_inward_invoice i\n", "JOIN ims.ims_inventory_stock_details s ON s.grn_id = i.grn_id\n", "WHERE i.created_at >= '{day}' - interval '1' DAY\n", "  AND i.outlet_id IN {*d_outlets,}\n", "  AND i.source_type = 2\n", "GROUP BY 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4bf8514e-f538-4f21-95be-153c545ea1ff", "metadata": {}, "outputs": [], "source": ["df4 = pd.read_sql_query(query4, ims)"]}, {"cell_type": "code", "execution_count": null, "id": "15ee1407-847d-4ac7-8979-61bcc24fe58f", "metadata": {}, "outputs": [], "source": ["df3 = df3.merge(df4, how=\"left\", on=[\"invoice_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "31ae0b94-5677-44a7-8865-38b8dbfa4225", "metadata": {}, "outputs": [], "source": ["df3 = (\n", "    df3.groupby([\"sto_id\"])\n", "    .agg(\n", "        num_invoices=(\"invoice_id\", \"count\"),\n", "        billed_at_max=(\"sto_billed_at\", \"max\"),\n", "        dispatched_at_max=(\"sto_dispatched_at\", \"max\"),\n", "        grn_completed_at_max=(\"grn_completed_ts\", \"max\"),\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ec21628c-3ef9-4e48-bb69-e28ade8bac5e", "metadata": {}, "outputs": [], "source": ["df3[\"billed_flag\"] = np.where(df3[\"billed_at_max\"].notnull(), 1, 0)\n", "df3[\"dispatched_flag\"] = np.where(df3[\"dispatched_at_max\"].notnull(), 1, 0)\n", "df3[\"grn_flag\"] = np.where(df3[\"grn_completed_at_max\"].notnull(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "cc8632d3-6c2c-4c6b-8be7-cf1a65a78c2d", "metadata": {}, "outputs": [], "source": ["df1 = df1.merge(df3, how=\"left\", on=[\"sto_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "54ed201d-4288-428f-9bd1-f587e5c66145", "metadata": {}, "outputs": [], "source": ["df_final = (\n", "    df1.groupby([\"outlet_id\", \"merchant_outlet_id\", \"ars_run_id\"])\n", "    .agg(\n", "        stos_created_at=(\"ars_creation_ts\", \"min\"),\n", "        total_stos_created=(\"sto_id\", \"count\"),\n", "        ars_qty=(\"expected_quantity\", \"sum\"),\n", "        total_stos_billed=(\"billed_flag\", \"sum\"),\n", "        billed_at=(\"billed_at_max\", \"max\"),\n", "        billed_qty=(\"billed_quantity\", \"sum\"),\n", "        dispatched_at=(\"dispatched_at_max\", \"max\"),\n", "        total_stos_dispatched=(\"dispatched_flag\", \"sum\"),\n", "        total_stos_grn=(\"grn_flag\", \"sum\"),\n", "        grn_completed_at=(\"grn_completed_at_max\", \"max\"),\n", "        grn_qty=(\"inward_quantity\", \"sum\"),\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "489c3949-ae6a-4b03-850a-990ab25d69fb", "metadata": {}, "outputs": [], "source": ["df_final = df_final.merge(df0, how=\"inner\", left_on=\"outlet_id\", right_on=\"o_id\")\n", "df_final = df_final.merge(\n", "    df0, how=\"inner\", left_on=\"merchant_outlet_id\", right_on=\"o_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bc0fd228-ece4-4852-844f-2ab6b5209836", "metadata": {}, "outputs": [], "source": ["df_final = df_final.rename(\n", "    columns={\n", "        \"outlet_id\": \"Source Outlet ID\",\n", "        \"outlet_name_x\": \"Source Outlet Name\",\n", "        \"merchant_outlet_id\": \"Destination Outlet ID\",\n", "        \"outlet_name_y\": \"Destination Outlet Name\",\n", "        \"ars_run_id\": \"ARS RUN ID\",\n", "        \"total_stos_created\": \"Total STOs Created\",\n", "        \"stos_created_at\": \"STOs Created At\",\n", "        \"ars_qty\": \"ARS Created Qty\",\n", "        \"total_stos_billed\": \"Total STOs Billed\",\n", "        \"billed_at\": \"Billing Completed At\",\n", "        \"billed_qty\": \"Billed Qty\",\n", "        \"total_stos_dispatched\": \"Total STOs Dispatched\",\n", "        \"dispatched_at\": \"Dispatched At\",\n", "        \"total_stos_grn\": \"Total STOs GRN\",\n", "        \"grn_completed_at\": \"GRN Completed At\",\n", "        \"grn_qty\": \"GRN Qty\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "791807ff-7c5f-499c-a6e5-c118394c9420", "metadata": {}, "outputs": [], "source": ["df_final = df_final[\n", "    [\n", "        \"ARS RUN ID\",\n", "        \"Source Outlet ID\",\n", "        \"Source Outlet Name\",\n", "        \"Destination Outlet ID\",\n", "        \"Destination Outlet Name\",\n", "        \"Total STOs Created\",\n", "        \"STOs Created At\",\n", "        \"ARS Created Qty\",\n", "        \"Total STOs Billed\",\n", "        \"Billing Completed At\",\n", "        \"Billed <PERSON><PERSON>\",\n", "        \"Total STOs Dispatched\",\n", "        \"Dispatched At\",\n", "        \"Total STOs GRN\",\n", "        \"GRN Completed At\",\n", "        \"GRN Qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3257f4e0-db3d-4add-a20c-7dedc44f162f", "metadata": {}, "outputs": [], "source": ["df_final[\"STOs Created At\"] = df_final[\"STOs Created At\"].astype(\"datetime64[s]\")\n", "df_final[\"ARS Created Qty\"] = df_final[\"ARS Created Qty\"].astype(\"int64\")\n", "df_final[\"Total STOs Billed\"] = df_final[\"Total STOs Billed\"].astype(\"int64\")\n", "df_final[\"Billed Qty\"] = df_final[\"Billed Qty\"].astype(\"int64\")\n", "df_final[\"Total STOs Dispatched\"] = df_final[\"Total STOs Dispatched\"].astype(\"int64\")\n", "df_final[\"GRN Qty\"] = df_final[\"GRN Qty\"].astype(\"int64\")\n", "df_final[\"Total STOs GRN\"] = df_final[\"Total STOs GRN\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "1c939290-e3ff-4a5d-a38f-593cb5cb5a16", "metadata": {}, "outputs": [], "source": ["df_final = df_final.sort_values(by=\"ARS RUN ID\", ascending=False).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "6ca1bd61-b8c9-4268-9d7d-f9a0701395ff", "metadata": {}, "outputs": [], "source": ["df_final[\"STO Creation to Dispatch time\"] = (\n", "    df_final[\"Dispatched At\"] - df_final[\"STOs Created At\"]\n", ")\n", "df_final[\"STO Creation to GRN time\"] = (\n", "    df_final[\"GRN Completed At\"] - df_final[\"STOs Created At\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6b51c242-6638-42ea-8e9c-8f342cfa3841", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_final,\n", "    \"1zJ4ewHPnDj0pXhFOHme3jqM3FCrntxjqclyaJ5_aT-E\",\n", "    \"Raw_data_jhub\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bca54324-50fa-496f-a426-27bdc8921577", "metadata": {}, "outputs": [], "source": ["print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "id": "61178456-8cc6-4483-951e-01a04b32c5b7", "metadata": {}, "outputs": [], "source": ["df_final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3fc4908f-1d16-41fc-aeef-05f3a38c88e3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}