{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install lightgbm\n", "!pip install joblib\n", "!pip install pytz\n", "!pip install datetime\n", "!pip install -q scikit-learn==0.22.2.post1\n", "import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime\n", "import joblib\n", "import pytz\n", "import lightgbm as lgb\n", "from sklearn import datasets, linear_model, metrics\n", "from datetime import datetime as datetime\n", "from datetime import timedelta\n", "\n", "retail = pb.get_connection(\"retail\")\n", "Redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building prediction dataset with PO related data "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if datetime.now(pytz.timezone(\"Asia/Calcutta\")).hour <= 6:\n", "    start_date = (datetime.today()).date()  # enter date comma separated\n", "    end_date = (datetime.today() + timedelta(days=1)).date()\n", "else:\n", "    start_date = (datetime.today() + timedelta(days=1)).date()\n", "    end_date = (datetime.today() + timedelta(days=1)).date()\n", "print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_p1 = f\"\"\"\n", "SELECT f.id AS facility,\n", "             f.name AS facility_name,\n", "             o.id AS outlet_id,\n", "             o.name AS outlet_name,\n", "             date(comp.scheduled_time) AS po_created_at_date,\n", "             scheduled_time,\n", "             po.id AS po_id ,\n", "             po.po_number as reference_number,\n", "             count(DISTINCT poi.item_id) AS po_SKU_count,\n", "             sum(poi.units_ordered) AS po_quantity,\n", "             (sum(poi.units_ordered*weight_in_gm)/1000.0)as total_weight_in_kgs,\n", "             count(distinct case when p.storage_type in (2,3,6,7) then poi.item_id end) as cold_sku_count,\n", "             count(distinct case when p.storage_type not in (2,3,6,7) then poi.item_id end) as grocery_sku_count,\n", "             sum(case when p.storage_type in (2,3,6,7) then poi.units_ordered end) as cold_quantity,\n", "             sum(case when p.storage_type not in (2,3,6,7) then poi.units_ordered end) as grocery_quantity\n", "      from lake_po.purchase_order po    \n", "      INNER join lake_po.purchase_order_items poi ON poi.po_id=po.id\n", "      INNER join lake_retail.view_console_outlet o ON o.id=po.outlet_id\n", "      INNER join lake_crates.facility f ON o.facility_id = f.id\n", "      INNER join lake_rpc.product_product p ON p.variant_id=poi.variant_id\n", "      INNER join (select pop.id as po_id,convert_timezone('Asia/Kolkata',ps.schedule_date_time)as scheduled_time\n", "      from lake_po.purchase_order pop \n", "            inner join lake_po.po_schedule ps on pop.id = ps.po_id_id\n", "                 where date(convert_timezone('Asia/Kolkata',ps.schedule_date_time)) between '{start_date}' and '{end_date}'  \n", "                 and pop.id not in (select distinct po_id from lake_po.purchase_order_log where po_state_id=9)\n", "                 group by 1,2) as comp\n", "      on comp.po_id=po.id\n", "      WHERE facility_id in (1,3,42,26,29,49,12,140,268,92,63,30,48,149,22,43,15,34,24,299,120,139)\n", "      and o.name not like '%%Cold%%' and o.name not like '%%F&V%%'\n", "      GROUP BY 1,2,3,4,5,6,7,8\n", "      \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pred_base1 = pd.read_sql_query(sql=query_p1, con=Redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pred_base1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pred_base1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_pred = pred_base1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_pred.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_pred.to_csv(\"prediction_data.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data transformation on prediction dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_pred[\"month\"] = pd.DatetimeIndex(final_pred.po_created_at_date).month\n", "final_pred[\"day\"] = pd.DatetimeIndex(final_pred.po_created_at_date).day\n", "final_pred[\"day_name\"] = pd.DatetimeIndex(final_pred.po_created_at_date).day_name()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_pred[\"grocery_sku_perc\"] = (\n", "    final_pred[\"grocery_sku_count\"] / final_pred[\"po_sku_count\"]\n", ") * 100\n", "final_pred[\"grocery_quan_perc\"] = (\n", "    final_pred[\"grocery_quantity\"] / final_pred[\"po_quantity\"]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = final_pred.drop(\n", "    columns=[\n", "        \"scheduled_time\",\n", "        \"facility_name\",\n", "        \"outlet_name\",\n", "        \"outlet_id\",\n", "        \"grocery_sku_count\",\n", "        \"cold_sku_count\",\n", "        \"po_created_at_date\",\n", "        \"cold_quantity\",\n", "        \"grocery_quantity\",\n", "        \"po_id\",\n", "        \"reference_number\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_copy = final_pred.drop(\n", "    columns=[\n", "        \"grocery_sku_count\",\n", "        \"cold_sku_count\",\n", "        \"po_created_at_date\",\n", "        \"cold_quantity\",\n", "        \"grocery_quantity\",\n", "        \"reference_number\",\n", "    ]\n", ")\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.concat([data, pd.get_dummies(data[\"facility\"], prefix=\"fac_\")], axis=1)\n", "data = pd.concat([data, pd.get_dummies(data[\"day_name\"])], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = [\n", "    \"fac__1\",\n", "    \"fac__3\",\n", "    \"fac__42\",\n", "    \"fac__26\",\n", "    \"fac__29\",\n", "    \"fac__49\",\n", "    \"fac__12\",\n", "    \"fac__140\",\n", "    \"fac__268\",\n", "    \"fac__92\",\n", "    \"fac__63\",\n", "    \"fac__30\",\n", "    \"fac__48\",\n", "    \"fac__149\",\n", "    \"fac__22\",\n", "    \"fac__43\",\n", "    \"fac__15\",\n", "    \"fac__34\",\n", "    \"fac__24\",\n", "    \"fac__299\",\n", "    \"fac__120\",\n", "    \"fac__139\",\n", "    \"Monday\",\n", "    \"Tuesday\",\n", "    \"Wednesday\",\n", "    \"Thursday\",\n", "    \"Friday\",\n", "    \"Saturday\",\n", "    \"Sunday\",\n", "]\n", "c = data.columns.tolist()\n", "for i in x:\n", "    if i in c:\n", "        continue\n", "    else:\n", "        data[i] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.drop(columns=[\"facility\", \"day_name\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.dropna()\n", "data_copy = data_copy.dropna()\n", "data.shape\n", "prediction_data = data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prediction_data.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading models from cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["op_cloud_filepath_lr = \"/eep/warehouse/inbound/model_dumps/lr_model_v3.sav\"\n", "op_cloud_filepath_gbm = \"/eep/warehouse/inbound/model_dumps/gbm_model_v3.sav\"\n", "op_cloud_filepath_gbm_grn = \"/eep/warehouse/inbound/model_dumps/gbm_model_grn_v3.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_filename_lightgbm_model = \"lightgbm_model_v3.sav\"\n", "local_filename_lightgbm_model_grn = \"lightgbm_model_grn_v3.sav\"\n", "local_filename_lr_model = \"lr_model_v3.sav\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket_name = \"prod-dse-projects\"\n", "pb.from_s3(bucket_name, op_cloud_filepath_lr, local_filename_lr_model)\n", "pb.from_s3(bucket_name, op_cloud_filepath_gbm, local_filename_lightgbm_model)\n", "pb.from_s3(bucket_name, op_cloud_filepath_gbm_grn, local_filename_lightgbm_model_grn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["regressor_model = joblib.load(local_filename_lr_model)\n", "gbm_model = joblib.load(local_filename_lightgbm_model)\n", "gbm_model_grn = joblib.load(local_filename_lightgbm_model_grn)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prediction flow\n", "- Passing the prediction dataset to the model\n", "- Storing the predicted values/datasets in Redshift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred = gbm_model.predict(prediction_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred_grn = gbm_model_grn.predict(prediction_data)\n", "fin_pred_grn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred1 = pd.DataFrame(fin_pred).reset_index()\n", "fin_pred1 = fin_pred1.drop(columns=[\"index\"])\n", "fin_pred1.rename(columns={0: \"predicted_unloading_time_mins\"}, inplace=True)\n", "fin_pred_grn = pd.DataFrame(fin_pred_grn).reset_index()\n", "fin_pred_grn = fin_pred_grn.drop(columns=[\"index\"])\n", "fin_pred_grn.rename(columns={0: \"predicted_grn_time_mins\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred3 = pd.concat([fin_pred1, fin_pred_grn], axis=1)\n", "fin_pred3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_copy = data_copy.reset_index()\n", "data_copy = data_copy.drop(columns=[\"index\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred2 = pd.concat([data_copy, fin_pred3], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["IST = pytz.timezone(\"Asia/Kolkata\")\n", "fin_pred2[\"created_at_IST\"] = datetime.now(IST)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_pred2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"178Ontt-fuohSrQ8vEJqnVZrsVklgoh4SbRK6P9w-SVs\"\n", "ideal = pb.from_sheets(sheet_id, \"Sheet1\", service_account=\"service_account\")\n", "\n", "ideal[\"month\"] = (datetime.today() + timedelta(days=1)).month\n", "ideal[\"day\"] = (datetime.today() + timedelta(days=1)).day\n", "ideal[\"day_name\"] = (datetime.today() + timedelta(days=1)).strftime(\"%A\")\n", "\n", "ideal = ideal.rename(\n", "    columns={\n", "        \"Facility\": \"facility\",\n", "        \"Ideal_SKUs_per_PO\": \"po_sku_count\",\n", "        \"Ideal_quantity_per_PO\": \"po_quantity\",\n", "        \"Ideal_weight_kgs\": \"total_weight_in_kgs\",\n", "    }\n", ")\n", "\n", "ideal[\"po_sku_count\"] = ideal[\"po_sku_count\"].astype(\"int\")\n", "ideal[\"po_quantity\"] = ideal[\"po_quantity\"].astype(\"int\")\n", "ideal[\"total_weight_in_kgs\"] = ideal[\"total_weight_in_kgs\"].astype(\"float\")\n", "\n", "ideal[\"grocery_sku_perc\"] = 100\n", "ideal[\"grocery_quan_perc\"] = 100\n", "ideal\n", "ideal_copy = ideal.copy()\n", "ideal = ideal.drop(columns=[\"Ideal_PO_count\"])\n", "\n", "ideal = pd.concat([ideal, pd.get_dummies(ideal[\"facility\"], prefix=\"fac_\")], axis=1)\n", "ideal = pd.concat([ideal, pd.get_dummies(ideal[\"day_name\"])], axis=1)\n", "\n", "x = [\n", "    \"fac__1\",\n", "    \"fac__3\",\n", "    \"fac__42\",\n", "    \"fac__26\",\n", "    \"fac__29\",\n", "    \"fac__49\",\n", "    \"fac__12\",\n", "    \"fac__140\",\n", "    \"fac__268\",\n", "    \"fac__92\",\n", "    \"fac__63\",\n", "    \"fac__30\",\n", "    \"fac__48\",\n", "    \"fac__149\",\n", "    \"fac__22\",\n", "    \"fac__43\",\n", "    \"fac__15\",\n", "    \"fac__34\",\n", "    \"fac__24\",\n", "    \"fac__299\",\n", "    \"fac__120\",\n", "    \"fac__139\",\n", "    \"Monday\",\n", "    \"Tuesday\",\n", "    \"Wednesday\",\n", "    \"Thursday\",\n", "    \"Friday\",\n", "    \"Saturday\",\n", "    \"Sunday\",\n", "]\n", "c = ideal.columns.tolist()\n", "for i in x:\n", "    if i in c:\n", "        continue\n", "    else:\n", "        ideal[i] = 0\n", "\n", "ideal = ideal.drop(columns=[\"facility\", \"day_name\"])\n", "\n", "ideal = ideal.dropna()\n", "prediction_data1 = ideal\n", "\n", "\n", "ideal_pred_unl = gbm_model.predict(prediction_data1)\n", "ideal_pred_grn = gbm_model_grn.predict(prediction_data1)\n", "\n", "fin_pred2_id = pd.DataFrame(ideal_pred_unl).reset_index()\n", "fin_pred2_id = fin_pred2_id.drop(columns=[\"index\"])\n", "fin_pred2_id.rename(columns={0: \"ideal_unloading_time_mins\"}, inplace=True)\n", "ideal_pred_grn = pd.DataFrame(ideal_pred_grn).reset_index()\n", "ideal_pred_grn = ideal_pred_grn.drop(columns=[\"index\"])\n", "ideal_pred_grn.rename(columns={0: \"ideal_grn_time_mins\"}, inplace=True)\n", "\n", "fin_pred4 = pd.concat([fin_pred2_id, ideal_pred_grn], axis=1)\n", "\n", "ideal_copy = ideal_copy.reset_index()\n", "ideal_copy = ideal_copy.drop(columns=[\"index\"])\n", "\n", "ideal_final = pd.concat([ideal_copy, fin_pred4], axis=1)\n", "\n", "ideal_final = ideal_final.drop(\n", "    columns=[\"month\", \"day_name\", \"day\", \"grocery_sku_perc\", \"grocery_quan_perc\"]\n", ")\n", "\n", "ideal_final[\"facility\"] = ideal_final[\"facility\"].astype(\"int\")\n", "ideal_final[\"Ideal_PO_count\"] = ideal_final[\"Ideal_PO_count\"].astype(\"int\")\n", "\n", "ideal_final = ideal_final.rename(\n", "    columns={\n", "        \"po_sku_count\": \"ideal_po_sku_count\",\n", "        \"po_quantity\": \"ideal_po_quantity\",\n", "        \"total_weight_in_kgs\": \"ideal_weight_in_kgs\",\n", "    }\n", ")\n", "ideal_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_predicted_df = pd.merge(fin_pred2, ideal_final, on=\"facility\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_predicted_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_predicted_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_predicted_df = final_predicted_df.rename(columns={\"facility\": \"facility_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "    {\"name\": \"scheduled_time\", \"type\": \"datetime\", \"description\": \"scheduled_time\"},\n", "    {\"name\": \"po_id\", \"type\": \"int\", \"description\": \"po_id\"},\n", "    {\"name\": \"po_sku_count\", \"type\": \"int\", \"description\": \"po_sku_count\"},\n", "    {\"name\": \"po_quantity\", \"type\": \"int\", \"description\": \"po_quantity\"},\n", "    {\n", "        \"name\": \"total_weight_in_kgs\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total_weight_in_kgs\",\n", "    },\n", "    {\"name\": \"month\", \"type\": \"int\", \"description\": \"month\"},\n", "    {\"name\": \"day\", \"type\": \"int\", \"description\": \"day\"},\n", "    {\"name\": \"day_name\", \"type\": \"varchar\", \"description\": \"day_name\"},\n", "    {\"name\": \"grocery_sku_perc\", \"type\": \"float\", \"description\": \"grocery_sku_perc\"},\n", "    {\"name\": \"grocery_quan_perc\", \"type\": \"float\", \"description\": \"grocery_quan_perc\"},\n", "    {\n", "        \"name\": \"predicted_unloading_time_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"predicted_unloading_time_mins\",\n", "    },\n", "    {\n", "        \"name\": \"predicted_grn_time_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"predicted_grn_time_mins\",\n", "    },\n", "    {\"name\": \"created_at_IST\", \"type\": \"datetime\", \"description\": \"created_at_IST\"},\n", "    {\n", "        \"name\": \"ideal_po_sku_count\",\n", "        \"type\": \"float\",\n", "        \"description\": \"ideal_po_sku_count\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_po_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"ideal_po_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_weight_in_kgs\",\n", "        \"type\": \"float\",\n", "        \"description\": \"ideal_weight_in_kgs\",\n", "    },\n", "    {\n", "        \"name\": \"Ideal_PO_count\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Ideal_PO_count\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_unloading_time_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"ideal_unloading_time_mins\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_grn_time_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"ideal_grn_time_mins\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inbound_effort_estimation_project_v3\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"po_id\"],\n", "    \"sortkey\": [\"scheduled_time\", \"outlet_id\"],\n", "    \"incremental_key\": \"created_at_IST\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This tables stores the predictions from the inbound effort estimation model\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final_predicted_df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}