{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Master"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "\n", "sheet_name1 = \"Master\"\n", "Master = pb.from_sheets(sheet_id, sheet_name1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master = Master.iloc[1:]\n", "\n", "new_header = Master.iloc[0]\n", "Master = Master[1:]\n", "Master.columns = new_header\n", "\n", "Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.columns\n", "Master.drop(\n", "    columns=[\"\", \"\", \"Store Name\", \"sender FC for replesn\"], axis=1, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master[\"Outlet ID\"] = Master[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")\n", "Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_list = Master[\"Outlet ID\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list1 = [x for x in outlet_list if pd.isnull(x) == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import datetime, date, timedelta\n", "import calendar\n", "\n", "dt = datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "year = dt.year\n", "month = dt.month\n", "\n", "num_days = calendar.monthrange(year, month)[1]\n", "days = [date(year, month, day) for day in range(1, num_days + 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray = []\n", "for i in range(0, len(list1)):\n", "    for j in range(0, len(days)):\n", "        tempArray = [list1[i], days[j]]\n", "        finalArray.append(tempArray[:])\n", "finalArray = pd.<PERSON><PERSON><PERSON><PERSON>(finalArray)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray.columns = [\"outlet_id\", \"date\"]\n", "finalArray[\"date\"] = pd.to_datetime(finalArray[\"date\"])\n", "finalArray.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Billed GMV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "SELECT B.outlet_id,\n", "       --B.outlet_name,\n", "       SUM(B.SPIT) AS IncTax,\n", "       --SUM(B.SPET) AS ExcTax,\n", "       B.TransactionDate\n", "FROM\n", "    (\n", "    SELECT A.outlet_id,\n", "           --A.outlet_name,\n", "           A.invoice_id,\n", "           <PERSON><PERSON>gro<PERSON>_order_id,\n", "           A.is_billed_from_order_info,\n", "           <PERSON>.crate_ids,\n", "           SUM(A.PreTaxAmount) AS PreTaxAmount,\n", "           SUM(A.TotalTaxAmount) AS TotalTaxAmount,\n", "           SUM(A.TotalSalesAmount) AS TotalSalesAmount,\n", "           SUM(A.TotalReturnAmount) AS TotalReturnAmount,\n", "           (SUM(A.TotalSalesAmount) - SUM(A.TotalReturnAmount)) AS SPIT,\n", "           CASE WHEN SUM(A.<PERSON>mount) > 0 \n", "                THEN (SUM(A.TotalSalesAmount) - SUM(A.TotalTaxAmount)) \n", "                ELSE (((SUM(A.TotalReturnAmount) - SUM(A.TotalTaxAmount)))*(-1))\n", "           END AS SPET,\n", "           <PERSON><PERSON>,\n", "           A.TransactionDate\n", "\n", "    from\n", "        (\n", "        SELECT \n", "               case \n", "                   when p.outlet_id in (1445,1637,1638) then 1445\n", "                   when p.outlet_id in (1405,1675,1676) then 1676\n", "                   when p.outlet_id in (1449,1699,1700) then 1700\n", "                   else p.outlet_id end as outlet_id,\n", "               --p.outlet_name,\n", "               p.invoice_id,\n", "               p.grofers_order_id,\n", "               p.is_billed_from_order_info,\n", "               p.crate_ids,\n", "               Round(sum(p.actual_sales)-sum((CASE WHEN pi.cgst_value IS NULL THEN 0 ELSE pi.cgst_value END)+ \n", "                                             (CASE WHEN pi.sgst_value IS NULL THEN 0 ELSE pi.sgst_value END)+ \n", "                                             (CASE WHEN pi.igst_value IS NULL THEN 0 ELSE pi.igst_value END)+ \n", "                                             (CASE WHEN pi.cess_value IS NULL THEN 0 ELSE pi.cess_value END)),2) AS PreTaxAmount,\n", "               sum((CASE WHEN pi.cgst_value IS NULL THEN 0 ELSE pi.cgst_value END)+ \n", "                   (CASE WHEN pi.sgst_value IS NULL THEN 0 ELSE pi.sgst_value END)+ \n", "                   (CASE WHEN pi.igst_value IS NULL THEN 0 ELSE pi.igst_value END)+ \n", "                   (CASE WHEN pi.cess_value IS NULL THEN 0 ELSE pi.cess_value END)) AS TotalTaxAmount,\n", "               (CASE WHEN p.invoice_type_id = 1 THEN p.actual_sales ELSE 0 END) AS TotalSalesAmount,\n", "               (CASE WHEN p.invoice_type_id = 2 THEN p.actual_sales ELSE 0 END) AS TotalReturnAmount,\n", "               convert_timezone('Asia/Kolkata',p.pos_timestamp) AS TransactionTime,\n", "               DATE(convert_timezone('Asia/Kolkata',p.pos_timestamp)) AS TransactionDate\n", "        FROM lake_pos.pos_invoice p\n", "        INNER JOIN lake_pos.pos_invoice_product_details pi ON pi.invoice_id=p.id\n", "        INNER JOIN lake_retail.console_outlet o ON p.outlet_id = o.id\n", "        WHERE p.invoice_type_id IN (1,2)\n", "        AND date(p.pos_timestamp + interval '5.5 hours') \n", "            between date(CURRENT_DATE - interval '31 days') and date(CURRENT_DATE - interval '1 days')\n", "        AND o.business_type_id = 7\n", "        GROUP BY 1,2,3,4,5,8,9,10,11\n", "        ) AS A\n", "    group by 1,2,3,4,5,12,13\n", "    ) AS B\n", "group by 1,3\n", "order by 1,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = pd.read_sql_query(q2, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delivered GMV & Orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q3 = \"\"\"\n", "\n", "with fc_mapping as \n", "(\n", "select distinct\n", "    pco.facility_id,\n", "    cf.name as facility_name,\n", "    bom.external_id as backend_merchant_id,\n", "    bom.name as backend_merchant_name,\n", "    pco.id as outlet_id,\n", "    pco.name as outlet_name\n", "from lake_oms_bifrost.view_oms_merchant as bom\n", "inner join  lake_retail.console_outlet_cms_store as cms\n", "    on bom.external_id = cms.cms_store \n", "    and cms.active = true\n", "    and cms.cms_update_active = true\n", "    and virtual_merchant_type <> 'superstore_merchant'\n", "inner join  lake_retail.console_outlet  as pco\n", "    on cms.outlet_id = pco.id\n", "inner join  lake_crates.facility  as cf \n", "    on cf.id = pco.facility_id\n", "),\n", "\n", "e3_stores as \n", "(\n", "select merchant_id  \n", "from consumer.express_stores ex \n", "where ex.active=1        \n", "union\n", "select external_id \n", "from lake_logistics.logistics_node\n", "),\n", "\n", "sch as\n", "(\n", "select distinct\n", "    city,\n", "    max(outlet_id) over(partition by frontend_merchant_id) as outlet_id,\n", "    max(outlet_name) over(partition by frontend_merchant_id) as outlet_name,\n", "    max(facility_name) over(partition by frontend_merchant_id) as facility,\n", "    frontend_merchant_id,\n", "    frontend_merchant_name,\n", "    station,\n", "    order_id,\n", "    sch_slot_start,\n", "    sch_slot_end,\n", "    current_status,\n", "    total_cost,\n", "    order_type\n", "from\n", "    (\n", "    select distinct\n", "        m.city_name as city,\n", "        fc.outlet_id,\n", "        fc.outlet_name,\n", "        fc.facility_name,\n", "        m.external_id as frontend_merchant_id,\n", "        m.name as frontend_merchant_name,\n", "        upper(SUBSTRING(station,8)) as station,\n", "        o.id as order_id,\n", "        os.id as suborder_id,\n", "        convert_timezone('Asia/Kolkata',(timestamp 'epoch' + o.slot_start * interval '1 second')) sch_slot_start,\n", "        convert_timezone('Asia/Kolkata',(timestamp 'epoch' + o.slot_end * interval '1 second')) sch_slot_end,\n", "        o.current_status,\n", "        o.procurement_amount as total_cost,\n", "        o.type as order_type\n", "    from lake_oms_bifrost.oms_order o\n", "    join lake_oms_bifrost.view_oms_merchant m \n", "        on o.merchant_id = m.id \n", "        and  m.virtual_merchant_type = 'superstore_merchant'\n", "    inner join e3_stores ex on m.external_id=ex.merchant_id    \n", "    inner join lake_oms_bifrost.oms_suborder os \n", "        on os.order_id = o.id \n", "        and os.type not ilike 'DigitalSuborder'\n", "    left join lake_oms_bifrost.view_oms_merchant bom \n", "        on os.backend_merchant_id = bom.id \n", "        and bom.virtual_merchant_type <> 'superstore_merchant'\n", "    left join fc_mapping fc \n", "        on fc.backend_merchant_id = bom.external_id\n", "    where o.type in ('RetailForwardOrder')\n", "    and o.slot_start::int between extract(epoch from ((((current_date-31)::date)::timestamp) - interval '5.5 hour')) \n", "    and extract(epoch from ((((current_date-1)::date)::timestamp) + interval '1 day' - interval '5.5 hour'))\n", "    )\n", "),\n", "\n", "res as \n", "(\n", "select distinct \n", "    city,\n", "    max(outlet_id) over(partition by frontend_merchant_id) as outlet_id,\n", "    max(outlet_name) over(partition by frontend_merchant_id) as outlet_name,\n", "    max(facility_name) over(partition by frontend_merchant_id) as facility,\n", "    frontend_merchant_id,\n", "    frontend_merchant_name,\n", "    station,\n", "    sch_slot_start,\n", "    sch_slot_end,\n", "    order_id,\n", "    current_status,\n", "    total_cost,\n", "    reason_key,\n", "    order_type\n", "from \n", "(\n", "    select distinct r.*, \n", "            dense_rank() over (partition by order_id,event_ts_ist::date,ooe_slot ORDER BY event_ts_ist desc) AS ooe_rank \n", "    from\n", "    (\n", "        select distinct\n", "            m.city_name as city,\n", "            fc.outlet_id,\n", "            fc.outlet_name,\n", "            fc.facility_name,\n", "            m.external_id as frontend_merchant_id,\n", "            m.name as frontend_merchant_name,\n", "            upper(SUBSTRING(station,8)) as station,\n", "            (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'old_slot_start',true)::int* interval '1 second' + interval '5.5 hour') as sch_slot_start,\n", "            (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'old_slot_end',true)::int* interval '1 second' + interval '5.5 hour') as sch_slot_end,\n", "            (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'new_slot_start',true)::int* interval '1 second' + interval '5.5 hour') as new_slot_start,\n", "            (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'new_slot_end',true)::int* interval '1 second' + interval '5.5 hour') as new_slot_end,\n", "            oe.order_id,\n", "            os.id as suborder_id,\n", "            (oe.install_ts  + interval '5.5 hour') as event_ts_ist,\n", "            o.current_status,\n", "            o.procurement_amount as total_cost,\n", "            oe.reason_key,\n", "            cast(json_extract_path_text(oe.extra,'old_slot_start',true) as text)||cast(json_extract_path_text(oe.extra,'old_slot_end',true)as text)||cast(json_extract_path_text(oe.extra,'new_slot_start',true)as text)||cast(json_extract_path_text(oe.extra,'new_slot_end',true)as text) as ooe_slot,\n", "            o.type as order_type\n", "        from lake_oms_bifrost.oms_order_event oe\n", "        inner join lake_oms_bifrost.oms_order o \n", "            on oe.order_id = o.id\n", "        join lake_oms_bifrost.view_oms_merchant m \n", "            on o.merchant_id = m.id \n", "            and  m.virtual_merchant_type = 'superstore_merchant'\n", "        inner join e3_stores ex \n", "            on m.external_id=ex.merchant_id    \n", "        inner join lake_oms_bifrost.oms_suborder \n", "            os on os.order_id = o.id \n", "            and os.type not ilike 'DigitalSuborder'\n", "        left join lake_oms_bifrost.view_oms_merchant bom \n", "            on os.backend_merchant_id = bom.id \n", "            and bom.virtual_merchant_type <> 'superstore_merchant'\n", "        left join fc_mapping fc \n", "            on fc.backend_merchant_id = bom.external_id\n", "        where o.type in ('RetailForwardOrder')\n", "        and oe.event_type_key = 'order_rescheduling'\n", "        and json_extract_path_text(oe.extra,'old_slot_start',true)::int between extract(epoch from ((((current_date-31)::date)::timestamp) - interval '5.5 hour')) \n", "        and extract(epoch from ((((current_date-1)::date)::timestamp) + interval '1 day' - interval '5.5 hour'))\n", "    )r\n", "    where sch_slot_start::date < new_slot_start::date\n", ")r1\n", "where ooe_rank = 1\n", "),\n", "\n", "lm_base as\n", "(\n", "select distinct \n", "    city, \n", "    outlet_id,\n", "    outlet_name,\n", "    facility,\n", "    frontend_merchant_id,\n", "    frontend_merchant_name, \n", "    station,\n", "    sch_slot_start, \n", "    sch_slot_end, \n", "    order_id, \n", "    current_status,\n", "    total_cost,\n", "    'new_sch' as order_cat,\n", "    null as reason_key,\n", "    order_type\n", "from sch\n", "union\n", "select distinct \n", "    city, \n", "    outlet_id,\n", "    outlet_name,\n", "    facility,\n", "    frontend_merchant_id,\n", "    frontend_merchant_name, \n", "    station,\n", "    sch_slot_start, \n", "    sch_slot_end, \n", "    order_id, \n", "    current_status,\n", "    total_cost,\n", "    'resch' as order_cat, \n", "    reason_key,\n", "    order_type\n", "from res\n", "),\n", "\n", "delivered_lm as \n", "(\n", "select  external_order_id as id,\n", "        (delivery_slot_start_time+ interval '330 minute') as sch_slot_start,\n", "        (oa.actual_event_time + interval '330 minute') as delivered_ts,\n", "        employee_id ,\n", "        null as user_role\n", "from lake_last_mile.order o\n", "Inner Join lake_last_mile.order_action oa \n", "    on o.id = oa.order_id \n", "inner join lake_last_mile.field_executive fe \n", "    on fe.id=field_executive_id\n", "where oa.action_type = 'DELIVERED'\n", "and (oa.actual_event_time + interval '330 minute')::date between ((current_date-31)::date) and ((current_date)::date)\n", "),\n", "\n", "delivered_logistics as \n", "(\n", "select lsl.label_value as id,\n", "    (delivery_start  + interval '330 minute') as sch_slot_start,\n", "    (lt.update_ts  + interval '330 minute') as delivered_ts,\n", "    employee_id,\n", "    case \n", "        when json_extract_path_text(meta,'user_role')<>'' then json_extract_path_text(meta,'user_role') \n", "        else null end as user_role\n", "from lake_logistics.logistics_shipment ls \n", "inner join lake_logistics.logistics_shipment_label lsl \n", "    on lsl.shipment_id=ls.id  \n", "    and label_type='ORDER_ID' \n", "    and status='DELIVERED'\n", "left join lake_logistics.logistics_task lt \n", "    on lsl.label_value = lt.shipment_label_value \n", "    and lt.type = 'ORDER_DELIVERY_TASK' \n", "    and state = 'COMPLETED'\n", "inner join lake_logistics.logistics_user_profile p \n", "    on p.id=lt.user_profile_id\n", "inner join lake_logistics.logistics_user u \n", "    on u.id=p.user_id\n", "where (delivery_start+ interval '330 minute')::date  between ((current_date-31)::date) and ((current_date-1)::date)\n", "and (lt.update_ts+ interval '330 minute')::date  between ((current_date-31)::date) and ((current_date-1)::date)\n", "),\n", "    \n", "delivered as\n", "(\n", "select * \n", "from delivered_lm\n", "union \n", "select * \n", "from delivered_logistics\n", "),\n", "\n", "pos_invoice_amt as \n", "(\n", "select os.order_id,\n", "    sum(distinct actual_sales) as actual_sales \n", "from lake_oms_bifrost.oms_suborder os \n", "join  lake_pos.pos_invoice  a \n", "    on os.id = a.grofers_order_id \n", "    and a.invoice_type_id = 1 \n", "join lake_pos.pos_invoice_product_details b \n", "    on a.id = b.invoice_id \n", "    and name not like 'Offer%%'\n", "Where current_status not in ('REPROCURED','CANCELLED')\n", "and DATE(TIMESTAMP 'epoch' + os.slot_start*INTERVAL '1 Second' +INTERVAL '5.5 hour') between ((current_date-31)::date) and ((current_date)::date)\n", "group by 1\n", "),\n", "\n", "---------SKU---------\n", "procured_sku_count as \n", "(\n", "select order_id,\n", "    sum(sku) as sku,\n", "    sum(quantity) as quantity ,\n", "    sum(actual_sales) as actual_sales\n", "from \n", "    (\n", "    select distinct os.order_id,\n", "        os.id,\n", "        count(distinct pd.id) as sku , \n", "        sum(pd.quantity) as quantity, \n", "        max(actual_sales) as actual_sales \n", "    from lake_oms_bifrost.oms_suborder os \n", "    inner join lake_pos.pos_invoice pos \n", "        on os.id=pos.grofers_order_id \n", "        and invoice_type_id=1\n", "    inner join lake_pos.pos_invoice_product_details pd \n", "        on pos.id=pd.invoice_id\n", "    where current_status not in ('REPROCURED','CANCELLED')\n", "    and pd.name not ilike '%%Offer%%'\n", "    and DATE(TIMESTAMP 'epoch' + os.slot_start*INTERVAL '1 Second' +INTERVAL '5.5 hour') between ((current_date-50)::date) and ((current_date)::date)\n", "    group by 1,2\n", "    )\n", "group by 1\n", "),\n", "\n", "order_dump as\n", "(\n", "select distinct\n", "    b.sch_slot_start::date as sch_date,\n", "    b.city,\n", "    b.outlet_id,\n", "    b.outlet_name,\n", "    b.facility,\n", "    b.frontend_merchant_id,\n", "    b.frontend_merchant_name,\n", "    station,\n", "    case when extract(hour from b.sch_slot_start)/10<1 then '0'||extract(hour from b.sch_slot_start)::text\n", "         else extract(hour from b.sch_slot_start)::text end\n", "    ||'-'||\n", "    case when extract(hour from b.sch_slot_end)/10<1 then '0'||extract(hour from b.sch_slot_end)::text\n", "        else extract(hour from b.sch_slot_end)::text end\n", "    as slot,\n", "    case when (extract(hour from b.sch_slot_end) - extract(hour from b.sch_slot_start)) = 1 then 'express slot'\n", "         else 'long slot' \n", "    end AS slot_type,\n", "    b.order_id,\n", "    d.id as delivered,\n", "    employee_id,\n", "    NVL(user_role,(case when employee_id like 'GSP%%' or employee_id like 'VSP%%' or employee_id like 'GEP%%' or employee_id like 'FLX%%' then 'GSP' \n", "    when employee_id like 'AMP%%' or employee_id like 'CAT%%' or employee_id like 'CON%%' or employee_id like 'CRE%%'\n", "    or employee_id like 'DEP%%' or employee_id like 'EKO%%' or employee_id like 'MIC%%' or employee_id like 'OYE%%'\n", "    or employee_id like 'PS%%' or employee_id like 'SAI%%' or employee_id like 'SHA%%' or employee_id like 'SIT%%' \n", "    or employee_id like 'TRE%%' or employee_id like 'WOW%%' or employee_id like 'TRO%%'  \n", "    or employee_id like 'JII%%'  or employee_id like 'EBI%%' or employee_id like 'CAR%%'\n", "    or employee_id like 'ASI%%'  or employee_id like 'IPC%%' or employee_id like 'BAP%%'  or employee_id like 'TEA%%' \n", "    or employee_id like 'RAJ%%' or employee_id like 'DIG%%' or employee_id like 'TET%%' \n", "    or employee_id like 'VSY%%' \n", "    or employee_id like 'BAT%%' or employee_id like 'MOO%%' or employee_id like 'PS%%' or employee_id like 'ZYN%%' \n", "    or employee_id like 'ASI%%'  or employee_id like 'GAY%%' or employee_id like 'MAT%%' or employee_id like 'AIM%%'\n", "    or employee_id like 'AVA%%' or employee_id like 'RAP%%' or employee_id like 'GRO%%' or employee_id like 'ZYP%%'\n", "    or employee_id like 'THE%%' or employee_id like 'LOG%%' or employee_id like 'ZOMATOKA%%' or employee_id like 'EXA%%'\n", "    or employee_id like 'MER%%' or employee_id like 'DOT%%' or employee_id like 'VXP%%' or employee_id like 'ORI%%'\n", "    or employee_id like 'ZOMATOLKO%%' or employee_id like 'TUS%%' or employee_id like 'ETO%%' or employee_id like 'SAN%%'\n", "    or employee_id like 'SNP%%' or employee_id like 'ZOM%%' \n", "    then '3PL'  else 'FE' end))as fe_category,\n", "    case when b.order_cat = 'resch' then 1 end as rescheduled,\n", "    case when b.current_status = 'CANCELLED' then 1 end as cancelled,\n", "    case when b.order_cat = 'resch' and \n", "                        b.reason_key in ('RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY',\n", "                                        'RESCHEDULE_DELIVERY_WEATHER_CONDITIONS',\n", "                                        'RESCHEDULE_MERCHANT_MISSED_BY_AC',\n", "                                        'RESCHEDULE_DELIVERY_OTHERS',\n", "                                        'RESCHEDULE_MERCHANT_MISSED_BY_LM',\n", "                                        'RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY',\n", "                                        'RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES',\n", "                                        'RESCHEDULE_CUSTOMER_ADDRESS_ISSUE'\n", "                                        ) then 1 end as LM_reschedule,\n", "    case when b.order_cat = 'resch' and\n", "                        b.reason_key in ('RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS',\n", "                                        'RESCHEDULE_CRM DRIVEN_OTHERS',\n", "                                        'RESCHEDULE_CRM DRIVEN_TECH_ISSUE',\n", "                                        'RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM',\n", "                                        'RESCHEDULE_CRM DRIVEN_PRE_ORDER'\n", "                                        ) then 1 end as CRM_reschedule,\n", "    sku.sku,\n", "    sku.actual_sales,\n", "    sku.quantity,\n", "    b.order_type\n", "    -- pic.procured_item_count as \n", "from lm_base b\n", "left join delivered d \n", "    on d.id = b.order_id \n", "    and b.sch_slot_start::date = d.sch_slot_start::date\n", "left join procured_sku_count sku \n", "    on sku.order_id=b.order_id\n", "order by 1 desc,2,3,4\n", ")\n", "\n", "select sch_date,\n", "        case \n", "           when outlet_id in (1445,1637,1638) then 1445\n", "           when outlet_id in (1405,1675,1676) then 1676\n", "           when outlet_id in (1449,1699,1700) then 1700\n", "           else outlet_id end as outlet_id,\n", "        fe_category,\n", "        count(distinct order_id) as order_count,\n", "        sum(actual_sales) as delivered_gmv,\n", "        sum(sku) as procured_sku_count,\n", "        sum(quantity) as procured_item_count\n", "from order_dump  \n", "where cancelled is null \n", "and delivered is not null\n", "group by 1,2,3\n", "order by 2,1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3 = pd.read_sql_query(q3, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3[\"fe_category\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3[\"sch_date\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delivered GMV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df31 = df3.groupby(by=[\"sch_date\", \"outlet_id\"]).sum()\n", "df31 = df31.reset_index()\n", "df31.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delivered Orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df32 = df3.groupby(by=[\"sch_date\", \"outlet_id\", \"fe_category\"]).sum()\n", "df32 = df32.reset_index()\n", "df32.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wrangling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2[\"transactiondate\"] = pd.to_datetime(df2[\"transactiondate\"])\n", "df31[\"sch_date\"] = pd.to_datetime(df31[\"sch_date\"])\n", "df32[\"sch_date\"] = pd.to_datetime(df32[\"sch_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pd.merge(\n", "    finalArray,\n", "    df2,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"date\"],\n", "    right_on=[\"outlet_id\", \"transactiondate\"],\n", ")\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final.merge(\n", "    df31,\n", "    how=\"outer\",\n", "    left_on=[\"outlet_id\", \"date\"],\n", "    right_on=[\"outlet_id\", \"sch_date\"],\n", ")\n", "final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.drop(\n", "    [\n", "        \"order_count\",\n", "        \"procured_sku_count\",\n", "        \"procured_item_count\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.rename(\n", "    columns={\n", "        \"inctax\": \"billed_gmv\",\n", "    },\n", "    inplace=True,\n", ")\n", "final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df32.drop(\n", "    [\"delivered_gmv\", \"procured_sku_count\", \"procured_item_count\"], axis=1, inplace=True\n", ")\n", "df32.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table = pd.pivot_table(\n", "    df32,\n", "    index=[\"sch_date\", \"outlet_id\"],\n", "    columns=[\"fe_category\"],\n", "    values=\"order_count\",\n", "    aggfunc=np.sum,\n", ")\n", "table = table.reset_index()\n", "table = table.fillna(0)\n", "table.rename(\n", "    columns={\n", "        \"3PL\": \"DeliveredOrders_3PL\",\n", "        \"FE\": \"DeliveredOrders_FE\",\n", "        \"GSP\": \"DeliveredOrders_GSP\",\n", "        \"CPO\": \"DeliveredOrders_CPO\",\n", "        \"GEP\": \"DeliveredOrders_GEP\",\n", "    },\n", "    inplace=True,\n", ")\n", "table.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names_must = [\n", "    \"sch_date\",\n", "    \"outlet_id\",\n", "    \"DeliveredOrders_3PL\",\n", "    \"DeliveredOrders_CPO\",\n", "    \"DeliveredOrders_FE\",\n", "    \"DeliveredOrders_GSP\",\n", "    \"DeliveredOrders_GEP\",\n", "]\n", "\n", "column_names_present = table.columns\n", "\n", "for column_names in column_names_must:\n", "    if column_names not in table.columns:\n", "        table[column_names] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table[\"DeliveredOrders_3PL_CPO\"] = (\n", "    table[\"DeliveredOrders_3PL\"] + table[\"DeliveredOrders_CPO\"]\n", ")\n", "table[\"DeliveredOrders_GSP_GEP\"] = (\n", "    table[\"DeliveredOrders_GSP\"] + table[\"DeliveredOrders_GEP\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table.drop(\n", "    [\n", "        \"DeliveredOrders_3PL\",\n", "        \"DeliveredOrders_CPO\",\n", "        \"DeliveredOrders_GSP\",\n", "        \"DeliveredOrders_GEP\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table.rename(\n", "    columns={\n", "        \"DeliveredOrders_3PL_CPO\": \"DeliveredOrders_3PL\",\n", "        \"DeliveredOrders_GSP_GEP\": \"DeliveredOrders_GSP\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final1.merge(table, how=\"outer\", on=[\"outlet_id\", \"sch_date\"])\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "sd = datetime.datetime(year, month, 1)\n", "start_date = sd.date()\n", "\n", "ed = datetime.datetime(year, month, day)\n", "end_date = ed.date()\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mask = (final[\"date\"] >= start_date) & (final[\"date\"] <= end_date)\n", "final = final.loc[mask]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.drop([\"sch_date\", \"transactiondate\"], axis=1, inplace=True)\n", "final.rename(columns={\"date\": \"sch_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"outlet_id\"] = final[\"outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"outlet_id\",\n", "    \"billed_gmv\",\n", "    \"sch_date\",\n", "    \"delivered_gmv\",\n", "    \"DeliveredOrders_FE\",\n", "    \"DeliveredOrders_GSP\",\n", "    \"DeliveredOrders_3PL\",\n", "]\n", "\n", "final = final.reindex(columns=column_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"billed_gmv\", \"type\": \"float\", \"description\": \"Total Billed GMV\"},\n", "    {\n", "        \"name\": \"sch_date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Date when the activity happened\",\n", "    },\n", "    {\"name\": \"delivered_gmv\", \"type\": \"float\", \"description\": \"Total Delivered GMV\"},\n", "    {\n", "        \"name\": \"DeliveredOrders_FE\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total orders delivered by FEs\",\n", "    },\n", "    {\n", "        \"name\": \"DeliveredOrders_GSP\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total orders delivered by GSPs\",\n", "    },\n", "    {\n", "        \"name\": \"DeliveredOrders_3PL\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total orders delivered by 3PLs\",\n", "    },\n", "]\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"e3_orders_split\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"sch_date\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"outlet_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table gives GMV and delivered orders across 3PL, FE and GSP\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}