{"cells": [{"cell_type": "code", "execution_count": null, "id": "6892a6af-14c7-4147-ad1d-12a268a905a2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import pytz\n", "from datetime import timedelta, date, datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "eb51377f-963b-4fc7-be0c-77b72b7a59c7", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "ist = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "markdown", "id": "f946e76b-9d83-4c0a-8395-f47bfab35602", "metadata": {}, "source": ["### SHIFT LOGIC"]}, {"cell_type": "code", "execution_count": null, "id": "1831cfd7-c18c-4a8e-88d3-5bfb54e9d72e", "metadata": {}, "outputs": [], "source": ["for i in range(0, 1):\n", "    today = (datetime.now(ist) - timed<PERSON>ta(days=i)).date()\n", "    tomorrow = (datetime.now(ist) - timed<PERSON>ta(days=i - 1)).date()\n", "    print(today, tomorrow)\n", "    # sheet_id = '1ORTvvI63BpN1qp1ukTBORmTpqIl00qpUZyoNLG3RXYU'\n", "    # sheet_name = 'Sheet1'\n", "    sheet_id = \"1ox_iA39SPHw0Jl4fq1Vz2t19H4urhn74TBtd6lWSZYA\"\n", "    sheet_name = \"shift_details\"\n", "\n", "    shift = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "    shift = shift.iloc[1:]\n", "    shift.columns = [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"shift_start_time\",\n", "        \"morning_per_days_load\",\n", "        \"shift_end_time\",\n", "        \"evening_per_days_load\",\n", "    ]\n", "\n", "    shift2 = shift.copy()\n", "    shift2[\"start_date\"] = str(today)\n", "    shift2[\"end_date\"] = str(tomorrow)\n", "    shift2[\"start_datetime\"] = pd.to_datetime(\n", "        shift2[\"start_date\"] + shift2[\"shift_end_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "    )\n", "    shift2[\"end_datetime\"] = pd.to_datetime(\n", "        shift2[\"end_date\"] + shift2[\"shift_start_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "    )\n", "    shift2[\"shift\"] = 2\n", "    shift2 = shift2[\n", "        [\n", "            \"facility_id\",\n", "            \"shift\",\n", "            \"evening_per_days_load\",\n", "            \"start_datetime\",\n", "            \"end_datetime\",\n", "        ]\n", "    ]\n", "\n", "    shift1 = shift.copy()\n", "    shift1[\"date\"] = str(today)\n", "    shift1[\"start_datetime\"] = pd.to_datetime(\n", "        shift1[\"date\"] + shift1[\"shift_start_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "    )\n", "    shift1[\"end_datetime\"] = pd.to_datetime(\n", "        shift1[\"date\"] + shift1[\"shift_end_time\"], format=\"%Y-%m-%d%I:%M %p\"\n", "    )\n", "    shift1[\"shift\"] = 1\n", "    shift1 = shift1[\n", "        [\n", "            \"facility_id\",\n", "            \"shift\",\n", "            \"morning_per_days_load\",\n", "            \"start_datetime\",\n", "            \"end_datetime\",\n", "        ]\n", "    ]\n", "\n", "    shift = pd.concat([shift1, shift2], axis=0)\n", "\n", "    shift.head(2)\n", "\n", "    shift[\"per_load\"] = np.where(\n", "        shift[\"shift\"] == 2,\n", "        shift[\"evening_per_days_load\"],\n", "        shift[\"morning_per_days_load\"],\n", "    )\n", "\n", "    daybefyes = shift.copy()\n", "    daybefyes[\"start_datetime\"] = daybefyes[\"start_datetime\"] - timed<PERSON>ta(days=2)\n", "    daybefyes[\"end_datetime\"] = daybefyes[\"end_datetime\"] - timed<PERSON>ta(days=2)\n", "\n", "    yesterday = shift.copy()\n", "    yesterday[\"start_datetime\"] = yesterday[\"start_datetime\"] - <PERSON><PERSON><PERSON>(days=1)\n", "    yesterday[\"end_datetime\"] = yesterday[\"end_datetime\"] - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "    shift = pd.concat([shift, yesterday, daybefyes], axis=0).drop_duplicates()\n", "    shift[\"facility_id\"] = shift[\"facility_id\"].astype(int)\n", "    shift[\"shift_date\"] = shift[\"start_datetime\"].dt.normalize()\n", "\n", "    attr = pd.read_sql_query(\n", "        f\"\"\"\n", "    SELECT *\n", "    FROM lake_view_ars.backend_facility_transfer_attributes\n", "    WHERE ((from_date <= cast(cast('{today}' AS date) - interval '2' DAY AS date)\n", "            AND to_date >= cast(cast('{today}' AS date) - interval '2' DAY AS date))\n", "           OR (from_date <= cast('{today}' AS date)\n", "               AND to_date >= cast('{today}' AS date))\n", "           OR (from_date > cast(cast('{today}' AS date) - interval '2' DAY AS date)\n", "               AND to_date < cast('{today}' AS date)))\n", "      AND lake_active_record = TRUE\n", "      AND id != 671\n", "    \"\"\",\n", "        presto,\n", "    )\n", "\n", "    attr[\"from_date\"] = pd.to_datetime(attr[\"from_date\"])\n", "    attr[\"to_date\"] = pd.to_datetime(attr[\"to_date\"])\n", "\n", "    planned_cap = pd.DataFrame()\n", "    for index, row in attr.iterrows():\n", "        start = max(row[\"from_date\"].date(), (today - <PERSON><PERSON><PERSON>(2)))\n", "        end = min(row[\"to_date\"].date(), today)\n", "        period = pd.date_range(start=start, end=end)\n", "        bar = period.to_frame(index=False)\n", "        bar[\"facility_id\"] = row[\"backend_facility_id\"]\n", "        bar[\"planned_capacity_quantity\"] = row[\"picking_capacity_quantity\"]\n", "        planned_cap = pd.concat([planned_cap, bar], axis=0)\n", "\n", "    planned_cap = planned_cap.rename(columns={0: \"shift_date\"})\n", "\n", "    shift = shift.merge(planned_cap, on=[\"facility_id\", \"shift_date\"], how=\"left\")\n", "\n", "    shift[\"per_load\"] = shift[\"per_load\"].str.replace(\"%\", \"\").astype(int)\n", "    shift[\"planned_capacity_quantity\"] = (\n", "        shift[\"planned_capacity_quantity\"] * shift[\"per_load\"]\n", "    ) / 100\n", "\n", "    shift = shift[\n", "        [\n", "            \"facility_id\",\n", "            \"shift_date\",\n", "            \"shift\",\n", "            \"start_datetime\",\n", "            \"end_datetime\",\n", "            \"per_load\",\n", "            \"planned_capacity_quantity\",\n", "        ]\n", "    ]\n", "\n", "    shift = shift.dropna()\n", "\n", "    shift[\"planned_capacity_quantity\"] = shift[\"planned_capacity_quantity\"].astype(int)\n", "\n", "    shift = (\n", "        shift.groupby(\n", "            by=[\n", "                \"facility_id\",\n", "                \"shift_date\",\n", "                \"shift\",\n", "                \"start_datetime\",\n", "                \"end_datetime\",\n", "                \"per_load\",\n", "            ]\n", "        )[\"planned_capacity_quantity\"]\n", "        .max()\n", "        .reset_index()\n", "    )\n", "\n", "    shift = shift.drop_duplicates()\n", "\n", "    assert not shift.duplicated([\"facility_id\", \"shift_date\", \"shift\"]).any()\n", "\n", "    shift.columns\n", "\n", "    shift1 = (\n", "        shift.groupby([\"facility_id\", \"shift_date\"])\n", "        .planned_capacity_quantity.sum()\n", "        .reset_index()\n", "    )\n", "\n", "    shift_final = shift[\n", "        [\n", "            \"facility_id\",\n", "            \"shift_date\",\n", "            \"shift\",\n", "            \"start_datetime\",\n", "            \"end_datetime\",\n", "            \"per_load\",\n", "        ]\n", "    ].merge(shift1, on=[\"facility_id\", \"shift_date\"])\n", "\n", "    shift_final.head(2)\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "        {\"name\": \"shift_date\", \"type\": \"date\", \"description\": \"shift_date\"},\n", "        {\"name\": \"shift\", \"type\": \"int\", \"description\": \"shift\"},\n", "        {\"name\": \"start_datetime\", \"type\": \"datetime\", \"description\": \"start_datetime\"},\n", "        {\"name\": \"end_datetime\", \"type\": \"datetime\", \"description\": \"end_datetime\"},\n", "        {\"name\": \"per_load\", \"type\": \"int\", \"description\": \"per_load\"},\n", "        {\n", "            \"name\": \"planned_capacity_quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"planned_capacity_quantity\",\n", "        },\n", "    ]\n", "    # if i == 0:\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"productivity_dashboard_shift_details\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"shift_date\", \"shift\"],\n", "        \"sortkey\": [\"shift_date\"],\n", "        \"incremental_key\": \"shift_date\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores facility shift level details for productivity dashboard\",  # Description of the table being sent to redshift\n", "    }\n", "    pb.to_redshift(shift_final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9a6d94b3-2f3b-441c-985e-3f8c6ab1639e", "metadata": {}, "outputs": [], "source": ["print(\"<PERSON><PERSON> ran succesfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "939672e3-4ca4-4f01-8b9f-afcce42377a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}