{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import time\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import datetime\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1SxqDhtAeqEh7FRMxEYBGsBvRNMoSrnGfNIbxS3m8eMM\"\n", "shift_base = pb.from_sheets(sheet_id, \"Sheet1\", clear_cache=True)\n", "shift_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shift_base[\"shift_start_time\"] = pd.to_datetime(\n", "    shift_base[\"shift_start_time\"], format=\"%H:%M\"\n", ").dt.time\n", "shift_base[\"shift_end_time\"] = pd.to_datetime(\n", "    shift_base[\"shift_end_time\"], format=\"%H:%M\"\n", ").dt.time\n", "shift_base[\"delta\"] = np.where(\n", "    shift_base[\"shift_start_time\"] > shift_base[\"shift_end_time\"], 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shift_base.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def daterange(date1, date2):\n", "    for n in range(int((date2 - date1).days) + 1):\n", "        yield date1 + datetime.timedelta(n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datelist = []\n", "# start_dt = date(2021, 6, 9)\n", "# end_dt = date(2021, 6, 13)\n", "# Last_Date =  date(2020, 11, 2)\n", "Start_Date = datetime.datetime.today() - datetime.timedelta(days=1)\n", "Last_Date = datetime.datetime.today() - datetime.timedelta(days=0)\n", "for dt in daterange(Start_Date, Last_Date):\n", "    datelist.append(dt.strftime(\"%Y-%m-%d\"))\n", "datelist"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for date in datelist:\n", "    cur_date = pd.to_datetime(date, format=\"%Y-%m-%d\").date()\n", "    cur_date_cpy = cur_date.strftime(\"%Y-%m-%d\")\n", "    nxd = (cur_date + datetime.timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "    prd = (cur_date + datetime.timedelta(days=-1)).strftime(\"%Y-%m-%d\")\n", "    print(cur_date, cur_date_cpy, nxd, prd)\n", "    new_shift = shift_base[0:0]\n", "    new_shift[\"operation_date\"] = \"\"\n", "    for index, row in shift_base.iterrows():\n", "        copy_row = row\n", "        if row[\"delta\"] == 0:\n", "            row[\"shift_start_time\"] = pd.to_datetime(\n", "                cur_date_cpy + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            row[\"shift_end_time\"] = pd.to_datetime(\n", "                cur_date_cpy + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            row[\"operation_date\"] = cur_date\n", "            new_shift = new_shift.append(row, ignore_index=True)\n", "        else:\n", "            copy_row[\"shift_start_time\"] = pd.to_datetime(\n", "                prd + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            copy_row[\"shift_end_time\"] = pd.to_datetime(\n", "                cur_date_cpy + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            copy_row[\"operation_date\"] = cur_date + datetime.timedelta(days=-1)\n", "            new_shift = new_shift.append(copy_row, ignore_index=True)\n", "            row[\"shift_start_time\"] = pd.to_datetime(\n", "                cur_date_cpy + \" \" + row[\"shift_start_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            row[\"shift_end_time\"] = pd.to_datetime(\n", "                nxd + \" \" + row[\"shift_end_time\"].strftime(\"%H:%M:%S\")\n", "            )\n", "            row[\"operation_date\"] = cur_date\n", "            new_shift = new_shift.append(row, ignore_index=True)\n", "    new_shift[\"facility_id\"] = new_shift[\"facility_id\"].astype(str).astype(int)\n", "    query = f\"\"\"Select log.outlet_id,co.facility_id,grofers_order_id,variant_id,log.inventory_update_id,\"delta\" as quantity,Convert_timezone('Asia/Kolkata',log.pos_timestamp) as billing_time,\n", "    log.created_by_name as biller_id\n", "    FROM lake_ims.ims_inventory_log log\n", "    INNER JOIN lake_retail.console_outlet co ON co.id=log.outlet_id\n", "    WHERE date(Convert_timezone('Asia/Kolkata',log.pos_timestamp)) = '{date}'\n", "    AND inventory_update_type_id=50;\"\"\"\n", "    base = pd.read_sql(query, redshift)\n", "\n", "    merged = base.merge(\n", "        new_shift[\n", "            [\n", "                \"facility_id\",\n", "                \"shift_num\",\n", "                \"shift_start_time\",\n", "                \"shift_end_time\",\n", "                \"operation_date\",\n", "            ]\n", "        ],\n", "        how=\"inner\",\n", "        on=\"facility_id\",\n", "    )\n", "\n", "    merged[\"valid\"] = np.where(\n", "        (merged[\"shift_start_time\"] <= merged[\"billing_time\"])\n", "        & (merged[\"billing_time\"] < merged[\"shift_end_time\"]),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    merged_final = merged[merged[\"valid\"] == 1]\n", "\n", "    merged_final = merged_final.drop(columns={\"valid\"})\n", "    merged_final[\"etl_timestamp_utc\"] = datetime.datetime.now()\n", "    merged_final[\"operation_date\"] = pd.to_datetime(\n", "        merged_final[\"operation_date\"], format=\"%Y-%m-%d\"\n", "    )\n", "\n", "    merged_final[[\"facility_id\", \"outlet_id\", \"quantity\"]] = merged_final[\n", "        [\"facility_id\", \"outlet_id\", \"quantity\"]\n", "    ].astype(\"int\")\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "        {\n", "            \"name\": \"grofers_order_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"grofers_order_id\",\n", "        },\n", "        {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "        {\n", "            \"name\": \"inventory_update_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"inventory_update_id\",\n", "        },\n", "        {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"picked quantity\"},\n", "        {\"name\": \"billing_time\", \"type\": \"datetime\", \"description\": \"picking time\"},\n", "        {\"name\": \"biller_id\", \"type\": \"varchar\", \"description\": \"picker_Id\"},\n", "        {\"name\": \"shift_num\", \"type\": \"varchar\", \"description\": \"Picker_Name\"},\n", "        {\n", "            \"name\": \"shift_start_time\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"picking_completed_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"shift_end_time\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"picking_completed_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"operation_date\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"picking_completed_at_ist\",\n", "        },\n", "        {\n", "            \"name\": \"etl_timestamp_utc\",\n", "            \"type\": \"datetime\",\n", "            \"description\": \"etl_timestamp_utc\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"biller_item_shift_mapping\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"inventory_update_id\"],\n", "        \"sortkey\": [\"billing_time\", \"outlet_id\"],\n", "        \"incremental_key\": \"etl_timestamp_utc\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"This tables maps every inventory update corresponding to B2C billing to the biller and the picking shift\",\n", "    }\n", "    pb.to_redshift(merged_final, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}