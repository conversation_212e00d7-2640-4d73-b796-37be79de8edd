{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime\n", "from datetime import datetime, date\n", "import calendar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month_mapping = {\n", "    \"Month_Name\": [\n", "        \"Jan\",\n", "        \"Feb\",\n", "        \"Mar\",\n", "        \"Apr\",\n", "        \"May\",\n", "        \"Jun\",\n", "        \"Jul\",\n", "        \"Aug\",\n", "        \"Sep\",\n", "        \"Oct\",\n", "        \"Nov\",\n", "        \"Dec\",\n", "    ],\n", "    \"Month_Number\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],\n", "}\n", "map_month = pd.DataFrame(month_mapping)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Month_31 = [1, 3, 5, 7, 8, 10, 12]\n", "Month_30 = [4, 6, 9, 11]\n", "Month_28 = [2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LM Components"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### LM Rates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct outlet_id,\n", "        city,\n", "        \"store type\",\n", "        sch_date,\n", "        sum(deliveredorders_3pl) as deliveredorders_3pl,\n", "        sum(deliveredorders_fe) as deliveredorders_fe,\n", "        sum(deliveredorders_gsp) as deliveredorders_gsp \n", "from metrics.e3_orders_split os\n", "join metrics.e3_cost c\n", "on os.outlet_id = c.outletid\n", "and os.sch_date = c.datenew\n", "where EXTRACT(month FROM sch_date) = EXTRACT(month FROM current_date-1)\n", "and sch_date is not null\n", "group by 1,2,3,4\n", "order by 1,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, con=redshift)\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"sch_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "sheet_name = \"LM rates \"\n", "LM_rates = pb.from_sheets(sheet_id, sheet_name)\n", "LM_rates.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = LM_rates.iloc[1]\n", "LM_rates = LM_rates[2:]\n", "LM_rates.columns = new_header"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_rates.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_rates.columns\n", "LM_rates.drop([\"\", \"\", \"\"], axis=1, inplace=True)\n", "LM_rates.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = data.merge(LM_rates, how=\"inner\", left_on=[\"city\"], right_on=[\"Location\"])\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[[\"GSP_PPC\", \"3PL_PPC\"]] = temp1[[\"GSP_PPC\", \"3PL_PPC\"]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[\"LM_GSP_Cost\"] = temp1[\"deliveredorders_gsp\"] * temp1[\"GSP_PPC\"]\n", "temp1[\"LM_3PL_Cost\"] = temp1[\"deliveredorders_3pl\"] * temp1[\"3PL_PPC\"]\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.drop([\"Location\"], axis=1, inplace=True)\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[\"sch_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# temp1.shape, temp1.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Offroll Avg PPC"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"Offroll - Avg PPC \"\n", "OffrollPPC = pb.from_sheets(sheet_id, sheet_name)\n", "OffrollPPC.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = OffrollPPC.iloc[1]\n", "OffrollPPC = OffrollPPC[2:]\n", "OffrollPPC.columns = new_header\n", "OffrollPPC.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OffrollPPC.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OffrollPPC.drop([\"\", \"\", \"\"], axis=1, inplace=True)\n", "OffrollPPC.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OffrollPPC[[\"Store Ops_Avg PPC\", \"FE_Avg PPC\"]] = OffrollPPC[\n", "    [\"Store Ops_Avg PPC\", \"FE_Avg PPC\"]\n", "].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OffrollPPC[\"Store Ops_Avg PPC\"] = OffrollPPC[\"Store Ops_Avg PPC\"] / 30\n", "OffrollPPC[\"FE_Avg PPC\"] = OffrollPPC[\"FE_Avg PPC\"] / 30"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct outlet_id,\n", "        city,\n", "        \"store type\",\n", "        datenew,\n", "        sum(\"fe total\") as FE_HC,\n", "        (sum(\"assistant (putter/loader) total\") + sum(\"biller total\") + sum(\"executive - store operations total\") + \n", "            sum(\"executive admin total\") + sum(\"picker total\") + sum(\"station manager total\") + sum(\"team leader total\")) as StoreOps_HC\n", "from metrics.e3_orders_split os\n", "join metrics.e3_cost c\n", "on os.outlet_id = c.outletid\n", "and os.sch_date = c.datenew\n", "where EXTRACT(month FROM datenew) = EXTRACT(month FROM current_date-1)\n", "and sch_date is not null\n", "group by 1,2,3,4\n", "order by 1,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, con=redshift)\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"datenew\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = data.merge(OffrollPPC, how=\"inner\", left_on=[\"city\"], right_on=[\"Location\"])\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.dtypes, temp2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[[\"outlet_id\", \"fe_hc\", \"Store Ops_Avg PPC\", \"FE_Avg PPC\"]] = temp2[\n", "    [\"outlet_id\", \"fe_hc\", \"Store Ops_Avg PPC\", \"FE_Avg PPC\"]\n", "].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"LM_FE_Cost\"] = temp2[\"fe_hc\"] * temp2[\"FE_Avg PPC\"]\n", "temp2[\"E3_StoreOps_Cost\"] = temp2[\"storeops_hc\"] * temp2[\"Store Ops_Avg PPC\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.drop([\"Location\"], axis=1, inplace=True)\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"datenew\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# temp2.shape, temp2.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merge1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp3 = temp1.merge(\n", "    temp2,\n", "    how=\"outer\",\n", "    left_on=[\"outlet_id\", \"city\", \"store type\", \"sch_date\"],\n", "    right_on=[\"outlet_id\", \"city\", \"store type\", \"datenew\"],\n", ")\n", "temp3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp3.drop([\"datenew\"], axis=1, inplace=True)\n", "temp3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp3[\"sch_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# temp3.shape, temp3.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Onroll PPC & HC"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"Onroll - PPC and HC\"\n", "OnrollPPC = pb.from_sheets(sheet_id, sheet_name)\n", "OnrollPPC.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = OnrollPPC.iloc[1]\n", "OnrollPPC = OnrollPPC[2:]\n", "OnrollPPC.columns = new_header\n", "OnrollPPC.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPPC.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPPC.drop([\"\", \"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPPC.rename(\n", "    columns={\n", "        \"Per person \\n monthly cost\": \"PPC\",\n", "        \"Common Onroll HC \\n (Not dedicated to any store)\": \"HC\",\n", "    },\n", "    inplace=True,\n", ")\n", "OnrollPPC.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPPC.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPPC[[\"PPC\", \"HC\"]] = OnrollPPC[[\"PPC\", \"HC\"]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")\n", "\n", "OnrollPPC[\"LM_Onroll_Cost\"] = OnrollPPC[\"PPC\"] * OnrollPPC[\"HC\"]\n", "OnrollPPC.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merge Final LM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4 = temp3.merge(OnrollPPC, how=\"outer\", left_on=[\"city\"], right_on=[\"Location\"])\n", "temp4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4.drop([\"Location\"], axis=1, inplace=True)\n", "temp4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4[\"sch_date\"] = pd.to_datetime(temp4[\"sch_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4[\"sch_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4.drop(\n", "    [\n", "        \"deliveredorders_3pl\",\n", "        \"deliveredorders_fe\",\n", "        \"deliveredorders_gsp\",\n", "        \"GSP_PPC\",\n", "        \"3PL_PPC\",\n", "        \"fe_hc\",\n", "        \"Store Ops_Avg PPC\",\n", "        \"FE_Avg PPC\",\n", "        \"PPC\",\n", "        \"HC\",\n", "        \"storeops_hc\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4.columns, temp4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4 = temp4[temp4[\"sch_date\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4 = temp4.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"city\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"City\",\n", "    },\n", "    {\n", "        \"name\": \"store type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Type of outlet\",\n", "    },\n", "    {\n", "        \"name\": \"sch_date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Date\",\n", "    },\n", "    {\n", "        \"name\": \"LM_GSP_Cost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"GSP cost (LM)\",\n", "    },\n", "    {\n", "        \"name\": \"LM_3PL_Cost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"3PL cost (LM)\",\n", "    },\n", "    {\n", "        \"name\": \"LM_FE_Cost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"FE cost (LM)\",\n", "    },\n", "    {\n", "        \"name\": \"E3_StoreOps_Cost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Store Ops cost (E3)\",\n", "    },\n", "    {\n", "        \"name\": \"LM_Onroll_Cost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Onroll cost (LM)\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"e3_LMcost\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"sch_date\"],\n", "    \"sortkey\": [\"sch_date\"],\n", "    \"incremental_key\": \"outlet_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Maps pending putaway for facilities where multi replenishment happens\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_redshift(temp4, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### E3 - Onroll Direct"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"E3- Onroll direct\"\n", "OnrollDirect = pb.from_sheets(sheet_id, sheet_name)\n", "OnrollDirect.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = OnrollDirect.iloc[1]\n", "OnrollDirect = OnrollDirect[2:]\n", "OnrollDirect.columns = new_header\n", "OnrollDirect.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.drop(\n", "    [\n", "        \"\",\n", "        \"\",\n", "        \"\",\n", "        \"\",\n", "        \"\",\n", "        \"\",\n", "        \"\",\n", "        \"Direct Onroll - HC\",\n", "        \"Direct cost\",\n", "        \"allocated cost\",\n", "        \"Store Name\",\n", "        \"City\",\n", "        \"Store Type\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect[\"Month\"] = OnrollDirect[\"Month\"].astype(\"str\")\n", "OnrollDirect.dtypes\n", "\n", "new = OnrollDirect[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "OnrollDirect[\"MonthName\"] = new[0]\n", "OnrollDirect[\"Year\"] = new[1]\n", "\n", "OnrollDirect.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect = OnrollDirect.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "OnrollDirect.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "OnrollDirect[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.head(2), OnrollDirect.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect[\"Outlet ID\"] = OnrollDirect[\"Outlet ID\"].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")\n", "outlet_list = OnrollDirect[\"Outlet ID\"].unique()\n", "outlet_list = outlet_list.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta, date\n", "import calendar\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "num_days = calendar.monthrange(year, month)[1]\n", "days = [date(year, month, day) for day in range(1, num_days + 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray = []\n", "for i in range(0, len(outlet_list)):\n", "    for j in range(0, len(days)):\n", "        tempArray = [outlet_list[i], days[j]]\n", "        finalArray.append(tempArray[:])\n", "finalArray = pd.<PERSON><PERSON><PERSON><PERSON>(finalArray)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray.columns = [\"outlet_id\", \"date\"]\n", "finalArray.dropna(subset=[\"outlet_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray[\"date\"] = pd.to_datetime(finalArray[\"date\"])\n", "finalArray[\"month\"] = finalArray[\"date\"].dt.month\n", "finalArray[\"outlet_id\"] = finalArray[\"outlet_id\"].apply(pd.to_numeric, errors=\"coerce\")\n", "finalArray.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollDirect.dtypes, finalArray.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp = OnrollDirect.merge(\n", "    finalArray,\n", "    how=\"inner\",\n", "    left_on=[\"Outlet ID\", \"Month_Number\"],\n", "    right_on=[\"outlet_id\", \"month\"],\n", ")\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.drop([\"Outlet ID\", \"month\"], axis=1, inplace=True)\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp[\"Total - onroll cost\"] = temp[\"Total - onroll cost\"].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if temp[\"Month_Number\"].isin(Month_30).any():\n", "    temp[\"Daily_OnrollCost\"] = temp[\"Total - onroll cost\"] / 30\n", "elif temp[\"Month_Number\"].isin(Month_28).any():\n", "    temp[\"Daily_OnrollCost\"] = temp[\"Total - onroll cost\"] / 28\n", "else:\n", "    temp[\"Daily_OnrollCost\"] = temp[\"Total - onroll cost\"] / 31\n", "\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.drop([\"Total - onroll cost\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollCost = temp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MG orders - 3PL FC"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"3PL- FC \"\n", "FC_3PL = pb.from_sheets(sheet_id, sheet_name)\n", "FC_3PL.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = FC_3PL.iloc[1]\n", "FC_3PL = FC_3PL[2:]\n", "FC_3PL.columns = new_header\n", "FC_3PL.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL.drop(\n", "    [\"\", \"\", \"\", \"\", \"\", \"Store Name\", \"City\", \"Store Type\", \"\", \"\"],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### E3 - Merchant MG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"E3 merchant- MG\"\n", "MG = pb.from_sheets(sheet_id, sheet_name)\n", "MG.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = MG.iloc[1]\n", "MG = MG[2:]\n", "MG.columns = new_header\n", "MG.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG.drop([\"\", \"\", \"Store Name\", \"City\", \"\", \"\", \"\", \"\", \"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG[\"Picking included\"] = MG[\"Picking included\"].fillna(\"No\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG[\"Month\"] = MG[\"Month\"].astype(\"str\")\n", "MG.dtypes\n", "\n", "new = MG[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "MG[\"MonthName\"] = new[0]\n", "MG[\"Year\"] = new[1]\n", "\n", "MG.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG = MG.merge(map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"])\n", "MG.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "MG[\"year\"] = 2021\n", "MG.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Forecasted GMV and Orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "--------Existing  Dark Store Order Dump-----\n", "\n", "with fc_mapping as \n", "(\n", "    select distinct\n", "        pco.id as outlet_id,\n", "        pco.name as outlet_name,\n", "        pco.facility_id,\n", "        cf.name as facility_name,\n", "        bom.external_id as backend_merchant_id,\n", "        bom.name as backend_merchant_name\n", "    from lake_oms_bifrost.view_oms_merchant as bom\n", "    inner join  lake_retail.console_outlet_cms_store  as cms\n", "        on bom.external_id = cms.cms_store \n", "        and cms.active = true\n", "        and cms.cms_update_active = true\n", "        and virtual_merchant_type <> 'superstore_merchant'\n", "    inner join  lake_retail.console_outlet  as pco\n", "        on cms.outlet_id = pco.id\n", "    inner join  lake_crates.facility  as cf \n", "        on cf.id = pco.facility_id\n", "),\n", "e3_stores as \n", "(\n", "select merchant_id  from consumer.express_stores ex where ex.active=1        \n", "union\n", "select external_id from lake_logistics.logistics_node\n", "),\n", "sch as\n", "(\n", "    select distinct\n", "        city,\n", "        max(outlet_id) over(partition by frontend_merchant_id) as outlet_id,\n", "        max(outlet_name) over(partition by frontend_merchant_id) as outlet_name,\n", "        max(facility_name) over(partition by frontend_merchant_id) as facility,\n", "        frontend_merchant_id,\n", "        frontend_merchant_name,\n", "        order_id,\n", "        sch_slot_start,\n", "        sch_slot_end,\n", "        current_status,\n", "        total_cost,\n", "        order_type\n", "    from\n", "    (\n", "        select distinct\n", "            m.city_name as city,\n", "            fc.outlet_id,\n", "            fc.outlet_name,\n", "            fc.facility_name,\n", "            m.external_id as frontend_merchant_id,\n", "            m.name as frontend_merchant_name,\n", "            o.id as order_id,\n", "            os.id as suborder_id,\n", "            convert_timezone('Asia/Kolkata',(timestamp 'epoch' + o.slot_start * interval '1 second')) sch_slot_start,\n", "            convert_timezone('Asia/Kolkata',(timestamp 'epoch' + o.slot_end * interval '1 second')) sch_slot_end,\n", "            o.current_status,\n", "            o.procurement_amount as total_cost,\n", "            o.type as order_type\n", "        from lake_oms_bifrost.oms_order o\n", "        inner join lake_oms_bifrost.view_oms_merchant m \n", "            on o.merchant_id = m.id \n", "         inner join e3_stores ex on m.external_id=ex.merchant_id\n", "        inner join lake_oms_bifrost.oms_suborder os on os.order_id = o.id and os.type not ilike 'DigitalSuborder'\n", "        left join lake_oms_bifrost.view_oms_merchant bom on os.backend_merchant_id = bom.id and bom.virtual_merchant_type <> 'superstore_merchant'\n", "        left join fc_mapping fc on fc.backend_merchant_id = bom.external_id\n", "        where o.type in ('RetailForwardOrder')\n", "            and o.slot_start::int between extract(epoch from ((((current_date-31)::date)::timestamp) - interval '5.5 hour')) \n", "                        and extract(epoch from ((((current_date-1)::date)::timestamp) + interval '1 day' - interval '5.5 hour'))\n", "    )\n", ")\n", "\n", ",\n", "res as \n", "(\n", "    select distinct \n", "        city,\n", "        max(outlet_id) over(partition by frontend_merchant_id) as outlet_id,\n", "        max(outlet_name) over(partition by frontend_merchant_id) as outlet_name,\n", "        max(facility_name) over(partition by frontend_merchant_id) as facility,\n", "        frontend_merchant_id,\n", "        frontend_merchant_name,\n", "        sch_slot_start,\n", "        sch_slot_end,\n", "        order_id,\n", "        current_status,\n", "        total_cost,\n", "        reason_key,\n", "        order_type\n", "    from \n", "    (\n", "        select distinct r.*, \n", "        dense_rank() over (partition by order_id,event_ts_ist::date,ooe_slot ORDER BY event_ts_ist desc) AS ooe_rank \n", "        from\n", "        (\n", "            select distinct\n", "                m.city_name as city,\n", "                fc.outlet_id,\n", "                fc.outlet_name,\n", "                fc.facility_name,\n", "                m.external_id as frontend_merchant_id,\n", "                m.name as frontend_merchant_name,\n", "                (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'old_slot_start',true)::int* interval '1 second' + interval '5.5 hour') as sch_slot_start,\n", "                (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'old_slot_end',true)::int* interval '1 second' + interval '5.5 hour') as sch_slot_end,\n", "                (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'new_slot_start',true)::int* interval '1 second' + interval '5.5 hour') as new_slot_start,\n", "                (TIMESTAMP 'epoch' + json_extract_path_text(oe.extra,'new_slot_end',true)::int* interval '1 second' + interval '5.5 hour') as new_slot_end,\n", "                oe.order_id,\n", "                os.id as suborder_id,\n", "                (oe.install_ts  + interval '5.5 hour') as event_ts_ist,\n", "                o.current_status,\n", "                o.procurement_amount as total_cost,\n", "                oe.reason_key,\n", "                cast(json_extract_path_text(oe.extra,'old_slot_start',true) as text)||cast(json_extract_path_text(oe.extra,'old_slot_end',true)as text)||cast(json_extract_path_text(oe.extra,'new_slot_start',true)as text)||cast(json_extract_path_text(oe.extra,'new_slot_end',true)as text) as ooe_slot,\n", "                o.type as order_type\n", "            from lake_oms_bifrost.oms_order_event oe\n", "            inner join lake_oms_bifrost.oms_order o on oe.order_id = o.id\n", "            inner join lake_oms_bifrost.view_oms_merchant m on o.merchant_id = m.id and m.external_id in (select merchant_id from consumer.express_stores)\n", "            inner join lake_oms_bifrost.oms_suborder os on os.order_id = o.id and os.type not ilike 'DigitalSuborder'\n", "            left join lake_oms_bifrost.view_oms_merchant bom on os.backend_merchant_id = bom.id and bom.virtual_merchant_type <> 'superstore_merchant'\n", "            left join fc_mapping fc on fc.backend_merchant_id = bom.external_id\n", "            where o.type in ('RetailForwardOrder')\n", "                and oe.event_type_key = 'order_rescheduling'\n", "                and json_extract_path_text(oe.extra,'old_slot_start',true)::int between extract(epoch from ((((current_date-31)::date)::timestamp) - interval '5.5 hour')) \n", "                    and extract(epoch from ((((current_date-1)::date)::timestamp) + interval '1 day' - interval '5.5 hour'))\n", "        )r\n", "        where sch_slot_start::date < new_slot_start::date\n", "    )r1\n", "    where ooe_rank = 1\n", "),\n", "lm_base as\n", "(\n", "    select distinct \n", "        city, \n", "        outlet_id,\n", "        outlet_name,\n", "        facility,\n", "        frontend_merchant_id,\n", "        frontend_merchant_name, \n", "        sch_slot_start, \n", "        sch_slot_end, \n", "        order_id, \n", "        current_status,\n", "        total_cost,\n", "        'new_sch' as order_cat,\n", "        null as reason_key,\n", "        order_type\n", "        -- max_timestamp\n", "    from sch\n", "    union\n", "    select distinct \n", "        city, \n", "        outlet_id,\n", "        outlet_name,\n", "        facility,\n", "        frontend_merchant_id,\n", "        frontend_merchant_name, \n", "        sch_slot_start, \n", "        sch_slot_end, \n", "        order_id, \n", "        current_status,\n", "        total_cost,\n", "        'resch' as order_cat, \n", "        reason_key,\n", "        order_type\n", "    from res\n", "),\n", "delivered_lm as \n", "(select  external_order_id as id,\n", "(delivery_slot_start_time+ interval '330 minute') as sch_slot_start,\n", "(oa.actual_event_time + interval '330 minute') as delivered_ts,\n", "employee_id \n", "from lake_last_mile.order o\n", "Inner Join lake_last_mile.order_action oa on o.id = oa.order_id \n", "inner join lake_last_mile.field_executive fe on fe.id=field_executive_id\n", "where oa.action_type = 'DELIVERED'\n", "and (oa.actual_event_time + interval '330 minute')::date between ((current_date-31)::date) and ((current_date)::date)\n", "),\n", "delivered_logistics as \n", "(select \n", "         lsl.label_value as id,\n", "        (delivery_start  + interval '330 minute') as sch_slot_start,\n", "        (lt.update_ts  + interval '330 minute') as delivered_ts,\n", "         employee_id\n", "     from lake_logistics.logistics_shipment ls \n", "      inner join lake_logistics.logistics_shipment_label lsl on lsl.shipment_id=ls.id  and label_type='ORDER_ID' and status='DELIVERED'\n", "      left join lake_logistics.logistics_task lt on lsl.label_value = lt.shipment_label_value and lt.type = 'ORDER_DELIVERY_TASK' and state = 'COMPLETED'\n", "     inner join lake_logistics.logistics_user_profile p on p.id=lt.user_profile_id\n", "     inner join lake_logistics.logistics_user  u on u.id=p.user_id\n", "    where (delivery_start+ interval '330 minute')::date  between ((current_date-31)::date) and ((current_date-1)::date)\n", "    and (lt.update_ts+ interval '330 minute')::date  between ((current_date-31)::date) and ((current_date-1)::date)\n", "),\n", "delivered as\n", "(\n", "select * from delivered_lm\n", "union \n", "select * from delivered_logistics\n", "),\n", " pos_invoice_amt as \n", "(select os.order_id,sum(distinct actual_sales) as actual_sales from lake_oms_bifrost.oms_suborder os \n", "join  lake_pos.pos_invoice  a on os.id = a.grofers_order_id and a.invoice_type_id = 1 \n", "join  lake_pos.pos_invoice_product_details  b on a.id = b.invoice_id and name not like 'Offer%%'\n", "Where --a.invoice_type_id = 1\n", "--AND name not like 'Offer%%'\n", " current_status not in ('REPROCURED','CANCELLED')\n", "and \n", "DATE(TIMESTAMP 'epoch' + os.slot_start*INTERVAL '1 Second' +INTERVAL '5.5 hour') between ((current_date-31)::date) and ((current_date)::date)\n", "group by 1),\n", "\n", "\n", "\n", "---------SKU---------\n", "procured_sku_count as (\n", " select \n", "order_id,sum(sku) as sku,sum(quantity) as quantity ,sum(actual_sales) as actual_sales\n", "from \n", "(select\n", "distinct os.order_id,os.id,count(distinct pd.id) as sku , sum(pd.quantity) as quantity, max(actual_sales) as actual_sales \n", "from lake_oms_bifrost.oms_suborder os \n", "inner join lake_pos.pos_invoice pos on os.id=pos.grofers_order_id and invoice_type_id=1\n", "inner join lake_pos.pos_invoice_product_details pd on pos.id=pd.invoice_id\n", "where current_status not in ('REPROCURED','CANCELLED')\n", " and pd.name not ilike '%%Offer%%'\n", " and DATE(TIMESTAMP 'epoch' + os.slot_start*INTERVAL '1 Second' +INTERVAL '5.5 hour') between ((current_date-31)::date) and ((current_date)::date)\n", "group by 1,2)\n", "group by 1),\n", "\n", "\n", "order_dump as\n", "(\n", "    select distinct\n", "        b.sch_slot_start::date as sch_date,\n", "        b.city,\n", "        b.outlet_id,\n", "        b.outlet_name,\n", "        b.facility,\n", "        b.frontend_merchant_id,\n", "        b.frontend_merchant_name,\n", "        case when extract(hour from b.sch_slot_start)/10<1 then '0'||extract(hour from b.sch_slot_start)::text\n", "             else extract(hour from b.sch_slot_start)::text end\n", "        ||'-'||\n", "        case when extract(hour from b.sch_slot_end)/10<1 then '0'||extract(hour from b.sch_slot_end)::text\n", "            else extract(hour from b.sch_slot_end)::text end\n", "        as slot,\n", "        case when (extract(hour from b.sch_slot_end) - extract(hour from b.sch_slot_start)) = 1 then 'express slot'\n", "             else 'long slot' \n", "        end AS slot_type,\n", "        b.order_id,\n", "        d.id as delivered,\n", "        employee_id,\n", "        (case when employee_id like 'GSP%%' or employee_id like 'VSP%%' or employee_id like 'GEP%%' or employee_id like 'FLX%%' then 'GSP' \n", "        when employee_id like 'AMP%%' or employee_id like 'CAT%%' or employee_id like 'CON%%' or employee_id like 'CRE%%'\n", "        or employee_id like 'DEP%%' or employee_id like 'EKO%%' or employee_id like 'MIC%%' or employee_id like 'OYE%%'\n", "        or employee_id like 'PS%%' or employee_id like 'SAI%%' or employee_id like 'SHA%%' or employee_id like 'SIT%%' \n", "        or employee_id like 'TRE%%' or employee_id like 'WOW%%' or employee_id like 'TRO%%'  \n", "        or employee_id like 'JII%%'  or employee_id like 'EBI%%' or employee_id like 'CAR%%'\n", "        or employee_id like 'ASI%%'  or employee_id like 'IPC%%' or employee_id like 'BAP%%'  or employee_id like 'TEA%%' \n", "        or employee_id like 'RAJ%%' or employee_id like 'DIG%%' or employee_id like 'TET%%' or employee_id like 'VSY%%' \n", "        or employee_id like 'BAT%%' or employee_id like 'MOO%%' or employee_id like 'PS%%' or employee_id like 'ZYN%%' \n", "        or employee_id like 'ASI%%'  or employee_id like 'GAY%%' or employee_id like 'MAT%%' or employee_id like 'AIM%%'\n", "        or employee_id like 'AVA%%' or employee_id like 'RAP%%' or employee_id like 'GRO%%' or employee_id like 'ZYP%%'\n", "        or employee_id like 'THE%%' or employee_id like 'LOG%%' or employee_id like 'ZOMATOKA%%' or employee_id like 'EXA%%'\n", "        or employee_id like 'MER%%' or employee_id like 'DOT%%' or employee_id like 'VXP%%' or employee_id like 'ORI%%'\n", "        or employee_id like 'ZOMATOLKO%%' or employee_id like 'TUS%%' or employee_id like 'ETO%%' or employee_id like 'SAN%%'\n", "        or employee_id like 'SNP%%' or employee_id like 'ZOM%%' \n", "        then '3PL' else 'FE' end)as fe_category,\n", "        case when b.order_cat = 'resch' then 1 end as rescheduled,\n", "        case when b.current_status = 'CANCELLED' then 1 end as cancelled,\n", "        case when b.order_cat = 'resch' and \n", "                            b.reason_key in ('RESCHEDULE_DELIVERY_DELAY_IN_DELIVERY',\n", "                                            'RESCHEDULE_DELIVERY_WEATHER_CONDITIONS',\n", "                                            'RESCHEDULE_MERCHANT_MISSED_BY_AC',\n", "                                            'RESCHEDULE_DELIVERY_OTHERS',\n", "                                            'RESCHEDULE_MERCHANT_MISSED_BY_LM',\n", "                                            'RESCHEDULE_CUSTOMER_PARTIAL_DELIVERY',\n", "                                            'RESCHEDULE_DELIVERY_GROUND_ISSUES/FE_SHORTAGES',\n", "                                            'RESCHEDULE_CUSTOMER_ADDRESS_ISSUE'\n", "                                            ) then 1 end as LM_reschedule,\n", "        case when b.order_cat = 'resch' and\n", "                            b.reason_key in ('RESCHEDULE_CRM DRIVEN_EXCEPTIONAL_-_NO_SMS',\n", "                                            'RESCHEDULE_CRM DRIVEN_OTHERS',\n", "                                            'RESCHEDULE_CRM DRIVEN_TECH_ISSUE',\n", "                                            'RESCHEDULE_CRM DRIVEN_LATE_APPROVAL_BY_CRM',\n", "                                            'RESCHEDULE_CRM DRIVEN_PRE_ORDER'\n", "                                            ) then 1 end as CRM_reschedule,\n", "        sku.sku,\n", "        sku.actual_sales,\n", "        sku.quantity,\n", "        b.order_type\n", "        -- pic.procured_item_count as \n", "    from lm_base b\n", "   \n", "    left join delivered d on d.id = b.order_id and b.sch_slot_start::date = d.sch_slot_start::date\n", "    left join procured_sku_count sku on sku.order_id=b.order_id\n", "    \n", "    order by 1 desc,2,3,4,5\n", ")\n", "\n", "select sch_date,\n", "        case \n", "           when outlet_id in (1445,1637,1638) then 1445\n", "           when outlet_id in (1405,1675,1676) then 1676\n", "           when outlet_id in (1449,1699,1700) then 1700\n", "           else outlet_id end as outlet_id,\n", "        fe_category,\n", "        count(distinct order_id) as order_count,\n", "        sum(actual_sales) as delivered_gmv,\n", "        sum(sku) as procured_sku_count,\n", "        sum(quantity) as procured_item_count\n", "from order_dump  \n", "where cancelled is null \n", "and delivered is not null\n", "group by 1,2,3\n", "order by 2,1\n", "\n", "----query ends---\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(query, con=redshift)\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"sch_date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.groupby([\"sch_date\", \"outlet_id\"])[\"order_count\", \"delivered_gmv\"].agg(\n", "    \"sum\"\n", ")\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.reset_index()\n", "data[\"sch_date\"] = pd.to_datetime(data[\"sch_date\"])\n", "data[\"month\"] = data[\"sch_date\"].dt.month\n", "data.rename(columns={\"sch_date\": \"date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.dtypes, data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta, date\n", "import calendar\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "year = dt.year\n", "currentMonth = dt.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_CM = data[data[\"month\"] == currentMonth]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_CM[\"outlet_id\"] = data_CM[\"outlet_id\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select outlet_id,\n", "        co.name as outlet_name\n", "from\n", "    (\n", "    select distinct case \n", "            when id in (1445,1637,1638) then 1445\n", "            when id in (1405,1675,1676) then 1676\n", "            when id in (1449,1699,1700) then 1700\n", "            else id end as outlet_id\n", "    from lake_retail.console_outlet\n", "    where business_type_id = 7\n", "    ) a\n", "join lake_retail.console_outlet co\n", "on a.outlet_id = co.id\n", "\"\"\"\n", "outlet_list = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list1 = outlet_list.values.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta, date\n", "import calendar\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "num_days = calendar.monthrange(year, month)[1]\n", "days = [date(year, month, day) for day in range(1, num_days + 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray = []\n", "for i in range(0, len(list1)):\n", "    for j in range(0, len(days)):\n", "        tempArray = [list1[i][0], list1[i][1], days[j]]\n", "        finalArray.append(tempArray[:])\n", "finalArray = pd.<PERSON><PERSON><PERSON><PERSON>(finalArray)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray.columns = [\"outlet_id\", \"outlet_name\", \"date\"]\n", "finalArray.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray.dtypes\n", "finalArray[\"date\"] = pd.to_datetime(finalArray[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pd.merge(finalArray, data_CM, how=\"left\", on=[\"outlet_id\", \"date\"])\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"delivered_gmv\"] = final.delivered_gmv.fillna(0)\n", "final[\"order_count\"] = final.order_count.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1 = final.values.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(l1)):\n", "    if l1[i][3] == 0:\n", "        l1[i][3] = round(((l1[i - 1][3] + l1[i - 2][3] + l1[i - 3][3]) / 3), 2)\n", "        l1[i][4] = round(((l1[i - 1][4] + l1[i - 2][4] + l1[i - 3][4]) / 3), 2)\n", "df = pd.DataFrame(l1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"date\",\n", "    \"order_count\",\n", "    \"delivered_gmv\",\n", "    \"month\",\n", "]\n", "\n", "df[\"order_count\"] = np.round(df[\"order_count\"], decimals=2)\n", "df[\"delivered_gmv\"] = np.round(df[\"delivered_gmv\"], decimals=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns, df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.drop([\"month\"], axis=1, inplace=True)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.rename(\n", "    columns={\n", "        \"order_count\": \"Actual_Forecasted_Orders\",\n", "        \"delivered_gmv\": \"Actual_Forecasted_GMV\",\n", "    },\n", "    inplace=True,\n", ")\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL.columns, MG.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL[\"Outlet ID\"] = FC_3PL[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")\n", "MG[\"Outlet ID\"] = MG[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FC_3PL.shape, MG.shape, df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merge with order MG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = df.merge(FC_3PL, how=\"left\", left_on=[\"outlet_id\"], right_on=[\"Outlet ID\"])\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.drop([\"Remarks\", \"Outlet ID\"], axis=1, inplace=True)\n", "temp1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = temp1.fillna(0)\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[\"Revised MG Orders\"] = temp1[\"Revised MG Orders\"].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[\"Orders\"] = temp1[[\"Actual_Forecasted_Orders\", \"Revised MG Orders\"]].max(axis=1)\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.dtypes, temp1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1[\n", "    [\n", "        \"Daily MG orders\",\n", "        \"Rate- per order\",\n", "        \"Revised MG Orders\",\n", "        \"Revised Rate- per order\",\n", "    ]\n", "] = temp1[\n", "    [\n", "        \"Daily MG orders\",\n", "        \"Rate- per order\",\n", "        \"Revised MG Orders\",\n", "        \"Revised Rate- per order\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1.drop(\n", "    [\n", "        \"Actual_Forecasted_Orders\",\n", "        \"Daily MG orders\",\n", "        \"Rate- per order\",\n", "        \"Revised MG Orders\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "temp1[\"E3_3PLcost\"] = temp1[\"Orders\"] * temp1[\"Revised Rate- per order\"]\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E3_3PLcost = temp1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merge with GMV MG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"month\"] = df[\"date\"].dt.month\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.groupby([\"outlet_id\", \"outlet_name\", \"month\"])[\n", "    \"Actual_Forecasted_Orders\", \"Actual_Forecasted_GMV\"\n", "].agg(\"sum\")\n", "df = df.reset_index()\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.drop([\"Actual_Forecasted_Orders\"], axis=1, inplace=True)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MG.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = df.merge(\n", "    MG,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"month\"],\n", "    right_on=[\"Outlet ID\", \"Month_Number\"],\n", ")\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.drop([\"Outlet ID\", \"Month_Number\"], axis=1, inplace=True)\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.loc[temp2[\"Picking included\"] == \"Yes\", \"GMV\"] = (\n", "    temp2[\"Actual_Forecasted_GMV\"] * 4.5 / 100\n", ")\n", "temp2.loc[temp2[\"Picking included\"] == \"No\", \"GMV\"] = (\n", "    temp2[\"Actual_Forecasted_GMV\"] * 3.5 / 100\n", ")\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.columns, temp2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"Minimum Guarantee\\n (Monthly)\"] = temp2[\"Minimum Guarantee\\n (Monthly)\"].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"Commission\"] = temp2[[\"GMV\", \"Minimum Guarantee\\n (Monthly)\"]].max(axis=1)\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"Commission\"] = temp2[\"Commission\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.drop([\"Minimum Guarantee\\n (Monthly)\", \"GMV\"], axis=1, inplace=True)\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta, date\n", "import calendar\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "num_days = calendar.monthrange(year, month)[1]\n", "days = [date(year, month, day) for day in range(1, num_days + 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"outlet_id\"] = temp2[\"outlet_id\"].apply(pd.to_numeric, errors=\"coerce\")\n", "outlet_list = temp2[\"outlet_id\"].unique()\n", "outlet_list = outlet_list.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray = []\n", "for i in range(0, len(outlet_list)):\n", "    for j in range(0, len(days)):\n", "        tempArray = [outlet_list[i], days[j]]\n", "        finalArray.append(tempArray[:])\n", "finalArray = pd.<PERSON><PERSON><PERSON><PERSON>(finalArray)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray.columns = [\"outlet_id\", \"date\"]\n", "finalArray.dropna(subset=[\"outlet_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["finalArray[\"date\"] = pd.to_datetime(finalArray[\"date\"])\n", "finalArray[\"month\"] = finalArray[\"date\"].dt.month\n", "finalArray[\"outlet_id\"] = finalArray[\"outlet_id\"].apply(pd.to_numeric, errors=\"coerce\")\n", "finalArray.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp = temp2.merge(finalArray, how=\"inner\", on=[\"outlet_id\", \"month\"])\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if temp[\"month\"].isin(Month_30).any():\n", "    temp[\"Daily_Commission\"] = temp[\"Commission\"] / 30\n", "elif temp[\"month\"].isin(Month_28).any():\n", "    temp[\"Daily_Commission\"] = temp[\"Commission\"] / 28\n", "else:\n", "    temp[\"Daily_Commission\"] = temp[\"Commission\"] / 31\n", "\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp.drop(\n", "    [\"Actual_Forecasted_GMV\", \"year\", \"month\", \"Commission\", \"Picking included\"],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Commission = temp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### E3 Merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Commission.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollCost.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Commission.shape, OnrollCost.shape, E3_3PLcost.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = Commission.merge(OnrollCost, how=\"outer\", on=[\"outlet_id\", \"date\"])\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E3_3PLcost.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = temp1.merge(E3_3PLcost, how=\"outer\", on=[\"outlet_id\", \"outlet_name\", \"date\"])\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.drop(\n", "    [\"Actual_Forecasted_GMV\", \"Revised Rate- per order\", \"Orders\"], axis=1, inplace=True\n", ")\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.columns, temp2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "sd = datetime.datetime(year, month, 1)\n", "start_date = sd.date()\n", "\n", "ed = datetime.datetime(year, month, day)\n", "end_date = ed.date()\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mask = (temp2[\"date\"] >= start_date) & (temp2[\"date\"] <= end_date)\n", "temp2 = temp2.loc[mask]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"date\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.drop([\"outlet_name\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"Store Type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Type of outlet\",\n", "    },\n", "    {\n", "        \"name\": \"date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Date\",\n", "    },\n", "    {\n", "        \"name\": \"Daily_Commission\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Daily commission based on minimum guarantee of GMV\",\n", "    },\n", "    {\n", "        \"name\": \"Daily_OnrollCost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Daily onroll cost based on minimum guarantee of orders\",\n", "    },\n", "    {\n", "        \"name\": \"E3_3PLcost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"3PL cost for E3\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"E3_CostComponents\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"date\"],\n", "    \"sortkey\": [\"date\"],\n", "    \"incremental_key\": \"date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"E3 cost components\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_redshift(temp2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}