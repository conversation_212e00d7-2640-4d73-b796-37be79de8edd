{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import os\n", "import math\n", "import time\n", "import random\n", "import numpy as np\n", "import sqlalchemy as sqla\n", "import datetime\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picklist = \"\"\"\n", "SELECT p.outlet_id,\n", "            o.name AS outlet_name,\n", "            cl.id as city_id,\n", "            cl.name as city_name,\n", "            o.facility_id,\n", "            f.name as facility_name,\n", "            p.id as pick_list_id,\n", "            p.pick_list_id as ground_pick_list_id ,\n", "            convert_tz(p.created_at,'+00:00','+05:30') AS Created_at_ist,\n", "            date(convert_tz(p.assigned_at,'+00:00','+05:30')) AS assigned_on,\n", "            convert_tz(p.max_scheduled_time,'+00:00','+05:30') AS Scheduled_Delivery_at_ist,\n", "          \n", "          \n", "            \n", "            \n", "            p.assigned_to_name AS picker_Id,\n", "            rwu.name as Picker_Name,\n", "            rwu.role_id,\n", "            ur.name as user_role,\n", "            date(rwu.doj) as doj,\n", "            DATEDIFF(NOW(),rwu.doj) AS picker_experience_in_days,\n", "            \n", "            convert_tz(p.picking_started_at,'+00:00','+05:30') as picking_started_at_ist,\n", "            convert_tz(pll.pos_timestamp,'+00:00','+05:30') as picking_completed_at_ist,\n", "            TIMESTAMPDIFF(SECOND,p.picking_started_at,pll.pos_timestamp)/60 as time_taken_for_picking_in_mins,\n", "            wd.imei as device_imei,\n", "    \n", "            count(pli.pick_list_item_id) as Item_Count,\n", "            sum(pli.required_quantity) as required_quantity,\n", "            sum(pli.picked_quantity) as picked_quantity,\n", "            count(case when pli.state = 3 then pli.pick_list_item_id end) as no_of_not_found_items\n", "            \n", "            \n", "\n", "FROM warehouse_location.warehouse_pick_list p\n", "INNER JOIN retail.console_outlet o ON p.outlet_id = o.id\n", "INNER JOIN retail.console_location cl ON o.tax_location_id = cl.id\n", "INNER JOIN crates.facility f ON f.id = o.facility_id\n", "INNER JOIN warehouse_location.pick_list_log pll ON p.id = pll.pick_list_id \n", "INNER JOIN warehouse_location.warehouse_pick_list_item pli ON p.id = pli.pick_list_id\n", "INNER JOIN warehouse_location.warehouse_devices wd on p.assigned_to_device_id = wd.id\n", "LEFT JOIN retail.warehouse_user rwu ON rwu.emp_id = p.assigned_to_name\n", "LEFT JOIN retail.warehouse_user_role ur on rwu.role_id = ur.id\n", "\n", "WHERE \n", "\n", "\n", "date(convert_tz(p.max_scheduled_time,'+00:00','+05:30')) between adddate(curdate(), Interval -4 day) and adddate(curdate(), Interval 7 day)\n", "AND p.state in (4,6,10)\n", "AND pll.picklist_state = 4 \n", "and p.type = 1\n", "#and o.name not like '%%Cold%%' \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21\n", "order by p.id\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(picklist, con_retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.il<PERSON>[1:10, 1:20]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["picker = f\"\"\"\n", "SELECT pck.pick_list_id,\n", "       created_by_name,\n", "       rwu.name AS Picker_Name1,\n", "       rwu.role_id as role_id1,\n", "       ur.name AS user_role1,\n", "       date(rwu.doj) AS doj1,\n", "       DATEDIFF(day,date(rwu.doj),current_date) AS picker_experience_in_days1\n", "FROM\n", "  (SELECT pll.pick_list_id,\n", "          pll.created_by_name,\n", "          min(pll.pos_timestamp)AS tme,\n", "          rank() over(PARTITION BY pll.pick_list_id\n", "                      ORDER BY tme ASC,pll.created_by_name) AS fst\n", "   FROM lake_warehouse_location.pick_list_log pll inner join \n", "   lake_warehouse_location.warehouse_pick_list p ON p.id = pll.pick_list_id \n", "   WHERE pll.picklist_state=3\n", "     AND pll.created_by_name IS NOT NULL\n", "     AND date(convert_timezone('Asia/Kolkata',p.max_scheduled_time)) between current_date-4 and current_date+7\n", "   GROUP BY 1,\n", "            2)AS pck\n", "LEFT JOIN lake_retail.warehouse_user rwu ON rwu.emp_id = pck.created_by_name\n", "LEFT JOIN lake_retail.warehouse_user_role ur ON rwu.role_id = ur.id\n", "WHERE fst=1;\n", "\"\"\"\n", "df_irt = pd.read_sql_query(picker, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_irt.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_irt.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_fin = df.copy()\n", "df_merged = pd.merge(df, df_irt, on=[\"pick_list_id\"], how=\"left\")\n", "df_merged.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged[\"created_by_name\"] = df_merged[\"created_by_name\"].astype(str)\n", "df_merged[\"irt_check\"] = (df_merged[\"created_by_name\"] == \"\") | (\n", "    df_merged[\"created_by_name\"] == \"nan\"\n", ")\n", "df_merged[\"picker_id_check\"] = df_merged[\"created_by_name\"] != df_merged[\"picker_Id\"]\n", "df_merged[\"picker_id_check\"] = df_merged[\"picker_id_check\"].astype(int)\n", "df_merged[\"irt_check\"] = df_merged[\"irt_check\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged[\"picker_Id_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"created_by_name\"],\n", "    df_merged[\"picker_Id\"],\n", ")\n", "df_merged[\"Picker_Name_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"picker_name1\"],\n", "    df_merged[\"Picker_Name\"],\n", ")\n", "df_merged[\"role_id_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"role_id1\"],\n", "    df_merged[\"role_id\"],\n", ")\n", "df_merged[\"user_role_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"user_role1\"],\n", "    df_merged[\"user_role\"],\n", ")\n", "df_merged[\"role_id_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"role_id1\"],\n", "    df_merged[\"role_id\"],\n", ")\n", "df_merged[\"doj_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"doj1\"],\n", "    df_merged[\"doj\"],\n", ")\n", "df_merged[\"picker_experience_in_days_fin\"] = np.where(\n", "    (df_merged[\"picker_id_check\"] == 1) & (df_merged[\"irt_check\"] == 0),\n", "    df_merged[\"picker_experience_in_days1\"],\n", "    df_merged[\"picker_experience_in_days\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged = df_merged.drop(\n", "    columns=[\n", "        \"created_by_name\",\n", "        \"picker_name1\",\n", "        \"role_id1\",\n", "        \"user_role1\",\n", "        \"doj1\",\n", "        \"picker_experience_in_days1\",\n", "        \"picker_Id\",\n", "        \"Picker_Name\",\n", "        \"role_id\",\n", "        \"user_role\",\n", "        \"doj\",\n", "        \"picker_experience_in_days\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_merged = df_merged.rename(\n", "    columns={\n", "        \"picker_Id_fin\": \"picker_Id\",\n", "        \"Picker_Name_fin\": \"Picker_Name\",\n", "        \"role_id_fin\": \"role_id\",\n", "        \"user_role_fin\": \"user_role\",\n", "        \"doj_fin\": \"doj\",\n", "        \"picker_experience_in_days_fin\": \"picker_experience_in_days\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df_merged.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hold = \"\"\"\n", "\n", "\n", " SELECT wpll.pick_list_id,\n", "             wpll.picklist_state,\n", "             wpll.pos_timestamp\n", "            --  lag(wpll.pos_timestamp,1) over (ORDER BY wpll.pick_list_id, wpll.pos_timestamp) AS prev_timestamp\n", "     \n", "      FROM warehouse_location.pick_list_log wpll\n", "      INNER JOIN warehouse_location.warehouse_pick_list wpl ON wpl.id = wpll.pick_list_id\n", "      \n", "      WHERE date(wpl.max_scheduled_time) between adddate(curdate(), Interval -4 day) and adddate(curdate(), Interval 7 day)\n", "            AND wpll.PICKLIST_STATE IN (12,13) \n", "      ORDER BY 1,\n", "               3\n", "            \n", "  \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_sql_query(hold, con_retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[\"Data_lagged\"] = df1.pos_timestamp.shift(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = df1.loc[df1[\"picklist_state\"] == 13]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2[\"pick_list_on_hold_in_mins\"] = df2[\"pos_timestamp\"] - df2[\"Data_lagged\"]\n", "df3 = df2.copy()\n", "df3[\"pick_list_on_hold_in_mins\"] = round(\n", "    (df3[\"pick_list_on_hold_in_mins\"].astype(\"timedelta64[s]\")) / 60, 2\n", ")\n", "df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df4 = (\n", "    df3.groupby([\"pick_list_id\"])\n", "    .agg({\"pick_list_on_hold_in_mins\": \"sum\"})\n", "    .reset_index()\n", ")\n", "df4.head(2)\n", "\n", "df4[\"Picklist_on_Hold\"] = 1\n", "df4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = pd.merge(df, df4, on=[\"pick_list_id\"], how=\"left\")\n", "final1[[\"Picklist_on_Hold\", \"pick_list_on_hold_in_mins\", \"role_id\"]] = final1[\n", "    [\"Picklist_on_Hold\", \"pick_list_on_hold_in_mins\", \"role_id\"]\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.shape\n", "# final1.to_csv(\"2.csv\")\n", "# df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Outlet_SKU = \"\"\"\n", "\n", "\n", "SELECT p.outlet_id,\n", "            o.name AS outlet_name,\n", "            count(distinct pli.item_id) as Outlet_SKU\n", "            \n", "\n", "FROM warehouse_location.warehouse_pick_list p\n", "INNER JOIN retail.console_outlet o ON p.outlet_id = o.id\n", "INNER JOIN warehouse_location.pick_list_log pll ON p.id = pll.pick_list_id \n", "INNER JOIN warehouse_location.warehouse_pick_list_item pli ON p.id = pli.pick_list_id\n", "\n", "\n", "\n", "\n", "WHERE \n", "date(convert_tz(p.max_scheduled_time,'+00:00','+05:30')) between adddate(curdate(), Interval -4 day) and adddate(curdate(), Interval 7 day)\n", "AND p.state in (4,6,10)\n", "AND pll.picklist_state = 4 \n", "and p.type = 1\n", "#and o.name not like '%%Cold%%'\n", "group by 1,2\n", "order by 2\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5 = pd.read_sql_query(Outlet_SKU, con_retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df5.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2 = pd.merge(\n", "    final1, df5[[\"outlet_id\", \"Outlet_SKU\"]], on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2.head(5)\n", "final2[\"etl_timestamp_utc\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2.<PERSON><PERSON>[0:8, 12:15]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2 = final2[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"pick_list_id\",\n", "        \"ground_pick_list_id\",\n", "        \"Created_at_ist\",\n", "        \"assigned_on\",\n", "        \"Scheduled_Delivery_at_ist\",\n", "        \"picker_Id\",\n", "        \"Picker_Name\",\n", "        \"role_id\",\n", "        \"user_role\",\n", "        \"doj\",\n", "        \"picker_experience_in_days\",\n", "        \"picking_started_at_ist\",\n", "        \"picking_completed_at_ist\",\n", "        \"time_taken_for_picking_in_mins\",\n", "        \"device_imei\",\n", "        \"Item_Count\",\n", "        \"required_quantity\",\n", "        \"picked_quantity\",\n", "        \"no_of_not_found_items\",\n", "        \"pick_list_on_hold_in_mins\",\n", "        \"Picklist_on_Hold\",\n", "        \"Outlet_SKU\",\n", "        \"etl_timestamp_utc\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "final2[[\"required_quantity\", \"picked_quantity\", \"role_id\"]] = final2[\n", "    [\"required_quantity\", \"picked_quantity\", \"role_id\"]\n", "].astype(\"int\")\n", "final2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2[\"picker_experience_in_days\"].head(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final2.shape\n", "# final2.il<PERSON>[1:4,:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "    {\"name\": \"city_id\", \"type\": \"int\", \"description\": \"city_id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\"name\": \"pick_list_id\", \"type\": \"int\", \"description\": \"pick_list_id\"},\n", "    {\n", "        \"name\": \"ground_pick_list_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"ground_pick_list_id\",\n", "    },\n", "    {\"name\": \"Created_at_ist\", \"type\": \"datetime\", \"description\": \"Created_at_ist\"},\n", "    {\"name\": \"assigned_on\", \"type\": \"datetime\", \"description\": \"assigned_on\"},\n", "    {\n", "        \"name\": \"Scheduled_Delivery_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"Scheduled_Delivery_at_ist\",\n", "    },\n", "    {\"name\": \"picker_Id\", \"type\": \"varchar\", \"description\": \"picker_Id\"},\n", "    {\"name\": \"Picker_Name\", \"type\": \"varchar\", \"description\": \"Picker_Name\"},\n", "    {\"name\": \"role_id\", \"type\": \"int\", \"description\": \"role_id\"},\n", "    {\"name\": \"user_role\", \"type\": \"varchar\", \"description\": \"user_role\"},\n", "    {\"name\": \"doj\", \"type\": \"datetime\", \"description\": \"doj\"},\n", "    {\n", "        \"name\": \"picker_experience_in_days\",\n", "        \"type\": \"float\",\n", "        \"description\": \"picker_experience_in_days\",\n", "    },\n", "    {\n", "        \"name\": \"picking_started_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"picking_started_at_ist\",\n", "    },\n", "    {\n", "        \"name\": \"picking_completed_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"picking_completed_at_ist\",\n", "    },\n", "    {\n", "        \"name\": \"time_taken_for_picking_in_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"time_taken_for_picking_in_mins\",\n", "    },\n", "    {\"name\": \"device_imei\", \"type\": \"float\", \"description\": \"device_imei\"},\n", "    {\"name\": \"Item_Count\", \"type\": \"int\", \"description\": \"Item_Count\"},\n", "    {\"name\": \"required_quantity\", \"type\": \"int\", \"description\": \"required_quantity\"},\n", "    {\"name\": \"picked_quantity\", \"type\": \"int\", \"description\": \"picked_quantity\"},\n", "    {\n", "        \"name\": \"no_of_not_found_items\",\n", "        \"type\": \"int\",\n", "        \"description\": \"no_of_not_found_items\",\n", "    },\n", "    {\n", "        \"name\": \"pick_list_on_hold_in_mins\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pick_list_on_hold_in_mins\",\n", "    },\n", "    {\n", "        \"name\": \"Picklist_on_Hold\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Flag for picklists that were put on hold\",\n", "    },\n", "    {\"name\": \"Outlet_SKU\", \"type\": \"int\", \"description\": \"Outlet_SKU\"},\n", "    {\n", "        \"name\": \"etl_timestamp_utc\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl_timestamp_utc\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"picklist_completed\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"pick_list_id\"],\n", "    \"sortkey\": [\"Scheduled_Delivery_at_ist\", \"outlet_id\"],\n", "    \"incremental_key\": \"etl_timestamp_utc\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This tables powers the picker performance dashboard on tableau\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final2.to_csv(\"final.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Flat table picklist_completed creation\n", "pb.to_redshift(final2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}