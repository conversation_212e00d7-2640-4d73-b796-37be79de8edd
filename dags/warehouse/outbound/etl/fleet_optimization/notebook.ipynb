{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sender Lat/Long"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Backend Facility Lat/Long\n", "\n", "sheet_id = \"1pMT_PLfa6Wvnralx2VFmQNq0PKf4j8hb5A2esFThTNI\"\n", "sheet_name = \"FC\"\n", "\n", "df1 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Facility ID,Name\n", "\n", "q1 = \"\"\"\n", "select distinct id,\n", "        name\n", "from lake_crates.facility\n", "\"\"\"\n", "df2 = pd.read_sql_query(q1, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["A = df1.merge(df2, how=\"inner\", left_on=[\"FC_Name\"], right_on=[\"name\"])\n", "A.drop([\"name\"], axis=1, inplace=True)\n", "A.rename(columns={\"id\": \"FacilityID\", \"FC_Name\": \"FacilityName\"}, inplace=True)\n", "<PERSON><PERSON>head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All Receivers for Sender Facilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "select distinct co.id as sender_outlet_id,\n", "                co.name as sender_name,\n", "                co.address as sender_address,\n", "                co.facility_id as sender_facility_id,\n", "                cot.id as receiver_outlet_id,\n", "                cot.name as reciever_name, \n", "                cot.address as receiver_address,\n", "                cot.facility_id as receiver_facility_id\n", "from ims.ims_sto_details i\n", "join retail.console_outlet co \n", "on co.id = i.outlet_id \n", "and co.business_type_id not in (7,8)\n", "join retail.console_outlet cot \n", "on cot.id = i.merchant_outlet_id \n", "and cot.business_type_id =7 \n", "where date(i.created_at) >= current_date-7\n", "order by 4,8\n", "\"\"\"\n", "df3 = pd.read_sql_query(q2, con=pb.get_connection(\"[Replica] IMS\"))\n", "df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SenderLL = A.merge(\n", "    df3, how=\"inner\", left_on=[\"FacilityID\"], right_on=[\"sender_facility_id\"]\n", ")\n", "SenderLL.drop(\n", "    [\n", "        \"sender_outlet_id\",\n", "        \"sender_name\",\n", "        \"sender_address\",\n", "        \"FacilityID\",\n", "        \"Dispatch Address\",\n", "        \"FC\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "SenderLL.rename(\n", "    columns={\n", "        \"FacilityName\": \"sender_facility_name\",\n", "        \"Latitude\": \"sender_Latitude\",\n", "        \"Longitude\": \"sender_Longitude\",\n", "    },\n", "    inplace=True,\n", ")\n", "SenderLL.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Recent STOs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct sto.sto_id,\n", "    sto.outlet_id,\n", "    sto.merchant_outlet_id,\n", "    (from_unixtime(sto.created_at/pow(10,6)) + interval '5' HOUR + interval '30' MINUTE) as sto_created_at,\n", "    sum(si.reserved_quantity * pp.w)/1000.0 as weight_in_kg\n", "from lake_ims.ims_sto_details sto \n", "join lake_ims.ims_sto_item si\n", "    on sto.sto_id = si.sto_id\n", "join (select item_id, \n", "            max(weight_in_gm) as w \n", "      from lake_rpc.product_product \n", "      group by 1\n", "      ) pp\n", "    on si.item_id = pp.item_id\n", "where cast((from_unixtime(sto.created_at/pow(10,6)) + interval '5' HOUR + interval '30' MINUTE) as date) = cast(current_date as date)\n", "group by 1,2,3,4\n", "\"\"\"\n", "data = pd.read_sql_query(query, con=pb.get_connection(\"[Warehouse] Presto\"))\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"sto_created_at\"] = pd.to_datetime(data[\"sto_created_at\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "data[\"present_datetime\"] = datetime.now()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"difference\"] = data[\"present_datetime\"] - data[\"sto_created_at\"]\n", "data[\"difference_in_sec\"] = data[\"difference\"].dt.total_seconds()\n", "data[\"difference_in_hrs\"] = divmod(data[\"difference_in_sec\"], 3600)[0]\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data[data[\"difference_in_hrs\"] < 2]\n", "data.drop(\n", "    [\"present_datetime\", \"difference\", \"difference_in_sec\", \"difference_in_hrs\"],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct co.id as outlet_id,\n", "    -- co.name as outlet_name,\n", "    f.id as facility_id,\n", "    f.name as facility_name\n", "from lake_retail.console_outlet co\n", "join lake_crates.facility f\n", "on co.facility_id = f.id\n", "\"\"\"\n", "mapping = pd.read_sql_query(query, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = data.merge(mapping, how=\"left\", on=[\"outlet_id\"])\n", "temp1.rename(\n", "    columns={\n", "        \"facility_id\": \"sender_facility_id\",\n", "        \"outlet_id\": \"sender_outlet_id\",\n", "        \"facility_name\": \"sender_facility_name\",\n", "    },\n", "    inplace=True,\n", ")\n", "temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct co.id as outlet_id,\n", "    -- co.name as outlet_name,\n", "    f.id as facility_id\n", "    --f.name as facility_name\n", "from lake_retail.console_outlet co\n", "join lake_crates.facility f\n", "on co.facility_id = f.id\n", "\"\"\"\n", "mapping = pd.read_sql_query(query, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = temp1.merge(\n", "    mapping, how=\"left\", left_on=[\"merchant_outlet_id\"], right_on=[\"outlet_id\"]\n", ")\n", "temp2.rename(\n", "    columns={\n", "        \"facility_id\": \"receiver_facility_id\",\n", "        \"merchant_outlet_id\": \"receiver_outlet_id\",\n", "    },\n", "    inplace=True,\n", ")\n", "temp2.drop([\"outlet_id\"], axis=1, inplace=True)\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2[\"weight_in_kg\"] = temp2[\"weight_in_kg\"].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = temp2.groupby(\n", "    [\"sender_facility_id\", \"sender_facility_name\", \"receiver_facility_id\"]\n", ").sum()\n", "temp2.drop([\"sto_id\", \"sender_outlet_id\", \"receiver_outlet_id\"], axis=1, inplace=True)\n", "temp2 = temp2.reset_index()\n", "temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SenderLL.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Filtered for sender facilities which have lat/long\n", "\n", "final1 = temp2.merge(\n", "    SenderLL, how=\"inner\", on=[\"sender_facility_id\", \"sender_facility_name\"]\n", ")\n", "final1.drop([\"receiver_facility_id_y\"], axis=1, inplace=True)\n", "final1.rename(columns={\"receiver_facility_id_x\": \"receiver_facility_id\"}, inplace=True)\n", "final1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Receiver Lat/Long"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q3 = \"\"\"\n", "SELECT DISTINCT facility_id,\n", "                facility_name,\n", "                pos_outlet_id as outlet_id,\n", "                pos_outlet_name as outlet_name,\n", "                -- pos_outlet_city_name,\n", "                backend_merchant_id,\n", "                backend_merchant_name,\n", "                frontend_merchant_id,\n", "                frontend_merchant_name,\n", "                m.latitude,\n", "                m.longitude\n", "FROM dwh.dim_merchant_outlet_facility_mapping x\n", "left join lake_cms.view_gr_merchant m \n", "on m.id = x.frontend_merchant_id \n", "where is_current = true  and x.chain_id = 1383\n", "and frontend_merchant_name not ilike '%%test%%'\n", "order by 1\n", "\"\"\"\n", "df4 = pd.read_sql_query(q3, con=redshift)\n", "df4.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Join to get receiver lat/long"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_senderFacility = final1.sender_facility_id.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_senderFacility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr = []\n", "\n", "for i in range(0, len(list_senderFacility)):\n", "    data_i = final1[final1[\"sender_facility_id\"] == list_senderFacility[i]]\n", "\n", "    list_receiverFacility = data_i.receiver_facility_id.unique().tolist()\n", "    latlong_i = df4[df4[\"facility_id\"].isin(list_receiverFacility)]\n", "\n", "    # Join to get receiver mapping for senders\n", "    final_i = data_i.merge(\n", "        latlong_i,\n", "        left_on=[\"receiver_facility_id\"],\n", "        right_on=[\"facility_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    final_i.drop(\n", "        [\n", "            \"receiver_outlet_id\",\n", "            \"reciever_name\",\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"backend_merchant_id\",\n", "            \"backend_merchant_name\",\n", "            \"frontend_merchant_id\",\n", "            \"frontend_merchant_name\",\n", "            \"receiver_address\",\n", "        ],\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "    final_i.rename(\n", "        columns={\"latitude\": \"Receiver_Latitude\", \"longitude\": \"Receiver_Longitude\"},\n", "        inplace=True,\n", "    )\n", "    final_i = final_i.drop_duplicates()\n", "\n", "    # Cross join\n", "    final_i[\"join\"] = 1\n", "    new_df_i = final_i.merge(final_i, on=\"join\").drop(\"join\", axis=1)\n", "\n", "    new_df_i.drop(\n", "        [\n", "            \"sender_facility_id_y\",\n", "            \"sender_facility_name_y\",\n", "            \"sender_Latitude_y\",\n", "            \"sender_Longitude_y\",\n", "            \"weight_in_kg_y\",\n", "        ],\n", "        axis=1,\n", "        inplace=True,\n", "    )\n", "\n", "    new_df_i.rename(\n", "        columns={\n", "            \"sender_facility_id_x\": \"sender_facility_id\",\n", "            \"sender_facility_name_x\": \"sender_facility_name\",\n", "            \"sender_Latitude_x\": \"Sender_Latitude\",\n", "            \"sender_Longitude_x\": \"Sender_Longitude\",\n", "            \"receiver_facility_id_x\": \"receiver_facility_id1\",\n", "            \"Receiver_Latitude_x\": \"Receiver_Latitude1\",\n", "            \"Receiver_Longitude_x\": \"Receiver_Longitude1\",\n", "            \"receiver_facility_id_y\": \"receiver_facility_id2\",\n", "            \"Receiver_Latitude_y\": \"Receiver_Latitude2\",\n", "            \"Receiver_Longitude_y\": \"Receiver_Longitude2\",\n", "            \"weight_in_kg_x\": \"weight_in_kg\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    new_df_i = new_df_i[\n", "        new_df_i[\"receiver_facility_id1\"] != new_df_i[\"receiver_facility_id2\"]\n", "    ]\n", "\n", "    arr.append(new_df_i)\n", "\n", "arr = pd.concat(arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Distance using Lat/Long"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dist_S_R1 = []\n", "\n", "for index, row in arr.iterrows():\n", "\n", "    # Sender to first receiver\n", "    coords = (\n", "        \"\"\n", "        + str(row[\"Sender_Longitude\"])\n", "        + \",\"\n", "        + str(row[\"Sender_Latitude\"])\n", "        + \";\"\n", "        + str(row[\"Receiver_Longitude1\"])\n", "        + \",\"\n", "        + str(row[\"Receiver_Latitude1\"])\n", "    )\n", "\n", "    statemnt = (\n", "        OSRM_URL\n", "        + \"/{}/v1/driving/\".format(\"route\")\n", "        + coords\n", "        + \"?overview=false&annotations=true\"\n", "    )\n", "    r = requests.get(statemnt)\n", "\n", "    try:\n", "        dist = r.json()[\"routes\"][0][\"distance\"]\n", "    except:\n", "        dist = 0\n", "\n", "    dist_S_R1.append(dist)\n", "\n", "arr[\"dist_sender_receiver1\"] = dist_S_R1\n", "arr[\"dist_sender_receiver1\"] = arr[\"dist_sender_receiver1\"] / 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dist_R1_R2 = []\n", "\n", "for index, row in arr.iterrows():\n", "\n", "    # First receiver to second receiver\n", "    coords = (\n", "        \"\"\n", "        + str(row[\"Receiver_Longitude1\"])\n", "        + \",\"\n", "        + str(row[\"Receiver_Latitude1\"])\n", "        + \";\"\n", "        + str(row[\"Receiver_Longitude2\"])\n", "        + \",\"\n", "        + str(row[\"Receiver_Latitude2\"])\n", "    )\n", "\n", "    statemnt = (\n", "        OSRM_URL\n", "        + \"/{}/v1/driving/\".format(\"route\")\n", "        + coords\n", "        + \"?overview=false&annotations=true\"\n", "    )\n", "    r = requests.get(statemnt)\n", "    try:\n", "        dist = r.json()[\"routes\"][0][\"distance\"]\n", "    except:\n", "        dist = 0\n", "\n", "    dist_R1_R2.append(dist)\n", "\n", "arr[\"dist_receiver1_receiver2\"] = dist_R1_R2\n", "arr[\"dist_receiver1_receiver2\"] = arr[\"dist_receiver1_receiver2\"] / 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr[\"total_dist\"] = arr[\"dist_sender_receiver1\"] + arr[\"dist_receiver1_receiver2\"]\n", "arr.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr[\"speed_kmph\"] = 20\n", "arr[\"inTransitTime_sender_receiver1\"] = (\n", "    arr[\"dist_sender_receiver1\"] / arr[\"speed_kmph\"]\n", ") * 60\n", "arr[\"inTransitTime_receiver1_receiver2\"] = (\n", "    arr[\"dist_receiver1_receiver2\"] / arr[\"speed_kmph\"]\n", ") * 60\n", "arr[\"total_inTransitTime\"] = (arr[\"total_dist\"] / arr[\"speed_kmph\"]) * 60\n", "arr.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"sender_facility_id\",\n", "    \"sender_facility_name\",\n", "    \"Sender_Latitude\",\n", "    \"Sender_Longitude\",\n", "    \"weight_in_kg\",\n", "    \"receiver_facility_id1\",\n", "    \"Receiver_Latitude1\",\n", "    \"Receiver_Longitude1\",\n", "    \"receiver_facility_id2\",\n", "    \"Receiver_Latitude2\",\n", "    \"Receiver_Longitude2\",\n", "    \"dist_sender_receiver1\",\n", "    \"dist_receiver1_receiver2\",\n", "    \"total_dist\",\n", "    \"speed_kmph\",\n", "    \"inTransitTime_sender_receiver1\",\n", "    \"inTransitTime_receiver1_receiver2\",\n", "    \"total_inTransitTime\",\n", "]\n", "\n", "arr = arr.reindex(columns=column_names)\n", "arr.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Vehicle Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"17gmIf6vdjpsrvzzZZgbDwohtGyVfvolbYOC3bmo3-CI\"\n", "sheet_name = \"Vehicle Details\"\n", "veh = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = veh.iloc[0]\n", "veh = veh[1:]\n", "veh.columns = new_header"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veh = veh[0:11]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veh.drop([\"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veh.rename(\n", "    columns={\n", "        \"TRIP COST (Included all expences,MCD, Toll Tax ,NGT & state tax)\": \"TripCost\",\n", "        \"TRUCK TYPE\": \"Truck_Type\",\n", "        \"L\": \"Length\",\n", "        \"B\": \"Breadth\",\n", "        \"H\": \"Height\",\n", "        \"H Limit\": \"Height_Limit\",\n", "        \"Volume (cu ft)\": \"Volume_cu_ft\",\n", "        \"SIZE (FT)\": \"Size_ft\",\n", "        \"MAX WEIGHT\": \"Max_Weight\",\n", "        \"Vehicle Capacity (kg)\": \"VehicleCapacityKG\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veh[\n", "    [\n", "        \"Length\",\n", "        \"Breadth\",\n", "        \"Height\",\n", "        \"Height_Limit\",\n", "        \"Volume_cu_ft\",\n", "        \"VehicleCapacityKG\",\n", "        \"TripCost\",\n", "    ]\n", "] = veh[\n", "    [\n", "        \"Length\",\n", "        \"Breadth\",\n", "        \"Height\",\n", "        \"Height_Limit\",\n", "        \"Volume_cu_ft\",\n", "        \"VehicleCapacityKG\",\n", "        \"TripCost\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (veh[\"Max_Weight\"] == \"850kg to 1ton\"),\n", "    (veh[\"Max_Weight\"] == \"1 ton\"),\n", "    (veh[\"Max_Weight\"] == \"1.5 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"2.5 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"3 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"4 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"6 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"10 Ton\"),\n", "    (veh[\"Max_Weight\"] == \"6 to 7 ton\"),\n", "    (veh[\"Max_Weight\"] == \"7/8/9 tone\"),\n", "    (veh[\"Max_Weight\"] == \"15 Ton\"),\n", "]\n", "\n", "# create a list of the values we want to assign for each condition\n", "values = [925, 1000, 1500, 2500, 3000, 4000, 6000, 10000, 6500, 8000, 15000]\n", "\n", "# create a new column and use np.select to assign values to it using our lists as arguments\n", "veh[\"Max_Wt_kg\"] = np.select(conditions, values)\n", "\n", "veh.drop([\"VehicleCapacityKG\"], axis=1, inplace=True)\n", "veh[\"Veh_Cap_kg\"] = veh[\"Max_Wt_kg\"] * 0.8\n", "\n", "veh.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["veh.sort_values(by=[\"Veh_Cap_kg\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (arr[\"weight_in_kg\"] <= 740),\n", "    (arr[\"weight_in_kg\"] > 740) & (arr[\"weight_in_kg\"] <= 800),\n", "    (arr[\"weight_in_kg\"] > 800) & (arr[\"weight_in_kg\"] <= 1200),\n", "    (arr[\"weight_in_kg\"] > 1200) & (arr[\"weight_in_kg\"] <= 2000),\n", "    (arr[\"weight_in_kg\"] > 2000) & (arr[\"weight_in_kg\"] <= 2400),\n", "    (arr[\"weight_in_kg\"] > 2400) & (arr[\"weight_in_kg\"] <= 3200),\n", "    (arr[\"weight_in_kg\"] > 3200) & (arr[\"weight_in_kg\"] <= 4800),\n", "    (arr[\"weight_in_kg\"] > 4800) & (arr[\"weight_in_kg\"] <= 5200),\n", "    (arr[\"weight_in_kg\"] > 5200) & (arr[\"weight_in_kg\"] <= 6400),\n", "    (arr[\"weight_in_kg\"] > 6400) & (arr[\"weight_in_kg\"] <= 8000),\n", "    (arr[\"weight_in_kg\"] > 8000) & (arr[\"weight_in_kg\"] <= 12000),\n", "]\n", "\n", "# create a list of the values we want to assign for each condition\n", "values = [\n", "    \"TATA ACE\",\n", "    \"Dost\",\n", "    \"Pickup\",\n", "    \"TATA 407\",\n", "    \"14 FEET\",\n", "    \"17 FEET\",\n", "    \"19 FEET\",\n", "    \"CONTAINER 20 FT\",\n", "    \"CONTAINER 32 FT SXL\",\n", "    \"TATA 22 FT\",\n", "    \"CONTAINER 32 FT MXL\",\n", "]\n", "\n", "\n", "# create a new column and use np.select to assign values to it using our lists as arguments\n", "arr[\"Vehicle\"] = np.select(conditions, values)\n", "\n", "arr.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_mapping = arr[\n", "    [\n", "        \"sender_facility_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_facility_id1\",\n", "        \"weight_in_kg\",\n", "        \"Vehicle\",\n", "        \"dist_sender_receiver1\",\n", "        \"speed_kmph\",\n", "        \"inTransitTime_sender_receiver1\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All Receivers mapped before applying time constraint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SenderList = arr[\"sender_facility_id\"].unique().tolist()\n", "newList = []\n", "\n", "for i in range(0, len(SenderList)):\n", "    Receivers = (\n", "        arr[arr[\"sender_facility_id\"] == SenderList[i]]\n", "        .receiver_facility_id1.unique()\n", "        .tolist()\n", "    )\n", "    newList.append([SenderList[i], Receivers])\n", "SenderReceiverMapping = pd.DataFrame(newList)\n", "SenderReceiverMapping.columns = [\"SenderFacility\", \"ReceiverFacilities_All\"]\n", "print(SenderReceiverMapping)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Condition for 45 minutes in transit time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr0 = arr[\n", "    (arr[\"inTransitTime_sender_receiver1\"] < 45)\n", "    & (arr[\"inTransitTime_receiver1_receiver2\"] < 45)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr.shape, arr0.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Encoding to get min distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp_arr = arr0.values.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import base64\n", "\n", "new_arr = []\n", "for i in range(0, len(temp_arr)):\n", "    row_arr = []\n", "    row_arr.extend(temp_arr[i][:])\n", "    sorted_values = [temp_arr[i][5], temp_arr[i][8]]\n", "    sorted_values.sort()\n", "    final_value = str(sorted_values[0]) + \"-\" + str(sorted_values[1])\n", "    final_value_bytes = final_value.encode(\"ascii\")\n", "    base64_bytes = base64.b64encode(final_value_bytes)\n", "    row_arr.append(base64_bytes.decode(\"ascii\"))\n", "    new_arr.append(row_arr)\n", "\n", "# pd.DataFrame(sorted_arr)\n", "sorted_arr = sorted(new_arr, key=lambda x: x[-1])\n", "df = pd.DataFrame(sorted_arr)\n", "df.shape\n", "\n", "df.columns = [\n", "    \"SenderFacilityID\",\n", "    \"SenderFacilityName\",\n", "    \"SenderLatitude\",\n", "    \"SenderLongitude\",\n", "    \"Weight_KG\",\n", "    \"DarkStore1_ID\",\n", "    \"DarkStore1_Latitude\",\n", "    \"DarkStore1_Longitude\",\n", "    \"DarkStore2_ID\",\n", "    \"DarkStore2_Latitude\",\n", "    \"DarkStore2_Longitude\",\n", "    \"Dist_Sender_DS1\",\n", "    \"Dist_DS1_DS2\",\n", "    \"Total_Dist\",\n", "    \"Speed_kmph\",\n", "    \"InTransitTime_Sender_DS1_min\",\n", "    \"InTransitTime_DS1_DS2_min\",\n", "    \"Total_InTransitTime_min\",\n", "    \"Vehicle\",\n", "    \"Encode\",\n", "]\n", "\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import pandasql as ps\n", "\n", "final1 = ps.sqldf(\n", "    \"\"\"\n", "        SELECT SenderFacilityID,\n", "            SenderFacilityName,\n", "            SenderLatitude,\n", "            Sender<PERSON><PERSON><PERSON>,\n", "            Weight_KG,\n", "            DarkStore1_ID,\n", "            DarkStore1_Latitude,\n", "            DarkStore1_Longitude,\n", "            DarkStore2_ID,\n", "            DarkStore2_Latitude,\n", "            DarkStore2_Longitude,\n", "            Dist_Sender_DS1,\n", "            Dist_DS1_DS2,\n", "            Total_Dist,\n", "            Speed_kmph,\n", "            InTransitTime_Sender_DS1_min,\n", "            InTransitTime_DS1_DS2_min,\n", "            Total_InTransitTime_min,\n", "            Vehicle,\n", "            df.Encode\n", "        FROM df\n", "        JOIN \n", "            (\n", "                SELECT DISTINCT\n", "                    Encode,\n", "                    MIN(Total_Dist) over (partition by encode) as min_dist_path\n", "                FROM df\n", "            )a\n", "        ON df.Encode = a.Encode AND df.Total_Dist = a.min_dist_path\n", "        \"\"\",\n", "    locals(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.drop([\"Encode\"], axis=1, inplace=True)\n", "final1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapping to get names of receivers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select distinct id,name\n", "from lake_crates.facility\n", "\"\"\"\n", "m1 = pd.read_sql_query(q, con=pb.get_connection(\"[Warehouse] Redshift\"))\n", "m2 = pd.read_sql_query(q, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = final1.merge(m1, how=\"inner\", left_on=[\"DarkStore1_ID\"], right_on=[\"id\"])\n", "a.drop([\"id\"], axis=1, inplace=True)\n", "a.rename(columns={\"name\": \"DarkStore1_Name\"}, inplace=True)\n", "a.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = a.merge(m2, how=\"inner\", left_on=[\"DarkStore2_ID\"], right_on=[\"id\"])\n", "b.drop([\"id\"], axis=1, inplace=True)\n", "b.rename(columns={\"name\": \"DarkStore2_Name\"}, inplace=True)\n", "b.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"SenderFacilityID\",\n", "    \"SenderFacilityName\",\n", "    \"SenderLatitude\",\n", "    \"SenderLongitude\",\n", "    \"Weight_KG\",\n", "    \"DarkStore1_ID\",\n", "    \"DarkStore1_Name\",\n", "    \"DarkStore1_Latitude\",\n", "    \"DarkStore1_Longitude\",\n", "    \"DarkStore2_ID\",\n", "    \"DarkStore2_Name\",\n", "    \"DarkStore2_Latitude\",\n", "    \"DarkStore2_Longitude\",\n", "    \"Dist_Sender_DS1\",\n", "    \"Dist_DS1_DS2\",\n", "    \"Total_Dist\",\n", "    \"Speed_kmph\",\n", "    \"InTransitTime_Sender_DS1_min\",\n", "    \"InTransitTime_DS1_DS2_min\",\n", "    \"Total_InTransitTime_min\",\n", "    \"Vehicle\",\n", "]\n", "\n", "b = b.reindex(columns=column_names)\n", "\n", "b.drop(\n", "    [\n", "        \"SenderLatitude\",\n", "        \"SenderLongitude\",\n", "        \"DarkStore1_Latitude\",\n", "        \"DarkStore1_Longitude\",\n", "        \"DarkStore2_Latitude\",\n", "        \"DarkStore2_Longitude\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "b.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Milk Runs (to <PERSON><PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1iA_aBwuyrilEDMebsHMcM4oNPxjk-C7_kJm06Cad_BA\"\n", "sheet_name = \"Milk Runs\"\n", "pb.to_sheets(b, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Receivers Covered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SenderList = arr[\"sender_facility_id\"].unique().tolist()\n", "newList = []\n", "\n", "for i in range(0, len(SenderList)):\n", "    a = (\n", "        final1[final1[\"SenderFacilityID\"] == SenderList[i]]\n", "        .DarkStore1_ID.unique()\n", "        .tolist()\n", "    )\n", "    b = (\n", "        final1[final1[\"SenderFacilityID\"] == SenderList[i]]\n", "        .DarkStore2_ID.unique()\n", "        .tolist()\n", "    )\n", "    Receivers = a + b\n", "    R_set = set(Receivers)\n", "    Unique_Receivers = list(R_set)\n", "    newList.append([SenderList[i], Unique_Receivers])\n", "SenderReceivers_Covered = pd.DataFrame(newList)\n", "SenderReceivers_Covered.columns = [\"SenderFacility\", \"ReceiverFacilities_Covered\"]\n", "print(SenderReceivers_Covered)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Single Mapping (not milk runs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = SenderReceiverMapping.merge(\n", "    SenderReceivers_Covered, how=\"inner\", on=[\"SenderFacility\"]\n", ")\n", "\n", "a[\"CoveredAll\"] = np.where(\n", "    a[\"ReceiverFacilities_All\"] == a[\"ReceiverFacilities_Covered\"], \"Yes\", \"No\"\n", ")\n", "\n", "b = a[a[\"CoveredAll\"] == \"No\"].values.tolist()\n", "\n", "misses = []\n", "for i in range(0, len(b)):\n", "    misses.append([b[i][0], list(set(b[i][1]).difference(b[i][2]))])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_mapping.rename(\n", "    columns={\n", "        \"sender_facility_id\": \"SenderFacilityID\",\n", "        \"sender_facility_name\": \"SenderFacilityName\",\n", "        \"receiver_facility_id1\": \"DarkStore1_ID\",\n", "        \"weight_in_kg\": \"Weight_KG\",\n", "        \"dist_sender_receiver1\": \"Dist_Sender_DS1\",\n", "        \"speed_kmph\": \"Speed_kmph\",\n", "        \"inTransitTime_sender_receiver1\": \"InTransitTime_Sender_DS1_min\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "select distinct id,name\n", "from lake_crates.facility\n", "\"\"\"\n", "m1 = pd.read_sql_query(q, con=pb.get_connection(\"[Warehouse] Redshift\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aa = single_mapping.merge(m1, how=\"inner\", left_on=[\"DarkStore1_ID\"], right_on=[\"id\"])\n", "aa.drop([\"id\"], axis=1, inplace=True)\n", "aa.rename(columns={\"name\": \"DarkStore1_Name\"}, inplace=True)\n", "aa.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"SenderFacilityID\",\n", "    \"SenderFacilityName\",\n", "    \"DarkStore1_ID\",\n", "    \"DarkStore1_Name\",\n", "    \"Weight_KG\",\n", "    \"Dist_Sender_DS1\",\n", "    \"Speed_kmph\",\n", "    \"InTransitTime_Sender_DS1_min\",\n", "    \"Vehicle\",\n", "]\n", "\n", "aa = aa.reindex(columns=column_names)\n", "\n", "aa.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["appended_data = []\n", "for i in range(0, len(misses)):\n", "    single_runs = aa[\n", "        (aa[\"SenderFacilityID\"] == misses[i][0])\n", "        & (aa[\"DarkStore1_ID\"].isin(misses[i][1]))\n", "    ]\n", "    appended_data.append(single_runs)\n", "\n", "appended_data = pd.concat(appended_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### One to One (to <PERSON><PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1iA_aBwuyrilEDMebsHMcM4oNPxjk-C7_kJm06Cad_BA\"\n", "sheet_name = \"One to One\"\n", "pb.to_sheets(appended_data, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}