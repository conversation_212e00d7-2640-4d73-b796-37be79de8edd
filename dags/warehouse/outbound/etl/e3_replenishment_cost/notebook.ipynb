{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime\n", "from datetime import datetime, date\n", "import calendar"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### General"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month_mapping = {\n", "    \"Month_Name\": [\n", "        \"Jan\",\n", "        \"Feb\",\n", "        \"Mar\",\n", "        \"Apr\",\n", "        \"May\",\n", "        \"Jun\",\n", "        \"Jul\",\n", "        \"Aug\",\n", "        \"Sep\",\n", "        \"Oct\",\n", "        \"Nov\",\n", "        \"Dec\",\n", "    ],\n", "    \"Month_Number\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],\n", "}\n", "map_month = pd.DataFrame(month_mapping)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Month_31 = [1, 3, 5, 7, 8, 10, 12]\n", "Month_30 = [4, 6, 9, 11]\n", "Month_28 = [2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### E3 - outbound qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select BilledDate,\n", "        Receiver_OutletID,\n", "        co.name as Receiver_OutletName,\n", "        sum(B2B_DS_outbound) as B2B_DS_outbound\n", "from\n", "    (\n", "    select\n", "          date(pi.pos_timestamp + interval '5.5 hrs') as BilledDate,\n", "          case \n", "               when con.id in (1445,1637,1638) then 1445\n", "               when con.id in (1405,1675,1676) then 1676\n", "               when con.id in (1449,1699,1700) then 1700\n", "               else con.id end as Receiver_OutletID,\n", "          --con.name as Receiver_OutletName,\n", "          coalesce(sum(case when invoice_type_id in (5,14,16) and con.business_type_id = 7 then pd.quantity end),0) as B2B_DS_outbound\n", "    from lake_pos.pos_invoice pi\n", "    join lake_retail.console_outlet co on co.id = pi.outlet_id\n", "    join lake_crates.facility fc on fc.id = co.facility_id\n", "    join lake_pos.pos_invoice_product_details pd on pi.id = pd.invoice_id\n", "    left join lake_ims.ims_sto_details sto on pi.grofers_order_id = sto.sto_id\n", "    left join lake_retail.console_outlet con on con.id = sto.merchant_outlet_id\n", "    where date(pi.pos_timestamp + interval '5.5 hrs') between (current_date-31) and (current_date)\n", "    group by 1,2\n", "    having B2B_DS_outbound>0\n", "    order by 2,1\n", "    ) a\n", "join lake_retail.console_outlet co\n", "on a.Receiver_OutletID = co.id\n", "group by 1,2,3\n", "order by 2,1\n", "\"\"\"\n", "data = pd.read_sql_query(query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns, data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"billeddate\"] = pd.to_datetime(data[\"billeddate\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"receiver_outletid\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Master Sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "sheet_name = \"Master\"\n", "Master = pb.from_sheets(sheet_id, sheet_name)\n", "Master.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = Master.iloc[1]\n", "Master = Master[2:]\n", "Master.columns = new_header\n", "Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master[\"Outlet ID\"] = Master[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.rename(\n", "    columns={\n", "        \"Outlet ID\": \"outletid\",\n", "        \"Store Name\": \"StoreName\",\n", "        \"Store Type\": \"StoreType\",\n", "        \"sender FC for replesn\": \"SenderLocation\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master = Master[Master[\"outletid\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master[\"outletid\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = data.merge(\n", "    Master, how=\"outer\", left_on=[\"receiver_outletid\"], right_on=[\"outletid\"]\n", ")\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pandasql\n", "import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = ps.sqldf(\n", "    \"\"\"\n", "         SELECT coalesce(receiver_outletid,outletid) as receiver_outletid,\n", "                 billeddate,\n", "                 SenderLocation,\n", "                 sum(b2b_ds_outbound) as b2b_ds_outbound\n", "         FROM final\n", "         GROUP BY 1,2,3\n", "         \"\"\",\n", "    locals(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1[\"billeddate\"] = pd.to_datetime(final1[\"billeddate\"])\n", "final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1[final1[\"receiver_outletid\"] == 971]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replenishment Cost per unit - Components"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Manpower"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_Manpower PU\"\n", "ManpowerPU = pb.from_sheets(sheet_id, sheet_name)\n", "ManpowerPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = ManpowerPU.iloc[1]\n", "ManpowerPU = ManpowerPU[2:]\n", "ManpowerPU.columns = new_header\n", "ManpowerPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU[\"Month\"] = ManpowerPU[\"Month\"].astype(\"str\")\n", "ManpowerPU.dtypes\n", "\n", "new = ManpowerPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "ManpowerPU[\"MonthName\"] = new[0]\n", "ManpowerPU[\"Year\"] = new[1]\n", "\n", "ManpowerPU.drop(columns=[\"Month\"], inplace=True)\n", "ManpowerPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU = ManpowerPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "ManpowerPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if ManpowerPU[\"Month_Number\"].isin(Month_30).any():\n", "    ManpowerPU.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    ManpowerPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "ManpowerPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU = pd.melt(\n", "    ManpowerPU,\n", "    id_vars=[\"Sender FC \", \"Month_Number\", \"year\"],\n", "    value_vars=ManpowerPU.columns[1:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Manpower\",\n", ")\n", "\n", "ManpowerPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU[\"Date\"] = ManpowerPU[\"Date\"].astype(int)\n", "ManpowerPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU[\"dateInt\"] = (\n", "    ManpowerPU[\"year\"].astype(str)\n", "    + ManpowerPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + ManpowerPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "ManpowerPU[\"DateNew\"] = pd.to_datetime(ManpowerPU[\"dateInt\"], format=\"%Y%m%d\")\n", "ManpowerPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "ManpowerPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU[\"Manpower\"] = ManpowerPU[\"Manpower\"].apply(pd.to_numeric, errors=\"coerce\")\n", "ManpowerPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU = ManpowerPU.groupby([\"Sender FC \", \"DateNew\"]).agg(\"sum\")\n", "ManpowerPU = ManpowerPU.reset_index()\n", "ManpowerPU.tail(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU = ManpowerPU[ManpowerPU[\"Sender FC \"] != \"\"]\n", "ManpowerPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Rent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_Rent PU\"\n", "RentPU = pb.from_sheets(sheet_id, sheet_name)\n", "RentPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = RentPU.iloc[1]\n", "RentPU = RentPU[2:]\n", "RentPU.columns = new_header\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU[\"Month\"] = RentPU[\"Month\"].astype(\"str\")\n", "RentPU.dtypes\n", "\n", "new = RentPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "RentPU[\"MonthName\"] = new[0]\n", "RentPU[\"Year\"] = new[1]\n", "\n", "RentPU.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU = RentPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if RentPU[\"Month_Number\"].isin(Month_30).any():\n", "    RentPU.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    RentPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "RentPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU = pd.melt(\n", "    RentPU,\n", "    id_vars=[\n", "        \"Sender FC \",\n", "        \"Month_Number\",\n", "        \"year\",\n", "    ],\n", "    value_vars=RentPU.columns[1:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Rent\",\n", ")\n", "\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU[\"Date\"] = RentPU[\"Date\"].astype(int)\n", "RentPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU[\"dateInt\"] = (\n", "    RentPU[\"year\"].astype(str)\n", "    + RentPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + RentPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "RentPU[\"DateNew\"] = pd.to_datetime(RentPU[\"dateInt\"], format=\"%Y%m%d\")\n", "RentPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU.columns, RentPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU[[\"Rent\"]] = RentPU[[\"Rent\"]].apply(pd.to_numeric, errors=\"coerce\")\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU = RentPU.groupby([\"Sender FC \", \"DateNew\"]).sum()\n", "RentPU = RentPU.reset_index()\n", "RentPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RentPU = RentPU[RentPU[\"Sender FC \"] != \"\"]\n", "RentPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### G&A"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_G&A PU\"\n", "GnAPU = pb.from_sheets(sheet_id, sheet_name)\n", "GnAPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = GnAPU.iloc[1]\n", "GnAPU = GnAPU[2:]\n", "GnAPU.columns = new_header\n", "GnAPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU[\"Month\"] = GnAPU[\"Month\"].astype(\"str\")\n", "GnAPU.dtypes\n", "\n", "new = GnAPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "GnAPU[\"MonthName\"] = new[0]\n", "GnAPU[\"Year\"] = new[1]\n", "\n", "GnAPU.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU = GnAPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "GnAPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if GnAPU[\"Month_Number\"].isin(Month_30).any():\n", "    GnAPU.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    GnAPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "GnAPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU = pd.melt(\n", "    GnAPU,\n", "    id_vars=[\"Sender FC \", \"Month_Number\", \"year\"],\n", "    value_vars=GnAPU.columns[1:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"GnA\",\n", ")\n", "\n", "GnAPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU[\"Date\"] = GnAPU[\"Date\"].astype(int)\n", "GnAPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU[\"dateInt\"] = (\n", "    GnAPU[\"year\"].astype(str)\n", "    + GnAPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + GnAPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "GnAPU[\"DateNew\"] = pd.to_datetime(GnAPU[\"dateInt\"], format=\"%Y%m%d\")\n", "GnAPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "GnAPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU[[\"GnA\"]] = GnAPU[[\"GnA\"]].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU = GnAPU.groupby([\"Sender FC \", \"DateNew\"]).sum()\n", "GnAPU = GnAPU.reset_index()\n", "GnAPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnAPU = GnAPU[GnAPU[\"Sender FC \"] != \"\"]\n", "GnAPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### FC Onroll"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_Onroll PU\"\n", "OnrollPU = pb.from_sheets(sheet_id, sheet_name)\n", "OnrollPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = OnrollPU.iloc[1]\n", "OnrollPU = OnrollPU[2:]\n", "OnrollPU.columns = new_header\n", "OnrollPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU[\"Month\"] = OnrollPU[\"Month\"].astype(\"str\")\n", "OnrollPU.dtypes\n", "\n", "new = OnrollPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "OnrollPU[\"MonthName\"] = new[0]\n", "OnrollPU[\"Year\"] = new[1]\n", "\n", "OnrollPU.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU = OnrollPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "OnrollPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if OnrollPU[\"Month_Number\"].isin(Month_30).any():\n", "    OnrollPU.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    OnrollPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "OnrollPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU = pd.melt(\n", "    OnrollPU,\n", "    id_vars=[\"Sender FC \", \"Month_Number\", \"year\"],\n", "    value_vars=OnrollPU.columns[1:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Onroll_Day\",\n", ")\n", "\n", "OnrollPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU[\"Date\"] = OnrollPU[\"Date\"].astype(int)\n", "OnrollPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU[\"dateInt\"] = (\n", "    OnrollPU[\"year\"].astype(str)\n", "    + OnrollPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + OnrollPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "OnrollPU[\"DateNew\"] = pd.to_datetime(OnrollPU[\"dateInt\"], format=\"%Y%m%d\")\n", "OnrollPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "OnrollPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU[[\"Onroll_Day\"]] = OnrollPU[[\"Onroll_Day\"]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU = OnrollPU.groupby([\"Sender FC \", \"DateNew\"]).sum()\n", "OnrollPU = OnrollPU.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OnrollPU = OnrollPU[OnrollPU[\"Sender FC \"] != \"\"]\n", "OnrollPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### FC Feeder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_Ee_Feeder Transp PU\"\n", "FeederPU = pb.from_sheets(sheet_id, sheet_name)\n", "FeederPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = FeederPU.iloc[1]\n", "FeederPU = FeederPU[2:]\n", "FeederPU.columns = new_header\n", "FeederPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU[\"Month\"] = FeederPU[\"Month\"].astype(\"str\")\n", "FeederPU.dtypes\n", "\n", "new = FeederPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "FeederPU[\"MonthName\"] = new[0]\n", "FeederPU[\"Year\"] = new[1]\n", "\n", "FeederPU.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU = FeederPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "FeederPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if FeederPU[\"Month_Number\"].isin(Month_30).any():\n", "    FeederPU.drop([\"31\"], axis=1, inplace=True)\n", "<PERSON><PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    FeederPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "FeederPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU = pd.melt(\n", "    FeederPU,\n", "    id_vars=[\"Sender FC \", \"Month_Number\", \"year\"],\n", "    value_vars=FeederPU.columns[3:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"<PERSON><PERSON><PERSON>\",\n", ")\n", "\n", "FeederPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU[\"Date\"] = FeederPU[\"Date\"].astype(int)\n", "FeederPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU[\"dateInt\"] = (\n", "    FeederPU[\"year\"].astype(str)\n", "    + FeederPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + FeederPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "FeederPU[\"DateNew\"] = pd.to_datetime(FeederPU[\"dateInt\"], format=\"%Y%m%d\")\n", "FeederPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "FeederPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU[[\"Feeder\"]] = FeederPU[[\"Feeder\"]].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU = FeederPU.groupby([\"Sender FC \", \"DateNew\"]).sum()\n", "FeederPU = FeederPU.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FeederPU = FeederPU[FeederPU[\"Sender FC \"] != \"\"]\n", "FeederPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3<PERSON> Cost"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1_x8YzMANrPMMTQ1jmMC9qHBvfKZ7R4NPaMhDOpYq5Z0\"\n", "sheet_name = \"FC_3PL Cost PU\"\n", "Cost_3PLPU = pb.from_sheets(sheet_id, sheet_name)\n", "Cost_3PLPU.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = Cost_3PLPU.iloc[1]\n", "Cost_3PLPU = Cost_3PLPU[2:]\n", "Cost_3PLPU.columns = new_header\n", "Cost_3PLPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.drop([\"\", \"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU[\"Month\"] = Cost_3PLPU[\"Month\"].astype(\"str\")\n", "Cost_3PLPU.dtypes\n", "\n", "new = Cost_3PLPU[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "Cost_3PLPU[\"MonthName\"] = new[0]\n", "Cost_3PLPU[\"Year\"] = new[1]\n", "\n", "Cost_3PLPU.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU = Cost_3PLPU.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "Cost_3PLPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if Cost_3PLPU[\"Month_Number\"].isin(Month_30).any():\n", "    Cost_3PLPU.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>st_3PLPU[\"Month_Number\"].isin(Month_28).any():\n", "    Cost_3PLPU.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "Cost_3PLPU[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.columns[1:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU = pd.melt(\n", "    Cost_3PLPU,\n", "    id_vars=[\"Sender FC \", \"Month_Number\", \"year\"],\n", "    value_vars=Cost_3PLPU.columns[1:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Cost_3PL\",\n", ")\n", "\n", "Cost_3PLPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU[\"Date\"] = Cost_3PLPU[\"Date\"].astype(int)\n", "Cost_3PLPU.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU[\"dateInt\"] = (\n", "    Cost_3PLPU[\"year\"].astype(str)\n", "    + Cost_3PLPU[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + Cost_3PLPU[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "Cost_3PLPU[\"DateNew\"] = pd.to_datetime(Cost_3PLPU[\"dateInt\"], format=\"%Y%m%d\")\n", "Cost_3PLPU.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "Cost_3PLPU.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU[[\"Cost_3PL\"]] = Cost_3PLPU[[\"Cost_3PL\"]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU = Cost_3PLPU.groupby([\"Sender FC \", \"DateNew\"]).sum()\n", "Cost_3PLPU = Cost_3PLPU.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Cost_3PLPU = Cost_3PLPU[Cost_3PLPU[\"Sender FC \"] != \"\"]\n", "Cost_3PLPU.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Final Merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU.shape, RentPU.shape, GnAPU.shape, OnrollPU.shape, FeederPU.shape, Cost_3PLPU.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ManpowerPU[\"Sender FC \"].unique(), RentPU[\"Sender FC \"].unique(), GnAPU[\n", "    \"Sender FC \"\n", "].unique(), OnrollPU[\"Sender FC \"].unique(), FeederPU[\n", "    \"Sender FC \"\n", "].unique(), Cost_3PLPU[\n", "    \"Sender FC \"\n", "].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = ManpowerPU.merge(RentPU, how=\"outer\", on=[\"Sender FC \", \"DateNew\"])\n", "temp1.head(2), temp1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp2 = temp1.merge(GnAPU, how=\"outer\", on=[\"Sender FC \", \"DateNew\"])\n", "temp2.head(2), temp2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp3 = temp2.merge(OnrollPU, how=\"outer\", on=[\"Sender FC \", \"DateNew\"])\n", "temp3.head(2), temp3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp4 = temp3.merge(FeederPU, how=\"outer\", on=[\"Sender FC \", \"DateNew\"])\n", "temp4.head(2), temp4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5 = temp4.merge(Cost_3PLPU, how=\"outer\", on=[\"Sender FC \", \"DateNew\"])\n", "temp5.head(2), temp5.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.rename(\n", "    columns={\n", "        \"Sender FC \": \"SenderFC\",\n", "        \"Manpower\": \"ManpowerCostPU\",\n", "        \"Rent\": \"RentPU\",\n", "        \"GnA\": \"GnA_PU\",\n", "        \"Onroll_Day\": \"OnrollCostPU\",\n", "        \"Feeder\": \"TransportCostPU\",\n", "        \"Cost_3PL\": \"Cost_3PL_PU\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5 = temp5.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.columns, temp5.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MERGE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp5.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final1.merge(\n", "    temp5,\n", "    how=\"outer\",\n", "    left_on=[\"billeddate\", \"SenderLocation\"],\n", "    right_on=[\"DateNew\", \"SenderFC\"],\n", ")\n", "final_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.drop([\"SenderFC\", \"DateNew\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df[\"ManpowerCost\"] = final_df[\"ManpowerCostPU\"] * final_df[\"b2b_ds_outbound\"]\n", "final_df[\"Rent\"] = final_df[\"RentPU\"] * final_df[\"b2b_ds_outbound\"]\n", "final_df[\"GnA\"] = final_df[\"GnA_PU\"] * final_df[\"b2b_ds_outbound\"]\n", "final_df[\"OnrollCost\"] = final_df[\"OnrollCostPU\"] * final_df[\"b2b_ds_outbound\"]\n", "final_df[\"TransportCost\"] = final_df[\"TransportCostPU\"] * final_df[\"b2b_ds_outbound\"]\n", "final_df[\"Cost_3PL\"] = final_df[\"Cost_3PL_PU\"] * final_df[\"b2b_ds_outbound\"]\n", "\n", "final_df.rename(columns={\"b2b_ds_outbound\": \"InboundQty\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.columns, final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"billeddate\",\n", "    \"receiver_outletid\",\n", "    \"InboundQty\",\n", "    \"SenderLocation\",\n", "    \"ManpowerCostPU\",\n", "    \"RentPU\",\n", "    \"GnA_PU\",\n", "    \"OnrollCostPU\",\n", "    \"TransportCostPU\",\n", "    \"Cost_3PL_PU\",\n", "    \"ManpowerCost\",\n", "    \"Rent\",\n", "    \"GnA\",\n", "    \"OnrollCost\",\n", "    \"TransportCost\",\n", "    \"Cost_3PL\",\n", "]\n", "\n", "final_df = final_df.reindex(columns=column_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df[final_df[\"receiver_outletid\"] == 971]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = final_df[final_df[\"receiver_outletid\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df[\"billeddate\"] = final_df[\"billeddate\"].fillna(\"9999-01-01\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"billeddate\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Billed date\",\n", "    },\n", "    {\n", "        \"name\": \"receiver_outletid\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Receiver Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"InboundQty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Inbound qty to E3\",\n", "    },\n", "    {\n", "        \"name\": \"SenderLocation\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Sender FC location\",\n", "    },\n", "    {\n", "        \"name\": \"ManpowerCostPU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Manpower cost per unit\",\n", "    },\n", "    {\n", "        \"name\": \"RentPU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Rent per unit\",\n", "    },\n", "    {\n", "        \"name\": \"GnA_PU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"GnA per unit\",\n", "    },\n", "    {\n", "        \"name\": \"OnrollCostPU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Onroll Cost per unit\",\n", "    },\n", "    {\n", "        \"name\": \"TransportCostPU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Feeder transport cost per unit\",\n", "    },\n", "    {\n", "        \"name\": \"Cost_3PL_PU\",\n", "        \"type\": \"float\",\n", "        \"description\": \"3PL cost per unit\",\n", "    },\n", "    {\n", "        \"name\": \"ManpowerCost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total Manpower cost\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON><PERSON>\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total Rent\",\n", "    },\n", "    {\n", "        \"name\": \"GnA\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total GnA\",\n", "    },\n", "    {\n", "        \"name\": \"OnrollCost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total Onroll Cost\",\n", "    },\n", "    {\n", "        \"name\": \"TransportCost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total Feeder transport cost\",\n", "    },\n", "    {\n", "        \"name\": \"Cost_3PL\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Total 3PL cost\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"E3_ReplenishmentCost\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"billeddate\", \"receiver_outletid\"],\n", "    \"sortkey\": [\"billeddate\"],\n", "    \"incremental_key\": \"billeddate\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Replenishment cost for E3s\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## End"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}