{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month_mapping = {\n", "    \"Month_Name\": [\n", "        \"Jan\",\n", "        \"Feb\",\n", "        \"Mar\",\n", "        \"Apr\",\n", "        \"May\",\n", "        \"Jun\",\n", "        \"Jul\",\n", "        \"Aug\",\n", "        \"Sep\",\n", "        \"Oct\",\n", "        \"Nov\",\n", "        \"Dec\",\n", "    ],\n", "    \"Month_Number\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],\n", "}\n", "map_month = pd.DataFrame(month_mapping)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Month_31 = [1, 3, 5, 7, 8, 10, 12]\n", "Month_30 = [4, 6, 9, 11]\n", "Month_28 = [2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manpower"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1zHg_HAJyy9fd1_20olq3GE_UIsneXpsyl6gO3iHSgRk\"\n", "sheet_name = \"Sheet1\"\n", "df = pb.from_sheets(sheet_id, sheet_name)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = df.iloc[1]\n", "df = df[2:]\n", "df.columns = new_header"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.drop([\"\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Role\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Role\"].replace(\n", "    {\n", "        \"BILLER\": \"<PERSON><PERSON>\",\n", "        \"PICKER\": \"Picker\",\n", "        \"Team leader\": \"Team Leader\",\n", "        \"Executive Admin.\": \"Executive Admin\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Role\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Month\"] = df[\"Month\"].astype(\"str\")\n", "df.dtypes\n", "\n", "new = df[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "df[\"MonthName\"] = new[0]\n", "df[\"Year\"] = new[1]\n", "\n", "df.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.merge(map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"])\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df[\"Month_Number\"].isin(Month_30).any():\n", "    df.drop([\"31\"], axis=1, inplace=True)\n", "elif df[\"Month_Number\"].isin(Month_28).any():\n", "    df.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "df[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns[6:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.melt(\n", "    df,\n", "    id_vars=[\n", "        \"Outlet ID\",\n", "        \"ES Name\",\n", "        \"Grofers Code\",\n", "        \"Location\",\n", "        \"Role\",\n", "        \"Month_Number\",\n", "        \"year\",\n", "    ],\n", "    value_vars=df.columns[6:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Attendance\",\n", ")\n", "\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Date\"] = df[\"Date\"].astype(int)\n", "df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"dateInt\"] = (\n", "    df[\"year\"].astype(str)\n", "    + df[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + df[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "df[\"DateNew\"] = pd.to_datetime(df[\"dateInt\"], format=\"%Y%m%d\")\n", "df.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Attendance\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Present = [\"P\", \"p\", \"N\"]\n", "Total = [\"P\", \"p\", \"WO\", \"Wo\", \"wo\", \"C-OFF\", \"C-Off\", \"W0\", \"W\", \"N\", \"H\"]\n", "NotConsider = [\"LEFT\", \"A\", \"Left\", \"NJ\", \"L\", \"a\", \"left\", \"\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_P = df[df[\"Attendance\"].isin(Present)]\n", "df_P.shape\n", "\n", "df_T = df[df[\"Attendance\"].isin(Total)]\n", "df_T.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_<PERSON><PERSON>head(2), df_<PERSON>.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_P = df_P.groupby([\"Outlet ID\", \"ES Name\", \"Location\", \"Role\", \"DateNew\"]).count()\n", "df_P = df_P.reset_index()\n", "df_P.drop([\"Grofers Code\"], axis=1, inplace=True)\n", "df_P.rename(columns={\"Attendance\": \"Manpower_Present\"}, inplace=True)\n", "\n", "df_T = df_T.groupby([\"Outlet ID\", \"ES Name\", \"Location\", \"Role\", \"DateNew\"]).count()\n", "df_T = df_T.reset_index()\n", "df_T.drop([\"Grofers Code\"], axis=1, inplace=True)\n", "df_T.rename(columns={\"Attendance\": \"Manpower_Total\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_<PERSON>.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_P.drop([\"ES Name\", \"Location\"], axis=1, inplace=True)\n", "df_<PERSON>.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_P = pd.pivot_table(\n", "    df_P,\n", "    index=[\"Outlet ID\", \"DateNew\"],\n", "    columns=[\"Role\"],\n", "    values=[\"Manpower_Present\"],\n", "    aggfunc=np.sum,\n", ")\n", "final_P.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_T = pd.pivot_table(\n", "    df_T,\n", "    index=[\"Outlet ID\", \"DateNew\"],\n", "    columns=[\"Role\"],\n", "    values=[\"Manpower_Total\"],\n", "    aggfunc=np.sum,\n", ")\n", "final_T.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_P.columns = final_P.columns.droplevel()\n", "final_P = final_P.reset_index()\n", "final_P.head(2)\n", "\n", "final_T.columns = final_T.columns.droplevel()\n", "final_T = final_T.reset_index()\n", "final_T.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_P.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_P.rename(\n", "    columns={\n", "        \"Assistant (Putter/Loader)\": \"Assistant (Putter/Loader) Present\",\n", "        \"Biller\": \"<PERSON><PERSON> Present\",\n", "        \"Executive - Store Operations\": \"Executive - Store Operations Present\",\n", "        \"Executive Admin\": \"Executive Admin Present\",\n", "        \"F&V Executive\": \"F&V Executive Present\",\n", "        \"FE\": \"FE Present\",\n", "        \"Picker\": \"Picker Present\",\n", "        \"Station Manager\": \"Station Manager Present\",\n", "        \"Team Leader\": \"Team Leader Present\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "final_T.rename(\n", "    columns={\n", "        \"Assistant (Putter/Loader)\": \"Assistant (Putter/Loader) Total\",\n", "        \"Biller\": \"Biller Total\",\n", "        \"Executive - Store Operations\": \"Executive - Store Operations Total\",\n", "        \"Executive Admin\": \"Executive Admin Total\",\n", "        \"F&V Executive\": \"F&V Executive Total\",\n", "        \"FE\": \"FE Total\",\n", "        \"Picker\": \"Picker Total\",\n", "        \"Station Manager\": \"Station Manager Total\",\n", "        \"Team Leader\": \"Team Leader Total\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Manpower = final_P.merge(final_T, how=\"inner\", on=[\"DateNew\", \"Outlet ID\"])\n", "Manpower[\"Outlet ID\"] = Manpower[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")\n", "Manpower.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Manpower.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Manpower.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "## Fleet Cost\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"18LJqS_lRmLBbR5OoqZcdiuo6kRGhpp0BhhkDPTWo4Yo\"\n", "\n", "sheet_name1 = \"North_HR-NCR/Delhi\"\n", "HR_NCR = pb.from_sheets(sheet_id, sheet_name1)\n", "\n", "sheet_name2 = \"North_UP-NCR/Delhi\"\n", "UP_NCR = pb.from_sheets(sheet_id, sheet_name2)\n", "\n", "sheet_name3 = \"East\"\n", "East = pb.from_sheets(sheet_id, sheet_name3)\n", "\n", "sheet_name4 = \"West\"\n", "West = pb.from_sheets(sheet_id, sheet_name4)\n", "\n", "sheet_name5 = \"South\"\n", "South = pb.from_sheets(sheet_id, sheet_name5)\n", "\n", "sheet_name6 = \"FNV\"\n", "FNV = pb.from_sheets(sheet_id, sheet_name6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_header = HR_NCR.iloc[1]\n", "HR_NCR = HR_NCR[2:]\n", "HR_NCR.columns = new_header\n", "\n", "new_header = UP_NCR.iloc[1]\n", "UP_NCR = UP_NCR[2:]\n", "UP_NCR.columns = new_header\n", "\n", "new_header = East.iloc[1]\n", "East = East[2:]\n", "East.columns = new_header\n", "\n", "new_header = West.iloc[1]\n", "West = West[2:]\n", "West.columns = new_header\n", "\n", "new_header = South.iloc[1]\n", "South = South[2:]\n", "South.columns = new_header\n", "\n", "new_header = FNV.iloc[1]\n", "FNV = FNV[2:]\n", "FNV.columns = new_header\n", "\n", "HR_NCR.columns, UP_NCR.columns, East.columns, West.columns, South.columns, FNV.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HR_NCR[\"Flag\"] = \"Non-FNV\"\n", "UP_NCR[\"Flag\"] = \"Non-FNV\"\n", "East[\"Flag\"] = \"Non-FNV\"\n", "West[\"Flag\"] = \"Non-FNV\"\n", "South[\"Flag\"] = \"Non-FNV\"\n", "FNV[\"Flag\"] = \"FNV\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = HR_NCR.append(UP_NCR).append(East).append(West).append(South).append(FNV)\n", "data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.drop(\n", "    [\n", "        \"\",\n", "        \"\",\n", "        \"Day 1\",\n", "        \"Day 2\",\n", "        \"Day 3\",\n", "        \"Day 4\",\n", "        \"Day 5\",\n", "        \"Day 6\",\n", "        \"Day 7\",\n", "        \"Day 8\",\n", "        \"Day 9\",\n", "        \"Day 10\",\n", "        \"Day 11\",\n", "        \"Day 12\",\n", "        \"Day 13\",\n", "        \"Day 14\",\n", "        \"Day 15\",\n", "        \"Day 16\",\n", "        \"Day 17\",\n", "        \"Day 18\",\n", "        \"Day 19\",\n", "        \"Day 20\",\n", "        \"Day 21\",\n", "        \"Day 22\",\n", "        \"Day 23\",\n", "        \"Day 24\",\n", "        \"Day 25\",\n", "        \"Day 26\",\n", "        \"Day 27\",\n", "        \"Day 28\",\n", "        \"Day 29\",\n", "        \"Day 30\",\n", "        \"Day 31\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[[\"Outlet ID\", \"Daily/trip Cost \"]] = data[[\"Outlet ID\", \"Daily/trip Cost \"]].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")\n", "\n", "data[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = data[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = data[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].multiply(\n", "    data[\"Daily/trip Cost \"], axis=\"index\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Month\"] = data[\"Month\"].astype(\"str\")\n", "data.dtypes\n", "\n", "new = data[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "data[\"MonthName\"] = new[0]\n", "data[\"Year\"] = new[1]\n", "\n", "data.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = data.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data[\"Month_Number\"].isin(Month_30).any():\n", "    data.drop([\"31\"], axis=1, inplace=True)\n", "elif data[\"Month_Number\"].isin(Month_28).any():\n", "    data.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "data[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns[4:-3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.drop([\"Vehicle Size\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.melt(\n", "    data,\n", "    id_vars=[\n", "        \"Function\",\n", "        \"Outlet ID\",\n", "        \"Fixed/Adhoc\",\n", "        \"Daily/trip Cost \",\n", "        \"Flag\",\n", "        \"Month_Number\",\n", "        \"year\",\n", "    ],\n", "    value_vars=data.columns[4:-3].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Fleet Cost\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Date\"] = data[\"Date\"].astype(int)\n", "data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"dateInt\"] = (\n", "    data[\"year\"].astype(str)\n", "    + data[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + data[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "data[\"DateNew\"] = pd.to_datetime(data[\"dateInt\"], format=\"%Y%m%d\")\n", "data.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns, data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"Function\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cold = [\"Express Store - Cold\"]\n", "NonFNV = [\n", "    \"First Mile\",\n", "    \"Express Store\",\n", "    \"Express Store - RVP\",\n", "    \"ES Setup - Infra\",\n", "    \"Express Store - Cold\",\n", "    \"IT\",\n", "]\n", "FNV = [\"Express Store - FNV\"]\n", "LM = [\"Last Mile\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FleetCost_Cold = data[(data[\"Function\"].isin(Cold)) & (data[\"Flag\"] == \"Non-FNV\")]\n", "FleetCost_NonFNV = data[(data[\"Function\"].isin(NonFNV)) & (data[\"Flag\"] == \"Non-FNV\")]\n", "FleetCost_FNV = data[(data[\"Function\"].isin(FNV)) | (data[\"Flag\"] == \"FNV\")]\n", "LastMile = data[data[\"Function\"].isin(LM)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FleetCost_Cold = FleetCost_Cold.groupby([\"Outlet ID\", \"DateNew\"]).sum()\n", "# FleetCost_Cold = FleetCost_Cold.reset_index()\n", "# FleetCost_Cold.head()\n", "\n", "FleetCost_NonFNV = FleetCost_NonFNV.groupby([\"Outlet ID\", \"DateNew\"]).sum()\n", "FleetCost_NonFNV = FleetCost_NonFNV.reset_index()\n", "FleetCost_NonFNV.head()\n", "\n", "FleetCost_FNV = FleetCost_FNV.groupby([\"Outlet ID\", \"DateNew\"]).sum()\n", "FleetCost_FNV = FleetCost_FNV.reset_index()\n", "FleetCost_FNV.head()\n", "\n", "LastMile = LastMile.groupby([\"Outlet ID\", \"DateNew\"]).sum()\n", "LastMile = LastMile.reset_index()\n", "LastMile.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FleetCost_NonFNV.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FleetCost_Cold.drop([\"Daily/trip Cost \"], axis=1, inplace=True)\n", "\n", "FleetCost_NonFNV.drop([\"Daily/trip Cost \"], axis=1, inplace=True)\n", "\n", "FleetCost_FNV.drop([\"Daily/trip Cost \"], axis=1, inplace=True)\n", "\n", "LastMile.drop([\"Daily/trip Cost \"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FleetCost_NonFNV[\"Flag\"] = \"FleetCost_NonFNV\"\n", "# FleetCost_Cold[\"Flag\"] = \"FleetCost_Cold\"\n", "FleetCost_FNV[\"Flag\"] = \"FleetCost_FNV\"\n", "LastMile[\"Flag\"] = \"FleetCost_LM\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fleet_LM_Cost = FleetCost_NonFNV.append(FleetCost_FNV).append(LastMile)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fleet_LM_Cost.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fleet_LM_Cost = pd.pivot_table(\n", "    Fleet_LM_Cost,\n", "    index=[\"Outlet ID\", \"DateNew\"],\n", "    columns=[\"Flag\"],\n", "    values=[\"Fleet Cost\"],\n", "    aggfunc=np.sum,\n", ")\n", "\n", "Fleet_LM_Cost.columns = Fleet_LM_Cost.columns.droplevel()\n", "\n", "Fleet_LM_Cost = Fleet_LM_Cost.reset_index()\n", "Fleet_LM_Cost.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fleet_LM_Cost = Fleet_LM_Cost.fillna(0)\n", "Fleet_LM_Cost.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "\n", "sheet_name1 = \"Rent\"\n", "Rent = pb.from_sheets(sheet_id, sheet_name1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent = Rent.iloc[1:]\n", "\n", "new_header = Rent.iloc[0]\n", "Rent = Rent[1:]\n", "Rent.columns = new_header\n", "\n", "Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.columns, Rent.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.drop(\n", "    [\"Rent Start Date\", \"\", \"\", \"\", \"\", \"\", \"\"],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.replace(\",\", \"\", regex=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Area \\n(Sq Ft)\",\n", "        \"Monthly rent\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = Rent[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Area \\n(Sq Ft)\",\n", "        \"Monthly rent\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.columns, Rent.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent[\"Month\"] = Rent[\"Month\"].astype(\"str\")\n", "Rent.dtypes\n", "\n", "new = Rent[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "Rent[\"MonthName\"] = new[0]\n", "Rent[\"Year\"] = new[1]\n", "\n", "Rent.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent = Rent.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if Rent[\"Month_Number\"].isin(Month_30).any():\n", "    Rent.drop([\"31\"], axis=1, inplace=True)\n", "el<PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    Rent.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "Rent[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.columns[6:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent = pd.melt(\n", "    Rent,\n", "    id_vars=[\n", "        \"Outlet ID\",\n", "        \"Store Name\",\n", "        \"City\",\n", "        \"Store Type\",\n", "        \"Area \\n(Sq Ft)\",\n", "        \"Monthly rent\",\n", "        \"Month_Number\",\n", "        \"year\",\n", "    ],\n", "    value_vars=Rent.columns[6:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Rent\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent[\"dateInt\"] = (\n", "    Rent[\"year\"].astype(str)\n", "    + Rent[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + Rent[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "Rent[\"DateNew\"] = pd.to_datetime(Rent[\"dateInt\"], format=\"%Y%m%d\")\n", "\n", "Rent.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.drop(\n", "    [\"Store Name\", \"City\", \"Store Type\", \"Area \\n(Sq Ft)\", \"Monthly rent\"],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent = Rent.fillna(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## G&A"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "\n", "sheet_name1 = \"G&A\"\n", "GnA = pb.from_sheets(sheet_id, sheet_name1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA = GnA.iloc[1:]\n", "\n", "new_header = GnA.iloc[0]\n", "GnA = GnA[1:]\n", "GnA.columns = new_header\n", "\n", "GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.columns, GnA.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.drop([\"\", \"\", \"\", \"G&A Start Date\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.columns, GnA.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.replace(\",\", \"\", regex=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Monthly G&A\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = GnA[\n", "    [\n", "        \"Outlet ID\",\n", "        \"Monthly G&A\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA[\"Month\"] = GnA[\"Month\"].astype(\"str\")\n", "GnA.dtypes\n", "\n", "new = GnA[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "GnA[\"MonthName\"] = new[0]\n", "GnA[\"Year\"] = new[1]\n", "\n", "GnA.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA = GnA.merge(map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"])\n", "GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if GnA[\"Month_Number\"].isin(Month_30).any():\n", "    GnA.drop([\"31\"], axis=1, inplace=True)\n", "elif <PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    GnA.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "GnA[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.columns[6:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GnA.drop(columns=[\"for Sep'21\\nno of days\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.columns[5:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA = pd.melt(\n", "    GnA,\n", "    id_vars=[\n", "        \"Outlet ID\",\n", "        \"Store Name\",\n", "        \"City\",\n", "        \"Store Type\",\n", "        \"Monthly G&A\",\n", "        \"Month_Number\",\n", "        \"year\",\n", "    ],\n", "    value_vars=GnA.columns[5:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"G&A\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA[\"dateInt\"] = (\n", "    GnA[\"year\"].astype(str)\n", "    + GnA[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + GnA[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "GnA[\"DateNew\"] = pd.to_datetime(GnA[\"dateInt\"], format=\"%Y%m%d\")\n", "\n", "GnA.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.drop([\"Store Name\", \"City\", \"Store Type\", \"Monthly G&A\"], axis=1, inplace=True)\n", "GnA.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mar<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "\n", "sheet_name1 = \"Margins\"\n", "Margins = pb.from_sheets(sheet_id, sheet_name1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fixed = Margins.iloc[0, 4]\n", "Fixed = Fixed.strip(\"%\")\n", "Fixed = np.array(Fixed, dtype=np.int64)\n", "Fixed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins = Margins.iloc[1:]\n", "\n", "new_header = Margins.iloc[0]\n", "Margins = Margins[1:]\n", "Margins.columns = new_header\n", "\n", "Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.dtypes, Margins.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.drop(columns=[\"\", \"\", \"\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols = [\n", "    \"1\",\n", "    \"2\",\n", "    \"3\",\n", "    \"4\",\n", "    \"5\",\n", "    \"6\",\n", "    \"7\",\n", "    \"8\",\n", "    \"9\",\n", "    \"10\",\n", "    \"11\",\n", "    \"12\",\n", "    \"13\",\n", "    \"14\",\n", "    \"15\",\n", "    \"16\",\n", "    \"17\",\n", "    \"18\",\n", "    \"19\",\n", "    \"20\",\n", "    \"21\",\n", "    \"22\",\n", "    \"23\",\n", "    \"24\",\n", "    \"25\",\n", "    \"26\",\n", "    \"27\",\n", "    \"28\",\n", "    \"29\",\n", "    \"30\",\n", "    \"31\",\n", "]\n", "Margins[cols] = Margins[cols].apply(lambda x: x.str.strip(\"%\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins[\"Outlet ID\"].replace(\n", "    {\n", "        \"1637\": \"1445\",\n", "        \"1638\": \"1445\",\n", "        \"1405\": \"1676\",\n", "        \"1675\": \"1676\",\n", "        \"1449\": \"1700\",\n", "        \"1699\": \"1700\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>[\n", "    [\n", "        \"Outlet ID\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = Margins[\n", "    [\n", "        \"Outlet ID\",\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].apply(\n", "    pd.to_numeric, errors=\"coerce\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "] = Margins[\n", "    [\n", "        \"1\",\n", "        \"2\",\n", "        \"3\",\n", "        \"4\",\n", "        \"5\",\n", "        \"6\",\n", "        \"7\",\n", "        \"8\",\n", "        \"9\",\n", "        \"10\",\n", "        \"11\",\n", "        \"12\",\n", "        \"13\",\n", "        \"14\",\n", "        \"15\",\n", "        \"16\",\n", "        \"17\",\n", "        \"18\",\n", "        \"19\",\n", "        \"20\",\n", "        \"21\",\n", "        \"22\",\n", "        \"23\",\n", "        \"24\",\n", "        \"25\",\n", "        \"26\",\n", "        \"27\",\n", "        \"28\",\n", "        \"29\",\n", "        \"30\",\n", "        \"31\",\n", "    ]\n", "].add(\n", "    Fixed, axis=\"index\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins[\"Month\"] = <PERSON><PERSON>[\"Month\"].astype(\"str\")\n", "Margins.dtypes\n", "\n", "new = Margins[\"Month\"].str.split(\"'\", n=1, expand=True)\n", "\n", "Margins[\"MonthName\"] = new[0]\n", "Margins[\"Year\"] = new[1]\n", "\n", "Margins.drop(columns=[\"Month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins = Margins.merge(\n", "    map_month, how=\"inner\", left_on=[\"MonthName\"], right_on=[\"Month_Name\"]\n", ")\n", "Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if Margins[\"Month_Number\"].isin(Month_30).any():\n", "    Margins.drop([\"31\"], axis=1, inplace=True)\n", "<PERSON><PERSON>[\"Month_Number\"].isin(Month_28).any():\n", "    Margins.drop([\"29\", \"30\", \"31\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.drop([\"MonthName\", \"Month_Name\", \"Year\"], axis=1, inplace=True)\n", "Margins[\"year\"] = 2021"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.columns[4:-2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins = pd.melt(\n", "    <PERSON><PERSON>,\n", "    id_vars=[\"Outlet ID\", \"Store Name\", \"City\", \"Store Type\", \"Month_Number\", \"year\"],\n", "    value_vars=Margins.columns[4:-2].values,\n", "    var_name=\"Date\",\n", "    value_name=\"Margin%\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins[\"dateInt\"] = (\n", "    Margins[\"year\"].astype(str)\n", "    + <PERSON><PERSON>[\"Month_Number\"].astype(str).str.zfill(2)\n", "    + <PERSON><PERSON>[\"Date\"].astype(str).str.zfill(2)\n", ")\n", "Margins[\"DateNew\"] = pd.to_datetime(<PERSON>gins[\"dateInt\"], format=\"%Y%m%d\")\n", "Margins.drop([\"dateInt\", \"Date\", \"Month_Number\", \"year\"], axis=1, inplace=True)\n", "Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.drop([\"Store Name\", \"City\", \"Store Type\"], axis=1, inplace=True)\n", "Margins.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Master"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1pzdIx0Ix5vEDaVcC1lHLavNWCV7t_E0ZZHZ4ZN-fNXY\"\n", "\n", "sheet_name1 = \"Master\"\n", "Master = pb.from_sheets(sheet_id, sheet_name1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master = Master.iloc[1:]\n", "\n", "new_header = Master.iloc[0]\n", "Master = Master[1:]\n", "Master.columns = new_header\n", "\n", "Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.columns\n", "Master.drop(columns=[\"\", \"\", \"Store Name\"], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master[\"Outlet ID\"] = Master[\"Outlet ID\"].apply(pd.to_numeric, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Master[\"Live Date\"] = Master[\"Live Date\"].apply(pd.to_datetime)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Master.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Merging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Manpower.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Fleet_LM_Cost.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Rent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GnA.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Margins.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from datetime import timedelta\n", "\n", "dt = datetime.datetime.today() - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "year = dt.year\n", "month = dt.month\n", "day = dt.day\n", "\n", "sd = datetime.datetime(year, month, 1)\n", "start_date = sd.date()\n", "\n", "ed = datetime.datetime(year, month, day)\n", "end_date = ed.date()\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mask = (Rent[\"DateNew\"] >= start_date) & (Rent[\"DateNew\"] <= end_date)\n", "Rent = Rent.loc[mask]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Temp1 = Master.merge(Rent, how=\"left\", on=[\"Outlet ID\"])\n", "Temp1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Temp2 = Temp1.merge(GnA, how=\"left\", on=[\"Outlet ID\", \"DateNew\"])\n", "Temp2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Temp3 = Temp2.merge(Fleet_LM_Cost, how=\"left\", on=[\"Outlet ID\", \"DateNew\"])\n", "Temp3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final = Temp3.merge(Manpower, how=\"left\", on=[\"Outlet ID\", \"DateNew\"])\n", "Final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1 = Final.merge(<PERSON><PERSON>, how=\"left\", on=[\"Outlet ID\", \"DateNew\"])\n", "Final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.rename(\n", "    columns={\n", "        \"Outlet ID\": \"OutletID\",\n", "        \"outlet_name\": \"OutletName\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.dtypes, Final1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1[\"DateNew\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1 = Final1[Final1[\"DateNew\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1 = Final1.<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.rename(columns={\"sender FC for replesn\": \"SenderLocation\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names_must = [\n", "    \"OutletID\",\n", "    \"DateNew\",\n", "    \"Assistant (Putter/Loader) Present\",\n", "    \"Biller Present\",\n", "    \"Executive - Store Operations Present\",\n", "    \"Executive Admin Present\",\n", "    \"F&V Executive Present\",\n", "    \"FE Present\",\n", "    \"Picker Present\",\n", "    \"Station Manager Present\",\n", "    \"Team Leader Present\",\n", "    \"Assistant (Putter/Loader) Total\",\n", "    \"Biller Total\",\n", "    \"Executive - Store Operations Total\",\n", "    \"Executive Admin Total\",\n", "    \"F&V Executive Total\",\n", "    \"FE Total\",\n", "    \"Picker Total\",\n", "    \"Station Manager Total\",\n", "    \"Team Leader Total\",\n", "    \"FleetCost_FNV\",\n", "    \"FleetCost_LM\",\n", "    \"FleetCost_NonFNV\",\n", "    \"Rent\",\n", "    \"G&A\",\n", "    \"Margin%\",\n", "    \"City\",\n", "    \"Store Type\",\n", "    \"SenderLocation\",\n", "    \"Live Date\",\n", "]\n", "\n", "column_names_present = Final1.columns\n", "\n", "for column_names in column_names_must:\n", "    if column_names not in Final1.columns:\n", "        Final1[column_names] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_names = [\n", "    \"OutletID\",\n", "    \"DateNew\",\n", "    \"Assistant (Putter/Loader) Present\",\n", "    \"Biller Present\",\n", "    \"Executive - Store Operations Present\",\n", "    \"Executive Admin Present\",\n", "    \"F&V Executive Present\",\n", "    \"FE Present\",\n", "    \"Picker Present\",\n", "    \"Station Manager Present\",\n", "    \"Team Leader Present\",\n", "    \"Assistant (Putter/Loader) Total\",\n", "    \"Biller Total\",\n", "    \"Executive - Store Operations Total\",\n", "    \"Executive Admin Total\",\n", "    \"F&V Executive Total\",\n", "    \"FE Total\",\n", "    \"Picker Total\",\n", "    \"Station Manager Total\",\n", "    \"Team Leader Total\",\n", "    \"FleetCost_FNV\",\n", "    \"FleetCost_LM\",\n", "    \"FleetCost_NonFNV\",\n", "    \"Rent\",\n", "    \"G&A\",\n", "    \"Margin%\",\n", "    \"City\",\n", "    \"Store Type\",\n", "    \"SenderLocation\",\n", "    \"Live Date\",\n", "]\n", "\n", "Final1 = Final1.reindex(columns=column_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1[\"Live Date\"] = Final1[\"Live Date\"].apply(pd.to_datetime)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Flat table - to redash"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"OutletID\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Outlet ID\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON><PERSON><PERSON>\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Date\",\n", "    },\n", "    {\n", "        \"name\": \"Assistant (Putter/Loader) Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Assistant (Putter/Loader) Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON><PERSON>\",\n", "        \"type\": \"float\",\n", "        \"description\": \"<PERSON><PERSON> headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Executive - Store Operations Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Executive - Store Operations Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Executive Admin Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Executive Admin Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"F&V Executive Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"F&V Executive Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"FE Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"FE Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON><PERSON> Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"<PERSON><PERSON> Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Station Manager Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Station Manager Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Team Leader Present\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Team Leader Present headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Assistant (Putter/Loader) Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Assistant (Putter/Loader) Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Biller Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"<PERSON><PERSON> Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Executive - Store Operations Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Executive - Store Operations Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Executive Admin Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Executive Admin Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"F&V Executive Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"F&V Executive Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"FE Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"FE Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Picker Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Picker Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Station Manager Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Station Manager Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"Team Leader Total\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Team Leader Total headcount\",\n", "    },\n", "    {\n", "        \"name\": \"FleetCost_FNV\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Fleet Cost for FNV\",\n", "    },\n", "    {\n", "        \"name\": \"FleetCost_LM\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Last mile fleet cost\",\n", "    },\n", "    {\n", "        \"name\": \"FleetCost_NonFNV\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Fleet Cost for Non-FNV\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON><PERSON>\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Rent\",\n", "    },\n", "    {\n", "        \"name\": \"G&A\",\n", "        \"type\": \"float\",\n", "        \"description\": \"G&A\",\n", "    },\n", "    {\n", "        \"name\": \"Margin%\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Margins\",\n", "    },\n", "    {\n", "        \"name\": \"City\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Name of the outlet\",\n", "    },\n", "    {\n", "        \"name\": \"Store Type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Name of the outlet\",\n", "    },\n", "    {\n", "        \"name\": \"SenderLocation\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Sender Location of the outlet for replenishment\",\n", "    },\n", "    {\n", "        \"name\": \"Live Date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Live date of the outlet\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"E3_Cost\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"OutletID\", \"DateNew\"],\n", "    \"sortkey\": [\"OutletID\"],\n", "    \"incremental_key\": \"DateNew\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Cost metrics mapped to an outlet\",  # Description of the table being sent to redshift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(Final1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}