{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "# START_DATE = '2020-06-12'\n", "# END_DATE = '2020-06-12'\n", "\n", "today = date.today()\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "START_DATE = (datetime.now(tz) - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "END_DATE = (datetime.now(tz) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "START_DATE1 = (datetime.now(tz) - timed<PERSON>ta(days=3)).strftime(\"%Y-%m-%d\")\n", "END_DATE1 = (datetime.now(tz) - timed<PERSON>ta(days=0)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["START_DATE, END_DATE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "item_category_query = \"\"\"\n", "SELECT distinct\n", "pp.item_id,\n", "pp.variant_id,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID\n", "/*\n", "ipm.product_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID,\n", "CAT2.NAME AS l2,\n", "CAT2.ID AS l2_ID,\n", "pb.name as brand,\n", "pb.id as brand_id\n", "P.ENABLED_FLAG*/\n", "FROM\n", "lake_cms.GR_PRODUCT P\n", "INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1 \n", "\"\"\"\n", "\n", "item_category_df = pd.read_sql_query(sql=item_category_query, con=redshift_con)\n", "item_category_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with base_table as\n", "(\n", "select distinct\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_start * interval '1 second')) as slot_start,\n", "    convert_timezone('asia/kolkata',(timestamp 'epoch' + oo.slot_end * interval '1 second')) as slot_end,\n", "    oo.id as order_id,\n", "    case when soi.suborder_id is null then so2.id else soi.suborder_id end as suborder_id,\n", "    ooi.product_id as product_id,\n", "    ooi.id as order_item_id,\n", "    ooi.quantity as ordered_quantity,\n", "    ooi.procured_quantity as procured_quantity  \n", "from \n", "(\n", "select distinct\n", "    order_id\n", "from lake_oms_bifrost.oms_suborder os\n", "left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "  and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", ") as os\n", "inner join lake_oms_bifrost.oms_order as oo on os.order_id = oo.id\n", "and oo.type in ('RetailForwardOrder','InternalForwardOrder')\n", "and oo.current_status = 'DELIVERED'\n", "inner join lake_oms_bifrost.oms_order_item as ooi on oo.id = ooi.order_id\n", "inner join lake_oms_bifrost.oms_merchant as om on oo.merchant_id = om.id \n", "--and  om.virtual_merchant_type = 'superstore_merchant'\n", "left join lake_oms_bifrost.oms_suborder_item soi on soi.order_item_id = ooi.id\n", "left join lake_oms_bifrost.oms_suborder so on so.id=soi.suborder_id\n", "left join (select order_id, max(id) as id from lake_oms_bifrost.oms_suborder \n", "where   (slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "        and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", "    and type = 'RetailSuborder'\n", "  and current_status in ('AT_DELIVERY_CENTER','BILLED')\n", "group by 1 ) so2 on oo.id=so2.order_id\n", "),\n", "<PERSON><PERSON>ey as (Select bt.*,\n", "                 oic.reason_key\n", "from base_table bt\n", "left join lake_oms_bifrost.oms_order_item_cancellation oic ON bt.order_item_id = oic.order_item_id\n", "),\n", "Final as (\n", "Select ReasonKey.*,o.id as Outlet_id,o.name as Outlet_name,o.facility_id as Facility_id,f.name as facilityname, om.city_name as city\n", "from ReasonKey \n", "left join lake_oms_bifrost.oms_suborder so1 on so1.id = ReasonKey.suborder_id\n", "left join lake_oms_bifrost.oms_merchant m on so1.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on ReasonKey.order_id = oo.id\n", "left join lake_oms_bifrost.oms_merchant om on om.id = oo.merchant_id\n", ")\n", "Select * from Final \n", "--where (reason_key is not null and reason_key!='None') or procured_quantity<ordered_quantity\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_sql_query(\n", "    sql=query, con=redshift, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df.facilityname.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"reason_key\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\n", "    df[\"reason_key\"].apply(\n", "        lambda x: x\n", "        in [\n", "            \"ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING\",\n", "            \"ITEM_CANCEL_INVENTORY_CHECK_AT_APPROVAL\",\n", "            \"ITEM_CANCEL_INVENTORY_CHECK_AT_REPROCURE\",\n", "            \"ITEM_CANCEL_COIN_DEAL_INVALID_TRANSACTION\",\n", "        ]\n", "    )\n", "].order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["round(\n", "    (\n", "        df[df[\"procured_quantity\"] < df[\"ordered_quantity\"]].order_id.nunique()\n", "        / df.order_id.nunique()\n", "    )\n", "    * 100,\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"outlet_name\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = df[\"procured_quantity\"] < df[\"ordered_quantity\"]\n", "df[\"unfulfilled_order\"] = X.apply(lambda x: int(x))\n", "# df=df[df['reason_key']=='ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# X=df['procured_quantity']<df['ordered_quantity']\n", "# df['unfulfilled_order']=X.apply(lambda x: int(x))\n", "\n", "Order_df = df.groupby([\"city\", \"order_id\"], as_index=False).agg(\n", "    {\"unfulfilled_order\": max}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_df = Order_df.groupby(\"city\", as_index=False).agg(\n", "    {\"order_id\": \"nunique\", \"unfulfilled_order\": sum}\n", ")\n", "Order_df[\"Fill Rate\"] = round(\n", "    (Order_df[\"unfulfilled_order\"] / Order_df[\"order_id\"]) * 100, 2\n", ")\n", "Order_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_df.unfulfilled_order.sum() / Order_df.order_id.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[df[\"reason_key\"] == \"ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING\"].isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.order_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df.to_csv(\"Data.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "it_pd_log_query = \"\"\"\n", "select  \n", "x.product_id::int, item_id::int ,multiplier  \n", "from \n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.it_pd_log i \n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\n", "\"\"\"\n", "\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["it_pd_log_df[it_pd_log_df[\"product_id\"] == 424299]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final = pd.merge(\n", "    left=df,\n", "    right=it_pd_log_df,\n", "    how=\"inner\",\n", "    left_on=[\"product_id\"],\n", "    right_on=[\"product_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(Final[Final[\"suborder_id\"] == 0].product_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final[\"key\"] = Final[\"order_item_id\"].astype(str) + \"|\" + Final[\"item_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(Final)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"FillRateAttribution\",\n", "#     \"column_dtypes\": column_dtypes,\n", "#     \"primary_key\": [\"key\"],\n", "#     \"sortkey\": [\"outlet_id\"],\n", "#     \"incremental_key\": \"slot_end\",\n", "#     \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "# }\n", "# pb.to_redshift(Final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_redshift(Final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping.rename(columns={'sub_order_id':'NewOrder'},inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping2 = Final[['sub_order_id','outlet_id','facilityname','facility_id']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping2.rename(columns={'sub_order_id':'NewOrder2',\n", "#                         'outlet_id':'outlet_id1',\n", "#                         'facilityname':'facilityname1',\n", "#                          'facility_id':'facility_id1'\n", "#                         },inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final1 = Final.merge(Mapping, on='order_id', how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final2 = Final1.merge(Mapping2, how='left', left_on='NewOrder', right_on='NewOrder2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final2.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final.to_csv(\"FillRateOrderLevel.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> and <PERSON>sche<PERSON>le and Metering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"SELECT T2.* , o.name as outlet_name, l.id as city_id, l.name as city_name FROM\n", "(\n", "SELECT T1.*, ul.`fnv_upper_limit`, ul.`upper_limit` , ul.`feeder_live`\n", "\n", "FROM\n", "(\n", "SELECT T.*, il.item_id , il.actual_quantity , il.blocked_quantity  FROM\n", "\n", "(\n", "SELECT od.outlet as outlet_id , od.order_id , od.ancestor, \n", "date(convert_tz(od.scheduled_at,\"+00:00\",\"+05:30\")) as scheduled_at, \n", "od.status_id as order_status_id, \n", "ol.id,\n", "date(convert_tz(ol.created_at,\"+00:00\",\"+05:30\")) as created_at,\n", "\n", "ol.description, \n", "ol.status_id as log_status_id, \n", "date(convert_tz(ol.old_scheduled_at,\"+00:00\",\"+05:30\")) as old_scheduled_at\n", "\n", "FROM ims.ims_order_details od\n", "inner join ims.ims_order_log ol on od.order_id = ol.order_id\n", "\n", "where date(od.scheduled_at)  between %(start_date)s and %(end_date)s and ol.`description` in ('Reprocure','Created') \n", "\n", ")T\n", "\n", "inner join ims.ims_item_inventory_log il on T.id = il.order_log_id\n", "where il.actual_quantity < il.blocked_quantity\n", ") T1\n", "\n", "inner join ims.ims_item_inventory ul on T1.item_id = ul.item_id and T1.outlet_id = ul.outlet_id \n", ") T2 \n", "\n", "inner join retail.console_outlet o on T2.outlet_id = o.id \n", "inner join retail.console_location l on o.tax_location_id = l.id\n", "\"\"\"\n", "##"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log = pd.read_sql_query(\n", "    sql=Query, con=retail, params={\"start_date\": START_DATE1, \"end_date\": END_DATE1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log[\"key\"] = Order_log.ancestor.astype(str) + \"|\" + Order_log.item_id.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(Order_log[\"key\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log_Fil = Order_log.groupby([\"ancestor\", \"item_id\"], as_index=False).agg(\n", "    {\"description\": max, \"fnv_upper_limit\": max, \"upper_limit\": max}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log_Fil.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Order_log_Fil.ancestor = Order_log_Fil.ancestor.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1 = pd.merge(\n", "    Final,\n", "    Order_log_Fil,\n", "    how=\"left\",\n", "    left_on=[\"order_id\", \"item_id\"],\n", "    right_on=[\"ancestor\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1[~Final1[\"description\"].isnull()].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final1.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FnV Items "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QueryFnv = \"\"\"Select distinct item_id,max(outlet_type) as outlet_type from lake_rpc.product_product where outlet_type=1 group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FnV_Data = pd.read_sql_query(sql=QueryFnv, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FnV_Data = FnV_Data[[\"item_id\", \"outlet_type\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final2 = Final1.merge(FnV_Data, how=\"left\", on=\"item_id\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON> <PERSON><PERSON> "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"Select order_id,event_type_key,max(oe.install_ts) as EditTime from \n", "            lake_oms_bifrost.oms_order_event oe left join lake_oms_bifrost.oms_order oo on oo.id = oe.order_id\n", "           where event_type_key='order_item_edit_v2' and (timestamp 'epoch' + cast(oo.slot_start as int) * interval '1 second' + interval '5.5 hour')::date\n", "           between %(start_date)s and %(end_date)s\n", "           group by 1,2\n", "           \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OMSEditCart = pd.read_sql_query(\n", "    sql=Query, con=redshift_con, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OMSEditCart.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final3 = Final2.merge(OMSEditCart, how=\"left\", on=\"order_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final3.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Picking Time "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "With Base as (\n", "Select id from lake_oms_bifrost.oms_suborder where\n", "(slot_start between extract(epoch from (%(start_date)s::timestamp - interval '5.5 hour')) \n", "and extract(epoch from (%(end_date)s::timestamp + interval '1 day' - interval '5.5 hour')))\n", ")\n", "\n", "SELECT Base.id,plog.pos_timestamp as Picking_time\n", "From Base\n", "JOIN lake_warehouse_location.warehouse_pick_list_order_mapping plo on Base.id = plo.order_id\n", "JOIN lake_warehouse_location.pick_list_log plog on plog.pick_list_id=plo.pick_list_id\n", "where plog.picklist_state=4 \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Picking_Time = pd.read_sql_query(\n", "    sql=Query, con=redshift_con, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Picking_Time.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4 = Final3.merge(Picking_Time, how=\"left\", left_on=\"suborder_id\", right_on=\"id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4[\"EditCart\"] = Final4[\"picking_time\"] < Final4[\"edittime\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4[\"picking_time\"].fillna(datetime.now(), inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bad Inventory Update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4.outlet_id = Final4.outlet_id.fillna(0)\n", "Final4.outlet_id = Final4.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "select  date(ul.created_at) as BadStockDate,\n", "        ul.outlet_id,\n", "        ul.variant_id ,\n", "        rpc.item_id,\n", "        sum(\"delta\")\n", "from  lake_ims.ims_bad_inventory_update_log ul left join lake_rpc.product_product rpc\n", "on ul.variant_id=rpc.variant_id\n", "where ul.created_at between %(start_date)s and %(end_date)s\n", "group by 1,2,3,4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Bad_Inventory = pd.read_sql_query(\n", "    sql=Query,\n", "    con=redshift_con,\n", "    params={\"start_date\": START_DATE1, \"end_date\": END_DATE1},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Bad_Inventory.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final4[\"PickingDate\"] = Final4[\"picking_time\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final5 = pd.merge(\n", "    Final4,\n", "    Bad_Inventory,\n", "    left_on=[\"outlet_id\", \"item_id\", \"PickingDate\"],\n", "    right_on=[\"outlet_id\", \"item_id\", \"badstockdate\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Bad_Inventory.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inventory Sanity Issue "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query = \"\"\"select facility_id,\n", "#        item_id,\n", "#        order_date::date,\n", "#        sum(actual_quantity)-sum(blocked_quantity) as available_quantity,\n", "#        max(active_flag) as active_flag\n", "# from consumer.rpc_daily_availability\n", "# where order_date::date between %(start_date)s and %(end_date)s\n", "# group by 1,2,3\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# IMS_Inventory = pd.read_sql_query(\n", "#     sql=Query, con=redshift_con, params={\"start_date\": START_DATE, \"end_date\": END_DATE}\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final6 = pd.merge(\n", "#     Final5,\n", "#     IMS_Inventory,\n", "#     left_on=[\"facility_id\", \"item_id\", \"PickingDate\"],\n", "#     right_on=[\"facility_id\", \"item_id\", \"order_date\"],\n", "#     how=\"left\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# IMS_Inventory.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final6.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Final6.shape\n", "# Final6.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final5.EditCart = Final5.EditCart.astype(int)\n", "Final5.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final6.<PERSON><PERSON>(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final5.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final5.product_id = Final5.product_id.astype(float)\n", "Final5.ancestor = Final5.ancestor.astype(float)\n", "Final5.fnv_upper_limit = Final5.fnv_upper_limit.astype(float)\n", "Final5.upper_limit = Final5.upper_limit.astype(float)\n", "Final5[\"sum\"] = Final5[\"sum\"].astype(float)\n", "Final5.procured_quantity = Final5.procured_quantity.astype(float)\n", "# Final5.ordered_quantity = Final5.ordered_quantity.astype(float)\n", "Final5.upper_limit = Final5.upper_limit.astype(float)\n", "# Final6.available_quantity= Final6.available_quantity.astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"reason_key\": \"L1 Reason for fill rate issues stored in oms_order_item_cancellation\",\n", "    \"unfulfilled_order\": \"whether items was unfulfilled or not 1 or 0\",\n", "    \"description\": \"Description in ims_order_log indicating nayala or reschedule\",\n", "    \"fnv_upper_limit\": \"whether item is on FnV upper limit\",\n", "    \"upper_limit\": \"whether item is on metering\",\n", "    \"outlet_type\": \"Type of Outlet indicating FnV or Regular Items \",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Final5[\"key\"] = (\n", "    Final5[\"order_item_id\"].astype(str) + \"|\" + Final5[\"item_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(Final5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"FillRateAttribution\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"slot_end\",\n", "    \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert,\n", "    \"table_description\": \"stores fill rate data at order item level and there reasons for being unfulfilled\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(Final5, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query=\"\"\"Select * from metrics.FillRateAttribution limit 1000\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data = pd.read_sql_query(\n", "#     sql=Query, con=redshift\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final5.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}