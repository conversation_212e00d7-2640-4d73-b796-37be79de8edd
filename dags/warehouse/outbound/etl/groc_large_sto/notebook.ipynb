{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, date, timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5)).date()\n", "\n", "d = datetime.strftime(start_date, \"%Y-%m-%d\")\n", "\n", "groc_large_items = [\n", "    (\n", "        363,\n", "        789,\n", "        (\n", "            10032109,\n", "            10044233,\n", "            10045966,\n", "            10045970,\n", "            10046004,\n", "            10007372,\n", "            10000910,\n", "            10006982,\n", "            10045203,\n", "            10011367,\n", "            10000365,\n", "            10015381,\n", "            10025210,\n", "            10048245,\n", "            10005527,\n", "            10003239,\n", "            10010320,\n", "            10034890,\n", "            10016712,\n", "            10005320,\n", "            10045065,\n", "            10025189,\n", "            10005318,\n", "            10028596,\n", "            10001430,\n", "            10026520,\n", "            10001390,\n", "            10037999,\n", "            10039482,\n", "            10036711,\n", "            10032704,\n", "            10035386,\n", "            10036611,\n", "            10019017,\n", "            10034603,\n", "            10035986,\n", "            10017936,\n", "            10043418,\n", "            10043739,\n", "            10044599,\n", "            10037897,\n", "            10047434,\n", "            10043738,\n", "            10049953,\n", "            10038289,\n", "            10027357,\n", "            10037915,\n", "            10046306,\n", "            10045154,\n", "            10049952,\n", "            10037903,\n", "            10050001,\n", "            10050566,\n", "            10046308,\n", "            10046982,\n", "            10046304,\n", "            10025659,\n", "            10037901,\n", "            10037907,\n", "            10045155,\n", "            10047252,\n", "            10045885,\n", "            10045019,\n", "            10045158,\n", "            10046310,\n", "            10050565,\n", "            10047446,\n", "            10046854,\n", "            10044600,\n", "            10045392,\n", "            10045244,\n", "            10046993,\n", "            10045750,\n", "            10037904,\n", "            10037906,\n", "            10044048,\n", "            10014865,\n", "            10047251,\n", "            10050695,\n", "            10034374,\n", "            10050696,\n", "            10047931,\n", "            10045156,\n", "            10045157,\n", "            10046229,\n", "            10045416,\n", "            10047437,\n", "            10050693,\n", "            10030009,\n", "            10050000,\n", "            10037909,\n", "            10049954,\n", "            10037949,\n", "            10046222,\n", "            10047435,\n", "            10047436,\n", "            10050694,\n", "            10045749,\n", "            10046230,\n", "            10036851,\n", "            10047883,\n", "            10047126,\n", "            10002198,\n", "            10005321,\n", "            10046781,\n", "            10050567,\n", "            10044602,\n", "            10014861,\n", "            10036613,\n", "            10047882,\n", "            10047249,\n", "            10047225,\n", "            10045251,\n", "            10046305,\n", "            10050564,\n", "            10043042,\n", "            10037902,\n", "            10047001,\n", "            10044607,\n", "            10015382,\n", "            10002146,\n", "            10013953,\n", "            10051373,\n", "            10010778,\n", "            10010131,\n", "            10000059,\n", "            10034886,\n", "            10013720,\n", "            10016541,\n", "            10044234,\n", "            10046000,\n", "            10044230,\n", "            10046006,\n", "            10005766,\n", "            10044604,\n", "        ),\n", "    ),\n", "    (\n", "        481,\n", "        792,\n", "        (\n", "            10046004,\n", "            10035859,\n", "            10032048,\n", "            10046000,\n", "            10035861,\n", "            10032047,\n", "            10045970,\n", "            10007372,\n", "            10000910,\n", "            10011367,\n", "            10048245,\n", "            10000365,\n", "            10045203,\n", "            10005527,\n", "            10003239,\n", "            10010320,\n", "            10034890,\n", "            10035986,\n", "            10012285,\n", "            10016547,\n", "            10043418,\n", "            10043739,\n", "            10044599,\n", "            10047434,\n", "            10043738,\n", "            10026520,\n", "            10036858,\n", "            10049953,\n", "            10050564,\n", "            10027357,\n", "            10045154,\n", "            10049952,\n", "            10047448,\n", "            10046308,\n", "            10044604,\n", "            10046982,\n", "            10045104,\n", "            10050172,\n", "            10037907,\n", "            10045155,\n", "            10047252,\n", "            10045251,\n", "            10001390,\n", "            10044602,\n", "            10045158,\n", "            10046310,\n", "            10035386,\n", "            10050565,\n", "            10045292,\n", "            10047446,\n", "            10046854,\n", "            10044600,\n", "            10050177,\n", "            10014861,\n", "            10046993,\n", "            10045750,\n", "            10037906,\n", "            10043042,\n", "            10014865,\n", "            10047251,\n", "            10050695,\n", "            10034374,\n", "            10050696,\n", "            10047882,\n", "            10047931,\n", "            10045156,\n", "            10046229,\n", "            10035465,\n", "            10050693,\n", "            10050173,\n", "            10030009,\n", "            10050000,\n", "            10001430,\n", "            10049954,\n", "            10046222,\n", "            10046305,\n", "            10045749,\n", "            10046230,\n", "            10047883,\n", "            10047126,\n", "            10044607,\n", "            10034603,\n", "            10037999,\n", "            10045885,\n", "            10050567,\n", "            10047249,\n", "            10046304,\n", "            10047437,\n", "            10047225,\n", "            10050694,\n", "            10047436,\n", "            10047435,\n", "            10050566,\n", "            10046306,\n", "            10036851,\n", "            10046781,\n", "            10046597,\n", "            10050174,\n", "            10037901,\n", "            10046853,\n", "            10037909,\n", "            10050176,\n", "            10037897,\n", "            10045392,\n", "            10047001,\n", "            10050001,\n", "            10009035,\n", "            10046504,\n", "            10028382,\n", "            10035463,\n", "            10046469,\n", "            10045995,\n", "            10002146,\n", "            10051373,\n", "            10046022,\n", "            10010778,\n", "            10000059,\n", "            10010131,\n", "            10034886,\n", "            10046006,\n", "            10005766,\n", "            10013720,\n", "            10016541,\n", "            10015521,\n", "            10044234,\n", "            10037949,\n", "        ),\n", "    ),\n", "    (\n", "        334,\n", "        834,\n", "        (\n", "            10000910,\n", "            10005673,\n", "            10005318,\n", "            10045203,\n", "            10007372,\n", "            10016712,\n", "            10005320,\n", "            10000365,\n", "            10005882,\n", "            10003239,\n", "            10015381,\n", "            10041072,\n", "            10048245,\n", "            10005527,\n", "            10025171,\n", "            10015216,\n", "            10024958,\n", "            10025189,\n", "            10034890,\n", "            10048244,\n", "            10041983,\n", "            10028385,\n", "            10041975,\n", "            10043646,\n", "            10041972,\n", "            10035986,\n", "            10024484,\n", "            10024485,\n", "            10043418,\n", "            10043739,\n", "            10043738,\n", "            10045158,\n", "            10045750,\n", "            10045885,\n", "            10045749,\n", "            10047001,\n", "            10001390,\n", "            10047249,\n", "            10030009,\n", "            10046310,\n", "            10037999,\n", "            10045157,\n", "            10046308,\n", "            10014861,\n", "            10045019,\n", "            10050694,\n", "            10050695,\n", "            10027357,\n", "            10045156,\n", "            10045392,\n", "            10046305,\n", "            10046781,\n", "            10050693,\n", "            10036711,\n", "            10038289,\n", "            10045155,\n", "            10046230,\n", "            10050176,\n", "            10050000,\n", "            10001430,\n", "            10034603,\n", "            10043173,\n", "            10046229,\n", "            10045154,\n", "            10046222,\n", "            10046306,\n", "            10047446,\n", "            10047448,\n", "            10049954,\n", "            10050001,\n", "            10050177,\n", "            10043042,\n", "            10044049,\n", "            10045251,\n", "            10046982,\n", "            10047252,\n", "            10047434,\n", "            10047883,\n", "            10049953,\n", "            10014865,\n", "            10026520,\n", "            10036851,\n", "            10037901,\n", "            10037904,\n", "            10045416,\n", "            10046597,\n", "            10046854,\n", "            10046993,\n", "            10047126,\n", "            10049952,\n", "            10037909,\n", "            10002146,\n", "            10044326,\n", "            10051373,\n", "            10041618,\n", "            10000059,\n", "            10010778,\n", "            10015382,\n", "            10037915,\n", "            10034886,\n", "            10037949,\n", "            10010131,\n", "            10037903,\n", "            10044602,\n", "            10044604,\n", "            10008261,\n", "            10034602,\n", "            10044599,\n", "            10044600,\n", "            10044601,\n", "            10044603,\n", "            10002322,\n", "            10037906,\n", "            10037907,\n", "            10034374,\n", "            10036912,\n", "            10008680,\n", "            10047882,\n", "            10015085,\n", "            10050249,\n", "            10050250,\n", "            10050251,\n", "            10050248,\n", "            10016544,\n", "            10016547,\n", "            10045047,\n", "        ),\n", "    ),\n", "    (\n", "        292,\n", "        840,\n", "        (\n", "            10008069,\n", "            10007372,\n", "            10000910,\n", "            10015381,\n", "            10000365,\n", "            10016712,\n", "            10008199,\n", "            10041072,\n", "            10005882,\n", "            10015216,\n", "            10016357,\n", "            10048245,\n", "            10003239,\n", "            10005527,\n", "            10045203,\n", "            10011367,\n", "            10025189,\n", "            10010320,\n", "            10045065,\n", "            10031874,\n", "            10050176,\n", "            10050177,\n", "            10035986,\n", "            10043418,\n", "            10043739,\n", "            10014861,\n", "            10043738,\n", "            10045158,\n", "            10045157,\n", "            10045749,\n", "            10001390,\n", "            10047001,\n", "            10045156,\n", "            10046310,\n", "            10045750,\n", "            10036711,\n", "            10046308,\n", "            10046230,\n", "            10046229,\n", "            10050566,\n", "            10027357,\n", "            10046305,\n", "            10046993,\n", "            10047249,\n", "            10045154,\n", "            10046781,\n", "            10050694,\n", "            10046854,\n", "            10047126,\n", "            10046306,\n", "            10047434,\n", "            10045885,\n", "            10046597,\n", "            10050001,\n", "            10037904,\n", "            10047800,\n", "            10049953,\n", "            10036851,\n", "            10036858,\n", "            10037907,\n", "            10045155,\n", "            10045251,\n", "            10046222,\n", "            10047435,\n", "            10050172,\n", "            10050693,\n", "            10050695,\n", "            10050564,\n", "            10050565,\n", "            10050567,\n", "            10046853,\n", "            10047225,\n", "            10047252,\n", "            10047797,\n", "            10049954,\n", "            10014865,\n", "            10037906,\n", "            10038289,\n", "            10045392,\n", "            10046982,\n", "            10047931,\n", "            10049952,\n", "            10050175,\n", "            10001430,\n", "            10030009,\n", "            10045416,\n", "            10047882,\n", "            10047883,\n", "            10002146,\n", "            10015382,\n", "            10050623,\n", "            10037755,\n", "            10051373,\n", "            10041618,\n", "            10050628,\n", "            10037562,\n", "            10021993,\n", "            10010131,\n", "            10000059,\n", "            10013720,\n", "            10029488,\n", "            10037949,\n", "            10016541,\n", "            10044602,\n", "            10044599,\n", "            10044604,\n", "            10005766,\n", "            10029629,\n", "            10028290,\n", "            10008261,\n", "            10050173,\n", "            10050174,\n", "            10047446,\n", "            10047448,\n", "            10050000,\n", "            10031345,\n", "            10029733,\n", "            10029725,\n", "            10037764,\n", "            10029734,\n", "            10007518,\n", "            10025164,\n", "            10025171,\n", "            10025187,\n", "            10034708,\n", "            10034712,\n", "            10008077,\n", "            10005318,\n", "            10001428,\n", "            10001425,\n", "            10037999,\n", "            10002205,\n", "            10044240,\n", "            10008070,\n", "            10017611,\n", "            10016040,\n", "            10016039,\n", "            10047785,\n", "            10030235,\n", "            10030238,\n", "            10030240,\n", "            10018056,\n", "            10021994,\n", "            10025659,\n", "            10047251,\n", "            10047436,\n", "            10047437,\n", "            10002958,\n", "            10005321,\n", "            10017458,\n", "            10002349,\n", "            10048136,\n", "            10004529,\n", "            10005577,\n", "            10050696,\n", "            10049887,\n", "            10045439,\n", "            10048133,\n", "            10044050,\n", "            10044049,\n", "            10045191,\n", "            10044048,\n", "            10044051,\n", "            10045161,\n", "            10010778,\n", "            10034886,\n", "            10034374,\n", "            10034890,\n", "            10028596,\n", "            10033451,\n", "            10006863,\n", "            10010605,\n", "            10027358,\n", "            10017504,\n", "            10017503,\n", "            10032148,\n", "            10043742,\n", "            10027359,\n", "            10046307,\n", "            10012329,\n", "            10032614,\n", "            10043646,\n", "            10050272,\n", "            10050273,\n", "            10050289,\n", "            10050294,\n", "            10050295,\n", "            10050275,\n", "            10050276,\n", "            10050277,\n", "            10043173,\n", "            10043174,\n", "            10047884,\n", "            10047885,\n", "            10039754,\n", "            10029684,\n", "            10029681,\n", "            10033243,\n", "            10033238,\n", "            10032606,\n", "            10032609,\n", "            10050249,\n", "            10050250,\n", "            10050251,\n", "        ),\n", "    ),\n", "    (\n", "        287,\n", "        825,\n", "        (\n", "            10005882,\n", "            10007372,\n", "            10000910,\n", "            10015517,\n", "            10005320,\n", "            10005673,\n", "            10015516,\n", "            10045203,\n", "            10005318,\n", "            10015518,\n", "            10025189,\n", "            10000365,\n", "            10015381,\n", "            10003239,\n", "            10005527,\n", "            10048245,\n", "            10011367,\n", "            10010320,\n", "            10034890,\n", "            10027385,\n", "            10037945,\n", "            10035986,\n", "            10043418,\n", "            10043739,\n", "            10049918,\n", "            10037897,\n", "            10043738,\n", "            10046781,\n", "            10036858,\n", "            10038289,\n", "            10050564,\n", "            10027357,\n", "            10037903,\n", "            10047448,\n", "            10050001,\n", "            10050566,\n", "            10046308,\n", "            10046982,\n", "            10025659,\n", "            10037907,\n", "            10045155,\n", "            10047252,\n", "            10046310,\n", "            10045251,\n", "            10045019,\n", "            10044602,\n", "            10045158,\n", "            10028596,\n", "            10035386,\n", "            10050565,\n", "            10047446,\n", "            10046854,\n", "            10037902,\n", "            10014861,\n", "            10047434,\n", "            10046993,\n", "            10045750,\n", "            10037904,\n", "            10037906,\n", "            10043042,\n", "            10044048,\n", "            10014865,\n", "            10047251,\n", "            10050695,\n", "            10034374,\n", "            10050696,\n", "            10047882,\n", "            10047931,\n", "            10046229,\n", "            10045416,\n", "            10047437,\n", "            10030009,\n", "            10050000,\n", "            10037909,\n", "            10049954,\n", "            10046222,\n", "            10047435,\n", "            10047436,\n", "            10050694,\n", "            10045749,\n", "            10046230,\n", "            10036851,\n", "            10047883,\n", "            10047126,\n", "            10034603,\n", "            10041530,\n", "            10046306,\n", "            10050174,\n", "            10050177,\n", "            10026520,\n", "            10012286,\n", "            10050567,\n", "            10046305,\n", "            10045154,\n", "            10045392,\n", "            10049952,\n", "            10049953,\n", "            10050693,\n", "            10045885,\n", "            10037901,\n", "            10041972,\n", "            10047001,\n", "            10050176,\n", "            10002146,\n", "            10015514,\n", "            10051373,\n", "            10045466,\n", "            10045464,\n", "            10000059,\n", "            10051362,\n", "            10015382,\n", "            10051365,\n", "            10051364,\n", "            10013720,\n", "            10010778,\n", "            10034886,\n", "            10037949,\n", "            10005766,\n", "            10037915,\n", "            10046348,\n", "            10046347,\n", "        ),\n", "    ),\n", "    (\n", "        116,\n", "        534,\n", "        (\n", "            10007372,\n", "            10000910,\n", "            10006982,\n", "            10045203,\n", "            10011367,\n", "            10000365,\n", "            10015381,\n", "            10025210,\n", "            10048245,\n", "            10005527,\n", "            10003239,\n", "            10010320,\n", "            10034890,\n", "            10016712,\n", "            10005320,\n", "            10045065,\n", "            10025189,\n", "            10005318,\n", "            10028596,\n", "            10001430,\n", "            10026520,\n", "            10001390,\n", "            10037999,\n", "            10039482,\n", "            10036711,\n", "            10032704,\n", "            10035386,\n", "            10036611,\n", "            10019017,\n", "            10034603,\n", "            10035986,\n", "            10017936,\n", "            10043418,\n", "            10043739,\n", "            10044599,\n", "            10037897,\n", "            10047434,\n", "            10043738,\n", "            10049953,\n", "            10038289,\n", "            10027357,\n", "            10037915,\n", "            10046306,\n", "            10045154,\n", "            10049952,\n", "            10037903,\n", "            10050001,\n", "            10050566,\n", "            10046308,\n", "            10046982,\n", "            10046304,\n", "            10025659,\n", "            10037901,\n", "            10037907,\n", "            10045155,\n", "            10047252,\n", "            10045885,\n", "            10045019,\n", "            10045158,\n", "            10046310,\n", "            10050565,\n", "            10047446,\n", "            10046854,\n", "            10044600,\n", "            10045392,\n", "            10045244,\n", "            10046993,\n", "            10045750,\n", "            10037904,\n", "            10037906,\n", "            10044048,\n", "            10014865,\n", "            10047251,\n", "            10050695,\n", "            10034374,\n", "            10050696,\n", "            10047931,\n", "            10045156,\n", "            10045157,\n", "            10046229,\n", "            10045416,\n", "            10047437,\n", "            10050693,\n", "            10030009,\n", "            10050000,\n", "            10037909,\n", "            10049954,\n", "            10037949,\n", "            10046222,\n", "            10047435,\n", "            10047436,\n", "            10050694,\n", "            10045749,\n", "            10046230,\n", "            10036851,\n", "            10047883,\n", "            10047126,\n", "            10002198,\n", "            10005321,\n", "            10046781,\n", "            10050567,\n", "            10044602,\n", "            10014861,\n", "            10036613,\n", "            10047882,\n", "            10047249,\n", "            10047225,\n", "            10045251,\n", "            10046305,\n", "            10050564,\n", "            10043042,\n", "            10037902,\n", "            10047001,\n", "            10044607,\n", "            10015382,\n", "            10002146,\n", "            10013953,\n", "            10051373,\n", "            10010778,\n", "            10010131,\n", "            10000059,\n", "            10034886,\n", "            10013720,\n", "            10016541,\n", "            10045970,\n", "            10032109,\n", "            10046004,\n", "            10044234,\n", "            10046000,\n", "            10045966,\n", "            10044233,\n", "            10044230,\n", "            10046006,\n", "            10005766,\n", "            10044604,\n", "        ),\n", "    ),\n", "    (\n", "        369,\n", "        828,\n", "        (\n", "            10007372,\n", "            10000910,\n", "            10015517,\n", "            10005320,\n", "            10005673,\n", "            10015516,\n", "            10045203,\n", "            10005318,\n", "            10015518,\n", "            10025189,\n", "            10000365,\n", "            10015381,\n", "            10003239,\n", "            10005527,\n", "            10048245,\n", "            10011367,\n", "            10010320,\n", "            10034890,\n", "            10027385,\n", "            10037945,\n", "            10035986,\n", "            10043418,\n", "            10043739,\n", "            10049918,\n", "            10037897,\n", "            10043738,\n", "            10046781,\n", "            10036858,\n", "            10038289,\n", "            10050564,\n", "            10027357,\n", "            10037903,\n", "            10047448,\n", "            10050001,\n", "            10050566,\n", "            10046308,\n", "            10046982,\n", "            10025659,\n", "            10037907,\n", "            10045155,\n", "            10047252,\n", "            10046310,\n", "            10045251,\n", "            10045019,\n", "            10044602,\n", "            10045158,\n", "            10028596,\n", "            10035386,\n", "            10050565,\n", "            10047446,\n", "            10046854,\n", "            10037902,\n", "            10014861,\n", "            10047434,\n", "            10046993,\n", "            10045750,\n", "            10037904,\n", "            10037906,\n", "            10043042,\n", "            10044048,\n", "            10014865,\n", "            10047251,\n", "            10050695,\n", "            10034374,\n", "            10050696,\n", "            10047882,\n", "            10047931,\n", "            10046229,\n", "            10045416,\n", "            10047437,\n", "            10030009,\n", "            10050000,\n", "            10037909,\n", "            10049954,\n", "            10046222,\n", "            10047435,\n", "            10047436,\n", "            10050694,\n", "            10045749,\n", "            10046230,\n", "            10036851,\n", "            10047883,\n", "            10047126,\n", "            10034603,\n", "            10041530,\n", "            10046306,\n", "            10050174,\n", "            10050177,\n", "            10026520,\n", "            10012286,\n", "            10050567,\n", "            10046305,\n", "            10045154,\n", "            10045392,\n", "            10049952,\n", "            10049953,\n", "            10050693,\n", "            10045885,\n", "            10037901,\n", "            10041972,\n", "            10047001,\n", "            10050176,\n", "            10002146,\n", "            10015514,\n", "            10051373,\n", "            10045466,\n", "            10045464,\n", "            10000059,\n", "            10051362,\n", "            10015382,\n", "            10051365,\n", "            10051364,\n", "            10013720,\n", "            10010778,\n", "            10034886,\n", "            10037949,\n", "            10005766,\n", "            10037915,\n", "            10046348,\n", "            10046347,\n", "        ),\n", "    ),\n", "    (\n", "        512,\n", "        843,\n", "        (\n", "            10000910,\n", "            10015381,\n", "            10007372,\n", "            10000365,\n", "            10008069,\n", "            10008199,\n", "            10015216,\n", "            10016712,\n", "            10005882,\n", "            10003239,\n", "            10041072,\n", "            10048245,\n", "            10025189,\n", "            10025171,\n", "            10034890,\n", "            10005318,\n", "            10010320,\n", "            10005527,\n", "            10011367,\n", "            10025187,\n", "            10045065,\n", "            10024891,\n", "            10045169,\n", "            10031083,\n", "            10035986,\n", "            10043418,\n", "            10043739,\n", "            10014861,\n", "            10043738,\n", "            10047001,\n", "            10047251,\n", "            10046310,\n", "            10045749,\n", "            10045750,\n", "            10045019,\n", "            10046305,\n", "            10046230,\n", "            10027357,\n", "            10046781,\n", "            10047252,\n", "            10046308,\n", "            10047249,\n", "            10036711,\n", "            10046229,\n", "            10046854,\n", "            10046982,\n", "            10049953,\n", "            10001430,\n", "            10026520,\n", "            10046306,\n", "            10047225,\n", "            10049952,\n", "            10046993,\n", "            10047435,\n", "            10047446,\n", "            10050000,\n", "            10050564,\n", "            10045244,\n", "            10049954,\n", "            10037904,\n", "            10045155,\n", "            10045885,\n", "            10046222,\n", "            10047799,\n", "            10050001,\n", "            10002198,\n", "            10045392,\n", "            10047800,\n", "            10030009,\n", "            10037906,\n", "            10044607,\n", "            10045020,\n", "            10045251,\n", "            10045416,\n", "            10046853,\n", "            10047882,\n", "            10050177,\n", "            10050176,\n", "            10037907,\n", "            10037909,\n", "            10047448,\n", "            10014865,\n", "            10031874,\n", "            10036611,\n", "            10036613,\n", "            10036858,\n", "            10038289,\n", "            10043173,\n", "            10045018,\n", "            10045104,\n", "            10047883,\n", "            10047885,\n", "            10047931,\n", "            10043042,\n", "            10044048,\n", "            10047437,\n", "            10002146,\n", "            10050623,\n", "            10013953,\n", "            10015382,\n", "            10037755,\n", "            10041618,\n", "            10000059,\n", "            10051373,\n", "            10050628,\n", "            10021993,\n", "            10010778,\n", "            10037562,\n", "            10029488,\n", "            10010131,\n", "            10029629,\n", "            10034886,\n", "            10013720,\n", "            10016541,\n", "            10037915,\n", "            10037949,\n", "            10034154,\n", "            10005766,\n", "            10044604,\n", "            10029495,\n", "            10037903,\n", "        ),\n", "    ),\n", "    (\n", "        337,\n", "        831,\n", "        (\n", "            10008199,\n", "            10018056,\n", "            10007372,\n", "            10000910,\n", "            10005882,\n", "            10005318,\n", "            10000365,\n", "            10003239,\n", "            10041072,\n", "            10025189,\n", "            10048245,\n", "            10014894,\n", "            10036525,\n", "            10045018,\n", "            10044607,\n", "            10050173,\n", "            10035986,\n", "            10024484,\n", "            10024485,\n", "            10043418,\n", "            10043739,\n", "            10043738,\n", "            10045749,\n", "            10046310,\n", "            10045750,\n", "            10047001,\n", "            10043646,\n", "            10047436,\n", "            10014861,\n", "            10046982,\n", "            10001390,\n", "            10047435,\n", "            10045019,\n", "            10027357,\n", "            10046306,\n", "            10047249,\n", "            10050564,\n", "            10046230,\n", "            10045155,\n", "            10046305,\n", "            10046308,\n", "            10046993,\n", "            10047225,\n", "            10047797,\n", "            10030009,\n", "            10046781,\n", "            10014865,\n", "            10036711,\n", "            10046854,\n", "            10043174,\n", "            10047251,\n", "            10047252,\n", "            10043042,\n", "            10043173,\n", "            10045158,\n", "            10045416,\n", "            10046853,\n", "            10047434,\n", "            10047446,\n", "            10047883,\n", "            10050000,\n", "            10050176,\n", "            10050566,\n", "            10050174,\n", "            10050694,\n", "            10037904,\n", "            10037907,\n", "            10041972,\n", "            10045251,\n", "            10046229,\n", "            10046597,\n", "            10047437,\n", "            10049953,\n", "            10026520,\n", "            10034603,\n", "            10037906,\n", "            10047448,\n", "            10047882,\n", "            10047931,\n", "            10049952,\n", "            10049954,\n", "            10050001,\n", "            10050172,\n", "            10050175,\n", "            10050565,\n", "            10050567,\n", "            10037901,\n", "            10037909,\n", "            10035186,\n", "            10030190,\n", "            10047942,\n", "            10021996,\n", "            10037755,\n", "            10045709,\n", "            10028953,\n", "            10036970,\n", "            10018054,\n", "            10021994,\n", "            10030193,\n", "            10045400,\n", "            10002146,\n", "            10030188,\n", "            10021992,\n", "            10021993,\n", "            10018058,\n", "            10021991,\n", "            10035191,\n", "            10030189,\n", "            10045368,\n", "            10013953,\n", "            10035190,\n", "            10051373,\n", "            10029488,\n", "            10035194,\n", "            10035193,\n", "            10000059,\n", "            10037562,\n", "            10010778,\n", "            10010131,\n", "            10030191,\n", "            10050670,\n", "            10037903,\n", "            10044602,\n", "            10034886,\n", "            10037949,\n", "            10044600,\n", "            10044604,\n", "            10008261,\n", "            10008257,\n", "            10008259,\n", "            10008397,\n", "            10012542,\n", "            10014864,\n", "            10014872,\n", "            10014879,\n", "            10018052,\n", "            10024889,\n", "            10030001,\n", "            10034602,\n", "            10035141,\n", "            10035189,\n", "            10037897,\n", "            10037902,\n", "            10044185,\n", "            10044237,\n", "            10044313,\n", "            10044314,\n", "            10044435,\n", "            10044558,\n", "            10044599,\n", "            10044601,\n", "            10044603,\n", "        ),\n", "    ),\n", "    (\n", "        343,\n", "        837,\n", "        (\n", "            10005673,\n", "            10005882,\n", "            10045203,\n", "            10005318,\n", "            10007372,\n", "            10000365,\n", "            10000910,\n", "            10041072,\n", "            10003239,\n", "            10048245,\n", "            10005527,\n", "            10045065,\n", "            10010320,\n", "            10025189,\n", "            10034890,\n", "            10012513,\n", "            10035986,\n", "            10046036,\n", "            10046257,\n", "            10005578,\n", "            10006305,\n", "            10012285,\n", "            10016547,\n", "            10003856,\n", "            10024484,\n", "            10024485,\n", "            10043418,\n", "            10043739,\n", "            10049918,\n", "            10043738,\n", "            10045749,\n", "            10045158,\n", "            10045157,\n", "            10045750,\n", "            10026520,\n", "            10046310,\n", "            10045156,\n", "            10047251,\n", "            10047249,\n", "            10014865,\n", "            10046308,\n", "            10034603,\n", "            10046854,\n", "            10050567,\n", "            10046229,\n", "            10050693,\n", "            10046230,\n", "            10047435,\n", "            10047225,\n", "            10049953,\n", "            10047882,\n", "            10047797,\n", "            10045019,\n", "            10045155,\n", "            10046597,\n", "            10001390,\n", "            10046305,\n", "            10046993,\n", "            10001430,\n", "            10046781,\n", "            10047436,\n", "            10050695,\n", "            10047434,\n", "            10047252,\n", "            10050001,\n", "            10027357,\n", "            10046306,\n", "            10047446,\n", "            10047931,\n", "            10045154,\n", "            10046222,\n", "            10047437,\n", "            10050694,\n", "            10043042,\n", "            10047448,\n", "            10036858,\n", "            10037906,\n", "            10037907,\n", "            10037999,\n", "            10045244,\n", "            10050176,\n", "            10036851,\n", "            10046982,\n", "            10019017,\n", "            10037897,\n", "            10038289,\n", "            10045251,\n", "            10045392,\n", "            10050566,\n", "            10037902,\n", "            10050177,\n", "            10002146,\n", "            10051373,\n", "            10000059,\n", "            10010131,\n", "            10010778,\n", "            10037949,\n", "            10034886,\n", "            10013720,\n", "            10044602,\n", "            10005766,\n", "            10037903,\n", "            10044600,\n", "            10037915,\n", "        ),\n", "    ),\n", "    (\n", "        367,\n", "        807,\n", "        (\n", "            10005320,\n", "            10005673,\n", "            10005882,\n", "            10000910,\n", "            10045203,\n", "            10024958,\n", "            10005318,\n", "            10015381,\n", "            10000365,\n", "            10015517,\n", "            10015216,\n", "            10015516,\n", "            10007372,\n", "            10041072,\n", "            10025189,\n", "            10003239,\n", "            10048245,\n", "            10015518,\n", "            10005527,\n", "            10010320,\n", "            10034890,\n", "            10045065,\n", "            10048244,\n", "            10045020,\n", "            10049945,\n", "            10035986,\n", "            10042183,\n", "            10005578,\n", "            10012285,\n", "            10024484,\n", "            10024485,\n", "            10043418,\n", "            10043739,\n", "            10002146,\n", "            10051373,\n", "            10000059,\n", "            10010778,\n", "            10010131,\n", "            10034886,\n", "            10005766,\n", "            10037949,\n", "            10013720,\n", "            10037915,\n", "            10044602,\n", "            10016541,\n", "            10037903,\n", "            10044600,\n", "            10014861,\n", "            10036711,\n", "            10045019,\n", "            10045885,\n", "            10001390,\n", "            10037999,\n", "            10046853,\n", "            10014865,\n", "            10046781,\n", "            10046982,\n", "            10047001,\n", "            10049954,\n", "            10045244,\n", "            10043173,\n", "            10045392,\n", "            10046597,\n", "            10034603,\n", "            10047446,\n", "            10049952,\n", "            10037904,\n", "            10046993,\n", "            10049953,\n", "            10050175,\n", "            10037909,\n", "            10043174,\n", "            10046222,\n", "            10047797,\n", "            10002198,\n", "            10028596,\n", "            10030009,\n", "            10046229,\n", "            10047448,\n", "            10047882,\n", "            10050000,\n", "            10050174,\n", "            10043042,\n", "            10046230,\n", "            10036858,\n", "            10050001,\n", "            10050173,\n", "            10026520,\n", "            10047126,\n", "            10047883,\n", "            10036851,\n", "            10037906,\n", "            10038289,\n", "            10045251,\n", "            10050176,\n", "            10050172,\n", "            10037897,\n", "            10045416,\n", "            10037901,\n", "            10037902,\n", "            10037907,\n", "            10046854,\n", "            10047800,\n", "            10047931,\n", "            10043738,\n", "            10045749,\n", "            10045158,\n", "            10045156,\n", "            10045750,\n", "            10045157,\n", "            10047251,\n", "            10027357,\n", "            10047252,\n", "            10047435,\n", "            10047437,\n", "            10047249,\n", "            10047436,\n", "            10046308,\n", "            10047225,\n", "            10046305,\n", "            10050564,\n", "            10045154,\n", "            10050694,\n", "            10045155,\n", "            10047434,\n", "            10050695,\n", "            10046310,\n", "            10050693,\n", "            10050567,\n", "            10050565,\n", "            10050566,\n", "            10003856,\n", "            10005321,\n", "            10007518,\n", "            10011367,\n", "            10012286,\n", "            10012329,\n", "            10012513,\n", "            10013721,\n", "            10016544,\n", "            10016547,\n", "            10024965,\n", "            10025171,\n", "            10025187,\n", "            10025659,\n", "            10026290,\n", "            10028099,\n", "            10034374,\n", "            10035141,\n", "            10035142,\n", "            10036058,\n", "            10044313,\n", "            10044314,\n", "            10044801,\n", "            10045047,\n", "            10046227,\n", "            10046294,\n", "            10046304,\n", "            10047186,\n", "            10047407,\n", "            10047785,\n", "            10049920,\n", "            10050242,\n", "            10050248,\n", "            10050652,\n", "            10001425,\n", "            10001428,\n", "            10001430,\n", "            10023931,\n", "            10028385,\n", "            10039754,\n", "            10041971,\n", "            10041972,\n", "            10041975,\n", "            10041983,\n", "            10050272,\n", "            10050273,\n", "            10050277,\n", "            10050294,\n", "            10050295,\n", "            10050328,\n", "            10050329,\n", "            10001456,\n", "            10002177,\n", "            10002322,\n", "            10002324,\n", "            10002340,\n", "            10002349,\n", "            10002351,\n", "            10004529,\n", "            10005577,\n", "            10008680,\n", "            10010070,\n", "            10012159,\n", "            10012542,\n", "            10014864,\n", "            10014870,\n", "            10015085,\n", "            10015600,\n", "            10017502,\n", "            10019017,\n", "            10019020,\n", "            10019023,\n", "            10019037,\n", "            10020674,\n", "            10022179,\n", "            10024889,\n", "            10024891,\n", "            10027358,\n", "            10029973,\n", "            10030001,\n", "            10032148,\n", "            10032384,\n", "            10032470,\n", "            10032474,\n", "            10034602,\n", "            10036173,\n", "            10036174,\n", "            10036176,\n", "            10036177,\n", "            10036178,\n", "            10036179,\n", "            10036181,\n", "            10036182,\n", "            10036184,\n", "            10036185,\n", "            10036188,\n", "            10036189,\n", "            10036347,\n", "            10036351,\n", "            10036352,\n", "            10036353,\n", "            10036355,\n", "            10036356,\n", "            10036359,\n", "            10036360,\n", "            10036361,\n", "            10036362,\n", "            10036363,\n", "            10036364,\n", "            10036367,\n", "            10036368,\n", "            10036369,\n", "            10036370,\n", "            10036657,\n", "            10036840,\n", "            10036846,\n", "            10036847,\n", "            10036848,\n", "            10036849,\n", "            10036850,\n", "            10036853,\n", "            10036854,\n", "            10036855,\n", "            10036861,\n", "            10036863,\n", "            10036864,\n", "            10036868,\n", "            10036879,\n", "            10036892,\n", "            10036907,\n", "            10036908,\n", "            10036911,\n", "            10036912,\n", "            10036914,\n", "            10037916,\n", "            10038287,\n", "            10038292,\n", "            10039800,\n", "            10041989,\n", "            10043736,\n", "            10043742,\n", "            10043788,\n", "            10043789,\n", "            10044048,\n", "            10044049,\n", "            10044050,\n", "            10044051,\n", "            10044067,\n", "            10044185,\n", "            10044189,\n", "            10044435,\n", "            10044599,\n", "            10044601,\n", "            10044603,\n", "            10044604,\n", "            10044606,\n", "            10044607,\n", "            10044608,\n", "            10044612,\n", "            10044613,\n", "            10044730,\n", "            10044921,\n", "            10045159,\n", "            10045161,\n", "            10045191,\n", "            10045256,\n", "            10045292,\n", "            10045293,\n", "            10045439,\n", "            10045790,\n", "            10045824,\n", "            10045825,\n", "            10045826,\n", "            10046306,\n", "            10046307,\n", "            10046501,\n", "            10046616,\n", "            10046617,\n", "            10046618,\n", "            10046619,\n", "            10046985,\n", "            10046986,\n", "            10046988,\n", "            10046989,\n", "            10046999,\n", "            10047224,\n", "            10047246,\n", "            10047461,\n", "            10047462,\n", "            10047463,\n", "            10047844,\n", "            10047845,\n", "            10047851,\n", "            10047867,\n", "            10047876,\n", "            10047877,\n", "            10047922,\n", "            10047923,\n", "            10047924,\n", "            10047943,\n", "            10048131,\n", "            10048133,\n", "            10048134,\n", "            10048136,\n", "            10048137,\n", "            10048138,\n", "            10049887,\n", "            10049944,\n", "            10049946,\n", "            10049970,\n", "            10049987,\n", "            10050249,\n", "            10050250,\n", "            10050251,\n", "            10050257,\n", "            10050696,\n", "            10051931,\n", "        ),\n", "    ),\n", "    (\n", "        475,\n", "        804,\n", "        (\n", "            10007372,\n", "            10006982,\n", "            10011367,\n", "            10000910,\n", "            10000365,\n", "            10045203,\n", "            10005527,\n", "            10048245,\n", "            10010320,\n", "            10035986,\n", "            10043418,\n", "            10043739,\n", "            10043738,\n", "            10038289,\n", "            10050564,\n", "            10027357,\n", "            10046308,\n", "            10046982,\n", "            10045155,\n", "            10045885,\n", "            10045019,\n", "            10045158,\n", "            10046310,\n", "            10045392,\n", "            10047434,\n", "            10046993,\n", "            10044048,\n", "            10014865,\n", "            10050695,\n", "            10047931,\n", "            10045156,\n", "            10045157,\n", "            10045416,\n", "            10050693,\n", "            10046306,\n", "            10046305,\n", "            10050694,\n", "            10045749,\n", "            10034603,\n", "            10045750,\n", "            10046781,\n", "            10047249,\n", "            10047251,\n", "            10050565,\n", "            10050566,\n", "            10047436,\n", "            10047225,\n", "            10045154,\n", "            10027385,\n", "            10047252,\n", "            10047435,\n", "            10046102,\n", "            10046138,\n", "            10045988,\n", "            10002146,\n", "            10045976,\n", "            10032008,\n", "            10045964,\n", "            10046137,\n", "            10045998,\n", "            10051373,\n", "            10010131,\n", "            10000059,\n", "            10050242,\n", "        ),\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1NFQ6hAbvzFadvWCy1wJ72JQSLIWgBeMaERGlzeZa_G4\"\n", "\n", "outlet_info = pb.from_sheets(sheet_id, \"large_items\")\n", "\n", "outlet_info[[\"item_id\"]] = outlet_info[[\"item_id\"]].drop_duplicates()\n", "outlet_info[\"item_id\"].replace(\"\", np.nan, inplace=True)\n", "outlet_info.dropna(subset=[\"item_id\"], inplace=True)\n", "outlet_info[\"item_id\"] = outlet_info[\"item_id\"].astype(int)\n", "ALL_ITEMS = tuple(outlet_info.item_id.unique())\n", "\n", "\n", "# ALL_ITEMS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_info[\"from_outlet\"] = outlet_info[\"from_outlet\"].drop_duplicates()\n", "outlet_info[\"from_outlet\"].replace(\"\", np.nan, inplace=True)\n", "outlet_info.dropna(subset=[\"from_outlet\"], inplace=True)\n", "outlet_info[\"from_outlet\"] = outlet_info[\"from_outlet\"].astype(int)\n", "FROM_OUTLETS = tuple(outlet_info.from_outlet.unique())\n", "\n", "# FROM_OUTLETS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn = pd.DataFrame()\n", "\n", "for i in groc_large_items:\n", "    grn_query = \"\"\"\n", "            SELECT * FROM \n", "       (SELECT ro.name AS 'Outlet Name',\n", "           ro.id AS 'Outlet ID',\n", "           date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) AS 'Stock In Date',\n", "       (CASE\n", "\n", "             WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "             ELSE 'Normal Stock-In'\n", "        END) AS \"Stock-In type\",\n", "\n", "\n", "           r.item_id AS 'item_id',\n", "           r.name AS Product,\n", "\n", "           sum(i.delta-i3.return_quantity) as grn_qty\n", "\n", "\n", "    FROM\n", "       ims.ims_inventory_log i\n", "           INNER JOIN\n", "       rpc.product_product r ON r.variant_id = i.variant_id \n", "           INNER JOIN\n", "       retail.console_outlet ro ON ro.id = i.outlet_id\n", "         INNER JOIN\n", "       ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "         INNER JOIN\n", "       pos.pos_invoice pi on pi.invoice_id = i.merchant_invoice_id\n", "\n", "       WHERE i.inventory_update_type_id IN (1,28,76)\n", "       AND i.outlet_id = %(outlet)s\n", "       AND  date(convert_tz(i.created_at,'+00:00','+05:30')) =  %(start_date)s\n", "       AND i.delta > 0\n", "       GROUP BY 1,2,3,4,5,6) T\n", "       WHERE item_id in %(item_ids)s\n", "    \"\"\"\n", "    temp = pd.read_sql_query(\n", "        sql=grn_query,\n", "        con=retail,\n", "        params={\"start_date\": d, \"outlet\": i[0], \"item_ids\": i[2]},\n", "    )\n", "    grn = grn.append(temp)\n", "\n", "pb.to_sheets(grn, \"1_YZpn7EZnDZSKNsWG7WuJN9gT8_7CHzJy1ZoXU2rSVo\", \"GRN Done\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv = pd.DataFrame()\n", "\n", "for i in groc_large_items:\n", "    item_wise_actual_blocked_qty_query = \"\"\"   SELECT \n", "         iii.outlet_id,\n", "         iii.item_id,\n", "         iii.quantity as actual_inv,\n", "         COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0) as blocked_inv,\n", "         (iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0)) as Net_avai_Quantity\n", "    FROM\n", "    ims.ims_item_inventory iii\n", "         LEFT JOIN\n", "    ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "         INNER JOIN\n", "    retail.warehouse_outlet_mapping wom on iii.outlet_id = wom.cloud_store_id\n", "         INNER JOIN\n", "    retail.console_outlet co ON co.id=iii.outlet_id\n", "         WHERE iii.outlet_id = %(outlet_id)s AND iii.active = 1 AND  iii.item_id IN %(item_ids)s\n", "    GROUP BY 1,2,3;\n", "    \"\"\"\n", "\n", "    temp = pd.read_sql_query(\n", "        item_wise_actual_blocked_qty_query,\n", "        retail,\n", "        params={\"outlet_id\": i[0], \"item_ids\": i[2]},\n", "    )\n", "    inv = inv.append(temp, ignore_index=True)\n", "\n", "\n", "pb.to_sheets(\n", "    inv, \"1_YZpn7EZnDZSKNsWG7WuJN9gT8_7CHzJy1ZoXU2rSVo\", \"Avaialble Inventory of Items\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto = pd.DataFrame()\n", "\n", "for i in groc_large_items:\n", "    sto_query = \"\"\"SELECT base_1.*,\n", "       base_2.grn_details_created_at,\n", "       base_2.inwarded_quantity\n", "    \n", "    \n", "  FROM   \n", "    \n", "(SELECT sto_base.*, \n", "sto_items.name as item_name,\n", "sto_items.mrp,\n", "sto_items.description\n", "\n", "       FROM\n", "(SELECT sto.*,\n", "       i.item_id,\n", "       i.expected_quantity,\n", "       i.reserved_quantity,\n", "       i.billed_quantity,\n", "       i.inward_quantity,\n", "       i.released_reserved_quantity,\n", "       i.released_billed_quantity,\n", "      convert_tz(i.updated_at,'+00:00','+05:30')  as sto_item_updated_at\n", "            \n", "FROM\n", "(Select sto1.*,\n", "                 max(case when log.sto_state=1 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Created_at,\n", "                 max(case when log.sto_state=5 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_partial_billed_at,\n", "                 max(case when log.sto_state=2 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Billed_at,\n", "                    max(case when log.sto_state=3 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Expired_at,\n", "                    max(case when log.sto_state=4 then convert_tz(log.created_at,'+00:00','+05:30')  end) as sto_Inward_at\n", " FROM\n", "(Select sto.sto_id,\n", "       sto.outlet_id as sender_outlet_id,\n", "       o.name as sender_outlet_name,\n", "       sto.merchant_outlet_id as receiving_outlet_id,\n", "       o1.name as receiver_outlet_name,\n", "       convert_tz(sto.delivery_date,'+00:00','+05:30') as delivery_date,\n", "       convert_tz(sto.created_at,'+00:00','+05:30') as created_date,\n", "       convert_tz(sto.updated_at,'+00:00','+05:30') as sto_status_updated_at,\n", "       sto.sto_type as sto_type_id,\n", "\n", "      case when sto.sto_type = 1 then 'Manual'\n", "           when sto.sto_type = 2 then 'Automated' end as sto_type,\n", "      sto.sto_state as sto_state_id,\n", "      case when sto.sto_state = 1 then 'Created'\n", "           when sto.sto_state = 2 then 'Billed'\n", "           when sto.sto_state = 5 then 'Partial-Billed'\n", "            when sto.sto_state = 3 then 'Expired'\n", "            when sto.sto_state = 4 then 'Inward' \n", "            else 'New State'\n", "        end as sto_state,    \n", "\n", "      p_inv.invoice_id,\n", "      convert_tz(p_inv.pos_timestamp,'+00:00','+05:30') as STO_Invoice_created_at ,\n", "      p_inv.actual_sales,\n", "            \n", "            \n", "       CASE\n", "            WHEN transfer_state = 1 THEN 'Raised'\n", "            WHEN transfer_state = 2 THEN 'GRN Complete'\n", "            WHEN transfer_state = 3 THEN 'GRN Partial'\n", "            WHEN transfer_state = 4 THEN 'GRN Force Complete'\n", "            ELSE 'NA'\n", "        END AS Invoice_State,\n", "\n", " \n", "   max(convert_tz(inward.created_at,'+00:00','+05:30')) as grn_created_at,\n", "   \n", "   sum(inward.net_amount) as inward_amount\n", "\n", "from ims.ims_sto_details sto\n", "inner join retail.console_outlet o on sto.outlet_id = o.id\n", "inner join retail.console_outlet o1 on sto.merchant_outlet_id = o1.id\n", "\n", "left join pos.pos_invoice p_inv on sto.sto_id = p_inv.grofers_order_id and invoice_type_id = 5\n", "\n", "left join ims.ims_inward_invoice inward on p_inv.invoice_id = inward.vendor_invoice_id and inward.source_type = 2 \n", "\n", "where date(sto.created_at) >= adddate(CURDATE() , interval -1 day) \n", "-- AND sto.outlet_id = 363 AND sto.merchant_outlet_id = 789\n", " AND sto.outlet_id = '{sender_outlet}' AND sto.merchant_outlet_id = '{receiver_outlet}'\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16\n", "\n", ") sto1\n", "inner join ims.ims_sto_state_log log on sto1.sto_id = log.sto_id\n", "\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", "\n", ") sto\n", "\n", "inner join ims.ims_sto_item i on sto.sto_id = i.sto_id\n", "\n", ") sto_base\n", "\n", "inner join po.sto_items on sto_base.sto_id = sto_items.sto_id and sto_base.item_id = sto_items.item_id\n", "order by 1\n", "\n", ") base_1 \n", "\n", "left join \n", "\n", "(SELECT        inward.vendor_invoice_id,\n", "               p.item_id,\n", "               max(convert_tz(sd.created_at,\"+00:00\",\"+05:30\")) as grn_details_created_at,\n", "               sum(coalesce(sd.delta,0)) as inwarded_quantity\n", "        \n", "      From  ims.ims_inward_invoice inward\n", "INNER JOIN ims.ims_inventory_stock_details sd  on inward.grn_id = sd.grn_id and inward.source_type = 2 \n", "INNER JOIN rpc.product_product p on sd.variant_id = p.variant_id \n", "where date(sd.created_at) >= adddate(CURDATE() , interval -1 day)\n", "group by 1,2\n", "\n", ") base_2\n", "\n", "on base_1.invoice_id = base_2.vendor_invoice_id and base_1.item_id = base_2.item_id\n", "    \"\"\".format(\n", "        sender_outlet=i[0], receiver_outlet=i[1]\n", "    )\n", "\n", "    temp = pd.read_sql_query(sto_query, con=retail)\n", "    sto = sto.append(temp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto.drop(\n", "    [\n", "        \"sto_status_updated_at\",\n", "        \"sto_type_id\",\n", "        \"sto_type\",\n", "        \"sto_state_id\",\n", "        \"STO_Invoice_created_at\",\n", "        \"sto_Created_at\",\n", "        \"sto_partial_billed_at\",\n", "        \"sto_Billed_at\",\n", "        \"sto_Expired_at\",\n", "        \"sto_Inward_at\",\n", "        \"expected_quantity\",\n", "        \"inward_quantity\",\n", "        \"released_reserved_quantity\",\n", "        \"released_billed_quantity\",\n", "        \"sto_item_updated_at\",\n", "        \"mrp\",\n", "        \"description\",\n", "        \"grn_details_created_at\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "sto.fillna(0, inplace=True)\n", "sto[\"Pending_qauntity\"] = sto[\"billed_quantity\"] - sto[\"inwarded_quantity\"]\n", "\n", "pb.to_sheets(sto, \"1_YZpn7EZnDZSKNsWG7WuJN9gT8_7CHzJy1ZoXU2rSVo\", \"STOs Details\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}