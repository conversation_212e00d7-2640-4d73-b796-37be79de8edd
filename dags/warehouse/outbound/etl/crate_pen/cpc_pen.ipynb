{"cells": [{"cell_type": "code", "execution_count": null, "id": "55ead608-cadd-44ca-a5a7-4e53a836545f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "8a4715d0-7fc8-4711-8eab-aa0914b68ad5", "metadata": {}, "outputs": [], "source": ["# redshift = pb.get_connection('[Warehouse] Redshift')\n", "# presto = pb.get_connection(\"[Warehouse] Presto\")\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "f497c8b8-d382-40e1-b20c-31d41f21ea68", "metadata": {}, "outputs": [], "source": ["raw = pd.read_sql_query(\n", "    \"\"\"\n", "select *,\n", "(case when extract(hour from created_at) < 8 THEN cast(created_at - interval '1' day as date) else cast(created_at as date) end) created_on, \n", "(case when extract(hour from created_at) >= 8 and extract(hour from created_at) < 20 THEN 1 else 2 end) shift\n", "from (select cil.source_outlet_id,\n", "             replace(replace(s.name, 'Super Store '), 'SS ') source_outlet_name,\n", "             cil.destination_outlet_id,\n", "             replace(replace(d.name, 'Super Store '), 'SS ') destination_outlet_name,\n", "             s.business_type_id,\n", "             cil.operation_type,\n", "             cil.created_at + interval '5' hour + interval '30' minute created_at,\n", "             (case when crate_type = 16 then cil.quantity else 0 end) quantity_regular,\n", "             (case when crate_type = 18 then cil.quantity else 0 end) quantity_cold,\n", "             (case when crate_type = 19 then cil.quantity else 0 end) quantity_milk\n", "from crates.crate_inventory_log cil\n", "left join lake_retail.console_outlet s on s.id = cil.source_outlet_id\n", "left join lake_retail.console_outlet d on d.id = cil.destination_outlet_id\n", "where insert_ds_ist > '2022-06-15' \n", "and insert_ds_ist between cast(current_date - interval '14' day as varchar) \n", "and cast(current_date as varchar))\n", "\"\"\",\n", "    trino,\n", ")\n", "raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e9f47c77-374d-47f9-a8ba-bd9745550f13", "metadata": {}, "outputs": [], "source": ["# raw[raw['shift'] == 1].groupby(['created_on']).agg({'created_at' : [min, max]})"]}, {"cell_type": "code", "execution_count": null, "id": "97f28c48-356d-4a5e-9260-a69cf935046a", "metadata": {}, "outputs": [], "source": ["# raw[raw['shift'] == 2].groupby(['created_on']).agg({'created_at' : [min, max]})"]}, {"cell_type": "markdown", "id": "f01b7b5a-90f3-4193-8c8a-0f374f189ae0", "metadata": {}, "source": ["### CPC & PC Pendency"]}, {"cell_type": "code", "execution_count": null, "id": "d1d5867c-07df-444b-8453-885e9e514adc", "metadata": {}, "outputs": [], "source": ["ow = raw[(raw[\"business_type_id\"].isin([19, 20])) & (raw[\"operation_type\"] == 1)]\n", "inw = raw[(raw[\"business_type_id\"].isin([19, 20])) & (raw[\"operation_type\"] == 2)]"]}, {"cell_type": "code", "execution_count": null, "id": "3d5b2a99-df0a-4dac-87a3-ea6c77581a8c", "metadata": {}, "outputs": [], "source": ["ow = (\n", "    ow.groupby(\n", "        [\n", "            \"source_outlet_id\",\n", "            \"source_outlet_name\",\n", "            \"destination_outlet_id\",\n", "            \"destination_outlet_name\",\n", "            \"created_on\",\n", "            \"shift\",\n", "        ]\n", "    )\n", "    .agg({\"quantity_regular\": sum, \"quantity_cold\": sum, \"quantity_milk\": sum})\n", "    .reset_index()\n", ")\n", "\n", "inw = (\n", "    inw.groupby(\n", "        [\n", "            \"source_outlet_id\",\n", "            \"source_outlet_name\",\n", "            \"destination_outlet_id\",\n", "            \"destination_outlet_name\",\n", "            \"created_on\",\n", "            \"shift\",\n", "        ]\n", "    )\n", "    .agg({\"quantity_regular\": sum, \"quantity_cold\": sum, \"quantity_milk\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "db04670f-6ce2-4eba-b5da-cf635a491f2f", "metadata": {}, "outputs": [], "source": ["s_d = ow.merge(\n", "    inw,\n", "    on=[\"source_outlet_id\", \"destination_outlet_id\", \"created_on\", \"shift\"],\n", "    how=\"outer\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ee141480-dea5-403c-978a-b15a25187fd5", "metadata": {}, "outputs": [], "source": ["s_d[\"source_outlet_name\"] = np.where(\n", "    s_d[\"source_outlet_name_x\"].isna(),\n", "    s_d[\"source_outlet_name_y\"],\n", "    s_d[\"source_outlet_name_x\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0d31715b-d26c-4fe7-8b08-d5cb203c169c", "metadata": {}, "outputs": [], "source": ["s_d[\"destination_outlet_name\"] = np.where(\n", "    s_d[\"destination_outlet_name_x\"].isna(),\n", "    s_d[\"destination_outlet_name_y\"],\n", "    s_d[\"destination_outlet_name_x\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cd235761-4a6f-44fa-9c9f-e1599fb22671", "metadata": {}, "outputs": [], "source": ["s_d = s_d.rename(\n", "    columns={\n", "        \"quantity_regular_x\": \"regular_crates_sent\",\n", "        \"quantity_cold_x\": \"cold_crates_sent\",\n", "        \"quantity_milk_x\": \"milk_crates_sent\",\n", "        \"quantity_regular_y\": \"regular_crates_received\",\n", "        \"quantity_cold_y\": \"cold_crates_received\",\n", "        \"quantity_milk_y\": \"milk_crates_received\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3d1a7446-c689-46b6-bdef-d5fa0029e7ce", "metadata": {}, "outputs": [], "source": ["s_d = s_d[\n", "    [\n", "        \"source_outlet_id\",\n", "        \"source_outlet_name\",\n", "        \"destination_outlet_id\",\n", "        \"destination_outlet_name\",\n", "        \"created_on\",\n", "        \"shift\",\n", "        \"regular_crates_sent\",\n", "        \"cold_crates_sent\",\n", "        \"milk_crates_sent\",\n", "        \"regular_crates_received\",\n", "        \"cold_crates_received\",\n", "        \"milk_crates_received\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f7928809-6186-4fbb-a801-4656ecb79708", "metadata": {}, "outputs": [], "source": ["s_d.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "74fa88cc-232b-4669-91d1-caef69f7dd9d", "metadata": {}, "outputs": [], "source": ["s_d = s_d.sort_values(\n", "    [\"created_on\", \"shift\", \"source_outlet_id\", \"destination_outlet_id\"],\n", "    ascending=[False, False, True, True],\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "ec6a730e-c567-4b3d-b229-31100989dff4", "metadata": {}, "outputs": [], "source": ["s_d[\"regular_crate_pendency\"] = (\n", "    s_d[\"regular_crates_sent\"] - s_d[\"regular_crates_received\"]\n", ")\n", "s_d[\"cold_crate_pendency\"] = s_d[\"cold_crates_sent\"] - s_d[\"cold_crates_received\"]\n", "s_d[\"milk_crate_pendency\"] = s_d[\"milk_crates_sent\"] - s_d[\"milk_crates_received\"]"]}, {"cell_type": "code", "execution_count": null, "id": "eec67c01-5297-4d4d-b2fc-527e5e43a811", "metadata": {}, "outputs": [], "source": ["s_d.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f9efbf85-4417-4b14-8406-abafef5ba137", "metadata": {"tags": []}, "outputs": [], "source": ["sheet_id = \"1JVmnCPkSh5gHfn3DUiNx8KtPkokTorrvQGFGxwf73T0\"\n", "sheet_name = \"CPC/PC Store Wise Summary (CPC Side Processing)\"\n", "pb.to_sheets(s_d, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "037ffabf-e76e-4897-9dce-6cb0a1e5ee59", "metadata": {}, "outputs": [], "source": ["s2 = (\n", "    s_d.groupby([\"source_outlet_id\", \"source_outlet_name\", \"created_on\", \"shift\"])[\n", "        [\n", "            \"regular_crates_sent\",\n", "            \"cold_crates_sent\",\n", "            \"milk_crates_sent\",\n", "            \"regular_crates_received\",\n", "            \"cold_crates_received\",\n", "            \"milk_crates_received\",\n", "            \"regular_crate_pendency\",\n", "            \"cold_crate_pendency\",\n", "            \"milk_crate_pendency\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f3bd5081-fff4-476a-802e-dfb83b2ccbc3", "metadata": {}, "outputs": [], "source": ["s2 = s2.sort_values(\n", "    [\"created_on\", \"shift\", \"source_outlet_id\"], ascending=[False, False, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "60680228-111b-4b48-827d-b3210fbc2ab6", "metadata": {}, "outputs": [], "source": ["sheet_name = \"CPC/PC Shift Summary (CPC Side Processing)\"\n", "pb.to_sheets(s2, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "15d1b9f5-aa91-4571-8ddf-2be8ebb2f7a6", "metadata": {}, "outputs": [], "source": ["s2 = (\n", "    s_d.groupby([\"source_outlet_id\", \"source_outlet_name\"])[\n", "        [\n", "            \"regular_crates_sent\",\n", "            \"cold_crates_sent\",\n", "            \"milk_crates_sent\",\n", "            \"regular_crates_received\",\n", "            \"cold_crates_received\",\n", "            \"milk_crates_received\",\n", "            \"regular_crate_pendency\",\n", "            \"cold_crate_pendency\",\n", "            \"milk_crate_pendency\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e804a9fd-c10d-4700-a886-9ef35236a98a", "metadata": {}, "outputs": [], "source": ["s2 = s2.sort_values(\"source_outlet_id\", ascending=True)"]}, {"cell_type": "code", "execution_count": null, "id": "912da722-ae43-442a-802c-256ccb1a22a4", "metadata": {}, "outputs": [], "source": ["sheet_name = \"CPC/PC Summary\"\n", "pb.to_sheets(s2, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "id": "0fd62001-4ef7-4035-8458-9851656e39b2", "metadata": {}, "source": ["### DS Pendency"]}, {"cell_type": "code", "execution_count": null, "id": "f06f5929-0b0f-4732-96a9-f35f279eebeb", "metadata": {}, "outputs": [], "source": ["ow = raw[(raw[\"business_type_id\"] == 7) & (raw[\"operation_type\"] == 1)]\n", "inw = raw[(raw[\"business_type_id\"] == 7) & (raw[\"operation_type\"] == 2)]"]}, {"cell_type": "code", "execution_count": null, "id": "1066c7e5-173d-4e53-b07c-951a94787396", "metadata": {}, "outputs": [], "source": ["ow = (\n", "    ow.groupby([\"source_outlet_id\", \"source_outlet_name\", \"created_on\", \"shift\"])\n", "    .agg({\"quantity_regular\": sum, \"quantity_cold\": sum, \"quantity_milk\": sum})\n", "    .reset_index()\n", ")\n", "\n", "inw = (\n", "    inw.groupby([\"source_outlet_id\", \"source_outlet_name\", \"created_on\", \"shift\"])\n", "    .agg({\"quantity_regular\": sum, \"quantity_cold\": sum, \"quantity_milk\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5a98ac32-ad4a-4ac9-94ac-a77ceb66832b", "metadata": {}, "outputs": [], "source": ["d_s = ow.merge(inw, on=[\"source_outlet_id\", \"created_on\", \"shift\"], how=\"outer\")"]}, {"cell_type": "code", "execution_count": null, "id": "61de1af7-cd9c-433a-a222-9dbc9439fffb", "metadata": {}, "outputs": [], "source": ["d_s[\"destination_outlet_name\"] = np.where(\n", "    d_s[\"source_outlet_name_x\"].isna(),\n", "    d_s[\"source_outlet_name_y\"],\n", "    d_s[\"source_outlet_name_x\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b51024e2-64b7-418b-a6aa-6a1f972aef07", "metadata": {}, "outputs": [], "source": ["d_s = d_s.rename(\n", "    columns={\n", "        \"quantity_regular_x\": \"regular_crates_sent\",\n", "        \"quantity_cold_x\": \"cold_crates_sent\",\n", "        \"quantity_milk_x\": \"milk_crates_sent\",\n", "        \"quantity_regular_y\": \"regular_crates_received\",\n", "        \"quantity_cold_y\": \"cold_crates_received\",\n", "        \"quantity_milk_y\": \"milk_crates_received\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f5f58610-757a-464a-a231-7eef03ff1db0", "metadata": {}, "outputs": [], "source": ["d_s = d_s[\n", "    [\n", "        \"source_outlet_id\",\n", "        \"destination_outlet_name\",\n", "        \"created_on\",\n", "        \"shift\",\n", "        \"regular_crates_received\",\n", "        \"cold_crates_received\",\n", "        \"milk_crates_received\",\n", "        \"regular_crates_sent\",\n", "        \"cold_crates_sent\",\n", "        \"milk_crates_sent\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "771163b1-2898-4673-818e-a6d91dc262ca", "metadata": {}, "outputs": [], "source": ["d_s.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a26d4c46-52a6-4375-9190-e4b98f851c5e", "metadata": {}, "outputs": [], "source": ["d_s = d_s.sort_values(\n", "    [\"created_on\", \"shift\", \"source_outlet_id\"], ascending=[False, False, True]\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "id": "974a0efb-9788-451f-a3d0-6394dc372916", "metadata": {}, "outputs": [], "source": ["d_s = d_s.rename(\n", "    columns={\n", "        \"source_outlet_id\": \"darkstore_outlet_id\",\n", "        \"destination_outlet_name\": \"store_name\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4a9d2e8-039a-4f87-9157-7e334b6ef401", "metadata": {}, "outputs": [], "source": ["d_s[\"regular_crate_pendency\"] = (\n", "    d_s[\"regular_crates_received\"] - d_s[\"regular_crates_sent\"]\n", ")\n", "d_s[\"cold_crate_pendency\"] = d_s[\"cold_crates_received\"] - d_s[\"cold_crates_sent\"]\n", "d_s[\"milk_crate_pendency\"] = s_d[\"milk_crates_received\"] - s_d[\"milk_crates_sent\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6f466967-e87a-41d4-869d-b58e94a6ab6d", "metadata": {}, "outputs": [], "source": ["d_s.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "dbd6be97-388f-4e7f-8e7e-254d0a365eb5", "metadata": {}, "outputs": [], "source": ["sheet_name = \"DS Summary (DS Side Processing)\"\n", "pb.to_sheets(d_s, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "id": "c8172326-89d0-4e0e-b752-f86bd6ce810a", "metadata": {}, "source": ["### Adherence"]}, {"cell_type": "code", "execution_count": null, "id": "55774e42-e557-4d75-8a21-0eeb64cfa9c7", "metadata": {}, "outputs": [], "source": ["all_ = pd.read_sql_query(\n", "    \"\"\"\n", "select *,\n", "(case when extract(hour from created_at) < 8 THEN cast(created_at - interval '1' day as date) else cast(created_at as date) end) created_on\n", "-- (case when extract(hour from created_at) >= 8 and extract(hour from created_at) < 20 THEN 1 else 2 end) shift\n", "from (select s.id source_outlet_id,\n", "s.name source_outlet_name,\n", "d.id destination_outlet_id,\n", "d.name destination_outlet_name,\n", "l2.name destination_city_name,\n", "isd.created_at + interval '5' hour + interval '30' minute created_at\n", "from ims.ims_sto_details isd\n", "join lake_retail.console_outlet s on s.id = isd.outlet_id and s.business_type_id in (19,20)\n", "join lake_retail.console_outlet d on d.id = isd.merchant_outlet_id and d.business_type_id = 7\n", "join lake_retail.console_location l2 on l2.id = d.tax_location_id\n", "where isd.created_at >= cast('2022-06-16' as timestamp) - interval '5' hour - interval '30' minute\n", "and isd.created_at >= cast(current_date - interval '14' day as timestamp) - interval '5' hour - interval '30' minute\n", "and d.id not in (select cast(site_id as int)\n", "             from storeops.er\n", "             group by site_id)\n", "group by 1,2,3,4,5,6)\n", "\"\"\",\n", "    trino,\n", ")\n", "all_.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "9f0e190b-9dc3-4e03-a11d-eec23c9004ae", "metadata": {}, "outputs": [], "source": ["adh = pd.read_sql_query(\n", "    \"\"\"\n", "select source_outlet_id,\n", "business_type_id,\n", "(case when extract(hour from created_at) < 8 THEN cast(created_at - interval '1' day as date) else cast(created_at as date) end) created_on,\n", "-- (case when extract(hour from created_at) >= 8 and extract(hour from created_at) < 20 THEN 1 else 2 end) shift, \n", "count(case when operation_type = 1 then 1 else null end)/3 as num_outwards,\n", "count(case when operation_type = 2 then 1 else null end)/3 as num_inwards\n", "from (select source_outlet_id,\n", "             s.business_type_id,\n", "             cil.created_at + interval '5' hour + interval '30' minute created_at,\n", "             operation_type\n", "from crates.crate_inventory_log cil\n", "join lake_retail.console_outlet s on s.id = cil.source_outlet_id and s.business_type_id in (7,19,20)\n", "where insert_ds_ist > '2022-06-15' \n", "and insert_ds_ist between cast(current_date - interval '14' day as varchar) \n", "and cast(current_date as varchar))\n", "group by 1,2,3\n", "\"\"\",\n", "    trino,\n", ")\n", "adh.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "59e0927e-a896-433c-8126-f4426abf610c", "metadata": {}, "outputs": [], "source": ["s_adh = adh[adh[\"business_type_id\"].isin([19, 20])]\n", "d_adh = adh[adh[\"business_type_id\"] == 7]"]}, {"cell_type": "code", "execution_count": null, "id": "b22f7849-7930-4d60-890f-a716de02bbea", "metadata": {}, "outputs": [], "source": ["num_ds = all_.groupby(\"source_outlet_id\").destination_outlet_id.nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "8d5fe5c2-74cf-4c61-8066-79f76ed5000f", "metadata": {}, "outputs": [], "source": ["s_all = all_[[\"source_outlet_id\", \"source_outlet_name\", \"created_on\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "ee63bd46-e66d-4a64-8173-16e970ac1297", "metadata": {}, "outputs": [], "source": ["s_all = s_all.merge(num_ds, on=\"source_outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "4d001b45-609e-4f8e-be6d-56b7c31ac651", "metadata": {}, "outputs": [], "source": ["s_all[\"source_type\"] = np.where(\n", "    s_all[\"source_outlet_name\"].str.contains(\"CPC\"), \"CPC\", \"PC\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ba3adf15-3114-4200-9806-b3f7fd1af5df", "metadata": {}, "outputs": [], "source": ["new = s_all.merge(s_adh, on=[\"source_outlet_id\", \"created_on\"], how=\"left\").drop(\n", "    columns=\"business_type_id\"\n", ")\n", "new.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "fd591a79-60b1-4d79-8684-fdd70d2b3e53", "metadata": {}, "outputs": [], "source": ["new.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "cc77d027-b807-4ba9-85bc-e3a622cf60b1", "metadata": {}, "outputs": [], "source": ["new = new.rename(columns={\"destination_outlet_id\": \"num_ds\"}).fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "56b45621-433f-4ed4-94f8-a46c653408d0", "metadata": {}, "outputs": [], "source": ["sheet_name = \"CPC/PC Adherence Raw\"\n", "pb.to_sheets(new, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "f439069d-17ce-4e8e-a467-3a82eecf601c", "metadata": {}, "outputs": [], "source": ["d_all = all_[\n", "    [\n", "        \"destination_outlet_id\",\n", "        \"destination_outlet_name\",\n", "        \"destination_city_name\",\n", "        \"created_on\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "e7309d57-0f67-42b7-8020-e5f3ad8644a4", "metadata": {}, "outputs": [], "source": ["new = d_all.merge(\n", "    d_adh,\n", "    left_on=[\"destination_outlet_id\", \"created_on\"],\n", "    right_on=[\"source_outlet_id\", \"created_on\"],\n", "    how=\"left\",\n", ").drop(columns=[\"business_type_id\", \"source_outlet_id\"])\n", "new.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "86191c2b-b26d-4ddd-b3d9-0e33fa3887a9", "metadata": {}, "outputs": [], "source": ["new.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2ce28a3a-231c-44cd-81d8-e8f60ceb202d", "metadata": {}, "outputs": [], "source": ["new = new.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "88f1f46f-7ac6-4639-b1e1-3af3b20da76a", "metadata": {}, "outputs": [], "source": ["sheet_name = \"DS Adherence Raw\"\n", "pb.to_sheets(new, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "abbac7bc-9bb1-472b-91a6-89e9fd16e339", "metadata": {}, "outputs": [], "source": ["# cpc -> ds, (cpc, ds, 1)\n", "# ds -> , (ds, null, 2)\n", "# ds -> cpc, (ds, null, 1)\n", "# cpc -> , (cpc, ds, 2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}