{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "\n", "#  sheet variable\n", "\n", "sheet_id = \"1PopqtEVlJP5OsLBEvV-bnAvs9SpwcuAIlZeqQ1lzcdM\"\n", "\n", "# Date Time Variable\n", "\n", "NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "RUN_TIMESTAMP = str(\n", "    NOW_IST.replace(minute=0, second=0, microsecond=0) + <PERSON><PERSON><PERSON>(hours=1)\n", ")\n", "RUN_HOUR = str((NOW_IST + timedelta(hours=1)).hour)\n", "pd.options.mode.chained_assignment = None\n", "NOW_IST\n", "int(RUN_HOUR)\n", "RUN_DATE\n", "\n", "# RUN_DATE=RUN_DATE + pd.DateOffset(-1)\n", "# type(RUN_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyala_query = \"\"\"\n", "\n", "-- select T2.* from (\n", "SELECT T1.*, ul.`fnv_upper_limit`, ul.`upper_limit` , ul.`feeder_live`\n", "\n", "FROM\n", "(\n", "SELECT T.*, il.item_id , il.actual_quantity , il.blocked_quantity  FROM\n", "\n", "(\n", "SELECT  distinct (od.order_id) as order_id , od.ancestor,od.outlet as outlet_id,  \n", "date(convert_tz(od.scheduled_at,\"+00:00\",\"+05:30\")) as del_date, \n", "od.status_id as order_status_id, \n", "ol.id,\n", "date(convert_tz(ol.created_at,\"+00:00\",\"+05:30\")) as created_at,\n", "\n", "ol.description as description, \n", "ol.status_id as log_status_id, \n", "date(convert_tz(ol.old_scheduled_at,\"+00:00\",\"+05:30\")) as old_scheduled_at\n", "\n", "FROM ims.ims_order_details od\n", "inner join ims.ims_order_log ol on od.order_id = ol.order_id\n", "\n", "where \n", " date(convert_tz(od.scheduled_at,\"+00:00\",\"+05:30\"))  between  DATE_ADD(current_date,INTERVAL -1 DAY) and DATE_ADD(current_date,INTERVAL 1 DAY)\n", "-- and od.outlet = 100\n", "and ol.`description` in ('<PERSON>rocure','Created') \n", "and   od.id>= 51544614  \n", "and   ol.id>129220047\n", "and od.status_id in ('2','4')\n", "order by 1,2,6\n", "\n", ")T\n", "\n", "inner join ims.ims_item_inventory_log il on T.id = il.order_log_id\n", "where il.actual_quantity < il.blocked_quantity\n", "order by 1,3,2,5\n", ")T1\n", "\n", "inner join ims.ims_item_inventory ul on T1.item_id = ul.item_id and T1.outlet_id = ul.outlet_id \n", "\n", "-- )T2\n", "-- where fnv_upper_limit is null \n", "-- and upper_limit is null \n", "-- and feeder_live = 0\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d1 = pd.read_sql_query(nyala_query, retail)\n", "nyla_d1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d2 = nyla_d1[\n", "    (pd.isnull(nyla_d1[\"upper_limit\"]))\n", "    & (pd.isnull(nyla_d1[\"fnv_upper_limit\"]))\n", "    & (nyla_d1[\"feeder_live\"] == 0)\n", "]\n", "nyla_d2.reset_index(inplace=True, drop=True)\n", "nyla_d2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d3 = nyla_d2.loc[:, (\"order_id\", \"outlet_id\", \"del_date\", \"description\")]\n", "nyla_d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d3 = nyla_d3.drop_duplicates()\n", "nyla_d3.reset_index(inplace=True, drop=True)\n", "nyla_d3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pklist_query = \"\"\"\n", "select  outlet_id,outlet,fac_id,fac_name,del_date,slot ,order_id,pl_state,\n", "sum(required_quantity) as req_qty, sum(picked_quantity) as picked_qty,\n", "count(item_id) skus,\n", "case when sum(required_quantity) = sum(picked_quantity)  and pl_state='billed' then 1 else 0 end as fulfilled\n", "from\n", "(\n", "select \n", "pl.outlet_id as outlet_id, rco.name as outlet,\n", "fac.id as fac_id,fac.name as fac_name,\n", "pl.id as pl_id,\n", "-- wplom.order_id,\n", "pl.state,case when  pl.state=1  then 'created' when  pl.state=10 then 'billed' else 'other' end as pl_state,\n", "date(convert_tz(pl.min_scheduled_time,\"+00:00\",\"+05:30\")) as del_date,\n", "CASE WHEN HOUR(convert_tz(pl.min_scheduled_time,\"+00:00\",\"+05:30\")) >=6 AND HOUR(convert_tz(pl.min_scheduled_time,\"+00:00\",\"+05:30\")) <=16\n", "THEN 'SLOT A' ELSE 'SLOT B' END AS slot,\n", "\n", "pli.required_quantity,pli.picked_quantity,pli.order_id,pli.item_id,pli.item_name\n", "\n", "from warehouse_location.warehouse_pick_list pl\n", "inner join warehouse_location.warehouse_pick_list_item pli on pl.id=pli.pick_list_id\n", "-- inner join warehouse_location.warehouse_pick_list_order_mapping wplom on pl.id=wplom.pick_list_id\n", "left join   retail.console_outlet rco ON pl.outlet_id = rco.id\n", "-- left join    retail.console_location  rcl ON rco.tax_location_id=rcl.id\n", "left join    crates.facility fac on fac.id=rco.facility_id\n", "where \n", "date(convert_tz(pl.min_scheduled_time,\"+00:00\",\"+05:30\"))  between  DATE_ADD(current_date,INTERVAL -1 DAY) and DATE_ADD(current_date,INTERVAL 1 DAY)\n", "-- and date(convert_tz(pl.min_scheduled_time,\"+00:00\",\"+05:30\")) <current_date-6\n", "and pl.id>43566420 and pl.type=1 and pl.state = 10\n", "and  pli.id>=*********  \n", "-- and pl.outlet_id=100\n", "-- and fac.id=12\n", "-- and fac.name not like \"%%DS%%\" \n", "-- and rco.name not like ('%%cold%%') \n", "and rco.name not like  ('%%Dark%%') and  rco.name not like  ('%%large%%') and  rco.name not like  ('%%DS1%%')\n", "-- and pli.required_quantity!=pli.picked_quantity\n", "\n", ")\n", "T1 group by 1,2,3,4,5,6,7,8\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pklist = pd.read_sql_query(pklist_query, retail)\n", "pklist.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pklist.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nyla_d3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    pklist,\n", "    nyla_d3,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"del_date\", \"order_id\"],\n", "    right_on=[\"outlet_id\", \"del_date\", \"order_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_df[\"description\"].fillna(\"WH\", inplace = True)\n", "final_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_df1=final_df[(final_df['outlet_id']==100)\n", "#                    & (final_df['del_date']==RUN_DATE)\n", "# #                    & (final_df['fulfilled']==0)\n", "#                   ]\n", "\n", "final_df1 = final_df\n", "\n", "\n", "final_df1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def f(x):\n", "    if x[\"fulfilled\"] == 1:\n", "        return \"Complete\"\n", "    elif x[\"fulfilled\"] == 0 and (x[\"description\"] == \"Reprocure\"):\n", "        return \"Reprocured\"\n", "    elif x[\"fulfilled\"] == 0 and (x[\"description\"] == \"Created\"):\n", "        return \"<PERSON>yala\"\n", "    else:\n", "        return \"WH_impact\"\n", "\n", "\n", "final_df1[\"ord_stat\"] = final_df1.apply(f, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df1[final_df1[\"fulfilled\"] == 0].tail(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output = final_df1.groupby(\n", "    [\"outlet_id\", \"slot\", \"del_date\", \"ord_stat\"], as_index=False\n", ")[[\"order_id\"]].count()\n", "output.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output[(output[\"outlet_id\"] == 100) & (output[\"del_date\"] == RUN_DATE)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "output[\"run\"] = run_id\n", "output.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Unique Identifier for Outlet\"},\n", "    {\"name\": \"slot\", \"type\": \"varchar\", \"description\": \"slot name\"},\n", "    {\"name\": \"del_date\", \"type\": \"datetime\", \"description\": \"delivery date for order\"},\n", "    {\"name\": \"order_stat\", \"type\": \"varchar\", \"description\": \"status of the order\"},\n", "    {\"name\": \"ord_id\", \"type\": \"int\", \"description\": \"unique Id for Order\"},\n", "    {\"name\": \"run\", \"type\": \"datetime\", \"description\": \"RunID\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"warehouse_live_tracker_nyala\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"del_date\", \"slot\"],\n", "    \"primary_key\": [\"outlet_id\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"run\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"being used for getting current nayala impact on unfulfilled orders\",\n", "}\n", "\n", "# kwargs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(output, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# output1=final_df1.groupby(['outlet_id', 'slot','del_date','ord_stat'],as_index=False).agg(\n", "#     # Get max of the duration column for each group\n", "#     ord_cnt=('order_id', count())\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}