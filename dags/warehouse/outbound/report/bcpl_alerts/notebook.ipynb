{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BCPL Alerts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "\n", "pd.options.display.max_columns = 100\n", "import math\n", "import numpy as np\n", "\n", "!pip install matplotlib\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, timedelta\n", "import pytz\n", "\n", "tz = pytz.timezone(\"Asia/Calcutta\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datetime.now(tz).hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if datetime.now(tz).hour < 4:\n", "    day = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=1)\n", "else:\n", "    day = datetime.now(tz).date() - <PERSON><PERSON><PERSON>(days=0)\n", "print(day)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select p.outlet_id,vt.legal_name,c.name as outlet_name,p.invoice_id, p.actual_sales\n", "            from lake_view_pos.pos_invoice p \n", "            join lake_view_vms.vms_vendor_city_mapping v on v.vendor_id = p.source_entity_vendor_id \n", "            join lake_view_vms.vms_vendor_city_tax_info vt on vt.vendor_city_id = v.id\n", "            join lake_retail.console_outlet c on c.id = p.outlet_id\n", "            where p.invoice_type_id in (1,2)\n", "            and p.insert_ds_ist = '{day}'\n", "            and vt.legal_name = 'BLINK COMMERCE PRIVATE LIMITED' \"\"\"\n", "outward_data = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary = (\n", "    outward_data.groupby([\"outlet_name\"])\n", "    .agg({\"invoice_id\": \"nunique\", \"actual_sales\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outward_guidelines_summary.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=7.0,\n", "    row_height=0.825,\n", "    font_size=24,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    #     cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        colLabels=[\"Outlet Name\", \"Invoices Billed\", \"Sale Value\"],\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(day)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if outward_guidelines_summary.shape[0] > 0:\n", "    import requests\n", "    import os\n", "    from slack_sdk import WebClient\n", "    from slack_sdk.errors import SlackApiError\n", "    import matplotlib.pyplot as plt\n", "\n", "    text = f\"\"\" <!channel> :red_circle::red_circle::red_circle: BCPL Sale Alert for {day} :red_circle::red_circle::red_circle:\n", "    \\n\"\"\"\n", "    fig, ax = render_mpl_table(outward_guidelines_summary, header_columns=0)\n", "    fig.savefig(\"BCPL_Invoices_B2C_Billed.png\")\n", "    token = \"******************************************************\"\n", "    client = WebClient(token=token)\n", "    file_check = \"./BCPL_Invoices_B2C_Billed.png\"\n", "    channel = \"bcpl_sale_alerts\"\n", "    try:\n", "        response = client.chat_postMessage(channel=channel, text=text)\n", "        filepath = file_check\n", "        response = client.files_upload(channels=channel, file=filepath)\n", "    except SlackApiError as e:\n", "        assert e.response[\"ok\"] is False\n", "        assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "        print(f\"Got an error: {e.response['error']}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}