{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "from jinja2 import Template\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aur = pb.get_connection(\"aurora\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=0)).date()\n", "end_date = (datetime.today() + timedelta(hours=5.5) + timedelta(days=1)).date()\n", "today = start_date.strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Ic56ZNPLyp46KuFD2UPBvPxhA7rdg3rJQIf9-cmgbPo\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "select \n", "co.facility_id,\n", "wf.name as facility_name,\n", "co.name as outlet_name,\n", "co.id as outlet_id,\n", "iod.ancestor,\n", "iod.order_id,\n", "os.name as order_status,\n", "convert_tz(iod.slot_handover_time,'+00:00','+05:30') as 'handovertime',\n", "convert_tz(iod.scheduled_at,'+00:00','+05:30') as 'scheduled_delivery_timestamp',\n", "date(convert_tz(iod.scheduled_at,'+00:00','+05:30')) as 'scheduled_delivery_date',\n", "convert_tz(pi.pos_timestamp,'+00:00','+05:30') as 'order_billed_timestamp',\n", "extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) as hour, \n", "case when extract(hour from convert_tz(iod.scheduled_at,'+00:00','+05:30')) <= 16 then 'SLOT A' ELSE 'SLOT B' end as Delivery_Slot\n", "from ims.ims_order_details iod inner join ims.ims_order_status os on iod.status_id = os.id inner join \n", "retail.console_outlet co on co.id=iod.outlet inner join\n", "retail.warehouse_facility wf on wf.id=co.facility_id left join\n", "pos.pos_invoice pi on pi.grofers_order_id=iod.order_id\n", "where  date(scheduled_at) BETWEEN  SUBDATE(now(),interval 1 day) AND now() and (co.name like '%% NC%%' or co.id=1077 or co.id=1094) \"\"\"\n", "\n", "b1 = pd.read_sql_query(sql=query, con=retail_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" select \n", "              order_id,\n", "              batch_id as order_batch_id,\n", "             (pos_created_at + interval '5.5 hours') as batch_id_assigned_at \n", "             from whl.batch_order_mapping where created_at > (CURRENT_DATE - interval '7 days')\"\"\"\n", "b2 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select \n", "             fhb.id as order_batch_id, \n", "             fhb.name as batch_name,\n", "             case when fhb.state = 1 then 'Created'\n", "             when fhb.state = 2 then 'Tagged'\n", "             when fhb.state = 3 then 'Activated'\n", "             when fhb.state = 4 then 'Handover_activated'\n", "             when fhb.state = 5 then 'Ready_for_Handover'\n", "             when fhb.state = 6 then 'Completed' end\n", "             as batch_current_state,\n", "             fhb.total_orders,\n", "             fhb.dropped_orders,\n", "             fhb.drop_zone_id ,\n", "             dz.name as drop_zone_name, \n", "             dz.state as drop_zone_state\n", "\n", "            from whl.forward_handover_batch fhb \n", "            inner join whl.warehouse_drop_zone dz on fhb.drop_zone_id = dz.id\n", "            where date(fhb.created_at) BETWEEN  (CURRENT_DATE - interval '20 days')::date AND (CURRENT_DATE + interval '1 days')::date\"\"\"\n", "\n", "b3 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"Select \n", "            batch_id as order_batch_id,\n", "            order_id, \n", "            crate_id,\n", "            crate_drop_timestamp,\n", "            max(crate_drop_timestamp) over (partition by order_id ) as last_crate_drop_timestamp \n", "          FROM \n", "              (Select \n", "                  batch_id, \n", "                  order_id,\n", "                  crate_id , \n", "                  (pos_created_at + interval '5.5 hours') as crate_drop_timestamp\n", "                FROM whl.batch_crate_mapping where created_at > (CURRENT_DATE - interval '7 days'))\n", "            T\"\"\"\n", "\n", "b4 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b5 = b1.merge(b2, how=\"left\", on=[\"order_id\"])\n", "\n", "b6 = b5.merge(b3, how=\"left\", on=[\"order_batch_id\"])\n", "b7 = b6.merge(b4, how=\"left\", on=[\"order_batch_id\", \"order_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.scheduled_delivery_date = pd.to_datetime(b7.scheduled_delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7[\"data_updated_at\"] = datetime.utcnow() - timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b7.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = (\n", "    b7[b7[\"order_status\"] != \"Cancelled Nonbillable\"]\n", "    .groupby(\n", "        [\n", "            \"facility_name\",\n", "            \"outlet_id\",\n", "            \"scheduled_delivery_date\",\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Received\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y1 = (\n", "#     temp[\n", "#         (temp[\"pick_list_state\"] != 5)\n", "#         & (temp[\"pick_list_state\"] != 11)\n", "#         & (pd.isnull(temp[\"completion_time\"]) == False)\n", "#     ]\n", "#     .groupby([\"facility\", \"scheduled_delivery_date\", \"delivery_slot\", \"handovertime\"])[\n", "#         \"order_id\"\n", "#     ]\n", "#     .nunique()\n", "#     .reset_index()\n", "#     .rename(columns={\"order_id\": \"Picked\"})\n", "# )\n", "# y2 = y.merge(\n", "#     y1,\n", "#     how=\"left\",\n", "#     on=[\"facility\", \"handovertime\", \"scheduled_delivery_date\", \"delivery_slot\"],\n", "# )\n", "y3 = (\n", "    b7[\n", "        (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "        & (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    ]\n", "    .groupby(\n", "        [\n", "            \"facility_name\",\n", "            \"outlet_id\",\n", "            \"scheduled_delivery_date\",\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Billed\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y4 = y.merge(\n", "    y3,\n", "    how=\"left\",\n", "    on=[\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"handovertime\",\n", "        \"scheduled_delivery_date\",\n", "        \"Delivery_Slot\",\n", "    ],\n", ")\n", "y5 = (\n", "    b7[\n", "        (b7[\"order_status\"] != \"Cancelled Nonbillable\")\n", "        & (pd.isnull(b7[\"crate_drop_timestamp\"]) == False)\n", "    ]\n", "    .groupby(\n", "        [\n", "            \"facility_name\",\n", "            \"outlet_id\",\n", "            \"scheduled_delivery_date\",\n", "            \"Delivery_Slot\",\n", "            \"handovertime\",\n", "        ]\n", "    )[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Dropped\"})\n", ")\n", "y6 = y4.merge(\n", "    y5,\n", "    how=\"left\",\n", "    on=[\n", "        \"facility_name\",\n", "        \"outlet_id\",\n", "        \"handovertime\",\n", "        \"scheduled_delivery_date\",\n", "        \"Delivery_Slot\",\n", "    ],\n", ")\n", "\n", "y6[[\"Billed\", \"Dropped\"]] = y6[[\"Billed\", \"Dropped\"]].fillna(0).astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y6.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(y6, \"1Ic56ZNPLyp46KuFD2UPBvPxhA7rdg3rJQIf9-cmgbPo\", \"Order Flow Summary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_not_dropped = b7[\n", "    (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    & (pd.isnull(b7.crate_drop_timestamp) == True)\n", "][\n", "    [\n", "        \"facility_name\",\n", "        \"outlet_name\",\n", "        \"outlet_id\",\n", "        \"batch_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"crate_id\",\n", "        \"handovertime\",\n", "        \"scheduled_delivery_date\",\n", "        \"Delivery_Slot\",\n", "        \"data_updated_at\",\n", "    ]\n", "]\n", "orders_not_dropped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_dropped = b7[\n", "    (pd.isnull(b7[\"order_billed_timestamp\"]) == False)\n", "    & (pd.isnull(b7.crate_drop_timestamp) == False)\n", "][\n", "    [\n", "        \"facility_name\",\n", "        \"outlet_name\",\n", "        \"outlet_id\",\n", "        \"batch_name\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"crate_id\",\n", "        \"handovertime\",\n", "        \"scheduled_delivery_date\",\n", "        \"Delivery_Slot\",\n", "        \"data_updated_at\",\n", "    ]\n", "]\n", "orders_dropped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_not_dropped,\n", "    \"1Ic56ZNPLyp46KuFD2UPBvPxhA7rdg3rJQIf9-cmgbPo\",\n", "    \"Orders Not dropped\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    orders_dropped, \"1Ic56ZNPLyp46KuFD2UPBvPxhA7rdg3rJQIf9-cmgbPo\", \"Orders dropped\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Outlets = pb.from_sheets(\"1Ic56ZNPLyp46KuFD2UPBvPxhA7rdg3rJQIf9-cmgbPo\", \"Emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Orders = Orders.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Emails=Outlets[Outlets['OutletId']== str(outlet)].Email_Id.to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Outlet_List = Outlets.OutletId.drop_duplicates().to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "for outlet in Outlet_List:\n", "\n", "    Name = Outlets[Outlets[\"OutletId\"] == str(outlet)][\"Facility Name\"].unique()[0]\n", "\n", "    Orders = y6[(y6[\"facility_name\"] == Name) & (y6[\"outlet_id\"] == int(outlet))]\n", "\n", "    to_email = Outlets[Outlets[\"OutletId\"] == str(outlet)].Email_Id.to_list()\n", "\n", "    Orders = Orders.to_dict(orient=\"rows\")\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    orders_not_dropped[\n", "        (orders_not_dropped[\"facility_name\"] == Name)\n", "        & (orders_not_dropped[\"outlet_id\"] == int(outlet))\n", "    ].to_csv(\"/tmp/OrdersNotDropped_\" + Name + \".csv\")\n", "\n", "    orders_dropped[\n", "        (orders_dropped[\"facility_name\"] == Name)\n", "        & (orders_dropped[\"outlet_id\"] == int(outlet))\n", "    ].to_csv(\"/tmp/OrdersDropped_\" + Name + \".csv\")\n", "\n", "    email_template = \"<PERSON>car<PERSON>_emails.html\"\n", "    loader = jinja2.FileSystemLoader(\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/outbound/report/ninjacart/\"\n", "    )\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "    template = tmpl_environ.get_template(email_template)\n", "\n", "    rendered = template.render(products1=Orders)\n", "\n", "    subject = (\n", "        \"Orders Dropped and Not Dropped Data For \"\n", "        + Name\n", "        + \": \"\n", "        + str(outlet)\n", "        + \" on \"\n", "        + str(today)\n", "    )\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content=rendered,\n", "        files=[\n", "            \"/tmp/OrdersNotDropped_\" + Name + \".csv\",\n", "            \"/tmp/OrdersDropped_\" + Name + \".csv\",\n", "        ],\n", "    )\n", "\n", "    print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y6[y6['facility_name']==Name]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y6['facility_name'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}