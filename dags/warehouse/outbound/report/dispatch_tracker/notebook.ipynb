{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "from datetime import datetime\n", "from datetime import timedelta\n", "\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\")\n", "\n", "pd.options.mode.chained_assignment = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Redshift = pb.get_connection(\"redshift\")\n", "sco = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(minutes=30, hours=5)\n", "RUN_DATE = NOW_IST.date()\n", "\n", "end_date = (datetime.now() - timed<PERSON>ta(days=1)).date()\n", "start_date = (datetime.now() - timed<PERSON>ta(days=16)).date()\n", "# end_date, start_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query to fetch expected dispatch time from SCO db"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_query = \"\"\"\n", "with my_table as (select name, day , slots :: json from (\n", "select name, day ,\n", "flexi_week_schedule_day -> 'timeslots' as slots from (\n", "select  name, 'monday' as day,flexi_week_schedule -> 'monday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'tuesday' as day,flexi_week_schedule -> 'tuesday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'wednesday' as day,flexi_week_schedule -> 'wednesday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'thursday' as day,flexi_week_schedule -> 'thursday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'friday' as day,flexi_week_schedule -> 'friday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'saturday' as day,flexi_week_schedule -> 'saturday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", "union \n", "select  name, 'sunday' as day,flexi_week_schedule -> 'sunday' flexi_week_schedule_day from (\n", "select distinct name, flexi_week_schedule from sco_dispatch_point\n", ") a\n", ") b\n", ") c )\n", "\n", "select lower(dp_name) as dispatch_point_name,\n", "        day as day_of_the_week,\n", "        slot_start_time as slot_start,\n", "        slot_end_time as slot_end,\n", "        handover_time as handover_time,\n", "        handover_before as handover_before ,\n", "        dispatch_time as dispatch_time_sco,\n", "         dispatch_before as dispatch_before\n", "    from(\n", "select dp_name,\n", "        day,\n", "        freeze_before,\n", "        handover_before,\n", "        dispatch_before,\n", "        case when length(cast(start as text)) = 3 then concat('0',left(cast(start as text),1),':',right(cast(start as text),2))\n", "        when length(cast(start as text)) = 4 then concat(left(cast(start as text),2),':',right(cast(start as text),2)) end as slot_start_time,\n", "        case when length(cast(end_time as text)) = 3 then concat('0',left(cast(end_time as text),1),':',right(cast(end_time as text),2))\n", "        when length(cast(end_time as text)) = 4 then concat(left(cast(end_time as text),2),':',right(cast(end_time as text),2)) end as slot_end_time,\n", "        case when length(cast(handover_time as text)) = 3 then concat('0',left(cast(handover_time as text),1),':',right(cast(handover_time as text),2))\n", "        when length(cast(handover_time as text)) = 4 then concat(left(cast(handover_time as text),2),':',right(cast(handover_time as text),2)) end as handover_time,\n", "        case when length(cast(dispatch_time as text)) = 3 then concat('0',left(cast(dispatch_time as text),1),':',right(cast(dispatch_time as text),2))\n", "        when length(cast(dispatch_time as text)) = 4 then concat(left(cast(dispatch_time as text),2),':',right(cast(dispatch_time as text),2)) end as dispatch_time\n", "    from (\n", "select name as dp_name , \n", "        day,\n", "        slots -> 'freeze_before' as freeze_before,\n", "        -- concat(slots -> 'start', '-' ,slots -> 'end') as slot_time,\n", "        slots -> 'start' as start,\n", "        slots -> 'end' as end_time,\n", "        slots -> 'handover_time' as handover_time,\n", "        slots -> 'handover_before' as handover_before,\n", "        slots -> 'dispatch_time' as dispatch_time,\n", "        slots -> 'dispatch_before' as dispatch_before\n", "        from (\n", "select name , day ,json_array_elements(slots) as slots\n", "from my_table t\n", ") a\n", ") a\n", ") a \n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data_fm_dp = pd.read_sql_query(sql=raw_query, con=sco)\n", "# raw_data_fm_dp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query to fetch raw data of all orders placed on day-1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "\n", "With batch_details as \n", "\n", "(  Select distinct loc.loc_name as source,outlet,outlet_name,\n", "        locd.external_loc_id as destination_dp,\n", "          order_batch_id,\n", "          scheduled_delivery_timestamp,\n", "          slot,\n", "          order_id\n", "\n", "    from \n", "\n", "(  Select  orders.source_facility,orders.outlet,orders.outlet_name,\n", "           orders.scheduled_delivery_timestamp,\n", "           CASE WHEN EXTRACT(hours FROM orders.scheduled_delivery_timestamp) <= 15 \n", "             THEN 'SLOT A' ELSE 'SLOT B' END AS slot,\n", "           bom.batch_id as order_batch_id,\n", "           orders.order_id\n", "    from \n", "         \n", "(  Select f.id as facility_id,iod.outlet,r.name as outlet_name,\n", "          f.name as source_facility,\n", "          iod.order_id, \n", "          convert_timezone('Asia/Kolkata',scheduled_at) as scheduled_delivery_timestamp\n", "\n", "from lake_ims.ims_order_details iod\n", "join lake_pos.pos_invoice pi on iod.order_id = pi.grofers_order_id and pi.invoice_type_id = 1\n", "inner join  lake_retail.console_outlet r on iod.outlet = r.id \n", "inner join lake_crates.facility f on r.facility_id = f.id\n", "where  \n", "date(convert_timezone('Asia/Kolkata',scheduled_at))  = current_date - 1\n", "\n", "-- order_id= 96360593\n", "-- current_date - 1\n", "\n", " \n", ") orders\n", "\n", "left join lake_warehouse_location.batch_order_mapping bom on orders.order_id = bom.order_id\n", "group by 1,2,3,4,5,6,7\n", "\n", ") batch_orders\n", "\n", "inner join lake_logex.batch b on batch_orders.order_batch_id = b.external_criteria\n", "join \n", "    ( \n", "        select external_loc_id, loc_name\n", "        from lake_logex.location_db\n", "        group by 1,2\n", "    ) loc on loc.external_loc_id = b.location_loc_id\n", "    join \n", "    ( \n", "        select external_loc_id, loc_name\n", "        from lake_logex.location_db\n", "        group by 1,2\n", "    ) locd on locd.external_loc_id = b.next_destination_loc_id\n", "-- where  loc.loc_name = 'SS Warehouse G1'\n", "        where b.completion_time > b.created_date\n", "\n", "),\n", "\n", "changed_batch as\n", "(\n", "select en_ref_order_id,external_criteria as new_batch from lake_logex.container_activity  \n", "where  en_ref_order_id in \n", "        (\n", "        select distinct(en_ref_order_id) from lake_logex.container_activity \n", "        where\n", "        -- en_ref_order_id in (96360593) and \n", "        remove_reason='BATCH_CHANGED'\n", "        -- external_criteria='0dd1ab81-5042-4dd6-9fe3-84e1d4b0556b'\n", "        -- limit 10\n", "        )\n", "and         activity_type='DISPATCH'\n", "),\n", "\n", "batch_updated as\n", "(\n", "select source,outlet,outlet_name,destination_dp,\n", "coalesce(new_batch,order_batch_id)  order_batch_id,\n", "scheduled_Delivery_timestamp,slot,order_id\n", "from\n", "        (\n", "        select bd.*,cb.new_batch\n", "        from batch_details bd \n", "        left join  changed_batch cb on bd.order_id=cb.en_ref_order_id\n", "        )\n", "),\n", "\n", "batch_handover_activity AS \n", "(\n", "    select \n", "        b.external_criteria as batch_id,\n", "        max(expected_end_time + interval '5.5 hour') as expected_handover_end_time, \n", "        max(end_time + interval '5.5 hour') as actual_handover_end_time\n", "    \n", "    from lake_logex.activity  a\n", "    inner join lake_logex.batch  b on a.batch_id = b.id\n", "    where a.activity_type = 'HANDOVER' \n", "    -- and date(a.created_date) BETWEEN  (CURRENT_DATE - interval '5 days')::date AND CURRENT_DATE::date \n", "    group by 1\n", "),\n", "\n", "batch_dispatch_activity AS \n", "\n", "(   \n", "    select \n", "        b.external_criteria as batch_id,\n", "        max(a.expected_end_time + interval '5.5 hour') as expected_dispatch_end_time, \n", "        max(end_time + interval '5.5 hour') as dispatch_time\n", "        \n", "    from lake_logex.activity a\n", "    inner join lake_logex.batch  b on a.batch_id = b.id\n", "    where a.activity_type = 'DISPATCH' \n", "    -- and date(a.created_date) BETWEEN  (CURRENT_DATE - interval '2 days')::date AND CURRENT_DATE::date \n", "    group by 1\n", ") ,\n", "\n", "dispatch_order as (\n", "select \n", "distinct en_ref_order_id, ca.activity_type,\n", "case when ca.status = 'REMOVED' then 'REMOVED' else 'CANCELLED_DISPATCHED' end as status\n", "from lake_logex.container_activity ca\n", "where ca.attempt_to_remove = 'TRUE'\n", "and remove_reason = 'CANCELLED'\n", "),\n", "\n", "final as\n", "(\n", "select a.*,\n", "        case when dispatch_time is null then 'NOT_DISPATCHED'\n", "        when status = 'REMOVED' then 'NOT_DISPATCHED'\n", "        when dispatch_time is not null and status = 'CANCELLED_DISPATCHED' then 'CANCELLED_DISPATCH'\n", "        when delivery_time is not null then  'DISPATCHED' \n", "        else 'DISPATCHED' end as flag_dispatch,\n", "        case \n", "        when  dispatch_time is not null and disp_lag>30 then 'delayed'\n", "        when  dispatch_time is not null and disp_lag<=30 then 'on_time'\n", "        -- when  dispatch_time is not null and status = 'CANCELLED_DISPATCHED' then 'CANCELLED_DISPATCH'\n", "        -- when  (dispatch_time is null or status = 'REMOVED')then 'NOT_DISPATCHED'\n", "        else ''\n", "        end as disp_lag_flag\n", "        \n", "        from (\n", "Select facility_id::int,facility_name,\n", "        batch_updated.*,\n", "       expected_handover_end_time,\n", "       actual_handover_end_time,\n", "       expected_dispatch_end_time,\n", "       dispatch_time,\n", "       datediff(minute,expected_dispatch_end_time,dispatch_time) disp_lag,\n", "       delivery_time::date,\n", "       to_char((delivery_slot_start_time + interval '5.5 hour'),'HH24:MI')as slot_start,\n", "       to_char((delivery_slot_end_time + interval '5.5 hour'),'HH24:MI')as slot_end,\n", "       dispatch_order.status,\n", "       count(distinct case when ca.status = 'PROCESSED' then container_id end) as dispatched_containers\n", "from batch_updated \n", "left join batch_handover_activity on batch_updated.order_batch_id = batch_handover_activity.batch_id\n", "left join batch_dispatch_activity on batch_updated.order_batch_id = batch_dispatch_activity.batch_id\n", "left join lake_logex.container_activity ca on batch_updated.order_id = ca.en_ref_order_id\n", "left join dispatch_order on batch_updated.order_id = dispatch_order.en_ref_order_id\n", "left join lake_oms_bifrost.oms_suborder os on os.id = batch_updated.order_id\n", "left join metrics.outlet_zone_mapping ozm on ozm.outlet_id= batch_updated.outlet\n", "left join lake_last_mile.order o on o.external_order_id = os.order_id\n", "                                    and batch_updated.scheduled_delivery_timestamp:: date = o.delivery_time::date\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19           )       a\n", "    -- where facility_id=3\n", ")\n", "\n", "select \n", "*\n", "    from final\n", "order by 1 asc\n", "\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(sql=query, con=Redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Adding day of the week to the raw data - required to merge sco data with the raw data\n", "data[\"day_of_the_week\"] = data.apply(\n", "    lambda row: row[\"scheduled_delivery_timestamp\"].date().strftime(\"%A\").lower(),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["expected_df = raw_data_fm_dp.copy()\n", "# data.to_csv(\"data.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merging sco expected time data with the raw data\n", "new_df = pd.merge(\n", "    data,\n", "    expected_df,\n", "    how=\"left\",\n", "    left_on=[\"destination_dp\", \"slot_start\", \"slot_end\", \"day_of_the_week\"],\n", "    right_on=[\"dispatch_point_name\", \"slot_start\", \"slot_end\", \"day_of_the_week\"],\n", ")\n", "# new_df.to_csv(\"final_df.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to add date part to the expected dispatch time - this is required to calculate the lag time\n", "def exp_disp_time(a, b, c):\n", "    if len(a) >= 5 and len(b) >= 5:\n", "        datetime.strptime(a, \"%H:%M\")\n", "        datetime.strptime(b, \"%H:%M\")\n", "        # datetime.strptime(c, '%Y-%m-%d')\n", "        if a < b:\n", "            return datetime.strptime(str(c) + \" \" + str(a) + \":00\", \"%Y-%m-%d %H:%M:%S\")\n", "        elif a >= b:\n", "            return datetime.strptime(\n", "                str(c - timedelta(days=1)) + \" \" + str(a) + \":00\", \"%Y-%m-%d %H:%M:%S\"\n", "            )\n", "        else:\n", "            return ()\n", "    else:\n", "        return ()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_df[\"exp_dis_time\"] = new_df.apply(\n", "    lambda row: exp_disp_time(\n", "        str(row[\"dispatch_time_sco\"]), str(row[\"slot_start\"]), row[\"delivery_time\"]\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Defining a function to choose expected dispatch time from logex(raw data) in case the data isn't avilable in sco\n", "def fin_exp(a, b):\n", "    if a == ():\n", "        return b\n", "    else:\n", "        return a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_df[\"fin_exp_dis_time\"] = new_df.apply(\n", "    lambda row: fin_exp(row[\"exp_dis_time\"], row[\"expected_dispatch_end_time\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = new_df.drop(\n", "    [\n", "        \"disp_lag\",\n", "        \"slot_start\",\n", "        \"slot_end\",\n", "        \"dispatch_point_name\",\n", "        \"day_of_the_week\",\n", "        \"handover_time\",\n", "        \"handover_before\",\n", "        \"dispatch_time_sco\",\n", "        \"dispatch_before\",\n", "        \"exp_dis_time\",\n", "        \"disp_lag_flag\",\n", "        \"expected_dispatch_end_time\",\n", "    ],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculating the lag in minutes (dispatch_time - expected dispatch time)\n", "df[\"lag\"] = new_df.apply(\n", "    lambda row: (\n", "        (row[\"dispatch_time\"] - row[\"fin_exp_dis_time\"]).total_seconds() / 60.0\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Adding a display lag flag based on the absolute lag value\n", "df.loc[df[\"lag\"] > 30, \"disp_lag_flag\"] = \"delayed\"\n", "df.loc[df[\"lag\"] <= 30, \"disp_lag_flag\"] = \"on_time\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"schedule_date\"] = new_df.apply(\n", "    lambda row: row[\"scheduled_delivery_timestamp\"].date(), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Computing the count of cancelled orders at a facility level\n", "df2 = df.loc[df[\"status\"].isin([\"REMOVED\", \"CANCELLED_DISPATCH\"])]\n", "fin_df_can = (\n", "    df2.groupby([\"facility_id\", \"facility_name\", \"schedule_date\"])\n", "    .agg({\"order_id\": pd.Series.nunique})\n", "    .reset_index()\n", ")\n", "fin_df_can.rename(columns={\"order_id\": \"cancelled_before_dispatch\"}, inplace=True)\n", "# fin_df_can.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df.loc[df['lag'] <= 30, 'lag_count'] = 1\n", "# df.loc[df['lag'] > 30, 'lag_count'] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Computing the count of otd orders at a facility level\n", "df11 = df.loc[df[\"disp_lag_flag\"] == \"on_time\"]\n", "df1 = df11.loc[df11[\"flag_dispatch\"] == \"DISPATCHED\"]\n", "fin_df_otd = (\n", "    df1.groupby([\"facility_id\", \"facility_name\", \"schedule_date\"])\n", "    .agg({\"order_id\": pd.Series.nunique})\n", "    .reset_index()\n", ")\n", "fin_df_otd.rename(columns={\"order_id\": \"otd_cnt\"}, inplace=True)\n", "# fin_df_otd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mer1 = pd.merge(\n", "    fin_df_can,\n", "    fin_df_otd,\n", "    how=\"outer\",\n", "    on=[\"facility_id\", \"facility_name\", \"schedule_date\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Computing the count of billed orders at a facility level\n", "fin_df = (\n", "    df.groupby([\"facility_id\", \"facility_name\", \"schedule_date\"])\n", "    .agg({\"order_id\": pd.Series.nunique})\n", "    .reset_index()\n", ")\n", "fin_df.rename(columns={\"order_id\": \"billed\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Computing the count of dispatched orders at a facility level\n", "df3 = df.loc[df[\"flag_dispatch\"] == \"DISPATCHED\"]\n", "fin_df_dis = (\n", "    df3.groupby([\"facility_id\", \"facility_name\", \"schedule_date\"])\n", "    .agg({\"order_id\": pd.Series.nunique})\n", "    .reset_index()\n", ")\n", "fin_df_dis.rename(columns={\"order_id\": \"dispatched_orders\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mer2 = pd.merge(\n", "    fin_df_dis, mer1, how=\"outer\", on=[\"facility_id\", \"facility_name\", \"schedule_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creating a summary dataframe with all details\n", "merged_df = pd.merge(\n", "    fin_df, mer2, how=\"left\", on=[\"facility_id\", \"facility_name\", \"schedule_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df.rename(\n", "    columns={\n", "        \"order_id_x\": \"billed\",\n", "        \"lag_count\": \"otd_cnt\",\n", "        \"cancelled_count\": \"cancelled_before_dispatch\",\n", "        \"order_id_y\": \"dispatched_orders\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df[\"adoption\"] = merged_df.apply(\n", "    lambda row: round(\n", "        (\n", "            (100 * row[\"dispatched_orders\"])\n", "            / (row[\"billed\"] - row[\"cancelled_before_dispatch\"])\n", "        ),\n", "        2,\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df[\"otd_perc\"] = merged_df.apply(\n", "    lambda row: round(((100 * row[\"otd_cnt\"]) / (row[\"dispatched_orders\"])), 2), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df = merged_df[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"schedule_date\",\n", "        \"billed\",\n", "        \"cancelled_before_dispatch\",\n", "        \"dispatched_orders\",\n", "        \"adoption\",\n", "        \"otd_cnt\",\n", "        \"otd_perc\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df[\"billed\"] = merged_df[\"billed\"].fillna(0)\n", "merged_df[\"cancelled_before_dispatch\"] = merged_df[\"cancelled_before_dispatch\"].fillna(\n", "    0\n", ")\n", "merged_df[\"dispatched_orders\"] = merged_df[\"dispatched_orders\"].fillna(0)\n", "merged_df[\"adoption\"] = merged_df[\"adoption\"].fillna(0)\n", "merged_df[\"otd_cnt\"] = merged_df[\"otd_cnt\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_df = merged_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Preparing the data sets to be written to the google sheet document\n", "raw_data = df[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"source\",\n", "        \"outlet\",\n", "        \"outlet_name\",\n", "        \"destination_dp\",\n", "        \"order_batch_id\",\n", "        \"scheduled_delivery_timestamp\",\n", "        \"slot\",\n", "        \"order_id\",\n", "        \"expected_handover_end_time\",\n", "        \"actual_handover_end_time\",\n", "        \"fin_exp_dis_time\",\n", "        \"dispatch_time\",\n", "        \"lag\",\n", "        \"delivery_time\",\n", "        \"status\",\n", "        \"dispatched_containers\",\n", "        \"flag_dispatch\",\n", "        \"disp_lag_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data.rename(\n", "    columns={\"lag\": \"disp_lag\", \"fin_exp_dis_time\": \"expected_dispatch_end_time\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exp_time = (\n", "    raw_data.groupby(\n", "        [\n", "            \"facility_name\",\n", "            \"outlet_name\",\n", "            \"destination_dp\",\n", "            \"expected_handover_end_time\",\n", "            \"actual_handover_end_time\",\n", "            \"expected_dispatch_end_time\",\n", "            \"dispatch_time\",\n", "            \"slot\",\n", "        ]\n", "    )\n", "    .agg({\"order_id\": pd.Series.nunique})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Preparing a summary table to be shared via email\n", "summary_table = summary_df.to_html(index=False)\n", "temp = data.style.set_properties(**{\"text-align\": \"center\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1xxXSjOqwVwmy4sWbaCua1xxsTbVcDoUzHs2uiFWT9vw\"\n", "rec = pb.from_sheets(sheet_id, \"mail_list\", service_account=\"service_account\")\n", "\n", "mail_list = rec[\"rec\"].reset_index(drop=True)\n", "mail_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len = mail_list.size\n", "\n", "final_rec = []\n", "for i in range(0, len):\n", "    x = mail_list[i]\n", "    final_rec.append(x)\n", "final_rec"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "# from_email = \"<EMAIL>\"\n", "to_email = final_rec\n", "# to_email = ['<EMAIL>','<EMAIL>']\n", "subject = \"Dispatch adherence & OTD summary for \" + str(end_date)\n", "\n", "\n", "html_content = (\n", "    \"\"\"\n", "Hi,<br><br>\n", "\n", "Please find the adoption summary & OTD for dispatch process from orders dispatched yesterday.<br><br>\n", "\n", "\"\"\"\n", "    + summary_table\n", "    + \"\"\"<br><br>\n", "\n", "Raw data for orders dispatched without service can be found \n", "<a href='https://docs.google.com/spreadsheets/d/1xxXSjOqwVwmy4sWbaCua1xxsTbVcDoUzHs2uiFWT9vw/edit#gid=0'>\n", "here.\n", "</a>\n", "<br><br>\n", "Best,<br>\n", "Data Team\n", "\"\"\"\n", ")\n", "\n", "\n", "to_email = final_rec\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content\n", "    #     , files=['raw_data.csv'],\n", ")\n", "print(\"mail sent\")\n", "\n", "\n", "sheet_id = \"1xxXSjOqwVwmy4sWbaCua1xxsTbVcDoUzHs2uiFWT9vw\"\n", "sheet_name = \"raw_data\"\n", "pb.to_sheets(raw_data, sheet_id, sheet_name, service_account=\"service_account\")\n", "pb.to_sheets(summary_df, sheet_id, \"Summary\", service_account=\"service_account\")\n", "pb.to_sheets(exp_time, sheet_id, \"expected time T-1\", service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_df[\"facility_id1\"] = summary_df[\"facility_id\"].astype(int)\n", "summary_df = summary_df.drop(columns=[\"facility_id\"])\n", "summary_df.rename(columns={\"facility_id1\": \"facility_id\"}, inplace=True)\n", "summary_df = summary_df[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"schedule_date\",\n", "        \"billed\",\n", "        \"cancelled_before_dispatch\",\n", "        \"dispatched_orders\",\n", "        \"adoption\",\n", "        \"otd_cnt\",\n", "        \"otd_perc\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\"name\": \"schedule_date\", \"type\": \"datetime\", \"description\": \"schedule_date\"},\n", "    {\"name\": \"billed\", \"type\": \"float\", \"description\": \"billed\"},\n", "    {\n", "        \"name\": \"cancelled_before_dispatch\",\n", "        \"type\": \"float\",\n", "        \"description\": \"cancelled_before_dispatch\",\n", "    },\n", "    {\"name\": \"dispatched_orders\", \"type\": \"float\", \"description\": \"dispatched_orders\"},\n", "    {\"name\": \"adoption\", \"type\": \"float\", \"description\": \"adoption\"},\n", "    {\"name\": \"otd_cnt\", \"type\": \"float\", \"description\": \"otd_cnt\"},\n", "    {\"name\": \"otd_perc\", \"type\": \"float\", \"description\": \"otd_perc\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"otd_summary\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"schedule_date\"],\n", "    \"sortkey\": [\"schedule_date\", \"facility_id\"],\n", "    \"incremental_key\": \"schedule_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This tables stores the OTD performance summary\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(summary_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# end of code"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}