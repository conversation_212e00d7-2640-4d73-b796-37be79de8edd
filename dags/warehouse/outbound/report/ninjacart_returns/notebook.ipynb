{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while True:\n", "    try:\n", "        sheet_id = \"1ogq1cSPRzGb_8Q6Ghw6U2sH3rmb9NOVehGfEAmcTeYw\"\n", "        sheet_name = \"Sheet1\"\n", "        OutletEmails = pb.from_sheets(sheet_id, sheet_name)\n", "        break\n", "    except:\n", "        print(\"Oops!  That was an error.  Try again...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletEmails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletEmails = OutletEmails.replace(\"1021\", \"1098\")\n", "OutletEmails.drop(OutletEmails[OutletEmails[\"OutletID \"] == \"1074\"].index, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OutletEmails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Outlets = OutletEmails[\"OutletID \"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"select  wli.reason, \n", "        wli.sub_order_id, \n", "        wli.scheduleddate::date,\n", "        wli.item_id,\n", "        wli.item_name,\n", "        wli.outlet_id as outlet_id,\n", "        co.name as outlet_name,\n", "        whreturntaskid as Return_Task,\n", "        case when inward_date is null then 'Not Inwarded' else 'Inwarded' end as InwardStatus,\n", "        inward_date,\n", "        sum(expected_quantity) as expected_quantity,\n", "        sum(inward_quantity) as inward_quantity,\n", "        sum(expectedvalue) as expectedvalue,\n", "        sum(inwardvalue) as inwardvalue\n", "from metrics.returns_wh_lm_integration wli\n", "left join lake_retail.console_outlet co on co.id=wli.outlet_id\n", "where wli.outlet_id=%(outlet_id)s and wli.scheduleddate::date between current_date-10 and current_date-3\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in Outlets:\n", "    print(\n", "        outlet_id,\n", "        pd.read_sql_query(\n", "            sql=Query, con=redshift, params={\"outlet_id\": outlet_id}\n", "        ).shape,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet_id in Outlets:\n", "    Data = pd.read_sql_query(sql=Query, con=redshift, params={\"outlet_id\": outlet_id})\n", "\n", "    Data.to_csv(\"/tmp/Returns_T3_T10.csv\")\n", "\n", "    import jinja2\n", "    import pencilbox as pb\n", "    from jinja2 import Template\n", "\n", "    from_email = \"<EMAIL>\"\n", "    subject = \"Returns for the Outlet\" + str(outlet_id) + \" for T-10 to T-3\"\n", "\n", "    to_email = OutletEmails[OutletEmails[\"OutletID \"] == str(outlet_id)][\n", "        \"Email\"\n", "    ].tolist()\n", "\n", "    message_text = \"Please find attached csv file for the Outlet \" + str(outlet_id)\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        cc=[\"<EMAIL>\"],\n", "        bcc=[],\n", "        html_content=message_text,\n", "        files=[\"/tmp/Returns_T3_T10.csv\"],\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}