{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_conn = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class B2BExcessStockTransferReport:\n", "    def __init__(self):\n", "\n", "        try:\n", "            start_date = (\n", "                datetime.today() + timedelta(hours=5.5) - timed<PERSON>ta(days=5)\n", "            ).date()\n", "            end_date = (\n", "                datetime.today() + timedelta(hours=5.5) - timedelta(days=1)\n", "            ).date()\n", "            start_date_time_str = start_date.strftime(\"%Y-%m-%d\")\n", "            end_date_time_str = end_date.strftime(\"%Y-%m-%d\")\n", "            start_date_time = start_date_time_str + \" 18:30:00\"\n", "            end_date_time = end_date_time_str + \" 18:30:00\"\n", "            self.df = pd.DataFrame(\n", "                columns=[\n", "                    \"outlet_id\",\n", "                    \"outlet_name\",\n", "                    \"outlet_location\",\n", "                    \"merchant_name\",\n", "                    \"merchant_id\",\n", "                    \"sto_id\",\n", "                    \"invoice_id\",\n", "                    \"billing_date\",\n", "                    \"item_id\",\n", "                    \"variant_id\",\n", "                    \"product_name\",\n", "                    \"item_type\",\n", "                    \"variant_mrp\",\n", "                    \"grammage\",\n", "                    \"quantity\",\n", "                    \"cgst_per\",\n", "                    \"sgst_per\",\n", "                    \"igst_per\",\n", "                    \"cess_per\",\n", "                    \"unit_selling_price\",\n", "                    \"total_selling_price\",\n", "                ]\n", "            )\n", "            self.fetch_outlets_and_compute(start_date_time, end_date_time, end_date)\n", "\n", "        except Exception as e:\n", "            print(e)\n", "\n", "    def fetch_outlets_and_compute(self, start_date_time, end_date_time, end_date):\n", "\n", "        self.sales_invoice_df = self.get_sales_report(start_date_time, end_date_time)\n", "        outlet_ids = self.sales_invoice_df[\"outlet_id\"].unique()\n", "\n", "        for outlet_id in outlet_ids:\n", "            outlet_invoice_df = self.compute_invoice_details(\n", "                outlet_id, start_date_time, end_date_time\n", "            )\n", "            self.df = self.df.append(outlet_invoice_df, sort=False)\n", "\n", "    def compute_invoice_details(self, outlet_id, start_date_time, end_date_time):\n", "\n", "        invoice_landing_price_df = self.get_invoice_hot_landing_price(\n", "            outlet_id, start_date_time, end_date_time\n", "        )\n", "        invoice_landing_price_df[\"merchant_id\"] = invoice_landing_price_df[\n", "            \"merchant_id\"\n", "        ].astype(\"int\")\n", "        required_invoice_df = self.sales_invoice_df.merge(\n", "            invoice_landing_price_df,\n", "            how=\"inner\",\n", "            on=[\"sto_id\", \"invoice_id\", \"merchant_id\", \"variant_id\", \"quantity\"],\n", "        )\n", "        return required_invoice_df\n", "\n", "    def get_invoice_hot_landing_price(self, outlet_id, start_date_time, end_date_time):\n", "\n", "        query = \"\"\"SELECT \n", "                    variant_id,\n", "                    grofers_order_id as sto_id,\n", "                    invoice_id,\n", "                    merchant_id,\n", "                    sum(delta) as quantity,\n", "                    avg(transaction_lp) as \"hot_landing_price\",\n", "                    (sum(delta) * avg(transaction_lp)) as \"total_value\" \n", "                FROM ims.ims_inventory_log \n", "                WHERE outlet_id={outlet_id} AND pos_timestamp BETWEEN '{start_date_time}' AND '{end_date_time}' and inventory_update_type_id in (124,92) GROUP BY 1,2,3,4;\"\"\"\n", "        query = query.format(\n", "            start_date_time=start_date_time,\n", "            end_date_time=end_date_time,\n", "            outlet_id=outlet_id,\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail_conn)\n", "        return rows\n", "\n", "    def get_sales_report(self, start_date_time, end_date_time):\n", "\n", "        query = \"\"\"SELECT\n", "                    o.id as \"outlet_id\",\n", "                    o.name as \"outlet_name\",\n", "                    out_loc.name as \"outlet_location\",\n", "                    pi.customer_name as \"merchant_name\",\n", "                    cm.id as merchant_id,\n", "                    pi.grofers_order_id as \"sto_id\",\n", "                    pi.invoice_id as \"invoice_id\",\n", "                    pp.item_id as \"item_id\",\n", "                    pp.variant_id as \"variant_id\",\n", "                    pp.name as \"product_name\",\n", "                    (CASE\n", "                        WHEN pp.outlet_type = 1 THEN 'F&V type'\n", "                        WHEN pp.outlet_type = 2 THEN 'Grocery type'\n", "                        ELSE 'Not Assigned'\n", "                    END) AS \"item_type\",\n", "                    pp.variant_mrp as \"variant_mrp\",\n", "                    pp.variant_uom_text as \"grammage\",\n", "                    pd.quantity as \"quantity\",\n", "                    coalesce(pd.igst_per,0)  as \"igst_per\",\n", "                    coalesce(pd.cgst_per,0)  as \"cgst_per\",\n", "                    coalesce(pd.sgst_per,0)  as \"sgst_per\",\n", "                    coalesce(pd.cess_per,0)  as \"cess_per\",\n", "                    Date(convert_tz(pi.pos_timestamp,'+00:00','+05:30')) as \"billing_date\",\n", "                    pd.selling_price as \"unit_selling_price\",\n", "                    round(pd.quantity*pd.selling_price,2) as \"total_selling_price\"\n", "\n", "                FROM\n", "                pos.pos_invoice pi\n", "                    INNER JOIN\n", "                pos.pos_invoice_product_details pd on pi.id=pd.invoice_id\n", "                    INNER JOIN\n", "                rpc.product_product pp on pp.variant_id = pd.variant_id\n", "                    INNER JOIN\n", "                retail.console_outlet o on o.id=pi.outlet_id\n", "                    INNER JOIN\n", "                retail.console_merchant cm on cm.id=pi.merchant_id\n", "                    INNER JOIN\n", "                retail.console_outlet  m_out on cm.outlet_id=m_out.id\n", "                    INNER JOIN\n", "                retail.console_company_type mer_type on mer_type.id=m_out.company_type_id\n", "                    INNER JOIN\n", "                retail.console_company_type out_type on out_type.id=o.company_type_id\n", "                    INNER JOIN\n", "                retail.console_location out_loc on out_loc.id=o.tax_location_id\n", "                    INNER JOIN\n", "                retail.console_location mer_loc on mer_loc.id=m_out.tax_location_id\n", "                WHERE  pi.invoice_type_id=14  and pi.pos_timestamp between '{start_date_time}' AND '{end_date_time}' AND (mer_type.entity_type = \"THIRD_PARTY\" or out_type.entity_type = \"THIRD_PARTY\") order by o.id;\"\"\"\n", "        query = query.format(\n", "            start_date_time=start_date_time,\n", "            end_date_time=end_date_time,\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail_conn)\n", "        return rows\n", "\n", "\n", "B2BExcessStockTransferReport()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = B2BExcessStockTransferReport()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = x.df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = df1.drop([\"outlet_id\", \"merchant_id\", \"variant_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = (datetime.today() + timedelta(hours=5.5) - timedelta(days=1)).date()\n", "df1.to_csv(\n", "    r\"b2b_excess_stock_transfer_report.csv.gz\",\n", "    index=False,\n", "    header=True,\n", "    compression=\"gzip\",\n", ")\n", "from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"B2B Excess stock transfer report\"\n", "html_content = \"<p>Please find the attachment.<p>\"\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[\"b2b_excess_stock_transfer_report.csv.gz\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}