{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Supply chain lean index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "!pip install pandasql\n", "pd.set_option(\"display.max_columns\", 50)\n", "\n", "import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"picker_id\": \"picker employee id\",\n", "    \"active_hours\": \"active working hours of picker on the day\",\n", "    \"shift_time\": \"shift time of picker defined\",\n", "    \"availability\": \"time availability of picker: active hours/shift time\",\n", "    \"total_orders_picked\": \"orders picked by picker on the picking day\",\n", "    \"IPP/Hr\": \"ideal items per picker per hour - a number defined for each outlet \",\n", "    \"Required Qty (Available Hrs)\": \"required quantity based on available hours\",\n", "    \"Required Qty (Shift Hrs)\": \"required quantity based on Shift hours\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipp = pb.from_sheets(\"12su3M8qDORUunSxGXM5jB786vCdzQVliSkeIH-aWiMI\", \"Input\").iloc[\n", "    :, [0, 2]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["j = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = pd.DataFrame()\n", "for i in range(j, 0, -1):\n", "    today = date.today()\n", "    tz = pytz.timezone(\"Asia/Kolkata\")\n", "    RUN_DATE = (datetime.now(tz) - timedelta(days=i)).strftime(\"%Y-%m-%d\")\n", "    print(RUN_DATE)\n", "    query = \"\"\"SELECT \n", "              co.facility_id,\n", "              f.name as facility_name,\n", "              x.picking_completed_at_ist::date AS picking_date,\n", "              x.picker_id,\n", "              d.id as device_id,\n", "              x.picker_name,\n", "              x.user_role,\n", "              count(pick_list_id) AS Total_Orders_Picked,\n", "              sum(required_quantity) AS Actual_Required_Qty,\n", "              sum(picked_quantity) AS Actual_Picked_Qty\n", "       FROM metrics.picklist_completed x\n", "       left join lake_retail.console_outlet co on co.id = x.outlet_id \n", "       left join lake_crates.facility f on f.id = co.facility_id\n", "       left join lake_warehouse_location.warehouse_devices d on d.imei = x.device_imei\n", "       WHERE picking_completed_at_ist::date = %(run_date)s and user_role in ('Picker') and business_type_id not in (7,8)\n", "       GROUP BY 1,\n", "                2,\n", "                3,\n", "                4,\n", "                5,\n", "                6,\n", "                7\"\"\"\n", "    data = pd.read_sql_query(\n", "        sql=query,\n", "        con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "        params={\"run_date\": RUN_DATE},\n", "    )\n", "\n", "    query1 = \"\"\"\n", "    select co.facility_id,f.name as facility_name,role, dl.user_name as emp_id, wu.name as user_name, dl.device_id,action, (pos_timestamp + interval '5.5 hrs') as pos_timestamp \n", "    from lake_warehouse_location.warehouse_device_user_log dl\n", "    left join lake_retail.console_outlet co on co.id = dl.outlet_id\n", "    left join lake_crates.facility f on f.id = co.facility_id\n", "    left join lake_retail.warehouse_user wu on wu.emp_id = dl.user_name\n", "    where date(pos_timestamp + interval '5.5 hrs') = %(run_date)s and action in ('LOGIN','LOGOUT','FORCE_LOGOUT')\n", "    and role in ('PICKER')\n", "    order by 1,2,3,4,6,8\n", "    \"\"\"\n", "    data1 = pd.read_sql_query(\n", "        sql=query1,\n", "        con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "        params={\"run_date\": RUN_DATE},\n", "    )\n", "    data_final = data\n", "    pickers = data_final.picker_id.unique()\n", "\n", "    data2 = data1[data1[\"emp_id\"].isin(pickers)]\n", "    import pandasql as ps\n", "\n", "    enter_to_purchase_time = ps.sqldf(\n", "        \"\"\"\n", "        SELECT distinct p.facility_id,p.role,p.emp_id,p.user_name,p.device_id,\n", "        max(p.pos_timestamp) over (partition by p.emp_id,p.device_id, p.pos_timestamp order by p.pos_timestamp desc) as start, \n", "        min(e.pos_timestamp) over (partition by p.emp_id,p.device_id, p.pos_timestamp order by p.pos_timestamp desc) as end\n", "        FROM data2 p\n", "        inner join data2 e on e.emp_id = p.emp_id and e.device_id = p.device_id\n", "        where  p.action in ('LOGIN') and e.action in ('LOGOUT','FORCE_LOGOUT')\n", "         and e.pos_timestamp > p.pos_timestamp\n", "        order by e.pos_timestamp\"\"\",\n", "        locals(),\n", "    )\n", "    enter_to_purchase_time[\"diff\"] = pd.to_datetime(\n", "        enter_to_purchase_time[\"end\"]\n", "    ) - pd.to_datetime(enter_to_purchase_time[\"start\"])\n", "    final_time = pd.DataFrame()\n", "    for m in pickers:\n", "        x = data1[data1[\"emp_id\"] == m]\n", "        devices = x.device_id.dropna().unique()\n", "        for w in devices:\n", "            delta1 = np.nan\n", "            delta2 = np.nan\n", "            today = RUN_DATE\n", "            yesterday = date.today() - <PERSON><PERSON><PERSON>(days=i - 1)\n", "            y = enter_to_purchase_time[\n", "                (enter_to_purchase_time[\"emp_id\"] == m)\n", "                & ((enter_to_purchase_time[\"device_id\"] == w))\n", "            ][\"diff\"].sum()\n", "            if x.shape[0] > 0:\n", "                if x[x[\"device_id\"] == w].iloc[0, :][\"action\"] in (\n", "                    \"LOGOUT\" \"FORCE_LOGOUT\"\n", "                ):\n", "                    action = x[x[\"device_id\"] == w].iloc[0, :][\"action\"]\n", "                    min_logout = x[(x[\"device_id\"] == w) & (x[\"action\"] == action)][\n", "                        \"pos_timestamp\"\n", "                    ].min()\n", "                    delta1 = pd.to_datetime(min_logout) - pd.to_datetime(today)\n", "                elif x[x[\"device_id\"] == w].iloc[-1, :][\"action\"] == \"LOGIN\":\n", "                    max_login = x[\n", "                        (x[\"device_id\"] == w) & (x[\"action\"].isin([\"LOGIN\"]))\n", "                    ][\"pos_timestamp\"].max()\n", "                    delta2 = pd.to_datetime(yesterday) - pd.to_datetime(max_login)\n", "            final = pd.DataFrame([y, delta1, delta2]).sum()\n", "            z = pd.DataFrame({\"emp_id\": m, \"device_id\": w, \"final_time\": final})\n", "            final_time = final_time.append(z)\n", "    summ = data_final.merge(\n", "        final_time,\n", "        left_on=[\"picker_id\", \"device_id\"],\n", "        right_on=[\"emp_id\", \"device_id\"],\n", "        how=\"inner\",\n", "    )\n", "\n", "    summ = summ.drop(columns=[\"emp_id\"])\n", "\n", "    summ[\"active_hours\"] = summ[\"final_time\"] / pd.Timedelta(hours=1)\n", "\n", "    summ[summ[\"active_hours\"] == summ[\"active_hours\"].max()]\n", "\n", "    summ[\"shift_time\"] = 8\n", "    summ = summ.merge(ipp.astype(int), how=\"left\", on=[\"facility_id\"])\n", "\n", "    summ = summ.drop(columns=\"final_time\")\n", "    summ[\"IPP/Hr\"] = summ[\"IPP/Hr\"].fillna(120)\n", "    summ[\"IPP/Hr\"] = summ[\"IPP/Hr\"].astype(int)\n", "\n", "    summ[\"availability\"] = summ[\"active_hours\"] / summ[\"shift_time\"] * 100\n", "    summ[\"Required Qty (Available Hrs)\"] = summ[\"active_hours\"] * summ[\"IPP/Hr\"]\n", "    summ[\"Required Qty (Shift Hrs)\"] = summ[\"shift_time\"] * summ[\"IPP/Hr\"]\n", "    summ[\"Efficiency (Available Hrs)\"] = (\n", "        summ[\"actual_picked_qty\"] / summ[\"Required Qty (Available Hrs)\"] * 100\n", "    )\n", "    summ[\"Efficiency (Shift Hrs)\"] = (\n", "        summ[\"actual_picked_qty\"] / summ[\"Required Qty (Shift Hrs)\"] * 100\n", "    )\n", "\n", "    summ[\"Overall Picking Efficiency\"] = (\n", "        summ[\"availability\"] * summ[\"Efficiency (Available Hrs)\"] / 100\n", "    )\n", "    summ_final = summ[\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"picking_date\",\n", "            \"picker_id\",\n", "            \"device_id\",\n", "            \"picker_name\",\n", "            \"active_hours\",\n", "            \"shift_time\",\n", "            \"availability\",\n", "            \"total_orders_picked\",\n", "            \"actual_picked_qty\",\n", "            \"IPP/Hr\",\n", "            \"Required <PERSON><PERSON> (Available Hrs)\",\n", "            \"Required <PERSON><PERSON> (Shift Hrs)\",\n", "            \"Efficiency (Available Hrs)\",\n", "            \"Efficiency (Shift Hrs)\",\n", "        ]\n", "    ]\n", "\n", "    summ_final = summ_final.sort_values(by=\"Efficiency (Shift Hrs)\", ascending=False)\n", "    print(summ_final.picking_date.unique(), summ_final.shape)\n", "    print(summ_final.active_hours.min(), summ_final.active_hours.max())\n", "\n", "    summ_final[\"key\"] = (\n", "        summ_final[\"picker_id\"].astype(str)\n", "        + \"|\"\n", "        + summ_final[\"picking_date\"].astype(str)\n", "    )\n", "    summ_final.head(2)\n", "    final_data = pd.concat([final_data, summ_final], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "\n", "column_dtypes = redshift_schema(final_data)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"picker_supplychain_leanindex\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"facility_id\"],\n", "    \"incremental_key\": \"picking_date\",\n", "    \"table_description\": \"to store picker performance details - supply chain lean index(SCLI)\",\n", "    \"load_type\": \"upsert\",  # upsert, rebuild, truncate or upsert\n", "}\n", "pb.to_redshift(final_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    summ_final.iloc[:, 0:16], \"12su3M8qDORUunSxGXM5jB786vCdzQVliSkeIH-aWiMI\", \"Data\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary MTD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import calendar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date.today().replace(day=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = date.today()\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "START_DATE = (datetime.now(tz) - timed<PERSON>ta(days=30)).date()\n", "END_DATE = (datetime.now(tz) - timed<PERSON>ta(days=1)).date()\n", "if (datetime.now(tz) - <PERSON><PERSON><PERSON>(days=1)).day > 3:\n", "    month = datetime.now(tz).month\n", "    last_day = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "    start_day = date.today().replace(day=1)\n", "else:\n", "    month = datetime.now(tz).month - 1\n", "    last_day = date.today().replace(day=1) - <PERSON><PERSON><PERSON>(days=1)\n", "    start_day = date.today().replace(day=1).replace(month=month)\n", "print(START_DATE, END_DATE)\n", "# print(month)\n", "print(start_day, last_day)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT facility_id,\n", "       facility_name,\n", "       count(DISTINCT picking_date) AS date_considered,\n", "       --count(picker_id) AS number_of_pickers,\n", "       sum(active_hours) AS total_active_hours,\n", "       sum(shift_time) AS total_shift_time,\n", "       sum(active_hours)/sum(shift_time)*100 AS \"availability_average(percent)\",\n", "       sum(total_orders_picked) AS total_orders_picked,\n", "       sum(actual_picked_qty) AS total_actual_picked_qty,\n", "       avg(\"IPP/Hr\") AS \"IPP/Hr\",\n", "       avg(\"IPP/Hr\")*total_active_hours as \"total_required_quantity (Available hrs)\",\n", "       total_actual_picked_qty/\"total_required_quantity (Available hrs)\"*100 as \"Efficiency Available Hrs(percent)\",\n", "       \"availability_average(percent)\"*\"Efficiency Available Hrs(percent)\"/100 as \"Efficiency Shift Hrs(percent)\"\n", "FROM metrics.picker_supplychain_leanindex\n", "where date(picking_date) between %(start_day)s and %(end_day)s\n", "GROUP BY 1,\n", "         2\n", "         \"\"\"\n", "data_30 = pd.read_sql_query(\n", "    sql=query,\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "    params={\"start_day\": START_DATE, \"end_day\": END_DATE},\n", ")\n", "data_mtd = pd.read_sql_query(\n", "    sql=query,\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", "    params={\"start_day\": start_day, \"end_day\": last_day},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    data_30, \"12su3M8qDORUunSxGXM5jB786vCdzQVliSkeIH-aWiMI\", \"Summary_last_30days\"\n", ")\n", "pb.to_sheets(data_mtd, \"12su3M8qDORUunSxGXM5jB786vCdzQVliSkeIH-aWiMI\", \"Summary_MTD\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}