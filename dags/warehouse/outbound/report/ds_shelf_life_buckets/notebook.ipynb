{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import numpy as np\n", "import pytz\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "pd.set_option(\"display.max_columns\", None)\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_id\n", "# outlet_id\n", "# outlet_name\n", "# item_id\n", "# variant_id\n", "# item_name\n", "# l0\n", "# l0_id\n", "# l1\n", "# l1_id\n", "# l2\n", "# l2_id\n", "# shelf_life\n", "# landing_price\n", "# rtv_flag\n", "# 100-71% life left\n", "# 70-51% life left\n", "# 50-41% life left\n", "# 40-36% life left\n", "# 35-33% life left\n", "# below 32% life left\n", "# expired\n", "# total_quantity\n", "# etl_timestamp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_sql_query(\n", "    f\"\"\"SELECT \n", "    outlet.facility_id,\n", "    f.name as facility_name,\n", "    outlet.id as \"outlet_id\",\n", "    outlet.name as \"outlet_name\",\n", "    l.name as city_name,\n", "    p.item_id as \"item_id\",\n", "    il.variant_id as \"variant_id\",\n", "    p.name as \"product_name\",\n", "    p.variant_mrp as \"mrp\",\n", "    pb.name as \"brand_name\",\n", "    pt.name as product_type,\n", "    l0,\n", "    l0_id,\n", "    l1,\n", "    l1_id,\n", "    l2,\n", "    l2_id,\n", "    p.shelf_life as product_shelf_life,\n", "    sum(il.quantity) as \"total_quantity\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int)  between 71 and 100 \n", "         then il.quantity else 0 end) as \"100-71%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int)  between 51 and 70 \n", "         then il.quantity else 0 end) as \"70-51%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int)  between 41 and 50\n", "         then il.quantity else 0 end) as \"50-41%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int)  between 36 and 40 \n", "         then il.quantity else 0 end) as \"40-36%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int)  between 33 and 35 \n", "         then il.quantity else 0 end) as \"35-33%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int) between 0 and 32 \n", "         then il.quantity else 0 end) as \"below 32%% life left\",\n", "    sum(case when cast((date_diff('day',current_date,il.batch)*100.0/p.shelf_life) as int) <= 0\n", "         then il.quantity else 0 end) as \"expired\"\n", "FROM lake_view_warehouse_location.warehouse_item_location il\n", "INNER JOIN lake_view_warehouse_location.warehouse_outlet_mapping m on m.warehouse_id = il.warehouse_id \n", "INNER JOIN lake_view_rpc.product_product p ON p.variant_id= il.variant_id\n", "INNER JOIN lake_view_rpc.item_product_mapping ip on ip.item_id = p.item_id \n", "INNER JOIN lake_cms.gr_product gp on gp.id = ip.product_id \n", "INNER JOIN lake_cms.gr_product_type pt on gp.type_id=pt.id\n", "INNER JOIN lake_view_retail.console_outlet outlet on outlet.id= m.outlet_id and outlet.active = 1 and outlet.device_id <> 47 and outlet.id <> 451\n", "INNER JOIN lake_view_retail.console_location l on l.id = outlet.tax_location_id\n", "INNER JOIN lake_view_crates.facility f on f.id = outlet.facility_id\n", "LEFT JOIN  lake_view_rpc.item_category_details icd ON icd.item_id = p.item_id\n", "INNER JOIN lake_view_warehouse_location.warehouse_storage_location wsl on il.location_id=wsl.id\n", "INNER JOIN lake_view_rpc.product_brand pb on pb.id=p.brand_id\n", "WHERE outlet.business_type_id in (7) and p.active = 1 and il.quantity > 0\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18\"\"\",\n", "    presto,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"etl_timestamp\"] = pd.to_datetime(datetime.now(ist))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"facility_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "    {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"variant_id\"},\n", "    {\"name\": \"product_name\", \"type\": \"varchar\", \"description\": \"product_name\"},\n", "    {\"name\": \"mrp\", \"type\": \"float\", \"description\": \"mrp\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar\", \"description\": \"brand_name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\"name\": \"l0_id\", \"type\": \"float\", \"description\": \"l0_id\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1\"},\n", "    {\"name\": \"l1_id\", \"type\": \"float\", \"description\": \"l1_id\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2\"},\n", "    {\"name\": \"l2_id\", \"type\": \"float\", \"description\": \"l2_id\"},\n", "    {\"name\": \"product_shelf_life\", \"type\": \"float\", \"description\": \"shelf_life\"},\n", "    {\n", "        \"name\": \"total_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"total_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"100-71% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"100-71% life left\",\n", "    },\n", "    {\n", "        \"name\": \"70-51% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"70-51% life left\",\n", "    },\n", "    {\n", "        \"name\": \"50-41% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"50-41% life left\",\n", "    },\n", "    {\n", "        \"name\": \"40-36% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"40-36% life left\",\n", "    },\n", "    {\n", "        \"name\": \"35-33% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"35-33% life left\",\n", "    },\n", "    {\n", "        \"name\": \"Below 32% life left\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Below 32% life left\",\n", "    },\n", "    {\"name\": \"expired\", \"type\": \"int\", \"description\": \"expired\"},\n", "    {\n", "        \"name\": \"etl_timestamp\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl_timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ds_shelflife_buckets\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"variant_id\"],\n", "    \"sortkey\": [\"outlet_id\", \"variant_id\"],\n", "    \"incremental_key\": \"etl_timestamp\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, upsert\n", "    \"table_description\": \"This tables contains Darkstore Items Quantity in buckets of remaining shelf life as a percentage\",\n", "}\n", "\n", "pb.to_redshift(data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}