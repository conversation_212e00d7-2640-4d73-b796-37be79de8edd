{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_query = \"\"\"\n", "SELECT p.invoice_id AS \"Invoice ID\",\n", "       p.original_invoice_id AS \"Orgianl Invoice ID\",\n", "       (ret_inv.pos_timestamp + interval '5.5 hrs') AS \"Orgianl Invoice Timestamp\",\n", "       p.grofers_order_id AS \"Grofers Order ID\",\n", "       (p.pos_timestamp + interval '5.5 hrs') AS \"POS Timestamp\",\n", "       p.outlet_name AS \"Store Name\",\n", "       l1.name AS \"Supply City\",\n", "       l2.name AS \"Customer City\",\n", "       rp.item_id,\n", "       rp.variant_description,\n", "       rp.hsn_code,\n", "       rp.name AS \"Product Name\",\n", "       (CASE\n", "            WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "            WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"Item Type\",\n", "       COALESCE(pi.cgst_per,0) AS cgst_per,\n", "       COALESCE(pi.sgst_per,0) AS sgst_per,\n", "       COALESCE(pi.igst_per,0) AS igst_per,\n", "       COALESCE(pi.cess_per,0) AS cess_per,\n", "       sum(pi.quantity) AS \"Qty\",\n", "       pi.unit_rate AS \"MRP\",\n", "       (pi.unit_rate - pi.selling_price) AS Discount,\n", "       pi.selling_price AS \"Selling Price\",\n", "       sum(COALESCE(pi.cess_value,0)) AS cess_value,\n", "       sum(COALESCE(pi.cgst_value,0)) AS cgst_value,\n", "       sum(COALESCE(pi.sgst_value,0)) AS sgst_value,\n", "       sum(COALESCE(pi.igst_value,0)) AS igst_value,\n", "       sum(coalesce(pi.vat,0)) AS vat,\n", "       sum(coalesce(pi.cst,0)) AS cst,\n", "       sum(coalesce(pi.surcharge,0)) AS surcharge,\n", "       sum(coalesce(pi.vat,0) + coalesce(pi.cst,0) + coalesce(pi.surcharge,0)) AS \"Total Tax\",\n", "       sum(COALESCE(pi.cess_value,0)+COALESCE(pi.cgst_value,0)+COALESCE(pi.sgst_value,0)+COALESCE(pi.igst_value,0)) AS \"Total GST\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 7 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Gross Bill Amount\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 8 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Return Amount\",\n", "       p.customer_name,\n", "       p.customer_address\n", "FROM (select *\n", "        from lake_pos.pos_invoice\n", "        where date(convert_timezone('Asia/Kolkata',pos_timestamp)) = date(CURRENT_DATE-1)\n", "        and invoice_type_id IN (7, 8)\n", "        AND outlet_name ilike '%%SSC%%'\n", "      ) p \n", "INNER JOIN lake_pos.pos_invoice_product_details pi ON p.id = pi.invoice_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "LEFT JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "LEFT JOIN lake_pos.pos_invoice ret_inv ON ret_inv.invoice_id = p.original_invoice_id\n", "WHERE pi.store_offer_id IS NULL\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11,\n", "         12,\n", "         13,\n", "         14,\n", "         15,\n", "         16,\n", "         17,\n", "         19,\n", "         20,\n", "         21,\n", "         33,\n", "         34,\n", "         p.invoice_type_id,\n", "         p.pos_timestamp\n", "ORDER BY p.pos_timestamp;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_data = pd.read_sql_query(SSC_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_data[\"date\"] = SSC_data[\"pos timestamp\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_agg = SSC_data.groupby([\"date\"])[\n", "    \"qty\", \"total gross bill amount\", \"total return amount\"\n", "].agg(\"sum\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_agg = SSC_agg.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_agg[\"Type of outlet\"] = \"SSC\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SSC_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_query = \"\"\"\n", "SELECT p.invoice_id AS \"Invoice ID\",\n", "       p.original_invoice_id AS \"Orgianl Invoice ID\",\n", "       (ret_inv.pos_timestamp + interval '5.5 hrs') AS \"Orgianl Invoice Timestamp\",\n", "       p.grofers_order_id AS \"Grofers Order ID\",\n", "       (p.pos_timestamp + interval '5.5 hrs') AS \"POS Timestamp\",\n", "       p.outlet_name AS \"Store Name\",\n", "       l1.name AS \"Supply City\",\n", "       l2.name AS \"Customer City\",\n", "       rp.item_id,\n", "       rp.variant_description,\n", "       rp.hsn_code,\n", "       rp.name AS \"Product Name\",\n", "       (CASE\n", "            WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "            WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"Item Type\",\n", "       COALESCE(pi.cgst_per,0) AS cgst_per,\n", "       COALESCE(pi.sgst_per,0) AS sgst_per,\n", "       COALESCE(pi.igst_per,0) AS igst_per,\n", "       COALESCE(pi.cess_per,0) AS cess_per,\n", "       sum(pi.quantity) AS \"Qty\",\n", "       pi.unit_rate AS \"MRP\",\n", "       (pi.unit_rate - pi.selling_price) AS Discount,\n", "       pi.selling_price AS \"Selling Price\",\n", "       sum(COALESCE(pi.cess_value,0)) AS cess_value,\n", "       sum(COALESCE(pi.cgst_value,0)) AS cgst_value,\n", "       sum(COALESCE(pi.sgst_value,0)) AS sgst_value,\n", "       sum(COALESCE(pi.igst_value,0)) AS igst_value,\n", "       sum(coalesce(pi.vat,0)) AS vat,\n", "       sum(coalesce(pi.cst,0)) AS cst,\n", "       sum(coalesce(pi.surcharge,0)) AS surcharge,\n", "       sum(coalesce(pi.vat,0) + coalesce(pi.cst,0) + coalesce(pi.surcharge,0)) AS \"Total Tax\",\n", "       sum(COALESCE(pi.cess_value,0)+COALESCE(pi.cgst_value,0)+COALESCE(pi.sgst_value,0)+COALESCE(pi.igst_value,0)) AS \"Total GST\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 7 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Gross Bill Amount\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 8 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Return Amount\",\n", "       p.customer_name,\n", "       p.customer_address\n", "FROM (select *\n", "        from lake_pos.pos_invoice\n", "        where date(convert_timezone('Asia/Kolkata',pos_timestamp)) = date(CURRENT_DATE-1)\n", "        and invoice_type_id IN (7, 8)\n", "        AND outlet_name ilike '%%HOT%%'\n", "      ) p \n", "INNER JOIN lake_pos.pos_invoice_product_details pi ON p.id = pi.invoice_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "LEFT JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "LEFT JOIN lake_pos.pos_invoice ret_inv ON ret_inv.invoice_id = p.original_invoice_id\n", "WHERE pi.store_offer_id IS NULL\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11,\n", "         12,\n", "         13,\n", "         14,\n", "         15,\n", "         16,\n", "         17,\n", "         19,\n", "         20,\n", "         21,\n", "         33,\n", "         34,\n", "         p.invoice_type_id,\n", "         p.pos_timestamp\n", "ORDER BY p.pos_timestamp;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_data = pd.read_sql_query(HOT_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_data[\"date\"] = HOT_data[\"pos timestamp\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_agg = HOT_data.groupby([\"date\"])[\n", "    \"qty\", \"total gross bill amount\", \"total return amount\"\n", "].agg(\"sum\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_agg = HOT_agg.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_agg[\"Type of outlet\"] = \"HOT\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HOT_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_query = \"\"\"\n", "SELECT p.invoice_id AS \"Invoice ID\",\n", "       p.original_invoice_id AS \"Orgianl Invoice ID\",\n", "       (ret_inv.pos_timestamp + interval '5.5 hrs') AS \"Orgianl Invoice Timestamp\",\n", "       p.grofers_order_id AS \"Grofers Order ID\",\n", "       (p.pos_timestamp + interval '5.5 hrs') AS \"POS Timestamp\",\n", "       p.outlet_name AS \"Store Name\",\n", "       l1.name AS \"Supply City\",\n", "       l2.name AS \"Customer City\",\n", "       rp.item_id,\n", "       rp.variant_description,\n", "       rp.hsn_code,\n", "       rp.name AS \"Product Name\",\n", "       (CASE\n", "            WHEN rp.outlet_type = 1 THEN 'F&V type'\n", "            WHEN rp.outlet_type = 2 THEN 'Grocery type'\n", "            ELSE 'Not Assigned'\n", "        END) AS \"Item Type\",\n", "       COALESCE(pi.cgst_per,0) AS cgst_per,\n", "       COALESCE(pi.sgst_per,0) AS sgst_per,\n", "       COALESCE(pi.igst_per,0) AS igst_per,\n", "       COALESCE(pi.cess_per,0) AS cess_per,\n", "       sum(pi.quantity) AS \"Qty\",\n", "       pi.unit_rate AS \"MRP\",\n", "       (pi.unit_rate - pi.selling_price) AS Discount,\n", "       pi.selling_price AS \"Selling Price\",\n", "       sum(COALESCE(pi.cess_value,0)) AS cess_value,\n", "       sum(COALESCE(pi.cgst_value,0)) AS cgst_value,\n", "       sum(COALESCE(pi.sgst_value,0)) AS sgst_value,\n", "       sum(COALESCE(pi.igst_value,0)) AS igst_value,\n", "       sum(coalesce(pi.vat,0)) AS vat,\n", "       sum(coalesce(pi.cst,0)) AS cst,\n", "       sum(coalesce(pi.surcharge,0)) AS surcharge,\n", "       sum(coalesce(pi.vat,0) + coalesce(pi.cst,0) + coalesce(pi.surcharge,0)) AS \"Total Tax\",\n", "       sum(COALESCE(pi.cess_value,0)+COALESCE(pi.cgst_value,0)+COALESCE(pi.sgst_value,0)+COALESCE(pi.igst_value,0)) AS \"Total GST\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 7 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Gross Bill Amount\",\n", "       CASE\n", "           WHEN p.invoice_type_id = 8 THEN sum(Round(pi.selling_price*pi.quantity,2))\n", "           ELSE 0\n", "       END AS \"Total Return Amount\",\n", "       p.customer_name,\n", "       p.customer_address\n", "FROM (select *\n", "        from lake_pos.pos_invoice\n", "        where date(convert_timezone('Asia/Kolkata',pos_timestamp)) = date(CURRENT_DATE-1)\n", "        and invoice_type_id IN (7, 8)\n", "        AND outlet_name ilike '%%MODI%%'\n", "      ) p \n", "INNER JOIN lake_pos.pos_invoice_product_details pi ON p.id = pi.invoice_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id = p.outlet_id\n", "INNER JOIN lake_rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "INNER JOIN lake_retail.console_location l1 ON l1.id=r.tax_location_id\n", "LEFT JOIN lake_retail.console_location l2 ON l2.id=p.customer_city\n", "LEFT JOIN lake_pos.pos_invoice ret_inv ON ret_inv.invoice_id = p.original_invoice_id\n", "WHERE pi.store_offer_id IS NULL\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7,\n", "         8,\n", "         9,\n", "         10,\n", "         11,\n", "         12,\n", "         13,\n", "         14,\n", "         15,\n", "         16,\n", "         17,\n", "         19,\n", "         20,\n", "         21,\n", "         33,\n", "         34,\n", "         p.invoice_type_id,\n", "         p.pos_timestamp\n", "ORDER BY p.pos_timestamp;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_data = pd.read_sql_query(MODI_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_data[\"date\"] = MODI_data[\"pos timestamp\"].dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_agg = MODI_data.groupby([\"date\"])[\n", "    \"qty\", \"total gross bill amount\", \"total return amount\"\n", "].agg(\"sum\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_agg = MODI_agg.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_agg[\"Type of outlet\"] = \"MODI\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODI_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = SSC_agg.append(HOT_agg).append(MODI_agg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.rename(\n", "    columns={\n", "        \"qty\": \"Total Quantity\",\n", "        \"total gross bill amount\": \"Total Gross Bill Amount\",\n", "        \"total return amount\": \"Total Return Amount\",\n", "        \"date\": \"Date\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"Date\",\n", "        \"Type of outlet\",\n", "        \"Total Gross Bill Amount\",\n", "        \"Total Return Amount\",\n", "        \"Total Quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"Total Gross Bill Amount\"] = final[\"Total Gross Bill Amount\"].astype(\"int64\")\n", "final[\"Total Return Amount\"] = final[\"Total Return Amount\"].astype(\"int64\")\n", "final[\"Total Quantity\"] = final[\"Total Quantity\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=4.0,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#de5426\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"w\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(final, header_columns=0)\n", "fig.savefig(\"B2B_Report_Summary.png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "\n", "# filepath = './mytable.png'\n", "token = \"******************************************************\"\n", "client = WebClient(token=token)\n", "file_check = \"./B2B_Report_Summary.png\"  # test#\"./image.png\"\n", "try:\n", "    filepath = file_check\n", "    response = client.files_upload(channels=\"#b2b-sales-tracker\", file=filepath)\n", "    assert response[\"file\"]  # the uploaded file\n", "except SlackApiError as e:\n", "    # You will get a SlackApiError if \"ok\" is False\n", "    assert e.response[\"ok\"] is False\n", "    assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "    print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# b2b-sales-tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}