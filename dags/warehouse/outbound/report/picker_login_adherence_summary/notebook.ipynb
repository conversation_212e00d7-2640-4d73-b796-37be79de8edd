{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import time\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import datetime\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1EKkASbWKX2jHd9fyGkwGaY-grvN5T2rsEYmtsvDsPEw\"\n", "# picker_base = pb.from_sheets(sheet_id, \"Sheet6\",clear_cache=True)\n", "# picker_base.head(2)\n", "\n", "# picker_base[\"null_check\"]= (picker_base[\"total_pickers\"].isna()) | (picker_base[\"total_pickers\"]=='')\n", "# picker_base[\"null_check\"]=picker_base[\"null_check\"].astype(int)\n", "\n", "# picker_base=picker_base.loc[picker_base[\"null_check\"]==0]\n", "\n", "# picker_base[\"operation_date\"]=pd.to_datetime(picker_base[\"operation_date\"]).dt.date\n", "# picker_base[\"facility_id\"]=picker_base[\"facility_id\"].astype(int)\n", "# picker_base[\"total_pickers\"]=picker_base[\"total_pickers\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select a.*,pc.unique_pickers_for_last_15_days\n", "from\n", "(SELECT operation_date::date,\n", "          facility_id,\n", "          facility_name,\n", "          count(distinct picker_id) as logged_in_pickers,\n", "          round((count(distinct case when total_time_hours <=8.0 then picker_id end)*100.0)/logged_in_pickers,1) as below_8_hrs,\n", "          round((count(distinct case when total_time_hours >8.0 and total_time_hours <=10.0 then picker_id end)*100.0)/logged_in_pickers,1) as btw_8_to_10_hrs,\n", "          round((count(distinct case when total_time_hours >10.0 and total_time_hours <=12.0 then picker_id end)*100.0)/logged_in_pickers,1) as btw_10_to_12_hrs,\n", "          round((count(distinct case when total_time_hours >12.0 and total_time_hours <=14.0 then picker_id end)*100.0)/logged_in_pickers,1) as btw_12_to_14_hrs,\n", "          round((count(distinct case when total_time_hours >14.0 and total_time_hours <=16.0 then picker_id end)*100.0)/logged_in_pickers,1) as btw_14_to_16_hrs,\n", "          round((count(distinct case when total_time_hours >16.0 then picker_id end)*100.0)/logged_in_pickers,1) as greater_than_16_hrs\n", "from (\n", "SELECT facility_id,\n", "          f.name AS facility_name,\n", "          operation_date,\n", "          picker_id,\n", "          count(distinct datepart('hour', p.picking_time))as total_time_hours\n", "   FROM metrics.picker_item_shift_mapping p\n", "   INNER JOIN lake_crates.facility f ON f.id=p.facility_id\n", "   WHERE operation_date::date = current_date-1\n", "     AND outlet_id IN (100,116,512,287,369,292,337,714,1112,314,520,475,343,481,334,1159)\n", "   GROUP BY 1,2,3,4)\n", "group by 1,2,3)as a \n", "      left join (SELECT facility_id,\n", "          count(distinct picker_id) as unique_pickers_for_last_15_days\n", "   FROM metrics.picker_item_shift_mapping \n", "   WHERE operation_date::date BETWEEN current_date-16 AND current_date-1\n", "   group by 1)as pc on a.facility_id=pc.facility_id\n", "   order by 1,2;\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base = base.rename(columns={\"operation_date\": \"oprational_date\"})\n", "# \"below_8_hrs\":\"%below_8_hrs\",\"btw_8_to_10_hrs\":\"%btw_8_to_10_hrs\",\n", "#                          \"btw_10_to_12_hrs\":\"%btw_10_to_12_hrs\",\"btw_12_to_14_hrs\":\"%btw_12_to_14_hrs\",\n", "#                          \"btw_14_to_16_hrs\":\"%btw_14_to_16_hrs\",\"greater_than_16_hrs\":\"%greater_than_16_hrs\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_date = (datetime.datetime.today() - datetime.timedelta(days=1)).date()\n", "# filtered_picker_base=picker_base.loc[picker_base[\"operation_date\"]==run_date]\n", "# filtered_picker_base.head()\n", "\n", "# merged=pd.merge(base,filtered_picker_base[[\"facility_id\",\"total_pickers\"]],on='facility_id',how='left')\n", "\n", "# merged[\"login_adherence\"]=round((merged[\"logged_in_pickers\"]*100)/merged[\"total_pickers\"],0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged = base.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged[\n", "    [\n", "        \"below_8_hrs\",\n", "        \"btw_8_to_10_hrs\",\n", "        \"btw_10_to_12_hrs\",\n", "        \"btw_12_to_14_hrs\",\n", "        \"btw_14_to_16_hrs\",\n", "        \"greater_than_16_hrs\",\n", "    ]\n", "] = (\n", "    merged[\n", "        [\n", "            \"below_8_hrs\",\n", "            \"btw_8_to_10_hrs\",\n", "            \"btw_10_to_12_hrs\",\n", "            \"btw_12_to_14_hrs\",\n", "            \"btw_14_to_16_hrs\",\n", "            \"greater_than_16_hrs\",\n", "        ]\n", "    ].astype(str)\n", "    + \"%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged = merged[\n", "    [\n", "        \"oprational_date\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"logged_in_pickers\",\n", "        \"below_8_hrs\",\n", "        \"btw_8_to_10_hrs\",\n", "        \"btw_10_to_12_hrs\",\n", "        \"btw_12_to_14_hrs\",\n", "        \"btw_14_to_16_hrs\",\n", "        \"greater_than_16_hrs\",\n", "        \"unique_pickers_for_last_15_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailing_list = pb.from_sheets(sheet_id, \"email_list\", clear_cache=True)[\n", "    \"email_id\"\n", "].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailing_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "\n", "from_email = \"<EMAIL>\"  # \"<EMAIL>\"\n", "to_email = mailing_list\n", "\n", "subject = \"Picker Login Adherence Summary for \" + str(run_date)\n", "\n", "loader = jinja2.FileSystemLoader(\n", "    \"/usr/local/airflow/dags/repo/dags/warehouse/outbound/report/picker_login_adherence_summary/\"\n", "    # \"/home/<USER>/Development/grofers/Saurav_dev/picker_productivity/Login_adherence\"\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(\"email_template.html\")\n", "\n", "df = merged\n", "message_text = template.render(merged_details=df.to_dict(orient=\"records\"), percent=\"%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=message_text,\n", "    # files=files,\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}