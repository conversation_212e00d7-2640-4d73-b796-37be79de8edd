{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hLlptuQRbzD5T5DrgXl8oTmorHPVxbp9Np7uC2epu_A\"\n", "\n", "Facility = pb.from_sheets(sheet_id, \"Facility_id\")\n", "for (columnName, columnData) in Facility.iteritems():\n", "    facility = tuple(columnData.values)\n", "facility[:8]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out = pd.DataFrame()\n", "for f_id in facility:\n", "\n", "    query = \"\"\"WITH orders AS\n", "      (SELECT co.id AS outlet_id,\n", "              co.name AS outlet_name,\n", "              omsso.id AS suborder_id,\n", "              omsso.order_id,\n", "              current_status,\n", "              (TIMESTAMP 'epoch' + slot_end * interval '1 second' + interval '5.5 hours') AS delivery_date_slot\n", "       FROM lake_oms_bifrost.oms_suborder omsso\n", "       INNER JOIN lake_oms_bifrost.oms_merchant m ON omsso.backend_merchant_id = m.id\n", "       INNER JOIN lake_retail.console_outlet_cms_store cms ON m.external_id = cms.cms_store\n", "       INNER JOIN lake_retail.console_outlet co ON cms.outlet_id = co.id\n", "       WHERE date((TIMESTAMP 'epoch' + slot_end * interval '1 second' + interval '5.5 hours')) BETWEEN CURRENT_DATE-7 AND CURRENT_DATE\n", "         AND cms.active = 1\n", "         AND cms.cms_update_active = 1\n", "         AND co.facility_id =  %(facility_id)s\n", "         AND current_status NOT IN ('CANCELLED',\n", "                                    'REPROCURED')\n", "         AND omsso.type = 'RetailSuborder'),\n", "         SOE AS\n", "      ( SELECT O.outlet_id,\n", "               O.outlet_name,\n", "               O.suborder_id,\n", "               O.order_id,\n", "               O.current_status,\n", "               O.delivery_date_slot,\n", "               latest_batch_created_at,\n", "               latest_batch_name,\n", "               latest_sort_id\n", "       FROM ORDERS O\n", "       LEFT JOIN\n", "         (SELECT aa.suborder_id,\n", "                 (aa.install_ts + interval '5.5 hours') AS latest_batch_created_at,\n", "                 json_extract_path_text(json_extract_path_text(aa.extra, 'to_details'),'name') AS latest_batch_name,\n", "                 json_extract_path_text(json_extract_path_text(aa.extra, 'to_details'),'sort_id') AS latest_sort_id\n", "          FROM\n", "            (SELECT suborder_id,\n", "                    max(id) AS id\n", "             FROM lake_oms_bifrost.oms_suborder_event\n", "             WHERE date(convert_timezone('Asia/Kolkata',install_ts)) BETWEEN CURRENT_DATE-40 AND CURRENT_DATE+1\n", "               AND event_type_key = 'sort_id_update'\n", "             GROUP BY 1) a\n", "          INNER JOIN lake_oms_bifrost.oms_suborder_event aa ON a.id = aa.id) aaa ON O.suborder_id = aaa.suborder_id),\n", "         omsso1 AS\n", "      (SELECT omsso.outlet_id,\n", "              omsso.outlet_name,\n", "              omsso.suborder_id,\n", "              omsso.order_id,\n", "              omsso.current_status,\n", "              omsso.delivery_date_slot,\n", "              omsso.latest_batch_created_at,\n", "              omsso.latest_batch_name,\n", "              omsso.latest_sort_id,\n", "              max(soe.install_ts + interval '5.5 hours') AS billing_time\n", "       FROM SOE omsso\n", "       LEFT JOIN lake_oms_bifrost.oms_suborder_event soe ON omsso.suborder_id = soe.suborder_id\n", "       AND event_type_key = 'retail_invoice_creation'\n", "       AND date(convert_timezone('Asia/Kolkata',install_ts)) BETWEEN CURRENT_DATE - interval '40 day' AND CURRENT_DATE + interval '1 day'\n", "       GROUP BY 1,\n", "                2,\n", "                3,\n", "                4,\n", "                5,\n", "                6,\n", "                7,\n", "                8,\n", "                9),\n", "         lma1 AS\n", "      (SELECT outlet_id,\n", "              outlet_name,\n", "              omsso1.order_id,\n", "              suborder_id,\n", "              current_status,\n", "              delivery_date_slot,\n", "              billing_time,\n", "              latest_batch_created_at,\n", "              latest_batch_name,\n", "              latest_sort_id,\n", "              json_extract_path_text(lma.meta, 'route_id') AS route,\n", "              (lma.actual_event_time + interval '5.5 hours') AS route_created_at\n", "       FROM omsso1\n", "       LEFT JOIN lake_last_mile.order lmo ON omsso1.order_id::text = lmo.external_order_id::text\n", "       LEFT JOIN lake_last_mile.order_action lma ON lmo.id = lma.order_id::integer\n", "       WHERE date(convert_timezone('Asia/Kolkata',delivery_slot_end_time)) BETWEEN CURRENT_DATE - interval '7 day' AND CURRENT_DATE + interval '1 DAY'\n", "      AND action_type IN ('ADDED_TO_ROUTE')\n", "      AND date(convert_timezone('Asia/Kolkata',lma.created_at)) BETWEEN CURRENT_DATE - interval '40 DAY' AND CURRENT_DATE + interval '1 DAY'),\n", "\n", "      TT as (\n", "\n", "      select  lma1.outlet_id,\n", "                lma1.outlet_name,\n", "                lma1.order_id, \n", "                suborder_id,\n", "                current_status,\n", "                delivery_date_slot,\n", "                latest_batch_created_at,\n", "                billing_time,\n", "                lma1.route_created_at, \n", "                LAG(lma1.route_created_at,1) OVER (PARTITION BY suborder_id ORDER BY lma1.route_created_at) previous_route_created_at,\n", "                batch.name as old_batch,\n", "                latest_batch_name as new_batch,\n", "                dz.name as old_drop_zone,\n", "                latest_sort_id,\n", "                LAG(lmp.name,1) OVER (PARTITION BY suborder_id ORDER BY lma1.route_created_at) old_palette,\n", "                lmp.name as new_palette,\n", "\n", "                RANK () OVER ( PARTITION BY suborder_id ORDER BY route_created_at DESC) route_rank\n", "\n", "        from lma1 \n", "        left join lake_warehouse_location.batch_order_mapping batch_map ON lma1.suborder_id::text = batch_map.order_id \n", "        left join lake_warehouse_location.forward_handover_batch batch ON batch_map.batch_id = batch.id \n", "        left join lake_warehouse_location.warehouse_drop_zone dz on batch.drop_zone_id = dz.id\n", "        left join lake_last_mile.route lmr on lma1.route::integer = lmr.id\n", "        left join lake_last_mile.palette_utilization lmpu on lmr.palette_utilization_id = lmpu.id \n", "        left join lake_last_mile.palette lmp on lmpu.palette_id = lmp.id \n", "        order by order_id , route_created_at\n", "       ),\n", "\n", "       Raw_data AS (\n", "    SELECT outlet_id,\n", "           outlet_name,\n", "           order_id,\n", "           suborder_id,\n", "           current_status,\n", "           to_char(delivery_date_slot, 'YYYY-MM-DD HH24:MI:SS ') AS delivery_date_slot,\n", "           to_char(latest_batch_created_at, 'YYYY-MM-DD HH24:MI:SS ') AS latest_batch_created_at,\n", "           to_char(billing_time, 'YYYY-MM-DD HH24:MI:SS ') AS billing_time,\n", "           to_char(previous_route_created_at, 'YYYY-MM-DD HH24:MI:SS ') AS previous_route_created_at,\n", "           to_char(route_created_at, 'YYYY-MM-DD HH24:MI:SS ') AS route_created_at,\n", "           old_batch,\n", "           new_batch,\n", "           old_drop_zone,\n", "           latest_sort_id,\n", "           old_palette,\n", "           new_palette,\n", "           CASE\n", "               WHEN latest_batch_created_at > billing_time\n", "                    AND old_batch != new_batch THEN 'yes'\n", "               ELSE 'no'\n", "           END AS check_batch_change_after_billing,\n", "           CASE\n", "               WHEN route_created_at > billing_time\n", "                    AND old_palette != new_palette THEN 'yes'\n", "               ELSE 'no'\n", "           END AS check_route_change_after_billing\n", "    FROM TT\n", "    WHERE route_rank IN (1) ),\n", "\n", "    Raw_data1 AS\n", "      (SELECT date(Raw_data.delivery_date_slot) as delivery_date,\n", "              Raw_data.outlet_id,\n", "              Raw_data.outlet_name,\n", "              Raw_data.order_id,\n", "              Raw_data.suborder_id,\n", "              Raw_data.current_status,\n", "              Raw_data.delivery_date_slot,\n", "              Raw_data.latest_batch_created_at,\n", "              Raw_data.billing_time,\n", "              Raw_data.previous_route_created_at,\n", "              Raw_data.route_created_at,\n", "              old_batch,\n", "              new_batch,\n", "              old_drop_zone,\n", "              dz.name AS new_drop_zone,\n", "              old_palette,\n", "              new_palette,\n", "              Raw_data.check_batch_change_after_billing,\n", "              Raw_data.check_route_change_after_billing\n", "              \n", "       FROM Raw_data\n", "       LEFT JOIN lake_warehouse_location.forward_handover_batch batch ON latest_sort_id = batch.id\n", "       LEFT JOIN lake_warehouse_location.warehouse_drop_zone dz ON batch.drop_zone_id = dz.id)\n", "         \n", "       select * from raw_data1  \n", "\n", "\n", "      \"\"\"\n", "    temp = pd.read_sql_query(sql=query, con=redshift, params={\"facility_id\": f_id})\n", "    out = out.append(temp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "out[\"updated_at\"] = run_id\n", "out.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out1 = out[\n", "    (out[\"check_batch_change_after_billing\"] == \"yes\")\n", "    | (out[\"check_route_change_after_billing\"] == \"yes\")\n", "]\n", "out1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    out1,\n", "    \"1dHjruBOsYHu9hUiX1DeAWs3JiPAH368Y_dV2eJWm7IY\",\n", "    \"(T-7) to (T)\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders = (\n", "    out.groupby([\"delivery_date\", \"outlet_name\"])[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Total Orders\"})\n", ")\n", "batch_change = (\n", "    out[out[\"check_batch_change_after_billing\"] == \"yes\"]\n", "    .groupby([\"delivery_date\", \"outlet_name\"])[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Batch Change\"})\n", ")\n", "route_change = (\n", "    out[out[\"check_route_change_after_billing\"] == \"yes\"]\n", "    .groupby([\"delivery_date\", \"outlet_name\"])[\"order_id\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"Route Change\"})\n", ")\n", "x = total_orders.merge(batch_change, how=\"left\", on=[\"delivery_date\", \"outlet_name\"])\n", "out_summary = x.merge(route_change, how=\"left\", on=[\"delivery_date\", \"outlet_name\"])\n", "out_summary[\"Batch Change %\"] = (\n", "    out_summary[\"Batch Change\"] / out_summary[\"Total Orders\"]\n", ") * 100\n", "out_summary[\"Route Change %\"] = (\n", "    out_summary[\"Route Change\"] / out_summary[\"Total Orders\"]\n", ") * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    out_summary,\n", "    \"1dHjruBOsYHu9hUiX1DeAWs3JiPAH368Y_dV2eJWm7IY\",\n", "    \"Summary <PERSON><PERSON> and Route Change After Billing\",\n", "    clear_cache=True,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}