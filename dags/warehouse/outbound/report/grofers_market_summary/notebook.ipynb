{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "Redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "POS = pb.get_connection(\"[Replica] POS\")\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select \n", "p.outlet_id,\n", "r.name as <PERSON><PERSON>,\n", " -- p.id, picklist_state\n", "        p.id as pick_list,\n", "        i.order_id as suborder,\n", "        i.item_id,\n", "        i.item_name,\n", "        i.item_type as item_type,\n", "        ii.upper_limit,\n", "        date(convert_tz(p.min_scheduled_time,'+00:00','+05:30')) as Sch_date,\n", "        p.assigned_to_name,\n", "        i.required_quantity as required_quantity, \n", "        i.picked_quantity as picked_quantity,\n", "        (i.required_quantity) - (i.picked_quantity)  as Differance,\n", "        date(convert_tz(pi.pos_timestamp,'+00:00','+05:30')) as Billed_at_Po<PERSON>,\n", "        DATE_FORMAT(convert_tz(current_timestamp,'+00:00','+05:30'), \"%%H:%%i:%%s\") as Last_Updated\n", " from warehouse_location.warehouse_pick_list p\n", " inner join warehouse_location.pick_list_log l on p.id = l.pick_list_id\n", " inner join warehouse_location.warehouse_pick_list_item i on p.id = i.pick_list_id\n", " inner join ims.ims_item_inventory ii on ii.outlet_id = i.outlet_id and ii.item_id = i.item_id \n", " inner join retail.console_outlet r on p.outlet_id = r.id\n", " inner join pos_invoice pi on pi.grofers_order_id = i.order_id\n", " where date(convert_tz(p.min_scheduled_time,'+00:00','+05:30')) between current_date -1 and current_date\n", " and p.type = 1 and l.picklist_state = 10 -- and i.state = 2\n", " and r.business_type_id = 8\n", " and (i.required_quantity) - (i.picked_quantity) >0\n", " order by 1\n", " \"\"\"\n", "Data1 = pd.read_sql_query(sql=query, con=POS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = pb.from_sheets(\n", "    sheetid=\"1VMg1oDla7GfdfacgZMb5h4RdMH90TfXg_zGSO8_R7y8\", sheetname=\"Summary\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.reset_index(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.columns = Data.iloc[\n", "    0,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data = Data.iloc[\n", "    1:,\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Emails = pb.from_sheets(\n", "    sheetid=\"1u_FrS1lWrXdS8Gsl9HwLt8OFig68lhkqzneWki6nrfE\", sheetname=\"Sheet1\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1u_FrS1lWrXdS8Gsl9HwLt8OFig68lhkqzneWki6nrfE\"\n", "outlets = pb.from_sheets(sheet_id, \"Sheet1\")\n", "outlets = outlets[[\"Outlet ID\"]].drop_duplicates()\n", "outlets.dropna(subset=[\"Outlet ID\"], inplace=True)\n", "\n", "# outlets[\"outlet_id\"] = outlets[\"outlet_id\"].astype(int)\n", "email_list = pb.from_sheets(sheet_id, \"Sheet1\")\n", "email_list_to = email_list[[\"Store Email ID\", \"Outlet ID\"]]\n", "email_list_cc = email_list[[\"ABM Mail ID\", \"Outlet ID\"]]\n", "email_list_to[\"Store Email ID\"] = email_list_to[\"Store Email ID\"].astype(str)\n", "email_list_cc[\"ABM Mail ID\"] = email_list_cc[\"ABM Mail ID\"].astype(str)\n", "# email_list[\"outlet_id\"]=email_list[\"outlet_id\"].astype(int)\n", "outlets = list(outlets[\"Outlet ID\"].unique())\n", "# outlets\n", "# email_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sending Emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "for outlet in outlets:\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    subject = \"Grofers Market Outbound Metrics Summary for \" + end\n", "\n", "    Data_New = Data1.copy()\n", "\n", "    Data2 = Data_New[Data_New[\"outlet_id\"] == int(outlet)]\n", "\n", "    Data2.to_csv(\"/tmp/Unfulfilled_Items.csv\")\n", "\n", "    to_email = email_list_to[email_list_to[\"Outlet ID\"] == str(outlet)][\n", "        \"Store Email ID\"\n", "    ].tolist()\n", "\n", "    cc_email = email_list_cc[email_list_cc[\"Outlet ID\"] == str(outlet)][\n", "        \"ABM Mail ID\"\n", "    ].tolist()\n", "\n", "    #     to_email = [\"<EMAIL>\"]\n", "    #     cc_email = [\"<EMAIL>\"]\n", "\n", "    Data3 = Data[Data[\"Outlet_id\"] == str(outlet)]\n", "\n", "    items1 = Data3.to_dict(orient=\"rows\")\n", "\n", "    email_template = \"email_template.html\"\n", "    loader = jinja2.FileSystemLoader(\n", "        searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/outbound/report/grofers_market_summary/\"\n", "        #         searchpath=\"/home/<USER>/airflow-dags/daggers/warehouse/outbound/report/grofers_market_summary/\"\n", "    )\n", "    tmpl_environ = jinja2.Environment(loader=loader)\n", "    template = tmpl_environ.get_template(email_template)\n", "\n", "    rendered = template.render(products1=items1)\n", "\n", "    pb.send_email(\n", "        from_email=from_email,\n", "        to_email=to_email,\n", "        cc=cc_email,\n", "        subject=subject,\n", "        html_content=rendered,\n", "        files=[\"/tmp/Unfulfilled_Items.csv\"],\n", "    )\n", "\n", "    print(\"Mail Sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}