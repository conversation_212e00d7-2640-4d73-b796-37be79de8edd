{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import time\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logex = pb.get_connection(\"logex\")\n", "retail = pb.get_connection(\"retail\")\n", "oms_bifrost = pb.get_connection(\"oms_bifrost\")\n", "aur = pb.get_connection(\"aurora\")\n", "shipment = pb.get_connection(\"oms_bifrost\")\n", "last_mile = pb.get_connection(\"last_mile\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hLlptuQRbzD5T5DrgXl8oTmorHPVxbp9Np7uC2epu_A\"\n", "\n", "try:\n", "    Facility = pb.from_sheets(sheet_id, \"Facility_id\")\n", "except APIError as err:\n", "    error_code = err.response.json()[\"error\"][\"code\"]\n", "    if error_code == 503:\n", "        time.sleep(10)\n", "        Facility = pb.from_sheets(sheet_id, \"Facility_id\")\n", "    else:\n", "        raise err\n", "\n", "for (columnName, columnData) in Facility.iteritems():\n", "    facility = tuple(columnData.values)\n", "facility[:8]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT m.external_id,\n", "       omsso.id AS suborder_id,\n", "       omsso.order_id,\n", "       current_status,\n", "       (TIMESTAMP 'epoch' + slot_end * interval '1 second' + interval '5.5 hours') AS delivery_date_slot\n", "FROM oms_suborder omsso\n", "INNER JOIN oms_merchant m ON omsso.backend_merchant_id = m.id\n", "WHERE date((TIMESTAMP 'epoch' + slot_end * interval '1 second' + interval '5.5 hours')) = date(now() + interval '5.5hours' + interval '1 day')\n", "  AND current_status NOT IN ('CANCELLED',\n", "                             'REPROCURED')\n", "  AND omsso.type = 'RetailSuborder'\"\"\"\n", "\n", "oms = pd.read_sql(query, shipment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Outlet-merchant mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for facility_id in facility:\n", "\n", "query = \"\"\"SELECT co.id as outlet_id,\n", "       co.name as outlet_name,\n", "       cms_store,\n", "       cms.active,\n", "       cms.cms_update_active,\n", "       facility_id\n", "FROM retail.console_outlet_cms_store cms\n", "INNER JOIN retail.console_outlet co ON cms.outlet_id = co.id\n", "and cms.active = 1\n", "AND cms.cms_update_active = 1\n", "\"\"\"\n", "merchant = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant = merchant[merchant[\"facility_id\"].isin(facility)]\n", "merchant.drop([\"facility_id\"], axis=1, inplace=True)\n", "merchant.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders = oms.merge(\n", "    merchant, left_on=[\"external_id\"], right_on=[\"cms_store\"], how=\"inner\"\n", ")\n", "orders = orders[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"suborder_id\",\n", "        \"order_id\",\n", "        \"current_status\",\n", "        \"delivery_date_slot\",\n", "    ]\n", "].drop_duplicates()\n", "orders.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Sub order event"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select suborder_id,latest_batch_name,latest_sort_id,\n", "to_char( latest_batch_created_at, 'YYYY-MM-DD HH24:MI:SS ') as latest_batch_created_at\n", "from\n", "(Select aa.suborder_id, \n", "                         (aa.install_ts + interval '5.5 hours') as latest_batch_created_at,\n", "                         aa.extra::json->'to_details'->>'name' as latest_batch_name,\n", "                         aa.extra::json->'to_details'->>'sort_id' as latest_sort_id\n", "\n", "\n", "                        FROM\n", "\n", "                    (Select suborder_id , max(id) as id  \n", "                         from bifrost.oms_suborder_event \n", "                         where date_ist(install_ts) \n", "                         between date(now() + interval '5.5hours' - interval '5 day') \n", "                                                                              and date(now() + interval '5.5hours' + interval '1 day')\n", "                                             and event_type_key = 'sort_id_update' \n", "                                             group by 1\n", "                    ) a \n", "\n", "                    INNER JOIN bifrost.oms_suborder_event aa on a.id = aa.id)x1\n", "                     \"\"\"\n", "\n", "SOE = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SOE.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["soe1 = orders.merge(SOE, on=[\"suborder_id\"], how=\"left\")\n", "soe1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select suborder_id,\n", "to_char( bill, 'YYYY-MM-DD HH24:MI:SS ') as billing_time\n", "from\n", "(select suborder_id,\n", "(install_ts + interval '5.5 hours') as bill\n", "    from bifrost.oms_suborder_event\n", "    where event_type_key = 'retail_invoice_creation' \n", "    and date_ist(install_ts) between date(now() + interval '5.5hours' - interval '5 day') and date(now() + interval '5.5hours' + interval '1 day'))a \"\"\"\n", "\n", "omsso2 = pd.read_sql_query(sql=query, con=aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["omsso2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["omsso12 = soe1.merge(omsso2, on=[\"suborder_id\"], how=\"left\")\n", "omsso12.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["omsso1 = (\n", "    omsso12.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"suborder_id\",\n", "            \"order_id\",\n", "            \"current_status\",\n", "            \"delivery_date_slot\",\n", "            \"latest_batch_created_at\",\n", "            \"latest_batch_name\",\n", "            \"latest_sort_id\",\n", "        ]\n", "    )\n", "    .agg({\"billing_time\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "omsso1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "SELECT external_order_id as order_id,\n", "lma.meta,\n", "lma.actual_event_time route_created_at\n", "FROM last_mile.order lmo\n", "left join last_mile.order_action lma on lmo.id = lma.order_id  \n", "where date(delivery_slot_end_time)>=current_date \n", "and action_type in ('ADDED_TO_ROUTE')\n", "and date(lma.created_at) between current_date-20 and current_date+1\n", "\"\"\"\n", "lma2 = pd.read_sql_query(query, last_mile)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lma2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lma2[\"order_id\"] = lma2[\"order_id\"].astype(\"str\")\n", "omsso1[\"order_id\"] = omsso1[\"order_id\"].astype(\"str\")\n", "\n", "lma1 = omsso1.merge(lma2, on=[\"order_id\"], how=\"left\")\n", "lma1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "lma1[\"route_created_at\"] = pd.DatetimeIndex(lma1[\"route_created_at\"]) + timedelta(\n", "    hours=5, minutes=30\n", ")\n", "lma1[\"route\"] = lma1[\"meta\"].str.replace(r\"\\D+\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT order_id as suborder_id,\n", "       batch_id,\n", "       batch.name AS old_batch,\n", "       dz.name AS old_drop_zone,\n", "       outlet_id\n", "FROM warehouse_location.batch_order_mapping batch_map\n", "LEFT JOIN warehouse_location.forward_handover_batch batch ON batch_map.batch_id = batch.id\n", "LEFT JOIN warehouse_location.warehouse_drop_zone dz ON batch.drop_zone_id = dz.id\n", "WHERE date(batch_map.pos_created_at)>=CURRENT_DATE-1\"\"\"\n", "\n", "drop_zone = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["drop_zone[\"suborder_id\"] = drop_zone[\"suborder_id\"].astype(\"int\")\n", "\n", "tt12 = lma1.merge(drop_zone, on=[\"suborder_id\", \"outlet_id\"], how=\"left\")\n", "tt12.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select lmr.id as route,\n", "lmp.name as new_palette\n", "from\n", "route lmr \n", "left join last_mile.palette_utilization lmpu on lmr.palette_utilization_id = lmpu.id \n", "left join last_mile.palette lmp on lmpu.palette_id = lmp.id \"\"\"\n", "\n", "tt2 = pd.read_sql_query(query, last_mile)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tt2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tt12[\"route\"] = tt12[\"route\"].fillna(0)\n", "tt2[\"route\"] = tt2[\"route\"].astype(\"int\")\n", "tt12[\"route\"] = tt12[\"route\"].astype(\"int\")\n", "\n", "tt = tt12.merge(tt2, on=[\"route\"], how=\"left\")\n", "tt.sort_values(by=[\"order_id\", \"route_created_at\"], inplace=True)\n", "tt.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tt[\"previous_route_created_at\"] = (\n", "    tt.sort_values(by=[\"route_created_at\"], ascending=True)\n", "    .groupby([\"suborder_id\"])[\"route_created_at\"]\n", "    .shift(1)\n", ")\n", "tt[\"old_palette\"] = (\n", "    tt.sort_values(by=[\"route_created_at\"], ascending=True)\n", "    .groupby([\"suborder_id\"])[\"new_palette\"]\n", "    .shift(1)\n", ")\n", "tt.head(2)\n", "tt[\"constant\"] = 1\n", "tt.sort_values(by=[\"suborder_id\", \"route_created_at\"], inplace=True, ascending=False)\n", "\n", "tt[\"route_rank\"] = tt.groupby([\"suborder_id\"])[\"constant\"].apply(lambda x: x.cumsum())\n", "tt.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Raw_data = tt[tt[\"route_rank\"] == 1]\n", "Raw_data[\"route\"] = Raw_data[\"route\"].replace(0, np.nan)\n", "Raw_data[\"route_created_at\"] = Raw_data[\"route_created_at\"].fillna(\"\")\n", "Raw_data[\"latest_batch_created_at\"] = Raw_data[\"latest_batch_created_at\"].fillna(\"\")\n", "Raw_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def remarks(x):\n", "    if (\n", "        x[\"latest_batch_created_at\"] > x[\"billing_time\"]\n", "        and x[\"old_batch\"] != x[\"latest_batch_name\"]\n", "    ):\n", "        return \"yes\"\n", "    else:\n", "        return \"no\"\n", "\n", "\n", "def remarks2(x):\n", "    if (\n", "        x[\"route_created_at\"] > x[\"billing_time\"]\n", "        and x[\"old_palette\"] != x[\"new_palette\"]\n", "    ):\n", "        return \"yes\"\n", "    else:\n", "        return \"no\"\n", "\n", "\n", "Raw_data[\"latest_batch_created_at\"] = Raw_data[\"latest_batch_created_at\"].astype(\"str\")\n", "Raw_data[\"billing_time\"] = Raw_data[\"billing_time\"].astype(\"str\")\n", "Raw_data[\"route_created_at\"] = Raw_data[\"route_created_at\"].astype(\"str\")\n", "Raw_data[\"latest_batch_name\"] = Raw_data[\"latest_batch_name\"].astype(\"str\")\n", "Raw_data[\"old_batch\"] = Raw_data[\"old_batch\"].astype(\"str\")\n", "\n", "Raw_data[\"check_batch_change_after_billing\"] = Raw_data.apply(remarks, axis=1)\n", "Raw_data[\"check_route_change_after_billing\"] = Raw_data.apply(remarks2, axis=1)\n", "Raw_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" \n", "select batch.id as latest_sort_id,\n", "dz.name as new_drop_zone from\n", " warehouse_location.forward_handover_batch batch \n", "left join warehouse_location.warehouse_drop_zone dz on batch.drop_zone_id = dz.id\"\"\"\n", "\n", "raw12 = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw12.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw1 = Raw_data.merge(raw12, on=[\"latest_sort_id\"], how=\"left\")\n", "raw1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "raw1[\"updated_at\"] = run_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = raw1[\n", "    [\n", "        \"updated_at\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"suborder_id\",\n", "        \"order_id\",\n", "        \"current_status\",\n", "        \"delivery_date_slot\",\n", "        \"latest_batch_created_at\",\n", "        \"billing_time\",\n", "        \"previous_route_created_at\",\n", "        \"route_created_at\",\n", "        \"old_batch\",\n", "        \"latest_batch_name\",\n", "        \"old_drop_zone\",\n", "        \"new_drop_zone\",\n", "        \"old_palette\",\n", "        \"new_palette\",\n", "        \"check_batch_change_after_billing\",\n", "        \"check_route_change_after_billing\",\n", "    ]\n", "].drop_duplicates()\n", "final[\"order_id\"] = final[\"order_id\"].astype(\"int\")\n", "final.head()\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out = final[\n", "    (final[\"check_batch_change_after_billing\"] == \"yes\")\n", "    | (final[\"check_route_change_after_billing\"] == \"yes\")\n", "]\n", "out.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    out,\n", "    \"1dHjruBOsYHu9hUiX1DeAWs3JiPAH368Y_dV2eJWm7IY\",\n", "    \"Raw Batch and Route Change After Billing(T+1)\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# total_orders = (\n", "#     final.groupby([\"outlet_name\"])[\"order_id\"]\n", "#     .nunique()\n", "#     .reset_index()\n", "#     .rename(columns={\"order_id\": \"Total Orders\"})\n", "# )\n", "# batch_change = (\n", "#     final[final[\"check_batch_change_after_billing\"] == \"yes\"]\n", "#     .groupby([\"outlet_name\"])[\"order_id\"]\n", "#     .nunique()\n", "#     .reset_index()\n", "#     .rename(columns={\"order_id\": \"Batch Change\"})\n", "# )\n", "# route_change = (\n", "#     final[final[\"check_route_change_after_billing\"] == \"yes\"]\n", "#     .groupby([\"outlet_name\"])[\"order_id\"]\n", "#     .nunique()\n", "#     .reset_index()\n", "#     .rename(columns={\"order_id\": \"Route Change\"})\n", "# )\n", "# x = total_orders.merge(batch_change, how=\"left\", on=\"outlet_name\")\n", "# out_summary = x.merge(route_change, how=\"left\", on=\"outlet_name\")\n", "# out_summary[\"Batch Change %\"] = (\n", "#     out_summary[\"Batch Change\"] / out_summary[\"Total Orders\"]\n", "# ) * 100\n", "# out_summary[\"Route Change %\"] = (\n", "#     out_summary[\"Route Change\"] / out_summary[\"Total Orders\"]\n", "# ) * 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     out_summary,\n", "#     \"1dHjruBOsYHu9hUiX1DeAWs3JiPAH368Y_dV2eJWm7IY\",\n", "#     \"Summary <PERSON><PERSON> and <PERSON> Change After Billing\",\n", "# )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}