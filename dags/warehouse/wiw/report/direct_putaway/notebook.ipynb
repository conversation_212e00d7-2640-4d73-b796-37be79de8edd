{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["external_sheet = \"1NFQ6hAbvzFadvWCy1wJ72JQSLIWgBeMaERGlzeZa_G4\"\n", "sheet_id = \"1UwI0wIo96VF8KWnqhmlc6nowzSkOjKPp4B6r6qNJV0E\"\n", "skus_count = pb.from_sheets(external_sheet, \"WinW-Outlet list\")\n", "skus_count = skus_count[[\"outlet_id\", \"skus_count_direct_putaway\"]].drop_duplicates()\n", "skus_count[\"skus_count_direct_putaway\"] = skus_count[\n", "    \"skus_count_direct_putaway\"\n", "].replace(\"\", np.nan)\n", "skus_count[\"skus_count_direct_putaway\"] = skus_count[\n", "    \"skus_count_direct_putaway\"\n", "].<PERSON><PERSON>(400)\n", "skus_count[\"outlet_id\"] = skus_count[\"outlet_id\"].astype(\"int\")\n", "skus_count.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in skus_count[\"outlet_id\"]:\n", "    if outlet not in x:\n", "        x.append(outlet)\n", "print(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Top skus"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output = pd.DataFrame()\n", "\n", "for outlet in x:\n", "    skus = skus_count[skus_count[\"outlet_id\"] == outlet]\n", "    sku_count = skus.iloc[0, 1]\n", "    sku_count = int(sku_count)\n", "\n", "    query = \"\"\"select * from \n", "\n", "    (SELECT outlet_id,\n", "           item_id,\n", "           cpd,\n", "           RANK() OVER (PARTITION BY outlet_id\n", "                        ORDER BY cpd desc)\n", "    FROM\n", "      (SELECT wom.cloud_store_id AS outlet_id,\n", "              dwc.item_id,\n", "              sum(dwc.consumption) AS cpd\n", "       FROM\n", "         (SELECT outlet_id,\n", "                 item_id,date,consumption\n", "          FROM consumer.snorlax_date_wise_consumption\n", "          WHERE date BETWEEN CURRENT_DATE + 1 AND CURRENT_DATE +7) dwc\n", "       JOIN consumer.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "       WHERE wom.cloud_store_id = %(outlet_id)s\n", "       GROUP BY 1,\n", "                2\n", "       ORDER BY wom.cloud_store_id,\n", "                cpd DESC)a)b\n", "                where rank<= %(rank)s\n", "                \"\"\"\n", "\n", "    top_skus = pd.read_sql(\n", "        query, redshift, params={\"outlet_id\": outlet, \"rank\": sku_count}\n", "    )\n", "    output = output.append(top_skus)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"Top skus\"\n", "\n", "pb.to_sheets(output, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Adrep generated"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT indent_date,\n", "       x1.outlet_id,\n", "       x1.item_id,\n", "       x1.adrep_qty\n", "FROM\n", "  (SELECT date(convert_tz(w.created_at,\"+00:00\",\"+05:30\")) AS indent_date,\n", "          w.outlet_id,\n", "          w.id AS indent_id,\n", "          i.item_id,\n", "          i.forecasted_quantity,\n", "          i.quantity AS indent_quantity,\n", "          i.forecasted_quantity-i.quantity AS adrep_qty\n", "   FROM warehouse_location.warehouse_wave_indent w\n", "   INNER JOIN\n", "     (SELECT date(convert_tz(created_at,\"+00:00\",\"+05:30\")),\n", "             outlet_id,\n", "             max(convert_tz(created_at,\"+00:00\",\"+05:30\")) AS max_date\n", "      FROM warehouse_location.warehouse_wave_indent\n", "      WHERE date(convert_tz(created_at,\"+00:00\",\"+05:30\")) BETWEEN date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval -20 DAY) AND date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 0 DAY)\n", "      GROUP BY 1,\n", "               2)a ON w.outlet_id=a.outlet_id\n", "   INNER JOIN warehouse_location.warehouse_wave_indent_item i ON w.id=i.indent_id\n", "   AND (convert_tz(w.created_at,\"+00:00\",\"+05:30\"))=a.max_date\n", "   AND i.forecasted_quantity>i.quantity\n", "   ORDER BY 1,\n", "            2)x1\"\"\"\n", "\n", "adrep_generated = pd.read_sql_query(query, retail)\n", "adrep_generated.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_skus_adrep = output.merge(adrep_generated, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "top_skus_adrep.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"adrep_raw\"\n", "\n", "pb.to_sheets(top_skus_adrep, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Indent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT indent_date,\n", "       x1.outlet_id,\n", "       x1.item_id,\n", "       x1.forecasted_quantity,\n", "       x1.indent_quantity\n", "FROM\n", "  (SELECT date(convert_tz(w.created_at,\"+00:00\",\"+05:30\")) AS indent_date,\n", "          w.outlet_id,\n", "          w.id AS indent_id,\n", "          i.item_id,\n", "          i.forecasted_quantity,\n", "          i.quantity AS indent_quantity\n", "   FROM warehouse_location.warehouse_wave_indent w\n", "   INNER JOIN\n", "     (SELECT date(convert_tz(created_at,\"+00:00\",\"+05:30\")),\n", "             outlet_id,\n", "             max(convert_tz(created_at,\"+00:00\",\"+05:30\")) AS max_date\n", "      FROM warehouse_location.warehouse_wave_indent\n", "      WHERE date(convert_tz(created_at,\"+00:00\",\"+05:30\")) BETWEEN date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval -20 DAY) AND date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 0 DAY)\n", "      GROUP BY 1,\n", "               2)a ON w.outlet_id=a.outlet_id\n", "   INNER JOIN warehouse_location.warehouse_wave_indent_item i ON w.id=i.indent_id\n", "   AND (convert_tz(w.created_at,\"+00:00\",\"+05:30\"))=a.max_date\n", "   AND i.quantity>0\n", "   ORDER BY 1,\n", "            2)x1\"\"\"\n", "\n", "indent = pd.read_sql_query(query, retail)\n", "indent.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_top = indent.merge(output, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "indent_top.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"indent_raw\"\n", "\n", "pb.to_sheets(indent_top, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### putlist generated"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT date(dated) AS putlist_created_at,\n", "       outlet_id,\n", "       item_id,\n", "       sum(quantity) as quantity\n", "FROM\n", "  (SELECT DISTINCT dated,\n", "                   EXTRACT(HOUR\n", "                           FROM dated) AS hours,\n", "                   outlet_id,\n", "                   item_id,\n", "                   sum(quantity) AS quantity\n", "   FROM\n", "     (SELECT convert_timezone('Asia/Kolkata',put_list.created_at) AS dated,\n", "             put_list.id,\n", "             put_list.putlist_id,\n", "             outlet.id AS outlet_id,\n", "             put_list_item.item_id,\n", "             put_list_item.quantity\n", "      FROM consumer.warehouse_location_warehouse_put_list put_list\n", "      INNER JOIN consumer.retail_console_outlet outlet ON outlet.id=put_list.outlet_id\n", "      INNER JOIN consumer.warehouse_location_warehouse_put_list_item put_list_item ON put_list_item.putlist_id=put_list.id\n", "        AND put_list.creation_type IN (1,\n", "                                       6)\n", "        AND date(convert_timezone('Asia/Kolkata',put_list.created_at)) between current_date-20 and CURRENT_DATE-1 )x1\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)x1\n", "WHERE hours<20\n", "group by 1,2,3\"\"\"\n", "\n", "putlist = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["putlist.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["putlist_top_skus_adrep = putlist.merge(output, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "putlist_top_skus_adrep.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"putlist_raw\"\n", "\n", "pb.to_sheets(\n", "    putlist_top_skus_adrep, sheet_id, sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT x1.dated,\n", "       x1.outlet_id,\n", "       x1.adrep_item_id AS item_id,\n", "       delta AS dp_quantity\n", "FROM\n", "  (SELECT date(convert_tz(al.pos_timestamp,\"+00:00\",\"+05:30\")) AS dated,\n", "          al.outlet_id,\n", "          al.item_id AS adrep_item_id,\n", "          sum(al.delta) AS delta\n", "   FROM warehouse_location.adrep_log al\n", "   WHERE event_type IN (\"2\")\n", "     AND date(convert_tz(al.pos_timestamp,\"+00:00\",\"+05:30\")) BETWEEN date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval -20 DAY) AND date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval -1 DAY)\n", "     AND hour(convert_tz(al.pos_timestamp,\"+00:00\",\"+05:30\"))<20\n", "   GROUP BY 1,\n", "            2,\n", "            3)x1\n", "         \"\"\"\n", "\n", "direct_putaway = pd.read_sql_query(query, retail)\n", "direct_putaway.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["direct_putaway_top_skus = direct_putaway.merge(output, on=[\"outlet_id\", \"item_id\"])\n", "direct_putaway_top_skus.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"directputaway_raw\"\n", "\n", "pb.to_sheets(\n", "    direct_putaway_top_skus, sheet_id, sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Supermarket available"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT a.outlet_id,\n", "          a.outlet_name,\n", "          a.item_id,\n", "          total_qty_supermarket,\n", "          to_be_picked_qty_from_supermarket,\n", "          available_supermarket_inventory\n", "   FROM\n", "     (SELECT DISTINCT outlet_id,\n", "                      r.name AS outlet_name,\n", "                      supermarket_id,\n", "                      item_id,\n", "                      sum(Total_Qty_in_supermarket) AS total_qty_supermarket,\n", "                      sum(to_be_picked_qty_from_supermarket) AS to_be_picked_qty_from_supermarket,\n", "                      sum(available_supermarket_inventory) AS available_supermarket_inventory\n", "      FROM\n", "        (SELECT T1.outlet_id,\n", "                T1.supermarket_id,\n", "                T1.supermarket_name,\n", "                T1.item_id,\n", "                T2.delivery_date,\n", "                coalesce(T1.Total_Qty_in_ministore,0) AS Total_Qty_in_supermarket,\n", "                coalesce(T2.to_be_picked_qty,0) AS to_be_picked_qty_from_supermarket,\n", "                coalesce(T1.Total_Qty_in_ministore,0) - coalesce(T2.to_be_picked_qty,0) AS available_supermarket_inventory\n", "         FROM\n", "           (SELECT MAP.outlet_id,\n", "                   sm.id AS supermarket_id,\n", "                   sm.name AS supermarket_name,\n", "                   sm.capacity AS supermarket_capacity,\n", "                   wil.item_id,\n", "                   sum(wil.quantity) AS Total_Qty_in_ministore\n", "            FROM\n", "              (SELECT item_id,\n", "                      location_id,\n", "                      quantity,\n", "                      warehouse_id,\n", "                      active\n", "               FROM warehouse_location.warehouse_item_location)wil\n", "            INNER JOIN warehouse_location.warehouse_storage_location sl ON sl.id = wil.location_id\n", "            INNER JOIN warehouse_location.warehouse_outlet_mapping MAP ON wil.warehouse_id = MAP.warehouse_id\n", "            INNER JOIN warehouse_location.warehouse_supermarket sm ON sl.supermarket_id = sm.id\n", "            WHERE sl.location_type = 2\n", "              AND wil.active = 1\n", "              AND sm.enabled = 1\n", "              and wil.quantity>0\n", "            GROUP BY 1,\n", "                     2,\n", "                     3,\n", "                     4,\n", "                     5)T1\n", "         LEFT JOIN\n", "           (SELECT p.outlet_id,\n", "                   p.supermarket_id,\n", "                   date(convert_tz(p.min_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "                   i.item_id,\n", "                   sum(i.required_quantity) AS to_be_picked_qty\n", "            FROM\n", "              (SELECT outlet_id,\n", "                      supermarket_id,\n", "                      id,\n", "                      min_scheduled_time,\n", "                      TYPE,\n", "                      state\n", "               FROM warehouse_location.warehouse_pick_list\n", "               WHERE id>44404456)p\n", "            INNER JOIN\n", "              (SELECT item_id,\n", "                      required_quantity,\n", "                      pick_list_id\n", "               FROM warehouse_location.warehouse_pick_list_item\n", "               WHERE pick_list_id>44404456 )i ON p.id = i.pick_list_id\n", "            WHERE date(convert_tz(p.min_scheduled_time,\"+00:00\",\"+05:30\")) = adddate(curdate(), interval 2 DAY)\n", "              AND p.state IN ('1',\n", "                              '2',\n", "                              '3',\n", "                              '12',\n", "                              '9',\n", "                              '14',\n", "                              '13')\n", "              AND p.type = 1\n", "            GROUP BY 1,\n", "                     2,\n", "                     3,\n", "                     4)T2 ON T1.outlet_id = T2.outlet_id\n", "         AND T1.supermarket_id = T2.supermarket_id\n", "         AND T1.item_id = T2.item_id) ss\n", "      INNER JOIN retail.console_outlet r ON ss.outlet_id=r.id\n", "      WHERE outlet_id IN (100, 116, 340, 363, 367, 369, 451, 503, 287, 334, 520, 481, 714, 314)\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4)a\n", "   \n", "\n", "  \"\"\"\n", "supermarket_qty = pd.read_sql_query(query, retail)\n", "supermarket_qty.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"supermarket_available\"\n", "pb.to_sheets(supermarket_qty, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inbound"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT x1.*\n", "FROM\n", "  (SELECT stock_in_date,\n", "          facility_id,\n", "          facility_name,\n", "          item_id,\n", "          sum(grn_quantity) AS grn_quantity\n", "   FROM\n", "     (SELECT stock_in_date,\n", "             facility_id,\n", "             facility_name,\n", "             outlet_id,\n", "             outlet_name,\n", "             city_id,\n", "             city_name,\n", "             merchant,\n", "             po_received,\n", "             invoice_type_id,\n", "             stock_in_type,\n", "             CASE\n", "                 WHEN po_received IS NULL\n", "                      AND invoice_type_id=5 THEN FALSE\n", "                 WHEN po_received IS NOT NULL THEN FALSE\n", "                 ELSE TRUE\n", "             END AS is_dummy_merchant,\n", "             item_id,\n", "             sum(grn_quantity) AS grn_quantity\n", "      FROM\n", "        (SELECT i.\"delta\"-i3.return_quantity AS Grn_quantity,\n", "                r.item_id AS item_id,\n", "                ro.id AS outlet_id,\n", "                ro.name AS outlet_name,\n", "                f.id AS facility_id,\n", "                f.name AS facility_name,\n", "                lo.id AS city_id,\n", "                lo.name AS city_name,\n", "                i3.purchase_order_id AS po_received,\n", "                rcm.name AS merchant,\n", "                pi.invoice_type_id,\n", "                date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) AS stock_in_date,\n", "                (CASE\n", "                     WHEN pi.invoice_type_id = 5 THEN 'ESTO Stock-In'\n", "                     ELSE 'Normal Stock-In'\n", "                 END) AS stock_in_type\n", "         FROM consumer.ims_ims_inventory_log i\n", "         INNER JOIN consumer.rpc_product_product r ON r.variant_id= i.variant_id\n", "         LEFT JOIN consumer.rpc_product_brand br ON br.id=r.brand_id\n", "         LEFT JOIN consumer.rpc_product_manufacturer man ON man.id=br.manufacturer_id\n", "         INNER JOIN consumer.pos_console_outlet ro ON ro.id = i.outlet_id\n", "         INNER JOIN consumer.retail_console_location lo ON ro.tax_location_id=lo.id\n", "         LEFT JOIN consumer.crates_facility f ON ro.facility_id = f.id\n", "         INNER JOIN consumer.ims_ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         INNER JOIN consumer.pos_ims_inventory_stock_details i3 ON i3.inventory_update_id::varchar = i.inventory_update_id::varchar\n", "         INNER JOIN consumer.retail_console_merchant rcm ON rcm.id = i.merchant_id\n", "         LEFT JOIN consumer.po_purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "         LEFT JOIN consumer.view_pos_invoice pi ON pi.invoice_id=i.merchant_invoice_id\n", "         WHERE i.inventory_update_type_id IN (1,\n", "                                              28,\n", "                                              76)\n", "           AND date(convert_timezone('Asia/Kolkata',i.created_at)) BETWEEN CURRENT_DATE-interval '20 day' AND CURRENT_DATE- interval '1 day'\n", "           AND i.\"delta\" > 0\n", "       \n", "           AND rcm.name NOT LIKE '%%HOT%%'\n", "           AND rcm.name NOT IN ('FNV Dummy GRN Pan India',\n", "                                'Customer Returns Without Invoice',\n", "                                'Store Infra Merchant',\n", "                                'HOT Warehouse Bulk FNV',\n", "                                'F&V Dump stock - Jaipur',\n", "                                'Customer Return without Invoice',\n", "                                'SSC Warehouse Bulk FNV')) x1\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9,\n", "               10,\n", "               11,\n", "               12,\n", "               13)x2\n", "   WHERE is_dummy_merchant IS FALSE\n", "     AND outlet_name NOT LIKE '%%Infra Outlet%%'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)x1\n", "\"\"\"\n", "\n", "inbound = pd.read_sql(query, redshift)\n", "inbound.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,\n", "facility_id\n", "from retail.console_outlet\"\"\"\n", "outlet = pd.read_sql_query(query, retail)\n", "outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_outlet = inbound.merge(outlet, on=[\"facility_id\"], how=\"left\")\n", "inbound_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_outlet = inbound_outlet[inbound_outlet[\"outlet_id\"].isin(x)]\n", "inbound_outlet = inbound_outlet.drop_duplicates()\n", "inbound_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_outlet_top_skus = inbound_outlet.merge(\n", "    output, on=[\"outlet_id\", \"item_id\"], how=\"inner\"\n", ")\n", "inbound_outlet_top_skus.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"inbound raw\"\n", "pb.to_sheets(\n", "    inbound_outlet_top_skus, sheet_id, sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,\n", "name as outlet_name\n", "from retail.console_outlet\"\"\"\n", "outlet_name = pd.read_sql_query(query, retail)\n", "outlet_name = outlet_name[outlet_name[\"outlet_id\"].isin(x)]\n", "outlet_name.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"outlets\"\n", "pb.to_sheets(outlet_name, sheet_id, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Bad stock"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH expiry_damage_raw AS\n", "  (SELECT co.id AS outlet_id,\n", "          co.name AS outlet_name,\n", "          prod.item_id AS item_id,\n", "          prod.name AS item_name,\n", "          iil.\"delta\" lost_qty,\n", "          date(convert_timezone('Asia/Kolkata', pos_timestamp)) inventory_update_date,\n", "          iut.name AS update_name\n", "   FROM consumer.pos_ims_inventory_log iil\n", "   LEFT JOIN consumer.pos_ims_inventory_stock_details inv_stock ON iil.inventory_update_id::varchar=inv_stock.inventory_update_id::varchar\n", "   INNER JOIN consumer.pos_console_outlet co ON co.id = iil.outlet_id\n", "   INNER JOIN consumer.pos_product_product prod ON prod.variant_id = iil.variant_id\n", "   INNER JOIN consumer.pos_ims_inventory_update_type iut ON iut.id = iil.inventory_update_type_id\n", "   WHERE date(convert_timezone('Asia/Kolkata', pos_timestamp)) BETWEEN CURRENT_DATE-20 AND CURRENT_DATE\n", "     AND iut.id IN (12,\n", "                    64)\n", "     AND prod.outlet_type IN (2)\n", "  )\n", "SELECT inventory_update_date,\n", "       e.outlet_id,\n", "       outlet_name,\n", "       e.item_id,\n", "       update_name AS reason,\n", "       sum(lost_qty) AS total_quantity\n", "FROM expiry_damage_raw e\n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "\n", "bad_stock = pd.read_sql(query, redshift)\n", "bad_stock.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_stock = bad_stock[bad_stock[\"outlet_id\"].isin(x)]\n", "bad_stock.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_stock_top_skus = bad_stock.merge(output, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "# bad_stock_top_skus.groupby([\"inventory_update_date\", \"outlet_id\", \"reason\"]).agg(\n", "#     {\"item_id\": \"nunique\"}\n", "# ).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"Detailed sheet: Item level\"\n", "\n", "pb.to_sheets(\n", "    bad_stock_top_skus, sheet_id, sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directputaway tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_dp = \"1S5T2uv9LSwnoV0JDO230mg3mMrpZU-POPnVI9DStKQc\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SF Buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT outlet_id,\n", "       date_ AS delivery_date,\n", "       CASE\n", "           WHEN Final_bucket IS NULL THEN \"D\"\n", "           ELSE Final_bucket\n", "       END AS Final_bucket,\n", "       count(DISTINCT item_id) AS skus,\n", "       sum(required_quantity) AS qty\n", "FROM\n", "  (SELECT shortfall.*,\n", "          bucket.Final_bucket\n", "   FROM\n", "     (SELECT dated AS date_,\n", "             outlet_id,\n", "             item_id,\n", "             sum(required_quantity) AS required_quantity\n", "      FROM\n", "        (SELECT w.dated,\n", "                w.outlet_id,\n", "                w.id,\n", "                convert_tz(w.created_at,\"+00:00\",\"+05:30\") AS wave_created_at,\n", "                p.id AS bulk_pick_list_id,\n", "                p.`type`,\n", "                pi.item_id,\n", "                CASE\n", "                    WHEN pi.state IN (3,\n", "                                      8) THEN 'Not Found'\n", "                    ELSE 'Found'\n", "                END AS item_state,\n", "                pi.required_quantity\n", "         FROM\n", "           (SELECT DISTINCT date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS dated,\n", "                            w.outlet_id,\n", "                            w.id,\n", "                            w.created_at\n", "            FROM warehouse_location.warehouse_wave w\n", "            INNER JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON w.id = m.wave_id\n", "            INNER JOIN warehouse_location.warehouse_pick_list p ON p.id = m.pick_list_id\n", "            WHERE date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) BETWEEN date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval -15 DAY) AND date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 0 DAY)\n", "              AND w.wave_type IN (2,\n", "                                  6)) w\n", "         INNER JOIN warehouse_location.warehouse_pick_list p ON w.id = p.wave_id\n", "         INNER JOIN warehouse_location.warehouse_pick_list_item pi ON p.id = pi.pick_list_id\n", "         WHERE p.type IN (3,\n", "                          7))t\n", "      GROUP BY 1,\n", "               2,\n", "               3) shortfall\n", "   LEFT JOIN\n", "     (SELECT a.item_id,\n", "             b.cloud_store_id AS outlet_id, /* a.tag_value AS BucketA_or_B,\n", "                a1.tag_value AS Bucket_X,*/ (CASE\n", "                                                 WHEN a.tag_value='A'\n", "                                                      AND a1.tag_value ='Y' THEN 'X'\n", "                                                 WHEN a.tag_value='A'\n", "                                                      AND a1.tag_value IS NULL THEN 'A'\n", "                                                 ELSE 'B'\n", "                                             END) AS Final_bucket\n", "      FROM rpc.item_outlet_tag_mapping a\n", "      INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "      LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "      AND a1.outlet_id=a.outlet_id\n", "      AND a1.tag_type_id=6\n", "      AND a1.tag_value='Y'\n", "      WHERE b.cloud_store_id IN ( 100,\n", "                                  116,\n", "                                  287,\n", "                                  314,\n", "                                  340,\n", "                                  346,\n", "                                  363,\n", "                                  369,\n", "                                  451,\n", "                                  459,\n", "                                  503,\n", "                                  334,\n", "                                  520,\n", "                                  481,\n", "                                  714,\n", "                                  512)\n", "        AND a.tag_type_id IN (1)\n", "        AND a.active=1)bucket ON shortfall.outlet_id=bucket.outlet_id\n", "   AND shortfall.item_id=bucket.item_id)t\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\"\n", "\n", "sf = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"sf_raw\"\n", "\n", "pb.to_sheets(sf, sheet_dp, sheet_name, service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inbound"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT a.item_id,\n", "                b.cloud_store_id AS outlet_id,\n", "                (CASE\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value ='Y' THEN 'X'\n", "                     WHEN a.tag_value='A'\n", "                          AND a1.tag_value IS NULL THEN 'A'\n", "                     ELSE 'B'\n", "                 END) AS Final_bucket\n", "FROM rpc.item_outlet_tag_mapping a\n", "INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "AND a1.outlet_id=a.outlet_id\n", "AND a1.tag_type_id=6\n", "AND a1.tag_value='Y'\n", "WHERE b.cloud_store_id IN (99,\n", "                         100,\n", "                         116,\n", "                         287,\n", "                         314,\n", "                         340,\n", "                         346,\n", "                         363,\n", "                         367,\n", "                         369,\n", "                         451,\n", "                         459,\n", "                         503,\n", "                         334,\n", "                         520,714,481,512)\n", "  AND a.tag_type_id IN (1)\n", "  AND a.active=1\"\"\"\n", "buckets = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_outlet_bucket = inbound_outlet.merge(\n", "    buckets, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "inbound_outlet_bucket[\"Final_bucket\"] = inbound_outlet_bucket[\"Final_bucket\"].fillna(\n", "    \"D\"\n", ")\n", "inbound_outlet_bucket.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inbound_final = (\n", "    inbound_outlet_bucket.groupby(\n", "        [\"stock_in_date\", \"facility_id\", \"facility_name\", \"Final_bucket\"]\n", "    )\n", "    .agg({\"item_id\": \"nunique\", \"grn_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "inbound_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"inbound_raw\"\n", "\n", "pb.to_sheets(inbound_final, sheet_dp, sheet_name, service_account=\"service_account\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}