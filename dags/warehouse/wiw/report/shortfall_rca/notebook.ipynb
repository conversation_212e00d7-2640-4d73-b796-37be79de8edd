{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import pandas_redshift as pr\n", "import datetime as dt\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import pymysql\n", "import matplotlib.pyplot as plt\n", "import mysql.connector as sql\n", "import sqlalchemy as sqla\n", "import yagmail\n", "import keyring\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "from sklearn.ensemble import RandomForestRegressor\n", "\n", "db_connection = sql.connect(\n", "    host=\"reportsmysql.retail.prod.grofer.io\", user=\"reports_ro\", passwd=\"epai0OoK\"\n", ")\n", "connection_string = (\n", "    \"mysql+pymysql://reports_ro:<EMAIL>\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select  distinct dated,outlet_id , \n", "           item_id,item_state,\n", "          sum(required_quantity) as item_quantity\n", "       \n", "       from \n", "(select w.dated,\n", "w.outlet_id , \n", "\t   w.id,\n", "       convert_tz(w.created_at,\"+00:00\",\"+05:30\") as wave_created_at,\n", "       p.id as bulk_pick_list_id,\n", "       p.`type`,\n", "       pi.item_id,\n", "       case when pi.state in (3,8) then 'Not Found' else 'Found' end as item_state,\n", "       pi.required_quantity\n", "       \n", "            \n", "from \n", "(select distinct date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) as dated,w.outlet_id,\n", "\t    w.id,\n", "\t    w.created_at\n", " \n", "from warehouse_location.warehouse_wave w \n", "inner join warehouse_location.warehouse_wave_pick_list_mapping m \n", "on \t\t\tw.id = m.wave_id\n", "inner join  warehouse_location.warehouse_pick_list p\n", "on          p.id = m.pick_list_id \n", "where date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) = current_date - interval 1 day\n", "\n", "and w.wave_type = 2) w\n", "inner join\n", "warehouse_location.warehouse_pick_list p  \n", "on w.id = p.wave_id \n", "inner join warehouse_location.warehouse_pick_list_item pi on p.id = pi.pick_list_id \n", "where p.type = 3)t\n", "group by 1,2,3,4;\"\"\"\n", "\n", "sf = pd.read_sql_query(query, db_connection)\n", "sf.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(dt.datetime.now())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf[\"outlet_item\"] = sf.apply(lambda x: \"%s_%s\" % (x[\"outlet_id\"], x[\"item_id\"]), axis=1)\n", "indents = pd.read_csv(\"indents_2019-06-16.csv\")\n", "indents[\"item_id\"] = indents[\"item_id\"].astype(int)\n", "indents[\"outlet_item\"] = indents.apply(\n", "    lambda x: \"%s_%s\" % (x[\"outlet\"], x[\"item_id\"]), axis=1\n", ")\n", "sf_indent = sf.merge(\n", "    indents,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"outlet_item\"],\n", "    right_on=[\"outlet\", \"outlet_item\"],\n", ")\n", "sf_indent.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent.item_state.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sf_indent_chk = sf_indent.groupby(['dated','outlet_id']).aggregate({'item_id_x':'nunique','item_state': lambda x: 'count' if  == 'Not Found','item_id_y':'nunique'}).reset_index()\n", "# sf_indent_chk.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct dated,outlet,item_id,sum(required_quantity) as reqd_quantity\n", "from\n", "(select date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) as dated,o.outlet,\n", "      o.id,\n", "      i.item_id,\n", "     sum(i.quantity) as required_quantity\n", "from ims.ims_order_details o\n", "inner join ims.ims_order_items i on o.id = i.order_details_id\n", "where date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) = current_date - interval 1 day\n", "AND status_id NOT IN (3, 5)\n", "group by 1,2,3,4) a group by 1,2,3;\"\"\"\n", "\n", "demand = pd.read_sql_query(query, db_connection)\n", "demand.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["demand.outlet.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select date(convert_tz(wv.created_at,\"+00:00\",\"+05:30\")) as dated,p.outlet_id,\n", "pi.item_id,max(pi.updated_at + interval 330 minute) as put_completion_time\n", "from\n", "warehouse_location.warehouse_pick_list p\n", "join warehouse_location.warehouse_put_list pl on pl.pick_list_id = p.id\n", "join warehouse_location.warehouse_wave wv on pl.wave_id = wv.id\n", "join warehouse_location.warehouse_put_list_item pi on pi.putlist_id = pl.id\n", "where \n", "wv.wave_type = 3 and pi.item_state_id = 4\n", "and date(convert_tz(wv.created_at,\"+00:00\",\"+05:30\")) = current_date - interval 3 day\n", "group by 1,2,3;\"\"\"\n", "\n", "replenish = pd.read_sql_query(query, db_connection)\n", "replenish.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from os import listdir\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main_dir = \"/home/<USER>/Ajay/Shortfall_RCA/indentations/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_formatted_df(main_dir, file_name):\n", "\n", "    path = os.path.join(main_dir, file_name)\n", "    if os.path.isfile(path):\n", "        df = pd.read_csv(path, engine=\"python\")\n", "        outlet = file_name.split(\"_\")[-1].replace(\".csv\", \"\")\n", "\n", "        df[\"outlet\"] = outlet\n", "        # df['date'] = dt.date.today\n", "        df[\"date\"] = (dt.datetime.now() + timed<PERSON>ta(days=-1)).strftime(\"%Y-%m-%d\")\n", "        # print(dt.datetime.now().strftime(\"%Y-%m-%d\"))\n", "        # print(path)\n", "\n", "        df.to_csv(path, index=None)\n", "        # print(df.head())\n", "    else:\n", "        pass\n", "\n", "\n", "#     return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file in os.listdir(main_dir):\n", "    create_formatted_df(main_dir, file)\n", "    # print(file_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path1 = \"/home/<USER>/Ajay/Shortfall_RCA/indentations/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "path1 = glob.glob(\n", "    \"/home/<USER>/Ajay/Shortfall_RCA/indentations/output_data_indent\" + \"*.csv\"\n", ")\n", "path1[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.path.getmtime(path1[0])\n", "last_modified_at = dt.datetime.fromtimestamp(1558683178.237626).strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "indents = pd.concat([pd.read_csv(f) for f in path1])\n", "indents.to_csv(\n", "    \"indents_\" + (dt.datetime.now() + <PERSON><PERSON><PERSON>(days=-1)).strftime(\"%Y-%m-%d\") + \".csv\",\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final indent files\n", "file11 = glob.glob(\"indents\" + \"*.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file in file11:\n", "    if (\n", "        dt.datetime.today() - pd.to_datetime(file.split(\"_\")[-1].replace(\".csv\", \"\"))\n", "    ).days > 2:\n", "        os.remove(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "path21 = glob.glob(\"/home/<USER>/Ajay/indents\" + \"*.csv\")\n", "for file in path21:\n", "    if (\n", "        dt.datetime.today() - pd.to_datetime(file.split(\"_\")[-1].replace(\".csv\", \"\"))\n", "    ).days == 1:\n", "        val = path21.index(file)\n", "        indents = pd.read_csv(path21[val])\n", "indents.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# post indent processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["replenish[\"dated\"] = pd.to_datetime(replenish[\"dated\"])\n", "demand[\"dated\"] = pd.to_datetime(demand[\"dated\"])\n", "sf[\"dated\"] = pd.to_datetime(sf[\"dated\"])\n", "indents[\"date\"] = pd.to_datetime(indents[\"date\"])\n", "indents[\"item_id\"] = indents[\"item_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["replenish[\"date1\"] = replenish[\"dated\"] + <PERSON><PERSON><PERSON>(days=2)\n", "indents[\"date1\"] = indents[\"date\"] + <PERSON><PERSON>ta(days=0)\n", "indents.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf[\"outlet_item\"] = sf.apply(lambda x: \"%s_%s\" % (x[\"outlet_id\"], x[\"item_id\"]), axis=1)\n", "indents[\"outlet_item\"] = indents.apply(\n", "    lambda x: \"%s_%s\" % (x[\"outlet\"], x[\"item_id\"]), axis=1\n", ")\n", "demand[\"outlet_item\"] = demand.apply(\n", "    lambda x: \"%s_%s\" % (x[\"outlet\"], x[\"item_id\"]), axis=1\n", ")\n", "replenish[\"outlet_item\"] = replenish.apply(\n", "    lambda x: \"%s_%s\" % (x[\"outlet_id\"], x[\"item_id\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent = sf.merge(\n", "    indents,\n", "    how=\"left\",\n", "    left_on=[\"dated\", \"outlet_id\", \"outlet_item\"],\n", "    right_on=[\"date1\", \"outlet\", \"outlet_item\"],\n", ")\n", "sf_indent.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent.rename(\n", "    columns={\n", "        \"dated\": \"delivery_date\",\n", "        \"item_quantity\": \"sf_qty\",\n", "        \"final_quantity_to_be_picked\": \"indent_qty\",\n", "        \"item_id_x\": \"sf_item\",\n", "        \"item_id_y\": \"indent_item\",\n", "        \"outlet_id\": \"sf_outlet\",\n", "        \"date\": \"indent_date\",\n", "    },\n", "    inplace=True,\n", ")\n", "sf_indent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["demand.rename(\n", "    columns={\"reqd_quantity\": \"demand_qty\", \"item_id\": \"demand_item\"}, inplace=True\n", ")\n", "replenish[\"put_completion_time\"] = pd.to_datetime(replenish[\"put_completion_time\"])\n", "replenish.rename(columns={\"item_id\": \"rep_item\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent_demand = sf_indent.merge(\n", "    demand,\n", "    how=\"left\",\n", "    left_on=[\"delivery_date\", \"outlet_item\", \"indent_item\"],\n", "    right_on=[\"dated\", \"outlet_item\", \"demand_item\"],\n", ")\n", "sf_indent_demand.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent_demand[\"demand_indent_comp\"] = sf_indent_demand.apply(\n", "    lambda x: 1\n", "    if ((x[\"indent_qty\"] < x[\"demand_qty\"]) & (~pd.isna(x[\"indent_item\"])))\n", "    else 0,\n", "    axis=1,\n", ")\n", "sf_indent_demand.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent_demand_rep = sf_indent_demand.merge(\n", "    replenish,\n", "    how=\"left\",\n", "    left_on=[\"delivery_date\", \"sf_outlet\", \"outlet_item\", \"demand_item\"],\n", "    right_on=[\"date1\", \"outlet_id\", \"outlet_item\", \"rep_item\"],\n", ")\n", "sf_indent_demand_rep.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent_demand_rep[\"put_max_time\"] = sf_indent_demand_rep.delivery_date + timedelta(\n", "    days=-1, hours=9\n", ")\n", "sf_indent_demand_rep.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_indent_demand_rep[\"rep_comp\"] = sf_indent_demand_rep.apply(\n", "    lambda x: 1\n", "    if (\n", "        (x[\"put_completion_time\"] > x[\"put_max_time\"])\n", "        & (~pd.isna(x[\"indent_item\"]))\n", "        & (x[\"demand_qty\"] > x[\"indent_qty\"])\n", "    )\n", "    else 0,\n", "    axis=1,\n", ")\n", "sf_indent_demand_rep.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca = sf_indent_demand_rep[\n", "    [\n", "        \"delivery_date\",\n", "        \"sf_outlet\",\n", "        \"sf_item\",\n", "        \"item_state\",\n", "        \"outlet_item\",\n", "        \"sf_qty\",\n", "        \"indent_item\",\n", "        \"indent_qty\",\n", "        \"demand_item\",\n", "        \"demand_qty\",\n", "        \"demand_indent_comp\",\n", "        \"rep_item\",\n", "        \"rep_comp\",\n", "    ]\n", "].drop_duplicates()\n", "sf_rca.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca.to_csv(\"shortfall_rca_raw_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca.sf_outlet.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v1 = (\n", "    sf_rca.groupby([\"delivery_date\", \"sf_outlet\"])\n", "    .aggregate(\n", "        {\"sf_item\": \"nunique\", \"indent_item\": lambda x: x[x.notnull()].nunique()}\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v2 = (\n", "    sf_rca.groupby([\"delivery_date\", \"sf_outlet\"])\n", "    .apply(\n", "        lambda x: pd.Series(\n", "            dict(\n", "                bucket2=x[x.demand_indent_comp == 1].demand_item.nunique(),\n", "                bucket3=x[\n", "                    (x.rep_comp == 1)\n", "                    & (\n", "                        ~(\n", "                            x.indent_item.isnull()\n", "                            | (\n", "                                (x.indent_item.notnull())\n", "                                & (x.demand_qty > x.indent_qty)\n", "                            )\n", "                        )\n", "                    )\n", "                ].rep_item.nunique(),\n", "                bucket4=x[\n", "                    (x.item_state == \"Not Found\") & (x.indent_item.notnull())\n", "                ].sf_item.nunique(),\n", "            )\n", "        )\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v12 = sf_rca_v1.merge(sf_rca_v2, how=\"inner\", on=[\"delivery_date\", \"sf_outlet\"])\n", "sf_rca_v12.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v12.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_rca_v12[\"Bucket1\"] = round(\n", "    (1 - (sf_rca_v12[\"indent_item\"] / sf_rca_v12[\"sf_item\"])) * 100, 2\n", ")\n", "sf_rca_v12[\"Bucket2\"] = round((sf_rca_v12[\"bucket2\"] / sf_rca_v12[\"sf_item\"]) * 100, 2)\n", "sf_rca_v12[\"Bucket3\"] = round((sf_rca_v12[\"bucket3\"] / sf_rca_v12[\"sf_item\"]) * 100, 2)\n", "sf_rca_v12[\"Bucket4\"] = round((sf_rca_v12[\"bucket4\"] / sf_rca_v12[\"sf_item\"]) * 100, 2)\n", "sf_rca_v12[\"Bucket5\"] = round(\n", "    (\n", "        100\n", "        - (\n", "            sf_rca_v12[\"Bucket1\"]\n", "            + sf_rca_v12[\"Bucket2\"]\n", "            + sf_rca_v12[\"Bucket3\"]\n", "            + sf_rca_v12[\"Bucket4\"]\n", "        )\n", "    ),\n", "    2,\n", ")\n", "sf_rca_v12.rename(\n", "    columns={\n", "        \"delivery_date\": \"Delivery_date\",\n", "        \"sf_outlet\": \"Outlet_id\",\n", "        \"sf_item\": \"Items_in_shortfall\",\n", "    },\n", "    inplace=True,\n", ")\n", "sf_rca_final = sf_rca_v12[\n", "    [\n", "        \"Delivery_date\",\n", "        \"Outlet_id\",\n", "        \"Items_in_shortfall\",\n", "        \"Bucket1\",\n", "        \"Bucket2\",\n", "        \"Bucket3\",\n", "        \"Bucket4\",\n", "        \"Bucket5\",\n", "    ]\n", "].drop_duplicates()\n", "sf_rca_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sf_rca_final.to_csv('Shortfall_RCA_report_' + dt.datetime.now().strftime(\"%Y-%m-%d\") + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mail send part\"\"\"\n", "Bucket1 = \"%Shortfall items not in indent\"\n", "Bucket2 = \"%Indent items with indent quantity less than demand quantity\"\n", "Bucket3 = \"%Indent items with indent quantity less than demand quantity which where not replenished untill 9 am the next day\"\n", "\n", "\n", "html_body1 = sf_rca_final.to_html(\n", "    index=True,\n", "    header=True,\n", "    classes=\"table table-bordered table-striped table-condensed\",\n", ").replace(\"\\n\", \" \")\n", "html = \"Outlet-wise Shortfall RCA Buckets\" + \"\\n\" + html_body1 + \"\\n\"\n", "body = (\n", "    \"Hi,\"\n", "    + \"\\n\"\n", "    + \"Please find below the shortfall RCA report for \"\n", "    + (dt.datetime.now() + timed<PERSON>ta(days=-1)).strftime(\"%Y-%m-%d\")\n", "    + \".\"\n", "    + \"\\n\"\n", "    + \"\\n\"\n", "    + html\n", "    + \"\\n\"\n", "    + \"Description of the different buckets are as follows:\"\n", "    + \"\\n\"\n", "    + \"Bucket1: \"\n", "    + Bucket1\n", "    + \"\\n\"\n", "    + \"Bucket2: \"\n", "    + Bucket2\n", "    + \"\\n\"\n", "    + \"Bucket3: \"\n", "    + Bucket3\n", "    + \"\\n\"\n", "    + \"Bucket4: \"\n", "    + Bucket4\n", "    + \"\\n\"\n", "    + \"Bucket5: \"\n", "    + Bucket5\n", ")\n", "\n", "\n", "yag = yagmail.SMTP(\"<EMAIL>\", \"ebfzndmythlskuui\")\n", "subject = \"Shortfall RCA Report_\" + (dt.datetime.now() + timed<PERSON>ta(days=-1)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "email_list = [\"ajay<PERSON>.<EMAIL>\", \"<EMAIL>\"]\n", "yag.send(\n", "    email_list, subject=subject, contents=body, attachments=\"shortfall_rca_raw_data.csv\"\n", ")\n", "\n", "print(dt.datetime.now())\n", "print(\"mail_successfully_sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.3"}}, "nbformat": 4, "nbformat_minor": 2}