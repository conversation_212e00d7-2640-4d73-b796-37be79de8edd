{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTLET_ID = 2653\n", "OUTLET_ID_list = [2653]\n", "ENV = \"prod\"\n", "OUTPUT_BASE_DIR = \"indents\"\n", "GLOBAL_BASE_DIR = \"cpd\"\n", "NOW = \"2021-11-01\"\n", "current_time = \"2021-01-20T00:00:00+00:00\"\n", "run_id = \"manual_\" + str(date.today())\n", "dt = datetime.now()\n", "NOW = datetime.strptime(NOW, \"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = run_id.replace(\"scheduled\", \"sch\")\n", "run_id = run_id.replace(\"manual\", \"man\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_query(name, query, con, params=None):\n", "    try:\n", "        df = pd.read_sql_query(query, con=con, params=params)\n", "    except:\n", "        df = pd.read_sql_query(query, con=con, params=params)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT distinct\n", "o.facility_id,\n", "I.outlet_id,\n", "         p.item_id,\n", "         sum(I.QUANTITY) AS IMS_Qty\n", "  FROM ims.ims_inventory I\n", "  INNER JOIN rpc.product_product p ON p.variant_id = I.variant_id\n", "  INNER JOIN retail.console_outlet o ON o.id = I.outlet_id\n", "  WHERE I.outlet_id in ({outlets})\n", "    AND I.ACTIVE = 1\n", "    group by 1,2,3\"\"\".format(\n", "    outlets=\",\".join([str(x) for x in OUTLET_ID_list])\n", ")\n", "\n", "\n", "ims_inventory = fetch_query(\"ims_inventory\", query, pb.get_connection(\"[Replica] IMS\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inventory.IMS_Qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku_buckets_query = \"\"\"SELECT a.item_id,\n", "/* a.tag_value AS BucketA_or_B,\n", "            a1.tag_value AS Bucket_X,*/ (CASE\n", "                                             WHEN a.tag_value='A'\n", "                                                  AND a1.tag_value ='Y' THEN 'X'\n", "                                             WHEN a.tag_value='A'\n", "                                                  AND a1.tag_value IS NULL THEN 'A'\n", "                                             ELSE 'B'\n", "                                         END) AS Final_bucket\n", "FROM rpc.item_outlet_tag_mapping a\n", "INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "AND a1.outlet_id=a.outlet_id\n", "AND a1.tag_type_id=6\n", "AND a1.tag_value='Y'\n", "WHERE  b.cloud_store_id = %(outlet_id)s\n", "AND a.tag_type_id IN (1)\n", " \"\"\"\n", "sku_bucket = fetch_query(\n", "    \"sku_bucket\", sku_buckets_query, retail, params={\"outlet_id\": 714}\n", ")\n", "\n", "sku_bucket.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv = ims_inventory.merge(sku_bucket, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1 = ims_inv[~ims_inv[\"Final_bucket\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supermarket = pd.read_sql_query(\n", "    \"\"\"SELECT \n", "                    32 as supermarket_id,\n", "                    wil.item_id,\n", "                    sum(wil.quantity) as super_market_qty\n", "   FROM warehouse_location.warehouse_storage_location sl \n", "   LEFT JOIN warehouse_location.warehouse_item_location wil ON sl.id = wil.location_id\n", "   LEFT JOIN warehouse_location.warehouse_outlet_mapping map ON wil.warehouse_id = map.warehouse_id\n", "   WHERE map.outlet_id = 2653 AND wil.active = 1 and sl.location_type = 2 group by 1,2\n", "\"\"\",\n", "    con=pb.get_connection(\"[Replica] POS\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supermarket.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1 = ims_inv1.merge(supermarket, how=\"left\", on=[\"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_size_of_item = \"\"\"select item_id,\n", "    max(case when (outer_case_size = 0 or outer_case_size is NULL)  then 1 else outer_case_size end) as outer_case_size \n", "    from rpc.product_product\n", "    where active = 1\n", "    group by 1\n", "    \"\"\"\n", "\n", "case_size = fetch_query(\n", "    \"case_size_of_item\", case_size_of_item, pb.get_connection(\"retail\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1 = ims_inv1.merge(case_size, how=\"left\", on=[\"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1[\"supermarket_id\"] = 32\n", "ims_inv1[\"extra\"] = ims_inv1[\"IMS_Qty\"] - ims_inv1[\"super_market_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1[\"final\"] = np.where(\n", "    ims_inv1.Final_bucket == \"X\",\n", "    ims_inv1.extra * 0.75,\n", "    np.where(ims_inv1.Final_bucket == \"A\", ims_inv1.extra * 0.5, ims_inv1.extra * 0.25),\n", ")\n", "ims_inv1[\"final_indent_qty\"] = (\n", "    (ims_inv1[\"final\"] / ims_inv1[\"outer_case_size\"]).astype(int) + 1\n", ") * ims_inv1[\"outer_case_size\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_cap = 21000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1f = ims_inv1.sample(frac=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_inv1f[\"cumsum\"] = ims_inv1f.final_indent_qty.cumsum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent = ims_inv1f[ims_inv1f[\"cumsum\"] < indent_cap][[\"item_id\", \"final_indent_qty\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent = indent.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item category mapping\n", "item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          C.id as l0_id,\n", "          (CASE\n", "               WHEN C1.id = C.id THEN C2.id\n", "               ELSE C1.id\n", "           END) AS l1_id,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "  SELECT rpc.item_id,\n", "          cat.L0,\n", "          l0_id,\n", "          cat.L1,\n", "          l1_id,\n", "          handling_type,\n", "          storage_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   INNER JOIN lake_rpc.item_details id on id.item_id = rpc.item_id and id.active = 1 \n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(\n", "    item_attributes_query, pb.get_connection(\"[Warehouse] Redshift\")\n", ")\n", "item_product_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_query = \"\"\"select (case when json_extract(pos_config,\"$.sto_split_criteria\") in (4,5) then 'YES' else 'NO' end) as flag\n", "from retail.console_outlet where id = %(outlet_id)s\"\"\"\n", "zone_flag = pd.read_sql_query(\n", "    zone_query, con=pb.get_connection(\"retail\"), params={\"outlet_id\": OUTLET_ID}\n", ")\n", "zone = zone_flag.flag.unique()[0]\n", "print(zone)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if zone == \"YES\":\n", "    query1 = \"\"\"select handling_type,storage_type,zone_type from warehouse_location.item_zone_type\"\"\"\n", "    zone_type = pd.read_sql_query(query1, retail)\n", "    query = \"\"\"select b.zone_id, a.zone_identifier, b.zone_type, l0_id,l1_id\n", "               from warehouse_location.item_category_zone_type_zone_id b\n", "               inner join warehouse_location.warehouse_zone_id_mapping a on a.id = b.zone_id \n", "               inner join warehouse_location.warehouse_outlet_mapping wom on wom.warehouse_id = a.warehouse_id\n", "               where outlet_id = %(outlet_ids)s\"\"\"\n", "    zones = pd.read_sql_query(query, retail, params={\"outlet_ids\": OUTLET_ID})\n", "    zones = zones.fillna(0)\n", "    item_product_mapping.handling_type = item_product_mapping.handling_type.fillna(\n", "        0\n", "    ).astype(int)\n", "    item_product_mapping.storage_type = item_product_mapping.storage_type.fillna(\n", "        0\n", "    ).astype(int)\n", "    xx = item_product_mapping.merge(\n", "        zone_type, on=[\"handling_type\", \"storage_type\"], how=\"left\"\n", "    )\n", "    # matching the predefined zone_ids\n", "    yy = xx.merge(\n", "        zones[zones[\"l0_id\"] == 0][[\"zone_id\", \"zone_type\"]],\n", "        on=[\"zone_type\"],\n", "        how=\"left\",\n", "    )\n", "    yy1 = yy.merge(\n", "        zones[(zones[\"l1_id\"] == 0) & (zones[\"l0_id\"] != 0)][\n", "            [\"zone_id\", \"zone_type\", \"l0_id\"]\n", "        ],\n", "        on=[\"zone_type\", \"l0_id\"],\n", "        how=\"left\",\n", "    )\n", "    yy2 = yy1.merge(\n", "        zones[[\"zone_id\", \"zone_type\", \"l0_id\", \"l1_id\"]],\n", "        on=[\"l0_id\", \"l1_id\", \"zone_type\"],\n", "        how=\"left\",\n", "    )\n", "    yy2[\"final_zone_id\"] = np.where(\n", "        yy2[\"zone_id\"].notnull(),\n", "        yy2[\"zone_id\"],\n", "        np.where(yy2[\"zone_id_y\"].notnull(), yy2[\"zone_id_y\"], yy2[\"zone_id_x\"]),\n", "    )\n", "    yy2 = yy2.drop(columns={\"zone_id_y\", \"zone_id_x\", \"zone_id\"})\n", "    yy2 = yy2.rename(columns={\"final_zone_id\": \"zone_id\"})\n", "    yy2 = yy2.merge(\n", "        zones[[\"zone_id\", \"zone_identifier\"]].drop_duplicates(),\n", "        on=[\"zone_id\"],\n", "        how=\"inner\",\n", "    )\n", "#     dd = pd.read_csv(os.path.join(OUTPUT_BASE_DIR,\"data\",\"output\",\"final_indent_result_%s\" % OUTLET_ID + \"_\" + str(current_time) + \".csv\",))\n", "#     ee = dd.merge(yy2[['item_id','zone_type','zone_id','zone_identifier']], on = ['item_id'], how = 'left')\n", "#     ee.to_csv(os.path.join(OUTPUT_BASE_DIR,\"data\",\"output\",\"final_indent_result_%s\" % OUTLET_ID + \"_\" + str(current_time) + \".csv\",))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent[\"outlet_id\"] = 2653\n", "indent[\"supermarket_id\"] = 32\n", "indent[\"case_qty_to_be_picked\"] = indent[\"final_indent_qty\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_directputaway_new = indent.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_directputaway_new = indent_directputaway_new[\n", "    [\n", "        \"outlet_id\",\n", "        \"supermarket_id\",\n", "        \"item_id\",\n", "        \"case_qty_to_be_picked\",\n", "        \"final_indent_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_directputaway_new.item_id.nunique(), indent_directputaway_new.final_indent_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_disabled = \"YES\"\n", "if zone == \"YES\":\n", "    xy = indent_directputaway_new.merge(\n", "        yy2[[\"item_id\", \"zone_id\", \"zone_identifier\"]].drop_duplicates(),\n", "        on=[\"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    print(\"Total zone_ids: \", xy.zone_id.nunique())\n", "    adrep = pd.DataFrame()\n", "    count = 0\n", "    zones_list = [x for x in list(xy.zone_id.unique()) if str(x) != \"nan\"]\n", "    for zone_i in zones_list:\n", "        count += 1\n", "        zone_identif = xy[xy[\"zone_id\"] == zone_i].zone_identifier.unique()[0]\n", "        print(\"Sending Zone Indent number: \", zone_i, zone_identif)\n", "        WH_GPOS_URL = (\n", "            \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/wave-indent-with-items/\"\n", "        )\n", "        indent_directputaway_zone = xy[xy[\"zone_id\"] == zone_i][\n", "            [\n", "                \"outlet_id\",\n", "                \"supermarket_id\",\n", "                \"item_id\",\n", "                \"case_qty_to_be_picked\",\n", "                \"final_indent_qty\",\n", "            ]\n", "        ]\n", "        if OUTLET_ID in OUTLET_ID_list and is_disabled == \"YES\":\n", "            df_final = indent_directputaway_zone.sort_values([\"outlet_id\", \"item_id\"])\n", "            df_final[\"case_qty_to_be_picked\"] = df_final[\"final_indent_qty\"]\n", "            df_final = df_final[df_final[\"case_qty_to_be_picked\"] > 0]\n", "        else:\n", "            df_final = indent_directputaway_zone.sort_values([\"outlet_id\", \"item_id\"])\n", "            if count != len(zones_list):\n", "                adrep_zone = df_final[\n", "                    df_final[\"case_qty_to_be_picked\"] != df_final[\"final_indent_qty\"]\n", "                ]\n", "                adrep = pd.concat([adrep, adrep_zone], axis=0)\n", "                df_final[\"case_qty_to_be_picked\"] = df_final[\"final_indent_qty\"]\n", "            else:\n", "                df_final[\"case_qty_to_be_picked\"] = df_final[\n", "                    \"case_qty_to_be_picked\"\n", "                ].astype(\"int\")\n", "                adrep[\"case_qty_to_be_picked\"] = (\n", "                    adrep[\"case_qty_to_be_picked\"] - adrep[\"final_indent_qty\"]\n", "                )\n", "                adrep[\"final_indent_qty\"] = 0\n", "                df_final = pd.concat([df_final, adrep], axis=0)\n", "            df_final = df_final[df_final[\"case_qty_to_be_picked\"] > 0]\n", "        df_final[\"type\"] = 1\n", "        df_final[\"run_id\"] = run_id\n", "        print(df_final.shape)\n", "        df_final.rename(\n", "            columns={\n", "                \"case_qty_to_be_picked\": \"forecasted_quantity\",\n", "                \"final_indent_qty\": \"quantity\",\n", "                \"supermarket_id\": \"to_supermarket_id\",\n", "            },\n", "            inplace=True,\n", "        )\n", "        stores = list(df_final.to_supermarket_id.unique())\n", "        for store in stores:\n", "            for (run_id, outlet_id, to_supermarket_id, type), item_details in df_final[\n", "                df_final.to_supermarket_id == store\n", "            ].groupby([\"run_id\", \"outlet_id\", \"to_supermarket_id\", \"type\"]):\n", "                items = item_details[\n", "                    [\"item_id\", \"quantity\", \"forecasted_quantity\"]\n", "                ].to_dict(orient=\"records\")\n", "                payload = {\n", "                    \"run_id\": str(zone_identif) + \"_\" + run_id,\n", "                    \"type\": int(type),\n", "                    \"outlet_id\": int(outlet_id),\n", "                    \"to_supermarket_id\": int(store),\n", "                    \"items\": items,\n", "                }\n", "                print(str(zone_identif) + \"_\" + run_id)\n", "                time.sleep(5)\n", "                response = requests.post(WH_GPOS_URL, json=payload)\n", "                print(response.text)\n", "else:\n", "    WH_GPOS_URL = (\n", "        \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/wave-indent-with-items/\"\n", "    )\n", "    if OUTLET_ID in outlets_all and is_disabled == \"YES\":\n", "        df_final = indent_directputaway_new.sort_values([\"outlet_id\", \"item_id\"])\n", "        df_final[\"case_qty_to_be_picked\"] = df_final[\"final_indent_qty\"]\n", "        df_final = df_final[df_final[\"case_qty_to_be_picked\"] > 0]\n", "    else:\n", "        df_final = indent_directputaway_new.sort_values([\"outlet_id\", \"item_id\"])\n", "        df_final[\"case_qty_to_be_picked\"] = df_final[\"case_qty_to_be_picked\"].astype(\n", "            \"int\"\n", "        )\n", "        df_final = df_final[df_final[\"case_qty_to_be_picked\"] > 0]\n", "    df_final[\"type\"] = 1\n", "    df_final[\"run_id\"] = run_id\n", "    df_final.rename(\n", "        columns={\n", "            \"case_qty_to_be_picked\": \"forecasted_quantity\",\n", "            \"final_indent_qty\": \"quantity\",\n", "            \"supermarket_id\": \"to_supermarket_id\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    stores = list(df_final.to_supermarket_id.unique())\n", "    for store in stores:\n", "        for (run_id, outlet_id, to_supermarket_id, type), item_details in df_final[\n", "            df_final.to_supermarket_id == store\n", "        ].groupby([\"run_id\", \"outlet_id\", \"to_supermarket_id\", \"type\"]):\n", "            items = item_details[\n", "                [\"item_id\", \"quantity\", \"forecasted_quantity\"]\n", "            ].to_dict(orient=\"records\")\n", "            payload = {\n", "                \"run_id\": run_id,\n", "                \"type\": int(type),\n", "                \"outlet_id\": int(outlet_id),\n", "                \"to_supermarket_id\": int(store),\n", "                \"items\": items,\n", "            }\n", "            response = requests.post(WH_GPOS_URL, json=payload)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}