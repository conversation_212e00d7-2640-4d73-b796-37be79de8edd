{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from gspread.exceptions import APIError\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "import numpy as np\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id_input = 264\n", "outlet_id_input = 1104"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_date = (datetime.now() - timedelta(days=0)).date()\n", "curr_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select x.*,co1.business_type_id,co1.facility_id as facility_id_rec\n", " from (SELECT \n", "              from_outlet_id AS hot_outlet,\n", "              map.cloud_store_id AS outlet_id,\n", "              co.name AS outlet_name,\n", "              co.facility_id,\n", "              to_outlet_id as to_outlet_hot,\n", "              map1.cloud_store_id AS to_outlet_id,\n", "              sto_date, \n", "              (case when to_outlet_id is null then to_outlet_hot else to_outlet_id end) as rec_outlet_id,\n", "              ars_forecast.run_id,\n", "              ars_forecast.item_id,\n", "              cpd_sum,\n", "              threshold_doi,\n", "              cpd.consumption as cpd_one_day\n", "       FROM lake_ars.frontend_cycle_sto_quantity ars_forecast\n", "       LEFT JOIN lake_retail.warehouse_outlet_mapping MAP ON map.warehouse_id = ars_forecast.from_outlet_id\n", "       LEFT JOIN lake_retail.warehouse_outlet_mapping MAP1 ON map1.warehouse_id = ars_forecast.to_outlet_id\n", "       LEFT JOIN lake_retail.console_outlet co ON co.id = map.cloud_store_id\n", "       LEFT JOIN (select dwc.outlet_id,dwc.item_id,dwc.consumption\n", "        from lake_snorlax.date_wise_consumption dwc\n", "        inner join lake_retail.console_outlet co on co.id = outlet_id and co.business_type_id = 7\n", "        where \"date\" = current_date + 1) cpd on cpd.outlet_id = ars_forecast.to_outlet_id\n", "        and cpd.item_id = ars_forecast.item_id\n", "       INNER JOIN (Select run_id from\n", "                          (SELECT jr.facility_id,\n", "                                  jr2.run_id\n", "                           FROM\n", "                             (SELECT facility_id,\n", "                                     max(completed_at) AS completed_at\n", "                              FROM (select * from lake_ars.job_run where is_valid_json(simulation_params) = true)\n", "                              WHERE completed_at IS NOT NULL\n", "                                AND json_extract_path_text(simulation_params, 'run', true) IN ('multi_node_ordering',\n", "                                                                                         'multi_business')\n", "                              GROUP BY 1) jr\n", "                           INNER JOIN lake_ars.job_run jr2 ON jr.facility_id=jr2.facility_id\n", "                           AND jr.completed_at = jr2.completed_at) where facility_id=%(facility_id_input)s)as lr on lr.run_id=ars_forecast.run_id\n", "       WHERE map.cloud_store_id=1104 and sto_date = CURRENT_DATE\n", "       GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13)x LEFT JOIN lake_retail.console_outlet co1 ON co1.id = x.rec_outlet_id\n", "       where co1.business_type_id in (7,8)\n", "\"\"\"\n", "ars_v1 = pd.read_sql_query(\n", "    sql=query, con=redshift, params={\"facility_id_input\": facility_id_input}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v1 = ars_v1.drop(\n", "    columns=[\n", "        \"hot_outlet\",\n", "        \"to_outlet_hot\",\n", "        \"to_outlet_hot\",\n", "        \"run_id\",\n", "        \"business_type_id\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v1[\"rec_outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket = pd.read_sql_query(\n", "    sql=\"\"\"select facility_id,item_id,case when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A' else 'B' end as buckets from consumer.rpc_daily_availability where order_date = (select max(order_date) from  consumer.rpc_daily_availability)\"\"\",\n", "    con=pb.get_connection(\"[Warehouse] Redshift\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bucket.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v1a = ars_v1.merge(\n", "    bucket,\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v1a.shape, ars_v1a[ars_v1a.buckets.isnull()].shape, ars_v1a[\n", "    ars_v1a.threshold_doi.isnull()\n", "].shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Superstore qty & opensto"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Superstore availability details\n", "superstore_inventory = \"\"\"select distinct co.id as outlet_id,item_id, sum(available_supermarket_inventory) as available_supermarket_inventory\n", "from\n", "(SELECT T1.outlet_id,\n", "   T1.supermarket_id,\n", "   T1.supermarket_name,\n", "   T1.item_id,\n", "   T2.delivery_date,\n", "coalesce(T1.Total_Qty_in_ministore,0) as Total_Qty_in_supermarket,\n", "coalesce(T2.to_be_picked_qty,0) as to_be_picked_qty_from_supermarket,\n", "coalesce(T1.Total_Qty_in_ministore,0) - coalesce(T2.to_be_picked_qty,0) as available_supermarket_inventory\n", "\n", "FROM\n", "\n", "   (SELECT          map.outlet_id,\n", "                    sm.id as supermarket_id,\n", "                    sm.name as supermarket_name,\n", "                    sm.capacity as supermarket_capacity,\n", "                    wil.item_id,\n", "                    sum(wil.quantity) AS Total_Qty_in_ministore\n", "   FROM  warehouse_location.warehouse_item_location wil\n", "   INNER JOIN warehouse_location.warehouse_storage_location sl ON sl.id = wil.location_id\n", "   INNER JOIN warehouse_location.warehouse_outlet_mapping map ON wil.warehouse_id = map.warehouse_id\n", "   INNER JOIN warehouse_location.warehouse_supermarket sm ON sl.supermarket_id = sm.id\n", "   WHERE map.outlet_id = %(outlet_id_input)s AND sl.location_type = 2 AND wil.active = 1 and sm.enabled = 1\n", "   GROUP BY 1,2,3,4,5)T1\n", "\n", "   LEFT JOIN \n", "\n", "   (SELECT  p.outlet_id, \n", "            p.supermarket_id,\n", "            date(convert_tz(p.min_scheduled_time,\"+00:00\",\"+05:30\")) as delivery_date, \n", "            i.item_id, \n", "            sum(i.required_quantity) as to_be_picked_qty \n", "  FROM  warehouse_location.warehouse_pick_list p \n", "  INNER JOIN warehouse_location.warehouse_pick_list_item i on p.id = i.pick_list_id\n", "  WHERE date(convert_tz(p.min_scheduled_time,\"+00:00\",\"+05:30\")) >= current_date\n", "  AND p.state in ('1','2','3','12','9','14','13') and p.type = 1 \n", "  group by 1,2,3,4)T2\n", "  ON T1.outlet_id = T2.outlet_id and T1.supermarket_id = T2.supermarket_id and T1.item_id = T2.item_id) ss\n", "  left join retail.console_outlet co on co.id = ss.outlet_id \n", "  group by 1,2\"\"\"\n", "superstore_inventory = pd.read_sql_query(\n", "    superstore_inventory, retail, params={\"outlet_id_input\": outlet_id_input}\n", ")\n", "\n", "superstore_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select * from\n", "(SELECT mn1.*,\n", "       CASE\n", "           WHEN item_pkl.pick_list_id IS NULL THEN expected_quantity\n", "           ELSE actual_quantity\n", "       END AS actual_quantity\n", "FROM\n", "  (SELECT sto.sto_id,\n", "          sto.sender_outlet_id,\n", "          sto.item_id,\n", "          sto.business_type_id,\n", "          sto.expected_quantity,\n", "          wave.wave_id,\n", "          picklist.pick_list_id,\n", "          outlet_id\n", "   FROM\n", "     (SELECT sto.sto_id,\n", "             sto.outlet_id sender_outlet_id,\n", "             o.name sender_outlet_name,\n", "             sto.merchant_outlet_id receiving_outlet_id,\n", "             o1.name receiver_outlet_name,\n", "             o1.business_type_id,\n", "             convert_tz(sto.delivery_date,'+00:00','+05:30') AS delivery_date,\n", "             si.item_id,\n", "             si.expected_quantity\n", "      FROM ims.ims_sto_details sto\n", "      INNER JOIN retail.console_outlet o ON sto.outlet_id = o.id\n", "      INNER JOIN retail.console_outlet o1 ON sto.merchant_outlet_id = o1.id and o1.business_type_id in (7,8)\n", "      INNER JOIN ims.ims_sto_item si ON sto.sto_id=si.sto_id\n", "      WHERE sto.outlet_id = %(outlet)s and sto.sto_state = 1\n", "       ) sto\n", "   LEFT JOIN\n", "     (SELECT DISTINCT id wave_id,\n", "                      entity_id sto_id\n", "      FROM warehouse_location.warehouse_wave) wave ON sto.sto_id=wave.sto_id\n", "   LEFT JOIN\n", "     (SELECT id pick_list_id,\n", "             outlet_id,\n", "             wave_id\n", "      FROM warehouse_location.warehouse_pick_list --  where state!=4\n", " ) picklist ON wave.wave_id=picklist.wave_id) mn1\n", "LEFT JOIN\n", "  (SELECT pick_list_id,\n", "          item_id,\n", "          required_quantity-picked_quantity actual_quantity,\n", "          item_name\n", "   FROM warehouse_location.warehouse_pick_list_item) item_pkl ON mn1.pick_list_id= item_pkl.pick_list_id\n", "AND mn1.item_id=item_pkl.item_id) x\n", "WHERE actual_quantity>0\"\"\"\n", "\n", "opensto = pd.read_sql_query(sql=query, con=retail, params={\"outlet\": outlet_id_input})\n", "opensto.facility_id = facility_id_input\n", "opensto = (\n", "    opensto.groupby([\"sender_outlet_id\", \"item_id\"])\n", "    .actual_quantity.sum()\n", "    .reset_index()\n", "    .rename(columns={\"sender_outlet_id\": \"outlet_id\"})\n", ")\n", "opensto.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory[\n", "    superstore_inventory.available_supermarket_inventory > 0\n", "].item_id.nunique(), superstore_inventory.available_supermarket_inventory.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secondary_avl1 = superstore_inventory[\n", "    superstore_inventory.available_supermarket_inventory > 0\n", "]\n", "secondary_avl = (\n", "    secondary_avl1.groupby([\"outlet_id\"])\n", "    .agg({\"item_id\": \"nunique\", \"available_supermarket_inventory\": sum})\n", "    .reset_index()\n", ")\n", "secondary_avl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory1 = superstore_inventory.merge(\n", "    opensto,\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "superstore_inventory1[\"actual_quantity\"] = superstore_inventory1[\n", "    \"actual_quantity\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory1[\"available_supermarket_inventory\"] = np.where(\n", "    superstore_inventory1[\"available_supermarket_inventory\"]\n", "    - superstore_inventory1[\"actual_quantity\"]\n", "    < 0,\n", "    0,\n", "    superstore_inventory1[\"available_supermarket_inventory\"]\n", "    - superstore_inventory1[\"actual_quantity\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["opensto.actual_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory1.available_supermarket_inventory.sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inventory "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\" \n", "select a.outlet_id, a.item_id,  a.IMS_Qty-coalesce(b.blocked_quantity,0) as unblocked_qty from\n", "(SELECT distinct\n", "         o.id as outlet_id,\n", "         p.item_id,\n", "         sum(I.QUANTITY) AS IMS_Qty\n", "  FROM ims.ims_inventory I\n", "  INNER JOIN rpc.product_product p ON p.variant_id = I.variant_id\n", "  INNER JOIN retail.console_outlet o ON o.id = I.outlet_id\n", "  WHERE I.QUANTITY>0\n", "    AND I.ACTIVE = 1\n", "    group by 1,2\n", ") a\n", "left join (SELECT outlet_id,item_id, SUM(Blocked_Quantity) as blocked_quantity\n", "FROM (\n", "select co.facility_id,outlet_id,item_id,date(convert_tz(scheduled_at,'+00:00','+05:30')) as delivery_date, quantity as Blocked_Quantity\n", "FROM\n", "(SELECT id, order_id, outlet as outlet_id, scheduled_at\n", "FROM ims.ims_order_details\n", "where status_id  = 1) order_details\n", "LEFT JOIN ims.ims_order_items as order_items ON order_details.id = order_items.order_details_id\n", "left join retail.console_outlet co on co.id = outlet_id) x\n", "GROUP BY 1,2) b on a.outlet_id = b.outlet_id and a.item_id = b.item_id\n", "\"\"\"\n", "unb_inv = pd.read_sql_query(sql=query1, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_inv[unb_inv[\"outlet_id\"] == 1104].unblocked_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x = unb_inv.merge(superstore_inventory, on = [\"facility_id\",\"item_id\"], how = 'inner')\n", "x = unb_inv.merge(superstore_inventory, on=[\"outlet_id\", \"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[x[\"unblocked_qty\"] > 0].item_id.nunique(), x.unblocked_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[\"unblocked_qty\"] = x[\"unblocked_qty\"].fillna(0) - x[\"available_supermarket_inventory\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[\"unblocked_qty\"] = x[\"unblocked_qty\"].fillna(0)\n", "x[\"unblocked_qty\"] = np.where(x[\"unblocked_qty\"] < 0, 0, x[\"unblocked_qty\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[x[\"unblocked_qty\"] > 0].item_id.nunique(), x.unblocked_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = x.rename(\n", "    columns={\"available_supermarket_inventory\": \"actual_supermarket_inventory\"}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Option 3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Logic\n", "Replenishment Indent Final = \n", "MAX(MIN(3* MAX(Sale_CPD_One_Day,1) + CPD_Sum - Front_End_Start_Inventory, CPD_Sum),0) - Current Secondary Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unb_inv.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a = ars_v1a.merge(\n", "    unb_inv,\n", "    left_on=[\"rec_outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a[\"unblocked_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a[ars_v3a.cpd_one_day.isnull()].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a[\"cpd_one_day\"] = ars_v3a[\"cpd_one_day\"].fillna(0)\n", "ars_v3a[\"cpd_one_day\"] = np.where(ars_v3a[\"cpd_one_day\"] < 1, 1, ars_v3a[\"cpd_one_day\"])\n", "ars_v3a[\"updated_qty\"] = np.where(\n", "    ars_v3a[\"cpd_one_day\"] * 3 + ars_v3a[\"cpd_sum\"] - ars_v3a[\"unblocked_qty\"]\n", "    > ars_v3a[\"cpd_sum\"],\n", "    ars_v3a[\"cpd_sum\"],\n", "    ars_v3a[\"cpd_one_day\"] * 3\n", "    + ars_v3a[\"cpd_sum\"]\n", "    - ars_v3a[\"unblocked_qty\"].fillna(0),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a[\"updated_qty\"] = np.where(ars_v3a[\"updated_qty\"] < 0, 0, ars_v3a[\"updated_qty\"])\n", "ars_v3a[\"updated_qty\"] = np.ceil(ars_v3a[\"updated_qty\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a[ars_v3a[\"updated_qty\"] > 0].item_id.nunique(), ars_v3a[\n", "    ars_v3a[\"updated_qty\"] > 0\n", "].updated_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3a = ars_v3a.rename(columns={\"outlet_id_x\": \"outlet_id\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b = (\n", "    ars_v3a.groupby([\"outlet_id\", \"facility_id\", \"item_id\"])\n", "    .agg({\"updated_qty\": sum})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_inventory1 = superstore_inventory1.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b = ars_v3b.merge(\n", "    superstore_inventory1,\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b[ars_v3b.available_supermarket_inventory > 0].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b[\"final_qty\"] = ars_v3b[\"updated_qty\"] - ars_v3b[\n", "    \"available_supermarket_inventory\"\n", "].fillna(0)\n", "ars_v3b[\"final_qty\"] = np.where(ars_v3b[\"final_qty\"] < 0, 0, ars_v3b[\"final_qty\"])\n", "\n", "ars_v3b.final_qty = np.ceil(ars_v3b.final_qty)\n", "\n", "ars_v3b = ars_v3b[ars_v3b[\"final_qty\"] > 0]\n", "\n", "ars_v3b.final_qty.sum(), ars_v3b[ars_v3b[\"final_qty\"] > 0].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3b.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_size_of_item = \"\"\"select item_id,\n", "    max(case when (outer_case_size = 0 or outer_case_size is NULL)  then 1 else outer_case_size end) as outer_case_size \n", "    from rpc.product_product\n", "    where active = 1\n", "    group by 1\n", "    \"\"\"\n", "case_size = pd.read_sql_query(case_size_of_item, pb.get_connection(\"retail\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3c = ars_v3b.merge(case_size, on=[\"item_id\"], how=\"left\")\n", "ars_v3c[\"final_qty_case\"] = (\n", "    np.ceil(ars_v3c[\"final_qty\"] / ars_v3c[\"outer_case_size\"])\n", "    * ars_v3c[\"outer_case_size\"]\n", ")\n", "ars_v3c.final_qty_case.sum(), ars_v3c.item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x[x.unblocked_qty > 0].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = x.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3d = ars_v3c.merge(\n", "    x.rename(columns={\"unblocked_qty\": \"prim\"}), on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "ars_v3d[\"prim\"] = ars_v3d[\"prim\"].fillna(0)\n", "ars_v3d[\"required_qty_prim\"] = np.where(\n", "    ars_v3d[\"prim\"] - ars_v3d[\"final_qty\"] < 0, ars_v3d[\"prim\"], ars_v3d[\"final_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.ceil(ars_v3d[\"required_qty_prim\"]).sum(), ars_v3d[\n", "    ars_v3d[\"required_qty_prim\"] > 0\n", "].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3d[\"required_qty_prim_case\"] = np.where(\n", "    ars_v3d[\"prim\"] - ars_v3d[\"final_qty_case\"] < 0,\n", "    ars_v3d[\"prim\"],\n", "    ars_v3d[\"final_qty_case\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.ceil(ars_v3d[\"required_qty_prim_case\"]).sum(), ars_v3d[\n", "    ars_v3d[\"required_qty_prim_case\"] > 0\n", "].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3d = ars_v3d[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"updated_qty\",\n", "        \"actual_supermarket_inventory\",\n", "        \"available_supermarket_inventory\",\n", "        \"final_qty\",\n", "        \"outer_case_size\",\n", "        \"final_qty_case\",\n", "        \"prim\",\n", "        \"required_qty_prim\",\n", "        \"required_qty_prim_case\",\n", "    ]\n", "]\n", "ars_v3d = ars_v3d.rename(\n", "    columns={\n", "        \"updated_qty\": \"required_qty\",\n", "        \"prim\": \"primary_available_qty\",\n", "        \"required_qty_prim\": \"indent_qty\",\n", "        \"required_qty_prim_case\": \"indent_qty_case\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3d.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3e = ars_v3d[ars_v3d[\"indent_qty_case\"] >= 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3e.shape, ars_v3d.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secondary_avl = secondary_avl.rename(\n", "    columns={\n", "        \"item_id\": \"secondary_avl_skus\",\n", "        \"available_supermarket_inventory\": \"secondary_avl_qty\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x1 = (\n", "    ars_v3d.groupby([\"outlet_id\"])\n", "    .agg({\"item_id\": \"nunique\", \"final_qty\": sum, \"final_qty_case\": sum})\n", "    .reset_index()\n", ")\n", "x1 = x1.rename(columns={\"item_id\": \"required_skus\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x2 = x1.merge(secondary_avl, on=[\"outlet_id\"])\n", "x2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x2[\"indent_qty\"] = ars_v3e[\"indent_qty\"].sum()\n", "x2[\"item_id\"] = ars_v3e[ars_v3e[\"indent_qty\"] > 0].item_id.nunique()\n", "x2[\"indent_qty_case\"] = ars_v3e[\"indent_qty_case\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y = (\n", "#     ars_v3e.groupby([\"outlet_id\"])\n", "#     .agg({\"indent_qty\": sum, \"\": \"nunique\", \"indent_qty_case\": sum})\n", "#     .reset_index()\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = x2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.rename(\n", "    columns={\"item_id\": \"final_indent_SKUs\", \"indent_qty_case\": \"final_indent_quantity\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final = final.drop(columns = ['required_skus','final_qty','final_qty_case'])\n", "final[\"prim_skus\"] = x[x[\"unblocked_qty\"] > 0].item_id.nunique()\n", "final[\"prim_qty\"] = x.unblocked_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final.drop(columns=[\"indent_qty\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_v3e[\"facility_id\"] = 264\n", "ars_v3e.indent_qty_case.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = ars_v3e[[\"outlet_id\", \"item_id\", \"indent_qty_case\", \"final_qty_case\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ars_v1a.to_csv(\"ars_run_v1a_output.csv\")\n", "# unb_inv.to_csv(\"unb_inv_output.csv\")\n", "ars_v3d.to_csv(\"final_indent_detailed_file.csv\")\n", "# x.to_csv(\"prim.csv\")\n", "# final1.to_csv(\"final_indent.csv\")\n", "# ars_v3e.to_csv(\"indent_test.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ars_v1a.to_csv(\"/tmp/ars_run_v1a_output.csv\")\n", "# unb_inv.to_csv(\"/tmp/unb_inv_output.csv\")\n", "ars_v3d.to_csv(\"/tmp/final_indent_file_1.csv\")\n", "# x.to_csv(\"/tmp/prim.csv\")\n", "# final1.to_csv(\"/tmp/final_indent.csv\")\n", "# ars_v3e.to_csv(\"/tmp/indent_test.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final1.rename(\n", "    columns={\n", "        \"final_qty_case\": \"case_qty_to_be_picked\",\n", "        \"indent_qty_case\": \"final_indent_qty\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1[\"supermarket_id\"] = 28"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1 = final1[\n", "    [\n", "        \"outlet_id\",\n", "        \"supermarket_id\",\n", "        \"item_id\",\n", "        \"case_qty_to_be_picked\",\n", "        \"final_indent_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.final_indent_qty.sum(), final1[final1[\"final_indent_qty\"] > 0].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["adrep = final1.copy()\n", "adrep[\"adrep\"] = adrep[\"case_qty_to_be_picked\"] - adrep[\"final_indent_qty\"]\n", "final[\"adrep_qty\"] = adrep[\"adrep\"].sum()\n", "final[\"adrep_skus\"] = adrep[adrep[\"adrep\"] > 0].item_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[\n", "    [\n", "        \"outlet_id\",\n", "        \"secondary_avl_skus\",\n", "        \"secondary_avl_qty\",\n", "        \"prim_skus\",\n", "        \"prim_qty\",\n", "        \"final_indent_SKUs\",\n", "        \"final_indent_quantity\",\n", "        \"adrep_skus\",\n", "        \"adrep_qty\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GPOS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"manual_\" + str(datetime.now())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_id = 1104\n", "WH_GPOS_URL = (\n", "    \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/wave-indent-with-items/\"\n", ")\n", "\n", "\n", "# if OUTLET_ID in outlets_all and is_disabled==\"YES\":\n", "\n", "df_final = final1.sort_values([\"outlet_id\", \"item_id\"])\n", "# df_final[\"case_qty_to_be_picked\"] = df_final[\"final_indent_qty\"]\n", "df_final = df_final[df_final[\"case_qty_to_be_picked\"] > 0]\n", "\n", "df_final[\"type\"] = 1\n", "df_final[\"run_id\"] = \"manual_\" + str(datetime.now())\n", "df_final.rename(\n", "    columns={\n", "        \"case_qty_to_be_picked\": \"forecasted_quantity\",\n", "        \"final_indent_qty\": \"quantity\",\n", "        \"supermarket_id\": \"to_supermarket_id\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "stores = list(df_final.to_supermarket_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "for store in stores:\n", "    for (run_id, outlet_id, to_supermarket_id, type), item_details in df_final[\n", "        df_final.to_supermarket_id == store\n", "    ].groupby([\"run_id\", \"outlet_id\", \"to_supermarket_id\", \"type\"]):\n", "        items = item_details[[\"item_id\", \"quantity\", \"forecasted_quantity\"]].to_dict(\n", "            orient=\"records\"\n", "        )\n", "        payload = {\n", "            \"run_id\": run_id,\n", "            \"type\": int(type),\n", "            \"outlet_id\": int(outlet_id),\n", "            \"to_supermarket_id\": int(store),\n", "            \"items\": items,\n", "        }\n", "        response = requests.post(WH_GPOS_URL, json=payload)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1.to_csv(\"final_indent_file.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final.item_id.nunique(), df_final.quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table = final.to_html(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from_email = \"<EMAIL>\"\n", "from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"Farukhnagar Replenishment Indent - \" + str(curr_date)\n", "\n", "\n", "html_content = (\n", "    \"\"\"\n", "Hi <PERSON>,<br><br>\n", "\n", "Details of SKUs and quantities to be picked from supermarkets are as follows:\n", "<br><br>\n", "\"\"\"\n", "    + final_table\n", "    + \"\"\"<br><br>\n", "\n", "\n", "Best,<br>\n", "Data Team\n", "\"\"\"\n", ")\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[\"final_indent_detailed_file.csv\", \"final_indent_file.csv\"],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "df_final[\"etl_timestamp_utc\"] = datetime.datetime.now()\n", "df_final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"to_supermarket_id\", \"type\": \"int\", \"description\": \"to_supermarket_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "    {\n", "        \"name\": \"forecasted_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"forecasted_quantity\",\n", "    },\n", "    {\"name\": \"quantity\", \"type\": \"float\", \"description\": \"quantity\"},\n", "    {\"name\": \"type\", \"type\": \"int\", \"description\": \"type\"},\n", "    {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"run_id\"},\n", "    {\n", "        \"name\": \"etl_timestamp_utc\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"etl_timestamp_utc\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"farukhnagar_indent_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"run_id\", \"item_id\"],\n", "    \"sortkey\": [\"etl_timestamp_utc\"],\n", "    \"incremental_key\": \"etl_timestamp_utc\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This tables stores the item level details of the replenishment indents sent to Farukhnagar facility\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(df_final, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}