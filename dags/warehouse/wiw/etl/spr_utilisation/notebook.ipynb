{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, date, timedelta\n", "import pytz\n", "import numpy as np\n", "\n", "strr = time.time()\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 200)\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "now = datetime.now(pytz.timezone(\"Asia/Kolkata\"))\n", "current_time = now.strftime(\"%H:%M\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = str(date.today())\n", "hour = now.hour\n", "# print(today)\n", "# print(hour)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Attributes of Dasna\n", "outlet_id = 714\n", "facility_id = 92\n", "warehouse_id = 74"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Need to update locations in the sheet for additional outlets and change the query and update the outlet id above "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pb.from_sheets(\"12LmRyYk8biimSqc_qm88b2F9lFUuWFUfghDqu6WBXos\", \"SPR Locations\")\n", "data[\"Level\"] = data[\"Level\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["locations = list(data[\"Location Name\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "st = time.time()\n", "data_q = f\"\"\"\n", "SELECT main.*\n", "FROM\n", "(WITH ol AS\n", "  (SELECT outlet_id,\n", "          warehouse_id\n", "   FROM lake_view_warehouse_location.warehouse_outlet_mapping\n", "   WHERE outlet_id = {outlet_id}),\n", "      locs AS\n", "  (SELECT warehouse_id,\n", "          id,\n", "          location_name,\n", "          zone_identifier\n", "   FROM lake_view_warehouse_location.warehouse_storage_location\n", "   WHERE upper(location_name) IN {*locations,} and warehouse_id = {warehouse_id}),\n", "      items AS\n", "  (SELECT i.item_id,\n", "          ids.name AS item_name,\n", "          i.location_id,\n", "          i.batch,\n", "          i.quantity,\n", "          i.variant_id,\n", "          v.vendor_name AS entity\n", "   FROM lake_view_warehouse_location.warehouse_item_location i\n", "   LEFT JOIN lake_view_vms.vms_vendor v ON v.id = i.entity_vendor_id\n", "   LEFT JOIN lake_view_rpc.item_details ids ON ids.item_id = i.item_id\n", "   WHERE (i.quantity > 0) \n", "     AND (i.location_id IN\n", "            (SELECT id\n", "             FROM locs)) )\n", "SELECT outlet_id,\n", "       item_id,\n", "       item_name,\n", "       variant_id,\n", "       location_id,\n", "       location_name,\n", "       zone_identifier,\n", "       cast(batch AS date) AS batch,\n", "       quantity,\n", "       entity\n", "FROM ol\n", "JOIN locs ON locs.warehouse_id = ol.warehouse_id\n", "JOIN items ON items.location_id = locs.id) main\n", "\"\"\"\n", "location_qty = pd.read_sql_query(data_q, presto)\n", "print(\"Run time: \", time.time() - st)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location_qty1 = location_qty.merge(\n", "    data, left_on=[\"location_name\"], right_on=[\"Location Name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location_qty1[\"location_type\"] = np.where(\n", "    location_qty1[\"Level\"] > 1, \"G+ location\", \"G location\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# location_qty1.groupby(['Location Type']).agg({'item_id':'nunique','quantity':sum}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spr_data = location_qty1[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"variant_id\",\n", "        \"entity\",\n", "        \"batch\",\n", "        \"quantity\",\n", "        \"location_id\",\n", "        \"location_name\",\n", "        \"Level\",\n", "        \"location_type\",\n", "        \"zone_identifier\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(spr_data, \"12LmRyYk8biimSqc_qm88b2F9lFUuWFUfghDqu6WBXos\", \"SPR Items\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# migration items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pilot = pb.from_sheets(\"12LmRyYk8biimSqc_qm88b2F9lFUuWFUfghDqu6WBXos\", \"Dasna_input\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qty = pilot[[\"outlet_id\", \"item_id\", \"avg_cpd\", \"req_doi\"]]\n", "qty[\"outlet_id\"] = qty[\"outlet_id\"].astype(int)\n", "qty[\"avg_cpd\"] = qty[\"avg_cpd\"].astype(int)\n", "qty[\"req_doi\"] = qty[\"req_doi\"].astype(float)\n", "qty[\"item_id\"] = qty[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qty[\"min\"] = np.ceil(qty[\"avg_cpd\"] * (qty[\"req_doi\"] - 0.5))\n", "qty[\"max\"] = np.ceil(qty[\"avg_cpd\"] * (qty[\"req_doi\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spr_data1 = spr_data[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"variant_id\",\n", "        \"batch\",\n", "        \"location_name\",\n", "        \"quantity\",\n", "        \"Level\",\n", "        \"entity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gr = spr_data1[spr_data1[\"Level\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gr1 = gr.groupby([\"outlet_id\", \"item_id\"]).quantity.sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = qty.merge(gr1, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "res = res.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res[\"req\"] = np.where(res[\"quantity\"] <= res[\"min\"], res[\"max\"] - res[\"quantity\"], 0)\n", "res[\"extra\"] = np.where(res[\"quantity\"] > res[\"max\"], res[\"quantity\"] - res[\"max\"], 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grplus = spr_data1[spr_data1[\"Level\"] != 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grplus = grplus[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"variant_id\",\n", "        \"batch\",\n", "        \"location_name\",\n", "        \"entity\",\n", "        \"quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pilot[\"item_id\"] = pilot[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = list(pilot.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT DISTINCT 714 as outlet_id, \n", "                id.item_id, \n", "                id.outer_case_size as case_quantity\n", "FROM lake_rpc.item_details id \n", "WHERE item_id IN {*items1,}\n", "\"\"\"\n", "spr_data2 = pd.read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grfinal = grplus[grplus.item_id.isin(pilot.item_id.unique())].merge(\n", "    res[[\"outlet_id\", \"item_id\", \"req\"]], on=[\"outlet_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grfinal = grfinal.sort_values(\n", "    by=[\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"batch\",\n", "        \"entity\",\n", "        \"quantity\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grfinal = grfinal[grfinal[\"req\"] != 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grfinal[\"cumsum_qty\"] = grfinal.groupby(\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "    ]\n", ").quantity.cumsum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grfinal[\"final\"] = grfinal[\"cumsum_qty\"] - grfinal[\"req\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(\n", "    columns=[\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"variant_id\",\n", "        \"batch\",\n", "        \"location_name\",\n", "        \"entity\",\n", "        \"loc_quantity\",\n", "        \"quantity\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for outlet in grfinal.outlet_id.unique():\n", "    for item in grfinal[(grfinal.outlet_id == outlet)].item_id.unique():\n", "        count = 1\n", "        rcount = 1\n", "        for index, row in grfinal[\n", "            (grfinal.outlet_id == outlet) & (grfinal.item_id == item)\n", "        ].iterrows():\n", "            if rcount == 1:\n", "                required = row.req\n", "                rcount += 1\n", "            if row.final <= 0:\n", "                df.loc[len(df)] = [\n", "                    row.outlet_id,\n", "                    row.item_id,\n", "                    row.variant_id,\n", "                    row.batch,\n", "                    row.location_name,\n", "                    row.entity,\n", "                    row.quantity,\n", "                    row.quantity,\n", "                ]\n", "                required = required - row.quantity\n", "            else:\n", "                if count == 1:\n", "                    df.loc[len(df)] = [\n", "                        row.outlet_id,\n", "                        row.item_id,\n", "                        row.variant_id,\n", "                        row.batch,\n", "                        row.location_name,\n", "                        row.entity,\n", "                        row.quantity,\n", "                        required,\n", "                    ]\n", "                    count += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"quantity\"] = df[\"quantity\"].astype(\"int32\")\n", "df[\"loc_quantity\"] = df[\"loc_quantity\"].astype(\"int32\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.merge(spr_data2, how=\"inner\", on=[\"outlet_id\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index, row in df.iterrows():\n", "    if row.loc_quantity == row.quantity:\n", "        pass\n", "    else:\n", "        if row.quantity % row.case_quantity == 0:\n", "            pass\n", "        else:\n", "            qty1 = ((row.quantity // row.case_quantity) + 1) * row.case_quantity\n", "            df.loc[index] = [\n", "                row.outlet_id,\n", "                row.item_id,\n", "                row.variant_id,\n", "                row.batch,\n", "                row.location_name,\n", "                row.entity,\n", "                row.loc_quantity,\n", "                qty1,\n", "                row.case_quantity,\n", "            ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final = df[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"location_name\",\n", "        \"variant_id\",\n", "        \"quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = (\n", "    df_final.groupby([\"outlet_id\", \"item_id\"])\n", "    .quantity.sum()\n", "    .reset_index()\n", "    .merge(qty[[\"outlet_id\", \"item_id\", \"avg_cpd\"]], on=[\"outlet_id\", \"item_id\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd[\"req_doi\"] = round(\n", "    fd[\"quantity\"].astype(float) * 1.0 / fd[\"avg_cpd\"].astype(float), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = fd.sort_values(by=[\"req_doi\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd[\"rank\"] = range(1, len(fd) + 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = fd[[\"outlet_id\", \"item_id\", \"rank\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fx = fd.merge(\n", "    df_final,\n", "    on=[\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fx = fx[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"variant_id\",\n", "        \"location_name\",\n", "        \"quantity\",\n", "        \"rank\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fx = fx.rename(\n", "    columns={\n", "        \"quantity\": \"required_quantity\",\n", "        \"variant_id\": \"suggested_variant_id\",\n", "        \"location_name\": \"suggested_location\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fx"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "for outlet in fx.outlet_id.unique():\n", "    for item in fx[(fx.outlet_id == outlet)].item_id.unique():\n", "        list1 = []\n", "        dict2 = {}\n", "        for index, row in fx[\n", "            (fx.outlet_id == outlet) & (fx.item_id == item)\n", "        ].iterrows():\n", "            dict1 = {}\n", "            dict1[\"suggested_location\"] = row.suggested_location\n", "            dict1[\"suggested_variant_id\"] = row.suggested_variant_id\n", "            dict1[\"required_quantity\"] = row.required_quantity\n", "            dict1[\"rank\"] = row[\"rank\"]\n", "            list1.append(dict1)\n", "        dict2[\"item_id\"] = item\n", "        dict2[\"outlet_id\"] = outlet\n", "        dict2[\"locations\"] = list1\n", "        # print(dict2)\n", "        url = \"http://retail-whl-retail.prod-sgp-k8s.grofer.io/v1/create-spr-replenishment-tasks/\"\n", "        payload = json.dumps(dict2, default=str)\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "        response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "        if response.status_code != 204:\n", "            print(response.text)\n", "            response = requests.request(\"POST\", url, headers=headers, data=payload)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"End of Code\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}