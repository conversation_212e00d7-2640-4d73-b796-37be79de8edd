{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%html\n", "# <span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at 'In [9]'.</span>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = (datetime.now(tz) - timed<PERSON>ta(days=30)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timedelta(days=0)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start, end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"SELECT facility_id,\n", "       facility_name,\n", "       outlet_id,\n", "       station,\n", "       city,\n", "       scheduleddate,\n", "       sub_order_id,\n", "       item_id,\n", "       variant_id,\n", "       l1_reason,\n", "       reason,\n", "       returning_entity,\n", "       employee_id,\n", "       entityname,\n", "       expected_quantity,\n", "       inward_quantity,\n", "       selling_price,\n", "       dispatch_time,\n", "       whreturntaskid,\n", "       whtaskcreatedat,\n", "       whtaskreceivedat,\n", "       whtaskcompletedat,\n", "       inward_date\n", "FROM metrics.returns_wh_lm_integration\n", "WHERE scheduleddate::date BETWEEN %(start)s AND %(end)s\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base = pd.read_sql_query(\n", "    sql=Query, con=redshift_con, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["li = Base[\"sub_order_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["li = li.drop_duplicates(keep=\"last\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["li = li.to_list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["li2 = \"','\".join(map(str, li))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# li2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "good_returns_query = (\n", "    \"\"\"\n", "  select  iil.grofers_order_id::int sub_order_id ,iil.variant_id, coalesce(sum(iil.\"delta\"),0) as good_quantity\n", "  from\n", "   lake_ims.ims_inventory_log iil \n", "   join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id and  iut.group_name in ('return')\n", "   --, 'HRA updates')\n", "   and iil.grofers_order_id in ('\"\"\"\n", "    + li2\n", "    + \"\"\"')\n", "   join lake_pos.view_pos_invoice ppi on ppi.grofers_order_id=iil.grofers_order_id and iil.invoice_id=ppi.invoice_id\n", "  and (\n", "       (iut.is_good_inventory_update=TRUE and iut.good_inventory_operation = '+')\n", "        or (iut.is_inventory_update =TRUE and iut.inventory_operation = '+')\n", "        )\n", "     and iil.grofers_order_id IS NOT NULL\n", "     and iil.grofers_order_id !='' group by 1,2\n", "\n", "\n", "\"\"\"\n", ")\n", "# print(good_returns_query)\n", "\n", "good_returns_df = pd.read_sql_query(sql=good_returns_query, con=redshift_con)\n", "good_returns_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_2 = pd.merge(\n", "    Base,\n", "    good_returns_df,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "bad_returns_query = (\n", "    \"\"\"\n", "\n", "\n", "\n", "  select iil.grofers_order_id::int sub_order_id, iil.variant_id, coalesce(sum(iil.\"delta\"),0) bad_quantity,\n", "  coalesce(sum(case when iil.inventory_update_type_id in (7,33) then iil.\"delta\" else 0 end ),0) as damage_quantity,\n", "  coalesce(sum(case when iil.inventory_update_type_id in (9,34,63,67) then iil.\"delta\" else 0 end),0) as expired_quantity\n", "  from\n", "   lake_ims.ims_inventory_log iil \n", "   join lake_ims.ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id  \n", "   and iut.group_name in ('return')\n", "   and iil.grofers_order_id in ('\"\"\"\n", "    + li2\n", "    + \"\"\"')\n", "  and iut.is_bad_inventory_update=1\n", "         and iut.bad_inventory_operation='+'\n", "   join lake_pos.view_pos_invoice ppi on ppi.grofers_order_id=iil.grofers_order_id and iil.invoice_id=ppi.invoice_id\n", "  group by 1,2\n", "  \n", "\n", "\n", "\"\"\"\n", ")\n", "# print(good_returns_query)\n", "\n", "bad_returns_df = pd.read_sql_query(sql=bad_returns_query, con=redshift_con)\n", "bad_returns_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_3 = pd.merge(\n", "    Base_2,\n", "    bad_returns_df,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_3[\n", "    Base_3[\"good_quantity\"].isnull()\n", "    & Base_3[\"bad_quantity\"].isnull()\n", "    & Base_3[\"inward_quantity\"]\n", "    > 0\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"expectedreturndate\": \"Date of Return Generation\",\n", "    \"l1_reason\": \"l1 reason for return i.e. cancel , reprocure or doorstep\",\n", "    \"reason\": \"further bifurcation of doorstep into granular reasons\",\n", "    \"scheduleddate\": \"scheduled delivery date\",\n", "    \"inward_date\": \"date of inward/reverse billing\",\n", "    \"expected_quantity\": \"Quantity Expected to return\",\n", "    \"selling_price\": \"Price of item at billing\",\n", "    \"inward_quantity\": \"Quantity Inwarded out of Expected\",\n", "    \"inwardedvalue\": \"Amount Inwarded against expected\",\n", "    \"expectedvalue\": \"Amount Expected\",\n", "    \"good_quantity\": \"good quantity out of total returned\",\n", "    \"bad_quantity\": \"bad quantity out of total returned\",\n", "    \"damage_quantity\": \"damage qty out of bad qty\",\n", "    \"expired_quantity\": \"expired out of bad qty\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_3[\n", "    Base_3[\"good_quantity\"] + Base_3[\"bad_quantity\"] > Base_3[\"inward_quantity\"]\n", "].bad_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_3[\"inward_quantity\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Base_3[\"key\"] = (\n", "    Base_3[\"sub_order_id\"].astype(str) + \"|\" + Base_3[\"variant_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(Base_3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"returns_good_bad_inv\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"sub_order_id\"],\n", "    \"incremental_key\": \"scheduleddate\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"All returns being marked good or bad is being stored here\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(Base_3, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}