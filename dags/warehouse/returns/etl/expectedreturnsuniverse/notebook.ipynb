{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"hide_input": true, "inputHidden": true}, "outputs": [], "source": ["# %%html\n", "# <span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at 'In [9]'.</span>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 1.084232, "end_time": "2020-01-15T07:04:01.692986", "exception": false, "start_time": "2020-01-15T07:04:00.608754", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "today = date.today()\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = datetime.now(tz).replace(day=1).strftime(\"%Y-%m-%d\")\n", "start1 = (datetime.now(tz).replace(day=1) - <PERSON><PERSON><PERSON>(days=7)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if today.day <= 5:\n", "    start = (today - relativedelta(months=1)).replace(day=1).strftime(\"%Y-%m-%d\")\n", "    start1 = (\n", "        (today - <PERSON><PERSON><PERSON>(months=1)).replace(day=1) - <PERSON><PERSON><PERSON>(days=7)\n", "    ).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.456348, "end_time": "2020-01-15T07:04:02.626661", "exception": false, "start_time": "2020-01-15T07:04:02.170313", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["run_period_start = start\n", "run_period_end = end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# datetime.now(tz).replace(day=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.497758, "end_time": "2020-01-15T07:04:03.548918", "exception": false, "start_time": "2020-01-15T07:04:03.051160", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "# run_period_start = \"2020-03-05\"\n", "# run_period_end = \"2020-04-21\"\n", "# cwd = \"/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets\""]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.444813, "end_time": "2020-01-15T07:04:04.589931", "exception": false, "start_time": "2020-01-15T07:04:04.145118", "status": "completed"}, "tags": []}, "source": ["# L0 Category Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 36.35422, "end_time": "2020-01-15T07:04:41.372564", "exception": false, "start_time": "2020-01-15T07:04:05.018344", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "item_category_query = \"\"\"\n", "SELECT distinct\n", "pp.variant_id,\n", "CAT.NAME AS l0,\n", "CAT1.NAME AS l1\n", "/*\n", "pp.item_id,\n", "ipm.product_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID,\n", "CAT2.NAME AS l2,\n", "CAT2.ID AS l2_ID,\n", "pb.name as brand,\n", "pb.id as brand_id\n", "P.ENABLED_FLAG*/\n", "FROM\n", "lake_cms.GR_PRODUCT P\n", "INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN lake_cms.GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN lake_cms.GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join lake_rpc.item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join lake_rpc.product_product pp on pp.item_id=ipm.item_id\n", "left join lake_rpc.product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1 \n", "\"\"\"\n", "\n", "item_category_df = pd.read_sql_query(sql=item_category_query, con=redshift_con)\n", "item_category_df.sample(2)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.420572, "end_time": "2020-01-15T07:04:43.058427", "exception": false, "start_time": "2020-01-15T07:04:42.637855", "status": "completed"}, "tags": []}, "source": ["# Item Variant attributes from rpc.product_product "]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 27.266135, "end_time": "2020-01-15T07:05:10.751417", "exception": false, "start_time": "2020-01-15T07:04:43.485282", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "rpc_product_product_query = \"\"\"\n", "select item_id::int as item_id_1, *  from \n", "lake_rpc.product_product rpp where active=1 and approved=1\n", "\"\"\"\n", "\n", "rpc_product_product_df = pd.read_sql_query(\n", "    sql=rpc_product_product_query, con=redshift_con\n", ")\n", "rpc_product_product_df.sample(2)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.434932, "end_time": "2020-01-15T07:05:12.453246", "exception": false, "start_time": "2020-01-15T07:05:12.018314", "status": "completed"}, "tags": []}, "source": ["# Product to Item Mapping through IT PD Log"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 17.51, "end_time": "2020-01-15T07:05:30.399059", "exception": false, "start_time": "2020-01-15T07:05:12.889059", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "it_pd_log_query = \"\"\"\n", "select  \n", "x.product_id::int, item_id::int ,multiplier  \n", "from \n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.it_pd_log i \n", "--join order_product_details opd on opd.product_id=i.product_id\n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\n", "\"\"\"\n", "\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift_con)\n", "it_pd_log_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# roi.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as billed_at,\n", "# os.install_ts as suborder_created_at,\n", "# oi.product_id::int,\n", "# roi.expected_quantity::int as expected_quantity\n", "# from consumer.lm_return_order ro\n", "# join consumer.lm_return_order_item roi ON ro.id= roi.return_order_id\n", "# join consumer.lm_return_order_item_transaction roit ON roit.return_order_item_id=roi.id\n", "# join consumer.lm_order_item oi ON roi.item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# --join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "# --join consumer.rpc_product_product pp on pp.item_id\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "# where\n", "# ro.return_cause='DELIVERED'\n", "# union\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as suborder_created_at,\n", "# os.install_ts as sub_order_created,\n", "# oi.product_id::int,\n", "# ri.quantity::int as expected_quantity\n", "# from consumer.lm_recon_order ro\n", "# join consumer.lm_recon_item ri ON ri.recon_order_id=ro.id\n", "# join consumer.lm_recon r ON ro.recon_id=r.id\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "# where\n", "# ro.recon_return_order_cause='DELIVERED'\n", "# union\n", "\n", "\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as billed_at,\n", "# os.install_ts as suborder_created_at,\n", "# oi.product_id::int,\n", "# ri.quantity::int as expected_quantity\n", "# from\n", "# consumer.lm_return_flow_order ro\n", "# join consumer.lm_return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id and ri.quantity>0\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=83344005\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.45856, "end_time": "2020-01-15T07:05:32.129368", "exception": false, "start_time": "2020-01-15T07:05:31.670808", "status": "completed"}, "tags": []}, "source": ["# Doorstep returns"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 186.836635, "end_time": "2020-01-15T07:08:39.388042", "exception": false, "start_time": "2020-01-15T07:05:32.551407", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "doorstep_return_order_query = \"\"\"\n", "    \n", "select \n", "'DOORSTEP RETURN' as l1_Reason,\n", "loi.missing_reason as reason,\n", "os.type as sub_order_type,\n", "os.id as sub_order_id,\n", "os.order_id as order_id,\n", "pi.outlet_id,\n", "(timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as ScheduledDate,\n", "lo.delivery_time as delivery_at,\n", "lo.delivery_time::date as expectedreturndate,\n", "pi.pos_timestamp as billed_at, \n", "os.install_ts as suborder_created_at,\n", "loi.product_id,\n", "coalesce(loi.procured_quantity,0)-coalesce(loi.delivered_quantity,0) as expected_quantity\n", "from\n", "lake_last_mile.order lo \n", "join lake_last_mile.order_item loi on loi.order_id=lo.id and len(trim(loi.missing_reason))>0\n", "join  lake_oms_bifrost.oms_suborder os ON os.id=loi.sub_order_id  \n", "join lake_pos.view_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1 \n", "--join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "--join consumer.rpc_product_product pp on pp.item_id\n", "where ((timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date>=%(run_period_start)s\n", "and (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date<=%(run_period_end)s )\n", "and os.type not like '%%Digital%%' and os.type not like '%%Drop%%'\n", "\"\"\"\n", "\n", "# print (doorstep_return_order_query)\n", "\n", "doorstep_return_order_df = pd.read_sql_query(\n", "    sql=doorstep_return_order_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df.scheduleddate.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.482395, "end_time": "2020-01-15T07:08:41.167101", "exception": false, "start_time": "2020-01-15T07:08:40.684706", "status": "completed"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df = doorstep_return_order_df.replace(r\"^\\s*$\", 0, regex=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df[\"sub_order_id\"] = doorstep_return_order_df[\n", "    \"sub_order_id\"\n", "].astype(int)\n", "doorstep_return_order_df[\"product_id\"] = doorstep_return_order_df[\"product_id\"].astype(\n", "    float\n", ")\n", "doorstep_return_order_df[\"expected_quantity\"] = doorstep_return_order_df[\n", "    \"expected_quantity\"\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df[doorstep_return_order_df.sub_order_id == 93071898]"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["### Product ID from doorstep returns to be mapped with item ids\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df = pd.merge(\n", "    left=doorstep_return_order_df,\n", "    right=it_pd_log_df,\n", "    how=\"inner\",\n", "    left_on=[\"product_id\"],\n", "    right_on=[\"product_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Duplicate on suborder_id and product_id\n", "\n", "# b=doorstep_return_order_item_level_df\n", "# b['is_duplicate']=b.duplicated(subset=['sub_order_id', 'product_id'], keep='first')\n", "# b.head()\n", "# x=b[b.is_duplicate==True]\n", "# x.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df[\"new_expected_quantity\"] = (\n", "    doorstep_return_order_item_level_df[\"expected_quantity\"]\n", "    * doorstep_return_order_item_level_df[\"multiplier\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_item_level_df[\n", "    doorstep_return_order_item_level_df.sub_order_id == 93071898\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# check for the repition in original dataframe\n", "# doorstep_return_order_item_level_df.loc[\n", "#     doorstep_return_order_item_level_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_groupby_df = (\n", "    doorstep_return_order_item_level_df.groupby(\n", "        [\n", "            \"l1_reason\",\n", "            \"reason\",\n", "            \"sub_order_type\",\n", "            \"sub_order_id\",\n", "            \"delivery_at\",\n", "            \"outlet_id\",\n", "            \"order_id\",\n", "            \"expectedreturndate\",\n", "            \"scheduleddate\",\n", "            \"billed_at\",\n", "            \"suborder_created_at\",\n", "            \"item_id\",\n", "        ],\n", "        as_index=False,\n", "    ).agg({\"new_expected_quantity\": \"sum\"})\n", ")\n", "# doorste"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_item_level_groupby_df[\n", "    doorstep_return_order_item_level_groupby_df.sub_order_id == 93071898\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_groupby_df.loc[\n", "#     doorstep_return_order_item_level_groupby_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# merge items to all possible variants\n", "\n", "doorstep_return_order_variant_level_df = pd.merge(\n", "    left=doorstep_return_order_item_level_groupby_df,\n", "    right=rpc_product_product_df,\n", "    how=\"inner\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id_1\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df[\n", "    doorstep_return_order_variant_level_df[\"sub_order_id\"] == 93071898\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df[\n", "    doorstep_return_order_variant_level_df.sub_order_id == 93071898\n", "][[\"variant_id\", \"item_id_x\", \"sub_order_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2 = doorstep_return_order_variant_level_df[\n", "    [\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"sub_order_type\",\n", "        \"sub_order_id\",\n", "        \"delivery_at\",\n", "        \"outlet_id\",\n", "        \"order_id\",\n", "        \"expectedreturndate\",\n", "        \"scheduleddate\",\n", "        \"billed_at\",\n", "        \"suborder_created_at\",\n", "        \"item_id_x\",\n", "        \"variant_id\",\n", "        \"new_expected_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2.rename(\n", "    columns={\"new_expected_quantity\": \"required_quantity\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2[\n", "    doorstep_return_order_variant_level_df2[\"sub_order_id\"] == 93071898\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2[doorstep_return_order_variant_level_df2.sub_order_id==90887415]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2[\n", "#     [\"sub_order_id\", \"variant_id\"]\n", "# ].drop_duplicates().shape[0] - doorstep_return_order_variant_level_df2.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2.required_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2[\n", "    doorstep_return_order_variant_level_df2.sub_order_id == 93071898\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["InvoiceDetails = \"\"\"\n", "Select \n", "pi.grofers_order_id as SubOrderId,\n", "pipd.variant_id,\n", "pipd.name as item_name,\n", "avg(selling_price) selling_price\n", "from lake_pos.view_pos_invoice pi\n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "and pi.created_at>=%(run_period_start)s and pi.created_at::date<=current_date-15\n", "Group by 1,2,3\n", "\"\"\"\n", "\n", "Invoice1 = pd.read_sql_query(\n", "    sql=InvoiceDetails,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": start1},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["InvoiceDetails = \"\"\"\n", "Select \n", "pi.grofers_order_id as SubOrderId,\n", "pipd.variant_id,\n", "pipd.name as item_name,\n", "avg(selling_price) selling_price\n", "from lake_pos.view_pos_invoice pi\n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "and pi.created_at>=current_date-16 and pi.created_at::date<=current_date\n", "Group by 1,2,3\n", "\"\"\"\n", "\n", "Invoice2 = pd.read_sql_query(\n", "    sql=InvoiceDetails,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": start1},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice = pd.concat([Invoice1, Invoice2], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice[\"suborderid\"] = Invoice[\"suborderid\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df3 = pd.merge(\n", "    left=doorstep_return_order_variant_level_df2,\n", "    right=Invoice,\n", "    how=\"inner\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"suborderid\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df3[\n", "    doorstep_return_order_variant_level_df3[\"sub_order_id\"] == 93578824\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df3.scheduleddate.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["InvoiceDetails_Returned = \"\"\"\n", "Select \n", "distinct pi.grofers_order_id::int as SubOrderId,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "max(pi.created_at) as Inward_Date,\n", "sum(quantity) as returned_quantity\n", "from lake_pos.view_pos_invoice pi\n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "and pi.created_at>=%(run_period_start)s and pi.created_at::date<=current_date\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "Invoice_Returned = pd.read_sql_query(\n", "    sql=InvoiceDetails_Returned,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice_Returned.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice_Returned[Invoice_Returned[\"suborderid\"] == 93578824]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4 = doorstep_return_order_variant_level_df3.merge(\n", "    Invoice_Returned,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"suborderid\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4[\n", "    doorstep_return_order_variant_level_df4.sub_order_id == 92917955\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4[\n", "    \"returned_quantity\"\n", "] = doorstep_return_order_variant_level_df4[\"returned_quantity\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4[\n", "    doorstep_return_order_variant_level_df4.sub_order_id == 92936400\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["len(doorstep_return_order_variant_level_df2.scheduleddate.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_invoice_query = \"\"\"\n", "# select * from\n", "# (select\n", "# roi.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# --avg(pipd.quantity) as required_quantity,\n", "# avg(selling_price)::int selling_price\n", "# from consumer.lm_return_order ro\n", "# join consumer.lm_return_order_item roi ON ro.id= roi.return_order_id\n", "# join consumer.lm_return_order_item_transaction roit ON roit.return_order_item_id=roi.id\n", "# join consumer.lm_order_item oi ON roi.item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<='2020-06-02'\n", "# --%(run_period_end)s\n", "# where\n", "# ro.return_cause='DELIVERED'\n", "# group by 1,2,3\n", "# union\n", "# select\n", "# ri.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# --avg(pipd.quantity) as required_quantity,\n", "# avg(selling_price)::int as selling_price\n", "# from consumer.lm_recon_order ro\n", "# join consumer.lm_recon_item ri ON ri.recon_order_id=ro.id\n", "# join consumer.lm_recon r ON ro.recon_id=r.id\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<='2020-06-02'\n", "# --%(run_period_end)s\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "# where\n", "# ro.recon_return_order_cause='DELIVERED'\n", "# group by 1,2,3\n", "# union\n", "# select\n", "# ri.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# --avg(pipd.quantity) as required_quantity,\n", "# avg(selling_price)::int as selling_price\n", "\n", "# from\n", "# consumer.lm_return_flow_order ro\n", "# join consumer.lm_return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id and  ri.quantity>0\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=78176787\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<='2020-06-02'\n", "# --%(run_period_end)s\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "\n", "# group by 1,2,3\n", "# )c\n", "# group by 1,2,3,4\n", "\n", "# \"\"\"\n", "\n", "# doorstep_return_order_invoice_df = pd.read_sql_query(\n", "#     sql=doorstep_return_order_invoice_query,\n", "#     con=redshift_con,\n", "#     params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", "# )\n", "# doorstep_return_order_invoice_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_invoice_df[doorstep_return_order_invoice_df.sub_order_id==92505200].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_invoice_df[\n", "#     [\"sub_order_id\", \"variant_id\"]\n", "# # ].drop_duplicates().shape[0] - doorstep_return_order_invoice_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_invoice_df.groupby(\n", "#     [\"sub_order_id\", \"variant_id\", \"item_name\", \"selling_price\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_invoice_df[doorstep_return_order_invoice_df['sub_order_id']==76327579 ].sort_values('selling_price',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df = pd.merge(\n", "#     left=doorstep_return_order_variant_level_df2,\n", "#     right=doorstep_return_order_invoice_df,\n", "#     how=\"inner\",\n", "#     left_on=[\"sub_order_id\", \"variant_id\"],\n", "# #     right_on=[\"sub_order_id\", \"variant_id\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df[doorstep_return_invoice_variant_level_df.sub_order_id==90887415]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df.loc[\n", "#     doorstep_return_invoice_variant_level_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df=doorstep_return_order_variant_level_df2.groupby(['outlet_id',\t'suborder_created_at',\t'billed_at'\t,'sub_order_id',\t'variant_id'\n", "#                                         ],as_index = False).agg({'required_quantity':'sum',\n", "\n", "\n", "#                                                                                               })\n", "# doorstep_return_invoice_variant_level_df.loc[doorstep_return_order_variant_level_df2['sub_order_id']==79008835].sort_values('variant_id')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df[\n", "#     [\"sub_order_id\", \"variant_id\"]\n", "# # ].drop_duplicates().shape[0] - doorstep_return_invoice_variant_level_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# redshift_con = pb.get_connection(\"redshift\")\n", "\n", "# doorstep_return_order_inward_query = \"\"\"\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# roi.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# coalesce(max(pi.pos_timestamp)::timestamp, null) as inwarded_at,\n", "# avg(pipd.quantity)::int as inward_quantity\n", "\n", "# from consumer.lm_return_order ro\n", "# join consumer.lm_return_order_item roi ON ro.id= roi.return_order_id\n", "# join consumer.lm_return_order_item_transaction roit ON roit.return_order_item_id=roi.id\n", "# join consumer.lm_order_item oi ON roi.item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "# join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "# and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "# left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "# where\n", "# greo.invoice_id is null\n", "\n", "# and\n", "# ro.return_cause='DELIVERED'\n", "# group by 1,2,3,4\n", "# union\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# coalesce(max(pi.pos_timestamp)::timestamp,null) as inwarded_at,\n", "# avg(pipd.quantity)::int as inward_quantity\n", "# from consumer.lm_recon_order ro\n", "# join consumer.lm_recon_item ri ON ri.recon_order_id=ro.id\n", "# join consumer.lm_recon r ON ro.recon_id=r.id\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "# join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "# and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "# left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "# where\n", "# greo.invoice_id is null\n", "# and\n", "# ro.recon_return_order_cause='DELIVERED'\n", "# group by 1,2,3,4\n", "\n", "\n", "# union\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pipd.variant_id::varchar,\n", "# pipd.name::varchar as item_name,\n", "# coalesce(max(pi.pos_timestamp)::timestamp,null) as inwarded_at,\n", "# avg(pipd.quantity)::int as inward_quantity\n", "\n", "# from\n", "# consumer.lm_return_flow_order ro\n", "# join consumer.lm_return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "# join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "# and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "# join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "# left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "# where\n", "# greo.invoice_id is null\n", "# group by 1,2,3,4\n", "\n", "\n", "# \"\"\"\n", "\n", "# doorstep_return_order_inward_df = pd.read_sql_query(\n", "#     sql=doorstep_return_order_inward_query,\n", "#     con=redshift_con,\n", "#     params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", "# )\n", "# doorstep_return_order_inward_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4[\n", "    [\"sub_order_id\", \"variant_id\", \"inward_date\"]\n", "].drop_duplicates().shape[0] - doorstep_return_order_variant_level_df4.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_inward_df.groupby(\n", "#     [\"reason\", \"sub_order_id\", \"variant_id\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_inward_df[doorstep_return_order_inward_df['sub_order_id']==72932733]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_inward_df.loc[\n", "#     doorstep_return_order_inward_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df = pd.merge(\n", "#     doorstep_return_invoice_variant_level_df,\n", "#     doorstep_return_order_inward_df,\n", "#     how=\"left\",\n", "#     left_on=[\"sub_order_id\", \"variant_id\"],\n", "#     right_on=[\"sub_order_id\", \"variant_id\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df.loc[doorstep_return_invoice_variant_level_df['sub_order_id']==79008835]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df.loc[doorstep_order_df[\"sub_order_id\"] == 77291413]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "#     0\n", "# ] - doorstep_order_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df = doorstep_order_df.drop(\"reason_y\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df.rename(columns={\"reason_x\": \"reason\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df4[\n", "    doorstep_return_order_variant_level_df4[\"sub_order_id\"] == 92936400\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df = doorstep_return_order_variant_level_df4[\n", "    [\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"sub_order_type\",\n", "        \"sub_order_id\",\n", "        \"order_id\",\n", "        \"delivery_at\",\n", "        \"outlet_id\",\n", "        \"expectedreturndate\",\n", "        \"scheduleddate\",\n", "        \"billed_at\",\n", "        \"suborder_created_at\",\n", "        \"inward_date\",\n", "        \"item_id_x\",\n", "        \"variant_id\",\n", "        \"item_name_x\",\n", "        \"required_quantity\",\n", "        \"selling_price\",\n", "        \"returned_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.rename(\n", "    columns={\"item_name_x\": \"item_name\", \"item_id_x\": \"item_id\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.returned_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.required_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df[\"InwardValue\"] = (\n", "    doorstep_order_df[\"returned_quantity\"] * doorstep_order_df[\"selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df[\"ExpectedValue\"] = (\n", "    doorstep_order_df[\"required_quantity\"] * doorstep_order_df[\"selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df[\"InwardValue\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(doorstep_order_df.sub_order_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df[\"inward_date\"] = pd.to_datetime(doorstep_order_df[\"inward_date\"])\n", "doorstep_order_df[\"expectedreturndate\"] = pd.to_datetime(\n", "    doorstep_order_df[\"expectedreturndate\"]\n", ")\n", "doorstep_order_df[\"scheduleddate\"] = pd.to_datetime(doorstep_order_df[\"scheduleddate\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df[\"scheduleddate\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df[doorstep_order_df['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df[doorstep_order_df.expected_quantity==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df.loc[doorstep_order_df[\"sub_order_id\"] == 78835627]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# to check duplicacy\n", "# doorstep_order_df.groupby(['outlet_id','reason','sub_order_id','variant_id']).size().reset_index().sort_values(0,ascending=False)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["# Reschedule and Cancellation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "forward_invoice_query = \"\"\"\n", "select\n", "returndate as expectedreturndate,\n", "'Rescheduled/Cancellation' as l1_reason,\n", "reason,\n", "type as sub_order_type,\n", "outlet_id,\n", "suborder_created_at::timestamp,\n", "billed_at::timestamp,\n", "ScheduledDate::timestamp as scheduleddate,\n", "sub_order_id,\n", "order_id,\n", "variant_id,\n", "item_name,\n", "inwarded_at::timestamp,\n", "avg(required_quantity) required_quantity,\n", "avg(selling_price) selling_price ,\n", "avg(inwarded_quantity) inwarded_quantity\n", "from\n", "(\n", "select\n", "pi.returndate,\n", "pi.reason,\n", "pi.outlet_id,\n", "pi.type,\n", "pi.suborder_created_at,\n", "pi.pos_timestamp as billed_at,\n", "pi.ScheduledDate,\n", "pi.sub_order_id,\n", "pi.order_id,\n", "pi.variant_id,\n", "pi.item_name,\n", "max(pi.required_quantity) as required_quantity,\n", "max(pi.selling_price) as selling_price,\n", "max(pipd2.pos_timestamp)::timestamp as inwarded_at,\n", "sum(coalesce(pipd2.return_quantity,0)) as inwarded_quantity\n", "from \n", "(\n", "select\n", "(os.install_ts + interval '330 minute') as suborder_created_at,\n", "(os.update_ts + interval '330 minute') as returndate,\n", "os.type,\n", "os.current_status as reason,\n", "pi.outlet_id,\n", "pi.pos_timestamp,\n", "pi.grofers_order_id as sub_order_id,\n", "os.order_id as order_id,\n", "(timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as ScheduledDate,\n", "pipd.variant_id,\n", "pipd.name as item_name,\n", "AVG(pipd.selling_price) selling_price ,\n", "sum(pipd.quantity) as required_quantity\n", "from\n", "lake_oms_bifrost.oms_suborder os \n", "join \n", "( select grofers_order_id, max(pos_timestamp) max_pos_timestamp \n", "  from lake_pos.view_pos_invoice \n", "  where invoice_type_id=1 \n", "   and created_at::date >=%(run_period_start1)s  and created_at::date <=current_date group by 1) m on os.id=m.grofers_order_id\n", " join lake_pos.view_pos_invoice pi \n", "on os.id=pi.grofers_order_id and pi.invoice_type_id=1 and os.current_status in ('CANCELLED','REPROCURED') and os.type not like '%%Digital%%' and os.type not like '%%Drop%%'\n", "and (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date between %(run_period_start)s  and %(run_period_end)s\n", "and m.grofers_order_id=pi.grofers_order_id and m.max_pos_timestamp=pi.pos_timestamp\n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%' \n", "join lake_rpc.product_product rp on rp.variant_id=pipd.variant_id\n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", ") pi\n", "\n", "left join \n", "(select pi.invoice_id,pi.grofers_order_id, variant_id, pi.pos_timestamp::timestamp, sum(pipd.quantity) as return_quantity from\n", "lake_pos.view_pos_invoice pi \n", "join lake_pos.pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "\n", "left join lake_ims.grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "where\n", "greo.invoice_id is null\n", "group by 1,2,3,4) pipd2\n", "on pipd2.variant_id=pi.variant_id and pipd2.grofers_order_id=pi.sub_order_id\n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", ") c\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "\"\"\"\n", "\n", "forward_invoice_df = pd.read_sql_query(\n", "    sql=forward_invoice_query,\n", "    con=redshift_con,\n", "    params={\n", "        \"run_period_end\": run_period_end,\n", "        \"run_period_start\": run_period_start,\n", "        \"run_period_start1\": start1,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"sub_order_id\"] == \"93402679\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[[\"sub_order_type\", \"variant_id\"]].drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# forward_invoice_df.sort_values('sub_order_id')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"sub_order_id\"] == \"90782092\"][\n", "    \"inwarded_quantity\"\n", "].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["224332\n", "\n", "10670"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final=forward_invoice_df[forward_invoice_df['reason']=='REPROCURED']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final1=Final.groupby(['scheduleddate','billed_at','sub_order_id','outlet_id'], as_index=False).agg({'required_quantity':sum,\n", "#                                                                                              'inwarded_quantity':sum,\n", "#                                                                                             'inwarded_at':max\n", "#                                                                                             })"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final1.to_csv(\"Data_Reschedule.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"sub_order_id\"] == \"90991632\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Test=forward_invoice_df[[\"sub_order_id\", \"variant_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test['Key'] = Test['sub_order_id'].astype(str) +\"|\"+ Test['variant_id'].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df = forward_invoice_df.groupby(\n", "    [\n", "        \"expectedreturndate\",\n", "        \"sub_order_id\",\n", "        \"sub_order_type\",\n", "        \"variant_id\",\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"outlet_id\",\n", "        \"suborder_created_at\",\n", "        \"billed_at\",\n", "        \"scheduleddate\",\n", "        \"order_id\",\n", "        \"item_name\",\n", "    ],\n", "    as_index=False,\n", ").agg(\n", "    {\n", "        \"inwarded_at\": max,\n", "        \"required_quantity\": max,\n", "        \"selling_price\": max,\n", "        \"inwarded_quantity\": max,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["forward_invoice_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - forward_invoice_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df.groupby(\n", "#     [\"sub_order_id\", \"variant_id\", \"required_quantity\",\"inward_quantity\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# forward_invoice_df[\"diff\"] = (\n", "#     forward_invoice_df[\"inward_quantity\"] - forward_invoice_df[\"required_quantity\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# forward_invoice_df[forward_invoice_df[\"diff\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[\"inwardedvalue\"] = (\n", "    forward_invoice_df[\"inwarded_quantity\"] * forward_invoice_df[\"selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[\"expectedvalue\"] = (\n", "    forward_invoice_df[\"required_quantity\"] * forward_invoice_df[\"selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[\"expectedvalue\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"sub_order_id\"] == \"90840426\"].sort_values(\n", "    \"inwardedvalue\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df.dtypes"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["# Reverse Pick Up"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# redshift_con = pb.get_connection(\"redshift\")\n", "\n", "# reverse_pickup_query = \"\"\"\n", "# SELECT\n", "#   'INTERNAL REVERSE' as reason,\n", "#   oiom.id::text,\n", "#   oiom.install_ts as sub_order_created_at,\n", "#   internal_order_id,\n", "#   original_order_id,\n", "#   ooi.product_id::int,\n", "#   ooi.quantity,\n", "#   --ooi.selling_price,\n", "#   ooi.product_title,\n", "#   reason_id,\n", "#   o.type,\n", "#   o.direction,\n", "#   o.current_status,\n", "#   reason.id,\n", "#   message,\n", "#   code,\n", "#   is_active,\n", "#   source\n", "# FROM oms_internal_order_mapping oiom\n", "#   INNER JOIN  consumer.oms_order o ON oiom.internal_order_id = o.id\n", "#   INNER JOIN consumer.oms_reason reason ON oiom.reason_id = reason.id\n", "#   INNER JOIN consumer.oms_order_item ooi on ooi.order_id=o.id\n", "#   inner join consumer.oms_suborder os on os.order_id=oiom.original_order_id\n", "#   WHERE (timestamp 'epoch' + cast(o.slot_start as int) * interval '1 second' + interval '5.5 hour')::date between '2020-05-01' and '2020-05-31'\n", "#   AND\n", "#   o.type='InternalReverseOrder'\n", "#   and o.current_status='DELIVERED'\n", "#   group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17\n", "# \"\"\"\n", "\n", "# reverse_pickup_df = pd.read_sql_query(\n", "#     sql=reverse_pickup_query,\n", "#     con=redshift_con,\n", "#     params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", "# )\n", "# reverse_pickup_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_df.loc[reverse_pickup_df[\"internal_order_id\"] == 51968768]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df = pd.merge(\n", "#     reverse_pickup_df,\n", "#     it_pd_log_df,\n", "#     how=\"inner\",\n", "#     left_on=\"product_id\",\n", "#     right_on=\"product_id\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df[\"quantity_to_be_fetched\"] = (\n", "#     reverse_pickup_item_level_df[\"quantity\"]\n", "#     * reverse_pickup_item_level_df[\"multiplier\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df = reverse_pickup_item_level_df[\n", "#     [\n", "#         \"reason\",\n", "#         \"sub_order_created_at\",\n", "#         \"internal_order_id\",\n", "#         \"item_id\",\n", "#         \"original_order_id\",\n", "#         \"quantity_to_be_fetched\",\n", "#     ]\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df = reverse_pickup_item_level_df.drop_duplicates(keep=\"last\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df = reverse_pickup_item_level_df.groupby(\n", "#     [\n", "#         \"reason\",\n", "#         \"sub_order_created_at\",\n", "#         \"internal_order_id\",\n", "#         \"item_id\",\n", "#         \"original_order_id\",\n", "#     ],\n", "#     as_index=False,\n", "# ).agg({\"quantity_to_be_fetched\": \"sum\"})\n", "# # reverse_pickup_item_level_df.loc[\n", "# #     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# # ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df = pd.merge(\n", "#     reverse_pickup_item_level_df,\n", "#     rpc_product_product_df,\n", "#     how=\"inner\",\n", "#     left_on=\"item_id\",\n", "#     right_on=\"item_id\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# rpc_product_product_df.loc[rpc_product_product_df[\"item_id\"] == 10001077]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df = reverse_pickup_variant_level_df[\n", "#     [\n", "#         \"reason\",\n", "#         \"sub_order_created_at\",\n", "#         \"internal_order_id\",\n", "#         \"variant_id\",\n", "#         \"item_id\",\n", "#         \"original_order_id\",\n", "#         \"quantity_to_be_fetched\",\n", "#     ]\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.loc[\n", "#     reverse_pickup_variant_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.drop_duplicates(keep=\"last\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.drop_duplicates(keep=\"last\", inplace=True)\n", "# reverse_pickup_variant_level_df.loc[\n", "#     reverse_pickup_variant_level_df[\"internal_order_id\"] == 51968768\n", "# ].sort_values(\"variant_id\", ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# redshift_con = pb.get_connection(\"redshift\")\n", "\n", "# reverse_pickup_forward_invoice_query = \"\"\"\n", "# select\n", "# 'INTERNAL REVERSE' as reason ,\n", "# pi.outlet_id,\n", "# internal_order_id as sub_order_id,\n", "# rev.install_ts as billed_at,\n", "# pipd.variant_id,\n", "# pipd.name as item_name,\n", "# null as inwarded_at,\n", "# 0 as inward_quantity,\n", "# avg(pipd.selling_price) selling_price\n", "# from\n", "# (SELECT\n", "#   os.id as sub_order_id, internal_order_id, oiom.install_ts\n", "# FROM oms_internal_order_mapping oiom\n", "#   INNER JOIN  consumer.oms_order o ON oiom.internal_order_id = o.id\n", "#   inner join consumer.oms_suborder os on os.order_id=oiom.original_order_id\n", "#   WHERE oiom.install_ts>=%(run_period_start)s and oiom.install_ts<=%(run_period_end)s\n", "#   AND\n", "#   o.type='InternalReverseOrder'\n", "#   and o.current_status!='CANCELLED'\n", "#   --and internal_order_id= 49126100\n", "#   group by 1,2,3) rev\n", "#   join pos_pos_invoice pi on rev.sub_order_id=pi.grofers_order_id and pi.invoice_type_id=1\n", "#   join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and variant_id not like 'G%%'\n", "#   group by 1,2,3,4,5,6,7,8\n", "# \"\"\"\n", "\n", "# reverse_pickup_forward_invoice_df = pd.read_sql_query(\n", "#     sql=reverse_pickup_forward_invoice_query,\n", "#     con=redshift_con,\n", "#     params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", "# )\n", "# reverse_pickup_forward_invoice_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_suborder_variant_level_df = pd.merge(\n", "#     reverse_pickup_variant_level_df,\n", "#     reverse_pickup_forward_invoice_df,\n", "#     left_on=[\"internal_order_id\", \"variant_id\"],\n", "#     right_on=[\"sub_order_id\", \"variant_id\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_suborder_variant_level_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_suborder_variant_level_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_suborder_variant_level_df[\n", "#     [\"internal_order_id\", \"variant_id\"]\n", "# ].drop_duplicates().shape[0] - reverse_pickup_suborder_variant_level_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_suborder_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_orders_variant_level_df = reverse_pickup_suborder_variant_level_df[\n", "#     [\n", "#         \"reason_x\",\n", "#         \"outlet_id\",\n", "#         \"sub_order_created_at\",\n", "#         \"billed_at\",\n", "#         \"internal_order_id\",\n", "#         \"variant_id\",\n", "#         \"item_name\",\n", "#         \"quantity_to_be_fetched\",\n", "#         \"selling_price\",\n", "#         \"inwarded_at\",\n", "#         \"inward_quantity\",\n", "#     ]\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_orders_variant_level_df.rename(\n", "#     columns={\n", "#         \"sub_order_created_at\": \"suborder_created_at\",\n", "#         \"quantity_to_be_fetched\": \"required_quantity\",\n", "#         \"selling_price_x\": \"selling_price\",\n", "#         \"internal_order_id\": \"sub_order_id\",\n", "#         \"reason_x\": \"reason\",\n", "#     },\n", "#      inplace=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_orders_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_orders_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df1 = doorstep_order_df[\n", "    [\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"sub_order_type\",\n", "        \"sub_order_id\",\n", "        \"order_id\",\n", "        \"delivery_at\",\n", "        \"outlet_id\",\n", "        \"expectedreturndate\",\n", "        \"scheduleddate\",\n", "        \"billed_at\",\n", "        \"suborder_created_at\",\n", "        \"inward_date\",\n", "        \"item_id\",\n", "        \"variant_id\",\n", "        \"item_name\",\n", "        \"required_quantity\",\n", "        \"selling_price\",\n", "        \"returned_quantity\",\n", "        \"InwardValue\",\n", "        \"ExpectedValue\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df1.rename(\n", "    columns={\n", "        \"required_quantity\": \"expected_quantity\",\n", "        \"returned_quantity\": \"inward_quantity\",\n", "        \"InwardValue\": \"inwardedvalue\",\n", "        \"ExpectedValue\": \"expectedvalue\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df1 = forward_invoice_df[\n", "    [\n", "        \"expectedreturndate\",\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"sub_order_type\",\n", "        \"outlet_id\",\n", "        \"suborder_created_at\",\n", "        \"billed_at\",\n", "        \"scheduleddate\",\n", "        \"sub_order_id\",\n", "        \"order_id\",\n", "        \"variant_id\",\n", "        \"item_name\",\n", "        \"inwarded_at\",\n", "        \"required_quantity\",\n", "        \"selling_price\",\n", "        \"inwarded_quantity\",\n", "        \"inwardedvalue\",\n", "        \"expectedvalue\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df1.rename(\n", "    columns={\n", "        \"inwarded_at\": \"inward_date\",\n", "        \"required_quantity\": \"expected_quantity\",\n", "        \"inwarded_quantity\": \"inward_quantity\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df1.drop([\"delivery_at\", \"item_id\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df1 = doorstep_order_df1[\n", "    [\n", "        \"expectedreturndate\",\n", "        \"l1_reason\",\n", "        \"reason\",\n", "        \"sub_order_type\",\n", "        \"outlet_id\",\n", "        \"suborder_created_at\",\n", "        \"billed_at\",\n", "        \"scheduleddate\",\n", "        \"sub_order_id\",\n", "        \"order_id\",\n", "        \"variant_id\",\n", "        \"item_name\",\n", "        \"inward_date\",\n", "        \"expected_quantity\",\n", "        \"selling_price\",\n", "        \"inward_quantity\",\n", "        \"inwardedvalue\",\n", "        \"expectedvalue\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df = pd.concat(\n", "    [forward_invoice_df1, doorstep_order_df1], sort=False, axis=0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df.to_csv('./Development/ReturnExpectancyOrderItem_May01_02.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(return_orders_df.sub_order_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df[\"Key\"] = (\n", "    return_orders_df[\"sub_order_id\"].astype(str)\n", "    + \"|\"\n", "    + return_orders_df[\"variant_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"expectedreturndate\": \"Date of Return Generation\",\n", "    \"l1_reason\": \"l1 reason for return i.e. cancel , reprocure or doorstep\",\n", "    \"reason\": \"further bifurcation of doorstep into granular reasons\",\n", "    \"scheduleddate\": \"scheduled delivery date\",\n", "    \"inward_date\": \"date of inward/reverse billing\",\n", "    \"expected_quantity\": \"Quantity Expected to return\",\n", "    \"selling_price\": \"Price of item at billing\",\n", "    \"inward_quantity\": \"Quantity Inwarded out of Expected\",\n", "    \"inwardedvalue\": \"Amount Inwarded against expected\",\n", "    \"expectedvalue\": \"Amount Expected\",\n", "    \"key\": \"Unique key\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(return_orders_df)\n", "column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"expectedreturnsuniverse\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"Key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"scheduleddate\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"All expected suborders and items originating from Doorstep Returns ,Cancellations and Reschedules\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(return_orders_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}, "papermill": {"duration": 285.389574, "end_time": "2020-01-15T07:08:43.144152", "environment_variables": {}, "exception": true, "input_path": "/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets/notebook.ipynb", "output_path": "s3://grofers-prod-dse/airflow/notebooks/warehouse_returns_etl_returns_at_outlets_v1/scheduled__2020-01-15T06:00:00+00:00/output-2020-01-15.ipynb", "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets", "run_period_end": "2020-01-15", "run_period_start": "2019-12-16"}, "start_time": "2020-01-15T07:03:57.754578", "version": "1.0.0"}}, "nbformat": 4, "nbformat_minor": 4}