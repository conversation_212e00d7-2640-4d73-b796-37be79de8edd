{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import pytz\n", "import jinja2\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "Redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "today = date.today()\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = datetime.now(tz).replace(day=1).strftime(\"%Y-%m-%d\")\n", "start1 = (datetime.now(tz).replace(day=1) - <PERSON><PERSON><PERSON>(days=5)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if today.day <= 7:\n", "    start = (today - relativedelta(months=1)).replace(day=1).strftime(\"%Y-%m-%d\")\n", "    start1 = (datetime.strptime(start, \"%Y-%m-%d\") - timedelta(days=5)).strftime(\n", "        \"%Y-%m-%d\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start, end, start1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_period_start = start\n", "run_period_end = end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Universe = \"\"\"Select uni.sub_order_id,\n", "                     uni.l1_reason,\n", "                     uni.reason,\n", "                     uni.order_id,\n", "                     uni.scheduleddate,\n", "                     uni.variant_id,\n", "                     uni.billed_at,\n", "                     uni.inward_date,\n", "                     uni.expectedreturndate,\n", "                     rpp.item_id,\n", "                     uni.item_name,\n", "                     f.id as facility_id,\n", "                     f.name as facility_name,\n", "                     o.id as outlet_id,\n", "                     os.backend_merchant_id,\n", "                     os.slot_start,\n", "                     os.slot_end,\n", "                     os.slot_type,\n", "                     case when oo.station in ('NSD','') then oo.station\n", "                     when oo.station is null then 'Null'\n", "                     else upper(substring(oo.station,8,length(oo.station)-7)) end as station,\n", "                     om.city_name as city,\n", "                     extract(month from scheduleddate) as ScheduledMonth,\n", "                     extract(year from scheduleddate) as ScheduledYear,\n", "                     sum(expected_quantity) as expected_quantity,\n", "                     sum(inward_quantity) as inward_quantity,\n", "                     avg(selling_price) as selling_price,\n", "                     sum(expectedvalue) as ExpectedValue,\n", "                     sum(inwardedvalue) as InwardValue\n", "from metrics.expectedreturnsuniverse uni left join  lake_rpc.product_product rpp on rpp.variant_id=uni.variant_id\n", "left join lake_oms_bifrost.oms_suborder os on os.id = uni.sub_order_id\n", "left join lake_oms_bifrost.oms_merchant m on os.backend_merchant_id = m.id \n", "left join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store and cms.active = 1 and cms_update_active = 1\n", "left join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "left join lake_oms_bifrost.oms_order oo on oo.id=uni.order_id\n", "left join lake_oms_bifrost.oms_merchant om on om.id=oo.merchant_id\n", "where uni.scheduleddate between %(Start)s and %(End)s\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData = pd.read_sql_query(\n", "    sql=Universe,\n", "    con=Redshift,\n", "    params={\"Start\": run_period_start, \"End\": run_period_end},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"sub_order_id\"] = UniverseData[\"sub_order_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"city\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"expectedvalue\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"inwardvalue\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WithOrderId = \"\"\"\n", "select 'with_oid' as  type, \n", "rtoi.order_id, \n", "rtoi.item_id,\n", "listagg( distinct rtsl.return_task_id,',') as return_task_id,\n", "max(rt.created_at) as created_at,\n", "max(rt.received_at) as received_at,\n", "max(comp.completed_at) as completed_at,\n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity\n", "from lake_warehouse_location.return_task_state_log rtsl\n", "join lake_warehouse_location.return_task rt on rt.id=rtsl.return_task_id\n", "join lake_warehouse_location.return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join lake_ims.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join lake_pos.view_pos_invoice pos on pos.grofers_order_id= rtoi.order_id and invoice_type_id=1\n", "left join \n", "(select rt1.id,convert_timezone('Asia/Kolkata',rtsl2.created_at) as completed_at\n", "from lake_warehouse_location.return_task rt1\n", "join lake_warehouse_location.return_task_state_log rtsl2 on rt1.id=rtsl2.return_task_id and rtsl2.state=7\n", "where convert_timezone('Asia/Kolkata',rt1.created_at)>=%(Start)s\n", ") comp on comp.id = rt.id\n", "where rtsl.state=1 \n", "and convert_timezone('Asia/Kolkata',rt.created_at)>=%(Start)s \n", "and len(trim(rtoi.order_id))>5 and rtoi.order_id is not null and rtoi.order_id<>'' and rtoi.order_id <> 'None' and rtoi.order_id<>' '\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["This_Month_Returns_Data = pd.read_sql_query(\n", "    sql=WithOrderId, con=Redshift, params={\"Start\": start1}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["This_Month_Returns_Data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["This_Month_Returns_Data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["This_Month_Returns_Data.order_id = This_Month_Returns_Data.order_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData = pd.merge(\n", "    UniverseData,\n", "    This_Month_Returns_Data,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"item_id\"],\n", "    right_on=[\"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"WHTimeDiff_inHour\"] = UniverseData.apply(\n", "    lambda x: (x[\"completed_at\"] - x[\"received_at\"]).to_seconds() / 3600\n", "    if (x[\"completed_at\"] is not np.NaN) & (x[\"received_at\"] is not np.NaN)\n", "    else np.<PERSON>,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def WithinTimeCalc24(TimeDiff):\n", "    if TimeDiff <= 24:\n", "        return 1\n", "    elif np.<PERSON>(TimeDiff):\n", "        return None\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData[\"WithinTime\"] = list(\n", "    map(WithinTimeCalc24, UniverseData[\"WHTimeDiff_inHour\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.rename(\n", "    columns={\n", "        \"type\": \"WH_type\",\n", "        \"order_id_y\": \"WH_SuborderID\",\n", "        \"return_task_id\": \"WHReturnTaskID\",\n", "        \"created_at\": \"WHTaskCreatedAt\",\n", "        \"received_at\": \"WHTaskReceivedAt\",\n", "        \"completed_at\": \"WHTaskCompletedAt\",\n", "        \"expected_quantity_x\": \"expected_quantity\",\n", "        \"expected_quantity_y\": \"WH_Task_expected_quantity\",\n", "        \"map_quantity\": \"WH_task_Map_Quantity\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query2 = \"\"\"\n", "# With batch_details as\n", "# (  Select distinct loc.loc_name as source,\n", "#         locd.external_loc_id as destination_dp,\n", "#           order_batch_id,\n", "#           schedule_date,\n", "#           sub_order_id,\n", "#         order_id\n", "#     from\n", "# (  select distinct (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date as schedule_date,\n", "# os.id as sub_order_id,\n", "# os.order_id,\n", "# b.id as order_batch_id,\n", "# b.location_loc_id,\n", "# b.next_destination_loc_id ,\n", "# b.completion_time,\n", "# b.created_date\n", "# from metrics.expectedreturnsuniverse u\n", "# join lake_oms_bifrost.oms_suborder os on u.sub_order_id = os.id\n", "# join lake_logex.sub_order so on  u.sub_order_id = so.sub_order_id\n", "# left join lake_logex.batch b on so.external_criteria = b.external_criteria\n", "# where  (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date between %(Start)s and %(End)s\n", "# ) b\n", "# left join\n", "#     (\n", "#         select external_loc_id, loc_name\n", "#         from lake_logex.location_db\n", "#         group by 1,2\n", "#     ) loc on loc.external_loc_id = b.location_loc_id\n", "#  left join\n", "#     (\n", "#         select external_loc_id, loc_name\n", "#         from lake_logex.location_db\n", "#         group by 1,2\n", "#     ) locd on locd.external_loc_id = b.next_destination_loc_id\n", "#         where b.completion_time > b.created_date\n", "# ),\n", "# batch_handover_activity AS\n", "# (\n", "#     select\n", "#         b.id as batch_id,\n", "#         max(expected_end_time + interval '5.5 hour') as expected_handover_end_time,\n", "#         max(end_time + interval '5.5 hour') as actual_handover_end_time\n", "#     from lake_logex.activity  a\n", "#     inner join lake_logex.batch  b on a.batch_id = b.id\n", "#     where a.activity_type = 'HANDOVER'\n", "#     group by 1\n", "# ),\n", "# batch_dispatch_activity AS\n", "# (\n", "#     select\n", "#         b.id as batch_id,\n", "#         max(a.expected_end_time + interval '5.5 hour') as expected_dispatch_end_time,\n", "#         max(end_time + interval '5.5 hour') as dispatch_time\n", "#     from lake_logex.activity a\n", "#     inner join lake_logex.batch  b on a.batch_id = b.id\n", "#     where a.activity_type = 'DISPATCH'\n", "#     group by 1\n", "# ) ,\n", "# dispatch_order as (\n", "# select en_ref_order_id ,\n", "# case when status = 'REMOVED' and  row_num = 1 then 'NOT_DISPATCH'\n", "# when status = 'PROCESSED' and  row_num = 1 then 'CANCELLED_BUT_DISPATCH'\n", "# when row_num = 2 then 'CANCELLED_PARTIAL_DISPATCH' end as DISPATCH_FLAG\n", "# from (\n", "# select a.*, count (en_ref_order_id) as row_num\n", "# from (\n", "# select\n", "# distinct en_ref_order_id, ca.status\n", "# from lake_logex.container_activity ca\n", "# join metrics.expectedreturnsuniverse u on u.sub_order_id = ca.en_ref_order_id\n", "# where ca.attempt_to_remove = 'TRUE'\n", "# and remove_reason = 'CANCELLED'\n", "# and ca.status in ('REMOVED', 'PROCESSED')\n", "# group by 1,2\n", "# ) a group by 1,2\n", "# ) group by 1,2\n", "# )\n", "# select distinct sub_order_id as order_id , dispatch_time , flag_dispatch\n", "# from (\n", "# select sub_order_id ,order_batch_id, dispatch_time , DISPATCH_FLAG , reached_dp_time ,\n", "#         case when reached_dp_time is null and dispatch_time is null then 'NOT_DISPATCH'\n", "#         when DISPATCH_FLAG is not null then DISPATCH_FLAG\n", "#         when DISPATCH_FLAG is null and dispatch_time is not null then 'DISPATCHED'\n", "#         when reached_dp_time is not null and dispatch_time is null then 'DISPATCHED_WITHOUT_FM' end as flag_dispatch\n", "#         from (\n", "# Select batch_details.*,\n", "#        expected_dispatch_end_time,\n", "#        dispatch_time,\n", "#        (actual_event_time+ interval '5.5 hour') as reached_dp_time,\n", "#        DISPATCH_FLAG\n", "# from batch_details\n", "# left join batch_dispatch_activity on batch_details.order_batch_id = batch_dispatch_activity.batch_id\n", "# left join dispatch_order on batch_details.sub_order_id = dispatch_order.en_ref_order_id\n", "# left join lake_oms_bifrost.oms_suborder os on os.id = batch_details.sub_order_id\n", "# left join lake_last_mile.order o on o.external_order_id = os.order_id\n", "# left join (select *\n", "#                     from lake_last_mile.order_action where (actual_event_time+ interval '5.5 hour')::date between %(Start)s and %(End)s\n", "#                                             and action_type = 'SECONDMILE_PICKED'\n", "#                     ) oa on oa.order_id = o.id and (oa.actual_event_time+ interval '5.5 hour')::date = batch_details.schedule_date\n", "#                                     group by 1,2,3,4,5,6,7,8,9,10\n", "#                                     ) a\n", "#                                     ) a\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query2 = \"\"\"with dispatch as\n", "(with suborder as(\n", "select (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date as sch_date,\n", "        loc.loc_name as source,\n", "    so.sub_order_id as sub_order_id,\n", "    os.order_id as order_id,\n", "         case when ca.status = 'PROCESSED' then 'DISPATCHED' \n", "         when ca.status = 'REMOVED' and remove_reason = 'BATCH_CHANGED' then 'DISPATCHED'\n", "         else 'NOT_DISPATCHED' end as flag\n", "from lake_logex.sub_order so \n", "join lake_oms_bifrost.oms_suborder os on so.sub_order_id = os.id \n", "                                and (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour') :: date between %(Start)s and %(End)s\n", "join lake_logex.container_activity ca on ca.en_ref_order_id = so.sub_order_id \n", "left join lake_logex.batch b on so.external_criteria = b.external_criteria\n", "left join \n", "    ( \n", "        select external_loc_id, loc_name\n", "        from lake_logex.location_db\n", "        group by 1,2\n", "    ) loc on loc.external_loc_id = b.location_loc_id\n", " left join \n", "    ( \n", "        select external_loc_id, loc_name\n", "        from lake_logex.location_db\n", "        group by 1,2\n", "    ) locd on locd.external_loc_id = b.next_destination_loc_id\n", "    \n", "    where ca.activity_type = 'DISPATCH'\n", "    -- and remove_reason <> 'BATCH_CHANGED' and ca.status <> 'REMOVED'\n", "group by 1,2,3,4,5\n", ")\n", "\n", "select sch_date,\n", "        source,\n", "        order_id,\n", "        sub_order_id,\n", "        case when rows = 1 and flag = 'DISPATCHED' then 'DISPATCHED'\n", "            when rows = 1 and flag = 'NOT_DISPATCHED' then 'NOT_DISPATCHED' \n", "            else 'PARTIAL_DISPATCH' end as dispatch_flag\n", "            from (\n", "select so.*, count(sub_order_id) over (partition by sub_order_id order by flag rows between unbounded PRECEDING and unbounded FOLLOWING) as rows\n", "from suborder so\n", "-- where source <> 'SS Mumbai M2 (LA)'\n", "-- and sub_order_id = 99327848\n", "group by 1,2,3,4,5\n", ") a \n", "-- where source = 'SS Warehouse G1'\n", "group by 1,2,3,4,5),\n", "\n", "base as (\n", "select distinct scheduleddate::date as scheduleddate, sub_order_id , order_id\n", "from metrics.expectedreturnsuniverse\n", "where scheduleddate::date between %(Start)s and %(End)s\n", "-- and facility_name = 'Super Store Gurgaon G1 - Warehouse'\n", ")\n", "\n", "select distinct   sub_order_id as order_id,\n", "                   scheduleddate as dispatch_time,\n", "        case when dispatch_flag is not null then dispatch_flag else 'NOT DISPATCHED' end as flag_dispatch\n", "        from (\n", "select scheduleddate , b.sub_order_id , b.order_id, d.dispatch_flag\n", "from base b\n", "left join dispatch d on b.sub_order_id = d.sub_order_id\n", ") a \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData = pd.read_sql_query(\n", "    sql=Query2, con=Redshift, params={\"Start\": run_period_start, \"End\": run_period_end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData = DispatchData[\n", "    [\"order_id\", \"dispatch_time\", \"flag_dispatch\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData[\"order_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DispatchData[\"order_id\"] = DispatchData[\"order_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData = pd.merge(\n", "    UniverseData, DispatchData, how=\"left\", left_on=\"sub_order_id\", right_on=\"order_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.rename(columns={\"order_id\": \"Dispatch_suborderid\"}, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LM Task Integration \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["it_pd_log_query = \"\"\"select  \n", "x.product_id::int, item_id::int ,multiplier  \n", "from \n", "consumer.it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.it_pd_log i \n", "--join order_product_details opd on opd.product_id=i.product_id\n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\"\"\"\n", "\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=Redshift)\n", "it_pd_log_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"with expected_rto as\n", "(\n", "select * from (\n", "select (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as schedule_date,\n", "    a.id,a.order_item_id,a.sub_order_id,a.quantity,a.value,oi.product_id, roi.return_flow_order_id from\n", "(\n", "select * from (\n", "    select id,order_item_id,sub_order_id,quantity,value, max(id) over (partition by order_item_id,sub_order_id) as id_max\n", "    from (\n", "    select\n", "    roi.id,\n", "    order_item_id,\n", "    roi.sub_order_id,\n", "    sum(roi.quantity) quantity,\n", "    sum(roi.quantity*oi.price_per_unit) as value\n", "    from lake_last_mile.return_flow_order_item roi\n", "    inner join lake_last_mile.order_item oi on oi.id = roi.order_item_id\n", "    join lake_last_mile.return_flow_order ro on ro.id = roi.return_flow_order_id\n", "    where roi.sub_order_id in (select distinct sub_order_id from metrics.expectedreturnsuniverse\n", "                                                    where scheduleddate between %(Start)s and %(End)s)\n", "    and ro.parent_return_flow_order_id is null\n", "    group by 1,2,3)) where id = id_max and quantity > 0\n", "    ) a\n", "    join lake_last_mile.return_flow_order_item roi on roi.id = a.id \n", "    join lake_oms_bifrost.oms_suborder os on os.id = a.sub_order_id\n", "    join lake_last_mile.order_item oi on oi.id = a.order_item_id\n", "    )\n", "    ),\n", "expected_this_month as\n", "(\n", "    select id,\n", "         return_flow_task_id,return_flow_order_cause\n", "    from lake_last_mile.return_flow_order where parent_return_flow_order_id is null\n", "    group by 1,2,3\n", ")\n", "-- select sum(value) from (\n", "select schedule_date,c.created_at, city, station,\n", "case when returning_entity_type = 'DP' then dp.name else fe.name end as Name,\n", "case when returning_entity_type = 'DP' then fe2.employee_id else fe.employee_id end as Employee_id,\n", "returning_entity_type as returning_entity, c.sub_order_id,c.return_flow_order_cause,product_id,\n", "Case when lower(fep.name) like 'mic%%' then 'Micelio Mobility Pvt Ltd'\n", "when lower(fep.name) like 'dep%%' then 'Dependo Logistics Solutions Pvt. Ltd'\n", "when lower(fep.name) like 'sit%%' then 'SITICS'\n", "when lower(fep.name) like 'ps con%%' then 'PS consultancy'\n", "when lower(fep.name) like 'sha%%' then 'Shadow fax'\n", "when lower(fep.name) like 'cre%%' then 'CABT'\n", "when lower(fep.name) like 'zom%%' then '<PERSON>oma<PERSON>'\n", "when lower(fep.name) like 'gre%%' then 'GREEN LEGENDS MOTOCORP PVT LTD' else 'Grofers' end as delivery_partner,\n", "sum(quantity) as quantity, \n", "sum(value) as value \n", "from (\n", "select er.schedule_date, er.order_item_id order_item_id,er.sub_order_id, er.product_id,\n", "        quantity, value,em.return_flow_order_cause,\n", "        c.id as task_id, c.collecting_entity_id,c.returning_entity_id,c.created_at, collecting_entity_type, returning_entity_type,\n", "        s.name as station, city.name as city\n", "from expected_rto er\n", "join expected_this_month em on er.return_flow_order_id = em.id\n", "join lake_last_mile.return_flow_task c on em.return_flow_task_id = c.id\n", "left join lake_last_mile.station s on s.id = c.station_id\n", "left join lake_last_mile.city city on city.id = s.city_id\n", ") c\n", "left join lake_last_mile.field_executive fe on fe.id = c.returning_entity_id and c.collecting_entity_type = 'DP'\n", "left join lake_last_mile.distribution_point dp on dp.id = c.returning_entity_id and c.returning_entity_type = 'DP'\n", "left join lake_last_mile.field_executive fe2 on fe2.id = dp.primary_fe_id and c.returning_entity_type = 'DP'\n", "left join lake_last_mile.field_executive_payroll fep on fep.id = fe.field_executive_payroll_id \n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Task_Data = pd.read_sql_query(\n", "    sql=Query, con=Redshift, params={\"Start\": run_period_start, \"End\": run_period_end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Task_Data[\"product_id\"] = LM_Task_Data[\"product_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Task_Data1 = pd.merge(LM_Task_Data, it_pd_log_df, how=\"left\", on=\"product_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Task_Data1[\"quantity_actual\"] = (\n", "    LM_Task_Data1[\"quantity\"] * LM_Task_Data1[\"multiplier\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM_Task_Data1 = LM_Task_Data1.groupby(\n", "    [\n", "        \"name\",\n", "        \"employee_id\",\n", "        \"returning_entity\",\n", "        \"item_id\",\n", "        \"sub_order_id\",\n", "        \"created_at\",\n", "        \"delivery_partner\",\n", "        \"return_flow_order_cause\",\n", "    ],\n", "    as_index=False,\n", ").agg({\"quantity_actual\": sum})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData = pd.merge(\n", "    UniverseData, LM_Task_Data1, how=\"left\", on=[\"sub_order_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.rename(\n", "    columns={\n", "        \"quantity_actual\": \"LM_Quantity_Actual\",\n", "        \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "        \"created_at\": \"LM_Created_At\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ledger "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ledger_Query = \"\"\"with expected_rto as\n", "(\n", "select * from\n", "(\n", "    select (roi.created_at + interval '5.5 hour')::date as Date,\n", "    roi.id,\n", "    return_flow_order_id,\n", "    order_item_id,\n", "    roi.sub_order_id,\n", "    case when ro.parent_return_flow_order_id is null then 'EXPECTED' else 'RETURN' end as expected_flag,\n", "    oi.price_per_unit,\n", "    sum(roi.quantity) as quantity\n", "    from lake_last_mile.return_flow_order_item roi\n", "    left join lake_last_mile.return_flow_order ro on roi.return_flow_order_id = ro.id\n", "    inner join lake_last_mile.order_item oi on oi.id = roi.order_item_id\n", "    where roi.sub_order_id in (select distinct sub_order_id from metrics.expectedreturnsuniverse \n", "                                        where scheduleddate between %(Start)s and %(End)s\n", "                                                )\n", "    group by 1,2,3,4,5,6,7) a\n", "    where quantity > 0\n", "    ),\n", "\n", "ledger as\n", "    ( select distinct pendency_type_id, entity_type,\n", "            case when return_flow_settlement_type = 'FORGIVE' then pendency_clearance_reason end as pendency_clearance_reason,\n", "            sum(case when return_flow_settlement_type = 'APP_HANDOVER' then debit_balance end ) as APP_HANDOVER_qnty,\n", "            sum(case when return_flow_settlement_type = 'APP_HANDOVER' then debit_balance*er.price_per_unit end ) as APP_HANDOVER_value,\n", "            sum(case when return_flow_settlement_type = 'FORGIVE' then debit_balance end ) as FORGIVE_qnty,\n", "            sum(case when return_flow_settlement_type = 'FORGIVE' then debit_balance*er.price_per_unit end ) as FORGIVE_value,\n", "            sum(case when return_flow_settlement_type = 'SALARY_SETTLED' then debit_balance end ) as SALARY_SETTLED_qnty,\n", "            sum(case when return_flow_settlement_type = 'SALARY_SETTLED' then debit_balance*er.price_per_unit end ) as SALARY_SETTLED_value\n", "    from lake_last_mile.return_flow_ledger l\n", "    join expected_rto er on er.id = l.pendency_type_id\n", "    where return_flow_pendency_type = 'ITEM' \n", "    group by 1,2,3\n", "    )\n", "\n", "select sub_order_id , \n", "        max(case when entity_type in ('GSP','FE') then pendency_clearance_reason end) as gsp_forgiven_reason,\n", "        sum(case when entity_type in ('GSP','FE') then FORGIVE_value end) as gsp_forgiven,\n", "        sum(case when entity_type in ('GSP','FE') then SALARY_SETTLED_value  end) as gsp_salary_settled,\n", "        max(case when entity_type in ('DP') then pendency_clearance_reason end) as dp_forgiven_reason,\n", "        sum(case when entity_type in ('DP') then FORGIVE_value end) as dp_forgiven,\n", "        sum(case when entity_type in ('DP') then SALARY_SETTLED_value  end) as dsp_salary_settled\n", "from (\n", "select distinct sub_order_id, entity_type,expected_flag,pendency_clearance_reason,\n", "        sum(APP_HANDOVER_qnty) APP_HANDOVER_qnty, sum(APP_HANDOVER_value) APP_HANDOVER_value,\n", "        sum(FORGIVE_qnty) FORGIVE_qnty, sum(FORGIVE_value) FORGIVE_value,\n", "        sum(SALARY_SETTLED_qnty) SALARY_SETTLED_qnty, sum(SALARY_SETTLED_value) SALARY_SETTLED_value\n", "        from (\n", "            select sub_order_id, order_item_id ,entity_type,expected_flag,pendency_clearance_reason,\n", "                     APP_HANDOVER_qnty, APP_HANDOVER_value, FORGIVE_qnty, FORGIVE_value,\n", "                     SALARY_SETTLED_qnty, SALARY_SETTLED_value, max(id) from (\n", "            select distinct er.id, er.sub_order_id, er.order_item_id ,entity_type,expected_flag,pendency_clearance_reason,\n", "                     APP_HANDOVER_qnty, APP_HANDOVER_value, FORGIVE_qnty, FORGIVE_value,\n", "                     SALARY_SETTLED_qnty, SALARY_SETTLED_value\n", "                    from expected_rto er \n", "                    join ledger l on er.id = l.pendency_type_id\n", "                    group by 1,2,3,4,5,6,7,8,9,10,11,12\n", "                    )a \n", "                    where FORGIVE_value is not null or SALARY_SETTLED_value is not null\n", "                    group by 1,2,3,4,5,6,7,8,9,10,11) a\n", "                    group by 1,2,3,4\n", ") group by 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Ledger = pd.read_sql_query(\n", "    sql=Ledger_Query,\n", "    con=Redshift,\n", "    params={\"Start\": run_period_start, \"End\": run_period_end},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData = pd.merge(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, how=\"left\", on=[\"sub_order_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseData.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GSP to DP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_dp = \"\"\"with expected_rto as\n", "(\n", "select * from (\n", "select (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as schedule_date,\n", "    a.id,a.order_item_id,a.sub_order_id,a.quantity,a.price_per_unit,oi.product_id, roi.return_flow_order_id from\n", "(\n", "select * from (\n", "    select id,order_item_id,sub_order_id,quantity,price_per_unit, max(id) over (partition by order_item_id,sub_order_id) as id_max\n", "    from (\n", "    select\n", "    roi.id,\n", "    order_item_id,\n", "    roi.sub_order_id,\n", "    sum(roi.quantity) quantity,\n", "    sum(oi.price_per_unit) as price_per_unit\n", "    from lake_last_mile.return_flow_order_item roi\n", "    inner join lake_last_mile.order_item oi on oi.id = roi.order_item_id\n", "    join lake_last_mile.return_flow_order ro on ro.id = roi.return_flow_order_id\n", "    where roi.sub_order_id in (select distinct sub_order_id from metrics.expectedreturnsuniverse \n", "                                                where scheduleddate between %(Start)s and %(End)s)\n", "    and ro.parent_return_flow_order_id is null\n", "    group by 1,2,3)) where id = id_max and quantity > 0\n", "    ) a\n", "    join lake_last_mile.return_flow_order_item roi on roi.id = a.id \n", "    join lake_oms_bifrost.oms_suborder os on os.id = a.sub_order_id\n", "    join lake_last_mile.order_item oi on oi.id = a.order_item_id\n", "    )\n", "    ),\n", "expected_this_month as\n", "(\n", "    select id,\n", "         return_flow_task_id\n", "    from lake_last_mile.return_flow_order where parent_return_flow_order_id is null\n", "    group by 1,2\n", "),\n", "complete_task as\n", "(\n", "    select id, collecting_entity_id,returning_entity_id, return_flow_task_state,station_id,\n", "    (completed_at + interval '5.5 hour') as completed_date\n", "    from lake_last_mile.return_flow_task where collecting_entity_type = 'DP'\n", "            and return_flow_task_state = 'COMPLETED'\n", ")\n", "-- select sum(value) from (\n", "select schedule_date,completed_date, station, city,sub_order_id,product_id,\n", "fe.name as returning_entity_name, fe.employee_id as returning_entity_employee_id,\n", "dp.name as collecting_entity_name , fe2.employee_id as collecting_entity_employee_id,\n", "Case when lower(fep.name) like 'mic%%' then 'Micelio Mobility Pvt Ltd'\n", "when lower(fep.name) like 'dep%%' then 'Dependo Logistics Solutions Pvt. Ltd'\n", "when lower(fep.name) like 'sit%%' then 'SITICS'\n", "when lower(fep.name) like 'ps con%%' then 'PS consultancy'\n", "when lower(fep.name) like 'sha%%' then 'Shadow fax'\n", "when lower(fep.name) like 'cre%%' then 'CABT'\n", "when lower(fep.name) like 'zom%%' then '<PERSON>oma<PERSON>' \n", "when lower(fep.name) like 'gre%%' then 'GREEN LEGENDS MOTOCORP PVT LTD'else 'Grofers' end as delivery_partner,\n", "sum(collected_qty) quantity,sum(value) value \n", "-- count(distinct task_id)\n", "from (\n", "select er.schedule_date,er.sub_order_id,\n", "er.id as return_flow_item_id,er.order_item_id, rf.collected_qty, (rf.collected_qty*er.price_per_unit) as value,er.product_id,\n", "er.return_flow_order_id as return_flow_order_id,c.completed_date,\n", "c.id as task_id, c.collecting_entity_id,c.returning_entity_id, c.return_flow_task_state,\n", "         s.name as station, city.name as city\n", "from expected_rto er\n", "join expected_this_month em on er.return_flow_order_id = em.id\n", "join metrics.lm_return_flow_task_completed_flat rf on rf.return_flow_order_item_id = er.id\n", "join complete_task c on em.return_flow_task_id = c.id\n", "left join lake_last_mile.station s on s.id = c.station_id\n", "left join lake_last_mile.city city on city.id = s.city_id\n", "where rf.collected_qty > 0\n", ") c\n", "left join lake_last_mile.field_executive fe on fe.id = c.returning_entity_id\n", "left join lake_last_mile.distribution_point dp on dp.id = c.collecting_entity_id\n", "left join lake_last_mile.field_executive fe2 on fe2.id = dp.primary_fe_id\n", "left join lake_last_mile.field_executive_payroll fep on fep.id = fe.field_executive_payroll_id \n", "group by 1,2,3,4,5,6,7,8,9,10,11\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_dp_task = pd.read_sql_query(\n", "    sql=gsp_dp, con=Redshift, params={\"Start\": run_period_start, \"End\": run_period_end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_dp_task[\"product_id\"] = gsp_dp_task[\"product_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_dp_task1 = pd.merge(gsp_dp_task, it_pd_log_df, how=\"left\", on=\"product_id\")\n", "gsp_dp_task1[\"quantity_actual\"] = gsp_dp_task1[\"quantity\"] * gsp_dp_task1[\"multiplier\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gsp_dp_task1 = gsp_dp_task1.groupby(\n", "    [\n", "        \"completed_date\",\n", "        \"returning_entity_name\",\n", "        \"returning_entity_employee_id\",\n", "        \"collecting_entity_name\",\n", "        \"collecting_entity_employee_id\",\n", "        \"delivery_partner\",\n", "        \"item_id\",\n", "        \"sub_order_id\",\n", "    ],\n", "    as_index=False,\n", ").agg({\"quantity_actual\": sum})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMerged = pd.merge(\n", "    UniverseData, gsp_dp_task1, how=\"left\", on=[\"sub_order_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMerged[\"expected_value_GSP_DP\"] = (\n", "    UniverseDataMerged[\"quantity_actual\"] * UniverseDataMerged[\"selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMerged.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMerged.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMerged.rename(\n", "    columns={\n", "        \"completed_date\": \"GSP_DP_completed_date\",\n", "        \"returning_entity_name\": \"GSP_DP_returning_entity_name\",\n", "        \"returning_entity_employee_id\": \"GSP_DP_returning_entity_employee_id\",\n", "        \"collecting_entity_name\": \"GSP_DP_collecting_entity_name\",\n", "        \"collecting_entity_employee_id\": \"GSP_DP_collecting_entity_employee_id\",\n", "        \"quantity_actual\": \"GSP_DP_quantity_actual\",\n", "        \"delivery_partner_x\": \"delivery_partner_LM\",\n", "        \"delivery_partner_y\": \"delivery_partner_GSP_DP\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# UniverseDataMerged['GSP_DP_completed_date']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DP HFE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe = \"\"\"with base as (\n", "with expected_rto as\n", "(\n", "select * from (\n", "select (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as schedule_date,\n", "    a.id,a.order_item_id,a.sub_order_id,a.quantity,a.price_per_unit,oi.product_id, roi.return_flow_order_id from\n", "(\n", "select * from (\n", "    select id,order_item_id,sub_order_id,quantity,price_per_unit, max(id) over (partition by order_item_id,sub_order_id) as id_max\n", "    from (\n", "    -- (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as schedule_date,\n", "    select\n", "    roi.id,\n", "    order_item_id,\n", "    roi.sub_order_id,\n", "    sum(roi.quantity) quantity,\n", "    sum(oi.price_per_unit) as price_per_unit\n", "    from lake_last_mile.return_flow_order_item roi\n", "    inner join lake_last_mile.order_item oi on oi.id = roi.order_item_id\n", "    join lake_last_mile.return_flow_order ro on ro.id = roi.return_flow_order_id\n", "    where roi.sub_order_id in (select distinct sub_order_id from metrics.expectedreturnsuniverse \n", "                                                             where scheduleddate between %(Start)s and %(End)s)\n", "    -- and roi.sub_order_id = 93313187\n", "    and ro.parent_return_flow_order_id is null\n", "    group by 1,2,3)) where id = id_max and quantity > 0\n", "    ) a\n", "    join lake_last_mile.return_flow_order_item roi on roi.id = a.id \n", "    join lake_oms_bifrost.oms_suborder os on os.id = a.sub_order_id\n", "    join lake_last_mile.order_item oi on oi.id = a.order_item_id\n", "    )\n", "    ),\n", "expected_this_month as\n", "(\n", "    select id,\n", "         return_flow_task_id\n", "    from lake_last_mile.return_flow_order where parent_return_flow_order_id is null\n", "    -- and extract(month from (created_at + interval '5.5 hour')::date ) = extract(month from current_date - 1)\n", "    -- and (created_at + interval '5.5 hour')::date <= current_date - 1\n", "    group by 1,2\n", ")\n", "-- select * from (\n", "select er.schedule_date,er.id as return_flow_item_id, em.id as return_flow_order_id,sub_order_id,order_item_id,product_id,\n", "        c.id as task_id, c.child_return_flow_task_id,\n", "        c.collecting_entity_id,c.returning_entity_id, collecting_entity_type, returning_entity_type\n", "        -- s.name as station, city.name as city\n", "from expected_rto er\n", "join expected_this_month em on er.return_flow_order_id = em.id\n", "join lake_last_mile.return_flow_task c on em.return_flow_task_id = c.id\n", "-- ) where returning_entity_type in ('GSP','FE')\n", "),\n", "expected_this_month1 as\n", "(\n", "    select id,parent_return_flow_order_id,return_flow_task_id\n", "    from lake_last_mile.return_flow_order ro\n", "    group by 1,2,3\n", "),\n", "expected_rto1 as\n", "( select * from (\n", "select (timestamp 'epoch' + cast(os.slot_start as int) * interval '1 second' + interval '5.5 hour')::date as schedule_date,\n", "    a.id,a.order_item_id,a.sub_order_id,a.quantity,a.price_per_unit,a.product_id, a.return_flow_order_id from\n", "(\n", "    select\n", "    roi.id,\n", "    order_item_id,\n", "    roi.sub_order_id,\n", "    roi.return_flow_order_id,\n", "    oi.product_id,\n", "    sum(roi.quantity) quantity,\n", "    sum(oi.price_per_unit) as price_per_unit\n", "    from lake_last_mile.return_flow_order_item roi\n", "    inner join lake_last_mile.order_item oi on oi.id = roi.order_item_id\n", "    join lake_last_mile.return_flow_order ro on ro.id = roi.return_flow_order_id\n", "    where roi.sub_order_id in (select distinct sub_order_id from metrics.expectedreturnsuniverse \n", "                                                 where scheduleddate between %(Start)s and %(End)s )\n", "    group by 1,2,3,4,5\n", "    ) a\n", "    join lake_oms_bifrost.oms_suborder os on os.id = a.sub_order_id\n", "    -- and roi.sub_order_id = 93313187\n", "     and quantity > 0\n", "    )\n", "    ),\n", "-- select * from expected_rto1\n", "complete_task1 as\n", "(\n", "    select id, collecting_entity_id,returning_entity_id, return_flow_task_state,station_id,\n", "    (completed_at + interval '5.5 hour') as completed_date, child_return_flow_task_id\n", "    from lake_last_mile.return_flow_task where returning_entity_type = 'DP'\n", "            and return_flow_task_state = 'COMPLETED'\n", ")\n", "-- select *\n", "-- from (\n", "select schedule_date,max(completed_date) completed_date, station, city,c.sub_order_id,c.product_id,\n", "dp.name as returning_entity_name , fe2.employee_id as returning_entity_employee_id,\n", "max(fe.name) as collecting_entity_name, max(fe.employee_id) as collecting_entity_employee_id,parent_return_flow_order_id,\n", "sum(collected_qty) quantity,sum(value) value \n", "from (\n", "select distinct er.schedule_date,er.id as return_flow_item_id, er.order_item_id,em.id as return_flow_order_id,em.parent_return_flow_order_id,\n", "        er.sub_order_id, er.product_id,em.return_flow_task_id\n", "        ,rf.collected_qty, (rf.collected_qty*er.price_per_unit) as value,\n", "        completed_date, c.id as task_id, c.collecting_entity_id,c.returning_entity_id, c.return_flow_task_state,\n", "      s.name as station, city.name as city\n", "from expected_rto1 er\n", "join expected_this_month1 em on er.return_flow_order_id = em.id\n", "join metrics.lm_return_flow_task_completed_flat rf on rf.return_flow_order_item_id = er.id\n", "join base b on b.sub_order_id = er.sub_order_id and b.return_flow_item_id = er.id and b.returning_entity_type = 'DP' \n", "join complete_task1 c on em.return_flow_task_id = c.id\n", "left join lake_last_mile.station s on s.id = c.station_id\n", "left join lake_last_mile.city city on city.id = s.city_id\n", "-- where er.sub_order_id in (select sub_order_id from base) --where returning_entity_type in ('DP'))\n", "--where c.id in (select task_id from base where returning_entity_type in ('DP'))\n", "where rf.collected_qty > 0\n", ") c\n", "left join lake_last_mile.field_executive fe on fe.id = c.collecting_entity_id\n", "left join lake_last_mile.distribution_point dp on dp.id = c.returning_entity_id\n", "left join lake_last_mile.field_executive fe2 on fe2.id = dp.primary_fe_id\n", "group by 1,3,4,5,6,7,8,11\n", "union \n", "select schedule_date,max(completed_date) completed_date, station, city,sub_order_id,product_id,\n", "dp.name as returning_entity_name , fe2.employee_id as returning_entity_employee_id,\n", "max(fe.name) as collecting_entity_name, max(fe.employee_id) as collecting_entity_employee_id, parent_return_flow_order_id,\n", "sum(collected_qty) quantity,sum(value) value \n", "-- count(distinct task_id)\n", "from (\n", "select distinct er.schedule_date,er.id as return_flow_item_id, er.order_item_id,er.sub_order_id,er.product_id,\n", "        em.id as return_flow_order_id, em.return_flow_task_id,em.parent_return_flow_order_id\n", "        ,rf.collected_qty, (rf.collected_qty*er.price_per_unit) as value,\n", "        completed_date, c.id as task_id, c.collecting_entity_id,c.returning_entity_id, c.return_flow_task_state,\n", "      s.name as station, city.name as city\n", "from expected_rto1 er\n", "join base b on b.sub_order_id = er.sub_order_id and b.returning_entity_type in ('GSP','FE') and b.product_id = er.product_id\n", "join expected_this_month1 em on er.return_flow_order_id = em.id and em.parent_return_flow_order_id = b.return_flow_order_id\n", "join metrics.lm_return_flow_task_completed_flat rf on rf.return_flow_order_item_id = er.id\n", "join complete_task1 c on em.return_flow_task_id = c.id\n", "left join lake_last_mile.station s on s.id = c.station_id\n", "left join lake_last_mile.city city on city.id = s.city_id\n", "-- where er.sub_order_id in (select sub_order_id from base) --where returning_entity_type in ('DP'))\n", "-- where c.id in (select task_id from base where returning_entity_type in ('DP'))\n", "where rf.collected_qty > 0\n", ") c\n", "left join lake_last_mile.field_executive fe on fe.id = c.collecting_entity_id\n", "left join lake_last_mile.distribution_point dp on dp.id = c.returning_entity_id\n", "left join lake_last_mile.field_executive fe2 on fe2.id = dp.primary_fe_id\n", "group by 1,3,4,5,6,7,8,11\n", "-- )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task = pd.read_sql_query(\n", "    sql=dp_hfe, con=Redshift, params={\"Start\": run_period_start, \"End\": run_period_end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task[\"product_id\"] = dp_hfe_task[\"product_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task[\"key\"] = (\n", "    dp_hfe_task[\"sub_order_id\"].astype(str)\n", "    + \"|\"\n", "    + dp_hfe_task[\"product_id\"].astype(str)\n", ")\n", "dp_hfe_task[\"key1\"] = (\n", "    dp_hfe_task[\"sub_order_id\"].astype(str)\n", "    + \"|\"\n", "    + dp_hfe_task[\"product_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task.groupby(\"key\", as_index=False).agg({\"key1\": \"count\"}).sort_values(\n", "    \"key1\", ascending=False\n", ").head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task1 = pd.merge(dp_hfe_task, it_pd_log_df, how=\"left\", on=\"product_id\")\n", "dp_hfe_task1[\"quantity_actual\"] = dp_hfe_task1[\"quantity\"] * dp_hfe_task1[\"multiplier\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp_hfe_task1 = dp_hfe_task1.groupby(\n", "    [\n", "        \"returning_entity_name\",\n", "        \"returning_entity_employee_id\",\n", "        \"item_id\",\n", "        \"sub_order_id\",\n", "        \"collecting_entity_employee_id\",\n", "    ],\n", "    as_index=False,\n", ").agg({\"quantity_actual\": sum, \"completed_date\": max, \"collecting_entity_name\": max})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull = pd.merge(\n", "    UniverseDataMerged, dp_hfe_task1, how=\"left\", on=[\"sub_order_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull[UniverseDataMergedfull[\"sub_order_id\"] == 92512388]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull.rename(\n", "    columns={\n", "        \"returning_entity_name\": \"DP_HFE_returning_entity_name\",\n", "        \"returning_entity_employee_id\": \"DP_HFE_returning_entity_employee_id\",\n", "        \"quantity_actual\": \"DP_HFE_quantity_actual\",\n", "        \"completed_date\": \"DP_HFE_completed_date\",\n", "        \"collecting_entity_name\": \"DP_HFE_collecting_entity_name\",\n", "        \"collecting_entity_employee_id\": \"DP_HFE_collecting_entity_employee_id\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull.facility_id.fillna(0, inplace=True)\n", "UniverseDataMergedfull.outlet_id.fillna(0, inplace=True)\n", "UniverseDataMergedfull.backend_merchant_id.fillna(0, inplace=True)\n", "UniverseDataMergedfull.dsp_salary_settled.fillna(0, inplace=True)\n", "UniverseDataMergedfull.gsp_salary_settled.fillna(0, inplace=True)\n", "UniverseDataMergedfull.dp_forgiven.fillna(0, inplace=True)\n", "UniverseDataMergedfull.gsp_forgiven.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull.facility_id = UniverseDataMergedfull.facility_id.astype(int)\n", "UniverseDataMergedfull.outlet_id = UniverseDataMergedfull.outlet_id.astype(int)\n", "UniverseDataMergedfull.backend_merchant_id = (\n", "    UniverseDataMergedfull.backend_merchant_id.astype(int)\n", ")\n", "UniverseDataMergedfull.dsp_salary_settled = (\n", "    UniverseDataMergedfull.dsp_salary_settled.astype(object)\n", ")\n", "UniverseDataMergedfull.gsp_salary_settled = (\n", "    UniverseDataMergedfull.gsp_salary_settled.astype(float)\n", ")\n", "UniverseDataMergedfull.Dispatch_suborderid = (\n", "    UniverseDataMergedfull.Dispatch_suborderid.astype(float)\n", ")\n", "UniverseDataMergedfull.dp_forgiven = UniverseDataMergedfull.dp_forgiven.astype(float)\n", "UniverseDataMergedfull.gsp_forgiven = UniverseDataMergedfull.gsp_forgiven.astype(float)\n", "UniverseDataMergedfull.dispatch_time = pd.to_datetime(\n", "    UniverseDataMergedfull.dispatch_time\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Description_Dict = {\n", "    \"expectedreturndate\": \"Date of Return Generation\",\n", "    \"l1_reason\": \"l1 reason for return i.e. cancel , reprocure or doorstep\",\n", "    \"reason\": \"further bifurcation of doorstep into granular reasons\",\n", "    \"scheduleddate\": \"scheduled delivery date\",\n", "    \"inward_date\": \"date of inward/reverse billing\",\n", "    \"expected_quantity\": \"Quantity Expected to return\",\n", "    \"selling_price\": \"Price of item at billing\",\n", "    \"inward_quantity\": \"Quantity Inwarded out of Expected\",\n", "    \"inwardedvalue\": \"Amount Inwarded against expected\",\n", "    \"expectedvalue\": \"Amount Expected\",\n", "    \"key\": \"Unique key\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\n", "            \"name\": key,\n", "            \"type\": ref_dict[value],\n", "            \"description\": Description_Dict.get(\n", "                key, \"Column name sufficient for description\"\n", "            ),\n", "        }\n", "        for key, value in metadata.items()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UniverseDataMergedfull[\"key\"] = (\n", "    UniverseDataMergedfull[\"sub_order_id\"].astype(str)\n", "    + \"|\"\n", "    + UniverseDataMergedfull[\"variant_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = redshift_schema(UniverseDataMergedfull)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"returns_wh_lm_integration\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outlet_id\"],\n", "    \"incremental_key\": \"scheduleddate\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Updated table on expected returns with more information on end to end return cycle of the items\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(UniverseDataMergedfull, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query = \"\"\"Select  distinct dp_forgiven from metrics.returns_wh_lm_integration\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data = pd.read_sql_query(\n", "#     sql=Query, con=Redshift\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}