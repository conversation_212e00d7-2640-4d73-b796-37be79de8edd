{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_task = \"\"\"sELECT  t.facility_id, \n", "--date(t.created_at) created_at,\n", "cf.name as facility_name,\n", "coalesce(count(t.id),0) order_ids\n", "FROM \n", " consumer.warehouse_location_return_task t\n", " join consumer.crates_facility cf on cf.id=t.facility_id\n", "where\n", "convert_timezone('Asia/Kolkata',t.created_at)<=current_date -5\n", "and state!=7\n", "group by 1,2\n", "\"\"\"\n", "open_task_df = pd.read_sql_query(sql=open_task, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_task_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_count = \"\"\"select facility_id,facility_name,--received_date,\n", "count(product_id) as product_id_count\n", "from\n", "(select\n", "rt.facility_id, \n", "cf.name as facility_name,\n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id, \n", "rtoa.product_id\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "JOIN consumer.warehouse_location_return_task_order rto on rto.return_task_id=rt.id\n", "join  consumer.warehouse_location_return_task_order_actual rtoa on rtoa.return_task_order_id=rto.id\n", "join  consumer.crates_facility cf on cf.id=rt.facility_id\n", "where \n", " rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=current_date -2 and convert_timezone('Asia/Kolkata',rtsl.created_at)<current_date -1) x\n", "group by 1,2--,3\n", "--and rtsl.created_at>='2020-02-17' and rtsl.created_at<='2020-02-18'\n", "--and rtoi.return_task_id=302757\n", "--group by 1,2,3,4,5,6,7,8,9,10\n", "--,11\n", "\n", "\n", "\"\"\"\n", "prod_count_df = pd.read_sql_query(sql=product_count, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_count_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with oid_base as (\n", "select 'with_oid' as  type, rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rtsl.created_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "avg(rtoi.quantity) as expected_quantity, \n", "avg(rtoi.map_quantity) map_quantity,\n", "avg(pdt.selling_price) as selling_price\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join  consumer.pos_pos_invoice pos on pos.grofers_order_id= rtoi.order_id \n", "left join  consumer.pos_pos_invoice_product_details pdt on pdt.invoice_id = pos.id\n", "left join consumer.rpc_product_product prod on prod.variant_id = pdt.variant_id\n", "\n", "\n", "where invoice_type_id=1 and prod.item_id=rtoi.item_id and prod.variant_id not like '%%G00%%'\n", "and rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=current_date -2 and convert_timezone('Asia/Kolkata',rtsl.created_at)<current_date -1\n", "--and rtsl.created_at>='2020-02-17' and rtsl.created_at<='2020-02-18'\n", "--and rtoi.return_task_id=302757\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "without_oid_base as(\n", "select \n", " 'without_oid' as type,\n", "rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rtsl.created_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "\n", "rtoi.order_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity,\n", "avg(lp.landing_price) as selling_price\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join \n", "(select item_id, avg(landing_price) landing_price\n", "from\n", "(select pp.item_id, a.variant_id, a.landing_price\n", "from\n", "ims_ims_inventory_landing_price a\n", "join\n", "(select variant_id, max(pos_timestamp) pos_timestamp\n", "from\n", "consumer.ims_ims_inventory_landing_price\n", "group by 1) b on a.variant_id=b.variant_id and a.pos_timestamp=b.pos_timestamp\n", "join consumer.rpc_product_product pp on pp.variant_id=a.variant_id)c\n", "group by 1) lp on lp.item_id= rtoi.item_id\n", "\n", "where \n", " rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rtsl.created_at)>=current_date -2 and convert_timezone('Asia/Kolkata',rtsl.created_at)<current_date -1\n", "--and rtsl.created_at>='2020-02-17' and rtsl.created_at<='2020-02-18'\n", "--and rtoi.return_task_id=329146\n", "and (rtoi.order_id ='None' or rtoi.order_id='' or rtoi.order_id is null or  rtoi.order_id=' ' or len(trim(rtoi.order_id))<1)\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "\n", ")\n", "\n", "-- select return_task_id, order_id, item_id, count(*)\n", "-- from\n", "-- (\n", "select o.*, c.name as facility_name from oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "\n", "union\n", "select o.*, c.name as facility_name from without_oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "-- ) c \n", "-- group by 1,2,3 having count(*)>1\n", "\n", "\"\"\"\n", "\n", "# df=pd.read_sql_query(sql=query, con=con_redshift)\n", "\n", "\n", "df = pd.read_sql_query(sql=query, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df[df.return_task_id==335739].to_csv('335739.csv')\n", "df.type.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.groupby([\"return_task_id\"]).agg({\"expected_quantity\": \"sum\", \"map_quantity\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.expected_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_rto_efficiancy = df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_facility = pd.DataFrame(\n", "    data=task_rto_efficiancy[\"facility_name\"].unique(), columns=[\"facility_name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_rto_efficiancy = task_rto_efficiancy[\n", "    [\n", "        \"received_date\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"return_task_id\",\n", "        \"received_state\",\n", "        \"latest_state\",\n", "        \"received_ts\",\n", "        \"latest_ts\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_rto_efficiancy[\"time_diff_in_hour\"] = (\n", "    (task_rto_efficiancy[\"latest_ts\"] - task_rto_efficiancy[\"received_ts\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_completion_mean = (\n", "    task_rto_efficiancy[task_rto_efficiancy.latest_state == 7]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"time_diff_in_hour\": \"mean\"})\n", ")\n", "task_completion_before_24 = (\n", "    task_rto_efficiancy[\n", "        (task_rto_efficiancy.latest_state == 7)\n", "        & (task_rto_efficiancy.time_diff_in_hour <= 24)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", ")\n", "task_completion_after_24 = (\n", "    task_rto_efficiancy[\n", "        (task_rto_efficiancy.latest_state == 7)\n", "        & (task_rto_efficiancy.time_diff_in_hour >= 24)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", ")\n", "task_not_completed = (\n", "    task_rto_efficiancy[task_rto_efficiancy.latest_state != 7]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level1 = pd.merge(\n", "    all_facility,\n", "    task_completion_before_24,\n", "    how=\"left\",\n", "    left_on=[\"facility_name\"],\n", "    right_on=[\"facility_name\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level1.rename(columns={\"return_task_id\": \"RTCompletedBefore24Hr\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level2 = pd.merge(\n", "    level1,\n", "    task_completion_after_24,\n", "    how=\"left\",\n", "    left_on=\"facility_name\",\n", "    right_on=\"facility_name\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level2.rename(columns={\"return_task_id\": \"RTCompletedAfter24Hr\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level3 = pd.merge(\n", "    level2,\n", "    task_not_completed,\n", "    how=\"left\",\n", "    left_on=\"facility_name\",\n", "    right_on=\"facility_name\",\n", ")\n", "level3.rename(columns={\"return_task_id\": \"RTNotCompleted\"}, inplace=True)\n", "level4 = pd.merge(\n", "    level3,\n", "    task_completion_mean,\n", "    how=\"left\",\n", "    left_on=\"facility_name\",\n", "    right_on=\"facility_name\",\n", ")\n", "level4.rename(columns={\"time_diff_in_hour\": \"AvgTimeOfCompletion\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4.fillna(0, inplace=True)\n", "# level4.round({'AvgTimeOfCompletion': 1},inplace=True)\n", "level4.AvgTimeOfCompletion = level4.AvgTimeOfCompletion.round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["############## Avg time to close a task##################"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4 = level4.merge(\n", "    open_task_df, how=\"left\", left_on=\"facility_name\", right_on=\"facility_name\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4[\"RTProcessingAfter24Hr\"] = (\n", "    level4[\"RTCompletedAfter24Hr\"] + level4[\"RTNotCompleted\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4 = level4.fillna(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON>em <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df = df\n", "task_item_level_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df[\"expected_value\"] = (\n", "    task_item_level_df[\"selling_price\"] * task_item_level_df[\"expected_quantity\"]\n", ")\n", "task_item_level_df[\"mapped_value\"] = (\n", "    task_item_level_df[\"selling_price\"] * task_item_level_df[\"map_quantity\"]\n", ")\n", "task_item_level_df[\"diff_value\"] = (\n", "    task_item_level_df[\"expected_value\"] - task_item_level_df[\"mapped_value\"]\n", ")\n", "task_item_level_df[\"diff_qty\"] = (\n", "    task_item_level_df[\"expected_quantity\"] - task_item_level_df[\"map_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df[\"latest_state\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_states = pd.DataFrame(\n", "    [\n", "        [\"DRAFT\", 1],\n", "        [\"ORDER_SYNCED\", 2],\n", "        [\"ORDER_SYNC_FAILED\", 3],\n", "        [\"CREATED\", 4],\n", "        [\"RECEIVED\", 5],\n", "        [\"RECEIVED_CRATE\", 6],\n", "        [\"COMPLETED\", 7],\n", "    ],\n", "    columns=[\"states\", \"id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df2 = pd.merge(\n", "    task_item_level_df, task_states, how=\"left\", left_on=\"received_state\", right_on=\"id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df2.rename(columns={\"states\": \"received_states\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df3 = pd.merge(\n", "    task_item_level_df2, task_states, how=\"left\", left_on=\"latest_state\", right_on=\"id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df3.rename(columns={\"states\": \"latest_states\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4 = task_item_level_df3.drop(columns=[\"id_x\", \"id_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4[\"time_diff_in_hour\"] = (\n", "    (task_item_level_df4[\"latest_ts\"] - task_item_level_df4[\"received_ts\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_item_level_df4['ReceivedDate']=pd.to_datetime(task_item_level_df4['received_ts'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4.rename(\n", "    columns={\n", "        \"facility_id\": \"FacilityID\",\n", "        \"facility_name\": \"FacilityName\",\n", "        \"return_task_id\": \"ReturnTaskID\",\n", "        \"received_ts\": \"ReceivedTs\",\n", "        \"latest_states\": \"LatestStateName\",\n", "        \"time_diff_in_hour\": \"TimeTaken\",\n", "        \"order_id\": \"OrderID\",\n", "        \"item_id\": \"ItemID\",\n", "        \"item_name\": \"ItemName\",\n", "        \"expected_quantity\": \"ExpectedQuantity\",\n", "        \"map_quantity\": \"MappedQuantity\",\n", "        \"diff_qty\": \"DiffQuantity\",\n", "        \"expected_value\": \"ExpectedValue\",\n", "        \"mapped_value\": \"MappedValue\",\n", "        \"diff_value\": \"DiffValue\",\n", "        \"latest_ts\": \"LatestTs\",\n", "        \"ancestor\": \"AncestorOrderID\",\n", "        \"task_created_at\": \"TaskCreatedTs\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df4.drop(\n", "    columns=[\"received_state\", \"latest_state\", \"selling_price\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df5 = task_item_level_df4[\n", "    [\n", "        \"type\",\n", "        \"FacilityID\",\n", "        \"FacilityName\",\n", "        \"ReturnTaskID\",\n", "        \"TaskCreatedTs\",\n", "        \"ReceivedTs\",\n", "        \"LatestStateName\",\n", "        \"TimeTaken\",\n", "        \"AncestorOrderID\",\n", "        \"OrderID\",\n", "        \"ItemID\",\n", "        \"ItemName\",\n", "        \"ExpectedQuantity\",\n", "        \"MappedQuantity\",\n", "        \"DiffQuantity\",\n", "        \"ExpectedValue\",\n", "        \"MappedValue\",\n", "        \"DiffValue\",\n", "        \"LatestTs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_df5.type.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df = task_item_level_df5.groupby([\"FacilityName\"]).agg(\n", "    {\n", "        \"ReturnTaskID\": \"nunique\",\n", "        \"ExpectedQuantity\": \"sum\",\n", "        \"MappedQuantity\": \"sum\",\n", "        \"ExpectedValue\": \"sum\",\n", "        \"MappedValue\": \"sum\",\n", "        \"DiffValue\": \"sum\",\n", "    }\n", ")\n", "\n", "task_item_level_with_order_id_df2 = (\n", "    task_item_level_df5.groupby([\"FacilityName\", \"type\"])\n", "    .agg(\n", "        {\n", "            \"ExpectedQuantity\": \"sum\",\n", "            \"MappedQuantity\": \"sum\",\n", "            \"ExpectedValue\": \"sum\",\n", "            \"MappedValue\": \"sum\",\n", "            \"DiffValue\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "task_item_level_with_order_id_df2 = task_item_level_with_order_id_df2.pivot(\n", "    index=\"FacilityName\",\n", "    columns=\"type\",\n", "    values=[\n", "        \"ExpectedQuantity\",\n", "        \"MappedQuantity\",\n", "        \"ExpectedValue\",\n", "        \"MappedValue\",\n", "        \"DiffValue\",\n", "    ],\n", ").reset_index()\n", "\n", "\n", "task_item_level_with_order_id_df2 = pd.merge(\n", "    task_item_level_with_order_id_df,\n", "    task_item_level_with_order_id_df2,\n", "    on=\"FacilityName\",\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3 = task_item_level_with_order_id_df2.rename(\n", "    columns={\n", "        \"FacilityName\": \"FacilityName\",\n", "        \"ReturnTaskID\": \"ReturnTaskIDCount\",\n", "        (\"MappedQuantity\", \"with_oid\"): \"MappedQuantity_WithOID\",\n", "        (\"MappedQuantity\", \"without_oid\"): \"MappedQuantity_WithoutOID\",\n", "        (\"DiffValue\", \"with_oid\"): \"DiffValue_WithOID\",\n", "        (\"DiffValue\", \"without_oid\"): \"DiffValue_WithoutOID\",\n", "        (\"ExpectedQuantity\", \"with_oid\"): \"ExpectedQuantity_WithOID\",\n", "        (\"ExpectedQuantity\", \"without_oid\"): \"ExpectedQuantity_WithoutOID\",\n", "        (\"ExpectedValue\", \"without_oid\"): \"ExpectedValue_WithoutOID\",\n", "        (\"ExpectedValue\", \"with_oid\"): \"ExpectedValue_WithOID\",\n", "        (\"MappedValue\", \"without_oid\"): \"MappedValue_WithoutOID\",\n", "        (\"MappedValue\", \"with_oid\"): \"MappedValue_WithOID\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["58486.54609 + 45568.025189"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3[\"DiffQuantity\"] = (\n", "    task_item_level_with_order_id_df3[\"ExpectedQuantity\"]\n", "    - task_item_level_with_order_id_df3[\"MappedQuantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3[\"DiffQuantityWithOID\"] = (\n", "    task_item_level_with_order_id_df3[\"ExpectedQuantity_WithOID\"]\n", "    - task_item_level_with_order_id_df3[\"MappedQuantity_WithOID\"]\n", ")\n", "task_item_level_with_order_id_df3\n", "\n", "\n", "# task_item_level_with_order_id_df3[\"DiffQuantityWithoutOID\"] = (\n", "#     task_item_level_with_order_id_df3[\"ExpectedQuantity_WithoutOID\"]\n", "#     - task_item_level_with_order_id_df3[\"MappedQuantity_WithoutOID\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3[\"DiffQuantityWithoutOID\"] = (\n", "    task_item_level_with_order_id_df3[\"ExpectedQuantity_WithoutOID\"]\n", "    - task_item_level_with_order_id_df3[\"MappedQuantity_WithoutOID\"]\n", ")\n", "\n", "\n", "task_item_level_with_order_id_df3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["5386 - 4512"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df4 = task_item_level_with_order_id_df3[\n", "    [\n", "        \"FacilityName\",\n", "        \"ReturnTaskIDCount\",\n", "        \"ExpectedQuantity\",\n", "        \"MappedQuantity\",\n", "        \"ExpectedQuantity_WithOID\",\n", "        \"MappedQuantity_WithOID\",\n", "        \"ExpectedQuantity_WithoutOID\",\n", "        \"MappedQuantity_WithoutOID\",\n", "        \"DiffQuantity\",\n", "        \"DiffQuantityWithOID\",\n", "        \"DiffQuantityWithoutOID\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_value_level_with_order_id_df = task_item_level_df5.groupby([\"FacilityName\"]).agg(\n", "#     {\n", "#         \"ReturnTaskID\": \"nunique\",\n", "#         \"ExpectedValue\": \"sum\",\n", "#         \"MappedValue\": \"sum\",\n", "#         \"DiffValue\": \"sum\",\n", "#     }\n", "# )\n", "\n", "# task_value_level_with_order_id_df2 = (\n", "#     task_item_level_df5.groupby([\"FacilityName\", \"type\"])\n", "#     .agg({\"MappedValue\": \"sum\", \"DiffValue\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "# task_value_level_with_order_id_df2 = task_value_level_with_order_id_df2.pivot(\n", "#     index=\"FacilityName\", columns=\"type\", values=[\"MappedValue\", \"DiffValue\"]\n", "# ).reset_index()\n", "\n", "\n", "# task_value_level_with_order_id_df2 = pd.merge(\n", "#     task_value_level_with_order_id_df,\n", "#     task_value_level_with_order_id_df2,\n", "#     on=\"FacilityName\",\n", "#     how=\"inner\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_value_level_with_order_id_df2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_value_level_with_order_id_df3 = task_value_level_with_order_id_df2.rename(\n", "#     columns={\n", "#         (\"MappedValue\", \"with_oid\"): \"MappedValue_WithOID\",\n", "#         (\"MappedValue\", \"without_oid\"): \"MappedValue_WithoutOID\",\n", "#     }\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df3.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_value_level_with_order_id_df4 = task_item_level_with_order_id_df3[\n", "    [\n", "        \"FacilityName\",\n", "        \"ReturnTaskIDCount\",\n", "        \"ExpectedValue\",\n", "        \"MappedValue\",\n", "        \"ExpectedValue_WithOID\",\n", "        \"MappedValue_WithOID\",\n", "        \"ExpectedValue_WithoutOID\",\n", "        \"MappedValue_WithoutOID\",\n", "        \"DiffValue\",\n", "        \"DiffValue_WithOID\",\n", "        \"DiffValue_WithoutOID\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_value_level_with_order_id_df4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_value_level_with_order_id_df4.ExpectedValue = task_value_level_with_order_id_df4.ExpectedValue.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.MappedValue = task_value_level_with_order_id_df4.MappedValue.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.MappedValue_WithOID = task_value_level_with_order_id_df4.MappedValue_WithOID.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.MappedValue_WithoutOID = task_value_level_with_order_id_df4.MappedValue_WithoutOID.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.DiffValue = task_value_level_with_order_id_df4.DiffValue.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.DiffValue_WithOID = task_value_level_with_order_id_df4.DiffValue_WithOID.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.DiffValue_WithoutOID = task_value_level_with_order_id_df4.DiffValue_WithoutOID.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.ExpectedValue_WithOID = task_value_level_with_order_id_df4.ExpectedValue_WithOID.astype(\n", "    int\n", ")\n", "task_value_level_with_order_id_df4.ExpectedValue_WithoutOID = task_value_level_with_order_id_df4.ExpectedValue_WithoutOID.astype(\n", "    int\n", ")\n", "\n", "dr_at_value_df = task_value_level_with_order_id_df4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_item_level_with_order_id_df4.MappedQuantity_WithOID = task_item_level_with_order_id_df4.MappedQuantity_WithOID.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.MappedQuantity_WithoutOID = task_item_level_with_order_id_df4.MappedQuantity_WithoutOID.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.DiffQuantity = task_item_level_with_order_id_df4.DiffQuantity.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.ExpectedQuantity = task_item_level_with_order_id_df4.ExpectedQuantity.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.DiffQuantityWithOID = task_item_level_with_order_id_df4.DiffQuantityWithOID.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.DiffQuantityWithoutOID = task_item_level_with_order_id_df4.DiffQuantityWithoutOID.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.ExpectedQuantity_WithOID = task_item_level_with_order_id_df4.ExpectedQuantity_WithOID.astype(\n", "    int\n", ")\n", "task_item_level_with_order_id_df4.ExpectedQuantity_WithoutOID = task_item_level_with_order_id_df4.ExpectedQuantity_WithoutOID.astype(\n", "    int\n", ")\n", "\n", "dr_at_qty_df = task_item_level_with_order_id_df4\n", "dr_at_qty_df = task_item_level_with_order_id_df4.merge(\n", "    prod_count_df, how=\"left\", left_on=\"FacilityName\", right_on=\"facility_name\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dr_at_qty_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = task_item_level_df5[\n", "    [\n", "        \"FacilityID\",\n", "        \"FacilityName\",\n", "        \"ReturnTaskID\",\n", "        \"ReceivedTs\",\n", "        \"LatestStateName\",\n", "        \"LatestTs\",\n", "        \"OrderID\",\n", "        \"ItemID\",\n", "        \"ItemName\",\n", "        \"ExpectedQuantity\",\n", "        \"MappedQuantity\",\n", "        \"ExpectedValue\",\n", "        \"MappedValue\",\n", "    ]\n", "]\n", "df1.rename(\n", "    columns={\n", "        \"ReturnTaskID\": \"WH ReturnTask ID\",\n", "        \"TaskCreatedTs\": \"Task Created Date\",\n", "        \"ReceivedTs\": \"Task Received Date\",\n", "        \"LatestStateName\": \"Latest State of Task\",\n", "        \"LatestTs\": \"Time of Completion\",\n", "        \"AncestorOrderID\": \"Order ID\",\n", "        \"OrderID\": \"Suborder ID\",\n", "        \"ItemID\": \"Item ID\",\n", "        \"ItemName\": \"Item Name\",\n", "        \"ExpectedQuantity\": \"Sent Item Quantity\",\n", "        \"MappedQuantity\": \"Received Item Quantity\",\n", "        \"ExpectedValue\": \"Sent Value in Rupees\",\n", "        \"MappedValue\": \"Received Value in Rupees\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "df1.to_csv(\"/tmp/Direct_Returns_\" + today + \".csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = dr_at_value_df.to_dict(orient=\"rows\")\n", "items2 = dr_at_qty_df.to_dict(orient=\"rows\")\n", "items3 = level4.to_dict(orient=\"rows\")\n", "items4 = today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from jinja2 import Template\n", "\n", "# from_email = \"<EMAIL>\"\n", "# subject = \"Direct Returns Report for \" + today\n", "\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \" <EMAIL>\",\n", "#     \" <EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \" <EMAIL>\",\n", "#     \" <EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \" <EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "\n", "# # to_email = [\"<EMAIL>\", \"<EMAIL>\"]\n", "\n", "# with open(os.path.join(cwd, \"email_template.html\"), \"r\") as f:\n", "#     t = Template(f.read())\n", "\n", "# rendered = t.render(\n", "#     products=items, products2=items2, products3=items3, products4=items4\n", "# )\n", "\n", "# pb.send_email(\n", "#     from_email,\n", "#     to_email,\n", "#     subject,\n", "#     html_content=rendered,\n", "#     files=[\"/tmp/Direct_Returns_\" + today + \".csv\"],\n", "# )\n", "# print(\"mail sent\")\n", "\n", "# # email_template = \"email_template.html\"\n", "# # loader = jinja2.FileSystemLoader(searchpath=\"/home/<USER>/\")\n", "# # tmpl_environ = jinja2.Environment(loader=loader)\n", "# # template = tmpl_environ.get_template(email_template)\n", "\n", "# # rendered = template.render(products=items, products2=items2, products3=items3, products4=items4\n", "# #                           )\n", "\n", "# # pb.send_email(\n", "# #     from_email,\n", "# #     to_email,\n", "# #     subject,\n", "# #     html_content=rendered,\n", "# #     files=[\"/home/<USER>/Direct_Returns_\"+ today + \".csv\"],\n", "# # )\n", "# # print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}