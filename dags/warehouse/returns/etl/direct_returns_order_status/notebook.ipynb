{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail_slave = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OMS_Bifrost = pb.get_connection(\"oms_bifrost\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LM = pb.get_connection(\"last_mile\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "Select rto.return_task_id,\n", "       date(rto.created_at) as created_at,\n", "       ancestor as parent_id,\n", "       order_id as suborder_id,\n", "       name as facility,\n", "       case \n", "       when b.state=1 then 'DRAFT' \n", "       when b.state=2 then 'ORDER_SYNCED' \n", "       when b.state=3 then 'ORDER_SYNC_FAILED' \n", "       when b.state=4 then 'CREATED' \n", "       when b.state=5 then 'RECEIVED' \n", "       when b.state=6 then 'RECEIVED_CRATE' \n", "       when b.state=7 then 'COMPLETED' \n", "       end as LatestState,\n", "       rto.expected_quantity\n", "from warehouse_location.return_task_order rto left join \n", "(\n", "Select return_task_id,\n", "       max(state) as state\n", "from warehouse_location.return_task_state_log \n", "group by return_task_id\n", ") b on rto.return_task_id=b.return_task_id\n", "left join warehouse_location.return_task rt on rt.id=rto.return_task_id\n", "left join crates.facility fcname on fcname.id = rt.facility_id\n", "where date(rto.created_at) between current_date-1 and current_date and b.state<=7\n", "\"\"\"\n", "\n", "# print(Query)\n", "\n", "Returns = pd.read_sql_query(sql=Query, con=retail_slave)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def <PERSON><PERSON><PERSON><PERSON><PERSON>(OrderID):\n", "    if OrderID is None or OrderID == \"None\":\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Returns[\"ReversePickupStatus\"] = list(map(ReversePickup, Returns[\"suborder_id\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Suborders = Returns[Returns[\"suborder_id\"] != \"None\"][\"suborder_id\"].astype(str)\n", "Suborders = Suborders.str.cat(sep=\",\")\n", "# Suborders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"Select * from oms_suborder where id in \"\"\"\n", "where = \"(\" + Suborders + \")\"\n", "\n", "Query2 = Query + where\n", "\n", "# print(Query2)\n", "OMS_Suborder = pd.read_sql_query(sql=Query2, con=OMS_Bifrost)\n", "# Data.head(10)\n", "\n", "Status = OMS_Suborder.filter([\"id\", \"current_status\"])\n", "\n", "Status = Status.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Returns_Status = pd.merge(\n", "    Returns, Status, how=\"left\", left_on=\"suborder_id\", right_on=\"id\"\n", ")\n", "# Returns_Status.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def func(row):\n", "    if row[\"ReversePickupStatus\"] == 1:\n", "        return \"Reverse Pickup\"\n", "    elif row[\"current_status\"] == \"REPROCURED\":\n", "        return \"Rescheduled\"\n", "    elif row[\"current_status\"] == \"CANCELLED\":\n", "        return \"Cancelled\"\n", "    else:\n", "        return \"Check for Doorstep\"\n", "\n", "\n", "Returns_Status[\"FinalStatus\"] = Returns_Status.apply(func, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Returns_Status['FinalStatus'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Orders_LM = Returns_Status[Returns_Status[\"FinalStatus\"] == \"Check for Doorstep\"][\n", "    \"parent_id\"\n", "].astype(str)\n", "Orders_LM = Orders_LM.str.cat(sep=\",\")\n", "Orders_LM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "Select external_order_id, order_id,max(oi.missing_reason) as MissingReason from last_mile.order ord\n", "left join last_mile.order_item oi on oi.order_id=ord.id\n", "where external_order_id in \"\"\"\n", "where = \"(\" + Orders_LM + \")\"\n", "Groupby = \"\"\" Group by external_order_id, order_id\"\"\"\n", "Query = Query + where + Groupby\n", "\n", "LM_Data = pd.read_sql_query(sql=Query, con=LM)\n", "\n", "\n", "def func(row):\n", "    if row[\"MissingReason\"] is not None:\n", "        return \"Doorstep Return\"\n", "    else:\n", "        return \"Reason Not Found\"\n", "\n", "\n", "LM_Data[\"Status\"] = LM_Data.apply(func, axis=1)\n", "LM_Data = LM_Data.filter([\"external_order_id\", \"Status\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FinalDataforProcess = pd.merge(\n", "    Returns_Status,\n", "    LM_Data,\n", "    how=\"left\",\n", "    left_on=\"parent_id\",\n", "    right_on=\"external_order_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def func(row):\n", "    if row[\"FinalStatus\"] == \"Check for DoorStep\":\n", "        return row[\"Status\"]\n", "    else:\n", "        return row[\"FinalStatus\"]\n", "\n", "\n", "FinalDataforProcess[\"FinalStatus\"] = FinalDataforProcess.apply(func, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FinalDataforProcess.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FinalDataforProcess = FinalDataforProcess.filter(\n", "    [\n", "        \"return_task_id\",\n", "        \"created_at\",\n", "        \"parent_id\",\n", "        \"suborder_id\",\n", "        \"facility\",\n", "        \"LatestState\",\n", "        \"expected_quantity\",\n", "        \"FinalStatus\",\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FinalDataforProcess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FinalDataforProcess.rename(columns={\"FinalStatus\": \"Return Reason\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rO2RqapVrh8yLQXYRVtPaBat9oftDi3hwW3kX9PhlxE\"\n", "sheet_name = \"Return_Reason\"\n", "pb.to_sheets(FinalDataforProcess, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Rescheduled and Cancelled Orders from OMS_Suborder\n", "\n", "### Return Pickup Order directly from return\n", "\n", "### LM Tables for DoorStep Return\n", "\n", "- LM Order get order id => here external_order_id is ancestor\n", "- LM Order Item => Here order_id is Id from LM Order\n", "- If missing reason is not null for any item then that order is doorstep return\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}