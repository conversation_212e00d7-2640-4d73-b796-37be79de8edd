{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"hide_input": true, "inputHidden": true}, "outputs": [], "source": ["# %%html\n", "# <span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at 'In [9]'.</span>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 1.084232, "end_time": "2020-01-15T07:04:01.692986", "exception": false, "start_time": "2020-01-15T07:04:00.608754", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = (datetime.now(tz) - timed<PERSON>ta(days=30)).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timedelta(days=0)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start, end"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.456348, "end_time": "2020-01-15T07:04:02.626661", "exception": false, "start_time": "2020-01-15T07:04:02.170313", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["run_period_start = start\n", "run_period_end = end"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.497758, "end_time": "2020-01-15T07:04:03.548918", "exception": false, "start_time": "2020-01-15T07:04:03.051160", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "# run_period_start = \"2020-03-05\"\n", "# run_period_end = \"2020-04-21\"\n", "# cwd = \"/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets\""]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.444813, "end_time": "2020-01-15T07:04:04.589931", "exception": false, "start_time": "2020-01-15T07:04:04.145118", "status": "completed"}, "tags": []}, "source": ["# L0 Category Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 36.35422, "end_time": "2020-01-15T07:04:41.372564", "exception": false, "start_time": "2020-01-15T07:04:05.018344", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "item_category_query = \"\"\"\n", "SELECT distinct\n", "pp.variant_id,\n", "CAT.NAME AS l0,\n", "CAT1.NAME AS l1\n", "/*pp.item_id,\n", "ipm.product_id,\n", "p.type as product_type,\n", "p.type_id as p_type,\n", "CAT.NAME AS l0,\n", "CAT.ID AS l0_ID,\n", "CAT1.NAME AS l1,\n", "CAT1.ID AS l1_ID,\n", "CAT2.NAME AS l2,\n", "CAT2.ID AS l2_ID,\n", "pb.name as brand,\n", "pb.id as brand_id\n", "P.ENABLED_FLAG*/\n", "FROM\n", "GR_PRODUCT P\n", "INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER JOIN GR_CATEGORY CAT2 ON PCM.CATEGORY_ID = CAT2.ID AND PCM.IS_PRIMARY=TRUE\n", "INNER JOIN GR_CATEGORY CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "INNER JOIN GR_CATEGORY CAT ON  CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "inner join consumer.rpc_item_product_mapping ipm on ipm.product_id=p.id and ipm.item_id is not null\n", "left join rpc_product_product pp on pp.item_id=ipm.item_id\n", "left join rpc_product_brand pb on pb.id=pp.brand_id\n", "where pp.active =1 and pp.approved=1 \n", "\"\"\"\n", "\n", "item_category_df = pd.read_sql_query(sql=item_category_query, con=redshift_con)\n", "item_category_df.sample(2)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.420572, "end_time": "2020-01-15T07:04:43.058427", "exception": false, "start_time": "2020-01-15T07:04:42.637855", "status": "completed"}, "tags": []}, "source": ["# Item Variant attributes from rpc.product_product "]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 27.266135, "end_time": "2020-01-15T07:05:10.751417", "exception": false, "start_time": "2020-01-15T07:04:43.485282", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "rpc_product_product_query = \"\"\"\n", "select item_id::int as item_id_1, *  from \n", "consumer.rpc_product_product rpp where active=1 and approved=1\n", "\"\"\"\n", "\n", "rpc_product_product_df = pd.read_sql_query(\n", "    sql=rpc_product_product_query, con=redshift_con\n", ")\n", "rpc_product_product_df.sample(2)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.434932, "end_time": "2020-01-15T07:05:12.453246", "exception": false, "start_time": "2020-01-15T07:05:12.018314", "status": "completed"}, "tags": []}, "source": ["# Product to Item Mapping through IT PD Log"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 17.51, "end_time": "2020-01-15T07:05:30.399059", "exception": false, "start_time": "2020-01-15T07:05:12.889059", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "it_pd_log_query = \"\"\"\n", "select  \n", "x.product_id::int, item_id::int ,multiplier  \n", "from \n", "consumer.updated_it_pd_log it join\n", "(\n", "select  i.product_id, max(updated_on) as max_updated_at from \n", "consumer.updated_it_pd_log i \n", "--join order_product_details opd on opd.product_id=i.product_id\n", "group by 1\n", ")\n", "x\n", "on x.product_id=it.product_id\n", "and updated_on=x.max_updated_at\n", "\"\"\"\n", "\n", "it_pd_log_df = pd.read_sql_query(sql=it_pd_log_query, con=redshift_con)\n", "it_pd_log_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# roi.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as billed_at,\n", "# os.install_ts as suborder_created_at,\n", "# oi.product_id::int,\n", "# roi.expected_quantity::int as expected_quantity\n", "# from consumer.lm_return_order ro\n", "# join consumer.lm_return_order_item roi ON ro.id= roi.return_order_id\n", "# join consumer.lm_return_order_item_transaction roit ON roit.return_order_item_id=roi.id\n", "# join consumer.lm_order_item oi ON roi.item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# --join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "# --join consumer.rpc_product_product pp on pp.item_id\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "# where\n", "# ro.return_cause='DELIVERED'\n", "# union\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as suborder_created_at,\n", "# os.install_ts as sub_order_created,\n", "# oi.product_id::int,\n", "# ri.quantity::int as expected_quantity\n", "# from consumer.lm_recon_order ro\n", "# join consumer.lm_recon_item ri ON ri.recon_order_id=ro.id\n", "# join consumer.lm_recon r ON ro.recon_id=r.id\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id\n", "# join consumer.lm_order o ON ro.order_id=o.id\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "# where\n", "# ro.recon_return_order_cause='DELIVERED'\n", "# union\n", "\n", "\n", "# select\n", "# 'DOORSTEP RETURN' as reason,\n", "# ri.sub_order_id::int,\n", "# pi.outlet_id,\n", "# pi.pos_timestamp as billed_at,\n", "# os.install_ts as suborder_created_at,\n", "# oi.product_id::int,\n", "# ri.quantity::int as expected_quantity\n", "# from\n", "# consumer.lm_return_flow_order ro\n", "# join consumer.lm_return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "# join consumer.lm_order_item oi ON ri.order_item_id=oi.id and ri.quantity>0\n", "# join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=83344005\n", "# join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1\n", "# and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": 0.45856, "end_time": "2020-01-15T07:05:32.129368", "exception": false, "start_time": "2020-01-15T07:05:31.670808", "status": "completed"}, "tags": []}, "source": ["# Doorstep returns"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 186.836635, "end_time": "2020-01-15T07:08:39.388042", "exception": false, "start_time": "2020-01-15T07:05:32.551407", "status": "completed"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "doorstep_return_order_query = \"\"\"\n", "    \n", "select \n", "'DOORSTEP RETURN' as reason,\n", "os.id as sub_order_id,\n", "pi.outlet_id,\n", "pi.pos_timestamp as billed_at, \n", "os.install_ts as suborder_created_at,\n", "loi.product_id,\n", "loi.quantity as expected_quantity,\n", "missing_reason\n", "from \n", "\n", "lake_last_mile.order lo \n", "join lake_last_mile.order_item loi on loi.order_id=lo.id and len(trim(loi.missing_reason))>0\n", "join oms_suborder os ON os.order_id=lo.external_order_id -- and roi.sub_order_id=63487214 \n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1 \n", "--join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "--join consumer.rpc_product_product pp on pp.item_id\n", "and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "\n", "    \n", "\"\"\"\n", "\n", "# print (doorstep_return_order_query)\n", "\n", "doorstep_return_order_df = pd.read_sql_query(\n", "    sql=doorstep_return_order_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": 0.482395, "end_time": "2020-01-15T07:08:41.167101", "exception": false, "start_time": "2020-01-15T07:08:40.684706", "status": "completed"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df = doorstep_return_order_df.replace(r\"^\\s*$\", 0, regex=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_df[\"sub_order_id\"] = doorstep_return_order_df[\n", "    \"sub_order_id\"\n", "].astype(float)\n", "doorstep_return_order_df[\"product_id\"] = doorstep_return_order_df[\"product_id\"].astype(\n", "    float\n", ")\n", "doorstep_return_order_df[\"expected_quantity\"] = doorstep_return_order_df[\n", "    \"expected_quantity\"\n", "].astype(float)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["### Product ID from doorstep returns to be mapped with item ids\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df = pd.merge(\n", "    left=doorstep_return_order_df,\n", "    right=it_pd_log_df,\n", "    how=\"inner\",\n", "    left_on=[\"product_id\"],\n", "    right_on=[\"product_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Duplicate on suborder_id and product_id\n", "\n", "# b=doorstep_return_order_item_level_df\n", "# b['is_duplicate']=b.duplicated(subset=['sub_order_id', 'product_id'], keep='first')\n", "# b.head()\n", "# x=b[b.is_duplicate==True]\n", "# x.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_df[\"new_expected_quantity\"] = (\n", "    doorstep_return_order_item_level_df[\"expected_quantity\"]\n", "    * doorstep_return_order_item_level_df[\"multiplier\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_item_level_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# check for the repition in original dataframe\n", "# doorstep_return_order_item_level_df.loc[\n", "#     doorstep_return_order_item_level_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_item_level_groupby_df = doorstep_return_order_item_level_df.groupby(\n", "    [\n", "        \"reason\",\n", "        \"sub_order_id\",\n", "        \"outlet_id\",\n", "        \"billed_at\",\n", "        \"suborder_created_at\",\n", "        \"item_id\",\n", "    ],\n", "    as_index=False,\n", ").agg(\n", "    {\"new_expected_quantity\": \"sum\"}\n", ")\n", "# doorste"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_groupby_df.loc[\n", "#     doorstep_return_order_item_level_groupby_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_item_level_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# merge items to all possible variants\n", "\n", "doorstep_return_order_variant_level_df = pd.merge(\n", "    left=doorstep_return_order_item_level_groupby_df,\n", "    right=rpc_product_product_df,\n", "    how=\"inner\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id_1\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_item_level_groupby_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2 = doorstep_return_order_variant_level_df[\n", "    [\n", "        \"reason\",\n", "        \"outlet_id\",\n", "        \"suborder_created_at\",\n", "        \"billed_at\",\n", "        \"sub_order_id\",\n", "        \"variant_id\",\n", "        \"new_expected_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2.rename(\n", "    columns={\"new_expected_quantity\": \"required_quantity\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2[\n", "    [\"sub_order_id\", \"variant_id\"]\n", "].drop_duplicates().shape[0] - doorstep_return_order_variant_level_df2.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_variant_level_df2.required_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2.loc[\n", "#     doorstep_return_order_variant_level_df2[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_variant_level_df2.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "doorstep_return_order_invoice_query = \"\"\"\n", "    select * from \n", "(select \n", "roi.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "--avg(pipd.quantity) as required_quantity,\n", "avg(selling_price)::int selling_price\n", "from lake_last_mile.return_order ro\n", "join lake_last_mile.return_order_item roi ON ro.id= roi.return_order_id \n", "join lake_last_mile.return_order_item_transaction roit ON roit.return_order_item_id=roi.id \n", "join lake_last_mile.return_order_item oi ON roi.item_id=oi.id\n", "join lake_last_mile.return_order o ON ro.order_id=o.id\n", "join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214 \n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1 \n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "\n", "\n", "and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "where \n", "ro.return_cause='DELIVERED'\n", "group by 1,2,3\n", "union \n", "select \n", "ri.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "--avg(pipd.quantity) as required_quantity,\n", "avg(selling_price)::int as selling_price\n", "from lake_last_mile.recon_order ro\n", "join lake_last_mile.recon_item ri ON ri.recon_order_id=ro.id \n", "join lake_last_mile.recon r ON ro.recon_id=r.id\n", "join lake_last_mile.order_item oi ON ri.order_item_id=oi.id\n", "join lake_last_mile.order o ON ro.order_id=o.id\n", "join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1 \n", "and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "\n", "where\n", "ro.recon_return_order_cause='DELIVERED'\n", "group by 1,2,3\n", "\n", "union\n", "select \n", "ri.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "--avg(pipd.quantity) as required_quantity,\n", "avg(selling_price)::int as selling_price\n", "\n", "from \n", "lake_last_mile.return_flow_order ro\n", "join lake_last_mile.return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "join lake_last_mile.order_item oi ON ri.order_item_id=oi.id and  ri.quantity>0\n", "join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=78176787\n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=1 \n", "and pi.created_at>=%(run_period_start)s and pi.created_at<=%(run_period_end)s\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", " \n", "group by 1,2,3\n", ")c\n", "group by 1,2,3,4\n", "\n", "\n", "\"\"\"\n", "\n", "doorstep_return_order_invoice_df = pd.read_sql_query(\n", "    sql=doorstep_return_order_invoice_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "doorstep_return_order_invoice_df.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_invoice_df[\n", "    [\"sub_order_id\", \"variant_id\"]\n", "].drop_duplicates().shape[0] - doorstep_return_order_invoice_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_invoice_df.groupby(\n", "    [\"sub_order_id\", \"variant_id\", \"item_name\", \"selling_price\"]\n", ").size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_invoice_df[doorstep_return_order_invoice_df['sub_order_id']==76327579 ].sort_values('selling_price',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_invoice_variant_level_df = pd.merge(\n", "    left=doorstep_return_order_variant_level_df2,\n", "    right=doorstep_return_order_invoice_df,\n", "    how=\"inner\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df.loc[\n", "#     doorstep_return_invoice_variant_level_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df=doorstep_return_order_variant_level_df2.groupby(['outlet_id',\t'suborder_created_at',\t'billed_at'\t,'sub_order_id',\t'variant_id'\n", "#                                         ],as_index = False).agg({'required_quantity':'sum',\n", "\n", "\n", "#                                                                                               })\n", "# doorstep_return_invoice_variant_level_df.loc[doorstep_return_order_variant_level_df2['sub_order_id']==79008835].sort_values('variant_id')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_invoice_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_invoice_variant_level_df[\n", "    [\"sub_order_id\", \"variant_id\"]\n", "].drop_duplicates().shape[0] - doorstep_return_invoice_variant_level_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "doorstep_return_order_inward_query = \"\"\"\n", "select \n", "'DOORSTEP RETURN' as reason,\n", "roi.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "coalesce(max(pi.pos_timestamp)::timestamp, null) as inwarded_at,\n", "avg(pipd.quantity)::int as inward_quantity\n", "\n", "from lake_last_mile.return_order ro\n", "join lake_last_mile.return_order_item roi ON ro.id= roi.return_order_id \n", "join lake_last_mile.return_order_item_transaction roit ON roit.return_order_item_id=roi.id \n", "join lake_last_mile.order_item oi ON roi.item_id=oi.id\n", "join  lake_last_mile.order o ON ro.order_id=o.id\n", "join oms_suborder os ON os.id=roi.sub_order_id-- and roi.sub_order_id=63487214 \n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "where\n", "greo.invoice_id is null\n", "\n", "and \n", "ro.return_cause='DELIVERED'\n", "group by 1,2,3,4\n", "union \n", "select \n", "'DOORSTEP RETURN' as reason,\n", "ri.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "coalesce(max(pi.pos_timestamp)::timestamp,null) as inwarded_at,\n", "avg(pipd.quantity)::int as inward_quantity\n", "from lake_last_mile.recon_order ro\n", "join lake_last_mile.recon_item ri ON ri.recon_order_id=ro.id \n", "join lake_last_mile.recon r ON ro.recon_id=r.id\n", "join lake_last_mile.order_item oi ON ri.order_item_id=oi.id\n", "join lake_last_mile.order o ON ro.order_id=o.id\n", "join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "where\n", "greo.invoice_id is null\n", "and\n", "ro.recon_return_order_cause='DELIVERED'\n", "group by 1,2,3,4\n", "\n", "\n", "union\n", "select\n", "'DOORSTEP RETURN' as reason,\n", "ri.sub_order_id::int,\n", "pipd.variant_id::varchar,\n", "pipd.name::varchar as item_name,\n", "coalesce(max(pi.pos_timestamp)::timestamp,null) as inwarded_at,\n", "avg(pipd.quantity)::int as inward_quantity\n", "\n", "from \n", "lake_last_mile.return_flow_order ro\n", "join lake_last_mile.return_flow_order_item ri on ro.id=ri.return_flow_order_id and return_flow_order_cause in ('DELIVERED')\n", "join lake_last_mile.order_item oi ON ri.order_item_id=oi.id\n", "join oms_suborder os ON os.id=ri.sub_order_id --and ri.sub_order_id=63487214\n", "join pos_pos_invoice pi on os.id=pi.grofers_order_id and pi.invoice_type_id=2\n", "join pos_pos_invoice pi2 on os.id=pi2.grofers_order_id and pi2.invoice_type_id=1\n", "\n", "and pi2.created_at>=%(run_period_start)s  and pi2.created_at<=%(run_period_end)s\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2 and pipd.variant_id not like 'G%%'\n", "left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "where\n", "greo.invoice_id is null\n", "group by 1,2,3,4\n", "\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "doorstep_return_order_inward_df = pd.read_sql_query(\n", "    sql=doorstep_return_order_inward_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "doorstep_return_order_inward_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_return_order_inward_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_return_order_inward_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - doorstep_return_order_inward_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_inward_df.groupby(\n", "#     [\"reason\", \"sub_order_id\", \"variant_id\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_return_order_inward_df[doorstep_return_order_inward_df['sub_order_id']==72932733]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_order_inward_df.loc[\n", "#     doorstep_return_order_inward_df[\"sub_order_id\"] == 77291413\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df = pd.merge(\n", "    doorstep_return_invoice_variant_level_df,\n", "    doorstep_return_order_inward_df,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_return_invoice_variant_level_df.loc[doorstep_return_invoice_variant_level_df['sub_order_id']==79008835]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df.loc[doorstep_order_df[\"sub_order_id\"] == 77291413]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - doorstep_order_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df = doorstep_order_df.drop(\"reason_y\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df.rename(columns={\"reason_x\": \"reason\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df = doorstep_order_df[\n", "    [\n", "        \"reason\",\n", "        \"outlet_id\",\n", "        \"suborder_created_at\",\n", "        \"billed_at\",\n", "        \"sub_order_id\",\n", "        \"variant_id\",\n", "        \"item_name_x\",\n", "        \"required_quantity\",\n", "        \"selling_price\",\n", "        \"inwarded_at\",\n", "        \"inward_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.rename(columns={\"item_name_x\": \"item_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.inward_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.required_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - doorstep_order_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df[doorstep_order_df['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doorstep_order_df[doorstep_order_df.expected_quantity==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# doorstep_order_df.loc[doorstep_order_df[\"sub_order_id\"] == 78835627]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# to check duplicacy\n", "# doorstep_order_df.groupby(['outlet_id','reason','sub_order_id','variant_id']).size().reset_index().sort_values(0,ascending=False)"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["# Reschedule and Cancellation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "forward_invoice_query = \"\"\"\n", "select\n", "reason,\n", "outlet_id,\n", "suborder_created_at::timestamp,\n", "billed_at::timestamp,\n", "sub_order_id,\n", "variant_id,\n", "item_name,\n", "inwarded_at::timestamp,\n", "avg(required_quantity) required_quantity,\n", "avg(selling_price) selling_price ,\n", "avg(inward_quantity) inward_quantity\n", "from\n", "(\n", "select\n", "pi.reason,\n", "pi.outlet_id,\n", "pi.suborder_created_at,\n", "pi.pos_timestamp as billed_at,\n", "pi.sub_order_id,\n", "pi.variant_id,\n", "pi.item_name,\n", "pi.required_quantity,\n", "pi.selling_price ,\n", "max(pipd2.pos_timestamp)::timestamp as inwarded_at,\n", "sum(pipd2.return_quantity) inward_quantity\n", "--coalesce(sum(pipd2.quantity),0) as inwarded_quantity\n", "from \n", "(\n", "select\n", "os.install_ts as suborder_created_at,\n", "os.current_status as reason,\n", "pi.outlet_id,\n", "pi.pos_timestamp,\n", "pi.grofers_order_id as sub_order_id,\n", "pipd.variant_id,\n", "pipd.name as item_name,\n", "AVG(pipd.selling_price) selling_price ,\n", "sum(pipd.quantity) as required_quantity\n", "from\n", "oms_suborder os \n", "join \n", "( select grofers_order_id, max(pos_timestamp) max_pos_timestamp \n", "  from consumer.pos_pos_invoice \n", "  where invoice_type_id=1 \n", "  and created_at>=%(run_period_start)s  and created_at<=%(run_period_end)s group by 1) m on os.id=m.grofers_order_id\n", " join consumer.pos_pos_invoice pi \n", "on os.id=pi.grofers_order_id and pi.invoice_type_id=1 and os.current_status in ('CANCELLED','REPROCURED') and os.type not  ilike ('%%digital%%') \n", "\n", "and m.grofers_order_id=pi.grofers_order_id and m.max_pos_timestamp=pi.pos_timestamp\n", "\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=1 and pipd.variant_id not like 'G%%'\n", "where pi.created_at>=%(run_period_start)s  and pi.created_at<=%(run_period_end)s \n", "--and pi.grofers_order_id=78450865\n", "group by 1,2,3,4,5,6,7\n", ") pi\n", "\n", "left join \n", "(select pi.invoice_id,pi.grofers_order_id, variant_id, pi.pos_timestamp::timestamp, sum(pipd.quantity) as return_quantity from\n", "consumer.pos_pos_invoice pi join\n", "consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and pi.invoice_type_id=2\n", "join pos_pos_invoice pi4 on pi4.grofers_order_id=pi.grofers_order_id and pi4.invoice_type_id=1  \n", "and pi4.created_at>=%(run_period_start)s and pi4.created_at<=%(run_period_end)s \n", "left join consumer.ims_grn_return_expected_order greo on greo.invoice_id=pi.invoice_id and greo.status=2\n", "where\n", "greo.invoice_id is null\n", "group by 1,2,3,4) pipd2\n", "\n", "on pipd2.variant_id=pi.variant_id and pipd2.grofers_order_id=pi.sub_order_id\n", "\n", "\n", "--and pi.created_at>='2020-01-01' and pi.created_at<='2020-01-10'\n", "-- and os.install_ts>='2020-01-01' and os.install_ts<='2020-01-10'\n", "group by 1,2,3,4,5,6,7,8,9\n", "order by sub_order_id, variant_id\n", ") c\n", "group by 1,2,3,4,5,6,7,8\n", "\n", "\"\"\"\n", "\n", "forward_invoice_df = pd.read_sql_query(\n", "    sql=forward_invoice_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "\n", "forward_invoice_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["forward_invoice_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df.groupby(\n", "#     [\"outlet_id\", \"reason\", \"sub_order_id\", \"variant_id\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df[\"required_quantity\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"sub_order_id\"] == \"79234652\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df[\"inward_quantity\"].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["forward_invoice_df[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - forward_invoice_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df.groupby(\n", "#     [\"sub_order_id\", \"variant_id\", \"required_quantity\",\"inward_quantity\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[\"diff\"] = (\n", "    forward_invoice_df[\"inward_quantity\"] - forward_invoice_df[\"required_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df[forward_invoice_df[\"diff\"] > 0]"]}, {"cell_type": "markdown", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["# Reverse Pick Up"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "reverse_pickup_query = \"\"\"\n", "SELECT\n", "'INTERNAL REVERSE' as reason,\n", "  oiom.id::text,\n", "  oiom.install_ts as sub_order_created_at,\n", "  internal_order_id,\n", "  original_order_id,\n", "  ooi.product_id::int,\n", "  ooi.quantity,\n", "  --ooi.selling_price,\n", "  ooi.product_title,\n", "  reason_id,\n", "  o.type,\n", "  o.direction,\n", "  o.current_status,\n", "  reason.id,\n", "  message,\n", "  code,\n", "  is_active,\n", "  source\n", "FROM oms_internal_order_mapping oiom\n", "  INNER JOIN  consumer.oms_order o ON oiom.internal_order_id = o.id\n", "  INNER JOIN consumer.oms_reason reason ON oiom.reason_id = reason.id\n", "  INNER JOIN consumer.oms_order_item ooi on ooi.order_id=o.id\n", "  inner join consumer.oms_suborder os on os.order_id=oiom.original_order_id\n", "  WHERE oiom.install_ts>=%(run_period_start)s  and oiom.install_ts<=%(run_period_end)s \n", "  AND\n", "  o.type='InternalReverseOrder'\n", "  and o.current_status!='CANCELLED'\n", "  --and internal_order_id= 49126100\n", "  group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17--,18\n", "\"\"\"\n", "\n", "reverse_pickup_df = pd.read_sql_query(\n", "    sql=reverse_pickup_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "reverse_pickup_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_df.loc[reverse_pickup_df[\"internal_order_id\"] == 51968768]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_item_level_df = pd.merge(\n", "    reverse_pickup_df,\n", "    it_pd_log_df,\n", "    how=\"inner\",\n", "    left_on=\"product_id\",\n", "    right_on=\"product_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_item_level_df[\"quantity_to_be_fetched\"] = (\n", "    reverse_pickup_item_level_df[\"quantity\"]\n", "    * reverse_pickup_item_level_df[\"multiplier\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_item_level_df = reverse_pickup_item_level_df[\n", "    [\n", "        \"reason\",\n", "        \"sub_order_created_at\",\n", "        \"internal_order_id\",\n", "        \"item_id\",\n", "        \"original_order_id\",\n", "        \"quantity_to_be_fetched\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_item_level_df = reverse_pickup_item_level_df.drop_duplicates(keep=\"last\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_item_level_df = reverse_pickup_item_level_df.groupby(\n", "    [\n", "        \"reason\",\n", "        \"sub_order_created_at\",\n", "        \"internal_order_id\",\n", "        \"item_id\",\n", "        \"original_order_id\",\n", "    ],\n", "    as_index=False,\n", ").agg({\"quantity_to_be_fetched\": \"sum\"})\n", "# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df = pd.merge(\n", "    reverse_pickup_item_level_df,\n", "    rpc_product_product_df,\n", "    how=\"inner\",\n", "    left_on=\"item_id\",\n", "    right_on=\"item_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# rpc_product_product_df.loc[rpc_product_product_df[\"item_id\"] == 10001077]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_item_level_df.loc[\n", "#     reverse_pickup_item_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df = reverse_pickup_variant_level_df[\n", "    [\n", "        \"reason\",\n", "        \"sub_order_created_at\",\n", "        \"internal_order_id\",\n", "        \"variant_id\",\n", "        \"item_id\",\n", "        \"original_order_id\",\n", "        \"quantity_to_be_fetched\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# reverse_pickup_variant_level_df.loc[\n", "#     reverse_pickup_variant_level_df[\"internal_order_id\"] == 51968768\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.drop_duplicates(keep=\"last\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.drop_duplicates(keep=\"last\", inplace=True)\n", "# reverse_pickup_variant_level_df.loc[\n", "#     reverse_pickup_variant_level_df[\"internal_order_id\"] == 51968768\n", "# ].sort_values(\"variant_id\", ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_variant_level_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "reverse_pickup_forward_invoice_query = \"\"\"\n", "select \n", "'INTERNAL REVERSE' as reason ,\n", "pi.outlet_id,\n", "internal_order_id as sub_order_id, \n", "rev.install_ts as billed_at,\n", "pipd.variant_id, \n", "pipd.name as item_name, \n", "null as inwarded_at,\n", "0 as inward_quantity,\n", "avg(pipd.selling_price) selling_price\n", "from\n", "(SELECT\n", "  os.id as sub_order_id, internal_order_id, oiom.install_ts\n", "FROM oms_internal_order_mapping oiom\n", "  INNER JOIN  consumer.oms_order o ON oiom.internal_order_id = o.id\n", "  inner join consumer.oms_suborder os on os.order_id=oiom.original_order_id\n", "  WHERE oiom.install_ts>=%(run_period_start)s and oiom.install_ts<=%(run_period_end)s\n", "  AND\n", "  o.type='InternalReverseOrder'\n", "  and o.current_status!='CANCELLED'\n", "  --and internal_order_id= 49126100\n", "  group by 1,2,3) rev\n", "  join pos_pos_invoice pi on rev.sub_order_id=pi.grofers_order_id and pi.invoice_type_id=1\n", "  join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pi.id and variant_id not like 'G%%'\n", "  group by 1,2,3,4,5,6,7,8\n", "\"\"\"\n", "\n", "reverse_pickup_forward_invoice_df = pd.read_sql_query(\n", "    sql=reverse_pickup_forward_invoice_query,\n", "    con=redshift_con,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "reverse_pickup_forward_invoice_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_suborder_variant_level_df = pd.merge(\n", "    reverse_pickup_variant_level_df,\n", "    reverse_pickup_forward_invoice_df,\n", "    left_on=[\"internal_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_suborder_variant_level_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_suborder_variant_level_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_suborder_variant_level_df[\n", "    [\"internal_order_id\", \"variant_id\"]\n", "].drop_duplicates().shape[0] - reverse_pickup_suborder_variant_level_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_suborder_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_orders_variant_level_df = reverse_pickup_suborder_variant_level_df[\n", "    [\n", "        \"reason_x\",\n", "        \"outlet_id\",\n", "        \"sub_order_created_at\",\n", "        \"billed_at\",\n", "        \"internal_order_id\",\n", "        \"variant_id\",\n", "        \"item_name\",\n", "        \"quantity_to_be_fetched\",\n", "        \"selling_price\",\n", "        \"inwarded_at\",\n", "        \"inward_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_orders_variant_level_df.rename(\n", "    columns={\n", "        \"sub_order_created_at\": \"suborder_created_at\",\n", "        \"quantity_to_be_fetched\": \"required_quantity\",\n", "        \"selling_price_x\": \"selling_price\",\n", "        \"internal_order_id\": \"sub_order_id\",\n", "        \"reason_x\": \"reason\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_orders_variant_level_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["reverse_pickup_orders_variant_level_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["doorstep_order_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doorstep_order_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["forward_invoice_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forward_invoice_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df = pd.concat(\n", "    [forward_invoice_df, doorstep_order_df, reverse_pickup_orders_variant_level_df],\n", "    sort=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df[return_orders_df['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df2 = pd.merge(\n", "    return_orders_df,\n", "    item_category_df,\n", "    how=\"inner\",\n", "    left_on=\"variant_id\",\n", "    right_on=\"variant_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_category_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df2.nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df2[return_orders_df2['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df2[return_orders_df2['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# item_category_df.loc[\n", "#     item_category_df[\"variant_id\"] == \"0decd0b8-3ebf-5337-50f6-cf1af23cd6b5\"\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df.loc[return_orders_df[\"sub_order_id\"] == \"77442344\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# forward_invoice_df.loc[forward_invoice_df[\"sub_order_id\"] == \"77442344\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df2.loc[return_orders_df2[\"sub_order_id\"] == \"77442344\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["li = return_orders_df2[\"sub_order_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["li = li.drop_duplicates(keep=\"last\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["li = li.to_list()\n", "# li=return_orders_df2[['sub_order_id']].apply(lambda x: ', '.join(x.astype(str)) if x.dtype=='int64' else ', '.join(\"\\'\"+x.astype(str)+\"\\'\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["li2 = \"','\".join(map(str, li))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df2.loc[return_orders_df2[\"sub_order_id\"] == 52936062]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "good_returns_query = (\n", "    \"\"\"\n", "\n", "\n", "  select  iil.grofers_order_id::int sub_order_id ,iil.variant_id, coalesce(sum(iil.\"delta\"),0) as good_quantity\n", "  from\n", "   consumer.ims_ims_inventory_log iil \n", "   join consumer.ims_ims_inventory_update_type iut ON iil.inventory_update_type_id=iut.id and  iut.group_name in ('return')--, 'HRA updates')\n", "   and iil.grofers_order_id in ('\"\"\"\n", "    + li2\n", "    + \"\"\"')\n", "   join consumer.pos_pos_invoice ppi on ppi.grofers_order_id=iil.grofers_order_id and iil.invoice_id=ppi.invoice_id\n", "  and (\n", "       (iut.is_good_inventory_update=TRUE and iut.good_inventory_operation = '+')\n", "        or (iut.is_inventory_update =TRUE and iut.inventory_operation = '+')\n", "        )\n", "     and iil.grofers_order_id IS NOT NULL\n", "     and iil.grofers_order_id !='' group by 1,2\n", "\n", "\n", "\"\"\"\n", ")\n", "# print(good_returns_query)\n", "\n", "good_returns_df = pd.read_sql_query(sql=good_returns_query, con=redshift_con)\n", "good_returns_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_returns_df[good_returns_df.sub_order_id == 85262788]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# good_returns_df.loc[good_returns_df[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df2.loc[return_orders_df2['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_returns_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["good_returns_df[\"variant_id\"] = good_returns_df[\"variant_id\"].astype(object)\n", "return_orders_df2[\"variant_id\"] = return_orders_df2[\"variant_id\"].astype(object)\n", "good_returns_df[\"sub_order_id\"] = good_returns_df[\"sub_order_id\"].astype(int)\n", "return_orders_df2[\"sub_order_id\"] = return_orders_df2[\"sub_order_id\"].astype(int)\n", "# good_returns_df[\"sub_order_id\"] = pd.to_numeric(good_returns_df[\"sub_order_id\"], downcast='signed')\n", "# return_orders_df2[\"sub_order_id\"] = pd.to_numeric(return_orders_df2[\"sub_order_id\"], downcast='signed')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df2.loc[return_orders_df2[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_returns_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df3 = pd.merge(\n", "    return_orders_df2,\n", "    good_returns_df,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df2.loc[return_orders_df2[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_returns_df[good_returns_df[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "bad_returns_query = (\n", "    \"\"\"\n", "\n", "\n", "\n", "  select iil.grofers_order_id::int sub_order_id, iil.variant_id, coalesce(sum(iil.\"delta\"),0) bad_quantity\n", "  from\n", "  consumer.ims_ims_inventory_log iil \n", "   join consumer.ims_ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id  \n", "   and iut.group_name in ('return')--, 'HRA updates')\n", "   and iil.grofers_order_id in ('\"\"\"\n", "    + li2\n", "    + \"\"\"')\n", "  and iut.is_bad_inventory_update='true'\n", "         and iut.bad_inventory_operation='+'\n", "   join consumer.pos_pos_invoice ppi on ppi.grofers_order_id=iil.grofers_order_id and iil.invoice_id=ppi.invoice_id\n", "  group by 1,2\n", "  \n", "\n", "\n", "\"\"\"\n", ")\n", "# print(good_returns_query)\n", "\n", "bad_returns_df = pd.read_sql_query(sql=bad_returns_query, con=redshift_con)\n", "bad_returns_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_returns_df[bad_returns_df[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df3[return_orders_df3['sub_order_id']==79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df4 = pd.merge(\n", "    return_orders_df3,\n", "    bad_returns_df,\n", "    how=\"left\",\n", "    left_on=[\"sub_order_id\", \"variant_id\"],\n", "    right_on=[\"sub_order_id\", \"variant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(return_orders_df3), len(return_orders_df4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df3.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[return_orders_df4[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["bad_returns_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4=return_orders_df4.fillna(value=0)\n", "# import numpy as np\n", "# return_orders_df4 = return_orders_df4[''].replace(np.nan, '', regex=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4.fillna(value=0,inplace=True, limit=None, downcast=None, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df4[[\"sub_order_id\", \"variant_id\"]].drop_duplicates().shape[\n", "    0\n", "] - return_orders_df4.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4[(return_orders_df4.sub_order_id == 79055155)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4.groupby(\n", "#     [\"outlet_id\", \"reason\", \"sub_order_id\", \"variant_id\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.loc[return_orders_df4[\"sub_order_id\"] == 79234652]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\")\n", "\n", "dispatch_query = (\n", "    \"\"\"\n", "select \n", "bom.order_id,\n", "bom.batch_id,\n", "convert_timezone('Asia/Kolkata',scheduled_at) scheduled_at,\n", "max(end_time + interval '5.5 hour') as dispatch_time\n", "from \n", "ims_order_details iod \n", "left join \n", "warehouse_location_batch_order_mapping bom on iod.order_id = bom.order_id\n", "inner join lake_logex.batch b on bom.batch_id = b.external_criteria\n", "left join lake_logex.activity a on a.batch_id = b.id and a.activity_type = 'DISPATCH' \n", "where \n", "bom.order_id in ('\"\"\"\n", "    + li2\n", "    + \"\"\"')\n", "group by 1,2,3\n", "  \n", "\"\"\"\n", ")\n", "# print(good_returns_query)\n", "\n", "dispatch_status_df = pd.read_sql_query(sql=dispatch_query, con=redshift_con)\n", "dispatch_status_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"sub_order_id\"] = return_orders_df4.sub_order_id.astype(int)\n", "dispatch_status_df[\"order_id\"] = dispatch_status_df.order_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4 = return_orders_df4.merge(\n", "    dispatch_status_df, how=\"left\", left_on=[\"sub_order_id\"], right_on=[\"order_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.drop([\"order_id\", \"batch_id\"], 1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4 = return_orders_df4.assign(event_r=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4['inward_to_good_bad_diff']=return_orders_df4['inward_quantity']-\n", "# return_orders_df4['good_quantity']-return_orders_df4['bad_quantity']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4.loc[\n", "#     return_orders_df4.reason == \"REPROCURED\", \"event_r\"\n", "# ] = \"RESCHEDULED\"\n", "# return_orders_df4.loc[return_orders_df4.reason == \"CANCELLED\", \"event_r\"] = \"CANCELLED\"\n", "# # df.loc[df.AAA >= 5, 'BBB'] = -1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df4.sample(15)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# return_orders_df4.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["return_orders_df4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.loc[return_orders_df4[\"sub_order_id\"] == 79369590]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"outlet_id\"] = return_orders_df4.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df4['suborder_created_at']=return_orders_df4.suborder_created_at.dt.strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"billed_at\"] = pd.to_datetime(return_orders_df4.billed_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"inwarded_at\"] = pd.to_datetime(return_orders_df4.inwarded_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"inwarded_at\"] = pd.to_datetime(return_orders_df4.inwarded_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"sub_order_id\"] = return_orders_df4.sub_order_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"good_quantity\"].fillna(0, inplace=True)\n", "return_orders_df4[\"good_quantity\"] = return_orders_df4.good_quantity.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"bad_quantity\"].fillna(0, inplace=True)\n", "return_orders_df4[\"bad_quantity\"] = return_orders_df4.bad_quantity.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"inward_quantity\"].fillna(0, inplace=True)\n", "return_orders_df4[\"inward_quantity\"] = return_orders_df4.inward_quantity.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"required_quantity\"].fillna(0, inplace=True)\n", "return_orders_df4[\"required_quantity\"] = return_orders_df4.required_quantity.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"selling_price\"].fillna(0, inplace=True)\n", "return_orders_df4[\"selling_price\"] = return_orders_df4.selling_price.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4[\"scheduled_at\"] = pd.to_datetime(return_orders_df4.scheduled_at)\n", "return_orders_df4[\"dispatch_time\"] = pd.to_datetime(return_orders_df4.dispatch_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df4['inward_quantity'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x=return_orders_df4.loc[return_orders_df4['sub_order_id']==79369590]\n", "# x=return_orders_df4.loc[return_orders_df4['reason']=='DOORSTEP RETURN']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4 = return_orders_df4.drop([\"diff\"], 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_orders_df4.to_csv('new_df4.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_orders_df4.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"reason\", \"type\": \"varchar\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"suborder_created_at\", \"type\": \"varchar\"},\n", "    {\"name\": \"billed_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"sub_order_id\", \"type\": \"int\"},\n", "    {\"name\": \"variant_id\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"inwarded_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"required_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"selling_price\", \"type\": \"int\"},\n", "    {\"name\": \"inward_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\"},\n", "    {\"name\": \"good_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"bad_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"scheduled_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"dispatch_time\", \"type\": \"timestamp\"},\n", "    # {\"name\": \"event_r\", \"type\": \"varchar\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"returns_at_facility\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"sub_order_id\", \"variant_id\"],\n", "    \"sortkey\": [\"sub_order_id\"],\n", "    \"incremental_key\": \"sub_order_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# pb.to_redshift(return_orders_df4, **kwargs)\n", "pb.to_redshift(return_orders_df4, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}, "papermill": {"duration": 285.389574, "end_time": "2020-01-15T07:08:43.144152", "environment_variables": {}, "exception": true, "input_path": "/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets/notebook.ipynb", "output_path": "s3://grofers-prod-dse/airflow/notebooks/warehouse_returns_etl_returns_at_outlets_v1/scheduled__2020-01-15T06:00:00+00:00/output-2020-01-15.ipynb", "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/warehouse/returns/etl/returns_at_outlets", "run_period_end": "2020-01-15", "run_period_start": "2019-12-16"}, "start_time": "2020-01-15T07:03:57.754578", "version": "1.0.0"}}, "nbformat": 4, "nbformat_minor": 4}