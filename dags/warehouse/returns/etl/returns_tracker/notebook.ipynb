{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "from dateutil import parser"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh_id = \"1cc6V7xH2MBGHr4-Z44faHZ4w1Iha95dwxQJQVULwE9A\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh_id_olddata = \"1u0MKW-DnlW3r5BFaC0KA5B_ov75IXh5b20iSyy98dok\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select * from metrics.expiry_returns_tracker_updated\"\"\"\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "data = pd.read_sql_query(sql=query, con=redshift)\n", "old_data_g1 = data[data[\"fc\"] == \"G1\"]\n", "old_data_g3 = data[data[\"fc\"] == \"G3\"]\n", "old_data_g5 = data[data[\"fc\"] == \"G5\"]\n", "old_data_jhil = data[data[\"fc\"] == \"Jhilmil\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(old_data_g1, sh_id_olddata, \"G1_OldData\")\n", "pb.to_sheets(old_data_g3, sh_id_olddata, \"G3_OldData\")\n", "pb.to_sheets(old_data_g5, sh_id_olddata, \"G5_OldData\")\n", "pb.to_sheets(old_data_jhil, sh_id_olddata, \"Jhilmil\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Reading a snapshot of data from sheets & processing it"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_g1 = pb.from_sheets(sh_id, \"G1_Outlet\")\n", "data_g3 = pb.from_sheets(sh_id, \"G3_Outlet\")\n", "data_g5 = pb.from_sheets(sh_id, \"G5_Outlet\")\n", "data_j = pb.from_sheets(sh_id, \"Jhilmil\")\n", "\n", "data_old = pd.concat([data_g1, data_g3, data_g5, data_j], axis=0, sort=False)\n", "data_old.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fc_mapping = pb.from_sheets(\n", "    \"1dm6KDo1EcapR4stsPFclq6UD__kuhO6X4_Pu42o7-Qc\", \"Facility <> Station mapping\"\n", ")\n", "fc_mapping[\"facility_name\"] = fc_mapping[\"facility_name\"].replace(\n", "    [\"Super Store Gurgaon G4-B - Warehouse\"], \"G5\"\n", ")\n", "fc_mapping[\"facility_name\"] = fc_mapping[\"facility_name\"].replace(\n", "    [\"Super Store Gurgaon G1 - Warehouse\"], \"G1\"\n", ")\n", "fc_mapping[\"facility_name\"] = fc_mapping[\"facility_name\"].replace(\n", "    [\"Super Store Gurgaon G3 - Warehouse\"], \"G3\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["G1_list = tuple(\n", "    filter(\n", "        lambda x: x is not None,\n", "        fc_mapping[fc_mapping[\"facility_name\"] == \"G1\"][\"station_name\"].to_list(),\n", "    )\n", ")\n", "G3_list = tuple(\n", "    filter(\n", "        lambda x: x is not None,\n", "        fc_mapping[fc_mapping[\"facility_name\"] == \"G3\"][\"station_name\"].to_list(),\n", "    )\n", ")\n", "G5_list = tuple(\n", "    filter(\n", "        lambda x: x is not None,\n", "        fc_mapping[fc_mapping[\"facility_name\"] == \"G5\"][\"station_name\"].to_list(),\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["G1_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Jhilmil_list = tuple(filter(None, [\"DL51\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"\n", "SELECT ri.outlet_id AS OutletID,\n", "       ri.l1_reason AS Reason,\n", "       ri.station as Station,\n", "       (case when ri.station in {} then 'G1'\n", "            when ri.station in {} then 'G3'\n", "            when ri.station in {} then 'G5'\n", "            when ri.station in ('DL51') then 'Jhilmil'\n", "            else Null end) as FC,\n", "       ri.order_id_x AS ParentID,\n", "       ri.whreturntaskid AS ReturnTaskID,\n", "       ri.sub_order_id AS SubOrderId,\n", "       date(ri.scheduleddate) AS ScheduledDate,\n", "       ri.item_id AS ItemId,\n", "       ri.item_name AS ItemName,\n", "       ri.expected_quantity AS ExpectedQuantity,\n", "       ri.inward_quantity AS inward_quantity_system,\n", "       ri.expectedvalue AS ExpectedValue\n", "FROM metrics.returns_wh_lm_integration ri\n", "--LEFT JOIN lake_warehouse_location.warehouse_pick_list_item wpli ON (wpli.order_id = ri.sub_order_id)\n", "WHERE scheduleddate::date = current_date - 2\n", "  AND ri.outlet_id = 1098\n", "ORDER BY ScheduledDate ASC\n", "-- select distinct outlet_id,facility_name from metrics.returns_wh_lm_integration limit 100\n", "-- select distinct backend_merchant_id from metrics.returns_wh_lm_integration where outlet_id_x = 520\n", "\"\"\".format(\n", "    G1_list, G3_list, G5_list\n", ")\n", "Redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "data_new = pd.read_sql_query(sql=Query, con=Redshift)\n", "data_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(data_new[data_new.station == 'DL51'], sh_id, \"Jhilmil\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new.groupby(\"fc\")[\"station\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.concat([data_old, data_new], axis=0, sort=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import timedelta, date\n", "import datetime\n", "\n", "dt = datetime.datetime.today() - datetime.timedelta(days=3)\n", "dt1 = datetime.datetime.today() - datetime.timedelta(days=4)\n", "dt2 = datetime.datetime.today() - datetime.timedelta(days=5)\n", "dt = dt.strftime(\"%Y-%m-%d\")\n", "dt1 = dt1.strftime(\"%Y-%m-%d\")\n", "dt2 = dt2.strftime(\"%Y-%m-%d\")\n", "print(str(dt), str(dt1), str(dt2))\n", "time.sleep(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.scheduleddate.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_snap = data[\n", "    (data[\"receivingstatus\"] != \"\")\n", "    & (data[\"inward status\"] != \"\")\n", "    & ((data[\"processed?\"] == \"YES\") | (data[\"processed?\"] == \"Yes\"))\n", "    & (\n", "        (data[\"scheduleddate\"] != str(dt))\n", "        | (data[\"scheduleddate\"] != str(dt1))\n", "        | (data[\"scheduleddate\"] != str(dt2))\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_snap.scheduleddate.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pushback = pd.concat([data, data_snap], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape, data_snap.shape, data_pushback.shape,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pushback = data_pushback.drop_duplicates(keep=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_pushback.shape, data_new.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data_pushback[data_pushback[\"fc\"] == \"G1\"], sh_id, \"G1_Outlet\")\n", "pb.to_sheets(data_pushback[data_pushback[\"fc\"] == \"G3\"], sh_id, \"G3_Outlet\")\n", "pb.to_sheets(data_pushback[data_pushback[\"fc\"] == \"G5\"], sh_id, \"G5_Outlet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data_pushback[data_pushback[\"fc\"] == \"Jhilmil\"], sh_id, \"Jhilmil\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_snap[\"key\"] = (\n", "    data_snap[\"outletid\"].astype(str)\n", "    + \"|\"\n", "    + data_snap[\"scheduleddate\"].astype(str)\n", "    + \"|\"\n", "    + data_snap[\"suborderid\"].astype(str)\n", "    + \"|\"\n", "    + data_snap[\"itemid\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qu = \"\"\"select * from metrics.expiry_returns_tracker_updated\"\"\"\n", "dat = pd.read_sql_query(sql=qu, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_snap = data_snap[dat.columns]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_snap.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dat.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "        \"datetime64[ns, UTC]\": \"timestamp\",\n", "    }\n", "    return [\n", "        {\"name\": key, \"type\": ref_dict[value], \"description\": key}\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "def metadata_dict(df):\n", "    return df.dtypes.apply(lambda x: x.name).to_dict()\n", "\n", "\n", "column_dtypes = redshift_schema(data_snap)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"expiry_returns_tracker_updated\",\n", "    \"table_description\": \"returns_tracker\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"key\"],\n", "    \"sortkey\": [\"outletid\"],\n", "    \"incremental_key\": \"ScheduledDate\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(data_snap, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary Part"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_g1_final = pd.concat([data_g1, old_data_g1], axis=0, sort=False)\n", "data_g3_final = pd.concat([data_g3, old_data_g3], axis=0, sort=False)\n", "data_g5_final = pd.concat([data_g5, old_data_g5], axis=0, sort=False)\n", "data_jhilmil = pd.concat([data_j, old_data_jhil], axis=0, sort=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_g1_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summ = data_g1_final[data_g1_final[\"scheduleddate\"] == date]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_g5_final[data_g5_final[\"scheduleddate\"] == \"2021-01-01\"][\n", "    \"processed?\"\n", "].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def summary(data_g1_final):\n", "    summary_ = pd.DataFrame()\n", "    for date in data_g1_final[\"scheduleddate\"].unique():\n", "        summ = data_g1_final[data_g1_final[\"scheduleddate\"] == date]\n", "        suborders = summ.suborderid.nunique()\n", "        item_ids = summ.itemid.count()\n", "        if \"Received Sealed and sent to DD\" in summ.receivingstatus.value_counts():\n", "            receivedSealed = summ.receivingstatus.value_counts()[\n", "                \"Received Sealed and sent to DD\"\n", "            ]\n", "        else:\n", "            receivedSealed = 0\n", "        if \"Received Unsealed and sent to DD\" in summ.receivingstatus.value_counts():\n", "            receivedUnsealed = summ.receivingstatus.value_counts()[\n", "                \"Received Unsealed and sent to DD\"\n", "            ]\n", "        else:\n", "            receivedUnsealed = 0\n", "        if \"Not received from DD\" in summ.receivingstatus.value_counts():\n", "            notReceived = summ.receivingstatus.value_counts()[\"Not received from DD\"]\n", "        elif \"Not received from LM\" in summ.receivingstatus.value_counts():\n", "            notReceived = summ.receivingstatus.value_counts()[\"Not received from LM\"]\n", "        else:\n", "            notReceived = 0\n", "        if \"Others\" in summ.receivingstatus.value_counts():\n", "            others = summ.receivingstatus.value_counts()[\"Others\"]\n", "        else:\n", "            others = 0\n", "        if \"Received\" in summ[\"inward status\"].value_counts():\n", "            received_inward = summ[\"inward status\"].value_counts()[\"Received\"]\n", "        else:\n", "            received_inward = 0\n", "        if \"Not Received\" in summ[\"inward status\"].value_counts():\n", "            not_received_inward = summ[\"inward status\"].value_counts()[\"Not Received\"]\n", "        else:\n", "            not_received_inward = 0\n", "        if \"Others\" in summ[\"inward status\"].value_counts():\n", "            others_inward = summ[\"inward status\"].value_counts()[\"Others\"]\n", "        else:\n", "            others_inward = 0\n", "        if \"YES\" in summ[\"processed?\"].value_counts():\n", "            processed = summ[\"processed?\"].value_counts()[\"YES\"]\n", "        else:\n", "            processed = 0\n", "        if \"Yes\" in summ[\"processed?\"].value_counts():\n", "            processed += summ[\"processed?\"].value_counts()[\"Yes\"]\n", "        else:\n", "            processed += 0\n", "        if \"yes\" in summ[\"processed?\"].value_counts():\n", "            processed += summ[\"processed?\"].value_counts()[\"yes\"]\n", "        else:\n", "            processed += 0\n", "        lastRecord = {\n", "            \"ScheduledDate\": [date],\n", "            \"Total Suborder IDs\": suborders,\n", "            \"Total Item IDs\": item_ids,\n", "            \"warehouse_status\": receivedSealed\n", "            + receivedUnsealed\n", "            + notReceived\n", "            + others,\n", "            \"receivedSealed\": receivedSealed,\n", "            \"receivedUnsealed\": receivedUnsealed,\n", "            \"notReceived\": notReceived,\n", "            \"others\": others,\n", "            \"inward_status\": received_inward + not_received_inward + others_inward,\n", "            \"InwardReceived\": received_inward,\n", "            \"InwardNotReceived\": not_received_inward,\n", "            \"InwardOthers\": others_inward,\n", "            \"Processed\": processed,\n", "        }\n", "        data = pd.DataFrame(lastRecord)\n", "        summary_ = pd.concat([summary_, data], axis=0)\n", "    return summary_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# summary_g1 = []\n", "# lastRecord = {\n", "#     \"ScheduledDate\": None,\n", "#     \"Total Suborder IDs\": 0,\n", "#     \"Total Item IDs\": 0,\n", "#     \"receivedSealed\": 0,\n", "#     \"receivedUnsealed\": 0,\n", "#     \"notReceived\": 0,\n", "#     \"others\": 0,\n", "#     \"InwardReceived\": 0,\n", "#     \"InwardNotReceived\": 0,\n", "#     \"InwardOthers\": 0\n", "# }\n", "# for k, row in data_g1_final.iterrows():\n", "#     if (lastRecord[\"ScheduledDate\"] != None and lastRecord[\"ScheduledDate\"] != row.scheduleddate):\n", "#         summary_g1.append(lastRecord)\n", "#         lastRecord = {\n", "#             \"ScheduledDate\": None,\n", "#             \"Total Suborder IDs\": 0,\n", "#             \"Total Item IDs\": 0,\n", "#             \"receivedSealed\": 0,\n", "#             \"receivedUnsealed\": 0,\n", "#             \"notReceived\": 0,\n", "#             \"others\": 0,\n", "#             \"InwardReceived\": 0,\n", "#             \"InwardNotReceived\": 0,\n", "#             \"InwardOthers\": 0\n", "#         }\n", "#     else:\n", "#         if lastRecord[\"ScheduledDate\"] == None:\n", "#             lastRecord[\"ScheduledDate\"] = row.scheduleddate\n", "#         lastRecord[\"Total Suborder IDs\"] += 1\n", "#         lastRecord[\"Total Item IDs\"] += 1\n", "#         if row.ReceivingStatus == 'Not Received':\n", "#             lastRecord[\"notReceived\"] += 1\n", "#         elif row.ReceivingStatus == 'Received Sealed':\n", "#             lastRecord[\"receivedSealed\"] += 1\n", "#         elif row.ReceivingStatus == 'Received Unsealed':\n", "#             lastRecord[\"receivedUnsealed\"] += 1\n", "#         elif row.ReceivingStatus == 'Others':\n", "#             lastRecord[\"others\"] += 1\n", "#         elif row[\"Inward Status\"] == 'Received':\n", "#             lastRecord[\"InwardReceived\"] += 1\n", "#         elif row[\"Inward Status\"] == 'Not Received':\n", "#             lastRecord[\"InwardNotReceived\"] =+ 1\n", "#         else:\n", "#             lastRecord[\"InwardOthers\"] =+ 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(summary(data_g5_final), sh_id, \"G5_Summary\")\n", "pb.to_sheets(summary(data_g3_final), sh_id, \"G3_Summary\")\n", "pb.to_sheets(summary(data_g1_final), sh_id, \"G1_Summary\")\n", "pb.to_sheets(summary(data_jhilmil), sh_id, \"Jhilmil_Summary\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}