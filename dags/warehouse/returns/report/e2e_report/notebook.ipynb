{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pip\n", "\n", "\n", "def import_or_install(package):\n", "    try:\n", "        __import__(package)\n", "    except ImportError:\n", "        pip.main([\"install\", package])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import_or_install(\"Babel\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb\n", "\n", "Redshift = pb.get_connection(\"redshift\")\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "from datetime import date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "from babel.numbers import format_currency\n", "\n", "tz = pytz.timezone(\"Asia/Kolkata\")\n", "start = datetime.now(tz).replace(day=1).strftime(\"%Y-%m-%d\")\n", "end = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "Prev = (datetime.now(tz) - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "today = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "startperiod = (datetime.now(tz) - <PERSON><PERSON><PERSON>(days=6)).date()\n", "prevday = (datetime.now(tz) - <PERSON><PERSON><PERSON>(days=1)).date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# todayDate = datetime.date.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (datetime.now(tz)).day <= 2:\n", "    today = datetime.now(tz)\n", "    start = (today - relativedelta(months=1)).replace(day=1).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start, end, today, startperiod, Prev, prevday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Symbol = u\"\\u20B9\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = 1000000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"{0:,2,3.2f}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import decimal\n", "# def currencyInIndiaFormat(n):\n", "#     d = decimal.Decimal(str(n))\n", "#     if d.as_tuple().exponent < -2:\n", "#         s = str(n)\n", "#     else:\n", "#         s = \"{0:3.2f}\".format(n)\n", "#     return (s)\n", "# currencyInIndiaFormat(1000000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Overall_Diff_Query = \"\"\"\n", "       select \n", "       scheduleddate::date,\n", "       sum(expectedvalue) as ExpectedValue,\n", "       sum(inwardvalue) as InwardValue,\n", "       sum(expectedvalue)-sum(inwardvalue) as OverallDiff,\n", "       sum(case when reason='MISSIGN' then expectedvalue end) - sum(case when reason='MISSIGN' then inwardvalue end) as DiffFrom_ItemMissing\n", "from metrics.returns_wh_lm_integration \n", "where  scheduleddate::date between %(start)s and %(end)s\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff = pd.read_sql_query(\n", "    sql=Overall_Diff_Query, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff.sort_values(\"scheduleddate\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff_MTD = OverallDiff.agg(\n", "    {\n", "        \"expectedvalue\": sum,\n", "        \"inwardvalue\": sum,\n", "        \"overalldiff\": sum,\n", "        \"difffrom_itemmissing\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff_MTD = OverallDiff_MTD.to_frame().transpose()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff_MTD[\"scheduleddate\"] = \"MTD\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff = OverallDiff[OverallDiff[\"scheduleddate\"] >= startperiod]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff[\"scheduleddate\"] = list(\n", "    map(lambda x: x.strftime(\"%Y-%m-%d\"), OverallDiff[\"scheduleddate\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff = pd.concat([OverallDiff, OverallDiff_MTD], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff = OverallDiff[\n", "    [\n", "        \"scheduleddate\",\n", "        \"expectedvalue\",\n", "        \"inwardvalue\",\n", "        \"overalldiff\",\n", "        \"difffrom_itemmissing\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff[\"expectedvalue\"] = list(\n", "    map(lambda x: round(x), OverallDiff[\"expectedvalue\"])\n", ")\n", "OverallDiff[\"inwardvalue\"] = list(map(lambda x: round(x), OverallDiff[\"inwardvalue\"]))\n", "OverallDiff[\"overalldiff\"] = list(map(lambda x: round(x), OverallDiff[\"overalldiff\"]))\n", "OverallDiff[\"difffrom_itemmissing\"] = list(\n", "    map(lambda x: round(x), OverallDiff[\"difffrom_itemmissing\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH = \"\"\"\n", "       select \n", "       scheduleddate::date,\n", "       sum(expectedvalue) as Expected<PERSON><PERSON><PERSON>,\n", "       sum(case when wh_type='with_oid' then dp_hfe_quantity_actual*selling_price else 0 end) as  wh_expectedvalue_task,\n", "       sum(case when wh_type='with_oid' then inwardvalue else 0 end) as wh_inwardedvalue_task,\n", "       sum(case when wh_type='with_oid' and withintime=1 then inwardvalue else 0 end) as within_time_inwardedvalue,\n", "       sum(case when wh_type!='with_oid' then inwardvalue else 0 end) as wh_inwardedwithout_task,\n", "       sum(inwardvalue) as InwardV<PERSON>ueOverall,\n", "       sum(expectedvalue) - sum(inwardvalue) as OverallDiff,\n", "       sum(case when wh_type='with_oid' and withintime=0 then dp_hfe_quantity_actual*selling_price else 0 end) -sum(case when wh_type='with_oid' and withintime=0 then inwardvalue else 0 end) as not_in_time_diff,\n", "       sum(case when dispatch_time is null then expectedvalue else 0 end) - sum(case when dispatch_time is null then inwardvalue else 0 end) as NotDispatched_Diff\n", "from metrics.returns_wh_lm_integration \n", "where  scheduleddate::date between %(start)s and %(end)s\n", "and station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5') \n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = pd.read_sql_query(\n", "    sql=E2E_View_WH, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data.round()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GRN Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GRNQuery = \"\"\"With GRN as (\n", "SELECT iii.outlet_id,\n", "       outlet_name,\n", "       rpp.name,\n", "       rpp.item_id,\n", "       iisd.variant_id,\n", "       rtgn.return_task_id,\n", "       date(iii.created_at) as created_at,\n", "       sum(iisd.\"delta\"::int) AS Total_quantity,\n", "       SUM (CASE\n", "                WHEN piil.inventory_update_type_id IN (87,\n", "                                                       88,\n", "                                                       89) THEN (iisd.\"delta\")::int\n", "            END) AS Damage\n", "FROM  lake_ims.view_ims_inward_invoice iii\n", "JOIN  lake_ims.ims_inventory_stock_details iisd ON iisd.grn_id=iii.grn_id\n", "JOIN lake_rpc.product_product rpp ON rpp.variant_id=iisd.variant_id\n", "JOIN  lake_ims.ims_inventory_log piil ON piil.inventory_update_id::varchar=iisd.inventory_update_id::varchar\n", "left join lake_warehouse_location.return_task_grn rtgn on rtgn.grn_id=iisd.grn_id\n", "WHERE date(iii.created_at) BETWEEN  %(start)s and %(end)s\n", "  AND iii.source_type=3\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6,\n", "         7\n", "),         \n", "LandingPrice AS\n", "  (SELECT wlp.item_id,\n", "          wlp.outlet_id,\n", "          avg(wlp.landing_price) landing_price\n", "   FROM consumer.weighted_landing_price wlp\n", "   JOIN\n", "     (SELECT item_id,\n", "             outlet_id,\n", "             max(dt_ist) dt_ist\n", "      FROM consumer.weighted_landing_price\n", "      GROUP BY 1,2) a ON a.dt_ist=wlp.dt_ist\n", "   AND wlp.item_id=a.item_id and a.outlet_id=wlp.outlet_id\n", "   GROUP BY 1,2)\n", "\n", "Select \n", "       created_at,\n", "       sum(TotalQty*LandingPrice) as TotalGRN,\n", "       sum(Damage*LandingPrice) as DamageAmount,\n", "       sum(WithTaskGRN*LandingPrice) as WithTaskGRN,\n", "       sum(WithoutTaskGRN*LandingPrice) as WithoutTaskGRN\n", "From       \n", "(\n", "Select    a.outlet_id,\n", "          a.outlet_name,\n", "          a.item_id,\n", "          a.variant_id,\n", "          a.created_at,\n", "          o.facility_id,\n", "          f.name as facilityname,\n", "          sum(case when return_task_id is not null then Total_quantity else 0 end) as WithTaskGRN,\n", "          sum(case when return_task_id is null then Total_quantity else 0 end) as WithoutTaskGRN,\n", "          sum(Total_quantity) as TotalQty,\n", "          sum(Damage) as Damage,\n", "          avg(landing_price) as LandingPrice\n", "From GRN a left join LandingPrice b on a.item_id=b.item_id and a.outlet_id=b.outlet_id\n", "left join lake_retail.console_outlet o on a.outlet_id= o.id\n", "left join lake_retail.console_location l on o.tax_location_id = l.id\n", "left join lake_crates.facility f on o.facility_id = f.id\n", "group by 1,2,3,4,5,6,7\n", ") Group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GRN = pd.read_sql_query(sql=GRNQuery, con=Redshift, params={\"start\": start, \"end\": end})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GRN"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = pd.merge(\n", "    E2E_View_WH_Data, GRN, how=\"left\", left_on=\"scheduleddate\", right_on=\"created_at\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = E2E_View_WH_Data.round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data[\"inwardvalueoverall\"] = (\n", "    E2E_View_WH_Data[\"withtaskgrn\"] + E2E_View_WH_Data[\"inwardvalueoverall\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data[\"wh_diff\"] = (\n", "    E2E_View_WH_Data[\"notdispatched_diff\"] + E2E_View_WH_Data[\"not_in_time_diff\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data[\"overalldiff\"] = (\n", "    E2E_View_WH_Data[\"overalldiff\"] - E2E_View_WH_Data[\"totalgrn\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data[\"lm_diff\"] = (\n", "    E2E_View_WH_Data[\"overalldiff\"] - E2E_View_WH_Data[\"wh_diff\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data[\"grn\"] = E2E_View_WH_Data[\"withouttaskgrn\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = E2E_View_WH_Data[\n", "    [\n", "        \"scheduleddate\",\n", "        \"wh_expectedvalue_task\",\n", "        \"wh_inwardedvalue_task\",\n", "        \"withtaskgrn\",\n", "        \"within_time_inwardedvalue\",\n", "        \"wh_inwardedwithout_task\",\n", "        \"inwardvalueoverall\",\n", "        \"grn\",\n", "        \"overalldiff\",\n", "        \"wh_diff\",\n", "        \"lm_diff\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data_MTD = E2E_View_WH_Data.agg(\n", "    {\n", "        \"wh_expectedvalue_task\": sum,\n", "        \"wh_inwardedvalue_task\": sum,\n", "        \"withtaskgrn\": sum,\n", "        \"within_time_inwardedvalue\": sum,\n", "        \"wh_inwardedwithout_task\": sum,\n", "        \"inwardvalueoverall\": sum,\n", "        \"grn\": sum,\n", "        \"overalldiff\": sum,\n", "        \"wh_diff\": sum,\n", "        \"lm_diff\": sum,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data_MTD = E2E_View_WH_Data_MTD.to_frame().transpose()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = E2E_View_WH_Data[E2E_View_WH_Data[\"scheduleddate\"] >= startperiod]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data_MTD[\"scheduleddate\"] = \"MTD\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data = pd.concat([E2E_View_WH_Data, E2E_View_WH_Data_MTD], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_WH_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM = \"\"\"with gsp_dp as (\n", "with gsp as ( select scheduleddate , sum(expected_value) as gsp_expected_value , sum(collected_value ) as gsp_collected_value\n", "            from (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as GSP_Name,\n", "        employee_id ,\n", "        delivery_partner_lm as delivery_partner,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(gsp_dp_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and  returning_entity in ('GSP', 'FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    where station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", ") a \n", "group by 1),\n", "\n", "dp as (\n", "with direct_pendency as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as DP_Name,\n", "        employee_id as dp_employee_id,\n", "        returning_entity,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('DP')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    ),\n", "    \n", "returned as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        gsp_dp_collecting_entity_name as dp_name,\n", "        gsp_dp_collecting_entity_employee_id as dp_employee_id,\n", "        'GSP' as returning_entity,\n", "        sum(gsp_dp_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and gsp_dp_quantity_actual is not null\n", "    and returning_entity in ('GSP','FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a\n", ")\n", "select scheduleddate , sum(expected_value) as dp_expected_value , sum(collected_value) dp_collected_value from (\n", "select * from direct_pendency\n", "union \n", "select * from returned\n", ") where station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "group by 1\n", ")\n", "select * from (\n", "select gsp.scheduleddate , gsp_expected_value , gsp_collected_value , dp_expected_value , dp_collected_value\n", "from gsp\n", "left join dp on gsp.scheduleddate = dp.scheduleddate\n", "where gsp.scheduleddate between current_date -6 and current_date -2\n", "union \n", "select current_date -1 as scheduleddate , sum(gsp_expected_value) gsp_expected_value,\n", "        sum(gsp_collected_value) as gsp_collected_value, \n", "        sum(dp_expected_value) as dp_expected_value,\n", "        sum(dp_collected_value) as dp_collected_value\n", "    from gsp\n", "left join dp on gsp.scheduleddate = dp.scheduleddate\n", "group by 1\n", ") order by 1\n", ") ,\n", "\n", "offline_ledger as (\n", "select a.*, 0 as dp_salary_settled from (\n", "select scheduleddate, Coalesce(sum(gsp_dp_offline_settled),0) gsp_dp_offline_settled,\n", "        Coalesce(sum(gsp_salary_settled),0) gsp_salary_settled,\n", "        Coalesce(sum(dp_hfe_offline_settled),0) dp_hfe_offline_settled\n", "        from (\n", "select scheduleddate :: date ,sub_order_id, max (gsp_forgiven) as gsp_dp_offline_settled,\n", "        max(gsp_salary_settled) as gsp_salary_settled,\n", "        max(dp_forgiven) as dp_hfe_offline_settled\n", "        from metrics.returns_wh_lm_integration \n", "       where  scheduleddate::date between %(start)s and %(end)s\n", "        and returning_entity is not null\n", "        group by 1,2\n", "        ) a where scheduleddate between current_date -6 and current_date -2\n", "        group by 1\n", "    union\n", "    select current_date -1 as scheduleddate , a.*\n", "    from(\n", "    select Coalesce(sum(gsp_dp_offline_settled),0) gsp_dp_offline_settled,\n", "        Coalesce(sum(gsp_salary_settled),0) gsp_salary_settled,\n", "        Coalesce(sum(dp_hfe_offline_settled),0) dp_hfe_offline_settled\n", "        from(\n", "select  scheduleddate:: date ,sub_order_id, max (gsp_forgiven) as gsp_dp_offline_settled,\n", "        max(gsp_salary_settled) as gsp_salary_settled,\n", "        max(dp_forgiven) as dp_hfe_offline_settled\n", "        from metrics.returns_wh_lm_integration \n", "      where  scheduleddate::date between %(start)s and %(end)s\n", "        and returning_entity is not null\n", "        group by 1,2\n", "        )) a \n", ") a  order by 1\n", "),\n", "\n", "sub_orders as(\n", "select * from (\n", "select scheduleddate :: date,\n", "        count(distinct case when flag_dispatch = 'DISPATCHED' then sub_order_id end) as dispatched_orders,\n", "        count(distinct case when flag_dispatch = 'DISPATCHED' and returning_entity is not null then sub_order_id end) \n", "        as task_dispatch_orders,\n", "        (task_dispatch_orders / dispatched_orders)*100 as percentage_suborders\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "    group by 1 ) where scheduleddate between current_date -6 and current_date -2\n", "union \n", "select current_date- 1 as scheduleddate,\n", "        count(distinct case when flag_dispatch = 'DISPATCHED' then sub_order_id end) as dispatched_orders,\n", "        count(distinct case when flag_dispatch = 'DISPATCHED' and returning_entity is not null then sub_order_id end) \n", "        as task_dispatch_orders,\n", "        (task_dispatch_orders / dispatched_orders)*100 as percentage_suborders\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", ")\n", "\n", "select scheduleddate , percentage_suborders,\n", "        gsp_expected_value, gsp_collected_value , gsp_dp_offline_settled+gsp_salary_settled as gsp_dp_accounted_offline,\n", "        dp_expected_value, dp_collected_value , dp_hfe_offline_settled+dp_salary_settled as dp_hfe_accounted_offline\n", "    from(\n", "select gd.*, ol.gsp_dp_offline_settled , ol.gsp_salary_settled, \n", "        ol.dp_hfe_offline_settled , ol.dp_salary_settled, so.percentage_suborders\n", "from gsp_dp gd\n", "left join offline_ledger ol on gd. scheduleddate = ol.scheduleddate\n", "left join sub_orders so on so.scheduleddate = gd.scheduleddate\n", ") order by 1\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data = pd.read_sql_query(\n", "    sql=E2E_View_LM, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data = E2E_View_LM_Data.round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data_MTD = E2E_View_LM_Data[\n", "    E2E_View_LM_Data[\"scheduleddate\"].astype(str) == Prev\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data_MTD[\"scheduleddate\"] = \"MTD\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data = E2E_View_LM_Data[E2E_View_LM_Data[\"scheduleddate\"] < prevday]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data = pd.concat([E2E_View_LM_Data, E2E_View_LM_Data_MTD], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View_LM_Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View = pd.merge(E2E_View_LM_Data, E2E_View_WH_Data, on=\"scheduleddate\", how=\"inner\")\n", "E2E_View"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Station View"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Query1 = \"\"\"with gsp as ( select city , station,\n", "                count(distinct employee_id) as gsp_count,\n", "                sum(pendency) as gsp_pendency\n", "            from(\n", "            select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as GSP_Name,\n", "        employee_id ,\n", "        delivery_partner_lm as delivery_partner,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(gsp_dp_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "     where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('GSP', 'FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    where pendency > 0\n", "    ) group by 1,2\n", "    ),\n", "dp as (\n", "    with direct_pendency as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as DP_Name,\n", "        employee_id as dp_employee_id,\n", "        returning_entity,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "     where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('DP')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    where pendency > 0\n", "    ),\n", "    \n", "returned as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        gsp_dp_collecting_entity_name as dp_name,\n", "        gsp_dp_collecting_entity_employee_id as dp_employee_id,\n", "        'GSP' as returning_entity,\n", "        sum(gsp_dp_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and gsp_dp_quantity_actual is not null\n", "    and returning_entity in ('GSP','FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a\n", "    where pendency > 0\n", ")\n", "select city, station , count(distinct dp_employee_id) as dp_count, sum(pendency) dp_pendency from (\n", "select * from direct_pendency\n", "union \n", "select * from returned\n", ") group by 1,2\n", "),\n", "\n", "\n", "\n", "base as ( select distinct city, station\n", " from metrics.returns_wh_lm_integration \n", " where  scheduleddate::date between %(start)s and %(end)s\n", " group by 1,2)\n", "\n", "select a.*\n", "        from (\n", "select distinct b.city,\n", "        b.station,\n", "        Coalesce(gsp_count,0) gsp_count,\n", "        Coalesce(dp_count,0) dp_count,\n", "        -- hfe_count,\n", "        Coalesce(gsp_pendency,0) gsp_pendency,\n", "        Coalesce(dp_pendency,0) dp_pendency\n", "        -- hfe_pendency\n", "    from base b\n", "    left join gsp on b.city = gsp.city and b.station = gsp.station\n", "    left join dp on b.city = dp.city and b.station = dp.station\n", "    -- left join hfe on b.city = hfe.city and b.station = hfe.station\n", "    where b.station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "    ) a where gsp_count+dp_count+gsp_pendency+dp_pendency <> 0\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView = pd.read_sql_query(\n", "    sql=Query1, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView = StationView.round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView_Total = StationView.agg(\n", "    {\"gsp_count\": sum, \"dp_count\": sum, \"gsp_pendency\": sum, \"dp_pendency\": sum}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView_Total = StationView_Total.to_frame().transpose()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView_Total[\"city\"] = \"Pan India\"\n", "StationView_Total[\"station\"] = \"All\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView = pd.concat([StationView, StationView_Total], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView = StationView[\n", "    [\"city\", \"station\", \"gsp_count\", \"dp_count\", \"gsp_pendency\", \"dp_pendency\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Attachment "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WH_Query = \"\"\"\n", "       select \n", "       facility_id,\n", "       facility_name,\n", "       scheduleddate::date,\n", "       count(distinct sub_order_id) as Suborders,\n", "       sum(expected_quantity) as expected_quantity,\n", "       sum(expectedvalue) as expected_value,\n", "       sum(inwardvalue) as InwardV<PERSON>ueOverall,\n", "       sum(expectedvalue)-sum(inwardvalue) as TotalDiff,\n", "       sum(case when wh_type='with_oid' and withintime=0 then expectedvalue else 0 end) -sum(case when wh_type='with_oid' and withintime=0 then inwardvalue else 0 end) as not_in_time_diff,\n", "       sum(case when dispatch_time is null then expectedvalue else 0 end) - sum(case when dispatch_time is null then inwardvalue else 0 end) as NotDispatched_Diff\n", "from metrics.returns_wh_lm_integration \n", "where  scheduleddate::date between %(start)s and %(end)s\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WH_E2E_Data = pd.read_sql_query(\n", "    sql=WH_Query, con=Redshift, params={\"start\": start, \"end\": end}\n", ")\n", "WH_E2E_Data = WH_E2E_Data.sort_values([\"facility_id\", \"scheduleddate\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Raw_Data_Query = \"\"\"\n", "Select * from metrics.returns_wh_lm_integration \n", "where  scheduleddate::date between %(start)s and %(end)s\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Raw_Data = pd.read_sql_query(sql=Raw_Data_Query, con=Redshift,params={'start':start,'end':end})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GSP_Raw = \"\"\"\n", "        select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "        select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as GSP_Name,\n", "        employee_id ,\n", "        delivery_partner_lm as delivery_partner,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(gsp_dp_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('GSP', 'FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    where pendency > 0\n", "    and station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GSP_Data = pd.read_sql_query(\n", "    sql=GSP_Raw, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DP_Raw = \"\"\"\n", "        with direct_pendency as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        entityname as DP_Name,\n", "        employee_id as dp_employee_id,\n", "        returning_entity,\n", "        sum(lm_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('DP')\n", "    group by 1,2,3,4,5,6\n", "    ) a \n", "    where pendency > 0\n", "    ),\n", "    \n", "returned as (\n", "select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "select city,\n", "        station,\n", "        scheduleddate :: date,\n", "        gsp_dp_collecting_entity_name as dp_name,\n", "        gsp_dp_collecting_entity_employee_id as dp_employee_id,\n", "        'GSP' as returning_entity,\n", "        sum(gsp_dp_quantity_actual*selling_price) as expected_value,\n", "        Coalesce(sum(dp_hfe_quantity_actual*selling_price),0) as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and gsp_dp_quantity_actual is not null\n", "    and returning_entity in ('GSP','FE')\n", "    group by 1,2,3,4,5,6\n", "    ) a\n", "    where pendency > 0\n", ")\n", "select * from (\n", "select * from direct_pendency\n", "union \n", "select * from returned\n", ") where station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DP_Data = pd.read_sql_query(\n", "    sql=DP_Raw, con=Redshift, params={\"start\": start, \"end\": end}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HFE_Raw = \"\"\"\n", "        select a.*,\n", "        expected_value - collected_value as pendency\n", "        from (\n", "    select city,\n", "            station,\n", "            scheduleddate,\n", "            hfe_name,\n", "            hfe_employee_id,\n", "            expected_value,\n", "            case when collected_value > expected_value then expected_value else collected_value end as collected_value\n", "            from(\n", "select distinct city,\n", "        station,\n", "        scheduleddate :: date,\n", "        dp_hfe_collecting_entity_name as hfe_name,\n", "        dp_hfe_collecting_entity_employee_id as hfe_employee_id,\n", "        sum(dp_hfe_quantity_actual*selling_price)::int as expected_value,\n", "        inwardvalue::int as collected_value\n", "    from metrics.returns_wh_lm_integration \n", "    where  scheduleddate::date between %(start)s and %(end)s\n", "    and returning_entity in ('DP','GSP','FE')\n", "    and dp_hfe_quantity_actual is not null\n", "    and datediff(hours,whtaskreceivedat,whtaskcompletedat) < 24\n", "    group by 1,2,3,4,5,7\n", "    ) b \n", "    ) a\n", "    where pendency > 0\n", "    and station not in ('HREE1','DLEE1','DLEE2','DLEE3','DLEE4','DLEE5')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# HFE_Data = pd.read_sql_query(sql=HFE_Raw, con=Redshift,params={'start':start,'end':end})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openpyxl\n", "\n", "writer = pd.ExcelWriter(\"/tmp/E2E_Returns_\" + today + \".xlsx\")\n", "WH_E2E_Data.to_excel(writer, sheet_name=\"Facility Level\", index=False)\n", "\n", "writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Raw_Data.to_excel(writer, sheet_name=\"Raw Data\", index=False)\n", "# writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GSP_Data.to_excel(writer, sheet_name=\"GSP Level\", index=False)\n", "writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DP_Data.to_excel(writer, sheet_name=\"DP Level\", index=False)\n", "writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# HFE_Data.to_excel(writer, sheet_name=\"HFE Level\", index=False)\n", "# writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["format_currency(5433422.8012, \"INR\", locale=\"en_IN\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OverallDiff[\"expectedvalue\"] = OverallDiff[\"expectedvalue\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "OverallDiff[\"inwardvalue\"] = OverallDiff[\"inwardvalue\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "OverallDiff[\"overalldiff\"] = OverallDiff[\"overalldiff\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "OverallDiff[\"difffrom_itemmissing\"] = OverallDiff[\"difffrom_itemmissing\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StationView[\"gsp_pendency\"] = StationView[\"gsp_pendency\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "StationView[\"dp_pendency\"] = StationView[\"dp_pendency\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "# # StationView[\"hfe_pendency\"] = StationView[\"hfe_pendency\"].apply(lambda x: format_currency(float(x), 'INR', locale='en_IN'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View.drop(\"percentage_suborders\", inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View[\"gsp_expected_value\"] = E2E_View[\"gsp_expected_value\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"gsp_collected_value\"] = E2E_View[\"gsp_collected_value\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"gsp_dp_accounted_offline\"] = E2E_View[\"gsp_dp_accounted_offline\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "\n", "E2E_View[\"dp_expected_value\"] = E2E_View[\"dp_expected_value\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"dp_collected_value\"] = E2E_View[\"dp_collected_value\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"dp_hfe_accounted_offline\"] = E2E_View[\"dp_hfe_accounted_offline\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "\n", "E2E_View[\"grn\"] = E2E_View[\"grn\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"inwardvalueoverall\"] = E2E_View[\"inwardvalueoverall\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"lm_diff\"] = E2E_View[\"lm_diff\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "\n", "E2E_View[\"overalldiff\"] = E2E_View[\"overalldiff\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"wh_diff\"] = E2E_View[\"wh_diff\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"wh_expectedvalue_task\"] = E2E_View[\"wh_expectedvalue_task\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "\n", "E2E_View[\"wh_inwardedvalue_task\"] = E2E_View[\"wh_inwardedvalue_task\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"wh_inwardedwithout_task\"] = E2E_View[\"wh_inwardedwithout_task\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "E2E_View[\"within_time_inwardedvalue\"] = E2E_View[\"within_time_inwardedvalue\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")\n", "\n", "E2E_View[\"withtaskgrn\"] = E2E_View[\"withtaskgrn\"].apply(\n", "    lambda x: format_currency(float(x), \"INR\", locale=\"en_IN\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["E2E_View.drop(\"withtaskgrn\", inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = OverallDiff.to_dict(orient=\"rows\")\n", "items2 = StationView.to_dict(orient=\"rows\")\n", "items3 = E2E_View.to_dict(orient=\"rows\")\n", "items4 = end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"End to End Returns Report for \" + end\n", "\n", "# to_email = [\"<EMAIL>\",\"<EMAIL>\"]\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>\",\n", "    \"dl3_patpar<PERSON>@grofers.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"dl3_patpar<PERSON>@grofers.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "email_template = \"email_template.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/returns/report/e2e_report/\"\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(\n", "    products1=items1, products2=items2, products3=items3, today=items4\n", ")\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\"/tmp/E2E_Returns_\" + today + \".xlsx\"],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>,\",\n", "#     \"<EMAIL>,\",\n", "#     \"<EMAIL>,\",\n", "#     \"<EMAIL>\",\n", "#     \"dl3_patpar<PERSON>@grofers.com\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"dl3_patpar<PERSON>@grofers.com\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}