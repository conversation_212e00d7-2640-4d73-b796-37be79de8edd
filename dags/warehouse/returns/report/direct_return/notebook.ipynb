{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_count = \"\"\"\n", "select facility_id,facility_name,\n", "count(product_id) as product_id_count\n", "from\n", "(select\n", "rt.facility_id, \n", "cf.name as facility_name,\n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id, \n", "rtoa.product_id\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "JOIN consumer.warehouse_location_return_task_order rto on rto.return_task_id=rt.id\n", "join  consumer.warehouse_location_return_task_order_actual rtoa on rtoa.return_task_order_id=rto.id\n", "join  consumer.crates_facility cf on cf.id=rt.facility_id\n", "where \n", " rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rt.received_at)>=%(run_period_start)s and convert_timezone('Asia/Kolkata',rt.received_at)<=%(run_period_end)s\n", ")a\n", "group by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty = \"\"\"\n", "with \n", "map_log as (\n", "Select c.name as facility_name,\n", "return_task_id, \n", "order_id, item_id, invoice_id \n", "from\n", "consumer.warehouse_location_return_task rt\n", "join consumer.warehouse_location_return_task_order_item_map_log iml on rt.id=iml.return_task_id  \n", "and convert_timezone('Asia/Kolkata',rt.received_at)>=%(run_period_start)s \n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=%(run_period_end)s\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)>='2020-04-09'\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)<='2020-04-10'\n", "join crates_facility c on rt.facility_id=c.id\n", ")\n", "\n", "select ml2.facility_name, \n", "ml2.return_task_id, \n", "ml2.invoice_id invoice_id1, ml2.order_id, \n", "ml2.item_id,\n", "abc.invoice_id, \n", "coalesce(abc.\"delta\",0) as bad_qty\n", "from\n", "map_log ml2\n", "left join \n", "(select iil.invoice_id, iil.grofers_order_id order_id, pp.item_id, \"delta\"\n", "from \n", "map_log ml \n", "join consumer.ims_ims_inventory_log iil on iil.invoice_id=ml.invoice_id and iil.grofers_order_id=ml.order_id \n", "join consumer.ims_ims_inventory_update_type iut ON iil.inventory_update_type_id = iut.id  and is_bad_inventory_update is true and bad_inventory_operation='+'\n", "join consumer.rpc_product_product pp on pp.variant_id=iil.variant_id\n", "group by 1,2,3,4) abc\n", " on ml2.invoice_id=abc.invoice_id and ml2.order_id=abc.order_id  and abc.item_id=ml2.item_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_without_oid_bad_qty = \"\"\" \n", "select \n", "facility_name,\n", " return_task_id, \n", " grn_id,\n", " item_id, \n", " landing_price landing_price,\n", " sum(bad_qty) bad_qty,\n", "  sum(bad_qty)*landing_price loss\n", " \n", " from\n", " (select \n", "c.name as facility_name,\n", " rtg.return_task_id, rtg.grn_id, iil.variant_id, pp.item_id, \"delta\" as bad_qty, landing_price\n", "from consumer.warehouse_location_return_task rt \n", "join  consumer.warehouse_location_return_task_grn rtg on rtg.return_task_id=rt.id \n", "     and convert_timezone('Asia/Kolkata',rt.received_at)>='2020-02-03'\n", "    and convert_timezone('Asia/Kolkata',rt.received_at)<='2020-02-04'\n", "left join consumer.pos_ims_inventory_stock_details isd on isd.grn_id=rtg.grn_id\n", "left join consumer.ims_ims_inventory_log iil on iil.inventory_update_id::varchar=isd.inventory_update_id::varchar\n", "join consumer.rpc_product_product pp on pp.variant_id=iil.variant_id\n", "and iil.inventory_update_type_id in (87,88,89)\n", "join crates_facility c on rt.facility_id=c.id\n", ") a\n", "group by 1,2,3,4,5\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7 = \"\"\"\n", "select 'with_oid' as  type, \n", "rt.facility_id, \n", "c.name as facility_name,\n", "date(convert_timezone('Asia/Kolkata',rt.received_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rt.received_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "join crates_facility c on rt.facility_id=c.id\n", "where\n", "rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rt.received_at)>current_date -8 and convert_timezone('Asia/Kolkata',rt.received_at)<current_date -1\n", "and rt.expected_quantity>0\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7_query = \"\"\"\n", "\n", "with oid_base as (\n", "select 'with_oid' as  type, rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rt.received_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rt.received_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "avg(rtoi.quantity) as expected_quantity, \n", "avg(rtoi.map_quantity) map_quantity,\n", "avg(pdt.selling_price) as selling_price\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join  consumer.pos_pos_invoice pos on pos.grofers_order_id= rtoi.order_id \n", "left join  consumer.pos_pos_invoice_product_details pdt on pdt.invoice_id = pos.id\n", "left join consumer.rpc_product_product prod on prod.variant_id = pdt.variant_id\n", "\n", "\n", "where invoice_type_id=1 and prod.item_id=rtoi.item_id and prod.variant_id not like '%%G00%%'\n", "and rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rt.received_at)>current_date -9 and convert_timezone('Asia/Kolkata',rt.received_at)<current_date -1\n", "--and rtsl.created_at>='2020-02-17' and rtsl.created_at<='2020-02-18'\n", "--and rtoi.return_task_id=302757\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "without_oid_base as(\n", "select \n", " 'without_oid' as type,\n", "rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rt.received_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rt.received_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "\n", "rtoi.order_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity,\n", "avg(lp.landing_price) as selling_price\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join \n", "(select item_id, avg(landing_price) landing_price\n", "from\n", "(select pp.item_id, a.variant_id, a.landing_price\n", "from\n", "ims_ims_inventory_landing_price a\n", "join\n", "(select variant_id, max(pos_timestamp) pos_timestamp\n", "from\n", "consumer.ims_ims_inventory_landing_price\n", "group by 1) b on a.variant_id=b.variant_id and a.pos_timestamp=b.pos_timestamp\n", "join consumer.rpc_product_product pp on pp.variant_id=a.variant_id)c\n", "group by 1) lp on lp.item_id= rtoi.item_id\n", "\n", "where \n", " rtsl.state=5 \n", "and convert_timezone('Asia/Kolkata',rt.received_at)>current_date -9 and convert_timezone('Asia/Kolkata',rt.received_at)<current_date -1\n", "--and rtsl.created_at>='2020-02-17' and rtsl.created_at<='2020-02-18'\n", "--and rtoi.return_task_id=329146\n", "and (rtoi.order_id ='None' or rtoi.order_id='' or rtoi.order_id is null or  rtoi.order_id=' ' or len(trim(rtoi.order_id))<1)\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "\n", ")\n", "\n", "-- select return_task_id, order_id, item_id, count(*)\n", "-- from\n", "-- (\n", "select o.*, c.name as facility_name from oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "\n", "union\n", "select o.*, c.name as facility_name from without_oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "-- ) c \n", "-- group by 1,2,3 having count(*)>1\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_query = \"\"\"\n", "with oid_base as ( \n", "select 'with_oid' as  type, rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rt.received_at)) as received_date,\n", "rt.id return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rt.received_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "pos.invoice_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity\n", "from consumer.warehouse_location_return_task rt \n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rt.id\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, '2020-04-10')))\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, '2020-04-10'))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join  consumer.pos_pos_invoice pos on pos.grofers_order_id= rtoi.order_id and invoice_type_id=1\n", "where \n", "len(trim(rtoi.order_id))>5\n", "and rt.state=7\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "without_oid_base as(\n", "select \n", " 'without_oid' as type,\n", "rt.facility_id, \n", "date(convert_timezone('Asia/Kolkata',rt.received_at)) as received_date,\n", "rt.id return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rt.received_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "'' as invoice_id,\n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity\n", "from \n", "consumer.warehouse_location_return_task rt-- on rt.id=rtsl.return_task_id\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rt.id\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, '2020-04-10')))\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, '2020-04-10'))\n", " and convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "where \n", "(rtoi.order_id ='None' or rtoi.order_id='' or rtoi.order_id is null or  rtoi.order_id=' ' or len(trim(rtoi.order_id))<5)\n", "and rt.state=7\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", ")\n", "select o.*, c.name as facility_name from oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "union\n", "select o.*, c.name as facility_name from without_oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_query = \"\"\"\n", "with oid_base as ( \n", "select 'with_oid' as  type, rt.facility_id, \n", "rtoi.return_task_order_id,\n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rtsl.created_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "pos.invoice_id, \n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "avg(rtoi.quantity) as expected_quantity, \n", "avg(rtoi.map_quantity) map_quantity\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "--and convert_timezone('Asia/Kolkata',rtsl.created_at)>='2020-04-09' and convert_timezone('Asia/Kolkata',rtsl.created_at)<'2020-04-10'\n", "and convert_timezone('Asia/Kolkata',rt.received_at)>=%(run_period_start)s and convert_timezone('Asia/Kolkata',rt.received_at)<=%(run_period_end)s\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "left join  consumer.pos_pos_invoice pos on pos.grofers_order_id= rtoi.order_id and invoice_type_id=1\n", "where \n", "rtsl.state=5 \n", "and len(trim(rtoi.order_id))>5\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", "),\n", "\n", "without_oid_base as(\n", "select \n", " 'without_oid' as type,\n", "rt.facility_id, \n", "return_task_order_id,\n", "date(convert_timezone('Asia/Kolkata',rtsl.created_at)) as received_date,\n", "rtsl.return_task_id , \n", "convert_timezone('Asia/Kolkata',rt.created_at) as task_created_at,\n", "rtsl.state as received_state, \n", "rt.state as latest_state, \n", "convert_timezone('Asia/Kolkata',rtsl.created_at) received_ts, \n", "convert_timezone('Asia/Kolkata',rt.updated_at) as latest_ts,\n", "rtoi.order_id, \n", "'' as invoice_id,\n", "iod.ancestor,\n", "rtoi.item_id, \n", "rtoi.item_name, \n", "sum(rtoi.quantity) as expected_quantity, \n", "sum(rtoi.map_quantity) map_quantity\n", "from consumer.warehouse_location_return_task_state_log rtsl\n", "join consumer.warehouse_location_return_task rt on rt.id=rtsl.return_task_id\n", "--and convert_timezone('Asia/Kolkata',rtsl.created_at)>='2020-04-09' and convert_timezone('Asia/Kolkata',rtsl.created_at)<'2020-04-10'\n", "and convert_timezone('Asia/Kolkata',rt.received_at)>=%(run_period_start)s and convert_timezone('Asia/Kolkata',rt.received_at)<=%(run_period_end)s\n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rtsl.return_task_id\n", "left join  consumer.ims_order_details iod on iod.order_id=rtoi.order_id\n", "where \n", " rtsl.state=5 \n", "and (rtoi.order_id ='None' or rtoi.order_id='' or rtoi.order_id is null or  rtoi.order_id=' ' or len(trim(rtoi.order_id))<5)\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", ")\n", "select o.*, c.name as facility_name from oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "union\n", "select o.*, c.name as facility_name from without_oid_base\n", "o join crates_facility c on o.facility_id=c.id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping = \"\"\"\n", "SELECT \n", "rt.id return_task_id,\n", "rtoi.order_id order_id,\n", "rto.city_name,\n", "rto.dp_name,\n", "rto.dp_unique_name,\n", "rto.station_id\n", "from\n", "warehouse_location.return_task rt\n", "join warehouse_location.return_task_order_item rtoi on rtoi.return_task_id=rt.id\n", "join warehouse_location.return_task_order rto on rto.id=rtoi.return_task_order_id\n", "where\n", "rt.received_at>=current_date() -10\n", "and rt.received_at<=current_date\n", "\n", "group by 1,2,3,4,5,6\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["station_mapping = \"\"\"\n", "select \n", "id station_id,name station_name from \n", "lake_last_mile.station\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_selling_price = \"\"\"\n", "select \n", "return_task_id,\n", "suborder_id,\n", "pp.item_id,\n", "avg(selling_price) selling_price,\n", "sum(sold_qty) sold_qty\n", "from \n", "(select \n", "return_task_id,\n", "order_id suborder_id,\n", "pipd.variant_id,\n", "name as item_name,\n", "selling_price,\n", "quantity sold_qty\n", "from\n", "(select \n", "rt.id return_task_id,\n", "rtoi.order_id,\n", "pos.id\n", "from consumer.warehouse_location_return_task rt \n", "join consumer.warehouse_location_return_task_order_item rtoi on rtoi.return_task_id=rt.id and rt.received_at>current_date-3\n", "join consumer.pos_pos_invoice pos on pos.grofers_order_id= rtoi.order_id and invoice_type_id=1\n", "--join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=pos.id\n", "where\n", "convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "\n", "--rtoi.order_id=89407186\n", "group by 1,2,3\n", ") a\n", "join consumer.pos_pos_invoice_product_details pipd on pipd.invoice_id=a.id and pipd.variant_id not like '%%G00%%'\n", "group by 1,2,3,4,5,6\n", ")b\n", "join consumer.rpc_product_product  pp on pp.variant_id=b.variant_id\n", "group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["without_oid_landing_price = \"\"\"\n", "select wlp.item_id,avg(wlp.landing_price) landing_price\n", "from\n", "consumer.weighted_landing_price wlp \n", "join \n", "(select item_id, max(dt_ist) dt_ist from  consumer.weighted_landing_price\n", "group by 1) a\n", "on a.dt_ist=wlp.dt_ist and wlp.item_id=a.item_id\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_reason_mapping = \"\"\"\n", "select\n", "rto.return_task_id,\n", "rto.order_id,\n", "case when rto.\"type\"=1 then 'DEFAULT'\n", "     when rto.\"type\"=2 then 'REPLACE'\n", "     when rto.\"type\"=3 then 'RETURN'\n", "     when rto.\"type\"=4 then 'DELIVERED'\n", "     when rto.\"type\"=5 then 'CANCELLED'\n", "     when rto.\"type\"=6 then 'RESCHEDULED'\n", "     when rto.\"type\"=7 then 'PICKED_UP'\n", "     when rto.\"type\"=8 then 'DELIVERY_FAILED'\n", "end as return_reason\n", "\n", "from \n", "consumer.warehouse_location_return_task rt\n", "join \n", "(\n", "select rt.id, rto.order_id, max(rto.updated_at) updated_at\n", "from\n", "consumer.warehouse_location_return_task rt \n", "join consumer.warehouse_location_return_task_order rto on rto.return_task_id=rt.id\n", "where \n", "--rt.id=506607\n", "convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "group by 1,2\n", ") urto on urto.id=rt.id\n", "join consumer.warehouse_location_return_task_order rto on rto.return_task_id=rt.id and rto.order_id=urto.order_id and urto.updated_at=rto.updated_at\n", "where\n", "--rt.id=506607\n", "-- convert_timezone('Asia/Kolkata',rt.received_at) >=date(date_trunc('month',DATEADD('day', -1, '2020-04-10')))\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at) <=date(DATEADD('day', 0, '2020-04-10'))\n", " convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "group by 1,2,3\n", "\n", "\n", "\n", "-- select\n", "-- rto.return_task_id,\n", "-- rto.order_id,\n", "-- case when rto.\"type\"=1 then 'DEFAULT'\n", "--      when rto.\"type\"=2 then 'REPLACE'\n", "--      when rto.\"type\"=3 then 'RETURN'\n", "--      when rto.\"type\"=4 then 'DELIVERED'\n", "--      when rto.\"type\"=5 then 'CANCELLED'\n", "--      when rto.\"type\"=6 then 'RESCHEDULED'\n", "--      when rto.\"type\"=7 then 'PICKED_UP'\n", "--      when rto.\"type\"=8 then 'DELIVERY_FAILED'\n", "-- end as return_reason\n", "\n", "-- from \n", "-- consumer.warehouse_location_return_task rt\n", "-- join \n", "-- consumer.warehouse_location_return_task_order rto on rto.return_task_id=rt.id\n", "-- where\n", "-- -- convert_timezone('Asia/Kolkata',rt.received_at) >=date(date_trunc('month',DATEADD('day', -1, '2020-04-10')))\n", "-- -- and convert_timezone('Asia/Kolkata',rt.received_at) <=date(DATEADD('day', 0, '2020-04-10'))\n", "--  convert_timezone('Asia/Kolkata',rt.received_at)>=date(date_trunc('month',DATEADD('day', -1, %(run_period_end)s)))\n", "-- and convert_timezone('Asia/Kolkata',rt.received_at)<=date(DATEADD('day', 0, %(run_period_end)s))\n", "\n", "\n", "-- group by 1,2,3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_type = \"\"\"\n", "select \n", "*,\n", "case when (rtgi_item_id>0 and rtgi_map_qty>0 ) or (rtoi_map_qty>rtgi_map_qty and rtgi_map_qty>0 ) then 'Auto'\n", " when (rtoi_item_id>0  and rtoi_map_qty>0 and rtgi_item_id is null  ) then 'Manual'\n", " when (rtoi_map_qty>0 and rtgi_map_qty=0) then 'Manual'\n", "end as mapping_type\n", "from\n", "(select \n", "convert_timezone('Asia/Kolkata',received_at ) received_at,\n", "rtoi.return_task_id rtoi_return_task_id, \n", "rtoi.order_id rtoi_order_id, \n", "rtoi.item_id rtoi_item_id,\n", "rtgi.item_id rtgi_item_id, \n", "sum(rtoi.quantity) rtoi_expected_qty, \n", "sum(rtoi.map_quantity) rtoi_map_qty,\n", "sum(rtgi.map_quantity) rtgi_map_qty,\n", "sum(rtoi.map_quantity)-sum(rtgi.map_quantity) rtoi_to_rtgi_map_diff\n", "from \n", "consumer.warehouse_location_return_task rt \n", "join consumer.warehouse_location_return_task_order_item  rtoi  on rt.id=rtoi.return_task_id\n", "left join consumer.warehouse_location_return_task_grn_item rtgi on rtoi.return_task_id=rtgi.return_task_id\n", "and rtgi.item_id=rtoi.item_id\n", "where\n", "convert_timezone('Asia/Kolkata',received_at ) >=  %(run_period_start)s \n", "and convert_timezone('Asia/Kolkata',received_at ) <=  %(run_period_end)s \n", "--rtoi.return_task_id = 371657  \n", "group by 1,2,3,4,5\n", ")\n", "a\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "task_states = pd.DataFrame(\n", "    [\n", "        [\"DRAFT\", 1],\n", "        [\"ORDER_SYNCED\", 2],\n", "        [\"ORDER_SYNC_FAILED\", 3],\n", "        [\"CREATED\", 4],\n", "        [\"RECEIVED\", 5],\n", "        [\"RECEIVED_CRATE\", 6],\n", "        [\"COMPLETED\", 7],\n", "    ],\n", "    columns=[\"states\", \"id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import csv\n", "import glob\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "\n", "import pytz\n", "import jinja2\n", "import pandas as pd\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "Start = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "End = (datetime.now(tz) - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_period_start = Start\n", "run_period_end = End"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %store -r t_minus_2_query\n", "# %store -r mtd_query\n", "# %store -r t_minus_2_with_oid_bad_qty\n", "# %store -r t_minus_2_without_oid_bad_qty\n", "# %store -r task_states\n", "# %store -r t_minus_2_selling_price\n", "# %store -r without_oid_landing_price"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"redshift\")\n", "t_minus_2_orders = pd.read_sql_query(\n", "    sql=t_minus_2_query,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "t_minus_2_selling_price_result = pd.read_sql_query(\n", "    sql=t_minus_2_selling_price,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "\n", "without_oid_landing_price_result = pd.read_sql_query(\n", "    sql=without_oid_landing_price,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_orders.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_orders.replace({\"order_id\": {\"None\": 0}}, inplace=True)\n", "t_minus_2_orders.replace({\"invoice_id\": {\"None\": 0}}, inplace=True)\n", "t_minus_2_orders.replace({\"ancestor\": {\"None\": 0}}, inplace=True)\n", "t_minus_2_orders.invoice_id = t_minus_2_orders.invoice_id.fillna(\"0\", inplace=True)\n", "t_minus_2_orders.loc[t_minus_2_orders[\"ancestor\"].isnull(), \"ancestor\"] = \"0\"\n", "t_minus_2_orders.loc[t_minus_2_orders[\"invoice_id\"].isnull(), \"invoice_id\"] = \"0\"\n", "t_minus_2_orders.loc[t_minus_2_orders[\"order_id\"].isnull(), \"order_id\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_orders = (\n", "    t_minus_2_orders.groupby(\n", "        [\n", "            \"type\",\n", "            \"facility_id\",\n", "            \"received_date\",\n", "            \"return_task_id\",\n", "            \"task_created_at\",\n", "            \"received_state\",\n", "            \"latest_state\",\n", "            \"received_ts\",\n", "            \"latest_ts\",\n", "            \"order_id\",\n", "            \"invoice_id\",\n", "            \"ancestor\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"expected_quantity\": \"sum\", \"map_quantity\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_orders.head(1)\n", "# t_minus_2_orders[t_minus_2_orders.order_id==0]\n", "# t_minus_2_orders[(t_minus_2_orders.return_task_id==450387) & (t_minus_2_orders.item_id==10042749) ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_orders[t_minus_2_orders.order_id=='87448235']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_orders.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_orders = t_minus_2_orders[\n", "    (t_minus_2_orders.order_id.notnull()) & (t_minus_2_orders.order_id != \"None\")\n", "].merge(\n", "    t_minus_2_selling_price_result[\n", "        [\"return_task_id\", \"suborder_id\", \"item_id\", \"selling_price\"]\n", "    ],\n", "    how=\"left\",\n", "    left_on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", "    right_on=[\"return_task_id\", \"suborder_id\", \"item_id\"],\n", ")\n", "t_minus_2_with_orders = t_minus_2_with_orders.drop(columns=[\"suborder_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["without_oid_landing_price_result.item_id = without_oid_landing_price_result.item_id.astype(\n", "    int\n", ")\n", "without_oid_landing_price_result.rename(\n", "    columns={\"landing_price\": \"selling_price\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_orders = t_minus_2_orders.merge(\n", "    t_minus_2_with_orders[[\"return_task_id\", \"order_id\", \"item_id\", \"selling_price\"]],\n", "    how=\"left\",\n", "    on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_null_result = t_minus_2_orders[\n", "    (t_minus_2_orders[\"selling_price\"] == \"None\")\n", "    | (t_minus_2_orders[\"selling_price\"].isnull())\n", "][[\"return_task_id\", \"order_id\", \"item_id\"]].merge(\n", "    without_oid_landing_price_result, how=\"left\", on=\"item_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result = t_minus_2_orders.merge(\n", "    t_minus_2_null_result, how=\"left\", on=[\"return_task_id\", \"order_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result[\"selling_price_x\"] = t_minus_2_result[\"selling_price_x\"].fillna(0)\n", "t_minus_2_result[\"selling_price_y\"] = t_minus_2_result[\"selling_price_y\"].fillna(0)\n", "t_minus_2_result[\"selling_price\"] = (\n", "    t_minus_2_result[\"selling_price_x\"] + t_minus_2_result[\"selling_price_y\"]\n", ")\n", "t_minus_2_result[\"selling_price\"] = t_minus_2_result[\"selling_price\"].astype(int)\n", "t_minus_2_result = t_minus_2_result.drop(columns=[\"selling_price_x\", \"selling_price_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result.groupby([\"return_task_id\"]).agg(\n", "    {\"expected_quantity\": \"sum\", \"map_quantity\": \"sum\"}\n", ").sort_values(\"expected_quantity\")\n", "t_minus_2_result.expected_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_result[t_minus_2_result.return_task_id==577085]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %store -r mtd_query\n", "con_redshift = pb.get_connection(\"redshift\")\n", "mtd_orders = pd.read_sql_query(\n", "    sql=mtd_query,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_orders.replace({\"order_id\": {\"None\": 0}}, inplace=True)\n", "mtd_orders.replace({\"invoice_id\": {\"None\": 0}}, inplace=True)\n", "mtd_orders.replace({\"ancestor\": {\"None\": 0}}, inplace=True)\n", "mtd_orders.invoice_id = mtd_orders.invoice_id.fillna(\"0\", inplace=True)\n", "mtd_orders.loc[mtd_orders[\"ancestor\"].isnull(), \"ancestor\"] = \"0\"\n", "mtd_orders.loc[mtd_orders[\"invoice_id\"].isnull(), \"invoice_id\"] = \"0\"\n", "mtd_orders.loc[mtd_orders[\"order_id\"].isnull(), \"order_id\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_orders.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_with_orders = mtd_orders[\n", "    (mtd_orders.order_id.notnull()) & (mtd_orders.order_id != \"None\")\n", "].merge(\n", "    t_minus_2_selling_price_result[\n", "        [\"return_task_id\", \"suborder_id\", \"item_id\", \"selling_price\"]\n", "    ],\n", "    how=\"left\",\n", "    left_on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", "    right_on=[\"return_task_id\", \"suborder_id\", \"item_id\"],\n", ")\n", "mtd_with_orders = mtd_with_orders.drop(columns=[\"suborder_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_with_orders.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_orders[[\"return_task_id\", \"order_id\", \"item_id\"]].drop_duplicates().shape[\n", "    0\n", "] - mtd_orders.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mtd_orders.groupby(\n", "#     [\"return_task_id\", \"order_id\",\"item_id\"]\n", "# ).size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_orders = mtd_orders.merge(\n", "    mtd_with_orders[[\"return_task_id\", \"order_id\", \"item_id\", \"selling_price\"]],\n", "    how=\"left\",\n", "    on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_orders.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_null_result = mtd_orders[\n", "    (mtd_orders[\"selling_price\"] == \"None\") | (mtd_orders[\"selling_price\"].isnull())\n", "][[\"return_task_id\", \"order_id\", \"item_id\"]].merge(\n", "    without_oid_landing_price_result, how=\"left\", on=\"item_id\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result = mtd_orders.merge(\n", "    mtd_null_result, how=\"left\", on=[\"return_task_id\", \"order_id\", \"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result[\"selling_price_x\"] = mtd_result[\"selling_price_x\"].fillna(0)\n", "mtd_result[\"selling_price_y\"] = mtd_result[\"selling_price_y\"].fillna(0)\n", "mtd_result[\"selling_price\"] = (\n", "    mtd_result[\"selling_price_x\"] + mtd_result[\"selling_price_y\"]\n", ")\n", "mtd_result[\"selling_price\"] = mtd_result[\"selling_price\"].astype(int)\n", "mtd_result = mtd_result.drop(columns=[\"selling_price_x\", \"selling_price_y\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mtd_result.to_csv('mtd_result.csv')\n", "\n", "# mtd_result[mtd_result.order_id=='None']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mtd_result.head()\n", "mtd_result.groupby([\"received_date\"]).agg(\n", "    {\"expected_quantity\": \"sum\", \"map_quantity\": \"sum\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mtd_result[[\"return_task_id\", \"order_id\",\"item_id\"]].drop_duplicates().shape[\n", "#     0\n", "# ] - mtd_result.shape[0]\n", "mtd_result.expected_quantity.sum()\n", "mtd_result.map_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level = pd.merge(\n", "    t_minus_2_result, task_states, how=\"left\", left_on=\"received_state\", right_on=\"id\"\n", ")\n", "t_minus_2_task_at_item_level.rename(columns={\"states\": \"received_states\"}, inplace=True)\n", "\n", "t_minus_2_task_at_item_level = pd.merge(\n", "    t_minus_2_task_at_item_level,\n", "    task_states,\n", "    how=\"left\",\n", "    left_on=\"latest_state\",\n", "    right_on=\"id\",\n", ")\n", "t_minus_2_task_at_item_level.rename(columns={\"states\": \"latest_states\"}, inplace=True)\n", "t_minus_2_task_at_item_level.drop(columns=[\"id_x\", \"id_y\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "t_minus_2_task_at_item_level[\"selling_price\"] = np.round(\n", "    t_minus_2_task_at_item_level[\"selling_price\"], 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level[\"expected_value\"] = (\n", "    t_minus_2_task_at_item_level[\"selling_price\"]\n", "    * t_minus_2_task_at_item_level[\"expected_quantity\"]\n", ")\n", "t_minus_2_task_at_item_level[\"mapped_value\"] = (\n", "    t_minus_2_task_at_item_level[\"selling_price\"]\n", "    * t_minus_2_task_at_item_level[\"map_quantity\"]\n", ")\n", "t_minus_2_task_at_item_level[\"diff_value\"] = (\n", "    t_minus_2_task_at_item_level[\"expected_value\"]\n", "    - t_minus_2_task_at_item_level[\"mapped_value\"]\n", ")\n", "t_minus_2_task_at_item_level[\"diff_qty\"] = (\n", "    t_minus_2_task_at_item_level[\"expected_quantity\"]\n", "    - t_minus_2_task_at_item_level[\"map_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_task_at_item_level[t_minus_2_task_at_item_level.item_id==10003117]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level.mapped_value.sum()\n", "t_minus_2_task_at_item_level.loc[\n", "    t_minus_2_task_at_item_level[\"mapped_value\"]\n", "    > t_minus_2_task_at_item_level[\"expected_value\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level.diff_value = t_minus_2_task_at_item_level.diff_value.astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_task_at_item_level.to_csv('t_minus_2_task_at_item_level.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_t_minus_2_qty_facility_agg = (\n", "    t_minus_2_task_at_item_level.groupby([\"facility_name\", \"latest_state\"])\n", "    .agg(\n", "        {\n", "            \"received_date\": \"max\",\n", "            \"return_task_id\": \"nunique\",\n", "            \"expected_quantity\": \"sum\",\n", "            \"map_quantity\": \"sum\",\n", "            \"diff_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "state_t_minus_2_qty_facility_agg[\"data_view\"] = \"ItemCount\"\n", "state_t_minus_2_qty_facility_agg.rename(\n", "    columns={\n", "        \"expected_quantity\": \"expected\",\n", "        \"map_quantity\": \"map\",\n", "        \"diff_qty\": \"diff\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "\n", "state_t_minus_2_value__facility_agg = (\n", "    t_minus_2_task_at_item_level.groupby([\"facility_name\", \"latest_state\"])\n", "    .agg(\n", "        {\n", "            \"received_date\": \"max\",\n", "            \"return_task_id\": \"nunique\",\n", "            \"expected_value\": \"sum\",\n", "            \"mapped_value\": \"sum\",\n", "            \"diff_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "state_t_minus_2_value__facility_agg.rename(\n", "    columns={\"expected_value\": \"expected\", \"mapped_value\": \"map\", \"diff_value\": \"diff\"},\n", "    inplace=True,\n", ")\n", "state_t_minus_2_value__facility_agg[\"data_view\"] = \"ValueinRs\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_t_minus_2_qty_facility_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_t_minus_2_value__facility_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_t_minus_2_agg = pd.concat(\n", "    [state_t_minus_2_qty_facility_agg, state_t_minus_2_value__facility_agg],\n", "    axis=0,\n", "    sort=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comp_state_t_minus_2_agg_diff = state_t_minus_2_agg[\n", "    [\n", "        \"facility_name\",\n", "        \"latest_state\",\n", "        \"received_date\",\n", "        \"return_task_id\",\n", "        \"diff\",\n", "        \"data_view\",\n", "    ]\n", "][state_t_minus_2_agg.latest_state == 7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["not_comp_state_t_minus_2_agg_diff = state_t_minus_2_agg[\n", "    [\n", "        \"facility_name\",\n", "        \"latest_state\",\n", "        \"received_date\",\n", "        \"return_task_id\",\n", "        \"diff\",\n", "        \"data_view\",\n", "    ]\n", "][state_t_minus_2_agg.latest_state != 7]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comp_state_t_minus_2_agg_diff = (\n", "    comp_state_t_minus_2_agg_diff.groupby(\n", "        [\"facility_name\", \"received_date\", \"data_view\"]\n", "    )\n", "    .agg({\"diff\": \"sum\"})\n", "    .reset_index()\n", ")\n", "comp_state_t_minus_2_agg_diff.rename(columns={\"diff\": \"comp_diff\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["not_comp_state_t_minus_2_agg_diff = (\n", "    not_comp_state_t_minus_2_agg_diff.groupby(\n", "        [\"facility_name\", \"received_date\", \"data_view\"]\n", "    )\n", "    .agg({\"diff\": \"sum\"})\n", "    .reset_index()\n", ")\n", "not_comp_state_t_minus_2_agg_diff.rename(\n", "    columns={\"diff\": \"not_comp_diff\"}, inplace=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overall Return"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Type 1 is for quantity and 2 is Value\n", "\n", "t_minus_2_qty_facility_agg = (\n", "    t_minus_2_task_at_item_level.groupby([\"facility_name\"])\n", "    .agg(\n", "        {\n", "            \"received_date\": \"max\",\n", "            \"return_task_id\": \"nunique\",\n", "            \"expected_quantity\": \"sum\",\n", "            \"map_quantity\": \"sum\",\n", "            \"diff_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "t_minus_2_qty_facility_agg.rename(\n", "    columns={\n", "        \"expected_quantity\": \"expected\",\n", "        \"map_quantity\": \"map\",\n", "        \"diff_qty\": \"diff\",\n", "    },\n", "    inplace=True,\n", ")\n", "t_minus_2_qty_facility_agg[\"data_view\"] = \"ItemCount\"\n", "# t_minus_2_qty_facility_agg\n", "\n", "t_minus_2_value__facility_agg = (\n", "    t_minus_2_task_at_item_level.groupby([\"facility_name\"])\n", "    .agg(\n", "        {\n", "            \"received_date\": \"max\",\n", "            \"return_task_id\": \"nunique\",\n", "            \"expected_value\": \"sum\",\n", "            \"mapped_value\": \"sum\",\n", "            \"diff_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "t_minus_2_value__facility_agg.rename(\n", "    columns={\"expected_value\": \"expected\", \"mapped_value\": \"map\", \"diff_value\": \"diff\"},\n", "    inplace=True,\n", ")\n", "t_minus_2_value__facility_agg[\"data_view\"] = \"ValueinRs\"\n", "t_minus_2_agg = pd.concat(\n", "    [t_minus_2_qty_facility_agg, t_minus_2_value__facility_agg], axis=0, sort=False\n", ")\n", "t_minus_2_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_qty_facility_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_agg.diff=t_minus_2_agg.diff.round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result[\"expected_value\"] = (\n", "    mtd_result[\"selling_price\"] * mtd_result[\"expected_quantity\"]\n", ")\n", "mtd_result[\"mapped_value\"] = mtd_result[\"selling_price\"] * mtd_result[\"map_quantity\"]\n", "mtd_result[\"diff_value\"] = mtd_result[\"expected_value\"] - mtd_result[\"mapped_value\"]\n", "mtd_result[\"diff_qty\"] = mtd_result[\"expected_quantity\"] - mtd_result[\"map_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result[[\"expected_quantity\", \"map_quantity\"]].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_result_qty_facility_agg = (\n", "    mtd_result.groupby([\"facility_name\"]).agg({\"diff_qty\": \"sum\"}).reset_index()\n", ")\n", "mtd_result_qty_facility_agg.rename(columns={\"diff_qty\": \"mtd_diff\"}, inplace=True)\n", "mtd_result_qty_facility_agg[\"data_view\"] = \"ItemCount\"\n", "mtd_result_qty_facility_agg\n", "mtd_result_value_facility_agg = (\n", "    mtd_result.groupby([\"facility_name\"]).agg({\"diff_value\": \"sum\"}).reset_index()\n", ")\n", "mtd_result_value_facility_agg.rename(columns={\"diff_value\": \"mtd_diff\"}, inplace=True)\n", "mtd_result_value_facility_agg[\"data_view\"] = \"ValueinRs\"\n", "mtd_agg = pd.concat(\n", "    [mtd_result_qty_facility_agg, mtd_result_value_facility_agg], axis=0, sort=False\n", ")\n", "mtd_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mtd_agg.mtd_diff.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mtd_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns = t_minus_2_agg.merge(\n", "    mtd_agg, how=\"left\", on=[\"facility_name\", \"data_view\"]\n", ").reset_index()\n", "\n", "# overall_reutrns.drop(columns=['facility_name_x'], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns[\"expected\"] = overall_reutrns.expected.astype(int)\n", "overall_reutrns[\"map\"] = overall_reutrns.map.astype(int)\n", "# overall_reutrns['diff']=overall_reutrns.diff.astype(int)\n", "overall_reutrns[\"mtd_diff\"] = overall_reutrns.mtd_diff.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# overall_reutrns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# comp_state_t_minus_2_agg_diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns_1 = overall_reutrns.merge(\n", "    comp_state_t_minus_2_agg_diff,\n", "    how=\"left\",\n", "    on=[\"facility_name\", \"received_date\", \"data_view\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns_2 = overall_reutrns_1.merge(\n", "    not_comp_state_t_minus_2_agg_diff,\n", "    how=\"left\",\n", "    on=[\"facility_name\", \"received_date\", \"data_view\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns_2.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns_2.comp_diff = overall_reutrns_2.comp_diff.astype(int)\n", "overall_reutrns_2.not_comp_diff = overall_reutrns_2.not_comp_diff.astype(int)\n", "# overall_reutrns_2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Return Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_task_at_item_level.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_item_level_with_order_id_df = t_minus_2_task_at_item_level.groupby([\"FacilityName\"]).agg(\n", "#     {\n", "#         \"ReturnTaskID\": \"nunique\",\n", "#         \"ExpectedQuantity\": \"sum\",\n", "#         \"MappedQuantity\": \"sum\",\n", "#         \"ExpectedValue\":\"sum\",\n", "#         \"MappedValue\":\"sum\",\n", "#         \"DiffValue\": \"sum\",\n", "#     }\n", "# )\n", "\n", "t_minus_2_task_both_types = (\n", "    t_minus_2_task_at_item_level.groupby([\"facility_name\", \"type\"])\n", "    .agg(\n", "        {\n", "            \"received_date\": \"max\",\n", "            \"expected_quantity\": \"sum\",\n", "            \"map_quantity\": \"sum\",\n", "            \"diff_qty\": \"sum\",\n", "            \"expected_value\": \"sum\",\n", "            \"mapped_value\": \"sum\",\n", "            \"diff_value\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "t_minus_2_task_both_types = t_minus_2_task_both_types.fillna(0)\n", "\n", "\n", "# t_minus_2_task_both_types\n", "\n", "\n", "t_minus_2_task_both_types_pivot = t_minus_2_task_both_types.pivot(\n", "    index=\"facility_name\",\n", "    columns=\"type\",\n", "    values=[\n", "        \"received_date\",\n", "        \"expected_quantity\",\n", "        \"map_quantity\",\n", "        \"expected_value\",\n", "        \"mapped_value\",\n", "        \"diff_qty\",\n", "        \"diff_value\",\n", "    ],\n", ")\n", "t_minus_2_task_both_types_pivot.columns = [\n", "    \"_\".join(col) for col in t_minus_2_task_both_types_pivot.columns\n", "]\n", "t_minus_2_task_both_types_pivot = t_minus_2_task_both_types_pivot.fillna(0)\n", "\n", "# t_minus_2_task_both_types_pivot[['']]\n", "\n", "# task_item_level_with_order_id_df2 = pd.merge(\n", "#     task_item_level_with_order_id_df,\n", "#     task_item_level_with_order_id_df2,\n", "#     on=\"FacilityName\",\n", "#     how=\"inner\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_task_both_types_pivot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def appendWithoutV<PERSON>ues():\n", "    t_minus_2_task_both_types_pivot[\"received_date_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"expected_quantity_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"map_quantity_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"expected_value_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"mapped_value_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"diff_qty_without_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"diff_value_without_oid\"] = 0\n", "\n", "\n", "def appendWith<PERSON><PERSON>ues():\n", "    t_minus_2_task_both_types_pivot[\"received_date_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"expected_quantity_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"map_quantity_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"expected_value_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"mapped_value_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"diff_qty_with_oid\"] = 0\n", "    t_minus_2_task_both_types_pivot[\"diff_value_with_oid\"] = 0\n", "\n", "\n", "if not \"received_date_without_oid\" in t_minus_2_task_both_types_pivot.columns:\n", "    appendWithoutValues()\n", "if not \"received_date_with_oid\" in t_minus_2_task_both_types_pivot.columns:\n", "    appendWithValues()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_task_both_types_pivot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %store -r t_minus_2_with_oid_bad_qty\n", "# %store -r t_minus_2_without_oid_bad_qty\n", "t_minus_2_with_oid_bad_qty_df = pd.read_sql_query(\n", "    sql=t_minus_2_with_oid_bad_qty,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")\n", "t_minus_2_without_oid_bad_qty_df = pd.read_sql_query(\n", "    sql=t_minus_2_without_oid_bad_qty,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_with_oid_bad_qty_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df[t_minus_2_with_oid_bad_qty_df.bad_qty > 0][\n", "    [\"return_task_id\", \"invoice_id\", \"invoice_id1\", \"order_id\", \"item_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_result[t_minus_2_result.invoice_id=='BGC100R200004231']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_result[['return_task_id','invoice_id','order_id','item_id','selling_price']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result.item_id = t_minus_2_result.item_id.astype(int)\n", "t_minus_2_with_oid_bad_qty_df.item_id = t_minus_2_with_oid_bad_qty_df.item_id.astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df = t_minus_2_with_oid_bad_qty_df[\n", "    t_minus_2_with_oid_bad_qty_df.bad_qty > 0\n", "][\n", "    [\"facility_name\", \"return_task_id\", \"invoice_id\", \"order_id\", \"item_id\", \"bad_qty\"]\n", "].merge(\n", "    t_minus_2_result[\n", "        [\"return_task_id\", \"invoice_id\", \"order_id\", \"item_id\", \"selling_price\"]\n", "    ],\n", "    how=\"inner\",\n", "    on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df[\"loss\"] = (\n", "    t_minus_2_with_oid_bad_qty_df[\"selling_price\"]\n", "    * t_minus_2_with_oid_bad_qty_df[\"bad_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_with_oid_bad_qty_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_result[\n", "    (t_minus_2_result.order_id == \"89407186\") & (t_minus_2_result.item_id == 10003117)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df = (\n", "    t_minus_2_with_oid_bad_qty_df.groupby(\n", "        [\"facility_name\", \"return_task_id\", \"order_id\", \"item_id\"]\n", "    )\n", "    .agg({\"selling_price\": \"mean\", \"bad_qty\": \"sum\", \"loss\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_with_oid_bad_qty_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df[\n", "    [\"return_task_id\", \"order_id\", \"item_id\"]\n", "].drop_duplicates().shape[0] - t_minus_2_with_oid_bad_qty_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_agg = (\n", "    t_minus_2_with_oid_bad_qty_df.groupby([\"facility_name\"])\n", "    .agg({\"bad_qty\": \"sum\", \"loss\": \"sum\"})\n", "    .reset_index()\n", ")\n", "t_minus_2_with_oid_bad_qty_agg.rename(\n", "    columns={\"bad_qty\": \"with_oid_bad_qty\", \"loss\": \"with_oid_loss\"}, inplace=True\n", ")\n", "t_minus_2_without_oid_bad_qty_agg = (\n", "    t_minus_2_without_oid_bad_qty_df.groupby([\"facility_name\"])\n", "    .agg({\"bad_qty\": \"sum\", \"loss\": \"sum\"})\n", "    .reset_index()\n", ")\n", "t_minus_2_without_oid_bad_qty_agg.rename(\n", "    columns={\"bad_qty\": \"without_oid_bad_qty\", \"loss\": \"without_oid_loss\"}, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_without_oid_bad_qty_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_without_oid_bad_qty_agg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_qty_agg = pd.merge(\n", "    t_minus_2_with_oid_bad_qty_agg,\n", "    t_minus_2_without_oid_bad_qty_agg,\n", "    how=\"left\",\n", "    on=\"facility_name\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_qty_agg = bad_qty_agg.fillna(0)\n", "# bad_qty_agg\n", "return_processing = t_minus_2_task_both_types_pivot.merge(\n", "    bad_qty_agg, how=\"left\", on=\"facility_name\"\n", ")\n", "return_processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing[\"product_value\"] = (\n", "    return_processing[\"expected_value_with_oid\"]\n", "    + return_processing[\"expected_value_without_oid\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_val = return_processing[\n", "    [\"received_date_with_oid\", \"facility_name\", \"product_value\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_val"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_qty = return_processing[\n", "    [\n", "        \"facility_name\",\n", "        \"received_date_with_oid\",\n", "        \"map_quantity_with_oid\",\n", "        \"with_oid_bad_qty\",\n", "        \"map_quantity_without_oid\",\n", "        \"without_oid_bad_qty\",\n", "    ]\n", "]\n", "return_processing_qty[\"return_qty_processed\"] = (\n", "    return_processing_qty[\"map_quantity_with_oid\"]\n", "    + return_processing_qty[\"map_quantity_without_oid\"]\n", ")\n", "return_processing_qty[\"data_view\"] = \"Quantity\"\n", "return_processing_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_value = return_processing[\n", "    [\n", "        \"facility_name\",\n", "        \"received_date_with_oid\",\n", "        \"mapped_value_with_oid\",\n", "        \"with_oid_loss\",\n", "        \"mapped_value_without_oid\",\n", "        \"without_oid_loss\",\n", "    ]\n", "]\n", "return_processing_value[\"return_value_processed\"] = (\n", "    return_processing_value[\"mapped_value_with_oid\"]\n", "    + return_processing_value[\"mapped_value_without_oid\"]\n", ")\n", "return_processing_value[\"data_view\"] = \"Value\"\n", "# return_processing_value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# return_processing_value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_quantity = return_processing_qty.rename(\n", "    columns={\n", "        \"facility_name\": \"facility_name\",\n", "        \"received_date_with_oid\": \"received_date\",\n", "        \"map_quantity_with_oid\": \"processed_non_rpu\",\n", "        \"return_qty_processed\": \"returns_processed\",\n", "        \"with_oid_bad_qty\": \"damage_non_rpu\",\n", "        \"map_quantity_without_oid\": \"processed_rpu\",\n", "        \"without_oid_bad_qty\": \"damage_rpu\",\n", "    }\n", ")\n", "return_processing_values = return_processing_value.rename(\n", "    columns={\n", "        \"facility_name\": \"facility_name\",\n", "        \"received_date_with_oid\": \"received_date\",\n", "        \"mapped_value_with_oid\": \"processed_non_rpu\",\n", "        \"return_value_processed\": \"returns_processed\",\n", "        \"with_oid_loss\": \"damage_non_rpu\",\n", "        \"mapped_value_without_oid\": \"processed_rpu\",\n", "        \"without_oid_loss\": \"damage_rpu\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_df = pd.concat(\n", "    [return_processing_quantity, return_processing_values], axis=0, sort=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_df = return_processing_df[\n", "    [\n", "        \"received_date\",\n", "        \"facility_name\",\n", "        \"data_view\",\n", "        \"returns_processed\",\n", "        \"processed_non_rpu\",\n", "        \"damage_non_rpu\",\n", "        \"processed_rpu\",\n", "        \"damage_rpu\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_df.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_df[\"damage_rpu\"] = return_processing_df.damage_rpu.astype(int)\n", "return_processing_df[\"damage_non_rpu\"] = return_processing_df.damage_non_rpu.astype(int)\n", "return_processing_df[\n", "    \"returns_processed\"\n", "] = return_processing_df.returns_processed.astype(int)\n", "return_processing_df[\n", "    \"processed_non_rpu\"\n", "] = return_processing_df.processed_non_rpu.astype(int)\n", "return_processing_df[\"processed_rpu\"] = return_processing_df.processed_rpu.astype(int)\n", "# return_processing_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_processing_df_final = (\n", "    return_processing_df.groupby([\"received_date\", \"data_view\"])\n", "    .agg(\n", "        {\n", "            \"returns_processed\": \"sum\",\n", "            \"processed_non_rpu\": \"sum\",\n", "            \"damage_non_rpu\": \"sum\",\n", "            \"processed_rpu\": \"sum\",\n", "            \"damage_rpu\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Return Effeciency (last 7days) (FC)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tz = pytz.timezone(\"Asia/Kolkata\")\n", "# first_day= (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "# last_day = (datetime.now(tz) - timed<PERSON>ta(days=9)).strftime(\"%Y-%m-%d\")\n", "# last_day, first_day\n", "# mtd_result['received_date']=mtd_result['received_date'].astype('datetime64[ns]')\n", "# first_day.astype('datetime64[ns]')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7_data = pd.read_sql_query(sql=t_minus_7, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7_data.received_date.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_7_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7_data = t_minus_7_data[\n", "    [\n", "        \"facility_name\",\n", "        \"received_date\",\n", "        \"return_task_id\",\n", "        \"received_state\",\n", "        \"latest_state\",\n", "        \"received_ts\",\n", "        \"latest_ts\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_7_data[\"time_diff_in_hour\"] = (\n", "    (t_minus_7_data[\"latest_ts\"] - t_minus_7_data[\"received_ts\"]).astype(\n", "        \"timedelta64[m]\"\n", "    )\n", "    / 60\n", ").round(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_7_data.sort_values(by=['received_date','time_diff_in_hour'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_task = (\n", "    t_minus_7_data.groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "task_completion_mean = (\n", "    t_minus_7_data[t_minus_7_data.latest_state == 7]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"time_diff_in_hour\": \"mean\"})\n", "    .reset_index()\n", ")\n", "task_completion_before_12 = (\n", "    t_minus_7_data[\n", "        (t_minus_7_data.latest_state == 7) & (t_minus_7_data.time_diff_in_hour <= 12)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "task_completion_before_24 = (\n", "    t_minus_7_data[\n", "        (t_minus_7_data.latest_state == 7)\n", "        & (t_minus_7_data.time_diff_in_hour > 12)\n", "        & (t_minus_7_data.time_diff_in_hour <= 24)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "# task_completion_after_24 = t_minus_7_data[(t_minus_7_data.latest_state == 7)& (t_minus_7_data.time_diff_in_hour >= 24)].groupby([\"received_date\", \"facility_name\"]).agg({\"return_task_id\": \"nunique\"}).reset_index()\n", "\n", "\n", "task_completion_before_48 = (\n", "    t_minus_7_data[\n", "        (t_minus_7_data.latest_state == 7)\n", "        & (t_minus_7_data.time_diff_in_hour > 24)\n", "        & (t_minus_7_data.time_diff_in_hour <= 48)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "task_completion_after_48 = (\n", "    t_minus_7_data[\n", "        (t_minus_7_data.latest_state == 7) & (t_minus_7_data.time_diff_in_hour > 48)\n", "    ]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "task_not_completed = (\n", "    t_minus_7_data[t_minus_7_data.latest_state != 7]\n", "    .groupby([\"received_date\", \"facility_name\"])\n", "    .agg({\"return_task_id\": \"nunique\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_completion_before_48"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_completion_before_24\n", "\n", "# task_completion_before_48, task_completion_after_24, task_completion_after_48"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = total_task.merge(\n", "    task_completion_before_12,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "a\n", "a.rename(\n", "    columns={\n", "        \"return_task_id_x\": \"total_task\",\n", "        \"return_task_id_y\": \"processed_in_12_hours\",\n", "    },\n", "    inplace=True,\n", ")\n", "# a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = a.merge(\n", "    task_completion_before_24,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "b.rename(columns={\"return_task_id\": \"processed_in_24_hours\"}, inplace=True)\n", "# b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# task_completion_before_48"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c = b.merge(\n", "    task_completion_before_48,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "c.rename(columns={\"return_task_id\": \"processed_in_48_hours\"}, inplace=True)\n", "# c"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = c.merge(\n", "    task_completion_after_48,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "d.rename(columns={\"return_task_id\": \"processed_after_48_hours\"}, inplace=True)\n", "# d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["e = d.merge(\n", "    task_not_completed,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "e.rename(columns={\"return_task_id\": \"pending_tasks\"}, inplace=True)\n", "# e"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f = e.merge(\n", "    task_completion_mean,\n", "    how=\"left\",\n", "    left_on=[\"received_date\", \"facility_name\"],\n", "    right_on=[\"received_date\", \"facility_name\"],\n", ")\n", "f.rename(columns={\"time_diff_in_hour\": \"avg_time_to_close_task\"}, inplace=True)\n", "f.fillna(int(0), inplace=True)\n", "f[\"avg_time_to_close_task\"].round(0)\n", "# f"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# f"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = (\n", "    f.groupby([\"received_date\"])\n", "    .agg(\n", "        {\n", "            \"total_task\": \"sum\",\n", "            \"processed_in_12_hours\": \"sum\",\n", "            \"processed_in_24_hours\": \"sum\",\n", "            \"processed_in_48_hours\": \"sum\",\n", "            \"processed_after_48_hours\": \"sum\",\n", "            \"pending_tasks\": \"sum\",\n", "            \"avg_time_to_close_task\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_effeciency_last_7days_FC = g"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_effeciency_last_7days_FC.processed_in_12_hours = return_effeciency_last_7days_FC.processed_in_12_hours.astype(\n", "    int\n", ")\n", "return_effeciency_last_7days_FC.processed_in_24_hours = return_effeciency_last_7days_FC.processed_in_24_hours.astype(\n", "    int\n", ")\n", "return_effeciency_last_7days_FC.processed_in_48_hours = return_effeciency_last_7days_FC.processed_in_48_hours.astype(\n", "    int\n", ")\n", "return_effeciency_last_7days_FC.processed_after_48_hours = return_effeciency_last_7days_FC.processed_after_48_hours.astype(\n", "    int\n", ")\n", "return_effeciency_last_7days_FC.pending_tasks = return_effeciency_last_7days_FC.pending_tasks.astype(\n", "    int\n", ")\n", "return_effeciency_last_7days_FC.avg_time_to_close_task = return_effeciency_last_7days_FC.avg_time_to_close_task.astype(\n", "    int\n", ")\n", "# return_effeciency_last_7days_FC"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Raw Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_with_oid_bad_qty_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_without_oid_bad_qty_df[\"order_id\"] = 0\n", "t_minus_2_without_oid_bad_qty_df = (\n", "    t_minus_2_without_oid_bad_qty_df.groupby(\n", "        [\"return_task_id\", \"item_id\", \"facility_name\", \"order_id\"]\n", "    )\n", "    .agg({\"bad_qty\": \"sum\", \"loss\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_without_oid_bad_qty_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv = t_minus_2_task_at_item_level.merge(\n", "    t_minus_2_with_oid_bad_qty_df,\n", "    how=\"left\",\n", "    on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv[t_minus_2_csv.order_id == 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv.expected_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_csv.to_csv('abc.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_task_at_item_level.expected_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv.groupby(\n", "    [\"return_task_id\", \"order_id\", \"item_id\"]\n", ").size().reset_index().sort_values(0, ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_csv.loc[ t_minus_2_csv.return_task_id==444201]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data=t_minus_2_csv.groupby([\"return_task_id\",\n", "#                          \"task_created_at\",\n", "#                          \"received_ts\",\n", "#                          \"latest_ts\",\n", "#                          \"latest_states\",\n", "#                          \"facility_name_x\",\n", "#                          \"ancestor\",\n", "#                          \"order_id\",\n", "#                          \"item_id\",\n", "#                          \"item_name\"\n", "\n", "#                         ]).agg({\"expected_quantity\": \"sum\",\n", "#                                 \"map_quantity\":\"sum\",\n", "#                                 \"bad_qty\":\"sum\",\n", "#                                 \"diff_qty\":\"sum\",\n", "#                                 \"expected_value\":\"sum\",\n", "#                                 \"mapped_value\":\"sum\",\n", "#                                 \"diff_value\":\"sum\"\n", "\n", "\n", "#                                      }).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a.bad_qty.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data.expected_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %store -r abc\n", "# abcd = pd.read_sql_query(sql=abc, con=con_redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_csv=t_minus_2_csv.merge(t_minus_2_without_oid_bad_qty_df, how=\"left\", on =['return_task_id', 'item_id'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_csv.to_csv('t_minus_2_csv.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_retail = pb.get_connection(\"retail\")\n", "city_dp_mapping_df = pd.read_sql_query(\n", "    sql=city_dp_mapping,\n", "    con=con_retail,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping_df[city_dp_mapping_df.order_id == \"None\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping_df.replace({\"order_id\": {\"None\": 0}}, inplace=True)\n", "# city_dp_mapping_df.sort_values('order_id')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping_df[city_dp_mapping_df.order_id == 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping_df.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"redshift\")\n", "station_mapping_df = pd.read_sql_query(\n", "    sql=station_mapping,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_dp_mapping_df.station_id = city_dp_mapping_df.station_id.astype(int)\n", "station_mapping_df.station_id = station_mapping_df.station_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["station_city_dp_mapping_df = city_dp_mapping_df.merge(\n", "    station_mapping_df, how=\"inner\", left_on=[\"station_id\"], right_on=[\"station_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# station_city_dp_mapping_df.to_csv('station_city_dp_mapping_df.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"redshift\")\n", "return_reason_mapping_df = pd.read_sql_query(\n", "    sql=return_reason_mapping,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# station_city_dp_mapping_df[station_city_dp_mapping_df['order_id']==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_csv[t_minus_2_csv.order_id == 0].head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final = t_minus_2_csv.merge(\n", "    station_city_dp_mapping_df, how=\"inner\", on=[\"return_task_id\", \"order_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_reason_mapping_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_reason_mapping_df.replace({\"order_id\": {\"None\": 0}}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final = t_minus_2_raw_data_final.merge(\n", "    return_reason_mapping_df,\n", "    how=\"left\",\n", "    left_on=[\"return_task_id\", \"order_id\"],\n", "    right_on=[\"return_task_id\", \"order_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con_redshift = pb.get_connection(\"redshift\")\n", "mapping_type_df = pd.read_sql_query(\n", "    sql=mapping_type,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_type_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_type_df.rename(\n", "    columns={\n", "        \"rtoi_return_task_id\": \"return_task_id\",\n", "        \"rtoi_order_id\": \"order_id\",\n", "        \"rtoi_item_id\": \"item_id\",\n", "    },\n", "    inplace=True,\n", ")\n", "mapping_type_df.replace({\"order_id\": {\"None\": 0}}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final = t_minus_2_raw_data_final.merge(\n", "    mapping_type_df[[\"return_task_id\", \"order_id\", \"item_id\", \"mapping_type\"]],\n", "    how=\"left\",\n", "    on=[\"return_task_id\", \"order_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_finall.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final.drop(columns=['external_order_id'], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tz = pytz.timezone(\"Asia/Kolkata\")\n", "today = (datetime.now(tz) - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final1 = t_minus_2_raw_data_final[\n", "    [\n", "        \"return_task_id\",\n", "        \"task_created_at\",\n", "        \"received_ts\",\n", "        \"latest_ts\",\n", "        \"latest_states\",\n", "        \"city_name\",\n", "        \"station_name\",\n", "        \"dp_name\",\n", "        \"dp_unique_name\",\n", "        \"facility_name_x\",\n", "        \"mapping_type\",\n", "        \"ancestor\",\n", "        \"order_id\",\n", "        \"return_reason\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"expected_quantity\",\n", "        \"map_quantity\",\n", "        \"bad_qty\",\n", "        \"diff_qty\",\n", "        \"expected_value\",\n", "        \"mapped_value\",\n", "        \"diff_value\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final1.expected_value = t_minus_2_raw_data_final1.expected_value.astype(\n", "    int\n", ")\n", "t_minus_2_raw_data_final1.mapped_value = t_minus_2_raw_data_final1.mapped_value.astype(\n", "    int\n", ")\n", "t_minus_2_raw_data_final1.diff_value = t_minus_2_raw_data_final1.diff_value.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final1.replace{}\n", "t_minus_2_raw_data_final1.replace({\"ancestor\": {\"0\": \"\"}}, inplace=True)\n", "t_minus_2_raw_data_final1.replace({\"order_id\": {\"0\": \"\"}}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final1.rename(\n", "    columns={\n", "        \"return_task_id\": \"WH Task ID\",\n", "        \"task_created_at\": \"Created Timestamp\",\n", "        \"received_ts\": \"Received Timestamp\",\n", "        \"latest_ts\": \"Latest State Timestamp\",\n", "        \"latest_states\": \"Latest State\",\n", "        \"city_name\": \"City\",\n", "        \"station_name\": \"Station\",\n", "        \"dp_name\": \"Source DP\",\n", "        \"dp_unique_name\": \"DP Unique Name\",\n", "        \"facility_name_x\": \"Designation Warehouse\",\n", "        \"mapping_type\": \"Mapping Type\",\n", "        \"ancestor\": \"Order ID \",\n", "        \"order_id\": \"SubOrder ID\",\n", "        \"return_reason\": \"Return Reason\",\n", "        \"item_id\": \"Item ID\",\n", "        \"item_name\": \"Item Name\",\n", "        \"expected_quantity\": \"Sent Item Quantity\",\n", "        \"map_quantity\": \"Received Item Quantity\",\n", "        \"bad_qty\": \"Damaged Quantity\",\n", "        \"diff_qty\": \"Loss in Quantity\",\n", "        \"expected_value\": \"Sent Value in Rupees\",\n", "        \"mapped_value\": \"Received Value in Rupees\",\n", "        \"diff_value\": \"Loss in Rupees\",\n", "    },\n", "    inplace=True,\n", ")\n", "# t_minus_2_raw_data_final1\n", "# t_minus_2_raw_data_final1.to_csv(\"/home/<USER>/Direct_Return_Report/Direct_Returns_\" + today + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# overall_reutrns.diff\n", "# overall_reutrns.diff.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t_minus_2_raw_data_final1.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openpyxl\n", "\n", "df2 = t_minus_2_raw_data_final1.copy()\n", "raw_d = df2[[\"Station\"]].drop_duplicates().reset_index()\n", "\n", "writer = pd.ExcelWriter(\"/tmp/Direct_Returns_\" + today + \".xlsx\")\n", "for i in range(0, len(raw_d) - 1):\n", "    part = raw_d.at[i, \"Station\"]\n", "    station_data = df2[df2.Station == part]\n", "    station_data.to_excel(writer, sheet_name=part, index=False)\n", "writer.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_val.rename(columns={\"product_value\": \"product_id_count\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prod_count_df = pd.read_sql_query(\n", "    sql=product_count,\n", "    con=con_redshift,\n", "    params={\"run_period_end\": run_period_end, \"run_period_start\": run_period_start},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# prod_count_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_count = pd.concat([prod_count_df, prod_val], axis=0, sort=False).reset_index()\n", "p_count.drop(\n", "    columns=[\"received_date_with_oid\", \"facility_id\", \"facility_name\"], inplace=True\n", ")\n", "p_count.product_id_count = p_count.product_id_count.astype(int)\n", "# p_count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns2 = pd.concat([overall_reutrns, p_count], axis=1, sort=False)\n", "# overall_reutrns2.product_id_count=overall_reutrns2.product_id_count.astype(int)\n", "overall_reutrns2.head()\n", "\n", "# overall_reutrns3=overall_reutrns2.groupby(['received_date','data_view']).agg({\"return_task_id\":\"sum\",\n", "#                                                             \"expected\":\"sum\",\n", "#                                                             \"map\":\"sum\",\n", "#                                                             \"diff\":\"sum\",\n", "#                                                             \"product_id_count\":\"sum\",\n", "#                                                             \"mtd_diff\":\"sum\"\n", "\n", "#                                                                              }).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# overall_reutrns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_reutrns_3 = pd.concat([overall_reutrns_2, p_count], axis=1, sort=False)\n", "overall_reutrns_3.product_id_count = overall_reutrns_3.product_id_count.astype(int)\n", "overall_reutrns_3.head()\n", "overall_reutrns_4 = (\n", "    overall_reutrns_3.groupby([\"received_date\", \"data_view\"])\n", "    .agg(\n", "        {\n", "            \"return_task_id\": \"sum\",\n", "            \"expected\": \"sum\",\n", "            \"map\": \"sum\",\n", "            \"diff\": \"sum\",\n", "            \"comp_diff\": \"sum\",\n", "            \"not_comp_diff\": \"sum\",\n", "            \"product_id_count\": \"sum\",\n", "            \"mtd_diff\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "overall_reutrns_4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# overall_reutrns=overall_reutrns3\n", "overall_reutrns = overall_reutrns_4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = overall_reutrns.to_dict(orient=\"rows\")\n", "items2 = return_processing_df_final.to_dict(orient=\"rows\")\n", "items3 = return_effeciency_last_7days_FC.to_dict(orient=\"rows\")\n", "items4 = today\n", "# items5=  prod_count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"Direct Returns Report for \" + today\n", "\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>,\",\n", "    \"<EMAIL>\",\n", "    \"dl3_patpar<PERSON>@grofers.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"dl3_patpar<PERSON>@grofers.com\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "# to_email = [\"<EMAIL>\"]\n", "\n", "email_template = \"email_template.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/warehouse/returns/report/direct_return/\"\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(\n", "    products1=items1, products2=items2, products3=items3, products4=items4\n", ")\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\"/tmp/Direct_Returns_\" + today + \".xlsx\"],\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### email_list\n", "# '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>,','<EMAIL>,','<EMAIL>,','<EMAIL>','<EMAIL>'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df.head(10)\n", "# mapping_type_df.fillna(0,inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df.replace({'rtoi_order_id':{'None': 0}},inplace=True)\n", "# #((not(math.isnan(x['rtgi_item_id']))) & (x['rtgi_item_id']>0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import math\n", "\n", "# def mapping(x):\n", "#     if (\n", "#         x[((x['rtoi_map_qty']> x['rtgi_map_qty']) & (x['rtgi_map_qty']>0)) |\n", "#           ((x['rtgi_item_id'].notnull()) & (x['rtgi_item_id']>0))]\n", "#        ):\n", "#        # x['mapping_type']='Manual'\n", "#         return 'Manual'\n", "#     else:\n", "\n", "#         #x['mapping_type']=''\n", "#         return ''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df.iloc[0,:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# #if(mapping_type_df['rtgi_item_id'].notnull() &\n", "# #mapping_type_df['rtgi_item_id']>0\n", "\n", "# # x[\n", "# #     ((x['rtoi_map_qty']> x['rtgi_map_qty']) & (x['rtgi_map_qty']>0))|\n", "# #     ((x['rtgi_item_id'].notnull()) & (x['rtgi_item_id']>0))\n", "# # ]\n", "# a = mapping_type_df[\n", "#     ((mapping_type_df['rtoi_map_qty']> mapping_type_df['rtgi_map_qty']) & (mapping_type_df['rtgi_map_qty']>0))|\n", "#     ((mapping_type_df['rtgi_item_id'].notnull()) & (mapping_type_df['rtgi_item_id']>0))\n", "# ]\n", "# a[\"mapping_type\"]=\"Manual\"\n", "# a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# b=mapping_type_df[\n", "#     ((mapping_type_df['rtoi_item_id']> 0) & (mapping_type_df['rtoi_map_qty']>0)) &\n", "#     (mapping_type_df['rtgi_item_id'].isnull())\n", "# ]\n", "# b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df[mapping_type_df.rtoi_map_qty==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x=pd.concat([a,b], axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x.shape, mapping_type_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_type_df[mapping_type_df.rtoi_map_qty>0].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x[(x.rtoi_return_task_id=='399193') & (x.rtoi_order_id=='86537778') & (x.rtoi_item_id=='10045595')]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x.replace({'rtoi_order_id':{'None': 0}},inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# t_minus_2_raw_data_final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# xyz=t_minus_2_raw_data_final.merge(x[['rtoi_return_task_id','rtoi_order_id','rtoi_item_id','mapping_type']], how=\"left\", left_on=['return_task_id','order_id','item_id'],\n", "#                                right_on=['rtoi_return_task_id','rtoi_order_id','rtoi_item_id'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# xyz.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}