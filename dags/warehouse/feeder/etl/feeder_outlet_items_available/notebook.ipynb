{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["North_Star_Metric_Feeder_Items = \"\"\"Select outlet_id as feeder_id, o.name as feeder_name , item_id as feeder_item_id, quantity as feeder_item_id_quantity , \n", "convert_tz(i.updated_at,\"+00:00\",\"+05:30\") as feeder_inventory_updated_at \n", "from ims.ims_item_inventory i\n", "inner join retail.console_outlet o on i.outlet_id = o.id\n", "where outlet_id in (select distinct feeder_id from ims.feeder_outlet_mapping where active = 1) and i.active = 1 \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["North_Star_Metric_Outlet_Items = \"\"\"Select TT.*,\n", "CASE when feeder_live = 1 then CMS_update_inventory_for_feeder_enabled_items\n", "     when fnv_upper_limit is not null then CMS_update_inventory_fnv_upper_limit\n", "     when upper_limit is not null then CMS_update_inventory_upper_limit\n", "     else CMS_update_inventory end as CMS_Inventory_Update_Sent\n", "From \n", "(Select o.name as outlet,\n", "T.*\n", "FROM \n", "(Select inv.outlet_id,\n", "   inv.item_id as outlet_item_id,\n", "   inv.quantity as outlet_item_id_quantity,   \n", "   inv.feeder_quantity as feeder_quantity_available_at_outlet,\n", "   inv.item_inventory_updated_at,\n", "   bloc_inv.item_blocked_inventory_updated_at,\n", "   coalesce(order_blocked_quantity,0) as order_blocked_quantity,\n", "   coalesce(esto_blocked_quantity,0) as esto_blocked_quantity,\n", "   coalesce(tat_blocked_quantity,0) as tat_blocked_quantity ,\n", " \n", "   feeder_live,\n", " \n", "    (case when feeder_live = 1 and (inv.quantity > (tat_blocked_quantity + esto_blocked_quantity)) then (inv.quantity + inv.feeder_quantity - order_blocked_quantity - esto_blocked_quantity) else 0 end )as CMS_update_inventory_for_feeder_enabled_items,\n", " \n", "   upper_limit,\n", "\n", "   (case when upper_limit is not null then (upper_limit - order_blocked_quantity - esto_blocked_quantity) else 0 end )as CMS_update_inventory_upper_limit,\n", "  \n", "\n", "   fnv_upper_limit,\n", "\n", "  (case when fnv_upper_limit is not null  then (fnv_upper_limit - order_blocked_quantity - esto_blocked_quantity) else 0 end )as CMS_update_inventory_fnv_upper_limit,\n", "  \n", " (case when feeder_live = 0 and upper_limit is null and fnv_upper_limit is null then (inv.quantity - order_blocked_quantity - esto_blocked_quantity) else 0 end )as CMS_update_inventory,\n", "\n", " \n", "     inv.quantity - coalesce(bloc_inv.order_blocked_quantity,0) - coalesce(bloc_inv.esto_blocked_quantity,0) Physical_Inventory_at_the_outlet   \n", "   \n", "FROM\n", "\n", "(Select i.outlet_id, i.item_id, i.quantity, i.feeder_live, i.feeder_quantity, i.upper_limit, i.fnv_upper_limit, \n", "convert_tz(i.updated_at,\"+00:00\",\"+05:30\") as item_inventory_updated_at\n", "from ims.ims_item_inventory i\n", "where i.active = 1 and i.outlet_id in (select distinct outlet_id from ims.feeder_outlet_mapping where active = 1)\n", ")inv\n", "\n", "LEFT JOIN \n", "\n", "(select outlet_id,\n", "item_id, \n", "max(convert_tz(updated_at,\"+00:00\",\"+05:30\")) as item_blocked_inventory_updated_at,\n", "sum(order_blocked_quantity) as order_blocked_quantity, \n", "sum(esto_blocked_quantity) as esto_blocked_quantity, \n", "sum(tat_blocked_quantity) as tat_blocked_quantity\n", "From \n", "(select i.outlet_id, i.item_id, i.updated_at, \n", "case when i.blocked_type = 1 then i.quantity else 0 end as order_blocked_quantity,\n", "case when i.blocked_type = 2 then i.quantity else 0 end as esto_blocked_quantity,\n", "case when i.blocked_type = 3 then i.quantity else 0 end as tat_blocked_quantity\n", "from ims.ims_item_blocked_inventory i\n", "where i.active = 1 and i.outlet_id in (select distinct outlet_id from ims.feeder_outlet_mapping where active = 1)\n", "\n", "order by 1,2\n", ") T2\n", "Group by 1,2\n", ")bloc_inv\n", "\n", "on inv.outlet_id = bloc_inv.outlet_id and inv.item_id = bloc_inv.item_id\n", "\n", ") T\n", "left join retail.console_outlet o on T.outlet_id = o.id \n", "order by Physical_Inventory_at_the_outlet asc)TT\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["North_Star_Metric_Feeder_Items_o = pandas.read_sql_query(\n", "    sql=North_Star_Metric_Feeder_Items, con=retail_db\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["North_Star_Metric_Outlet_Items_o = pandas.read_sql_query(\n", "    sql=North_Star_Metric_Outlet_Items, con=retail_db\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_outlet_groups = \"\"\"select m.FEEDER_ID , o1.name as Feeder_name, m.OUTLET_ID , o.name as outlet_name\n", "from ims.feeder_outlet_mapping m\n", "inner join retail.console_outlet o on m.outlet_id = o.id\n", "inner join retail.console_outlet o1 on m.feeder_id = o1.id\n", "where m.active =1\n", "group by 1,2,3,4 \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_outlet_groups_o = pandas.read_sql_query(sql=Feeder_outlet_groups, con=retail_db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["All_feeder_outlet_item_combinations = pandas.merge(\n", "    North_Star_Met<PERSON>_Fe<PERSON>r_Items_o,\n", "    North_Star_Metric_Outlet_Items_o,\n", "    left_on=(\"feeder_item_id\"),\n", "    right_on=(\"outlet_item_id\"),\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations = pandas.merge(\n", "    All_feeder_outlet_item_combinations,\n", "    Feeder_outlet_groups_o,\n", "    left_on=(\"feeder_id\", \"outlet_id\"),\n", "    right_on=(\"FEEDER_ID\", \"OUTLET_ID\"),\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations[\n", "    \"Max_updated_at\"\n", "] = Mapped_feeder_outlet_item_combinations.apply(\n", "    lambda x: max(\n", "        x[\"item_inventory_updated_at\"],\n", "        x[\"item_blocked_inventory_updated_at\"],\n", "        x[\"feeder_inventory_updated_at\"],\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns = Mapped_feeder_outlet_item_combinations[\n", "    [\n", "        \"FEEDER_ID\",\n", "        \"Feeder_name\",\n", "        \"OUTLET_ID\",\n", "        \"outlet_name\",\n", "        \"Max_updated_at\",\n", "        \"feeder_item_id\",\n", "        \"feeder_item_id_quantity\",\n", "        \"feeder_inventory_updated_at\",\n", "        \"outlet_item_id\",\n", "        \"outlet_item_id_quantity\",\n", "        \"feeder_quantity_available_at_outlet\",\n", "        \"item_inventory_updated_at\",\n", "        \"item_blocked_inventory_updated_at\",\n", "        \"order_blocked_quantity\",\n", "        \"esto_blocked_quantity\",\n", "        \"tat_blocked_quantity\",\n", "        \"feeder_live\",\n", "        \"CMS_update_inventory_for_feeder_enabled_items\",\n", "        \"upper_limit\",\n", "        \"CMS_update_inventory_upper_limit\",\n", "        \"fnv_upper_limit\",\n", "        \"CMS_update_inventory_fnv_upper_limit\",\n", "        \"CMS_update_inventory\",\n", "        \"Physical_Inventory_at_the_outlet\",\n", "        \"CMS_Inventory_Update_Sent\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns = Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.item_blocked_inventory_updated_at = Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.item_blocked_inventory_updated_at.replace(\n", "    0, np.nan\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_dict = {\n", "    \"FEEDER_ID\": int,\n", "    \"Feeder_name\": str,\n", "    \"OUTLET_ID\": int,\n", "    \"outlet_name\": str,\n", "    \"feeder_item_id\": int,\n", "    \"feeder_item_id_quantity\": int,\n", "    \"outlet_item_id\": int,\n", "    \"outlet_item_id_quantity\": int,\n", "    \"feeder_quantity_available_at_outlet\": int,\n", "    \"order_blocked_quantity\": int,\n", "    \"esto_blocked_quantity\": int,\n", "    \"tat_blocked_quantity\": int,\n", "    \"feeder_live\": int,\n", "    \"CMS_update_inventory_for_feeder_enabled_items\": int,\n", "    \"upper_limit\": int,\n", "    \"CMS_update_inventory_upper_limit\": int,\n", "    \"fnv_upper_limit\": int,\n", "    \"CMS_update_inventory_fnv_upper_limit\": int,\n", "    \"CMS_update_inventory\": int,\n", "    \"Physical_Inventory_at_the_outlet\": int,\n", "    \"CMS_Inventory_Update_Sent\": int,\n", "    \"item_blocked_inventory_updated_at\": \"datetime64[ns]\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns = Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.astype(\n", "    convert_dict\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_feeder_outlet_item_combinations_remove_duplicate_columns.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"FEEDER_ID\", \"type\": \"int\"},\n", "    {\"name\": \"<PERSON><PERSON><PERSON>_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"OUTLET_ID\", \"type\": \"int\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"Max_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"feeder_item_id\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_item_id_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_inventory_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"outlet_item_id\", \"type\": \"int\"},\n", "    {\"name\": \"outlet_item_id_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_quantity_available_at_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"item_inventory_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"item_blocked_inventory_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"order_blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"esto_blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"tat_blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_live\", \"type\": \"int\"},\n", "    {\"name\": \"CMS_update_inventory_for_feeder_enabled_items\", \"type\": \"int\"},\n", "    {\"name\": \"upper_limit\", \"type\": \"int\"},\n", "    {\"name\": \"CMS_update_inventory_upper_limit\", \"type\": \"int\"},\n", "    {\"name\": \"fnv_upper_limit\", \"type\": \"int\"},\n", "    {\"name\": \"CMS_update_inventory_fnv_upper_limit\", \"type\": \"int\"},\n", "    {\"name\": \"CMS_update_inventory\", \"type\": \"int\"},\n", "    {\"name\": \"Physical_Inventory_at_the_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"CMS_Inventory_Update_Sent\", \"type\": \"int\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"items_on_feeder_and_outlet\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"FEEDER_ID\", \"OUTLET_ID\", \"feeder_item_id\"],\n", "    \"sortkey\": [\"FEEDER_ID\", \"OUTLET_ID\", \"feeder_item_id\"],\n", "    \"incremental_key\": \"Max_updated_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(\n", "    Mapped_feeder_outlet_item_combinations_remove_duplicate_columns, **kwargs\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}