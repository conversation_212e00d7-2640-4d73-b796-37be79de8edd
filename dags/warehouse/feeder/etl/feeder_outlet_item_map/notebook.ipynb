{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb\n", "\n", "retail_db = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_Item_Mapping = \"\"\"select feeder_outlet_item_mapping.*,  Feeder_live_table.* FROM \n", "(select map.feeder_id, \n", "        o.name as feeder, \n", "        map.outlet_id, \n", "        o1.name as outlet, \n", "        map.tat, \n", "        i.item_id , \n", "        T.outer_case_size ,\n", "        <PERSON>.Item_Name,\n", "        i.feeder_live, \n", "        i.enabled ,\n", "        map.updated_at as feeder_outlet_mapping_updated_at,\n", "        i.updated_at as feeder_outlet_item_mapping_updated_at\n", "        \n", "from ims.feeder_outlet_mapping map\n", "left join ims.feeder_outlet_item_mapping i on map.feeder_id = i.feeder_id and map.outlet_id = i.outlet_id\n", "inner join retail.console_outlet o on map.feeder_id = o.id \n", "inner join retail.console_outlet o1 on map.outlet_id = o1.id\n", "inner join (select item_id, max(name) as 'Item_Name', max(outer_case_size) as 'outer_case_size'\n", "from rpc.product_product p \n", "where active = 1 and approved = 1\n", "group by 1\n", "order by 1)T on i.item_id = T.item_id\n", "where map.active = 1) feeder_outlet_item_mapping\n", "\n", "LEFT JOIN \n", "\n", "\n", "(Select TTT.*,\n", "case when manual_feeder_live = 0 THEN 0 ELSE auto_feeder_live END AS Feeder_Status\n", "FROM\n", "(Select inv.outlet_id as inv_outlet_id,\n", "     inv.item_id as outlet_item_id,\n", "   inv.quantity as outlet_item_id_quantity,   \n", "   inv.feeder_quantity as feeder_quantity_available_at_outlet,\n", "   inv.item_inventory_updated_at, \n", "   bloc_inv.item_blocked_inventory_updated_at,\n", "   coalesce(order_blocked_quantity,0) as order_blocked_quantity,\n", "   coalesce(esto_blocked_quantity,0) as esto_blocked_quantity,\n", "   coalesce(tat_blocked_quantity,0) as tat_blocked_quantity ,\n", " \n", "   feeder_live as manual_feeder_live,\n", "   case when inv.quantity > (esto_blocked_quantity+tat_blocked_quantity) THEN 1 ELSE 0 END AS auto_feeder_live\n", "   \n", "   \n", " \n", "     \n", "FROM\n", " \n", "(Select i.outlet_id, i.item_id, i.quantity, i.feeder_live, i.feeder_quantity,  i.updated_at as item_inventory_updated_at\n", "from ims.ims_item_inventory i\n", "where i.active = 1 and i.outlet_id in (select distinct outlet_id from ims.feeder_outlet_mapping where active = 1) and i.item_id in (Select distinct item_id from ims.feeder_outlet_item_mapping)\n", " \n", ")inv\n", "\n", "\n", "LEFT JOIN \n", "\n", "\n", "(select outlet_id,\n", "item_id, \n", "max(updated_at) as item_blocked_inventory_updated_at,\n", "sum(order_blocked_quantity) as order_blocked_quantity, \n", "sum(esto_blocked_quantity) as esto_blocked_quantity, \n", "sum(tat_blocked_quantity) as tat_blocked_quantity\n", "From \n", "(select i.outlet_id, i.item_id, i.updated_at, \n", "case when i.blocked_type = 1 then i.quantity else 0 end as order_blocked_quantity,\n", "case when i.blocked_type = 2 then i.quantity else 0 end as esto_blocked_quantity,\n", "case when i.blocked_type = 3 then i.quantity else 0 end as tat_blocked_quantity\n", "from ims.ims_item_blocked_inventory i\n", "where i.active = 1 and i.outlet_id in (select distinct outlet_id from ims.feeder_outlet_mapping where active = 1) and i.item_id in (Select distinct item_id from ims.feeder_outlet_item_mapping)\n", "\n", "order by 1,2\n", ") T2\n", "Group by 1,2\n", ")bloc_inv\n", "\n", "\n", "on inv.outlet_id = bloc_inv.outlet_id and inv.item_id = bloc_inv.item_id)TTT)Feeder_live_table\n", "\n", "ON feeder_outlet_item_mapping.outlet_id = Feeder_live_table.inv_outlet_id AND feeder_outlet_item_mapping.item_id = Feeder_live_table.outlet_item_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_Item_Mapping_output = pandas.read_sql_query(\n", "    sql=Feeder_Item_Mapping, con=retail_db\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_Item_Mapping_output[\"Max_updated_at\"] = Feeder_Item_Mapping_output.apply(\n", "    lambda x: max(\n", "        x[\"feeder_outlet_mapping_updated_at\"],\n", "        x[\"feeder_outlet_item_mapping_updated_at\"],\n", "        x[\"item_inventory_updated_at\"],\n", "        x[\"item_blocked_inventory_updated_at\"],\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Feeder_Item_Mapping_output.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"feeder_id\", \"type\": \"int\"},\n", "    {\"name\": \"feeder\", \"type\": \"varchar\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"outlet\", \"type\": \"varchar\"},\n", "    {\"name\": \"tat\", \"type\": \"int\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"outer_case_size\", \"type\": \"int\"},\n", "    {\"name\": \"Item_Name\", \"type\": \"varchar\"},\n", "    {\"name\": \"feeder_live\", \"type\": \"int\"},\n", "    {\"name\": \"enabled\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_outlet_mapping_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"feeder_outlet_item_mapping_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"inv_outlet_id\", \"type\": \"int\"},\n", "    {\"name\": \"outlet_item_id\", \"type\": \"int\"},\n", "    {\"name\": \"outlet_item_id_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"feeder_quantity_available_at_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"item_inventory_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"item_blocked_inventory_updated_at\", \"type\": \"datetime\"},\n", "    {\"name\": \"order_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"esto_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"tat_blocked_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"manual_feeder_live\", \"type\": \"int\"},\n", "    {\"name\": \"auto_feeder_live\", \"type\": \"int\"},\n", "    {\"name\": \"Feeder_Status\", \"type\": \"int\"},\n", "    {\"name\": \"Max_updated_at\", \"type\": \"datetime\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"feeder_item_mapping_ft\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"feeder_id\", \"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"feeder_id\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"Max_updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(<PERSON><PERSON>r_Item_Mapping_output, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}