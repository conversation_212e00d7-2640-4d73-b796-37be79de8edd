{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "import requests\n", "from requests.adapters import HTTPAdapter\n", "from requests.compat import urljoin\n", "from urllib3.util.retry import Retry"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW_IST = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "RUN_HOUR = NOW_IST.hour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PricingClientV1(object):\n", "    BASE_URL = \"http://product-pricing-pricing-pre-prod.prod-sgp-k8s.grofer.io\"\n", "\n", "    def __init__(self, token, url=None):\n", "        retries = Retry(total=3, backoff_factor=0.1, status_forcelist=[500])\n", "\n", "        self.url = url and url or self.BASE_URL\n", "        self.session = requests.Session()\n", "        self.session.headers.update(\n", "            {\"content-type\": \"application/json\", \"crap-token\": token}\n", "        )\n", "        self.session.mount(self.BASE_URL, HTTPAdapter(max_retries=retries))\n", "\n", "    def _build_url(self, path):\n", "        return urljoin(self.url, path)\n", "\n", "    def update_demand_supply_ratio(self, superstore_ratios):\n", "        url = self._build_url(\"/api/prices/v1/demand_to_supply_ratio/\")\n", "        return self.session.post(url, json={\"data\": superstore_ratios}).json()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>SLOT CHARGES INFO</h3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")\n", "ticktock_connection = pb.get_connection(\"[Replica] Ticktock\")\n", "\n", "ticktok = \"\"\"select merchant_id,min(cashback_amount) as slot_charge from \n", "(select \n", "merchant_id,\n", "day_count,\n", "slot,\n", "cashback_amount,\n", "created_at,\n", "rank() over (partition by merchant_id,slot,day_count order by created_at desc) as rank\n", "from merchant_slot_cashback   \n", "where day_count=1\n", "and created_at::date > '2021-01-01') a\n", "where rank=1\n", "group by 1\n", "order by 1,2 \"\"\"\n", "\n", "merchant_id = \"\"\"select distinct frontend_merchant_id as merchant_id\n", "from lake_supply_orchestrator.sco_merchant sm \n", "inner join lake_supply_orchestrator.sco_city sc on sm.city_id = sc.id\n", "    and sm.active = 'true'\n", "    and sm.name not ilike '%%B2B%%'\n", "    and sc.name <> 'Not in service area'\n", "inner join lake_cms.view_gr_merchant gm on gm.id = sm.frontend_merchant_id\n", "inner join lake_supply_orchestrator.sco_network_path snp on snp.merchant_id = sm.id\n", "    and snp.active = 'true'\n", "order by 1\"\"\"\n", "\n", "merchant_name = \"\"\"select merchant_id,name as merchant_name,city_name as city from \n", "(select distinct external_id as merchant_id,name,city_name, rank() over (partition by external_id order by install_ts desc) rank\n", "from lake_oms_bifrost.oms_merchant\n", "where name like '%%Super Store%%' \n", "and city_name <> ' '\n", "and city_name <> 'Not in service area'\n", "and name not like '%%FS%%'\n", "and name not like '%%B2B%%'\n", "and name not like '%%ES%%'\n", ") a\n", "where rank=1\"\"\"\n", "\n", "ticktock_df = pd.read_sql(ticktok, ticktock_connection)\n", "merchant_id_df = pd.read_sql(merchant_id, redshift_connection)\n", "merchant_df = pd.read_sql(merchant_name, redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ticktock_sc_df = merchant_id_df.merge(ticktock_df, how=\"left\", on=[\"merchant_id\"])\n", "ticktock_sc_df = ticktock_sc_df.fillna(0)\n", "\n", "sc_data_updated = ticktock_sc_df.merge(merchant_df, on=[\"merchant_id\"], how=\"inner\")\n", "sc_data_updated = sc_data_updated[\n", "    [\"city\", \"merchant_id\", \"merchant_name\", \"slot_charge\"]\n", "]\n", "sc_data_updated[\"slot_charge\"] = sc_data_updated[\"slot_charge\"].astype(int)\n", "sc_data_updated = sc_data_updated.sort_values(\n", "    by=[\"city\", \"merchant_name\"], ascending=True\n", ").reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>GETTING DATA FOR LAST UPDATED PRICE TYPE</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slot_charges_with_price_type = \"\"\"\n", "select city,merchant_id,merchant_name, slot_charge\n", "from \n", "(select city,merchant_id,merchant_name, slot_charge, price_intervention_type,\n", "rank() over (partition by city, merchant_id, merchant_name order by install_ts_ist desc) as rank\n", "from metrics.merchant_price_intervention_logs) a \n", "where rank=1\n", "order by 1,2,3,4\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sc_data_last_insatlled = pd.read_sql(slot_charges_with_price_type, redshift_connection)\n", "sc_data_decrease = sc_data_updated.merge(\n", "    sc_data_last_insatlled, on=[\"merchant_id\"], how=\"left\"\n", ")\n", "sc_data_decrease[\"slot_charge_y\"] = sc_data_decrease[\"slot_charge_y\"].fillna(999)\n", "\n", "# filtering ones where slot charge has decreased from what it was uploaded before\n", "decrease_condition = np.where(\n", "    sc_data_decrease[\"slot_charge_x\"] < sc_data_decrease[\"slot_charge_y\"], True, False\n", ")\n", "sc_data_decrease[\"decrease\"] = decrease_condition\n", "sc_data_decrease = sc_data_decrease[sc_data_decrease[\"decrease\"] == True]\n", "sc_data_decrease = sc_data_decrease.drop(\n", "    columns=[\"city_y\", \"merchant_name_y\", \"slot_charge_y\", \"decrease\"]\n", ")\n", "sc_data_decrease = sc_data_decrease.rename(\n", "    columns={\n", "        \"city_x\": \"city\",\n", "        \"merchant_name_x\": \"merchant_name\",\n", "        \"slot_charge_x\": \"slot_charge\",\n", "    }\n", ")\n", "sc_data_decrease = sc_data_decrease.reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>ACCORDING TO RUN HOUR USING INCREASING/DECREASING MERCHANT'S DATA</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if RUN_HOUR == 2:\n", "    sc_data = sc_data_updated\n", "    price_increase = 1\n", "    print(\"Prices revised\")\n", "else:\n", "    if sc_data_decrease.shape[0] != 0:\n", "        sc_data = sc_data_decrease\n", "        price_increase = 0\n", "        print(\"Prices decreased\")\n", "    else:\n", "        sc_data = pd.DataFrame()\n", "        pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"Price Intervention : No decreament found in slot charges from last insertion\",\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>PRICING RULES</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from gspread.exceptions import APIError\n", "\n", "_retry_for = [429, 500, 503]\n", "\n", "\n", "def from_sheets(sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(60)\n", "\n", "\n", "pb.to_sheets_patch = to_sheets\n", "pb.from_sheets_patch = from_sheets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GOOGLE SHEET ACCESS VARIABLES\n", "rules_sheet_id = \"1N8o8KWDF7kDje0zFuXvbmfbTyO2ZcoI59H92aK_ZqI8\"\n", "rules_sheet_name = \"pricing_rules\"\n", "output_sheet_name = \"current_price_interventions_output\"\n", "\n", "pricing_rules = pb.from_sheets_patch(\n", "    rules_sheet_id, rules_sheet_name, service_account=\"service_account\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>FILTERING RULES & MODIFYING PRICE CONFIG DF</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_rules = pricing_rules[pricing_rules[\"active\"] == \"TRUE\"]\n", "pricing_rules = pricing_rules.drop(columns={\"active\"})\n", "\n", "pricing_rules[\"slot_charge_start\"] = pricing_rules[\"slot_charge_start\"].astype(\"int64\")\n", "pricing_rules[\"slot_charge_end\"] = pricing_rules[\"slot_charge_end\"].astype(\"int64\")\n", "pricing_rules[\"intervention_type\"] = pricing_rules[\"intervention_type\"].astype(\"int64\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>ASSINGING PRICE INTERVENTION TYPE TO MERCHANT LEVEL DATA</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index1, row1 in sc_data.iterrows():\n", "    for index2, row2 in pricing_rules.iterrows():\n", "        if (\n", "            row1[\"slot_charge\"] >= row2[\"slot_charge_start\"]\n", "            and row1[\"slot_charge\"] <= row2[\"slot_charge_end\"]\n", "        ):\n", "            sc_data.loc[index1, \"price_intervention\"] = row2[\"intervention_type\"]\n", "        else:\n", "            None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>UPDATING DATAFRAME TO BE SENT AND ADDING INSTALL_TS_IST</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sc_data.shape[0] != 0:\n", "    if pricing_rules.shape[0] != 0:\n", "        sc_data[\"price_intervention\"] = sc_data[\"price_intervention\"].astype(\"int64\")\n", "\n", "        pb.to_sheets_patch(\n", "            sc_data,\n", "            rules_sheet_id,\n", "            output_sheet_name,\n", "            service_account=\"service_account\",\n", "        )\n", "\n", "        pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"Price Intervention : Type updated in Google sheet\",\n", "        )\n", "        sc_data[\"install_ts_ist\"] = sc_data.apply(\n", "            lambda row: pd.to_datetime(NOW_IST).strftime(\"%Y-%m-%d %H:%M:%S\"), axis=1\n", "        )\n", "        sc_data = sc_data.rename(\n", "            columns={\"price_intervention\": \"price_intervention_type\"}\n", "        )\n", "        sc_data = sc_data[\n", "            [\n", "                \"install_ts_ist\",\n", "                \"city\",\n", "                \"merchant_id\",\n", "                \"merchant_name\",\n", "                \"slot_charge\",\n", "                \"price_intervention_type\",\n", "            ]\n", "        ]\n", "    else:\n", "        pb.send_slack_message(\n", "            channel=\"planning-dag-alerts\",\n", "            text=\"Price Intervention : No config rules found\",\n", "        )\n", "else:\n", "    None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>HITTING API TO UPDATE THE PRICE INTERVENTION TYPES</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sc_data.shape[0] != 0:\n", "    sc_api_df = sc_data.drop(columns={\"install_ts_ist\", \"merchant_name\", \"slot_charge\"})\n", "    for index1, row1 in sc_api_df.iterrows():\n", "        for index2, row2 in pricing_rules.iterrows():\n", "            if row1[\"price_intervention_type\"] == row2[\"intervention_type\"]:\n", "                sc_api_df.loc[index1, \"demand_to_supply_ratio\"] = float(\n", "                    row2[\"d_s_proxy\"]\n", "                )\n", "            else:\n", "                None\n", "    sc_api_df = sc_api_df.drop(columns={\"price_intervention_type\"})\n", "    superstore_ratios = sc_api_df.rename(\n", "        columns={\"merchant_id\": \"frontend_superstore_id\"}\n", "    ).to_dict(orient=\"records\")\n", "    pricing_client = PricingClientV1(\n", "        token=\"sampletoken\",\n", "        url=\"http://product-pricing-pricing.prod-sgp-k8s.grofer.io/api/prices/v1/demand_to_supply_ratio/\",\n", "    )\n", "    pricing_client.update_demand_supply_ratio(superstore_ratios)\n", "else:\n", "    None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>PRINTING DATA TO REDSHIFT</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"merchant_price_intervention_logs\",\n", "    \"table_description\": \"Price Intervention logs at Merchant level\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"install_ts_ist\",\n", "            \"type\": \"timestamp\",\n", "            \"description\": \"row install time\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City name\"},\n", "        {\n", "            \"name\": \"merchant_id\",\n", "            \"type\": \"bigint\",\n", "            \"description\": \"merchant id\",\n", "        },\n", "        {\n", "            \"name\": \"merchant_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"merchant name\",\n", "        },\n", "        {\n", "            \"name\": \"slot_charge\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"slot charge\",\n", "        },\n", "        {\n", "            \"name\": \"price_intervention_type\",\n", "            \"type\": \"smallint\",\n", "            \"description\": \"price_intervention_type\",\n", "        },\n", "    ],\n", "    \"sortkey\": [\"city\", \"merchant_id\"],\n", "    \"incremental_key\": \"install_ts\",\n", "    \"load_type\": \"append\",\n", "}\n", "if sc_data.shape[0] != 0 and pricing_rules.shape[0] != 0:\n", "    pb.to_redshift(sc_data, **kwargs)\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "    print(\"Appended successfully\")\n", "    print(\"- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\")\n", "else:\n", "    print(\"Dataframe empty\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h5>SENDING OUT MAIL IF INCREAMENT/DECREAMENT</H5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if sc_data.shape[0] != 0 and pricing_rules.shape[0] != 0:\n", "    sc_data = sc_data.drop(columns=[\"install_ts_ist\"])\n", "    filepath = sc_data.to_csv(\"intervention_type.csv\")\n", "    pathname = \"/tmp/intervention_type.csv\"\n", "    sc_data.to_csv(pathname, index=False)\n", "\n", "    MAILER_SHEETID = \"1N8o8KWDF7kDje0zFuXvbmfbTyO2ZcoI59H92aK_ZqI8\"\n", "    MAILING_LIST = \"mailing_list\"\n", "    mailer = pb.from_sheets(\n", "        MAILER_SHEETID,\n", "        MAILING_LIST,\n", "        service_account=\"service_account\",\n", "        clear_cache=True,\n", "    )\n", "    mailer = mailer[\"mail\"].apply(lambda x: str(x)).to_list()\n", "\n", "    def send_mail(mailer, subject, html_content, files):\n", "        print(subject)\n", "        from_email = \"<EMAIL>\"\n", "        to_email = mailer\n", "        pb.send_email(from_email, to_email, subject, html_content, files)\n", "\n", "    subject = \"[Update] || Price Intervention type at Merchant level\"\n", "    html_content = \"\"\"<p>Please find the merchant level details in attached csv where price intervention type is updated</p>\"\"\"\n", "    pb.send_slack_message(\n", "        channel=\"planning-dag-alerts\",\n", "        text=\"Price Intervention : Mail sent\",\n", "    )\n", "\n", "    send_mail(mailer, subject, html_content, [pathname])\n", "else:\n", "    None"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}