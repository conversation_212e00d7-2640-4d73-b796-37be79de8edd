{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import pencilbox as pb\n", "import pendulum\n", "\n", "import requests\n", "from requests.adapters import HTTPAdapter\n", "from requests.compat import urljoin\n", "from urllib3.util.retry import Retry"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["class PricingClientV1(object):\n", "    BASE_URL = \"http://product-pricing-pricing-pre-prod.prod-sgp-k8s.grofer.io\"\n", "\n", "    def __init__(self, token, url=None):\n", "        retries = Retry(total=3, backoff_factor=0.1, status_forcelist=[500])\n", "\n", "        self.url = url and url or self.BASE_URL\n", "        self.session = requests.Session()\n", "        self.session.headers.update(\n", "            {\"content-type\": \"application/json\", \"crap-token\": token}\n", "        )\n", "        self.session.mount(self.BASE_URL, HTTPAdapter(max_retries=retries))\n", "\n", "    def _build_url(self, path):\n", "        return urljoin(self.url, path)\n", "\n", "    def update_demand_supply_ratio(self, superstore_ratios):\n", "        url = self._build_url(\"/api/prices/v1/demand_to_supply_ratio/\")\n", "        return self.session.post(url, json={\"data\": superstore_ratios}).json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_ratio_threshold(row):\n", "    threshold = float(settings[\"default_ratio_threshold\"])\n", "    for key in [\"city\", \"merchant_id\"]:\n", "        try:\n", "            threshold = ratio_threshold[row[key]]\n", "        except KeyError:\n", "            continue\n", "    return threshold\n", "\n", "\n", "def get_pt_threshold(row):\n", "    threshold = int(settings[\"default_promise_time_days_threshold\"])\n", "    for key in [\"city\", \"merchant_id\"]:\n", "        try:\n", "            threshold = promise_time_threshold[row[key]]\n", "        except KeyError:\n", "            continue\n", "    return threshold\n", "\n", "\n", "def to_float_safe(x):\n", "    try:\n", "        return float(x)\n", "    except:\n", "        return pandas.np.nan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = pendulum.now(\"Asia/Kolkata\")\n", "now_date_str = now.to_date_string()\n", "yesterday_date_str = now.yesterday(\"Asia/Kolkata\").to_date_string()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pt_db = pb.get_connection(\"[Replica] Promise Time\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["supply_demand_ratios = pb.from_sheets(\n", "    \"12xC8LA8gXirJYlWASjj8WUCZLUrdqcQF1ztbrMewdI0\",\n", "    \"D/S ratio PAN India\",\n", "    service_account=\"service_account\",\n", ")\n", "config = pb.from_sheets(\n", "    \"12xC8LA8gXirJYlWASjj8WUCZLUrdqcQF1ztbrMewdI0\",\n", "    \"Config\",\n", "    service_account=\"service_account\",\n", ")\n", "city_thresholds = pb.from_sheets(\n", "    \"12xC8LA8gXirJYlWASjj8WUCZLUrdqcQF1ztbrMewdI0\",\n", "    \"City Config\",\n", "    service_account=\"service_account\",\n", ")\n", "merchant_thresholds = pb.from_sheets(\n", "    \"12xC8LA8gXirJYlWASjj8WUCZLUrdqcQF1ztbrMewdI0\",\n", "    \"Merchant Config\",\n", "    service_account=\"service_account\",\n", ")\n", "demand_mapping = pb.from_sheets(\n", "    \"12xC8LA8gXirJYlWASjj8WUCZLUrdqcQF1ztbrMewdI0\",\n", "    \"Demand - Supply Mapping\",\n", "    service_account=\"service_account\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["settings = {}\n", "for row in config.to_dict(\"records\"):\n", "    settings[row[\"Name\"]] = row[\"Value\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ratio_threshold = {}\n", "promise_time_threshold = {}\n", "for row in city_thresholds.to_dict(\"records\"):\n", "    try:\n", "        ratio_threshold[row[\"city\"]] = float(row[\"ratio_threshold\"])\n", "    except ValueError:\n", "        pass\n", "\n", "    try:\n", "        promise_time_threshold[row[\"city\"]] = int(row[\"promise_time_days_threshold\"])\n", "    except ValueError:\n", "        pass\n", "\n", "for row in merchant_thresholds.to_dict(\"records\"):\n", "    try:\n", "        ratio_threshold[row[\"merchant_id\"]] = float(row[\"ratio_threshold\"])\n", "    except:\n", "        pass\n", "\n", "    try:\n", "        promise_time_threshold[row[\"merchant_id\"]] = int(\n", "            row[\"promise_time_days_threshold\"]\n", "        )\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["earliest_slot_details = pandas.read_sql_query(\n", "    \"\"\"\n", "    SELECT city, merchant_id::varchar, merchant_name,\n", "        backend_merchant_id,\n", "        backend_merchant_name,\n", "        promise_time_days,\n", "        promise_time_hours\n", "    FROM earliest_slot_details\n", "    WHERE\n", "        order_type = 'normal'\n", "        AND user_type = 'Non-SBC'\n", "\"\"\",\n", "    con=pt_db,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ratios = supply_demand_ratios[\n", "    supply_demand_ratios[\"checkout_date\"] == yesterday_date_str\n", "]\n", "ratios = ratios.drop([\"city\", \"merchant_name\"], axis=1)\n", "ratios[\"demand_supply_ratio\"] = ratios[\"d/s_ratio\"].apply(to_float_safe)\n", "ratios = ratios.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["esd = earliest_slot_details[\n", "    earliest_slot_details[\"backend_merchant_id\"].isin(\n", "        demand_mapping.backend_merchant_id\n", "    )\n", "]\n", "esd = esd.merge(ratios, on=[\"merchant_id\"])\n", "esd[\"ratio_threshold\"] = esd.apply(get_ratio_threshold, axis=1)\n", "esd[\"promise_time_days_threshold\"] = esd.apply(get_pt_threshold, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trigger_candidates = esd[esd[\"promise_time_days\"] >= esd[\"promise_time_days_threshold\"]]\n", "# trigger_candidates = trigger_candidates[trigger_candidates[\"demand_supply_ratio\"] >= trigger_candidates[\"ratio_threshold\"]]\n", "trigger_candidates = (\n", "    trigger_candidates[[\"city\", \"merchant_id\", \"demand_supply_ratio\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["superstore_ratios = trigger_candidates.rename(\n", "    columns={\n", "        \"merchant_id\": \"frontend_superstore_id\",\n", "        \"demand_supply_ratio\": \"demand_to_supply_ratio\",\n", "    }\n", ").to_dict(orient=\"records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(superstore_ratios)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pricing_client = PricingClientV1(token=\"sampletoken\")\n", "pricing_client = PricingClientV1(\n", "    token=\"sampletoken\",\n", "    url=\"http://product-pricing-pricing.prod-sgp-k8s.grofer.io/api/prices/v1/demand_to_supply_ratio/\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_client.update_demand_supply_ratio(superstore_ratios)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}