{"cells": [{"cell_type": "code", "execution_count": null, "id": "51f9eb33-336d-48fe-b9f0-d6e4f014085e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "cda6fd83-6dfc-4750-941e-598914e83fb0", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1lzE6IEuxad1Pqsr9tKFid5OB4kVa6OQqIDRq3wg62NQ\"\n", "sheet_name = \"data\"\n", "lcd = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "0d5abc97-5d16-42d7-972c-60cbfa894fa6", "metadata": {}, "outputs": [], "source": ["lcd = pd.read_csv(\"Last Change Date - data (3).csv\", keep_default_na=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0b4f2412-e369-4ea3-8c3e-9f6c1d7834be", "metadata": {}, "outputs": [], "source": ["lcd.info()"]}, {"cell_type": "code", "execution_count": null, "id": "2a71dc80-65a2-4c7a-9ebf-24aa3986aeb8", "metadata": {}, "outputs": [], "source": ["lcd.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3c98a4fc-c6ac-45e4-af5a-26086dba56ff", "metadata": {}, "outputs": [], "source": ["lcd_upload = lcd.astype(\n", "    {\n", "        \"l2_category_id\": \"Int64\",\n", "        \"l2_category\": \"string\",\n", "        \"old_att_ids\": \"string\",\n", "        \"new_att_ids\": \"string\",\n", "        \"last_change_date\": \"string\",\n", "    }\n", ")\n", "lcd_upload"]}, {"cell_type": "code", "execution_count": null, "id": "355de039-3539-40b3-a370-b73c76e614e7", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"plp_experiment_last_change_date_test\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"L2 category id of a product.\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"attribute id mapped with l2 category.\",\n", "        },\n", "        {\n", "            \"name\": \"old_att_ids\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"new_att_ids\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"last_change_date\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"na\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"l2_category_id\",\n", "        \"attribute_id\",\n", "        \"last_change_date\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table contains last change date of plp attribute groups against L2 category\",\n", "}\n", "\n", "pb.to_trino(data_obj=lcd_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "520ed1d8-dbbf-4921-85a7-1699e0050e81", "metadata": {}, "outputs": [], "source": ["lcdq = \"\"\"\n", "WITH mx AS (\n", "    SELECT\n", "        l2_category_id,\n", "        MAX(CAST(last_change_date AS DATE)) AS max_lcd\n", "    FROM\n", "        blinkit_staging.interim.plp_experiment_last_change_date_test\n", "    GROUP BY 1\n", ")\n", "SELECT\n", "    a.l2_category_id,\n", "    a.old_att_ids,\n", "    a.new_att_ids,\n", "    CAST(a.last_change_date AS DATE) AS last_change_date\n", "FROM\n", "    blinkit_staging.interim.plp_experiment_last_change_date_test a\n", "JOIN\n", "    mx ON mx.l2_category_id = a.l2_category_id AND mx.max_lcd = CAST(a.last_change_date AS DATE)\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "lcd = pd.read_sql_query(sql=lcdq, con=con)\n", "lcd"]}, {"cell_type": "code", "execution_count": null, "id": "85fa8d50-a57d-4299-ba4c-7de38afe4761", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "308b8144-2395-4d2e-90b5-f64ac14b8656", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f82b9ac6-e944-468f-b7ff-ee8e30e3b562", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bf61ba1b-62eb-429e-b257-13e4cd9ad055", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3d4c8a0-b01a-4746-8047-f42e7e400251", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1lzE6IEuxad1Pqsr9tKFid5OB4kVa6OQqIDRq3wg62NQ\"\n", "sheet_name = \"live_xp_l2s\"\n", "live = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "9b482bc2-04f4-4267-ba47-68de538954ab", "metadata": {}, "outputs": [], "source": ["live = pd.read_csv(\"Last Change Date - live_xp_l2s.csv\", keep_default_na=False)"]}, {"cell_type": "code", "execution_count": null, "id": "57281c7c-7d0f-4518-9e4c-871b4e3f4f6b", "metadata": {}, "outputs": [], "source": ["live"]}, {"cell_type": "code", "execution_count": null, "id": "db00be5f-7a34-4217-84e4-fbc08ba94527", "metadata": {}, "outputs": [], "source": ["live_upload = live.astype(\n", "    {\n", "        \"l2_category_id\": \"Int64\",\n", "        \"l2_category\": \"string\",\n", "        \"live_flag\": \"Int64\",\n", "        \"enabled_pids\": \"Int64\",\n", "    }\n", ")\n", "live_upload"]}, {"cell_type": "code", "execution_count": null, "id": "0b92171e-6f9c-4f41-85a4-1b8849e066a6", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"plp_experiment_live_experiment_flag_test\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"L2 category id of a product.\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"attribute id mapped with l2 category.\",\n", "        },\n", "        {\n", "            \"name\": \"live_flag\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"enabled_pids\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"l2_category_id\",\n", "        \"attribute_id\",\n", "        \"last_change_date\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table contains the list of L2s where plp experiment is live\",\n", "}\n", "\n", "pb.to_trino(data_obj=live_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9795c207-7f2b-4f28-b378-e3155a645a84", "metadata": {}, "outputs": [], "source": ["lq = \"\"\"\n", "SELECT\n", "    *\n", "FROM\n", "    blinkit_staging.interim.plp_experiment_live_experiment_flag_test\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "live = pd.read_sql_query(sql=lq, con=con)\n", "live"]}, {"cell_type": "code", "execution_count": null, "id": "29ecdc8e-728e-49b0-a30e-5166024092af", "metadata": {}, "outputs": [], "source": ["ap = \"\"\"\n", "    WITH EnabledCategories AS (\n", "        SELECT\n", "            c.id AS l2_category_id,\n", "            c.name AS L2\n", "        FROM \n", "            cms.gr_category c\n", "        JOIN\n", "            cms.gr_product_category_mapping pcm ON pcm.category_id = c.id\n", "        WHERE \n", "            pcm.is_primary\n", "            AND pcm.lake_active_record\n", "            AND c.enabled_flag\n", "    --        AND c.id = 1164\n", "        GROUP BY 1, 2\n", "    ),\n", "    --SELECT * FROM cms.gr_category WHERE id = 301\n", "    --SELECT * FROM cms.gr_product_category_mapping WHERE category_id = 301\n", "    --SELECT * FROM EnabledCategories WHERE l2_category_id = 301\n", "    PLPPDPAttributes AS (\n", "        SELECT\n", "            entity_instance_id AS l2_category_id,\n", "            L2,\n", "            plp.attribute_id,\n", "            an.name AS attribute_name,\n", "            plp.service_type,\n", "            ROW_NUMBER() OVER (PARTITION BY entity_instance_id ORDER BY sort_order)  sort_order\n", "        FROM \n", "            cms.entity_attribute_mappings plp\n", "        LEFT JOIN\n", "            cms.gr_attribute an ON plp.attribute_id = an.id AND an.lake_active_record\n", "        JOIN\n", "            EnabledCategories ec ON ec.l2_category_id = plp.entity_instance_id\n", "        WHERE \n", "            plp.entity_id = 1\n", "            AND plp.enabled_flag\n", "            AND plp.service_type IN ('PLP')\n", "            AND plp.type = 'key_highlights'\n", "            AND plp.lake_active_record\n", "    --        AND plp.sort_order < 3\n", "    )\n", "    SELECT\n", "        l2_category_id,\n", "        L2,\n", "        ARRAY_AGG(attribute_name ORDER BY sort_order) FILTER (WHERE sort_order < 3) AS plp_attributes,\n", "        ARRAY_AGG(CAST(attribute_id AS INT)) FILTER (WHERE sort_order < 3) AS plp_attribute_ids,\n", "        ARRAY_AGG(CAST(attribute_id AS INT)) AS all_plp_attribute_ids,\n", "        CARDINALITY(ARRAY_AGG(attribute_name ORDER BY sort_order) FILTER (WHERE sort_order < 3)) AS card_plp_attributes\n", "    FROM\n", "        PLPPDPAttributes\n", "    GROUP BY 1, 2\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "actual_plp = pd.read_sql_query(sql=ap, con=con)\n", "actual_plp"]}, {"cell_type": "code", "execution_count": null, "id": "a370ace3-7054-40fe-87bb-0bf489535308", "metadata": {}, "outputs": [], "source": ["actual_plp.info()"]}, {"cell_type": "code", "execution_count": null, "id": "c81e5883-415c-4dce-9495-8315856715e6", "metadata": {}, "outputs": [], "source": ["actual_plp_upload = actual_plp.astype(\n", "    {\n", "        \"l2_category_id\": \"Int64\",  # INTEGER\n", "        \"L2\": \"string\",  # VARCHAR\n", "        # Arrays stay as object dtype so pb.to_trino can serialize lists properly\n", "        \"plp_attributes\": \"object\",  # ARRAY(VARCHAR)\n", "        \"plp_attribute_ids\": \"object\",  # ARRAY(INTEGER)\n", "        \"all_plp_attribute_ids\": \"object\",  # ARRAY(INTEGER)\n", "        \"card_plp_attributes\": \"Int64\",  # INTEGER\n", "    }\n", ")\n", "actual_plp_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "c0c4bc49-4282-4175-bb19-1c46ccadb78a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"plp_experiment_actual_atts_test\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"L2 category id of a product.\",\n", "        },\n", "        {\n", "            \"name\": \"L2\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"attribute id mapped with l2 category.\",\n", "        },\n", "        {\n", "            \"name\": \"plp_attributes\",\n", "            \"type\": \"ARRAY(VARCHAR)\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"plp_attribute_ids\",\n", "            \"type\": \"ARRAY(INTEGER)\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"all_plp_attribute_ids\",\n", "            \"type\": \"ARRAY(INTEGER)\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"card_plp_attributes\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"l2_category_id\",\n", "        \"plp_attributes\",\n", "        \"all_plp_attribute_ids\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table contains the list of L2s where plp experiment is live\",\n", "}\n", "\n", "pb.to_trino(data_obj=actual_plp_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "c2158dab-e423-46d2-9475-c75c23710b28", "metadata": {}, "outputs": [], "source": ["ap = \"\"\"\n", "SELECT\n", "    *\n", "FROM\n", "    blinkit_staging.interim.plp_experiment_actual_atts_test\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "actual_plp = pd.read_sql_query(sql=ap, con=con)\n", "actual_plp"]}, {"cell_type": "code", "execution_count": null, "id": "782c18c1-6306-43fd-b820-b9715af3d1a0", "metadata": {}, "outputs": [], "source": ["actual_plp"]}, {"cell_type": "code", "execution_count": null, "id": "3b0a90fc-1ba3-42da-b9ea-54d818729a81", "metadata": {}, "outputs": [], "source": ["actual_plp[actual_plp[\"l2\"] == \"Matic Detergent Powders\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b7f04d5a-7a95-4da3-840b-a5d8c5a0dc4a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "32b21c64-63a1-4d67-aeab-d6acf0ce228e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e0e3b54-b85e-4f03-9709-c3ea81ed9607", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6305fef7-e925-4215-9564-4bf3c360a57c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d680141-b392-4ef2-9645-828e67a56976", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aacddb20-c70b-4a87-880d-cb8329553faa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3bd20ae5-626e-4ea2-985d-7106c0794135", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7779f9d7-bf16-46a0-83e7-86f012b93889", "metadata": {}, "outputs": [], "source": ["bn = \"\"\"\n", "WITH PreCategoryBase AS (\n", "    SELECT\n", "        gcgm.category_id AS l2_category_id,\n", "        gc.name AS l2_category,\n", "        --gcgm.group_id,\n", "        --gg.name AS group_name,\n", "        ggam.attribute_id,\n", "        ga.name AS attribute_name,\n", "        --gcgam.is_mandatory,\n", "        --ga.is_mandatory AS global_m_flag,\n", "        ga.attribute_value_type\n", "    FROM\n", "        cms.gr_category_group_mapping gcgm\n", "    JOIN\n", "        cms.gr_group_attribute_mapping ggam\n", "            ON ggam.group_id = gcgm.group_id\n", "            AND ggam.lake_active_record\n", "            AND ggam.enabled_flag\n", "    JOIN\n", "        cms.gr_category_group_attribute_mapping gcgam\n", "            ON gcgam.category_id = gcgm.category_id\n", "            AND ggam.attribute_id = gcgam.attribute_id\n", "            AND gcgm.group_id = gcgam.group_id\n", "            AND gcgam.lake_active_record\n", "            AND gcgam.enabled_flag\n", "    JOIN\n", "        cms.gr_attribute ga\n", "            ON ga.id = ggam.attribute_id\n", "            AND ga.lake_active_record\n", "            --AND attribute_type = 'shown_to_customer'\n", "    JOIN\n", "        cms.gr_group gg\n", "            ON gg.id = gcgm.group_id\n", "            AND gg.lake_active_record\n", "            AND gg.is_active\n", "    JOIN\n", "        cms.gr_category gc\n", "            ON gc.id = gcgm.category_id\n", "            AND gc.enabled_flag\n", "            AND gc.lake_active_record\n", "    WHERE\n", "--        gcgm.category_id = 50\n", "        gcgm.lake_active_record\n", "        AND gcgm.enabled_flag\n", "    GROUP BY 1, 2, 3, 4, 5\n", "),\n", "EnabledCategories AS (\n", "    SELECT\n", "        c.id AS l2_category_id,\n", "        c.name AS L2\n", "    FROM \n", "        cms.gr_category c\n", "    JOIN\n", "        cms.gr_product_category_mapping pcm ON pcm.category_id = c.id\n", "    WHERE \n", "        pcm.is_primary\n", "        AND pcm.lake_active_record\n", "        AND c.enabled_flag\n", "--        AND c.id = 1164\n", "    GROUP BY 1, 2\n", "),\n", "PLPPDPAttributes AS (\n", "    SELECT\n", "        entity_instance_id AS l2_category_id,\n", "        L2,\n", "        plp.attribute_id,\n", "        an.name AS attribute_name,\n", "        plp.service_type AS Source,\n", "        ROW_NUMBER() OVER (PARTITION BY entity_instance_id ORDER BY sort_order) sort_order\n", "    FROM \n", "        cms.entity_attribute_mappings plp\n", "    LEFT JOIN\n", "        cms.gr_attribute an ON plp.attribute_id = an.id AND an.lake_active_record\n", "    JOIN\n", "        EnabledCategories ec ON ec.l2_category_id = plp.entity_instance_id\n", "    WHERE \n", "        plp.entity_id = 1\n", "        AND plp.enabled_flag\n", "        AND plp.service_type IN ('PLP')\n", "        AND plp.type = 'key_highlights'\n", "        AND plp.lake_active_record\n", "--        AND plp.sort_order < 3\n", "--    GROUP BY 1, 2, 3, 4, 5, 6\n", "),\n", "CategoryBase AS (\n", "    SELECT\n", "        pcb.l2_category_id,\n", "        pcb.l2_category,\n", "        pcb.attribute_id,\n", "        pcb.attribute_name,\n", "        attribute_value_type,\n", "        COALESCE(ppa.Source, 'Not on PLP') AS source\n", "    FROM\n", "        PreCategoryBase pcb\n", "    LEFT JOIN\n", "        PLPPDPAttributes ppa ON ppa.l2_category_id = pcb.l2_category_id AND ppa.attribute_id = pcb.attribute_id\n", "),\n", "--SELECT * FROM CategoryBase\n", "enabled AS (\n", "    SELECT\n", "        dp.product_id,\n", "        l0_category,\n", "        l2_category,\n", "        l1_category,\n", "        gpam.attribute_id,\n", "        gpam.value\n", "    FROM\n", "        dwh.dim_product dp\n", "    JOIN\n", "        cms.gr_product_attribute_mapping gpam ON gpam.product_id = dp.product_id AND gpam.lake_active_record\n", "    WHERE\n", "        is_product_enabled\n", "        AND is_current\n", "        --AND NOT(is_combo)\n", "),\n", "base AS (\n", "    SELECT\n", "        cb.attribute_id,\n", "        cb.attribute_name,\n", "        cb.attribute_value_type,\n", "        cb.l2_category,\n", "        cb.source,\n", "        e.product_id,\n", "        e.value,\n", "        TRY_CAST(e.value AS DECIMAL(10,3)) AS float_flag,\n", "        TRIM(LOWER(TRY_CAST(e.value AS VARCHAR))) AS bool_flag\n", "    FROM\n", "        CategoryBase cb\n", "    JOIN\n", "        enabled e ON e.l2_category = cb.l2_category AND e.attribute_id = cb.attribute_id\n", "    WHERE\n", "        e.value NOT IN ('', ' ', 'NA', 'na', 'N/A')\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9\n", "),\n", "bfinal AS (\n", "    SELECT\n", "        b.attribute_id,\n", "        attribute_name,\n", "        attribute_value_type,\n", "        ARRAY_DISTINCT(ARRAY_AGG(l2_category) FILTER (WHERE source = 'PLP')) AS plp_L2s,\n", "        --ARRAY_AGG(r.value) AS top_5_vals,\n", "        COUNT(DISTINCT product_id) AS all_pids,\n", "        COUNT(DISTINCT CASE WHEN float_flag IS NOT NULL THEN product_id ELSE NULL END) AS floaty_pids,\n", "        COUNT(DISTINCT CASE WHEN bool_flag IN ('true', 'false', 'yes', 'no') THEN product_id ELSE NULL END) AS bool_pids\n", "    FROM\n", "        base b\n", "    GROUP BY 1, 2, 3\n", ")\n", "SELECT * FROM bfinal\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "bool_number = pd.read_sql_query(sql=bn, con=con)\n", "bool_number"]}, {"cell_type": "code", "execution_count": null, "id": "b0a726f7-a6da-4a16-a2d2-ab46eb24b020", "metadata": {}, "outputs": [], "source": ["bool_number.info()"]}, {"cell_type": "code", "execution_count": null, "id": "412210b7-a0b0-4341-84b4-e61292d821ab", "metadata": {}, "outputs": [], "source": ["bool_number_upload = bool_number.astype(\n", "    {\n", "        \"attribute_id\": \"Int64\",\n", "        \"attribute_name\": \"string\",\n", "        \"attribute_value_type\": \"string\",\n", "        \"plp_L2s\": \"object\",\n", "        \"all_pids\": \"Int64\",\n", "        \"floaty_pids\": \"Int64\",\n", "        \"bool_pids\": \"Int64\",\n", "    }\n", ")\n", "bool_number_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "85cff69d-73ae-4346-bcf6-51492233e9df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0b9269eb-74b1-4c9d-8288-ca9d7f12d97f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"plp_experiment_bool_numeric_data_test\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"attribute_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"L2 category id of a product.\",\n", "        },\n", "        {\n", "            \"name\": \"attribute_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"attribute id mapped with l2 category.\",\n", "        },\n", "        {\n", "            \"name\": \"attribute_value_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"plp_L2s\",\n", "            \"type\": \"ARRAY(VARCHAR)\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"all_pids\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"floaty_pids\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "        {\n", "            \"name\": \"bool_pids\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"na\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"attribute_id\",\n", "        \"plp_L2s\",\n", "        \"all_pids\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table contains the list of L2s where plp experiment is live\",\n", "}\n", "\n", "pb.to_trino(data_obj=bool_number_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8e51d41a-8078-4b68-b977-99ee132a3a03", "metadata": {}, "outputs": [], "source": ["bn = \"\"\"\n", "SELECT\n", "    *\n", "FROM\n", "    blinkit_staging.interim.plp_experiment_bool_numeric_data_test\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "bool_number = pd.read_sql_query(sql=bn, con=con)\n", "bool_number"]}, {"cell_type": "code", "execution_count": null, "id": "f58d30fe-7166-4f24-aa6d-4b54f7d93cf5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "49ff2509-8805-45a8-a928-e07a7f92e085", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0f9df5d1-837d-4ca3-9bdc-28fb57092687", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "82110463-9320-4d5c-bbc5-47ea2bf34719", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ff2a2961-1d76-4508-9b3a-5ad914f1493a", "metadata": {}, "outputs": [], "source": ["main = \"\"\"\n", "WITH live_xp_cats AS (\n", "    SELECT\n", "        DISTINCT l2_category_id\n", "    FROM\n", "        cms_etls.plp_experiment_live_experiment_flag\n", "    WHERE\n", "        live_flag = 1\n", "),\n", "--SELECT * FROM live_xp_cats\n", "\n", "mx AS (\n", "    SELECT\n", "        l2_category_id,\n", "        MAX(CAST(last_change_date AS DATE)) AS max_lcd\n", "    FROM\n", "        blinkit_staging.interim.plp_experiment_last_change_date_test\n", "    GROUP BY 1\n", "),\n", "l2_att_lcd_mapping AS (\n", "    SELECT\n", "        a.l2_category_id,\n", "        a.old_att_ids,\n", "        a.new_att_ids,\n", "        CAST(a.last_change_date AS DATE) AS last_change_date\n", "    FROM\n", "        blinkit_staging.interim.plp_experiment_last_change_date_test a\n", "    JOIN\n", "        mx ON mx.l2_category_id = a.l2_category_id AND mx.max_lcd = CAST(a.last_change_date AS DATE)\n", "),\n", "--SELECT * FROM l2_new_att_lcd_mapping\n", "latest_change AS (\n", "    SELECT\n", "        l2_category,\n", "        MAX(lcd) AS max_lcd\n", "    FROM\n", "        l2_att_lcd_mapping\n", "    GROUP BY 1\n", "),\n", "\n", "pre_lalm AS (\n", "    SELECT\n", "        l2_category,\n", "        TRANSFORM(SPLIT(post_attribute, ','), x -> trim(x)) arr,\n", "        lcd AS max_lcd\n", "    FROM\n", "        l2_att_lcd_mapping a\n", "    WHERE\n", "        lcd = (SELECT max_lcd FROM latest_change WHERE l2_category = a.l2_category)\n", "        AND post_attribute NOT IN ('With/Without Roach','Product Width (cm)','ISI Certified','Contains preservatives','Total Sugar Per 100 g (g)','Hygienically Vacuum Packed','Latency_ms','Club with Brand Offers','Apparel Blouse Included','Length (in mm)','Product Height (cm)','Auto Shutoff','Dishwash Safe','No Artificial Flavor','BPA Free','Batteries Included','No Artificial Color','IsImmersiveSound','Calender Year','Battery Required','ENC_ENX availability','Pet Friendly','Microwave Safe','Gloves Touch Screen','Inverter Technology','Assembly Required','Refillable','Reusable','Steam Wash')\n", "),\n", "\n", "lalm AS (\n", "    SELECT\n", "        l2_category,\n", "        CASE WHEN arr[1] = 'Unit (with options)' THEN ARRAY_DISTINCT(arr) ELSE ARRAY_SORT(ARRAY_DISTINCT(arr)) END AS post_attribute,\n", "        max_lcd\n", "    FROM\n", "        pre_lalm\n", "),\n", "\n", "blacklisted AS (\n", "    SELECT ARRAY[\n", "        4454, 587, 660, 721, 9228, 5415, 6187, 4544, 5452, 579,\n", "        628, 2624, 565, 730, 651, 3585, 722, 6188, 4548, 3584,\n", "        6185, 2295, 564, 5529, 5878, 630, 4619, 761, 5880\n", "    ] AS blacklist\n", "),\n", "\n", "impr_cg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        a.properties__highlight_ids,\n", "        SUM(impr) AS impressions\n", "    FROM\n", "        consumer_etls.plp_highlights_impr a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE\n", "            WHEN l2_category IN (SELECT l2_category FROM lalm)\n", "                THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category)\n", "            ELSE DATE '2025-04-01'\n", "        END\n", "        AND mod != 1\n", "        AND properties__page_name = 'Search Page'\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "click_cg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        a.properties__highlight_ids,\n", "        SUM(pdp_visits) AS clicks\n", "    FROM\n", "        consumer_etls.plp_highlights_pdp_visits a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE\n", "            WHEN l2_category IN (SELECT l2_category FROM lalm)\n", "                THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category)\n", "            ELSE DATE '2025-04-01'\n", "        END\n", "        AND mod != 1\n", "        AND properties__page_name = 'Search Page'\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "atc_cg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        a.properties__highlight_ids,\n", "        SUM(CASE WHEN properties__sub_page_name = 'product_page' THEN atc END) AS PDP_ATC,\n", "        SUM(CASE WHEN properties__sub_page_name NOT IN ('product_page','variant_bottom_sheet') OR properties__sub_page_name IS NULL THEN atc END) AS DIRECT_ATC\n", "    FROM\n", "        consumer_etls.plp_highlights_atc a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE\n", "            WHEN l2_category IN (SELECT l2_category FROM lalm)\n", "                THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category)\n", "            ELSE DATE '2025-04-01'\n", "        END\n", "        AND mod != 1\n", "        AND properties__page_name = 'Search Page'\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "\n", "final_cg AS (\n", "    SELECT\n", "        a.at_date_ist,\n", "        a.l2_category,\n", "        a.properties__highlight_ids,\n", "        impressions,\n", "        clicks,\n", "        COALESCE(PDP_ATC, 0) AS PDP_ATC,\n", "        COALESCE(DIRECT_ATC, 0) AS DIRECT_ATC,\n", "        COALESCE(PDP_ATC, 0) + COALESCE(DIRECT_ATC, 0) AS TOTAL_ATC\n", "    FROM\n", "        impr_cg AS a\n", "    JOIN\n", "        click_cg AS b ON a.l2_category=b.l2_category AND a.properties__highlight_ids=b.properties__highlight_ids AND a.at_date_ist = b.at_date_ist\n", "    JOIN\n", "        atc_cg AS c ON a.l2_category = c.l2_category AND a.properties__highlight_ids = c.properties__highlight_ids AND a.at_date_ist = c.at_date_ist\n", "),\n", "--SELECT * FROM final_cg\n", "\n", "impr_tg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        properties__highlight_ids,\n", "        SUM(impr) AS impressions\n", "    FROM\n", "        consumer_etls.plp_highlights_impr a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN\n", "        blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE\n", "                            WHEN a.l2_category IN (SELECT l2_category FROM lalm)\n", "                                THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category)\n", "                                ELSE DATE '2025-04-01'\n", "                        END\n", "        AND mod = 1\n", "        AND properties__page_name = 'Search Page'\n", "        -- and l2_category in (select l2_category from dwh.dim_product where l2_category_id in (164,114,2114,223,310,268,145,1367,1392,5696,5703,5694,5701,5702,5698,5699,5695,5697,5700,5705) and is_current group by 1 )\n", "        --AND l2_category in ('Farm-Fresh Eggs','Smoking Cessation','Glucose Energy Drink','Energy Drinks','Healthcare Devices','Men''s Essentials','Rusks & Wafers','Dishwashing Powders','Batter','Boards & Accessories','Hookah Flavor','Premium Milk Drinks','Ponni Rice','Frozen Potato Snacks','Fresh Fruits','Batteries','Kitchen & Dining Disposables','Office Accessories','Coffee Machine & Accessories','Body Treatment Cream & Roll On','Men''s Pre & Post Shave','Dishwashing Bars','Flowers & Leaves','Hot Chocolate','Poker & Playing Cards','High Protein & Brown Eggs','Markers & Highlighters','Honey','Extension Board','Computer Accessories','DIYs Tools & Locks','Vermicelli','ORS','Shaped Chocolates','Coconut Water','Buns, Pavs & Pizza Base','Men''s Razors & Trimmers','Cold Drink Bottles','Condoms','Pancake Mix','Women''s Razors & Trimmers','Laundry Needs','Notebooks','Writing Aids & Tools','Period Panty','Vegetable Frozen Snacks','Gym Gear & Wearables','Crunchy Peanut Butter','Dessert Mixes','Baby Wipes','Body Oil & Others','Cookies','Toothbrush','Frozen Veg Indian Breads','Bouquets','Art Kits & Craft Materials','Festive Assortment','Bakery Cookies','Wipers','Incense Sticks & Cones','Files & Organisers','Eyebrow Pencil & Enhancers','Makeup Fixer','Potato Chips','Toilet & Other Disposables','Sports Toys','Wound Care','Tinted Face Creams','Butter & More','Antiseptic Liquid','Shringar','Clay, Dough, Slime & Sand','Instant Coffee','Cough & Cold','Frozen Chicken Snacks','Baby Food (6-12 M)','Bottles & Flasks','Artificial Nails','Oral Care Essentials','Brooms & Mops','Premium Chocolates','Lubricants','Paints','Chocolate Packs','Face Gels & Treatment Creams','Antacid','Conditioners','LED Lights','Sanitary Pads','Auto Grooming','Office Paper Stationery','Yogurt','Hair Sprays, Gels & Creams','Weighing Machine','Crayons & Color Pens','Adhesive Tapes','Candles & Diyas','Dishwashing Gels','Detergent Powders','Intimate Hygiene & Care','Makeup Accessories','Spice Mixes','Paint Brushes & Accessories','Chewing Gum','Frozen Peas & Corn','Premium Pasta','Talc Powder','Nail Accessories','Shoe Care','Candies','Fitness Supplements','Kitchen Accessories','Dips','Concealer','Oral & Nasal Care','Bathing Soaps','Earbuds & Headsets','Swimming Accessories','Frozen Chicken','Building Blocks & Sets','Pencils','Bath & Body Accessories','Cricket','Dog Toys','Mouthwash & Spray','Body Lotions','Hair Tools & Accessories','Cup Noodles','Craft Paper & Sheets','Baby Food (12-18 M)','Skin & Hair Care','Men''s Hair Styling','Matic Liquid Detergent','Cars & Bikes','Adult Diapers','Face Moisturisers','Masks & Sanitizers','Kitchen Cleaners','Gift Wrapping Needs','Characters & Figures','Blush & Highlighter','Drawing & Colouring Books','Fans & Coolers','Hand & Power Tools','Face Accessories','Towels','Face Masks & Packs','Cold Drink Cans','Props','Acne & Blackhead Fixers','Bathing Needs','Beauty & Fashion Fixers','Facial Kits & Bleach','Hand & Foot Care','Feeding Essentials','Assorted Crisps','Papad & Fryums','Garbage Bags','Hair Serums','Kettles & Egg Boilers','Fruit Juices','Pens','Face Cleansers','Kitchen Towels & Napkins','Toy Guns','Transport & Other Vehicles','Stain Remover & Whiteners','Classic Games','Health Drinks & Supplements','Kitten Food','Mango Drinks','Nachos','Eye Accessories','Irons & Garment Care','Gourmet Bakery','Tea Premix','Photography & Videography','Hair Straighteners & Dryers','Kajal','Fresh Bakery','Chargers','Pooja Samagri','Mugs & Glasses','Healthy Crisps','Popcorn','Plants','Crisps & Puffs','Decorative Lamps','Travel Accessories','Socks','Stuffed Animals','Eyeliner','Pre & Post Workout','Rice Assortment','Instant Noodles','Beauty Travel Essentials','Creamy Peanut Butter','Day & Night Creams','Aroma & Candles','Wafer Chocolates','Play & Activity','Hair Treatments','Action Figures','Lunch Boxes','Lip Balm','Bags & Pouches','Herbal Drinks','Gaming & Entertainment','Non-Alcoholic Drinks','Chocolate Gift Pack','Face Wash','Mouth Fresheners','Cream Biscuits','Kid''s Cereals','Children''s Activity Books','Power Banks','Football, Basketball & Others','Cleaning Accessories','Premium Candies','Primer','Smart Watches','Bar Accessories','Diapers','Cocktail Mixers','Instant Pasta','Lunch Boxes & Water Bottles','Flavoured Milk & Milk Shake','Bath & Beauty Gifts','Premium Energy Drinks','Bathroom Accessories','Cutlery & Spoons','Premium Fruit Drinks','Tablets, Syrups & Powders','Chocolate Health Drinks','Gardening','Tubs')\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "click_tg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        properties__highlight_ids,\n", "        SUM(pdp_visits) AS clicks\n", "    FROM\n", "        consumer_etls.plp_highlights_pdp_visits a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN\n", "        blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE WHEN a.l2_category IN (SELECT l2_category FROM lalm) THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category) ELSE DATE '2025-04-01' END\n", "        AND mod = 1\n", "        AND properties__page_name = 'Search Page'\n", "        -- and l2_category in (select l2_category from dwh.dim_product where l2_category_id in (164,114,2114,223,310,268,145,1367,1392,5696,5703,5694,5701,5702,5698,5699,5695,5697,5700,5705) and is_current group by 1 )\n", "        --AND l2_category in ('Farm-Fresh Eggs','Smoking Cessation','Glucose Energy Drink','Energy Drinks','Healthcare Devices','Men''s Essentials','Rusks & Wafers','Dishwashing Powders','Batter','Boards & Accessories','Hookah Flavor','Premium Milk Drinks','Ponni Rice','Frozen Potato Snacks','Fresh Fruits','Batteries','Kitchen & Dining Disposables','Office Accessories','Coffee Machine & Accessories','Body Treatment Cream & Roll On','Men''s Pre & Post Shave','Dishwashing Bars','Flowers & Leaves','Hot Chocolate','Poker & Playing Cards','High Protein & Brown Eggs','Markers & Highlighters','Honey','Extension Board','Computer Accessories','DIYs Tools & Locks','Vermicelli','ORS','Shaped Chocolates','Coconut Water','Buns, Pavs & Pizza Base','Men''s Razors & Trimmers','Cold Drink Bottles','Condoms','Pancake Mix','Women''s Razors & Trimmers','Laundry Needs','Notebooks','Writing Aids & Tools','Period Panty','Vegetable Frozen Snacks','Gym Gear & Wearables','Crunchy Peanut Butter','Dessert Mixes','Baby Wipes','Body Oil & Others','Cookies','Toothbrush','Frozen Veg Indian Breads','Bouquets','Art Kits & Craft Materials','Festive Assortment','Bakery Cookies','Wipers','Incense Sticks & Cones','Files & Organisers','Eyebrow Pencil & Enhancers','Makeup Fixer','Potato Chips','Toilet & Other Disposables','Sports Toys','Wound Care','Tinted Face Creams','Butter & More','Antiseptic Liquid','Shringar','Clay, Dough, Slime & Sand','Instant Coffee','Cough & Cold','Frozen Chicken Snacks','Baby Food (6-12 M)','Bottles & Flasks','Artificial Nails','Oral Care Essentials','Brooms & Mops','Premium Chocolates','Lubricants','Paints','Chocolate Packs','Face Gels & Treatment Creams','Antacid','Conditioners','LED Lights','Sanitary Pads','Auto Grooming','Office Paper Stationery','Yogurt','Hair Sprays, Gels & Creams','Weighing Machine','Crayons & Color Pens','Adhesive Tapes','Candles & Diyas','Dishwashing Gels','Detergent Powders','Intimate Hygiene & Care','Makeup Accessories','Spice Mixes','Paint Brushes & Accessories','Chewing Gum','Frozen Peas & Corn','Premium Pasta','Talc Powder','Nail Accessories','Shoe Care','Candies','Fitness Supplements','Kitchen Accessories','Dips','Concealer','Oral & Nasal Care','Bathing Soaps','Earbuds & Headsets','Swimming Accessories','Frozen Chicken','Building Blocks & Sets','Pencils','Bath & Body Accessories','Cricket','Dog Toys','Mouthwash & Spray','Body Lotions','Hair Tools & Accessories','Cup Noodles','Craft Paper & Sheets','Baby Food (12-18 M)','Skin & Hair Care','Men''s Hair Styling','Matic Liquid Detergent','Cars & Bikes','Adult Diapers','Face Moisturisers','Masks & Sanitizers','Kitchen Cleaners','Gift Wrapping Needs','Characters & Figures','Blush & Highlighter','Drawing & Colouring Books','Fans & Coolers','Hand & Power Tools','Face Accessories','Towels','Face Masks & Packs','Cold Drink Cans','Props','Acne & Blackhead Fixers','Bathing Needs','Beauty & Fashion Fixers','Facial Kits & Bleach','Hand & Foot Care','Feeding Essentials','Assorted Crisps','Papad & Fryums','Garbage Bags','Hair Serums','Kettles & Egg Boilers','Fruit Juices','Pens','Face Cleansers','Kitchen Towels & Napkins','Toy Guns','Transport & Other Vehicles','Stain Remover & Whiteners','Classic Games','Health Drinks & Supplements','Kitten Food','Mango Drinks','Nachos','Eye Accessories','Irons & Garment Care','Gourmet Bakery','Tea Premix','Photography & Videography','Hair Straighteners & Dryers','Kajal','Fresh Bakery','Chargers','Pooja Samagri','Mugs & Glasses','Healthy Crisps','Popcorn','Plants','Crisps & Puffs','Decorative Lamps','Travel Accessories','Socks','Stuffed Animals','Eyeliner','Pre & Post Workout','Rice Assortment','Instant Noodles','Beauty Travel Essentials','Creamy Peanut Butter','Day & Night Creams','Aroma & Candles','Wafer Chocolates','Play & Activity','Hair Treatments','Action Figures','Lunch Boxes','Lip Balm','Bags & Pouches','Herbal Drinks','Gaming & Entertainment','Non-Alcoholic Drinks','Chocolate Gift Pack','Face Wash','Mouth Fresheners','Cream Biscuits','Kid''s Cereals','Children''s Activity Books','Power Banks','Football, Basketball & Others','Cleaning Accessories','Premium Candies','Primer','Smart Watches','Bar Accessories','Diapers','Cocktail Mixers','Instant Pasta','Lunch Boxes & Water Bottles','Flavoured Milk & Milk Shake','Bath & Beauty Gifts','Premium Energy Drinks','Bathroom Accessories','Cutlery & Spoons','Premium Fruit Drinks','Tablets, Syrups & Powders','Chocolate Health Drinks','Gardening','Tubs')\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "atc_tg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        a.l2_category,\n", "        properties__highlight_ids,\n", "        SUM(CASE WHEN properties__sub_page_name = 'product_page' THEN atc END) AS PDP_ATC,\n", "        SUM(CASE WHEN properties__sub_page_name NOT IN  ('product_page','variant_bottom_sheet') OR properties__sub_page_name IS NULL THEN atc END) AS DIRECT_ATC\n", "    FROM\n", "        consumer_etls.plp_highlights_atc a\n", "    JOIN\n", "        live_xp_cats lxc ON lxc.l2 = a.l2_category\n", "    CROSS JOIN\n", "        blacklisted\n", "    WHERE\n", "        at_date_ist >= CASE WHEN a.l2_category IN (SELECT l2_category FROM lalm) THEN (SELECT max_lcd FROM lalm WHERE lalm.l2_category = a.l2_category) ELSE DATE '2025-04-01' END\n", "        --and at_date_ist >= date('2025-03-20')\n", "        --where at_date_ist between date('2025-04-01') and date('2025-04-25')\n", "        AND mod = 1\n", "        AND properties__page_name = 'Search Page'\n", "        --and l2_category in (select l2_category from dwh.dim_product where l2_category_id in (164,114,2114,223,310,268,145,1367,1392,5696,5703,5694,5701,5702,5698,5699,5695,5697,5700,5705) and is_current group by 1 )\n", "        --AND l2_category in ('Farm-Fresh Eggs','Smoking Cessation','Glucose Energy Drink','Energy Drinks','Healthcare Devices','Men''s Essentials','Rusks & Wafers','Dishwashing Powders','Batter','Boards & Accessories','Hookah Flavor','Premium Milk Drinks','Ponni Rice','Frozen Potato Snacks','Fresh Fruits','Batteries','Kitchen & Dining Disposables','Office Accessories','Coffee Machine & Accessories','Body Treatment Cream & Roll On','Men''s Pre & Post Shave','Dishwashing Bars','Flowers & Leaves','Hot Chocolate','Poker & Playing Cards','High Protein & Brown Eggs','Markers & Highlighters','Honey','Extension Board','Computer Accessories','DIYs Tools & Locks','Vermicelli','ORS','Shaped Chocolates','Coconut Water','Buns, Pavs & Pizza Base','Men''s Razors & Trimmers','Cold Drink Bottles','Condoms','Pancake Mix','Women''s Razors & Trimmers','Laundry Needs','Notebooks','Writing Aids & Tools','Period Panty','Vegetable Frozen Snacks','Gym Gear & Wearables','Crunchy Peanut Butter','Dessert Mixes','Baby Wipes','Body Oil & Others','Cookies','Toothbrush','Frozen Veg Indian Breads','Bouquets','Art Kits & Craft Materials','Festive Assortment','Bakery Cookies','Wipers','Incense Sticks & Cones','Files & Organisers','Eyebrow Pencil & Enhancers','Makeup Fixer','Potato Chips','Toilet & Other Disposables','Sports Toys','Wound Care','Tinted Face Creams','Butter & More','Antiseptic Liquid','Shringar','Clay, Dough, Slime & Sand','Instant Coffee','Cough & Cold','Frozen Chicken Snacks','Baby Food (6-12 M)','Bottles & Flasks','Artificial Nails','Oral Care Essentials','Brooms & Mops','Premium Chocolates','Lubricants','Paints','Chocolate Packs','Face Gels & Treatment Creams','Antacid','Conditioners','LED Lights','Sanitary Pads','Auto Grooming','Office Paper Stationery','Yogurt','Hair Sprays, Gels & Creams','Weighing Machine','Crayons & Color Pens','Adhesive Tapes','Candles & Diyas','Dishwashing Gels','Detergent Powders','Intimate Hygiene & Care','Makeup Accessories','Spice Mixes','Paint Brushes & Accessories','Chewing Gum','Frozen Peas & Corn','Premium Pasta','Talc Powder','Nail Accessories','Shoe Care','Candies','Fitness Supplements','Kitchen Accessories','Dips','Concealer','Oral & Nasal Care','Bathing Soaps','Earbuds & Headsets','Swimming Accessories','Frozen Chicken','Building Blocks & Sets','Pencils','Bath & Body Accessories','Cricket','Dog Toys','Mouthwash & Spray','Body Lotions','Hair Tools & Accessories','Cup Noodles','Craft Paper & Sheets','Baby Food (12-18 M)','Skin & Hair Care','Men''s Hair Styling','Matic Liquid Detergent','Cars & Bikes','Adult Diapers','Face Moisturisers','Masks & Sanitizers','Kitchen Cleaners','Gift Wrapping Needs','Characters & Figures','Blush & Highlighter','Drawing & Colouring Books','Fans & Coolers','Hand & Power Tools','Face Accessories','Towels','Face Masks & Packs','Cold Drink Cans','Props','Acne & Blackhead Fixers','Bathing Needs','Beauty & Fashion Fixers','Facial Kits & Bleach','Hand & Foot Care','Feeding Essentials','Assorted Crisps','Papad & Fryums','Garbage Bags','Hair Serums','Kettles & Egg Boilers','Fruit Juices','Pens','Face Cleansers','Kitchen Towels & Napkins','Toy Guns','Transport & Other Vehicles','Stain Remover & Whiteners','Classic Games','Health Drinks & Supplements','Kitten Food','Mango Drinks','Nachos','Eye Accessories','Irons & Garment Care','Gourmet Bakery','Tea Premix','Photography & Videography','Hair Straighteners & Dryers','Kajal','Fresh Bakery','Chargers','Pooja Samagri','Mugs & Glasses','Healthy Crisps','Popcorn','Plants','Crisps & Puffs','Decorative Lamps','Travel Accessories','Socks','Stuffed Animals','Eyeliner','Pre & Post Workout','Rice Assortment','Instant Noodles','Beauty Travel Essentials','Creamy Peanut Butter','Day & Night Creams','Aroma & Candles','Wafer Chocolates','Play & Activity','Hair Treatments','Action Figures','Lunch Boxes','Lip Balm','Bags & Pouches','Herbal Drinks','Gaming & Entertainment','Non-Alcoholic Drinks','Chocolate Gift Pack','Face Wash','Mouth Fresheners','Cream Biscuits','Kid''s Cereals','Children''s Activity Books','Power Banks','Football, Basketball & Others','Cleaning Accessories','Premium Candies','Primer','Smart Watches','Bar Accessories','Diapers','Cocktail Mixers','Instant Pasta','Lunch Boxes & Water Bottles','Flavoured Milk & Milk Shake','Bath & Beauty Gifts','Premium Energy Drinks','Bathroom Accessories','Cutlery & Spoons','Premium Fruit Drinks','Tablets, Syrups & Powders','Chocolate Health Drinks','Gardening','Tubs')\n", "        AND CARDINALITY(\n", "            ARRAY_INTERSECT(\n", "                TRANSFORM(SPLIT(a.properties__highlight_ids, ','), x -> CAST(x AS INTEGER)),\n", "                blacklist\n", "            )\n", "        ) = 0\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "\n", "final_tg AS (\n", "    SELECT\n", "        a.at_date_ist,\n", "        a.l2_category,\n", "        a.properties__highlight_ids,\n", "        impressions,\n", "        clicks,\n", "        COALESCE(PDP_ATC, 0) AS PDP_ATC,\n", "        COALESCE(DIRECT_ATC, 0) AS DIRECT_ATC,\n", "        COALESCE(PDP_ATC, 0) + COALESCE(DIRECT_ATC, 0) AS TOTAL_ATC\n", "    FROM\n", "        impr_tg AS a\n", "    JOIN\n", "        click_tg AS b ON a.l2_category = b.l2_category AND a.properties__highlight_ids = b.properties__highlight_ids AND a.at_date_ist = b.at_date_ist \n", "    JOIN\n", "        atc_tg AS c ON a.l2_category = c.l2_category AND a.properties__highlight_ids = c.properties__highlight_ids AND a.at_date_ist = c.at_date_ist\n", "),\n", "--SELECT TYPEOF(properties__highlight_ids) FROM final_tg\n", "--SELECT * FROM final_tg WHERE l2_category = \n", "\n", "cg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        l2_category,\n", "        properties__highlight_ids,\n", "        IF(b.name IS NOT NULL, FILTER(ARRAY[b.name, c.name], x -> x IS NOT NULL), NULL) AS attribute,\n", "        impressions,\n", "        clicks,\n", "        PDP_ATC,\n", "        DIRECT_ATC,\n", "        TOTAL_ATC\n", "    FROM\n", "        final_cg AS a\n", "    LEFT JOIN\n", "        cms.gr_attribute AS b ON SPLIT(a.properties__highlight_ids, ',')[1] = CAST(b.id AS VARCHAR) \n", "    LEFT JOIN\n", "        cms.gr_attribute AS c ON IF(CARDINALITY(SPLIT(a.properties__highlight_ids, ',')) > 1, SPLIT(a.properties__highlight_ids, ',')[2],NULL) = CAST(c.id AS VARCHAR)\n", "    WHERE\n", "        CONCAT(b.name, IF(c.name IS NOT NULL, CONCAT(',', c.name), '')) IS NOT NULL\n", "),\n", "--select * from cg limit 5\n", "tg AS (\n", "    SELECT\n", "        at_date_ist,\n", "        l2_category,\n", "        properties__highlight_ids,\n", "        IF(b.name IS NOT NULL, FILTER(ARRAY[b.name, c.name], x -> x IS NOT NULL), NULL) AS attribute,\n", "        impressions,\n", "        clicks,\n", "        PDP_ATC,\n", "        DIRECT_ATC,\n", "        TOTAL_ATC\n", "    FROM\n", "        final_tg AS a\n", "    LEFT JOIN\n", "        cms.gr_attribute AS b ON SPLIT(a.properties__highlight_ids, ',')[1] = CAST(b.id AS VARCHAR) \n", "    LEFT JOIN\n", "        cms.gr_attribute AS c ON IF(CARDINALITY(SPLIT(a.properties__highlight_ids, ',')) > 1, SPLIT(a.properties__highlight_ids, ',')[2],NULL) = CAST(c.id AS VARCHAR)\n", "    WHERE\n", "        CONCAT(b.name, IF(c.name IS NOT NULL, CONCAT(',', c.name), '')) IS NOT NULL\n", "),\n", "--SELECT * FROM tg LIMIT 1\n", "tg_agg AS (\n", "    SELECT\n", "        l2_category,\n", "        \n", "        CASE\n", "            WHEN attribute[1] = 'Unit (with options)' THEN ARRAY_DISTINCT(attribute)\n", "            ELSE ARRAY_SORT(ARRAY_DISTINCT(attribute))\n", "        END AS tg_attribute,\n", "        \n", "        CASE\n", "            WHEN attribute[1] = 'Unit (with options)' THEN TRANSFORM(SPLIT(properties__highlight_ids, ','), x -> CAST(x AS INT))\n", "            ELSE (\n", "                    SELECT ARRAY_AGG(CAST(id AS INT) ORDER BY attr)\n", "                    FROM\n", "                        UNNEST(SPLIT(properties__highlight_ids, ','), attribute) AS t(id, attr)\n", "                    )\n", "        END AS tg_attribute_ids,\n", "        \n", "        CARDINALITY\n", "        (\n", "            CASE\n", "                WHEN attribute[1] = 'Unit (with options)' THEN ARRAY_DISTINCT(attribute)\n", "                ELSE ARRAY_SORT(ARRAY_DISTINCT(attribute))\n", "            END\n", "        ) AS card_tg_attribute,\n", "        \n", "        SUM(impressions)        AS tg_imp,\n", "        SUM(clicks)  AS tg_pdp_opens,\n", "        SUM(DIRECT_ATC)    AS tg_dir_atc\n", "    FROM\n", "        tg\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "--SELECT * FROM tg_agg\n", "cg_agg AS (\n", "    SELECT\n", "        l2_category,\n", "        CASE\n", "            WHEN attribute[1] = 'Unit (with options)' THEN ARRAY_DISTINCT(attribute)\n", "            ELSE ARRAY_SORT(ARRAY_DISTINCT(attribute))\n", "        END AS cg_attribute,\n", "        \n", "        CASE\n", "            WHEN attribute[1] = 'Unit (with options)' THEN TRANSFORM(SPLIT(properties__highlight_ids, ','), x -> CAST(x AS INT))\n", "            ELSE (\n", "                    SELECT ARRAY_AGG(CAST(id AS INT) ORDER BY attr)\n", "                    FROM\n", "                        UNNEST(SPLIT(properties__highlight_ids, ','), attribute) AS t(id, attr)\n", "                    )\n", "        END AS cg_attribute_ids,\n", "        \n", "        CARDINALITY\n", "        (\n", "            CASE\n", "                WHEN attribute[1] = 'Unit (with options)' THEN ARRAY_DISTINCT(attribute)\n", "                ELSE array_sort(ARRAY_DISTINCT(attribute))\n", "            END\n", "        ) AS card_cg_attribute,\n", "        SUM(impressions)        AS cg_imp,\n", "        SUM(clicks)  AS cg_pdp_opens,\n", "        SUM(DIRECT_ATC)    AS cg_dir_atc\n", "    FROM\n", "        cg\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "max_imp_cg_agg AS (\n", "    SELECT\n", "        a.l2_category,\n", "        a.cg_attribute,\n", "        cg_attribute_ids,\n", "        card_cg_attribute,\n", "        a.cg_imp,\n", "        a.cg_pdp_opens,\n", "        a.cg_dir_atc,\n", "        lalm.max_lcd\n", "    FROM\n", "        (\n", "            SELECT\n", "                *\n", "            FROM    \n", "                (\n", "                    SELECT\n", "                        l2_category,\n", "                        cg_attribute,\n", "                        cg_attribute_ids,\n", "                        card_cg_attribute,\n", "                        cg_imp,\n", "                        cg_pdp_opens,\n", "                        cg_dir_atc,\n", "                        ROW_NUMBER() OVER (PARTITION BY l2_category ORDER BY cg_imp DESC) AS rnk\n", "                    FROM\n", "                        cg_agg\n", "                ) b\n", "            WHERE rnk = 1\n", "        ) a\n", "    LEFT JOIN\n", "        lalm ON lalm.l2_category = a.l2_category-- AND a.cg_attribute = lalm.post_attribute\n", "--    WHERE\n", "--        rnk = 1\n", "),\n", "--SELECT * FROM max_imp_cg_agg\n", "EnabledCategories AS (\n", "    SELECT\n", "        c.id AS l2_category_id,\n", "        c.name AS L2\n", "    FROM \n", "        cms.gr_category c\n", "    JOIN\n", "        cms.gr_product_category_mapping pcm ON pcm.category_id = c.id\n", "    WHERE \n", "        pcm.is_primary\n", "        AND pcm.lake_active_record\n", "        AND c.enabled_flag\n", "--        AND c.id = 1164\n", "    GROUP BY 1, 2\n", "),\n", "PLPPDPAttributes AS (\n", "    SELECT\n", "        entity_instance_id AS l2_category_id,\n", "        L2,\n", "        plp.attribute_id,\n", "        an.name AS attribute_name,\n", "        plp.service_type,\n", "        ROW_NUMBER() OVER (PARTITION BY entity_instance_id ORDER BY sort_order) sort_order\n", "    FROM \n", "        cms.entity_attribute_mappings plp\n", "    LEFT JOIN\n", "        cms.gr_attribute an ON plp.attribute_id = an.id AND an.lake_active_record\n", "    JOIN\n", "        EnabledCategories ec ON ec.l2_category_id = plp.entity_instance_id\n", "    WHERE \n", "        plp.entity_id = 1\n", "        AND plp.enabled_flag\n", "        AND plp.service_type IN ('PLP')\n", "        AND plp.type = 'key_highlights'\n", "        AND plp.lake_active_record\n", "--        AND plp.sort_order < 3\n", "--    GROUP BY 1, 2, 3, 4, 5, 6\n", "),\n", "actual_plp_atts AS (\n", "    SELECT\n", "        l2_category_id,\n", "        L2,\n", "        ARRAY_AGG(attribute_name ORDER BY sort_order) AS plp_attributes,\n", "        ARRAY_AGG(attribute_id ORDER BY sort_order) AS plp_attribute_ids,\n", "        CARDINALITY(ARRAY_AGG(attribute_name ORDER BY sort_order)) AS card_plp_attributes\n", "    FROM\n", "        PLPPDPAttributes\n", "    WHERE\n", "        sort_order < 3\n", "    GROUP BY 1, 2\n", "),\n", "final AS (\n", "    SELECT\n", "        ta.l2_category,\n", "        tg_attribute,\n", "        tg_attribute_ids,\n", "        card_tg_attribute,\n", "        tg_imp,\n", "        tg_pdp_opens,\n", "        tg_dir_atc,\n", "        cg_attribute,\n", "        cg_attribute_ids,\n", "        card_cg_attribute,\n", "        CASE WHEN cg_attribute[1] = tg_attribute[1] THEN 1 ELSE 0 END AS same_first_attribute,\n", "        CASE WHEN card_tg_attribute = 1 AND CONTAINS(cg_attribute, tg_attribute[1]) AND card_cg_attribute > 1 THEN 1 ELSE 0 END AS filter_1,\n", "        CASE WHEN tg_attribute <> cg_attribute THEN 1 ELSE 0 END AS filter_2,\n", "        ca.max_lcd,\n", "        cg_imp,\n", "        cg_pdp_opens,\n", "        cg_dir_atc,\n", "        tg_pdp_opens * 1.0000000000 / tg_imp AS tg_pdp_imp,\n", "        cg_pdp_opens * 1.0000000000 / cg_imp AS cg_pdp_imp,\n", "        (tg_pdp_opens * 1.0000000000 / tg_imp) - (cg_pdp_opens * 1.0000000000 / cg_imp) AS delta_pdp_imp,\n", "        tg_dir_atc * 1.0000000000 / tg_imp AS tg_atc_imp,\n", "        cg_dir_atc * 1.0000000000 / cg_imp AS cg_atc_imp,\n", "        (tg_dir_atc * 1.0000000000 / tg_imp) - (cg_dir_atc * 1.0000000000 / cg_imp) AS delta_atc_imp,\n", "        ARRAY_JOIN(\n", "            CASE\n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 2\n", "                    AND NOT CONTAINS(cg_attribute, 'Unit (with options)')\n", "                    AND NOT CONTAINS(cg_attribute, tg_attribute[1])\n", "                THEN\n", "                    ARRAY[ tg_attribute[1], cg_attribute[2] ]\n", "                    \n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 2\n", "                    AND CONTAINS(cg_attribute, 'Unit (with options)')\n", "                    AND NOT CONTAINS(cg_attribute, tg_attribute[1])\n", "                THEN\n", "                    ARRAY[ cg_attribute[1], tg_attribute[1] ]\n", "                \n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 1\n", "                THEN\n", "                    CASE\n", "                        WHEN\n", "                            card_plp_attributes = 2\n", "                        THEN\n", "                            CASE\n", "                                WHEN\n", "                                    tg_attribute[1] != plp_attributes[2]\n", "                                THEN\n", "                                    ARRAY[ tg_attribute[1], plp_attributes[2] ]\n", "                            ELSE\n", "                                ARRAY[ tg_attribute[1], cg_attribute[1] ]\n", "                            END\n", "                    ELSE\n", "                        CASE\n", "                            WHEN\n", "                                tg_attribute[1] != plp_attributes[1]\n", "                            THEN\n", "                                ARRAY[ tg_attribute[1], plp_attributes[1] ]\n", "                        ELSE\n", "                            ARRAY[ tg_attribute[1], cg_attribute[1] ]\n", "                        END\n", "                    END\n", "            ELSE\n", "                tg_attribute\n", "            END, ', '\n", "        )\n", "        AS new_plp_list,\n", "        \n", "        ARRAY_JOIN(\n", "            CASE\n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 2\n", "                    AND NOT CONTAINS(cg_attribute, 'Unit (with options)')\n", "                    AND NOT CONTAINS(cg_attribute, tg_attribute[1])\n", "                THEN\n", "                    ARRAY[ tg_attribute_ids[1], cg_attribute_ids[2] ]\n", "                    \n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 2\n", "                    AND CONTAINS(cg_attribute, 'Unit (with options)')\n", "                    AND NOT CONTAINS(cg_attribute, tg_attribute[1])\n", "                THEN\n", "                    ARRAY[ cg_attribute_ids[1], tg_attribute_ids[1] ]\n", "                \n", "                WHEN\n", "                    card_tg_attribute = 1\n", "                    AND card_cg_attribute = 1\n", "                THEN\n", "                    CASE\n", "                        WHEN\n", "                            card_plp_attributes = 2\n", "                        THEN\n", "                            CASE\n", "                                WHEN\n", "                                    tg_attribute[1] != plp_attributes[2]\n", "                                THEN\n", "                                    ARRAY[ tg_attribute_ids[1], plp_attribute_ids[2] ]\n", "                            ELSE\n", "                                ARRAY[ tg_attribute_ids[1], cg_attribute_ids[1] ]\n", "                            END\n", "                    ELSE\n", "                        CASE\n", "                            WHEN\n", "                                tg_attribute[1] != plp_attributes[1]\n", "                            THEN\n", "                                ARRAY[ tg_attribute_ids[1], plp_attribute_ids[1] ]\n", "                        ELSE\n", "                            ARRAY[ tg_attribute_ids[1], cg_attribute_ids[1] ]\n", "                        END\n", "                    END\n", "            ELSE\n", "                tg_attribute_ids\n", "            END, ', '\n", "        )\n", "        AS new_plp_list_ids\n", "-- when tg att is 1, cg att is 1, make tg att 1 as the new plp 1, and then look for the second elemtn in actual - if it mathces with tg att 1 then cg att\n", "    FROM\n", "        tg_agg ta\n", "    LEFT JOIN\n", "        max_imp_cg_agg ca ON ca.l2_category = ta.l2_category\n", "    LEFT JOIN\n", "        actual_plp_atts apa ON apa.L2 = ta.l2_category\n", "),\n", "pre_z_base AS (\n", "    SELECT\n", "        l2_category,\n", "        tg_attribute,\n", "        tg_imp,\n", "        tg_dir_atc,\n", "        tg_pdp_imp,\n", "        tg_atc_imp,\n", "        cg_attribute,\n", "        max_lcd,\n", "        cg_imp,\n", "        cg_dir_atc,\n", "        cg_pdp_imp,\n", "        cg_atc_imp,\n", "        delta_pdp_imp,\n", "        delta_atc_imp,\n", "        new_plp_list,\n", "        tg_attribute_ids,\n", "        cg_attribute_ids,\n", "        new_plp_list_ids\n", "    FROM\n", "         final    \n", "    WHERE\n", "        filter_1 = 0\n", "        AND filter_2 = 1\n", "        AND tg_dir_atc > 500\n", ")--,\n", "SELECT * FROM pre_z_base-- WHERE l2_category = 'Matic Detergent Powders'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e57dd805-9167-4138-88c0-90baaf87a3a1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec06b1e1-dfff-4a89-83b5-a63f8d032eba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a3e401b-dd7c-4b31-884f-5353c7d14be7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46b7d12e-ad6b-43d6-9c15-bd044d7bac71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a08c5470-12a4-4d7a-a878-908d28d75fc5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "691789f4-56d6-4d22-a8b0-bee4a7950817", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}