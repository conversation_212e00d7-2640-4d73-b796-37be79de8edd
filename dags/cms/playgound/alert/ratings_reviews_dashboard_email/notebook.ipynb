{"cells": [{"cell_type": "code", "execution_count": null, "id": "4642bed1-2ed0-44dc-bdc2-bd6a99dc38ab", "metadata": {}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "14d33c1e-147e-4ea5-8047-b1f497723e30", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "from datetime import datetime\n", "import sys\n", "\n", "# Detect execution date (Airflow or local)\n", "try:\n", "    exec_date = datetime.strptime(\n", "        str(get_ipython().user_ns[\"execution_date\"]), \"%Y-%m-%dT%H:%M:%S.%fZ\"\n", "    )\n", "except KeyError:\n", "    exec_date = datetime.today()\n", "\n", "# Check for first Monday\n", "if exec_date.weekday() == 0 and 1 <= exec_date.day <= 7:\n", "    print(\"✅ First Monday of the month — continue execution.\")\n", "else:\n", "    print(\"❌ Not the first Monday of the month — exiting gracefully.\")\n", "    print(\"Notebook intentionally skipped.\")\n", "    sys.exit(0)  # ✅ EXIT SUCCESSFULLY\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "42c3996b-4569-47e4-9678-1ec753bd651a", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import gc"]}, {"cell_type": "code", "execution_count": null, "id": "a6fac7b0-2d0a-45a8-91b8-d617df7be14b", "metadata": {}, "outputs": [], "source": ["!pip install xlsxwriter"]}, {"cell_type": "code", "execution_count": null, "id": "93aff93f-196f-4d3e-919d-14dabf41f29b", "metadata": {}, "outputs": [], "source": ["!pip install rich"]}, {"cell_type": "code", "execution_count": null, "id": "80c77176-9927-4675-b54d-fb3fb5d70916", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "61d6ed6c-bd01-49db-9215-522d0c47c6b9", "metadata": {}, "outputs": [], "source": ["import json\n", "import time\n", "from tqdm.auto import tqdm\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "a465d383-f50c-4f9c-b41e-994babed3a0f", "metadata": {}, "outputs": [], "source": ["!pip install openai"]}, {"cell_type": "code", "execution_count": null, "id": "2b689c80-4e16-4c4a-a6a7-e1cbceaf3436", "metadata": {}, "outputs": [], "source": ["import openai"]}, {"cell_type": "code", "execution_count": null, "id": "e634ee32-8969-4079-9e0c-43df569371d7", "metadata": {}, "outputs": [], "source": ["api_key = \"***********************************************************************************************************************************************************************\"\n", "openai.api_key = api_key"]}, {"cell_type": "code", "execution_count": null, "id": "0ae8202c-3e2e-41e9-8424-b01134d10c15", "metadata": {}, "outputs": [], "source": ["model_name = \"gpt-4o-mini\"\n", "\n", "BASE_URL = \"https://api.openai.com/v1/chat/completions\"\n", "HEADERS = {\"Authorization\": f\"Bearer {api_key}\", \"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": null, "id": "1bbb07fa-7557-45f0-a8fa-bbfcbda96b6a", "metadata": {}, "outputs": [], "source": ["client = openai.Client(api_key=api_key)"]}, {"cell_type": "code", "execution_count": null, "id": "a40796e2-6006-4ad9-9a0b-46b72ee543fe", "metadata": {}, "outputs": [], "source": ["mfg = \"\"\"\n", "WITH pid_mfg AS (\n", "    SELECT\n", "        gp.id AS product_id,\n", "        pb.manufacturer_id AS mfg_id,\n", "        pm.name AS mfg_name\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "            AND dp.l0_category_id = 332\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "        --AND pb.manufacturer_id = 2942\n", "    GROUP BY 1, 2, 3\n", "),\n", "vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY AS yesterday,\n", "\t\tcurrent_date - INTERVAL '181' DAY AS global_start_date\n", "),\n", "order_details AS (\n", "\tSELECT\n", "\t\tpd.mfg_id,\n", "\t\tpd.mfg_name,\n", "\t\tCOUNT(DISTINCT od.product_id) AS products,\n", "\t\t--ARRAY_DISTINCT(ARRAY_AGG(pd.product_id)) AS products_list,\n", "\t\tMAX(od.install_ts) AS flag\n", "\tFROM\n", "\t    oms_bifrost.oms_order_item od\n", "\tJOIN\n", "\t    pid_mfg pd ON pd.product_id = od.product_id\n", "\tWHERE\n", "\t\tod.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR)\n", "        AND od.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "        --AND om.city_name IN ('Bengaluru', 'Chennai', 'Delhi', 'Faridabad', 'HR-NCR', 'Kolkata', 'Mumbai')\n", "        AND od.lake_active_record\n", "    GROUP BY 1, 2\n", "),\n", "--SELECT * FROM order_details\n", "base AS (\n", "    SELECT\n", "        id AS user_id,\n", "        email,\n", "        TRIM(manufacturer_ids) AS manufacturer_ids\n", "    FROM\n", "        vendor_console.user_details\n", "    WHERE\n", "        active\n", "),\n", "\n", "mfg_email_user AS (\n", "    SELECT\n", "        user_id,\n", "        email,\n", "        CAST(TRIM(manu_id) AS INT) AS mfg_id\n", "    FROM\n", "        base,\n", "        UNNEST(SPLIT(manufacturer_ids, ',')) AS t(manu_id)\n", "    WHERE\n", "        manufacturer_ids IS NOT NULL\n", "),\n", "\n", "final AS (\n", "    SELECT\n", "        od.mfg_id,\n", "        od.mfg_name,\n", "        od.products,\n", "        --od.products_list,\n", "        meu.user_id,\n", "        meu.email,\n", "        DENSE_RANK () OVER (ORDER BY od.mfg_id) AS rnk\n", "    FROM\n", "        order_details od\n", "    JOIN\n", "        mfg_email_user meu\n", "            ON meu.mfg_id = od.mfg_id\n", "    WHERE\n", "        flag IS NOT NULL\n", "        AND products > 1\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    final\n", "WHERE\n", "    rnk < 3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3b75b542-58a4-420a-bcdc-a686a5396ad6", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "mfg_df = pd.read_sql_query(sql=mfg, con=con)\n", "\n", "mfg_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "6909ecde-f53d-4608-a314-498ff01ab2fd", "metadata": {}, "outputs": [], "source": ["mfg_df"]}, {"cell_type": "code", "execution_count": null, "id": "ecbb66f3-de55-418b-b45b-3dd577298c69", "metadata": {}, "outputs": [], "source": ["manufacturers = mfg_df[\"mfg_id\"].dropna().astype(int).unique().tolist()\n", "manufacturers"]}, {"cell_type": "code", "execution_count": null, "id": "5d0131f6-fdbb-4798-b2a7-9ee368ede6d8", "metadata": {}, "outputs": [], "source": ["# manufacturers = [1399, 2373]"]}, {"cell_type": "code", "execution_count": null, "id": "ed28b1ac-b6ca-4b82-9d79-324350660777", "metadata": {}, "outputs": [], "source": ["ratings_query_template = \"\"\"\n", "WITH vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY           AS yesterday,\n", "\t\tcurrent_date - INTERVAL '562' DAY         AS global_start_date\n", "),\n", "products_data AS (\n", "    SELECT\n", "        ipm.product_id,\n", "        gp.name AS product_name,\n", "        pb.manufacturer_id,\n", "        pm.name AS manufacturer,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "            {filter_clause}\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "order_details AS (\n", "\tSELECT\n", "\t\tod.order_id                           AS order_id,\n", "\t\tCAST(od.insert_ds_ist AS date)    AS order_date,\n", "\t\tom.city_name\n", "\tFROM\n", "\t    oms_bifrost.oms_order_item od\n", "\tJOIN\n", "\t    products_data pd ON pd.product_id = od.product_id\n", "\tJOIN\n", "\t    oms_bifrost.oms_order ooi\n", "\t        ON ooi.id = od.order_id AND ooi.lake_active_record\n", "\t        AND ooi.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR) AND ooi.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "\tJOIN\n", "\t    oms_bifrost.oms_merchant om\n", "\t        ON om.id = ooi.merchant_id\n", "            AND ooi.lake_active_record\n", "\t        AND om.insert_ds_ist IS NOT NULL\n", "\tWHERE\n", "\t\tod.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR)\n", "        AND od.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "        --AND om.city_name IN ('Bengaluru', 'Chennai', 'Delhi', 'Faridabad', 'HR-NCR', 'Kolkata', 'Mumbai')\n", "        AND od.lake_active_record\n", "),\n", "raw_ratings_data AS (\n", "\tSELECT\n", "\t\tTRY_CAST(regexp_replace(partition_key, '^#u_id-', '') AS BIGINT)   AS user_id,\n", "\t\tTRY_CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT) AS order_id,\n", "\t\tTRY_CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER)      AS product_id,\n", "\t\tTRY_CAST(rating AS INTEGER)                                        AS rating,\n", "\t\tDATE(FROM_UNIXTIME(updated_at))                                AS updated_date\n", "\tFROM\n", "\t    zomato.dynamodb.blinkit_ratings_and_reviews\n", "\tWHERE\n", "\t\tupdated_at >= (SELECT to_unixtime(yesterday) FROM vars) - 562 * 86400\n", "\t\tAND updated_at <  (SELECT to_unixtime(yesterday) FROM vars)\n", "\t\tAND sort_key  LIKE '%%#p_id%%'\n", "\t\tAND partition_key LIKE '#u_id%%'\n", "\tGROUP BY 1, 2, 3, 4, 5\n", "),\n", "rating_data AS (\n", "\tSELECT\n", "\t\trrd.user_id,\n", "\t\trrd.order_id,\n", "\t\trrd.product_id,\n", "\t\tpd.product_name,\n", "\t\trrd.rating,\n", "\t\trrd.updated_date,\n", "\t\tpd.l2_category,\n", "\t\tpd.l0_category,\n", "\t\tpd.brand,\n", "\t\tpd.product_type,\n", "\t\tod.order_date,\n", "\t\tod.city_name\n", "\tFROM\n", "\t    raw_ratings_data rrd\n", "\tJOIN\n", "\t    products_data pd ON pd.product_id = rrd.product_id\n", "    JOIN\n", "        order_details od ON rrd.order_id = od.order_id\n", "\tWHERE\n", "\t\trrd.product_id IN (SELECT product_id FROM products_data)\n", "\tGROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12\n", "),\n", "--SELECT * FROM order_details LIMIT 1\n", "karma_score AS (\n", "\tSELECT\n", "\t\tTRY_CAST(entity_id AS BIGINT) AS user_id,\n", "\t\tCASE LOWER(feature_label)\n", "\t\t\tWHEN 'gold'   THEN 10\n", "\t\t\tWHEN 'silver' THEN  9\n", "\t\t\tWHEN 'bronze' THEN  8\n", "\t\t\tWHEN 'new'    THEN  7\n", "\t\t\tWHEN 'iron'   THEN  6\n", "\t\t\tELSE 7\n", "\t\tEND AS karma_score\n", "\tFROM\n", "\t    feature_store.user_karma_v4_trino\n", "),\n", "base AS (\n", "\tSELECT\n", "\t\tr.user_id,\n", "\t\tr.order_id,\n", "\t\tr.product_id,\n", "\t\tr.product_name,\n", "\t\tr.rating,\n", "\t\tr.order_date,\n", "\t\tr.city_name,\n", "\t\tCOALESCE(ks.karma_score, 7) AS karma_score,\n", "\t\tr.l2_category,\n", "\t\tr.l0_category,\n", "\t\tr.brand,\n", "\t\tr.product_type\n", "\tFROM\n", "\t    rating_data r\n", "\tLEFT JOIN\n", "\t    karma_score ks ON ks.user_id = r.user_id\n", "\tWHERE\n", "\t\tr.user_id   IS NOT NULL\n", "\t\tAND r.order_id  IS NOT NULL\n", "\t\tAND r.product_id IS NOT NULL\n", "\t\tAND r.rating    IS NOT NULL\n", "),\n", "week_offsets AS (\n", "\tSELECT\n", "\t    seq AS week_offset\n", "\tFROM\n", "\t    UNNEST(sequence(0, 7)) AS t(seq)\n", "),\n", "--SELECT * FROM week_offsets\n", "week_ends AS (\n", "\tSELECT\n", "\t\tweek_offset,\n", "\t\tCAST(DATE_ADD('day', -7 * week_offset, DATE_ADD('day', 6, DATE_TRUNC('week', (SELECT yesterday FROM vars)))) AS DATE) AS week_end_date\n", "\tFROM\n", "\t    week_offsets\n", "),\n", "--SELECT * FROM week_ends\n", "weekly_data AS (\n", "\tSELECT\n", "\t\tb.product_id,\n", "\t\tb.product_name,\n", "\t\tb.l2_category,\n", "\t\tb.l0_category,\n", "\t\tb.product_type,\n", "\t\tb.city_name,\n", "        b.brand,\n", "\t\twe.week_offset,\n", "\t\twe.week_end_date,\n", "\t\tb.rating,\n", "\t\tb.karma_score,\n", "\t\tb.order_date,\n", "\t\tPOWER(0.99, DATE_DIFF('day', b.order_date, we.week_end_date)) AS recency_factor\n", "\tFROM\n", "\t    base b\n", "\tCROSS JOIN\n", "\t    week_ends we\n", "\tWHERE\n", "\t\tb.order_date <= we.week_end_date\n", "\t\tAND b.order_date >  we.week_end_date - INTERVAL '500' DAY\n", ")--,\n", "SELECT * FROM weekly_data\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "86663261-3145-430b-991e-cd99e13f68d7", "metadata": {}, "outputs": [], "source": ["ptype_query_template = \"\"\"\n", "WITH vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY           AS yesterday,\n", "\t\tcurrent_date - INTERVAL '562' DAY         AS global_start_date\n", "),\n", "products_data AS (\n", "    SELECT\n", "        ipm.product_id,\n", "        gp.name AS product_name,\n", "        pb.manufacturer_id,\n", "        pm.name AS manufacturer,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "            {filter_clause}\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "ptype_data AS (\n", "    SELECT\n", "        gp.id AS product_id,\n", "        gp.name AS product_name,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        cms.gr_product gp\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = gp.id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "    JOIN\n", "        products_data pd\n", "            ON pd.product_type = dp.product_type\n", "    WHERE\n", "        gp.enabled_flag\n", "        AND gp.lake_active_record\n", "),\n", "order_details AS (\n", "\tSELECT\n", "\t\tod.order_id                           AS order_id,\n", "\t\tCAST(od.insert_ds_ist AS date)    AS order_date,\n", "\t\tom.city_name\n", "\tFROM\n", "\t    oms_bifrost.oms_order_item od\n", "\tJOIN\n", "\t    ptype_data pd ON pd.product_id = od.product_id\n", "\tJOIN\n", "\t    oms_bifrost.oms_order ooi\n", "\t        ON ooi.id = od.order_id AND ooi.lake_active_record\n", "\t        AND ooi.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR) AND ooi.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "\tJOIN\n", "\t    oms_bifrost.oms_merchant om\n", "\t        ON om.id = ooi.merchant_id AND ooi.lake_active_record\n", "\t        AND om.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR) AND od.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "\tWHERE\n", "\t\tod.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR) AND od.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "    --    AND om.city_name IN ('Bengaluru', 'Chennai', 'Delhi', 'Faridabad', 'HR-NCR', 'Kolkata', 'Mumbai')\n", "        AND od.lake_active_record\n", "),\n", "raw_ratings_data AS (\n", "\tSELECT\n", "\t\tTRY_CAST(regexp_replace(partition_key, '^#u_id-', '') AS BIGINT)   AS user_id,\n", "\t\tTRY_CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT) AS order_id,\n", "\t\tTRY_CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER)      AS product_id,\n", "\t\tTRY_CAST(rating AS INTEGER)                                        AS rating,\n", "\t\tDATE(FROM_UNIXTIME(updated_at))                                AS updated_date\n", "\tFROM\n", "\t    zomato.dynamodb.blinkit_ratings_and_reviews\n", "\tWHERE\n", "\t\tupdated_at >= (SELECT to_unixtime(yesterday) FROM vars) - 562 * 86400\n", "\t\tAND updated_at <  (SELECT to_unixtime(yesterday) FROM vars)\n", "\t\tAND sort_key  LIKE '%%#p_id%%'\n", "\t\tAND partition_key LIKE '#u_id%%'\n", "\tGROUP BY 1, 2, 3, 4, 5\n", "),\n", "rating_data AS (\n", "\tSELECT\n", "\t\trrd.user_id,\n", "\t\trrd.order_id,\n", "\t\trrd.product_id,\n", "\t\tpd.product_name,\n", "\t\trrd.rating,\n", "\t\trrd.updated_date,\n", "\t\tpd.l2_category,\n", "\t\tpd.l0_category,\n", "\t\tpd.brand,\n", "\t\tpd.product_type,\n", "\t\tod.order_date,\n", "\t\tod.city_name\n", "\tFROM\n", "\t    raw_ratings_data rrd\n", "\tJOIN\n", "\t    ptype_data pd ON pd.product_id = rrd.product_id\n", "    JOIN\n", "        order_details od ON rrd.order_id = od.order_id\n", "\tWHERE\n", "\t\trrd.product_id IN (SELECT product_id FROM ptype_data)\n", "\tGROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12\n", "),\n", "--SELECT * FROM order_details LIMIT 1\n", "karma_score AS (\n", "\tSELECT\n", "\t\tTRY_CAST(entity_id AS BIGINT) AS user_id,\n", "\t\tCASE LOWER(feature_label)\n", "\t\t\tWHEN 'gold'   THEN 10\n", "\t\t\tWHEN 'silver' THEN  9\n", "\t\t\tWHEN 'bronze' THEN  8\n", "\t\t\tWHEN 'new'    THEN  7\n", "\t\t\tWHEN 'iron'   THEN  6\n", "\t\t\tELSE 7\n", "\t\tEND AS karma_score\n", "\tFROM\n", "\t    feature_store.user_karma_v4_trino\n", "),\n", "base AS (\n", "\tSELECT\n", "\t\tr.user_id,\n", "\t\tr.order_id,\n", "\t\tr.product_id,\n", "\t\tr.product_name,\n", "\t\tr.rating,\n", "\t\tr.order_date,\n", "\t\tr.city_name,\n", "\t\tCOALESCE(ks.karma_score, 7) AS karma_score,\n", "\t\tr.l2_category,\n", "\t\tr.l0_category,\n", "\t\tr.brand,\n", "\t\tr.product_type\n", "\tFROM\n", "\t    rating_data r\n", "\tLEFT JOIN\n", "\t    karma_score ks ON ks.user_id = r.user_id\n", "\tWHERE\n", "\t\tr.user_id   IS NOT NULL\n", "\t\tAND r.order_id  IS NOT NULL\n", "\t\tAND r.product_id IS NOT NULL\n", "\t\tAND r.rating    IS NOT NULL\n", "),\n", "week_offsets AS (\n", "\tSELECT\n", "\t    seq AS week_offset\n", "\tFROM\n", "\t    UNNEST(sequence(0, 7)) AS t(seq)\n", "),\n", "--SELECT * FROM week_offsets\n", "week_ends AS (\n", "\tSELECT\n", "\t\tweek_offset,\n", "\t\tCAST(DATE_ADD('day', -7 * week_offset, DATE_ADD('day', 6, DATE_TRUNC('week', (SELECT yesterday FROM vars)))) AS DATE) AS week_end_date\n", "\tFROM\n", "\t    week_offsets\n", "),\n", "--SELECT * FROM week_ends\n", "weekly_data AS (\n", "\tSELECT\n", "\t\tb.product_id,\n", "\t\tb.product_name,\n", "\t\tb.l2_category,\n", "\t\tb.l0_category,\n", "\t\tb.product_type,\n", "\t\tb.city_name,\n", "        b.brand,\n", "\t\twe.week_offset,\n", "\t\twe.week_end_date,\n", "\t\tb.rating,\n", "\t\tb.karma_score,\n", "\t\tb.order_date,\n", "\t\tPOWER(0.99, DATE_DIFF('day', b.order_date, we.week_end_date)) AS recency_factor\n", "\tFROM\n", "\t    base b\n", "\tCROSS JOIN\n", "\t    week_ends we\n", "\tWHERE\n", "\t\tb.order_date <= we.week_end_date\n", "\t\tAND b.order_date >  we.week_end_date - INTERVAL '500' DAY\n", ")--,\n", "SELECT\n", "    product_type,\n", "    week_offset,\n", "    week_end_date,\n", "    SUM(karma_score * recency_factor * rating) AS weighted_sum,\n", "    SUM(karma_score * recency_factor) AS weight_sum,\n", "    COUNT(rating) AS total_count\n", "FROM\n", "    weekly_data\n", "GROUP BY 1, 2, 3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c13e6a26-c3b1-4bf1-82a9-9ffeff4ed78f", "metadata": {}, "outputs": [], "source": ["reviews_template = \"\"\"\n", "WITH vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY           AS yesterday,\n", "\t\tcurrent_date - INTERVAL '125' DAY         AS global_start_date\n", "),\n", "products_data AS (\n", "    SELECT\n", "        ipm.product_id,\n", "        gp.name AS product_name,\n", "        pb.manufacturer_id,\n", "        pm.name AS manufacturer,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "            {filter_clause}\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "rating_data AS \n", "(\n", "    SELECT\n", "        CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT) as order_id,\n", "        CAST(regexp_replace(partition_key, '^#u_id-', '') AS BIGINT) as user_id, \n", "        CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER) as product_id,\n", "        pd.product_name,\n", "        rating,\n", "        feedback,\n", "        -- COALESCE(location,CAST(ROW(0, 0) AS ROW(Latitude REAL, Longitude REAL))) AS location,\n", "        COALESCE(device[2],'') as device,\n", "        rr.created_at,\n", "        rr.updated_at,\n", "        TRIM(fsoid.city_name) AS city,\n", "        pd.manufacturer_id\n", "    FROM \n", "        zomato.dynamodb.blinkit_ratings_and_reviews rr\n", "\tJOIN\n", "\t    products_data pd ON pd.product_id = CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER)\n", "\tJOIN\n", "\t    dwh.fact_sales_order_details fsoid ON fsoid.order_id = CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT)\n", "\t    AND order_create_dt_ist > (SELECT global_start_date FROM vars)\n", "    WHERE \n", "        sort_key like '%%#p_id-%%'\n", "        AND partition_key LIKE '#u_id%%'\n", "),\n", "--SELECT order_id FROM rating_data LIMIT 1\n", "city_state_map AS (\n", "    SELECT\n", "        t1.name AS city,\n", "        t2.name AS state,\n", "        CASE\n", "            WHEN t2.name IN ('Andhra Pradesh', 'Karnataka', 'Kerala', 'Tamil Nadu', 'Telangana', 'Pondicherry') THEN 'Southern'\n", "            WHEN t2.name IN ('Bihar', 'Jharkhand', 'Odisha', 'West Bengal', 'Orissa', 'Arunachal Pradesh', 'Assam', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Sikkim', 'Tripura', 'Chhattisgarh') THEN 'Eastern'\n", "            WHEN t2.name IN ('Goa', 'Gujarat', 'Maharashtra', 'Dadra and Nagar Haveli', 'Madhya Pradesh') THEN 'Western'\n", "            WHEN t2.name IN ('Haryana', 'Himachal Pradesh', 'Punjab', 'Rajasthan', 'Jammu and Kashmir', 'Delhi', 'New Delhi', 'Chandigarh', 'Uttar Pradesh', 'Uttarakhand') THEN 'Northern'\n", "            ELSE 'Others'\n", "        END AS zone\n", "    FROM\n", "        retail.console_location t1\n", "    LEFT JOIN\n", "        retail.console_state t2 ON t1.state_id = t2.id AND t2.lake_active_record\n", "    WHERE\n", "        t1.lake_active_record\n", "    GROUP BY 1, 2, 3\n", ")\n", "--SELECT DISTINCT zone FROM order_details-- WHERE zone IS NULL\n", "SELECT\n", "    rd.product_id,\n", "    rd.product_name,\n", "    CASE\n", "        WHEN rd.city IN ('B<PERSON><PERSON>', 'udaipur', 'Solan', 'DELHI','KHARAR', 'jammu', 'Sri Ganganagar' , 'CHANDIGARH', 'jaipur', 'mohali', 'NewChandigarh', 'Muzaffarnagar', 'ghaziabad') THEN 'Northern'\n", "        WHEN rd.city IN ('B<PERSON><PERSON>eshwar', 'BOKARO', 'KOLKATA', 'jamshedpur', 'raipur') THEN 'Eastern'\n", "        WHEN rd.city IN ('bengaluru', 'Tiruchirapalli') THEN 'Southern'\n", "        WHEN rd.city IN ('goa', 'mumbai', 'PUNE') THEN 'Western'\n", "        ELSE csm.zone\n", "    END AS zone,\n", "    rd.rating,\n", "    rd.feedback AS review,\n", "    format_datetime(from_unixtime(rd.created_at), 'MMMM, yyyy') AS month,\n", "    manufacturer_id\n", "--    rd.city\n", "FROM\n", "    rating_data rd\n", "LEFT JOIN\n", "    city_state_map csm ON csm.city = rd.city\n", "WHERE\n", "    rd.feedback IS NOT NULL\n", "    AND rd.feedback NOT IN ('', ' ')\n", "--    AND zone IS NULL\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7\n", "ORDER BY 1, 4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bb2c0d0a-36b6-4613-afb5-b7c2a7356768", "metadata": {}, "outputs": [], "source": ["def _esc(s: str) -> str:\n", "    # simple SQL-escape for single quotes\n", "    return s.replace(\"'\", \"''\")\n", "\n", "\n", "def make_manufacturer_clause(mfr_id: int) -> str:\n", "    return f\"AND pm.id = {mfr_id}\"\n", "\n", "\n", "def fetch_for_manufacturer(mfr_id: int, con):\n", "    clause = make_manufacturer_clause(mfr_id)\n", "    df_weekly = pd.read_sql_query(ratings_query_template.format(filter_clause=clause), con=con)\n", "    df_ptype = pd.read_sql_query(ptype_query_template.format(filter_clause=clause), con=con)\n", "    return df_weekly, df_ptype, clause\n", "\n", "\n", "# establish your Trino connection\n", "con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6d9e9a8-e071-44d9-ba5b-dc5589ee4f3e", "metadata": {}, "outputs": [], "source": ["def _esc(s: str) -> str:\n", "    return s.replace(\"'\", \"''\").lower()\n", "\n", "\n", "def make_manufacturers_clause(mfr_ids: list[int]) -> str:\n", "    if not mfr_ids:\n", "        return \"\"\n", "    id_list = \", \".join(str(mid) for mid in mfr_ids)\n", "    return f\"AND pm.id IN ({id_list})\"\n", "\n", "\n", "def sql_escape_percent(q: str) -> str:\n", "    return q.replace(\"%\", \"%%\")\n", "\n", "\n", "def fetch_reviews_for_manufacturers(mfr_list: list[str], con):\n", "    clause = make_manufacturers_clause(mfr_list)\n", "    sql = sql_escape_percent(reviews_template.format(filter_clause=clause))\n", "    df = pd.read_sql_query(sql, con=con)\n", "    return df, clause\n", "\n", "\n", "# establish your Trino connection\n", "con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd2d053a-f2a2-4d79-934f-87f7af3d1ae6", "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\n", "You are a customer review analyst for an online marketplace that hosts thousands of products across multiple categories.\n", "\n", "Customers leave reviews after receiving their orders. These reviews can be about:\n", "- the **product itself** (brand responsibility),\n", "- the **delivery experience** (platform responsibility),\n", "- or they may be vague or irrelevant.\n", "\n", "Your task is to:\n", "1. **Classify each customer review into a Theme and Sub-theme**\n", "2. **Identify the Tone** of the review as either Positive or Negative\n", "\n", "---\n", "\n", "### 1. THEME CLASSIFICATION\n", "\n", "Each review must be assigned exactly **one of the following 3 high-level themes**:\n", "- **Product related review** — issues with the product itself, including its quality, quantity, price, damage, leakage, seal condition, etc.\n", "- **Service related review** — issues related to delivery experience, rider behavior, timeliness, missing/wrong products, damaged delivery bag, etc.\n", "- **Random review** — vague, irrelevant, abusive, or cannot be categorized into product or service\n", "\n", "> 🔹 Use **“random review”** only if the review is vague, irrelevant, or contains no mention — even indirectly — of product or service quality.\n", "\n", "⚠️ Short reviews like “good,” “not chilled,” or “useless” are still valid **product related reviews** if they imply product feedback. Do NOT mark such reviews as random.  \n", "Random reviews are things like: “Thank you,” “Dear team,” “As,” or just “<PERSON>” — messages that give **no actionable feedback**.\n", "\n", "> ⚠️ Strictly enforce theme-sub-theme consistency. Sub-themes must only be assigned under their corresponding theme. For example:\n", "> - `product damage review` → only under **Product related review**\n", "> - `delivery review` → only under **Service related review**\n", "\n", "---\n", "\n", "### SUB-THEME GUIDELINES (with examples):\n", "\n", "#### 🔹 PRODUCT RELATED REVIEW\n", "- `quality review` — effectiveness, performance, taste, smell, durability, texture, etc.\n", "- `quantity / size review` — too little, too small, weight mismatch, etc.\n", "- `pricing review` — too costly, not worth it\n", "- `product damage review` — product was leaking, torn, unsealed, open bottle, broken container (includes brand packaging like pouches, bottles, jars, etc.)\n", "  > *e.g., \"Product was open and leaking\", \"Seal was broken\", \"Top was torn\", \"Cap missing\", \"Packet puffed\", \"Batter pack leaked\"*\n", "\n", "> ⚠️ **IMPORTANT**: All reviews mentioning broken seal, puffed pack, bottle open, torn bag, leaking product, etc. should go under `product damage review`.\n", "\n", "#### 🔹 SERVICE RELATED REVIEW\n", "- `delivery review` — late, fast, or delayed deliveries\n", "- `rider review` — rider behavior, politeness, calls\n", "- `expiry review` — expired or near-expiry product received\n", "- `wrong / missing product review` — incorrect item sent, something not delivered\n", "- `packaging hygiene review` — external box or product packaging was dusty, smelly, or looked unhygienic\n", "  > *e.g., \"Box smelled weird\", \"Dusty bottle\", \"Smelly pouch\"*\n", "\n", "⚠️ Avoid overly specific sub-themes like “cap broken” or “delivered 14 minutes late.”\n", "\n", "---\n", "\n", "### 2. TONE CLASSIFICATION\n", "\n", "Assign exactly one of the below:\n", "- **Positive tone** — expresses satisfaction, approval, or praise (even if subtle)\n", "- **Negative tone** — expresses dissatisfaction, complaint, or frustration (explicit or implied)\n", "\n", "Do **not** mark anything as neutral or mixed. Always pick one dominant tone.\n", "\n", "---\n", "\n", "Your classification should help brands and platform teams take targeted actions. Be accurate and avoid mixing up product and delivery-related issues.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f64d96b0-97c0-4b19-b813-61398b997238", "metadata": {}, "outputs": [], "source": ["USER_PROMPT_TEMPLATE = \"\"\"You will be provided with two input values:\n", "- `product_name` (string)\n", "- `review` (string)\n", "\n", "Your task is to classify the review into:\n", "1. **Exactly one of the 3 high-level themes**\n", "2. **A corresponding sub-theme that provides more granularity**  \n", "3. **The tone of the review** (Positive or Negative)\n", "\n", "---\n", "\n", "### THEMES & EXAMPLES:\n", "\n", "1. **Product related review** — about the product’s features, damage, performance, pricing, quantity, etc.\n", "   - “Batter pouch was puffed and leaked”\n", "   - “Cap was broken and juice spilled”\n", "   - “Bottle was not sealed properly”\n", "   - “Too harsh for my skin”\n", "   - “Not worth the price”\n", "   - “Size is too small”\n", "   - “Good”\n", "   - “Not chilled”\n", "\n", "2. **Service related review** — about delivery experience, rider, expiry, missing items, outer bag condition, hygiene\n", "   - “Received product 3 hours late”\n", "   - “Delivery boy was polite”\n", "   - “Item missing from my order”\n", "   - “Outer delivery bag was torn”\n", "   - “Box was dusty and smelled bad”\n", "\n", "3. **Random review** — vague or irrelevant\n", "   - “Received”\n", "   - <PERSON><PERSON>”\n", "   - “How are you”\n", "   - “Dear Team”\n", "   - “As”\n", "\n", "> ⚠️ Do not mark short or unclear product feedbacks (like “good,” “useless,” “not chilled”) as random. These still count as product-related.\n", "\n", "> ⚠️ **Sub-themes must strictly correspond to their assigned parent theme.** For example:\n", "> - **Do not** assign `\"delivery review\"` to a product issue like size or leakage.\n", "> - **Do not** assign `\"product damage review\"` to a delivery complaint or hygiene issue.\n", "> - **Do not** fabricate or assume sub-themes outside the allowed mapping shown below.\n", "\n", "> 🚫 Violating this **theme-subtheme mapping** will result in misclassification.\n", "\n", "---\n", "\n", "### TONE CLASSIFICATION\n", "\n", "- **Positive tone**: expresses satisfaction or praise\n", "- **Negative tone**: expresses dissatisfaction, damage, delays, issues\n", "\n", "---\n", "\n", "### OUTPUT FORMAT (Use exactly):\n", "\n", "Tone: \"<Positive tone or Negative tone>\"  \n", "Theme: \"<one of the 3 themes>\"  \n", "Sub-theme: \"<relevant sub-theme>\"  \n", "Explanation: \"<brief justification (at least 50–70 words)>\"\n", "\n", "---\n", "\n", "### THEME TO SUB-THEME MAPPING\n", "\n", "Only use a sub-theme **from the corresponding theme** shown below. Cross-assignment is not allowed.\n", "\n", "1. Product related review  \n", "    - 1.1 \"product damage review\"  \n", "    - 1.2 \"quality review\"  \n", "    - 1.3 \"quantity / size review\"  \n", "    - 1.4 \"pricing review\"  \n", "\n", "2. Service related review  \n", "    - 2.1 \"expiry review\"  \n", "    - 2.2 \"packaging hygiene review\"  \n", "    - 2.3 \"wrong / missing product review\"  \n", "    - 2.4 \"delivery review\"  \n", "    - 2.5 \"rider review\"    \n", "\n", "3. Random review  \n", "    - Any response that does not fit clearly into product or service themes\n", "\n", "---\n", "\n", "### CHAIN OF THOUGHT TO FOLLOW\n", "\n", "1. Analyze the review's core message and emotional tone.  \n", "2. Identify the correct theme and then select a sub-theme *strictly from the allowed list for that theme*.  \n", "3. Clearly justify your classification with at least 50–70 words, using logic and examples where needed.\n", "\n", "---\n", "\n", "### INPUT ###\n", "product_name: {product_name}  \n", "review: {review_text}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6acac181-cd41-43bf-9cb6-e90e2945092c", "metadata": {}, "outputs": [], "source": ["reviews_df, clause_used = fetch_reviews_for_manufacturers(manufacturers, con)\n", "\n", "reviews_df[\"mfr_slug\"] = reviews_df[\"manufacturer_id\"].astype(str)\n", "\n", "rev_df = reviews_df.reset_index(drop=True)\n", "\n", "rev_df"]}, {"cell_type": "code", "execution_count": null, "id": "0776ffed-862d-43fe-8d12-4963a79264bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56a1217c-145f-4cf0-af72-8af700925d60", "metadata": {}, "outputs": [], "source": ["def parse_raw_output(raw_output: str) -> dict:\n", "    tone = \"\"\n", "    theme = \"\"\n", "    sub_theme = \"\"\n", "    explanation = \"\"\n", "    for line in raw_output.splitlines():\n", "        if line.strip().lower().startswith(\"tone\"):\n", "            parts = line.split(\":\", 1)\n", "            tone = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"theme\"):\n", "            parts = line.split(\":\", 1)\n", "            theme = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"sub-theme\"):\n", "            parts = line.split(\":\", 1)\n", "            sub_theme = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"explanation\"):\n", "            parts = line.split(\":\", 1)\n", "            explanation = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "    return {\"tone\": tone, \"theme\": theme, \"sub_theme\": sub_theme, \"explanation\": explanation}"]}, {"cell_type": "code", "execution_count": null, "id": "bfecf39f-c357-4023-8964-392f6d774878", "metadata": {}, "outputs": [], "source": ["from datetime import timedelta\n", "from pathlib import Path\n", "import json, time\n", "import pencilbox as pb  # assumed you're using this for Slack\n", "\n", "inp_path = Path(\"batch_input_reviews.jsonl\")\n", "out_dir = Path(\".\")\n", "out_dir.mkdir(exist_ok=True)\n", "\n", "print(\"Building batch payload...\")\n", "batch_requests = []\n", "for idx, row in rev_df.iterrows():\n", "    cid = f\"req_{idx}_{row['mfr_slug']}\"\n", "    user_prompt = USER_PROMPT_TEMPLATE.format(\n", "        product_name=row[\"product_name\"], review_text=row[\"review\"]\n", "    )\n", "    batch_requests.append(\n", "        {\n", "            \"custom_id\": cid,\n", "            \"method\": \"POST\",\n", "            \"url\": \"/v1/chat/completions\",\n", "            \"body\": {\n", "                \"model\": \"gpt-4o-mini\",\n", "                \"messages\": [\n", "                    {\"role\": \"system\", \"content\": SYSTEM_PROMPT},\n", "                    {\"role\": \"user\", \"content\": user_prompt},\n", "                ],\n", "                \"temperature\": 0.3,\n", "                \"max_tokens\": 500,\n", "            },\n", "        }\n", "    )\n", "\n", "with inp_path.open(\"w\") as f:\n", "    for req in batch_requests:\n", "        f.write(json.dumps(req) + \"\\n\")\n", "\n", "print(\"Uploading file and creating batch job...\")\n", "batch_file = client.files.create(file=inp_path.open(\"rb\"), purpose=\"batch\")\n", "batch = client.batches.create(\n", "    input_file_id=batch_file.id,\n", "    endpoint=\"/v1/chat/completions\",\n", "    completion_window=\"24h\",\n", "    metadata={\"description\": \"Review classification batch (multi-mfr)\"},\n", ")\n", "batch_id = batch.id\n", "\n", "print(f\"Batch created: {batch_id}\")\n", "\n", "# Wait for completion\n", "max_wait_secs = 24 * 60 * 60\n", "poll_interval = 10 * 60\n", "start_ts = time.time()\n", "\n", "print(\"Polling for batch status...\")\n", "while True:\n", "    batch = client.batches.retrieve(batch_id)\n", "    status = batch.status\n", "    rc = batch.request_counts or None\n", "    done = rc.completed if rc else 0\n", "    total = rc.total if rc else 0\n", "    failed = rc.failed if rc else 0\n", "    elapsed = timedelta(seconds=int(time.time() - start_ts))\n", "\n", "    print(f\"[{elapsed}] Batch status: {status} | Completed: {done}/{total} | Failed: {failed}\")\n", "\n", "    # Send Slack update\n", "    path = str(inp_path)  # Or any appropriate file you want to attach\n", "    message = (\n", "        f\"<!channel> GPT batch is running, status: {done}/{total} complete, \"\n", "        f\"time elapsed: {elapsed}, status: {status}\"\n", "    )\n", "    pb.send_slack_message(channel=\"testing_alerts\", text=message)\n", "\n", "    if status in (\"completed\", \"failed\", \"expired\"):\n", "        break\n", "    if time.time() - start_ts > max_wait_secs:\n", "        raise TimeoutError(f\"Waited {elapsed} but batch is still {status}.\")\n", "    time.sleep(poll_interval)\n", "\n", "if status != \"completed\":\n", "    raise RuntimeError(f\"Batch ended with status {status}\")\n", "print(\"<PERSON><PERSON> completed ✅\")\n", "\n", "# Parse results\n", "print(\"Downloading and parsing results...\")\n", "output_file_id = client.batches.retrieve(batch_id).output_file_id\n", "raw_lines = client.files.content(output_file_id).text.strip().splitlines()\n", "results = [json.loads(line) for line in raw_lines]\n", "\n", "print(\"Mapping results back to dataframe...\")\n", "tone_map, theme_map, subtheme_map, expl_map = {}, {}, {}, {}\n", "for res in results:\n", "    cid = res[\"custom_id\"]\n", "    content = res[\"response\"][\"body\"][\"choices\"][0][\"message\"][\"content\"]\n", "    out = parse_raw_output(content)\n", "    tone_map[cid] = out.get(\"tone\", \"ERROR\")\n", "    theme_map[cid] = out.get(\"theme\", \"ERROR\")\n", "    subtheme_map[cid] = out.get(\"sub_theme\", \"ERROR\")\n", "    expl_map[cid] = out.get(\"explanation\", \"\")\n", "\n", "# Back-fill in rev_df\n", "rev_df[\"tone\"] = [\n", "    tone_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"theme\"] = [\n", "    theme_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"sub_theme\"] = [\n", "    subtheme_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"explanation\"] = [\n", "    expl_map.get(f\"req_{i}_{s}\", \"\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "\n", "\n", "subtheme_to_theme = {\n", "    # Product Related Reviews\n", "    \"product damage review\": \"Product related review\",\n", "    \"quality review\": \"Product related review\",\n", "    \"quantity / size review\": \"Product related review\",\n", "    \"pricing review\": \"Product related review\",\n", "    # Service Related Reviews\n", "    \"expiry review\": \"Service related review\",\n", "    \"packaging hygiene review\": \"Service related review\",\n", "    \"wrong / missing product review\": \"Service related review\",\n", "    \"delivery review\": \"Service related review\",\n", "    \"rider review\": \"Service related review\",\n", "    # Random Reviews\n", "    \"random review\": \"Random review\",\n", "    \"Random review\": \"Random review\",\n", "    \"N/A\": \"Random review\",\n", "}\n", "\n", "rev_df[\"theme\"] = rev_df[\"sub_theme\"].map(subtheme_to_theme)\n", "\n", "print(\"Saving output CSVs by manufacturer slug...\")\n", "for slug, sub in rev_df.groupby(\"mfr_slug\"):\n", "    sub.to_csv(out_dir / f\"{slug}_reviews_classified.csv\", index=False)\n", "\n", "print(\"Done.\")"]}, {"cell_type": "code", "execution_count": null, "id": "aa4be004-b599-4255-b74b-1a65ef742a84", "metadata": {}, "outputs": [], "source": ["rev_df_to_load = rev_df[\n", "    [\n", "        \"product_id\",\n", "        \"zone\",\n", "        \"rating\",\n", "        \"review\",\n", "        \"month\",\n", "        \"theme\",\n", "        \"sub_theme\",\n", "        \"explanation\",\n", "        \"mfr_slug\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d97bc5d7-0f7d-44dc-a162-3a783eb53a81", "metadata": {}, "outputs": [], "source": ["rev_df_to_load"]}, {"cell_type": "code", "execution_count": null, "id": "4fc20c9f-082e-4164-9994-c300bf7cccd3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"reviews_data_post_model_classification_vtest\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Unique identifier for the product\",\n", "        },\n", "        {\n", "            \"name\": \"zone\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Geographical zone of the review\",\n", "        },\n", "        {\n", "            \"name\": \"rating\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Numeric rating given by the customer\",\n", "        },\n", "        {\n", "            \"name\": \"review\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Full text of the customer review\",\n", "        },\n", "        {\n", "            \"name\": \"month\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Month and year when the review was submitted (e.g. 'July, 2025')\",\n", "        },\n", "        {\n", "            \"name\": \"theme\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"High‑level theme assigned to the review\",\n", "        },\n", "        {\n", "            \"name\": \"sub_theme\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"More granular sub‑theme classification\",\n", "        },\n", "        {\n", "            \"name\": \"explanation\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Model’s explanation for the chosen sub_theme\",\n", "        },\n", "        {\n", "            \"name\": \"mfr_slug\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"manufacturer id\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"product_id\", \"month\", \"review\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": (\n", "        \"Raw customer reviews with theme/sub‑theme classifications and model explanations\"\n", "    ),\n", "}\n", "\n", "pb.to_trino(data_obj=rev_df_to_load, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "358b37be-be2b-4088-9d63-5dad25ef9b4c", "metadata": {}, "outputs": [], "source": ["def build_summary_and_lists(writer, pid_df):\n", "    from xlsxwriter.utility import xl_rowcol_to_cell, xl_col_to_name\n", "\n", "    wb = writer.book\n", "    ws_l = wb.add_worksheet(\"Lists\")\n", "    ws_s = wb.add_worksheet(\"Summary\")\n", "    pid_sheet = \"PID_Data\"\n", "\n", "    ws_s.hide_gridlines(2)\n", "\n", "    def xl(r, c):\n", "        return xl_rowcol_to_cell(r, c, row_abs=False, col_abs=False)\n", "\n", "    n_rows, _ = pid_df.shape\n", "    header = list(pid_df.columns)\n", "\n", "    def col_rng(colname):\n", "        idx = header.index(colname)\n", "        colL = xl_col_to_name(idx)\n", "        return f\"'{pid_sheet}'!${colL}$2:${colL}${n_rows+1}\"\n", "\n", "    R_L0 = col_rng(\"l0_category\")\n", "    R_L2 = col_rng(\"l2_category\")\n", "    R_BR = col_rng(\"brand\")\n", "    R_WO = col_rng(\"week_offset\")\n", "\n", "    # ---------- Lists sheet ----------\n", "    ws_l.write(0, 0, \"L0_list\")\n", "    ws_l.write(0, 1, \"L2_list\")\n", "    ws_l.write(0, 2, \"Brand_list\")\n", "\n", "    ws_l.write_formula(1, 0, f\"=UNIQUE(FILTER({R_L0}, {R_WO}=0))\")\n", "    ws_l.write_formula(\n", "        1, 1, f'=IFERROR(UNIQUE(FILTER({R_L2}, ({R_L0}=Summary!$A$4)*({R_WO}=0))),\"\")'\n", "    )\n", "    ws_l.write_formula(\n", "        1,\n", "        2,\n", "        f'=IFERROR(UNIQUE(FILTER({R_BR}, ({R_L0}=Summary!$A$4)*({R_WO}=0)*IF(Summary!$B$4=\"\",1,({R_L2}=Summary!$B$4)))),\"\")',\n", "    )\n", "\n", "    wb.define_name(\"L0_rng\", \"=Lists!$A$2:INDEX(Lists!$A:$A,COUNTA(Lists!$A:$A))\")\n", "    wb.define_name(\"L2_rng\", \"=Lists!$B$2:INDEX(Lists!$B:$B,COUNTA(Lists!$B:$B))\")\n", "    wb.define_name(\"Brand_rng\", \"=Lists!$C$2:INDEX(Lists!$C:$C,COUNTA(Lists!$C:$C))\")\n", "\n", "    ws_l.hide()\n", "\n", "    # ---------- Formats ----------\n", "    wrap = wb.add_format({\"text_wrap\": True, \"valign\": \"top\", \"border\": 1})\n", "    boldw = wb.add_format({\"text_wrap\": True, \"valign\": \"top\", \"bold\": True, \"border\": 1})\n", "    italic_wrap = wb.add_format({\"text_wrap\": True, \"valign\": \"top\", \"italic\": True, \"border\": 1})\n", "\n", "    yel = wb.add_format(\n", "        {\"bold\": True, \"bg_color\": \"#FFE600\", \"text_wrap\": True, \"align\": \"center\", \"border\": 1}\n", "    )\n", "    teal = wb.add_format(\n", "        {\n", "            \"bold\": True,\n", "            \"bg_color\": \"#47704C\",\n", "            \"font_color\": \"#FFFFFF\",\n", "            \"text_wrap\": True,\n", "            \"align\": \"center\",\n", "            \"border\": 1,\n", "        }\n", "    )\n", "    peach = wb.add_format(\n", "        {\"bold\": True, \"bg_color\": \"#FAD7A0\", \"text_wrap\": True, \"align\": \"center\", \"border\": 1}\n", "    )\n", "\n", "    filter_fmt = wb.add_format({\"bg_color\": \"#D0E3FF\", \"border\": 2})\n", "\n", "    left_fmt = wb.add_format({\"align\": \"left\", \"border\": 1})\n", "    center_fmt = wb.add_format({\"align\": \"center\", \"border\": 1})\n", "    gray_bg = wb.add_format({\"bg_color\": \"#F9F9F9\", \"border\": 1})\n", "\n", "    # Row indices\n", "    title_row = 0\n", "    desc_row = 1\n", "    label_row = 2\n", "    filter_row = 3\n", "    group_header = 4\n", "    col_header_row = 5\n", "    data_start = 6  # corresponds to Excel row 7\n", "\n", "    # 1) Filter Titles\n", "    ws_s.write(title_row, 0, \"Mandatory Filter\", boldw)\n", "    ws_s.write(title_row, 1, \"Optional Filter\", boldw)\n", "    ws_s.write(title_row, 2, \"Optional Filter\", boldw)\n", "\n", "    # 2) Filter Descriptions\n", "    ws_s.write(desc_row, 0, \"No data without this\", italic_wrap)\n", "    ws_s.write(desc_row, 1, \"Put blank in filter to see for all L2s\", italic_wrap)\n", "    ws_s.write(desc_row, 2, \"Put blank in filter to see for all Brands\", italic_wrap)\n", "    ws_s.write(\n", "        0,\n", "        11,\n", "        \"Performance band the product falls into compared to others of the same product type\",\n", "        italic_wrap,\n", "    )\n", "\n", "    # 3) Filter Labels\n", "    ws_s.write(label_row, 0, \"L0 (Choose below) ↓\", italic_wrap)\n", "    ws_s.write(label_row, 1, \"L2 (Choose below) ↓\", italic_wrap)\n", "    ws_s.write(label_row, 2, \"Brand (Choose below) ↓\", italic_wrap)\n", "\n", "    # 4) Filter Inputs (Blue Cell)\n", "    for i in range(3):\n", "        ws_s.write_blank(filter_row, i, None, filter_fmt)\n", "\n", "    ws_s.data_validation(filter_row, 0, filter_row, 0, {\"validate\": \"list\", \"source\": \"=L0_rng\"})\n", "    ws_s.data_validation(filter_row, 1, filter_row, 1, {\"validate\": \"list\", \"source\": \"=L2_rng\"})\n", "    ws_s.data_validation(filter_row, 2, filter_row, 2, {\"validate\": \"list\", \"source\": \"=Brand_rng\"})\n", "\n", "    # 5) Merged Group Headers\n", "    ws_s.merge_range(group_header, 3, group_header, 5, \"PAN India\", yel)\n", "    ws_s.merge_range(group_header, 6, group_header, 10, \"Count of ratings\", teal)\n", "\n", "    # 6) Column Headers\n", "    headers = [\n", "        \"Product ID\",\n", "        \"Product Name\",\n", "        \"Product Type\",\n", "        \"Product Ratings Count\",\n", "        \"Product Average Rating\",\n", "        \"Product Type Average Rating\",\n", "        \"★1\",\n", "        \"★2\",\n", "        \"★3\",\n", "        \"★4\",\n", "        \"★5\",\n", "        \"Percentile Bucket in Product Type\",\n", "        \"Difference from Product Type Average\",\n", "    ]\n", "    for c, h in enumerate(headers):\n", "        fmt = left_fmt if c < 3 else yel if 3 <= c < 6 else teal if 6 <= c < 11 else peach\n", "        ws_s.write(col_header_row, c, h, fmt)\n", "\n", "    # 7) Write filter formulas to row 7 only\n", "    out_order = [\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"product_type\",\n", "        \"rating_count\",\n", "        \"pan_pid_rating\",\n", "        \"pan_pytype_rating\",\n", "        \"count_rating_1\",\n", "        \"count_rating_2\",\n", "        \"count_rating_3\",\n", "        \"count_rating_4\",\n", "        \"count_rating_5\",\n", "        \"performance_against_ptype\",\n", "        \"diff_from_ptype_avg\",\n", "    ]\n", "    out_rngs = [col_rng(c) for c in out_order]\n", "    mask = (\n", "        f\"(({R_L0}=Summary!$A$4))\"\n", "        f'*IF(Summary!$B$4=\"\",1,({R_L2}=Summary!$B$4))'\n", "        f'*IF(Summary!$C$4=\"\",1,({R_BR}=Summary!$C$4))'\n", "        f\"*({R_WO}=0)\"\n", "    )\n", "    for ci, rng in enumerate(out_rngs):\n", "        fmt = left_fmt if ci < 3 else center_fmt\n", "        formula = f'=IFERROR(FILTER({rng},{mask}),\"\")'\n", "        ws_s.write_formula(data_start, ci, formula, fmt)\n", "\n", "    # 8) Format A8:M5000 (skip A7) to preserve formula row\n", "    for row in range(data_start + 1, 5000):\n", "        for col in range(len(headers)):\n", "            fmt = left_fmt if col < 3 else center_fmt\n", "            ws_s.write_blank(row, col, None, fmt)\n", "\n", "    # 9) 3-color conditional format on last column (M)\n", "    last_col = len(headers) - 1\n", "    ws_s.conditional_format(\n", "        data_start,\n", "        last_col,\n", "        5000,\n", "        last_col,\n", "        {\n", "            \"type\": \"3_color_scale\",\n", "            \"min_color\": \"#E2D0CF\",  # soft red\n", "            \"mid_color\": \"#E2E2CF\",  # soft yellow\n", "            \"max_color\": \"#CFE2D7\",  # soft green\n", "        },\n", "    )\n", "\n", "    # 10) Freeze at D7 (A–C + top 6 rows)\n", "    ws_s.freeze_panes(data_start, 3)\n", "\n", "    # 11) Set column widths\n", "    for i, h in enumerate(headers):\n", "        ws_s.set_column(i, i, max(16, len(h) + 2))"]}, {"cell_type": "code", "execution_count": null, "id": "f88567d2-a981-445f-b9b8-34583ab4bf39", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"email_dispatch_log_for_excel_reports_vtest\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"manufacturer_string\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"id of the manufacturer this report is for.\",\n", "        },\n", "        {\n", "            \"name\": \"excel_file_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Name of the Excel file sent in the email.\",\n", "        },\n", "        {\n", "            \"name\": \"email_sent_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Timestamp when the email was sent.\",\n", "        },\n", "        {\n", "            \"name\": \"email_addresses\",\n", "            \"type\": \"ARRAY(VARCHAR)\",\n", "            \"description\": \"List of email addresses the report was sent to.\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"manufacturer_string\", \"excel_file_name\", \"email_sent_date\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Log of email dispatches containing Excel reports per manufacturer, with recipient details.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d4e881ec-cc02-40bd-982e-fa0ecde4c9d8", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import re\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn\n", "\n", "\n", "# ---------- misc ----------\n", "def slugify(text: str) -> str:\n", "    return re.sub(r\"[^A-Za-z0-9]+\", \"_\", text).strip(\"_\").lower()\n", "\n", "\n", "output_dir = Path(\"/tmp/reports\")\n", "reviews_dir = Path(\".\")  # where you saved the per‑mfr review CSVs\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "steps = [\n", "    \"SQL fetch\",\n", "    \"Weights & groupby\",\n", "    \"Quartiles\",\n", "    \"Prev-week merge\",\n", "    \"Post metrics\",\n", "    \"Final trim/filter\",\n", "    \"Excel write (+reviews)\",\n", "    \"Email\",\n", "]\n", "\n", "with Progress(\n", "    SpinnerColumn(),\n", "    BarColumn(bar_width=None),\n", "    TextColumn(\"[bold blue]{task.description}\"),\n", "    TimeElapsedColumn(),\n", "    transient=False,\n", ") as progress:\n", "\n", "    outer = progress.add_task(\"[green]Manufacturers\", total=len(manufacturers))\n", "\n", "    for mfr in manufacturers:\n", "        inner = progress.add_task(f\"[cyan]{mfr}\", total=len(steps))\n", "        mfr_slug = str(mfr)  # use manufacturer_id directly as slug\n", "\n", "        # 1) SQL fetch\n", "        ratings_india_df, ptype_agg, clause = fetch_for_manufacturer(mfr, con)\n", "        progress.advance(inner)\n", "\n", "        # 2) Weights & groupby\n", "        ratings_india_df[\"weighted_rating\"] = (\n", "            ratings_india_df[\"karma_score\"]\n", "            * ratings_india_df[\"recency_factor\"]\n", "            * ratings_india_df[\"rating\"]\n", "        )\n", "        ratings_india_df[\"weight\"] = (\n", "            ratings_india_df[\"karma_score\"] * ratings_india_df[\"recency_factor\"]\n", "        )\n", "\n", "        group_cols = [\n", "            \"product_id\",\n", "            \"product_name\",\n", "            \"l2_category\",\n", "            \"l0_category\",\n", "            \"brand\",\n", "            \"product_type\",\n", "            \"week_offset\",\n", "            \"week_end_date\",\n", "        ]\n", "        weekly_agg = (\n", "            ratings_india_df.groupby(group_cols, dropna=False)\n", "            .agg(\n", "                weighted_sum=(\"weighted_rating\", \"sum\"),\n", "                weight_sum=(\"weight\", \"sum\"),\n", "                rating_count=(\"rating\", \"size\"),\n", "                count_rating_1=(\"rating\", lambda x: (x == 1).sum()),\n", "                count_rating_2=(\"rating\", lambda x: (x == 2).sum()),\n", "                count_rating_3=(\"rating\", lambda x: (x == 3).sum()),\n", "                count_rating_4=(\"rating\", lambda x: (x == 4).sum()),\n", "                count_rating_5=(\"rating\", lambda x: (x == 5).sum()),\n", "            )\n", "            .reset_index()\n", "        )\n", "        weekly_agg[\"pan_pid_rating\"] = (\n", "            weekly_agg[\"weighted_sum\"] / weekly_agg[\"weight_sum\"]\n", "        ).round(2)\n", "        weekly_agg[\"pan_pid_int_rating\"] = np.floor(weekly_agg[\"pan_pid_rating\"]).astype(int)\n", "        weekly_agg[\"pan_pid_half_rating\"] = np.floor(weekly_agg[\"pan_pid_rating\"] * 2) / 2.0\n", "        progress.advance(inner)\n", "\n", "        # 3) Quartiles\n", "        def assign_quartile_bucket(subdf):\n", "            sub = subdf.sort_values(\"pan_pid_rating\", ascending=False).copy()\n", "            n = len(sub)\n", "            ranks = np.arange(n)\n", "            buckets = np.floor(ranks * 4 / n).astype(int) + 1\n", "            sub[\"quartile_bucket\"] = np.clip(buckets, 1, 4)\n", "            return sub[[\"product_id\", \"week_offset\", \"quartile_bucket\"]]\n", "\n", "        pid_buckets = (\n", "            weekly_agg.groupby([\"product_type\", \"week_offset\"], group_keys=False)\n", "            .apply(assign_quartile_bucket)\n", "            .reset_index(drop=True)\n", "        )\n", "        progress.advance(inner)\n", "\n", "        # 4) Prev-week merge\n", "        f2 = weekly_agg.rename(\n", "            columns={\n", "                \"week_offset\": \"prev_week_offset\",\n", "                \"pan_pid_rating\": \"prev_pan_pid_rating\",\n", "                \"pan_pid_int_rating\": \"prev_pan_pid_int_rating\",\n", "                \"pan_pid_half_rating\": \"prev_pan_pid_half_rating\",\n", "                \"rating_count\": \"prev_rating_count\",\n", "            }\n", "        )\n", "        f2[\"week_offset\"] = f2[\"prev_week_offset\"] - 1\n", "        f2_keep = [\n", "            \"product_id\",\n", "            \"week_offset\",\n", "            \"prev_pan_pid_rating\",\n", "            \"prev_pan_pid_int_rating\",\n", "            \"prev_pan_pid_half_rating\",\n", "            \"prev_rating_count\",\n", "        ]\n", "        f2 = f2[f2_keep]\n", "\n", "        ptype_df = ptype_agg.assign(\n", "            pan_pytype_rating=(ptype_agg[\"weighted_sum\"] / ptype_agg[\"weight_sum\"]).round(2)\n", "        )[[\"product_type\", \"week_offset\", \"pan_pytype_rating\"]].drop_duplicates()\n", "\n", "        base_final = (\n", "            weekly_agg.merge(\n", "                ptype_df, on=[\"product_type\", \"week_offset\"], how=\"left\", validate=\"m:1\"\n", "            )\n", "            .merge(pid_buckets, on=[\"product_id\", \"week_offset\"], how=\"left\", validate=\"m:1\")\n", "            .merge(f2, on=[\"product_id\", \"week_offset\"], how=\"left\", validate=\"m:1\")\n", "        )\n", "        progress.advance(inner)\n", "\n", "        # 5) Post metrics\n", "        base_final[\"pan_int_change\"] = (\n", "            base_final[\"pan_pid_int_rating\"] - base_final[\"prev_pan_pid_int_rating\"]\n", "        )\n", "        base_final[\"pan_half_int_change\"] = (\n", "            base_final[\"pan_pid_half_rating\"] - base_final[\"prev_pan_pid_half_rating\"]\n", "        )\n", "        base_final[\"pan_rating_pct_change\"] = np.where(\n", "            base_final[\"prev_pan_pid_rating\"].ne(0),\n", "            (base_final[\"pan_pid_rating\"] - base_final[\"prev_pan_pid_rating\"])\n", "            * 100.0\n", "            / base_final[\"prev_pan_pid_rating\"],\n", "            np.nan,\n", "        )\n", "        base_final[\"rating_count_change\"] = (\n", "            base_final[\"rating_count\"] - base_final[\"prev_rating_count\"]\n", "        )\n", "\n", "        labels = {1: \"75 to 100%ile\", 2: \"50 to 75%ile\", 3: \"25 to 50%ile\", 4: \"0 to 25%ile\"}\n", "        base_final[\"performance_against_ptype\"] = base_final[\"quartile_bucket\"].map(labels)\n", "\n", "        nz = (base_final[\"pan_int_change\"] != 0) & (base_final[\"rating_count_change\"] != 0)\n", "        nzh = (base_final[\"pan_half_int_change\"] != 0) & (base_final[\"rating_count_change\"] != 0)\n", "        base_final[\"integral_change_flag\"] = np.where(\n", "            nz, base_final[\"pan_rating_pct_change\"], np.nan\n", "        )\n", "        base_final[\"half_integral_change_flag\"] = np.where(\n", "            nzh, base_final[\"pan_rating_pct_change\"], np.nan\n", "        )\n", "        progress.advance(inner)\n", "\n", "        # 6) Final trim/filter\n", "        cols_keep = [\n", "            \"product_id\",\n", "            \"product_name\",\n", "            \"l2_category\",\n", "            \"l0_category\",\n", "            \"brand\",\n", "            \"product_type\",\n", "            \"week_offset\",\n", "            \"week_end_date\",\n", "            \"rating_count\",\n", "            \"count_rating_1\",\n", "            \"count_rating_2\",\n", "            \"count_rating_3\",\n", "            \"count_rating_4\",\n", "            \"count_rating_5\",\n", "            \"pan_pid_rating\",\n", "            \"pan_pytype_rating\",\n", "            \"performance_against_ptype\",\n", "            \"pan_rating_pct_change\",\n", "            \"rating_count_change\",\n", "            \"integral_change_flag\",\n", "            \"half_integral_change_flag\",\n", "        ]\n", "        clean_pid = (\n", "            base_final.assign(\n", "                diff_from_ptype_avg=lambda d: (d[\"pan_pid_rating\"] - d[\"pan_pytype_rating\"]).round(\n", "                    2\n", "                )\n", "            )[cols_keep + [\"diff_from_ptype_avg\"]]\n", "            .sort_values([\"product_id\", \"week_offset\"])\n", "            .reset_index(drop=True)\n", "        )\n", "\n", "        low_pids = clean_pid.loc[\n", "            (clean_pid[\"week_offset\"] == 0) & (clean_pid[\"rating_count\"] <= 20), \"product_id\"\n", "        ].unique()\n", "        filtered_pid = clean_pid[~clean_pid[\"product_id\"].isin(low_pids)].reset_index(drop=True)\n", "        progress.advance(inner)\n", "\n", "        # 7) Excel write (+ optional Reviews tab)\n", "        fname = f\"{mfr_slug}_ratings_report.xlsx\"\n", "        report_path = output_dir / fname\n", "\n", "        # path to reviews csv for this manufacturer\n", "        reviews_csv = reviews_dir / f\"{mfr_slug}_reviews_classified.csv\"\n", "\n", "        with pd.ExcelWriter(report_path, engine=\"xlsxwriter\") as writer:\n", "            # PID sheet\n", "            filtered_pid.to_excel(writer, sheet_name=\"PID_Data\", index=False)\n", "            ws = writer.sheets[\"PID_Data\"]\n", "            for col_idx, col in enumerate(filtered_pid.columns):\n", "                max_len = max(filtered_pid[col].astype(str).map(len).max(), len(col))\n", "                ws.set_column(col_idx, col_idx, max_len + 2)\n", "\n", "            ws.hide()\n", "\n", "            # Summary/Lists\n", "            build_summary_and_lists(writer, filtered_pid)\n", "\n", "            # Reviews sheet if present\n", "            if reviews_csv.exists():\n", "                reviews_df_pre = pd.read_csv(reviews_csv)\n", "                reviews_df_pre = reviews_df_pre.drop(\n", "                    columns=[\n", "                        \"mfr_slug\",\n", "                        \"explanation\",\n", "                    ]\n", "                )\n", "                reviews_df_mfr = reviews_df_pre[\n", "                    reviews_df_pre[\"theme\"] == \"Product related review\"\n", "                ].copy()\n", "\n", "                reviews_df_mfr[\"theme\"] = (\n", "                    reviews_df_mfr[\"theme\"]\n", "                    .str.replace(\"review\", \"\", case=False, regex=False)\n", "                    .str.strip()\n", "                )\n", "\n", "                reviews_df_mfr[\"sub_theme\"] = (\n", "                    reviews_df_mfr[\"sub_theme\"]\n", "                    .str.replace(\"review\", \"\", case=False, regex=False)\n", "                    .str.strip()\n", "                )\n", "\n", "                reviews_df_mfr = reviews_df_mfr.drop(columns=[\"theme\"])\n", "\n", "                reviews_df_mfr.to_excel(writer, sheet_name=\"Reviews_Data\", index=False)\n", "                ws2 = writer.sheets[\"Reviews_Data\"]\n", "\n", "                wrap_format = writer.book.add_format({\"text_wrap\": True, \"valign\": \"top\"})\n", "                for col_idx, col in enumerate(reviews_df_mfr.columns):\n", "                    if col_idx == 4:\n", "                        ws2.set_column(col_idx, col_idx, 90, wrap_format)\n", "\n", "                    else:\n", "                        max_line_len = max(\n", "                            reviews_df_mfr[col]\n", "                            .astype(str)\n", "                            .map(lambda v: max(len(line) for line in v.split(\"\\n\")))\n", "                            .max(),\n", "                            len(col),\n", "                        )\n", "                        # Set column width based on that\n", "                        ws2.set_column(col_idx, col_idx, max_line_len + 2, wrap_format)\n", "\n", "                n_rows = len(reviews_df_mfr)\n", "\n", "                for row_idx in range(1, n_rows + 1):\n", "                    ws2.set_row(row_idx, None, wrap_format)\n", "\n", "        progress.advance(inner)\n", "\n", "        from_email = \"<EMAIL>\"\n", "\n", "        to_email = [\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "\n", "        mfg_df[\"mfg_id\"] = mfg_df[\"mfg_id\"].astype(int)\n", "\n", "        mfr_int = int(mfr)\n", "\n", "        mfg_name = mfg_df.loc[mfg_df[\"mfg_id\"] == mfr_int, \"mfg_name\"].iloc[0]\n", "\n", "        subject = f\"Ratings Report for {mfg_name}\"\n", "        html_content = (\n", "            f\"<p>Hi,<br>\"\n", "            f\"The ratings report for manufacturer <strong>{mfg_name}</strong> is attached.\"\n", "            \"</p>\"\n", "        )\n", "\n", "        pb.send_email(\n", "            from_email=from_email,\n", "            to_email=to_email,\n", "            subject=subject,\n", "            html_content=html_content,\n", "            files=[str(report_path)],\n", "        )\n", "\n", "        log_df = pd.DataFrame(\n", "            [\n", "                {\n", "                    \"manufacturer_string\": str(mfr),\n", "                    \"excel_file_name\": fname,\n", "                    \"email_sent_date\": pd.Timestamp.now(),\n", "                    \"email_addresses\": to_email,\n", "                }\n", "            ]\n", "        )\n", "\n", "        pb.to_trino(data_obj=log_df, **kwargs)\n", "\n", "        # Delete all large DataFrames and intermediates before next iteration\n", "        del (\n", "            ratings_india_df,\n", "            ptype_agg,\n", "            weekly_agg,\n", "            pid_buckets,\n", "            f2,\n", "            ptype_df,\n", "            base_final,\n", "            clean_pid,\n", "            filtered_pid,\n", "        )\n", "\n", "        if reviews_csv.exists():\n", "            del reviews_df_pre, reviews_df_mfr\n", "\n", "        # Force a garbage-collection pass\n", "        gc.collect()\n", "\n", "        print(f\"Memory cleaned and DataFrames deleted for '{mfr}'. Moving on to next manufacturer.\")\n", "\n", "        progress.advance(inner)\n", "        progress.update(outer, advance=1)\n", "        print(f\"→ Completed processing for Manufacturer {mfr}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}