
# -*- coding: utf-8 -*-
import sys, os
import logging
import random
import requests
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable, DagBag
from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s
from airflow.utils import timezone
import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))

def get_forecasts_inputs():
    url = "https://capacity-planner-preprod-data.prod-sgp-k8s.grofer.io/api/v1/blinkit/storeops/3/forecasts/20/inputs"
    data = pb.get_secret("data/services/planner-platform/preprod/backend/data_loader")
    ACCESS_TOKEN = data['access_token']
    REFRESH_TOKEN = data['refresh_token']

    headers = {
        "accept": "application/json",
        "Cookie": f"refresh_token={REFRESH_TOKEN}; access_token={ACCESS_TOKEN};"
    }

    today = datetime.today() + timedelta(days = 7)
    print(f"today's date:{today}")

    days_since_monday = today.weekday()
    start_date = today - timedelta(days=days_since_monday)

    end_date = start_date + timedelta(weeks=4)

    city_responses = []

    current_date = start_date
    while current_date < end_date:
        formatted_date = current_date.strftime("%Y-%m-%d 00:00:00")

        params = {
            "start_time": formatted_date,
            "time_grain": "weekly",
            "multi_aggregation_grain": "entity",
        }

        response = requests.get(url, params=params, headers=headers)

        print(f"Requesting week starting {formatted_date}")
        print("Status Code:", response.status_code)

        try:
            data = response.json()
            city_responses.append(data)
        except ValueError:
            print("Response is not valid JSON:", response.text)

        current_date += timedelta(weeks=1)

    print(f"\nTotal weeks collected: {len(city_responses)}")
    
    return city_responses


def separate_pending_and_approved(city_responses):
    """
    Separate API response data into two lists: one for 'pending' and one for 'approved' records.
    
    Parameters:
    -----------
    city_responses : list
        List of API response data dictionaries
    
    Returns:
    --------
    tuple
        (pending_records, approved_records): Two lists of dictionaries containing filtered records
    """
    pending_records = []
    approved_records = []
    
    # Process the response data
    for weekly_response in city_responses:
        data_entries = weekly_response.get("data", [])
        for entry in data_entries:
            # Create the record
            entity_id = entry.get("entity_id")
            row = {
                "entity_id": entity_id,
                "week_start_date": entry.get("start_time"),
                "status": entry.get("status"),
                "updated_at": entry.get("updated_at"),
                "ppi": None,
                "items_put_away_per_hour": None,
                "picker_util_fixed_wt_avg": None,
            }
            
            # Extract metric values
            for metric in entry.get("values", []):
                metric_name = metric["metric_name"].lower()
                if metric_name in ["ppi", "items_put_away_per_hour", "picker_util_fixed_wt_avg"]:
                    row[metric_name] = metric["value"]
            
            # Add to appropriate list based on status
            status = entry.get("status")
            if status == "pending":
                pending_records.append(row)
            elif status == "approved":
                approved_records.append(row)
    
    return pending_records, approved_records


def get_k8s_executor_config(executor_config={}):
    limits = {}
    requests = {}
    if executor_config.get("cpu"):
        limits["cpu"] = executor_config["cpu"]["limit"]
        requests["cpu"] = executor_config["cpu"]["request"]
    if executor_config.get("memory"):
        limits["memory"] = executor_config["memory"]["limit"]
        requests["memory"] = executor_config["memory"]["request"]

    return {
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                node_selector={"nodetype": executor_config.get("node_selector")},
                tolerations=[
                    k8s.V1Toleration(
                        effect="NoSchedule",
                        key="service",
                        operator="Equal",
                        value=executor_config.get("toleration"),
                    ),
                ],
                containers=[
                    k8s.V1Container(
                        image=Variable.get(
                            "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable",
                            "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable",
                        ),
                        name="base",
                        volume_mounts=[
                            k8s.V1VolumeMount(
                                mount_path="/usr/local/airflow/plugins",
                                name="airflow-dags",
                                sub_path="airflow/plugins",
                            ),
                        ],
                        resources=k8s.V1ResourceRequirements(limits=limits, requests=requests),
                    )
                ],
                service_account_name="blinkit-prep-airflow-prep-prep-eks-role",
            )
        )
    }


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "U07CE6P6UHY"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


# Constants
CHUNK_SIZE = 30
chunk_tasks = []
MAX_TASKS = 7

# Default value for actual_tasks before it's calculated
actual_tasks = MAX_TASKS

# Generate and split the data for pending and approved records
def generate_and_split_data(**kwargs):
    global actual_tasks
    # Get forecast inputs inside the function instead of at module level
    data = get_forecasts_inputs()
    
    # Split into pending and approved records
    pending_records, approved_records = separate_pending_and_approved(data)
    pending_data_length = len(pending_records)
    approved_data_length = len(approved_records)
    
    print(f"Total data items: {len(data)}")
    print(f"Pending records: {pending_data_length}")
    print(f"Approved records: {approved_data_length}")
    
    # Calculate IST timestamp
    input_ts_ist = datetime.now() + timedelta(hours=5.5)
    input_ts_ist = input_ts_ist.strftime("%Y-%m-%d %H:%M:%S")
    
    # Store the pending and approved records and IST timestamp for the respective notebooks
    kwargs['ti'].xcom_push(key='pending_records', value=pending_records)
    kwargs['ti'].xcom_push(key='approved_records', value=approved_records)
    kwargs['ti'].xcom_push(key='input_ts_ist', value=input_ts_ist)
    
    print(f"IST Timestamp: {input_ts_ist}")
    
    # Group pending records by entity_id
    entity_grouped_records = {}
    for record in pending_records:
        entity_id = record.get("entity_id")
        if entity_id not in entity_grouped_records:
            entity_grouped_records[entity_id] = []
        entity_grouped_records[entity_id].append(record)
    
    # Count unique entity_ids
    unique_entity_count = len(entity_grouped_records)
    print(f"Unique entity_ids found: {unique_entity_count}")
    
    # Calculate task distribution based on entity_ids
    print(f"Calculating task distribution based on entity_ids...")
    
    # Calculate how many entities each task should ideally process
    entities_per_task = CHUNK_SIZE  # Target 30 entities per task
    
    # Calculate ideal number of tasks needed for entities
    ideal_tasks = unique_entity_count / entities_per_task
    ideal_tasks_rounded = (unique_entity_count + entities_per_task - 1) // entities_per_task  # Ceiling division
    
    # Determine actual number of tasks we'll create
    global actual_tasks
    if ideal_tasks_rounded <= MAX_TASKS:
        # If we need fewer tasks than MAX_TASKS, use only what we need
        actual_tasks = ideal_tasks_rounded
    else:
        # Otherwise use max tasks allowed
        actual_tasks = MAX_TASKS
    
    print(f"Will use {actual_tasks} tasks for {unique_entity_count} entities")
    
    # Calculate entities per task with the simplified approach
    entities_per_task_distribution = {}
    if actual_tasks > 0:
        # Calculate base number of entities per task
        base_entities = unique_entity_count // actual_tasks
        # Calculate remaining entities
        remaining_entities = unique_entity_count % actual_tasks
        
        # Distribute entities to tasks
        for i in range(actual_tasks):
            if i < actual_tasks - 1:
                # For all tasks except the last one, assign base_entities
                entities_per_task_distribution[i] = base_entities
            else:
                # Last task gets base_entities plus remaining entities
                entities_per_task_distribution[i] = base_entities + remaining_entities
    
    print(f"Distributing pending data using entity-based approach...")
    
    # Create tasks with their associated entity_ids
    entity_ids_list = list(entity_grouped_records.keys())
    entity_start_index = 0
    
    # Initialize task_data_map to store records for each task
    task_data_map = {task_id: [] for task_id in range(actual_tasks)}
    
    # Distribute entities to tasks
    for task_id, entity_count in entities_per_task_distribution.items():
        if entity_count > 0 and entity_start_index < unique_entity_count:
            # Get the entity_ids for this task
            entity_end_index = min(entity_start_index + entity_count, unique_entity_count)
            task_entity_ids = entity_ids_list[entity_start_index:entity_end_index]
            entity_start_index = entity_end_index
            
            # Collect all records for these entity_ids
            for entity_id in task_entity_ids:
                task_data_map[task_id].extend(entity_grouped_records[entity_id])
            
            print(f"Task {task_id} assigned {len(task_entity_ids)} entities with {len(task_data_map[task_id])} records")
        else:
            # This could happen if we have fewer entities than tasks
            print(f"Task {task_id} assigned 0 entities (no data or exhausted)")
    
    # Push the task data to XCom
    for task_id, task_data in task_data_map.items():
        kwargs['ti'].xcom_push(key=f'chunk_{task_id}', value=task_data)
        print(f"Stored chunk_{task_id} with {len(task_data)} records from {entities_per_task_distribution.get(task_id, 0)} entities")
    
    # Create empty entries for any dummy tasks
    for i in range(len(entities_per_task_distribution), MAX_TASKS):
        kwargs['ti'].xcom_push(key=f'chunk_{i}', value=[])
        print(f"Stored empty chunk_{i} (dummy task)")
    
    # Store the actual tasks count for reference
    kwargs['ti'].xcom_push(key='actual_tasks', value=actual_tasks)
    
    print("Data distribution complete")
    return {
        'pending_data_length': pending_data_length,
        'approved_data_length': approved_data_length,
        'actual_tasks': actual_tasks,
        'input_ts_ist': input_ts_ist
    }

args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2024-08-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-11-03T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "U07CE6P6UHY",
    "execution_timeout": timedelta(minutes=120),
    "pool": "de_pool",
}

dag = DAG(
    dag_id="de_misc_workflow_split_dict_lists_v1",
    default_args=args,
    schedule_interval="5,35 * * * *",
    tags=["misc", "workflow", "dict_list", "de", "misc", "workflow"],
    catchup=False,
    concurrency=10,
    max_active_runs=1
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_workflow_split_dict_lists_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

notebook_task_bag = {}

print(f"Initializing DAG: {dag.dag_id}")

# Task to generate and split data
split_task = PythonOperator(
    task_id="generate_and_split_data",
    python_callable=generate_and_split_data,
    provide_context=True,
    dag=dag,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 1, "limit": 2},
            "memory": {"request": "4Gi", "limit": "7Gi"},
            "node_selector": "airflow-spot-general",
            "toleration": "airflow-spot-general",
        }
    ),
    retries=2,
    retry_delay=timedelta(minutes=2),
)
notebook_task_bag["generate_and_split_data"] = split_task
print(f"Task created: generate_and_split_data")

# Create task for processing pending records
for i in range(MAX_TASKS):
    task_id = f"process_chunk_{i}"
    
    # Always create PapermillOperators for the first 'actual_tasks' tasks
    if i < actual_tasks:
        # This is a real task with data to process
        chunk_task = PapermillOperator(
            task_id=task_id,
            input_nb="{cwd}/pending_notebook.ipynb".format(cwd=cwd),
            output_nb=f"s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_split_dict_lists_v1/{{{{ run_id }}}}/{task_id}/try_{{{{ task_instance.try_number }}}}.ipynb",
            parameters={
                "data_set": f"{{{{ task_instance.xcom_pull('generate_and_split_data', key='chunk_{i}') }}}}",
                "chunk_index": i,
                "set_name": f"Pending Chunk {i}",
                "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/split_dict_lists",
                "ts": "{{ prev_start_date_success }}",
                "input_ts_ist": "{{ task_instance.xcom_pull('generate_and_split_data', key='input_ts_ist') }}"
            },
            env=env,
            dag=dag,
            executor_config=get_k8s_executor_config(
                {
                    "cpu": {"request": 1, "limit": 2},
                    "memory": {"request": "4Gi", "limit": "7Gi"},
                    "node_selector": "airflow-spot-general",
                    "toleration": "airflow-spot-general",
                }
            ),
            retries=2,
            retry_delay=timedelta(minutes=2),
        )
        print(f"Task created: {task_id} (real task)")
    else:
        # This is a dummy task just for UI display
        chunk_task = DummyOperator(
            task_id=task_id,
            dag=dag
        )
        print(f"Task created: {task_id} (dummy task)")
    
    notebook_task_bag[task_id] = chunk_task
    chunk_tasks.append(chunk_task)

# Create task for processing approved records
approved_task = PapermillOperator(
    task_id="process_approved_records",
    input_nb="{cwd}/approved_notebook.ipynb".format(cwd=cwd),
    output_nb=f"s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_split_dict_lists_v1/{{{{ run_id }}}}/process_approved_records/try_{{{{ task_instance.try_number }}}}.ipynb",
    parameters={
        "data_set": "{{ task_instance.xcom_pull('generate_and_split_data', key='approved_records') }}",
        "set_name": "Approved Records",
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/split_dict_lists",
        "ts": "{{ prev_start_date_success }}",
        "input_ts_ist": "{{ task_instance.xcom_pull('generate_and_split_data', key='input_ts_ist') }}"
    },
    env=env,
    dag=dag,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 1, "limit": 2},
            "memory": {"request": "4Gi", "limit": "7Gi"},
            "node_selector": "airflow-spot-general",
            "toleration": "airflow-spot-general",
        }
    ),
    retries=2,
    retry_delay=timedelta(minutes=2),
)
notebook_task_bag["process_approved_records"] = approved_task
print(f"Task created: process_approved_records")

# Log task dependencies being established
print(f"Setting up task dependencies")

# Define task dependencies - split_task should run before all chunk processing tasks
split_task >> chunk_tasks
split_task >> approved_task

print(f"DAG {dag.dag_id} is fully initialized with {len(notebook_task_bag)} tasks")

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
