{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4e1b332-3b15-4880-833f-cbcdcb3139d5", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27\n", "!pip install pulp\n", "!pip install ortools==9.6.2534\n", "!pip install scipy"]}, {"cell_type": "code", "execution_count": null, "id": "502d7d6f-304b-421b-855c-c2c304506ede", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JUPYTERHUB_USER\"] = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "819d3faf-b487-4b93-b9a3-3bb62fd2d734", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "from pulp import *\n", "from pulp import LpProblem, LpMinimize, LpVariable, lpSum, LpStatus, pulp\n", "import time\n", "import boto3\n", "import ast\n", "import awswrangler as wr\n", "from concurrent.futures import ProcessPoolExecutor, as_completed\n", "from ortools.sat.python import cp_model\n", "from scipy.ndimage import median_filter\n", "import pickle as pkl\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "39ad4cf7-4457-42d3-8f73-f78152368b54", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "060f9294-552a-40f4-8a11-0389f546453c", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "update_ts_ist = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "update_ts_ist = update_ts_ist.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "update_ts_ist"]}, {"cell_type": "code", "execution_count": null, "id": "64c7af71-6859-4223-a840-6c7ea1e80d22", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "data_set = [] # This will be replaced with the actual data\n", "set_name = \"Set 1\" # This will be replaced with the actual set name"]}, {"cell_type": "code", "execution_count": null, "id": "c8873d78-b5db-46fd-83b6-8387685402be", "metadata": {}, "outputs": [], "source": ["#data_set = \"[{'entity_id': 3465, 'week_start_date': '2025-09-01 00:00:00', 'status': 'pending', 'updated_at': '2025-08-25 16:35:52', 'ppi': 12.0, 'items_put_away_per_hour': 251.0, 'picker_util_fixed_wt_avg': None}]\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "5baaa6d4-4bc2-4c37-87ca-5818fda04c07", "metadata": {}, "outputs": [], "source": ["print(f\"Data set type before conversion: {type(data_set)}\")\n", "\n", "# Convert data_set from string to dict/list if needed\n", "if isinstance(data_set, str):\n", "    print(\"Converting data_set from string to Python object\")\n", "    try:\n", "        data_set = json.loads(data_set)\n", "        print(\"Successfully converted from string using json.loads()\")\n", "    except Exception as e:\n", "        print(f\"Error converting using json: {str(e)}\")\n", "        # Alternative method using ast.literal_eval\n", "        try:\n", "            data_set = ast.literal_eval(data_set)\n", "            print(\"Successfully converted from string using ast.literal_eval()\")\n", "        except Exception as e2:\n", "            print(f\"Error converting using ast: {str(e2)}\")\n", "\n", "print(f\"Data set type after conversion: {type(data_set)}\")\n", "print(f\"Number of items in the data set: {len(data_set)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c926da2e-e278-4e3d-b8c0-efeb00543a1f", "metadata": {}, "outputs": [], "source": ["# Display the raw data\n", "print(\"Sample of pending records:\")\n", "print(data_set[:2] if len(data_set) > 2 else data_set)  # Show just a sample if large"]}, {"cell_type": "code", "execution_count": null, "id": "99ed4c9f-118a-4ee7-9c60-f5e8f4339cb0", "metadata": {}, "outputs": [], "source": ["# Convert to DataFrame\n", "if len(data_set) > 0:\n", "    df = pd.DataFrame(data_set)\n", "    print(\"DataFrame created successfully\")\n", "    \n", "    # Rename columns for planner input\n", "    planner_df = df.rename(columns = {\n", "        'ppi':'input_ppi',\n", "        'items_put_away_per_hour':'input_iph',\n", "        'picker_util_fixed_wt_avg':'input_ft_picker_util',\n", "        'entity_id':'outlet_id'\n", "    })\n", "    \n", "    # Select only the needed columns\n", "    planner_df = planner_df[['outlet_id','input_ppi','input_iph','input_ft_picker_util','week_start_date']]\n", "    \n", "    # Convert week_start_date to datetime\n", "    planner_df['week_start_date'] = pd.to_datetime(planner_df['week_start_date'])\n", "    \n", "    # Display the DataFrame\n", "    print(\"\\nProcessed pending records:\")\n", "    display(planner_df.head())\n", "else:\n", "    print(\"No pending records to process\")\n", "    sys.exit(0)"]}, {"cell_type": "code", "execution_count": null, "id": "97b41c18-6686-4f48-a550-664e35800628", "metadata": {}, "outputs": [], "source": ["input_df = pd.read_parquet(\"s3://prod-dse-projects/store_ops/monthly/september/optimiser_input_data_copy/\").rename(columns = {'checkout_date':'order_date'})#.drop(columns = ['new_packaged_iph','new_perishable_iph'])\n", "input_df['order_date'] = pd.to_datetime(input_df['order_date'] )\n", "# Need to change this path\n", "hourly_data = pd.read_parquet(\"s3://prod-dse-projects/store_ops/monthly/september/all_stores/hourly_data/\")[['order_date','hour','outlet_id','mh_required_for_packaged_putaway_unloading','mh_required_for_perishable_putaway_unloading','truck_delay_buffer']] \n", "hourly_data['order_date'] = pd.to_datetime(hourly_data['order_date'] )\n", "input_df = input_df.merge(hourly_data, on = ['order_date','hour','outlet_id'], how = 'left')\n", "input_df = input_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "affbadfe-db4c-46f4-bff1-90a331a6f7d9", "metadata": {}, "outputs": [], "source": ["input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1e41bf49-548b-4d1e-b6f1-eb06fcd0e7ba", "metadata": {}, "outputs": [], "source": ["input_df = input_df.sort_values(['order_date','hour'])\n", "input_df['day_since_monday'] = input_df['order_date'].dt.weekday\n", "input_df['week_start_date'] = input_df['order_date'] - pd.to_timedelta(input_df['day_since_monday'], unit='D')\n", "min_date = input_df['order_date'].min()\n", "max_date = input_df['order_date'].max()\n", "\n", "mask = input_df['order_date'].isin([input_df['order_date'].min(), input_df['order_date'].max()])\n", "input_df.loc[mask, 'week_start_date'] = pd.NaT\n", "\n", "input_df['week_start_date'] = input_df['week_start_date'].ffill().bfill()\n", "#input_df['week_start_date'] = pd.to_datetime(input_df['week_start_date'])\n", "\n", "\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "84dfbd1d-be7f-41ef-a2a7-f6b8e7cff2d4", "metadata": {}, "outputs": [], "source": ["store_list = list(set(planner_df.outlet_id))\n", "input_df = input_df[input_df.outlet_id.isin(store_list)]\n", "input_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e23b1704-311a-4b67-868f-2e23bd215e34", "metadata": {}, "outputs": [], "source": ["planner_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "32b05d4e-beb7-4674-bbb4-3e4492c76687", "metadata": {}, "outputs": [], "source": ["#planner_df = planner_df.fillna(10)"]}, {"cell_type": "code", "execution_count": null, "id": "2d58b60f-54f4-45e5-b504-4267e696b3da", "metadata": {}, "outputs": [], "source": ["planner_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "85ca38f2-952b-4ef8-b89b-a5aef8312d90", "metadata": {}, "outputs": [], "source": ["input_df = input_df.merge(planner_df,on=['outlet_id','week_start_date'], how = 'left')\n", "print(input_df.shape)\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c8677502-be84-48fc-86fe-4242d145a019", "metadata": {}, "outputs": [], "source": ["assert input_df.shape[0] == input_df.outlet_id.nunique() * 37 * 24"]}, {"cell_type": "code", "execution_count": null, "id": "4e2f268c-d0d6-4521-b053-1772d1f2c9f9", "metadata": {}, "outputs": [], "source": ["def apply_smoothning(df,column, window_size = 3):\n", "    # Function to smoothen the final data\n", "    filtered_data = df.loc[(df['hour'] >= 6) & (df['hour'] <= 23)]\n", "    smoothed_values = filtered_data.groupby(['outlet_id','order_date'])[column].transform(\n", "        lambda x: median_filter(x, size=window_size)\n", "    )\n", "    df['smoothed_'+column] = df[column]  \n", "    df.loc[(df['hour'] >= 6) & (df['hour'] <= 23), 'smoothed_'+column] = smoothed_values\n", "    return df  "]}, {"cell_type": "markdown", "id": "1c3dfd23-ded2-4be9-8006-84fb10da3c54", "metadata": {}, "source": ["## PPI calculation"]}, {"cell_type": "code", "execution_count": null, "id": "db352c1c-71f1-45d3-9067-d9d70344a36e", "metadata": {}, "outputs": [], "source": ["input_df['new_ft_ppi'] = input_df['ppi_hour_factor_fixed'] * input_df['input_ppi']\n", "input_df['new_ft_ppi'] = pd.to_numeric(input_df['new_ft_ppi'], errors='coerce')\n", "input_df['new_ft_ppi']  = np.round(input_df['new_ft_ppi'].clip(upper = 30, lower = 7),2)\n", "\n", "input_df = apply_smoothning(input_df,column=\"new_ft_ppi\")\n", "input_df['new_ft_ppi'] = input_df['smoothed_new_ft_ppi']\n", "input_df['ft_ppi'] = np.where(input_df['new_ft_ppi'].isnull(),input_df['ft_ppi'],input_df['new_ft_ppi'])"]}, {"cell_type": "code", "execution_count": null, "id": "84efe07c-7b97-4ecd-a2fb-a5ad7a7b8d56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b723023a-7420-47cd-b42b-cc786d9566b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2c82c1a1-f2a6-4aac-9f66-23d46070e513", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "071d5c3e-a677-42a8-9f86-cc6fe476a7e1", "metadata": {}, "source": ["## Util calculation"]}, {"cell_type": "code", "execution_count": null, "id": "7d3fea6c-0bc3-4806-ad69-3a576d986085", "metadata": {}, "outputs": [], "source": ["input_df['new_ft_util'] = input_df['util_hour_factor'] * input_df['input_ft_picker_util']/100\n", "input_df['new_ft_util'] = pd.to_numeric(input_df['new_ft_util'], errors='coerce')\n", "\n", "input_df['new_ft_util'] = input_df['new_ft_util'].clip(upper = 0.60, lower = 0.40)\n", "input_df = np.round(apply_smoothning(input_df,column=\"new_ft_util\"),2)\n", "input_df['new_ft_util'] = input_df['smoothed_new_ft_util']\n", "input_df['picker_utilisation_per'] = np.where(input_df['new_ft_util'].isnull(),input_df['picker_utilisation_per'],input_df['new_ft_util'])"]}, {"cell_type": "markdown", "id": "fe0d1499-8802-42af-996e-2ccb726587a6", "metadata": {}, "source": ["## OD PPI calculation"]}, {"cell_type": "code", "execution_count": null, "id": "dbfd1831-e39e-40af-931a-4f5844117ad1", "metadata": {}, "outputs": [], "source": ["input_df['od_ppi'] = (input_df['ft_ppi']/input_df['picker_utilisation_per'])/ (input_df['od_prod']/input_df['od_util'])"]}, {"cell_type": "markdown", "id": "1cae1429-e93e-416d-aee3-80f96570663a", "metadata": {}, "source": ["## IPH calculation"]}, {"cell_type": "code", "execution_count": null, "id": "57862255-a406-49b6-b0e6-0ac8c9d6ac1a", "metadata": {}, "outputs": [], "source": ["max_date = input_df['order_date'].max()\n", "min_date = input_df['order_date'].min()\n", "max_date,min_date"]}, {"cell_type": "code", "execution_count": null, "id": "13cd9ba4-08d6-44f4-bdff-60ba5c511df2", "metadata": {}, "outputs": [], "source": ["\n", "iph_df = input_df[(input_df.order_date > min_date) & (input_df.order_date < max_date)].groupby(['outlet_id','week_start_date']).agg(\n", "    forecasted_hourly_packaged = ('forecasted_hourly_packaged',sum),\n", "    forecasted_hourly_perishable = ('forecasted_hourly_perishable',sum),\n", "    packaged_iph = ('packaged_iph',max),\n", "    perishable_iph = ('perishable_iph',max),\n", "    unloading_perishable = ('mh_required_for_perishable_putaway_unloading',sum),\n", "    unloading_packaged = ('mh_required_for_packaged_putaway_unloading',sum),\n", "    truck_delay_buffer = ('truck_delay_buffer',sum),\n", "    central_iph = ('input_iph',max)\n", ").reset_index()\n", "iph_df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "6e9f0417-3939-4443-bf53-f29d3974b523", "metadata": {}, "outputs": [], "source": ["iph_df['central_iph'] = pd.to_numeric(iph_df['central_iph'], errors='coerce')\n", "iph_df['input_iph'] = (iph_df['forecasted_hourly_packaged'] +  iph_df['forecasted_hourly_perishable'])/((iph_df['forecasted_hourly_packaged']/iph_df['packaged_iph']) + (iph_df['forecasted_hourly_perishable']/iph_df['perishable_iph']) + iph_df['unloading_perishable'] + iph_df['unloading_packaged'] + iph_df['truck_delay_buffer']  )\n", "iph_df['pck_time'] = iph_df['forecasted_hourly_packaged']/iph_df['packaged_iph']\n", "iph_df['peri_time'] = iph_df['forecasted_hourly_perishable']/iph_df['perishable_iph']\n", "iph_df['unloading_time'] = iph_df['unloading_perishable'] + iph_df['unloading_packaged'] + iph_df['truck_delay_buffer']\n", "iph_df['input_putaway_time']= (iph_df['forecasted_hourly_packaged'] + iph_df['forecasted_hourly_perishable'] )/iph_df['central_iph']\n", "iph_df['model_putaway_time'] = iph_df['pck_time'] + iph_df['peri_time'] + iph_df['unloading_time']\n", "iph_df['delta'] = iph_df['input_putaway_time'] - iph_df['model_putaway_time']\n", "iph_df['pck_ratio'] = iph_df['pck_time']/(iph_df['pck_time'] + iph_df['peri_time'])\n", "iph_df['peri_ratio'] = iph_df['peri_time']/(iph_df['pck_time'] + iph_df['peri_time'])\n", "iph_df['new_pck_time'] = iph_df['pck_time'] + (iph_df['delta'] *  iph_df['pck_ratio'] )\n", "iph_df['new_peri_time'] = iph_df['peri_time'] + (iph_df['delta'] *  iph_df['peri_ratio'] )\n", "iph_df['model_putaway_time_new'] = iph_df['new_pck_time'] + iph_df['new_peri_time'] + iph_df['unloading_time']\n", "iph_df['new_packaged_iph'] = np.round(iph_df['forecasted_hourly_packaged']/iph_df['new_pck_time'],2)\n", "iph_df['new_perishable_iph'] = np.round(iph_df['forecasted_hourly_perishable']/iph_df['new_peri_time'],2)\n", "iph_df['new_input_iph']  = np.round((iph_df['forecasted_hourly_packaged'] +  iph_df['forecasted_hourly_perishable'])/((iph_df['forecasted_hourly_packaged']/iph_df['new_packaged_iph']) + (iph_df['forecasted_hourly_perishable']/iph_df['new_perishable_iph']) + iph_df['unloading_perishable'] + iph_df['unloading_packaged'] + iph_df['truck_delay_buffer']  ))\n", "iph_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2959d7d0-8fdf-465f-8c8c-38c4158e5285", "metadata": {}, "outputs": [], "source": ["iph_df = iph_df[['outlet_id','week_start_date','new_perishable_iph','new_packaged_iph']]\n", "input_df = input_df.merge(iph_df, on = ['outlet_id','week_start_date'], how = 'left')\n", "input_df['packaged_iph'] = np.where(input_df['new_packaged_iph'].isnull(), input_df['packaged_iph'], input_df['new_packaged_iph'] )\n", "input_df['perishable_iph'] = np.where(input_df['new_perishable_iph'].isnull(), input_df['perishable_iph'], input_df['new_perishable_iph'] )\n", "\n", "input_df['packaged_iph'] = input_df['packaged_iph'].clip(lower=60, upper=300)\n", "input_df['perishable_iph'] = input_df['perishable_iph'].clip(lower=80, upper=600)"]}, {"cell_type": "code", "execution_count": null, "id": "e9b7b59d-b3e8-4173-872f-ec6f5dcb1a58", "metadata": {}, "outputs": [], "source": ["input_df = input_df.drop(columns = ['input_ppi','input_iph','input_ft_picker_util','new_ft_ppi','smoothed_new_ft_ppi','new_ft_util','smoothed_new_ft_util','new_perishable_iph','new_packaged_iph'])\n", "assert input_df.shape[0] == input_df.outlet_id.nunique() * 37 * 24 ,\"<PERSON><PERSON><PERSON> Mismatch Error\"\n", "assert input_df.isna().sum().sum() == 0 ,\"NULL Value Error\""]}, {"cell_type": "markdown", "id": "d38520f9-9f4c-49d1-81fe-1cb84737208f", "metadata": {}, "source": ["## Optimiser"]}, {"cell_type": "code", "execution_count": null, "id": "1db1038b-88ee-471f-8d24-7f8af628db54", "metadata": {}, "outputs": [], "source": ["columns = [\n", "        'order_date', 'hour', 'outlet_id', \n", "        'qty_to_pick', 'perishable_qty_accumulated_final', 'packaged_qty_accumulated_final', \n", "        'mavg_ppi', 'mavg_pick_util', 'mavg_iph_packaged', 'mavg_iph_perishable', \n", "        'ppi_od', 'od_util', 'od_eph', 'fixed_eph', 'od_prod','absent_predict','avg_ipo',\n", "        'grn_difficulty_flag','mh_limit','od_max_evening','od_max_morning','od_max_noon','od_min_day',\n", "        'perishable_frozen_qty','fnv_qty','opm_75','od_max_day','week_start_date','current_ft_ppi','current_ft_util'\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "0f4e13f6-0033-4ede-b9d9-83f11b07f1a6", "metadata": {}, "outputs": [], "source": ["\n", "def erlang_c(c, lambda_s, mu):\n", "    \"\"\"\n", "    Compute the Erlang C probability that an order has to wait.\n", "    \n", "    Parameters:\n", "        c (int): Number of employees (servers).\n", "        lambda_s (float): Arrival rate (orders per second).\n", "        mu (float): Service rate per employee (orders per second).\n", "    \n", "    Returns:\n", "        float: The probability that an arriving order must wait.\n", "    \"\"\"\n", "    rho = lambda_s / (c * mu)\n", "    if rho >= 1:\n", "        return 1.0  # System is unstable if utilization >= 100%\n", "    numerator = (c * rho) ** c / (math.factorial(c) * (1 - rho))\n", "    denominator = sum((c * rho) ** n / math.factorial(n) for n in range(c)) + numerator\n", "    return numerator / denominator\n", "\n", "def overall_probability(c, lambda_s, mu, threshold=10):\n", "    \"\"\"\n", "    Compute the overall probability that an order is assigned within the threshold (seconds).\n", "    \n", "    Parameters:\n", "        c (int): Number of employees.\n", "        lambda_s (float): Arrival rate (orders/second).\n", "        mu (float): Service rate per employee (orders/second).\n", "        threshold (float): Time threshold in seconds.\n", "    \n", "    Returns:\n", "        float: Overall probability that an order is assigned within 'threshold' seconds.\n", "    \"\"\"\n", "    P_wait = erlang_c(c, lambda_s, mu)\n", "    p_if_wait = 1 - math.exp(-(c * mu - lambda_s) * threshold)\n", "    return (1 - P_wait) + P_wait * p_if_wait\n", "\n", "\n", "def compute_required_employees(row):\n", "    \"\"\"\n", "    Given a row with service parameters, compute the required number of employees\n", "    and corresponding manhours to meet the R2A target.\n", "    \n", "    Expected row columns:\n", "        - 'opm': Design orders per minute (e.g., 90th percentile)\n", "        - 'IPO': Average Items per Order\n", "        - 'PPI': Picking Time per Item (in seconds)\n", "        - 'R2A_target': R2A target (e.g., 0.85 for 85%)\n", "        - 'threshold': Waiting time threshold in seconds (e.g., 10)\n", "        - 'util_target': Maximum utilization target (e.g., 0.55)\n", "    \n", "    Returns:\n", "        pandas.Series: A series with 'required_employees' and 'manhours'\n", "    \"\"\"\n", "    \n", "    opm_design = row['opm_avg']\n", "    IPO = row['avg_ipo']\n", "    PPI = row['mavg_ppi']\n", "    R2A_target = 0.85\n", "    threshold = 10\n", "    util_target = row['mavg_pick_util']\n", "    \n", "    lambda_s = opm_design / 60.0  \n", "    service_time = IPO * PPI\n", "    if (lambda_s == 0) | (service_time==0):\n", "        return pd.Series({'mh_required_for_pick_qt': 0,'r2a': 0})\n", "    mu = 1 / service_time\n", "    \n", "    c_min_util = math.floor((opm_design/60) / (util_target * mu))\n", "    \n", "    required_c = None\n", "    for c in range(c_min_util, 10000):\n", "        if c * mu <= lambda_s:\n", "            continue\n", "        try:\n", "            p = overall_probability(c, lambda_s, mu, threshold)\n", "            if p >= R2A_target:\n", "                required_c = c\n", "                break\n", "        except Exception as e:\n", "            return pd.Series({'mh_required_for_pick_qt': required_c,'r2a': None})\n", "    \n", "    return pd.Series({'mh_required_for_pick_qt': required_c,'r2a': p})\n", "\n", "def get_r2a(row):\n", "    \"\"\"\n", "    Given a row with service parameters, compute the required number of employees\n", "    and corresponding manhours to meet the R2A target.\n", "    \n", "    Expected row columns:\n", "        - 'opm': Design orders per minute (e.g., 90th percentile)\n", "        - 'IPO': Average Items per Order\n", "        - 'PPI': Picking Time per Item (in seconds)\n", "        - 'R2A_target': R2A target (e.g., 0.85 for 85%)\n", "        - 'threshold': Waiting time threshold in seconds (e.g., 10)\n", "        - 'util_target': Maximum utilization target (e.g., 0.55)\n", "    \n", "    Returns:\n", "        pandas.Series: A series with 'required_employees' and 'manhours'\n", "    \"\"\"\n", "  \n", "    opm_design = row['opm_avg']\n", "    IPO = row['avg_ipo']\n", "    PPI = row['mavg_ppi']\n", "    threshold = 10\n", "    c = row['c']\n", "    \n", "    # Derived Parameters\n", "    lambda_s = opm_design / 60.0  \n", "    service_time = IPO * PPI\n", "    if (lambda_s == 0) | (service_time==0) | (c==0) :\n", "        return pd.Series({'r2a_c_minus_one': 0})\n", "    mu = 1 / service_time\n", "    \n", "    \n", "    return pd.Series({'r2a_c_minus_one':overall_probability(c, lambda_s, mu, threshold)})\n", "\n", "def calculate_picking_mh(df):\n", "    \n", "    df = df.copy()\n", "    \n", "    df['avg_ipo'] = np.where(df['avg_ipo']>=8,8,df['avg_ipo'])\n", "    df['opm_avg_raw'] = np.round(df['qty_to_pick']/(60*df['avg_ipo']),2)\n", "    df['opm_avg_raw'] = df['opm_avg_raw'].fillna(0)\n", "    \n", "    # kolkata_mask = df['outlet_id'].isin(kolkata_list)\n", "    # hour_mask_kolkata = df['hour'].isin(kolkata_hours)\n", "    # hour_mask_default = df['hour'].isin(default_hours)\n", "    \n", "    df['hour_rank'] = df.groupby(['outlet_id', 'order_date'])['qty_to_pick'].rank(method='first', ascending=False)\n", "    df['is_top_10_hour'] = df['hour_rank'] <= 10\n", "    df = df.sort_values(by=['outlet_id', 'order_date', 'hour'])\n", "    \n", "    df['prev_top_10'] = df.groupby(['outlet_id', 'order_date'])['is_top_10_hour'].shift(1)\n", "    df['next_top_10'] = df.groupby(['outlet_id', 'order_date'])['is_top_10_hour'].shift(-1)\n", "    df['prev_top_10'] = df['prev_top_10'].fillna(False)\n", "    df['next_top_10'] = df['next_top_10'].fillna(False)\n", "\n", "    df['bump_opm'] = df['is_top_10_hour'] | (df['prev_top_10'] & df['next_top_10'])\n", "   \n", "    \n", "    \n", "    \n", "    df['opm_avg'] = np.where(\n", "        df['bump_opm'],\n", "        np.minimum(df['opm_avg_raw'] * 1.30, df['opm_75']),\n", "        df['opm_avg_raw']\n", "    )\n", "    \n", "\n", "    \n", "    df[['mh_required_for_pick_qt', 'r2a']] = df.apply(compute_required_employees, axis=1)\n", "    df['mh_required_for_pick'] = (df['qty_to_pick']*df['mavg_ppi'])/(df['mavg_pick_util']*3600)\n", "    df['c'] = np.maximum(df['mh_required_for_pick_qt'] - 1,0)\n", "    df['c'] = df['c'].astype(int)\n", "    df['r2a_c_minus_one'] = df.apply(get_r2a, axis=1)\n", "    return df\n", "\n", "def input_data_prep(data):\n", "    data['eph_od'] = 1.0\n", "    data['eph_ft'] = 1.0\n", "    data['mavg_audit_mh'] = 0.0\n", "    data['mavg_fnv_mh'] = 0.0\n", "    \n", "    \n", "    data = data.rename(columns = {\n", "    'checkout_date':'order_date',\n", "    'forecast_hourly_item':'qty_to_pick',\n", "    'forecasted_hourly_perishable':'perishable_qty_accumulated_final',\n", "    'forecasted_hourly_packaged':'packaged_qty_accumulated_final',\n", "    'ft_ppi':'mavg_ppi',\n", "    'picker_utilisation_per':'mavg_pick_util',\n", "    'packaged_iph':'mavg_iph_packaged',\n", "     'perishable_iph':'mavg_iph_perishable',\n", "    'od_ppi':'ppi_od',\n", "    'od_util':'od_util',\n", "    'eph_od':'od_eph',\n", "    'eph_ft':'fixed_eph',\n", "    'od_prod':'od_prod'\n", "    })\n", "    \n", "    data = data[columns]\n", "    \n", "    data['order_date'] = pd.to_datetime(data['order_date'])\n", "    data['hour']  = data['hour'].astype(int)\n", "    data['qty_to_pick'] = data['qty_to_pick'].astype(int)\n", "    data['packaged_qty_accumulated_final'] = data['packaged_qty_accumulated_final'].fillna(0).astype(int)\n", "    data['mavg_pick_util'] = data['mavg_pick_util'].astype(float)\n", "    data['perishable_qty_accumulated_final'] = data['perishable_qty_accumulated_final'].fillna(0).astype(int)\n", "    data['mavg_ppi'] = data['mavg_ppi'].astype(float)\n", "    data['mavg_iph_packaged'] = data['mavg_iph_packaged'].astype(float)\n", "    data['mavg_iph_perishable'] = data['mavg_iph_perishable'].astype(float)\n", "    \n", "    data = calculate_picking_mh(data)\n", "    \n", "    return data\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "8e3ef192-5c8b-4650-b6b6-6764d1d745cc", "metadata": {}, "outputs": [], "source": ["input_df = input_df.sort_values(['outlet_id','order_date','hour'])\n", "input_df[\"perishable_frozen_qty\"] = input_df[\"forecasted_hourly_perishable\"]\n", "input_df[\"fnv_qty\"] = input_df[\"forecasted_hourly_fnv\"]\n", "input_df[\"forecasted_hourly_perishable\"] = (\n", "    input_df[\"forecasted_hourly_perishable\"] + input_df[\"forecasted_hourly_fnv\"]\n", ")\n", "\n", "outlet_ids = list(input_df[\"outlet_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "f2006925-4543-4784-b64e-23b4b38d63f0", "metadata": {}, "outputs": [], "source": ["input_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ea151260-db93-4b63-93eb-902de77459be", "metadata": {}, "outputs": [], "source": ["input_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "b622359c-687d-4045-8a58-35a7f84cffd5", "metadata": {}, "outputs": [], "source": ["def fetch_batch(batch):\n", "    batch_df = input_df[input_df.outlet_id.isin(batch)]\n", "    return input_data_prep(batch_df.copy())"]}, {"cell_type": "code", "execution_count": null, "id": "b8737dde-2d91-402c-a51d-ada8a42186fa", "metadata": {}, "outputs": [], "source": ["batch_size = 200\n", "all_dfs = []\n", "batches = [outlet_ids[i : i + batch_size] for i in range(0, len(outlet_ids), batch_size)]\n", "with ProcessPoolExecutor(max_workers=12) as executor:\n", "    futures = [executor.submit(fetch_batch, batch) for batch in batches]\n", "\n", "    for future in as_completed(futures):\n", "        all_dfs.append(future.result())"]}, {"cell_type": "code", "execution_count": null, "id": "35077a71-2f0e-4a05-8dfc-82d212bcfa82", "metadata": {}, "outputs": [], "source": ["input_data = pd.concat(all_dfs, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f0b28ae5-ab3d-4810-b4b5-6e3080e33ef3", "metadata": {}, "outputs": [], "source": ["input_data['nr'] = ((input_data['mh_required_for_pick_qt']/abs(input_data['r2a']-0.85)) + ((input_data['c'])/abs(input_data['r2a_c_minus_one']-0.85)))\n", "input_data['dr'] = ((1/abs(input_data['r2a']-0.85)) + (1/abs(input_data['r2a_c_minus_one']-0.85)))\n", "input_data['mh_required_for_pick_qt_vt_avg'] = input_data['nr']/input_data['dr']\n", "input_data['mh_required_for_pick_qt_vt_avg']  = np.where(input_data['r2a']>0.90,input_data['mh_required_for_pick_qt_vt_avg'],input_data['mh_required_for_pick_qt'])\n", "input_data['mh_required_for_pick_qt_vt_avg'] = np.maximum(input_data['mh_required_for_pick_qt_vt_avg'], input_data['mh_required_for_pick'] )\n", "input_data['mh_required_for_pick_qt'] = input_data['mh_required_for_pick_qt_vt_avg']"]}, {"cell_type": "code", "execution_count": null, "id": "94479038-6ee0-4990-856a-57260f333491", "metadata": {}, "outputs": [], "source": ["morning_slot = [4,5, 6, 7, 8, 9, 10, 11]\n", "noon_slot = [12, 13, 14, 15, 16, 17]\n", "evening_slot = [18, 19, 20, 21, 22, 23]\n", "ignore_dates = [input_data['order_date'].min(),input_data['order_date'].max()]\n", "\n", "input_data['slot'] = np.where((input_data['hour'].isin(morning_slot)) & (~input_data.order_date.isin(ignore_dates)), 'morning',\n", "                         np.where(input_data['hour'].isin(noon_slot) & (~input_data.order_date.isin(ignore_dates)) , 'noon',\n", "                         np.where(input_data['hour'].isin(evening_slot) & (~input_data.order_date.isin(ignore_dates)), 'evening', 'ignore')))"]}, {"cell_type": "code", "execution_count": null, "id": "b7def96c-e6d2-46fc-8df4-b6cccd3a8b53", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "(input_data['packaged_qty_accumulated_final'] > 0) & (input_data['hour'] == 20),\n", "(input_data['packaged_qty_accumulated_final'] > 0) & (input_data['hour'] == 21),\n", "(input_data['packaged_qty_accumulated_final'] > 0) & (input_data['hour'] == 22),\n", "(input_data['packaged_qty_accumulated_final'] > 0) & (input_data['hour'] == 23),\n", "]\n", "values = [9, 8, 7, 6]\n", "input_data['pkg_window'] = np.select(conditions, values, default=6)"]}, {"cell_type": "code", "execution_count": null, "id": "ab5c8ce2-4641-424e-97e1-1f71078b178b", "metadata": {}, "outputs": [], "source": ["store_list = list(input_data['outlet_id'].unique())\n", "len(store_list)"]}, {"cell_type": "code", "execution_count": null, "id": "2ccda922-bf05-4aec-add6-0e9c8ef9b25b", "metadata": {}, "outputs": [], "source": ["min_date = input_data['order_date'].min()#.astype(str)\n", "max_date = input_data['order_date'].max()#.astype(str)\n", "date_range = pd.date_range(start=min_date, end=max_date)\n", "dates_mid = date_range[1:-1]\n", "df = pd.DataFrame({'order_date': dates_mid})\n", "df['week'] = (df.index // 7) + 1\n", "first_row = pd.DataFrame({'order_date': [date_range[0]], 'week': [1]})\n", "last_week_num = df['week'].max() if not df.empty else 1\n", "last_row = pd.DataFrame({'order_date': [date_range[-1]], 'week': [last_week_num]})\n", "date_df = pd.concat([first_row, df, last_row], ignore_index=True)\n", "input_data  = input_data.merge(date_df, on = ['order_date'])"]}, {"cell_type": "code", "execution_count": null, "id": "3fe2f86f-c95e-48bc-a778-a5aec556052b", "metadata": {}, "outputs": [], "source": ["mumbai_list = [5807,6435,6714,6082,4476,5472,3706,5128,2848,3310,3993,5058,4908,2800,4440,4571,4441,3417,5560,4464,1419]"]}, {"cell_type": "code", "execution_count": null, "id": "fda1177d-a8ee-4701-88da-158b27d41728", "metadata": {}, "outputs": [], "source": ["wr.s3.download(path=f\"s3://prod-dse-projects/store_ops/monthly/september/store_timings_map.pkl\", local_file=f\"store_timings_map.pkl\")\n", "closure_dict_nested = pkl.load(open(\"store_timings_map.pkl\",'rb'))"]}, {"cell_type": "code", "execution_count": null, "id": "1af39e6b-0313-4cc5-832d-ab41be5014d0", "metadata": {}, "outputs": [], "source": ["closed_stores = list(closure_dict_nested.keys())"]}, {"cell_type": "code", "execution_count": null, "id": "ea93818d-0a2d-4d2c-9672-6ed43da8318e", "metadata": {}, "outputs": [], "source": ["def data_preprocess(df):\n", "    \n", "    #df = df.copy()\n", "    grn_difficulty_flag = df['grn_difficulty_flag'].max()\n", "    outlet_id = df['outlet_id'].max()\n", "    df = df[\n", "        [\n", "            'order_date', 'hour', 'outlet_id', 'qty_to_pick',\n", "            'perishable_qty_accumulated_final', 'packaged_qty_accumulated_final',\n", "            'mavg_ppi', 'mavg_pick_util', 'mavg_iph_packaged', 'mavg_iph_perishable',\n", "            'ppi_od', 'od_util', 'od_eph', 'fixed_eph', 'od_prod',\n", "            'absent_predict', 'mh_limit', 'mh_required_for_pick_qt',\n", "            'grn_difficulty_flag', 'slot', 'od_max_evening', 'od_max_morning',\n", "            'od_max_noon', 'od_min_day', 'perishable_frozen_qty', 'pkg_window','od_max_day','week'\n", "        ]\n", "    ]\n", "\n", "    df = df.reset_index()\n", "    del(df['index'])\n", "    \n", "    df = df.sort_values(by=[\"order_date\", \"hour\"])\n", "    \n", "    ## Picking manpower required\n", "    df['mh_required_for_pick'] = (df['qty_to_pick'] * df['mavg_ppi']) / (df['mavg_pick_util'] * 3600)\n", "\n", "    ## Perishable manpower required\n", "    df['mh_required_for_perishable_putaway'] = df['perishable_frozen_qty'] / df['mavg_iph_perishable']\n", "\n", "    ## Unloading perishable manpower required\n", "    df['mh_required_for_perishable_putaway_unloading'] = (((df['perishable_qty_accumulated_final'] / 50) * 50) / 3600) / 1.5\n", "\n", "    ## Unloading packaged manpower required\n", "    df['pkg_unloading_mh'] = (((df['packaged_qty_accumulated_final'] / 25) * 50) / 3600) / 1.5\n", "    \n", "    if grn_difficulty_flag == 0:\n", "        df['pkg_unloading_mh_66'] = df['pkg_unloading_mh'] * 0.50\n", "        df['pkg_unloading_mh_33'] = df['pkg_unloading_mh'] * 0.50\n", "        df['pkg_unloading_mh_spillover'] = df['pkg_unloading_mh_33'].shift(1).fillna(0)\n", "        df['mh_required_for_packaged_putaway_unloading'] = df['pkg_unloading_mh_66'] + df['pkg_unloading_mh_spillover']\n", "\n", "        #### Removing unloading manpower during night\n", "        df['mh_required_for_packaged_putaway_unloading_plus_1'] = df['pkg_unloading_mh_33'].shift(1)\n", "        df['mh_required_for_packaged_putaway_unloading_plus_2'] = df['pkg_unloading_mh_66'].shift(2)\n", "        df['mh_required_for_packaged_putaway_unloading_plus_3'] = df['pkg_unloading_mh_33'].shift(3)\n", "        df['mh_required_for_packaged_putaway_unloading_minus_1'] = df['pkg_unloading_mh_66'].shift(-1)\n", "        df['mh_required_for_packaged_putaway_unloading_minus_2'] = df['pkg_unloading_mh_66'].shift(-2)\n", "    else: \n", "        df['mh_required_for_packaged_putaway_unloading'] = df['pkg_unloading_mh'] + (df['pkg_unloading_mh'] > 0).astype(int)\n", "        df['mh_required_for_packaged_putaway_unloading_plus_1'] = df['pkg_unloading_mh'].shift(1) * 0.66\n", "        df['mh_required_for_packaged_putaway_unloading_plus_2'] = df['pkg_unloading_mh'].shift(2) * 0.66\n", "        df['mh_required_for_packaged_putaway_unloading_plus_3'] = df['pkg_unloading_mh'].shift(3) * 0.66\n", "        df['mh_required_for_packaged_putaway_unloading_minus_1'] = df['pkg_unloading_mh'].shift(-1) * 0.66\n", "        df['mh_required_for_packaged_putaway_unloading_minus_2'] = df['pkg_unloading_mh'].shift(-2) * 0.66\n", "    \n", "    # Adjusting values for night hours\n", "    df['mh_required_for_perishable_putaway_unloading'] = np.where(\n", "        (df.hour >= 23) | (df.hour <= 4), 0, df['mh_required_for_perishable_putaway_unloading']\n", "    )\n", "    df['mh_required_for_packaged_putaway_unloading'] = np.where(\n", "        (df.hour >= 23) | (df.hour <= 4), 0, df['mh_required_for_packaged_putaway_unloading']\n", "    )\n", "    df['absent_predict'] = np.where((df['hour'] >= 23) | (df['hour'] <= 5), 0, df['absent_predict'])\n", "\n", "    for col in [\n", "        'mh_required_for_packaged_putaway_unloading_plus_1',\n", "        'mh_required_for_packaged_putaway_unloading_plus_2',\n", "        'mh_required_for_packaged_putaway_unloading_minus_1',\n", "        'mh_required_for_packaged_putaway_unloading_minus_2',\n", "        'mh_required_for_packaged_putaway_unloading_plus_3'\n", "    ]:\n", "        df[col] = df[col].fillna(0)\n", "        \n", "    \n", "        \n", "    if outlet_id not in mumbai_list :\n", "        \n", "        df['buffer'] = (\n", "            df['mh_required_for_packaged_putaway_unloading_plus_1'] +\n", "            df['mh_required_for_packaged_putaway_unloading_minus_1'] +\n", "            df['mh_required_for_packaged_putaway_unloading_plus_2']\n", "        )\n", "    else:\n", "        \n", "        df['buffer'] = (\n", "            df['mh_required_for_packaged_putaway_unloading_plus_1'] +\n", "            df['mh_required_for_packaged_putaway_unloading_minus_1'] +\n", "            df['mh_required_for_packaged_putaway_unloading_plus_2'] +\n", "            df['mh_required_for_packaged_putaway_unloading_minus_2'] +\n", "            df['mh_required_for_packaged_putaway_unloading_plus_3']\n", "    ) \n", "\n", "\n", "    \n", "    \n", "    df = df.sort_values([\"order_date\", \"hour\"])\n", "    df['row_number'] = range(1, len(df) + 1)\n", "    \n", "    ## Final perishable Putaway mh required\n", "    \n", "    def compute_putaway(row):\n", "        diff = 6 - row['hour']\n", "\n", "        if diff <= 1:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = 0\n", "\n", "        elif diff == 2:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = row[\"mh_required_for_perishable_putaway\"] * 0.5\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = row[\"mh_required_for_perishable_putaway_current_hr\"]\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = 0\n", "\n", "        else:\n", "            row[\"mh_required_for_perishable_putaway_current_hr\"] = row[\"mh_required_for_perishable_putaway\"] * 0.33\n", "            row[\"mh_required_for_perishable_putaway_spillover\"] = row[\"mh_required_for_perishable_putaway_current_hr\"]\n", "            row[\"mh_required_for_perishable_putaway_spillover_2\"] = row[\"mh_required_for_perishable_putaway_current_hr\"]  \n", "\n", "        return row\n", "    \n", "    df = df.apply(compute_putaway, axis=1)\n", "    df[\"mh_required_for_perishable_putaway_spillover\"] = (\n", "        df[\"mh_required_for_perishable_putaway_spillover\"].shift(1).fillna(0)\n", "    )\n", "    df[\"mh_required_for_perishable_putaway_spillover_2\"] = (\n", "        df[\"mh_required_for_perishable_putaway_spillover_2\"].shift(2).fillna(0)\n", "    )\n", "\n", "    ## Final Non-negotiable manpower required\n", "    df['non_nego_mh_req'] = (\n", "        df['mh_required_for_packaged_putaway_unloading'] +\n", "        df['mh_required_for_perishable_putaway_unloading'] +\n", "        df['mh_required_for_pick'] +\n", "        df['mh_required_for_perishable_putaway_current_hr'] +\n", "        df['mh_required_for_perishable_putaway_spillover'] +\n", "        df[\"mh_required_for_perishable_putaway_spillover_2\"]\n", "    )\n", "    \n", "    df['total_fnv_audit_mh'] = 0\n", "    \n", "    df['total_unloading_perishable_putaway_mh'] = (\n", "        df['mh_required_for_perishable_putaway_current_hr'] +\n", "        df['mh_required_for_perishable_putaway_spillover'] +\n", "        df[\"mh_required_for_perishable_putaway_spillover_2\"] +\n", "        df['mh_required_for_perishable_putaway_unloading'] +\n", "        df['mh_required_for_packaged_putaway_unloading']\n", "    )\n", "    \n", "    df['actual_pick_mh'] = df['mh_required_for_pick']\n", "    \n", "    df['mh_required_for_pick'] = np.where(\n", "        df['hour'].isin([6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]),\n", "        np.maximum(df['mh_required_for_pick'], df['mh_required_for_pick_qt']),\n", "        df['mh_required_for_pick']\n", "    )\n", "    \n", "    df['total_fixed_mh'] = (\n", "        df['total_fnv_audit_mh'] +\n", "        df['mh_required_for_pick'] +\n", "        df['total_unloading_perishable_putaway_mh']\n", "    )\n", "    \n", "    if outlet_id in closed_stores:\n", "        df['mh_limit'] = np.where(\n", "            df['total_fixed_mh'] > df['mh_limit'],\n", "            df['total_fixed_mh'],\n", "            df['mh_limit']\n", "        )\n", "        df['mh_limit'] = df['mh_limit'].apply(np.ceil).astype(int)\n", "    \n", "    df = df.reset_index()\n", "    del(df['index'])\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "6cbdfc91-5ade-44f6-8a8e-5ae93c0f4fdf", "metadata": {}, "outputs": [], "source": ["\n", "def optimiser(df, store, potential_shift_starts,allowed_hours, index_dict, agg_df,\n", "                     morning_agg_df, noon_agg_df, evening_agg_df,\n", "                     enforce_hc_constraint=True, pkg_window=6,\n", "                     big_M=2000, hc_max_diff=7, od_min_diff=0, od_max_diff=0):\n", "    \n", "    model = cp_model.CpModel()\n", "        \n", "    # Decision variables\n", "    x = {s: model.NewIntVar(0, big_M, f\"Shift_Start_{s}\") for s in potential_shift_starts}\n", "    y = {t: model.NewIntVar(0, big_M, f\"Hourly_Workers_{t}\") for t in range(len(df))}\n", "    z = {(k, t): model.NewIntVar(0, big_M, f\"Truck_Distribution_{k}_{t}\")\n", "         for k in df[df[\"truck_arrival\"] == 1].index\n", "         for t in range(k, min(k + df[df.index == k]['pkg_window'].iloc[0], len(df)))}\n", "    b = {s: model.NewBoolVar(f\"Shift_Active_{s}\") for s in potential_shift_starts}\n", "    od_active = {t: model.NewIntVar(0, big_M, f\"OnDemand_Workers_{t}\") for t in range(len(df))}\n", "    od_start = {t: model.NewIntVar(0, big_M, f\"od_start_{t}\") for t in range(len(df))}\n", "    ref_hour = {hour: model.NewIntVar(0, big_M, f\"ref_hour_{hour}\") for hour in allowed_hours}\n", "    \n", "    \n", "    model.Minimize(\n", "        sum(od_active[t] * df.loc[t, 'od_eph'] for t in range(len(df))) +\n", "        sum(y[t] * df.loc[t, 'fixed_eph'] for t in range(len(df)))\n", "    )\n", "    \n", "    \n", "    # Constraints\n", "    for s in potential_shift_starts:\n", "        model.Add(x[s] <= big_M * b[s])\n", "        model.Add(x[s] >= b[s])\n", "        model.Add(x[s] <= df.loc[s, \"mh_limit\"])\n", "        \n", "    for k in df[df[\"truck_arrival\"] == 1].index:\n", "        model.Add(sum(z[k, t] for t in range(k, min(k + df[df.index == k]['pkg_window'].iloc[0], len(df)))) == df.loc[k, \"mh_req\"])\n", "        \n", "        \n", "    for i in range(len(potential_shift_starts) - 1):\n", "        s = potential_shift_starts[i]\n", "        next_valid_shifts = [s_next for s_next in potential_shift_starts if s < s_next <= s + 8]\n", "        if next_valid_shifts:\n", "            model.Add(sum(b[s_next] for s_next in next_valid_shifts) >= b[s])\n", "            \n", "    for t in range(len(df)):\n", "        \n", "        model.Add(y[t] == sum(x[s] for s in potential_shift_starts if s <= t < s + 9))\n", "           \n", "        model.Add(od_active[t] == sum(od_start[k] for k in range(max(0, t - 3), t + 1) if df.loc[k, 'hour'] % 2 == 0))\n", "        \n", "        dynamic = sum(z[k, t] for k in df[df[\"truck_arrival\"] == 1].index if (k, t) in z)\n", "        fixed = df.loc[t, \"fixed_mh_req\"]\n", "        \n", "        scale = 1000\n", "        od_prod_int = int(df.loc[t, \"od_prod\"] * scale)\n", "        buffer_int = int(df.loc[t, 'buffer'] * scale)\n", "        \n", "        model.Add(y[t] * scale + od_active[t] * od_prod_int >= fixed * scale + dynamic * scale + buffer_int)\n", "\n", "        model.Add(y[t] *scale  >= fixed * scale + dynamic * scale + buffer_int - (df.loc[t, 'pick_hour'] * scale))\n", "        \n", "        if df.loc[t, 'hour'] % 2 != 0:\n", "            model.Add(od_start[t] == 0)\n", "        \n", "        if df.loc[t, 'hour'] in [22, 23, 0, 1, 2, 3, 4, 5]:\n", "            model.Add(od_active[t] == 0)\n", "            model.Add(od_start[t] == 0)\n", "        \n", "        if df.loc[t, 'hour'] <= 18:\n", "            max_pick_hour = max(df.loc[t, \"pick_hour\"], df.loc[t+1, \"pick_hour\"], df.loc[t+2, \"pick_hour\"], df.loc[t+3, \"pick_hour\"])\n", "            model.Add(od_active[t] * od_prod_int <= max_pick_hour * scale)\n", "            \n", "            \n", "    for hour in allowed_hours:\n", "        for index in index_dict[hour]:\n", "            model.Add(x[index] <= ref_hour[hour] + 1)\n", "            model.Add(x[index] >= ref_hour[hour] - 1)\n", "    \n", "    # Aggregated constraints\n", "    hc_sum = 0\n", "    for _, row in agg_df.iterrows():\n", "        min_row, max_row = row[\"min\"], row[\"max\"]\n", "        od_max_value = row['od_max_day']\n", "        store_value = row['value']\n", "        hc_sum += sum(x[s] for s in potential_shift_starts if min_row <= s <= max_row)\n", "        od_sum = sum(od_active[t] for t in range(len(df)) if min_row <= t <= max_row)\n", "        model.Add(od_sum <= od_max_value + od_max_diff)\n", "        \n", "    # if enforce_hc_constraint:\n", "    #     model.Add(hc_sum <= store_value + hc_max_diff)\n", "    \n", "    \n", "    \n", "    for df_part, label in [(morning_agg_df, \"morning\"), (noon_agg_df, \"noon\"), (evening_agg_df, \"evening\")]:\n", "        for _, row in df_part.iterrows():\n", "            min_row, max_row = row[\"min\"], row[\"max\"]\n", "            od_sum_terms = [od_active[t] for t in range(len(df)) if min_row <= t <= max_row]\n", "            model.Add(sum(od_sum_terms) <= int(row['od_value']) + od_max_diff)\n", "            \n", "    tic = time.time()\n", "    solver = cp_model.CpSolver()\n", "    solver.parameters.max_time_in_seconds = 480\n", "    status = solver.<PERSON><PERSON>(model)\n", "    \n", "    toc = time.time()\n", "    print(f\"Time:{toc - tic} Outlet_id:{store} Status:{status}\")\n", "    \n", "    if status not in (cp_model.OPTIMAL, cp_model.FEASIBLE):\n", "        print(f\"Infeasible for: {od_min_diff},{hc_max_diff},{od_max_diff}, status: {status}\")\n", "        return None, 0, status\n", "    \n", "    \n", "    df[\"reduced_shift_workers\"] = [solver.Value(x[s]) if s in x else 0 for s in df.index]\n", "    df[\"on_demand_workers\"] = [solver.Value(od_active[t]) for t in range(len(df))]\n", "    df[\"adjusted_hourly_workers\"] = [solver.Value(y[t]) for t in range(len(df))]\n", "    df[\"truck_distribution\"] = 0\n", "    for k in df[df[\"truck_arrival\"] == 1].index:\n", "        for t in range(k, min(k + df[df.index == k]['pkg_window'].iloc[0], len(df))):\n", "            df.loc[t, \"truck_distribution\"] += int(solver.Value(z[k, t]))\n", "\n", "    df[\"od_cost\"] = df[\"on_demand_workers\"] * df['od_eph']\n", "    df[\"fixed_cost\"] = df[\"adjusted_hourly_workers\"] * df['fixed_eph']\n", "    df[\"total_cost\"] = df[\"od_cost\"] + df[\"fixed_cost\"]\n", "    df[\"new_residual_mh\"] = 0\n", "\n", "\n", "    \n", "    return df, 1, status\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "af87e839-3ad5-4c4e-932b-bbb2a273b410", "metadata": {}, "outputs": [], "source": ["def calculate_weekoffs(output_v1):\n", "    \n", "    daily_mp_req = output_v1.groupby(\"order_date\").agg(mp_req_total=(\"reduced_shift_workers\",\"sum\"),\n", "                                                        od_req=(\"on_demand_workers\",\"sum\")).reset_index()\n", "\n", "    # daily_mp_req = daily_mp_req[(daily_mp_req.order_date>min(daily_mp_req['order_date'])) & \n", "    #                             (daily_mp_req.order_date<max(daily_mp_req['order_date']))]        \n", "\n", "    daily_mp_req['mp_req_total'] = round(daily_mp_req['mp_req_total'],0)\n", "    daily_mp_req['new_active_mp'] = max(daily_mp_req['mp_req_total'])\n", "    daily_mp_req['week_offs_available'] = daily_mp_req['new_active_mp']-daily_mp_req['mp_req_total']\n", "\n", "    week_off_req = max(daily_mp_req['mp_req_total'])\n", "    week_off_possible = sum(daily_mp_req['week_offs_available'])\n", "    week_offs_to_be_given = week_off_req-week_off_possible\n", "\n", "\n", "    for i in range(0,50,1):\n", "        daily_mp_req['new_active_mp_u'] = daily_mp_req['new_active_mp'] + i\n", "        daily_mp_req['week_offs_available_u'] = daily_mp_req['new_active_mp_u']-daily_mp_req['mp_req_total']\n", "        week_off_req = max(daily_mp_req['new_active_mp_u'])\n", "        week_off_possible = sum(daily_mp_req['week_offs_available_u'])\n", "        week_offs_to_be_given = week_off_req-week_off_possible\n", "        print(\"added mp: \", i, \"new mp & week offs req: \", week_off_req, \"week offs possible: \",week_off_possible, \"extra need week offs: \",week_offs_to_be_given)\n", "        if week_offs_to_be_given<=0:\n", "            break\n", "\n", "    daily_mp_req = daily_mp_req.sort_values(by=\"order_date\",ascending=False).reset_index()\n", "\n", "    if i==0:\n", "        daily_mp_req = daily_mp_req.sort_values(by=\"week_offs_available_u\", ascending=False)\n", "        # Initialize allocation column\n", "        daily_mp_req[\"week_off_allocated\"] = 0\n", "        remaining_week_off_req = week_off_req\n", "\n", "        # Iterate and allocate week-offs\n", "        for i, row in daily_mp_req.iterrows():\n", "            max_possible = math.floor((min(row[\"week_offs_available_u\"], remaining_week_off_req))*(week_off_req/week_off_possible))\n", "            daily_mp_req.at[i, \"week_off_allocated\"] = max_possible\n", "            remaining_week_off_req -= max_possible\n", "\n", "            # Stop if fully allocated\n", "            if remaining_week_off_req <= 0:\n", "                # daily_mp_req['week_offs_available_u'] = daily_mp_req.at[i, \"week_off_allocated\"]\n", "                break\n", "\n", "        daily_mp_req['week_offs_available_u'] = daily_mp_req[\"week_off_allocated\"]\n", "        # print(\"executed\")\n", "        tg=(sum(daily_mp_req['week_offs_available_u']) - max(daily_mp_req['new_active_mp_u']))*(-1)\n", "        # Initialize cumulative sum\n", "        cumulative_sum = 0\n", "\n", "        # Iterate over rows and add 1 to 'week_offs_available_u' until the target sum is reached\n", "        for i in range(len(daily_mp_req)):\n", "            if cumulative_sum >= tg:\n", "                break\n", "            # Check the condition\n", "            if daily_mp_req.loc[i, 'week_offs_available_u'] < daily_mp_req.loc[i, 'week_offs_available']:\n", "                # Add 1 to 'week_offs_available_u'\n", "                daily_mp_req.loc[i, 'week_offs_available_u'] += 1\n", "                # Update the cumulative sum\n", "                cumulative_sum += 1\n", "\n", "        daily_mp_req['final_mp_at_store'] = daily_mp_req['new_active_mp_u']-daily_mp_req['week_offs_available_u']\n", "\n", "    else:\n", "        surplus_week_offs = sum(daily_mp_req['week_offs_available_u']) - max(daily_mp_req['new_active_mp_u'])\n", "        daily_mp_req['final_mp_at_store'] = daily_mp_req['new_active_mp_u']-daily_mp_req['week_offs_available_u']\n", "        if surplus_week_offs > 0:\n", "            daily_mp_req.loc[daily_mp_req.index[int(surplus_week_offs*-1):], 'week_offs_to_remove'] = 1\n", "        else:\n", "            daily_mp_req.loc[daily_mp_req.index[int(surplus_week_offs*-1):], 'week_offs_to_remove'] = 0\n", "        daily_mp_req['week_offs_to_remove'] = daily_mp_req['week_offs_to_remove'].fillna(0)\n", "        daily_mp_req['final_mp_at_store'] = daily_mp_req['final_mp_at_store']+daily_mp_req['week_offs_to_remove']\n", "        daily_mp_req['week_offs_available_u'] = daily_mp_req['week_offs_available_u']-daily_mp_req['week_offs_to_remove']\n", "\n", "    return  daily_mp_req"]}, {"cell_type": "code", "execution_count": null, "id": "c9f535d4-5f9d-4ec9-ba67-d6ee99d0ddf9", "metadata": {}, "outputs": [], "source": ["def process_store(store, data):\n", "    print(f\"\\nstarting store: {store}\")\n", "    \n", "    #optimizer_data = \n", "    df = data_preprocess(data.copy())\n", "    \n", "    if store in closed_stores:\n", "        hr_to_remove = closure_dict_nested[store]['hours']\n", "        df = df[~df.hour.isin(hr_to_remove)]\n", "\n", "\n", "    #df = optimizer_data\n", "    df = df[df.row_number >= 6].reset_index(drop=True)\n", "    df.insert(0, 'RowNumber', range(0, len(df)))\n", "    enforce_hc_constraint = True\n", "    \n", "    if store in closed_stores:\n", "        allowed_hours = closure_dict_nested[store]['allowed_hours']\n", "        potential_shift_starts = list(df[df.hour.isin(allowed_hours)]['RowNumber'])\n", "        \n", "    else:\n", "        allowed_hours = [4,5,6,7,8,9,12,13,14,15,20,21,22,23]\n", "        potential_shift_starts = list(df[df.hour.isin(allowed_hours)]['RowNumber'])\n", "        \n", "    \n", "    df[['od_max_day','od_max_morning','od_max_noon','od_max_evening']] = df[['od_max_day','od_max_morning','od_max_noon','od_max_evening']].astype(int)\n", "\n", "    agg_df = (\n", "        df.groupby(\"order_date\")\n", "          .agg({\n", "              \"RowNumber\": [\"min\", \"max\"],\n", "              \"od_max_day\": \"max\"\n", "          })\n", "          .reset_index()\n", "    )\n", "    agg_df.columns = ['order_date', 'min', 'max', 'od_max_day']\n", "    agg_df = agg_df[(agg_df.order_date != agg_df.order_date.min()) & \n", "                    (agg_df.order_date != agg_df.order_date.max())]\n", "   # display(agg_df)\n", "    try:\n", "        agg_df['value'] = hc_df[hc_df.outlet_id == store]['value'].iloc[0]\n", "    except Exception:\n", "        agg_df['value'] = 1000\n", "        enforce_hc_constraint = False\n", "\n", "    index_dict = {\n", "        hour: list(df[(df.hour == hour) &\n", "                      (df.order_date != df.order_date.min()) &\n", "                      (df.order_date != df.order_date.max())].index)\n", "        for hour in allowed_hours\n", "    }\n", "    \n", "    \n", "    morning_agg_df = ( \n", "                df[df.slot == 'morning'].groupby([\"order_date\"])\n", "                     .agg({\n", "                          \"RowNumber\": [\"min\", \"max\"],\n", "                         \"od_max_morning\":\"max\"\n", "                         \n", "                     })\n", "                       .reset_index()\n", "                ).rename(columns={'od_max_morning':'od_value'})\n", "\n", "    morning_agg_df.columns = ['order_date', 'min', 'max', 'od_value']\n", "    #display(morning_agg_df)\n", "    \n", "    noon_agg_df = ( \n", "            df[df.slot == 'noon'].groupby([\"order_date\"])\n", "                 .agg({\n", "                      \"RowNumber\": [\"min\", \"max\"],\n", "                     \"od_max_noon\":\"max\"\n", "\n", "                 })\n", "                   .reset_index()\n", "            ).rename(columns={'od_max_noon':'od_value'})\n", "    noon_agg_df.columns = ['order_date', 'min', 'max', 'od_value']\n", "    #display(noon_agg_df)\n", "    \n", "    evening_agg_df = ( \n", "        df[df.slot == 'evening'].groupby([\"order_date\"])\n", "             .agg({\n", "                  \"RowNumber\": [\"min\", \"max\"],\n", "                 \"od_max_evening\":\"max\"\n", "\n", "             })\n", "               .reset_index()\n", "        ).rename(columns={'od_max_evening':'od_value'})\n", "    \n", "    evening_agg_df.columns = ['order_date', 'min', 'max', 'od_value']\n", "\n", "    #display(evening_agg_df)\n", "\n", "\n", "    df['pick_hour'] = df['mh_required_for_pick'].apply(np.round).astype(int)\n", "    df['total_fixed_mh_temp_per'] = np.where(df['total_fixed_mh']>0,(df['total_fixed_mh']-np.floor(df['total_fixed_mh']))/df['total_fixed_mh'],0)\n", "    df['total_fixed_mh'] = np.where(df['total_fixed_mh_temp_per']>0.05,np.ceil(df['total_fixed_mh']),np.floor(df['total_fixed_mh']))\n", "    df['fixed_mh_req'] = df['total_fixed_mh'].apply(np.ceil).astype(int)\n", "    df['truck_arrival'] = np.where(df['packaged_qty_accumulated_final'] > 0, 1, 0)\n", "    df['mavg_iph_packaged'] = df['mavg_iph_packaged'].astype(int)\n", "    df['mh_req'] = round(df['packaged_qty_accumulated_final'] / df['mavg_iph_packaged'], 1)\n", "    df['mh_req'] = np.round(df['mh_req']).astype(int)\n", "    df['od_prod'] = np.round(df['od_prod'],2)\n", "    df['buffer'] = np.round(df['buffer'],2)\n", "    df['od_eph'] = df['od_eph'].astype(int)\n", "    df['fixed_eph'] = df['fixed_eph'].astype(int)\n", "    df['mh_limit'] = df['mh_limit'].astype(int)\n", "    #print(od_delta,od_delta//2)\n", "    \n", "    potential_shift_starts = list(df[df.hour.isin([4,5,6,7,8,9,12,13,14,15,20,21,22,23])]['RowNumber'])\n", "\n", "    try:\n", "        \n", "        od_min_diffs = [0, 10, 20, 40, 60]\n", "        hc_max_diffs = [0, 7 , 14, 21, 28, 35, 42, 50, 75,100,200]\n", "        od_max_diffs = [  ]  \n", "\n", "        success = False \n", "\n", "        res_df, is_success, status = optimiser(\n", "                    df,\n", "                    store,\n", "                    potential_shift_starts,\n", "                    allowed_hours,\n", "                    index_dict,\n", "                    agg_df,\n", "                    morning_agg_df,\n", "                    noon_agg_df,\n", "                    evening_agg_df,\n", "                    hc_max_diff=0,\n", "                    enforce_hc_constraint=enforce_hc_constraint,\n", "                    od_min_diff=0,\n", "                )\n", "        #print( is_success)\n", "        \n", "        if is_success:\n", "            df = res_df\n", "            output_v1 = df[['order_date', 'hour', 'packaged_qty_accumulated_final', 'mavg_iph_packaged',\n", "                            'total_fnv_audit_mh', 'mh_required_for_pick', 'total_unloading_perishable_putaway_mh',\n", "                            'total_fixed_mh', 'truck_distribution', 'truck_arrival', 'mh_req',\n", "                            'reduced_shift_workers', 'on_demand_workers', 'adjusted_hourly_workers',\n", "                            'new_residual_mh', 'od_cost', 'fixed_cost', 'total_cost', 'od_prod', 'actual_pick_mh',\n", "                            'buffer','absent_predict', 'week','mh_required_for_packaged_putaway_unloading','mh_required_for_perishable_putaway_unloading']]\n", "            output_v1 = output_v1.copy()\n", "   \n", "            output_v1['fixed_mh_allocated'] = output_v1['adjusted_hourly_workers']\n", "            output_v1['final_mh_req'] = output_v1['total_fixed_mh'] + output_v1['truck_distribution']\n", "            output_v1['shift_start_raw'] = output_v1['reduced_shift_workers']\n", "            \n", "            \n", "            if store in closed_stores:\n", "                output_v1['reduced_shift_workers'] = np.where(output_v1['hour'].isin([closure_dict_nested[store]['st'],14]), output_v1['reduced_shift_workers']+1,output_v1['reduced_shift_workers'] )\n", "                output_v1['adjusted_hourly_workers'] = np.where((output_v1['hour']>=closure_dict_nested[store]['st']) & (output_v1['hour']<=closure_dict_nested[store]['st']+8) , output_v1['adjusted_hourly_workers']+1,output_v1['adjusted_hourly_workers'] )\n", "                output_v1['adjusted_hourly_workers'] = np.where((output_v1['hour']>=14) & (output_v1['hour']<=22) , output_v1['adjusted_hourly_workers']+1,output_v1['adjusted_hourly_workers'] )\n", "                \n", "                output_v1['fnv_mh'] = np.where((output_v1['hour']>=closure_dict_nested[store]['st']) & (output_v1['hour']<=closure_dict_nested[store]['st']+8) , 1 , 0)\n", "                output_v1['fnv_mh'] = np.where((output_v1['hour']>=14) & (output_v1['hour']<=22) , output_v1['fnv_mh']+1, output_v1['fnv_mh'] )\n", "                \n", "            else:\n", "                output_v1['reduced_shift_workers'] = np.where(output_v1['hour'].isin([5,14]), output_v1['reduced_shift_workers']+1,output_v1['reduced_shift_workers'] )\n", "                output_v1['adjusted_hourly_workers'] = np.where((output_v1['hour']>=5) & (output_v1['hour']<=22) , output_v1['adjusted_hourly_workers']+1,output_v1['adjusted_hourly_workers'] )\n", "                output_v1['fnv_mh'] = np.where((output_v1['hour']>=5) & (output_v1['hour']<=22) , 1 , 0 )\n", "        \n", "           \n", "            \n", "            daily_agg = output_v1.groupby(\"order_date\").agg(\n", "                    fixed_mh_allocated=(\"fixed_mh_allocated\", \"sum\"),\n", "                    od_req=(\"on_demand_workers\", \"sum\"),\n", "                    final_mh_req=(\"final_mh_req\", \"sum\"),\n", "                    actual_pick_mh = (\"actual_pick_mh\",\"sum\"),\n", "                    picking_mh = (\"mh_required_for_pick\",\"sum\"),\n", "                    absent = (\"absent_predict\",\"max\")\n", "                ).reset_index()\n", "\n", "            def weighted_avg(group):\n", "                total_weight = group[\"mh_required_for_pick\"].sum()\n", "                if total_weight == 0:\n", "                    return np.nan\n", "                return (group[\"od_prod\"] * group[\"mh_required_for_pick\"]).sum() / total_weight\n", "\n", "            avg_od_prod = (\n", "                output_v1.groupby(\"order_date\")\n", "                .apply(weighted_avg)\n", "                .reset_index(name=\"avg_od_prod\")\n", "            )\n", "            \n", "            daily_agg = pd.merge(daily_agg, avg_od_prod, on=\"order_date\", how=\"left\")\n", "\n", "            daily_agg['audit_req'] = daily_agg['fixed_mh_allocated']*0.20 \n", "            daily_agg['breaks_mh_req'] = daily_agg['fixed_mh_allocated']*0.06\n", "            daily_agg['fnv_req'] = daily_agg['fixed_mh_allocated']*0.05\n", "            daily_agg['absenteeism'] = np.maximum(0, ( daily_agg['fixed_mh_allocated'] * daily_agg['absent']/100 ) - (daily_agg['picking_mh'] - daily_agg['actual_pick_mh']) )\n", "            daily_agg['resisdual'] = daily_agg['fixed_mh_allocated']+(daily_agg['od_req'] *  daily_agg['avg_od_prod'] ) + (18-daily_agg['fnv_req'])   - daily_agg['final_mh_req']-daily_agg['audit_req']-daily_agg['breaks_mh_req'] - daily_agg['absenteeism']\n", "            daily_agg['addon_md_base'] = daily_agg['resisdual'].abs() / 9\n", "            daily_agg['addon_md_need'] = np.round(daily_agg['addon_md_base'])\n", "            output_v1['mean_shift_workers'] = (output_v1.groupby('hour')['reduced_shift_workers'].transform('mean'))\n", "            output_v1['addon_assigned'] = 0\n", "            to_allocate = daily_agg.set_index('order_date')['addon_md_need'].to_dict()\n", "            mask_valid = output_v1['reduced_shift_workers'] > 0\n", "\n", "            output_v1['addon_md_need'] = np.nan\n", "            for date, n_need in to_allocate.items():\n", "                n = int(n_need)\n", "                if n <= 0:\n", "                    continue\n", "\n", "                idxs = output_v1.index[(output_v1['order_date'] == date) & \n", "                                       (output_v1['reduced_shift_workers'] > 0) & \n", "                                       (output_v1['hour'] >= 6) & \n", "                                       (output_v1['hour'] <= 15)]\n", "\n", "                for _ in range(n):\n", "                    current = output_v1.loc[idxs, ['reduced_shift_workers','addon_assigned','mean_shift_workers']].copy()\n", "                    current['total']     = current['reduced_shift_workers'] + current['addon_assigned']\n", "                    current['deviation'] = (current['total'] - current['mean_shift_workers'])/current['mean_shift_workers']\n", "\n", "                    # Find the index (in output_v1) of the hour with the lowest deviation\n", "                    worst_idx = current['deviation'].idxmin()\n", "                    output_v1.at[worst_idx, 'addon_assigned'] += 1\n", "\n", "                    # Increment just that one\n", "                    output_v1.at[worst_idx, 'addon_md_need'] = 1\n", "                    output_v1.at[worst_idx, 'reduced_shift_workers'] += 1\n", "                    output_v1['addon_md_need'] = output_v1['addon_md_need'].ffill(limit=8).fillna(0)\n", "                    output_v1['adjusted_hourly_workers'] = output_v1['adjusted_hourly_workers']+output_v1['addon_md_need']\n", "                    output_v1['addon_md_need'] = np.nan\n", "                    \n", "            raw_df = output_v1[(output_v1.order_date>min(output_v1['order_date'])) & \n", "                                        (output_v1.order_date<max(output_v1['order_date']))]\n", "            \n", "            all_dfs = []\n", "            for week_no in range(1,6):\n", "                print(f\"roster week:{week_no},{raw_df[raw_df.week  == week_no ].shape[0]}\")\n", "                roster_df = calculate_weekoffs(raw_df[raw_df.week  == week_no ].copy())\n", "                roster_df['week'] = week_no\n", "                print(f\"roster_output:{roster_df.shape[0]}\")\n", "                all_dfs.append(roster_df)\n", "\n", "        \n", "            daily_mp_req = pd.concat(all_dfs, ignore_index = True)\n", "            output_v1['outlet_id'] = store\n", "            daily_mp_req['outlet_id'] = store\n", "            output_v1['eph_od'] = df['od_eph']\n", "            output_v1['eph_fixed'] = df['fixed_eph']\n", "        \n", "\n", "            print(\"outlet_id: \", store,\" Done\")\n", "\n", "            return (store, output_v1,daily_mp_req, None, status)\n", "        else:\n", "            return (store, None, None, \"Timeout or infeasible\", status)\n", "    except Exception as e:\n", "        return (store, None,None, str(e),None)"]}, {"cell_type": "code", "execution_count": null, "id": "79eb9703-b718-4e9e-8619-772d353e5762", "metadata": {}, "outputs": [], "source": ["tic = time.time()\n", "store_data_dict = {store: input_data[input_data.outlet_id == store].copy() for store in store_list}\n", "\n", "output1_final = pd.DataFrame()\n", "output2_final = pd.DataFrame()\n", "results = []\n", "skipped_stores = []\n", "status_map= {}\n", "MAX_WORKERS = 30\n", "\n", "with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:\n", "    futures = {\n", "        executor.submit(process_store, store, store_df): store\n", "        for store, store_df in store_data_dict.items()\n", "    }\n", "\n", "    for future in as_completed(futures):\n", "        store = futures[future]\n", "        try:\n", "            store, output_v1, daily_mp_req, error, status = future.result()\n", "            status_map[store] = status\n", "            if error:\n", "                print(f\"Store {store} skipped due to error: {error}\")\n", "                skipped_stores.append(store)\n", "            else:\n", "                output1_final = pd.concat([output1_final, output_v1], ignore_index=True)\n", "                output2_final = pd.concat([output2_final, daily_mp_req], ignore_index=True)\n", "                #output_v1.to_parquet(f\"hourly/output_v1_{store}.parquet\")\n", "                #daily_mp_req.to_parquet(f\"roster/daily_mp_req_{store}.parquet\")\n", "                # output1_final.to_csv(\"output1_final.csv\")\n", "                # output2_final.to_csv(\"output2_final.csv\")\n", "                results.append(store)\n", "        except Exception as e:\n", "            print(f\"Unhandled error in store {store}: {e}\")\n", "            skipped_stores.append(store)\n", "\n", "toc = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "c414b4e7-6f2c-48dd-80a1-5f199e17cce6", "metadata": {}, "outputs": [], "source": ["output1_final = output1_final[\n", "    [\n", "        'outlet_id', 'order_date', 'hour',\n", "        'mh_required_for_pick', 'total_unloading_perishable_putaway_mh', \n", "        'total_fixed_mh', 'truck_distribution', 'mh_req',\n", "        'reduced_shift_workers', 'on_demand_workers', \n", "        'adjusted_hourly_workers', 'final_mh_req', 'od_prod','actual_pick_mh','fixed_mh_allocated',\n", "        'shift_start_raw','buffer','fnv_mh','absent_predict','week','mh_required_for_packaged_putaway_unloading',\n", "        'mh_required_for_perishable_putaway_unloading'\n", "        \n", "    ]\n", "]\n", "\n", "output1_final.columns = [\n", "    'outlet_id', 'order_date', 'hour',\n", "    'picking_mh', 'unloading_perishable_putaway_mh', \n", "    'non_nego_mh', 'pckg_mh_allocation', 'pkg_mh_required',\n", "    'employee_starting_shift', 'od_mh', 'fixed_mh',\n", "    'req_mh', 'od_prod','actual_pick_mh','fixed_mh_allocated','shift_start_raw',\n", "    'truck_delay_buffer','fnv_mh','absent_predict', 'week','mh_required_for_packaged_putaway_unloading',\n", "    'mh_required_for_perishable_putaway_unloading'\n", "]\n", "\n", "\n", "output1_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b8a17ea9-d7f4-4bb0-8fac-66af0a8d2936", "metadata": {}, "outputs": [], "source": ["output2_final = output2_final[['outlet_id','order_date', 'mp_req_total', 'od_req','new_active_mp_u', 'week_offs_available_u','final_mp_at_store','week']]\n", "output2_final.columns = ['outlet_id','order_date', 'fixed_md_required', 'od_mh_required','active_base_in_week', 'week_offs','final_mp_at_store_after_weekoffs','week']\n", "output2_final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "57ceb71d-ba73-4ac2-8456-7f6fcb4161e6", "metadata": {}, "outputs": [], "source": ["skipped_stores"]}, {"cell_type": "code", "execution_count": null, "id": "db4fdf5a-96c9-446a-aafa-e096ee5186e6", "metadata": {}, "outputs": [], "source": ["input_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8e1cad44-9602-46ca-8355-f30ceea8ad10", "metadata": {}, "outputs": [], "source": ["planner_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "af1c6b0f-c0c4-492e-9833-c644f476c957", "metadata": {}, "outputs": [], "source": ["df  = input_data.copy()\n", "hourly_df = output1_final.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "568cb53c-5433-4c03-bf6a-5e869063093a", "metadata": {}, "outputs": [], "source": ["### need to change columns name"]}, {"cell_type": "code", "execution_count": null, "id": "099f1cb2-1725-401c-b1c6-1475ee237fd4", "metadata": {}, "outputs": [], "source": ["df = df.merge(hourly_df[['order_date','outlet_id','hour','picking_mh','od_mh','pckg_mh_allocation','unloading_perishable_putaway_mh','mh_required_for_perishable_putaway_unloading','mh_required_for_packaged_putaway_unloading','truck_delay_buffer']])\n", "df['od_picking_time']  = df['od_mh'] * df['od_prod']\n", "df['fixed_picking_time'] = np.maximum(0,df['picking_mh'] -  df['od_picking_time'])\n", "df['ppi_numerator'] = df['mavg_ppi'] * df['qty_to_pick']\n", "df['util_numerator'] = df['mavg_pick_util'] * df['qty_to_pick']"]}, {"cell_type": "code", "execution_count": null, "id": "f49d7780-6c4f-4e22-bbdb-1eea5b17c08b", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "99491bca-1fcf-448d-8c2f-d9f0d3dc1ed3", "metadata": {}, "outputs": [], "source": ["max_date = df['order_date'].max()\n", "min_date = df['order_date'].min()\n", "df = df[(df.order_date>min_date) & (df.order_date<max_date)]"]}, {"cell_type": "code", "execution_count": null, "id": "eacebb66-0765-490d-89ec-c78e8f476f51", "metadata": {}, "outputs": [], "source": ["df['fixed_item'] = (df['qty_to_pick']* (df['fixed_picking_time']/df['picking_mh']))\n", "df['od_item'] = (df['qty_to_pick']* (df['od_picking_time']/df['picking_mh']))\n"]}, {"cell_type": "code", "execution_count": null, "id": "fc35de29-f190-4de6-aaa7-4bb5014bcb6f", "metadata": {}, "outputs": [], "source": ["contri_df = df.groupby(['outlet_id','week_start_date'])[['qty_to_pick','fixed_item','od_item']].sum().reset_index() \n", "contri_df['od_contribution_pct'] = np.round(100.0 * contri_df['od_item']/(contri_df['od_item']+contri_df['fixed_item']))\n", "contri_df['qty_ordered_od'] = np.round(contri_df['od_item'])\n", "contri_df['qty_ordered_fixed'] = np.round(contri_df['fixed_item'])\n", "contri_df['total_items_quantity_ordered'] = contri_df['qty_to_pick']\n", "contri_df = contri_df[['outlet_id','week_start_date','od_contribution_pct','qty_ordered_od','qty_ordered_fixed','total_items_quantity_ordered']]\n", "\n", "contri_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8325229b-9b88-4c70-af7b-17b75d7814de", "metadata": {}, "outputs": [], "source": ["iph_df = df.groupby(['outlet_id','week_start_date']).agg(\n", "    forecasted_hourly_packaged = ('packaged_qty_accumulated_final',sum),\n", "    forecasted_hourly_perishable = ('perishable_frozen_qty',sum),\n", "    packaged_iph = ('mavg_iph_packaged',max),\n", "    perishable_iph = ('mavg_iph_perishable',max),\n", "    unloading_perishable = ('mh_required_for_perishable_putaway_unloading',sum),\n", "    unloading_packaged = ('mh_required_for_packaged_putaway_unloading',sum),\n", "    truck_delay_buffer = ('truck_delay_buffer',sum)\n", "    ).reset_index()\n", "iph_df['putaway_qty'] = iph_df['forecasted_hourly_packaged'] + iph_df['forecasted_hourly_perishable']\n", "iph_df['putter_active_time_mins'] = 60*((iph_df['forecasted_hourly_packaged']/iph_df['packaged_iph']) + (iph_df['forecasted_hourly_perishable']/iph_df['perishable_iph']) + iph_df['unloading_perishable'] + iph_df['unloading_packaged'] + iph_df['truck_delay_buffer']  )\n", "iph_df['items_put_away_per_hour'] = np.round(60*iph_df['putaway_qty']/iph_df['putter_active_time_mins'])\n", "iph_df['putter_active_time_mins'] = np.round(iph_df['putter_active_time_mins'])\n", "iph_df = iph_df[['outlet_id','week_start_date','putaway_qty','putter_active_time_mins','items_put_away_per_hour']]\n", "iph_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0584be28-f344-43af-a611-9f9c79c35f06", "metadata": {}, "outputs": [], "source": ["final_df = contri_df.merge(iph_df,on = ['outlet_id','week_start_date'])\n", "final_df = final_df.merge(planner_df,on = ['outlet_id','week_start_date'], how = 'left')\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b9130864-f32c-491e-a8d4-083b9d71a6c6", "metadata": {}, "outputs": [], "source": ["curr_metric_df = df[['outlet_id','week_start_date','current_ft_ppi','current_ft_util']].drop_duplicates().reset_index(drop= True)\n", "curr_metric_df['current_ft_util'] = curr_metric_df['current_ft_util'] * 100.0\n", "final_df = final_df.merge(curr_metric_df, on=['outlet_id','week_start_date'],how='left')\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4074f22f-8dfc-491c-9626-70fb8bf37151", "metadata": {}, "outputs": [], "source": ["final_df['ppi'] = np.where(final_df['input_ppi'].isnull(),final_df['current_ft_ppi'],final_df['input_ppi'])\n", "final_df['picker_util_fixed_wt_avg'] = np.where(final_df['input_ft_picker_util'].isnull(),final_df['current_ft_util'],final_df['input_ft_picker_util'])\n", "final_df['total_picking_time_sec'] = final_df['ppi'] * final_df['total_items_quantity_ordered']\n", "final_df['picker_util_numerator_fixed'] = final_df['picker_util_fixed_wt_avg'] * final_df['total_items_quantity_ordered']\n", "final_df = final_df.drop(columns = ['input_ppi','input_iph','input_ft_picker_util','current_ft_ppi','current_ft_util'])"]}, {"cell_type": "code", "execution_count": null, "id": "f3f7cfa1-1853-41ac-b16b-fa2a3caa55eb", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "d819b7f7-d10a-454e-a31e-7f0d4f5c3266", "metadata": {}, "outputs": [], "source": ["week_df = df[['week','week_start_date']].drop_duplicates()\n", "hc_df = output2_final.merge(week_df, on = ['week'])\n", "hc_df  = hc_df.groupby(['outlet_id','week_start_date'])[['active_base_in_week']].max().reset_index().rename(columns = {'active_base_in_week':'headcount'})\n", "hc_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "39f8cdb7-687a-42c5-9e45-ae9d72be8b38", "metadata": {}, "outputs": [], "source": ["final_df = final_df.merge(hc_df, on =['outlet_id','week_start_date'])\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e60b531b-9fba-4d4e-a071-98825c566280", "metadata": {}, "outputs": [], "source": ["final_df['updated_ts'] = update_ts_ist\n", "final_df = final_df.rename(columns = {'outlet_id':'entity_id'})"]}, {"cell_type": "code", "execution_count": null, "id": "eb5034fe-5c6e-48bb-a757-65e665431f6b", "metadata": {"tags": []}, "outputs": [], "source": ["final_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "43517424-f837-4645-a62c-9fe8a9a88cd1", "metadata": {}, "outputs": [], "source": ["assert final_df.shape[0] == final_df.entity_id.nunique() * 5, \"Shape mismatch error\"\n", "assert final_df.isna().sum().sum() == 0, \"Null error\""]}, {"cell_type": "code", "execution_count": null, "id": "00237dd4-fd85-451a-b670-af56cda5e6fb", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"entity_id\",\n", "    \"week_start_date\",\n", "    \"updated_ts\",\n", "    \"ppi\",\n", "    \"total_picking_time_sec\",\n", "    \"total_items_quantity_ordered\",\n", "    \"picker_util_fixed_wt_avg\",\n", "    \"picker_util_numerator_fixed\",\n", "    \"od_contribution_pct\",\n", "    \"qty_ordered_od\",\n", "    \"qty_ordered_fixed\",\n", "    \"items_put_away_per_hour\",\n", "    \"putaway_qty\",\n", "    \"putter_active_time_mins\",\n", "    \"headcount\"\n", "]\n", "final_df = final_df[columns]\n", "final_df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bdff8dd2-2945-459e-a643-723306026458", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_weekly_temp_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the fixed employee\"},\n", "        {\n", "            \"name\": \"total_picking_time_sec\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"total_picking_time_fixed \",\n", "        },\n", "        {\"name\": \"total_items_quantity_ordered\", \"type\": \"REAL\", \"description\": \"total_items_quantity_ordered\"},\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" sum-product of qty x fixed_util\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_numerator_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"sum-product of the util and qty.\"\n", "        },\n", "        {\"name\": \"items_put_away_per_hour\", \"type\": \"REAL\", \"description\": \"items_put_away_per_hour\"},\n", "        {\n", "            \"name\": \"putaway_qty\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"putaway_qty\",\n", "        },\n", "        {\"name\": \"putter_active_time_mins\", \"type\": \"REAL\", \"description\": \"putter_active_time_mins\"},\n", "        {\n", "            \"name\": \"od_contribution_pct\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"od_contribution_pct\",\n", "        },\n", "        {\"name\": \"qty_ordered_od\", \"type\": \"REAL\", \"description\": \"qty_ordered_od\"},\n", "        {\n", "            \"name\": \"qty_ordered_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"qty_ordered_fixed\",\n", "        },{\n", "            \"name\": \"headcount\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" active base for the week.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2be546bf-2353-4eef-9e31-4e364cc13b2f", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=final_df, **metric_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "362a5d5d-8015-4ae8-a04c-bcc18339bc30", "metadata": {}, "outputs": [], "source": ["daily_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_daily_temp_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"mandays_fixed\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"manhours_od\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"headcount\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount available after week_offs  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\",\"date\", \"entity_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "57fc1ebd-fc35-47b4-9cd8-c5d5b235ac8e", "metadata": {}, "outputs": [], "source": ["week_df = df[['week','week_start_date']].drop_duplicates()\n", "hc_df = output2_final.merge(week_df, on = ['week'])"]}, {"cell_type": "code", "execution_count": null, "id": "4f9556e5-77f2-4d79-92be-e840a7ce2bd4", "metadata": {}, "outputs": [], "source": ["hc_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "53eca5e3-b4da-491d-86d0-f93fde92fcfe", "metadata": {}, "outputs": [], "source": ["hc_df[\"updated_ts\"] = update_ts_ist\n", "hc_df['week_start_date'] = pd.to_datetime(hc_df['week_start_date'])\n", "\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"fixed_md_required\",\n", "    \"od_mh_required\",\n", "    \"final_mp_at_store_after_weekoffs\",\n", "    \"week_offs\",\n", "    \"updated_ts\",\n", "]\n", "hc_df = hc_df[columns]\n", "hc_df = hc_df.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"outlet_id\":\"entity_id\",\n", "        \"fixed_md_required\": \"mandays_fixed\",\n", "        \"od_mh_required\": \"manhours_od\",\n", "        \"final_mp_at_store_after_weekoffs\": \"headcount\"\n", "    }\n", ")\n", "hc_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "34752311-3adf-44bd-a5ed-d26cc67e1cf6", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=hc_df, **daily_kwargs)"]}, {"cell_type": "markdown", "id": "8322de48-b942-452d-a2e9-d56a90103574", "metadata": {"tags": []}, "source": ["## Getting DS metrics"]}, {"cell_type": "code", "execution_count": null, "id": "6ae9d3a9-329b-43a5-8bdc-fbe4be5a9ac5", "metadata": {}, "outputs": [], "source": ["input_df = input_data.copy()\n", "hourly_df = output1_final.copy()\n", "\n", "input_df = input_df.merge(hourly_df[['order_date','outlet_id','hour','picking_mh','od_mh','pckg_mh_allocation','unloading_perishable_putaway_mh','actual_pick_mh','truck_delay_buffer']])\n", "input_df['od_picking_time']  = input_df['od_mh'] * input_df['od_prod']\n", "input_df['fixed_picking_time'] = input_df['picking_mh'] -  input_df['od_picking_time']\n", "input_df['fixed_picking_time'] = np.where(input_df['fixed_picking_time']<0,0,input_df['fixed_picking_time'])\n", "input_df['new_util'] = (input_df['mavg_pick_util']*input_df['actual_pick_mh'])/input_df['picking_mh']\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d08fed8a-25b7-46d3-84d1-04ea8a4c7480", "metadata": {}, "outputs": [], "source": ["min_date = input_df['order_date'].min()\n", "max_date = input_df['order_date'].max()\n", "df = input_df[(input_df.order_date> min_date) & (input_df.order_date< max_date)].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "3bc38321-fc61-4279-8718-ed6b22747442", "metadata": {}, "outputs": [], "source": ["df['fixed_item'] = df['qty_to_pick']* (df['fixed_picking_time']/df['picking_mh'])\n", "df['od_item'] = df['qty_to_pick']* (df['od_picking_time']/df['picking_mh'])\n", "df['fixed_num'] = df['fixed_item'] * df['mavg_ppi']\n", "df['od_num'] = df['od_item'] * df['ppi_od']\n", "agg_df = df.groupby(['order_date','outlet_id','week'])[['fixed_num','od_num','qty_to_pick','fixed_item','od_item']].sum().reset_index()\n", "\n", "agg_df['day_ppi'] = (agg_df['od_num'] + agg_df['fixed_num'])/agg_df['qty_to_pick']\n", "agg_df['day_ft_ppi'] = (agg_df['fixed_num'])/agg_df['fixed_item']\n", "agg_df['day_od_ppi'] = (agg_df['od_num'])/agg_df['od_item']\n", "\n", "\n", "result_df = agg_df.groupby(['outlet_id','week'])[['day_ppi','day_ft_ppi','day_od_ppi']].mean().reset_index()\n", "result_df['store_ppi'] = (result_df['day_ppi']).round(1)\n", "result_df['ft_ppi'] = (result_df['day_ft_ppi']).round(1)\n", "result_df['od_ppi'] = (result_df['day_od_ppi']).round(1)\n", "ppi_df = result_df[['week','outlet_id','store_ppi','ft_ppi','od_ppi']]\n", "ppi_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "33c098af-7a01-4010-bc56-95548099ab1f", "metadata": {}, "outputs": [], "source": ["contri_df = df.groupby(['outlet_id','week'])[['fixed_item','od_item']].sum().reset_index()\n", "contri_df['planned_od_item_share'] = contri_df['od_item']/(contri_df['od_item']+contri_df['fixed_item'])\n", "contri_df = contri_df.groupby(['outlet_id','week'])[['planned_od_item_share']].mean().reset_index()\n", "contri_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93921565-cd33-4bb6-8e8b-98e98efda314", "metadata": {}, "outputs": [], "source": ["residual_df = hourly_df[(hourly_df.order_date> min_date) & (hourly_df.order_date< max_date)].copy()\n", "residual_df['allocated'] = residual_df['fixed_mh'] + (residual_df['od_mh'] * residual_df['od_prod'])\n", "res_agg_df = residual_df.groupby(['outlet_id','week'])[['req_mh','allocated','fixed_mh_allocated','picking_mh','truck_delay_buffer']].sum().reset_index()\n", "\n", "res_agg_df['fnv_audit_breaks'] = res_agg_df['fixed_mh_allocated'] * 0.31\n", "res_agg_df['residual_plus_absent'] = res_agg_df['allocated'] - res_agg_df['req_mh'] - res_agg_df['fnv_audit_breaks'] - res_agg_df['truck_delay_buffer']\n", "res_agg_df['picking_share'] = res_agg_df['picking_mh'] / (res_agg_df['req_mh'] + res_agg_df['fnv_audit_breaks'] + res_agg_df['truck_delay_buffer'])\n", "res_agg_df['putaway_share'] = (res_agg_df['req_mh'] - res_agg_df['picking_mh']) / (res_agg_df['req_mh'] + res_agg_df['fnv_audit_breaks'] + res_agg_df['truck_delay_buffer'])\n", "res_agg_df['picking_buffer_share'] = np.maximum(res_agg_df['picking_share'] * res_agg_df['residual_plus_absent'],0)\n", "res_agg_df['putaway_bffer_share'] = np.maximum(res_agg_df['putaway_share'] * res_agg_df['residual_plus_absent'],0)\n", "res_agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "05b3d42f-1faf-46fc-ae97-4b1421f94cce", "metadata": {}, "outputs": [], "source": ["df['od_active_time'] = df['od_picking_time']/df['od_prod']\n", "df['od_busy_time'] = df['od_active_time'] * df['od_util']\n", "\n", "df['fixed_active_time'] = df['picking_mh'] - df['od_active_time'] \n", "df['fixed_active_time'] = np.where(df['fixed_active_time']<0,0,df['fixed_active_time'])\n", "df['fixed_busy_time'] = df['fixed_active_time'] * df['new_util']\n", "\n", "agg_df = df.groupby(['outlet_id','week'])[['fixed_busy_time','od_busy_time','qty_to_pick','picking_mh','fixed_active_time','od_active_time']].sum().reset_index()\n", "agg_df = agg_df.merge(res_agg_df[['outlet_id','picking_buffer_share','week']], on = ['outlet_id','week'])\n", "agg_df['ft_picker_active_time_final'] = (agg_df['picking_buffer_share']*agg_df['fixed_active_time']/agg_df['picking_mh']) + agg_df['fixed_active_time']\n", "agg_df['od_picker_active_time_final'] = (agg_df['picking_buffer_share']*agg_df['od_active_time']/agg_df['picking_mh']) + agg_df['od_active_time']\n", "agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "29de16fc-cda9-4441-8850-abfc5f9d6c92", "metadata": {}, "outputs": [], "source": ["agg_df['day_util'] = (agg_df['od_busy_time'] + agg_df['fixed_busy_time'])/(agg_df['od_picker_active_time_final'] + agg_df['ft_picker_active_time_final'])\n", "agg_df['ft_util'] = agg_df['fixed_busy_time']/agg_df['ft_picker_active_time_final']\n", "\n", "agg_df['store_util'] = (agg_df['day_util']).round(2)\n", "agg_df['ft_util'] = (agg_df['ft_util']).round(2)\n", "agg_df['od_util'] = 0.625\n", "util_df = agg_df[['outlet_id','week','store_util','ft_util','od_util']]\n", "util_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "63c02bd5-e11a-4f3c-88fc-fa7b1ca1dac0", "metadata": {}, "outputs": [], "source": ["df['pkg_qty_putaway'] = df['pckg_mh_allocation'] * df['mavg_iph_packaged']\n", "agg_df = df.groupby(['outlet_id','week'])[['perishable_qty_accumulated_final','pkg_qty_putaway','pckg_mh_allocation','unloading_perishable_putaway_mh','perishable_frozen_qty','truck_delay_buffer']].sum().reset_index()\n", "agg_df['total_putaway_qty'] = agg_df['pkg_qty_putaway'] + agg_df['perishable_frozen_qty']\n", "agg_df['total_putaway_mh'] = agg_df['pckg_mh_allocation'] + agg_df['unloading_perishable_putaway_mh'] + agg_df['truck_delay_buffer']\n", "agg_df = agg_df.merge(res_agg_df[['week','outlet_id','putaway_bffer_share']], on = ['outlet_id','week'])\n", "agg_df['total_putaway_mh'] = agg_df['total_putaway_mh'] + agg_df['putaway_bffer_share']\n", "\n", "\n", "agg_df['day_iph'] = agg_df['total_putaway_qty']/agg_df['total_putaway_mh']\n", "agg_df['day_iph'] = (agg_df['day_iph']).apply(np.floor)\n", "iph_df = agg_df.rename(columns ={'day_iph':'planned_store_iph'})\n", "iph_df = iph_df[['week','outlet_id','planned_store_iph']]\n", "\n", "iph_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9e959d38-82a9-4070-8b99-e50fe3aca971", "metadata": {}, "outputs": [], "source": ["result_df = ppi_df.merge(util_df, on = ['outlet_id','week'])\n", "result_df = result_df.merge(iph_df, on = ['outlet_id','week'])\n", "result_df = result_df.merge(contri_df, on = ['outlet_id','week'])\n", "\n", "result_df.columns = [\"week\",\"outlet_id\",\"planned_store_ppi\",\"planned_ft_ppi\",\"planned_od_ppi\",\"planned_store_util\",\"planned_ft_util\",\"planned_od_util\",\"planned_store_iph\",\"planned_od_item_share\"]\n", "result_df.head()"]}, {"cell_type": "markdown", "id": "93909405-d445-4a3c-8d7f-aa67a9008a0e", "metadata": {}, "source": ["### Pushing to DS temp tables"]}, {"cell_type": "code", "execution_count": null, "id": "7d952716-4668-49b9-b9de-7b0b4a308afb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ac920e7-090a-4146-9fed-6a852752b0d5", "metadata": {}, "outputs": [], "source": ["hourly_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_hourly_temp\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"picking_mh\", \"type\": \"REAL\", \"description\": \"Man-hours spent on picking\"},\n", "        {\"name\": \"putaway_mh\", \"type\": \"REAL\", \"description\": \"Man-hours allocated for putaway\"},\n", "        {\n", "            \"name\": \"non_nego_mh\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"man-hours spent on non-negotiable tasks\",\n", "        },\n", "        {\n", "            \"name\": \"pkg_putaway_mh\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Man-hours spent on package putaway\",\n", "        },\n", "        {\"name\": \"shift_start\", \"type\": \"REAL\", \"description\": \"Shift start time\"},\n", "        {\"name\": \"od_mh\", \"type\": \"REAL\", \"description\": \"OD man-hours allocated\"},\n", "        {\"name\": \"fixed_mh\", \"type\": \"REAL\", \"description\": \"Fulltime man-hours allocated\"},\n", "        {\"name\": \"req_mh\", \"type\": \"REAL\", \"description\": \"Required man-hours\"},\n", "        {\"name\": \"fnv\", \"type\": \"INTEGER\", \"description\": \"fnv executive allocated\"},\n", "        {\n", "            \"name\": \"buffer\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"<PERSON><PERSON><PERSON> present. Used for audit and breaks\",\n", "        },\n", "        {\n", "            \"name\": \"picking_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Actual man-hours spent on picking before adding absenteeism buffer.\",\n", "        },\n", "        {\n", "            \"name\": \"fixed_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Fixed man-hours from optimiser. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"shift_start_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Shift start time. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"is_roster\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Indicates if the data of  is used for roster or used as as filler days. Use 1 if want to extract roster data.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains daily roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8e6a1c27-640f-4482-ae2b-49ba4d955e91", "metadata": {}, "outputs": [], "source": ["week_df = input_data[['week','week_start_date']].drop_duplicates()\n", "output1_final = output1_final.merge(week_df, on = ['week'])"]}, {"cell_type": "code", "execution_count": null, "id": "95018738-8585-4c0d-b4ef-a4d43c75bcd8", "metadata": {}, "outputs": [], "source": ["hour_data_final = output1_final.copy()\n", "min_date = hour_data_final[\"order_date\"].min()\n", "max_date = hour_data_final[\"order_date\"].max()\n", "\n", "\n", "hour_data_final[\"week_start_date\"] = pd.to_datetime(hour_data_final[\"week_start_date\"])\n", "hour_data_final[\"putaway_mh\"] = (\n", "    hour_data_final[\"unloading_perishable_putaway_mh\"] + hour_data_final[\"pckg_mh_allocation\"]\n", ")\n", "hour_data_final[\"fnv\"] = hour_data_final[\"fnv_mh\"]\n", "hour_data_final[\"buffer\"] = (\n", "    (hour_data_final[\"od_mh\"] * hour_data_final[\"od_prod\"])\n", "    + hour_data_final[\"fixed_mh\"]\n", "    - hour_data_final[\"req_mh\"]\n", "    - hour_data_final[\"fnv\"]\n", ")\n", "# hour_data_final['fixed_mh_allocated'] = hour_data_final['fixed_mh_allocated'] - hour_data_final['fnv']\n", "hour_data_final[\"is_roster\"] = np.where(\n", "    (hour_data_final.order_date == min_date) | (hour_data_final.order_date == max_date), 0, 1\n", ")\n", "\n", "hour_data_final[\"updated_ts\"] = update_ts_ist\n", "\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"hour\",\n", "    \"picking_mh\",\n", "    \"putaway_mh\",\n", "    \"non_nego_mh\",\n", "    \"pckg_mh_allocation\",\n", "    \"employee_starting_shift\",\n", "    \"od_mh\",\n", "    \"fixed_mh\",\n", "    \"req_mh\",\n", "    \"fnv\",\n", "    \"buffer\",\n", "    \"actual_pick_mh\",\n", "    \"fixed_mh_allocated\",\n", "    \"shift_start_raw\",\n", "    \"is_roster\",\n", "    \"updated_ts\",\n", "]\n", "hour_data_final = hour_data_final[columns]\n", "hour_data_final = hour_data_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"pckg_mh_allocation\": \"pkg_putaway_mh\",\n", "        \"employee_starting_shift\": \"shift_start\",\n", "        \"fixed_mh_allocated\": \"fixed_mh_raw\",\n", "        \"actual_pick_mh\": \"picking_mh_raw\",\n", "    }\n", ")\n", "\n", "pb.to_trino(data_obj=hour_data_final, **hourly_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b705a960-46a5-4529-9f36-c1f86e2a8e75", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bcbe43d7-c9f4-4d1d-96cd-7fabb8bd5ff6", "metadata": {}, "outputs": [], "source": ["weekly_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_daily_temp\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"ft_mandays_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"od_manhours_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"active_ft_base_in_week\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount required during week. Unique full time people  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        \n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"outlet_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "367130c8-41a6-4702-8874-11697956bdc6", "metadata": {}, "outputs": [], "source": ["week_df = input_data[['week','week_start_date']].drop_duplicates()\n", "output2_final = output2_final.merge(week_df, on = ['week'])"]}, {"cell_type": "code", "execution_count": null, "id": "14851970-a5aa-41db-9406-8e6c4b755005", "metadata": {}, "outputs": [], "source": ["roster_data_final = output2_final.copy()\n", "# display(roster_data_final.info())\n", "roster_data_final[\"updated_ts\"] = update_ts_ist\n", "\n", "columns = [\n", "    \"order_date\",\n", "    \"outlet_id\",\n", "    \"fixed_md_required\",\n", "    \"od_mh_required\",\n", "    \"active_base_in_week\",\n", "    \"week_offs\",\n", "    \"updated_ts\",\n", "]\n", "roster_data_final = roster_data_final[columns]\n", "\n", "roster_data_final = roster_data_final.rename(\n", "    columns={\n", "        \"order_date\": \"date\",\n", "        \"fixed_md_required\": \"ft_mandays_req\",\n", "        \"od_mh_required\": \"od_manhours_req\",\n", "        \"active_base_in_week\": \"active_ft_base_in_week\",\n", "    }\n", ")\n", "pb.to_trino(data_obj=roster_data_final, **weekly_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "e8562d7e-c94c-4201-9bd4-b4d90422624f", "metadata": {}, "outputs": [], "source": ["input_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_input_temp\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week\",\n", "        },\n", "        {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"date\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"qty_to_pick\", \"type\": \"INTEGER\", \"description\": \"Total quantity to be picked\"},\n", "        {\n", "            \"name\": \"perishable_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"perishable grn qty\",\n", "        },\n", "        {\n", "            \"name\": \"packaged_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"packaged grn qty\",\n", "        },\n", "        {\"name\": \"mavg_ppi\", \"type\": \"REAL\", \"description\": \"ft employee ppi\"},\n", "        {\"name\": \"mavg_pick_util\", \"type\": \"REAL\", \"description\": \"ft employee picker util\"},\n", "        {\n", "            \"name\": \"mavg_iph_packaged\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (packaged)\",\n", "        },\n", "        {\n", "            \"name\": \"mavg_iph_perishable\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (perishable)\",\n", "        },\n", "        {\"name\": \"ppi_od\", \"type\": \"REAL\", \"description\": \"OD ppi\"},\n", "        {\"name\": \"od_util\", \"type\": \"REAL\", \"description\": \"OD picker util\"},\n", "        {\"name\": \"od_eph\", \"type\": \"REAL\", \"description\": \"OD eph\"},\n", "        {\"name\": \"fixed_eph\", \"type\": \"REAL\", \"description\": \"fulltime eph\"},\n", "        {\"name\": \"od_prod\", \"type\": \"REAL\", \"description\": \"OD productivity\"},\n", "        {\"name\": \"absent_predict\", \"type\": \"REAL\", \"description\": \"Predicted absenteeism metric\"},\n", "        {\"name\": \"avg_ipo\", \"type\": \"REAL\", \"description\": \"Average items picked per order\"},\n", "        {\n", "            \"name\": \"grn_difficulty_flag\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Flag indicating difficulty in GRN\",\n", "        },\n", "        {\n", "            \"name\": \"mh_limit\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" Ful time Man-hours limit for a the hour\",\n", "        },\n", "        {\"name\": \"od_max_evening\", \"type\": \"REAL\", \"description\": \"max OD limit for evening slot \"},\n", "        {\"name\": \"od_max_morning\", \"type\": \"REAL\", \"description\": \"max OD limit for morning slot \"},\n", "        {\"name\": \"od_max_noon\", \"type\": \"REAL\", \"description\": \"max OD limit for noon slot \"},\n", "        {\"name\": \"od_min_day\", \"type\": \"REAL\", \"description\": \"min OD limit for the day \"},\n", "        {\"name\": \"opm_avg_raw\", \"type\": \"REAL\", \"description\": \"average orders per minute\"},\n", "        {\"name\": \"opm_avg\", \"type\": \"REAL\", \"description\": \"orders per minute after bump-up\"},\n", "        {\n", "            \"name\": \"mh_required_for_pick_qt\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Man-hours required to pick after queuing theory.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Timestamp of the last update\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"order_date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"order_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains input data of the optimiser.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "3c3d0346-bb7d-48ce-883a-e019a3ce29ce", "metadata": {}, "outputs": [], "source": ["input_data_final = input_data.copy()\n", "input_data_final[\"week_start_date\"] = pd.to_datetime(input_data_final[\"week_start_date\"])\n", "input_data_final[\"updated_ts\"] = update_ts_ist\n", "columns = [\n", "    \"week_start_date\",\n", "    \"order_date\",\n", "    \"hour\",\n", "    \"outlet_id\",\n", "    \"qty_to_pick\",\n", "    \"perishable_qty_accumulated_final\",\n", "    \"packaged_qty_accumulated_final\",\n", "    \"mavg_ppi\",\n", "    \"mavg_pick_util\",\n", "    \"mavg_iph_packaged\",\n", "    \"mavg_iph_perishable\",\n", "    \"ppi_od\",\n", "    \"od_util\",\n", "    \"od_eph\",\n", "    \"fixed_eph\",\n", "    \"od_prod\",\n", "    \"absent_predict\",\n", "    \"avg_ipo\",\n", "    \"grn_difficulty_flag\",\n", "    \"mh_limit\",\n", "    \"od_max_evening\",\n", "    \"od_max_morning\",\n", "    \"od_max_noon\",\n", "    \"od_min_day\",\n", "    \"opm_avg_raw\",\n", "    \"opm_avg\",\n", "    \"mh_required_for_pick_qt\",\n", "    \"updated_ts\",\n", "]\n", "\n", "\n", "input_data_final = input_data_final[columns]\n", "\n", "pb.to_trino(data_obj=input_data_final, **input_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "65cf1a17-16a6-4f03-abe2-d80884bd69b9", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_sla_temp\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"planned_store_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the store\"},\n", "        {\n", "            \"name\": \"planned_ft_ppi\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned PPI for the full-time employee. \",\n", "        },\n", "        {\"name\": \"planned_od_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for OD\"},\n", "        {\n", "            \"name\": \"planned_store_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned store picker utilization\",\n", "        },\n", "        {\n", "            \"name\": \"planned_ft_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned full-time picker utilization\",\n", "        },\n", "        {\"name\": \"planned_od_util\", \"type\": \"REAL\", \"description\": \"Planned OD picker utilization\"},\n", "        {\"name\": \"planned_store_iph\", \"type\": \"REAL\", \"description\": \"Planned IPH for the store.\"},\n", "        {\n", "            \"name\": \"planned_od_item_share\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned od item contribution.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"outlet_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b70d3b25-dbe7-42e0-bff4-c7525d7b1628", "metadata": {}, "outputs": [], "source": ["week_df = input_data[['week','week_start_date']].drop_duplicates()\n", "result_df = result_df.merge(week_df, on = ['week'])\n", "result_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b2716a2b-1e35-43b8-bdfb-b5c0ba7e8fd8", "metadata": {}, "outputs": [], "source": ["metric_data_final = result_df.copy()\n", "metric_data_final[\"week_start_date\"] = pd.to_datetime(metric_data_final[\"week_start_date\"])\n", "if \"planned_od_item_share\" not in metric_data_final.columns:\n", "    metric_data_final[\"planned_od_item_share\"] = -1\n", "metric_data_final[\"updated_ts\"] = update_ts_ist\n", "columns = [\n", "    \"week_start_date\",\n", "    \"outlet_id\",\n", "    \"planned_store_ppi\",\n", "    \"planned_ft_ppi\",\n", "    \"planned_od_ppi\",\n", "    \"planned_store_util\",\n", "    \"planned_ft_util\",\n", "    \"planned_od_util\",\n", "    \"planned_store_iph\",\n", "    \"planned_od_item_share\",\n", "    \"updated_ts\",\n", "]\n", "metric_data_final = metric_data_final[columns]\n", "pb.to_trino(data_obj=metric_data_final, **metric_kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}