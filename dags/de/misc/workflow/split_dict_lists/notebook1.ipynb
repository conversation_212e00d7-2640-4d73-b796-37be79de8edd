{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Processing Set 2: Non-Electronics Categories\n", "\n", "This notebook processes the second set of data, which contains items from categories other than Electronics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import json\n", "import sys\n", "import os\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Input\n", "\n", "The data set is passed as a parameter from Airflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "data_set = [] # This will be replaced with the actual data\n", "set_name = \"Set 1\" # This will be replaced with the actual set name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Process the Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Data set type before conversion: {type(data_set)}\")\n", "\n", "# Convert data_set from string to dict/list if needed\n", "if isinstance(data_set, str):\n", "    print(\"Converting data_set from string to Python object\")\n", "    try:\n", "        data_set = json.loads(data_set)\n", "        print(\"Successfully converted from string using json.loads()\")\n", "    except Exception as e:\n", "        print(f\"Error converting using json: {str(e)}\")\n", "        # Alternative method using ast.literal_eval\n", "        try:\n", "            data_set = ast.literal_eval(data_set)\n", "            print(\"Successfully converted from string using ast.literal_eval()\")\n", "        except Exception as e2:\n", "            print(f\"Error converting using ast: {str(e2)}\")\n", "\n", "print(f\"Data set type after conversion: {type(data_set)}\")\n", "print(f\"Number of items in the data set: {len(data_set)}\")\n", "\n", "\n", "# Process the data using our function\n", "\n", "# stats = process_data(data_set, set_name)\n", "\n", "# Display the raw data\n", "print(\"Sample of data_set:\")\n", "print(data_set[:2] if len(data_set) > 2 else data_set)  # Show just a sample if large\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(data_set)\n", "print(\"DataFrame created successfully\")\n", "\n", "# Display the DataFrame\n", "df\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 4}