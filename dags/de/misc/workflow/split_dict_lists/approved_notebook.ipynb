{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4e1b332-3b15-4880-833f-cbcdcb3139d5", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.26.4\n", "!pip install papermill==2.3.0\n", "!pip install pandas==1.3.5\n", "!pip install awswrangler==3.9.0\n", "!pip install awscli==1.33.27\n", "!pip install pulp\n", "!pip install scipy"]}, {"cell_type": "code", "execution_count": null, "id": "6728f62e-e56d-4f75-9ab8-83db285261cc", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"JUPYTERHUB_USER\"] = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "55038195-83a6-47e5-91bf-5616eecf26e6", "metadata": {}, "outputs": [], "source": ["import ast\n", "import json\n", "import math\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import boto3\n", "import numpy as np\n", "import pandas as pd\n", "import awswrangler as wr\n", "import pencilbox as pb\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "b7db441f-7ee9-4a21-add6-06d6dd7f350e", "metadata": {}, "outputs": [], "source": ["def write_df_as_parquet_to_s3(data, s3_path, partition_cols=None, suffix=None):\n", "    mode = 'overwrite_partitions' if partition_cols else 'overwrite'\n", "    #LOGGER.info(f'writing data to s3 to path {s3_path}')\n", "\n", "    if suffix is not None:\n", "        s3_path = f\"{s3_path.strip('/')}/{suffix}\"\n", "\n", "    wr.s3.to_parquet(\n", "        data,\n", "        s3_path,\n", "        dataset=True,\n", "        mode=mode,\n", "        partition_cols=partition_cols\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "060f9294-552a-40f4-8a11-0389f546453c", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "update_ts_ist = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "update_ts_ist = update_ts_ist.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "update_ts_ist"]}, {"cell_type": "code", "execution_count": null, "id": "b6161a50-a0d5-42b6-a2b5-cc98ec44ad14", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "def read_sql_query(sql, conn, max_tries=3):\n", "    \"\"\"\n", "    Safely execute SQL query with retry mechanism\n", "    \n", "    Args:\n", "        sql (str): SQL query to execute\n", "        conn (object): Database connection\n", "        max_tries (int): Maximum number of retry attempts\n", "    \n", "    Returns:\n", "        pandas.DataFrame: Query result\n", "    \"\"\"\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, conn)\n", "            end = time.time()\n", "            \n", "            # Print execution time\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            \n", "            return df\n", "        except Exception as e:\n", "            print(e)\n", "            time.sleep(5)\n", "    \n", "    raise RuntimeError(\"Failed to execute SQL query after maximum attempts\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c4769d52-b395-4df1-ba73-feab9c903938", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "data_set = [] # This will be replaced with the actual data\n", "set_name = \"Set 1\" # This will be replaced with the actual set name"]}, {"cell_type": "code", "execution_count": null, "id": "df1bf551-fb35-4df7-8eb4-7c0cc0c4e755", "metadata": {}, "outputs": [], "source": ["#data_set = \"[{'entity_id': 3465, 'week_start_date': '2025-09-01 00:00:00', 'status': '', 'updated_at': '2025-08-25 16:35:52', 'ppi': 12.0, 'items_put_away_per_hour': 251.0, 'picker_util_fixed_wt_avg': None}]\""]}, {"cell_type": "code", "execution_count": null, "id": "89ed8ebe-c366-4b16-8f03-c5c5b73f5b0a", "metadata": {}, "outputs": [], "source": ["print(f\"Data set type before conversion: {type(data_set)}\")\n", "\n", "# Convert data_set from string to dict/list if needed\n", "if isinstance(data_set, str):\n", "    print(\"Converting data_set from string to Python object\")\n", "    try:\n", "        data_set = json.loads(data_set)\n", "        print(\"Successfully converted from string using json.loads()\")\n", "    except Exception as e:\n", "        print(f\"Error converting using json: {str(e)}\")\n", "        # Alternative method using ast.literal_eval\n", "        try:\n", "            data_set = ast.literal_eval(data_set)\n", "            print(\"Successfully converted from string using ast.literal_eval()\")\n", "        except Exception as e2:\n", "            print(f\"Error converting using ast: {str(e2)}\")\n", "\n", "print(f\"Data set type after conversion: {type(data_set)}\")\n", "print(f\"Number of items in the data set: {len(data_set)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "18cb52ff-ca44-4ff9-82b8-5cd004399ec9", "metadata": {}, "outputs": [], "source": ["# Display the raw data\n", "print(\"Sample of approved records:\")\n", "print(data_set[:2] if len(data_set) > 2 else data_set)  # Show just a sample if large"]}, {"cell_type": "code", "execution_count": null, "id": "46971717-de93-4e4f-9ef9-7c928734a406", "metadata": {}, "outputs": [], "source": ["# Convert to DataFrame\n", "if len(data_set) > 0:\n", "    df = pd.DataFrame(data_set)\n", "    print(\"DataFrame created successfully\")\n", "    \n", "    # Rename columns for planner input\n", "    planner_df = df.rename(columns = {\n", "        'ppi':'input_ppi',\n", "        'items_put_away_per_hour':'input_iph',\n", "        'picker_util_fixed_wt_avg':'input_ft_picker_util',\n", "        'entity_id':'outlet_id'\n", "    })\n", "    \n", "    # Select only the needed columns\n", "    planner_df = planner_df[['outlet_id','input_ppi','input_iph','input_ft_picker_util','week_start_date']]\n", "    \n", "    # Convert week_start_date to datetime\n", "    planner_df['week_start_date'] = pd.to_datetime(planner_df['week_start_date'])\n", "    \n", "    # Display the DataFrame\n", "    print(\"\\nProcessed approved records:\")\n", "    display(planner_df.head())\n", "else:\n", "    print(\"No approved records to process\")\n", "    sys.exit(0)"]}, {"cell_type": "code", "execution_count": null, "id": "4455249c-8fc3-4cad-ad67-2ba6230c1165", "metadata": {}, "outputs": [], "source": ["today = datetime.today() + <PERSON><PERSON><PERSON>(days=7)\n", "first_monday = today - <PERSON><PERSON><PERSON>(days=today.weekday())\n", "last_monday = first_monday + <PERSON><PERSON><PERSON>(weeks=4)\n", "first_monday, last_monday = first_monday.strftime(\"%Y-%m-%d\"), last_monday.strftime(\"%Y-%m-%d\")\n", "print(first_monday, last_monday)"]}, {"cell_type": "code", "execution_count": null, "id": "f7fafbae-1b87-4c32-a0c8-39fdcc0867e0", "metadata": {}, "outputs": [], "source": ["start_date = (datetime.strptime(first_monday, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')\n", "end_date   = (datetime.strptime(last_monday, '%Y-%m-%d')  + timedelta(days=7)).strftime('%Y-%m-%d')\n", "start_date,end_date"]}, {"cell_type": "code", "execution_count": null, "id": "d62a2515-a0ce-40ac-ab63-a3542a6f4347", "metadata": {}, "outputs": [], "source": ["outlet_ids_str = \", \".join(map(str, planner_df['outlet_id'].unique() ))\n", "outlet_ids_str"]}, {"cell_type": "markdown", "id": "3c2fc3c6-e84d-4954-9b35-32b9f3d86616", "metadata": {"tags": []}, "source": ["### pushing to planner_instore_forecasts_weekly_m1"]}, {"cell_type": "code", "execution_count": null, "id": "7665147b-bfef-48f5-9820-e6e1f7246f20", "metadata": {}, "outputs": [], "source": ["weekly_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_weekly_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the fixed employee\"},\n", "        {\n", "            \"name\": \"total_picking_time_sec\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"total_picking_time_fixed \",\n", "        },\n", "        {\"name\": \"total_items_quantity_ordered\", \"type\": \"REAL\", \"description\": \"total_items_quantity_ordered\"},\n", "        {\n", "            \"name\": \"picker_util_fixed_wt_avg\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" sum-product of qty x fixed_util\",\n", "        },\n", "        {\n", "            \"name\": \"picker_util_numerator_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"sum-product of the util and qty.\"\n", "        },\n", "        {\"name\": \"items_put_away_per_hour\", \"type\": \"REAL\", \"description\": \"items_put_away_per_hour\"},\n", "        {\n", "            \"name\": \"putaway_qty\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"putaway_qty\",\n", "        },\n", "        {\"name\": \"putter_active_time_mins\", \"type\": \"REAL\", \"description\": \"putter_active_time_mins\"},\n", "        {\n", "            \"name\": \"od_contribution_pct\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"od_contribution_pct\",\n", "        },\n", "        {\"name\": \"qty_ordered_od\", \"type\": \"REAL\", \"description\": \"qty_ordered_od\"},\n", "        {\n", "            \"name\": \"qty_ordered_fixed\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"qty_ordered_fixed\",\n", "        },{\n", "            \"name\": \"headcount\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" active base for the week.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"entity_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "9e056aa1-66a6-42e0-9c5d-35d752d9eeef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80fa4b76-5008-4b50-aaa7-28f3ba03f471", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.storeops_etls.planner_instore_forecasts_weekly_temp_m1\n", "     where \n", "     week_start_date >= date'{first_monday}'\n", "     and week_start_date <= date'{last_monday}'\n", "     and entity_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "372068ce-d5ca-4aaf-b986-5965cceeb31b", "metadata": {}, "outputs": [], "source": ["final_df = read_sql_query(query,trino_connection )\n", "a = final_df[['week_start_date','entity_id','ppi','picker_util_fixed_wt_avg','items_put_away_per_hour']]\n", "final_df['week_start_date'] = pd.to_datetime(final_df['week_start_date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "pb.to_trino(data_obj=final_df, **weekly_kwargs)\n", "print(\"Data pushed..\")"]}, {"cell_type": "markdown", "id": "b4f63398-964d-47de-b51b-e0343854b4ed", "metadata": {}, "source": ["### pushing to planner planner_instore_forecasts_daily_m1"]}, {"cell_type": "code", "execution_count": null, "id": "4152f7b2-efe9-47bc-8067-784921721723", "metadata": {}, "outputs": [], "source": ["daily_kwargs = {\n", "    \"schema_name\": \"storeops_etls\",\n", "    \"table_name\": \"planner_instore_forecasts_daily_m1\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"entity_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"mandays_fixed\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"manhours_od\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"headcount\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount available after week_offs  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\",\"date\", \"entity_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ac3f519c-ec0d-4437-87d3-c4765b716989", "metadata": {}, "outputs": [], "source": ["query =f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.storeops_etls.planner_instore_forecasts_daily_temp_m1\n", "     where \n", "     week_start_date >= date'{first_monday}'\n", "     and week_start_date <= date'{last_monday}'\n", "     and date>=date'{start_date}' and date <= date'{end_date}'\n", "     and entity_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3047a47d-1aa1-4533-ab00-2f80385eb928", "metadata": {}, "outputs": [], "source": ["\n", "final_df = read_sql_query(query,trino_connection )\n", "final_df['week_start_date'] = pd.to_datetime(final_df['week_start_date'])\n", "final_df['date'] = pd.to_datetime(final_df['date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "pb.to_trino(data_obj=final_df, **daily_kwargs)\n", "print(\"Data pushed..\") "]}, {"cell_type": "markdown", "id": "615f22ca-8236-4d45-b776-0eb287663520", "metadata": {}, "source": ["### pushing to planner store_ops_roster_hourly"]}, {"cell_type": "code", "execution_count": null, "id": "713a251a-9e84-4ca0-a5a9-665816dd0e7b", "metadata": {}, "outputs": [], "source": ["hourly_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_hourly\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week for which roster is created.\",\n", "        },\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"picking_mh\", \"type\": \"REAL\", \"description\": \"Man-hours spent on picking\"},\n", "        {\"name\": \"putaway_mh\", \"type\": \"REAL\", \"description\": \"Man-hours allocated for putaway\"},\n", "        {\n", "            \"name\": \"non_nego_mh\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"man-hours spent on non-negotiable tasks\",\n", "        },\n", "        {\n", "            \"name\": \"pkg_putaway_mh\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Man-hours spent on package putaway\",\n", "        },\n", "        {\"name\": \"shift_start\", \"type\": \"REAL\", \"description\": \"Shift start time\"},\n", "        {\"name\": \"od_mh\", \"type\": \"REAL\", \"description\": \"OD man-hours allocated\"},\n", "        {\"name\": \"fixed_mh\", \"type\": \"REAL\", \"description\": \"Fulltime man-hours allocated\"},\n", "        {\"name\": \"req_mh\", \"type\": \"REAL\", \"description\": \"Required man-hours\"},\n", "        {\"name\": \"fnv\", \"type\": \"INTEGER\", \"description\": \"fnv executive allocated\"},\n", "        {\n", "            \"name\": \"buffer\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"<PERSON><PERSON><PERSON> present. Used for audit and breaks\",\n", "        },\n", "        {\n", "            \"name\": \"picking_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Actual man-hours spent on picking before adding absenteeism buffer.\",\n", "        },\n", "        {\n", "            \"name\": \"fixed_mh_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Fixed man-hours from optimiser. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"shift_start_raw\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Shift start time. Before adding fnv and audits.\",\n", "        },\n", "        {\n", "            \"name\": \"is_roster\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Indicates if the data of  is used for roster or used as as filler days. Use 1 if want to extract roster data.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains daily roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f96b3f94-e2a3-40c2-8d74-38c5138ea340", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.ds_etls.store_ops_roster_hourly_temp\n", "     where \n", "     week_start_date >= date'{first_monday}'\n", "     and week_start_date <= date'{last_monday}'\n", "     and date>=date'{start_date}' and date <= date'{end_date}'\n", "     and outlet_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c197d1d7-e21e-433f-b8aa-f50fde2c7898", "metadata": {}, "outputs": [], "source": ["final_df = read_sql_query(query,trino_connection )\n", "final_df['week_start_date'] = pd.to_datetime(final_df['week_start_date'])\n", "final_df['date'] = pd.to_datetime(final_df['date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "pb.to_trino(data_obj=final_df, **hourly_kwargs)\n", "print(\"Data pushed..\") "]}, {"cell_type": "markdown", "id": "24b4c279-6a27-49a9-8efb-d379b153bbdb", "metadata": {}, "source": ["### pushing to planner store_ops_roster_daily"]}, {"cell_type": "code", "execution_count": null, "id": "20561182-bf2a-420a-a70a-1b91a7445bbc", "metadata": {}, "outputs": [], "source": ["daily_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_daily\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date for which roaster is created.\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\n", "            \"name\": \"ft_mandays_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Daily full-time MANDAYS required\",\n", "        },\n", "        {\n", "            \"name\": \"od_manhours_req\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Daily OD MANHOURS required.\",\n", "        },\n", "        {\n", "            \"name\": \"active_ft_base_in_week\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Active full-time headcount required during week. Unique full time people  \",\n", "        },\n", "        {\n", "            \"name\": \"week_offs\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \" Number of week-offs allocated on that day.\",\n", "        },\n", "        \n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \" Update Timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"date\", \"outlet_id\"],\n", "    \"partition_key\": [\"date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly roster of the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "a4ff286c-2ec2-4ed5-96f7-5abe576e8f04", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.ds_etls.store_ops_roster_daily_temp\n", "     where \n", "     date>=date'{start_date}' and date <= date'{end_date}'\n", "     and outlet_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "a0bfcfd0-7267-4ac3-9b0f-567e1679c162", "metadata": {}, "outputs": [], "source": ["\n", "final_df = read_sql_query(query,trino_connection )\n", "final_df['date'] = pd.to_datetime(final_df['date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "pb.to_trino(data_obj=final_df, **daily_kwargs)\n", "print(\"Data pushed..\") "]}, {"cell_type": "markdown", "id": "dad9c3e1-d8af-44cd-b400-bf96f1b7955d", "metadata": {}, "source": ["### pushing to planner store_ops_roster_input"]}, {"cell_type": "code", "execution_count": null, "id": "96440d89-da2c-4590-ac13-cd76cf68055d", "metadata": {}, "outputs": [], "source": ["input_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_input\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date(Monday) of the week\",\n", "        },\n", "        {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"date\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"Hour of the day\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"qty_to_pick\", \"type\": \"INTEGER\", \"description\": \"Total quantity to be picked\"},\n", "        {\n", "            \"name\": \"perishable_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"perishable grn qty\",\n", "        },\n", "        {\n", "            \"name\": \"packaged_qty_accumulated_final\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"packaged grn qty\",\n", "        },\n", "        {\"name\": \"mavg_ppi\", \"type\": \"REAL\", \"description\": \"ft employee ppi\"},\n", "        {\"name\": \"mavg_pick_util\", \"type\": \"REAL\", \"description\": \"ft employee picker util\"},\n", "        {\n", "            \"name\": \"mavg_iph_packaged\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (packaged)\",\n", "        },\n", "        {\n", "            \"name\": \"mavg_iph_perishable\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"items picked per hour (perishable)\",\n", "        },\n", "        {\"name\": \"ppi_od\", \"type\": \"REAL\", \"description\": \"OD ppi\"},\n", "        {\"name\": \"od_util\", \"type\": \"REAL\", \"description\": \"OD picker util\"},\n", "        {\"name\": \"od_eph\", \"type\": \"REAL\", \"description\": \"OD eph\"},\n", "        {\"name\": \"fixed_eph\", \"type\": \"REAL\", \"description\": \"fulltime eph\"},\n", "        {\"name\": \"od_prod\", \"type\": \"REAL\", \"description\": \"OD productivity\"},\n", "        {\"name\": \"absent_predict\", \"type\": \"REAL\", \"description\": \"Predicted absenteeism metric\"},\n", "        {\"name\": \"avg_ipo\", \"type\": \"REAL\", \"description\": \"Average items picked per order\"},\n", "        {\n", "            \"name\": \"grn_difficulty_flag\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Flag indicating difficulty in GRN\",\n", "        },\n", "        {\n", "            \"name\": \"mh_limit\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \" Ful time Man-hours limit for a the hour\",\n", "        },\n", "        {\"name\": \"od_max_evening\", \"type\": \"REAL\", \"description\": \"max OD limit for evening slot \"},\n", "        {\"name\": \"od_max_morning\", \"type\": \"REAL\", \"description\": \"max OD limit for morning slot \"},\n", "        {\"name\": \"od_max_noon\", \"type\": \"REAL\", \"description\": \"max OD limit for noon slot \"},\n", "        {\"name\": \"od_min_day\", \"type\": \"REAL\", \"description\": \"min OD limit for the day \"},\n", "        {\"name\": \"opm_avg_raw\", \"type\": \"REAL\", \"description\": \"average orders per minute\"},\n", "        {\"name\": \"opm_avg\", \"type\": \"REAL\", \"description\": \"orders per minute after bump-up\"},\n", "        {\n", "            \"name\": \"mh_required_for_pick_qt\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Man-hours required to pick after queuing theory.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Timestamp of the last update\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"order_date\", \"outlet_id\", \"hour\"],\n", "    \"partition_key\": [\"order_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains input data of the optimiser.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "331fd7de-8bc5-4629-9620-6f6dd315941b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.ds_etls.store_ops_roster_input_temp\n", "     where \n", "     week_start_date >= date'{first_monday}'\n", "     and week_start_date <= date'{last_monday}'\n", "     and order_date>=date'{start_date}' and order_date <= date'{end_date}'\n", "     and outlet_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "a73fa28d-632d-4118-8b0e-b7d2ceda3914", "metadata": {}, "outputs": [], "source": ["final_df = read_sql_query(query,trino_connection )\n", "b = final_df[['week_start_date','order_date','outlet_id','hour','mavg_ppi','mavg_pick_util','mavg_iph_packaged','mavg_iph_perishable','ppi_od']]\n", "final_df['week_start_date'] = pd.to_datetime(final_df['week_start_date'])\n", "final_df['order_date'] = pd.to_datetime(final_df['order_date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "final_df['opm_avg_raw'] = pd.to_numeric(final_df['opm_avg_raw'], errors='coerce')\n", "final_df['opm_avg'] = pd.to_numeric(final_df['opm_avg'], errors='coerce')\n", "pb.to_trino(data_obj=final_df, **input_kwargs)\n", "print(\"Data pushed..\") "]}, {"cell_type": "markdown", "id": "6f7318a0-0082-4055-865b-c2180032bc57", "metadata": {}, "source": ["### pushing to planner store_ops_roster_sla"]}, {"cell_type": "code", "execution_count": null, "id": "48b5b075-ca9a-46c5-8bdc-a16b5e729ad3", "metadata": {}, "outputs": [], "source": ["metric_kwargs = {\n", "    \"schema_name\": \"ds_etls\",\n", "    \"table_name\": \"store_ops_roster_sla\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"week_start_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Start date (Monday) of the week\",\n", "        },\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Unique identifier for the outlet\"},\n", "        {\"name\": \"planned_store_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for the store\"},\n", "        {\n", "            \"name\": \"planned_ft_ppi\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned PPI for the full-time employee. \",\n", "        },\n", "        {\"name\": \"planned_od_ppi\", \"type\": \"REAL\", \"description\": \"Planned PPI for OD\"},\n", "        {\n", "            \"name\": \"planned_store_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned store picker utilization\",\n", "        },\n", "        {\n", "            \"name\": \"planned_ft_util\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned full-time picker utilization\",\n", "        },\n", "        {\"name\": \"planned_od_util\", \"type\": \"REAL\", \"description\": \"Planned OD picker utilization\"},\n", "        {\"name\": \"planned_store_iph\", \"type\": \"REAL\", \"description\": \"Planned IPH for the store.\"},\n", "        {\n", "            \"name\": \"planned_od_item_share\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"Planned od item contribution.\",\n", "        },\n", "        {\"name\": \"updated_ts\", \"type\": \"VARCHAR\", \"description\": \"Update timestamp of the record\"},\n", "    ],\n", "    \"primary_key\": [\"week_start_date\", \"outlet_id\"],\n", "    \"partition_key\": [\"week_start_date\"],\n", "    \"incremental_key\": \"updated_ts\",\n", "    \"load_type\": \"upsert\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"This table contains weekly planned sla for the stores.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "a9bdfe17-202c-4235-a4bc-0611a4969bd0", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     SELECT * from \n", "     blinkit_staging.ds_etls.store_ops_roster_sla_temp\n", "     where \n", "     week_start_date >= date'{first_monday}'\n", "     and week_start_date <= date'{last_monday}'\n", "     and outlet_id in ({outlet_ids_str})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "06336be2-f875-41fd-b7a0-bc019dff0779", "metadata": {}, "outputs": [], "source": ["final_df = read_sql_query(query,trino_connection )\n", "final_df['week_start_date'] = pd.to_datetime(final_df['week_start_date'])\n", "final_df['updated_ts'] = update_ts_ist\n", "pb.to_trino(data_obj=final_df, **metric_kwargs)\n", "print(\"Data pushed..\")\n"]}, {"cell_type": "markdown", "id": "706318f6-2aa4-4e6d-b6df-0aee2fdf13fe", "metadata": {}, "source": ["### CHANGING INPUT DATA"]}, {"cell_type": "code", "execution_count": null, "id": "3a4845dc-aacf-49ac-bc93-55b9124c8926", "metadata": {}, "outputs": [], "source": ["input_df = pd.read_parquet(\"s3://prod-dse-projects/store_ops/monthly/september/optimiser_input_data_copy/\")\n", "input_df = input_df.sort_values(['checkout_date','hour'])\n", "input_df['day_since_monday'] = input_df['checkout_date'].dt.weekday\n", "input_df['week_start_date'] = input_df['checkout_date'] - pd.to_timedelta(input_df['day_since_monday'], unit='D')\n", "min_date = input_df['checkout_date'].min()\n", "max_date = input_df['checkout_date'].max()\n", "\n", "mask = input_df['checkout_date'].isin([input_df['checkout_date'].min(), input_df['checkout_date'].max()])\n", "input_df.loc[mask, 'week_start_date'] = pd.NaT\n", "\n", "input_df['week_start_date'] = input_df['week_start_date'].ffill().bfill()\n", "input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a3a6942b-fc7c-46c6-a4d2-5b885835ebeb", "metadata": {}, "outputs": [], "source": ["b.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e2c5616a-af4f-4a6d-8159-8d86ea97fdc9", "metadata": {}, "outputs": [], "source": ["input_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "5939a818-2914-409a-8f4b-d0dbe8bb808e", "metadata": {}, "outputs": [], "source": ["b = b.rename(columns = {'order_date':'checkout_date'})\n", "b['checkout_date'] = pd.to_datetime(b['checkout_date'])\n", "b['week_start_date'] = pd.to_datetime(b['week_start_date'])\n", "\n", "slice_df = input_df.merge(b,on=['week_start_date','checkout_date','hour','outlet_id'])\n", "slice_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b4261ac8-5808-4dc0-9174-4d271eafe774", "metadata": {}, "outputs": [], "source": ["slice_df['ft_ppi'] = slice_df['mavg_ppi']\n", "slice_df['od_ppi'] = slice_df['ppi_od']\n", "\n", "slice_df['picker_utilisation_per'] = slice_df['mavg_pick_util']\n", "slice_df['packaged_iph'] = slice_df['mavg_iph_packaged']\n", "slice_df['perishable_iph'] = slice_df['mavg_iph_perishable']\n", "slice_df = slice_df.drop(columns = ['mavg_ppi','mavg_pick_util','mavg_iph_packaged','mavg_iph_perishable','ppi_od','day_since_monday'])\n", "slice_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f81b482d-c606-45cc-84c7-60315158c817", "metadata": {}, "outputs": [], "source": ["a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d4a5bf46-b9c1-4194-ab7d-ea0995f7d7a4", "metadata": {}, "outputs": [], "source": ["a['week_start_date'] = pd.to_datetime(a['week_start_date'])\n", "a = a.rename(columns = {'entity_id':'outlet_id'})\n", "\n", "slice_df = slice_df.merge(a,on=['week_start_date','outlet_id'])\n", "slice_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "113fd20f-c339-4c41-ac23-027d9cea1f7a", "metadata": {}, "outputs": [], "source": ["slice_df['current_ft_util'] = slice_df['picker_util_fixed_wt_avg']/100\n", "slice_df['current_ft_ppi'] = slice_df['ppi']\n", "slice_df = slice_df.drop(columns = ['ppi','picker_util_fixed_wt_avg','items_put_away_per_hour','week_start_date'])\n", "slice_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "08027e5c-1a77-4f08-aa7a-1d367e3e698d", "metadata": {}, "outputs": [], "source": ["assert slice_df.isna().sum().sum() == 0\n", "assert slice_df.shape[0] == len(list(slice_df['outlet_id'].unique())) * 37 * 24"]}, {"cell_type": "code", "execution_count": null, "id": "620099d4-b36d-49b2-9f74-a868dacfe65a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b906dcf0-d3ba-4e3a-87a9-0070fa715832", "metadata": {}, "outputs": [], "source": ["sub_df = input_df[~input_df.outlet_id.isin(list(slice_df['outlet_id'].unique() ))].drop(columns=['day_since_monday','week_start_date'])"]}, {"cell_type": "code", "execution_count": null, "id": "05f61e9e-3ad9-4c17-9d80-ea856b24adee", "metadata": {}, "outputs": [], "source": ["final_df = pd.concat([sub_df,slice_df], ignore_index = True)"]}, {"cell_type": "code", "execution_count": null, "id": "c910511d-6b34-452b-a370-bfb17a697083", "metadata": {}, "outputs": [], "source": ["final_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "8dd62c16-1323-4b83-a92d-64dee40cc511", "metadata": {}, "outputs": [], "source": ["assert final_df.isna().sum().sum() == 0\n", "assert final_df.shape[0] == len(list(input_df['outlet_id'].unique())) * 37 * 24"]}, {"cell_type": "code", "execution_count": null, "id": "8975614f-a86e-4673-a064-d154f85dc39a", "metadata": {}, "outputs": [], "source": ["s3_path = f's3://prod-dse-projects/store_ops/monthly/september/optimiser_input_data_copy/'\n", "print(s3_path)\n", "write_df_as_parquet_to_s3(final_df,s3_path)"]}, {"cell_type": "code", "execution_count": null, "id": "6175ffa2-aa85-4d9c-84ef-cf12cefcc0e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}