{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import requests\n", "import pytz\n", "from datetime import datetime, timedelta\n", "\n", "!pip install PyMySQL==1.1.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Configuration parameters - passed from config.yml via papermill\n", "# These parameters are set in config.yml and injected by the DAG framework\n", "ts = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "# Core processing parameters\n", "ENTITY_NAME = \"storeops\"  # Set to specific entity name or None to process all entities\n", "LIST_ENTITIES_ONLY = False  # Set to True to only list available entities\n", "LOG_LEVEL = \"INFO\"  # Logging level\n", "DEBUG_MODE = False  # Set to True to enable debug mode (skip actual operations)\n", "\n", "# Environment configuration - configurable by end user\n", "ENVIRONMENT = \"preprod\"  # Environment setting (preprod, prod, etc.)\n", "\n", "SLACK_CHANNEL = \"planit-storeops-notifs\" if not DEBUG_MODE else \"bl-data-slack-testing\"\n", "\n", "# StarRocks configuration - passed from config.yml\n", "STARROCKS_SECRET_PATH = (\n", "    f\"data/services/planner-platform/{ENVIRONMENT}/databases/starrocks/capacity_planner\"\n", ")\n", "\n", "# Data loader configuration - fetch from vault\n", "DATA_LOADER_VAULT_PATH = f\"data/services/planner-platform/{ENVIRONMENT}/backend/data_loader\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"JUPYTERHUB_USER\" in os.environ:\n", "    DEBUG_MODE: bool = True\n", "    cwd = \"/home/<USER>/airflow-de-dags/dags/de/planner/workflow/data_loader\"\n", "    os.ch<PERSON>(cwd)\n", "else:\n", "    DEBUG_MODE: bool = False\n", "    cwd = \"/usr/local/airflow/dags/repo/dags/de/planner/workflow/data_loader\"\n", "    os.ch<PERSON>(cwd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "from config_loader import load_entity_config, get_available_entities, get_entity_categories\n", "from utils.forecast_utils import process_forecast\n", "from utils.starrocks_utils import get_max_updated_timestamp\n", "from utils.notification_utils import send_slack_notification, display_user_activity_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup logging\n", "logging.basicConfig(\n", "    level=getattr(logging, LOG_LEVEL.upper()),\n", "    format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\",\n", "    stream=sys.stdout,\n", "    force=True,\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# Fetch data loader configuration from vault\n", "logger.info(f\"Fetching data loader configuration from vault path: {DATA_LOADER_VAULT_PATH}\")\n", "try:\n", "    data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)\n", "    S3_BUCKET = data_loader_config.get(\"s3_bucket\", \"blinkit-data-staging\")\n", "    S3_REGION = data_loader_config.get(\"s3_region\", \"ap-southeast-1\")\n", "    IAM_ROLE_ARN = data_loader_config.get(\n", "        \"iam_role_arn\", \"arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role\"\n", "    )\n", "    logger.info(f\"Successfully fetched data loader configuration from vault\")\n", "    logger.info(f\"S3 bucket: {S3_BUCKET}\")\n", "    logger.info(f\"S3 region: {S3_REGION}\")\n", "    logger.info(f\"IAM role ARN: {IAM_ROLE_ARN}\")\n", "except Exception as e:\n", "    logger.warning(f\"Failed to fetch data loader configuration from vault: {str(e)}\")\n", "    logger.warning(\"Using fallback configuration values\")\n", "    # Fallback values - always use preprod\n", "    S3_BUCKET = \"blinkit-data-staging\"\n", "    S3_REGION = \"ap-southeast-1\"\n", "    IAM_ROLE_ARN = \"arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role\"\n", "\n", "# Calculate model trigger time\n", "if not ts:\n", "    ts = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "# Parse the timestamp and add 5 hours 30 minutes (IST offset)\n", "from datetime import timedelta\n", "\n", "base_time = datetime.fromisoformat(ts)\n", "adjusted_time = base_time + <PERSON><PERSON>ta(hours=5, minutes=30)\n", "MODEL_TRIGGER_TIME = adjusted_time.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "logger.info(f\"Original timestamp: {ts}\")\n", "logger.info(f\"Model trigger time (UTC + 05:30): {MODEL_TRIGGER_TIME}\")\n", "\n", "# Create StarRocks configuration object\n", "STARROCKS_CONFIG = {\n", "    \"secret_path\": STARROCKS_SECRET_PATH,\n", "    \"s3_bucket\": S3_BUCKET,\n", "    \"s3_region\": S3_REGION,\n", "    \"iam_role_arn\": IAM_ROLE_ARN,\n", "}\n", "logger.info(f\"StarRocks configuration initialized\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_forecasts_inputs_with_emails(entity_id=3, forecast_id=20):\n", "    \"\"\"\n", "    Fetch forecasts inputs data from the API, including user_emails field\n", "    \"\"\"\n", "    try:\n", "        # Get API credentials from vault\n", "        data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)\n", "        access_token = data_loader_config.get('access_token')\n", "        refresh_token = data_loader_config.get('refresh_token')\n", "\n", "        # Setup API request\n", "        base_url = f\"https://capacity-planner-preprod-data.prod-sgp-k8s.grofer.io/api/v1/blinkit/storeops/{entity_id}/forecasts/{forecast_id}/inputs\"\n", "\n", "        headers = {\n", "            \"accept\": \"application/json\",\n", "            \"Cookie\": f\"refresh_token={refresh_token}; access_token={access_token};\"\n", "        }\n", "\n", "        # Get current date and calculate start date (next Monday)\n", "        today = datetime.today() + <PERSON><PERSON><PERSON>(days=7)\n", "        days_since_monday = today.weekday()\n", "        start_date = today - <PERSON><PERSON><PERSON>(days=days_since_monday)\n", "\n", "        # Setup parameters\n", "        params = {\n", "            \"start_time\": start_date.strftime(\"%Y-%m-%d 00:00:00\"),\n", "            \"time_grain\": \"weekly\",\n", "            \"multi_aggregation_grain\": \"entity\",\n", "        }\n", "\n", "        # Make the API request\n", "        logger.info(f\"Fetching forecast inputs from API for week starting {params['start_time']}\")\n", "        response = requests.get(base_url, params=params, headers=headers)\n", "\n", "        if response.status_code == 200:\n", "            try:\n", "                data = response.json()\n", "                logger.info(f\"Successfully fetched {len(data.get('data', []))} records from API\")\n", "                return data\n", "            except ValueError:\n", "                logger.error(f\"Response is not valid JSON: {response.text}\")\n", "                return None\n", "        else:\n", "            logger.error(f\"API request failed with status code: {response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error fetching forecast inputs: {str(e)}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Track user emails and update timestamps\n", "initial_max_ts = None\n", "updated_emails = set()\n", "final_max_ts = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def collect_user_emails_in_range(data, start_ts=None, end_ts=None):\n", "    \"\"\"\n", "    Extract user emails from API response data and add to global set\n", "    Only collect emails from records with timestamps between start_ts and end_ts\n", "    \n", "    Args:\n", "        data (dict): API response data\n", "        start_ts (str): Start timestamp to filter records\n", "        end_ts (str): End timestamp to filter records\n", "    \"\"\"\n", "    global updated_emails\n", "    \n", "    if not data or not isinstance(data, dict):\n", "        logger.warning(\"No valid data provided to collect user emails\")\n", "        return\n", "    \n", "    records = data.get(\"data\", [])\n", "    for record in records:\n", "        # Check if the record has an updated_at timestamp in the desired range\n", "        record_ts = record.get(\"updated_at\")\n", "        \n", "        # Include records with timestamps between start_ts and end_ts\n", "        is_after_start = not start_ts or (record_ts and record_ts > start_ts)\n", "        is_before_end = not end_ts or (record_ts and record_ts <= end_ts)\n", "        \n", "        if is_after_start and is_before_end:\n", "            # Extract user emails from the record\n", "            emails = record.get(\"user_emails\", [])\n", "            if emails:\n", "                for email in emails:\n", "                    updated_emails.add(email)\n", "    \n", "    logger.info(f\"Total unique user emails collected in timestamp range: {len(updated_emails)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_entity_category(\n", "    entity_name, category, model_trigger_time, debug_mode=False, starrocks_config=None\n", "):\n", "    \"\"\"Process all forecasts for a specific entity category.\"\"\"\n", "    logger.info(\"-\" * 60)\n", "    logger.info(f\"STARTING {entity_name.upper()}.{category.upper()} UPDATE PROCESS\")\n", "    logger.info(\"-\" * 60)\n", "\n", "    try:\n", "        logger.info(f\"Using model trigger time: {model_trigger_time}\")\n", "        logger.info(f\"Debug mode: {debug_mode}\")\n", "\n", "        entity_config = load_entity_config(entity_name, category)\n", "        model_configs = entity_config.get(\"models\", {})\n", "\n", "        if not model_configs:\n", "            logger.warning(\n", "                f\"No model configurations found for entity '{entity_name}' category '{category}'\"\n", "            )\n", "            return {\n", "                \"entity\": entity_name,\n", "                \"category\": category,\n", "                \"processed\": 0,\n", "                \"total\": 0,\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "            }\n", "\n", "        processed_count = 0\n", "        total_forecasts = 0\n", "\n", "        for model_id, model_config in model_configs.items():\n", "            forecast_types = model_config.get(\"forecast_types\", [])\n", "            total_forecasts += len(forecast_types)\n", "\n", "            for forecast_type in forecast_types:\n", "                try:\n", "                    logger.info(\n", "                        f\"Processing {forecast_type} forecasts for model {model_id} in category {category}\"\n", "                    )\n", "                    result = process_forecast(\n", "                        forecast_type,\n", "                        entity_config,\n", "                        table_suffix=model_id,\n", "                        model_trigger_time=model_trigger_time,\n", "                        debug_mode=debug_mode,\n", "                        starrocks_config=starrocks_config,\n", "                    )\n", "                    if result:\n", "                        processed_count += 1\n", "                except Exception as e:\n", "                    logger.error(\n", "                        f\"Failed to process {forecast_type} forecast for model {model_id} in category {category}: {str(e)}\"\n", "                    )\n", "\n", "        logger.info(\n", "            f\"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}.{category}\"\n", "        )\n", "        logger.info(\"-\" * 60)\n", "        logger.info(f\"{entity_name.upper()}.{category.upper()} UPDATE PROCESS COMPLETED\")\n", "        logger.info(\"-\" * 60)\n", "\n", "        return {\n", "            \"entity\": entity_name,\n", "            \"category\": category,\n", "            \"processed\": processed_count,\n", "            \"total\": total_forecasts,\n", "            \"success\": processed_count > 0,\n", "        }\n", "\n", "    except Exception as e:\n", "        logger.error(\n", "            f\"Fatal error processing entity '{entity_name}' category '{category}': {str(e)}\"\n", "        )\n", "        return {\n", "            \"entity\": entity_name,\n", "            \"category\": category,\n", "            \"processed\": 0,\n", "            \"total\": 0,\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e),\n", "        }\n", "\n", "\n", "def process_entity(entity_name, model_trigger_time, debug_mode=False, starrocks_config=None):\n", "    \"\"\"Process all forecasts for a specific entity (all categories).\"\"\"\n", "    logger.info(\"=\" * 80)\n", "    logger.info(f\"STARTING {entity_name.upper()} UPDATE PROCESS\")\n", "    logger.info(\"=\" * 80)\n", "\n", "    try:\n", "        # Check if entity has categories\n", "        try:\n", "            categories = get_entity_categories(entity_name)\n", "            logger.info(f\"Entity '{entity_name}' has categories: {categories}\")\n", "\n", "            # Process each category\n", "            all_results = []\n", "            total_processed = 0\n", "            total_forecasts = 0\n", "            successful_categories = 0\n", "\n", "            for category in categories:\n", "                logger.info(f\"Processing category '{category}' for entity '{entity_name}'\")\n", "                result = process_entity_category(\n", "                    entity_name, category, model_trigger_time, debug_mode, starrocks_config\n", "                )\n", "                all_results.append(result)\n", "\n", "                total_processed += result[\"processed\"]\n", "                total_forecasts += result[\"total\"]\n", "                if result[\"success\"]:\n", "                    successful_categories += 1\n", "\n", "            logger.info(\"=\" * 60)\n", "            logger.info(f\"ENTITY {entity_name.upper()} CATEGORY SUMMARY\")\n", "            logger.info(\"=\" * 60)\n", "\n", "            for result in all_results:\n", "                category = result[\"category\"]\n", "                processed = result[\"processed\"]\n", "                total = result[\"total\"]\n", "                success = result[\"success\"]\n", "                status = \"✅ SUCCESS\" if success else \"❌ FAILED\"\n", "                logger.info(f\"  {category}: {processed}/{total} forecasts processed - {status}\")\n", "\n", "            logger.info(\"=\" * 80)\n", "            logger.info(f\"{entity_name.upper()} UPDATE PROCESS COMPLETED\")\n", "            logger.info(f\"Categories: {successful_categories}/{len(categories)} successful\")\n", "            logger.info(f\"Total forecasts: {total_processed}/{total_forecasts} processed\")\n", "            logger.info(\"=\" * 80)\n", "\n", "            return {\n", "                \"entity\": entity_name,\n", "                \"categories\": all_results,\n", "                \"processed\": total_processed,\n", "                \"total\": total_forecasts,\n", "                \"success\": successful_categories > 0,\n", "            }\n", "\n", "        except ValueError as e:\n", "            # Legacy single-category entity or entity without categories\n", "            if \"multiple categories\" not in str(e):\n", "                logger.info(f\"Entity '{entity_name}' is a legacy single-category entity\")\n", "                entity_config = load_entity_config(entity_name)\n", "                model_configs = entity_config.get(\"models\", {})\n", "\n", "                if not model_configs:\n", "                    logger.warning(f\"No model configurations found for entity '{entity_name}'\")\n", "                    return {\"entity\": entity_name, \"processed\": 0, \"total\": 0, \"success\": False}\n", "\n", "                processed_count = 0\n", "                total_forecasts = 0\n", "\n", "                for model_id, model_config in model_configs.items():\n", "                    forecast_types = model_config.get(\"forecast_types\", [])\n", "                    total_forecasts += len(forecast_types)\n", "\n", "                    for forecast_type in forecast_types:\n", "                        try:\n", "                            logger.info(\n", "                                f\"Processing {forecast_type} forecasts for model {model_id}\"\n", "                            )\n", "                            result = process_forecast(\n", "                                forecast_type,\n", "                                entity_config,\n", "                                table_suffix=model_id,\n", "                                model_trigger_time=model_trigger_time,\n", "                                debug_mode=debug_mode,\n", "                                starrocks_config=starrocks_config,\n", "                            )\n", "                            if result:\n", "                                processed_count += 1\n", "                        except Exception as e:\n", "                            logger.error(\n", "                                f\"Failed to process {forecast_type} forecast for model {model_id}: {str(e)}\"\n", "                            )\n", "\n", "                logger.info(\n", "                    f\"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}\"\n", "                )\n", "                logger.info(\"=\" * 80)\n", "                logger.info(f\"{entity_name.upper()} UPDATE PROCESS COMPLETED\")\n", "                logger.info(\"=\" * 80)\n", "\n", "                return {\n", "                    \"entity\": entity_name,\n", "                    \"processed\": processed_count,\n", "                    \"total\": total_forecasts,\n", "                    \"success\": processed_count > 0,\n", "                }\n", "            else:\n", "                raise e\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Fatal error processing entity '{entity_name}': {str(e)}\")\n", "        return {\n", "            \"entity\": entity_name,\n", "            \"processed\": 0,\n", "            \"total\": 0,\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e),\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_all_entities(model_trigger_time, debug_mode=False, starrocks_config=None):\n", "    \"\"\"Process all entities defined in the YAML configuration.\"\"\"\n", "    logger.info(\"=\" * 80)\n", "    logger.info(\"STARTING MULTI-ENTITY UPDATE PROCESS\")\n", "    logger.info(\"=\" * 80)\n", "\n", "    try:\n", "        entities = get_available_entities()\n", "        logger.info(f\"Found {len(entities)} entities to process: {entities}\")\n", "\n", "        results = []\n", "\n", "        for entity_name in entities:\n", "            try:\n", "                result = process_entity(\n", "                    entity_name, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG\n", "                )\n", "                results.append(result)\n", "            except Exception as e:\n", "                logger.error(f\"Failed to process entity '{entity_name}': {str(e)}\")\n", "                results.append(\n", "                    {\n", "                        \"entity\": entity_name,\n", "                        \"processed\": 0,\n", "                        \"total\": 0,\n", "                        \"success\": <PERSON><PERSON><PERSON>,\n", "                        \"error\": str(e),\n", "                    }\n", "                )\n", "\n", "        logger.info(\"=\" * 80)\n", "        logger.info(\"MULTI-ENTITY UPDATE PROCESS SUMMARY\")\n", "        logger.info(\"=\" * 80)\n", "\n", "        total_processed = 0\n", "        total_forecasts = 0\n", "        successful_entities = 0\n", "\n", "        for result in results:\n", "            entity_name = result[\"entity\"]\n", "            processed = result[\"processed\"]\n", "            total = result[\"total\"]\n", "            success = result[\"success\"]\n", "\n", "            status = \"✅ SUCCESS\" if success else \"❌ FAILED\"\n", "\n", "            # Check if this is a category-based entity result\n", "            if \"categories\" in result:\n", "                categories = result[\"categories\"]\n", "                logger.info(f\"{entity_name}: {processed}/{total} forecasts processed - {status}\")\n", "                for cat_result in categories:\n", "                    category = cat_result[\"category\"]\n", "                    cat_processed = cat_result[\"processed\"]\n", "                    cat_total = cat_result[\"total\"]\n", "                    cat_success = cat_result[\"success\"]\n", "                    cat_status = \"✅ SUCCESS\" if cat_success else \"❌ FAILED\"\n", "                    logger.info(\n", "                        f\"  └─ {category}: {cat_processed}/{cat_total} forecasts - {cat_status}\"\n", "                    )\n", "            else:\n", "                # Legacy single-category entity\n", "                logger.info(f\"{entity_name}: {processed}/{total} forecasts processed - {status}\")\n", "\n", "            total_processed += processed\n", "            total_forecasts += total\n", "            if success:\n", "                successful_entities += 1\n", "\n", "        logger.info(\"=\" * 80)\n", "        logger.info(f\"OVERALL SUMMARY: {successful_entities}/{len(entities)} entities successful\")\n", "        logger.info(f\"TOTAL FORECASTS: {total_processed}/{total_forecasts} processed\")\n", "        logger.info(\"=\" * 80)\n", "\n", "        return results\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Fatal error in multi-entity processing: {str(e)}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Main execution logic\n", "try:\n", "    # Get the initial max timestamp from StarRocks\n", "    logger.info(\"Fetching initial max timestamp from StarRocks\")\n", "    initial_max_ts = get_max_updated_timestamp('planner_instore_forecasts_weekly', 'updated_ts', schema=\"storeops_etls\", debug_mode=DEBUG_MODE, starrocks_config=STARROCKS_CONFIG)\n", "    if initial_max_ts:\n", "        logger.info(f\"Initial max timestamp from StarRocks: {initial_max_ts}\")\n", "    else:\n", "        logger.warning(\"No initial timestamp found from StarRocks\")\n", "    \n", "    # Fetch forecast inputs from API with user_emails field\n", "    logger.info(\"Fetching forecast inputs from API\")\n", "    forecast_data = get_forecasts_inputs_with_emails()\n", "    if forecast_data:\n", "        logger.info(f\"Successfully fetched forecast data with {len(forecast_data.get('data', []))} records\")\n", "    else:\n", "        logger.error(\"Failed to fetch forecast inputs from API\")\n", "    \n", "    # Handle list entities request\n", "    if LIST_ENTITIES_ONLY:\n", "        entities = get_available_entities()\n", "        print(\"Available entities:\")\n", "        for entity in entities:\n", "            print(f\"  - {entity}\")\n", "    else:\n", "        # Process specific entity or all entities\n", "        if ENTITY_NAME:\n", "            # Process single entity\n", "            available_entities = get_available_entities()\n", "            if ENTITY_NAME not in available_entities:\n", "                logger.error(f\"Entity '{ENTITY_NAME}' not found in configuration\")\n", "                logger.error(f\"Available entities: {available_entities}\")\n", "                raise ValueError(f\"Entity '{ENTITY_NAME}' not found\")\n", "\n", "            result = process_entity(ENTITY_NAME, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)\n", "            if not result[\"success\"]:\n", "                raise RuntimeError(f\"Failed to process entity '{ENTITY_NAME}'\")\n", "        else:\n", "            # Process all entities\n", "            results = process_all_entities(MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)\n", "\n", "            # Check if any entity failed\n", "            failed_entities = [r for r in results if not r[\"success\"]]\n", "            if failed_entities:\n", "                failed_names = [r[\"entity\"] for r in failed_entities]\n", "                logger.error(f\"Some entities failed to process: {failed_names}\")\n", "                raise RuntimeError(f\"Some entities failed: {failed_names}\")\n", "    \n", "    # Get the final max timestamp from StarRocks after processing\n", "    logger.info(\"Fetching final max timestamp from StarRocks\")\n", "    final_max_ts = get_max_updated_timestamp('planner_instore_forecasts_weekly', 'updated_ts', schema=\"storeops_etls\", debug_mode=DEBUG_MODE, starrocks_config=STARROCKS_CONFIG)\n", "    if final_max_ts:\n", "        logger.info(f\"Final max timestamp from StarRocks: {final_max_ts}\")\n", "    else:\n", "        logger.warning(\"No final timestamp found, using initial timestamp\")\n", "    \n", "    if forecast_data:\n", "        # Collect emails only from records between initial and final timestamps\n", "        collect_user_emails_in_range(forecast_data, initial_max_ts, final_max_ts)\n", "    \n", "    # Display user activity summary\n", "    display_user_activity_summary(updated_emails, initial_max_ts, final_max_ts, DEBUG_MODE, SLACK_CHANNEL, STARROCKS_CONFIG)\n", "\n", "except Exception as e:\n", "    logger.error(f\"Fatal error: {str(e)}\", exc_info=True)\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}