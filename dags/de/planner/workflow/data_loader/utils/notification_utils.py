
import logging
import pencilbox as pb

from utils.starrocks_utils import get_slack_ids_for_emails

logger = logging.getLogger(__name__)

def send_slack_notification(email_to_slack_id, start_ts, end_ts, slack_channel):
    """
    Send a Slack notification to users who made updates
    
    Args:
        email_to_slack_id (dict): Dictionary mapping email to slack ID
        start_ts (str): Start timestamp for the update period
        end_ts (str): End timestamp for the update period
        slack_channel (str): Slack channel to send the notification to
        
    Returns:
        bool: True if notification was sent, False otherwise
    """
    try:
        if not email_to_slack_id:
            logger.info("No users to notify")
            return False
            
        # Format user mentions
        user_mentions = [f"<@{slack_id}>" for slack_id in email_to_slack_id.values()]
        
        # Format message
        text = f"🔄 *PlanIt - Data Processing Notification*\n\n"
        text += f"Inputs from `{start_ts}` to `{end_ts}` have been processed.\n\n"
        text += "Users affected: " + ", ".join(user_mentions) + "\n"
        
        # Send message
        logger.info(f"Sending Slack notification to {len(user_mentions)} users")
        message = pb.send_slack_message(
            channel=slack_channel,
            text=text
        )
        
        logger.info(f"Slack notification sent")
        return True
        
    except Exception as e:
        logger.error(f"Error sending Slack notification: {str(e)}")
        return False
    
def display_user_activity_summary(updated_emails, initial_max_ts, final_max_ts, debug_mode, slack_channel, starrocks_config):
    """
    Display a summary of user emails that have made updates and send Slack notification
    
    Args:
        updated_emails (set): Set of user emails that have made updates
        initial_max_ts (str): Initial maximum timestamp
        final_max_ts (str): Final maximum timestamp
        debug_mode (bool): Whether to run in debug mode
        slack_channel (str): Slack channel to send the notification to
        starrocks_config (dict): StarRocks configuration
    """
    if not updated_emails:
        logger.info("No user activity detected during this run")
        return
    
    # First print all user emails for the log
    logger.info("="*80)
    logger.info("USER ACTIVITY SUMMARY")
    logger.info("="*80)
    logger.info(f"Total unique users who made updates: {len(updated_emails)}")
    logger.info("User emails:")
    
    for email in sorted(updated_emails):
        logger.info(f"  - {email}")
    
    logger.info("="*80)
    
    # After printing emails, get Slack IDs and send notification
    logger.info("Sending Slack notifications to users")
    
    # Get Slack IDs for the emails
    email_to_slack_id = get_slack_ids_for_emails(updated_emails, debug_mode, starrocks_config)
    
    # Send notification if we have Slack IDs and timestamps
    if email_to_slack_id:
        send_slack_notification(email_to_slack_id, initial_max_ts, final_max_ts, slack_channel)
        logger.info(f"Slack notifications sent to {len(email_to_slack_id)} users")
    else:
        logger.info("No Slack notifications sent - missing IDs or timestamps")
