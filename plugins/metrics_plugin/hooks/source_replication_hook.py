import json
import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry

from airflow.exceptions import AirflowException
from airflow.hooks.base_hook import BaseHook

SOURCE_REPLICATION_UI_URL = (
    "https://source-replication.grofer.io"
)
SOURCE_REPLICATION_API_VERSION = "1"


class SourceReplicationHook(BaseHook):
    """
    Returns the stored schema for the give source replication table
    by calling the Source Replication UI Service

    :param db_name: The source database name whose information needs to be fetched
    :type db_name: str
    :param table_name: The source table name whose information needs to be fetched
    :type table_name: str
    :param api_version: the latest API version of the Source Replication Service
    :type api_version: int/str
    """

    def __init__(self, db_name, table_name, s3_url, schema_name=None, *args, **kwargs):
        self.db_name = db_name
        self.table_name = table_name
        self.schema_name = schema_name
        self.s3_url = s3_url.strip('/')

    def get_column_dtypes(self):
        # TODO: Make the API call authenticated
        headers = {"Accept": "application/json"}
        if not self.db_name:
            if self.schema_name == "lake_grofers_db":
                self.db_name = "grofers_db_new1"
            elif self.schema_name == "lake_logistics":
                self.db_name = "logistics_server"
            elif self.schema_name == "lake_finance":
                self.db_name = "finance_db"
            else:
                self.db_name = self.schema_name[5:]

        full_table_name = ".".join(self.s3_url.split("/")[-1].split(".")[2:])
        url = f"{SOURCE_REPLICATION_UI_URL}/api/v{SOURCE_REPLICATION_API_VERSION}/tables/db:{self.db_name}:tbl:{full_table_name}"
        s = requests.Session()
        retries = Retry(total=3, backoff_factor=1, status_forcelist=[502, 503, 504])
        s.mount('https://', HTTPAdapter(max_retries=retries))
        res = s.get(url, headers=headers)
        column_dtypes = []
        if res.ok:
            column_dtypes = res.json().get("redshift_column_dtypes", [])
        else:
            self.log.info(
                f"API failed with status: {res.reason}({res.status_code})\n Response: {res.text}"
            )
        return column_dtypes
