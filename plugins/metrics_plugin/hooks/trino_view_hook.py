import pencilbox as pb

from airflow.hooks.base_hook import BaseHook



class TrinoViewHook(BaseHook):
    """
    Operator to create or refresh a view in Trino
    """

    def __init__(self, view_catalog: str, view_schema: str, view_name: str, security_type: str, view_definition: str, materialized: bool = False, grace_period: str = None, partition_columns: list = [], *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.view_catalog = view_catalog
        self.view_schema = view_schema
        self.view_name = view_name
        self.security_type = security_type
        self.view_definition = view_definition
        self.materialized = materialized
        self.grace_period = grace_period
        self.partition_columns = partition_columns

    def create(self):
        trino_connection = pb.get_connection("[ETL] Trino")
        if self.materialized:
            view_type = "MATERIALIZED VIEW"
            
            # Construct partition clause separately
            if self.partition_columns and len(self.partition_columns) > 0:
                joined_columns = ', '.join([f"'{col}'" for col in self.partition_columns])
                partition_clause = f"WITH (format = 'ORC', partitioning = ARRAY[{joined_columns}])"
            else:
                partition_clause = "WITH (format = 'ORC')"
            
            grace_clause = f"GRACE PERIOD {self.grace_period}" if self.grace_period else ""
            security_clause = ""
        else:
            view_type = "VIEW"
            partition_clause = ""
            grace_clause = ""
            security_clause = f"SECURITY {self.security_type}"
        
        view_ddl = f"""
            CREATE OR REPLACE {view_type} {self.view_catalog}.{self.view_schema}.{self.view_name} {security_clause} {partition_clause}
            AS
            {self.view_definition}
        """

        # Grace clause isn't being used currently; preference to performance is given instead of staleness
        try:
            trino_connection.execute(view_ddl).fetchall()
            print(f"*{self.view_schema}.{self.view_name}* has been created successfully.")
        except Exception as e:
            raise Exception(f"*{self.view_schema}.{self.view_name}* creation failed with: {e}")

    def refresh(self):
        if not self.materialized:
            return
        trino_connection = pb.get_connection("[ETL] Trino")
        refresh_view = f"""
            REFRESH MATERIALIZED VIEW {self.view_catalog}.{self.view_schema}.{self.view_name}
        """
        try:
            trino_connection.execute(refresh_view).fetchall()
            print(f"*{self.view_schema}.{self.view_name}* has been refreshed successfully.")
        except Exception as e:
            raise Exception(f"*{self.view_schema}.{self.view_name}* view refresh failed with {e}")
