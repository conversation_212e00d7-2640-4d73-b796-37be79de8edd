import asyncpg
from botocore.exceptions import Client<PERSON>rror
from asgiref.sync import sync_to_async

from airflow.exceptions import AirflowException
from airflow.hooks.base_hook import BaseHook
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from metrics_plugin.hooks.base_aws import AwsBaseHookAsync


class EmrStateHook(BaseHook):
    """
    Returns the state of an EMR step by reading it from a DB or fetching
    it via an API call, depending on the method specified by the user.

    If the EMR step status has to be read from a DB, the DB containg the
    status of the steps needs to be set up by the user by streaming or
    batching EMR events to a database in a table called `step_events`.
    The table should have the following columns:

    * step_id: The EMR step ids.
    * cluster_id: The EMR cluster ids.
    * step_state: State corresponding to the step_id.
    * event_timestamp: The time when this EMR event was generated.

    :param step_id: id of the step whose state has to be checked.
    :type step_id: str
    :param cluster_id: id of the cluster which contains the desired step.
    :type cluster_id: str
    :param db_conn_id: airflow connection id for the DB from where the step states will be queried.
    :type db_conn_id: str
    :param aws_conn_id: airflow connection id for the IAM user used for interacting with the cluster via EMR APIs.
    :type aws_conn_id: str
    """

    def __init__(self, step_id, cluster_id, db_conn_id, aws_conn_id, *args, **kwargs):
        self.step_id = step_id
        self.cluster_id = cluster_id
        self.db_conn_id = db_conn_id
        self.aws_conn_id = aws_conn_id

    def read_step_state_from_events(self):
        conn = BaseHook.get_connection(self.db_conn_id).get_hook()

        sql = (
            "SELECT step_state "
            "FROM step_events "
            "WHERE step_id = '{step_id}' "
            "ORDER BY event_timestamp DESC LIMIT 1"
        ).format(step_id=self.step_id)
        records = conn.get_records(sql)

        if not records:
            message = (
                f"No event found for step_id: {self.step_id}. "
                "State unknown, defaulting to 'PENDING'."
            )
            self.log.info(message)
            state = "PENDING"
        else:
            state = records[0][0]

        return state

    def read_step_state_from_api(self):
        emr = EmrHook(aws_conn_id=self.aws_conn_id).get_conn()

        response = emr.describe_step(ClusterId=self.cluster_id, StepId=self.step_id)
        if not response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            self.log.info("Bad HTTP response: %s.", response)
            self.log.info("State unknown, defaulting to 'PENDING'.")
            state = "PENDING"
        else:
            state = response["Step"]["Status"]["State"]

        return state

    def get_step_state(self, method):
        if method == "events":
            self.log.info("Polling step state via events DB.")
            state = self.read_step_state_from_events()
        elif method == "api":
            self.log.info("Polling step state via EMR APIs.")
            state = self.read_step_state_from_api()
        else:
            message = f"Poke method '{method}' is invalid. Allowed value are 'events' and 'api'."
            raise ValueError(message)

        return state
    def get_failure_reason(self):
        """
        Get the failure reason for a failed EMR step by making an API call.
        
        :return: The failure reason as a string
        :rtype: str
        """
        emr = EmrHook(aws_conn_id=self.aws_conn_id).get_conn()
        
        try:
            response = emr.describe_step(ClusterId=self.cluster_id, StepId=self.step_id)
            if not response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                self.log.error("Bad HTTP response: %s.", response)
                return "Failed to retrieve failure reason due to bad HTTP response"
            
            step_details = response["Step"]
            failure_details = step_details.get("Status", {}).get("FailureDetails", {})
            
            if failure_details:
                reason = failure_details.get("Reason", "No reason provided")
                message = failure_details.get("Message", "")
                log_file = failure_details.get("LogFile", "")
                
                failure_reason = f"Reason: {reason}"
                if message:
                    failure_reason += f", Message: {message}"
                if log_file:
                    failure_reason += f", Log file: {log_file}"
                
                return failure_reason
            else:
                return f"Step is in {step_details.get('Status', {}).get('State', 'UNKNOWN')} state but no failure details available"
        
        except Exception as e:
            self.log.error("Error retrieving failure reason: %s", str(e))
            return f"Failed to retrieve failure reason: {str(e)}"

class EmrStateHookAsync(AwsBaseHookAsync):
    """
    A thin wrapper to interact with AWS EMR API
    Additional arguments may be specified and are passed down to the underlying AwsBaseHook.
    For more details see here.
        - airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook
    :param cluster_id: id of the cluster which contains the desired step.
    :param step_id: step to check the state of
    :param client_type: boto3.client client_type. Eg 's3', 'emr' etc
    :param resource_type: boto3.resource resource_type. Eg 'dynamodb' etc
    :param config: Configuration for botocore client.
    """

    def __init__(self, cluster_id, step_id, db_conn_id, *args, **kwargs):
        kwargs["client_type"] = "emr"
        kwargs["resource_type"] = "emr-containers"
        super().__init__(*args, **kwargs)
        self.cluster_id = cluster_id
        self.step_id = step_id
        self.db_conn_id = db_conn_id

    async def read_step_state_from_events(self):
        conn = await sync_to_async(BaseHook.get_connection)(self.db_conn_id)
        async_conn = await asyncpg.connect(user=conn.login, password=conn.password,
                                    database=conn.schema, host=conn.host)
        try:
            sql = (
                "SELECT step_state "
                "FROM step_events "
                "WHERE step_id = '{step_id}' "
                "ORDER BY event_timestamp DESC LIMIT 1"
            ).format(step_id=self.step_id)
            row = await async_conn.fetchrow(sql)
            if not row:
                message = (
                    f"No event found for step_id: {self.step_id}. "
                    "State unknown, defaulting to 'PENDING'."
                )
                self.log.info(message)
                state = "PENDING"
            else:
                state = row[0]
        except Exception as e:
            self.log.error(str(e))
            raise e
        finally:
            await async_conn.close()
        return state

    async def read_step_state_from_api(self):
        async with await self.get_client_async() as client:
            try:
                response = await client.describe_step(
                    ClusterId=self.cluster_id, StepId=self.step_id
                )
                if not response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                    self.log.info("Bad HTTP response: %s.", response)
                    self.log.info("State unknown, defaulting to 'PENDING'.")
                    state = "PENDING"
                else:
                    state = response["Step"]["Status"]["State"]
                return state
            except ClientError as error:
                raise error

    async def get_step_state(self, method):
        if method == "events":
            self.log.info("Polling step state via events DB.")
            state = await self.read_step_state_from_events()
        elif method == "api":
            self.log.info("Polling step state via EMR APIs.")
            state = await self.read_step_state_from_api()
        else:
            message = f"Poke method '{method}' is invalid. Allowed value are 'events' and 'api'."
            raise ValueError(message)

        return state
    
    async def get_cluster_status(self):
        """
        Get the current status of the EMR cluster asynchronously
        
        :return: The cluster status object or None if there was an error
        """
        self.log.info("Getting status for cluster %s", self.cluster_id)

        try:
            async with await self.get_client_async() as client:
                response = await client.describe_cluster(ClusterId=self.cluster_id)
                if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
                    self.log.error("Bad HTTP response: %s", response)
                    return {}

                return response["Cluster"]["Status"]

        except Exception as e:
            self.log.error("Error getting cluster status: %s", str(e))
            return {}

    async def get_step_status(self):
        """
        Get the current status of a specific EMR step asynchronously

        :param step_id: The ID of the EMR step
        :return: The step status object or empty dict if there was an error
        """
        self.log.info("Getting status for step %s in cluster %s", self.step_id, self.cluster_id)

        try:
            async with await self.get_client_async() as client:
                response = await client.describe_step(ClusterId=self.cluster_id, StepId=self.step_id)
                if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
                    self.log.error("Bad HTTP response: %s", response)
                    return {}

                return response["Step"]["Status"]

        except Exception as e:
            self.log.error("Error getting step status: %s", str(e))
            return {}

