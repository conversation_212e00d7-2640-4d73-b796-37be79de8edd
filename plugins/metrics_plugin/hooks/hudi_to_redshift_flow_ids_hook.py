from datetime import datetime

import ast
import pandas as pd
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable


class HudiToRedshiftFlowIdsHook(BaseHook):
    """
    Determines if the hudi_to_redshift flow is to be run for any of the given
    tables at a given time, and returns the flow ids for all such tables.

    :param candidate_flow_ids: list of flow ids under consideration
    :type candidate_flow_ids: list
    :param db_engine: sqlalchemy engine for the nessie metadata DB
    :type db_engine: class 'sqlalchemy.engine.base.Engine'
    :param redshift_sla_seconds: SLA for the redshift job in seconds
    :type redshift_sla_seconds: int
    """

    def __init__(
        self,
        candidate_flow_ids,
        redshift_sla_seconds,
        db_engine,
    ):
        self.candidate_flow_ids = candidate_flow_ids
        self.db_engine = db_engine
        self.redshift_sla_seconds = redshift_sla_seconds

    def get_last_s3_hudi_flow_times(self, flow_ids_str, conn):
        """
        Returns the last-run time of s3_hudi flow for all given flow ids.

        Metadata for the s3_to_hudi flow is stored in the s3_hudi_flow_logs
        table hence last_hudi_commit_timestamp read from this table is the
        accurate estimate for the last run time of the s3_hudi flow.
        """
        last_s3_hudi_flow_ts_query = (
            "SELECT flow_id, last_hudi_commit_timestamp as last_run_ts FROM s3_hudi_flow_logs "
            f"WHERE flow_id IN ({flow_ids_str}) AND last_hudi_commit_timestamp IS NOT NULL"
        )
        last_s3_hudi_flow_ts_df = pd.read_sql_query(last_s3_hudi_flow_ts_query, conn)
        last_s3_hudi_flow_ts_df["last_run_ts"] = last_s3_hudi_flow_ts_df[
            "last_run_ts"
        ].map(lambda ts: datetime.strptime(ts, "%Y%m%d%H%M%S%f") if len(ts) > 14 else datetime.strptime(ts, "%Y%m%d%H%M%S"))

        return dict(
            zip(last_s3_hudi_flow_ts_df.flow_id, last_s3_hudi_flow_ts_df.last_run_ts)
        )

    def get_last_hudi_redshift_flow_times(self, flow_ids_str, conn):
        """
        Returns the last-run time of hudi_redshift flow for all given flow ids.

        Metadata for the hudi_to_redshift flow is stored in the hudi_inc_s3_flow_logs
        table hence last_hudi_commit_timestamp read from this table is the accurate
        estimate for the last run time of hudi_redshift flow.
        """

        last_hudi_redshift_flow_ts_query = (
            "SELECT flow_id, last_hudi_commit_timestamp AS last_run_ts "
            "FROM hudi_inc_s3_flow_logs "
            f"WHERE flow_id IN ({flow_ids_str}) AND last_hudi_commit_timestamp IS NOT NULL"
        )
        last_hudi_redshift_flow_ts_df = pd.read_sql_query(
            last_hudi_redshift_flow_ts_query, conn
        )
        last_hudi_redshift_flow_ts_df["last_run_ts"] = last_hudi_redshift_flow_ts_df[
            "last_run_ts"
        ].map(lambda ts: datetime.strptime(ts, "%Y%m%d%H%M%S%f") if len(ts) > 14 else datetime.strptime(ts, "%Y%m%d%H%M%S"))

        return dict(
            zip(
                last_hudi_redshift_flow_ts_df.flow_id,
                last_hudi_redshift_flow_ts_df.last_run_ts,
            )
        )

    def get_updated_table_flow_ids(self, hook_exec_ts=None):
        """
        returns the flow_ids for tables which have to be updated on redshift.

        If last_s3_hudi_flow_run_ts - last_hudi_redshift_flow_run_ts is a
        positive value then we can be assured that changes are present in the
        lake. Further, if the value is greater than the redshift_sla_seconds
        for the table, then we can conclude that the SLA criterion for the table
        is met and it needs to be updated on redshift.

        If the lake is guaranteed to have updates at some future time, hook_exec_ts
        can also be explicitly set to that time to predict if the hudi_redshift
        will run at that point in time.
        """
        # Flag to enable and disable hudi_to_redshift flow
        if not ast.literal_eval(Variable.get("HUDI_TO_REDSHIFT_ENABLED", default_var="True")):
            self.log.info("Hudi to redshift flow is disabled")
            return []

        flow_ids_str = ", ".join([f"'{flow}'" for flow in self.candidate_flow_ids])

        if hook_exec_ts:
            last_s3_hudi_flow_times = {
                flow_id: hook_exec_ts for flow_id in self.candidate_flow_ids
            }
        else:
            last_s3_hudi_flow_times = self.get_last_s3_hudi_flow_times(
                flow_ids_str, self.db_engine
            )

        last_hudi_redshift_flow_times = self.get_last_hudi_redshift_flow_times(
            flow_ids_str, self.db_engine
        )

        updated_tables_flow_ids = []
        for flow_id in self.candidate_flow_ids:
            # This condition makes sure that we skip the hudi_redshift flow
            # if, for any reason, the s3_hudi flow has never run for it yet.
            if flow_id not in last_s3_hudi_flow_times:
                continue

            # If `last_hudi_commit_timestamp` is NULL in `hudi_inc_s3_flow_logs`
            # table, then we always run the hudi_redshift flow (because NULL value
            # can mean that a new table is being added). When full load of a table
            # is required on redshift, we can set `last_hudi_commit_timestamp` to
            # NULL in the `hudi_inc_s3_flow_logs` table to make sure that
            # hudi_redshift flow is not skipped by any chance.
            if flow_id not in last_hudi_redshift_flow_times:
                updated_tables_flow_ids.append(flow_id)
                continue

            last_s3_hudi_flow_ts = last_s3_hudi_flow_times[flow_id]
            last_hudi_redshift_flow_ts = last_hudi_redshift_flow_times[flow_id]

            flows_time_diff = (
                last_s3_hudi_flow_ts - last_hudi_redshift_flow_ts
            ).total_seconds()

            if flows_time_diff >= self.redshift_sla_seconds:
                updated_tables_flow_ids.append(flow_id)
            elif flows_time_diff > 0 and (datetime.utcnow() - last_hudi_redshift_flow_ts).total_seconds() > self.redshift_sla_seconds:
                # For cases when hudi_redshift flow has run just before the s3_hudi flow and table stops getting updated
                updated_tables_flow_ids.append(flow_id)

        return list(set(updated_tables_flow_ids))
