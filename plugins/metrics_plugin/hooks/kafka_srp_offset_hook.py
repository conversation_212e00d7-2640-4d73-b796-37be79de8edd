import ast
import pandas as pd
import sys
import tenacity

from airflow.hooks.base_hook import <PERSON><PERSON>ook
from airflow.providers.http.hooks.http import HttpHook
from airflow.models import Variable


class KafkaSRPOffsetHook(BaseHook):
    """Returns the count of new changes to a kafka topic by taking the difference of current 
    kafka topic offset and the existing marker offset

    :param kafka_connection_id: Kafka connection id defined in Airflow connections
    :type kafka_connection_id: str
    :param db_connection: Connection engine to SRP DB
    :type db_conneciton: class 'sqlalchemy.engine.base.Engine'
    :param flow_id_topic_mapping: Kafka topic name mapped to corresponding SRP flow_id
    :type flow_id_topic_mapping: str
    """
    def __init__(self, kafka_connection_id, db_connection, flow_id_topic_mapping=None):
        self.kafka_connection_id = kafka_connection_id
        self.db_connection = db_connection
        self.flow_id_topic_mapping = flow_id_topic_mapping or {}

    def get_marker_offsets(self, flow_ids):
        """Get the offset value till which data has been already processed

        :param flow_ids: Flow ids
        :type flow_ids: list(str)
        :return: Marker offset values
        :rtype: list(int)
        """
        flow_ids_str = ", ".join([f"'{flow}'" for flow in flow_ids])
        query = (
            "SELECT flow_id, max_kafka_topic_offset_read "
            "FROM s3_hudi_flow_logs "
            "WHERE flow_id IN ({flow_ids_str})"
        ).format(flow_ids_str=flow_ids_str)

        markers_df = pd.read_sql_query(query, con=self.db_connection)
        marker_offsets = dict(
            zip(markers_df.flow_id, markers_df.max_kafka_topic_offset_read)
        )

        for flow_id in flow_ids:
            if flow_id not in marker_offsets:
                marker_offsets[flow_id] = 0

        return marker_offsets

    def get_current_offset(self, kafka_topic_name):
        """Get the current offset value of a kafka topic

        :param kafka_topic_name: Kafka topic name
        :type kafka_topic_name: str
        :return: Offset value
        :rtype: int
        """
        # We only have 1 partition per topic with partition_id = 0
        endpoint = f"topics/{kafka_topic_name}/partitions/0/offsets"
        retry_args = dict(
            wait=tenacity.wait_exponential(), stop=tenacity.stop_after_attempt(5)
        )

        http_hook = HttpHook(
            method="GET", http_conn_id=self.kafka_connection_id
        )

        resp = http_hook.run_with_advanced_retry(
            _retry_args=retry_args,
            endpoint=endpoint,
        )

        if resp.ok:
            return resp.json()["end_offset"]
        else:
            # We return max possible kafka offset beacause
            # failed api calls should not stop the flows.
            return sys.maxsize

    def offset_differences(self):
        """Get the count of new values in a kafka topic corresponding to its flow_id

        :return: Offset difference corresponding to the flow_id
        :rtype: dict

        :: Example Output:
        offset_differences_mapping = {
            "flow_id_1": 1000,
            "flow_id_2": 20000
        }
        """
        offset_differences_mapping = dict()
        marker_offsets = self.get_marker_offsets(list(self.flow_id_topic_mapping.keys()))
        for flow_id, kafka_topic_name in self.flow_id_topic_mapping.items():
            current_offset = self.get_current_offset(kafka_topic_name=kafka_topic_name)
            offset_differences_mapping[flow_id] = current_offset - marker_offsets[flow_id]

            self.log.info(f"Comparing offsets for topic {kafka_topic_name}")
            self.log.info(
                f"marker_offset: {marker_offsets[flow_id]}, current_offset: {current_offset}"
            )

        return offset_differences_mapping
