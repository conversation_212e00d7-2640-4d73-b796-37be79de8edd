import asyncio

from airflow.utils import timezone
from airflow.triggers.base import BaseTrigger, TriggerEvent
from metrics_plugin.hooks.emr_state_hook import EmrStateHookAsync

class EmrStateTrigger(BaseTrigger):
    """
    A trigger that fires once AWS EMR cluster step reaches either target or failed state
    :param cluster_id: cluster_id which contains the step to check the state of
    :param step_id: step to check the state of
    :param aws_conn_id: aws connection to use, defaults to 'aws_default'
    :param poke_interval: Time in seconds to wait between two consecutive call to
        check emr cluster step state
    :param target_states: the target states, sensor waits until
        step reaches any of these states
    :param failed_states: the failure states, sensor fails when
        step reaches any of these states
    """

    def __init__(
        self,
        cluster_id,
        step_id,
        aws_conn_id,
        db_conn_id,
        poke_method,
        poke_interval,
        event_sensor_timeout,
        sensor_start_time,
        target_states,
        failed_states,
        api_poke_interval=30,  # Add this parameter with default value
    ):
        super().__init__()
        self.cluster_id = cluster_id
        self.step_id = step_id
        self.aws_conn_id = aws_conn_id
        self.db_conn_id = db_conn_id
    
        self.poke_method = poke_method
        self.poke_interval = poke_interval
        self.event_sensor_timeout = event_sensor_timeout
        self.sensor_start_time = sensor_start_time
        self.api_poke_interval = api_poke_interval  # Use the parameter
    
        self.target_states = target_states or ["COMPLETED"]
        self.failed_states = failed_states or ["CANCELLED", "FAILED", "INTERRUPTED"]
        self.ideal_states = ["RUNNING", "WAITING"]

    def serialize(self):
        """Serializes EmrStateTrigger arguments and classpath."""
        return (
            "metrics_plugin.triggers.emr_state_trigger.EmrStateTrigger",
            {
                "cluster_id": self.cluster_id,
                "step_id": self.step_id,
                "aws_conn_id": self.aws_conn_id,
                "db_conn_id": self.db_conn_id,
                "poke_method": self.poke_method,
                "poke_interval": self.poke_interval,
                "event_sensor_timeout": self.event_sensor_timeout,
                "sensor_start_time": self.sensor_start_time,
                "target_states": self.target_states,
                "failed_states": self.failed_states,
                "api_poke_interval": self.api_poke_interval,  # Add this line
            },
        )

    async def set_poke_method_and_interval(self):
        time_elapsed = (timezone.utcnow() - self.sensor_start_time).total_seconds()
        self.log.info(f"Time elapsed: {time_elapsed}, event_sensor_timeout: {self.event_sensor_timeout}")
        self.log.info(f"Current poke_method: {self.poke_method}, poke_interval: {self.poke_interval}")
        self.log.info(f"api_poke_interval type: {type(self.api_poke_interval)}, value: {self.api_poke_interval}")

        if self.poke_method == "events" and time_elapsed > self.event_sensor_timeout:
            self.log.info("Event sensor timed out. Falling back to API sensor.")
            self.poke_method = "api"
            self.poke_interval = self.api_poke_interval
            self.log.info(f"Updated poke_method: {self.poke_method}, poke_interval: {self.poke_interval}")


    async def run(self):
        """Run until AWS EMR cluster step reach target or failed state"""
        try:
            self.log.info("Starting EmrStateTrigger.run()")
            await self.set_poke_method_and_interval()
            self.log.info("After set_poke_method_and_interval()")
            
            hook = EmrStateHookAsync(
                aws_conn_id=self.aws_conn_id,
                db_conn_id=self.db_conn_id,
                cluster_id=self.cluster_id,
                step_id=self.step_id
            )
            self.log.info("Created EmrStateHookAsync")
            
            while True:
                try:
                    self.log.info(f"Getting step state with method: {self.poke_method}")
                    state = await hook.get_step_state(method=self.poke_method)
                    if state in self.target_states:
                        yield TriggerEvent({"status": "success", "message": f"Job flow currently {state}"})
                    elif state in self.failed_states:
                        # Check cluster status
                        cluster_status = await hook.get_cluster_status()
                        cluster_state = cluster_status.get("State", "Unknown")
                        cluster_reason_code = cluster_status.get("StateChangeReason", {}).get("Code", "Unknown")
                        cluster_reason_message = cluster_status.get("StateChangeReason", {}).get("Message", "No message provided")
                        
                        # Base message with cluster URL and logs URL
                        base_message = """*Cluster URL*: https://ap-southeast-1.console.aws.amazon.com/emr/home?region=ap-southeast-1#/clusterDetails/{cluster_id}\n{space}*Logs*: https://ap-southeast-1.console.aws.amazon.com/s3/buckets/grofers-prod-dse-sgp?region=ap-southeast-1&bucketType=general&prefix=elasticmapreduce/{cluster_id}/steps/{step_id}/""".format(
                            cluster_id=self.cluster_id,
                            step_id=self.step_id,
                            space=" "*8
                        )
                        
                        # Different message based on cluster state
                        if cluster_state in self.ideal_states:
                            # If cluster is running but step has failed, get step status and include step details
                            step_status = await hook.get_step_status()
                            step_state = step_status.get("State", "Unknown")
                            
                            # Handle different step status formats
                            step_reason = "No reason provided"
                            if "FailureDetails" in step_status:
                                step_reason = step_status.get("FailureDetails", {}).get("Reason", "No reason provided")
                            elif "StateChangeReason" in step_status and step_status.get("StateChangeReason", {}).get("Message"):
                                step_reason = step_status.get("StateChangeReason", {}).get("Message")
                            
                            error_message = base_message + """\n{space}*Step State*: `{step_state}`\n{space}*Step Failure Reason*: `{step_reason}`""".format(
                                step_state=step_state,
                                step_reason=step_reason,
                                space=" "*8
                            )
                        else:
                            # If cluster is not running, include cluster details
                            error_message = base_message + """\n{space}*Cluster State*: `{cluster_state}`\n{space}*Reason Code*: `{cluster_reason_code}`\n{space}*Reason*: `{cluster_reason_message}`""".format(
                                cluster_state=cluster_state,
                                cluster_reason_code=cluster_reason_code,
                                cluster_reason_message=cluster_reason_message,
                                space=" "*8
                            )
                        
                        yield TriggerEvent({
                            "status": "error",
                            "message": error_message,
                        })
                    await asyncio.sleep(self.poke_interval)
                except Exception as inner_e:
                    yield TriggerEvent({"status": "error", "message": f"Error in step state check: {str(inner_e)}"})
                    break
        except Exception as e:
            yield TriggerEvent({"status": "error", "message": str(e)})
