# This is the class you derive to create a plugin
from airflow.plugins_manager import AirflowPlugin

# Importing base classes that we need to derive
from airflow.hooks.base_hook import BaseHook
from airflow.models import BaseOperator
from airflow.executors.base_executor import BaseExecutor
from airflow.sensors.base_sensor_operator import BaseSensorOperator

from metrics_plugin.operators.gmail_attachment_operator import GmailAttachmentOperator
from metrics_plugin.operators.mysql_download_operator import MySqlDownloadOperator
from metrics_plugin.operators.papermill_operator import PapermillOperator
from metrics_plugin.operators.create_hive_to_icerberg_table_operator import CreateHiveToIcebergOperator
from metrics_plugin.operators.pg_download_operator import PgDownloadOperator
from metrics_plugin.operators.redshift_to_s3_operator import RedshiftToS3Operator
from metrics_plugin.operators.s3_to_redshift_operator import S3ToRedshiftOperator
from metrics_plugin.operators.sheet_download_operator import SheetDownloadOperator
from metrics_plugin.operators.upstream_task_sensor import UpstreamTaskSensor
from metrics_plugin.operators.branch_srp_operator import BranchSRPOperator
from metrics_plugin.operators.dynamic_cluster_config_operator import (
    DynamicClusterConfigOperator,
)
from metrics_plugin.operators.branch_hudi_to_s3_operator import BranchHudiToS3Operator
# from metrics_plugin.operators.emr_state_sensor import EmrStateSensor
from metrics_plugin.operators.trino_create_view_operator import TrinoCreateViewOperator

from metrics_plugin.operators.emr_state_sensor import EmrStateSensorAsync, EmrStateSensor
from metrics_plugin.operators.srp_cluster_mapping_operator_v2 import (
    SRPClusterMappingOperatorV2,
)
from metrics_plugin.operators.srp_emr_bulk_add_steps_operator import (
    SRPEmrBulkAddStepsOperator,
)
from metrics_plugin.operators.presto_create_view_operator import PrestoCreateViewOperator

from metrics_plugin.operators.eks_submit_spark_job_operator import SparkKubernetesOperator
from metrics_plugin.operators.eks_poll_spark_job_sensor import SparkKubernetesSensor
from metrics_plugin.operators.ecs_update_service_count import ECSUpdateServiceCount
from metrics_plugin.operators.application_autoscaling_update import ApplicationAutoscalingUpdate

from metrics_plugin.hooks.google_api_hook import GoogleAPIBaseHook, GmailHook
from metrics_plugin.hooks.sheets_hook import GoogleCloudBaseHook
from metrics_plugin.hooks.emr_state_hook import EmrStateHook, EmrStateHookAsync
from metrics_plugin.hooks.kafka_srp_offset_hook import KafkaSRPOffsetHook
from metrics_plugin.hooks.hudi_to_redshift_flow_ids_hook import (
    HudiToRedshiftFlowIdsHook,
)
from metrics_plugin.hooks.source_replication_hook import SourceReplicationHook
from metrics_plugin.hooks.base_aws import AwsBaseHookAsync
from metrics_plugin.hooks.trino_create_view_hook import TrinoCreateViewHook
from metrics_plugin.hooks.trino_view_hook import TrinoViewHook

from metrics_plugin.triggers.emr_state_trigger import EmrStateTrigger
