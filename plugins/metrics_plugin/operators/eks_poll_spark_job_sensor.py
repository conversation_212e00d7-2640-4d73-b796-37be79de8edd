"""
Ref: https://airflow.apache.org/docs/apache-airflow-providers-cncf-kubernetes/stable/_modules/airflow/providers/cncf/kubernetes/sensors/spark_kubernetes.html#SparkKubernetesSensor
"""
from __future__ import annotations
import json
import time
from datetime import datetime, timedelta, timezone

from werkzeug.utils import cached_property
from typing import TYPE_CHECKING, Sequence

from kubernetes import client
from kubernetes.watch import Watch
from kubernetes.client import CoreV1Api, CustomObjectsApi

from airflow.exceptions import AirflowException, AirflowSkipException
from airflow.providers.cncf.kubernetes.hooks.kubernetes import KubernetesHook
from airflow.sensors.base import BaseSensorOperator

from metrics_plugin.operators.eks_spark.utils import log_utils
from metrics_plugin.operators.eks_spark.custom_object_launcher import CustomObjectLauncher

if TYPE_CHECKING:
    from airflow.utils.context import Context


class SparkKubernetesSensor(BaseSensorOperator):
    """
    Checks sparkApplication object in kubernetes cluster.

    see also::
        For more detail about Spark Application Object have a look at the reference:
        https://github.com/GoogleCloudPlatform/spark-on-k8s-operator/blob/v1beta2-1.1.0-2.4.5/docs/api-docs.md#sparkapplication

    :param application_name: spark Application resource name
    :param namespace: the kubernetes namespace where the sparkApplication reside in
    :param container_name: the kubernetes container name where the sparkApplication reside in
    :param kubernetes_conn_id: The :ref:`kubernetes connection<howto/connection:kubernetes>`
        to Kubernetes cluster.
    :param attach_log: determines whether logs for driver pod should be appended to the sensor log
    :param api_group: kubernetes api group of sparkApplication
    :param api_version: kubernetes api version of sparkApplication
    """
    template_fields: Sequence[str] = ("application_name", "namespace", "application_file")
    FAILURE_STATES = ("FAILED", "UNKNOWN")
    SUCCESS_STATES = ("COMPLETED",)
    DESIRED_STATES = ("RUNNING",)

    class SparkJobState:
        """Helper class to track the state of a Spark job."""

        def __init__(self, parent_logger):
            """Initialize the state tracking variables."""
            self.spark_context_stopped = False
            self.spark_stopped_time = None
            self.log = parent_logger.getChild('SparkJobState')
            self.WAITING_PERIOD = timedelta(minutes=5)

        def is_spark_stopped(self, line: str) -> bool:
            """Check if the line indicates SparkContext has stopped."""
            return (
                "Successfully stopped SparkContext" in line or
                ("Stopped Spark" in line and "HTTP/1.1" in line)
            )

        def update_state(self, line: str) -> None:
            """Update state based on log line content."""
            if self.is_spark_stopped(line):
                self.spark_context_stopped = True
                self.spark_stopped_time = datetime.now(timezone.utc)
                return

            # If we're waiting for completion, ignore metastore connection closure logs
            if self.spark_stopped_time is not None:
                if "Closed a connection to metastore," in line:
                    return

            # Update state only if it's not a metastore closure log during waiting period
            if self.is_spark_stopped(line):
                self.spark_context_stopped = True
                self.spark_stopped_time = datetime.now(timezone.utc)

        def is_complete(self) -> bool:
            """Check if the job is complete based on current state."""
            if not self.spark_context_stopped:
                return False
            
            if self.spark_stopped_time is None:
                return False
                
            # Check if waiting period has passed since Spark stopped
            elapsed_time = datetime.now(timezone.utc) - self.spark_stopped_time
            return elapsed_time >= self.WAITING_PERIOD





    def __init__(
            self,
            *,
            application_name: str,
            attach_log: bool = False,
            namespace: str | None = None,
            container_name: str = "base",
            kubernetes_conn_id: str = "kubernetes_default",
            api_group: str = "sparkoperator.k8s.io",
            api_version: str = "v1beta2",
            application_file: str = None,
            **kwargs,
    ) -> None:
        super().__init__(**kwargs)
        self.application_name = application_name
        self.attach_log = attach_log
        self.namespace = namespace
        self.container_name = container_name
        self.kubernetes_conn_id = kubernetes_conn_id
        self.api_group = api_group
        self.api_version = api_version
        self.application_file = application_file

    @cached_property
    def hook(self) -> KubernetesHook:
        return KubernetesHook(conn_id=self.kubernetes_conn_id)

    def _get_custom_driver_log(self, response: dict) -> bool:
        status_info = response["status"]
        if "driverInfo" not in status_info:
            return False
        driver_info = status_info["driverInfo"]
        if "podName" not in driver_info:
            return False
        driver_pod_name = driver_info["podName"]
        namespace = response["metadata"]["namespace"]
        pod_log_stream = Watch().stream(
            self.hook.core_v1_client.read_namespaced_pod_log,
            name=driver_pod_name,
            namespace=namespace,
            timestamps=True,
        )

        state = self.SparkJobState(self.log)
        
        # Check if we already have a complete state from previous logs
        if state.is_complete():
            return True

        for line in pod_log_stream:
            self.log.info(line)
            state.update_state(line)
            if state.is_complete():
                return True

        return False  # Return False if we exit the loop without meeting conditions

    def poke(self, context: Context) -> bool:
        self.log.info("Poking: %s", self.application_name)

        response = self.hook.get_custom_object(
            group=self.api_group,
            version=self.api_version,
            plural="sparkapplications",
            name=self.application_name,
            namespace=self.namespace,
        )

        try:
            application_state = response["status"]["applicationState"]["state"]
        except KeyError:
            return False

        if self.attach_log and application_state in self.DESIRED_STATES:
            if self._get_custom_driver_log(response):
                self.log.info(
                    log_utils.print_msg_box(msg="Spark context closed but spark application seems stuck. Marking this step success and deleting spark application gracefully.", indent=2)
                )
                # Clean up Spark application
                launcher = CustomObjectLauncher(
                    name=self.application_name,
                    namespace=self.namespace,
                    kube_client=self.client,
                    custom_obj_api=self.custom_obj_api,
                    template_body=json.loads(self.application_file)
                )
                try:
                    launcher.delete_spark_job(spark_job_name=self.application_name)
                    self.log.info(log_utils.print_msg_box(msg=f"Spark Application: {self.application_name} Deleted", indent=2))
                except Exception as e:
                    self.log.warning(f"Could not delete spark Application: {self.application_name}. Error Occurred: {e}")   
                return True
            # If _get_custom_driver_log returns False, continue with normal state checking

        if application_state in self.FAILURE_STATES:
            # TODO: remove this if block when min_airflow_version is set to higher than 2.7.1
            message = log_utils.print_msg_box(msg=f"Spark application failed with state: {application_state}", indent=2)
            if self.soft_fail:
                raise AirflowSkipException(message)
            raise AirflowException(message)
        elif application_state in self.SUCCESS_STATES:
            self.log.info(
                log_utils.print_msg_box(msg="Spark application ended successfully", indent=2)
            )
            return True
        else:
            self.log.info(
                log_utils.print_msg_box(msg=f"Spark application is still in state: {application_state}", indent=2)
            )
            return False

    @cached_property
    def client(self) -> CoreV1Api:
        return self.hook.core_v1_client

    @cached_property
    def custom_obj_api(self) -> CustomObjectsApi:
        return CustomObjectsApi()

    def on_kill(self) -> None:
        launcher = CustomObjectLauncher(
            name=self.application_name,
            namespace=self.namespace,
            kube_client=self.client,
            custom_obj_api=self.custom_obj_api,
            template_body=json.loads(self.application_file)
        )
        self.log.warning(log_utils.print_msg_box(msg=f"Task instance failed. Deleting spark Application: {self.application_name}", indent=2))
        try:
            launcher.delete_spark_job(spark_job_name=self.application_name)
            self.log.info(log_utils.print_msg_box(msg=f"Spark Application: {self.application_name} Deleted", indent=2))
        except Exception as e:
            self.log.warning(f"Could not delete spark Application: {self.application_name}. Error Occurred: {e}")