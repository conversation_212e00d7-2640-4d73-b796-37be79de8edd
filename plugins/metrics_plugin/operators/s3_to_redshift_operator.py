import json
import random
import string

from airflow.models import Connection
from airflow.stats import Stats
from airflow.utils.db import provide_session
from airflow.utils.decorators import apply_defaults
from airflow.exceptions import AirflowNotFoundException

from airflow.models import BaseOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from metrics_plugin.hooks.source_replication_hook import SourceReplicationHook

import decore


class S3ToRedshiftOperator(BaseOperator):

    template_fields = ("s3_url",)

    @apply_defaults
    def __init__(
        self,
        s3_url,
        schema_name,
        table_name,
        copy_params=[
            "compupdate off",
            "statupdate off",
            "delimiter ','",
            "dateformat 'auto'",
            "timeformat 'auto'",
            """csv quote as '"'""",
            "ignoreheader 1",
            "region as 'ap-southeast-1'",
        ],
        schema_location="local",
        column_dtypes=None,
        primary_key=None,
        sortkey="",
        incremental_key=None,
        force_upsert_without_increment_check=False,
        s3_access_iam_role=None,
        load_type="append",
        database_name=None,
        *args,
        **kwargs,
    ):
        super(S3ToRedshiftOperator, self).__init__(*args, **kwargs)
        self.s3_url = s3_url
        self.schema_name = schema_name.lower()
        self.table_name = table_name.lower()
        self.database_name = database_name
        self.copy_params = copy_params
        self.schema_location = schema_location
        self.column_dtypes = column_dtypes
        self.primary_key = primary_key and primary_key or ""
        self.sortkey = sortkey
        self.incremental_key = incremental_key
        self.force_upsert_without_increment_check = force_upsert_without_increment_check
        self.s3_access_iam_role = s3_access_iam_role
        self.load_type = load_type

    def execute(self, context):
        kwargs = {
            "schema_name": self.schema_name,
            "table_name": self.table_name,
            "copy_params": self.copy_params,
            "schema_location": self.schema_location,
            "column_dtypes": self.column_dtypes,
            "primary_key": self.primary_key,
            "sortkey": self.sortkey,
            "incremental_key": self.incremental_key,
            "force_upsert_without_increment_check": self.force_upsert_without_increment_check,
            "s3_access_iam_role": self.s3_access_iam_role,
            "load_type": self.load_type,
        }

        srp_hook = SourceReplicationHook(
            table_name=self.table_name,
            db_name=self.database_name,
            s3_url=self.s3_url,
            schema_name=self.schema_name,
        )
        srp_column_dtypes = srp_hook.get_column_dtypes()
        if srp_column_dtypes != self.column_dtypes:
            self.log.info(
                f"Mismatch detected in default column dtypes and fetched column dtypes:\n"
                f"Fetched from API: {srp_column_dtypes}\n"
                f"Default column d types: {self.column_dtypes}"
            )
            Stats.incr(f"srp.{self.table_name}.column_dtypes_mismatch")
        else:
            self.log.info("Column dtypes are matching!")
            Stats.incr(f"srp.{self.table_name}.column_dtypes_match")

        if not srp_column_dtypes:
            raise AirflowNotFoundException("No column dtypes found from source replicaion endpoint")

        kwargs["column_dtypes"] = srp_column_dtypes

        decore.to_redshift(self.s3_url, **kwargs)
