from datetime import timed<PERSON><PERSON>

from metrics_plugin.hooks.emr_state_hook import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from metrics_plugin.triggers.emr_state_trigger import EmrStateTrigger

from airflow.exceptions import AirflowException
from airflow.utils.context import Context
from airflow.sensors.base_sensor_operator import BaseSensorOperator
from airflow.utils import timezone
from airflow.utils.decorators import apply_defaults


class EmrStateSensor(BaseSensorOperator):
    """
    Polls the state of an EMR step till a target state is reached.
    If the step fails the sensor errors, failing the task.

    By default, the sensor tries to read the step status from a database
    containing the step_id and the corresponding status. The sensor falls
    back to EMR APIs if the target state cannot be read from the database
    within a stipulated amount of time.

    The DB containg the status of the steps needs to be set up by the user.
    See the docs of `EmrStateHook` to see how this DB can be set up. If a
    database containg the step states is not available, the EMR APIs can be
    used directly by setting the poke_method to 'api'.


    :param step_id: id of the step whose state has to be checked.
    :type step_id: str
    :param cluster_id: id of the cluster which contains the desired step.
    :type cluster_id: str
    :param db_conn_id: airflow connection id for the DB from where the step states will be queried.
    :type db_conn_id: str
    :param aws_conn_id: airflow connection id for the IAM user used for interacting with the cluster via EMR APIs.
    :type aws_conn_id: str
    :param db_poke_interval: time interval in seconds between successive queries to the step_state DB.
    :type db_poke_interval: int
    :param api_poke_interval: time interval in seconds between successive calls to the EMR API.
    :type api_poke_interval: int
    :param event_sensor_timeout: time interval in seconds after which the sensor falls back to the EMR APIs.
    :type event_sensor_timeout: int
    :param poke_method: specifies how the step status is fetched. Allowed values are 'events' and 'api'.
    :type poke_method: str
    """

    TARGET_STATES = ["COMPLETED"]
    FAILED_STATES = ["CANCELLED", "FAILED", "INTERRUPTED"]

    template_fields = ("step_id", "cluster_id")

    @apply_defaults
    def __init__(
        self,
        step_id,
        cluster_id,
        db_conn_id,
        aws_conn_id,
        db_poke_interval=15,
        api_poke_interval=600,
        event_sensor_timeout=1800,
        poke_method="events",
        timeout=7200,
        *args,
        **kwargs
    ):
        # this needs to be done first else the poke_interval
        # will always default to 60 seconds if not supplied explicitly.
        super().__init__(*args, **kwargs)

        self.step_id = step_id
        self.cluster_id = cluster_id
        self.db_conn_id = db_conn_id
        self.aws_conn_id = aws_conn_id

        self.poke_method = poke_method
        self.db_poke_interval = db_poke_interval
        self.api_poke_interval = api_poke_interval
        self.event_sensor_timeout = event_sensor_timeout

        self.sensor_start_time = timezone.utcnow()
        self.poke_interval = (
            db_poke_interval if self.poke_method == "events" else api_poke_interval
        )
        self.timeout = timeout
        self.emr = EmrStateHook(
            step_id=self.step_id,
            cluster_id=self.cluster_id,
            db_conn_id=self.db_conn_id,
            aws_conn_id=self.aws_conn_id,
        )

    def get_emr_step_state(self):
        self.log.info("Poking step %s on cluster %s.", self.step_id, self.cluster_id)

        state = self.emr.get_step_state(method=self.poke_method)

        return state

    def set_poke_method_and_interval(self):
        time_elapsed = (timezone.utcnow() - self.sensor_start_time).total_seconds()

        if self.poke_method == "events" and time_elapsed > self.event_sensor_timeout:
            self.log.info("Event sensor timed out. Falling back to API sensor.")
            self.poke_method = "api"
            self.poke_interval = self.api_poke_interval

    def poke(self, context):
        self.set_poke_method_and_interval()
        state = self.get_emr_step_state()
        self.log.info("Received step state: %s.", state)
        if state.upper() in self.TARGET_STATES:
            return True
        elif state.upper() in self.FAILED_STATES:
            # First check the cluster status to see if the failure is cluster-related
            cluster_status = self.emr.get_cluster_status()
            if cluster_status:
                cluster_state = cluster_status["State"]
                reason_code = cluster_status.get("StateChangeReason", {}).get("Code", "Unknown")
                if (cluster_state in ["TERMINATED_WITH_ERRORS"]) or (cluster_state == "TERMINATED" and reason_code == "USER_REQUEST"):
                    # Set a custom error channel in the context
                    context["error_channel"] = "bl-data-slack-testing"
                    self.log.info("Setting error channel to bl-data-slack-testing due to cluster failure")
                    reason_message = cluster_status.get("StateChangeReason", {}).get("Message", "No message provided")
                    error_msg = f"EMR cluster failure caused step failure. Cluster ID: {self.cluster_id}, Code: {reason_code}, Reason: {reason_message}"
                    self.log.error(error_msg)
                    raise AirflowException(error_msg)

            # If not a cluster failure, get the step failure details
            failure_reason = self.emr.get_failure_reason()
            self.log.error("EMR job failed. %s", failure_reason)
            raise AirflowException(f"EMR job failed. {failure_reason}")

        return False


class EmrStateSensorAsync(EmrStateSensor):
    def execute(self, context: Context):
        """Deferred and give control to trigger"""
        self.defer(
            timeout=timedelta(seconds=self.timeout),
            trigger=EmrStateTrigger(
                cluster_id=self.cluster_id,
                step_id=self.step_id,
                aws_conn_id=self.aws_conn_id,
                db_conn_id=self.db_conn_id,
                poke_method=self.poke_method,
                poke_interval=self.poke_interval,
                event_sensor_timeout=self.event_sensor_timeout,
                sensor_start_time=self.sensor_start_time,
                target_states=self.TARGET_STATES,
                failed_states=self.FAILED_STATES,
                api_poke_interval=self.api_poke_interval,  # Add this line
            ),
            method_name="execute_complete",
        )

    def execute_complete(self, context, event):
        """
        Callback for when the trigger fires - returns immediately.
        Relies on trigger to throw an exception, otherwise it assumes execution was
        successful.
        """
        if event:
            if event["status"] == "error":
                raise AirflowException(event["message"])

            self.log.info(event.get("message"))
            self.log.info("%s completed successfully.", self.cluster_id)
