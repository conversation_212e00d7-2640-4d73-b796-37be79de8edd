import json
import time
import pandas as pd
import sqlalchemy as sqla
from sqlalchemy.sql.compiler import RESERVED_WORDS

from airflow.models import BaseOperator
from airflow.utils.decorators import apply_defaults

import pencilbox as pb


class CreateHiveToIcebergOperator(BaseOperator):

    template_fields = ["hive_table", "hive_column_names_dict", "col_names_source"]
    template_ext = (".json",)
    template_fields_renderers = {
        "hive_column_names_dict": "json",
    }

    @apply_defaults
    def __init__(self, trino_catalog: str, trino_schema: str, trino_table: str, primary_key: list, partition_key: list, hive_table: str, hive_column_names_dict: dict, col_names_source: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.trino_catalog = trino_catalog
        self.trino_schema = trino_schema
        self.trino_table = trino_table
        self.primary_key = primary_key
        self.partition_key = partition_key
        self.hive_table = hive_table
        self.hive_column_names_dict = hive_column_names_dict
        self.col_names_source = col_names_source
        self.trino_connection = pb.get_connection("[ETL] Trino")

    def hive_to_iceberg_datatype(self, hive_datatype):
        # Define the HIVE to ICEBERG data type mapping
        hive_to_iceberg_mapping = {
            "VARCHAR": "VARCHAR",
            "BIGINT": "BIGINT",
            "INT": "INT",
            "BOOLEAN": "BOOLEAN",
            "TIMESTAMP": "TIMESTAMP(6)",
            "DECIMAL": "DECIMAL",
            "DATE": "DATE",
            "TIME": "TIME",
            "BINARY": "BINARY",
            "VARBINARY": "VARBINARY",
            "CHAR": "VARCHAR",
            "VARCHAR": "VARCHAR",
            "JSON": "VARCHAR",
            "UUID": "VARCHAR",
            "INTERVAL": "VARCHAR",
            "SMALLINT": "INT",
            "REAL": "REAL",
            "SMALLSERIAL": "INT",
            "SERIAL": "INT",
            "BIGSERIAL": "BIGINT",
            "BIT": "VARCHAR",
            "BIT VARYING": "VARCHAR",
            "DOUBLE": "DOUBLE"
        }

        #handling precision values
        base_type, *precision = hive_datatype.split('(')
        iceberg_datatype = hive_to_iceberg_mapping.get(base_type)
        
        if iceberg_datatype is None:
            raise ValueError(f"Unknown hive data type: {hive_datatype}")

        return f"{iceberg_datatype}({precision[0]}" if iceberg_datatype and "VARCHAR" not in iceberg_datatype and precision else iceberg_datatype

    def convert_hive_to_iceberg_dtypes(self, hive_col_names_dict):
        hive_col_names_dict = json.loads(hive_col_names_dict)
        print("hive_col_names_dict: ", hive_col_names_dict)
        data_types = hive_col_names_dict['col_type']
        column_names = hive_col_names_dict['col_name']

        iceberg_columns_name = []
        for i in range(len(column_names)):
            print(column_names)
            col_name = column_names[str(i)]
            data_type = self.hive_to_iceberg_datatype(data_types[str(i)])
            column_name = f'{col_name} {data_type}'
            iceberg_columns_name.append(column_name)

        iceberg_column_string = ", ".join(iceberg_columns_name)

        return iceberg_column_string
    
    def create_iceberg_table(self, trino_catalog, trino_schema, trino_table):
        # create table false condition to just create the table strucutre without any data
        iceberg_column_string = self.convert_hive_to_iceberg_dtypes(self.hive_column_names_dict)
        partition_condition = f"""
        WITH(
                PARTITIONING = ARRAY[{', '.join([f"'{key}'" for key in self.partition_key])}]
            )
        """ if self.partition_key else ""
        print("hive table: ", self.hive_table)
        # print("iceberg_columns: ", iceberg_columns)
        create_ice_stg_table = f"""
        CREATE TABLE IF NOT EXISTS {trino_catalog}.{trino_schema}.{trino_table} (
            {iceberg_column_string}
        ) {partition_condition}
        """
        
        print(create_ice_stg_table)
        
        execution_start_time = time.time()
        print(f"Creating playground table at iceberg catalog in Trino {trino_catalog}.{trino_schema}.{trino_table} from hive table")
        self.trino_connection.execute(create_ice_stg_table)
        print(f"Step 2 - create table  at iceberg catalog in Trino - Execution Time: --- {(time.time() - execution_start_time)} seconds ---")
        
        return f'{trino_catalog}.{trino_schema}.{trino_table}'
    
    def get_insert_query(self, distinct_partition_sql) :
        check_keys = self.primary_key + list(set(self.partition_key) - set(self.primary_key))
        # Construct the JOIN condition
        join_condition = ' AND '.join([f'hive.{col} = ice.{col}' for col in check_keys])
        # Construct the WHERE condition
        where_condition = " AND ".join(
            [f"(ice.{key} is NULL)" for key in check_keys]
        )
        iceberg_table = self.create_iceberg_table(self.trino_catalog, self.trino_schema, self.trino_table)
        partitioning_condition = f"""
            WHERE {', '.join(self.partition_key)} IN
                ({distinct_partition_sql})
            """ if self.partition_key else ""

        print("where_condition: ", where_condition)
        print("join_condition: ", join_condition)
        qry_insrt = f"""
                    MERGE INTO {iceberg_table} AS ice
                        USING (
                        SELECT * FROM {self.hive_table}
                         {partitioning_condition}
                        ) AS hive
                            ON ({join_condition})
                        WHEN NOT MATCHED AND ({where_condition})
                            THEN INSERT ({self.col_names_source})
                            VALUES ({', '.join(['hive.' + column.strip() for column in self.col_names_source.split(',')])})
                    """
        
        print(qry_insrt)
        
        return qry_insrt
    
    def create_view_sql(
        self,
        view_name: str,
        view_query: str,
        replace_view: bool = True,
        security: str = "INVOKER",
    ):
        return f"""CREATE {'OR REPLACE' if replace_view else ''} VIEW {view_name} SECURITY {security} AS
        {view_query}"""

    def execute(self, context):
        if self.partition_key:
            get_partitions = f"""SELECT DISTINCT {', '.join(self.partition_key)} FROM {self.hive_table}"""
            res = pd.read_sql_query(sql=get_partitions, con=self.trino_connection)

            # Define the chunk size
            chunk_size = 200

            # Split the DataFrame into chunks of 200 partitions each
            partition_chunks = [res[i:i + chunk_size] for i in range(0, len(res), chunk_size)]
            chunk_count  = len(partition_chunks)
        else:
            chunk_count = 1

        execution_start_time = time.time()

        print('--data migration process begins--')
        print(f"inserting into iceberg catalog in Trino blinkit_iceberg.{self.trino_schema}.{self.trino_table} from hive table")

        for i in range(0,chunk_count):
            distinct_partition_sql = ""
            if self.partition_key:
                offset_value = i*200
                distinct_partition_sql = f"""
                                SELECT DISTINCT {', '.join(self.partition_key)} FROM {self.hive_table}
                                ORDER BY {', '.join(self.partition_key)} OFFSET {offset_value} LIMIT {chunk_size}
                            """
                print(distinct_partition_sql)
            insert_query = self.get_insert_query(distinct_partition_sql)
            print(f"Step {i}")
            self.trino_connection.execute(insert_query).fetchall()
            print(f"Step {i}  - Execution Time: --- {(time.time() - execution_start_time)} seconds ---")


        create_iceberg_view = self.create_view_sql(
            view_name=f'''"blinkit"."{self.trino_schema}"."{self.trino_table}"''',
            view_query=f'''SELECT * FROM "{self.trino_catalog}"."{self.trino_schema}"."{self.trino_table}"'''
            )
        print("create view query: ", create_iceberg_view)
        self.trino_connection.execute(create_iceberg_view).fetchall()
        print("Trino iceberg view Created")