import boto3
from airflow.models import BaseOperator


class ECSUpdateServiceCount(BaseOperator):

    def __init__(self, region_name: str, cluster_name: str, service_name: str, desired_count: int,
                 force_update: bool = False,*args,**kwargs):
        super().__init__(*args, **kwargs)
        self.cluster_name: str = cluster_name
        self.service_name: str = service_name
        self.desired_count: int = desired_count
        self.force_update: bool = force_update
        self.region_name:str = region_name
        self.client: boto3.client = boto3.client("ecs", region_name=self.region_name)

    def execute(self, context):
        response = self.client.update_service(
            cluster=self.cluster_name,
            service=self.service_name,
            desiredCount=self.desired_count,
            forceNewDeployment=self.force_update)
        self.log.info(response)