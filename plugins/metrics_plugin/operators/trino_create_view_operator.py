import pencilbox as pb

from airflow.models import BaseOperator
from airflow.utils.decorators import apply_defaults

ALERT_CHANNEL = "blinkit-superset-creators"


class TrinoCreateViewOperator(BaseOperator):
    """
    Operator to create a view in Trino
    """

    @apply_defaults
    def __init__(self, view_catalog: str, view_schema: str, view_name: str, security_type: str, view_definition: str, slack_id: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.view_catalog = view_catalog
        self.view_schema = view_schema
        self.view_name = view_name
        self.security_type = security_type
        self.view_definition = view_definition
        self.slack_id = slack_id

    def execute(self, context):
        trino_connection = pb.get_connection("[ETL] Trino")
        view_ddl = f"""
            CREATE OR REPLACE VIEW {self.view_catalog}.{self.view_schema}.{self.view_name} SECURITY {self.security_type} AS (
                {self.view_definition}
            )
        """
        try:
            trino_connection.execute(view_ddl).fetchall()
            print_mes = f"*{self.view_schema}.{self.view_name}* has been created succesfully"
        except Exception as e:
            print_mes = f"*{self.view_schema}.{self.view_name}* has been failed with: {e}"

        print(print_mes)

        text_mes = f"<@{self.slack_id}>, {print_mes}"
        pb.send_slack_message(
            channel=ALERT_CHANNEL, text=text_mes
        )
