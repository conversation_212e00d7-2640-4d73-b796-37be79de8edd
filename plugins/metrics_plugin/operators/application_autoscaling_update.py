import boto3
from airflow.models import BaseOperator

class ApplicationAutoscalingUpdate(BaseOperator):

    def __init__(self, region_name: str, resource_id: str, min_capacity: int, max_capacity: int,
                 suspend_scalein: bool , suspend_scaleout: bool  ,*args, **kwargs):
        super().__init__(*args, **kwargs)
        self.resource_id: str = resource_id
        self.min_capacity: int = min_capacity
        self.max_capacity: int = max_capacity
        self.suspend_scalein: bool = suspend_scalein
        self.suspend_scaleout: bool = suspend_scaleout
        self.region_name: str = region_name
        self.client: boto3.client = boto3.client("application-autoscaling", region_name=region_name)


    def execute(self, context):
        response = self.client.register_scalable_target(ServiceNamespace='ecs',
                                                        ResourceId=self.resource_id, MinCapacity=self.min_capacity,
                                                        MaxCapacity=self.max_capacity,
                                                        ScalableDimension='ecs:service:DesiredCount',
                                                        SuspendedState={
                                                            'DynamicScalingInSuspended': self.suspend_scalein,
                                                            'DynamicScalingOutSuspended': self.suspend_scaleout})
        self.log.info(response)
