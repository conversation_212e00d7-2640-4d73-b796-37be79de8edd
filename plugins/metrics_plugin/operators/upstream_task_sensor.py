from datetime import datetime
import pytz

from sqlalchemy import func

from airflow.exceptions import AirflowException
from airflow.models import <PERSON>g<PERSON><PERSON>, DagModel, DagRun, TaskInstance
from airflow.sensors.base_sensor_operator import BaseSensorOperator
from airflow.utils.db import provide_session
from airflow.utils.decorators import apply_defaults
from airflow.utils.state import State


class UpstreamTaskSensor(BaseSensorOperator):
    """
    Waits for successful completion of a DAG or a task in a DAG after a specific timestamp

    """
    tz_ist = pytz.timezone('Asia/Kolkata')

    @apply_defaults
    def __init__(self,
                 external_dag_id_without_version,
                 external_task_id=None,
                 allowed_states=[State.SUCCESS, State.SKIPPED],
                 upstream_start_time=datetime.now(tz_ist).replace(hour=0,minute=0,second=0).astimezone(pytz.utc),
                 poke_interval=60*15,
                 timeout=60*90,
                 mode='reschedule',
                 *args,
                 **kwargs):
        super(UpstreamTaskSensor, self).__init__(
            poke_interval=poke_interval, timeout=timeout, mode=mode, *args, **kwargs)
        self.allowed_states = allowed_states
        self.upstream_start_time = upstream_start_time
        self.external_dag_id_without_version = external_dag_id_without_version
        self.external_task_id = external_task_id

    @provide_session
    def poke(self, context, session=None):

        self.log.info(f'Checking for dependency success for dag {self.external_dag_id_without_version}')

        DM = DagModel
        TI = TaskInstance
        DR = DagRun

        # can also filter on only unpaused dags, but that would mean we would have
        # to wait for the latest version to get done once before this to be triggered
        external_dag_ids = session.query(DM.dag_id).filter(
            DM.dag_id.op('~')(f'{self.external_dag_id_without_version}_v[0-9]{{1,}}')
        ).all()

        if not external_dag_ids:
            raise AirflowException(
                f'No dag versions available for the given id'
            )

        if self.external_task_id:
            count = session.query(func.count()).filter(
                TI.dag_id.in_(external_dag_ids),
                TI.task_id == self.external_task_id,
                TI.state.in_(self.allowed_states),
                TI.start_date >= self.upstream_start_time,
            ).scalar()
        else:
            count = session.query(func.count()).filter(
                DR.dag_id.in_(external_dag_ids),
                DR.state.in_(self.allowed_states),
                DR.start_date >= self.upstream_start_time,
            ).scalar()
        session.commit()
        self.log.info(f'Dependency has been executed {count} times since {self.upstream_start_time}')
        return count > 0
