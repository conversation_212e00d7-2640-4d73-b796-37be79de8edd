import ast
import datetime as dt
import tenacity as tc
from typing import Any, Dict, List, Optional, Union

from airflow.exceptions import AirflowException
from airflow.operators.branch_operator import BaseBranchOperator
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from collections import defaultdict


class SRPEmrBulkAddStepsOperator(BaseBranchOperator):
    """
    An operator that adds steps to an existing EMR job_flow. In this operator we are considering
    flow_id as srp_flow_id except for `JobFlowId` which denotes cluster_id of the EMR

    :param srp_flows_info: mapping of srp flow_id and corresponding flow details
        or reference to a mapping file (must be '.json'). (templated)
    :type srp_flows_info: Optional[dict]
    :param srp_task_id_cluster_mapping: mapping of srp downstream task id to the cluster on which
        the flow is running or reference to a mapping file (must be '.json'). (templated)
    :type srp_task_id_cluster_mapping: Optional[dict]
    :param spark_application_jar: common spark application jar path for all flows. (templated)
    :type spark_application_jar: str
    :param spark_submit_options: common spark submit options for all flows. (templated)
    :type spark_submit_options: dict
    :param aws_conn_id: aws connection to uses
    :type aws_conn_id: str
    :param do_xcom_push: if True, cluster_id_steps_info_mapping for new jobs
        is pushed to XCom with key cluster_id_steps_info_mapping.
    :type do_xcom_push: bool

    :: Example Output = {
        "flow_id_1": {
                "cluster_id": "cluster_id_1",
                "step_id": "step_id_1",
        },
        "flow_id_2": {
                "cluster_id": "cluster_id_2",
                "step_id": "step_id_2",
        }
    }
    """

    template_fields = [
        "srp_flows_info",
        "srp_task_id_cluster_mapping",
        "spark_application_jar",
        "spark_submit_options",
    ]
    template_ext = (".json",)
    template_fields_renderers = {
        "srp_flows_info": "json",
        "srp_task_id_cluster_mapping": "json",
    }
    ui_color = "#f98f15"

    def __init__(
        self,
        *,
        srp_flows_info: Optional[dict] = None,
        srp_task_id_cluster_mapping: Optional[dict] = {},
        spark_application_jar: str,
        spark_submit_options: dict,
        aws_conn_id: str = "aws_default",
        **kwargs,
    ):
        if kwargs.get("xcom_push") is not None:
            raise AirflowException(
                "'xcom_push' was deprecated, use 'do_xcom_push' instead"
            )
        if not srp_flows_info:
            raise AirflowException(
                "Flows info not specified! Can't trigger downstream tasks without it"
            )
        super().__init__(**kwargs)
        self.aws_conn_id = aws_conn_id
        self.srp_flows_info = srp_flows_info
        self.spark_application_jar = spark_application_jar
        self.spark_submit_options = spark_submit_options
        self.srp_task_id_cluster_mapping = srp_task_id_cluster_mapping

    def fetch_spark_steps(self, flow_id, **kwargs):
        if kwargs.get("step_id"):
            self.log.info(
                f"Skipping Spark Submit for flow_id {flow_id}. Flow is already running."
            )
            return []

        # Overriding values of spark_submit_options & spark_application_jar per flow level
        if kwargs.get("spark_submit_options"):
            self.spark_submit_options.update(kwargs["spark_submit_options"])
        if kwargs.get("spark_application_jar"):
            self.spark_application_jar = kwargs["spark_application_jar"]

        self.log.info(
            f"Creating Spark Submit Step for flow_id {flow_id} in {self.spark_submit_options['--deploy-mode']} mode"
        )

        # Initialize the spark-submit tuple
        spark_submit = ("spark-submit",)

        # Adding the spark submit options
        for opt, val in self.spark_submit_options.items():
            if opt == "--conf":
                for u, v in val.items():
                    spark_submit = spark_submit + (opt, f"{u}={v}")
            else:
                spark_submit = spark_submit + (opt, val)

        # Adding the spark application jar
        spark_submit = spark_submit + (self.spark_application_jar,)

        # Adding the spark application arguments at the end
        spark_submit = spark_submit + kwargs["spark_application_arguments"]

        return {
            "Name": f"""{flow_id}-{kwargs["spark_application_arguments"][0]}-{dt.datetime.now().strftime("%Y%m%d%H%M%S")}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }

    def update_running_steps(self):
        """Update the job details for already running flows"""

        # TODO: Move to flow_id_cluster_mapping in srp_cluster_mapping_operator
        self.task_id_flow_id_mapping = {}
        for flow_id, flow_info in self.srp_flows_info.items():
            self.task_id_flow_id_mapping[flow_info["downstream_task_id"]] = flow_id
            if flow_info.get("cluster_id"):
                self.log.info(
                    f"Skipping Spark Submit for flow_id {flow_id}. Flow is already running."
                )
                self.flow_id_job_mapping[flow_id] = {
                    "cluster_id": flow_info["cluster_id"],
                    "step_id": flow_info["step_id"],
                }

    def get_cluster_id_steps_mapping(self):
        """
        Generate cluster id to steps mapping for bulk submission purpose

        :: Example Output = {
            "cluster_id_1": [{
                "flow_id": "flow_id_1", "step": {step 1 details json}
            },{
                "flow_id": "flow_id_2", "step": {step 2 details json}
            }],
            "cluster_id_2": [{
                "flow_id": "flow_id_3", "step": {step 3 details json}
            },{
                "flow_id": "flow_id_4", "step": {step 4 details json}
            }]
        }
        """
        cluster_id_steps_info_mapping = defaultdict(list)

        for task_id, cluster_id in self.srp_task_id_cluster_mapping.items():
            # TODO: Move to flow_id_cluster_mapping in srp_cluster_mapping_operator
            flow_id = self.task_id_flow_id_mapping[task_id]
            cluster_id_steps_info_mapping[cluster_id].append(
                {
                    "flow_id": flow_id,
                    "step": self.fetch_spark_steps(
                        flow_id=flow_id, **self.srp_flows_info[flow_id]
                    ),
                }
            )

        return cluster_id_steps_info_mapping

    @tc.retry(
        retry=tc.retry_if_exception_type(tc.TryAgain),
        wait=tc.wait_exponential(multiplier=2, exp_base=3),
        stop=tc.stop_after_attempt(3),
    )
    def add_emr_steps(
        self, cluster_id: str, steps: Optional[Union[List[dict], str]] = []
    ):
        """Add steps to EMR cluster"""
        try:
            self.log.info("Adding steps to %s", cluster_id)

            # steps may arrive as a string representing a list
            if isinstance(steps, str):
                steps = ast.literal_eval(steps)

            response = self.emr.add_job_flow_steps(JobFlowId=cluster_id, Steps=steps)

            if not response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                raise AirflowException(f"Adding steps failed: {response}")
            else:
                self.log.info("Steps %s added to JobFlow", response["StepIds"])
                return response["StepIds"]
        except Exception as e:
            if e.response["Error"]["Code"] == "ThrottlingException":
                self.log.info("Step submission call got throttled!")
                raise tc.TryAgain
            raise e

    def choose_branch(self, context: Dict[str, Any]) -> List[str]:
        # Final output flow_id -> (step_id, flow_id)
        self.flow_id_job_mapping = {}

        # Update flow_id_job_mapping for currently running steps
        self.update_running_steps()

        # EMR job flow id (cluster id) -> [(flow id, spark step)] mapping
        cluster_id_steps_info_mapping = self.get_cluster_id_steps_mapping()
        self.log.info(cluster_id_steps_info_mapping)

        # To capture the errors that may occur while bulk submission
        errors = []

        emr_hook = EmrHook(aws_conn_id=self.aws_conn_id)
        self.emr = emr_hook.get_conn()

        for cluster_id, steps_info in cluster_id_steps_info_mapping.items():
            try:
                steps = [step_info["step"] for step_info in steps_info]
                step_ids = self.add_emr_steps(cluster_id=cluster_id, steps=steps)
                for i in range(len(steps_info)):
                    self.flow_id_job_mapping[steps_info[i]["flow_id"]] = {
                        "cluster_id": cluster_id,
                        "step_id": step_ids[i],
                    }
            except Exception as e:
                errors.append(
                    f"Cluster ID: {cluster_id}, Steps Details: {steps_info}, ERROR: {str(e)}"
                )

        # Push to xcom for use by the sensors (this is not optional)
        self.xcom_push(context, "flow_id_job_mapping", self.flow_id_job_mapping)

        if self.do_xcom_push:
            self.xcom_push(
                context, "cluster_id_steps_info_mapping", cluster_id_steps_info_mapping
            )

        if errors:
            raise AirflowException(
                "Following errors occured during bulk submission:\n{e}".format(
                    e="\n".join(errors)
                )
            )

        return list(self.task_id_flow_id_mapping.keys())
