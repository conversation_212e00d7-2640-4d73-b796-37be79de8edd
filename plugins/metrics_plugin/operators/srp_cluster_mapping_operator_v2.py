import pandas as pd

from airflow.exceptions import AirflowException
from airflow.models import BaseOperator
from airflow.stats import Stats
from airflow.utils.decorators import apply_defaults
from collections import defaultdict

import pencilbox as pb

from metrics_plugin.hooks.kafka_srp_offset_hook import KafkaSRPOffsetHook
from metrics_plugin.hooks.hudi_to_redshift_flow_ids_hook import HudiToRedshiftFlowIdsHook

CLUSTER_MAX_CONCURRENCY = {"large": 4, "medium": 18}
DEFAULT_CLUSTER_MAPPING = {600000000: "medium", 1000000000: "large"}
DEFAULT_CONFIG_IDENTIFIER = "spark_5_33"


class SRPClusterMappingOperatorV2(BaseOperator):
    """
    Checks the current available clusters and assign a table to it

    :param flows_to_run: (flow id -> {kafka topic, airflow task id}) mapping
    :type flows_to_run: dict
    :param connection_alias: pencilbox connection alias for the db where
    kafka marker offsets are present
    :type connection_alias: str
    :param redshift_sla_seconds(Optional): SLA at which redshift needs to be updated.
    Required for hudi-redshift flow
    :type redshift_sla_seconds: int

    .. code-block:: python

        flows_to_run = {
			"flow_id_1": {
				"kafka_topic": "kafka_topic_1",
				"downstream_task_id": "task_id_1",
		        "spark_submit_options": {
		            "--jars": "jar_paths",
		        },
		        "spark_application_jar": "jar_path",
		        "spark_application_arguements": ("s3Hudi", "--flowId", "flow_id_1"),
			}
		}

        op = SRPClusterMappingOperator(
            task_id="test",
            flows_to_run=flows_to_run,
            connection_alias="nessie"
        )
    """

    template_fields = ("flows_to_run", "connection_alias", "redshift_sla_seconds")

    @apply_defaults
    def __init__(self, flows_to_run, connection_alias, redshift_sla_seconds=None, *args, **kwargs):
        self.flows_to_run = flows_to_run
        self.connection_alias = connection_alias
        self.redshift_sla_seconds = redshift_sla_seconds
        super().__init__(*args, **kwargs)

    def get_available_clusters(self, conn):
        """
        :returns: (config identifier -> (cluster size -> (available cluster id -> queue_size))) mapping
        :rtype: dict

        :: Example Output = {
            "cluster_config_identifier_1": {
                "large": {"cluster_id_1": 23, "cluster_id_2": 43, "cluster_id_3": 45},
                "medium": {"cluster_id_4": 45, "cluster_id_5": 23},
            }
        }
        """
        query = """SELECT
                    cluster_id,
                    cluster_size,
                    config_identifier,
                    cluster_state,
                    SUM(is_busy) AS queue_size
                FROM ( SELECT DISTINCT ON (s.step_id)
                        CASE WHEN s.step_state in('PENDING', 'RUNNING') THEN
                            1
                        ELSE
                            0
                        END is_busy,
                        c.cluster_id,
                        c.cluster_size,
                        c.config_identifier,
                        c.cluster_state
                    FROM
                        step_events s
                    RIGHT JOIN emr_cluster_info c ON c.cluster_id = s.cluster_id
                WHERE
                    c.cluster_state IN ('available', 'starting')
                ORDER BY
                    s.step_id,
                    s.event_timestamp DESC) latest_step_states
                GROUP BY
                    1,2,3,4"""

        clusters_df = pd.read_sql_query(query, con=conn)
        available_clusters = defaultdict(lambda: defaultdict(dict))
        available_clusters_df = clusters_df[clusters_df.cluster_state == 'available']
        # If no clusters are yet available then submit jobs to clusters that are starting up
        if available_clusters_df.empty:
            available_clusters_df = clusters_df[clusters_df.cluster_state == 'starting']
        for row in available_clusters_df.to_dict(orient="records"):
            available_clusters[row["config_identifier"]][row["cluster_size"]][
                row["cluster_id"]
            ] = row["queue_size"]

        return available_clusters

    def get_cluster_mappings(self, flow_ids, conn):
        """
        :param flow_ids: flow IDs for which cluster type mapping are required
        :type flow_ids: list(str)
        :return: (flow_id -> (config identifier, (offset difference -> cluster type))) mapping
        :rtype: dict

        :: Example Output = {
            "flow_id_1": {
                "cluster_type_required": {
                    5000: "medium",
                    10000: "large",
                },
                "config_identifier": "cluster_config_identifier_1",
            }
        }
        """
        if not flow_ids:
            return dict()

        # Use default cluster mapping in case query fails for some reason or flow_id not present in db
        cluster_mappings = defaultdict(
            lambda: {
                "config_identifier": DEFAULT_CONFIG_IDENTIFIER,
                "cluster_type_required": dict(sorted((DEFAULT_CLUSTER_MAPPING).items())),
            }
        )
        query = ("""SELECT * FROM cluster_mappings WHERE flow_id in ({flow_ids_str})""").format(
            flow_ids_str=",".join([f"'{flow_id}'" for flow_id in flow_ids]),
        )
        try:
            cluster_mappings_df = pd.read_sql_query(query, con=conn)
        except Exception as e:
            cluster_mappings_df = pd.DataFrame(
                columns=["flow_id", "cluster_type_required", "config_identifier"]
            )
            self.log.error(f"Fetching cluster mapping failed: {e}")

        cluster_mappings_df.set_index("flow_id", inplace=True)
        for flow_id in flow_ids:
            try:
                cluster_mappings[flow_id] = {
                    "config_identifier": cluster_mappings_df.loc[flow_id]["config_identifier"],
                    "cluster_type_required": dict(
                        sorted(
                            {
                                int(k): v
                                for k, v in (
                                    cluster_mappings_df.loc[flow_id]["cluster_type_required"]
                                ).items()
                            }.items()
                        )
                    ),
                }
            except Exception as e:
                self.log.warning(
                    f"Cluster mappings not found for flow id: {flow_id}, resorting to "
                    f"default mapping: {cluster_mappings[flow_id]}"
                )

        return cluster_mappings

    def identify_ideal_cluster_type(self, cluster_mapping, incremental_size):
        """
        Get ideal cluster type corresponding to number of changes

        :param cluster_mapping: (offset difference -> cluster type) mapping
        :type cluster_mapping: dict
        :param incremental_size: count of incremental data
        :type incremental_size: int
        :return: ideal cluster type
        :rtype: str

        :: Example Input:
        cluster_mapping = {
            5000: "medium",
            10000: "large",
        }, incremental_size = 9000

        Output:
        "large"
        """
        table_sizes = list(cluster_mapping.keys())
        table_sizes.sort(key=lambda x: int(x))
        for table_size in table_sizes:
            if int(table_size) >= incremental_size:
                return cluster_mapping[table_size]

        self.log.warning(
            f"No cluster type available to process {incremental_size} rows. "
            f"Please alter the cluster type mapping and try again"
        )
        # return None to raise exception later on
        return None

    def get_cluster_type_task_id_mapping(self, conn):
        """
        Get all task_ids corresponding to a certain cluster type

        :return: (config identifier -> (cluster_type -> [task_id])) mapping
        :rtype: dict

        :: Example output:
        cluster_type_task_id_mapping = {
            "cluster_config_identifier_1":{
                "large": ["task_id_1", "task_id_2"],
                "medium": ["task_id_3", "task_id_4"],
            }
        }
        """
        cluster_type_task_id_mapping = defaultdict(lambda: defaultdict(list))
        cluster_mappings = self.get_cluster_mappings(self.flows_info.keys(), conn)
        self.log.info(f"Cluster mappings: {dict(cluster_mappings)}")
        for flow_id, step_details in self.flows_info.items():
            if step_details.get("cluster_id") and step_details.get("step_id"):
                # Case of flow already running, skip cluster allocation
                continue

            if self.redshift_sla_seconds:
                # Take smallest cluster required for flow id since hudi to redshift flow
                # do not have large load
                ideal_cluster_type = list(
                    cluster_mappings[flow_id]["cluster_type_required"].values()
                )[0]
            else:
                self.log.info(f"Identifying ideal cluster type for flow ID: {flow_id}")
                # Find the ideal cluster type for a table on the basis of cluster type
                # required generally and current incremental load
                ideal_cluster_type = self.identify_ideal_cluster_type(
                    cluster_mapping=cluster_mappings[flow_id]["cluster_type_required"],
                    incremental_size=step_details["incremental_rows"],
                )
            if not ideal_cluster_type:
                Stats.incr(
                    f"srp.lr_emr.sla_breached.too_many_changes.task_id.{step_details['downstream_task_id']}"
                )
            # Store all task ids corresponding to a cluster type
            cluster_type_task_id_mapping[cluster_mappings[flow_id]["config_identifier"]][
                ideal_cluster_type
            ].append(step_details["downstream_task_id"])
        return cluster_type_task_id_mapping

    def get_assignable_cluster_id(self, available_clusters, allowed_cluster_types):
        """
        Get cluster with least concurrency

        :param allowed_cluster_types: allowed cluster types in decreasing order of size
        :type allowed_cluster_types: list(str)
        :return: cluster id
        :rtype: str
        """
        cluster_id = None
        for cluster in reversed(allowed_cluster_types):
            # From available clusters of the same size assign the one with lowest concurrency
            cluster_id = min(
                available_clusters[cluster],
                key=available_clusters[cluster].get,
            )
            # If concurrency not available for the required cluster size, try for a
            # larger one unless it is the largest one
            if available_clusters[cluster][cluster_id] < CLUSTER_MAX_CONCURRENCY[cluster]:
                available_clusters[cluster][cluster_id] += 1
                break
            elif cluster == allowed_cluster_types[0]:
                self.log.warning(f"Cluster ID '{cluster_id}' got overallocated!")
                available_clusters[cluster][cluster_id] += 1
                Stats.gauge(
                    f"srp.lr_emr.overallocation.queue_size.{cluster}.cluster_id.{cluster_id}",
                    available_clusters[cluster][cluster_id] - CLUSTER_MAX_CONCURRENCY[cluster],
                )

        return cluster_id

    def assign_clusters_to_task_ids(self, available_clusters, cluster_type_task_id_mapping):
        """
        Assigns required/better clusters to task ids
        """
        self.allowed_cluster_types = []
        self.unassigned_task_ids = []

        # Assign clusters in the decreasing order of size
        for cluster_type in CLUSTER_MAX_CONCURRENCY:
            if available_clusters.get(cluster_type):
                self.allowed_cluster_types.append(cluster_type)
            elif cluster_type_task_id_mapping.get(cluster_type) and not self.allowed_cluster_types:
                self.unassigned_task_ids.extend(cluster_type_task_id_mapping[cluster_type])
                for task_id in cluster_type_task_id_mapping[cluster_type]:
                    self.log.warning(
                        f"'{cluster_type}' or better cluster was not found for task ID: {task_id}"
                    )
                    Stats.incr(f"srp.lr_emr.fallback_allocation.task_id.{task_id}")
                continue

            for task_id in cluster_type_task_id_mapping.get(cluster_type, []):
                self.task_id_cluster_mapping[task_id] = self.get_assignable_cluster_id(
                    available_clusters=available_clusters,
                    allowed_cluster_types=self.allowed_cluster_types,
                )

    def handle_unassigned_task_ids(self, available_clusters):
        """
        Assign largest available cluster to task ids for which ideal cluster type was not available
        """
        for task_id in self.unassigned_task_ids:
            self.log.warning(
                f"Assigning task_id '{task_id}' to '{self.allowed_cluster_types[0]}' cluster type"
            )
            self.task_id_cluster_mapping[task_id] = self.get_assignable_cluster_id(
                available_clusters=available_clusters,
                allowed_cluster_types=[self.allowed_cluster_types[0]],
            )

    def update_task_id_cluster_mapping(self, available_clusters, cluster_type_task_id_mapping):
        """
        Assign clusters to task ids on the basis of available clusters, assigning
        shortest possible cluster to each task id

        :param available_clusters: (cluster size -> [available cluster id -> queue_size]) mapping
        :type available_clusters: dict
        :param cluster_type_task_id_mapping: (cluster type -> task_ids) mapping
        :type cluster_type_task_id_mapping: dict
        """
        # If task IDs are present to be mapped and no available cluster then raise error
        if any(cluster_type_task_id_mapping.keys()) and not available_clusters:
            for task_ids in cluster_type_task_id_mapping.values():
                for task_id in task_ids:
                    Stats.incr(f"srp.lr_emr.sla_breached.no_clusters.task_id.{task_id}")
            raise AirflowException("No EMR clusters available!!")

        # Assign ideal clusters to task ids
        self.assign_clusters_to_task_ids(
            available_clusters=available_clusters,
            cluster_type_task_id_mapping=cluster_type_task_id_mapping,
        )

        # Assign largest available cluster id to task ids for which ideal cluster type was not available
        self.handle_unassigned_task_ids(available_clusters)

        # To monitor EMR queues
        for cluster_type, clusters in available_clusters.items():
            for cluster_id, queue_size in clusters.items():
                Stats.gauge(
                    f"srp.lr_emr.available.queue_size.{cluster_type}.cluster_id.{cluster_id}",
                    CLUSTER_MAX_CONCURRENCY[cluster_type] - queue_size,
                )

    def _filter_running_flows(self, flow_ids, conn, available_clusters):
        flow_type = "hudiIncS3" if self.redshift_sla_seconds else "s3Hudi"
        flow_ids_str = ", ".join([f"'{fid}'" for fid in flow_ids])
        cluster_ids = set()
        for key, cluster_confs in available_clusters.items():
            for size, clusters in cluster_confs.items():
                cluster_ids = cluster_ids.union(set(clusters.keys()))
        query = f"""
            SELECT DISTINCT ON (flow_id)
            flow_id, step_id, cluster_id, step_state
            FROM step_events
            WHERE flow_id in ({flow_ids_str}) AND
            step_name ILIKE '%%-{flow_type}-%%' AND
            cluster_id IN ('{"','".join(cluster_ids)}')
            ORDER BY flow_id, event_timestamp desc
        """
        latest_events = pd.read_sql_query(query, conn)

        running_flows = latest_events.loc[latest_events["step_state"].isin(["PENDING", "RUNNING"])]
        return dict(
            zip(
                running_flows.flow_id,
                zip(running_flows.cluster_id, running_flows.step_id),
            )
        )

    def get_s3_to_hudi_flows_info(self, conn):
        """
        Checks offset difference for each flow_id using KafkaSRPOffsetHook and
        decides whether it needs to run or not
        """
        offset_differences = KafkaSRPOffsetHook(
            kafka_connection_id="source-replication-kafka-rest-url",
            db_connection=conn,
            flow_id_topic_mapping={
                flow: self.flows_to_run.get(flow).get("kafka_topic")
                for flow in self.flows_to_run.keys()
            },
        ).offset_differences()

        return {
            flow_id: {
                "incremental_rows": offset_differences[flow_id],
                **details
            }
            for flow_id, details in self.flows_to_run.items()
            if offset_differences[flow_id] > 0
        }

    def get_hudi_to_redshift_flows_info(self, conn):
        """
        Checks if a flow_id needs to be run using HudiToRedshiftFlowIdsHook
        """
        flow_ids = HudiToRedshiftFlowIdsHook(
            candidate_flow_ids=self.flows_to_run.keys(),
            redshift_sla_seconds=self.redshift_sla_seconds,
            db_engine=conn,
        ).get_updated_table_flow_ids()

        return {
            flow_id: {
                **details
            }
            for flow_id, details in self.flows_to_run.items()
            if flow_id in flow_ids
        }

    def get_flows_info(self, conn, available_clusters):
        """Get step details for flows which needs to be run

        :: Example Output = {  
			"flow_id_1": {  
				"incremental_rows": 10000,
				"kafka_topic": "kafka_topic_1",
				"downstream_task_id": "task_id_1",
		        "spark_submit_options": {
		            "--jars": "jar_paths",
		        },
		        "spark_application_jar": "jar_path",
		        "spark_application_arguements": ("s3Hudi", "--flowId", "flow_id_1"),
				"cluster_id": "cluster_id_1",
				"step_id": "step_id_1"
			},  
			"flow_id_2": {
				"incremental_rows": 10000,
				"kafka_topic": "kafka_topic_2",
				"downstream_task_id": "task_id_2",
		        "spark_application_arguements": ("s3Hudi", "--flowId", "flow_id_2"),
			}
		}
        """
        if self.redshift_sla_seconds:
            flows_info = self.get_hudi_to_redshift_flows_info(conn)
        else:
            flows_info = self.get_s3_to_hudi_flows_info(conn)

        running_flows = self._filter_running_flows(self.flows_to_run.keys(), conn, available_clusters)

        # If a flow is already running, append corresponding cluster_id and step_id
        for flow_id in flows_info.keys():
            if running_flows.get(flow_id):
                flows_info[flow_id]["cluster_id"] = running_flows.get(flow_id)[0]
                flows_info[flow_id]["step_id"] = running_flows.get(flow_id)[1]

        return flows_info

    def execute(self, context):
        conn = pb.get_connection(self.connection_alias)

        all_available_clusters = self.get_available_clusters(conn=conn)
        self.flows_info = self.get_flows_info(conn=conn, available_clusters=all_available_clusters)
        self.log.info(f"Flows info: {self.flows_info}")
        self.log.info(f"Available clusters: {dict(all_available_clusters)}")
        cluster_type_task_id_mappings = self.get_cluster_type_task_id_mapping(conn=conn)
        self.log.info(f"Cluster type task id mapping: {dict(cluster_type_task_id_mappings)}")

        self.xcom_push(context, "flows_info", self.flows_info)

        self.task_id_cluster_mapping = dict()

        # Loop over config identifier and assign clusters to all tasks
        for identifier, mapping in cluster_type_task_id_mappings.items():
            self.update_task_id_cluster_mapping(
                available_clusters=all_available_clusters.get(identifier, dict()),
                cluster_type_task_id_mapping=mapping,
            )

        return self.task_id_cluster_mapping
