function(env="staging", spark_job_name="test-job", job_namespace="spark-jobs", driver_file_path="s3://path/to/driver/run_notebook.py",
            tenant="blinkit", spark_conf="{}", packages="[]", jars="[]", py_files="[]",
            job_arguments="[]", load_value="low", node_type="spot", max_executors=null, python_packages="")

local provisioner_type = if node_type == "od" then "od" else "spot";

local config = {
    namespace: job_namespace,
    serviceAccount: [job_namespace, "sa"],
    SparkImage: "************.dkr.ecr.ap-southeast-1.amazonaws.com/spark-platform:0.0.3_50ce7a0229b3",
    DriverNodeValue: [job_namespace, "driver"] ,
    ExecutorNodeValue: [job_namespace, "executor", provisioner_type],
    SparkVersion: "3.5.1",
    default_env: [
        {
            name: "SERVICE",
            value: "airflow"
        },
        {
            name: "ENVIRONMENT",
            value: "prep"
        },
        {
            name: "SOURCE",
            value: "airflow-prep"
        }
    ]
};

local pypi_conf = {
    driverVolumeMounts: [
                            {
                              "name": "package-volume",
                              "mountPath": "/usr/local/lib/python3.10/dist-packages"
                            }
                        ],
    initContainer: [
        {
           "name": "python-package-installer",
           "image": config.SparkImage,
           "command": [
                          "bash",
                          "-c",
                          "pip install --index-url https://pypi.grofer.io/simple " + python_packages +  " -t /python-packages/ && cp -r /usr/local/lib/python3.10/dist-packages/* /python-packages/"
                     ],
           "volumeMounts": [
                      {
                        "name": "package-volume",
                        "mountPath": "/python-packages"
                      }
           ]
        }
    ],
    volume: [
          {
            "name": "package-volume",
            "emptyDir": {}
          }
    ],

};

local load_type = {
    "low": {
        executor: {
            "initialExecutors": 3,
            "maxExecutors": 6,
            "memory": "8g",
            "cores": "1"
        },
        driver: {
            "memory": "2g",
            "cores": "1"
        }
    },
    "medium": {
        executor: {
            "initialExecutors": 6,
            "maxExecutors": 16,
            "memory": "16g",
            "cores": "2"
        },
        driver: {
            "memory": "2g",
            "cores": "1"
        }
    },
    "high" : {
        executor: {
            "initialExecutors": 10,
            "maxExecutors": 32,
            "memory": "32g",
            "cores": "3"
        },
        driver: {
            "memory": "2g",
            "cores": "1"
        }
    },
};

local extraJavaOptions = {
    driver: "-Divy.cache.dir=/tmp -Divy.home=/tmp -XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -XX:+G1SummarizeConcMark -XX:InitiatingHeapOccupancyPercent=35 -XX:OnOutOfMemoryError='kill -9 %p'",
    executor: "-Divy.cache.dir=/tmp -Divy.home=/tmp -XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -XX:+G1SummarizeConcMark -XX:InitiatingHeapOccupancyPercent=35 -XX:OnOutOfMemoryError='kill -9 %p'"
};

local dynamic_max_executors = if max_executors != null then std.parseInt(max_executors) else load_type[load_value]["executor"]["maxExecutors"];

local logging = {
    enable: true,
    volume: {
        common: {
            "name": "log4j-config",
            "configMap": {
                "name": "spark-log4j-config",
                "items": [
                    {
                        "key": "log4j2.xml",
                        "path": "log4j2.xml"
                    }
                ]
            }
        }
    },
    volumeMount:{
        common: [{
            "name": "log4j-config",
            "mountPath": "/etc/log4j"
        }]
    }
};

{
    "apiVersion": "sparkoperator.k8s.io/v1beta2",
    "kind": "SparkApplication",
    "metadata": {
        "name": spark_job_name,
        "namespace": config.namespace
    },
    "spec": {
        "deps": {
            "packages": [] + std.parseJson(packages),
            "jars": [] + std.parseJson(jars),
            "pyFiles": [] + std.parseJson(py_files)
        },
        "sparkUIOptions": {
            "ingressAnnotations": {
                "kubernetes.io/ingress.class": "nginx"
            }
        },
        "arguments": [] + std.parseJson(job_arguments),
        "type": "Python",
        "pythonVersion": "3",
        "mode": "cluster",
        "image": config.SparkImage,
        "imagePullPolicy": "IfNotPresent",
        "mainApplicationFile": driver_file_path,
        "volumes": [
            logging.volume.common,
        ] + (if python_packages != "" then pypi_conf.volume else []),
        "sparkVersion": config.SparkVersion,
        "restartPolicy": {
            "type": "OnFailure",
            "onFailureRetries": 1,
            "onFailureRetryInterval": 10,
            "onSubmissionFailureRetries": 1,
            "onSubmissionFailureRetryInterval": 20
        },
        "timeToLiveSeconds": 1800,
        "nodeSelector": {
            "tenant": tenant
        },
        "driver": {
            "tolerations": [
                {
                    "key": "node-type",
                    "operator": "Equal",
                    "value": std.join("-", config.DriverNodeValue),
                    "effect": "NoSchedule"
                }
            ],
            "labels": {
                "version": config.SparkVersion,
                "tenant": tenant,
                "application": "spark",
                "component" : "driver",
                "environment" : env
            },
            "volumeMounts": [] 
                            + (if python_packages != "" then pypi_conf.driverVolumeMounts else [])
                            + (if logging.enable then logging.volumeMount.common else []),
            "initContainers": []
                              + (if python_packages != "" then pypi_conf.initContainer else []),
            "serviceAccount": std.join("-", config.serviceAccount),
            "terminationGracePeriodSeconds": 300,
            "javaOptions": extraJavaOptions.driver,
            "env": config.default_env,
        },
        "executor": {
            "tolerations": [
                {
                    "key": "node-type",
                    "operator": "Equal",
                    "value": std.join("-", config.ExecutorNodeValue),
                    "effect": "NoSchedule"
                }
            ],
            "labels": {
                "version": config.SparkVersion,
                "tenant": tenant,
                "application": "spark",
                "component" : "executor",
                "environment" : env
            },
            "volumeMounts": [] 
                            + (if python_packages != "" then pypi_conf.driverVolumeMounts else [])
                            + (if logging.enable then logging.volumeMount.common else []),
            "initContainers": []
                              + (if python_packages != "" then pypi_conf.initContainer else []),
            "serviceAccount": std.join("-", config.serviceAccount),
            "javaOptions": extraJavaOptions.executor,
            "env": config.default_env,
        },
        "monitoring" : {
            "exposeDriverMetrics": true,
            "exposeExecutorMetrics": true,
            "prometheus": {
                "jmxExporterJar": "/opt/spark/jars/jmx_prometheus_javaagent-0.11.0.jar",
                "port": 9249
            }
        },
        "dynamicAllocation": {
            "enabled": true,
            "maxExecutors": dynamic_max_executors,
            "initialExecutors" : load_type[load_value]["executor"]["initialExecutors"],
            "minExecutors" : 1
        },
        "sparkConf": {
            "spark.driver.memory": load_type[load_value]["driver"]["memory"],
            "spark.driver.cores": load_type[load_value]["driver"]["cores"],
            "spark.executor.memory": load_type[load_value]["executor"]["memory"],
            "spark.executor.cores": load_type[load_value]["executor"]["cores"],
        } + std.parseJson(spark_conf),
        "sparkConfigMap": "spark-log4j-config"
    }
}
