function(env="staging",tenant="blinkit", custom_iam_role_for_job=null)

local logs_bucket =  "blinkit-analytics-logs";
local hive_metastore_uri_dict = {
    "blinkit": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083",
};

local iceberg_metastore_uri_dict = {
    "blinkit_iceberg": "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9084",
    "blinkit_iceberg_staging": "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9083",
};

local default_iam_role_conf = {
    "spark.hadoop.fs.s3a.aws.credentials.provider": "com.amazonaws.auth.WebIdentityTokenCredentialsProvider",
};

local custom_iam_role_conf = {
    "spark.hadoop.fs.s3a.assumed.role.credentials.provider": "com.amazonaws.auth.EC2ContainerCredentialsProviderWrapper",
    "spark.hadoop.fs.s3a.aws.credentials.provider": "org.apache.hadoop.fs.s3a.auth.AssumedRoleCredentialProvider",
    "spark.hadoop.fs.s3a.assumed.role.arn": std.format("arn:aws:iam::125719378300:role/%s", custom_iam_role_for_job),
};

local iam_role_conf = if custom_iam_role_for_job != null then custom_iam_role_conf else default_iam_role_conf;

{
    default_spark_conf: {
        "spark.plugins": "io.dataflint.spark.SparkDataflintPlugin",
        "spark.hadoop.fs.s3a.impl": "org.apache.hadoop.fs.s3a.S3AFileSystem",
        "spark.hadoop.fs.s3.impl": "org.apache.hadoop.fs.s3a.S3AFileSystem",
        "spark.hadoop.fs.s3a.connection.timeout": "1200000",
        "spark.hadoop.fs.s3a.path.style.access": "true",
        "spark.hadoop.fs.s3a.connection.maximum": "200",
        "spark.hadoop.fs.s3a.fast.upload": "true",
        "spark.hadoop.hive.metastore.uris": hive_metastore_uri_dict[tenant],
        "spark.hadoop.hive.execution.engine": "tez",
        "spark.default.parallelism": "50",
        "spark.dynamicAllocation.enabled": "true",
        // Enable logging to S3
        "spark.eventLog.dir": std.format("s3://%s/spark/prep/spark-event-logs/", logs_bucket),
        "spark.eventLog.enabled": "true",
        // Custom URL pattern for executor logs in History Server
        // Set environment variables for proper substitution
        // "spark.driverEnv.SPARK_APP_ID": "${SPARK_APPLICATION_ID}",
        // "spark.executorEnv.SPARK_APP_ID": "${SPARK_APPLICATION_ID}",
        // "spark.executorEnv.SPARK_EXECUTOR_ID": "$(SPARK_EXECUTOR_ID)",
        "spark.ui.custom.executor.log.url": "https://logs.analytics.blinkit.in/view/prep/spark-application-logs/{{APP_ID}}/{{EXECUTOR_ID}}/combined",
        // "spark.history.custom.executor.log.url": std.format("https://ap-southeast-1.console.aws.amazon.com/s3/buckets/%s?region=ap-southeast-1&bucketType=general&prefix=spark/prep/spark-application-logs/{{APP_ID}}/executor/{{EXECUTOR_ID}}/&tab=objects", logs_bucket),
        // "spark.ui.custom.driver.log.url": std.format("https://ap-southeast-1.console.aws.amazon.com/s3/buckets/%s?region=ap-southeast-1&bucketType=general&prefix=spark/prep/spark-application-logs/{{APP_ID}}/driver/&tab=objects", logs_bucket),
        // Enable driver logs to S3
        // "spark.driver.log.localDir": "/opt/spark/work-dir/s3a:/blinkit-analytics-logs/spark/prep/spark-application-logs/${spark.app.id}/driver",
        // "spark.driver.log.dfsDir": "s3://grofers-test-dse-singapore/neelesh/spark/application-logs/driver/1",
        // "spark.driver.log.persistToDfs.enabled": "true",
        "spark.executorEnv.SPARK_EXECUTOR_ATTRIBUTE_APP_ID": "$(SPARK_APPLICATION_ID)",
        "spark.executorEnv.SPARK_EXECUTOR_ATTRIBUTE_EXECUTOR_ID": "$(SPARK_EXECUTOR_ID)",
        "spark.driverEnv.SPARK_DRIVER_ATTRIBUTE_APP_ID": "$(SPARK_EXECUTOR_ID)",
        // Define which log files to track
        // "spark.executorEnv.SPARK_EXECUTOR_ATTRIBUTE_LOG_FILES": "stderr,stdout",
        // Define custom URL patterns for stderr and stdout logs
        // "spark.ui.custom.executor.stderr.url": std.format("https://ap-southeast-1.console.aws.amazon.com/s3/buckets/%s?region=ap-southeast-1&bucketType=general&prefix=spark/prep/spark-application-logs/{{APP_ID}}/executor/{{EXECUTOR_ID}}/stderr&tab=objects", logs_bucket),
        // "spark.ui.custom.executor.stdout.url": std.format("https://ap-southeast-1.console.aws.amazon.com/s3/buckets/%s?region=ap-southeast-1&bucketType=general&prefix=spark/prep/spark-application-logs/{{APP_ID}}/executor/{{EXECUTOR_ID}}/stdout&tab=objects", logs_bucket),
        "spark.executor.heartbeatInterval": "60s",
        "spark.history.fs.cleaner.enabled": "true",
        "spark.history.fs.cleaner.interval": "1h",
        "spark.history.fs.cleaner.maxAge": "72h",
        "spark.locality.wait": "10",
        "spark.logConf": "true",
        "spark.memory.fraction": "0.80",
        "spark.memory.storageFraction": "0.30",
        "spark.network.timeout": "800s",
        "spark.rdd.compress": "true",
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.shuffle.blockTransferService": "nio",
        "spark.shuffle.compress": "true",
        "spark.shuffle.spill.compress": "true",
        "spark.sql.hive.convertMetastoreOrc": "true",
        "spark.sql.orc.enableVectorizedReader": "true",
        "spark.sql.orc.impl": "native",
        "spark.sql.shuffle.partitions": "50",
        "spark.storage.level": "MEMORY_AND_DISK_SER",
        "spark.yarn.scheduler.reporterThread.maxFailures": "5",
        "spark.yarn.maxAppAttempts": "1",
        "spark.worker.timeout": "1200000",
        "spark.sql.broadcastTimeout": "12000",
        "spark.blacklist.decommissioning.enabled": "true",
        "spark.blacklist.decommissioning.timeout": "1h",
        "spark.driver.defaultJavaOptions": "-XX:+UseG1GC",
        "spark.decommissioning.timeout.threshold": "20",
        "spark.executor.defaultJavaOptions": "-XX:+UseG1GC",
        "spark.executor.id": "driver",
        "spark.files.fetchFailure.unRegisterOutputOnHost": "true",
        "spark.hadoop.orc.overwrite.output.file": "true",
        "spark.hadoop.yarn.timeline-service.enabled": "false",
        "spark.sql.catalogImplementation": "hive",
        "spark.sql.parquet.fs.optimized.committer.optimization-enabled": "true",
        "spark.shuffle.mapStatus.compression.codec": "lz4",
        "spark.hadoop.fs.s3a.committer.name": "directory",
        "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
        "spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored": "true",
        "spark.dynamicAllocation.executorIdleTimeout": "300s",
        "spark.sql.extensions": "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
        "spark.sql.catalog.spark_catalog": "org.apache.iceberg.spark.SparkSessionCatalog",
        "spark.sql.catalog.spark_catalog.type": "hive",
        "spark.sql.catalog.blinkit_iceberg": "org.apache.iceberg.spark.SparkCatalog",
        "spark.sql.blinkit_iceberg.handle-timestamp-without-timezone": "true",
        "spark.sql.catalog.blinkit_iceberg.uri": iceberg_metastore_uri_dict["blinkit_iceberg"],
        "spark.sql.catalog.blinkit_iceberg.type": "hive",
        "spark.sql.catalog.blinkit_iceberg_staging": "org.apache.iceberg.spark.SparkCatalog",
        "spark.sql.blinkit_iceberg_staging.handle-timestamp-without-timezone": "true",
        "spark.sql.catalog.blinkit_iceberg_staging.type": "hive",
        "spark.sql.catalog.blinkit_iceberg_staging.uri": iceberg_metastore_uri_dict["blinkit_iceberg_staging"],
    } + iam_role_conf
}
