import json
import _jsonnet

from typing import Dict, Any, List, Tuple, Optional
import logging
from airflow.models import Variable

logging.basicConfig(level=logging.INFO)

WORKFLOW_CONFIG_PATH = "/usr/local/airflow/dags/repo/plugins/metrics_plugin/operators/eks_spark/workflow_configs/"


def get_env():
    return Variable.get("env", "preprod")

def get_z_env():
    return Variable.get("z_env", "staging")

def get_job_application_file(step_command: Dict[str, Any], spark_job_name: str) -> Tuple[Dict[str, Any], str]:
    tenant: str = step_command.get("tenant", "blinkit")
    env: str = get_env()

    default_spark_config = get_default_spark_config(tenant=tenant, env=env)

    driver_file: str = step_command.get("driver_file", '')

    spark_conf: Dict[str, Any] = {**default_spark_config, **step_command.get("spark_conf", {})}

    packages: List[str] = step_command.get("spark_packages", [])

    jars: List[str] = step_command.get("spark_jars", [])

    py_files: List[str] = step_command.get("python_files", [])

    job_arguments: List[str] = step_command.get("job_args", [])

    job_namespace: str = _get_namespace(env, tenant)

    load_type: str = step_command.get("load_type", "low")

    node_type: str = step_command.get("node_type", "spot")

    max_executors: Optional[int] = step_command.get("max_executors", None)

    python_packages: List[str] = step_command.get("python_packages", [])

    application_file_json: Dict[str, Any] = _get_application_file_json(spark_job_name, tenant, driver_file, spark_conf,
                                                                       packages, jars, py_files, job_arguments,
                                                                       job_namespace, load_type,
                                                                       node_type, max_executors, python_packages, env)

    return application_file_json, job_namespace

def _get_namespace(env: str, tenant: str, etl_type: str = "default") -> str:
    env_suffix = env[:4] #production -> prod, preprod -> prep
    namespace_params = [env_suffix, tenant, "spark", etl_type]
    namespace = "-".join(namespace_params)
    return namespace


def _get_application_file_json(spark_job_name: str, tenant: str, driver_file: str, spark_conf: Dict[str, Any],
                               packages: List[str], jars: List[str], py_files: List[str], job_arguments: List[str],
                               job_namespace: str, load_type: str, node_type: str, max_executors: Optional[int],
                               python_packages: List[str], env: str) -> dict:
    job_config_path: str =  WORKFLOW_CONFIG_PATH + "spark_application_file_template.jsonnet"
    z_env = get_z_env()
    #default values
    python_packages = ["hvac"] + python_packages
    jars = ["s3://blinkit-eks-spark/artifacts/jars/hudi/emr-7.5.0-hudi-spark-bundle.jar"] + jars
    py_files = ["s3://grofers-test-dse-singapore/spark-k8s-test/jhub/py_packages/spark_tools_config.py", "s3://grofers-test-dse-singapore/spark-k8s-test/jhub/py_packages/libs.zip"] + py_files
    # f"s3://blinkit-analytics/config/pyjumbo/{z_env}/config/spark_tools_config.py",
    tla_vars: Dict[str, str] = {
        "env": env,
        "spark_job_name": spark_job_name,
        "job_namespace": job_namespace,
        "driver_file_path": driver_file,
        "tenant": tenant,
        "spark_conf": json.dumps(spark_conf),
        "packages": json.dumps(packages),
        "jars": json.dumps(jars),
        "py_files": json.dumps(py_files),
        "job_arguments": json.dumps(job_arguments),
        "load_value": load_type,
        "node_type": node_type,
        "python_packages": " ".join(python_packages)
    }
    # Only add max_executors if it is given through etl_config. Else pick from default template
    if max_executors is not None:
        tla_vars["max_executors"] = str(max_executors)
    application_file_json: Dict[str, Any] = json.loads(_jsonnet.evaluate_file(job_config_path, tla_vars=tla_vars))

    return application_file_json


def get_default_spark_config(tenant: str, custom_iam_role_for_job: Optional[str] = None, env: str = "Stage"):
    tla_vars: Dict[str, str] = {
        "env": env,
        "tenant": tenant
    }
    if custom_iam_role_for_job is not None:
        tla_vars["custom_iam_role_for_job"] = custom_iam_role_for_job
    workflow_config_path: str = WORKFLOW_CONFIG_PATH + "spark_workflow_config.jsonnet"
    workflow_config = json.loads(_jsonnet.evaluate_file(workflow_config_path, tla_vars=tla_vars))
    return workflow_config.get("default_spark_conf", {})