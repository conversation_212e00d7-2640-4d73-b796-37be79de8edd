# VERSION 2.1.0
# AUTHOR: Data Team
# DESCRIPTION: Basic Airflow container with decore and pencilbox

# BUILDING BASE IMAGE:
	# docker build . -f Docker<PERSON>le \
  # --build-arg AIRFLOW_VERSION="2.1.0"\
  # --build-arg AIRFLOW_HOME="/usr/local/airflow"\
	# --build-arg AIRFLOW_INSTALLATION_METHOD="apache-airflow" \
	# --build-arg AIRFLOW_CONSTRAINTS_REFERENCE="constraints-2.1.0"\
	# --build-arg ADDITIONAL_RUNTIME_APT_DEPS="libssl-dev default-libmysqlclient-dev build-essential libpq-dev libspatialindex-dev python3-rtree cmake g++ git libgdal-dev" \
	# --build-arg AIRFLOW_PIP_VERSION="20.3.1" \
	# --build-arg PYTHON_BASE_IMAGE="python:3.7-slim" \
	# --build-arg AIRFLOW_CONSTRAINTS_LOCATION="https://raw.githubusercontent.com/apache/airflow/constraints-2.1.0/constraints-3.7.txt" \
	# --tag registry.grofer.io/data/airflow-base:2.1.0-orbis-python3.7


FROM registry.grofer.io/data/airflow-base:2.1.0-orbis-python3.7

RUN pip install \
  --global-option=build_ext \
  --global-option="-I/usr/include/gdal/" \
  GDAL==$(gdal-config --version)


COPY decore /usr/local/decore
WORKDIR /usr/local/decore
RUN pip install --no-cache-dir .

COPY pencilbox /usr/local/pencilbox
WORKDIR /usr/local/pencilbox
RUN pip install --no-cache-dir .

COPY orbis /opt/orbis
WORKDIR /opt/orbis
RUN pip install --no-cache-dir .

COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt

WORKDIR ${AIRFLOW_HOME}
USER root
