GIT_REPO_NAME ?= $(shell basename `git rev-parse --show-toplevel`)

DOCKER_REPOSITORY ?= registry.grofer.io/data/airflow-orbis-python3.7
DOCKER_TAG ?= latest
DOCKER_IMAGE ?= $(DOCKER_REPOSITORY):$(DOCKER_TAG)

.PHONY: docker-build
docker-build:
	@if [ ! -d "./decore" ]; then\
		<NAME_EMAIL>:grofers/decore ./decore;\
	else\
		cd ./decore;\
		git pull origin master;\
		cd $(PWD);\
	fi

	@if [ ! -d "./pencilbox" ]; then\
		<NAME_EMAIL>:grofers/pencilbox ./pencilbox;\
	else\
		cd ./pencilbox;\
		git pull origin master;\
		cd $(PWD);\
	fi

	@if [ ! -d "./orbis" ]; then\
		<NAME_EMAIL>:grofers/orbis ./orbis;\
	else\
		cd ./orbis;\
		git pull origin master;\
		cd $(PWD);\
	fi

	docker build --build-arg PYTHON_DEPS="Flask-OAuthlib" --build-arg AIRFLOW_DEPS="crypto,google_auth,kubernetes,password,ssh" --tag=$(DOCKER_IMAGE) .

.PHONY: docker-push
docker-push: docker-build
	docker push $(DOCKER_IMAGE)
