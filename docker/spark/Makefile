GIT_REPO_NAME ?= $(shell basename `git rev-parse --show-toplevel`)

DOCKER_REPOSITORY ?= registry.grofer.io/data/airflow-spark
DOCKER_TAG ?= latest
DOCKER_IMAGE ?= $(DOCKER_REPOSITORY):$(DOCKER_TAG)

.PHONY: docker-build
docker-build:
	docker build --build-arg PYTHON_DEPS="Flask-OAuthlib" --build-arg AIRFLOW_DEPS="crypto,google_auth,kubernetes,password,ssh" --tag=$(DOCKER_IMAGE) .

.PHONY: docker-push
docker-push: docker-build
	docker push $(DOCKER_IMAGE)
