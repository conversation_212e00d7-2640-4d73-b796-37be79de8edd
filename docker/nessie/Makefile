GIT_REPO_NAME ?= $(shell basename `git rev-parse --show-toplevel`)

DOCKER_REPOSITORY ?= registry.grofer.io/data/airflow-nessie
DOCKER_TAG ?= latest
DOCKER_IMAGE ?= $(DOCKER_REPOSITORY):$(DOCKER_TAG)

.PHONY: clone
clone:
	wget http://apachemirror.wuchna.com/spark/spark-2.4.4/spark-2.4.4-bin-hadoop2.7.tgz
	tar -xvf spark-2.4.4-bin-hadoop2.7.tgz

	@if [ ! -d "./nessie" ]; then\
		<NAME_EMAIL>:grofers/nessie ./nessie;\
	else\
		cd ./nessie;\
		git pull origin master;\
		cd $(PWD);\
	fi

	@if [ ! -d "./decore" ]; then\
		<NAME_EMAIL>:grofers/decore ./decore;\
	else\
		cd ./decore;\
		git pull origin master;\
		cd $(PWD);\
	fi

.PHONY: build
build: clone
	cd ./nessie && sbt assembly;

.PHONY: docker-build
docker-build: build
	docker build --build-arg PYTHON_DEPS="Flask-OAuthlib" --build-arg AIRFLOW_DEPS="crypto,google_auth,kubernetes,password,ssh" --tag=$(DOCKER_IMAGE) .

.PHONY: docker-push
docker-push: docker-build
	docker push $(DOCKER_IMAGE)
