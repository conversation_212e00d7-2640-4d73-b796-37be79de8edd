# TODO: Remove once productionized, KEPT IT HERE TO RUN FROM LOCAL

import sys
import logging
import requests
from datetime import datetime, timedelta
import pytz
import pencilbox as pb

from config_loader import load_entity_config, get_available_entities, get_entity_categories
from utils.forecast_utils import process_forecast
from utils.starrocks_utils import get_max_updated_timestamp
from utils.notification_utils import send_slack_notification, display_user_activity_summary

# Configuration parameters - passed from config.yml via papermill
# These parameters are set in config.yml and injected by the DAG framework

# Core processing parameters
ENTITY_NAME = "storeops"  # Set to specific entity name or None to process all entities
LIST_ENTITIES_ONLY = False  # Set to True to only list available entities
LOG_LEVEL = "INFO"  # Logging level
DEBUG_MODE = True  # Set to True to enable debug mode (skip actual operations)

SLACK_CHANNEL = "planit-notifs" if not DEBUG_MODE else "bl-data-slack-testing"

# Environment configuration - configurable by end user
ENVIRONMENT = "preprod"  # Environment setting (preprod, prod, etc.)

# StarRocks configuration - passed from config.yml
STARROCKS_SECRET_PATH = f"data/services/planner-platform/{ENVIRONMENT}/databases/starrocks/capacity_planner"

# Data loader configuration - fetch from vault
DATA_LOADER_VAULT_PATH = f"data/services/planner-platform/{ENVIRONMENT}/backend/data_loader"


# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
    force=True
)
logger = logging.getLogger(__name__)

# Fetch data loader configuration from vault
logger.info(f"Fetching data loader configuration from vault path: {DATA_LOADER_VAULT_PATH}")
try:
    data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)
    S3_BUCKET = data_loader_config.get("s3_bucket", "blinkit-data-staging")
    S3_REGION = data_loader_config.get("s3_region", "ap-southeast-1")
    IAM_ROLE_ARN = data_loader_config.get("iam_role_arn", "arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role")
    logger.info(f"Successfully fetched data loader configuration from vault")
    logger.info(f"S3 bucket: {S3_BUCKET}")
    logger.info(f"S3 region: {S3_REGION}")
    logger.info(f"IAM role ARN: {IAM_ROLE_ARN}")
except Exception as e:
    logger.warning(f"Failed to fetch data loader configuration from vault: {str(e)}")
    logger.warning("Using fallback configuration values")
    # Fallback values - always use preprod
    S3_BUCKET = "blinkit-data-staging"
    S3_REGION = "ap-southeast-1"
    IAM_ROLE_ARN = "arn:aws:iam::183295456051:role/preprod-blinkit-planner-starrocks-role"

# Calculate model trigger time
MODEL_TRIGGER_TIME = (datetime.now(pytz.UTC) - timedelta(hours=4)).strftime("%Y-%m-%d %H:%M:%S")
logger.info(f"Model trigger time calculated: {MODEL_TRIGGER_TIME}")

def get_forecasts_inputs_with_emails(entity_id=3, forecast_id=20):
    """
    Fetch forecasts inputs data from the API, including user_emails field
    """
    try:
        # Get API credentials from vault
        data_loader_config = pb.get_secret(DATA_LOADER_VAULT_PATH)
        access_token = data_loader_config.get('access_token')
        refresh_token = data_loader_config.get('refresh_token')
        
        # Setup API request
        base_url = f"https://capacity-planner-preprod-data.prod-sgp-k8s.grofer.io/api/v1/blinkit/storeops/{entity_id}/forecasts/{forecast_id}/inputs"
        
        headers = {
            "accept": "application/json",
            "Cookie": f"refresh_token={refresh_token}; access_token={access_token};"
        }
        
        # Get current date and calculate start date (next Monday)
        today = datetime.today() + timedelta(days=7)
        days_since_monday = today.weekday()
        start_date = today - timedelta(days=days_since_monday)
        
        # Setup parameters
        params = {
            "start_time": start_date.strftime("%Y-%m-%d 00:00:00"),
            "time_grain": "weekly",
            "multi_aggregation_grain": "entity",
        }
        
        # Make the API request
        logger.info(f"Fetching forecast inputs from API for week starting {params['start_time']}")
        response = requests.get(base_url, params=params, headers=headers)
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"Successfully fetched {len(data.get('data', []))} records from API")
                return data
            except ValueError:
                logger.error(f"Response is not valid JSON: {response.text}")
                return None
        else:
            logger.error(f"API request failed with status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching forecast inputs: {str(e)}")
        return None

# Track user emails and update timestamps
initial_max_ts = None
updated_emails = set()
final_max_ts = None

# Create StarRocks configuration object
STARROCKS_CONFIG = {
    "secret_path": STARROCKS_SECRET_PATH,
    "s3_bucket": S3_BUCKET,
    "s3_region": S3_REGION,
    "iam_role_arn": IAM_ROLE_ARN
}
logger.info(f"StarRocks configuration initialized")


def collect_user_emails_in_range(data, start_ts=None, end_ts=None):
    """
    Extract user emails from API response data and add to global set
    Only collect emails from records with timestamps between start_ts and end_ts
    
    Args:
        data (dict): API response data
        start_ts (str): Start timestamp to filter records
        end_ts (str): End timestamp to filter records
    """
    global updated_emails
    
    if not data or not isinstance(data, dict):
        logger.warning("No valid data provided to collect user emails")
        return
    
    records = data.get("data", [])
    for record in records:
        # Check if the record has an updated_at timestamp in the desired range
        record_ts = record.get("updated_at")
        
        # Include records with timestamps between start_ts and end_ts
        is_after_start = not start_ts or (record_ts and record_ts > start_ts)
        is_before_end = not end_ts or (record_ts and record_ts <= end_ts)
        
        if is_after_start and is_before_end:
            # Extract user emails from the record
            emails = record.get("user_emails", [])
            if emails:
                for email in emails:
                    updated_emails.add(email)
    
    logger.info(f"Total unique user emails collected in timestamp range: {len(updated_emails)}")


def process_entity_category(entity_name, category, model_trigger_time, debug_mode=False, starrocks_config=None):
    """Process all forecasts for a specific entity category."""
    logger.info("-"*60)
    logger.info(f"STARTING {entity_name.upper()}.{category.upper()} UPDATE PROCESS")
    logger.info("-"*60)

    try:
        logger.info(f"Using model trigger time: {model_trigger_time}")
        logger.info(f"Debug mode: {debug_mode}")

        entity_config = load_entity_config(entity_name, category)
        model_configs = entity_config.get("models", {})

        if not model_configs:
            logger.warning(f"No model configurations found for entity '{entity_name}' category '{category}'")
            return {"entity": entity_name, "category": category, "processed": 0, "total": 0, "success": False}

        processed_count = 0
        total_forecasts = 0

        for model_id, model_config in model_configs.items():
            forecast_types = model_config.get("forecast_types", [])
            total_forecasts += len(forecast_types)

            for forecast_type in forecast_types:
                try:
                    logger.info(f"Processing {forecast_type} forecasts for model {model_id} in category {category}")
                    result = process_forecast(forecast_type, entity_config, table_suffix=model_id, model_trigger_time=model_trigger_time, debug_mode=debug_mode, starrocks_config=starrocks_config)
                    if result:
                        processed_count += 1
                except Exception as e:
                    logger.error(f"Failed to process {forecast_type} forecast for model {model_id} in category {category}: {str(e)}")

        logger.info(f"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}.{category}")
        logger.info("-"*60)
        logger.info(f"{entity_name.upper()}.{category.upper()} UPDATE PROCESS COMPLETED")
        logger.info("-"*60)

        return {
            "entity": entity_name,
            "category": category,
            "processed": processed_count,
            "total": total_forecasts,
            "success": processed_count > 0
        }

    except Exception as e:
        logger.error(f"Fatal error processing entity '{entity_name}' category '{category}': {str(e)}")
        return {"entity": entity_name, "category": category, "processed": 0, "total": 0, "success": False, "error": str(e)}

def process_entity(entity_name, model_trigger_time, debug_mode=False, starrocks_config=None):
    """Process all forecasts for a specific entity (all categories)."""
    logger.info("="*80)
    logger.info(f"STARTING {entity_name.upper()} UPDATE PROCESS")
    logger.info("="*80)

    try:
        # Check if entity has categories
        try:
            categories = get_entity_categories(entity_name)
            logger.info(f"Entity '{entity_name}' has categories: {categories}")

            # Process each category
            all_results = []
            total_processed = 0
            total_forecasts = 0
            successful_categories = 0

            for category in categories:
                logger.info(f"Processing category '{category}' for entity '{entity_name}'")
                result = process_entity_category(entity_name, category, model_trigger_time, debug_mode, starrocks_config)
                all_results.append(result)

                total_processed += result["processed"]
                total_forecasts += result["total"]
                if result["success"]:
                    successful_categories += 1

            logger.info("="*60)
            logger.info(f"ENTITY {entity_name.upper()} CATEGORY SUMMARY")
            logger.info("="*60)

            for result in all_results:
                category = result["category"]
                processed = result["processed"]
                total = result["total"]
                success = result["success"]
                status = "✅ SUCCESS" if success else "❌ FAILED"
                logger.info(f"  {category}: {processed}/{total} forecasts processed - {status}")

            logger.info("="*80)
            logger.info(f"{entity_name.upper()} UPDATE PROCESS COMPLETED")
            logger.info(f"Categories: {successful_categories}/{len(categories)} successful")
            logger.info(f"Total forecasts: {total_processed}/{total_forecasts} processed")
            logger.info("="*80)

            return {
                "entity": entity_name,
                "categories": all_results,
                "processed": total_processed,
                "total": total_forecasts,
                "success": successful_categories > 0
            }

        except ValueError as e:
            # Legacy single-category entity or entity without categories
            if "multiple categories" not in str(e):
                logger.info(f"Entity '{entity_name}' is a legacy single-category entity")
                entity_config = load_entity_config(entity_name)
                model_configs = entity_config.get("models", {})

                if not model_configs:
                    logger.warning(f"No model configurations found for entity '{entity_name}'")
                    return {"entity": entity_name, "processed": 0, "total": 0, "success": False}

                processed_count = 0
                total_forecasts = 0

                for model_id, model_config in model_configs.items():
                    forecast_types = model_config.get("forecast_types", [])
                    total_forecasts += len(forecast_types)

                    for forecast_type in forecast_types:
                        try:
                            logger.info(f"Processing {forecast_type} forecasts for model {model_id}")
                            result = process_forecast(forecast_type, entity_config, table_suffix=model_id, model_trigger_time=model_trigger_time, debug_mode=debug_mode, starrocks_config=starrocks_config)
                            if result:
                                processed_count += 1
                        except Exception as e:
                            logger.error(f"Failed to process {forecast_type} forecast for model {model_id}: {str(e)}")

                logger.info(f"Completed {processed_count}/{total_forecasts} forecast types for {entity_name}")
                logger.info("="*80)
                logger.info(f"{entity_name.upper()} UPDATE PROCESS COMPLETED")
                logger.info("="*80)

                return {
                    "entity": entity_name,
                    "processed": processed_count,
                    "total": total_forecasts,
                    "success": processed_count > 0
                }
            else:
                raise e

    except Exception as e:
        logger.error(f"Fatal error processing entity '{entity_name}': {str(e)}")
        return {"entity": entity_name, "processed": 0, "total": 0, "success": False, "error": str(e)}

def process_all_entities(model_trigger_time, debug_mode=False, starrocks_config=None):
    """Process all entities defined in the YAML configuration."""
    logger.info("="*80)
    logger.info("STARTING MULTI-ENTITY UPDATE PROCESS")
    logger.info("="*80)
    
    try:
        entities = get_available_entities()
        logger.info(f"Found {len(entities)} entities to process: {entities}")
        
        results = []
        
        for entity_name in entities:
            try:
                result = process_entity(entity_name, model_trigger_time, debug_mode, starrocks_config)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process entity '{entity_name}': {str(e)}")
                results.append({
                    "entity": entity_name,
                    "processed": 0,
                    "total": 0,
                    "success": False,
                    "error": str(e)
                })
        
        logger.info("="*80)
        logger.info("MULTI-ENTITY UPDATE PROCESS SUMMARY")
        logger.info("="*80)
        
        total_processed = 0
        total_forecasts = 0
        successful_entities = 0
        
        for result in results:
            entity_name = result["entity"]
            processed = result["processed"]
            total = result["total"]
            success = result["success"]

            status = "✅ SUCCESS" if success else "❌ FAILED"

            # Check if this is a category-based entity result
            if "categories" in result:
                categories = result["categories"]
                logger.info(f"{entity_name}: {processed}/{total} forecasts processed - {status}")
                for cat_result in categories:
                    category = cat_result["category"]
                    cat_processed = cat_result["processed"]
                    cat_total = cat_result["total"]
                    cat_success = cat_result["success"]
                    cat_status = "✅ SUCCESS" if cat_success else "❌ FAILED"
                    logger.info(f"  └─ {category}: {cat_processed}/{cat_total} forecasts - {cat_status}")
            else:
                # Legacy single-category entity
                logger.info(f"{entity_name}: {processed}/{total} forecasts processed - {status}")

            total_processed += processed
            total_forecasts += total
            if success:
                successful_entities += 1
        
        logger.info("="*80)
        logger.info(f"OVERALL SUMMARY: {successful_entities}/{len(entities)} entities successful")
        logger.info(f"TOTAL FORECASTS: {total_processed}/{total_forecasts} processed")
        logger.info("="*80)
        
        return results
    
    except Exception as e:
        logger.error(f"Fatal error in multi-entity processing: {str(e)}")
        return []


# Main execution logic
try:
    # Get the initial max timestamp from StarRocks
    logger.info("Fetching initial max timestamp from StarRocks")
    initial_max_ts = get_max_updated_timestamp('planner_instore_forecasts_weekly', 'updated_ts', schema="storeops_etls", debug_mode=DEBUG_MODE, starrocks_config=STARROCKS_CONFIG)
    if initial_max_ts:
        logger.info(f"Initial max timestamp from StarRocks: {initial_max_ts}")
    else:
        logger.warning("No initial timestamp found from StarRocks")
    
    # Fetch forecast inputs from API with user_emails field
    logger.info("Fetching forecast inputs from API")
    forecast_data = get_forecasts_inputs_with_emails()
    if forecast_data:
        logger.info(f"Successfully fetched forecast data with {len(forecast_data.get('data', []))} records")
    else:
        logger.error("Failed to fetch forecast inputs from API")
    
    # Handle list entities request
    if LIST_ENTITIES_ONLY:
        entities = get_available_entities()
        print("Available entities:")
        for entity in entities:
            print(f"  - {entity}")
    else:
        # Process specific entity or all entities
        if ENTITY_NAME:
            # Process single entity
            available_entities = get_available_entities()
            if ENTITY_NAME not in available_entities:
                logger.error(f"Entity '{ENTITY_NAME}' not found in configuration")
                logger.error(f"Available entities: {available_entities}")
                raise ValueError(f"Entity '{ENTITY_NAME}' not found")

            result = process_entity(ENTITY_NAME, MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)
            if not result["success"]:
                raise RuntimeError(f"Failed to process entity '{ENTITY_NAME}'")
        else:
            # Process all entities
            results = process_all_entities(MODEL_TRIGGER_TIME, DEBUG_MODE, STARROCKS_CONFIG)

            # Check if any entity failed
            failed_entities = [r for r in results if not r["success"]]
            if failed_entities:
                failed_names = [r['entity'] for r in failed_entities]
                logger.error(f"Some entities failed to process: {failed_names}")
                raise RuntimeError(f"Some entities failed: {failed_names}")
    
    # Get the final max timestamp from StarRocks after processing
    logger.info("Fetching final max timestamp from StarRocks")
    final_max_ts = get_max_updated_timestamp('planner_instore_forecasts_weekly', 'updated_ts', schema="storeops_etls", debug_mode=DEBUG_MODE, starrocks_config=STARROCKS_CONFIG)
    if final_max_ts:
        logger.info(f"Final max timestamp from StarRocks: {final_max_ts}")
    else:
        logger.warning("No final timestamp found, using initial timestamp")
    
    if forecast_data:
        # Collect emails only from records between initial and final timestamps
        collect_user_emails_in_range(forecast_data, initial_max_ts, final_max_ts)
    
    # Display user activity summary
    display_user_activity_summary(updated_emails, initial_max_ts, final_max_ts, DEBUG_MODE, SLACK_CHANNEL, STARROCKS_CONFIG)

except Exception as e:
    logger.error(f"Fatal error: {str(e)}", exc_info=True)
    raise
