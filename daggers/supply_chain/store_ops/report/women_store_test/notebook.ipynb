{"cells": [{"cell_type": "code", "execution_count": null, "id": "6050fc39-9a28-4fd7-a903-a233c3ad5736", "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import pytz\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "358826fd-da55-4b0c-8753-972c33c2a3fe", "metadata": {}, "outputs": [], "source": ["data = pb.from_sheets(\n", "    \"15XCOo9qj-K9WqQxZdrOEklcTLJ3BS5nBkK-InjHGz3I\",\n", "    \"Form Responses 1\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "532c1f39-4a57-4693-b520-682d19a0a26b", "metadata": {}, "outputs": [], "source": ["# data = pd.read_csv(\"women_friendly_stores.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa5862b7-0eac-4c1b-a1c8-4f530924cdbc", "metadata": {}, "outputs": [], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "c592ef48-0eb4-4447-9b68-a4c171ed70dc", "metadata": {}, "outputs": [], "source": ["data[\"Status\"] = data[\"Status\"].replace(r\"^\\s*$\", np.nan, regex=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4e9b86fb-12fa-4f62-8923-80b0f07c4a31", "metadata": {}, "outputs": [], "source": ["data_null_status = data[data[\"Status\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "id": "2d89e8ef-e925-4fa4-9757-d275af2f6a7c", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "8c77fc06-c2ec-4798-8cb9-92e260b6aa06", "metadata": {}, "outputs": [], "source": ["data_not_null = data[data[\"Status\"].notnull()]"]}, {"cell_type": "code", "execution_count": null, "id": "9e1545b1-722b-40df-b0fb-992e3aabcd3e", "metadata": {}, "outputs": [], "source": ["data_not_null"]}, {"cell_type": "code", "execution_count": null, "id": "d3663b7b-5b43-42df-90e9-a9bca320bbd2", "metadata": {}, "outputs": [], "source": ["data_null_status[\"is_women_friendly\"] = (\n", "    data_null_status[\"Block Status\"]\n", "    .map({\"Block of Women\": \"false\", \"Unblock for Women\": \"true\"})\n", "    .fillna(\"true\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "edaae4c5-3dba-4733-997c-7b8d04fcd172", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "ae380535-bdf8-42d8-ab2f-20ddb27e5012", "metadata": {}, "outputs": [], "source": ["data_null_status[\"Status\"] = data_null_status[\"Status\"].astype(\"object\")"]}, {"cell_type": "code", "execution_count": null, "id": "00688be0-6fa2-428b-a131-8e4fb5689122", "metadata": {}, "outputs": [], "source": ["data_null_status.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "002c31d0-6543-422c-a2d9-ba42cf4e8e2d", "metadata": {}, "outputs": [], "source": ["data_null_status[\"Site id\"] = data_null_status[\"Site id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "2343cd2f-435a-4f59-bd2d-30e521e15c1f", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "187ba525-3215-41a8-b9b3-53864622b8c9", "metadata": {}, "outputs": [], "source": ["url = \"https://ops-management-canary-ops-management.prod-sgp-k8s.grofer.io/master/sites/patch-meta/\"\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"x-api-key\": \"XwFsqSgs.cgzSlYerPhJX3X7apAqVEHhF4mNczIU6\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "6e9ae629-78da-43cf-8b56-8bdc73076fa5", "metadata": {}, "outputs": [], "source": ["def to_bool(val):\n", "    if isinstance(val, str):\n", "        val = val.strip().lower()\n", "        if val == \"true\":\n", "            return True\n", "        elif val == \"false\":\n", "            return False\n", "        else:\n", "            return None\n", "    return bool(val)\n", "\n", "\n", "for idx, row in data_null_status.iterrows():\n", "    payload_item = {\n", "        \"site_id\": int(row[\"Site id\"]),\n", "        \"meta\": {\"is_women_friendly\": to_bool(row[\"is_women_friendly\"])},\n", "    }\n", "\n", "    response = requests.patch(\n", "        url,\n", "        headers=headers,\n", "        data=json.dumps(payload_item),\n", "    )\n", "    response_json = response.json()\n", "\n", "    error_message = response_json.get(\"error_message\")\n", "\n", "    if error_message is None:\n", "        data_null_status.at[idx, \"Status\"] = \"Updated successfully\"\n", "    else:\n", "        data_null_status.at[idx, \"Status\"] = error_message\n", "\n", "    print(\n", "        f\"Site ID: {payload_item['site_id']}, \"\n", "        f\"StatusCode: {response.status_code}, \"\n", "        f\"Payload: {payload_item}, \"\n", "        f\"Status: {data_null_status.at[idx, 'Status']}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "1b4c279b-0e82-43a9-9774-6341f0461aa3", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "771adb74-2c35-4adc-9cfc-83da93ba58a3", "metadata": {}, "outputs": [], "source": ["data_null_status.drop(columns=[\"is_women_friendly\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2f7270f4-cc55-4765-8143-c31f98508c60", "metadata": {}, "outputs": [], "source": ["data_null_status.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d61d9a77-265e-4eb3-8f36-45fd6d155885", "metadata": {}, "outputs": [], "source": ["failed_df = data_null_status[data_null_status[\"Status\"] != \"Updated successfully\"]\n", "\n", "for email, group in failed_df.groupby(\"Email Address\"):\n", "    site_status_list = \"\\n\".join(\n", "        f\"Site ID: {row['Site id']}, Status: {row['Status']}\" for _, row in group.iterrows()\n", "    )\n", "\n", "    subject = \"Women Stores Site Update Failures\"\n", "    body = (\n", "        f\"Hello,\\n\\nThe following sites failed to update:\\n\\n{site_status_list}\\n\\nPlease review.\"\n", "    )\n", "\n", "    try:\n", "        pb.send_email(\n", "            from_email=\"<EMAIL>\",\n", "            to_email=[email],\n", "            subject=subject,\n", "            html_content=body,\n", "        )\n", "        print(f\"Email sent successfully to {email}!\")\n", "    except Exception as e:\n", "        print(f\"Failed to send email to {email}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e32736ce-e5f6-47d9-83d2-3a66c66ad6ce", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "3d4980d9-6d0a-4db8-b57a-829e0b0e6be1", "metadata": {}, "outputs": [], "source": ["data_null_status"]}, {"cell_type": "code", "execution_count": null, "id": "0fae2a1e-a6c1-4069-aa0b-3ad5c45da1a2", "metadata": {}, "outputs": [], "source": ["data_not_null"]}, {"cell_type": "code", "execution_count": null, "id": "aec4e0a9-9f72-4648-bdbc-d744f1370911", "metadata": {}, "outputs": [], "source": ["combined_df = pd.concat([data_null_status, data_not_null], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fbfedea5-db1e-4984-855e-49c7118da686", "metadata": {}, "outputs": [], "source": ["combined_df"]}, {"cell_type": "code", "execution_count": null, "id": "34e75771-95c4-49f6-8f73-587550d55396", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    combined_df,\n", "    \"15XCOo9qj-K9WqQxZdrOEklcTLJ3BS5nBkK-InjHGz3I\",\n", "    \"Form Responses 1\",\n", "    clear_cache=True,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}