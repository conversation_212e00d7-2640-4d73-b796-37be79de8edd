alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: forecasts_weekly
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: supply_chain
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0758LAP6DQ
path: supply_chain/store_ops/etl/forecasts_weekly
paused: true
pool: supply_chain_pool
project_name: store_ops
schedule:
  end_date: '2025-09-24T00:00:00'
  interval: 20,50 * * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
