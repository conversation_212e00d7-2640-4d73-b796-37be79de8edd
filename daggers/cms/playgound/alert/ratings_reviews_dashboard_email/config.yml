alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ratings_reviews_dashboard_email
dag_type: alert
escalation_priority: high
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: cms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08AFJVCEGL
path: cms/playgound/alert/ratings_reviews_dashboard_email
paused: true
pool: cms_pool
project_name: playgound
schedule:
  end_date: '2025-10-27T00:00:00'
  interval: 0 3 * * *
  start_date: '2025-08-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
