{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5d44e37d-7b13-4102-b7fd-c9e6d07281fa", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "f46788a8-a4c3-42bb-b201-4ab1de6b876b", "metadata": {}, "outputs": [], "source": ["att_ai_query = \"\"\"\n", "WITH pre_pmra_mapping AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.entity_instance_id[1]') AS BIGINT) AS request_id,\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.attribute[1]') AS BIGINT) AS attribute_id,\n", "\t\tentity_id AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.enabled_flag[1]') AS enabled_flag,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.value[1]') AS value,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\t--AND history LIKE '%%\"value\": [%%'\n", "\t\tAND entity_name = 'PropertyMapping'\n", "\t    AND fk_references LIKE '%%\"entity_id\": 667%%'\n", "\t\t--AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", "\t\tAND history NOT LIKE '%%Not Processed%%'\n", "),\n", "\n", "filter AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        attribute_id = 9741\n", "        AND request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "all_attempts AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "\t\t--f.property_mapping_id,\n", "\t\tROW_NUMBER() OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS attempt_no,\n", "\t\tppm.created_at AS attempt_ts,\n", "\t\tf.value AS rid_ai_result,\n", "        LAG(ppm.created_at)  OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS prev_ts,\n", "\t\tLEAD(ppm.created_at) OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS next_ts\n", "        --ppm.enabled_flag,\n", "        --ppm.source\n", "\tFROM\n", "\t\tfilter f\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = f.property_mapping_id\n", "\tWHERE\n", "\t    ppm.enabled_flag = 'True'\n", "),\n", "\n", "pre_att_ai_result_base AS (\n", "\tSELECT\n", "\t\taram.request_id,\n", "\t\taram.attribute_id,\n", "\t\tJSON_EXTRACT_SCALAR(a.history, '$.meta_value[1]') AS ai_result,\n", "\t\ta.created_at + INTERVAL '330' MINUTE AS created_at,\n", "\t\tROW_NUMBER() OVER (PARTITION BY aram.request_id, aram.attribute_id ORDER BY a.created_at) AS attempt_no\n", "\tFROM\n", "\t\tcms_historian.historian_model_history a\n", "\tJOIN\n", "\t    pre_pmra_mapping aram\n", "\t        ON aram.property_mapping_id = CAST(JSON_EXTRACT_SCALAR(a.fk_references, '$.property_mapping_id') AS BIGINT)\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "        AND entity_name = 'PropertyMetadataMapping'\n", "        AND a.fk_references LIKE '%%\"meta_id\": 341%%'\n", "        AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", ")\n", "--SELECT * FROM pre_att_ai_result_base WHERE request_id = 304327\n", "\n", "SELECT\n", "    a.request_id,\n", "    a.attempt_no,\n", "    b.attribute_id,\n", "    b.ai_result,\n", "    b.created_at\n", "FROM\n", "    all_attempts a\n", "LEFT JOIN\n", "    pre_att_ai_result_base b\n", "        ON a.request_id = b.request_id\n", "        AND b.created_at >= a.attempt_ts\n", "        AND b.created_at < COALESCE(a.next_ts, TIMESTAMP '2030-01-01 00:00:00')\n", "ORDER BY 1, 3, 5\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 3, "id": "a8bac41c-4c91-4a0c-96cf-3c39a4e62f6c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "att_ai_df = pd.read_sql_query(sql=att_ai_query, con=con)"]}, {"cell_type": "code", "execution_count": 4, "id": "53ea2b05-88db-4809-96a9-d75b102cb30f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 55422 entries, 0 to 55421\n", "Data columns (total 5 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   request_id    55422 non-null  int64  \n", " 1   attempt_no    55422 non-null  int64  \n", " 2   attribute_id  55421 non-null  float64\n", " 3   ai_result     45401 non-null  object \n", " 4   created_at    55421 non-null  object \n", "dtypes: float64(1), int64(2), object(2)\n", "memory usage: 2.1+ MB\n"]}], "source": ["att_ai_df.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "b087a886-f741-4c04-aa91-5291a263c70e", "metadata": {}, "outputs": [], "source": ["att_ai_df = att_ai_df.dropna(subset=[\"attribute_id\"])"]}, {"cell_type": "code", "execution_count": 6, "id": "dfc319ce-a7b4-484d-be85-545951bf91c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 55421 entries, 0 to 55421\n", "Data columns (total 5 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   request_id    55421 non-null  int64  \n", " 1   attempt_no    55421 non-null  int64  \n", " 2   attribute_id  55421 non-null  float64\n", " 3   ai_result     45401 non-null  object \n", " 4   created_at    55421 non-null  object \n", "dtypes: float64(1), int64(2), object(2)\n", "memory usage: 2.5+ MB\n"]}], "source": ["att_ai_df.info()"]}, {"cell_type": "code", "execution_count": 7, "id": "e395bc9f-a124-43a7-9248-4dd77961dc68", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>ai_result</th>\n", "      <th>created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27114</th>\n", "      <td>306057</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>undetected</td>\n", "      <td>2025-08-05 12:20:53.914</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27115</th>\n", "      <td>306057</td>\n", "      <td>4</td>\n", "      <td>3.0</td>\n", "      <td>None</td>\n", "      <td>2025-08-06 18:12:47.379</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       request_id  attempt_no  attribute_id   ai_result  \\\n", "27114      306057           3           3.0  undetected   \n", "27115      306057           4           3.0        None   \n", "\n", "                    created_at  \n", "27114  2025-08-05 12:20:53.914  \n", "27115  2025-08-06 18:12:47.379  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_df[(att_ai_df['request_id'] == 306057) & (att_ai_df['attribute_id'] == 3)]"]}, {"cell_type": "code", "execution_count": 8, "id": "da94f015-00b3-4594-94e4-793196890609", "metadata": {}, "outputs": [], "source": ["att_ai_df.sort_values([\"request_id\", \"attribute_id\", \"attempt_no\"], inplace=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "c64e4db3-1b21-494e-81f1-f17de4b8385f", "metadata": {}, "outputs": [], "source": ["att_ai_df[\"ai_result\"] = att_ai_df.groupby([\"request_id\", \"attribute_id\"])[\"ai_result\"].ffill()"]}, {"cell_type": "code", "execution_count": 10, "id": "b379f09f-99fa-407d-a642-0ee075f9743d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 55421 entries, 0 to 55421\n", "Data columns (total 5 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   request_id    55421 non-null  int64  \n", " 1   attempt_no    55421 non-null  int64  \n", " 2   attribute_id  55421 non-null  float64\n", " 3   ai_result     55421 non-null  object \n", " 4   created_at    55421 non-null  object \n", "dtypes: float64(1), int64(2), object(2)\n", "memory usage: 2.5+ MB\n"]}], "source": ["att_ai_df.info()"]}, {"cell_type": "code", "execution_count": 11, "id": "4c6e499e-7c00-4ec9-865d-26c05e25c05f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>ai_result</th>\n", "      <th>created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27114</th>\n", "      <td>306057</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>undetected</td>\n", "      <td>2025-08-05 12:20:53.914</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27115</th>\n", "      <td>306057</td>\n", "      <td>4</td>\n", "      <td>3.0</td>\n", "      <td>undetected</td>\n", "      <td>2025-08-06 18:12:47.379</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       request_id  attempt_no  attribute_id   ai_result  \\\n", "27114      306057           3           3.0  undetected   \n", "27115      306057           4           3.0  undetected   \n", "\n", "                    created_at  \n", "27114  2025-08-05 12:20:53.914  \n", "27115  2025-08-06 18:12:47.379  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_df[(att_ai_df['request_id'] == 306057) & (att_ai_df['attribute_id'] == 3)]"]}, {"cell_type": "code", "execution_count": 12, "id": "39c48835-e67d-42a8-b157-3538b750c3ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>ai_result</th>\n", "      <th>created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>563</th>\n", "      <td>301163</td>\n", "      <td>2</td>\n", "      <td>77.0</td>\n", "      <td>not match</td>\n", "      <td>2025-07-26 19:19:29.668</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     request_id  attempt_no  attribute_id  ai_result               created_at\n", "563      301163           2          77.0  not match  2025-07-26 19:19:29.668"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_df[(att_ai_df[\"request_id\"] == 301163) & (att_ai_df[\"attribute_id\"] == 77)]"]}, {"cell_type": "code", "execution_count": 13, "id": "dc027da0-60b3-4f52-9b42-598dcbe46e9d", "metadata": {}, "outputs": [], "source": ["att_ai_reasoning_query = \"\"\"\n", "WITH pre_pmra_mapping AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.entity_instance_id[1]') AS BIGINT) AS request_id,\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.attribute[1]') AS BIGINT) AS attribute_id,\n", "\t\tentity_id AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.enabled_flag[1]') AS enabled_flag,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.value[1]') AS value,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\t--AND history LIKE '%%\"value\": [%%'\n", "\t\tAND entity_name = 'PropertyMapping'\n", "\t    AND fk_references LIKE '%%\"entity_id\": 667%%'\n", "\t\t--AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", "\t\tAND history NOT LIKE '%%Not Processed%%'\n", "),\n", "\n", "filter AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        attribute_id = 9741\n", "        AND request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "all_attempts AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "\t\t--f.property_mapping_id,\n", "\t\tROW_NUMBER() OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS attempt_no,\n", "\t\tppm.created_at AS attempt_ts,\n", "\t\tf.value AS rid_ai_result,\n", "        LAG(ppm.created_at)  OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS prev_ts,\n", "\t\tLEAD(ppm.created_at) OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS next_ts\n", "        --ppm.enabled_flag,\n", "        --ppm.source\n", "\tFROM\n", "\t\tfilter f\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = f.property_mapping_id\n", "\tWHERE\n", "\t    ppm.enabled_flag = 'True'\n", "),\n", "\n", "pre_att_ai_result_base AS (\n", "\tSELECT\n", "\t\taram.request_id,\n", "\t\taram.attribute_id,\n", "\t\tJSON_EXTRACT_SCALAR(a.history, '$.meta_value[1]') AS ai_result,\n", "\t\ta.created_at + INTERVAL '330' MINUTE AS created_at,\n", "\t\tROW_NUMBER() OVER (PARTITION BY aram.request_id, aram.attribute_id ORDER BY a.created_at) AS attempt_no\n", "\tFROM\n", "\t\tcms_historian.historian_model_history a\n", "\tJOIN\n", "\t    pre_pmra_mapping aram\n", "\t        ON aram.property_mapping_id = CAST(JSON_EXTRACT_SCALAR(a.fk_references, '$.property_mapping_id') AS BIGINT)\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "        AND entity_name = 'PropertyMetadataMapping'\n", "        AND a.fk_references LIKE '%%\"meta_id\": 340%%'\n", "        AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", ")\n", "--SELECT * FROM pre_att_ai_result_base WHERE request_id = 304327\n", "\n", "SELECT\n", "    a.request_id,\n", "    a.attempt_no,\n", "    b.attribute_id,\n", "    b.ai_result AS ai_result_reason,\n", "    b.created_at\n", "FROM\n", "    all_attempts a\n", "LEFT JOIN\n", "    pre_att_ai_result_base b\n", "        ON a.request_id = b.request_id\n", "        AND b.created_at >= a.attempt_ts\n", "        AND b.created_at < COALESCE(a.next_ts, TIMESTAMP '2030-01-01 00:00:00')\n", "ORDER BY 1, 3, 5\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 14, "id": "5855df3f-41fb-48cf-a506-b7896b79f697", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "att_ai_reason_df = pd.read_sql_query(sql=att_ai_reasoning_query, con=con)"]}, {"cell_type": "code", "execution_count": 15, "id": "85e9aa46-c35a-4b8c-8ea2-cd3e98c34d35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 55448 entries, 0 to 55447\n", "Data columns (total 5 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   request_id        55448 non-null  int64  \n", " 1   attempt_no        55448 non-null  int64  \n", " 2   attribute_id      55447 non-null  float64\n", " 3   ai_result_reason  54289 non-null  object \n", " 4   created_at        55447 non-null  object \n", "dtypes: float64(1), int64(2), object(2)\n", "memory usage: 2.1+ MB\n"]}], "source": ["att_ai_reason_df.info()"]}, {"cell_type": "code", "execution_count": 16, "id": "0ca14039-7fe8-4ba8-9a42-8a6268d23d22", "metadata": {}, "outputs": [], "source": ["att_ai_reason_df = att_ai_reason_df.dropna(subset=[\"attribute_id\"])"]}, {"cell_type": "code", "execution_count": 17, "id": "e6ab5a11-9bab-4edd-94aa-bb825f40ac02", "metadata": {}, "outputs": [], "source": ["att_ai_reason_df.sort_values([\"request_id\", \"attribute_id\", \"attempt_no\"], inplace=True)"]}, {"cell_type": "code", "execution_count": 18, "id": "1bb1506c-96bc-491f-9019-4e6d2b046638", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>ai_result_reason</th>\n", "      <th>created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27113</th>\n", "      <td>306057</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>The liquid in the bottle appears to be a golde...</td>\n", "      <td>2025-08-05 12:20:53.918</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27114</th>\n", "      <td>306057</td>\n", "      <td>4</td>\n", "      <td>3.0</td>\n", "      <td>The product liquid is golden/amber in color, a...</td>\n", "      <td>2025-08-06 18:12:47.384</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       request_id  attempt_no  attribute_id  \\\n", "27113      306057           3           3.0   \n", "27114      306057           4           3.0   \n", "\n", "                                        ai_result_reason  \\\n", "27113  The liquid in the bottle appears to be a golde...   \n", "27114  The product liquid is golden/amber in color, a...   \n", "\n", "                    created_at  \n", "27113  2025-08-05 12:20:53.918  \n", "27114  2025-08-06 18:12:47.384  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_reason_df[(att_ai_reason_df[\"request_id\"] == 306057) & (att_ai_reason_df[\"attribute_id\"] == 3)]"]}, {"cell_type": "code", "execution_count": 19, "id": "d6042144-319b-448f-af59-af52688b4459", "metadata": {}, "outputs": [], "source": ["att_ai_reason_df[\"ai_result_reason\"] = att_ai_reason_df.groupby([\"request_id\", \"attribute_id\"])[\n", "    \"ai_result_reason\"\n", "].ffill()"]}, {"cell_type": "code", "execution_count": 20, "id": "73c3fc6a-88bd-47c1-b992-bfaba59543b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 55447 entries, 0 to 55447\n", "Data columns (total 5 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   request_id        55447 non-null  int64  \n", " 1   attempt_no        55447 non-null  int64  \n", " 2   attribute_id      55447 non-null  float64\n", " 3   ai_result_reason  55447 non-null  object \n", " 4   created_at        55447 non-null  object \n", "dtypes: float64(1), int64(2), object(2)\n", "memory usage: 2.5+ MB\n"]}], "source": ["att_ai_reason_df.info()"]}, {"cell_type": "code", "execution_count": 21, "id": "d0b2deef-da8d-4692-bac6-1f957f59339f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>ai_result_reason</th>\n", "      <th>created_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>563</th>\n", "      <td>301163</td>\n", "      <td>2</td>\n", "      <td>77.0</td>\n", "      <td>The text value 'On Image' is a vague placehold...</td>\n", "      <td>2025-07-26 19:19:29.670</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     request_id  attempt_no  attribute_id  \\\n", "563      301163           2          77.0   \n", "\n", "                                      ai_result_reason  \\\n", "563  The text value 'On Image' is a vague placehold...   \n", "\n", "                  created_at  \n", "563  2025-07-26 19:19:29.670  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_reason_df[\n", "    (att_ai_reason_df[\"request_id\"] == 301163) & (att_ai_reason_df[\"attribute_id\"] == 77)\n", "]"]}, {"cell_type": "code", "execution_count": 22, "id": "9ab75b0d-a654-4924-9cc7-ad10b6a70c21", "metadata": {}, "outputs": [], "source": ["att_ai_rejection_query = \"\"\"\n", "WITH pre_pmra_mapping AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.entity_instance_id[1]') AS BIGINT) AS request_id,\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.attribute[1]') AS BIGINT) AS attribute_id,\n", "\t\tentity_id AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.enabled_flag[1]') AS enabled_flag,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.value[1]') AS value,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\t--AND history LIKE '%%\"value\": [%%'\n", "\t\tAND entity_name = 'PropertyMapping'\n", "\t    AND fk_references LIKE '%%\"entity_id\": 667%%'\n", "\t\t--AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", "\t\tAND history NOT LIKE '%%Not Processed%%'\n", "),\n", "\n", "filter AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        attribute_id = 9741\n", "        AND request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "all_attempts AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "\t\t--f.property_mapping_id,\n", "\t\tROW_NUMBER() OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS attempt_no,\n", "\t\tppm.created_at AS attempt_ts,\n", "\t\tf.value AS rid_ai_result,\n", "        LAG(ppm.created_at)  OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS prev_ts,\n", "\t\tLEAD(ppm.created_at) OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS next_ts\n", "        --ppm.enabled_flag,\n", "        --ppm.source\n", "\tFROM\n", "\t\tfilter f\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = f.property_mapping_id\n", "\tWHERE\n", "\t    ppm.enabled_flag = 'True'\n", "),\n", "--SELECT * FROM for_all_attempts WHERE request_id = 304327\n", "\n", "rejection_reasons_base AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(a.fk_references, '$.property_mapping_id') AS BIGINT) AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(a.history, '$.meta_value[0]')  AS old_rejection_reason,\n", "\t\tJSON_EXTRACT_SCALAR(a.history, '$.meta_value[1]')  AS new_rejection_reason,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS rejection_reason_created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history a\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\t--insert_ds_ist IS NOT NULL\n", "\t\tAND a.history LIKE '%%\"meta_value\": [%%'\n", "\t\t--AND a.history LIKE '%%enabled_flag\": [null, \"True\"]%%'\n", "\t\tAND a.entity_name = 'PropertyMetadataMapping'\n", "\t\tAND a.fk_references LIKE '%%meta_id\": 270%%'\n", "),\n", "\n", "all_atts_attempt AS (\n", "    SELECT\n", "        a.request_id,\n", "        b.attempt_no,\n", "        b.attempt_ts,\n", "        b.next_ts,\n", "        a.attribute_id,\n", "        MIN_BY(c.old_rejection_reason, c.rejection_reason_created_at) AS old_rejection_reason,\n", "        MIN_BY(c.new_rejection_reason, c.rejection_reason_created_at) AS new_rejection_reason,\n", "        MIN(c.rejection_reason_created_at) AS rejection_reason_created_at\n", "    FROM\n", "        pre_pmra_mapping a\n", "    JOIN\n", "        all_attempts b\n", "            ON a.request_id = b.request_id\n", "    LEFT JOIN\n", "        rejection_reasons_base c\n", "            ON c.property_mapping_id = a.property_mapping_id\n", "            AND c.rejection_reason_created_at >= b.attempt_ts\n", "    GROUP BY 1, 2, 3, 4, 5\n", ")\n", "\n", "SELECT\n", "    request_id,\n", "    attempt_no,\n", "    attempt_ts,\n", "    attribute_id,\n", "    new_rejection_reason AS rejection_reason,\n", "    rejection_reason_created_at\n", "FROM\n", "    all_atts_attempt\n", "WHERE\n", "    rejection_reason_created_at IS NOT NULL\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 23, "id": "be3a286c-a50c-4096-b103-aae803a1148c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "att_ai_rejection_df = pd.read_sql_query(sql=att_ai_rejection_query, con=con)"]}, {"cell_type": "code", "execution_count": 24, "id": "8ffee01a-e85e-477e-bd4f-2edbdaac33ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1400 entries, 0 to 1399\n", "Data columns (total 6 columns):\n", " #   Column                       Non-Null Count  Dtype \n", "---  ------                       --------------  ----- \n", " 0   request_id                   1400 non-null   int64 \n", " 1   attempt_no                   1400 non-null   int64 \n", " 2   attempt_ts                   1400 non-null   object\n", " 3   attribute_id                 1400 non-null   int64 \n", " 4   rejection_reason             1400 non-null   object\n", " 5   rejection_reason_created_at  1400 non-null   object\n", "dtypes: int64(3), object(3)\n", "memory usage: 65.8+ KB\n"]}], "source": ["att_ai_rejection_df.info()"]}, {"cell_type": "code", "execution_count": 25, "id": "87a6563f-b6d5-4f35-b587-fdbb6759ed08", "metadata": {}, "outputs": [], "source": ["att_ai_rejection_df.sort_values([\"request_id\", \"attribute_id\", \"attempt_no\"], inplace=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "73cd992d-7464-4c24-b0ac-8d67f6b5a9c4", "metadata": {}, "outputs": [], "source": ["att_ai_rejection_df[\"rejection_reason\"] = att_ai_rejection_df.groupby([\"request_id\", \"attribute_id\"])[\n", "    \"rejection_reason\"\n", "].ffill()"]}, {"cell_type": "code", "execution_count": 27, "id": "0e489075-458a-4218-9a07-1ffa018c0b80", "metadata": {}, "outputs": [], "source": ["att_ai_pid_data_query = \"\"\"\n", "WITH pre_pmra_mapping AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.entity_instance_id[1]') AS BIGINT) AS request_id,\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.attribute[1]') AS BIGINT) AS attribute_id,\n", "\t\tentity_id AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.enabled_flag[1]') AS enabled_flag,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.value[1]') AS value,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\t--AND history LIKE '%%\"value\": [%%'\n", "\t\tAND entity_name = 'PropertyMapping'\n", "\t    AND fk_references LIKE '%%\"entity_id\": 667%%'\n", "\t\t--AND meta LIKE '%%\"cms-product-request-inference-output\"%%'\n", "\t\tAND history NOT LIKE '%%Not Processed%%'\n", "),\n", "\n", "filter AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        attribute_id = 9741\n", "        AND request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "for_all_attempts AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "\t\t--f.property_mapping_id,\n", "\t\tROW_NUMBER() OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS attempt_no,\n", "\t\tppm.created_at AS attempt_ts,\n", "\t\tf.value AS rid_ai_result,\n", "        LAG(ppm.created_at)  OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS prev_ts,\n", "\t\tLEAD(ppm.created_at) OVER (PARTITION BY f.request_id ORDER BY ppm.created_at) AS next_ts\n", "        --ppm.enabled_flag,\n", "        --ppm.source\n", "\tFROM\n", "\t\tfilter f\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = f.property_mapping_id\n", "\tWHERE\n", "\t    ppm.enabled_flag = 'True'\n", "),\n", "--SELECT * FROM for_all_attempts WHERE request_id = 304327\n", "\n", "pmra_mapping AS (\n", "    SELECT\n", "        request_id,\n", "        attribute_id,\n", "        property_mapping_id\n", "    FROM\n", "        pre_pmra_mapping\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "latest_mapped AS (\n", "    SELECT\n", "        entity_instance_id AS request_id,\n", "        attribute_id\n", "    FROM\n", "        cms.property_mappings\n", "    GROUP BY 1, 2\n", "    /*\n", "    WHERE\n", "        request_id = 304327\n", "    /*/\n", "),\n", "\n", "pr_base AS (\n", "    SELECT\n", "        er.id AS request_id,\n", "        CAST(pmm.meta_value AS BIGINT) AS product_id,\n", "        gp.install_ts  + INTERVAL '330' MINUTE AS pid_created_date\n", "    FROM\n", "        cms.entity_requests er\n", "    JOIN\n", "        cms.property_mappings pm\n", "            ON pm.entity_instance_id = er.id\n", "            AND pm.enabled_flag\n", "            AND pm.lake_active_record\n", "            AND pm.entity_id = 667\n", "    JOIN\n", "        cms.property_metadata_mappings pmm\n", "            ON pmm.property_mapping_id = pm.id\n", "            AND pmm.lake_active_record\n", "            AND pmm.enabled_flag\n", "            AND pmm.meta_id = 303\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = CAST(pmm.meta_value AS BIGINT)\n", "            AND gp.lake_active_record --AND gp.enabled_flag\n", "    GROUP BY 1, 2, 3\n", "), \n", "\n", "product_state_base AS (\n", "    SELECT\n", "        entity_id                                   AS product_id,\n", "        JSON_EXTRACT_SCALAR(history, '$.current_state[1]') AS product_state,\n", "        created_at + INTERVAL '330' MINUTE           AS state_change_ts\n", "    FROM\n", "        cms_historian.historian_model_history\n", "    WHERE\n", "        insert_ds_ist >= CAST(date_add('day', -90, current_date) AS varchar)\n", "        AND history NOT LIKE '%%\"current_state\": [null, \"draft\"]%%'\n", "        AND history LIKE '%%\"current_state\": %%'\n", "        AND entity_name = 'GrProduct'\n", "),\n", "\n", "product_attribute_base AS (\n", "    SELECT\n", "        CAST(JSON_EXTRACT_SCALAR(fk_references, '$.product_id')    AS BIGINT) AS product_id,\n", "        CAST(JSON_EXTRACT_SCALAR(fk_references, '$.attribute_id')  AS BIGINT) AS attribute_id,\n", "        JSON_EXTRACT_SCALAR(history, '$.value[0]')                  AS old_value,\n", "        JSON_EXTRACT_SCALAR(history, '$.value[1]')                  AS new_value,\n", "        action,\n", "        created_at + INTERVAL '330' MINUTE                          AS update_date\n", "    FROM\n", "        cms_historian.historian_model_history\n", "    WHERE\n", "        insert_ds_ist >= CAST(date_add('day', -90, current_date) AS VARCHAR)\n", "        AND history LIKE '%%\"value\"%%'\n", "        AND entity_name = 'GrProductAttributeMapping'\n", "),\n", "\n", "all_atts_attempt_state AS (\n", "    SELECT\n", "        a.request_id,\n", "        p.product_id,\n", "        p.pid_created_date,\n", "        b.attempt_no,\n", "        b.attempt_ts,\n", "        c.product_state,\n", "        c.state_change_ts,\n", "        ROW_NUMBER() OVER (PARTITION BY a.request_id, b.attempt_no ORDER BY c.state_change_ts) AS rnk\n", "    FROM\n", "        pmra_mapping a\n", "    JOIN\n", "        pr_base p\n", "            ON p.request_id = a.request_id\n", "    JOIN\n", "        for_all_attempts b\n", "            ON a.request_id = b.request_id\n", "    LEFT JOIN\n", "        product_state_base c\n", "            ON c.product_id = p.product_id\n", "            AND c.state_change_ts >= b.attempt_ts\n", "            AND c.state_change_ts < COALESCE(b.next_ts, TIMESTAMP '2030-01-01 00:00:00')\n", "            AND c.product_state IN ('qc_rejected', 'qc_approved')\n", "    /*\n", "    WHERE\n", "        a.request_id = 304727\n", "    /*/\n", "),\n", "\n", "all_atts_attempt_state_final AS (\n", "    SELECT\n", "        request_id,\n", "        product_id,\n", "        pid_created_date,\n", "        attempt_no,\n", "        attempt_ts,\n", "        CASE\n", "            WHEN product_state = 'qc_approved' THEN 'qc2_approved'\n", "            WHEN product_state = 'qc_rejected' THEN 'qc2_rejected'\n", "            ELSE product_state\n", "        END AS product_state,\n", "        state_change_ts\n", "    FROM\n", "        all_atts_attempt_state\n", "    WHERE\n", "        rnk = 1\n", "),\n", "--SELECT * FROM all_atts_attempt_state_final\n", "\n", "all_atts_attempt_pre_approved AS (\n", "    SELECT\n", "        a.request_id,\n", "        b.attempt_no,\n", "        c.state_change_ts,\n", "        ROW_NUMBER() OVER (PARTITION BY a.request_id, b.attempt_no ORDER BY c.state_change_ts) AS rnk\n", "    FROM\n", "        pmra_mapping a\n", "    JOIN\n", "        pr_base p\n", "            ON p.request_id = a.request_id\n", "    JOIN\n", "        for_all_attempts b\n", "            ON a.request_id = b.request_id\n", "    LEFT JOIN\n", "        product_state_base c\n", "            ON c.product_id = p.product_id\n", "            AND c.state_change_ts >= b.attempt_ts\n", "            AND c.state_change_ts < COALESCE(b.next_ts, TIMESTAMP '2030-01-01 00:00:00')\n", "            AND c.product_state IN ('qc_pre_approved')\n", "    /*\n", "    WHERE\n", "        a.request_id = 304727\n", "    /*/\n", "),\n", "\n", "all_atts_attempt_state_pre_approved_final AS (\n", "    SELECT\n", "        request_id,\n", "        attempt_no,\n", "        state_change_ts\n", "    FROM\n", "        all_atts_attempt_pre_approved\n", "    WHERE\n", "        rnk = 1\n", "),\n", "--SELECT * FROM all_atts_attempt_state_pre_approved_final\n", "\n", "qc2_input AS (\n", "    SELECT\n", "        a.request_id,\n", "        b.attempt_no,\n", "        a.attribute_id,\n", "        c.new_value AS manual_qc2_input,\n", "        ROW_NUMBER() OVER (PARTITION BY a.request_id, b.attempt_no, a.attribute_id ORDER BY c.update_date DESC) AS rnk\n", "    FROM\n", "        pmra_mapping a\n", "    JOIN\n", "        pr_base p\n", "            ON p.request_id = a.request_id\n", "    JOIN\n", "        for_all_attempts b\n", "            ON a.request_id = b.request_id\n", "    JOIN\n", "        all_atts_attempt_state_pre_approved_final f\n", "            ON f.request_id = a.request_id\n", "            AND f.attempt_no = b.attempt_no\n", "    JOIN\n", "        product_attribute_base c\n", "            ON c.product_id = p.product_id\n", "            AND a.attribute_id = c.attribute_id\n", "            AND c.update_date < f.state_change_ts\n", "    /*\n", "    WHERE\n", "        a.request_id = 304727\n", "    /*/\n", "),\n", "\n", "\n", "qc2_input_final AS (\n", "    SELECT\n", "        request_id,\n", "        attempt_no,\n", "        attribute_id,\n", "        manual_qc2_input\n", "    FROM\n", "        qc2_input\n", "    WHERE\n", "        rnk = 1\n", "),\n", "--SELECT * FROM qc2_input_final\n", "\n", "qc2_output AS (\n", "    SELECT\n", "        a.request_id,\n", "        b.attempt_no,\n", "        a.attribute_id,\n", "        c.new_value AS manual_qc2_output,\n", "        ROW_NUMBER() OVER (PARTITION BY a.request_id, b.attempt_no, a.attribute_id ORDER BY c.update_date DESC) AS rnk\n", "    FROM\n", "        pmra_mapping a\n", "    JOIN\n", "        pr_base p\n", "            ON p.request_id = a.request_id\n", "    JOIN\n", "        for_all_attempts b\n", "            ON a.request_id = b.request_id\n", "    JOIN\n", "        all_atts_attempt_state_final f\n", "            ON f.request_id = a.request_id\n", "            AND f.attempt_no = b.attempt_no\n", "    JOIN\n", "        product_attribute_base c\n", "            ON c.product_id = p.product_id\n", "            AND a.attribute_id = c.attribute_id\n", "            AND c.update_date < f.state_change_ts\n", "    /*\n", "    WHERE\n", "        a.request_id = 304727\n", "    /*/\n", "),\n", "\n", "qc2_output_final AS (\n", "    SELECT\n", "        request_id,\n", "        attempt_no,\n", "        attribute_id,\n", "        manual_qc2_output\n", "    FROM\n", "        qc2_output\n", "    WHERE\n", "        rnk = 1\n", ")\n", "\n", "SELECT\n", "    a.request_id,\n", "    a.attempt_no,\n", "    p.attribute_id,\n", "    b.product_id,\n", "    b.pid_created_date,\n", "    CASE\n", "        WHEN COALESCE(b.product_state, 'qc2_pending') = 'qc2_pending' THEN 0\n", "        ELSE 1 END AS manual_qc2_attempt_no,\n", "    CASE WHEN COALESCE(b.product_state, '') = '' THEN NULL ELSE qi.manual_qc2_input END AS manual_qc2_input,\n", "    CASE\n", "        WHEN COALESCE(b.product_id, 0) = 0 THEN 'qc2_not_applicable'\n", "        ELSE COALESCE(b.product_state, 'qc2_pending')\n", "    END AS manual_qc2_result,\n", "    state_change_ts AS manual_qc2_ts,\n", "    CASE WHEN COALESCE(b.product_state, '') = '' THEN NULL ELSE qo.manual_qc2_output END AS manual_qc2_output\n", "FROM\n", "    for_all_attempts a\n", "JOIN\n", "    latest_mapped p\n", "        ON a.request_id = p.request_id\n", "LEFT JOIN\n", "    all_atts_attempt_state_final b\n", "        ON a.request_id = b.request_id\n", "        AND a.attempt_no = b.attempt_no\n", "LEFT JOIN\n", "    qc2_input_final qi\n", "        ON a.request_id = qi.request_id\n", "        AND a.attempt_no = qi.attempt_no\n", "        AND p.attribute_id = qi.attribute_id\n", "LEFT JOIN\n", "    qc2_output_final qo\n", "        ON a.request_id = qo.request_id\n", "        AND a.attempt_no = qo.attempt_no\n", "        AND p.attribute_id = qo.attribute_id\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 28, "id": "5231109f-a161-4cfb-9994-d300374c8474", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "att_ai_pid_data_df = pd.read_sql_query(sql=att_ai_pid_data_query, con=con)"]}, {"cell_type": "code", "execution_count": 29, "id": "6f2b3e19-b07f-4821-a1f5-812fbc33b401", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>product_id</th>\n", "      <th>pid_created_date</th>\n", "      <th>manual_qc2_attempt_no</th>\n", "      <th>manual_qc2_input</th>\n", "      <th>manual_qc2_result</th>\n", "      <th>manual_qc2_ts</th>\n", "      <th>manual_qc2_output</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>301716</td>\n", "      <td>1</td>\n", "      <td>8124</td>\n", "      <td>680703.0</td>\n", "      <td>2025-06-25 17:34:32.998</td>\n", "      <td>1</td>\n", "      <td>True</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-16 17:18:27.919</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>301716</td>\n", "      <td>1</td>\n", "      <td>3551</td>\n", "      <td>680703.0</td>\n", "      <td>2025-06-25 17:34:32.998</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-16 17:18:27.919</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>301716</td>\n", "      <td>1</td>\n", "      <td>1708</td>\n", "      <td>680703.0</td>\n", "      <td>2025-06-25 17:34:32.998</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-16 17:18:27.919</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>301716</td>\n", "      <td>1</td>\n", "      <td>76</td>\n", "      <td>680703.0</td>\n", "      <td>2025-06-25 17:34:32.998</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-16 17:18:27.919</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301716</td>\n", "      <td>1</td>\n", "      <td>141</td>\n", "      <td>680703.0</td>\n", "      <td>2025-06-25 17:34:32.998</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-16 17:18:27.919</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333301</th>\n", "      <td>307585</td>\n", "      <td>1</td>\n", "      <td>4104</td>\n", "      <td>696462.0</td>\n", "      <td>2025-08-07 10:21:49.539</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333302</th>\n", "      <td>307585</td>\n", "      <td>1</td>\n", "      <td>634</td>\n", "      <td>696462.0</td>\n", "      <td>2025-08-07 10:21:49.539</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333303</th>\n", "      <td>307585</td>\n", "      <td>1</td>\n", "      <td>3089</td>\n", "      <td>696462.0</td>\n", "      <td>2025-08-07 10:21:49.539</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333304</th>\n", "      <td>307585</td>\n", "      <td>1</td>\n", "      <td>108</td>\n", "      <td>696462.0</td>\n", "      <td>2025-08-07 10:21:49.539</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333305</th>\n", "      <td>307585</td>\n", "      <td>1</td>\n", "      <td>588</td>\n", "      <td>696462.0</td>\n", "      <td>2025-08-07 10:21:49.539</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>333306 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        request_id  attempt_no  attribute_id  product_id  \\\n", "0           301716           1          8124    680703.0   \n", "1           301716           1          3551    680703.0   \n", "2           301716           1          1708    680703.0   \n", "3           301716           1            76    680703.0   \n", "4           301716           1           141    680703.0   \n", "...            ...         ...           ...         ...   \n", "333301      307585           1          4104    696462.0   \n", "333302      307585           1           634    696462.0   \n", "333303      307585           1          3089    696462.0   \n", "333304      307585           1           108    696462.0   \n", "333305      307585           1           588    696462.0   \n", "\n", "               pid_created_date  manual_qc2_attempt_no manual_qc2_input  \\\n", "0       2025-06-25 17:34:32.998                      1             True   \n", "1       2025-06-25 17:34:32.998                      1                5   \n", "2       2025-06-25 17:34:32.998                      1                    \n", "3       2025-06-25 17:34:32.998                      1                    \n", "4       2025-06-25 17:34:32.998                      1                    \n", "...                         ...                    ...              ...   \n", "333301  2025-08-07 10:21:49.539                      0             None   \n", "333302  2025-08-07 10:21:49.539                      0             None   \n", "333303  2025-08-07 10:21:49.539                      0             None   \n", "333304  2025-08-07 10:21:49.539                      0             None   \n", "333305  2025-08-07 10:21:49.539                      0             None   \n", "\n", "       manual_qc2_result            manual_qc2_ts manual_qc2_output  \n", "0           qc2_approved  2025-07-16 17:18:27.919              True  \n", "1           qc2_approved  2025-07-16 17:18:27.919                 5  \n", "2           qc2_approved  2025-07-16 17:18:27.919                    \n", "3           qc2_approved  2025-07-16 17:18:27.919                    \n", "4           qc2_approved  2025-07-16 17:18:27.919                    \n", "...                  ...                      ...               ...  \n", "333301       qc2_pending                     None              None  \n", "333302       qc2_pending                     None              None  \n", "333303       qc2_pending                     None              None  \n", "333304       qc2_pending                     None              None  \n", "333305       qc2_pending                     None              None  \n", "\n", "[333306 rows x 10 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_pid_data_df"]}, {"cell_type": "code", "execution_count": 30, "id": "cedb9b8d-4be3-4251-a52e-106d8baebd86", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>attempt_no</th>\n", "      <th>attribute_id</th>\n", "      <th>product_id</th>\n", "      <th>pid_created_date</th>\n", "      <th>manual_qc2_attempt_no</th>\n", "      <th>manual_qc2_input</th>\n", "      <th>manual_qc2_result</th>\n", "      <th>manual_qc2_ts</th>\n", "      <th>manual_qc2_output</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7583</th>\n", "      <td>305206</td>\n", "      <td>1</td>\n", "      <td>9235</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      request_id  attempt_no  attribute_id  product_id  \\\n", "7583      305206           1          9235    689174.0   \n", "\n", "             pid_created_date  manual_qc2_attempt_no manual_qc2_input  \\\n", "7583  2025-07-17 10:57:54.664                      1             None   \n", "\n", "     manual_qc2_result            manual_qc2_ts manual_qc2_output  \n", "7583      qc2_approved  2025-07-25 11:09:51.502              None  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_pid_data_df[\n", "    (att_ai_pid_data_df[\"request_id\"] == 305206) & (att_ai_pid_data_df[\"attribute_id\"] == 9235)\n", "]"]}, {"cell_type": "code", "execution_count": 31, "id": "884cf6a1-17eb-4032-8187-1deff6bc04a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["qc2_approved          172170\n", "qc2_pending           122485\n", "qc2_not_applicable     27237\n", "qc2_rejected           11414\n", "Name: manual_qc2_result, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["att_ai_pid_data_df[\"manual_qc2_result\"].value_counts()"]}, {"cell_type": "code", "execution_count": 32, "id": "6b629981-1861-4c8c-9309-64a95b63ddfb", "metadata": {}, "outputs": [], "source": ["# att_ai_pid_data_df[att_ai_pid_data_df['request_id'] == 301416]"]}, {"cell_type": "code", "execution_count": 33, "id": "0630f159-8aad-49f4-913d-59c1c27cdc62", "metadata": {}, "outputs": [], "source": ["main1 = \"\"\"\n", "WITH pre_pmra_mapping AS (\n", "\tSELECT\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.entity_instance_id[1]') AS BIGINT)     AS request_id,\n", "\t\tCAST(JSON_EXTRACT_SCALAR(history, '$.attribute[1]')        AS BIGINT)     AS attribute_id,\n", "\t\tentity_id                                                       AS property_mapping_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.enabled_flag[1]')              AS enabled_flag,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.value[1]')                     AS value,\n", "\t\tcreated_at + INTERVAL '330' MINUTE                             AS value_created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90,  CURRENT_DATE) AS VARCHAR)\n", "\t\tAND insert_ds_ist <  CAST(CURRENT_DATE                  AS VARCHAR)\n", "\t\tAND entity_name    = 'PropertyMapping'\n", "\t\tAND fk_references LIKE '%%\"entity_id\": 667%%'\n", "\t\tAND history       NOT LIKE '%%Not Processed%%'\n", "),\n", "----------------------------------------------------------------------------------------------------------\n", "filter AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        attribute_id = 9741\n", "        AND request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "all_attempts AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "        lm.attribute_id,\n", "\t\t--f.property_mapping_id,\n", "\t\tROW_NUMBER() OVER (PARTITION BY f.request_id ORDER BY ppm.value_created_at) AS attempt_no,\n", "\t\tppm.value_created_at AS attempt_ts,\n", "\t\tf.value AS rid_ai_result,\n", "        LAG(ppm.value_created_at)  OVER (PARTITION BY f.request_id ORDER BY ppm.value_created_at) AS prev_ts,\n", "\t\tLEAD(ppm.value_created_at) OVER (PARTITION BY f.request_id ORDER BY ppm.value_created_at) AS next_ts\n", "\tFROM\n", "\t\tfilter f\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = f.property_mapping_id\n", "    JOIN\n", "        (\n", "            SELECT\n", "                entity_instance_id AS request_id,\n", "                attribute_id\n", "            FROM\n", "                cms.property_mappings\n", "            GROUP BY 1, 2\n", "        ) lm ON lm.request_id = f.request_id\n", "\tWHERE\n", "\t    ppm.enabled_flag = 'True'\n", "),\n", "\n", "filter_input AS (\n", "    SELECT\n", "        request_id,\n", "        property_mapping_id,\n", "        value\n", "    FROM\n", "        pre_pmra_mapping\n", "    WHERE\n", "        request_id IS NOT NULL\n", "    GROUP BY 1, 2, 3\n", "),\n", "----------------------------------------------------------------------------------------------------------\n", "pre_input_base AS (\n", "\tSELECT\n", "\t\ta.request_id,\n", "\t\tppm.attribute_id,\n", "\t\ta.property_mapping_id,\n", "\t\ta.value,\n", "\t\tppm.value_created_at,\n", "\t\tDATE_TRUNC('second', ppm.value_created_at + INTERVAL '2' SECOND) AS enum_flag\n", "\tFROM\n", "\t\tfilter_input a\n", "\tJOIN\n", "\t\tpre_pmra_mapping ppm\n", "\t\t    ON ppm.property_mapping_id = a.property_mapping_id\n", "\t\t    AND ppm.enabled_flag = 'True'\n", "\t/*\n", "\tWHERE\n", "\t    a.request_id = 307707 AND ppm.attribute_id = 6402\n", "\t/*/\n", "),\n", "\n", "agg_counts AS (\n", "    SELECT\n", "        request_id,\n", "        attribute_id,\n", "        enum_flag,\n", "        MIN(value_created_at) AS value_created_at,\n", "        MAX(property_mapping_id) AS property_mapping_id,\n", "        COUNT(*) AS cnt,\n", "        LISTAGG(CAST(value AS VARCHAR), ',') WITHIN GROUP (ORDER BY value_created_at) AS value\n", "    FROM\n", "        pre_input_base\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "input_base AS (\n", "    SELECT\n", "        pib.request_id,\n", "        pib.attribute_id,\n", "        \n", "        CASE\n", "            WHEN ac.cnt > 1 THEN ac.property_mapping_id\n", "            ELSE pib.property_mapping_id\n", "        END AS property_mapping_id,\n", "        \n", "        CASE\n", "            WHEN ac.cnt > 1 THEN ac.value\n", "            ELSE pib.value\n", "        END AS value,\n", "        \n", "        CASE\n", "            WHEN ac.cnt > 1 THEN ac.value_created_at\n", "            ELSE pib.value_created_at\n", "        END AS value_created_at\n", "        \n", "    FROM\n", "        pre_input_base pib\n", "    LEFT JOIN\n", "        agg_counts ac\n", "            ON ac.request_id = pib.request_id\n", "            AND ac.attribute_id = pib.attribute_id\n", "            AND ac.enum_flag = pib.enum_flag\n", "    GROUP BY 1, 2, 3, 4, 5\n", "),\n", "--SELECT * FROM input_base\n", "----------------------------------------------------------------------------------------------------------\n", "state_base AS (\n", "\tSELECT\n", "\t\tentity_id AS request_id,\n", "\t\tJSON_EXTRACT_SCALAR(history, '$.state[1]') AS request_state,\n", "\t\tcreated_at + INTERVAL '330' MINUTE AS created_at\n", "\tFROM\n", "\t\tcms_historian.historian_model_history a\n", "    JOIN\n", "        filter_input fi\n", "            ON fi.request_id = a.entity_id\n", "\tWHERE\n", "\t\tinsert_ds_ist >= CAST(DATE_ADD('day', -90, CURRENT_DATE) AS VARCHAR)\n", "\t\tAND JSON_EXTRACT_SCALAR(history, '$.state[1]') IN ('image_pending', 'rejected', 'approved', 'content_qc')\n", "\t\tAND entity_name = 'EntityRequests'\n", "),\n", "--SELECT * FROM state_base WHERE request_id = 304327\n", "\n", "pre_post_ai_verification_state AS (\n", "    SELECT\n", "        a.request_id,\n", "        a.attempt_no,\n", "        a.attempt_ts,\n", "        a.next_ts,\n", "        MIN_BY(b.request_state, b.created_at) AS post_ai_verification_state,\n", "        MIN(b.created_at) AS created_at\n", "    FROM\n", "        all_attempts a\n", "    JOIN\n", "        state_base b\n", "            ON a.request_id = b.request_id\n", "            AND b.created_at > a.attempt_ts\n", "            AND b.request_state IN ('image_pending', 'rejected')\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "post_ai_verification_image_qc AS (\n", "    SELECT\n", "        a.request_id,\n", "        a.attempt_no,\n", "        a.attempt_ts,\n", "        a.post_ai_verification_state,\n", "        a.created_at,\n", "        MIN_BY(b.request_state, b.created_at) AS post_ai_verification_image_qc,\n", "        MIN(b.created_at) AS image_qc_ts\n", "    FROM\n", "        pre_post_ai_verification_state a\n", "    LEFT JOIN\n", "        state_base b\n", "            ON a.request_id = b.request_id\n", "            AND b.created_at > a.created_at\n", "            --AND b.created_at < COALESCE(a.next_ts, TIMESTAMP '2030-01-01 00:00:00')\n", "            AND a.post_ai_verification_state = 'image_pending'\n", "    GROUP BY 1, 2, 3, 4, 5\n", "),\n", "\n", "post_ai_verification_state AS (\n", "    SELECT\n", "        a.request_id,\n", "        a.attempt_no,\n", "        a.attempt_ts,\n", "        CASE\n", "            WHEN a.post_ai_verification_state = 'rejected' THEN 'qc1_rejected'\n", "            WHEN a.post_ai_verification_state = 'image_pending' THEN 'qc1_approved'\n", "            ELSE 'qc1_pending'\n", "        END AS post_ai_verification_state,\n", "        a.created_at,\n", "        CASE\n", "            WHEN a.post_ai_verification_state IS NULL OR a.post_ai_verification_state = 'rejected' THEN 'image_qc_not_applicable'\n", "            WHEN a.post_ai_verification_state = 'image_pending' AND a.post_ai_verification_image_qc IS NULL THEN 'image_qc_pending'\n", "            WHEN a.post_ai_verification_state = 'image_pending' AND a.post_ai_verification_image_qc = 'content_qc' THEN 'image_qc_rejected'\n", "            WHEN a.post_ai_verification_state = 'image_pending' AND a.post_ai_verification_image_qc = 'approved' THEN 'image_qc_approved'\n", "            WHEN a.request_id = 305162 THEN 'image_qc_approved'\n", "            ELSE 'check for edge case'\n", "        END AS image_qc_status,\n", "        image_qc_ts\n", "    FROM\n", "        post_ai_verification_image_qc a\n", "),\n", "--SELECT * FROM post_ai_verification_state\n", "\n", "inputs_fed_into_ai_attempt  AS (\n", "    SELECT\n", "        f.request_id,\n", "        f.attempt_no,\n", "        f.attempt_ts,\n", "        f.rid_ai_result,\n", "        f.attribute_id,\n", "        MAX_BY(ib.value, ib.value_created_at) AS value,\n", "        MAX(ib.value_created_at) AS value_created_at\n", "    FROM\n", "        all_attempts f\n", "    LEFT JOIN\n", "        input_base ib\n", "            ON ib.request_id = f.request_id\n", "            AND ib.attribute_id = f.attribute_id\n", "            --AND ib.input_value_created_at > COALESCE(f.prev_ts, TIMESTAMP '1970-01-01 00:00:00')\n", "            AND ib.value_created_at <= f.attempt_ts\n", "            --AND attribute_id NOT IN (9741, 7570, 7565)\n", "    GROUP BY 1, 2, 3, 4, 5\n", "),\n", "--SELECT * FROM inputs_fed_into_ai_attempt\n", "\n", "values_after_ai_attempt AS (\n", "    SELECT\n", "        f.request_id,\n", "        f.attempt_no,\n", "        f.attribute_id,\n", "        MIN_BY(ib.value, ib.value_created_at) AS value,\n", "        MIN(ib.value_created_at) AS value_created_at\n", "    FROM\n", "        all_attempts f\n", "    LEFT JOIN\n", "        post_ai_verification_state pavs\n", "            ON pavs.request_id = f.request_id\n", "            AND pavs.attempt_no = f.attempt_no\n", "    LEFT JOIN\n", "        input_base ib\n", "        ON ib.request_id = f.request_id\n", "        AND ib.attribute_id = f.attribute_id\n", "        AND ib.value_created_at > f.attempt_ts\n", "        AND ib.value_created_at <= COALESCE(pavs.created_at, TIMESTAMP '2030-01-01 00:00:00')\n", "    GROUP BY 1, 2, 3\n", "),\n", "--SELECT * FROM values_after_ai_attempt\n", "\n", "values_before_manual_qc1 AS (\n", "    SELECT\n", "        f.request_id,\n", "        f.attempt_no,\n", "        f.attribute_id,\n", "        MAX_BY(ib.value, ib.value_created_at) AS value,\n", "        MAX(ib.value_created_at) AS value_created_at\n", "    FROM\n", "        all_attempts f\n", "    JOIN\n", "        post_ai_verification_state pavs\n", "            ON pavs.request_id = f.request_id\n", "            AND pavs.attempt_no = f.attempt_no\n", "    JOIN\n", "        input_base ib\n", "        ON ib.request_id = f.request_id\n", "        AND ib.attribute_id = f.attribute_id\n", "        AND ib.value_created_at > f.attempt_ts\n", "        AND ib.value_created_at <= COALESCE(pavs.created_at, TIMESTAMP '2030-01-01 00:00:00')\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "values_after_manual_qc1 AS (\n", "    SELECT\n", "        f.request_id,\n", "        f.attempt_no,\n", "        f.attribute_id,\n", "        MIN_BY(ib.value, ib.value_created_at) AS value,\n", "        MIN(ib.value_created_at) AS value_created_at\n", "    FROM\n", "        all_attempts f\n", "    JOIN\n", "        post_ai_verification_state pavs\n", "            ON pavs.request_id = f.request_id\n", "            AND pavs.attempt_no = f.attempt_no\n", "    JOIN\n", "        input_base ib\n", "        ON ib.request_id = f.request_id\n", "        AND f.attribute_id = ib.attribute_id\n", "        AND ib.value_created_at >= pavs.created_at\n", "    GROUP BY 1, 2, 3\n", "),\n", "\n", "l2_and_request_name_by_attempt AS (\n", "\tSELECT\n", "\t\tf.request_id,\n", "\t\tf.attempt_no,\n", "        \n", "\t\tMAX_BY(\n", "\t\t\tCASE WHEN ib.attribute_id = 7565 THEN ib.value END,\n", "\t\t\tib.value_created_at)                                        AS l2_category_id,\n", "\n", "\n", "\t\tMAX_BY(\n", "\t\t\tCASE WHEN ib.attribute_id = 7563 THEN ib.value END,\n", "\t\t\tib.value_created_at)                                        AS request_name\n", "\n", "\tFROM\n", "\t\tall_attempts f\n", "\tJOIN\n", "\t\tinput_base ib\n", "\t\t\tON ib.request_id        = f.request_id\n", "\t\t\tAND ib.value_created_at <= f.attempt_ts\n", "\t\t\tAND ib.attribute_id     IN (7563, 7565)\n", "\tWHERE\n", "\t\tf.attribute_id IN (7563, 7565)\n", "\tGROUP BY 1, 2\n", ")\n", "\n", "SELECT\n", "\ta.request_id,\n", "    l.request_name,\n", "\tl.l2_category_id,\n", "\tgc.name AS l2_category,\n", "\t\n", "\ta.attempt_no,\n", "\t\n", "\ta.attempt_ts AS ai_verification_attempt_ts,\n", "\t\n", "\trid_ai_result,\n", "\ta.attribute_id,\n", "\n", "\tga.name AS attribute_name,\n", "\t\n", "\tga.attribute_value_type,\n", "\t\n", "\ta.value AS ai_model_input_value,\n", "\t\n", "\tCOALESCE(iq.value, a.value) AS manual_qc1_input_first_value,\n", "\tCOALESCE(oq.value, a.value) AS manual_qc1_input_latest_value,\n", "\tCOALESCE(p.attempt_no, 0) AS manual_qc1_attempt_no,\n", "\t\n", "\tp.created_at AS manual_qc1_ts,\n", "\tCOALESCE(p.post_ai_verification_state, 'qc1_pending') AS manual_qc1_result,\n", "\tCOALESCE(p.image_qc_status, 'image_qc_not_applicable') AS image_qc_status,\n", "    p.image_qc_ts\n", "FROM\n", "\tinputs_fed_into_ai_attempt a\n", "LEFT JOIN\n", "    post_ai_verification_state p\n", "        ON p.request_id = a.request_id\n", "        AND p.attempt_no = a.attempt_no\n", "LEFT JOIN\n", "    values_after_ai_attempt iq\n", "       ON iq.request_id = a.request_id\n", "\t    AND iq.attempt_no = a.attempt_no\n", "\t    AND iq.attribute_id = a.attribute_id\n", "LEFT JOIN\n", "    values_before_manual_qc1 oq\n", "       ON oq.request_id = a.request_id\n", "\t    AND oq.attempt_no = a.attempt_no\n", "\t    AND oq.attribute_id = a.attribute_id\n", "LEFT JOIN\n", "    values_after_manual_qc1 aq\n", "        ON aq.request_id = a.request_id\n", "\t    AND aq.attempt_no = a.attempt_no\n", "\t    AND aq.attribute_id = a.attribute_id\n", "JOIN\n", "    cms.gr_attribute ga\n", "        ON ga.id = a.attribute_id\n", "        AND ga.lake_active_record\n", "LEFT JOIN\n", "    l2_and_request_name_by_attempt l\n", "\t    ON l.request_id = a.request_id\n", "\t    AND l.attempt_no = a.attempt_no\n", "JOIN\n", "    cms.gr_category gc\n", "        ON gc.id = CAST(l.l2_category_id AS INT)\n", "        AND gc.enabled_flag\n", "        AND gc.lake_active_record\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 34, "id": "26c8bf41-7422-4e20-9437-2711a0a0a5f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempt 1 failed: (pyhive.exc.DatabaseError) {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'com.google.common.util.concurrent.MoreExecutors$ScheduledListeningDecorator$NeverSuccessfulListenableFutureTask.run(MoreExecutors.java:633)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}\n", "(Background on this error at: http://sqlalche.me/e/13/4xp6)\n", "Attempt 2 failed: (pyhive.exc.DatabaseError) {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}\n", "(Background on this error at: http://sqlalche.me/e/13/4xp6)\n", "Attempt 3 failed: (pyhive.exc.DatabaseError) {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}\n", "(Background on this error at: http://sqlalche.me/e/13/4xp6)\n"]}, {"ename": "DatabaseError", "evalue": "(pyhive.exc.DatabaseError) {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}\n(Background on this error at: http://sqlalche.me/e/13/4xp6)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mDatabaseError\u001b[0m                             Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/result.py:1284\u001b[0m, in \u001b[0;36mResultProxy.fetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1283\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1284\u001b[0m     l \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprocess_rows(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetchall_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m   1285\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_soft_close()\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/result.py:1230\u001b[0m, in \u001b[0;36mResultProxy._fetchall_impl\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1229\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1230\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcursor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchall\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1231\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:137\u001b[0m, in \u001b[0;36mDBAPICursor.fetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    131\u001b[0m \u001b[38;5;124;03m\"\"\"Fetch all (remaining) rows of a query result, returning them as a sequence of sequences\u001b[39;00m\n\u001b[1;32m    132\u001b[0m \u001b[38;5;124;03m(e.g. a list of tuples).\u001b[39;00m\n\u001b[1;32m    133\u001b[0m \n\u001b[1;32m    134\u001b[0m \u001b[38;5;124;03mAn :py:class:`~pyhive.exc.Error` (or subclass) exception is raised if the previous call to\u001b[39;00m\n\u001b[1;32m    135\u001b[0m \u001b[38;5;124;03m:py:meth:`execute` did not produce any result set or no call was issued yet.\u001b[39;00m\n\u001b[1;32m    136\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 137\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43<PERSON>r\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchone\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:106\u001b[0m, in \u001b[0;36mDBAPICursor.fetchone\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[38;5;66;03m# Sleep until we're done or we have some data to return\u001b[39;00m\n\u001b[0;32m--> 106\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetch_while\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_data\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_state\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m!=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_STATE_FINISHED\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data:\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:46\u001b[0m, in \u001b[0;36mDBAPICursor._fetch_while\u001b[0;34m(self, fn)\u001b[0m\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m fn():\n\u001b[0;32m---> 46\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetch_more\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     47\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m fn():\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/presto.py:308\u001b[0m, in \u001b[0;36mCursor._fetch_more\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    307\u001b[0m \u001b[38;5;124;03m\"\"\"Fetch the next URI and update state\"\"\"\u001b[39;00m\n\u001b[0;32m--> 308\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_process_response\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_requests_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_nextUri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_requests_kwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/trino.py:132\u001b[0m, in \u001b[0;36mCursor._process_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    131\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124<PERSON><PERSON><PERSON>\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m response_json:\n\u001b[0;32m--> 132\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m DatabaseError(response_json[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124<PERSON><PERSON><PERSON>\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[0;31mDatabaseError\u001b[0m: {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mDatabaseError\u001b[0m                             Traceback (most recent call last)", "Input \u001b[0;32mIn [34]\u001b[0m, in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;66;03m# usage\u001b[39;00m\n\u001b[1;32m     20\u001b[0m con   \u001b[38;5;241m=\u001b[39m pb\u001b[38;5;241m.\u001b[39mget_connection(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m[Warehouse] Trino\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 21\u001b[0m main1 \u001b[38;5;241m=\u001b[39m \u001b[43mread_with_retry\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmain1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcon\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m3\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdelay\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m5\u001b[39;49m\u001b[43m)\u001b[49m\n", "Input \u001b[0;32mIn [34]\u001b[0m, in \u001b[0;36mread_with_retry\u001b[0;34m(sql, con, retries, delay)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m attempt \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m1\u001b[39m, retries \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[1;32m     10\u001b[0m \t\u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 11\u001b[0m \t\t\u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_sql_query\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcon\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcon\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     12\u001b[0m \t\u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m     13\u001b[0m \t\t\u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttempt \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattempt\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m failed: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pandas/io/sql.py:436\u001b[0m, in \u001b[0;36mread_sql_query\u001b[0;34m(sql, con, index_col, coerce_float, params, parse_dates, chunksize, dtype)\u001b[0m\n\u001b[1;32m    378\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    379\u001b[0m \u001b[38;5;124;03mRead SQL query into a DataFrame.\u001b[39;00m\n\u001b[1;32m    380\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    433\u001b[0m \u001b[38;5;124;03mparameter will be converted to UTC.\u001b[39;00m\n\u001b[1;32m    434\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    435\u001b[0m pandas_sql \u001b[38;5;241m=\u001b[39m pandasSQL_builder(con)\n\u001b[0;32m--> 436\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpandas_sql\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_query\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    437\u001b[0m \u001b[43m    \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    438\u001b[0m \u001b[43m    \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex_col\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    439\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    440\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcoerce_float\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcoerce_float\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    441\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparse_dates\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_dates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    442\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    443\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    444\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pandas/io/sql.py:1593\u001b[0m, in \u001b[0;36mSQLDatabase.read_query\u001b[0;34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize, dtype)\u001b[0m\n\u001b[1;32m   1583\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_iterator(\n\u001b[1;32m   1584\u001b[0m         result,\n\u001b[1;32m   1585\u001b[0m         chunksize,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1590\u001b[0m         dtype\u001b[38;5;241m=\u001b[39mdtype,\n\u001b[1;32m   1591\u001b[0m     )\n\u001b[1;32m   1592\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1593\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[43mresult\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchall\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1594\u001b[0m     frame \u001b[38;5;241m=\u001b[39m _wrap_result(\n\u001b[1;32m   1595\u001b[0m         data,\n\u001b[1;32m   1596\u001b[0m         columns,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1600\u001b[0m         dtype\u001b[38;5;241m=\u001b[39mdtype,\n\u001b[1;32m   1601\u001b[0m     )\n\u001b[1;32m   1602\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m frame\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/result.py:1288\u001b[0m, in \u001b[0;36mResultProxy.fetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1286\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m l\n\u001b[1;32m   1287\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m-> 1288\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_handle_dbapi_exception\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1289\u001b[0m \u001b[43m        \u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcursor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontext\u001b[49m\n\u001b[1;32m   1290\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/base.py:1510\u001b[0m, in \u001b[0;36mConnection._handle_dbapi_exception\u001b[0;34m(self, e, statement, parameters, cursor, context)\u001b[0m\n\u001b[1;32m   1508\u001b[0m     util\u001b[38;5;241m.\u001b[39mraise_(newraise, with_traceback\u001b[38;5;241m=\u001b[39mexc_info[\u001b[38;5;241m2\u001b[39m], from_\u001b[38;5;241m=\u001b[39me)\n\u001b[1;32m   1509\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m should_wrap:\n\u001b[0;32m-> 1510\u001b[0m     \u001b[43mutil\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1511\u001b[0m \u001b[43m        \u001b[49m\u001b[43msqlalchemy_exception\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwith_traceback\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrom_\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\n\u001b[1;32m   1512\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1513\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1514\u001b[0m     util\u001b[38;5;241m.\u001b[39mraise_(exc_info[\u001b[38;5;241m1\u001b[39m], with_traceback\u001b[38;5;241m=\u001b[39mexc_info[\u001b[38;5;241m2\u001b[39m])\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/util/compat.py:182\u001b[0m, in \u001b[0;36mraise_\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    179\u001b[0m     exception\u001b[38;5;241m.\u001b[39m__cause__ \u001b[38;5;241m=\u001b[39m replace_context\n\u001b[1;32m    181\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 182\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exception\n\u001b[1;32m    183\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    184\u001b[0m     \u001b[38;5;66;03m# credit to\u001b[39;00m\n\u001b[1;32m    185\u001b[0m     \u001b[38;5;66;03m# https://cosmicpercolator.com/2016/01/13/exception-leaks-in-python-2-and-3/\u001b[39;00m\n\u001b[1;32m    186\u001b[0m     \u001b[38;5;66;03m# as the __traceback__ object creates a cycle\u001b[39;00m\n\u001b[1;32m    187\u001b[0m     \u001b[38;5;28;01mdel\u001b[39;00m exception, replace_context, from_, with_traceback\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/result.py:1284\u001b[0m, in \u001b[0;36mResultProxy.fetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1269\u001b[0m \u001b[38;5;124;03m\"\"\"Fetch all rows, just like DB-API ``cursor.fetchall()``.\u001b[39;00m\n\u001b[1;32m   1270\u001b[0m \n\u001b[1;32m   1271\u001b[0m \u001b[38;5;124;03mAfter all rows have been exhausted, the underlying DBAPI\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1280\u001b[0m \n\u001b[1;32m   1281\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1283\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1284\u001b[0m     l \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprocess_rows(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetchall_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m   1285\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_soft_close()\n\u001b[1;32m   1286\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m l\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/sqlalchemy/engine/result.py:1230\u001b[0m, in \u001b[0;36mResultProxy._fetchall_impl\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1228\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_fetchall_impl\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[1;32m   1229\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1230\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcursor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchall\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1231\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAtt<PERSON><PERSON>e<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m   1232\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_non_result([], err)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:137\u001b[0m, in \u001b[0;36mDBAPICursor.fetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    130\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfetchall\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[1;32m    131\u001b[0m     \u001b[38;5;124;03m\"\"\"Fetch all (remaining) rows of a query result, returning them as a sequence of sequences\u001b[39;00m\n\u001b[1;32m    132\u001b[0m \u001b[38;5;124;03m    (e.g. a list of tuples).\u001b[39;00m\n\u001b[1;32m    133\u001b[0m \n\u001b[1;32m    134\u001b[0m \u001b[38;5;124;03m    An :py:class:`~pyhive.exc.Error` (or subclass) exception is raised if the previous call to\u001b[39;00m\n\u001b[1;32m    135\u001b[0m \u001b[38;5;124;03m    :py:meth:`execute` did not produce any result set or no call was issued yet.\u001b[39;00m\n\u001b[1;32m    136\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 137\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43miter\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchone\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:106\u001b[0m, in \u001b[0;36mDBAPICursor.fetchone\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mProgrammingError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo query yet\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    105\u001b[0m \u001b[38;5;66;03m# Sleep until we're done or we have some data to return\u001b[39;00m\n\u001b[0;32m--> 106\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetch_while\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_data\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_state\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m!=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_STATE_FINISHED\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data:\n\u001b[1;32m    109\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/common.py:46\u001b[0m, in \u001b[0;36mDBAPICursor._fetch_while\u001b[0;34m(self, fn)\u001b[0m\n\u001b[1;32m     44\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_fetch_while\u001b[39m(\u001b[38;5;28mself\u001b[39m, fn):\n\u001b[1;32m     45\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m fn():\n\u001b[0;32m---> 46\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetch_more\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     47\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m fn():\n\u001b[1;32m     48\u001b[0m             time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_poll_interval)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/presto.py:308\u001b[0m, in \u001b[0;36mCursor._fetch_more\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    306\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_fetch_more\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[1;32m    307\u001b[0m     \u001b[38;5;124;03m\"\"\"Fetch the next URI and update state\"\"\"\u001b[39;00m\n\u001b[0;32m--> 308\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_process_response\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_requests_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_nextUri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_requests_kwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyhive/trino.py:132\u001b[0m, in \u001b[0;36mCursor._process_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    130\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_STATE_FINISHED\n\u001b[1;32m    131\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124<PERSON><PERSON>r\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m response_json:\n\u001b[0;32m--> 132\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m DatabaseError(response_json[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124merror\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[0;31mDatabaseError\u001b[0m: (pyhive.exc.DatabaseError) {'message': 'Query exceeded the maximum execution time limit of 20.00m', 'errorCode': 131075, 'errorName': 'EXCEEDED_TIME_LIMIT', 'errorType': 'INSUFFICIENT_RESOURCES', 'failureInfo': {'type': 'io.trino.spi.TrinoException', 'message': 'Query exceeded the maximum execution time limit of 20.00m', 'suppressed': [], 'stack': ['io.trino.execution.QueryTracker.enforceTimeLimits(QueryTracker.java:194)', 'io.trino.execution.QueryTracker.lambda$start$0(QueryTracker.java:90)', 'java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)', 'java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358)', 'java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)', 'java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)', 'java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)', 'java.base/java.lang.Thread.run(Thread.java:1570)'], 'errorInfo': {'code': 131075, 'name': 'EXCEEDED_TIME_LIMIT', 'type': 'INSUFFICIENT_RESOURCES'}}}\n(Background on this error at: http://sqlalche.me/e/13/4xp6)"]}], "source": ["import time\n", "import pandas as pd\n", "\n", "def read_with_retry(sql, con, retries=3, delay=5):\n", "\t\"\"\"\n", "\tAttempts to run pd.read_sql_query up to `retries` times,\n", "\tsleeping `delay` seconds between failures.\n", "\t\"\"\"\n", "\tfor attempt in range(1, retries + 1):\n", "\t\ttry:\n", "\t\t\treturn pd.read_sql_query(sql=sql, con=con)\n", "\t\texcept Exception as e:\n", "\t\t\tprint(f\"Attempt {attempt} failed: {e}\")\n", "\t\t\tif attempt == retries:\n", "\t\t\t\t# All retries exhausted, re-raise\n", "\t\t\t\traise\n", "\t\t\ttime.sleep(delay)\n", "\n", "# usage\n", "con   = pb.get_connection(\"[Warehouse] Trino\")\n", "main1 = read_with_retry(main1, con, retries=3, delay=5)"]}, {"cell_type": "code", "execution_count": null, "id": "4a6d56dc-05ce-4356-8053-c55365d07985", "metadata": {}, "outputs": [], "source": ["main1.info()"]}, {"cell_type": "code", "execution_count": null, "id": "ee831286-69f5-4b34-a9d5-d549fc209ed2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "029e2457-dd07-45d5-8ab1-e6b78a8ddc1a", "metadata": {}, "outputs": [], "source": ["merged_df = (\n", "    main1.merge(att_ai_df, on=[\"request_id\", \"attribute_id\", \"attempt_no\"], how=\"left\")\n", "    .merge(att_ai_reason_df, on=[\"request_id\", \"attribute_id\", \"attempt_no\"], how=\"left\")\n", "    .merge(att_ai_rejection_df, on=[\"request_id\", \"attribute_id\", \"attempt_no\"], how=\"left\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0c80711e-e7a8-4be5-a05c-1e72acbcbf5f", "metadata": {}, "outputs": [], "source": ["merged_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "426d1fbf-9cc2-41ee-ac6a-21e4e5434b27", "metadata": {}, "outputs": [], "source": ["merged_df[(merged_df[\"request_id\"] == 305206) & (merged_df[\"attribute_id\"] == 9235)]"]}, {"cell_type": "code", "execution_count": 42, "id": "3a087f3c-0983-4578-a378-db15ebe3a5cd", "metadata": {}, "outputs": [], "source": ["merged_df = merged_df.merge(\n", "    att_ai_pid_data_df, on=[\"request_id\", \"attribute_id\", \"attempt_no\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": 43, "id": "327f28ca-ccdc-4003-9c38-2c2b6623f975", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 325914 entries, 0 to 325913\n", "Data columns (total 32 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     325914 non-null  int64  \n", " 1   request_name                   325914 non-null  object \n", " 2   l2_category_id                 325914 non-null  object \n", " 3   l2_category                    325914 non-null  object \n", " 4   attempt_no                     325914 non-null  int64  \n", " 5   ai_verification_attempt_ts     325914 non-null  object \n", " 6   rid_ai_result                  325914 non-null  object \n", " 7   attribute_id                   325914 non-null  int64  \n", " 8   attribute_name                 325914 non-null  object \n", " 9   attribute_value_type           325528 non-null  object \n", " 10  ai_model_input_value           221943 non-null  object \n", " 11  manual_qc1_input_first_value   299406 non-null  object \n", " 12  manual_qc1_input_latest_value  299234 non-null  object \n", " 13  manual_qc1_attempt_no          325914 non-null  int64  \n", " 14  manual_qc1_ts                  318516 non-null  object \n", " 15  manual_qc1_result              325914 non-null  object \n", " 16  image_qc_status                325914 non-null  object \n", " 17  image_qc_ts                    191545 non-null  object \n", " 18  ai_result                      53321 non-null   object \n", " 19  created_at_x                   53321 non-null   object \n", " 20  ai_result_reason               53321 non-null   object \n", " 21  created_at_y                   53321 non-null   object \n", " 22  attempt_ts                     1408 non-null    object \n", " 23  rejection_reason               1408 non-null    object \n", " 24  rejection_reason_created_at    1408 non-null    object \n", " 25  product_id                     298431 non-null  float64\n", " 26  pid_created_date               298431 non-null  object \n", " 27  manual_qc2_attempt_no          324985 non-null  float64\n", " 28  manual_qc2_input               59290 non-null   object \n", " 29  manual_qc2_result              324985 non-null  object \n", " 30  manual_qc2_ts                  180556 non-null  object \n", " 31  manual_qc2_output              60502 non-null   object \n", "dtypes: float64(2), int64(4), object(26)\n", "memory usage: 82.1+ MB\n"]}], "source": ["merged_df.info()"]}, {"cell_type": "code", "execution_count": 44, "id": "69f27f57-f7ba-4a87-8741-9c6828c3126e", "metadata": {}, "outputs": [{"data": {"text/plain": ["qc1_rejected    19394\n", "qc1_pending      6994\n", "qc1_approved     1095\n", "Name: manual_qc1_result, dtype: int64"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.loc[merged_df[\"product_id\"].isna(), \"manual_qc1_result\"].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 45, "id": "8d2eff07-57ae-4d76-88f7-e2f7d9549b42", "metadata": {}, "outputs": [], "source": ["merged_df[\"qc2_before_image_qc\"] = merged_df[\"manual_qc2_ts\"] < merged_df[\"image_qc_ts\"]"]}, {"cell_type": "code", "execution_count": 46, "id": "6c29d99e-0e4d-45ba-a692-091a31c0b076", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>request_name</th>\n", "      <th>l2_category_id</th>\n", "      <th>l2_category</th>\n", "      <th>attempt_no</th>\n", "      <th>ai_verification_attempt_ts</th>\n", "      <th>rid_ai_result</th>\n", "      <th>attribute_id</th>\n", "      <th>attribute_name</th>\n", "      <th>attribute_value_type</th>\n", "      <th>...</th>\n", "      <th>rejection_reason</th>\n", "      <th>rejection_reason_created_at</th>\n", "      <th>product_id</th>\n", "      <th>pid_created_date</th>\n", "      <th>manual_qc2_attempt_no</th>\n", "      <th>manual_qc2_input</th>\n", "      <th>manual_qc2_result</th>\n", "      <th>manual_qc2_ts</th>\n", "      <th>manual_qc2_output</th>\n", "      <th>qc2_before_image_qc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>304974</td>\n", "      <td>GOAT Life Almond <PERSON> Overnight Oats ...</td>\n", "      <td>32</td>\n", "      <td>Instant Oats</td>\n", "      <td>2</td>\n", "      <td>2025-07-22 15:19:56.225</td>\n", "      <td>False</td>\n", "      <td>3088</td>\n", "      <td>Brand Warranty - SLP</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>691765.0</td>\n", "      <td>2025-07-23 15:31:43.204</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_rejected</td>\n", "      <td>2025-07-25 18:17:47.936</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>306126</td>\n", "      <td><PERSON><PERSON><PERSON>ad Atta High Protein, 1kg</td>\n", "      <td>4130</td>\n", "      <td>Atta Assortment</td>\n", "      <td>1</td>\n", "      <td>2025-07-23 12:55:29.805</td>\n", "      <td>False</td>\n", "      <td>644</td>\n", "      <td>Customer Care Details</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>691794.0</td>\n", "      <td>2025-07-23 15:58:46.407</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 14:36:04.683</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>305906</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>211</td>\n", "      <td>Festive Assortment</td>\n", "      <td>1</td>\n", "      <td>2025-07-21 10:51:41.944</td>\n", "      <td>False</td>\n", "      <td>7565</td>\n", "      <td>category</td>\n", "      <td>number</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690884.0</td>\n", "      <td>2025-07-21 17:26:18.113</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-27 18:42:02.294</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>305918</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> studded <PERSON><PERSON><PERSON></td>\n", "      <td>211</td>\n", "      <td>Festive Assortment</td>\n", "      <td>1</td>\n", "      <td>2025-07-21 10:57:53.497</td>\n", "      <td>False</td>\n", "      <td>7565</td>\n", "      <td>category</td>\n", "      <td>number</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690830.0</td>\n", "      <td>2025-07-21 16:05:44.792</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-27 18:42:11.060</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>306703</td>\n", "      <td>Ana<PERSON>za Fancy Rakhi SSR17</td>\n", "      <td>834</td>\n", "      <td>Rakhi Specials</td>\n", "      <td>1</td>\n", "      <td>2025-07-29 13:14:02.424</td>\n", "      <td>False</td>\n", "      <td>2371</td>\n", "      <td>Custom_Light_Theme</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>693528.0</td>\n", "      <td>2025-07-29 13:54:11.644</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-31 14:42:53.720</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325766</th>\n", "      <td>306708</td>\n", "      <td>Ana<PERSON>za Fancy Rakhi SSR22</td>\n", "      <td>834</td>\n", "      <td>Rakhi Specials</td>\n", "      <td>1</td>\n", "      <td>2025-07-29 13:05:27.213</td>\n", "      <td>False</td>\n", "      <td>2291</td>\n", "      <td>Gift Pack</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>693553.0</td>\n", "      <td>2025-07-29 14:16:02.095</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-31 13:22:26.669</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325767</th>\n", "      <td>306690</td>\n", "      <td>Anayza Fancy Rakhi SSR4</td>\n", "      <td>834</td>\n", "      <td>Rakhi Specials</td>\n", "      <td>1</td>\n", "      <td>2025-07-29 12:52:20.695</td>\n", "      <td>False</td>\n", "      <td>1004</td>\n", "      <td>Apple MPN</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>693539.0</td>\n", "      <td>2025-07-29 14:01:03.726</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-31 13:01:34.130</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325782</th>\n", "      <td>306711</td>\n", "      <td><PERSON><PERSON>za Fancy Ra<PERSON>i <PERSON>25</td>\n", "      <td>834</td>\n", "      <td>Rakhi Specials</td>\n", "      <td>1</td>\n", "      <td>2025-07-29 12:59:15.159</td>\n", "      <td>False</td>\n", "      <td>2541</td>\n", "      <td>Declaration</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>693547.0</td>\n", "      <td>2025-07-29 14:08:09.565</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-31 15:56:49.594</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325824</th>\n", "      <td>305910</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>211</td>\n", "      <td>Festive Assortment</td>\n", "      <td>1</td>\n", "      <td>2025-07-21 10:53:42.202</td>\n", "      <td>False</td>\n", "      <td>3552</td>\n", "      <td>SGST</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690922.0</td>\n", "      <td>2025-07-21 17:55:01.245</td>\n", "      <td>1.0</td>\n", "      <td>1.5</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-27 19:26:58.774</td>\n", "      <td>1.5</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325908</th>\n", "      <td>306713</td>\n", "      <td><PERSON><PERSON>za Fancy Rakhi <PERSON>27</td>\n", "      <td>834</td>\n", "      <td>Rakhi Specials</td>\n", "      <td>1</td>\n", "      <td>2025-07-29 12:55:51.721</td>\n", "      <td>False</td>\n", "      <td>31</td>\n", "      <td>Material</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>693532.0</td>\n", "      <td>2025-07-29 13:56:42.028</td>\n", "      <td>1.0</td>\n", "      <td>Plastic &amp; Thread</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-31 12:49:33.325</td>\n", "      <td>Thread</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9829 rows × 33 columns</p>\n", "</div>"], "text/plain": ["        request_id                                       request_name  \\\n", "45          304974  GOAT Life Almond Kulfi Protein Overnight Oats ...   \n", "92          306126                  Aashirvaad Atta High Protein, 1kg   \n", "96          305906                              <PERSON><PERSON><PERSON><PERSON><PERSON>   \n", "100         305918                    <PERSON><PERSON><PERSON>vi Om Stone studded Rakhi   \n", "162         306703                           Anayza Fancy Rakhi SSR17   \n", "...            ...                                                ...   \n", "325766      306708                           Anayza Fancy Rakhi SSR22   \n", "325767      306690                            Anayza Fancy Rakhi SSR4   \n", "325782      306711                           Anayza Fancy Rakhi SSR25   \n", "325824      305910                         <PERSON><PERSON><PERSON><PERSON>i   \n", "325908      306713                           Anayza Fancy Rakhi SSR27   \n", "\n", "       l2_category_id         l2_category  attempt_no  \\\n", "45                 32        Instant Oats           2   \n", "92               4130     Atta Assortment           1   \n", "96                211  Festive Assortment           1   \n", "100               211  Festive Assortment           1   \n", "162               834      Rakhi Specials           1   \n", "...               ...                 ...         ...   \n", "325766            834      Rakhi Specials           1   \n", "325767            834      Rakhi Specials           1   \n", "325782            834      Rakhi Specials           1   \n", "325824            211  Festive Assortment           1   \n", "325908            834      Rakhi Specials           1   \n", "\n", "       ai_verification_attempt_ts rid_ai_result  attribute_id  \\\n", "45        2025-07-22 15:19:56.225         False          3088   \n", "92        2025-07-23 12:55:29.805         False           644   \n", "96        2025-07-21 10:51:41.944         False          7565   \n", "100       2025-07-21 10:57:53.497         False          7565   \n", "162       2025-07-29 13:14:02.424         False          2371   \n", "...                           ...           ...           ...   \n", "325766    2025-07-29 13:05:27.213         False          2291   \n", "325767    2025-07-29 12:52:20.695         False          1004   \n", "325782    2025-07-29 12:59:15.159         False          2541   \n", "325824    2025-07-21 10:53:42.202         False          3552   \n", "325908    2025-07-29 12:55:51.721         False            31   \n", "\n", "               attribute_name attribute_value_type  ... rejection_reason  \\\n", "45       Brand Warranty - SLP                 enum  ...              NaN   \n", "92      Customer Care Details                 text  ...              NaN   \n", "96                   category               number  ...              NaN   \n", "100                  category               number  ...              NaN   \n", "162        Custom_Light_Theme                 text  ...              NaN   \n", "...                       ...                  ...  ...              ...   \n", "325766              Gift Pack                 enum  ...              NaN   \n", "325767              Apple MPN                 text  ...              NaN   \n", "325782            Declaration                 text  ...              NaN   \n", "325824                   SGST                 enum  ...              NaN   \n", "325908               Material                 text  ...              NaN   \n", "\n", "       rejection_reason_created_at product_id         pid_created_date  \\\n", "45                             NaN   691765.0  2025-07-23 15:31:43.204   \n", "92                             NaN   691794.0  2025-07-23 15:58:46.407   \n", "96                             NaN   690884.0  2025-07-21 17:26:18.113   \n", "100                            NaN   690830.0  2025-07-21 16:05:44.792   \n", "162                            NaN   693528.0  2025-07-29 13:54:11.644   \n", "...                            ...        ...                      ...   \n", "325766                         NaN   693553.0  2025-07-29 14:16:02.095   \n", "325767                         NaN   693539.0  2025-07-29 14:01:03.726   \n", "325782                         NaN   693547.0  2025-07-29 14:08:09.565   \n", "325824                         NaN   690922.0  2025-07-21 17:55:01.245   \n", "325908                         NaN   693532.0  2025-07-29 13:56:42.028   \n", "\n", "       manual_qc2_attempt_no  manual_qc2_input manual_qc2_result  \\\n", "45                       1.0              None      qc2_rejected   \n", "92                       1.0              None      qc2_approved   \n", "96                       1.0              None      qc2_approved   \n", "100                      1.0              None      qc2_approved   \n", "162                      1.0              None      qc2_approved   \n", "...                      ...               ...               ...   \n", "325766                   1.0              None      qc2_approved   \n", "325767                   1.0              None      qc2_approved   \n", "325782                   1.0              None      qc2_approved   \n", "325824                   1.0               1.5      qc2_approved   \n", "325908                   1.0  Plastic & Thread      qc2_approved   \n", "\n", "                  manual_qc2_ts manual_qc2_output qc2_before_image_qc  \n", "45      2025-07-25 18:17:47.936              None                True  \n", "92      2025-07-25 14:36:04.683              None                True  \n", "96      2025-07-27 18:42:02.294              None                True  \n", "100     2025-07-27 18:42:11.060              None                True  \n", "162     2025-07-31 14:42:53.720              None                True  \n", "...                         ...               ...                 ...  \n", "325766  2025-07-31 13:22:26.669              None                True  \n", "325767  2025-07-31 13:01:34.130              None                True  \n", "325782  2025-07-31 15:56:49.594              None                True  \n", "325824  2025-07-27 19:26:58.774               1.5                True  \n", "325908  2025-07-31 12:49:33.325            Thr<PERSON>                <PERSON>  \n", "\n", "[9829 rows x 33 columns]"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df[merged_df[\"qc2_before_image_qc\"] == True]"]}, {"cell_type": "code", "execution_count": 47, "id": "4caea070-f04f-442a-94a8-10bfd826ee12", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>request_name</th>\n", "      <th>l2_category_id</th>\n", "      <th>l2_category</th>\n", "      <th>attempt_no</th>\n", "      <th>ai_verification_attempt_ts</th>\n", "      <th>rid_ai_result</th>\n", "      <th>attribute_id</th>\n", "      <th>attribute_name</th>\n", "      <th>attribute_value_type</th>\n", "      <th>...</th>\n", "      <th>rejection_reason</th>\n", "      <th>rejection_reason_created_at</th>\n", "      <th>product_id</th>\n", "      <th>pid_created_date</th>\n", "      <th>manual_qc2_attempt_no</th>\n", "      <th>manual_qc2_input</th>\n", "      <th>manual_qc2_result</th>\n", "      <th>manual_qc2_ts</th>\n", "      <th>manual_qc2_output</th>\n", "      <th>qc2_before_image_qc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11597</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>6222</td>\n", "      <td>Ayush License</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14047</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>3802</td>\n", "      <td>Outer Case</td>\n", "      <td>number</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19248</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>7563</td>\n", "      <td>product_name</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24305</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>578</td>\n", "      <td>Height Without Packaging (in cm)</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27991</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>1741</td>\n", "      <td>Reference Links</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>300259</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>8124</td>\n", "      <td>upc_in_retail</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304960</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>7564</td>\n", "      <td>upc</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308219</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>9234</td>\n", "      <td>Landing Price</td>\n", "      <td>number</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>310663</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>2871</td>\n", "      <td>UnitValue</td>\n", "      <td>number</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>317432</th>\n", "      <td>305206</td>\n", "      <td>Fabindia Pink Cotton Woven Bathmat</td>\n", "      <td>360</td>\n", "      <td>Floor Mats &amp; Door Mats</td>\n", "      <td>1</td>\n", "      <td>2025-07-16 13:31:18.317</td>\n", "      <td>False</td>\n", "      <td>8128</td>\n", "      <td>request_updated</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>689174.0</td>\n", "      <td>2025-07-17 10:57:54.664</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>qc2_approved</td>\n", "      <td>2025-07-25 11:09:51.502</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>70 rows × 33 columns</p>\n", "</div>"], "text/plain": ["        request_id                        request_name l2_category_id  \\\n", "11597       305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "14047       305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "19248       305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "24305       305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "27991       305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "...            ...                                 ...            ...   \n", "300259      305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "304960      305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "308219      305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "310663      305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "317432      305206  Fabindia Pink Cotton Woven Bathmat            360   \n", "\n", "                   l2_category  attempt_no ai_verification_attempt_ts  \\\n", "11597   Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "14047   Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "19248   Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "24305   Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "27991   Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "...                        ...         ...                        ...   \n", "300259  Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "304960  Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "308219  Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "310663  Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "317432  Floor Mats & Door Mats           1    2025-07-16 13:31:18.317   \n", "\n", "       rid_ai_result  attribute_id                    attribute_name  \\\n", "11597          False          6222                     Ayush License   \n", "14047          False          3802                        Outer Case   \n", "19248          False          7563                      product_name   \n", "24305          False           578  Height Without Packaging (in cm)   \n", "27991          False          1741                   Reference Links   \n", "...              ...           ...                               ...   \n", "300259         False          8124                     upc_in_retail   \n", "304960         False          7564                               upc   \n", "308219         False          9234                     Landing Price   \n", "310663         False          2871                         UnitValue   \n", "317432         False          8128                   request_updated   \n", "\n", "       attribute_value_type  ... rejection_reason rejection_reason_created_at  \\\n", "11597                  text  ...              NaN                         NaN   \n", "14047                number  ...              NaN                         NaN   \n", "19248                  text  ...              NaN                         NaN   \n", "24305                  text  ...              NaN                         NaN   \n", "27991                  text  ...              NaN                         NaN   \n", "...                     ...  ...              ...                         ...   \n", "300259                 text  ...              NaN                         NaN   \n", "304960                 text  ...              NaN                         NaN   \n", "308219               number  ...              NaN                         NaN   \n", "310663               number  ...              NaN                         NaN   \n", "317432                 text  ...              NaN                         NaN   \n", "\n", "       product_id         pid_created_date manual_qc2_attempt_no  \\\n", "11597    689174.0  2025-07-17 10:57:54.664                   1.0   \n", "14047    689174.0  2025-07-17 10:57:54.664                   1.0   \n", "19248    689174.0  2025-07-17 10:57:54.664                   1.0   \n", "24305    689174.0  2025-07-17 10:57:54.664                   1.0   \n", "27991    689174.0  2025-07-17 10:57:54.664                   1.0   \n", "...           ...                      ...                   ...   \n", "300259   689174.0  2025-07-17 10:57:54.664                   1.0   \n", "304960   689174.0  2025-07-17 10:57:54.664                   1.0   \n", "308219   689174.0  2025-07-17 10:57:54.664                   1.0   \n", "310663   689174.0  2025-07-17 10:57:54.664                   1.0   \n", "317432   689174.0  2025-07-17 10:57:54.664                   1.0   \n", "\n", "       manual_qc2_input manual_qc2_result            manual_qc2_ts  \\\n", "11597              None      qc2_approved  2025-07-25 11:09:51.502   \n", "14047                 1      qc2_approved  2025-07-25 11:09:51.502   \n", "19248              None      qc2_approved  2025-07-25 11:09:51.502   \n", "24305              None      qc2_approved  2025-07-25 11:09:51.502   \n", "27991              None      qc2_approved  2025-07-25 11:09:51.502   \n", "...                 ...               ...                      ...   \n", "300259             None      qc2_approved  2025-07-25 11:09:51.502   \n", "304960             None      qc2_approved  2025-07-25 11:09:51.502   \n", "308219             None      qc2_approved  2025-07-25 11:09:51.502   \n", "310663              1.0      qc2_approved  2025-07-25 11:09:51.502   \n", "317432             None      qc2_approved  2025-07-25 11:09:51.502   \n", "\n", "       manual_qc2_output qc2_before_image_qc  \n", "11597               None               False  \n", "14047                  1               False  \n", "19248               None               False  \n", "24305               None               False  \n", "27991               None               False  \n", "...                  ...                 ...  \n", "300259              None               False  \n", "304960              None               False  \n", "308219              None               False  \n", "310663               1.0               False  \n", "317432              None               False  \n", "\n", "[70 rows x 33 columns]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df[merged_df[\"request_id\"] == 305206]"]}, {"cell_type": "code", "execution_count": 48, "id": "f97cb9e9-750c-411a-8717-d005ad45d382", "metadata": {}, "outputs": [], "source": ["criticality_query = \"\"\"\n", "WITH CategoryBase AS (\n", "    SELECT\n", "        gcgm.category_id AS l2_category_id,\n", "        ggam.attribute_id,\n", "        ga.name AS attribute_name,\n", "        gcgam.is_mandatory,\n", "        ga.is_mandatory AS global_m_flag,\n", "        ga.filled_by,\n", "        ga.attribute_type\n", "    FROM\n", "        cms.gr_category_group_mapping gcgm\n", "    JOIN\n", "        cms.gr_group_attribute_mapping ggam\n", "            ON ggam.group_id = gcgm.group_id\n", "            AND ggam.lake_active_record\n", "            AND ggam.enabled_flag\n", "    JOIN\n", "        cms.gr_category_group_attribute_mapping gcgam\n", "            ON gcgam.category_id = gcgm.category_id\n", "            AND ggam.attribute_id = gcgam.attribute_id\n", "            AND gcgm.group_id = gcgam.group_id\n", "            AND gcgam.lake_active_record\n", "            AND gcgam.enabled_flag\n", "    JOIN\n", "        cms.gr_attribute ga\n", "            ON ga.id = ggam.attribute_id\n", "            AND ga.lake_active_record\n", "            AND filled_by != 'internal'\n", "            AND attribute_type = 'shown_to_customer'\n", "    JOIN\n", "        cms.gr_group gg\n", "            ON gg.id = gcgm.group_id\n", "            AND gg.lake_active_record\n", "            AND gg.is_active\n", "    WHERE\n", "--        gcgm.category_id = 50\n", "        gcgm.lake_active_record\n", "        AND gcgm.enabled_flag\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7\n", "),\n", "product_category_mapping AS (\n", "    SELECT\n", "        gpcm.product_id,\n", "        gpcm.category_id AS l2_category_id\n", "    FROM\n", "        cms.gr_product_category_mapping gpcm\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = gpcm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    WHERE\n", "        gpcm.lake_active_record\n", "        AND gpcm.is_primary\n", "    GROUP BY 1, 2\n", "),\n", "product_att_mapping AS (\n", "    SELECT\n", "        gpam.product_id,\n", "        attribute_id,\n", "        value,\n", "        CASE WHEN LOWER(value) NOT IN ('na', 'n/a','', ' ') THEN 1 ELSE 0 END AS flag\n", "    FROM\n", "        cms.gr_product_attribute_mapping gpam\n", "    WHERE\n", "        gpam.lake_active_record\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "final AS (\n", "    SELECT\n", "        cb.l2_category_id,\n", "        cb.attribute_id,\n", "        pcm.product_id,\n", "        --pam.value,\n", "        COALESCE(pam.flag, -1) AS flag--,\n", "        --is_mandatory,\n", "        --global_m_flag--,\n", "        --CASE WHEN (is_mandatory OR global_m_flag) THEN TRUE ELSE FALSE END AS mandatory_flag\n", "    FROM\n", "        CategoryBase cb\n", "    JOIN\n", "        product_category_mapping pcm\n", "            ON pcm.l2_category_id = cb.l2_category_id\n", "    LEFT JOIN\n", "        product_att_mapping pam\n", "            ON pam.product_id = pcm.product_id\n", "            AND pam.attribute_id = cb.attribute_id\n", "    /*\n", "    WHERE\n", "        cb.l2_category_id = 2518\n", "        AND cb.attribute_id = 77\n", "    /*/\n", "    GROUP BY 1, 2, 3, 4\n", "),\n", "\n", "fill_rate_calc AS (\n", "    SELECT\n", "        f.l2_category_id,\n", "        f.attribute_id,\n", "        MAX(cb.is_mandatory OR cb.global_m_flag) AS mandatory_flag,\n", "        COUNT(DISTINCT product_id) AS total_pids,\n", "        COUNT(DISTINCT CASE WHEN flag = 1 THEN product_id ELSE NULL END) AS filled_pids,\n", "        COUNT(DISTINCT CASE WHEN flag = 0 THEN product_id ELSE NULL END) AS junk_filled_pids,\n", "        COUNT(DISTINCT CASE WHEN flag = -1 THEN product_id ELSE NULL END) AS not_filled_pids\n", "    FROM\n", "        final f\n", "    JOIN\n", "        CategoryBase cb\n", "            ON cb.l2_category_id = f.l2_category_id\n", "            AND cb.attribute_id = f.attribute_id\n", "    /*\n", "    WHERE\n", "        l2_category_id = 2518\n", "        AND attribute_id = 77\n", "    /*/\n", "    GROUP BY 1, 2\n", ")\n", "--SELECT * FROM fill_rate_calc WHERE l2_category_id = 211 AND attribute_id = 648\n", "SELECT\n", "    CAST(frc.l2_category_id AS INT) AS l2_category_id,\n", "    CAST(frc.attribute_id AS INT) AS attribute_id,\n", "    --filled_pids * 100.00 / total_pids AS fill_rate_pct,\n", "    CASE\n", "        WHEN attribute_id IN (243,578,580,643,644,2804,3447,6222,4544,4543) THEN 0\n", "        WHEN attribute_id IN (80,242,632,634,641,75,598,642,134,77,109,3,1933,3481,5277,5602,553) THEN 1\n", "        WHEN mandatory_flag AND (total_pids > 10 OR (filled_pids * 1.0000 / total_pids >= 0.5)) THEN 1\n", "        WHEN NOT(mandatory_flag) AND ((filled_pids * 1.0000 / total_pids >= 0.8) AND total_pids >= 10) THEN 1\n", "        ELSE 0\n", "    END AS criticality_flag\n", "FROM\n", "    fill_rate_calc frc\n", "GROUP BY 1, 2, 3\n", "\n", "UNION---------------\n", "\n", "SELECT\n", "    frc.l2_category_id,\n", "    7564 AS attribute_id,\n", "    1 AS criticality_flag\n", "FROM\n", "    fill_rate_calc frc\n", "GROUP BY 1, 2, 3\n", "\n", "UNION---------------\n", "\n", "SELECT\n", "    frc.l2_category_id,\n", "    7565 AS attribute_id,\n", "    0 AS criticality_flag\n", "FROM\n", "    fill_rate_calc frc\n", "GROUP BY 1, 2, 3\n", "\n", "UNION---------------\n", "\n", "SELECT\n", "    frc.l2_category_id,\n", "    7562 AS attribute_id,\n", "    1 AS criticality_flag\n", "FROM\n", "    fill_rate_calc frc\n", "GROUP BY 1, 2, 3\n", "\n", "\n", "ORDER BY 1, 2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 49, "id": "f2021732-5231-4ae1-b2ce-10a9a0b05f45", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "criticality_df = pd.read_sql_query(sql=criticality_query, con=con)"]}, {"cell_type": "code", "execution_count": 50, "id": "6d5872dc-a792-475f-b728-af92b08a702d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 35615 entries, 0 to 35614\n", "Data columns (total 3 columns):\n", " #   Column            Non-Null Count  Dtype\n", "---  ------            --------------  -----\n", " 0   l2_category_id    35615 non-null  int64\n", " 1   attribute_id      35615 non-null  int64\n", " 2   criticality_flag  35615 non-null  int64\n", "dtypes: int64(3)\n", "memory usage: 834.9 KB\n"]}], "source": ["criticality_df.info()"]}, {"cell_type": "code", "execution_count": 51, "id": "a385ed72-81c2-46c5-84f9-684e5c929b28", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>l2_category_id</th>\n", "      <th>attribute_id</th>\n", "      <th>criticality_flag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8</td>\n", "      <td>75</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>77</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8</td>\n", "      <td>80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>126</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35610</th>\n", "      <td>8065</td>\n", "      <td>3447</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35611</th>\n", "      <td>8065</td>\n", "      <td>6222</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35612</th>\n", "      <td>8065</td>\n", "      <td>7562</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35613</th>\n", "      <td>8065</td>\n", "      <td>7564</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35614</th>\n", "      <td>8065</td>\n", "      <td>7565</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>35615 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       l2_category_id  attribute_id  criticality_flag\n", "0                   8            75                 1\n", "1                   8            76                 0\n", "2                   8            77                 1\n", "3                   8            80                 1\n", "4                   8           126                 0\n", "...               ...           ...               ...\n", "35610            8065          3447                 0\n", "35611            8065          6222                 0\n", "35612            8065          7562                 1\n", "35613            8065          7564                 1\n", "35614            8065          7565                 0\n", "\n", "[35615 rows x 3 columns]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["criticality_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": 52, "id": "5628f2f2-5c33-432f-b6d3-761b3e04e5a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 35615 entries, 0 to 35614\n", "Data columns (total 3 columns):\n", " #   Column            Non-Null Count  Dtype\n", "---  ------            --------------  -----\n", " 0   l2_category_id    35615 non-null  int64\n", " 1   attribute_id      35615 non-null  int64\n", " 2   criticality_flag  35615 non-null  int64\n", "dtypes: int64(3)\n", "memory usage: 834.9 KB\n"]}], "source": ["criticality_df.info()"]}, {"cell_type": "code", "execution_count": 53, "id": "ac3201c2-9945-42aa-9865-35ced31e9f46", "metadata": {}, "outputs": [], "source": ["merged_df[[\"l2_category_id\", \"attribute_id\"]] = merged_df[[\"l2_category_id\", \"attribute_id\"]].apply(\n", "    pd.to_numeric, errors=\"raise\", downcast=\"integer\"\n", ")\n", "\n", "criticality_df[[\"l2_category_id\", \"attribute_id\"]] = criticality_df[\n", "    [\"l2_category_id\", \"attribute_id\"]\n", "].apply(pd.to_numeric, errors=\"raise\", downcast=\"integer\")"]}, {"cell_type": "code", "execution_count": 54, "id": "fecc21d8-ac7c-48f4-85d4-dd584df5c64f", "metadata": {}, "outputs": [], "source": ["merged_df[\"ai_result\"] = merged_df[\"ai_result\"].fillna(\"not verified\")"]}, {"cell_type": "code", "execution_count": 55, "id": "df659c91-5e88-4e59-85fd-ee0e18b3ce43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 325914 entries, 0 to 325913\n", "Data columns (total 33 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     325914 non-null  int64  \n", " 1   request_name                   325914 non-null  object \n", " 2   l2_category_id                 325914 non-null  int16  \n", " 3   l2_category                    325914 non-null  object \n", " 4   attempt_no                     325914 non-null  int64  \n", " 5   ai_verification_attempt_ts     325914 non-null  object \n", " 6   rid_ai_result                  325914 non-null  object \n", " 7   attribute_id                   325914 non-null  int16  \n", " 8   attribute_name                 325914 non-null  object \n", " 9   attribute_value_type           325528 non-null  object \n", " 10  ai_model_input_value           221943 non-null  object \n", " 11  manual_qc1_input_first_value   299406 non-null  object \n", " 12  manual_qc1_input_latest_value  299234 non-null  object \n", " 13  manual_qc1_attempt_no          325914 non-null  int64  \n", " 14  manual_qc1_ts                  318516 non-null  object \n", " 15  manual_qc1_result              325914 non-null  object \n", " 16  image_qc_status                325914 non-null  object \n", " 17  image_qc_ts                    191545 non-null  object \n", " 18  ai_result                      325914 non-null  object \n", " 19  created_at_x                   53321 non-null   object \n", " 20  ai_result_reason               53321 non-null   object \n", " 21  created_at_y                   53321 non-null   object \n", " 22  attempt_ts                     1408 non-null    object \n", " 23  rejection_reason               1408 non-null    object \n", " 24  rejection_reason_created_at    1408 non-null    object \n", " 25  product_id                     298431 non-null  float64\n", " 26  pid_created_date               298431 non-null  object \n", " 27  manual_qc2_attempt_no          324985 non-null  float64\n", " 28  manual_qc2_input               59290 non-null   object \n", " 29  manual_qc2_result              324985 non-null  object \n", " 30  manual_qc2_ts                  180556 non-null  object \n", " 31  manual_qc2_output              60502 non-null   object \n", " 32  qc2_before_image_qc            325914 non-null  bool   \n", "dtypes: bool(1), float64(2), int16(2), int64(3), object(25)\n", "memory usage: 78.6+ MB\n"]}], "source": ["merged_df.info()"]}, {"cell_type": "code", "execution_count": 56, "id": "1fb89044-2400-48e9-9e4c-30eb430fa0cd", "metadata": {}, "outputs": [], "source": ["rej_map = (\n", "    merged_df.loc[\n", "        merged_df[\"attribute_id\"] == 7566, [\"request_id\", \"attempt_no\", \"rejection_reason\"]\n", "    ]\n", "    .rename(columns={\"rejection_reason\": \"image_qc_rejected_reason\"})\n", "    .dropna(subset=[\"image_qc_rejected_reason\"])\n", "    .drop_duplicates(subset=[\"request_id\", \"attempt_no\"])\n", ")\n", "\n", "\n", "base = merged_df.loc[merged_df[\"attribute_id\"] != 7566].copy()\n", "\n", "\n", "merged_df = base.merge(rej_map, on=[\"request_id\", \"attempt_no\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": 57, "id": "12970a39-39e5-4398-aa98-3fd392d0a3c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 323188 entries, 0 to 323187\n", "Data columns (total 34 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     323188 non-null  int64  \n", " 1   request_name                   323188 non-null  object \n", " 2   l2_category_id                 323188 non-null  int16  \n", " 3   l2_category                    323188 non-null  object \n", " 4   attempt_no                     323188 non-null  int64  \n", " 5   ai_verification_attempt_ts     323188 non-null  object \n", " 6   rid_ai_result                  323188 non-null  object \n", " 7   attribute_id                   323188 non-null  int16  \n", " 8   attribute_name                 323188 non-null  object \n", " 9   attribute_value_type           322802 non-null  object \n", " 10  ai_model_input_value           219217 non-null  object \n", " 11  manual_qc1_input_first_value   296680 non-null  object \n", " 12  manual_qc1_input_latest_value  296508 non-null  object \n", " 13  manual_qc1_attempt_no          323188 non-null  int64  \n", " 14  manual_qc1_ts                  315872 non-null  object \n", " 15  manual_qc1_result              323188 non-null  object \n", " 16  image_qc_status                323188 non-null  object \n", " 17  image_qc_ts                    189922 non-null  object \n", " 18  ai_result                      323188 non-null  object \n", " 19  created_at_x                   53321 non-null   object \n", " 20  ai_result_reason               53321 non-null   object \n", " 21  created_at_y                   53321 non-null   object \n", " 22  attempt_ts                     1108 non-null    object \n", " 23  rejection_reason               1108 non-null    object \n", " 24  rejection_reason_created_at    1108 non-null    object \n", " 25  product_id                     295984 non-null  float64\n", " 26  pid_created_date               295984 non-null  object \n", " 27  manual_qc2_attempt_no          322268 non-null  float64\n", " 28  manual_qc2_input               59229 non-null   object \n", " 29  manual_qc2_result              322268 non-null  object \n", " 30  manual_qc2_ts                  179033 non-null  object \n", " 31  manual_qc2_output              60435 non-null   object \n", " 32  qc2_before_image_qc            323188 non-null  bool   \n", " 33  image_qc_rejected_reason       35711 non-null   object \n", "dtypes: bool(1), float64(2), int16(2), int64(3), object(26)\n", "memory usage: 80.4+ MB\n"]}], "source": ["merged_df.info()"]}, {"cell_type": "code", "execution_count": 58, "id": "38197601-1258-4056-b707-38a9fc2cf5da", "metadata": {}, "outputs": [], "source": ["final = merged_df.merge(\n", "    criticality_df, how=\"left\", on=[\"l2_category_id\", \"attribute_id\"]\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": 59, "id": "5ed238bb-3e5c-4d2a-a129-17633915ae8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 323148 entries, 0 to 323187\n", "Data columns (total 35 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     323148 non-null  int64  \n", " 1   request_name                   323148 non-null  object \n", " 2   l2_category_id                 323148 non-null  int16  \n", " 3   l2_category                    323148 non-null  object \n", " 4   attempt_no                     323148 non-null  int64  \n", " 5   ai_verification_attempt_ts     323148 non-null  object \n", " 6   rid_ai_result                  323148 non-null  object \n", " 7   attribute_id                   323148 non-null  int16  \n", " 8   attribute_name                 323148 non-null  object \n", " 9   attribute_value_type           322762 non-null  object \n", " 10  ai_model_input_value           219177 non-null  object \n", " 11  manual_qc1_input_first_value   296640 non-null  object \n", " 12  manual_qc1_input_latest_value  296468 non-null  object \n", " 13  manual_qc1_attempt_no          323148 non-null  int64  \n", " 14  manual_qc1_ts                  315832 non-null  object \n", " 15  manual_qc1_result              323148 non-null  object \n", " 16  image_qc_status                323148 non-null  object \n", " 17  image_qc_ts                    189901 non-null  object \n", " 18  ai_result                      323148 non-null  object \n", " 19  created_at_x                   53281 non-null   object \n", " 20  ai_result_reason               53281 non-null   object \n", " 21  created_at_y                   53281 non-null   object \n", " 22  attempt_ts                     1108 non-null    object \n", " 23  rejection_reason               1108 non-null    object \n", " 24  rejection_reason_created_at    1108 non-null    object \n", " 25  product_id                     295962 non-null  float64\n", " 26  pid_created_date               295962 non-null  object \n", " 27  manual_qc2_attempt_no          322228 non-null  float64\n", " 28  manual_qc2_input               59210 non-null   object \n", " 29  manual_qc2_result              322228 non-null  object \n", " 30  manual_qc2_ts                  179011 non-null  object \n", " 31  manual_qc2_output              60416 non-null   object \n", " 32  qc2_before_image_qc            323148 non-null  bool   \n", " 33  image_qc_rejected_reason       35693 non-null   object \n", " 34  criticality_flag               102211 non-null  float64\n", "dtypes: bool(1), float64(3), int16(2), int64(3), object(26)\n", "memory usage: 82.9+ MB\n"]}], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": 60, "id": "25edea29-7db8-4e08-ba60-6cd89f4555c8", "metadata": {}, "outputs": [], "source": ["final[\"criticality_flag\"] = final[\"criticality_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": 61, "id": "7d44c9ba-b4e6-474d-9e41-d60c92a04564", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 323148 entries, 0 to 323187\n", "Data columns (total 35 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     323148 non-null  int64  \n", " 1   request_name                   323148 non-null  object \n", " 2   l2_category_id                 323148 non-null  int16  \n", " 3   l2_category                    323148 non-null  object \n", " 4   attempt_no                     323148 non-null  int64  \n", " 5   ai_verification_attempt_ts     323148 non-null  object \n", " 6   rid_ai_result                  323148 non-null  object \n", " 7   attribute_id                   323148 non-null  int16  \n", " 8   attribute_name                 323148 non-null  object \n", " 9   attribute_value_type           322762 non-null  object \n", " 10  ai_model_input_value           219177 non-null  object \n", " 11  manual_qc1_input_first_value   296640 non-null  object \n", " 12  manual_qc1_input_latest_value  296468 non-null  object \n", " 13  manual_qc1_attempt_no          323148 non-null  int64  \n", " 14  manual_qc1_ts                  315832 non-null  object \n", " 15  manual_qc1_result              323148 non-null  object \n", " 16  image_qc_status                323148 non-null  object \n", " 17  image_qc_ts                    189901 non-null  object \n", " 18  ai_result                      323148 non-null  object \n", " 19  created_at_x                   53281 non-null   object \n", " 20  ai_result_reason               53281 non-null   object \n", " 21  created_at_y                   53281 non-null   object \n", " 22  attempt_ts                     1108 non-null    object \n", " 23  rejection_reason               1108 non-null    object \n", " 24  rejection_reason_created_at    1108 non-null    object \n", " 25  product_id                     295962 non-null  float64\n", " 26  pid_created_date               295962 non-null  object \n", " 27  manual_qc2_attempt_no          322228 non-null  float64\n", " 28  manual_qc2_input               59210 non-null   object \n", " 29  manual_qc2_result              322228 non-null  object \n", " 30  manual_qc2_ts                  179011 non-null  object \n", " 31  manual_qc2_output              60416 non-null   object \n", " 32  qc2_before_image_qc            323148 non-null  bool   \n", " 33  image_qc_rejected_reason       35693 non-null   object \n", " 34  criticality_flag               323148 non-null  float64\n", "dtypes: bool(1), float64(3), int16(2), int64(3), object(26)\n", "memory usage: 82.9+ MB\n"]}], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": 62, "id": "6d8115cd-b4f2-44a3-ac45-978f2e672e65", "metadata": {}, "outputs": [], "source": ["dup_keys = (\n", "    final.groupby([\"request_id\", \"attribute_id\", \"attempt_no\"])\n", "    .size()\n", "    .reset_index(name=\"count\")\n", "    .query(\"count > 1\")\n", ")"]}, {"cell_type": "code", "execution_count": 63, "id": "242224d4-127e-4dd4-b48b-9f5b161cd12c", "metadata": {}, "outputs": [], "source": ["duplicate_rows = final.merge(\n", "    dup_keys[[\"request_id\", \"attribute_id\", \"attempt_no\"]],\n", "    on=[\"request_id\", \"attribute_id\", \"attempt_no\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": 64, "id": "927669ae-14dd-4950-8952-194607a32ef4", "metadata": {}, "outputs": [], "source": ["duplicate_rows = duplicate_rows.sort_values([\"request_id\", \"attribute_id\", \"attempt_no\"])"]}, {"cell_type": "code", "execution_count": 65, "id": "3daf7069-431c-4f62-9076-b7d56d25919c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>request_id</th>\n", "      <th>request_name</th>\n", "      <th>l2_category_id</th>\n", "      <th>l2_category</th>\n", "      <th>attempt_no</th>\n", "      <th>ai_verification_attempt_ts</th>\n", "      <th>rid_ai_result</th>\n", "      <th>attribute_id</th>\n", "      <th>attribute_name</th>\n", "      <th>attribute_value_type</th>\n", "      <th>...</th>\n", "      <th>product_id</th>\n", "      <th>pid_created_date</th>\n", "      <th>manual_qc2_attempt_no</th>\n", "      <th>manual_qc2_input</th>\n", "      <th>manual_qc2_result</th>\n", "      <th>manual_qc2_ts</th>\n", "      <th>manual_qc2_output</th>\n", "      <th>qc2_before_image_qc</th>\n", "      <th>image_qc_rejected_reason</th>\n", "      <th>criticality_flag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>305714</td>\n", "      <td>Play with English and Math (Combined Edi</td>\n", "      <td>512</td>\n", "      <td>Children's Activity Books</td>\n", "      <td>1</td>\n", "      <td>2025-08-06 11:55:34.222</td>\n", "      <td>False</td>\n", "      <td>65</td>\n", "      <td>Author</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_not_applicable</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>305714</td>\n", "      <td>Play with English and Math (Combined Edi</td>\n", "      <td>512</td>\n", "      <td>Children's Activity Books</td>\n", "      <td>1</td>\n", "      <td>2025-08-06 11:55:34.222</td>\n", "      <td>False</td>\n", "      <td>65</td>\n", "      <td>Author</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_not_applicable</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>305714</td>\n", "      <td>Play with English and Math (Combined Edi</td>\n", "      <td>512</td>\n", "      <td>Children's Activity Books</td>\n", "      <td>1</td>\n", "      <td>2025-08-06 11:55:34.222</td>\n", "      <td>False</td>\n", "      <td>65</td>\n", "      <td>Author</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_not_applicable</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>305714</td>\n", "      <td>Play with English and Math (Combined Edi</td>\n", "      <td>512</td>\n", "      <td>Children's Activity Books</td>\n", "      <td>1</td>\n", "      <td>2025-08-06 11:55:34.222</td>\n", "      <td>False</td>\n", "      <td>65</td>\n", "      <td>Author</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_not_applicable</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>305715</td>\n", "      <td>MIND MANIA: Crayon Coloring</td>\n", "      <td>512</td>\n", "      <td>Children's Activity Books</td>\n", "      <td>1</td>\n", "      <td>2025-08-06 11:49:12.351</td>\n", "      <td>False</td>\n", "      <td>65</td>\n", "      <td>Author</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_not_applicable</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>307798</td>\n", "      <td>Simple Refreshing Facial Wash, 250ml | 100% So...</td>\n", "      <td>697</td>\n", "      <td>Face <PERSON></td>\n", "      <td>1</td>\n", "      <td>2025-08-07 10:41:33.951</td>\n", "      <td>True</td>\n", "      <td>6402</td>\n", "      <td>Hero Ingredient</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>696476.0</td>\n", "      <td>2025-08-07 10:53:26.210</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>307798</td>\n", "      <td>Simple Refreshing Facial Wash, 250ml | 100% So...</td>\n", "      <td>697</td>\n", "      <td>Face <PERSON></td>\n", "      <td>1</td>\n", "      <td>2025-08-07 10:41:33.951</td>\n", "      <td>True</td>\n", "      <td>6402</td>\n", "      <td>Hero Ingredient</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>696476.0</td>\n", "      <td>2025-08-07 10:53:26.210</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>307798</td>\n", "      <td>Simple Refreshing Facial Wash, 250ml | 100% So...</td>\n", "      <td>697</td>\n", "      <td>Face <PERSON></td>\n", "      <td>1</td>\n", "      <td>2025-08-07 10:41:33.951</td>\n", "      <td>True</td>\n", "      <td>6402</td>\n", "      <td>Hero Ingredient</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>696476.0</td>\n", "      <td>2025-08-07 10:53:26.210</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>307798</td>\n", "      <td>Simple Refreshing Facial Wash, 250ml | 100% So...</td>\n", "      <td>697</td>\n", "      <td>Face <PERSON></td>\n", "      <td>1</td>\n", "      <td>2025-08-07 10:41:33.951</td>\n", "      <td>True</td>\n", "      <td>6402</td>\n", "      <td>Hero Ingredient</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>696476.0</td>\n", "      <td>2025-08-07 10:53:26.210</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>307798</td>\n", "      <td>Simple Refreshing Facial Wash, 250ml | 100% So...</td>\n", "      <td>697</td>\n", "      <td>Face <PERSON></td>\n", "      <td>1</td>\n", "      <td>2025-08-07 10:41:33.951</td>\n", "      <td>True</td>\n", "      <td>6402</td>\n", "      <td>Hero Ingredient</td>\n", "      <td>enum</td>\n", "      <td>...</td>\n", "      <td>696476.0</td>\n", "      <td>2025-08-07 10:53:26.210</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>qc2_pending</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>557 rows × 35 columns</p>\n", "</div>"], "text/plain": ["     request_id                                       request_name  \\\n", "50       305714           Play with English and Math (Combined Edi   \n", "51       305714           Play with English and Math (Combined Edi   \n", "52       305714           Play with English and Math (Combined Edi   \n", "53       305714           Play with English and Math (Combined Edi   \n", "439      305715                        MIND MANIA: Crayon Coloring   \n", "..          ...                                                ...   \n", "193      307798  Simple Refreshing Facial Wash, 250ml | 100% So...   \n", "194      307798  Simple Refreshing Facial Wash, 250ml | 100% So...   \n", "195      307798  Simple Refreshing Facial Wash, 250ml | 100% So...   \n", "196      307798  Simple Refreshing Facial Wash, 250ml | 100% So...   \n", "197      307798  Simple Refreshing Facial Wash, 250ml | 100% So...   \n", "\n", "     l2_category_id                l2_category  attempt_no  \\\n", "50              512  Children's Activity Books           1   \n", "51              512  Children's Activity Books           1   \n", "52              512  Children's Activity Books           1   \n", "53              512  Children's Activity Books           1   \n", "439             512  Children's Activity Books           1   \n", "..              ...                        ...         ...   \n", "193             697                  Face Wash           1   \n", "194             697                  Face Wash           1   \n", "195             697                  Face Was<PERSON>           1   \n", "196             697                  Face Was<PERSON>           1   \n", "197             697                  Face Wash           1   \n", "\n", "    ai_verification_attempt_ts rid_ai_result  attribute_id   attribute_name  \\\n", "50     2025-08-06 11:55:34.222         <PERSON><PERSON><PERSON>            65           Author   \n", "51     2025-08-06 11:55:34.222         <PERSON><PERSON><PERSON>            65           Author   \n", "52     2025-08-06 11:55:34.222         <PERSON><PERSON><PERSON>            65           Author   \n", "53     2025-08-06 11:55:34.222         <PERSON><PERSON><PERSON>            65           Author   \n", "439    2025-08-06 11:49:12.351         <PERSON><PERSON><PERSON>            65           Author   \n", "..                         ...           ...           ...              ...   \n", "193    2025-08-07 10:41:33.951          True          6402  Hero Ingredient   \n", "194    2025-08-07 10:41:33.951          True          6402  Hero Ingredient   \n", "195    2025-08-07 10:41:33.951          True          6402  Hero Ingredient   \n", "196    2025-08-07 10:41:33.951          True          6402  Hero Ingredient   \n", "197    2025-08-07 10:41:33.951          True          6402  Hero Ingredient   \n", "\n", "    attribute_value_type  ... product_id         pid_created_date  \\\n", "50                  enum  ...        NaN                     None   \n", "51                  enum  ...        NaN                     None   \n", "52                  enum  ...        NaN                     None   \n", "53                  enum  ...        NaN                     None   \n", "439                 enum  ...        NaN                     None   \n", "..                   ...  ...        ...                      ...   \n", "193                 enum  ...   696476.0  2025-08-07 10:53:26.210   \n", "194                 enum  ...   696476.0  2025-08-07 10:53:26.210   \n", "195                 enum  ...   696476.0  2025-08-07 10:53:26.210   \n", "196                 enum  ...   696476.0  2025-08-07 10:53:26.210   \n", "197                 enum  ...   696476.0  2025-08-07 10:53:26.210   \n", "\n", "    manual_qc2_attempt_no  manual_qc2_input   manual_qc2_result manual_qc2_ts  \\\n", "50                    0.0              None  qc2_not_applicable          None   \n", "51                    0.0              None  qc2_not_applicable          None   \n", "52                    0.0              None  qc2_not_applicable          None   \n", "53                    0.0              None  qc2_not_applicable          None   \n", "439                   0.0              None  qc2_not_applicable          None   \n", "..                    ...               ...                 ...           ...   \n", "193                   0.0              None         qc2_pending          None   \n", "194                   0.0              None         qc2_pending          None   \n", "195                   0.0              None         qc2_pending          None   \n", "196                   0.0              None         qc2_pending          None   \n", "197                   0.0              None         qc2_pending          None   \n", "\n", "    manual_qc2_output qc2_before_image_qc image_qc_rejected_reason  \\\n", "50               None               False                      NaN   \n", "51               None               False                      NaN   \n", "52               None               False                      NaN   \n", "53               None               False                      NaN   \n", "439              None               False                      NaN   \n", "..                ...                 ...                      ...   \n", "193              None               False                      NaN   \n", "194              None               False                      NaN   \n", "195              None               False                      NaN   \n", "196              None               False                      NaN   \n", "197              None               False                      NaN   \n", "\n", "    criticality_flag  \n", "50               1.0  \n", "51               1.0  \n", "52               1.0  \n", "53               1.0  \n", "439              1.0  \n", "..               ...  \n", "193              0.0  \n", "194              0.0  \n", "195              0.0  \n", "196              0.0  \n", "197              0.0  \n", "\n", "[557 rows x 35 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["duplicate_rows"]}, {"cell_type": "code", "execution_count": 66, "id": "bec22d31-630b-47e6-989f-b6b7d5cec951", "metadata": {}, "outputs": [], "source": ["final = final.drop(columns=[\"created_at_x\", \"created_at_y\"])"]}, {"cell_type": "code", "execution_count": 67, "id": "b9563c54-a07d-4c7f-8a98-b39b26a7f7f2", "metadata": {}, "outputs": [], "source": ["cols = final.columns.tolist()\n", "\n", "cols.remove(\"ai_result\")\n", "cols.remove(\"ai_result_reason\")\n", "\n", "\n", "idx = cols.index(\"ai_model_input_value\")\n", "cols.insert(idx + 1, \"ai_result\")\n", "cols.insert(idx + 2, \"ai_result_reason\")\n", "\n", "final = final[cols]"]}, {"cell_type": "code", "execution_count": 68, "id": "ae193c48-1d83-4b11-bd9e-d28249fad41d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 323148 entries, 0 to 323187\n", "Data columns (total 33 columns):\n", " #   Column                         Non-Null Count   Dtype  \n", "---  ------                         --------------   -----  \n", " 0   request_id                     323148 non-null  int64  \n", " 1   request_name                   323148 non-null  object \n", " 2   l2_category_id                 323148 non-null  int16  \n", " 3   l2_category                    323148 non-null  object \n", " 4   attempt_no                     323148 non-null  int64  \n", " 5   ai_verification_attempt_ts     323148 non-null  object \n", " 6   rid_ai_result                  323148 non-null  object \n", " 7   attribute_id                   323148 non-null  int16  \n", " 8   attribute_name                 323148 non-null  object \n", " 9   attribute_value_type           322762 non-null  object \n", " 10  ai_model_input_value           219177 non-null  object \n", " 11  ai_result                      323148 non-null  object \n", " 12  ai_result_reason               53281 non-null   object \n", " 13  manual_qc1_input_first_value   296640 non-null  object \n", " 14  manual_qc1_input_latest_value  296468 non-null  object \n", " 15  manual_qc1_attempt_no          323148 non-null  int64  \n", " 16  manual_qc1_ts                  315832 non-null  object \n", " 17  manual_qc1_result              323148 non-null  object \n", " 18  image_qc_status                323148 non-null  object \n", " 19  image_qc_ts                    189901 non-null  object \n", " 20  attempt_ts                     1108 non-null    object \n", " 21  rejection_reason               1108 non-null    object \n", " 22  rejection_reason_created_at    1108 non-null    object \n", " 23  product_id                     295962 non-null  float64\n", " 24  pid_created_date               295962 non-null  object \n", " 25  manual_qc2_attempt_no          322228 non-null  float64\n", " 26  manual_qc2_input               59210 non-null   object \n", " 27  manual_qc2_result              322228 non-null  object \n", " 28  manual_qc2_ts                  179011 non-null  object \n", " 29  manual_qc2_output              60416 non-null   object \n", " 30  qc2_before_image_qc            323148 non-null  bool   \n", " 31  image_qc_rejected_reason       35693 non-null   object \n", " 32  criticality_flag               323148 non-null  float64\n", "dtypes: bool(1), float64(3), int16(2), int64(3), object(24)\n", "memory usage: 78.0+ MB\n"]}], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "c74a4508-6078-4699-b5a7-333ad3408c1c", "metadata": {}, "outputs": [], "source": ["# final[\"rid_ai_result\"] = final[\"rid_ai_result\"].replace(\"same as before\", pd.NA)\n", "\n", "\n", "# final.sort_values([\"request_id\", \"attribute_id\", \"attempt_no\"], inplace=True)\n", "\n", "\n", "# final[\"rid_ai_result\"] = final.groupby([\"request_id\", \"attribute_id\"])[\"rid_ai_result\"].ffill()"]}, {"cell_type": "code", "execution_count": null, "id": "45bdcd37-ac6c-41fe-ae28-83233d89003d", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "513db100-0aab-42de-960d-a833ad4713c4", "metadata": {}, "outputs": [], "source": ["final[\"ai_verification_attempt_ts\"] = pd.to_datetime(final[\"ai_verification_attempt_ts\"])\n", "\n", "\n", "today_start = pd.Timestamp.now().normalize()\n", "mask = final[\"rid_ai_result\"].isna() & (final[\"ai_verification_attempt_ts\"] >= today_start)\n", "\n", "\n", "final = final.loc[~mask].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ab88c68b-9b32-4f67-9847-0b1fa9fca44b", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "19ac39ed-fd2f-4ae3-82de-cd72601a3756", "metadata": {}, "outputs": [], "source": ["mask = final[\"request_name\"].isna() & (final[\"ai_verification_attempt_ts\"] >= today_start)\n", "\n", "\n", "final = final.loc[~mask].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e688f09f-dd9f-4a75-88de-250f22a4ef1b", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "fd32b354-7442-4ba8-a026-47fa386980c7", "metadata": {}, "outputs": [], "source": ["final = final.dropna(subset=[\"attribute_value_type\"]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b91649a9-071d-49cb-8a9b-ccb8a66a7007", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "52fa73f7-04ac-438f-9082-bec6531c1dd0", "metadata": {}, "outputs": [], "source": ["final[final[\"manual_qc2_result\"].isna()][[\"manual_qc1_result\", \"image_qc_status\"]].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "0de9e0cb-1178-4538-b826-c8f4937d2a91", "metadata": {}, "outputs": [], "source": ["final[(final[\"manual_qc1_result\"].isin([\"qc1_rejected\"])) & (final[\"manual_qc2_result\"].isna())][\n", "    [\"request_id\", \"ai_verification_attempt_ts\", \"image_qc_status\", \"image_qc_ts\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7b0ed09d-a63b-437b-8c0e-cdf217117b50", "metadata": {}, "outputs": [], "source": ["final.loc[\n", "    final[\"manual_qc1_result\"].isin([\"qc1_rejected\"]) & final[\"manual_qc2_result\"].isna(),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "24eb2054-b071-4372-b047-c357490dacad", "metadata": {}, "outputs": [], "source": ["final[(final[\"manual_qc1_result\"].isin([\"qc1_pending\"])) & (final[\"manual_qc2_result\"].isna())][\n", "    [\"request_id\", \"ai_verification_attempt_ts\", \"image_qc_status\", \"image_qc_ts\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "43862a4f-7130-4259-b4a2-83a1864c6228", "metadata": {}, "outputs": [], "source": ["final.loc[\n", "    final[\"manual_qc1_result\"].isin([\"qc1_pending\"]) & final[\"manual_qc2_result\"].isna(),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "c9076754-aeb4-4075-b3e7-d4e7300c05bc", "metadata": {}, "outputs": [], "source": ["final.info()"]}, {"cell_type": "code", "execution_count": null, "id": "8749773d-d4ca-4b4d-9fef-874404128ea0", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MXgGW3yvifBPweOC3tTU6Vt1CTYmPNumVuY0vmxKCzQ\"\n", "sheet_name = \"for alerts_do_not_use\"\n", "daily_assigning_df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "b0d32982-2588-4496-a1ff-fc354a769586", "metadata": {}, "outputs": [], "source": ["# daily_assigning_df = pd.read_csv('Approvals Daily Assigning - for alerts_do_not_use (3).csv')"]}, {"cell_type": "code", "execution_count": null, "id": "ba9b4d57-cbc8-4b85-beff-f24532db1e9f", "metadata": {}, "outputs": [], "source": ["daily_assigning_df[\"product_id\"] = (\n", "    pd.to_numeric(daily_assigning_df[\"product_id\"], errors=\"coerce\").dropna().astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "de2bdf9b-62de-4525-9305-7238e03d4d61", "metadata": {}, "outputs": [], "source": ["remarks_filtered = daily_assigning_df[[\"product_id\", \"error_remarks\", \"on_hold_remarks\", \"POC\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c2c746c7-56c9-43b2-a8a5-ae7a1623dd55", "metadata": {}, "outputs": [], "source": ["remarks_filtered[\"on_hold_remarks\"] = remarks_filtered[\"on_hold_remarks\"].fillna(\n", "    remarks_filtered[\"error_remarks\"]\n", ")\n", "\n", "remarks_filtered.drop(columns=\"error_remarks\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "08e9e8ac-83c1-46a0-b51f-8e1b537ea2f2", "metadata": {}, "outputs": [], "source": ["remarks_filtered_fine = remarks_filtered[[\"product_id\", \"on_hold_remarks\", \"POC\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "fcb9260b-2e60-4b17-aa6b-4956af74a032", "metadata": {}, "outputs": [], "source": ["remarks_filtered_fine = remarks_filtered_fine.loc[\n", "    remarks_filtered_fine[\"on_hold_remarks\"].notna()\n", "    & remarks_filtered_fine[\"on_hold_remarks\"].str.strip().astype(bool)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a3c1341e-692d-49fe-8189-14d89b4cd69c", "metadata": {}, "outputs": [], "source": ["first_per_pid = remarks_filtered_fine.groupby(\"product_id\", as_index=False).first()"]}, {"cell_type": "code", "execution_count": null, "id": "3cde558a-8382-444b-9910-77afb0c968c4", "metadata": {}, "outputs": [], "source": ["final[\"product_id\"] = pd.to_numeric(final[\"product_id\"], errors=\"raise\", downcast=\"integer\")\n", "\n", "first_per_pid[\"product_id\"] = pd.to_numeric(\n", "    first_per_pid[\"product_id\"], errors=\"raise\", downcast=\"integer\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d90398a-a2fe-4ebf-aec1-cc2575854ba5", "metadata": {}, "outputs": [], "source": ["final_ = pd.merge(final, first_per_pid, on=\"product_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "eaa00538-e651-40d0-aa94-60845ffe618b", "metadata": {}, "outputs": [], "source": ["final_upload = final_.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "713e342d-a1f9-4665-83fc-b08a869594e0", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "78e88fb9-ada2-41c4-a66a-03bb5d1d3d7e", "metadata": {}, "outputs": [], "source": ["cols_to_drop = [\"attempt_ts\", \"rejection_reason_created_at\", \"qc2_before_image_qc\"]\n", "\n", "rename_map = {\"rejection_reason\": \"manual_qc1_rejection_reason\"}\n", "\n", "final_upload = final_upload.drop(columns=cols_to_drop, errors=\"ignore\").rename(columns=rename_map)"]}, {"cell_type": "code", "execution_count": null, "id": "b7de91df-f1fa-49e9-9a8b-153e64dfde41", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "efc9f7e9-9325-4ab4-84e1-4ae0222126c1", "metadata": {}, "outputs": [], "source": ["cols = final_upload.columns.tolist()\n", "\n", "cols.remove(\"manual_qc1_rejection_reason\")\n", "\n", "\n", "idx = cols.index(\"manual_qc1_result\")\n", "cols.insert(idx + 1, \"manual_qc1_rejection_reason\")\n", "\n", "final_upload = final_upload[cols]"]}, {"cell_type": "code", "execution_count": null, "id": "c1142c9c-232c-4cdc-b44d-b84c42c9d32c", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "b178f437-5ff5-48a6-9155-8219ae3e3b97", "metadata": {}, "outputs": [], "source": ["final_upload[\"image_qc_status\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "67aa012a-0bec-42ab-a833-24fe3cc37e58", "metadata": {}, "outputs": [], "source": ["final_upload[\"manual_qc2_result\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "fb20083a-2b73-45f3-9a1f-2b3554a90b87", "metadata": {}, "outputs": [], "source": ["final_upload.loc[final_upload[\"product_id\"].isna(), \"manual_qc2_result\"].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "119f2b2c-4c18-466c-82fd-194fdf277142", "metadata": {}, "outputs": [], "source": ["final_upload.loc[final_upload[\"product_id\"].isna(), \"manual_qc2_result\"] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "27c96e8e-047e-423c-8027-c2e3f944cd7e", "metadata": {}, "outputs": [], "source": ["final_upload.loc[final_upload[\"product_id\"].isna(), \"manual_qc2_result\"].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "27208c4d-7d10-460d-99ce-280deae465df", "metadata": {}, "outputs": [], "source": ["final_upload.loc[final_upload[\"product_id\"].isna(), \"image_qc_status\"].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "de55dc6a-663c-4d28-b730-79bd2ea3aefa", "metadata": {}, "outputs": [], "source": ["final_upload.loc[final_upload[\"product_id\"].notna(), \"image_qc_status\"].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "60b52ac7-327e-43db-82b7-153b35217d71", "metadata": {}, "outputs": [], "source": ["qc1_freq = final_upload.loc[mask, \"image_qc_status\"].value_counts(dropna=False)\n", "qc1_freq"]}, {"cell_type": "code", "execution_count": null, "id": "cead21d2-2759-4929-8626-c84645f7ebfc", "metadata": {}, "outputs": [], "source": ["nan_rows = final_upload[final_upload[\"image_qc_status\"].isna()]\n", "nan_rows"]}, {"cell_type": "code", "execution_count": null, "id": "3e62377e-0ec3-4013-aba0-4018d1026fb3", "metadata": {}, "outputs": [], "source": ["edge_rows = final_upload[final_upload[\"image_qc_status\"] == \"check for edge case\"]\n", "edge_rows"]}, {"cell_type": "code", "execution_count": null, "id": "26746fc4-13c2-45bc-88e9-a1b7e75ec207", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "f541bedb-2183-46c9-b2ac-c8f34d25c9c3", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_rejected\"])\n", "    & final_upload[\"image_qc_status\"].isin(\n", "        [\"image_qc_approved\", \"image_qc_rejected\", \"image_qc_pending\"]\n", "    )\n", "][[\"request_id\", \"attribute_id\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "cdcefcea-b420-4127-8c06-8ca678a67df6", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_approved\"])\n", "    & final_upload[\"image_qc_status\"].isin([\"image_qc_not_applicable\"])\n", "][[\"request_id\", \"attribute_id\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c05fc1aa-82f6-4482-8fea-de5533ead954", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\"])\n", "    & final_upload[\"image_qc_status\"].isin(\n", "        [\"image_qc_approved\", \"image_qc_rejected\", \"image_qc_pending\"]\n", "    )\n", "][[\"request_id\", \"attribute_id\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "f2273827-f97f-4c86-a07d-afe75da5a561", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_rejected\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"])\n", "][[\"request_id\", \"manual_qc2_result\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "b42a45b8-871e-4b67-83e9-d38f7291accf", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"image_qc_status\"] == \"image_qc_rejected\", \"manual_qc2_result\"\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a14a4013-8f30-42fb-b3b0-d34cc6b2b1e6", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_rejected\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"]),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "007ce129-2713-49b6-b5d5-5fb5e1c32396", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_approved\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_not_applicable\"])\n", "][[\"request_id\", \"manual_qc2_result\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "3b13910e-aab9-4f39-82fd-dda3cf0a2b06", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_pending\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"])\n", "][[\"request_id\", \"manual_qc2_result\", \"manual_qc2_ts\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "8b7b14e6-d517-4611-98ab-1d7e51cd29d4", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"image_qc_status\"] == \"image_qc_pending\", \"manual_qc2_result\"\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6f1c55be-2a73-43c2-8539-ba1dc10f6edb", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_pending\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"]),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "ed1fd0d6-82cc-4d6b-9841-4fdddfcc598c", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"image_qc_status\"].isin([\"image_qc_pending\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_not_applicable\"])\n", "][[\"request_id\", \"manual_qc2_result\", \"manual_qc2_ts\", \"image_qc_status\", \"image_qc_ts\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "79af6549-a86d-40bd-8a37-5225eccf53bc", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"] == \"qc1_rejected\", \"manual_qc2_result\"\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0568cf80-1659-41b5-ade7-c90460c5b39c", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_rejected\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"]),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "56e00173-4a54-4075-8c97-85b845c8a034", "metadata": {}, "outputs": [], "source": ["final_upload[final_upload[\"request_id\"] == 305206][\n", "    [\n", "        \"request_id\",\n", "        \"product_id\",\n", "        \"attribute_name\",\n", "        \"pid_created_date\",\n", "        \"ai_verification_attempt_ts\",\n", "        \"attempt_no\",\n", "        \"manual_qc1_result\",\n", "        \"manual_qc1_ts\",\n", "        \"image_qc_status\",\n", "        \"image_qc_ts\",\n", "        \"manual_qc2_result\",\n", "        \"manual_qc2_ts\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "47227d39-3960-40fc-930e-777c8d7dac10", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"] == \"qc1_approved\", \"manual_qc2_result\"\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3a53082d-cb60-479a-bca0-400f168103b2", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"] == \"qc1_approved\", \"image_qc_status\"\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e464c10a-ba56-4c3c-92fb-03d0f1f96643", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    (final_upload[\"manual_qc1_result\"] == \"qc1_approved\")\n", "    & (final_upload[\"image_qc_status\"] == \"image_qc_approved\"),\n", "    \"manual_qc2_result\",\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b44ce7d0-b452-49a4-af44-484ec67e1723", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    (final_upload[\"manual_qc1_result\"] == \"qc1_approved\")\n", "    & (final_upload[\"image_qc_status\"] == \"image_qc_pending\"),\n", "    \"manual_qc2_result\",\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "5266fd92-b21d-4ea8-b264-4ac1e5a73eb9", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    (final_upload[\"manual_qc1_result\"] == \"qc1_approved\")\n", "    & (final_upload[\"image_qc_status\"] == \"image_qc_rejected\"),\n", "    \"manual_qc2_result\",\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "38503be1-fcc2-4990-9486-4506371e5b5d", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    (final_upload[\"manual_qc1_result\"] == \"qc1_pending\"),\n", "    \"manual_qc2_result\",\n", "].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0f454844-d198-4e56-8ed0-a3b7c2822b4f", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\"])\n", "    & final_upload[\"manual_qc2_result\"].isin([\"qc2_pending\", \"qc2_approved\", \"qc2_rejected\"]),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "5e532080-4976-4748-a83c-39940831f831", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\", \"qc1_rejected\"])\n", "    & final_upload[\"product_id\"].notna(),\n", "    \"manual_qc2_result\",\n", "] = \"qc2_not_applicable\""]}, {"cell_type": "code", "execution_count": null, "id": "e8cb4b23-6c85-40c8-84f2-d613dbfe8a11", "metadata": {}, "outputs": [], "source": ["final_upload[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\", \"qc1_rejected\"])\n", "    & final_upload[\"product_id\"].notna()\n", "][\n", "    [\n", "        \"request_id\",\n", "        \"attempt_no\",\n", "        \"manual_qc1_result\",\n", "        \"manual_qc2_result\",\n", "        \"product_id\",\n", "        \"manual_qc2_ts\",\n", "        \"image_qc_status\",\n", "        \"image_qc_ts\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "51cf3fad-df1d-46ab-93f5-c5e660069dc9", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\", \"qc1_approved\"]),\n", "    \"manual_qc1_rejection_reason\",\n", "] = pd.NA"]}, {"cell_type": "code", "execution_count": null, "id": "8a86ba93-68fa-4190-983f-b58fdbf9a092", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"image_qc_status\"].isin(\n", "        [\"image_qc_approved\", \"image_qc_not_applicable\", \"image_qc_pending\"]\n", "    ),\n", "    \"image_qc_rejected_reason\",\n", "] = pd.NA"]}, {"cell_type": "code", "execution_count": null, "id": "17107467-ce27-45db-8c89-660d40641581", "metadata": {}, "outputs": [], "source": ["final_upload.loc[\n", "    final_upload[\"manual_qc1_result\"].isin([\"qc1_pending\", \"qc1_rejected\"])\n", "    & final_upload[\"product_id\"].notna(),\n", "    [\"product_id\", \"pid_created_date\"],\n", "] = [pd.NA, pd.NaT]"]}, {"cell_type": "code", "execution_count": null, "id": "00f4c744-61b1-4079-bb03-79a97603f81b", "metadata": {}, "outputs": [], "source": ["ai_verified = final_upload[final_upload[\"ai_result\"] != \"not verified\"].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "0dfa0a65-449b-430d-bc16-76fee8160b31", "metadata": {}, "outputs": [], "source": ["ai_verified.info()"]}, {"cell_type": "code", "execution_count": null, "id": "90441058-3b67-4475-be0c-027210b1a77b", "metadata": {}, "outputs": [], "source": ["ai_verified[[\"manual_qc2_result\"]].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "6fa77fe9-a76b-4444-b172-4eb3cf927360", "metadata": {}, "outputs": [], "source": ["ai_verified[[\"rid_ai_result\"]].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "6187eb22-7565-458e-9358-3974f5090658", "metadata": {}, "outputs": [], "source": ["def same(a, b):\n", "    a = a.replace(\"\", np.nan)\n", "    b = b.replace(\"\", np.nan)\n", "    return (a == b) | (a.isna() & b.isna())\n", "\n", "\n", "def summarize(grp):\n", "    ai_ok = (grp[\"rid_ai_result\"] == \"True\").any()\n", "    ai_rej = (grp[\"rid_ai_result\"] == \"False\").any()\n", "\n", "    qc1_ok = (grp[\"manual_qc1_result\"] == \"qc1_approved\").any()\n", "    qc1_rej = (grp[\"manual_qc1_result\"] == \"qc1_rejected\").any()\n", "    qc1_pen = (grp[\"manual_qc1_result\"] == \"qc1_pending\").any()\n", "\n", "    i_ok = (grp[\"image_qc_status\"] == \"image_qc_approved\").any()\n", "    i_rej = (grp[\"image_qc_status\"] == \"image_qc_rejected\").any()\n", "    i_pen = (grp[\"image_qc_status\"] == \"image_qc_pending\").any()\n", "\n", "    qc2_ok = (grp[\"manual_qc2_result\"] == \"qc2_approved\").any()\n", "    qc2_rej = (grp[\"manual_qc2_result\"] == \"qc2_rejected\").any()\n", "    qc2_pen = (grp[\"manual_qc2_result\"] == \"qc2_pending\").any()\n", "    # qc2_pen = grp[\"manual_qc2_result\"].isna().any()\n", "\n", "    same_qc1 = same(grp[\"ai_model_input_value\"], grp[\"manual_qc1_input_latest_value\"]).all()\n", "    same_qc2 = same(grp[\"manual_qc1_input_latest_value\"], grp[\"manual_qc2_output\"]).all()\n", "\n", "    return pd.Series(\n", "        {\n", "            \"ai_ok\": ai_ok,\n", "            \"ai_rej\": ai_rej,\n", "            \"qc1_ok\": qc1_ok,\n", "            \"qc1_rej\": qc1_rej,\n", "            \"qc1_pen\": qc1_pen,\n", "            \"qc2_ok\": qc2_ok,\n", "            \"qc2_rej\": qc2_rej,\n", "            \"qc2_pen\": qc2_pen,\n", "            \"same_qc1\": same_qc1,\n", "            \"same_qc2\": same_qc2,\n", "            \"i_rej\": i_rej,\n", "            \"i_pen\": i_pen,\n", "            \"i_ok\": i_ok,\n", "        }\n", "    )\n", "\n", "\n", "pair_df = ai_verified.groupby([\"request_id\", \"attempt_no\"]).apply(summarize).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "c495c826-b13e-488c-896a-a9e2d034fc21", "metadata": {}, "outputs": [], "source": ["pair_df"]}, {"cell_type": "code", "execution_count": null, "id": "f580c2c8-7e94-425c-b0e7-2c492f8ef45c", "metadata": {}, "outputs": [], "source": ["# pair_df.to_csv('test.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "ed3344f3-553d-42e4-afde-20a9251437a5", "metadata": {}, "outputs": [], "source": ["pair_df[\"QC1 Bucket\"] = np.select(\n", "    [\n", "        pair_df.ai_ok & pair_df.qc1_rej,\n", "        pair_df.ai_ok & pair_df.qc1_ok & pair_df.same_qc1,\n", "        pair_df.ai_ok & pair_df.qc1_ok & ~pair_df.same_qc1,\n", "        pair_df.ai_rej & pair_df.qc1_rej,\n", "        pair_df.ai_rej & pair_df.qc1_ok & pair_df.same_qc1,\n", "        pair_df.ai_rej & pair_df.qc1_ok & ~pair_df.same_qc1,\n", "        pair_df.qc1_pen,\n", "    ],\n", "    [\n", "        \"Approved by AI, rejected in QC1\",\n", "        \"Approved by AI, approved in QC1 with same values\",\n", "        \"Approved by AI, approved in QC1 with value changes\",\n", "        \"Rejected by AI, also rejected in QC1\",\n", "        \"Rejected by AI, approved in QC1 with same values\",\n", "        \"Rejected by AI, approved in QC1 with value changes\",\n", "        \"Manual QC1 Pending\",\n", "    ],\n", "    default=\"Other\",\n", ")\n", "\n", "\n", "pair_df[\"QC2 Bucket\"] = np.select(\n", "    [\n", "        pair_df.qc2_ok & pair_df.same_qc2,\n", "        pair_df.qc2_ok & ~pair_df.same_qc2,\n", "        pair_df.qc2_rej,\n", "        pair_df.qc2_pen | pair_df.qc1_pen | pair_df.i_pen,\n", "        pair_df.qc1_rej | pair_df.i_rej,\n", "    ],\n", "    [\n", "        \"Approved in QC2 with same values\",\n", "        \"Approved in QC2 with different values\",\n", "        \"Rejected in QC2\",\n", "        \"QC2 Pending\",\n", "        \"QC2 Not Applicable\",\n", "    ],\n", "    default=\"Other\",\n", ")\n", "\n", "\n", "row_order = [\n", "    \"Approved by AI, rejected in QC1\",\n", "    \"Approved by AI, approved in QC1 with same values\",\n", "    \"Approved by AI, approved in QC1 with value changes\",\n", "    \"Rejected by AI, also rejected in QC1\",\n", "    \"Rejected by AI, approved in QC1 with same values\",\n", "    \"Rejected by AI, approved in QC1 with value changes\",\n", "    \"Manual QC1 Pending\",\n", "    \"TOTAL\",\n", "]\n", "col_order = [\n", "    \"Approved in QC2 with same values\",\n", "    \"Approved in QC2 with different values\",\n", "    \"Rejected in QC2\",\n", "    \"QC2 Pending\",\n", "    \"QC2 Not Applicable\",\n", "    \"TOTAL\",\n", "]\n", "\n", "matrix = pair_df.pivot_table(\n", "    index=\"QC1 Bucket\",\n", "    columns=\"QC2 Bucket\",\n", "    values=\"request_id\",\n", "    aggfunc=\"count\",\n", "    fill_value=0,\n", "    margins=True,\n", "    margins_name=\"TOTAL\",\n", ").reindex(index=row_order, columns=col_order)\n", "\n", "matrix"]}, {"cell_type": "code", "execution_count": null, "id": "121ff449-c622-4ba3-ab03-69d3318bd433", "metadata": {}, "outputs": [], "source": ["pair_df"]}, {"cell_type": "code", "execution_count": null, "id": "10f84af8-4551-4ef6-85b3-1a876dca4133", "metadata": {}, "outputs": [], "source": ["matrix = matrix.reset_index()\n", "matrix.insert(0, \"\", \"\")\n", "matrix"]}, {"cell_type": "code", "execution_count": null, "id": "3bf3a71d-9754-4ae4-914f-075cff1b785a", "metadata": {}, "outputs": [], "source": ["# matrix.to_csv('test.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "f97360d4-917b-4cfd-a007-16edd2c95300", "metadata": {}, "outputs": [], "source": ["df = matrix.copy()\n", "sheet_id = \"1f6CD1L2aYexVBbd5tojDETy3IwW3gx3Y9I95NfkzaUo\"\n", "sheet_name = \"RID Count post AI\"\n", "\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "addf30fe-aa58-4a1f-b866-c14ec8e3b9c2", "metadata": {}, "outputs": [], "source": ["pair_df[\"qc1_decided\"] = pair_df.qc1_ok | pair_df.qc1_rej\n", "pair_df[\"qc2_decided\"] = pair_df.qc2_ok | pair_df.qc2_rej\n", "\n", "\n", "def confusion_counts(df, pred_ok, pred_rej, true_ok, true_rej, decided_mask):\n", "    mask = df[decided_mask]\n", "    m = df[mask]\n", "    TP = (m[pred_ok] & m[true_ok]).sum()\n", "    FP = (m[pred_ok] & m[true_rej]).sum()\n", "    FN = (m[pred_rej] & m[true_ok]).sum()\n", "    TN = (m[pred_rej] & m[true_rej]).sum()\n", "    total = TP + FP + FN + TN  # same as m.shape[0]\n", "    return pd.Series({\"TP\": TP, \"FP\": FP, \"FN\": FN, \"TN\": TN, \"N\": total})\n", "\n", "\n", "qc1_counts = confusion_counts(\n", "    pair_df,\n", "    pred_ok=\"ai_ok\",\n", "    pred_rej=\"ai_rej\",\n", "    true_ok=\"qc1_ok\",\n", "    true_rej=\"qc1_rej\",\n", "    decided_mask=\"qc1_decided\",\n", ")\n", "\n", "qc2_counts = confusion_counts(\n", "    pair_df,\n", "    pred_ok=\"ai_ok\",\n", "    pred_rej=\"ai_rej\",\n", "    true_ok=\"qc2_ok\",\n", "    true_rej=\"qc2_rej\",\n", "    decided_mask=\"qc2_decided\",\n", ")\n", "\n", "\n", "def add_pcts(s):\n", "    denom = s[\"N\"]\n", "    df = s.to_frame().T  # 1-row DataFrame\n", "    return df.assign(\n", "        TP_pct=lambda x: x.TP / denom,\n", "        FP_pct=lambda x: x.FP / denom,\n", "        FN_pct=lambda x: x.FN / denom,\n", "        TN_pct=lambda x: x.TN / denom,\n", "    )\n", "\n", "\n", "qc1_pct = add_pcts(qc1_counts)\n", "qc2_pct = add_pcts(qc2_counts)\n", "\n", "\n", "def basic_metrics(c):\n", "    TP, FP, FN, TN = c.TP, c.FP, c.FN, c.TN\n", "    total = TP + FP + FN + TN\n", "    acc = (TP + TN) / total if total else float(\"nan\")\n", "    prec = TP / (TP + FP) if (TP + FP) else float(\"nan\")\n", "    rec = TP / (TP + FN) if (TP + FN) else float(\"nan\")\n", "    return pd.Series({\"accuracy\": acc, \"precision\": prec, \"recall\": rec})\n", "\n", "\n", "qc1_metrics = basic_metrics(qc1_counts)\n", "qc2_metrics = basic_metrics(qc2_counts)"]}, {"cell_type": "code", "execution_count": null, "id": "cdedd040-1ff9-4e0d-b353-45e379505e57", "metadata": {}, "outputs": [], "source": ["qc1_counts"]}, {"cell_type": "code", "execution_count": null, "id": "f367329e-97fc-45b8-9138-a9a3d239048f", "metadata": {}, "outputs": [], "source": ["qc1_metrics"]}, {"cell_type": "code", "execution_count": null, "id": "7ae3efa6-6585-499d-9e60-6a44813b3460", "metadata": {}, "outputs": [], "source": ["qc2_counts"]}, {"cell_type": "code", "execution_count": null, "id": "a16e5f40-fefe-48d5-a184-f5cc787a5d42", "metadata": {}, "outputs": [], "source": ["qc2_metrics"]}, {"cell_type": "code", "execution_count": null, "id": "98aae2f9-4bac-40c6-8368-ed5796be883a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "452619e7-190d-451f-beef-9062d17fc491", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "da8d435d-c11a-4993-a542-b0762cc50012", "metadata": {}, "outputs": [], "source": ["# final_df.to_csv('test.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b43889c0-bdc5-47f2-93bd-35e64fe711ee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3dbb0ae5-e4be-4cca-a506-8a4f1287a6f4", "metadata": {}, "outputs": [], "source": ["buckets = pair_df[[\"request_id\", \"attempt_no\", \"QC1 Bucket\", \"QC2 Bucket\"]]\n", "\n", "\n", "final_upload = final_upload.merge(buckets, on=[\"request_id\", \"attempt_no\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "4462bf75-5f8f-4945-adcb-77d8e1ed44de", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "26114b99-1486-4046-9f8a-828d9e2b595b", "metadata": {}, "outputs": [], "source": ["rename_map = {\"QC1 Bucket\": \"qc1_bucket\", \"QC2 Bucket\": \"qc2_bucket\"}\n", "\n", "final_upload = final_upload.rename(columns=rename_map)"]}, {"cell_type": "code", "execution_count": null, "id": "3326b0f2-e945-46bc-961c-32104018251c", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "6091c6b1-aa87-4d86-8f67-41816056dcf7", "metadata": {}, "outputs": [], "source": ["raw_agg_data = (\n", "    final_upload[\n", "        [\n", "            \"request_id\",\n", "            \"attempt_no\",\n", "            \"rid_ai_result\",\n", "            \"manual_qc1_result\",\n", "            \"image_qc_status\",\n", "            \"manual_qc2_result\",\n", "            \"qc1_bucket\",\n", "            \"qc2_bucket\",\n", "        ]\n", "    ]\n", "    .copy()\n", "    .drop_duplicates()\n", ")\n", "raw_agg_data"]}, {"cell_type": "code", "execution_count": null, "id": "5bd23a23-3d1a-4a17-a083-8f3767c7767b", "metadata": {}, "outputs": [], "source": ["# raw_agg_data[raw_agg_data[\"qc1_bucket\"] == \"Manual QC1 Pending\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0486bbaa-8e28-4b61-b04a-ca44d4a73b3e", "metadata": {}, "outputs": [], "source": ["raw_agg_data[raw_agg_data[\"request_id\"] == 306273]"]}, {"cell_type": "code", "execution_count": null, "id": "84cf9336-bdd4-4d63-abfe-f3e56e388570", "metadata": {}, "outputs": [], "source": ["# raw_agg_data.to_csv('test.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "ba59bd4e-145a-47e4-8dfc-83a4257feca3", "metadata": {}, "outputs": [], "source": ["df = raw_agg_data.copy()\n", "sheet_id = \"1f6CD1L2aYexVBbd5tojDETy3IwW3gx3Y9I95NfkzaUo\"\n", "sheet_name = \"Aggregated raw file for RID Count post AI\"\n", "\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "4cabf14e-719f-439e-87cc-a773f1470e6a", "metadata": {}, "outputs": [], "source": ["final_upload.info()"]}, {"cell_type": "code", "execution_count": null, "id": "021c5697-4c0c-4dd9-8c67-0808ac6deea4", "metadata": {}, "outputs": [], "source": ["final_upload.rename(columns={\"attempt_no\": \"ai_verification_attempt_no\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f091b48e-e19c-4ddd-8bb5-e7195a9a3130", "metadata": {}, "outputs": [], "source": ["final_upload = final_upload.astype(\n", "    {\n", "        \"request_id\": \"Int64\",\n", "        \"request_name\": \"string\",\n", "        \"l2_category_id\": \"Int64\",\n", "        \"l2_category\": \"string\",\n", "        \"ai_verification_attempt_no\": \"Int64\",\n", "        \"ai_verification_attempt_ts\": \"datetime64[ns]\",\n", "        \"rid_ai_result\": \"string\",\n", "        \"attribute_id\": \"Int64\",\n", "        \"attribute_name\": \"string\",\n", "        \"attribute_value_type\": \"string\",\n", "        \"ai_model_input_value\": \"string\",\n", "        \"ai_result\": \"string\",\n", "        \"ai_result_reason\": \"string\",\n", "        \"manual_qc1_input_first_value\": \"string\",\n", "        \"manual_qc1_input_latest_value\": \"string\",\n", "        \"manual_qc1_attempt_no\": \"Int64\",\n", "        \"manual_qc1_ts\": \"datetime64[ns]\",\n", "        \"manual_qc1_result\": \"string\",\n", "        \"manual_qc1_rejection_reason\": \"string\",\n", "        \"image_qc_status\": \"string\",\n", "        \"image_qc_ts\": \"datetime64[ns]\",\n", "        \"product_id\": \"Int64\",\n", "        \"pid_created_date\": \"datetime64[ns]\",\n", "        \"manual_qc2_attempt_no\": \"Int64\",\n", "        \"manual_qc2_input\": \"string\",\n", "        \"manual_qc2_result\": \"string\",\n", "        \"manual_qc2_ts\": \"datetime64[ns]\",\n", "        \"manual_qc2_output\": \"string\",\n", "        \"image_qc_rejected_reason\": \"string\",\n", "        \"criticality_flag\": \"Int64\",\n", "        \"on_hold_remarks\": \"string\",\n", "        \"POC\": \"string\",\n", "        \"qc1_bucket\": \"string\",\n", "        \"qc2_bucket\": \"string\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ddf20420-e05b-4d04-8f92-ab75d5ccadcf", "metadata": {}, "outputs": [], "source": ["'''\n", "check = \"\"\"\n", "SELECT COUNT(*) AS cnt FROM cms_etls.content_qc1_automation_ai_verification_tracking_v6\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "check_df = pd.read_sql_query(sql=check, con=con)\n", "'''"]}, {"cell_type": "code", "execution_count": null, "id": "2c846b9b-f36a-473f-9226-f247a494a189", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "check_df = pd.read_sql_query(sql=check, con=con)\n", "check_df\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "664df543-5b45-48f3-9221-fc7cad0a1917", "metadata": {}, "outputs": [], "source": ["# final_upload.to_csv('rectified.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e49db457-3264-48e0-a300-02486eda291e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "85ea1f7e-4a29-4cf6-ad53-ae3fc4caa4d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "51ed921c-5552-4c29-a032-53df089f0507", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "import sys\n", "\n", "# get counts\n", "expected = int(check_df[\"cnt\"].iloc[0])\n", "actual   = final_upload.shape[0]\n", "\n", "# validate\n", "if actual < expected:\n", "    print(f\"❌ DataFrame has {actual} rows, but expected at least {expected}. Exiting notebook.\")\n", "    sys.exit()\n", "\n", "# otherwise, continue…\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "52385946-475a-40e2-9928-1c8d9ab7fdda", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cms_etls\",\n", "    \"table_name\": \"content_qc1_automation_ai_verification_tracking_v7\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"request_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Request ID of a product that is going to be created.\",\n", "        },\n", "        {\n", "            \"name\": \"request_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Name of a product that is going to be created.\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"L2 category id of a product that is going to be created.\",\n", "        },\n", "        {\n", "            \"name\": \"l2_category\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"L2 category name of a product that is going to be created.\",\n", "        },\n", "        {\n", "            \"name\": \"ai_verification_attempt_no\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"ai attempt number\",\n", "        },\n", "        {\"name\": \"ai_verification_attempt_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"na\"},\n", "        {\"name\": \"rid_ai_result\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"attribute_id\", \"type\": \"INTEGER\", \"description\": \"na\"},\n", "        {\"name\": \"attribute_name\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"attribute_value_type\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"ai_model_input_value\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"ai_result\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"ai_result_reason\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_input_first_value\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_input_latest_value\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_attempt_no\", \"type\": \"INTEGER\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_result\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc1_rejection_reason\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"image_qc_status\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"image_qc_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"na\"},\n", "        {\"name\": \"product_id\", \"type\": \"INTEGER\", \"description\": \"na\"},\n", "        {\"name\": \"pid_created_date\", \"type\": \"TIMESTAMP(6)\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc2_attempt_no\", \"type\": \"INTEGER\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc2_input\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc2_result\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc2_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"na\"},\n", "        {\"name\": \"manual_qc2_output\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"image_qc_rejected_reason\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"criticality_flag\", \"type\": \"INTEGER\", \"description\": \"na\"},\n", "        {\"name\": \"on_hold_remarks\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"POC\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"qc1_bucket\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "        {\"name\": \"qc2_bucket\", \"type\": \"VARCHAR\", \"description\": \"na\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"request_id\",\n", "        \"attribute_id\",\n", "        \"ai_verification_attempt_no\",\n", "    ],\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"table contains ai verification outputs for each attribute x request id along with the ai verification flag for the request id.\",\n", "}\n", "\n", "pb.to_trino(data_obj=final_upload, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "dd3707dc-04a2-4fe7-9cd2-eb29d694a51f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import date, timedelta\n", "\n", "# Step 1: Extract the date\n", "df = ai_verified.copy()\n", "df[\"ai_attempt_date\"] = df[\"ai_verification_attempt_ts\"].dt.date\n", "\n", "# Step 2: Create unique attempt key\n", "df[\"unique_key\"] = df[\"request_id\"].astype(str) + \"_\" + df[\"attempt_no\"].astype(str)\n", "\n", "# Step 3: Total keys per date (distinct request_id × attempt_no)\n", "total_keys_df = (\n", "    df.drop_duplicates(subset=[\"request_id\", \"attempt_no\"])\n", "    .groupby(\"ai_attempt_date\")[\"unique_key\"]\n", "    .count()\n", "    .rename(\"total_keys\")\n", "    .reset_index()\n", ")\n", "\n", "# Step 4: Approved keys per date (only where rid_ai_result == \"True\")\n", "approved_df = df[df[\"rid_ai_result\"] == \"True\"]\n", "approved_keys_df = (\n", "    approved_df.drop_duplicates(subset=[\"request_id\", \"attempt_no\"])\n", "    .groupby(\"ai_attempt_date\")[\"unique_key\"]\n", "    .count()\n", "    .rename(\"approved_keys\")\n", "    .reset_index()\n", ")\n", "\n", "# Step 5: Merge totals and approvals\n", "daily_df = pd.merge(total_keys_df, approved_keys_df, on=\"ai_attempt_date\", how=\"left\").fillna(0)\n", "daily_df[\"approved_keys\"] = daily_df[\"approved_keys\"].astype(int)\n", "\n", "# ➕ Step 5.1: Add approval_rate column\n", "daily_df[\"approval_rate\"] = round((daily_df[\"approved_keys\"] / daily_df[\"total_keys\"]) * 100, 2)\n", "\n", "# Step 6: Get rolling date subsets\n", "latest_date = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "last_7_df = daily_df[daily_df[\"ai_attempt_date\"] >= (latest_date - timedelta(days=6))].reset_index(\n", "    drop=True\n", ")\n", "last_14_df = daily_df[daily_df[\"ai_attempt_date\"] >= (latest_date - timedelta(days=13))]\n", "last_30_df = daily_df[daily_df[\"ai_attempt_date\"] >= (latest_date - timedelta(days=29))]\n", "\n", "\n", "# Step 7: Approval rate function\n", "def raw_approval_rate(df_slice):\n", "    total = df_slice[\"total_keys\"].sum()\n", "    approved = df_slice[\"approved_keys\"].sum()\n", "    return round((approved / total * 100) if total else 0, 2)\n", "\n", "\n", "# Step 8: Compute metrics\n", "metrics = {\n", "    \"Overall\": raw_approval_rate(daily_df),\n", "    \"Last 7 Days\": raw_approval_rate(last_7_df),\n", "    \"Last 14 Days\": raw_approval_rate(last_14_df),\n", "    \"Last 30 Days\": raw_approval_rate(last_30_df),\n", "}\n", "\n", "metrics_df = pd.DataFrame([{\"period\": p, \"approval_rate\": r} for p, r in metrics.items()])"]}, {"cell_type": "code", "execution_count": null, "id": "9fd709ea-7d91-4740-b3d7-6f350782f2dd", "metadata": {}, "outputs": [], "source": ["last_7_df"]}, {"cell_type": "code", "execution_count": null, "id": "36dcb3cf-3a79-4886-ae9c-722e3c3ac39c", "metadata": {}, "outputs": [], "source": ["last_7_df[\"ai_attempt_date\"] = last_7_df[\"ai_attempt_date\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "42ce339f-f70f-4b40-b5ab-418762041c33", "metadata": {}, "outputs": [], "source": ["totals_row = pd.DataFrame(\n", "    [\n", "        {\n", "            \"ai_attempt_date\": \"Total\",\n", "            \"total_keys\": last_7_df[\"total_keys\"].astype(int).sum(),\n", "            \"approved_keys\": last_7_df[\"approved_keys\"].astype(int).sum(),\n", "            \"approval_rate\": round(\n", "                (last_7_df[\"approved_keys\"].sum() / last_7_df[\"total_keys\"].sum()) * 100, 2\n", "            ),\n", "        }\n", "    ]\n", ")\n", "\n", "last_7_df = pd.concat([last_7_df, totals_row], ignore_index=True)\n", "\n", "last_7_df"]}, {"cell_type": "code", "execution_count": null, "id": "2c5cfaa6-7221-4fae-9256-ec6daebdad92", "metadata": {}, "outputs": [], "source": ["daily_df"]}, {"cell_type": "code", "execution_count": null, "id": "193f646b-c312-4786-b4d4-9ebc3d2de6fc", "metadata": {}, "outputs": [], "source": ["df = daily_df.copy()\n", "sheet_id = \"1f6CD1L2aYexVBbd5tojDETy3IwW3gx3Y9I95NfkzaUo\"\n", "sheet_name = \"dod_raw\"\n", "\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "c37144cf-185d-498c-b219-fcce3c05d053", "metadata": {}, "outputs": [], "source": ["metrics_df"]}, {"cell_type": "code", "execution_count": null, "id": "9cae26f7-1cd2-4914-b5c2-201b0da<PERSON>cef", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "# ---------- Helper Functions ----------\n", "def cmat(counts, label):\n", "    \"\"\"Build a 2×2 confusion matrix with rows = Truth, cols = AI prediction.\"\"\"\n", "    return pd.DataFrame(\n", "        [\n", "            [counts.TP, counts.FN],  # Truth=Approved  → TP | FN\n", "            [counts.FP, counts.TN],  # Truth=Rejected → FP | TN\n", "        ],\n", "        index=[f\"{label} Approved\", f\"{label} Rejected\"],\n", "        columns=[\"AI Approved\", \"AI Rejected\"],\n", "    )\n", "\n", "\n", "def make_block(cnt, pct):\n", "    \"\"\"Turn a confusion matrix + its percentages into a side‑by‑side DataFrame.\"\"\"\n", "    return pd.DataFrame(\n", "        {\n", "            \"Truth\": cnt.index,\n", "            \"AI Approved\": cnt[\"AI Approved\"].to_numpy(),\n", "            \"AI Rejected\": cnt[\"AI Rejected\"].to_numpy(),\n", "            \"\": [\"\" for _ in cnt.index],\n", "            \"AI Approved (%)\": pct[\"AI Approved\"].to_numpy(),\n", "            \"AI Rejected (%)\": pct[\"AI Rejected\"].to_numpy(),\n", "        }\n", "    )\n", "\n", "\n", "def blank_row_like(df):\n", "    \"\"\"Insert an empty spacer row matching df’s columns.\"\"\"\n", "    return pd.DataFrame([{c: \"\" for c in df.columns}])\n", "\n", "\n", "def make_metrics_rows(metrics, label, cols):\n", "    \"\"\"Build accuracy/precision/recall rows for a given label.\"\"\"\n", "    metric_titles = [(\"accuracy\", \"Accuracy\"), (\"precision\", \"Precision\"), (\"recall\", \"Recall\")]\n", "    rows = []\n", "    for key, title in metric_titles:\n", "        row = {c: \"\" for c in cols}\n", "        row[\"Truth\"] = f\"{label} {title}\"\n", "        row[\"AI Rejected\"] = f\"{metrics[key] * 100:.2f}%\"\n", "        rows.append(row)\n", "    return pd.DataFrame(rows, columns=cols)\n", "\n", "\n", "def explanation_block_from_df(pair_df, cols):\n", "    \"\"\"\n", "    Build the high‑level ‘Effectiveness’ and ‘Accuracy’ rows\n", "    using all AI‑verified cases in pair_df (including pending).\n", "    \"\"\"\n", "    total_ai = len(pair_df)\n", "    ai_approved = pair_df[\"ai_ok\"].sum()\n", "    effectiveness = ai_approved / total_ai\n", "\n", "    qc1_correct = (\n", "        (pair_df[\"ai_ok\"] & pair_df[\"qc1_ok\"]) | (~pair_df[\"ai_ok\"] & pair_df[\"qc1_rej\"])\n", "    ).sum()\n", "    acc_qc1 = qc1_correct / total_ai\n", "\n", "    qc2_correct = (\n", "        (pair_df[\"ai_ok\"] & pair_df[\"qc2_ok\"]) | (~pair_df[\"ai_ok\"] & pair_df[\"qc2_rej\"])\n", "    ).sum()\n", "    acc_qc2 = qc2_correct / total_ai\n", "\n", "    rows = []\n", "    for title, val in [\n", "        (\"Effectiveness: AI approval rate - all time average\", effectiveness),\n", "        (\"Accuracy (out of AI verified): (AI + QC1 approved) & (AI + QC1 rejected)\", acc_qc1),\n", "        (\"Accuracy (out of AI verified): (AI + QC2 approved) & (AI + QC2 rejected)\", acc_qc2),\n", "    ]:\n", "        row = {c: \"\" for c in cols}\n", "        row[\"Truth\"] = title\n", "        row[\"\"] = f\"{val * 100:.2f}%\"\n", "        rows.append(row)\n", "    return pd.DataFrame(rows, columns=cols)\n", "\n", "\n", "def column_headers_row(cols, minimal=False):\n", "    \"\"\"Header row with or without % columns.\"\"\"\n", "    include = [\"Truth\", \"AI Approved\", \"AI Rejected\"]\n", "    if not minimal:\n", "        include += [\"AI Approved (%)\", \"AI Rejected (%)\"]\n", "    return pd.DataFrame([{col: (col if col in include else \"\") for col in cols}], columns=cols)\n", "\n", "\n", "def static_confusion_block(cols):\n", "    \"\"\"A legend block explaining TP/FP/FN/TN.\"\"\"\n", "    rows = [\n", "        {\"Truth\": \"QC Approved\", \"AI Approved\": \"True Positive\", \"AI Rejected\": \"False Negative\"},\n", "        {\"Truth\": \"QC Rejected\", \"AI Approved\": \"False Positive\", \"AI Rejected\": \"True Negative\"},\n", "    ]\n", "    for r in rows:\n", "        for c in cols:\n", "            <PERSON>.<PERSON>(c, \"\")\n", "    return pd.DataFrame(rows, columns=cols)\n", "\n", "\n", "# ---------- Build Core DataFrames ----------\n", "# (Assumes qc1_counts, qc2_counts, basic_metrics, pair_df, last_7_df are already defined)\n", "\n", "qc1_mat = cmat(qc1_counts, \"QC1\")\n", "qc1_mat_pct = (qc1_mat / qc1_counts[\"N\"] * 100).round(2)\n", "qc2_mat = cmat(qc2_counts, \"QC2\")\n", "qc2_mat_pct = (qc2_mat / qc2_counts[\"N\"] * 100).round(2)\n", "\n", "qc1_metrics = basic_metrics(qc1_counts)\n", "qc2_metrics = basic_metrics(qc2_counts)\n", "\n", "qc1_block = make_block(qc1_mat, qc1_mat_pct)\n", "qc2_block = make_block(qc2_mat, qc2_mat_pct)\n", "cols = qc1_block.columns\n", "empty_header = pd.DataFrame([{c: \"\" for c in cols}], columns=cols)\n", "\n", "# Block‑1: high‑level explanation & spacer\n", "block1 = [explanation_block_from_df(pair_df, cols), blank_row_like(qc1_block)]\n", "\n", "# Block‑2: confusion matrices + numeric metrics (now left‑aligned)\n", "block2 = [\n", "    column_headers_row(cols),\n", "    qc1_block,\n", "    blank_row_like(qc1_block),\n", "    make_metrics_rows(qc1_metrics, \"QC1\", cols),\n", "    blank_row_like(qc1_block),\n", "    column_headers_row(cols),\n", "    qc2_block,\n", "    blank_row_like(qc2_block),\n", "    make_metrics_rows(qc2_metrics, \"QC2\", cols),\n", "    blank_row_like(qc2_block),\n", "    column_headers_row(cols, minimal=True),\n", "    static_confusion_block(cols),\n", "]\n", "\n", "# Block‑3: <PERSON><PERSON> (last 7 days), with blank row *above* the title\n", "block3 = []\n", "if \"last_7_df\" in globals():\n", "    blank3 = pd.DataFrame([[\"\"] * len(last_7_df.columns)], columns=last_7_df.columns)\n", "    header3 = pd.DataFrame([list(last_7_df.columns)], columns=last_7_df.columns)\n", "    data3 = last_7_df.copy()\n", "    block3 = [blank3, header3, data3]\n", "\n", "\n", "# ---------- Compute padding & alignment ----------\n", "N = len(cols)\n", "M = N + 1\n", "S = len(last_7_df.columns) if \"last_7_df\" in globals() else 0\n", "max_cols = max(N, M, S)\n", "\n", "\n", "def pad_and_align(df, indent):\n", "    arr = df.values.tolist()\n", "    tmp = pd.DataFrame(arr, columns=range(indent, indent + df.shape[1]))\n", "    return tmp.reindex(columns=range(max_cols), fill_value=\"\")\n", "\n", "\n", "# ---------- Assemble Final DataFrame ----------\n", "parts = []\n", "\n", "# 1) Empty top‑left corner\n", "parts.append(pad_and_align(empty_header, 0))\n", "\n", "# 2) Model metrics section\n", "parts.append(\n", "    pad_and_align(\n", "        pd.DataFrame([[\"Model's high level metrics\", \"\", \"\", \"Till Date\"]], columns=[0, 1, 2, 3]), 0\n", "    )\n", ")\n", "for dfp in block1:\n", "    parts.append(pad_and_align(dfp, 0))\n", "\n", "# 3) Confusion matrices section (also starting at col 0)\n", "parts.append(pad_and_align(pd.DataFrame([[\"Confusion matrices\"]], columns=[0]), 0))\n", "for dfp in block2:\n", "    parts.append(pad_and_align(dfp, 0))  # <- indent changed to 0 here\n", "\n", "# 4) Optional summary section\n", "if block3:\n", "    parts.append(pad_and_align(block3[0], 0))  # blank above\n", "    parts.append(pad_and_align(pd.DataFrame([[\"DoD Summary (last 7 days)\"]], columns=[0]), 0))\n", "    for dfp in block3[1:]:\n", "        parts.append(pad_and_align(dfp, 0))\n", "\n", "final_df = pd.concat(parts, ignore_index=True)\n", "final_df.columns = [\"\"] * final_df.shape[1]"]}, {"cell_type": "code", "execution_count": null, "id": "ae7191a3-c9d9-4ccd-94f7-785ca6e8da2c", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "4904f815-047e-4519-be2d-bb7a09deb7d6", "metadata": {}, "outputs": [], "source": ["df = final_df.copy()\n", "sheet_id = \"1f6CD1L2aYexVBbd5tojDETy3IwW3gx3Y9I95NfkzaUo\"\n", "sheet_name = \"Confusion Matrices\"\n", "\n", "pb.to_sheets(df, sheet_id, sheet_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}