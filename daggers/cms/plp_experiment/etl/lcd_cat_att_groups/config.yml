alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: lcd_cat_att_groups
dag_type: etl
escalation_priority: high
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: cms
notebook:
  parameters: null
  priority_weight: 1
owner:
  email: <EMAIL>
  slack_id: U08AFJVCEGL
path: cms/plp_experiment/etl/lcd_cat_att_groups
paused: false
pool: cms_pool
project_name: plp_experiment
schedule:
  end_date: '2025-11-16T00:00:00'
  interval: 0 3 1 * *
  start_date: '2025-08-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
