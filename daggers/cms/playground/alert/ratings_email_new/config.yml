alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ratings_email_new
dag_type: alert
escalation_priority: high
execution_timeout: 4320
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prep-airflow-prep-prep-eks-role
    volume_mounts: []
  type: kubernetes
namespace: cms
notebook:
  parameters: null
  priority_weight: 1
owner:
  email: <EMAIL>
  slack_id: U08AFJVCEGL
path: cms/playground/alert/ratings_email_new
paused: false
pool: cms_pool
project_name: playground
schedule:
  end_date: '2025-11-17T00:00:00'
  interval: 0 3 1 * *
  start_date: '2025-08-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
