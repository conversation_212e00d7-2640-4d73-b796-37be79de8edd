{"cells": [{"cell_type": "code", "execution_count": null, "id": "9301114b-303e-422f-bd5b-5704ca43457b", "metadata": {}, "outputs": [], "source": ["import os, time, math, sys\n", "\n", "\n", "def _fmt_bytes(b):\n", "    if b is None:\n", "        return \"n/a\"\n", "    units = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\"]\n", "    i = 0\n", "    while b >= 1024 and i < len(units) - 1:\n", "        b /= 1024.0\n", "        i += 1\n", "    return f\"{b:.2f} {units[i]}\"\n", "\n", "\n", "# --- Primary: psutil (preferred: shows RSS incl. children) ---\n", "def mem_snapshot_psutil():\n", "    import psutil\n", "\n", "    proc = psutil.Process(os.getpid())\n", "    rss_self = proc.memory_info().rss\n", "    # Sum RSS of process + children (non-recursive True covers tree)\n", "    rss_tree = rss_self\n", "    for ch in proc.children(recursive=True):\n", "        try:\n", "            rss_tree += ch.memory_info().rss\n", "        except psutil.Error:\n", "            pass\n", "    vm = psutil.virtual_memory()\n", "    return {\n", "        \"rss_self\": rss_self,\n", "        \"rss_tree\": rss_tree,\n", "        \"sys_available\": vm.available,\n", "        \"sys_used\": vm.used,\n", "        \"sys_percent\": vm.percent,\n", "    }\n", "\n", "\n", "# --- Fallback: resource (peak RSS), no external deps ---\n", "def mem_peak_resource():\n", "    import resource, platform\n", "\n", "    ru = resource.getrusage(resource.RUSAGE_SELF)\n", "    # ru_maxrss units: KB on Linux, BYTES on macOS\n", "    if sys.platform == \"darwin\":\n", "        peak_bytes = int(ru.ru_maxrss)\n", "    else:\n", "        peak_bytes = int(ru.ru_maxrss) * 1024\n", "    return {\"peak_rss\": peak_bytes, \"platform\": platform.system()}\n", "\n", "\n", "# Optional: read cgroup v2 limits if running in a container\n", "def cgroup_limit():\n", "    paths = [\"/sys/fs/cgroup/memory.max\", \"/sys/fs/cgroup/memory.high\"]\n", "    for p in paths:\n", "        try:\n", "            with open(p) as f:\n", "                v = f.read().strip()\n", "                if v and v != \"max\":\n", "                    return int(v)\n", "        except Exception:\n", "            pass\n", "    return None\n", "\n", "\n", "def log_memory(label=\"\"):\n", "    peak = mem_peak_resource()\n", "    try:\n", "        snap = mem_snapshot_psutil()\n", "        print(\n", "            f\"[MEM]{' ' + label if label else ''} | \"\n", "            f\"RSS self: {_fmt_bytes(snap['rss_self'])} | \"\n", "            f\"RSS tree: {_fmt_bytes(snap['rss_tree'])} | \"\n", "            f\"Sys avail: {_fmt_bytes(snap['sys_available'])} | \"\n", "            f\"Sys used: {_fmt_bytes(snap['sys_used'])} ({snap['sys_percent']}%) | \"\n", "            f\"Peak RSS: {_fmt_bytes(peak['peak_rss'])}\"\n", "        )\n", "    except Exception:\n", "        # psutil not installed -> only peak\n", "        print(\n", "            f\"[MEM]{' ' + label if label else ''} | \"\n", "            f\"Peak RSS: {_fmt_bytes(peak['peak_rss'])} (psutil not available)\"\n", "        )\n", "    lim = cgroup_limit()\n", "    if lim:\n", "        print(f\"[MEM] cgroup memory limit: {_fmt_bytes(lim)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca537eb9-c892-47f0-8bb1-97014342dfc4", "metadata": {}, "outputs": [], "source": ["log_memory(\"start\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7eb32d0-427c-4e34-8dcb-7fb6f0e22d37", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "a4467ae9-ce71-4cbb-a0ef-da291d13899b", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "e0e75c6e-3940-4184-8f1e-170954950b3a", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": null, "id": "b1be4539-5bd7-4c6e-a307-00ad168a3ed4", "metadata": {}, "outputs": [], "source": ["!pip install psutil"]}, {"cell_type": "code", "execution_count": null, "id": "7513dbb6-458f-4434-8206-976542d1464c", "metadata": {}, "outputs": [], "source": ["log_memory(\"after importing libs\")"]}, {"cell_type": "code", "execution_count": null, "id": "53f29c29-2b03-483a-b213-7f7fc82806d6", "metadata": {}, "outputs": [], "source": ["AUTHENTICATION_KEY = pb.get_secret(\"retail/vendor_console\")[\"OPENFGA_TOKEN\"]"]}, {"cell_type": "code", "execution_count": null, "id": "9fb32cd9-a1a2-442f-92fd-5cd353a356f3", "metadata": {}, "outputs": [], "source": ["uq = \"\"\"\n", "WITH base AS (\n", "    SELECT\n", "        id AS user_id,\n", "        email,\n", "        TRIM(manufacturer_ids) AS manufacturer_ids\n", "    FROM\n", "        vendor_console.user_details\n", "    WHERE\n", "        active\n", "),\n", "\n", "mfg_email_user AS (\n", "    SELECT\n", "        user_id,\n", "        email,\n", "        CAST(TRIM(manu_id) AS INT) AS mfg_id\n", "    FROM\n", "        base,\n", "        UNNEST(SPLIT(manufacturer_ids, ',')) AS t(manu_id)\n", "    WHERE\n", "        manufacturer_ids IS NOT NULL\n", "),\n", "--SELECT * FROM mfg_email_user\n", "entity AS (\n", "    SELECT\n", "        id AS entity_id,\n", "        external_id AS mfg_id,\n", "        name\n", "    FROM\n", "        vendor_console.entity\n", "    WHERE\n", "        insert_ds_ist IS NOT NULL\n", "        --AND external_id = 72\n", "        AND type = 'manufacturer'\n", "        AND active\n", "        AND lake_active_record\n", "    GROUP BY 1, 2, 3\n", ")\n", "\n", "SELECT\n", "    e.*,\n", "    m.user_id,\n", "    m.email\n", "FROM\n", "    entity e\n", "JOIN\n", "    mfg_email_user m\n", "        ON e.mfg_id = m.mfg_id\n", "WHERE\n", "    email NOT LIKE '%%grofers%%'\n", "    AND email NOT LIKE '%%zomato%%'\n", "ORDER BY 2, 4, 1\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "user_df = pd.read_sql_query(sql=uq, con=con)\n", "user_df"]}, {"cell_type": "code", "execution_count": null, "id": "501f6bb3-56c5-422b-80ee-aa4e778dcb64", "metadata": {"jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["user_ids = user_df[\"user_id\"].dropna().astype(str).unique().tolist()\n", "user_ids"]}, {"cell_type": "code", "execution_count": null, "id": "0706d9cc-2cc6-4a24-8cf8-182294b631ba", "metadata": {}, "outputs": [], "source": ["URL = \"https://authorisation-service-openfga-platform-authorisation.prod-sgp-k8s.grofer.io/stores/01JYNNKDW3PENB624G0R0PMHRV/list-objects\"\n", "HEADERS = {\"Content-Type\": \"application/json\", \"Authorization\": f\"Bearer {AUTHENTICATION_KEY}\"}\n", "AUTH_MODEL_ID = \"01JYNNKE0V7EF7F5NEP4N6GV4V\""]}, {"cell_type": "code", "execution_count": null, "id": "48c03660-b38d-41f7-b02f-7d70922e3081", "metadata": {}, "outputs": [], "source": ["results = []\n", "\n", "for uid in user_ids:\n", "    payload = {\n", "        \"authorization_model_id\": AUTH_MODEL_ID,\n", "        \"type\": \"role_binding\",\n", "        \"relation\": \"assignee\",\n", "        \"user\": f\"user:{uid}\",\n", "        \"context\": {},\n", "        \"consistency\": \"MINIMIZE_LATENCY\",\n", "    }\n", "\n", "    try:\n", "        response = requests.post(URL, headers=HEADERS, json=payload, timeout=10)\n", "        response.raise_for_status()\n", "        data = response.json()\n", "\n", "        if \"objects\" not in data or not isinstance(data[\"objects\"], list):\n", "            print(f\"Unexpected response format for user_id {uid}: {data}\")\n", "            continue\n", "\n", "        for obj in data[\"objects\"]:\n", "            try:\n", "                # Remove prefix\n", "                clean_str = obj.replace(\"role_binding:\", \"\", 1)\n", "\n", "                # Split into left + role\n", "                left_part, role = clean_str.split(\"__\", 1)\n", "\n", "                # Split left part into user_type + entity_id\n", "                user_type, entity_id = left_part.split(\"_\", 1)\n", "\n", "                results.append(\n", "                    {\"user_id\": uid, \"user_type\": user_type, \"entity_id\": entity_id, \"role\": role}\n", "                )\n", "\n", "            except Exception as parse_err:\n", "                print(f\"Parsing error for user_id {uid}, object '{obj}': {parse_err}\")\n", "\n", "    except requests.exceptions.RequestException as req_err:\n", "        print(f\"API request failed for user_id {uid}: {req_err}\")\n", "\n", "\n", "role_bindings_df = pd.DataFrame(results)\n", "\n", "\n", "print(f\"Completed. Total rows fetched: {len(role_bindings_df)}\")\n", "print(role_bindings_df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "727764dc-5895-48d8-a622-30dbf3fce634", "metadata": {}, "outputs": [], "source": ["role_bindings_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "488a2434-3673-450e-96c6-5950dedb1e9f", "metadata": {}, "outputs": [], "source": ["user_df"]}, {"cell_type": "code", "execution_count": null, "id": "52cced69-26d6-4b4f-a7fb-a931799d5253", "metadata": {}, "outputs": [], "source": ["# Ensure matching dtypes before join\n", "role_bindings_df[\"entity_id\"] = role_bindings_df[\"entity_id\"].astype(\"int64\")\n", "role_bindings_df[\"user_id\"] = role_bindings_df[\"user_id\"].astype(\"int64\")\n", "\n", "user_df[\"entity_id\"] = user_df[\"entity_id\"].astype(\"int64\")\n", "user_df[\"user_id\"] = user_df[\"user_id\"].astype(\"int64\")\n", "\n", "# Inner join\n", "final_df = role_bindings_df.merge(\n", "    user_df[[\"entity_id\", \"mfg_id\", \"user_id\", \"email\"]], on=[\"entity_id\", \"user_id\"], how=\"inner\"\n", ")[[\"user_id\", \"email\", \"mfg_id\", \"role\"]]\n", "\n", "print(final_df)"]}, {"cell_type": "code", "execution_count": null, "id": "7501efd9-a8e9-4ed6-8d99-599e0ba7e262", "metadata": {}, "outputs": [], "source": ["final_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "e5f7b1db-d951-434f-bffb-3931a8fb60aa", "metadata": {}, "outputs": [], "source": ["log_memory(\"after role bindings\")"]}, {"cell_type": "code", "execution_count": null, "id": "e684fab0-2cde-4e12-8c81-81f1b6af3a29", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "\n", "\n", "TARGET_SCHEMA = \"cms_etls\"\n", "TARGET_TABLE = \"manufacturer_email_role_mapping_v1\"\n", "KEYS = [\"user_id\", \"mfg_id\", \"role\"]\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "q = f\"\"\"\n", "    SELECT\n", "        user_id,\n", "        email,\n", "        mfg_id,\n", "        role,\n", "        active,\n", "        update_ts\n", "    FROM\n", "        {TARGET_SCHEMA}.{TARGET_TABLE}\n", "\"\"\"\n", "\n", "\n", "# ------------ Helpers ------------\n", "def norm_ts_series(s: pd.Series) -> pd.Series:\n", "    s = pd.to_datetime(s, errors=\"coerce\", utc=True)\n", "    return s.dt.tz_localize(None).dt.floor(\"us\")\n", "\n", "\n", "def ensure_dtypes(df: pd.DataFrame) -> pd.DataFrame:\n", "    df = df.copy()\n", "    # keys\n", "    df[\"user_id\"] = pd.to_numeric(df[\"user_id\"], errors=\"raise\").astype(\"int64\")\n", "    df[\"mfg_id\"] = pd.to_numeric(df[\"mfg_id\"], errors=\"raise\").astype(\"int64\")\n", "    # text\n", "    df[\"email\"] = df[\"email\"].astype(object)\n", "    df[\"role\"] = df[\"role\"].astype(object)\n", "    # bool\n", "    df[\"active\"] = df[\"active\"].astype(bool)\n", "    # ts\n", "    df[\"update_ts\"] = norm_ts_series(df[\"update_ts\"])\n", "    return df\n", "\n", "\n", "# ------------ 1) Current pull (active=True) ------------\n", "run_ts = pd.Timestamp.utcnow().floor(\"us\")  # naive UTC, matches Trino TIMESTAMP(6)\n", "\n", "curr = (\n", "    final_df[[\"user_id\", \"email\", \"mfg_id\", \"role\"]]\n", "    .dropna(subset=[\"user_id\", \"mfg_id\", \"role\"])\n", "    .drop_duplicates(KEYS)\n", "    .copy()\n", ")\n", "curr[\"active\"] = True\n", "curr[\"update_ts\"] = run_ts\n", "curr = ensure_dtypes(curr)\n", "\n", "# ------------ 2) Read last snapshot (if exists) ------------\n", "try:\n", "    last = pd.read_sql_query(sql=q, con=con)\n", "    if not last.empty:\n", "        # normalize dtypes coming back from <PERSON><PERSON>/driver\n", "        last = ensure_dtypes(last)\n", "except Exception:\n", "    print(\"No staging table found.\")\n", "    last = pd.DataFrame(columns=[\"user_id\", \"email\", \"mfg_id\", \"role\", \"active\", \"update_ts\"])\n", "\n", "# ------------ 3) Reconcile (keep old inactive, reactivate if reappears) ------------\n", "if last.empty:\n", "    out_df = curr\n", "else:\n", "    base = last.copy()\n", "\n", "    # Join curr email onto last, to flip active + refresh email for keys seen today\n", "    j = base.merge(\n", "        curr[KEYS + [\"email\"]].rename(columns={\"email\": \"curr_email\"}),\n", "        on=KEYS,\n", "        how=\"left\",\n", "        indicator=True,\n", "    )\n", "\n", "    # Reactivate / refresh for keys present in curr\n", "    found_mask = j[\"_merge\"].eq(\"both\")\n", "    j.loc[found_mask, \"active\"] = True\n", "    j.loc[found_mask, \"email\"] = j.loc[found_mask, \"curr_email\"]\n", "    j.loc[found_mask, \"update_ts\"] = run_ts\n", "\n", "    # Deactivate keys that disappeared this run (were active last time)\n", "    missing_mask = j[\"_merge\"].eq(\"left_only\") & j[\"active\"].eq(True)\n", "    j.loc[missing_mask, \"active\"] = False\n", "    j.loc[missing_mask, \"update_ts\"] = run_ts\n", "\n", "    updated_last = j[[\"user_id\", \"email\", \"mfg_id\", \"role\", \"active\", \"update_ts\"]]\n", "\n", "    # Brand-new keys (in curr but not in last)\n", "    new_keys = (\n", "        curr[KEYS]\n", "        .merge(last[KEYS], on=KEYS, how=\"left\", indicator=True)\n", "        .query('_merge == \"left_only\"')\n", "        .drop(columns=[\"_merge\"])\n", "    )\n", "    new_rows = new_keys.merge(curr, on=KEYS, how=\"left\")\n", "\n", "    # Normalize timestamps on both sides before concat/sort (avoid tz mix)\n", "    updated_last[\"update_ts\"] = norm_ts_series(updated_last[\"update_ts\"])\n", "    new_rows[\"update_ts\"] = norm_ts_series(new_rows[\"update_ts\"])\n", "\n", "    # Use a stable surrogate for NaT during sorting so keep='last' is deterministic\n", "    fill_for_sort = pd.Timestamp.min  # naive\n", "    out_df = (\n", "        pd.concat(\n", "            [\n", "                updated_last.assign(_uts=updated_last[\"update_ts\"].fillna(fill_for_sort)),\n", "                new_rows.assign(_uts=new_rows[\"update_ts\"].fillna(fill_for_sort)),\n", "            ],\n", "            ignore_index=True,\n", "        )\n", "        .sort_values([\"user_id\", \"mfg_id\", \"role\", \"_uts\"], kind=\"mergesort\")\n", "        .drop(columns=\"_uts\")\n", "        .drop_duplicates(KEYS, keep=\"last\")\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "# Final dtype safety before write\n", "out_df = ensure_dtypes(out_df)\n", "\n", "# ------------ 4) Write snapshot (single call) ------------\n", "kwargs = {\n", "    \"schema_name\": TARGET_SCHEMA,\n", "    \"table_name\": TARGET_TABLE,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"user_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"user id, primary key in vendor_console.user_details\",\n", "        },\n", "        {\"name\": \"email\", \"type\": \"VARCHAR\", \"description\": \"email address of the user\"},\n", "        {\"name\": \"mfg_id\", \"type\": \"INTEGER\", \"description\": \"manufacturer id\"},\n", "        {\"name\": \"role\", \"type\": \"VARCHAR\", \"description\": \"access role on partnerbiz\"},\n", "        {\"name\": \"active\", \"type\": \"BOOLEAN\", \"description\": \"true if present in latest snapshot\"},\n", "        {\"name\": \"update_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"last observed change time\"},\n", "    ],\n", "    \"primary_key\": [\"user_id\", \"mfg_id\", \"role\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"mfg x email x role; soft-deletes via active=false; reactivations supported\",\n", "}\n", "\n", "pb.to_trino(data_obj=out_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "7366465f-17dc-429f-8ac9-13104d6932a0", "metadata": {}, "outputs": [], "source": ["out_df"]}, {"cell_type": "code", "execution_count": null, "id": "39821580-badc-4d3c-90b1-fd25d045cc1d", "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "    SELECT\n", "        user_id,\n", "        email,\n", "        mfg_id,\n", "        role\n", "    FROM\n", "        cms_etls.manufacturer_email_role_mapping_v1\n", "    WHERE\n", "        active\n", "    GROUP BY 1, 2, 3, 4\n", "\"\"\"\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "d = pd.read_sql_query(sql=q, con=con)\n", "roles_df = d.copy()\n", "roles_df"]}, {"cell_type": "code", "execution_count": null, "id": "cbf8e0d6-43ed-42a5-bfed-9ac940d8b868", "metadata": {}, "outputs": [], "source": ["target_roles = {\"admin\", \"key_account_manager\"}\n", "\n", "roles_df[\"has_admin_or_kam\"] = roles_df.groupby(\"mfg_id\")[\"role\"].transform(\n", "    lambda roles: int(any(r in target_roles for r in roles))\n", ")\n", "\n", "print(roles_df)"]}, {"cell_type": "code", "execution_count": null, "id": "a0e3fc73-153c-4377-b094-1efb9fa177d0", "metadata": {}, "outputs": [], "source": ["roles_df[\"is_admin_or_kam\"] = roles_df[\"role\"].isin([\"admin\", \"key_account_manager\"]).astype(int)\n", "\n", "print(roles_df)"]}, {"cell_type": "code", "execution_count": null, "id": "8185aaf3-b334-4862-be2b-e8fa7b564577", "metadata": {}, "outputs": [], "source": ["roles_df[\"email_flag\"] = roles_df[\"has_admin_or_kam\"] + roles_df[\"is_admin_or_kam\"]\n", "roles_df"]}, {"cell_type": "code", "execution_count": null, "id": "82df3d1b-c272-476f-a824-b0838598c260", "metadata": {}, "outputs": [], "source": ["roles_final = roles_df[roles_df[\"email_flag\"].isin([0, 2])]\n", "roles_final"]}, {"cell_type": "code", "execution_count": null, "id": "4153186c-f833-4d46-80e4-967d6d1ea4ec", "metadata": {}, "outputs": [], "source": ["!pip install papermill"]}, {"cell_type": "code", "execution_count": null, "id": "2174fbe6-8838-492c-af9c-bd2cdd49e74e", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "from datetime import datetime\n", "import sys\n", "\n", "# Detect execution date (Airflow or local)\n", "try:\n", "    exec_date = datetime.strptime(\n", "        str(get_ipython().user_ns[\"execution_date\"]), \"%Y-%m-%dT%H:%M:%S.%fZ\"\n", "    )\n", "except KeyError:\n", "    exec_date = datetime.today()\n", "\n", "# Check for first Monday\n", "if exec_date.weekday() == 0 and 1 <= exec_date.day <= 7:\n", "    print(\"✅ First Monday of the month — continue execution.\")\n", "else:\n", "    print(\"❌ Not the first Monday of the month — exiting gracefully.\")\n", "    print(\"Notebook intentionally skipped.\")\n", "    sys.exit(0)  # ✅ EXIT SUCCESSFULLY\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6fef6d19-20ac-4288-9cef-75616afe7541", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import gc"]}, {"cell_type": "code", "execution_count": null, "id": "79cc05f4-376a-4f9f-9f14-65b711e5f6d4", "metadata": {}, "outputs": [], "source": ["!pip install xlsxwriter"]}, {"cell_type": "code", "execution_count": null, "id": "df9edcf9-9ec7-418f-934e-9a7694025c64", "metadata": {}, "outputs": [], "source": ["!pip install rich"]}, {"cell_type": "code", "execution_count": null, "id": "8a2962ca-7795-4745-8b43-d8fa483bd0a9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "8ca5b5e5-4d3e-48a3-a9a6-a05b900db0f4", "metadata": {}, "outputs": [], "source": ["import json\n", "import time\n", "from tqdm.auto import tqdm\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "2dd99a39-be45-41d5-8932-b1f99f2a7109", "metadata": {}, "outputs": [], "source": ["!pip install openai"]}, {"cell_type": "code", "execution_count": null, "id": "291f39ab-f2e7-4397-83e4-a231f6e22e1e", "metadata": {}, "outputs": [], "source": ["import openai"]}, {"cell_type": "code", "execution_count": null, "id": "a6d2a34b-34cb-4160-96f0-26d4fdd56afe", "metadata": {}, "outputs": [], "source": ["api_key = \"***********************************************************************************************************************************************************************\"\n", "openai.api_key = api_key"]}, {"cell_type": "code", "execution_count": null, "id": "7caf5161-c1f3-4cf7-a110-cd019d601840", "metadata": {}, "outputs": [], "source": ["model_name = \"gpt-4o-mini\"\n", "\n", "BASE_URL = \"https://api.openai.com/v1/chat/completions\"\n", "HEADERS = {\"Authorization\": f\"Bearer {api_key}\", \"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": null, "id": "3835cbd8-663b-41fc-8842-b8f51a506e07", "metadata": {}, "outputs": [], "source": ["client = openai.Client(api_key=api_key)"]}, {"cell_type": "code", "execution_count": null, "id": "e3722f46-2f0e-45d8-be38-ca16b7e7cd1f", "metadata": {}, "outputs": [], "source": ["mfg = \"\"\"\n", "WITH pid_mfg AS (\n", "    SELECT\n", "        gp.id AS product_id,\n", "        pb.manufacturer_id AS mfg_id,\n", "        pm.name AS mfg_name\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "            AND dp.l0_category_id = 332\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "        --AND pb.manufacturer_id = 2942\n", "    GROUP BY 1, 2, 3\n", "),\n", "vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY AS yesterday,\n", "\t\tcurrent_date - INTERVAL '181' DAY AS global_start_date\n", "),\n", "order_details AS (\n", "\tSELECT\n", "\t\tpd.mfg_id,\n", "\t\tpd.mfg_name,\n", "\t\tCOUNT(DISTINCT od.product_id) AS products,\n", "\t\t--ARRAY_DISTINCT(ARRAY_AGG(pd.product_id)) AS products_list,\n", "\t\tMAX(od.install_ts) AS flag\n", "\tFROM\n", "\t    oms_bifrost.oms_order_item od\n", "\tJOIN\n", "\t    pid_mfg pd ON pd.product_id = od.product_id\n", "\tWHERE\n", "\t\tod.insert_ds_ist >= CAST((SELECT global_start_date FROM vars) AS VARCHAR)\n", "        AND od.insert_ds_ist < CAST((SELECT yesterday FROM vars) AS VARCHAR)\n", "        --AND om.city_name IN ('Bengaluru', 'Chennai', 'Delhi', 'Faridabad', 'HR-NCR', 'Kolkata', 'Mumbai')\n", "        AND od.lake_active_record\n", "    GROUP BY 1, 2\n", "),\n", "--SELECT * FROM order_details\n", "base AS (\n", "    SELECT\n", "        id AS user_id,\n", "        email,\n", "        TRIM(manufacturer_ids) AS manufacturer_ids\n", "    FROM\n", "        vendor_console.user_details\n", "    WHERE\n", "        active\n", "),\n", "\n", "mfg_email_user AS (\n", "    SELECT\n", "        user_id,\n", "        email,\n", "        CAST(TRIM(manu_id) AS INT) AS mfg_id\n", "    FROM\n", "        base,\n", "        UNNEST(SPLIT(manufacturer_ids, ',')) AS t(manu_id)\n", "    WHERE\n", "        manufacturer_ids IS NOT NULL\n", "),\n", "\n", "final AS (\n", "    SELECT\n", "        od.mfg_id,\n", "        od.mfg_name,\n", "        od.products,\n", "        --od.products_list,\n", "        meu.user_id,\n", "        meu.email,\n", "        DENSE_RANK () OVER (ORDER BY od.products DESC) AS rnk\n", "    FROM\n", "        order_details od\n", "    JOIN\n", "        mfg_email_user meu\n", "            ON meu.mfg_id = od.mfg_id\n", "    WHERE\n", "        flag IS NOT NULL\n", "        AND products > 1\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    final\n", "WHERE\n", "    rnk < 4\n", "ORDER BY products DESC, mfg_id, user_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6f4b3883-23f6-4759-9299-2e4cef5ca656", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")\n", "mfg_df = pd.read_sql_query(sql=mfg, con=con)\n", "\n", "mfg_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "df550b9d-0394-487e-bc15-e6f52b4ce3a7", "metadata": {}, "outputs": [], "source": ["# unique mfg_id, mfg_name, products, count(distinct email)\n", "res = (\n", "    mfg_df.dropna(subset=[\"mfg_id\"])\n", "    .astype({\"mfg_id\": \"int64\"})\n", "    .groupby([\"mfg_id\", \"mfg_name\", \"products\"], as_index=False)\n", "    .agg(distinct_email_count=(\"email\", pd.Series.nunique))  # counts unique non-null emails\n", ")\n", "\n", "# optional: sort\n", "res = res.sort_values([\"products\", \"mfg_id\"], ascending=[False, True])\n", "res"]}, {"cell_type": "code", "execution_count": null, "id": "40eab01a-f399-4bb7-94bb-581798e7cdb5", "metadata": {}, "outputs": [], "source": ["# Inner join on user_id + mfg_id, keep only mfg_df columns\n", "mfg_df = mfg_df.merge(\n", "    roles_final[[\"user_id\", \"mfg_id\"]], on=[\"user_id\", \"mfg_id\"], how=\"inner\"  # only keys needed\n", ")\n", "\n", "print(mfg_df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "dec802e3-f6ff-4af8-be32-3a765acb1df9", "metadata": {}, "outputs": [], "source": ["pre_manufacturers = mfg_df[\"mfg_id\"].dropna().astype(int).unique().tolist()\n", "\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "check_sql = \"\"\"\n", "    SELECT\n", "        DISTINCT\n", "        CAST(manufacturer_string AS INTEGER) AS mfg_id\n", "    FROM\n", "        cms_etls.email_dispatch_log_for_excel_reports_v3\n", "    WHERE\n", "        DATE_TRUNC('month', CAST(email_sent_date AS DATE)) = DATE_TRUNC('month', CURRENT_DATE)\n", "\"\"\"\n", "\n", "try:\n", "    emailed_df = pd.read_sql_query(sql=check_sql, con=con)\n", "    emailed_ids = set(\n", "        pd.to_numeric(emailed_df[\"mfg_id\"], errors=\"coerce\").dropna().astype(\"int64\").tolist()\n", "    )\n", "except Exception:\n", "    emailed_ids = set()\n", "\n", "manufacturers = [int(m) for m in pre_manufacturers if int(m) not in emailed_ids]\n", "\n", "print(f\"Total manufacturers: {len(pre_manufacturers)}\")\n", "print(f\"Already emailed this month: {len(emailed_ids)}\")\n", "print(f\"To process now: {len(manufacturers)}\")\n", "\n", "message = (\n", "    f\"<!channel> Total manufacturers: {len(pre_manufacturers)}, \"\n", "    f\"Already emailed this month: {len(emailed_ids)},\"\n", "    f\"To process now: {len(manufacturers)},\"\n", ")\n", "pb.send_slack_message(channel=\"testing_alerts\", text=message)"]}, {"cell_type": "code", "execution_count": null, "id": "384bb1d7-6d9d-4346-9cb9-89d69f1caf5c", "metadata": {}, "outputs": [], "source": ["manufacturers"]}, {"cell_type": "code", "execution_count": null, "id": "fdcdecca-951d-43ee-9ea5-6b394320ef26", "metadata": {}, "outputs": [], "source": ["# manufacturers = [1399, 2373]"]}, {"cell_type": "code", "execution_count": null, "id": "62229e1c-306f-4d4f-a2ad-0a77edc5b1d9", "metadata": {}, "outputs": [], "source": ["ratings_query_template = \"\"\"\n", "WITH products_data AS (\n", "    SELECT\n", "        ipm.product_id,\n", "        gp.name AS product_name,\n", "        pb.manufacturer_id,\n", "        pm.name AS manufacturer,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "            {filter_clause}\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "            --AND dp.l0_category_id = 332\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "raw_ratings_data AS (\n", "    SELECT \n", "        pid AS product_id,\n", "        pd.product_name,\n", "        pd.manufacturer_id,\n", "        pd.manufacturer,\n", "        pd.brand,\n", "        pd.l2_category,\n", "        pd.l0_category,\n", "        pd.product_type,\n", "        MAX_BY(pan_aggregated_rating[1], updated_at) AS pan_pid_rating,\n", "        MAX_BY(pan_aggregated_rating[2], updated_at) AS rating_count,\n", "        MAX_BY(rating_bucket[1], updated_at) AS count_rating_1,\n", "        MAX_BY(rating_bucket[2], updated_at) AS count_rating_2,\n", "        MAX_BY(rating_bucket[3], updated_at) AS count_rating_3,\n", "        MAX_BY(rating_bucket[4], updated_at) AS count_rating_4,\n", "        MAX_BY(rating_bucket[5], updated_at) AS count_rating_5\n", "    FROM\n", "        consumer_etls.product_merchant_aggregated_ratings a\n", "    JOIN\n", "        products_data pd\n", "            ON pd.product_id = a.pid\n", "    /*\n", "    WHERE\n", "        pid = 508066\n", "    /*/\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "ptype_ratings_data AS (\n", "    SELECT\n", "        product_type,\n", "        AVG(pan_pytype_rating) AS pan_pytype_rating\n", "    FROM\n", "        (\n", "            SELECT \n", "                a.pid,\n", "                pd.product_type,\n", "                MAX_BY(pan_aggregated_rating[1], updated_at) AS pan_pytype_rating\n", "            FROM\n", "                consumer_etls.product_merchant_aggregated_ratings a\n", "            JOIN\n", "                dwh.dim_product dp\n", "                    ON dp.product_id = a.pid\n", "                    AND dp.is_current\n", "                    AND dp.is_product_enabled\n", "            JOIN\n", "                products_data pd\n", "                    ON pd.product_type = dp.product_type\n", "            /*\n", "            WHERE\n", "                pid = 508066\n", "            /*/\n", "            GROUP BY 1, 2\n", "        ) a\n", "    GROUP BY 1\n", ")\n", "SELECT\n", "    a.*,\n", "    b.pan_pytype_rating\n", "FROM\n", "    raw_ratings_data a\n", "JOIN\n", "    ptype_ratings_data b\n", "        ON a.product_type = b.product_type\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ce085b48-a912-4228-bbff-69a1c61ba0b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3582e757-d4a3-4102-b7bd-11bce9c5a467", "metadata": {}, "outputs": [], "source": ["reviews_template = \"\"\"\n", "WITH vars AS (\n", "\tSELECT\n", "\t\tcurrent_date - INTERVAL '1' DAY           AS yesterday,\n", "\t\tcurrent_date - INTERVAL '91' DAY         AS global_start_date\n", "),\n", "products_data AS (\n", "    SELECT\n", "        ipm.product_id,\n", "        gp.name AS product_name,\n", "        pb.manufacturer_id,\n", "        pm.name AS manufacturer,\n", "        gp.brand,\n", "        dp.l2_category,\n", "        dp.l0_category,\n", "        dp.product_type\n", "    FROM\n", "        rpc.product_brand pb\n", "    JOIN\n", "        rpc.product_manufacturer pm\n", "            ON pm.id = pb.manufacturer_id\n", "            AND pm.lake_active_record\n", "            AND pm.active = 1\n", "            {filter_clause}\n", "    JOIN\n", "        rpc.product_product rpp\n", "            ON rpp.manufacturer = pm.name\n", "            AND rpp.active = 1\n", "            AND rpp.lake_active_record\n", "    JOIN\n", "        rpc.item_product_mapping ipm\n", "            ON ipm.item_id = rpp.item_id\n", "            AND ipm.active = 1\n", "            AND ipm.lake_active_record\n", "    JOIN\n", "        cms.gr_product gp\n", "            ON gp.id = ipm.product_id\n", "            AND gp.enabled_flag\n", "            AND gp.lake_active_record\n", "    JOIN\n", "        dwh.dim_product dp\n", "            ON dp.product_id = ipm.product_id\n", "            AND dp.is_current\n", "            AND dp.is_product_enabled\n", "            --AND dp.l0_category_id = 332\n", "    WHERE\n", "        pb.active = 1\n", "        AND pb.lake_active_record\n", "    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8\n", "),\n", "rating_data AS \n", "(\n", "    SELECT\n", "        CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT) as order_id,\n", "        CAST(regexp_replace(partition_key, '^#u_id-', '') AS BIGINT) as user_id, \n", "        CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER) as product_id,\n", "        pd.product_name,\n", "        rating,\n", "        feedback,\n", "        -- COALESCE(location,CAST(ROW(0, 0) AS ROW(Latitude REAL, Longitude REAL))) AS location,\n", "        COALESCE(device[2],'') as device,\n", "        rr.created_at,\n", "        rr.updated_at,\n", "        TRIM(fsoid.city_name) AS city,\n", "        pd.manufacturer_id\n", "    FROM \n", "        zomato.dynamodb.blinkit_ratings_and_reviews rr\n", "\tJOIN\n", "\t    products_data pd ON pd.product_id = CAST(regexp_replace(entity_id, '^#p_id-', '') AS INTEGER)\n", "\tJOIN\n", "\t    dwh.fact_sales_order_details fsoid ON fsoid.order_id = CAST(regexp_replace(parent_entity_id, '^#o_id-', '') AS BIGINT)\n", "\t    AND order_create_dt_ist > (SELECT global_start_date FROM vars)\n", "    WHERE \n", "        sort_key like '%%#p_id-%%'\n", "        AND partition_key LIKE '#u_id%%'\n", "),\n", "--SELECT order_id FROM rating_data LIMIT 1\n", "city_state_map AS (\n", "    SELECT\n", "        t1.name AS city,\n", "        t2.name AS state,\n", "        CASE\n", "            WHEN t2.name IN ('Andhra Pradesh', 'Karnataka', 'Kerala', 'Tamil Nadu', 'Telangana', 'Pondicherry') THEN 'Southern'\n", "            WHEN t2.name IN ('Bihar', 'Jharkhand', 'Odisha', 'West Bengal', 'Orissa', 'Arunachal Pradesh', 'Assam', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Sikkim', 'Tripura', 'Chhattisgarh') THEN 'Eastern'\n", "            WHEN t2.name IN ('Goa', 'Gujarat', 'Maharashtra', 'Dadra and Nagar Haveli', 'Madhya Pradesh') THEN 'Western'\n", "            WHEN t2.name IN ('Haryana', 'Himachal Pradesh', 'Punjab', 'Rajasthan', 'Jammu and Kashmir', 'Delhi', 'New Delhi', 'Chandigarh', 'Uttar Pradesh', 'Uttarakhand') THEN 'Northern'\n", "            ELSE 'Others'\n", "        END AS zone\n", "    FROM\n", "        retail.console_location t1\n", "    LEFT JOIN\n", "        retail.console_state t2 ON t1.state_id = t2.id AND t2.lake_active_record\n", "    WHERE\n", "        t1.lake_active_record\n", "    GROUP BY 1, 2, 3\n", ")\n", "--SELECT DISTINCT zone FROM order_details-- WHERE zone IS NULL\n", "SELECT\n", "    rd.product_id,\n", "    rd.product_name,\n", "    CASE\n", "        WHEN rd.city IN ('B<PERSON><PERSON>', 'udaipur', 'Solan', 'DELHI','KHARAR', 'jammu', 'Sri Ganganagar' , 'CHANDIGARH', 'jaipur', 'mohali', 'NewChandigarh', 'Muzaffarnagar', 'ghaziabad') THEN 'Northern'\n", "        WHEN rd.city IN ('B<PERSON><PERSON>eshwar', 'BOKARO', 'KOLKATA', 'jamshedpur', 'raipur') THEN 'Eastern'\n", "        WHEN rd.city IN ('bengaluru', 'Tiruchirapalli') THEN 'Southern'\n", "        WHEN rd.city IN ('goa', 'mumbai', 'PUNE') THEN 'Western'\n", "        ELSE csm.zone\n", "    END AS zone,\n", "    rd.rating,\n", "    rd.feedback AS review,\n", "    format_datetime(from_unixtime(rd.created_at), 'MMMM, yyyy') AS month,\n", "    manufacturer_id\n", "--    rd.city\n", "FROM\n", "    rating_data rd\n", "LEFT JOIN\n", "    city_state_map csm ON csm.city = rd.city\n", "WHERE\n", "    rd.feedback IS NOT NULL\n", "    AND rd.feedback NOT IN ('', ' ')\n", "--    AND zone IS NULL\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7\n", "ORDER BY 1, 4\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "eae36f4d-f679-4f1b-b3fe-33c19cdcda8c", "metadata": {}, "outputs": [], "source": ["def _esc(s: str) -> str:\n", "    # simple SQL-escape for single quotes\n", "    return s.replace(\"'\", \"''\")\n", "\n", "\n", "def make_manufacturer_clause(mfr_id: int) -> str:\n", "    return f\"AND pm.id = {mfr_id}\"\n", "\n", "\n", "def fetch_for_manufacturer(mfr_id: int, con):\n", "    clause = make_manufacturer_clause(mfr_id)\n", "    df_weekly = pd.read_sql_query(ratings_query_template.format(filter_clause=clause), con=con)\n", "    return df_weekly, clause\n", "\n", "\n", "# establish your Trino connection\n", "con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "7acf733d-0b52-416a-9349-5b5862819c92", "metadata": {}, "outputs": [], "source": ["def _esc(s: str) -> str:\n", "    return s.replace(\"'\", \"''\").lower()\n", "\n", "\n", "def make_manufacturers_clause(mfr_ids: list[int]) -> str:\n", "    if not mfr_ids:\n", "        return \"\"\n", "    id_list = \", \".join(str(mid) for mid in mfr_ids)\n", "    return f\"AND pm.id IN ({id_list})\"\n", "\n", "\n", "def sql_escape_percent(q: str) -> str:\n", "    return q.replace(\"%\", \"%%\")\n", "\n", "\n", "def fetch_reviews_for_manufacturers(mfr_list: list[str], con):\n", "    clause = make_manufacturers_clause(mfr_list)\n", "    sql = sql_escape_percent(reviews_template.format(filter_clause=clause))\n", "    df = pd.read_sql_query(sql, con=con)\n", "    return df, clause"]}, {"cell_type": "code", "execution_count": null, "id": "6d2a921e-d24f-4f1d-a352-384645d40b2b", "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\n", "You are a customer review analyst for an online marketplace that hosts thousands of products across multiple categories.\n", "\n", "Customers leave reviews after receiving their orders. These reviews can be about:\n", "- the **product itself** (brand responsibility),\n", "- the **delivery experience** (platform responsibility),\n", "- or they may be vague or irrelevant.\n", "\n", "Your task is to:\n", "1. **Classify each customer review into a Theme and Sub-theme**\n", "2. **Identify the Tone** of the review as either Positive or Negative\n", "\n", "---\n", "\n", "### 1. THEME CLASSIFICATION\n", "\n", "Each review must be assigned exactly **one of the following 3 high-level themes**:\n", "- **Product related review** — issues with the product itself, including its quality, quantity, price, damage, leakage, seal condition, etc.\n", "- **Service related review** — issues related to delivery experience, rider behavior, timeliness, missing/wrong products, damaged delivery bag, etc.\n", "- **Random review** — vague, irrelevant, abusive, or cannot be categorized into product or service\n", "\n", "> 🔹 Use **“random review”** only if the review is vague, irrelevant, or contains no mention — even indirectly — of product or service quality.\n", "\n", "⚠️ Short reviews like “good,” “not chilled,” or “useless” are still valid **product related reviews** if they imply product feedback. Do NOT mark such reviews as random.  \n", "Random reviews are things like: “Thank you,” “Dear team,” “As,” or just “<PERSON>” — messages that give **no actionable feedback**.\n", "\n", "> ⚠️ Strictly enforce theme-sub-theme consistency. Sub-themes must only be assigned under their corresponding theme. For example:\n", "> - `product damage review` → only under **Product related review**\n", "> - `delivery review` → only under **Service related review**\n", "\n", "---\n", "\n", "### SUB-THEME GUIDELINES (with examples):\n", "\n", "#### 🔹 PRODUCT RELATED REVIEW\n", "- `quality review` — effectiveness, performance, taste, smell, durability, texture, etc.\n", "- `quantity / size review` — too little, too small, weight mismatch, etc.\n", "- `pricing review` — too costly, not worth it\n", "- `product damage review` — product was leaking, torn, unsealed, open bottle, broken container (includes brand packaging like pouches, bottles, jars, etc.)\n", "  > *e.g., \"Product was open and leaking\", \"Seal was broken\", \"Top was torn\", \"Cap missing\", \"Packet puffed\", \"Batter pack leaked\"*\n", "\n", "> ⚠️ **IMPORTANT**: All reviews mentioning broken seal, puffed pack, bottle open, torn bag, leaking product, etc. should go under `product damage review`.\n", "\n", "#### 🔹 SERVICE RELATED REVIEW\n", "- `delivery review` — late, fast, or delayed deliveries\n", "- `rider review` — rider behavior, politeness, calls\n", "- `expiry review` — expired or near-expiry product received\n", "- `wrong / missing product review` — incorrect item sent, something not delivered\n", "- `packaging hygiene review` — external box or product packaging was dusty, smelly, or looked unhygienic\n", "  > *e.g., \"Box smelled weird\", \"Dusty bottle\", \"Smelly pouch\"*\n", "\n", "⚠️ Avoid overly specific sub-themes like “cap broken” or “delivered 14 minutes late.”\n", "\n", "---\n", "\n", "### 2. TONE CLASSIFICATION\n", "\n", "Assign exactly one of the below:\n", "- **Positive tone** — expresses satisfaction, approval, or praise (even if subtle)\n", "- **Negative tone** — expresses dissatisfaction, complaint, or frustration (explicit or implied)\n", "\n", "Do **not** mark anything as neutral or mixed. Always pick one dominant tone.\n", "\n", "---\n", "\n", "Your classification should help brands and platform teams take targeted actions. Be accurate and avoid mixing up product and delivery-related issues.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "9ca6a517-5c09-4ef4-84c2-66b296e578d5", "metadata": {}, "outputs": [], "source": ["USER_PROMPT_TEMPLATE = \"\"\"You will be provided with two input values:\n", "- `product_name` (string)\n", "- `review` (string)\n", "\n", "Your task is to classify the review into:\n", "1. **Exactly one of the 3 high-level themes**\n", "2. **A corresponding sub-theme that provides more granularity**  \n", "3. **The tone of the review** (Positive or Negative)\n", "\n", "---\n", "\n", "### THEMES & EXAMPLES:\n", "\n", "1. **Product related review** — about the product’s features, damage, performance, pricing, quantity, etc.\n", "   - “Batter pouch was puffed and leaked”\n", "   - “Cap was broken and juice spilled”\n", "   - “Bottle was not sealed properly”\n", "   - “Too harsh for my skin”\n", "   - “Not worth the price”\n", "   - “Size is too small”\n", "   - “Good”\n", "   - “Not chilled”\n", "\n", "2. **Service related review** — about delivery experience, rider, expiry, missing items, outer bag condition, hygiene\n", "   - “Received product 3 hours late”\n", "   - “Delivery boy was polite”\n", "   - “Item missing from my order”\n", "   - “Outer delivery bag was torn”\n", "   - “Box was dusty and smelled bad”\n", "\n", "3. **Random review** — vague or irrelevant\n", "   - “Received”\n", "   - <PERSON><PERSON>”\n", "   - “How are you”\n", "   - “Dear Team”\n", "   - “As”\n", "\n", "> ⚠️ Do not mark short or unclear product feedbacks (like “good,” “useless,” “not chilled”) as random. These still count as product-related.\n", "\n", "> ⚠️ **Sub-themes must strictly correspond to their assigned parent theme.** For example:\n", "> - **Do not** assign `\"delivery review\"` to a product issue like size or leakage.\n", "> - **Do not** assign `\"product damage review\"` to a delivery complaint or hygiene issue.\n", "> - **Do not** fabricate or assume sub-themes outside the allowed mapping shown below.\n", "\n", "> 🚫 Violating this **theme-subtheme mapping** will result in misclassification.\n", "\n", "---\n", "\n", "### TONE CLASSIFICATION\n", "\n", "- **Positive tone**: expresses satisfaction or praise\n", "- **Negative tone**: expresses dissatisfaction, damage, delays, issues\n", "\n", "---\n", "\n", "### OUTPUT FORMAT (Use exactly):\n", "\n", "Tone: \"<Positive tone or Negative tone>\"  \n", "Theme: \"<one of the 3 themes>\"  \n", "Sub-theme: \"<relevant sub-theme>\"  \n", "Explanation: \"<brief justification (at least 50–70 words)>\"\n", "\n", "---\n", "\n", "### THEME TO SUB-THEME MAPPING\n", "\n", "Only use a sub-theme **from the corresponding theme** shown below. Cross-assignment is not allowed.\n", "\n", "1. Product related review  \n", "    - 1.1 \"product damage review\"  \n", "    - 1.2 \"quality review\"  \n", "    - 1.3 \"quantity / size review\"  \n", "    - 1.4 \"pricing review\"  \n", "\n", "2. Service related review  \n", "    - 2.1 \"expiry review\"  \n", "    - 2.2 \"packaging hygiene review\"  \n", "    - 2.3 \"wrong / missing product review\"  \n", "    - 2.4 \"delivery review\"  \n", "    - 2.5 \"rider review\"    \n", "\n", "3. Random review  \n", "    - Any response that does not fit clearly into product or service themes\n", "\n", "---\n", "\n", "### CHAIN OF THOUGHT TO FOLLOW\n", "\n", "1. Analyze the review's core message and emotional tone.  \n", "2. Identify the correct theme and then select a sub-theme *strictly from the allowed list for that theme*.  \n", "3. Clearly justify your classification with at least 50–70 words, using logic and examples where needed.\n", "\n", "---\n", "\n", "### INPUT ###\n", "product_name: {product_name}  \n", "review: {review_text}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "243de9b9-474f-4c94-91b9-44175ae36935", "metadata": {}, "outputs": [], "source": ["reviews_df, clause_used = fetch_reviews_for_manufacturers(manufacturers, con)\n", "\n", "reviews_df[\"mfr_slug\"] = reviews_df[\"manufacturer_id\"].astype(str)\n", "\n", "rev_df = reviews_df.reset_index(drop=True)\n", "\n", "rev_df"]}, {"cell_type": "code", "execution_count": null, "id": "5302745e-faed-4881-a558-37f1629cc092", "metadata": {}, "outputs": [], "source": ["# ... write files ...\n", "log_memory(\"after generating reviews\")"]}, {"cell_type": "code", "execution_count": null, "id": "d6854700-a34c-447b-b5c0-e803f8d2a137", "metadata": {}, "outputs": [], "source": ["message = f\"<!channel> Reviews df is a go go!, \"\n", "pb.send_slack_message(channel=\"testing_alerts\", text=message)"]}, {"cell_type": "code", "execution_count": null, "id": "01bf3e78-89f8-4837-ba57-acea98d4859e", "metadata": {}, "outputs": [], "source": ["def parse_raw_output(raw_output: str) -> dict:\n", "    tone = \"\"\n", "    theme = \"\"\n", "    sub_theme = \"\"\n", "    explanation = \"\"\n", "    for line in raw_output.splitlines():\n", "        if line.strip().lower().startswith(\"tone\"):\n", "            parts = line.split(\":\", 1)\n", "            tone = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"theme\"):\n", "            parts = line.split(\":\", 1)\n", "            theme = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"sub-theme\"):\n", "            parts = line.split(\":\", 1)\n", "            sub_theme = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "        if line.strip().lower().startswith(\"explanation\"):\n", "            parts = line.split(\":\", 1)\n", "            explanation = parts[1].strip().strip('“”\"') if len(parts) == 2 else \"\"\n", "    return {\"tone\": tone, \"theme\": theme, \"sub_theme\": sub_theme, \"explanation\": explanation}"]}, {"cell_type": "code", "execution_count": null, "id": "acc69e2a-3feb-4073-b3c9-a72ebf21317f", "metadata": {}, "outputs": [], "source": ["from datetime import timedelta\n", "from pathlib import Path\n", "import json, time\n", "import pencilbox as pb\n", "\n", "inp_path = Path(\"batch_input_reviews.jsonl\")\n", "out_dir = Path(\".\")\n", "out_dir.mkdir(exist_ok=True)\n", "\n", "print(\"Building batch payload...\")\n", "batch_requests = []\n", "for idx, row in rev_df.iterrows():\n", "    cid = f\"req_{idx}_{row['mfr_slug']}\"\n", "    user_prompt = USER_PROMPT_TEMPLATE.format(\n", "        product_name=row[\"product_name\"], review_text=row[\"review\"]\n", "    )\n", "    batch_requests.append(\n", "        {\n", "            \"custom_id\": cid,\n", "            \"method\": \"POST\",\n", "            \"url\": \"/v1/chat/completions\",\n", "            \"body\": {\n", "                \"model\": \"gpt-4o-mini\",\n", "                \"messages\": [\n", "                    {\"role\": \"system\", \"content\": SYSTEM_PROMPT},\n", "                    {\"role\": \"user\", \"content\": user_prompt},\n", "                ],\n", "                \"temperature\": 0.3,\n", "                \"max_tokens\": 500,\n", "            },\n", "        }\n", "    )\n", "\n", "with inp_path.open(\"w\") as f:\n", "    for req in batch_requests:\n", "        f.write(json.dumps(req) + \"\\n\")\n", "\n", "print(\"Uploading file and creating batch job...\")\n", "batch_file = client.files.create(file=inp_path.open(\"rb\"), purpose=\"batch\")\n", "batch = client.batches.create(\n", "    input_file_id=batch_file.id,\n", "    endpoint=\"/v1/chat/completions\",\n", "    completion_window=\"24h\",\n", "    metadata={\"description\": \"Review classification batch (multi-mfr)\"},\n", ")\n", "batch_id = batch.id\n", "\n", "print(f\"Batch created: {batch_id}\")\n", "\n", "# Wait for completion\n", "max_wait_secs = 24 * 60 * 60\n", "poll_interval = 10 * 60\n", "start_ts = time.time()\n", "\n", "print(\"Polling for batch status...\")\n", "while True:\n", "    batch = client.batches.retrieve(batch_id)\n", "    status = batch.status\n", "    rc = batch.request_counts or None\n", "    done = rc.completed if rc else 0\n", "    total = rc.total if rc else 0\n", "    failed = rc.failed if rc else 0\n", "    elapsed = timedelta(seconds=int(time.time() - start_ts))\n", "\n", "    print(f\"[{elapsed}] Batch status: {status} | Completed: {done}/{total} | Failed: {failed}\")\n", "\n", "    # Send Slack update\n", "    path = str(inp_path)  # Or any appropriate file you want to attach\n", "    message = (\n", "        f\"<!channel> GPT batch is running, status: {done}/{total} complete, \"\n", "        f\"time elapsed: {elapsed}, status: {status}\"\n", "    )\n", "    pb.send_slack_message(channel=\"testing_alerts\", text=message)\n", "\n", "    if status in (\"completed\", \"failed\", \"expired\"):\n", "        break\n", "    if time.time() - start_ts > max_wait_secs:\n", "        raise TimeoutError(f\"Waited {elapsed} but batch is still {status}.\")\n", "    time.sleep(poll_interval)\n", "\n", "if status != \"completed\":\n", "    raise RuntimeError(f\"Batch ended with status {status}\")\n", "print(\"<PERSON><PERSON> completed ✅\")\n", "\n", "# Parse results\n", "print(\"Downloading and parsing results...\")\n", "output_file_id = client.batches.retrieve(batch_id).output_file_id\n", "raw_lines = client.files.content(output_file_id).text.strip().splitlines()\n", "results = [json.loads(line) for line in raw_lines]\n", "\n", "print(\"Mapping results back to dataframe...\")\n", "tone_map, theme_map, subtheme_map, expl_map = {}, {}, {}, {}\n", "for res in results:\n", "    cid = res[\"custom_id\"]\n", "    content = res[\"response\"][\"body\"][\"choices\"][0][\"message\"][\"content\"]\n", "    out = parse_raw_output(content)\n", "    tone_map[cid] = out.get(\"tone\", \"ERROR\")\n", "    theme_map[cid] = out.get(\"theme\", \"ERROR\")\n", "    subtheme_map[cid] = out.get(\"sub_theme\", \"ERROR\")\n", "    expl_map[cid] = out.get(\"explanation\", \"\")\n", "\n", "# Back-fill in rev_df\n", "rev_df[\"tone\"] = [\n", "    tone_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"theme\"] = [\n", "    theme_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"sub_theme\"] = [\n", "    subtheme_map.get(f\"req_{i}_{s}\", \"ERROR\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "rev_df[\"explanation\"] = [\n", "    expl_map.get(f\"req_{i}_{s}\", \"\") for i, s in zip(rev_df.index, rev_df[\"mfr_slug\"])\n", "]\n", "\n", "\n", "subtheme_to_theme = {\n", "    # Product Related Reviews\n", "    \"product damage review\": \"Product related review\",\n", "    \"quality review\": \"Product related review\",\n", "    \"quantity / size review\": \"Product related review\",\n", "    \"pricing review\": \"Product related review\",\n", "    # Service Related Reviews\n", "    \"expiry review\": \"Service related review\",\n", "    \"packaging hygiene review\": \"Service related review\",\n", "    \"wrong / missing product review\": \"Service related review\",\n", "    \"delivery review\": \"Service related review\",\n", "    \"rider review\": \"Service related review\",\n", "    # Random Reviews\n", "    \"random review\": \"Random review\",\n", "    \"Random review\": \"Random review\",\n", "    \"N/A\": \"Random review\",\n", "}\n", "\n", "rev_df[\"theme\"] = rev_df[\"sub_theme\"].map(subtheme_to_theme)\n", "\n", "print(\"Saving output CSVs by manufacturer slug...\")\n", "for slug, sub in rev_df.groupby(\"mfr_slug\"):\n", "    sub.to_csv(out_dir / f\"{slug}_reviews_classified.csv\", index=False)\n", "\n", "print(\"Done.\")"]}, {"cell_type": "code", "execution_count": null, "id": "e879c4f7-b2b9-473f-9767-a8712910cf52", "metadata": {}, "outputs": [], "source": ["message = f\"<!channel> Output CSVs by manufacturer slug are done, \"\n", "pb.send_slack_message(channel=\"testing_alerts\", text=message)"]}, {"cell_type": "code", "execution_count": null, "id": "38c37e7a-50d7-45c2-9ad8-a442f0ff8b58", "metadata": {}, "outputs": [], "source": ["rev_df_to_load = rev_df[\n", "    [\n", "        \"product_id\",\n", "        \"zone\",\n", "        \"rating\",\n", "        \"review\",\n", "        \"month\",\n", "        \"theme\",\n", "        \"sub_theme\",\n", "        \"explanation\",\n", "        \"mfr_slug\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e5828b33-619d-4ec7-b674-70e3d2c99cda", "metadata": {}, "outputs": [], "source": ["rev_df_to_load"]}, {"cell_type": "code", "execution_count": null, "id": "c25a2207-6e36-49b1-a86f-d0418ed24f94", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cms_etls\",\n", "    \"table_name\": \"reviews_data_post_model_classification_v5\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"product_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Unique identifier for the product\",\n", "        },\n", "        {\n", "            \"name\": \"zone\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Geographical zone of the review\",\n", "        },\n", "        {\n", "            \"name\": \"rating\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Numeric rating given by the customer\",\n", "        },\n", "        {\n", "            \"name\": \"review\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Full text of the customer review\",\n", "        },\n", "        {\n", "            \"name\": \"month\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Month and year when the review was submitted\",\n", "        },\n", "        {\n", "            \"name\": \"theme\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"High‑level theme assigned to the review\",\n", "        },\n", "        {\n", "            \"name\": \"sub_theme\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"More granular sub‑theme classification\",\n", "        },\n", "        {\n", "            \"name\": \"explanation\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Model’s explanation for the chosen sub_theme\",\n", "        },\n", "        {\n", "            \"name\": \"mfr_slug\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"manufacturer id\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"product_id\", \"month\", \"review\", \"mfr_slug\", \"explanation\", \"zone\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": (\n", "        \"Raw customer reviews with theme, sub‑theme classifications and model explanations\"\n", "    ),\n", "}\n", "\n", "pb.to_trino(data_obj=rev_df_to_load, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "bdaba379-4eb7-4eb7-b42e-e4b173ee0f45", "metadata": {}, "outputs": [], "source": ["def build_summary_and_lists(writer, pid_df):\n", "    from xlsxwriter.utility import xl_rowcol_to_cell, xl_col_to_name\n", "\n", "    wb = writer.book\n", "    ws_l = wb.add_worksheet(\"Lists\")\n", "    ws_s = wb.add_worksheet(\"Summary\")\n", "    pid_sheet = \"PID_Ratings_Data\"\n", "\n", "    ws_s.hide_gridlines(2)\n", "\n", "    def xl(r, c):\n", "        return xl_rowcol_to_cell(r, c, row_abs=False, col_abs=False)\n", "\n", "    n_rows, _ = pid_df.shape\n", "    header = list(pid_df.columns)\n", "\n", "    def col_rng(colname):\n", "        idx = header.index(colname)\n", "        colL = xl_col_to_name(idx)\n", "        return f\"'{pid_sheet}'!${colL}$2:${colL}${n_rows+1}\"\n", "\n", "    R_L0 = col_rng(\"l0_category\")\n", "    R_L2 = col_rng(\"l2_category\")\n", "    R_BR = col_rng(\"brand\")\n", "\n", "    # ---------- Lists sheet ----------\n", "    ws_l.write(0, 0, \"L0_list\")\n", "    ws_l.write(0, 1, \"L2_list\")\n", "    ws_l.write(0, 2, \"Brand_list\")\n", "\n", "    ws_l.write_formula(1, 0, f\"=UNIQUE({R_L0})\")\n", "    ws_l.write_formula(1, 1, f'=IFERROR(UNIQUE(FILTER({R_L2}, {R_L0}=Summary!$A$4)),\"\")')\n", "    ws_l.write_formula(\n", "        1,\n", "        2,\n", "        f'=IFERROR(UNIQUE(FILTER({R_BR}, ({R_L0}=Summary!$A$4)*IF(Summary!$B$4=\"\",1,({R_L2}=Summary!$B$4)))),\"\")',\n", "    )\n", "\n", "    wb.define_name(\"L0_rng\", \"=Lists!$A$2:INDEX(Lists!$A:$A,COUNTA(Lists!$A:$A))\")\n", "    wb.define_name(\"L2_rng\", \"=Lists!$B$2:INDEX(Lists!$B:$B,COUNTA(Lists!$B:$B))\")\n", "    wb.define_name(\"Brand_rng\", \"=Lists!$C$2:INDEX(Lists!$C:$C,COUNTA(Lists!$C:$C))\")\n", "\n", "    ws_l.hide()\n", "\n", "    # ---------- Formats ----------\n", "    wrap = wb.add_format({\"text_wrap\": True, \"valign\": \"top\", \"border\": 1})\n", "    boldw = wb.add_format(\n", "        {\"text_wrap\": True, \"bg_color\": \"#FFCCCB\", \"align\": \"center\", \"bold\": True, \"border\": 1}\n", "    )\n", "    italic_wrap = wb.add_format({\"text_wrap\": True, \"align\": \"center\", \"italic\": True, \"border\": 1})\n", "    super_bold_14 = wb.add_format(\n", "        {\n", "            \"bold\": True,\n", "            \"bg_color\": \"#FF0000\",\n", "            \"font_color\": \"#FFFFFF\",\n", "            \"font_size\": 14,\n", "            \"text_wrap\": True,\n", "            \"align\": \"center\",\n", "            \"border\": 1,\n", "        }\n", "    )\n", "    super_italic_14 = wb.add_format(\n", "        {\"italic\": True, \"font_size\": 14, \"text_wrap\": True, \"align\": \"center\", \"border\": 1}\n", "    )\n", "\n", "    yel = wb.add_format(\n", "        {\"bold\": True, \"bg_color\": \"#FFE600\", \"text_wrap\": True, \"align\": \"center\", \"border\": 1}\n", "    )\n", "\n", "    teal = wb.add_format(\n", "        {\n", "            \"bold\": True,\n", "            \"bg_color\": \"#47704C\",\n", "            \"font_color\": \"#FFFFFF\",\n", "            \"text_wrap\": True,\n", "            \"align\": \"center\",\n", "            \"border\": 1,\n", "        }\n", "    )\n", "\n", "    peach = wb.add_format(\n", "        {\"bold\": True, \"bg_color\": \"#FAD7A0\", \"text_wrap\": True, \"align\": \"center\", \"border\": 1}\n", "    )\n", "\n", "    filter_fmt = wb.add_format({\"bg_color\": \"#D0E3FF\", \"border\": 2})\n", "\n", "    left_fmt = wb.add_format({\"align\": \"left\", \"border\": 1})\n", "    center_fmt = wb.add_format({\"align\": \"center\", \"border\": 1})\n", "    gray_bg = wb.add_format({\"bg_color\": \"#F9F9F9\", \"border\": 1})\n", "\n", "    # Row indices\n", "    title_row = 0\n", "    desc_row = 1\n", "    label_row = 2\n", "    filter_row = 3\n", "    group_header = 4\n", "    col_header_row = 5\n", "    data_start = 6  # corresponds to Excel row 7\n", "\n", "    # 1) Filter Titles\n", "    ws_s.write(title_row, 0, \"Mandatory Filter ↓\", super_bold_14)\n", "    ws_s.write(title_row, 1, \"Optional Filter ↓\", boldw)\n", "    ws_s.write(title_row, 2, \"Optional Filter ↓\", boldw)\n", "\n", "    # 2) Filter Descriptions\n", "    ws_s.write(desc_row, 0, \"No data without this\", super_italic_14)\n", "    ws_s.write(desc_row, 1, \"Put blank in filter to see for all L2s\", italic_wrap)\n", "    ws_s.write(desc_row, 2, \"Put blank in filter to see for all Brands\", italic_wrap)\n", "    ws_s.write(\n", "        0,\n", "        11,\n", "        \"Performance band the product falls into compared to others of the same product type\",\n", "        italic_wrap,\n", "    )\n", "\n", "    # 3) Filter Labels\n", "    ws_s.write(label_row, 0, \"L0 (Choose below) ↓\", italic_wrap)\n", "    ws_s.write(label_row, 1, \"L2 (Choose below) ↓\", italic_wrap)\n", "    ws_s.write(label_row, 2, \"Brand (Choose below) ↓\", italic_wrap)\n", "\n", "    # 4) Filter Inputs (Blue Cell)\n", "    for i in range(3):\n", "        ws_s.write_blank(filter_row, i, None, filter_fmt)\n", "\n", "    ws_s.data_validation(filter_row, 0, filter_row, 0, {\"validate\": \"list\", \"source\": \"=L0_rng\"})\n", "    ws_s.data_validation(filter_row, 1, filter_row, 1, {\"validate\": \"list\", \"source\": \"=L2_rng\"})\n", "    ws_s.data_validation(filter_row, 2, filter_row, 2, {\"validate\": \"list\", \"source\": \"=Brand_rng\"})\n", "\n", "    # 5) Merged Group Headers\n", "    ws_s.merge_range(group_header, 3, group_header, 5, \"PAN India\", yel)\n", "    ws_s.merge_range(group_header, 6, group_header, 10, \"Count of ratings\", teal)\n", "\n", "    # 6) Column Headers\n", "    headers = [\n", "        \"Product ID\",\n", "        \"Product Name\",\n", "        \"Product Type\",\n", "        \"Product Ratings Count\",\n", "        \"Product Average Rating\",\n", "        \"Product Type Average Rating\",\n", "        \"★1\",\n", "        \"★2\",\n", "        \"★3\",\n", "        \"★4\",\n", "        \"★5\",\n", "        \"Percentile Bucket in Product Type\",\n", "        \"Difference from Product Type Average\",\n", "    ]\n", "    for c, h in enumerate(headers):\n", "        fmt = left_fmt if c < 3 else yel if 3 <= c < 6 else teal if 6 <= c < 11 else peach\n", "        ws_s.write(col_header_row, c, h, fmt)\n", "\n", "    # 7) Write filter formulas to row 7 only\n", "    out_order = [\n", "        \"product_id\",\n", "        \"product_name\",\n", "        \"product_type\",\n", "        \"rating_count\",\n", "        \"pan_pid_rating\",\n", "        \"pan_pytype_rating\",\n", "        \"count_rating_1\",\n", "        \"count_rating_2\",\n", "        \"count_rating_3\",\n", "        \"count_rating_4\",\n", "        \"count_rating_5\",\n", "        \"performance_against_ptype\",\n", "        \"diff_from_ptype_avg\",\n", "    ]\n", "    out_rngs = [col_rng(c) for c in out_order]\n", "    mask = (\n", "        f\"(({R_L0}=Summary!$A$4))\"\n", "        f'*IF(Summary!$B$4=\"\",1,({R_L2}=Summary!$B$4))'\n", "        f'*IF(Summary!$C$4=\"\",1,({R_BR}=Summary!$C$4))'\n", "    )\n", "    for ci, rng in enumerate(out_rngs):\n", "        fmt = left_fmt if ci < 3 else center_fmt\n", "        formula = f'=IFERROR(FILTER({rng},{mask}),\"\")'\n", "        ws_s.write_formula(data_start, ci, formula, fmt)\n", "\n", "    # 8) Format A8:M5000 (skip A7) to preserve formula row\n", "    for row in range(data_start + 1, 5000):\n", "        for col in range(len(headers)):\n", "            fmt = left_fmt if col < 3 else center_fmt\n", "            ws_s.write_blank(row, col, None, fmt)\n", "\n", "    # 9) 3-color conditional format on last column (M)\n", "    last_col = len(headers) - 1\n", "    ws_s.conditional_format(\n", "        data_start,\n", "        last_col,\n", "        5000,\n", "        last_col,\n", "        {\n", "            \"type\": \"3_color_scale\",\n", "            \"min_color\": \"#E2D0CF\",\n", "            \"mid_color\": \"#E2E2CF\",\n", "            \"max_color\": \"#CFE2D7\",\n", "        },\n", "    )\n", "\n", "    # 10) Freeze at D7 (A–C + top 6 rows)\n", "    ws_s.freeze_panes(data_start, 3)\n", "\n", "    # 11) Set column widths\n", "    for i, h in enumerate(headers):\n", "        ws_s.set_column(i, i, max(16, len(h) + 2))\n", "\n", "    return ws_s, ws_l"]}, {"cell_type": "code", "execution_count": null, "id": "addaf043-12e1-4019-863d-77a825e9a846", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"cms_etls\",\n", "    \"table_name\": \"email_dispatch_log_for_excel_reports_v3\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"manufacturer_string\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"id of the manufacturer this report is for.\",\n", "        },\n", "        {\n", "            \"name\": \"excel_file_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Name of the Excel file sent in the email.\",\n", "        },\n", "        {\n", "            \"name\": \"email_sent_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Timestamp when the email was sent.\",\n", "        },\n", "        {\n", "            \"name\": \"email_addresses\",\n", "            \"type\": \"ARRAY(VARCHAR)\",\n", "            \"description\": \"List of email addresses the report was sent to.\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"manufacturer_string\", \"excel_file_name\", \"email_sent_date\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Log of email dispatches containing Excel reports per manufacturer, with recipient details.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f7915b94-7996-4dbf-bfc4-e89420d04320", "metadata": {}, "outputs": [], "source": ["# ... write files ...\n", "log_memory(\"before excel loop starts\")"]}, {"cell_type": "code", "execution_count": null, "id": "9712d63e-ff3b-4259-a9b8-a68516e8efe5", "metadata": {}, "outputs": [], "source": ["import shutil"]}, {"cell_type": "code", "execution_count": null, "id": "f018dfec-824e-47ae-a499-18161c57b828", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import re\n", "import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn\n", "\n", "\n", "# ---------- misc ----------\n", "def slugify(text: str) -> str:\n", "    return re.sub(r\"[^A-Za-z0-9]+\", \"_\", text).strip(\"_\").lower()\n", "\n", "\n", "output_dir = Path(\"/tmp/reports\")\n", "reviews_dir = Path(\".\")  # where you saved the per‑mfr review CSVs\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "steps = [\n", "    \"SQL fetch\",\n", "    \"Weights & groupby\",\n", "    \"Quartiles\",\n", "    \"Prev-week merge\",\n", "    \"Post metrics\",\n", "    \"Final trim/filter\",\n", "    \"Excel write (+reviews)\",\n", "    \"Email\",\n", "]\n", "\n", "with Progress(\n", "    SpinnerColumn(),\n", "    BarColumn(bar_width=None),\n", "    TextColumn(\"[bold blue]{task.description}\"),\n", "    TimeElapsedColumn(),\n", "    transient=False,\n", ") as progress:\n", "\n", "    outer = progress.add_task(\"[green]Manufacturers\", total=len(manufacturers))\n", "\n", "    for mfr in manufacturers:\n", "\n", "        mfr_int = int(mfr)\n", "        mfg_df[\"mfg_id\"] = mfg_df[\"mfg_id\"].astype(int)\n", "        mfg_name = mfg_df.loc[mfg_df[\"mfg_id\"] == mfr_int, \"mfg_name\"].iloc[0]\n", "\n", "        progress.console.print(f\"\\nStarting processing for Manufacturer ID: {mfr_int} ({mfg_name})\")\n", "\n", "        inner = progress.add_task(f\"[cyan]{mfr}\", total=len(steps))\n", "        mfr_slug = str(mfr)\n", "\n", "        ratings_india_df, clause = fetch_for_manufacturer(mfr, con)\n", "        progress.advance(inner)\n", "\n", "        # 3) Quartiles\n", "        def assign_quartile_bucket(subdf):\n", "            sub = subdf[[\"product_id\", \"pan_pid_rating\"]].sort_values(\n", "                \"pan_pid_rating\", ascending=False\n", "            )\n", "            n = len(sub)\n", "            ranks = np.arange(n)\n", "            buckets = np.floor(ranks * 4 / n).astype(int) + 1\n", "            sub[\"quartile_bucket\"] = np.clip(buckets, 1, 4)\n", "            return sub[[\"product_id\", \"quartile_bucket\"]]\n", "\n", "        pid_buckets = (\n", "            ratings_india_df.groupby([\"product_type\"], group_keys=False)\n", "            .apply(assign_quartile_bucket)\n", "            .reset_index(drop=True)\n", "        )\n", "        progress.advance(inner)\n", "\n", "        base_final = ratings_india_df.merge(\n", "            pid_buckets, on=[\"product_id\"], how=\"left\", validate=\"m:1\"\n", "        )\n", "        progress.advance(inner)\n", "\n", "        labels = {1: \"75 to 100%ile\", 2: \"50 to 75%ile\", 3: \"25 to 50%ile\", 4: \"0 to 25%ile\"}\n", "        base_final[\"performance_against_ptype\"] = base_final[\"quartile_bucket\"].map(labels)\n", "        progress.advance(inner)\n", "\n", "        # 6) Final trim/filter\n", "        cols_keep = [\n", "            \"product_id\",\n", "            \"product_name\",\n", "            \"l2_category\",\n", "            \"l0_category\",\n", "            \"brand\",\n", "            \"product_type\",\n", "            \"rating_count\",\n", "            \"count_rating_1\",\n", "            \"count_rating_2\",\n", "            \"count_rating_3\",\n", "            \"count_rating_4\",\n", "            \"count_rating_5\",\n", "            \"pan_pid_rating\",\n", "            \"pan_pytype_rating\",\n", "            \"performance_against_ptype\",\n", "        ]\n", "        clean_pid = (\n", "            base_final.assign(\n", "                diff_from_ptype_avg=lambda d: (d[\"pan_pid_rating\"] - d[\"pan_pytype_rating\"]).round(\n", "                    2\n", "                )\n", "            )[cols_keep + [\"diff_from_ptype_avg\"]]\n", "            .sort_values([\"product_id\"])\n", "            .reset_index(drop=True)\n", "        )\n", "\n", "        low_pids = clean_pid.loc[(clean_pid[\"rating_count\"] <= 20), \"product_id\"].unique()\n", "        filtered_pid = clean_pid[~clean_pid[\"product_id\"].isin(low_pids)].reset_index(drop=True)\n", "        progress.advance(inner)\n", "\n", "        mfg_df[\"mfg_id\"] = mfg_df[\"mfg_id\"].astype(int)\n", "\n", "        mfr_int = int(mfr)\n", "\n", "        mfg_name = mfg_df.loc[mfg_df[\"mfg_id\"] == mfr_int, \"mfg_name\"].iloc[0]\n", "\n", "        # 7) Excel write (+ optional Reviews tab)\n", "        fname = f\"{mfg_name}_ratings_report.xlsx\"\n", "        report_path = output_dir / fname\n", "\n", "        # path to reviews csv for this manufacturer\n", "        reviews_csv = reviews_dir / f\"{mfr_slug}_reviews_classified.csv\"\n", "\n", "        if not reviews_csv.exists():\n", "            raise FileNotFoundError(\n", "                f\"Missing reviews CSV for manufacturer {mfr_int} ({mfg_name}): {reviews_csv}\"\n", "            )\n", "\n", "        with pd.ExcelWriter(report_path, engine=\"xlsxwriter\") as writer:\n", "            # Summary/Lists\n", "            ws_summary, ws_lists = build_summary_and_lists(writer, filtered_pid)\n", "\n", "            # PID sheet\n", "            filtered_pid.to_excel(writer, sheet_name=\"PID_Ratings_Data\", index=False)\n", "            ws_pid = writer.sheets[\"PID_Ratings_Data\"]\n", "            for col_idx, col in enumerate(filtered_pid.columns):\n", "                max_len = max(filtered_pid[col].astype(str).map(len).max(), len(col))\n", "                ws_pid.set_column(col_idx, col_idx, max_len + 2)\n", "\n", "            ws_summary.activate()\n", "\n", "            ws_summary.select()\n", "\n", "            ws_pid.hide()\n", "\n", "            ws_lists.hide()\n", "\n", "            reviews_df_pre = pd.read_csv(reviews_csv)\n", "            reviews_df_pre = reviews_df_pre.drop(\n", "                columns=[\n", "                    \"mfr_slug\",\n", "                    \"explanation\",\n", "                    \"manufacturer_id\",\n", "                ]\n", "            )\n", "            reviews_df_mfr = reviews_df_pre[\n", "                reviews_df_pre[\"theme\"] == \"Product related review\"\n", "            ].copy()\n", "\n", "            reviews_df_mfr[\"theme\"] = (\n", "                reviews_df_mfr[\"theme\"]\n", "                .str.replace(\"review\", \"\", case=False, regex=False)\n", "                .str.strip()\n", "            )\n", "\n", "            reviews_df_mfr[\"sub_theme\"] = (\n", "                reviews_df_mfr[\"sub_theme\"]\n", "                .str.replace(\"review\", \"\", case=False, regex=False)\n", "                .str.strip()\n", "            )\n", "\n", "            reviews_df_mfr = reviews_df_mfr.drop(columns=[\"theme\"])\n", "\n", "            reviews_df_mfr = reviews_df_mfr.rename(columns={\"sub_theme\": \"theme\"})\n", "\n", "            reviews_df_mfr.to_excel(writer, sheet_name=\"PID_Reviews_Data\", index=False)\n", "            ws2 = writer.sheets[\"PID_Reviews_Data\"]\n", "\n", "            wrap_format = writer.book.add_format({\"text_wrap\": True, \"valign\": \"top\"})\n", "            for col_idx, col in enumerate(reviews_df_mfr.columns):\n", "                if col_idx == 4:\n", "                    ws2.set_column(col_idx, col_idx, 90, wrap_format)\n", "\n", "                else:\n", "                    max_line_len = max(\n", "                        reviews_df_mfr[col]\n", "                        .astype(str)\n", "                        .map(lambda v: max(len(line) for line in v.split(\"\\n\")))\n", "                        .max(),\n", "                        len(col),\n", "                    )\n", "                    # Set column width based on that\n", "                    ws2.set_column(col_idx, col_idx, max_line_len + 2, wrap_format)\n", "\n", "            n_rows = len(reviews_df_mfr)\n", "\n", "            for row_idx in range(1, n_rows + 1):\n", "                ws2.set_row(row_idx, None, wrap_format)\n", "\n", "            ws_summary.activate()\n", "\n", "            ws_pid.hide()\n", "\n", "        progress.advance(inner)\n", "\n", "        from_email = \"<EMAIL>\"\n", "\n", "        static_emails = pd.DataFrame(\n", "            {\n", "                \"email\": [\n", "                    \"<EMAIL>\",\n", "                    \"<EMAIL>\",\n", "                    \"<EMAIL>\",\n", "                ]\n", "            }\n", "        )\n", "\n", "        mfr_emails = mfg_df.loc[mfg_df[\"mfg_id\"] == mfr_int, \"email\"].dropna().unique().tolist()\n", "\n", "        common_emails = static_emails[\"email\"].tolist()\n", "\n", "        to_email = list(set(common_emails))\n", "\n", "        mfg_df[\"mfg_id\"] = mfg_df[\"mfg_id\"].astype(int)\n", "\n", "        mfr_int = int(mfr)\n", "\n", "        mfg_name = mfg_df.loc[mfg_df[\"mfg_id\"] == mfr_int, \"mfg_name\"].iloc[0]\n", "\n", "        subject = f\"Blinkit Ratings & Reviews Report: {mfg_name}\"\n", "        html_content = f\"\"\"\n", "        <div style=\"font-family: Arial, sans-serif; font-size: 14px; color: #333; line-height: 1.5;\">\n", "            <p>Hi,</p>\n", "\n", "            <p>Please find attached the Ratings and Reviews reports for your products that are listed on Blinkit. \n", "            The objective of sharing these reports is to share insights with brands that can improve customer experience \n", "            for their products on Blinkit.</p>\n", "\n", "            <p style=\"font-weight: bold; margin-top: 16px;\">This report has 2 parts:</p>\n", "            <ol style=\"padding-left: 20px;\">\n", "                <li><strong>Ratings summary:</strong> Ratings data benchmarked against similar product types for all products sold at least once in the last 6 months</li>\n", "                <li><strong>Reviews:</strong> Raw product related reviews dump for last 90 days (categorised basis tone and theme)</li>\n", "            </ol>\n", "\n", "            <p style=\"font-weight: bold; margin-top: 16px;\">How to improve:</p>\n", "            <p>We recommend all our partners to study this data and, based on insights, recommend at least one or more of the following actions on their low-rated products:</p>\n", "            <ol style=\"padding-left: 20px;\">\n", "                <li><strong>Content Optimization:</strong> If there is a mismatch between what customers expect and the content listed on the platform, \n", "                    we recommend you update your listings. You can send us your updated <NAME_EMAIL></li>\n", "                <li><strong>Packaging Improvement:</strong> If there are frequent problems with damage, missing items, etc., we recommend you re-look at your packaging.\n", "                    In case you need help in deep-diving this further, please reach out to <NAME_EMAIL></li>\n", "                <li><strong>Product Improvement:</strong> In case your products are continuously low-rated, we recommend you re-look at the product quality, formulation, etc. \n", "                    Continued low ratings can lead to de-listing of the product from the platform in the future.</li>\n", "            </ol>\n", "\n", "            <p style=\"margin-top: 20px;\">The report for manufacturer <strong>{mfg_name}</strong> is attached.</p>\n", "        </div>\n", "        \"\"\"\n", "\n", "        temp_path = Path(fname)\n", "\n", "        shutil.copy(report_path, temp_path)\n", "\n", "        pb.send_email(\n", "            from_email=from_email,\n", "            to_email=to_email,\n", "            subject=subject,\n", "            html_content=html_content,\n", "            files=[str(temp_path)],\n", "        )\n", "\n", "        log_df = pd.DataFrame(\n", "            [\n", "                {\n", "                    \"manufacturer_string\": str(mfr),\n", "                    \"excel_file_name\": fname,\n", "                    \"email_sent_date\": pd.Timestamp.now(),\n", "                    \"email_addresses\": to_email,\n", "                }\n", "            ]\n", "        )\n", "\n", "        pb.to_trino(data_obj=log_df, **kwargs)\n", "\n", "        # Delete all large DataFrames and intermediates before next iteration\n", "        del (ratings_india_df,)\n", "\n", "        # Force a garbage-collection pass\n", "        gc.collect()\n", "\n", "        progress.console.print(\n", "            f\"Memory cleaned and DataFrames deleted for '{mfr}'. Moving on to next manufacturer.\"\n", "        )\n", "\n", "        progress.advance(inner)\n", "        progress.update(outer, advance=1)\n", "        progress.console.print(f\"→ Completed processing for Manufacturer {mfr}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9815859f-61be-4993-ae05-80134d9bdde6", "metadata": {}, "outputs": [], "source": ["# ... write files ...\n", "log_memory(\"after everything, let's hope we reach here. yay!\")"]}, {"cell_type": "code", "execution_count": null, "id": "31310dc6-39bc-4466-8378-a22a30d0216e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ca1c895d-fa51-4d8e-9868-9cd292c40c0c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "72ca7866-8022-4c6f-8bf2-d71ae0105d6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd83484f-3cc3-48a5-a918-fdf25b8043f3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}